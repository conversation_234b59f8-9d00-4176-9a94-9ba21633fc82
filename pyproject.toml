[tool]
[tool.poetry]
name = "zillionare_course"
version = "0.4.0"
homepage = "https://www.jieyu.ai"
description = "大富翁量化"
authors = ["aaron yang <<EMAIL>>"]
readme = "README.md"
license =  "MIT"
classifiers=[
    'Development Status :: 2 - Pre-Alpha',
    'Intended Audience :: Developers',
    'License :: OSI Approved :: MIT License',
    'Natural Language :: English',
    'Programming Language :: Python :: 3.8',
]
packages = [
    { include = "coursea" }
]

[tool.poetry.dependencies]
python = ">=3.11,<3.13"
fire = "0.4.0"

black  = { version = "^22.3.0"}
isort  = { version = "5.10.1"}
flake8  = { version = "4.0.1"}
flake8-docstrings = { version = "^1.6.0" }
mkdocs  = { version = "^1.2.3"}
mkdocs-include-markdown-plugin  = { version = "^3.2.3"}
mkdocs-material  = { version = "^8.1.11"}
mkdocstrings  = { version = "^0.18.0"}
mkdocs-material-extensions  = { version = "^1.0.3"}
mkdocs-autorefs = {version = "^0.3.1"}
pre-commit = {version = "^2.17.0"}
livereload = {version = "^2.6.3"}
mike = { version="^1.1.2", optional=true}
mkdocs-awesome-pages-plugin = "^2.8.0"
notedown = "^1.5.1"
ipykernel = "^6.21.2"
akshare = "^1.8.84"
yfinance = "^0.2.12"
tushare = "^1.2.89"
jqdatasdk = "^1.8.11"
baostock = "^0.8.8"
cython = "^0.29.33"
paramiko = "^3.1.0"
md-toc = "^8.2.0"
python-frontmatter = "^1.0.0"
alphalens-reloaded = "^0.4.3"
arrow = "^1.3.0"
pysubs2 = "^1.7.3"
rich = "^13.9.4"
plotly = "^5.24.1"
plotly-express = "^0.4.1"
ta-lib = "^0.5.1"
python-dotenv = "^1.0.1"
scikit-learn = "^1.6.0"
optuna = "^4.1.0"
fastjsonschema = "^2.21.1"
jsonschema = "^4.23.0"
attrs = "^24.3.0"
referencing = "^0.35.1"
rpds = "^5.1.0"
hdbscan = "^0.8.40"
backtrader = "^**********"
freezegun = "^1.5.1"
pyfolio-reloaded = "^0.9.8"
numpy = "1.26.4"
pyyaml = "^6.0.2"

[tool.poetry.scripts]
cheese_course = 'cheese_course.cli:main'

[[tool.poetry.source]]
name = "ali"
url = "https://mirrors.aliyun.com/pypi/simple"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | scripts/convert\.py
)/
'''
[tool.isort]
profile = "black"
