site_name: 大富翁
# site_url: http://www.jieyu.ai
repo_url: https://github.com/zillionare
repo_name: cheese_course
#strict: true
theme:
  name: material
  language: zh
  #logo: assets/logo.png
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      toggle:
        icon: material/weather-night
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle:
        icon: material/weather-sunny
        name: Switch to light mode
  features:
    - navigation.indexes
    - navigation.tabs
    - navigation.instant
    - navigation.tabs.sticky
    - header.autohide
    - navigation.top
    - toc.follow
    - toc.integrate
    - content.code.copy
    - content.code.annotate
extra_css:
  - stylesheets/extra.css
markdown_extensions:
  - pymdownx.emoji:
      emoji_index: !!python/name:materialx.emoji.twemoji
      emoji_generator: !!python/name:materialx.emoji.to_svg
  - pymdownx.critic
  - pymdownx.caret
  - pymdownx.mark
  - pymdownx.tilde
  - pymdownx.tabbed
  - attr_list
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.highlight:
      linenums: true
  - pymdownx.superfences
  - pymdownx.details
  - admonition
  - toc:
      baselevel: 1
      toc_depth: '2-4'
      permalink: true
      slugify: !!python/name:pymdownx.slugs.uslugify
  - meta
  - footnotes
plugins:
  - include-markdown
  - search:
      lang: ja
  - awesome-pages
extra:
  version:
    provider: mike
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/zillionare
      name: Github
    - icon: material/email
      link: "mailto:<EMAIL>"
  # to enable disqus, uncomment the following and put your disqus id below
  # disqus: disqus_id
# uncomment the following and put your google tracking id below to enable GA
#google_analytics:
  #- UA-xxx
  #- auto
