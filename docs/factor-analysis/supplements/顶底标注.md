None
如果没有coursea环境，可以使用外部数据。
依赖：omicron.plotting.candlestick可以在https://zillionare.github.io/omicron/2.0.0a78/api/plotting/candlestick/找到源码

```python
from coursea import *
await init()
from omicron.plotting.candlestick import peaks_and_valleys
import plotly.graph_objects as go
import traceback
from ipywidgets import Button, HBox, VBox, Textarea, Layout, HTML, Output, Box
from IPython.display import display
import arrow

bars = await Stock.get_bars("000852.XSHG", 5000, FrameType.MIN30, end=datetime.datetime(2023, 1, 15, 15))

i = 0
frames = bars["frame"]
close = bars["close"]
labelled_data = None

last_pv = []

recs = {}

def log(msg):
    global info

    info.clear_output()
    with info:
        if isinstance(msg, Exception):
            traceback.print_exc(msg)
        else:
            print(msg)
        
def show_bars(b):
    global i, frames, close, info, figbox, last_pv


    if len(last_pv) > 0:
        log(f"上一个顶点: {last_pv[0]} {last_pv[1]}")
        
    if i >= len(bars):
        log("已遍历完成")
        return
    else:
        s = i
        e = i + 120

        try:
            flags = peaks_and_valleys(close[s:e])
            cs = Candlestick(bars[s:e], show_peaks=True, height=600, ma_groups=[5, 10, 20], show_rsi=False)
            fig = go.FigureWidget(cs.figure)
            figbox.children = (fig, )
            
            peaks = frames[s:e][np.argwhere(flags == 1)].flatten()
            if len(last_pv):
                peaks = peaks[peaks > last_pv[1]]
            peaks_box.value = "\n".join([f"{frame}" for frame in peaks])

            valleys = frames[s:e][np.argwhere(flags == -1)].flatten()
            if len(last_pv):
                valleys = valleys[valleys > last_pv[1]]
            valleys_box.value = "\n".join([f"{frame}" for frame in valleys])
        except Exception as e:
            log(e)


def keep(b):
    global peaks_box, valleys_box, recs, last_pv, i, frames

    recs.update({arrow.get(frame).naive: 1 for frame in peaks_box.value.split("\n")})
    recs.update({arrow.get(frame).naive: -1 for frame in valleys_box.value.split("\n")})

    last_valley = arrow.get(valleys_box.value.split("\n")[-1])
    last_peak = arrow.get(peaks_box.value.split("\n")[-1])

    if last_valley > last_peak:
        last_pv = ["谷", last_valley.naive]
    else:
        last_pv = ["峰", last_peak.naive]

    i = np.argwhere(frames == last_pv[1]).flatten()[0]
    show_bars(None)

def save(b):
    global bars, recs, labelled_data, info
    labelled_data = pd.DataFrame(bars)
    labelled_data["flag"] = 0

    for frame, flag in recs.items():
        labelled_data.loc[labelled_data.frame == frame, "flag"] = flag

    log(f"存入{len(recs)}条记录到数据文件中")
    with open("pv-labels.pkl", "wb") as f:
        pickle.dump(labelled_data, f)
        log(f"成功保存")

    recs = {}

def goback(b):
    global i, last_pv

    i -= 120
    if i < 0:
        return
    
    last_pv = []
    show_bars(None)

backward_button = Button(
    description='前一组',
)

keep_button = Button(
    description='记录 > 下一组'
)

save_button = Button(
    description='存盘'
)

info = Output(layout=Layout(width="40%"))

peaks_box = Textarea(
    value='',
    placeholder='请输入峰值时间，每行一个',
    description='峰值时间',
    layout=Layout(width='40%',height='100px')
)

valleys_box = Textarea(
    value='',
    placeholder='valley moments here',
    description='请输入谷值时间，每行一个',
    layout=Layout(width='40%',height='100px')
)

figbox = Box(layout=Layout(width="100%"))
inputs = HBox((peaks_box, valleys_box))
buttons = HBox((backward_button, keep_button, save_button, info))
display(VBox((buttons, inputs, figbox)))

keep_button.on_click(keep)
save_button.on_click(save)
backward_button.on_click(goback)
show_bars(None)

```

```python
## 合并各阶段标注文件
import pickle
with open("pv-labels-1.pkl", "rb") as f:
    df1 = pickle.load(f)

with open("pv-labels-0.pkl", "rb") as f:
    df2 = pickle.load(f)

df = pd.concat((df1, df2))
with open("pv-labels.pkl", "wb") as f:
    pickle.dump(df, f)
```

```python
import time
import pickle
file = "bars_1d_2005_2023.pkl"

secs = await Security.select().types(["stock"]).eval()
barss = {}
start = datetime.date(2005, 1, 4)
end = datetime.date(2023,12,31)
t0 = time.time()

for i, sec in enumerate(secs):
    bars = await Stock.get_bars_in_range(sec, FrameType.DAY,start, end, fq=False)
    barss[sec] = bars
    if (i+1)%100 == 0:
        print(f"{i+1}/{len(secs)}, {(time.time() - t0)/60:.0f} elapsed")
        
with open(file, "wb") as f:
    pickle.dump(barss, f)
```

```python
with open(file, "rb") as f:
    print(pickle.load(f))
```

```python
!ls -lh bars_1d_2005_2023.pkl
```

```python

```
