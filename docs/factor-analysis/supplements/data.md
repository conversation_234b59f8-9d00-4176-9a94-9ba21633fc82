# 如何获取数据？

!!! important
    部分功能在预览环境下不可用，比如 Tushare 数据获取。
    
此 Notebook 将演示如何在本环境下获取数据。我们在全局名字空间下提供了一些辅助函数。

```python
import inspect

objs = []
for name in dir():
    if name.startswith('_'):
        continue

    obj = globals()[name]

    if inspect.isbuiltin(obj):
        continue
    elif inspect.isclass(obj):
        objs.append([name, "class", inspect.getdoc(obj)])
    elif inspect.isfunction(obj):
        objs.append([name, "function", inspect.getdoc(obj)])
    elif inspect.ismodule(obj):
        objs.append([name, "module", inspect.getdoc(obj)])
    elif inspect.ismethod(obj):
        objs.append([name, "method", inspect.getdoc(obj)])
    else:
        objs.append([name, "variable"])

pd.DataFrame(objs, columns=[ "name", "type", "doc"])
    
```

这里最重要的是load_bars,
load_sectors和fetch_valuation。此外，我们还提供了tushare的接口对象pro，它是已登录认证的接口。关于其用法，请见
[tushare](https://tushare.pro/document/2)。

## 自定义函数
下面，我们更详细地介绍上面的输出中，出现的几个自定义函数。

### 获取日线数据 load_bars

我们可以通过load_bars来获取日线数据。load_bars的三个参数分别是start, end和universe。它将返回一个由date,
asset索引的DataFrame，列有OHLC,volume, amount字段。此外，它还有price字段，该字段相当于open.shift(-1)。

它返回的数据以date和asset为索引。返回数据都进行了动态前复权处理。该函数将从本地加载数据，时间范围是2005年到2023年底。

```python
start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 12, 29)

# 1. 不指定universe, 随机取500支股票
np.random.seed(78)
load_bars(start, end)
```

我们还可以指定universe为-1，这样会加载全部股票数据：

```python
load_bars(start, end, -1)
```

我们也可以直接指定股票列表

```python
universe = ["000001.XSHE", "600000.XSHG"]

load_bars(start, end, tuple(universe))
```

### date2int和int2date

tushare等一些库常常使用形如"20240123"这样的字符串来表示时间。date2int和int2date提供转换功能（但你要自己完成字符串与int的转换）。

```python
print(str(date2int(datetime.date(2019, 1, 1))))
print(int2date(20190101))
```

### 获取证券列表 load_sectors

获取行业分类数据。返回一个dataframe, index为asset, columns为sector,其值为对应的分类

```python
load_sectors()
```

### 获取基本面数据 fetch_daily_basic

获取指定日期的所有股票的基本面信息。该函数是对tushare.daily_basic的一个封装。

```python
fetch_daily_basic(datetime.datetime.now().date())
```

### fetch_valuation

获取某一日所有个股的市值。

```python
fetch_valuation(datetime.datetime.now().date())
```

### alphatest

这是我们对Alphalens的封装。它使得你可以省去加载数据，执行因子预处理、提取价格等步骤，直接传入参数，获得结果，方便进行参数的grid
search。这个函数的使用，在后面的课程中有介绍。

但如果因子中需要横截面操作，则无法使用本函数，你需要自己调用Alphalens。

### 使用tushare

除了上述数据之外，如果你需要的数据在本环境中没有API可以直接获取，也可以试试 Tushare。

在环境中，我们已经配置好了tushare账号，请参考 Tushare 的官方文档来使用它。

```python
pro = pro_api()
pro.daily_basic().tail()
```

!!! tip
    在本环境使用tushare时，不需要导入 tushare 模块，并且在创建 pro 接口之前，无须调用 set_token() 函数。


我们还可以使用字符串数据，比如["000001.XSHE",
"600000.XSHG"]，等等。由于我们使用了缓存lru_cache，该方法要求传入的参数是可哈希的，所以，在调用时，我们将其转换为tuple。
