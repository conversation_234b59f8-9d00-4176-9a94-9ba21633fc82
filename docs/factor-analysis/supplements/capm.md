# 1. CAPM 中 Alpha 和 Beta的计算

在CAPM模型中，我们要先计算出资产的风险系数$\beta$，然后根据公式，计算出资产的预期收益率。随后，我们还能计算出Alpha指标。

基本步骤是：

1. 获得市场的无风险利率，即国债收益率。
2. 获取沪深300指数过去一年的行情数据，计算日收益率和年化收益率。
3. 从沪深300中任取一支个股x，获取过去一年的行情数据，计算日收益率和年化收益率。
4. 通过2和3的数据，计算$\beta$
5. 通过CAPM公式，计算个股x的预期年化收益率
6. 计算Alpha

本练习中，我们深入探讨了4种求Alpha的方法，将加深对CAPM和Jensen's Alpha的理解，了解各种算法和概念之间的异同，以及造成差异的原因。

## 1.1 获取国债收益率

这段代码中，请注意如何获取债券数据。

```python
import akshare as ak
import arrow

end = arrow.get('2023-12-31')
start = end.shift(years=-1)
end_ = f"{end.year}{end.month:02d}{end.day:02d}"
start_ = f"{start.year}{start.month:02d}{start.day:02d}"

bond = ak.bond_china_yield(start_date=start_, end_date=end_)
bond.set_index(keys='曲线名称', inplace=True)
bond

```

这样我们就得到了近一年的各种债券收益率。我们可以把“中债国债收益率曲线”一年期的平均值当作一年期国债收益：

```python
# 要求：从bond数据中，得到risk_free利率
# BEGIN SOLUTION
rf = bond[bond.index=='中债国债收益率曲线']['1年'].mean()

rf = rf / 100
# END SOLUTION

assert rf - 0.021 < 1e-4
```

## 1.2 沪深300行情数据及日收益率

在这个练习中，你将熟悉如何通过 akshare 来获取指数日线行情数据。

```python
import akshare as ak
import pandas as pd

# 通过akshare获取沪深300日线指数，并且过滤掉不在[start.naive, end.naive]之间的数据。结果保存在变量hs300中。
# 这里涉及到时间的转换和比较运算。你可以使用to_datetime和Arrow.naive方法。

hs300 = ak.stock_zh_index_daily(symbol="sz399300")
hs300.index = pd.to_datetime(hs300["date"])

filter = (hs300.index >= start.naive) & (hs300.index <= end.naive)
hs300 = hs300[filter]

assert hs300.index[0] == arrow.get('2023-01-03').naive

# 要求： 求出市场回报（即hs300日收益率数组），保存在market_returns变量中
# 提示： 使用pct_change，并且要移除掉nan

# BEGIN SOLUTION
market_returns = hs300["close"].pct_change().dropna()
# END SOLUTION

# 12月31日的收益率应该为0.49%
assert market_returns.iloc[len(market_returns) - 1] - 0.0049 <= 1e-4
```

## 1.3 取某一品种的行情数据，计算收益率

```python
# import akshare as ak
# code = "301269"
# index_stock_cons_df = ak.index_stock_cons(symbol="399300")
# bars = ak.stock_zh_a_hist(symbol=code, period="daily", start_date=start_, end_date=end_, adjust="qfq")
#returns = bars["收盘"].pct_change().dropna()


import tushare as ts
code = "301269.SZ"
bars = ts.pro_bar(ts_code=code, freq='D', adj='qfq', start_date=start_, end_date=end_)
bars.index = pd.to_datetime(bars['trade_date'])
# 要求：求得301269的每日收益率，保存在returns变量中
# BEGIN SOLUTION
bars = bars.sort_index()
returns = bars['close'].pct_change()[1:]
# END SOLUTION

assert returns.iloc[len(returns) - 1] - 0.0063 < 1e-4
```

## 1.4 计算$\beta$

```python
import numpy as np

# 要求：使用np.cov和我们已经获得的returns，market_returns变量，来计算beta
# BEGIN SOLUTION
cov = np.cov(returns, market_returns)
beta = cov[0,1]/cov[1,1]
# END SOLUTION

print("beta is", beta)

assert beta - 0.734 < 1e-4
```

## 1.5 由CAPM求得预期收益

该预期收益一般以年化形式表示。因此，需要先对$R_m$进行年化。

```python
# 要求: 通过 market_returns 计算出 annual_market_return
# 年化时，使用242个交易日作为年化因子

# BEGIN SOLUTION
annual_market_return = (1 + market_returns.mean()) ** 242 - 1
# END SOLUTION

assert annual_market_return - (-0.11) < 1e-4
```

接下来，我们通过CAPM公式，计算被选标的的年化预期收益

```python
# 要求：由CAPM公式计算出expected_return

# BEGIN SOLUTION
expected_return = rf + beta * (annual_market_return - rf)
print(f"资产{code}的预期年化收益", expected_return)
# END SOLUTION

assert expected_return - (-0.0752) < 1e-4
```

## 1.6 不同的计算方式，不一样的 Alpha

在求得$\beta$与预期收益之后，我们还可以求得个股的超额收益。
个股的实际年收益率为：

```python
ilast = len(bars) - 1
actual_return = bars.iloc[ilast]["close"]/bars.iloc[0]["close"] - 1
print(f"{code}的年实际收益为:{actual_return:.2%}")
```

### 1.6.1 根据 CAPM 公式求 Alpha
接下来我们计算 Alpha:

```python
# BEGIN SOLUTION
alpha = actual_return - expected_return
print(alpha)
# END SOLUTION

assert alpha - 0.2084 < 1e-4
```

我们的计算结果是否正确？我们可以使用两种方式进行验证。一是使用empyrical包中的alpha_beta方法，另一种方法，是求资产收益对市场组合的一阶线性回归。这里又至少有两种方法，即numpy.polyfit的方法和statsmodels的方法。这两者本质上都是最小二乘回归。

### 1.6.2 使用 empyrical
下面的单元格将通过empyrical包中的alpha_beta来计算资产的alpha和beta。

```python
from empyrical.stats import alpha_beta

alpha, beta = alpha_beta(returns.to_numpy(), market_returns.to_numpy(), risk_free = rf/242, annualization=242)

print(alpha, beta)

assert alpha - 0.3199 < 1e-4
assert beta - 0.7334 < 1e-4
```

可以看出，empyrical给出的beta与我们计算的完全一致，但在alpha的计算上，大家的结果并不一样(我们前面计算出的 alpha 大约是 0.208)。

### 1.6.3 使用 statsmodels

下面，我们np.polyfit的方法来计算alpha和beta:

```python
import statsmodels.regression.linear_model as lm
import statsmodels.tools.tools as ct

x = ct.add_constant(market_returns.to_numpy())
y = returns.to_numpy()

model = lm.OLS(y, x).fit()
alpha = model.params[0]
beta = model.params[1]

print(alpha, beta)
```

这样又得到一个完全不一样的alpha。

### 1.6.4 使用 np.polyfit

下面我们使用np.polyfit来计算这两个参数：

```python
mr = market_returns.to_numpy()
r = returns.to_numpy()
beta, alpha = np.polyfit(mr, r,deg=1)
print(alpha, beta)
```

可以看出，使用线性回归算法时，无论我们用的是np.polyfit，还是statsmodels，大家算出来的alpha和beta都是完全一致的。但我们按照
CAPM 公式、empyrical和线性回归，计算出来的alpha都不一致，问题在哪里？我们应该采信哪一种方案？

首先我们要注意，当我们比较两个策略、或者两个资产的alpha时，应该使用年化指标。我们计算出来的alpha（使用Jensen's
Alpha公式）是一个准年化指标（使用了实际一年241天的收益，而不是理论上的242天的收益）；而在empyrical方法中，我们使用了242天作为年化指标。而线性回归法算出的alpha，则是按日计算的alpha，并非年化指标。如果我们对它进行年化：

```python
daily_alpha = 0.0011741

annual_alpha = (1 + daily_alpha) ** 242 - 1
annual_alpha
```

此时我们得到的alpha为32.84%，就与empyrical得到的alpha（31.99%）相差无几了。

其次，empyrical在计算alpha时，是先求得每一天的超额收益，再在此基础上，进行累积计算得到的超额收益。而我们在计算时，是严格按公式，先求得资产的预期年化收益（包含了市场风险调整），再使用实际年收益减去这个预期年化收益得到的。这中间会产生一定的累积误差。

实际上，如果我们在计算资产的actual_return时，使用下面的方法来进行计算:

```python

# 在计算actual_return时，我们不用下面的方法
# actual_return = bars.iloc[ilast]["收盘"]/bars.iloc[0]["收盘"] - 1
# 而是使用：
actual_return = (1 + np.mean(returns))**242 - 1

alpha = actual_return - expected_return

```

此时得到的alpha就会与empyrical的结果比较接近了。

另外，我们在这里使用的年化指标是242，即认为一年有242个交易日。但不同的机构、不同的软件系统，在这个数据上可能各有各的看法。这也是影响Alpha最终数值的一个因素。如果我们拿自己算出的alpha与他人计算的alpha相比较，首先要注意，大家要使用同样的方法、同样的参数。

## 1.7 结论

四种方法，得到了两种不同的alpha，但结果都可以认为是正确的。在实际应用中，Alpha的绝对值实际上并不重要（因为它也并不是最终能实现的收益），和其它度量指标一样，我们看重的是策略指标在同等条件下的排名。在量化中，大家普遍使用empyrical包来进行指标计算，因此，我们就可以它计算出来的指标为准。
