
<!-- 标注数据量决定 -->

<!-- [Predicting Chinese stock market using XGBoost multi-objective optimization with optimal weighting](https://www.ncbi.nlm.nih.gov/pmc/articles/PMC10936758/)
-->

<!-- 我们构建的是一个选股模型，还是交易模型？实际上，我们应该构建两个模型，第一个是买入模型，它同时具有选股和择时能力；第二个是卖出模型，它有择时能力，只负责判断何时卖出
XGBoost很好，但LightGBM可能在内存占用、某些场景下的训练速度上会更优。这一章将介绍LightGBM如何使用。我们会给一个完整的例子，但不会涉及太多细节。这就是你常常在其他课程中会看到的那种内容。
-->

人工智能在近年来出现了迅猛的发展。从人工智能的层次来看，人工智能可以分为感知层、认知层、决策层和执行层。在不同的层次，先后发展出各种各样的人工智能模型。

感知层对应于人类的感知功能，比如视觉和听觉。人工智能的重大突破，正是率先从计算机视觉和听觉突破的。以Resnet[^kaiming]模型为代表的CNN模型，在2015年超越人类的视觉分辨能力。在OCR，语音转文本[^whisper]方面，在这一时期也有了重大突破，不过由于这一领域已经部分进入到认知领域了，所以部分场景下还落后于人类。作为语音转文本的逆转换，即文本转语音，近年来也发展迅速，当前最好的模型可能是Kokoro[^kokoro]等。

认知层是在感知层获取数据的基础上，对数据进行深入理解和分析，以挖掘数据中的内在含义和规律，实现目标识别、语义理解、知识推理等功能，使人工智能系统能够像人类一样对事物有更深入的认识和理解。2017年，Google Research团队首次提出了Transformer[^transformer]架构。次年，谷歌推出BERT，在多个自然语言处理基准测试中取得了当时的最优成绩。2020年，OpenAI发布GPT-3，展现出强大的语言生成能力，在众多自然语言处理任务上取得了优异成绩，能够完成文章生成、问答系统、机器翻译等多种任务，人类从此进入LLM（Large Language Models）模型时代。这些都是得益于Transformer架构的引入。

人工智能决策层主要负责根据认知层的分析结果进行判断并制定行动策略，主要范式是强化学习，重要的模型有Deep Q-Network等。2025年以来，DeekSeek在大语言模型的基础上，通过大规模强化学习训练出R1版本，在未使用监督微调（SFT）前置步骤的情况下展现出卓越的推理能力。在更早之前，基于强化学习的AlphGo和Alpha Zero模型已经在围棋上取得巨大成功，只不过受限于其决策能力无法迁移到其他领域，其应用受到了限制。

最终人工智能将实现具身化，广泛应用于工业生产、自动驾驶、家务杂活等领域。

就量化交易而言，可能值得探索的架构（模型）有以下几种：
1. CNN网络，用以k线模式识别。方法是将股票行情绘制为k线图，通过分类任务来确定k线形态，比如是否属于某种旗形整理。
2. Transformer架构。该架构的核心是自注意力机制。它可以并行地计算每个位置与其他所有位置之间的依赖关系，无论它们在序列中的距离有多远。在处理时间序列数据时，这意味着它能够同时考虑序列中的长期和短期依赖关系，而不像传统的循环神经网络（RNN）或卷积神经网络（CNN）那样有一定的局限性。例如在预测股票价格走势的时间序列中，它可以直接捕捉到相隔数月的价格数据之间的关联，而不仅仅是相邻时间步的数据。
3. 强化学习。强化学习可以用来训练一个用于交易的智能体。
4. LLM。大语言模型在新闻事件的情绪判断上有自己的优势。但我们也要注意，从事件的发生到被大众接收、最终转化为投资行为，是一个比较漫长的链条。我们既要足够早的发现事件并且埋伏进去，又要判断事件经过了充分传播，并且还要正确判断大众的情绪反应，这可能是一件很有难度的事。


在这一章中，我们将展望这些技术，讨论应用这些技术的可能性。当然，这些技术（比如CNN和Transformer）本质上都要求存在大量已标注数据，可能运用起来成本较高。此外，强化学习尽管对数据标注没有要求，但是，金融数据中存在的大量噪声，也可能使得训练变得十分困难。

!!! tip
    量化交易本质上是一种投机。任何投机行为都不可能成为主流范式 -- 换句话说，这个赛道不可能容纳很大的资金量。DeepSeek模型的背后，是一家名为幻方的私募。他们在2021年曾经达到了1000亿的资管规模（当是是小牛市）。这一规模在2024年6月已经下降到600亿左右。而据最新的报道（2025年初），他们的资管规模已经下降到了300亿左右。这一案例也说明，量化交易可能永远只是众多中小资金、甚至是散户的猎场。如果你试图用洲际导弹来控制这个猎场，这个猎场会是你的，但是，你得到的猎物是不够买到导弹的。

## 1. 如何获得免费的GPU算力

运行本章的示例往往需要比之前章节的示例大得多的算力，已经远远超过了课程环境所能提供的资源。我们将向大家介绍一些重要的免费GPU算力。

### 1.1. Google Colab

Google Colab大约在2018年起，就开始提供免费GPU算力。当前它能提供T4/V2-8这两种GPU/TPU。基础版每周可免费使用20~30个小时之间。每次对话最长可达12小时，但如果90分钟无操作，则会自动断开。

申请Google Colab需要有Google账号。当账号申请下来后，访问[Google Drive](https://drive.google.com/)并登录账号，通过下面的菜单，即可创建一个notebook：

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/google-colab.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

这个notebook默认将使用CPU。我们可以先在CPU环境下，以非常小的数据量调通程序，然后加大数据量并切换到GPU环境下进行训练。这个切换操作如下图所示：

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/colab-change-to-gpu.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

!!! tip
    T4 GPU是英伟达生成的基于Turing架构的GPU，配置有16GB显存，浮点性能达8.1T Flops。与之类比的是，NVIDIA的GTX 1070的单精度浮点性能为 6.5TFlops。V2-8是谷歌生产的TPU产品。它是专门为神经网络中常用的张量运算优化的一种硬件加速器，用于推理时，能耗比高于GPU。

在Colab环境中，你可以安装任意的Python库，比如Torch。

如果要使用自己的数据集，最方便的是将数据上传到Google Drive，然后通过Google Drive的接口，将数据集挂载到notebook中。

````markdown
```python
from google.colab import drive
drive.mount('/content/drive')
```
````
训练好的模型及经过处理的数据也可以保存到google drive中，再从drive中下载。

````markdown
```python
data.to_csv('/content/drive/MyDrive/processed_file.csv', index=False)
```
````

### 1.2. Kaggle

Kaggle也是大约从2018年起提供免费的GPU算力。现在，一个刚注册的用户可以使用的免费GPU时间大约固定在每周40小时。在注册Kaggle账号之后，一般就可以立即使用它的环境创建notebook，但如果要创建并使用数据集、使用GPU算力，一般还需要进行手机短信验证。

在Kaggle中，启用GPU计算的方法如下图所示：


<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/kaggle-switch-gpu.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

在kaggle中，使用自己的数据集，一般是先创建一个数据集，上传自己的数据到该数据集中，然后就可以在notebook中调用该数据集。也可以直接在notebook的右侧面板中，直接上传数据集。在kaggle中，连接后（或者上传的数据集）数据集都会挂载在/kaggle/input目录下。

生成的模型和数据集，将保存到/kaggle/working目录下（当然你得通过API写入文件）， 然后就可以在右侧面板的output下，找到该文件并下载。

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/kaggle-model-download.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

如果文件过大，有时候因下载速度过慢，导致中途失败，此时可以使用kaggle API来下载。kaggle API可以通过以下命令安装：

````markdown
```bash
pip install kaggle

kaggle datasets download -d your_username/your_dataset
```
````

在下载之前，需要在kaggle网站上创建相关的API Token。


## 2. CNN 价格预测

CNN是人类最早发明的、并且得到实用的深度学习网络。它的最核心的概念是通过卷积核在图像上滑动，从中提取出图像的特征线索，通过池化层来过滤掉不重要的噪声。通过多个卷积、池化和激活层的组合，在越靠近输入层的地方，CNN越注重局部和细节的特征；越靠近输出层的地方，CNN越能抽象出全局性的特征。在最后，通过一个全连接层，把提取的特征整合起来，就实现了分类或者回归的任务。

下面的图[^cs231n]清楚地显示了这一过程：

<div style='width:66%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/cnn-feature-hirearchy-visulization.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>


!!! tip
    在早期，人们往往把深度学习看成一种黑箱。但随着人们认识的加深，对神经网络的染色和切片技术也越来越成熟，我们现在已经可以很容易地看到CNN网络中每一层究竟学习到了什么特征。你可以访问[LiFeiFei](https://cs231n.stanford.edu/2021/slides/2021/lecture_14.pdf)的这篇论文，以了解更多技术细节。

在CNN中，有以下核心概念：
1. 卷积层(Convolutional Layer)：通过卷积核在输入数据上滑动，提取局部特征。
2. 激活函数 (Activation Function)：引入非线性，常用的有ReLU。
3. 池化层 (Pooling Layer)：如MaxPooling，用于下采样特征图，减少计算量。或者说，它提取最重要的特征，过滤掉了不重要的噪声。
4. 全连接层 (Fully Connected Layer)：将特征映射到输出空间。
5. 损失函数 (Loss Function)：衡量预测值与真实值之间的差异，常用的有MSE。这是我们之前讲解机器学习时讲过的概念了。
6. 优化器 (Optimizer)：如Adam，用于更新模型参数以最小化损失函数。

我们无法深入地介绍CNN，如果你对此感兴趣，可以参加斯坦福的CS231N公开课[^cs231n]。下面，我们就假定你已经熟悉了CNN的这些基本概念，直接讨论它在股价预测上的应用。

CNN最经典的应用就是进行图像模式识别。因此，我们很自然想到，应该可以将股票k线绘制出来，然后通过CNN网络来识别其中的横盘整理、三角形整理、艾略特波浪等形态。这种想法有一定的道理，但也有不小的难度：

1. 现阶段不存在这样一个开源的标注数据集，我们必须自己懂得这些理论，手工构造出一个数据集出来。
2. k线图不具有缩放不变性、旋转不变性和平移不变性等特征，因此，一般的图像数据增广方案无法使用，除了手工标注外，难以提供大规模的数据集。

在编写本课程之前，我已有一个比较好的三角形检测算法，可以为k线图进行自动标注。但是，在这种情况下，引入CNN来进行模式识别，显得多此一举。所以，在这里我们就给出一个简单的回归模型示例，主要是演示如何使用CNN模型，并以此为基础，讨论进一步改进的可能。

这个模型的原理是，以OHLC数据的过去win价格为特征，以T+1开盘价买入，T+2开盘价卖出得到的收益为目标，通过CNN模型进行训练和预测。

我们先给出示例代码：


<!--
```python
import pandas as pd
from zigzag import peak_valley_pivots

def find_runs(x):
    """Find runs of consecutive items in an array.

    Args:
        x: the sequence to find runs in

    Returns:
        A tuple of unique values, start indices, and length of runs
    """

    # ensure array
    x = np.asanyarray(x)
    if x.ndim != 1:
        raise ValueError("only 1D array supported")
    n = x.shape[0]

    # handle empty array
    if n == 0:
        return np.array([]), np.array([]), np.array([])

    else:
        # find run starts
        loc_run_start = np.empty(n, dtype=bool)
        loc_run_start[0] = True
        np.not_equal(x[:-1], x[1:], out=loc_run_start[1:])
        run_starts = np.nonzero(loc_run_start)[0]

        # find run values
        run_values = x[loc_run_start]

        # find run lengths
        run_lengths = np.diff(np.append(run_starts, n))

        return run_values, run_starts, run_lengths

def triangle_flag(df):
    """
    判断df所代表的股票是否存在三角形整理。

    首先找出压力线 resist 和支撑线 support。如果两线在当前时间点前n天和未来m天以内出现交点，则认为存在三角形整理。记resist的斜率为Sr, Support的斜率为Ss，则：

    Sr>0	Ss>0	|sr| > |ss|	    1	上升且发散三角
    Sr>0	Ss>0	|sr| < |ss|	    2	上升且收敛三角
    Sr>0    Ss<0	|sr| > |ss|	    3	发散偏上升三角
    Sr>0    Ss<0	|sr| < |ss|	    4	发散偏下降三角
    Sr<0	Ss>0	|sr| > |ss|	    5	下降且收敛三角
    Sr<0	Ss>0	|sr| < |ss|	    6	上升且收敛三角
    Sr<0	Ss<0	|sr| > |ss|	    7	下降且收敛三角
    Sr<0	Ss<0	|sr| < |ss|	    8	下降且发散三角

    参数：
        df - 个股行情数据
        n - 过去的天数
        m - 未来的天数
    返回：
        flag - 标记值
    """
    hthresh = df.high[-60:].pct_change().std() * 2
    lthresh = df.low[-60:].pct_change().std() * 2

    peaks = peak_valley_pivots(df.high.astype(np.float64), hthresh, -1 * hthresh)
    valleys = peak_valley_pivots(df.low.astype(np.float64), lthresh, -1 * lthresh)

    if len(peaks) == 0 or len(valleys) == 0:
        return 0, None, None
    
    peaks[0] = peaks[-1] = 0
    valleys[0] = valleys[-1] = 0

    pos_peaks = np.argwhere(peaks == 1).flatten()[-3:]
    pos_valleys = np.argwhere(valleys == -1).flatten()[-3:]

    if len(pos_peaks) < 2 or len(pos_valleys) < 2:
        return 0, None, None

    minx = min(pos_peaks[0], pos_valleys[0])
    y = df.high[pos_peaks].values
    p = np.polyfit(x=pos_peaks, y=y, deg=1)
    upper_trendline = np.poly1d(p)(np.arange(0, len(df)))

    y = df.low[pos_valleys].values
    v = np.polyfit(x=pos_valleys, y=y, deg=1)
    lower_trendline = np.poly1d(v)(np.arange(0, len(df)))

    sr, ss = p[0], v[0]

    flags = {
        (True, True, True): 1,
        (True, True, False): 2,
        (True, False, True): 3,
        (True, False, False): 4,
        (False, True, True): 5,
        (False, True, False): 6,
        (False, False, True): 7,
        (False, False, False): 8,
    }

    flag = flags[(sr > 0, ss > 0, abs(sr) > abs(ss))]

    return flag, upper_trendline, lower_trendline

def show_trendline(asset, df, resist, support, flag):
    desc = {
        1: "上升且发散三角",
        2: "上升且收敛三角",
        3: "发散偏上升三角",
        4: "发散偏下降三角",
        5: "下降且收敛三角",
        6: "上升且收敛三角",
        7: "下降且收敛三角",
        8: "下降且发散三角"
    }
    
    if isinstance(df, pd.DataFrame):
        df = df.reset_index().to_records(index=False)
        
    title=f"{asset} flag: {flag} - {desc[flag]}"
    cs = Candlestick(df, title=title, show_volume=False, show_rsi=False)
    cs.add_line("support", np.arange(len(df)), support)
    cs.add_line("resist", np.arange(len(df)), resist)
    cs.plot()


start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 29)
barss = load_bars(start, end, ("300814.XSHE", ))

for key, df in barss.groupby("asset"):
    df = df.reset_index().set_index("date")
    flag, resist, support = triangle_flag(df)
    if flag != 0:
        show_trendline(key, df, resist, support, flag)
```

-->

<Example id="cnn-predictor"/>

```python
import numpy as np
from numpy._typing import NDArray
from torch import nn
import os
from numpy.lib.stride_tricks import as_strided
import torch
from torch.utils.data import Dataset, DataLoader
import torch.optim as optim
from sklearn.metrics import mean_absolute_error

logger = logging.getLogger("cnn-predictor")

class FinancialDataset(Dataset):
    def __init__(self, features, labels):
        self.features = torch.FloatTensor(features)
        self.labels = torch.FloatTensor(labels)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.labels[idx]
        
class CNNPredictor():
    def __init__(self, channels: int=4, win: int=60):
        super().__init__()

        self.channels = channels
        self.win = win

        self._model_ = None
        self._name_: str | None = None
        self._desc_: str | None = None

    def build_model(self):
        model = nn.Sequential(
            nn.Conv1d(self.channels, 32, kernel_size=8),
            nn.ReLU(),
            nn.MaxPool1d(2),

            nn.Conv1d(32, 64, kernel_size=5),
            nn.ReLU(),
            nn.MaxPool1d(2),

            nn.Conv1d(64, 64, kernel_size=3),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),  # 这一步会将每个样本压缩成一个特征向量
        )

        fc = nn.Sequential(
            nn.Flatten(),  # 添加展平层
            nn.Linear(64, 16),
            nn.ReLU(),
            nn.Linear(16, 1)
        )

        self._model_ = nn.Sequential(model, fc)



    @property
    def model(self):
        return self._model_

    def save(self, path: str, auto_rename: bool = True, name: str | None = None):
        if self._model_ is None:
            raise ValueError("请先调用fit方法训练模型")

        if not os.path.exists(os.path.expanduser(path)):
            raise ValueError("保存路径不存在")

        name = name or self._name_
        if name is None:
            raise ValueError("请指定保存文件名")

        file = os.path.join(path, f"{name}.pkl")
        if os.path.exists(file):
            if auto_rename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"{name}_{timestamp}.pkl"
                file = os.path.join(path, name)
                assert not os.path.exists(file)
                logger.warning("保存路径已存在同名文件，已自动重命名为%s", name)
            else:
                raise ValueError("保存路径已存在同名文件，请指定指定文件名")

        with open(file, "wb") as f:
            pickle.dump(
                {
                    "model": self._model_,
                    "name": self._name_,
                    "desc": self._desc_,
                    "save_time": datetime.datetime.now(),
                },
                f,
            )

    def load(self, file_path: str):
        with open(file_path, "rb") as f:
            data = pickle.load(f)
            self._model_ = data["model"]
            self._name_ = data["name"]
            self._desc_ = data["desc"]
            logger.info("加载模型%s成功。", self._name_)

    def rolling_time_series(self, df: pd.DataFrame, win: int) -> pd.DataFrame:
        if len(df) < win:
            nans = np.full((0, win), np.nan)
            return pd.DataFrame(nans, index=df.index)
        
        ts = df.values
        stride = ts.strides
        shape = (len(ts) - win + 1, win)
        strides = stride + stride
        df = pd.DataFrame(as_strided(ts, shape, strides), index = df.index[win-1:])

        # 简单归一化
        return df.div(df.iloc[:,-1], axis=0)

    def train_test_split(
        self,
        data: NDArray,
        target: pd.Series,
        cuts=(0.7, 0.2),
        bs=64
    ) -> Tuple[DataLoader, DataLoader, DataLoader]:
        n = len(data)
        itrain = int(n * cuts[0])
        ival = itrain + int(n * cuts[1])

        train_data = FinancialDataset(data[:itrain], target.iloc[:itrain])
        val_data = FinancialDataset(data[itrain:ival], target.iloc[itrain:ival])
        test_data = FinancialDataset(data[ival:], target.iloc[ival:])

        return DataLoader(train_data, batch_size=bs, shuffle=False), DataLoader(val_data, batch_size=bs, shuffle=False), DataLoader(test_data, batch_size=bs, shuffle=False)
    

    def features_and_labels(self, barss):
        labels = barss["price"].unstack().pct_change().shift(-1).stack()

        # common index
        ci = None

        channels = ["open", "close", "high", "low"]
        data = [[]] * len(channels)
        for i, col in enumerate(channels):
            df = barss[col].unstack().ffill().stack()
            # df_ = (df/df.iloc[-1, :]).dropna().stack()

            rolled = df.groupby("asset").apply(
                lambda x: self.rolling_time_series(x, self.win)
            ).droplevel(0)

            if ci is None:
                ci = labels.index.intersection(rolled.index)

            data[i] = rolled.loc[ci]

        labels = labels.loc[ci]
        data = np.stack([*data], axis=2)
        # cnn 要求的shape是（batch_size, channels, seq_len）
        data = np.transpose(data, (0, 2, 1))
        return data, labels
    

    def train(self, barss, epochs=50, batch_size=64,patience=5):
        features, labels = self.features_and_labels(barss)

        train_data, val_data, test_data = self.train_test_split(
            features, labels, bs=batch_size
        )

        self.build_model()

        # 定义损失函数和优化器
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self._model_.parameters(), lr=0.001)

        best_val_loss = float('inf')
        early_stop_counter = 0

        # 训练模型
        for epoch in range(epochs):
            self._model_.train()
            running_loss = 0.0
            for batch_features, batch_labels in train_data:
                optimizer.zero_grad()
                # 展平卷积层的输出
                outputs = self._model_(batch_features).view(batch_features.size(0), -1)
                loss = criterion(outputs.squeeze(), batch_labels.float())
                loss.backward()
                optimizer.step()
                running_loss += loss.item()

            # 打印每个epoch的损失
            print(f"Epoch [{epoch+1}/{epochs}], Loss: {running_loss/len(train_data):.4f}")

            # 验证模型
            self._model_.eval()
            val_loss = 0.0
            val_preds = []
            val_true = []
            with torch.no_grad():
                for val_features, val_labels in val_data:
                    val_outputs = self._model_(val_features).view(val_features.size(0), -1)
                    val_loss += criterion(val_outputs.squeeze(), val_labels).item()
                    val_preds.extend(val_outputs.squeeze().cpu().numpy())
                    val_true.extend(val_labels.cpu().numpy())

            val_loss /= len(val_data)
            val_mape = mean_absolute_error(val_true, val_preds)
            print(f"Validation Loss: {val_loss:.4f}, Validation MAPE: {val_mape:.4f}")

            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                early_stop_counter = 0
            else:
                early_stop_counter += 1
                if early_stop_counter >= patience:
                    print(f"Early stopping at epoch {epoch+1}")
                    break

        # 测试模型
        self._model_.eval()
        test_loss = 0.0
        test_preds = []
        test_true = []
        with torch.no_grad():
            for test_features, test_labels in test_data:
                test_outputs = self._model_(test_features).view(test_features.size(0), -1)
                test_loss += criterion(test_outputs.squeeze(), test_labels).item()
                test_preds.extend(test_outputs.squeeze().cpu().numpy())
                test_true.extend(test_labels.cpu().numpy())

        test_loss /= len(test_data)
        test_mape = mean_absolute_error(test_true, test_preds)
        print(f"Test Loss: {test_loss:.4f}, Test MAPE: {test_mape:.4f}")

    def predict(self, barss):
        if self._model_ is None:
            raise ValueError("请先调用train方法训练模型")

        # 准备数据
        features, _ = self.features_and_labels(barss)
        dataset = FinancialDataset(features, np.zeros(len(features)))
        dataloader = DataLoader(dataset, batch_size=64, shuffle=False)

        # 设置模型为评估模式
        self._model_.eval()

        # 禁用梯度计算
        with torch.no_grad():
            predictions = []
            for batch_features, _ in dataloader:
                outputs = self._model_(batch_features).view(batch_features.size(0), -1)
                predictions.extend(outputs.squeeze().cpu().numpy())

        return np.array(predictions)

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12,29)
np.random.seed(78)

barss = load_bars(start, end, 100)
predictor = CNNPredictor(win = 60)
predictor.train(barss)
```

实现方案大致上涉及以下几个步骤：

### 2.1. 如何为训练提供数据？

在CNN模型训练中，训练数据集有可能很大，因此，我们需要一种能以迭代方式，分批次提供训练数据集的数据结构。在torch中已经提供了一个名为dataset的数据结构，但我们要重载它。

在示例中，实现代码如下所示：

```python
class FinancialDataset(Dataset):
    def __init__(self, features, labels):
        self.features = torch.FloatTensor(features)
        self.labels = torch.FloatTensor(labels)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.labels[idx]
```

我们要注意的是，在之前的机器学习课程中，我们一般使用DataFrame或者Numpy来保存特征数据，但在torch中，我们需要使用一种名为Tensor的数据结构来保存特征数据和标签数据。Tensor是一个数学概念，意思是张量，类似于多维数组。

### 2.2. 如何构建特征数据？

不同的模型应该有自己的答案。在[](#cnn-predictor)中，我们分别提取了OHLC四个维度上，长度达60个bar的数据作为特征。为了保证不同asset之间的价格在量纲上一致，我们将每一组的数据，都除以了最后一个bar的价格，因此，我们得到的每一维的特征数据类似于：


<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/rolled-close-price.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

在提供给cnn进行训练时，我们需要将这四个维度组合起来，并且形状符合卷积层的要求。而卷积层的输入要求是(batch_size, channels, *data_shape)，所以，我们在第161行~163行进行了重塑：

```bash
data = np.stack([*data], axis=2)
# cnn 要求的shape是（batch_size, channels, seq_len）
data = np.transpose(data, (0, 2, 1))
```

重塑的结果，是一个维度为(x, 4, 60)的数组。其中x取决于样本数、日期和特征数（这里是60）。

!!! tip
    batch_size与构建特征时的x相对应。在进行训练时，DataLoader会自动将数据分批，并返回一个batch_size大小的数据给cnn进行训练，所以，卷积层输入的第一维的大小是batch_size，而不是这里的x。分批使得模型对显存的要求降低了，同时也能减轻过拟合。

最后，我们在执行train_test_split时，将特征和标签数据转换为FinancialDataset，再通过DataLoader进行封装，以实现分批向CNN提供数据的功能。


### 2.3. 如何定义模型？

在[](#cnn-predictor)中，我们使用了三个卷积层。其中第一个卷积层的卷积核大小为8，第二个卷积核大小为5，第三个卷积核大小为3。在这里我们有意识地使用了Fibonacci数列，以期能与一些交易员的习惯相一致。当然，最终应该使用什么样的卷积核，可能要依赖于调优结果。

我们使用的是一维卷积。考虑到有时候股价会在接近前高时遇到压力下行，在接近前低时遇到支撑反弹，所以，使用一个4维卷积也似乎是合理的，不过这样的网络就更加复杂了。在我们的例子中，我们使用的是4个通道的一维卷积，这样处理起来简单了，但是，根据卷积网格的原理，每个通道的特征提取都是独立的，这样生成的网络，显然会丢失一些信号。

Conv1d的构造函数签名如下：

```python
class Conv1d(
    in_channels: int,
    out_channels: int,
    kernel_size: _size_1_t,
    stride: _size_1_t = 1,
    padding: _size_1_t | str = 0,
    dilation: _size_1_t = 1,
    groups: int = 1,
    bias: bool = True,
    padding_mode: str = "zeros",
    device: Unknown | None = None,
    dtype: Unknown | None = None
)
```

所以，在第37行代码中：

```python
model = nn.Sequential(
    nn.Conv1d(self.channels, 32, kernel_size=8),
    nn.ReLU(),
    nn.MaxPool1d(2),
    ...
```

我们告诉第一个卷积层，输入将有4个channels，而输出则有32个channels。这也是构造第二个卷积层时，第一个参数的由来。第二个卷积层将生成64个特征，或者说有64个输出channels，因此，第三个卷积层的输入channels也要指定为64。

最后，我们给模型添加了一个全连接层(50-56行)。它的作用是将特征映射到目标空间中。

### 2.4. 训练

在torch中，train（第185行）方法的作用是将模型设置为训练模式（与之对应的，是通过eval设置为评估模式）。在训练模式下，Dropout层会随机丢弃一部分神经元，以防止过拟合。而在评估模式下，Dropout层不会丢弃任何神经元，而是保持所有神经元激活。此外，还会启用BatchNorm层。

zero_grad的用途是清空优化器中的梯度缓存。在每个batch开始时，需要清空之前的梯度，以避免梯度累积。如果不清空，梯度会累加，导致模型训练不稳定。

第190行的作用是进行前向传播，计算模型的输出。最后的.view的作用是将输出调整为适合计算损失的形状（shape）。

criterion是计算损失。然后，我们对刚刚计算出来的loss进行反向传播，在这个过程中，将计算梯度。这些梯度将存储在模型参数的.grad属性中。

optimizer.step()的作用是更新模型参数，使得模型在训练过程中，能够更好的拟合训练数据。

接下来（第200行），在每一个epoch结束后，我们进行一次验证。首先需要通过`with torch.no_grad()`来禁用梯度计算，以提升性能。因为在验证阶段，是不需要利用梯度的。

其他的代码就比较容易理解了。这里还用到了early stopping，这个概念我们在之前的课程中也介绍过了。

在所有epoch训练完成（或者早停）之后，我们通过测试集对模型进行了最终的评估。由于在训练过程中，完全没有利用任何test数据，所以，如果测试阶段的性能与验证阶段的性能相差不大，就说明该模型有较好的泛化能力。

### 2.5. 生产和部署

训练好的模型通过save接口保存之后，就可以在生产环境下运行。在生产环境下运行模型，我们主要是调用predict方法。它使用的函数都在train方法中出现过，这里我们就不再多赘述了。


### 2.6. CNN的原理与性能优化

在我们这里给出的示例中，最终的MAPE指标大约在1.5%左右。在<ref>[第19课](19.md)</ref>中我们分析过，这样的预测结果价值不大，因为每日的平均随机波动就在1%左右，模型只要使用前一日的收盘价作为预测值，就能实现MAPE在1.5%左右了。

这个指标有可能得到提升吗？

在时间序列数据上，一维卷积核沿着时间轴方向进行滑动。例如，对于一个长度为 N 的时间序列，若卷积核大小为 k，那么卷积核会从时间序列的第 1 个元素开始，每次移动一个时间步长，依次与时间序列中的 k 个连续元素进行卷积操作，直到遍历完整个时间序列。卷积核中的每个参数都可以看作是对时间序列中不同时间点数据的权重。在卷积操作过程中，卷积核与时间序列数据进行点积运算，相当于对时间序列中局部的一段数据进行加权求和，从而提取出这一局部区域内的特征。

比如，第一个卷积层中的卷积核可能会对时间序列中的上升趋势、下降趋势、周期性等特征敏感，通过调整卷积核的参数，可以捕捉到不同类型的局部特征。第二个卷积层则可能识别出多个长升趋势和下降趋势的组合，从而有可能识别出波浪理论中的一些模式。

所以，通过CNN来进行模型识别是可行的，主要难度在数据标注上。

如果是用来预测股票价格呢？这可能就需要我们理解自己的定价模型是怎样的。如果我们自己都不清楚股价是如何确定的，那么任何非通用智能的AI模型都不可能做到这一点。

我们在前面（包括量化24课）已经提到过股价短期定价的一些机制，这里小结一下：

1. 动量机制使得我们能够预测下一个时间点的价格。它要么是直线、要么是二次曲线、要么是某种指数。
2. 反转机制确定了短期价格的顶和底。这可以通过RSI、整数关口、均线压力、三角形整理、箱形整理等机制来确定。

但是，这些定价机制，本身类似于表格数据，更适合使用梯度提升决策树。似乎很难通过卷积和池化来发现或者实现这样的规律。更何况，深度学习在训练前，数据必须进行量纲对齐，这也会抑制像整数关口这样的定价机制发挥作用。

总得来说，**CNN网络的特长是层次化抽象和模式规纳**，其工作机制与人类视觉认知的抽象过程高度相似。深刻了解这一点，你才能真正用好它。

!!! tip
    读者如果对CNN网络感兴趣，推荐阅读李飞飞的自传《我看见的世界》。它讲述了李飞飞的成长经历，也以双线叙事的维度，介绍人工智能，特别是计算机视觉，是如何看清世界的。

## 3. Transformer

股价数据是一种时间序列数据，理论上，适合这种数据的神经网络应该是RNN及其变体，比如 LSTM、GRU等。但是，它们在处理长序列时存在梯度消失或梯度爆炸的问题，难以捕捉到长距离的依赖关系，此外还存在无法并行计算的性能问题。而 Transformer 架构通过自注意力机制（Self - Attention），可以直接计算序列中任意两个位置之间的依赖关系，能够更好地捕捉股价数据中的长期依赖，例如过去几个月甚至几年的市场动态对当前股价的影响。

Transformer 架构主要由编码器（Encoder）和解码器（Decoder）组成，在股价预测中，通常只使用编码器部分。以下是主要组件及其作用：
1. 输入嵌入（Input Embedding）
将输入的股价数据（如开盘价、收盘价、成交量等）转换为低维的向量表示，以便后续的处理。
2. 位置编码（Position Encoding）
由于 Transformer 架构本身不具备处理序列顺序的能力，因此需要通过位置编码来为每个输入位置添加位置信息，使得模型能够区分不同位置的元素。
3. 自注意力机制（Self - Attention）
计算序列中每个位置与其他所有位置之间的相关性，通过加权求和的方式得到每个位置的输出，从而捕捉序列中的依赖关系。
4. 前馈神经网络（Feed - Forward Network）
对自注意力机制的输出进行非线性变换，增加模型的表达能力。
5. 编码器层堆叠
将多个编码器层堆叠在一起，以提取更高级的特征表示。

```python
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.metrics import mean_absolute_error

# 定义 Transformer 编码器模型
class TransformerEncoderModel(nn.Module):
    def __init__(self, input_dim, d_model, nhead, num_layers, output_dim):
        super(TransformerEncoderModel, self).__init__()
        self.embedding = nn.Linear(input_dim, d_model)
        self.pos_encoder = PositionalEncoding(d_model)
        encoder_layers = nn.TransformerEncoderLayer(d_model, nhead)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layers, num_layers)
        self.decoder = nn.Linear(d_model, output_dim)

    def forward(self, src):
        batch_size, seq_len, input_dim = src.shape
        # 调整形状以适合线性层
        src = src.view(batch_size * seq_len, input_dim)
        src = self.embedding(src)
        # 恢复形状
        src = src.view(batch_size, seq_len, -1)
        src = self.pos_encoder(src)
        output = self.transformer_encoder(src)
        output = self.decoder(output[:, -1, :])  # 取最后一个时间步的输出
        return output

# 定义位置编码类
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe.unsqueeze(0))

    def forward(self, x):
        x = x + self.pe[:, :x.size(1)]
        return x

def train_test_split(X, y, seq_len, cuts=(0.7, 0.2)):
    train_size, val_size = cuts
    # train test split
    total = len(X)
    X = X[["open", "high", "low", "close", "volume"]]
    train_pos = int(total * train_size)
    val_pos = train_pos + int(total * val_size)

    assert train_pos > seq_len

    X_train = []
    y_train = []
    for i in range(seq_len, train_pos):
        data = X.iloc[i-seq_len:i]
        # normalize
        data = data/data.iloc[-1]
        X_train.append(data.values)
        y_train.append(y.iloc[i])

    X_val = []
    y_val = []
    for i in range(train_pos, val_pos):
        data = X.iloc[i-seq_len:i]
        # normalize
        data = data/data.iloc[-1]
        X_val.append(data.values)
        y_val.append(y.iloc[i])

    X_test = []
    y_test = []
    for i in range(val_pos, total):
        data = X.iloc[i-seq_len:i]
        # normalize
        data = data/data.iloc[-1]
        X_test.append(data.values)
        y_test.append(y.iloc[i])

    X_train = np.array(X_train)
    y_train = np.array(y_train)
    X_val = np.array(X_val)
    y_val = np.array(y_val)
    X_test = np.array(X_test)
    y_test = np.array(y_test)

    return (torch.tensor(X_train, dtype=torch.float32),
            torch.tensor(y_train, dtype=torch.float32).unsqueeze(1),
            torch.tensor(X_val, dtype=torch.float32),
            torch.tensor(y_val, dtype=torch.float32).unsqueeze(1),
            torch.tensor(X_test, dtype=torch.float32),
            torch.tensor(y_test, dtype=torch.float32).unsqueeze(1))
    
def eval(model, X_test, y_test):
    with torch.no_grad():
        outputs = model(X_test)
        mae = mean_absolute_error(y_test.numpy(), outputs.numpy())

        df = pd.DataFrame({
            "actual": y_test.numpy().flatten(),
            "predicted": outputs.numpy().flatten()
        })

        display(df)
        
        print(f'MAE: {mae:.02%}')

def train(start, end, symbol, epochs = 100):
    barss = load_bars(start, end, (symbol, ))
    bars = barss.xs(symbol, level=1)

    # 提取收盘价
    target = bars.price.pct_change().shift(-1).dropna()

    X_train, y_train, X_val, y_val, X_test, y_test = train_test_split(bars[:-1], target, seq_len=30)
    print(X_train.shape, X_val.shape, X_test.shape)

    # 初始化模型
    input_dim = X_train.shape[2]
    d_model = 128
    nhead = 8
    num_layers = 2
    output_dim = 1
    model = TransformerEncoderModel(input_dim, d_model, nhead, num_layers, output_dim)

    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    # 训练模型
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X_train)
        loss = criterion(outputs, y_train)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            val_outputs = model(X_val)
            val_loss = criterion(val_outputs, y_val)
            mae = mean_absolute_error(y_val.numpy(), val_outputs.numpy())
        
        print(f'Epoch {epoch + 1}/{epochs}, Train Loss: {loss.item():.3f}, Val Loss: {val_loss.item():.3f}, Val MAE: {mae:.02%}')

    eval(model, X_test, y_test)

train(datetime.date(2023, 1, 1), datetime.date(2023, 12, 29), "000001.XSHE")
```

在示例中，我们使用了全部OHLC特征和成交量特征，特征长度为30，最终得到的MAPE大约在5%左右。这是一个比前面的例子还要差的一个数据。在我们的示例中，使用的是价格到涨跌幅的预测。我们可以通过增加更多的层来改善，也可以加入其他特征来改善这一结果。

## 4. Reinforcement Learning

强化学习是机器学习中的一个领域，强调智能体（agent）如何在环境中采取一系列行动，以最大化累积奖励。智能体在环境中不断进行试验和探索，通过与环境的交互获得奖励反馈，从而学习到最优的行为策略。例如，机器人在复杂环境中学习如何行走、游戏玩家训练游戏角色达到高分等都是强化学习的应用场景。

我们之前介绍的各种算法，无论是机器学习的还是人工智能的，多数是一种预测选股模型。然而，当我们选出正确的标的之后，也可能由于突然发生的外界影响、主力资金操作上的随意性，导致标的并不一定能立刻兑现我们的预测，并且还有可能在等待过程中，出现新的情况，导致之前的预测失效。

这些都是金融市场具有高度的动态性和不确定性的表征。正因为如此，很多时候，预测不如应对，因此，研究强化学习在量化交易中的应用，似乎有着其天然的理由。

强化学习的主要思想是试错学习机制：智能体（Agent）在环境（Environment）中采取行动（Action），根据反馈的奖励（Reward）调整策略（Policy）。这一思想大概来自于人类训练动物。

强化学习中，有以下核心概念：

1. 状态空间：它是环境中所有可能状态，在量化交易中，可以看成是价量、指标、持仓、市场氛围等。
2. 动作空间：智能体可执行的操作（买入/卖出/持有、仓位调整等）
3. 奖励函数：即时反馈信号（如单步收益、风险调整后收益等）

此外还有状态转移概率，折扣因子等。

在设计上，可以分为环境、状态、智能体等模块。在模型中，可以生成多个智能体实例，每一个实例相当于一个交易员，他们对盘面有不同的理解，也有不同的交易法则。比如，有的交易员会选择5日均线以上持有，跌破5日均线就卖出；有的会在走出某种形态后加仓；有的会根据技术指标来进行操作。最终，由强化学习算法来决定每个交易员当前的仓位应该是多少。拿到这个仓位比之后，每个交易员（智能体）就应该根据这个仓位来决定自己是买入还是卖出。

实现强化学习，一般需要借助一些Python框架。在金融领域，知名的强化学习框架有FinRL。与通用强化学习框架相比，主要是提供了一些金融领域独有的奖励函数（比如夏普率，组合收益等）、一些数据预处理工具和环境设定等。

## 5. 其他重要智能算法
### 5.1. kalman filter
卡尔曼滤波器（Kalman filter）是一种利用线性系统状态方程，通过系统输入输出观测数据，对系统状态进行最优估计的算法。在一些量化交易相关的文章中，我们也常常看看到这种算法的应用。

卡尔曼滤波器就像是一个聪明的 “数据处理小助手”。它能根据之前的信息和现在新观测到的数据，不断地去推测和更新一个系统现在的状态。比如在追踪一个移动的物体时，它能把物体之前的位置、速度等信息，和现在传感器探测到的信息结合起来，算出物体最可能的当前位置和状态，而且能把各种干扰和误差尽量减小，让结果更准确。

卡尔曼滤波器看上去与现在的一些人工智能算法有类似之处。但它们本质上有重要不同：卡尔曼滤波器是基于线性系统和高斯噪声假设的一种经典算法，有明确的数学模型和固定的计算流程。只有我们确定某个金融工程确实符合这一条件时，才应该使用它。它的优点是计算相对简单，可解释性较好。

### 5.2. Genetic Algorithm

遗传算法（Genetic Algorithm）是一种借鉴生物界自然选择和遗传机制的随机搜索算法，它的原理和操作过程可以用以下通俗的方式来理解：

1. 种群初始化：想象有一个很大的 “生物种群”，这里的 “生物” 其实是各种可能的问题解决方案。在量化交易中，一个 “生物” 可以是一种特定的交易策略，比如什么时候买入股票、什么时候卖出股票等规则的组合。一开始，这些策略都是随机生成的，就像自然界中生物的基因在一开始也是多种多样随机存在的。
2. 适应度评估：每个 “生物” 都有一个衡量它好坏的标准，叫做适应度。在量化交易里，适应度可以是这个交易策略在过去一段时间内赚了多少钱，或者它的风险控制得有多好等指标。赚的钱越多、风险越小，适应度就越高，就好像在自然界中，越能适应环境的生物生存能力越强一样。
选择：就像自然界中适应环境的生物更容易生存和繁衍一样，在遗传算法中，适应度高的 “生物”，也就是好的交易策略，有更大的概率被选择出来，去产生下一代。
3. 交叉：被选择出来的 “生物” 之间会进行 “交配”，也就是交叉操作。比如有两个交易策略，一个在上涨行情中买入股票的时机把握得很好，另一个在下跌行情中卖出股票的时机把握得很好，它们进行交叉，就可能产生一个新的策略，这个新策略既能在上涨时很好地买入，又能在下跌时及时卖出，综合了两个 “父母” 策略的优点。
4. 变异：在产生新的 “生物” 过程中，还会有一定概率发生 “变异”。就像自然界中生物的基因会发生突变一样，交易策略中的某些规则也可能会随机地发生变化，比如原本是上涨 5% 买入，可能变异后变成上涨 3% 就买入了。这种变异有可能产生更好的策略，也有可能产生更差的策略，但正是这种多样性的变化，让算法有机会找到更优的解决方案。
5. 迭代：通过不断地进行选择、交叉、变异操作，产生一代又一代的 “生物”，也就是新的交易策略。每一代的整体适应度都可能比上一代更好，就像生物在不断进化适应环境一样，最终找到一个或多个比较优的交易策略。


在量化交易中，我们也常常使用遗传算法来解决**复杂的优化问题**。比如，遗传算法可以帮助找到最优的交易参数组合。比如在一个均线策略中，均线的周期是多少、买卖的阈值是多少等参数，都可以通过遗传算法来寻找最优值，让策略在历史数据上的表现达到最佳，提高盈利的可能性。我们还可以把投资组合中的不同股票的组合和资金分配比例看作是 “生物” 个体，通过不断进化，找到风险调整后收益最高的投资组合方案，实现资产的合理配置。

与其他优化方法相比，它具有全局优化、多目标优化和非线性问题处理（即可用以无法用梯度下降等传统方法进行优化的场合）等优点。

常用的框架有[DEAP](https://github.com/DEAP/deap)，它在github上有6k星标。此外，[pyGAD](https://github.com/ahmedfgad/GeneticAlgorithmPython)也是常用库之一，它在github上有2k星标，并且社区也在积极维护中。

## 6. 结束语

到这里为止，我们的课程就结束了。我们学习了因子分析、也掌握了许多鼎鼎有名的因子和算法，我们还获得了非常深入的梯度决策提升树的知识，掌握了如何通过lightGBM来构建回归和分类任务。最后，我们还探讨了更先进的人工智能算法在量化交易领域运用的可能性。

现在，我们似乎可以自豪地说，十年磨一剑，霜刃未曾试。今日把示君，谁有不平事？

有一群猎人，为争夺一片猎场打得不可开交。这个猎场里物产丰富，有兔子、野猪、各种鹿。有的猎人只有普通的猎枪，有的猎人则拥有AK47。但有一天，有一个猎人租来了一支军队，飞机大炮管够。最终，他得到了猎场，但一年的收获也就是几头野猪，几张虎皮而已。几年之后，他也退出了这场竞争。

量化交易就是这样一个猎场。那个租来军队的猎人，也许就叫梁文锋[^wenfeng]。这里养不起一支军队，但如果你有一支AK47，也可以让自己的日子过得很舒服。

量化交易只是一种投机。我们可以不在乎它是否有社会价值，但投机本身并不创造价值，也因而无法分享到更多的价值。如果你管理着很大的一笔资金，要想实现它的保值增值，惟一的途径，就是把它投资到有利于人类生产效率提高的技术和行业中去，分享社会进步产生的红利。



## 7. 拓展阅读

[poloclub的博客](https://poloclub.github.io/cnn-explainer/)通过网页提供了一个名为CNN Explainer 的工具，通过可视化的方式，展示了CNN的工作原理。下面这张图就来自于该工具生成的动态图：

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/poloclub-cnn-explainer.gif'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>


## 8. Footnotes

[^kaiming]: 2015 年，微软研究院的何恺明等人提出的 ResNet 在 ImageNet 大规模视觉识别挑战赛（ILSVRC）中以 3.57% 的错误率大幅领先其他参赛队伍，并且首次将错误率降至低于人类水平（约为 5.1%）

[^transformer]: 2017年，Google Research团队在论文《Attention is All You Need》中首次提出 Transformer 架构，并在次年推出BERT（Bidirectional Encoder Representations from Transformers）

[^whisper]: 2022年，OpenAI 发布了自动语音识别 Whisper 模型，该模型支持超过99种语言，并且已[开源](https://github.com/openai/whisper)（76k stars）。在此基础上，社区开发出许多实用工具，比如[whisper.cpp](https://github.com/ggerganov/whisper.cpp)，它获得了超过38k的stars，并在性能上更有优势。

[^kokoro]: Huggingface上有一个[TTS leaderboard](https://huggingface.co/spaces/TTS-AGI/TTS-Arena)，目前表现最好的开源模型是[Kokoro v1.0](https://github.com/hexgrad/kokoro)。在中文模型中，表现最好的可能是[MaskGCT](https://github.com/open-mmlab/Amphion/blob/main/models/tts/maskgct/README.md)

[^cs231n]: 这张图来自斯坦福[CS231n课程](https://cs231n.github.io/convolutional-networks/)。CS231N是网上最著名的公开课之一，在Youtube上播放超过2000万次，[github](https://github.com/cs231n/cs231n.github.io)上星标超过10.3k。

[^wenfeng]: 梁文锋，幻方量化创始人。在2021年，公司资产管理规模达到了1000亿。2023年5月起，宣布做通用人工智能，7月正式创建深度求索人工公司。在2024年，幻方量化资产规模下降较多，有传闻称已不足300亿。

<!--https://www.youtube.com/watch?v=HjScAwMelXQ  -->

<!--CNN架构图-->
<!--https://i0.wp.com/developersbreach.com/wp-content/uploads/2020/08/cnn_banner.png?fit=1400%2C658&ssl=1-->
