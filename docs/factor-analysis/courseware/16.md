# 16. 模型优化

在机器学习中，模型优化是一个至关重要的环节。优化的目标是通过调整模型的参数，使其在训练数据上表现更好，同时在未见过的测试数据上也能保持良好的泛化能力。模型优化不仅涉及到选择合适的优化算法，还包括模型结构的设计、超参数的选择和调优等多个方面。

在量化交易中，因为金融市场的数据具有高噪声、非线性和动态变化的特点，这使得模型优化变得更加复杂和具有挑战性。比如，交叉验证是机器学习中常见的优化方法，但是，在量化交易领域，由于我们处理的数据往往具有时间矢量的单向性，所以，这种方法就会引起未来数据的问题。因此，在量化领域，交叉验证就必须选择别的方法。

## 1. 训练算法优化

模型优化有多个目标和方向，可以是加速训练过程，提高模型性能和泛化能力。从使用的技术和切入点来看，可以是参数优化算法，评估优化和超参数调优。

本课程中，我们的重点会放在评估优化和超参数调优这一类偏向应用层面的优化技术上。尽管我们不需要去发明算法，但是，一些模型提供了参数供我们选择优化算法，所以，我们也需要大致了解常见的优化算法的特点和适用范围，不然，我们也不能算是学会了使用这些模型。

在机器学习中，参数优化的目标是通过最小化损失函数来找到模型的最佳参数。损失函数衡量了模型预测值与真实值之间的差异，优化过程旨在找到使损失函数最小的参数组合。

在优化过程中，常常涉及到这些概念，需要大家记忆：

1. 梯度：梯度是一个向量，表示函数在某一点处的最大变化率方向。在多维空间中，梯度的每个分量表示函数在该维度上的偏导数。梯度的方向指向函数值增加最快的方向，负梯度方向则指向函数值减少最快的方向。
2. 步长（学习率）：步长（也称为学习率）是优化过程中每次更新参数的幅度。步长的选择对优化过程至关重要，步长过大可能导致优化过程不稳定，步长过小则会导致收敛速度慢。
3. 局部极小值和全局极小值：在优化过程中，可能存在多个局部极小值，这些点的损失函数值小于周围的点，但不一定是最小的。全局极小值是整个搜索空间中损失函数值最小的点。优化算法的目标是找到全局极小值，但在实际应用中，找到一个好的局部极小值也可能足够。
4. 梯度消失和梯度爆炸：在深度学习中，梯度消失和梯度爆炸是常见的问题。梯度消失是指在反向传播过程中，梯度逐渐变小，导致模型难以训练；梯度爆炸则是指梯度逐渐变大，导致模型参数更新过大，训练过程不稳定。

!!! tip
    我们在前面的课程中提及过，特征（因子）之间的量纲相差巨大时，其中量纲大的一些特征就会主导训练。原因就在于学习率是一个全局参数，它对所有的特征都适合。因此，如果特征之间的量纲不一致，就会出现学习率步长对一些特征来说太大，优化过程不稳，而对另一些特征来说，步长太小，收敛速度太慢。

要深入地理解这些概念，需要学习导数、梯度和反向传播，这里我们就省略了。但上面这些概念，是我们优化调参中需要使用或者遇到的。

根据优化过程中使用的梯度信息，优化算法可以分为一阶优化算法、二阶优化算法和零阶优化算法。

### 1.1. 一阶优化算法

梯度下降（Gradient Descent, GD）是最基本的优化算法。其核心思想是沿着负梯度方向逐步更新参数，以最小化损失函数。它又有三种主要变体，即批量梯度下降（Batch Gradient Descent, BGD）、小批量梯度下降（Mini-Batch Gradient Descent, MBGD）和随机梯度下降（Stochastic Gradient Descent, SGD）。这三者从训练速度上看是依次提升的，但随机梯度下降收敛路径不稳定，容易陷入局部极小值。实际应用中，小批量梯度下降是最常用的方法。

动量法是在梯度下降的基础上，增加了动量项来加速梯度下降的一种方法。动量法通过累积历史梯度信息，使得参数更新方向更加稳定，有助于更快地收敛到全局极小值。

AdaGrad 是一种自适应学习率的优化算法，通过动态调整每个参数的学习率来加速收敛。AdaGrad 通过累积历史梯度信息，使得频繁更新的参数学习率逐渐减小，不频繁更新的参数学习率保持较大，有助于处理稀疏数据。

RMSProp 是 AdaGrad 的改进版，通过引入指数加权平均来平滑梯度平方的累加和，避免学习率过早减小。

Adam 是目前最常用的优化算法之一，结合了动量法和 RMSProp 的优点。

### 1.2. 二阶优化算法

二阶优化算法中，包括牛顿法、拟牛顿法等。牛顿法利用了二阶导数信息，可以更快地收敛到全局极小值，但是，它利用了 Hessian 矩阵，计算复杂度，不适用于大规模数据集。

拟牛顿法则是通过近似 Hessian 矩阵来降低计算复杂度，又可分 BFGS 和 L-BFGS 两种方法。L-BFGS 适用于大规模数据集，是实际应用中最常用的拟牛顿法。

### 1.3. 零阶优化算法

零阶优化算法主要涉及粒子群优化（Particle Swarm Optimization, PSO）和遗传算法（Genetic Algorithm, GA）。粒子群优化是一种自适应优化算法，模拟鸟群或鱼群的行为，通过群体协作寻找最优解。

GA 模拟自然选择和遗传机制，通过选择、交叉和变异操作来优化模型。

这两种算法在某些高维和非凸优化问题中表现出色，但计算复杂度较高。

<!-- 目标和分类：
        一阶优化：SGD, Momentum, AdaGrad
        二阶优化：梯度和二阶导
        零阶优化方法： 粒子群、遗传算法 GA
    -->

## 2. 交叉验证

交叉验证（Cross-Validation）是评估机器学习模型性能的重要方法。通过将数据集分成多个子集，并在不同的子集上进行训练和验证，可以更准确地估计模型的泛化能力。

### 2.1. k-fold 交叉验证

当我们只有一个很小的数据集时，再把数据集划分为训练、验证和测试集，就往往导致数据量不足，此时即便模型训练完成，评估表现还不错，我们也无法确定它在生产环境下是否仍然有同样出色的表现。这时，一种被称为 k-fold 的交叉验证方法就被提了出来。它的基本步骤是：

1. 将数据集随机分成 k 个大小相等或者相近、互斥的子集（称为折）。
2. 对于每一个子集 i：
   - 使用剩余的 k-1 个子集作为训练集。
   - 使用第 i 个子集作为验证集。
3. 计算每个子集上的验证误差。
4. 最后，取所有子集验证误差的平均值作为最终的验证误差。

k-fold 交叉验证的优点是充分利用了数据集，减少了因数据划分带来的偏差。如果模型在多个子集上的评估表现趋向一致，那么我们对模型的泛化能力就有了更强的信心。当然，由于我们进行了 k 次训练，所以，当 k 值比较大时，整个训练过程就会被拉长。

我们通过下面的例子来说明 k-fold 应该如何使用。

<Example id=make_classification/>

```python
import numpy as np
from sklearn.datasets import make_classification
from collections import Counter
import matplotlib.pyplot as plt

# 生成类别不平衡的三分类数据集
X, y = make_classification(
    n_samples=500,        # 样本总数
    n_features=10,         # 特征数量
    n_informative=5,      # 有用的特征数量
    n_redundant=5,         # 冗余特征数量
    n_classes=3,           # 类别数量
    weights=[0.05, 0.3, 0.65], # 类别不平衡比例
    flip_y=0.01,           # 标签噪声比例
    random_state=42        # 随机种子
)

# 检查类别分布
class_distribution = Counter(y)
print("Class distribution:", class_distribution)
```

我们也可以绘制类别直方图，更形象直观地观察类别分布。

<Example id=classification_hist/>

```python
# 绘制类别分布直方图
plt.figure(figsize=(8, 4))
plt.hist(y, bins=3, rwidth=0.8, align='left', edgecolor='black')
plt.xticks([0, 1, 2])
plt.xlabel('Class')
plt.ylabel('Number of samples')
plt.title('Class Distribution')
plt.show()
```

<Example id=accuracy_score/>

```python
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score

X_train, X_test, y_train, y_test = train_test_split(X, y, random_state=78, test_size=0.2)

model = LogisticRegression(max_iter=5)
model.fit(X_train, y_train)

y_pred = model.predict(X_test)
accuracy_score(y_test, y_pred)
```

注意在训练模型时，我们使用了一个参数，max_iter。用来定义模型本身的参数，一般称为超参数 (hyper parameter)。此时，训练出来的模型，它的 accuracy 大约是在 0.7 左右。考虑到这个结果不是很好，于是，我们决定加大 max_iter，看能否得到更好的结果。

<Example id=max_iter/>

```python
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, random_state=78, test_size=0.2)

model = LogisticRegression(max_iter=50)
model.fit(X_train, y_train)

y_pred = model.predict(X_test)
accuracy_score(y_test, y_pred)
```

当我们把 max_iter 加大到 50 之后，发现准确率提高到了 74%。如果进一步加大 max_iter 的值，或许准确率还能进一步提高。但是，我们可以在实际生产中，使用 50，甚至更大的 max_iter 来训练模型，并使用训练后的模型吗？我们要如何回答这个问题？

这就是可以应用 k-fold 的场景之一。如果我们把数据集划分为 k 个子集（折），再使用最优的超参数 (max_iter)，并且在每个子集上都能得到较一致的结果的话，那么，我们就能对最优的 max_iter 建立起信心。

<Example id=k-fold/>

```python
from sklearn.model_selection import KFold

# 创建 KFold 对象，设置 K=5
kf = KFold(n_splits=5, shuffle=True, random_state=42)

# 初始化一个列表来存储每折的准确率
accuracies = []

# 进行 K 折交叉验证
for train_index, test_index in kf.split(X):
    X_train, X_test = X[train_index], X[test_index]
    y_train, y_test = y[train_index], y[test_index]
    
    model = LogisticRegression(max_iter=50)
    model.fit(X_train, y_train)
    
    # 预测并计算准确率
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    accuracies.append(accuracy)

# 输出每折的准确率和平均准确率
print("KFold Accuracies:", accuracies)
print("KFold Average Accuracy:", np.mean(accuracies))
```

我们得到的平均准确率仍然保持在 0.74，并且方差并不大，所以，如果我们对这个准确率满意，就可以使用 max_iter=50 来训练模型。

!!! tip
    k-fold 是一种模型评估方法，它的任务是评估我们优化后的超参数或者模型是否稳定，而不是直接进行优化。<br><br>在上述实验中，其中有一折的准确率达到了 0.76，但是我们并不能使用在该折数据下，训练出来的模型作为最终模型。k-fold 的作用是帮助我们评估模型的性能和稳定性。如果通过 k-fold 划分的数据集训练出来的模型，准确率一致性较好，方差较小，则说明模型以及我们选择的参数比较稳定可靠。通过 k-fold 来寻找到最优参数之后，我们还要使用全部数据和选择的参数重新训练一次模型，将其作为最终的模型来应用。<br><br>但是，由于我们之前通过 k-fold 进行过交叉验证，所以，如果交叉验证时的数据能给我们信心，那么，最终训练的模型，它的性能数据就是可靠的。这是 k-fold 在优化中的真正作用。

### 2.2. Stratified K-Fold Cross-Validation

k-fold 改善了数据集过拟合时如何评估模型的问题。但是，数据集还可能存在样本分布不均衡的问题。我们在之前的课程中就讨论过，样本分布不均衡会对模型的评估产生什么样的影响，并且在第 13 课的练习中，也提到了可以通过随机采样来进行改善。在数据集本身不平衡时，k-fold 的抽样过程质量如何，会对最终评估产生重要影响。为了公平起见，人们又提出来一种分层交叉验证（Stratified K-Fold Cross-Validation）的思想。在 sklearn 中对应的实现是 StratifiedKFold。

<Example id=stratified-k-fold/>

```python
from sklearn.model_selection import StratifiedKFold

skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

# 初始化一个列表来存储每折的准确率
stratified_accuracies = []

# 进行分层 K 折交叉验证
for train_index, test_index in skf.split(X, y):
    X_train, X_test = X[train_index], X[test_index]
    y_train, y_test = y[train_index], y[test_index]
    
    # 训练模型
    model = LogisticRegression(max_iter=200)
    model.fit(X_train, y_train)
    
    # 预测并计算准确率
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    stratified_accuracies.append(accuracy)

# 输出每折的准确率和平均准确率
print("StratifiedKFold Accuracies:", stratified_accuracies)
print("StratifiedKFold Average Accuracy:", np.mean(stratified_accuracies))
```

我们看到，在对模型进行分层抽样交叉验证时，准确率均值跟之前近似。方差略有扩大（这主要是因为样本数量少），但仍可接受。现在，我们对使用 max_iter=50 就更有信心了。

### 2.3. 留一法（Leave-One-Out Cross-Validation, LOOCV）

在 k-fold 中，具体划分多少层是个经验值。如果我们把 k-fold 中的折数扩大到等于样本数，此时就出现一种极端的情况，即共有 n 个 fold（n 为训练集的总样本数），每个 fold 中有 n-1 个样本作为训练集，而验证集则只有一个样本。这种方法被称为留一法。

它的具体操作步骤如下：

1. 对于每一个样本 i：
   - 使用除第 i 个样本外的所有样本作为训练集。
   - 使用第 i 个样本作为验证集。
2. 计算每个样本上的验证误差。
3. 最后，取所有样本验证误差的平均值作为最终的验证误差。

LOOCV 的优点是几乎完全利用了所有数据，减少了因数据划分带来的偏差。缺点是计算成本极高，尤其是在数据集较大时。

### 2.4. 时间序列交叉验证（Time Series Cross-Validation）

k-fold 方法可以运用在普通的数据集上。它在划分数据时，只保证了每个 fold 的之间的样本数量大致相等，但是往往打乱了顺序。

这对普通的机器学习任务并没有影响。但在量化交易中，我们处理的数据常常是有时间上的先后顺序的。如果我们用来训练的数据晚于预测的数据，这很可能会带来未来数据，同时，如果特征中有一些因子是基于时序构建的，这样前后颠倒也会导致逻辑混乱。

因此，我们需要专门为时间序列数据的交叉验证发明新的方法。在 Sklearn 中提供了 TimeSeriesSplit 方法来帮助我们完成数据的划分，并保证时间顺序不被打乱。

下面的代码演示了如何使用 TimeSeriesSplit：

<Example id=time-series-split/>

```python
from sklearn.model_selection import TimeSeriesSplit

tscv = TimeSeriesSplit(n_splits=5)
accuracies = []

for train, test in tscv.split(X, y):
    X_train = X[train]
    y_train = y[train]
    X_test = X[test]
    y_test = y[test]
    
    model = LogisticRegression(max_iter=200)
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    
    accuracy = accuracy_score(y_test, y_pred)
    accuracies.append(accuracy)
    
print("Accuracies:", accuracies)
print("Average Accuracy:", np.mean(accuracies))
```

TimeSeriesSplit 从使用方式上看非常像 k-fold，只不过在内部，它们划分数据的方式不一样而已。

### 2.5. 滚动预测（Rolling Forecasting）

上一节讲到的时间序列交叉验证方式，在量化交易中仍有不足之处。它的每一个划分$K$的数据，都与$K_{-1}$毫无关系。但实际上，这两者之间本应该有密切的关系：每一个数据集$K$，都应该有一部分从$K_{-1}$开始。但这个纽带被 TSCV 切断了。

因此，人们又发明了一种新的、更适用于金融数据分析的交叉验证方式，即滚动预测。它在划分数据集时，使用了滑动窗口。它的实现步骤如下：

1. 将数据集按时间顺序分成训练集和验证集。
2. 初始训练集包含前 n 个数据点，验证集包含接下来的一个或几个数据点。
3. 在初始训练集上训练模型，并在验证集上进行预测。
4. 将验证集中的数据点加入训练集，继续训练模型，并在下一个验证集上进行预测。
5. 重复上述步骤，直到遍历完所有数据点。

<div style='width:500px;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/tsfresh-rolling-forecasting.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>图片来源：tsfresh 文档</span>
</div>

下面的代码实现了当验证集只包含一个数据点的情况：

<Example id=rolling-forecasting/>

```python
initial_window = 50

predictions = []
actuals = []

for i in range(initial_window, len(X)):
    X_train = X[:i]
    y_train = y[:i]
    y_test = y[i:i+1]
    
    model = LogisticRegression(max_iter=200)
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    
    predictions.append(y_pred[0])
    actuals.append(y[i])

print("Average Accuracy:", accuracy_score(predictions, actuals))
```

滚动预测的优点是能够模拟实际的预测过程，确保模型在新数据上的表现。缺点是计算成本较高，且需要仔细选择滚动窗口的大小。

## 3. 参数搜索

在交叉验证一节中，我们已经看到，在训练 LogisticRegression 模型时，最大迭代次数会影响模型的性能。这个最大迭代次数，max_iter，一般被称作超参数。超参数（Hyperparameters）是指在机器学习模型训练之前就已经设定好的参数，它们决定了模型的结构和行为，但不是从训练数据中直接学习得到的。

常见的超参数有学习率（Learning Rate），正则化强度，决策树最大深度（max depth），支持向量机的核函数类型 (kernel type)，神经网络的层数和每层神经元数，批量大小 (batch size) 和迭代次数 (Epoch) 等。

超参数是由研究人员根据经验手动设定的。这种方法不够系统，有可能错过最佳组合。因此，人们发明了参数搜索 (Hyperparameter Tuning) 技术。

### 3.1. 网格搜索

在参数搜索的各种方案中，网格搜索是最直观、实现最简单的一种方法。它是通过穷举法（即网格搜索）遍历所有可能的参数组合，从而找到最优的超参数设置。

在 Sklearn 中，GridSearchCV 不仅实现了网格搜索，还实现了交叉验证。这正是它的名字中为什么同时包含 Grid 和 SV 的来由。下面，我们就以一个具体的例子来介绍它的使用。

在实验中，我们使用鸢尾花数据集作为训练数据集，模型是逻辑回归。我们先来生成基准测试：

<Example id=grid-search/>

```python
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, accuracy_score

iris = load_iris()
X, y = iris.data, iris.target

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

model = LogisticRegression(max_iter=5)

model.fit(X_train, y_train)
y_pred = model.predict(X_test)

print("Accuracy with default hyper parameters:", 
      accuracy_score(y_pred, y_test))
```

我们得到的准确率是 76.7%。下面，我们就来进行参数网格搜索：

```python
# 定义参数网格
param_grid = {
    'C': [0.1, 1, 10, 100],
    'solver': ['liblinear', 'lbfgs', 'newton-cg', 'sag', 'saga'],
    'penalty': ['l1', 'l2', 'elasticnet', 'none'],
    "max_iter": np.linspace(5, 50, 5).astype(int),
}

model = LogisticRegression()
grid_search = GridSearchCV(
    estimator=model,
    param_grid=param_grid,
    scoring='accuracy',
    cv=5,
    n_jobs=-1,
    verbose=2
)

grid_search.fit(X_train, y_train)

# grid_search 会输出许多警告
clear_output()

# 输出最佳交叉验证得分
print("Best Cross-validation Score:", grid_search.best_score_)

# 输出最佳参数组合
print("Best Parameters:", grid_search.best_params_)
```

现在，我们来验证一下选出的最佳模型，究竟成色几何：

<Example id=grid-search-best/>

```python
best_model = grid_search.best_estimator_
y_pred = best_model.predict(X_test)

# 打印分类报告
print("Classification Report:")
print(classification_report(y_test, y_pred))

# 打印测试集上的准确率
print("Test Accuracy:", accuracy_score(y_test, y_pred))
```

通过网格搜索，我们找到了最佳的超参数组合，使得模型的准确率达到了 100%。在上一节中，我们已经见过的参数 max_iter，现在，通过网格搜索，我们发现在 iris 数据集上，它的最佳参数是 15。

现在，我们深入到代码中，看看这一切究竟是如何实现的。

首先我们注意到示例先是定义了一个参数网格`param_grid`。基于这个网格定义，各种超参数的排列组合达到了 2000 组，也就是我们的训练会执行 2000 次，然后从中找出最佳的参数组合。

但是，参数网格中的参数关键字从何而来呢？不同的框架可能有不同的实现方式，但在 sklearn 当中，每个模型都应该定义自己的参数，并且通过 get_params 暴露出来。

<Example id=grid-search-get-params/>

```python
model = LogisticRegression()

model.get_params()
```

从输出中，我们得到 16 个左右的超参数，其中有我们熟悉的正则化强度 (C)、l1 正则化强度（当使用 ElasticNet 正则化时，l1 和 l2 的比例），penalty（正则化类型）,n_jobs, random_state 等几乎所有模型中都存在的超参数，也有只属于本模型的超参数，比如 fit_intercept, tol(tolerance，收敛判据的容差）等。当我们要生成参数网格时，就可以，也只能传入这些参数。

第二步，就是定义 GridSearchCV 的实例。在这里我们要传入模型（通过 estimator 参数传入）和参数网格（通过 param_grid 传入）。

此外，它还允许我们传入评估指标（scoring）和交叉验证（cv）等参数。这些参数并不是模型的一部分，因此，它们并不通过参数网格传入。但是，我们也可能需要尝试不同的评估指标、交叉验证的折数，此时，一般可以手工来完成。

然后我们调用 grid_search.fit 方法来尝试各种参数组合，并且在每一个参数组合下，对模型进行训练和评估，并且记录参数组和得分。最终，最佳参数、得分和模型就分别保存在 best_params_, best_score_和 best_estimator_中。

我们可以将这三个变量保存（持久化）到硬盘中，然后在生产环境中加载并执行预测任务。

!!! tip
    在代码中，cv参数可以是整数，也可以是某个交叉验证器对象。

### 3.2. 随机搜索

网格搜索已经很强大了，但是，它的缺点是，它需要穷举所有的参数组合，这可能非常耗时。因此，人们发明了随机搜索。

在 sklearn 中，RandomizedSearchCV 实现了随机搜索。

<Example id=random-search/>

```python
from scipy.stats import randint
from scipy.stats import reciprocal
from sklearn.model_selection import RandomizedSearchCV

# 定义参数分布
param_dist = {
    'C': reciprocal.rvs(1e-2, 1e2,size=1000),# 对数均匀分布
    'solver': ['liblinear', 'lbfgs', 'newton-cg', 'sag', 'saga'],
    'penalty': ['l1', 'l2', 'elasticnet', 'none'],
    "max_iter": randint(5, 50),
}

model = LogisticRegression()
random_search = RandomizedSearchCV(
    estimator=model,
    param_distributions=param_dist,
    n_iter=20,  # 尝试的参数组合数量
    scoring='accuracy',
    cv=5,
    n_jobs=-1,
    verbose=2,
    random_state=42
)

random_search.fit(X_train, y_train)

# random_search 有大量的警告
clear_output()

# 输出最佳交叉验证得分
print("Best Cross-validation Score:", random_search.best_score_)

# 输出最佳参数组合
print("Best Parameters:", random_search.best_params_)
```

这一次，总的训练次数只有 100 次，与网格搜索相比，大大降低。我们拿它找到的最佳参数和模型进行验证：

<Example id=random-search-best/>

```python
best_model = random_search.best_estimator_
y_pred = best_model.predict(X_test)
print("Classification Report:")
print(classification_report(y_test, y_pred))
print("Test Accuracy:", accuracy_score(y_test, y_pred))
```

得到的准确率也是 100%。尽管准确率与网格搜索的结果一致，但是，我们找到的最佳参数有所不同。事实上，如果你运行它多次，最佳参数都有可能不同。这也说明，随机搜索并不一定能找到全局最优解。

随机搜索与网格搜索相比，尽管大大提升了训练效率，但是，它无法保证找到全局最优解。于是，人们又发明了贝叶斯优化。

### 3.3. 贝叶斯优化

贝叶斯优化是一种更复杂的优化方法，利用概率模型来估计参数组合的质量。它通过逐步缩小搜索空间，可以在较少的评估次数内找到较优的参数组合。

sklearn 中并没有提供开箱即用的贝叶斯优化器。我们可以使用Optuna[^optuna]或者Hyperopt[^hyper]库来实现贝叶斯优化。这两个库都在github上开源，并且获得了相当多的关注。由于Optuna已经宣布支持了Python 3.13，看起来它的开发更为活跃，因此，在这里我们将使用Optuna为例进行演示。

<Example id=bayesian-optimization/>

```python
import optuna
from sklearn.utils._param_validation import InvalidParameterError

def objective(trial):
    # 定义要优化的超参数
    C = trial.suggest_loguniform('C', 1e-2, 1e2)
    solver = trial.suggest_categorical('solver', ['liblinear', 'lbfgs', 'newton-cg', 'sag', 'saga'])
    penalty = trial.suggest_categorical('penalty', ['l1', 'l2', 'elasticnet', 'none'])
    max_iter = trial.suggest_int('max_iter', 5, 50)

    # 创建模型
    model = LogisticRegression(C=C, solver=solver, penalty=penalty, max_iter=max_iter)

    # 训练模型
    try:
        model.fit(X_train, y_train)
    except (InvalidParameterError, ValueError):
        return

    # 预测并计算准确率
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)

    return accuracy

# 创建 Optuna 学习器
study = optuna.create_study(direction='maximize')

# 开始优化
study.optimize(objective, n_trials=100)

clear_output()

# 输出最佳参数组合
print("Best Parameters:", study.best_params)

# 输出最佳准确率
print("Best Accuracy:", study.best_value)

# 使用最佳模型进行预测
best_model = LogisticRegression(**study.best_params).fit(X_train, y_train)

print("Best model accuracy on test set:", accuracy_score(y_test, best_model.predict(X_test)))

# 打印分类报告
print("Classification Report:")
print(classification_report(y_test, y_pred))

# 打印测试集上的准确率
print("Test Accuracy:", accuracy_score(y_test, y_pred))
```

与sklearn相比，使用Optuna有两点显著的不同。首先，我们需要定义目标函数（示例中为objective），在该函数内，我们接受传入的参数trial，通过调用trial的suggest_*方法来建议搜索的参数。该方法会返回实际生成的参数，然后我们使用这些参数来创建模型、训练模型和评估模型（在本例中为计算并返回准确率）。objective必须返回数值类型。

然后，我们通过create_study方法来创建一个学习器。由于它是基于目标函数，而不是成本函数进行优化，所以，我们还必须指定它的优化方向。在这里，我们指定方向为最大化。

然后我们调用optimize方法来进行优化。这需要我们传入目标函数和另一个参数,n_trials。optimize将以此作为最大试验次数，但基于TPE（Tree-structured Parzen Estimator）算法来选择超参数。它将根据之前的试验结果动态调整后续的采样分布，从而实现快速搜索出最佳参数的目标。


最后，optuna只会保存最佳参数，但不会保存最佳模型。因此，我们需要在获得最佳参数之后，再次对模型进行训练，以获得最佳模型。

!!! tip
    Optuna没有实现交叉验证。但是，我们可以把sklearn中的交叉验证方法集成到objective函数中。


## 4. 拓展阅读

scikit-optimize 中提供了一个 BayesSearchCV[^skopt] 实现，有着与sklearn一致的接口。它在github上获得了2.7k stars。但是，该项目已被作者放弃，不推荐使用。

下面的示例说明了它的用法：

!!! attention
    要运行本段代码，你需要安装skopt库

    ```python
    model = LogisticRegression()
    search_spaces = {
        'C': (1e-2, 1e2, 'log-uniform'),  # 对数均匀分布
        'solver': ['liblinear', 'lbfgs', 'newton-cg', 'sag', 'saga'],
        'penalty': ['l2'],
        "max_iter": (5, 50, 'uniform')  # 均匀分布
    }

    # 定义 BayesSearchCV 实例
    bayes_search = BayesSearchCV(
        estimator=model,
        search_spaces=search_spaces,
        n_iter=5,  # 尝试的参数组合数量
        scoring='accuracy',
        cv=5,
        n_jobs=-1,
        verbose=2,
        random_state=42
    )

    # 开始优化
    bayes_search.fit(X_train, y_train)

    # 输出最佳交叉验证得分
    print("Best Cross-validation Score:", bayes_search.best_score_)

    # 输出最佳参数组合
    print("Best Parameters:", bayes_search.best_params_)

    # 使用最佳模型进行预测
    best_model = bayes_search.best_estimator_
    y_pred = best_model.predict(X_test)

    # 打印分类报告
    print("Classification Report:")
    print(classification_report(y_test, y_pred))

    # 打印测试集上的准确率
    print("Test Accuracy:", accuracy_score(y_test, y_pred))
    ```

这里需要解释的参数是 n_iter，它并不是指模型内部使用的迭代次数（跟 max_iter 关联的那一个），而是指优化器将在参数空间中，取多少个组合。默认值是 50，也可以取值为 1，或者任何整数。

在我们这个小小的模型中，贝叶斯优化似乎没有显示出它的作用。但是，当模型复杂度增大、超参数组合太多时，BayesSearchCV的威力就会逐渐显现。它能够以更小的搜索次数，几乎找到合局最优解。

<!-- 用于模型的解释工具， inspection & visualization-->
<!-- knn 预测 https://github.com/sammanthp007/Stock-Price-Prediction-Using-KNN-Algorithm -->

## 5. Footnotes

[^skopt]: [BayesSearchCV](https://scikit-optimize.github.io/stable/modules/generated/skopt.BayesSearchCV.html)文档。该库已于2024年2月成为归档项目，意味着作者很可能将不再继续维护。
[^optuna]: [Optuna](https://github.com/optuna/optuna)在Github上获得了11k stars。
[^hyper]: [HyperOpt](https://github.com/hyperopt/hyperopt)在Github上获得了7.3k的stars。
