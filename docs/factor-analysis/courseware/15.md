# 15. SKLearn 通用工具包

在前面的课程中，我们已经或多或少接触了 Sklearn。

Scikit learn 是一个非常全面的机器学习库，涵盖了广泛的传统机器学习算法。除了常见的监督学习算法如线性回归、逻辑回归、支持向量机（SVM）、朴素贝叶斯等用于分类和回归任务，还包括无监督学习算法如聚类算法（K-Means、DBSCAN 等）、降维算法（PCA、t-SNE 等）。这使得它能够满足各种不同类型的机器学习问题，从简单的线性关系建模到复杂的数据聚类和降维任务。

除了这些算法（模型）之外，它还提供了完整的机器学习流程工具，包括数据预处理（标准化、归一化、缺失值处理等）、模型评估（多种评估指标如准确率、召回率、F1 - score、均方误差等）和模型选择（交叉验证、网格搜索等）。这些功能对于初学者理解和实践机器学习的完整流程非常有帮助，并且在实际项目中可以方便地组合使用不同的工具来构建和优化模型。

在易用性和学习曲线上，Sklearn 具有简洁明了的 API 设计，对于初学者来说非常友好。例如，使用 Sklearn 拟合一个简单的线性回归模型，只需要创建模型对象（如 LinearRegression()），然后调用 fit 方法传入训练数据即可。这种简单的操作方式使得用户可以快速上手并理解模型的训练过程，有助于快速学习和实验不同的机器学习算法。

Sklearn 还有大量详细的文档和丰富的示例代码，这些资源可以帮助用户更好地理解每个算法的原理、参数含义以及如何正确地使用它们。例如，在 Sklearn 的官方文档中，对于每个算法都有详细的数学原理介绍、参数说明以及使用示例，方便用户参考和学习。

## 1. 内置数据集

scikit-learn 提供了一些常用的内置数据集，这些数据集可以直接通过 sklearn.datasets 模块加载。这些数据集主要用于教学、测试和快速原型开发。

### 1.1. Iris数据集

Iris 数据集是最著名的数据集之一，包含 150 个样本，每个样本有 4 个特征（花萼长度、花萼宽度、花瓣长度、花瓣宽度）和一个标签（鸢尾花的种类，共 3 种）。


<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/iris-virginica.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>维吉尼亚鸢尾</span>
</div>

Iris数据集是由美国生物学家Edgar Anderson于收集整理的，由Ronald Fisher发布于1936年。该数据集在机器学习社区中非常有名。

<Example id=load_iris/>

```python
from sklearn.datasets import load_iris
iris = load_iris()
X, y = iris.data, iris.target
print(X[:5, :],"\n\n", y[:5])
print(f"\n{iris.target_names}")
print(f"\n{iris.DESCR[:200]}")
```

该数据集适合用来学习分类算法。

### 1.2. Digits数据集

Digits数据集是由scikit-learn提供的一个手写数字数据集，该数据集包含1797个手写数字图片，每张图片大小为8x8，共64个像素点，每张图片是一个灰度图，灰度值为0。

<Example id=load_digits/>

```python
from sklearn.datasets import load_digits

digits = load_digits()
print(digits.data.shape)
plt.gray()
plt.matshow(digits.images[0])
plt.show()
```

!!! tips
    sklearn中的digist数据集由土耳其学者E. Alpaydin发布于1998年，是UCI ML手写数字数据集的测试集副本。这些信息可以通过digist.DESCR属性来访问到。

### 1.3. 加州房价数据集

California 房价数据集包含 20640 个样本，每个样本有 8 个特征（如人口、中位年龄等）和一个目标变量（房价中位数）。

<Example id=fetch_california_housing/>

```python
from sklearn.datasets import fetch_california_housing
housing = fetch_california_housing(data_home='/data')
X, y = housing.data, housing.target
print(X.shape, y.shape)
print(housing.feature_names)
print(housing.DESCR)
```

该数据集并不是sklearn的内置数据集。在加载时，如果本地无缓存，将会从这里下载[^cal_housing]。此后，每次加载数据集，都会从本地缓存中读取。

!!! info
    在Sklearn的早期版本中，还存在另一个类似的房价数据集，Boston房价数据集。但是，该数据集被质疑有歧视黑人的倾向[^medium]，于是被停用了。

### 1.4. MNIST和OpenML平台

该数据集是由杨立昆(Yann LeCun)等人创建并使用的。它来源于美国国家标准与技术研究院（NIST）的手写数字数据库 。它由训练集和测试集两部分组成，其中训练集包含 60,000 张手写数字的图像，测试集包含 10,000 张图像。这些手写数字的图像都是 28x28 像素的灰度图像，数字的范围是从 0 到 9。

杨立昆在研究卷积神经网络时，广泛使用了MNIST数据集，并基于它提出了LeNet-5模型，这是人类第一次将多层神经网络用于解决实际问题。

!!! info
    Sklearn提供了下载MNIST数据集的函数，该函数用于下载OpenML[^openml]平台上的数据集。

    ```python
    from sklearn.datasets import fetch_openml
    mnist = fetch_openml('mnist_784', version=1)
    X, y = mnist.data, mnist.target
    ```

在上述代码中，通过修改data_id，就可以下载其它数据集。

## 2. 人工合成数据集

除了真实数据集之外，sklearn还提供了生成数据集的函数，这些函数多以make_作为前缀，比如，我们可以生成一个分类数据集。

<Example id=make_classification/>

```python
from sklearn.datasets import make_classification

X, y = make_classification(n_samples=100, n_features=2, n_informative=2, n_redundant=0, random_state=42)
X[:5], y
```

在sklearn中的数据集远不止这里列举的这么多。sklearn把这些数据集都封装到datasets模块中，我们可以通过以load_为前缀或者make_为前缀的函数来获取它们。相关文档在这里[^datasets]。


## 3. 数据预处理
<!-- 标准化工具、归一化工具、缺失值处理、独一码编码 -->

为了数据能够适配模型训练，它们不仅要满足规定的数据格式（DataFrame，或者一维、二维数组），并且在数据的取值范围和分布上，都可能要满足一定的条件，才便于模型进行优化，得到泛化能力更强的模型，并且得到的预测结果才能得到一致的解释。

事实上，许多算法在设计时都假设每个特征的值接近于零，或者更重要的是，所有特征在可比较的尺度上变化。特别是，基于度量和梯度的估计器通常假设数据大约标准化（中心化特征且具有单位方差）。

值得注意的例外是基于决策树的估计器（决策树和各种梯度提升模型），它们对数据的任意缩放具有鲁棒性。

### 3.1. 标准化

标准化数据集是 sklearn 中许多机器学习算法的常见要求，如果单个特征不服从（大致上）标准正态分布，它们的表现往往会不够好。比如，目标函数中使用的一些元素，比如支持向量机的 RBF 核或者线性模型的 L1 和 L2 正则化器，假设所有特征都围绕零中心，或者具有相同数量级的方差。如果一个特征的方差比其它特征大几个量级，则该特征可能会主导目标函数。

在因子分析部分，我们已经介绍了如何实现标准化。在学习 sklearn 后，我们也可以使用 `StandardScaler` 类来实现标准化。

下面的例子演示了如何进行标准化，以及标准化之后，对模型预测能力的增强。

<Example id=standardization/>

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn import svm
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler

# 生成一个简单的二分类数据集
X, y = make_classification(n_samples=100, n_features=2, n_informative=2, n_redundant=0, random_state=42)

# 增加一个高方差特征
X_high_variance = np.hstack([X, (X[:, 0] * 100).reshape(-1, 1)])

# 创建 SVM 模型
clf_no_scaling = svm.SVC(kernel='linear')
clf_with_scaling = svm.SVC(kernel='linear')

# 拟合模型，不进行特征缩放
clf_no_scaling.fit(X_high_variance, y)

# 对特征进行标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X_high_variance)

# 拟合模型，进行特征缩放
clf_with_scaling.fit(X_scaled, y)

# 绘制决策边界
def plot_decision_boundary(X, y, clf, title, scaler=None):
    h = .02  # step size in the mesh
    x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
    y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                         np.arange(y_min, y_max, h))
    
    # 创建一个网格，包含所有三个特征
    grid_points = np.c_[xx.ravel(), yy.ravel(), np.zeros_like(xx.ravel())]
    
    Z = clf.predict(grid_points)
    Z = Z.reshape(xx.shape)
    plt.contourf(xx, yy, Z, alpha=0.8)
    plt.scatter(X[:, 0], X[:, 1], c=y, edgecolors='k', marker='o')
    plt.title(title)
    plt.xlabel('Feature 1')
    plt.ylabel('Feature 2')
    plt.show()

# 绘制未缩放特征的决策边界
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plot_decision_boundary(X_high_variance, y, clf_no_scaling, 'Decision Boundary without Scaling', None)

# 绘制缩放特征的决策边界
plt.subplot(1, 2, 2)
plot_decision_boundary(X_scaled, y, clf_with_scaling, 'Decision Boundary with Scaling', scaler)
plt.show()
```

<!-- BEGIN IPYNB STRIPOUT -->
这将生成以下两个图：

<div style="width:100%;display:flex; justify-content: space-between;">
<img style="width: 50%" src='https://images.jieyu.ai/images/2024/12/high-variance-no-scale.jpg'>
<img style="width: 35%" src='https://images.jieyu.ai/images/2024/12/high-variance-scaled.jpg'>
</div>

<!-- END IPYNB STRIPOUT -->

这段代码构建了一个有三个特征的数据集，它共有两个分类。其中第三个特征是具有高方差的特征。当我们使用未经标准化的数据集来训练时，模型并不能正确地预测出结果。而当我们对数据集进行标准化之后，模型性能大大提高，它能比较准确地划分出决策边界。

!!! tip
    这个示例为说明标准化的作用，有几处地方并不严谨，但不影响我们作出结论。第一点是它没有划分训练集和测试集，而是直接使用全部数据进行训练，也使用全部数据来进行预测（在函数 plot_decision_boundary 内）；第二点是，为了画出二维的决策边界示意图，它只使用了前两个特征，而忽略了第三个特征（通过设置值为 0）。这是一种不得已而为之的近似。

StandardScaler 去除平均值并使数据标准化为单位方差。这种缩放会缩小特征值的范围，然而，在计算经验平均值和标准差时，异常值会产生影响。请注意，特别是因为每个特征上的异常值有不同的大小，转换后数据在每个特征上的分布仍然会非常不同。

我们以加州房产数据集为例，看看 StandardScaler 的结果：

!!! tip
    加州房产数据是 sklearn 内置的一个数据集，但默认并不会安装，需要在使用前下载。下载后会缓存。

<Example id=standardization-housing/>

```python
from sklearn.datasets import fetch_california_housing
from sklearn.preprocessing import StandardScaler

pd.options.display.float_format = "{:.2f}".format

data_home = os.environ.get('coursea_datahome', '/data')
dataset = fetch_california_housing(data_home=data_home)
X_housing, y_housing = dataset.data, dataset.target
feature_names = dataset.feature_names
scaled = StandardScaler().fit_transform(X_housing)
pd.DataFrame(scaled, columns = feature_names).describe()
```

可以看出，转换后的平均收入特征的数据大多位于 [-2, 4] 范围内，而转换后的平均房屋占用率（AveOccup）数据则被压缩在较小的 [-0.2, 0.2] 范围内。如果这些特征本身符合正态分布，那么，50%分位处的数值应该是 0，因此，这些特征本身并不符合正态分布。

对不符合正态分布的数据，强行进行标准化调整，会带来什么后果呢？我们看到，不同特征之间的尺度仍然不太一致，并且，尽管调整后的 AveOccup 特征主要分布在 [-0.2,0.2] 的狭窄空间里，但是，它的最大值却高达 119.42。因此，对不符合正态分布的数据进行强调，无论是在特征内部，还是在特征间，都无法保证尺度的平衡。

这就要求我们进一步寻求其它能调和特征尺度平衡的方法。

### 3.2. 缩放

我们已经看到标准化的作用和它的局限。即使数据特征本身是正态分布的，在某些情况下，缩放仍然是必要的。比如，神经网络的激活函数（如 Sigmoid、Tanh 等）通常在 [0, 1] 或 [-1, 1] 范围内响应度最高。缩放可以确保输入数据在这个范围内，提高模型的训练效果。

sklearn 中提供了以下几种缩放器 [^scaler]：

#### 3.2.1. MinMaxScaler

MinMaxScaler 是一种常见的缩放器，它将数据缩放到给定的范围，通常是 [0, 1]。它通过将数据映射到给定的范围来缩放数据，而不考虑数据的分布。它的计算公式是：

$$
X_{std} = (X - X.min(axis=0)) / (X.max(axis=0) - X.min(axis=0)) \newline
$$

下面的示例演示了 MinMaxScaler 的用法，以及它和 StandardScaler 之间的区别。

<Example id=standardization-minmax/>

```python
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler

# 生成示例数据
data = np.array([[10, 0.1], [50, 0.5], [90, 0.9]])

# 标准化
scaler_standard = StandardScaler()
data_standardized = scaler_standard.fit_transform(data)
print("标准化后的数据：\n", data_standardized)

# 缩放
scaler_minmax = MinMaxScaler()
data_scaled = scaler_minmax.fit_transform(data)
print("缩放后的数据：\n", data_scaled)
```

从示例可以看出，Sklearn 定义的各种缩放器，调用 API 都是 fit_transform。有时候我们也可以使用 MinMaxScaler 来将数据缩放到其它范围。

#### 3.2.2. MaxAbsScaler

MaxAbsScaler 工作方式与 MinMaxScaler 非常相似，不过它的值域是 [-1,1]，适合要对数据进行零中心化的情况。

#### 3.2.3. RobustScaler

上述方法都对离群值不太鲁棒。RobustScaler 是一种更鲁棒的缩放器，它通过计算数据中四分位数（默认）来缩放数据，从而忽略异常值。它通过将数据缩放到给定的范围来缩放数据，而不考虑数据的分布。

<Example id=standardization-robust/>

```python
from sklearn.preprocessing import RobustScaler

transformer = RobustScaler()
scaled = transformer.fit_transform(X_housing)
feature_names = dataset.feature_names

pd.DataFrame(scaled, columns = feature_names).describe()
```

在这里，我们使用的仍然是加州房产数据。对比 StandardScaler 的结果，可以看出，RobustScaler 能确保数据按中心对称，不受离群值的影响（但它仍然保留了缩放后的离群值），从而更真实地保留了数据的分布特征。

#### 3.2.4. PowerTransformer

这种方法我们在第 2 课的视频中有介绍过，不过，通过 sklearn 提供的 PowerTransformer[^power] 进行变换会更简单。

<Example id=standardization-power/>

```python
from sklearn.preprocessing import PowerTransformer

transformer = PowerTransformer()
scaled = transformer.fit_transform(X_housing)
feature_names = dataset.feature_names

pd.DataFrame(scaled, columns = feature_names).describe()
```

#### 3.2.5. QuantileTransformer

与前面讲到的 scaler 相比，QuantileTransformer 是一个有趣的缩放器。它应用非线性变换，使得每个特征的概率密度函数映射到均匀分布或高斯分布。在这种情况下，所有数据，包括离群值，都将映射到范围在 [0, 1] 的均匀分布中，使得离群值无法与正常值区分开。

sklearn 上的这篇文档对 PowerTransformer 和 QuantileTransformer 的变换能力进行了对比：

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/comparison-between-robust-quantile.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

可以看出，无论是数据集是对数正态分布 (lognormal), 卡方分布 (chi-squared)，还是其它分布，QuantileTransformer 都能将其转换成为正态分布。

### 3.3. 逆变换

到目前为止，我们接触过的变换器都支持逆变换。这一点很重要。我们通过下面的例子来说明，对有些模型，如果没有逆变换器，就无法得到正确的结果。

<Example id=standardization-inverse/>

```python
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression

X = np.linspace(1, 10, 100)
y = X * 2 + np.random.normal(size=100)

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)

model1 = LinearRegression()
model1.fit(X_train.reshape((-1,1)), y_train.reshape((-1, 1)))

y_pred = model1.predict(y_test.reshape((-1,1)))
plt.plot(y_pred, y_test)
plt.title("without scaling")
```

这是没有进行缩放的情况下，得到的预测结果。这里使用了 QQ-Plot 来观察预测结果的准确性。当预测值和真实值越接近，它的图像就越接近 45 度角的直线。

<Example id=standardization-inverse-qqplot/>

```python
x_scaler = MinMaxScaler()
y_scaler = MinMaxScaler()

X_train_scaled = x_scaler.fit_transform(X_train.reshape((80,1)))
y_train_scaled = y_scaler.fit_transform(y_train.reshape((80,1)))

model2 = LinearRegression()
model2.fit(X_train_scaled, y_train_scaled)

X_test_scaled = x_scaler.transform(X_test.reshape((20,1)))
y_pred_scaled = model2.predict(X_test_scaled)
y_pred = model2.predict(X_test.reshape((20,1)))
plt.scatter(y_test, y_scaler.inverse_transform(y_pred_scaled), color='g', label="scaling back")
plt.scatter(y_test, y_pred, label="no scaling back")
plt.plot(y_test, y_test, label="true values")
plt.legend()
```

从输出中我们可以看到，如果 scaler 不能帮我们把预测值缩放回去，我们将得到错误的预测结果（蓝色点）。

### 3.4. 编码器

很多时候，我们会用字符串来给数据打标签。但在训练之前，这些字符串标签必须转换为数字，才能用于模型训练。

Sklearn 提供了丰富的标签编码器。

#### 3.4.1. LabelEncoder

LabelEncoder 将类别值转换为整数值。每个类别都会被分配一个唯一的整数标识符。

<Example id=standardization-labelencoder/>

```python
from sklearn.preprocessing import LabelEncoder

le = LabelEncoder()
labels = ['red', 'green', 'blue', 'red', 'green']
encoded_labels = le.fit_transform(labels)
print("编码后的标签：", encoded_labels)
print("解码后的标签：", le.inverse_transform(encoded_labels))
```

#### 3.4.2. OrdinalEncoder

OrdinalEncoder 类似于 LabelEncoder，但它可以处理多列数据，适用于 DataFrame。

<Example id=standardization-ordinalencoder/>

```python
from sklearn.preprocessing import OrdinalEncoder
import pandas as pd

df = pd.DataFrame({
    'color': ['red', 'green', 'blue', 'red', 'green'],
    'size': ['S', 'M', 'L', 'XL', 'XS']
})

oe = OrdinalEncoder()
encoded_df = oe.fit_transform(df)
print("编码后的数据：\n", encoded_df)
print("解码后的数据：\n", oe.inverse_transform(encoded_df))
```

#### 3.4.3. OneHotEncoder

OneHotEncoder 将类别值转换为 one-hot（独热码） 编码形式，即每个类别对应一个二进制向量中的一个位置，该位置的值为 1，其余位置的值为 0。

<Example id=standardization-onehotencoder/>

```python
from sklearn.preprocessing import OneHotEncoder
import pandas as pd

df = pd.DataFrame({
    'color': ['red', 'green', 'blue', 'red', 'green'],
    'size': ['S', 'M', 'L', 'XL', 'XS']
})

ohe = OneHotEncoder()
encoded_array = ohe.fit_transform(df).toarray()
print("编码后的数据：\n", encoded_array)
print("解码后的数据：\n", ohe.inverse_transform(encoded_array))
```

我们在因子分析中，讲解行业中性化时，就使用了独热码。在许多分类问题模型中，它们要求的标签常常会是独热码。

与 OneHotEncoder 类似的另一个编码器是 LabelBinarizer。

#### 3.4.4. MultiLabelBinarizer

有时候，我们面临的可能是多分类问题，即一个样本可以属于多个类别。比如，一支股票可能属于多个概念板块。

<Example id=standardization-multilabelbinarizer/>

```python
from sklearn.preprocessing import MultiLabelBinarizer

mlb = MultiLabelBinarizer()
labels = [['red'], ['green'], ['blue'], ['red', 'green']]
encoded_labels = mlb.fit_transform(labels)
print("编码后的标签：\n", encoded_labels)
print("解码后的标签：", mlb.inverse_transform(encoded_labels))
```

### 3.5. Metrics

在<ref>[](#14.md)</ref>中，我们在讲解度量函数的概念时，已经介绍了一些常见的度量函数。度量函数作为 sklearn 框架提供的公共函数中最重要的一部分，在这里，我们简要介绍一下 sklearn 中的 metrics[^metrics] 模块。

在 Sklearn 中，它是把评估函数 (scoring)、成对度量 (pairwise metrics) 和距离函数放在同一个模块 -- metrics 下，该模块是 sklearn 的顶层模块。在该模块中，分别为分类、回归和聚类任务提供了多个评估函数。

```mermaid
    graph LR
        A(metrics) --> B(scoring)
        A --> C(pairwise metrics)
        A --> D(distance metrics)
        A --> E(plotting)-->I(Confusion Matrix)
        B --> F(Regression)
        B --> G(Classification)
        B --> H(Clustering)
        E --> J(ROC Curve)
```

注意在plotting归类中，同一任务往往存在两类对象。比如，对于混淆矩阵而言，就同时存在confusion_matrix函数和ConfusionMatrixDisplay类。前者提供数据，后者进行具体的绘图操作。接下来，我们就详细介绍如何使用这些可视化工具。

## 4. 模型解释与可视化

模型的可视化一般是模型自带的功能。比如，我们在<ref>[第 13 课](#13.md)</ref>中，已经看到了决策树自带了可视化功能。

但是，在模型之外，也有一些通用的可视化工具。

### 4.1. RocCurveDisplay

<Example id=standardization-roc-curve/>

```python
from sklearn.datasets import fetch_openml
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.pipeline import make_pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import RocCurveDisplay, roc_curve

X, y = fetch_openml(data_id=1464, return_X_y=True)
X_train, X_test, y_train, y_test = train_test_split(X, y, stratify=y)

clf = make_pipeline(StandardScaler(), LogisticRegression(random_state=0))
clf.fit(X_train, y_train)

y_score = clf.decision_function(X_test)

fpr, tpr, _ = roc_curve(y_test, y_score, pos_label=clf.classes_[1])
RocCurveDisplay(fpr=fpr, tpr=tpr).plot()
```

代码中使用了 LogisticRegression 模型。它还使用了 fetch_openml 来获取数据。此外，我们还看到 make_pipeline 创建了一条流水线，以便依次对数据进行缩放和训练。使用流水线不仅使代码简洁，它还带来一个额外的好处，就是我们在进行预测时，不需要单独对 X_test 进行缩放了。

!!! tip
    在 Sklearn 中，一般分类模型使用后缀 Classifier，回归模型使用 Regressor。但 LogisticRegression 是分类模型，而不是回归模型。当然准确地讲，它的后缀也并不是 Regressor。

直到计算 fpr 和 tpr，代码都与<ref>[第 14 课](#14.md)</ref>相差无几。但接下来的部分，由于使用的 RocCurveDisplay 对象，整个绘制过程就变得非常简洁。

### 4.2. PrecisionRecallDisplay

这是一个类似于 ROC 的曲线。同样地，我们先是通过 precision_recall_curve 计算出相应的 precision 和 recall 数组，然后构建显示对象即可。

<Example id=standardization-precision-recall/>

```python
from sklearn.metrics import PrecisionRecallDisplay, precision_recall_curve

prec, recall, _ = precision_recall_curve(y_test, y_score, pos_label=clf.classes_[1])
PrecisionRecallDisplay(precision=prec, recall=recall).plot()
```

### 4.3. 混淆矩阵

分类模型的性能可以通过准确率、精确率和召回率来评估。当这些指标都很高时，我们称该模型为“表现良好”的模型。但是，如果这些指标不够好，我们就想知道，究竟错在哪儿了，这就是混淆矩阵的作用。

在这一节，我们将使用一个略为复杂的混淆矩阵例子来解释混淆矩阵。一旦我们完全理解了这个例子，那么理解更简单的二分类混淆矩阵就非常容易了。

假设我们有以下混淆矩阵 [^amin]：

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/3-classes-confustion-matrix.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>三分类中的困惑矩阵，by Mahmoud Fahmy Amin</span>
</div>

在对角线上的元素代表了正确分类的样本数量。比如，单元格$C_{11}$代表了 A 类样本被正确预测为类别 A 的数量，$C_{22}$和$C_{33}$以此类推。根据这三个单位格的值，我们可以求出模型的准确率为：

$$
accuracy = \frac{C_{11} + C_{22} + C_{33}}{total} = 98/150 = 0.653
$$

从纵向看，每一列代表了一项预测结果。单元格$C_{21}$代表了 B 类样本被预测为 A 类的数量 -- 这是一个False Positive，单元格$C_{31}$则代表了 C 类样本被预测为 A 类的数量 -- 这是另一个 False Positive。这样，我们就很容易算出精确率：

$$
precision_A = \frac{C{{11}}}{C_{21} + C_{22} + C_{31}} = 32/53=0.064
$$

从横向看，每一行代表了一类真实样本。单元格$C_{12}$代表了A类样本被预测成为B类的数量 -- 这是一个False Negative。单元格${C_{13}}$代表了A类样本被预测成为C类的数量 -- 这是另一个False Negative。

False Negative意味着模型没有『回想』起它应该记得的正样本，所以，这样我们就可以算出召回率：

$$
recall_A = \frac{C_{11}}{C_{11} + C_{12} + C_{13}} = 32 / 50 = 0.64
$$ 


我们可以通过ConfusionMatrixDisplay来绘制混淆矩阵：

<Example id=confusion-matrix/>

```python
import numpy as np
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import ConfusionMatrixDisplay, confusion_matrix
import matplotlib.pyplot as plt


X, y = make_classification(
    n_samples=1000,
    n_features=20,
    n_informative=3,
    n_redundant=10,
    n_classes=3,
    random_state=42,
)

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

# 使用随机森林分类器进行训练
clf = RandomForestClassifier(random_state=42)
clf.fit(X_train, y_train)

y_pred = clf.predict(X_test)

# 计算混淆矩阵
cm = confusion_matrix(y_test, y_pred)

# 绘制混淆矩阵
disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=clf.classes_)
disp.plot(cmap=plt.cm.Blues)
plt.title("Confusion Matrix for Three-Class Classification")
plt.grid(False)
plt.show()
```

最关键的步骤是，首先，根据样本测试集的标签，以及在测试集上预测出来的结果，通过confusion_matrix函数来计算出混淆矩阵，然后构建ConfusionMatrixDisplay对象来绘制混淆矩阵。

### 4.4. 决策边界可视化

在分类问题中，我们先是可以通accuracy, percision和recall等指标来评估模型的性能；在数据不够理想时，我们还常常借助Confustion Matrix来理解分类具体是怎么出错的。除此之外，我们还可以通过决策边界图，帮助我们直观地理解模型如何在特征空间中划分不同类别的样本的。

<Example id=decision-boundary/>

```python
from sklearn.datasets import load_iris
from sklearn.linear_model import LogisticRegression
from sklearn.inspection import DecisionBoundaryDisplay

iris = load_iris()
X = iris.data[:, :2]

classifier = LogisticRegression().fit(X, iris.target)

disp = DecisionBoundaryDisplay.from_estimator(
    classifier,
    X,
    response_method="predict",
    xlabel=iris.feature_names[0],
    ylabel=iris.feature_names[1],
    alpha=0.5,
)

disp.ax_.scatter(X[:, 0], X[:, 1], c=iris.target, edgecolor="k")
plt.show()
```

在代码中，我们先是训练了一个分类器，然后调用DecisionBoundaryDisplay.from_estimator，从训练过后的分类器中，构建出DecisionBoundaryDisplay对象并显示。DecisionBoundaryDisplay的绘制原理相当于[](#example-5)中的plot_decision_boundary函数。

## 5. 拓展阅读

除了 sklearn，还有其他几个机器学习库（不包含高级神经网络），比如：

**XGBoost**: XGBoost 是一个高效的梯度提升框架，支持分类、回归任务，特别是在结构化数据上的表现优秀。训练速度相对较慢，尤其是在处理大规模数据和高维特征时，因为它的计算复杂度相对较高。

**LightGBM**: 由微软开发的基于梯度提升决策树的框架，它的创新点在于采用了直方图算法（Histogram-based Algorithm）。在构建树的过程中，将连续的特征值划分成离散的区间（直方图），这样在分裂节点时可以快速计算每个区间的增益，大大减少了计算量。与传统的层序生长（Level - Wise）策略不同。它优先选择增益最大的叶子节点进行分裂，这样能够更快地降低损失函数的值，在相同的数据集和训练轮数下，通常可以生成更浅但更复杂的树结构，从而可能获得更好的精度。通常在训练速度上 LightGBM 表现更出色，由于直方图算法的高效性，在大数据集和高维数据情况下，能够快速地构建决策树。例如，在处理大规模文本分类任务或具有大量特征的图像数据时，LightGBM 的训练时间可能比 XGBoost 和 CatBoost 更短。

**CatBoost**: 由 Yandex 开发的框架，它能够自动处理类别型数据，无需进行复杂的独热编码（One - Hot Encoding）等预处理操作。例如，在处理包含产品类别、用户地区等类别型变量的电商数据集时，CatBoost 可以直接利用这些信息进行训练，减少了数据预处理的工作量和可能引入的误差。支持 GPU 和多 GPU 训练。它在分类任务上表现强于 XGBoost 和 LightGBM，并有更快的推理速度。

## 6. Footnotes

[^scaler]: 关于各种缩放器的讨论及可视化，参考 https://scikit-learn.org/stable/auto_examples/preprocessing/plot_all_scaling.html

[^power]: [sklearn](https://scikit-learn.org/stable/modules/generated/sklearn.preprocessing.PowerTransformer.html) 提供了 PowerTransformer 如何将非正态分布的数据转化为正态分布的详细文档。

[^metrics]: Sklearn 在 [这篇文档](https://scikit-learn.org/stable/modules/model_evaluation.html#model-evaluation) 中提供了关于如何选择评估函数的指南。

[^amin]: 关于混淆矩阵，Mahmoud Fahmy Amin 在《Journal of Engineering Research》上发表过两篇论文，分别介绍了 [二分类混淆矩阵](https://digitalcommons.aaru.edu.jo/cgi/viewcontent.cgi?article=1278&context=erjeng) 和 [三分类混淆矩阵](https://digitalcommons.aaru.edu.jo/cgi/viewcontent.cgi?article=1115&context=erjeng)。

[^cal_housing]: 加州房价数据集[下载地址](https://www.dcc.fc.up.pt/~ltorgo/Regression/cal_housing.tgz)

[^medium]: 波士顿住房数据集争议， [Medium](https://medium.com/@docintangible/racist-data-destruction-113e3eff54a8)

[^openml]: OpenML是一个在线平台，提供了大量的数据集。它的网站地址是[https://www.openml.org/](https://www.openml.org/)。你可以在这里寻找课程中没有提及的其它数据集，共计接近6000个。

[^datasets]: 更多Sklearn中的数据集，请见[https://scikit-learn.org/1.5/api/sklearn.datasets.html](https://scikit-learn.org/1.5/api/sklearn.datasets.html)。
