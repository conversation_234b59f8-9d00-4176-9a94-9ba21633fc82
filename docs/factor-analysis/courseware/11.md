# 11. 基本面因子和另类因子
## 1. Fama 三因子模型

Fama 三因子模型 [^fama] 最初由 Fama 和 French 在 1993 年提出。该模型可用如下公式描述：

$$
E(R_{it}) - R_{ft} = \beta_iE(R_{mt} - R_{ft})+s_i^E(SMB_t) + h_i^E(HML_t)
$$

其中：

* $R_{it}$表示资产 i 在时间 t 时的收益率
* $R_{ft}$表示时间 t 的无风险收益率
* $R_{mt}$表示时间 t 时的市场收益率；
* $SMB_t$表示时间 t 时，做多小市值、做空大市值股票的组合收益率
* $HML_t$表示时间 t 时，做多高账面市值，做空低账面市值股票的组合收益率。

这里的账面市值比是一个财务概念，可以简单地认为它是市净率的倒数。

Fama 三因子模型并不是我们这门课程里熟悉的因子概念，它是一种投资策略。具体地说，它是将投票池（universe）按以下方法，分成 6 部分：

| HML<br>SMB | H(30%) | M(40%) | L(30%) |
| ---------- | ------ | ------ | ------ |
| S(50%)     | S/H    | S/M    | S/L    |
| B(50%)     | B/H    | B/M    | B/L    |

然后按市值加权计算出 SMB（市值因子）和 HML（估值因子）：

$$
SMB = (S/H + S/M + S/L)/3 - (B/H + B/M + B/L)/3
$$

HML 因子的计算公式是：

$$
HML = (S/L + B/L) / 2 - (S/H + B/L)/2
$$

2015 年，Fama 和 French 将三因子模型扩展为五因子模型，即增加了盈利能力因子 (Profitability, RMW) 和投资因子 (Investment, CMA)。

我们先来看如何实现 FAMA 三因子，然后再讨论 FAMA 三因子与 Alphalens 因子检验的关系。

!!! tip
    加工整理后的三因子、五因子数据在 supplements/five-factors 目录下可以找到。

### 1.1. 获取市值和账面市值比数据

规模因子的原始数据是各资产的市值。为方便获取数据，我们先假设回测发生在 2018 年到 2023 年期间，并且要取在 2018 年已经上市的资产。

```python
start = datetime.date(2018,1,1)
end = datetime.date(2023,12,31)

# get_stock_list 定义在课程环境中，你可以通过%psource get_stock_list 查看源码
universe = get_stock_list(start, code_only=True)
universe[:10]
```

由于账面市值比就是市净率的倒数，而市净率与市值数据都可以通过 tushare 的`pro.daily_basic`接口直接获取。

```python
def get_mv_bm(instruments: Tuple[str], days: List[datetime.date])->pd.DataFrame:
    """获取各 instrument 对应日期的市值和账面市值比

    Args:
        instruments: 股票代码
        days: 日期列表，与因子生成日期对应
    Return:
        返回以日期、instrument 为索引，包含市值和账面市值比的 DataFrame
    """
    dfs = []
    for day in days:
        df = fetch_daily_basic(day)
        filtered = df[df.index.isin(instruments)]
        filtered["mv"] = filtered["circ_mv"]
        filtered["bm"] = 1/filtered["pb"]
        filtered = filtered[["mv", "bm"]]
        filtered["date"] = day
        dfs.append(filtered)

    df = pd.concat(dfs)
    df.set_index(["date", df.index], inplace=True)
    return df

dates = pd.bdate_range(start, end)
df = get_mv_bm(universe, dates)
df.tail()
```

这样就得到了市值和账面市值比，它们是由 date 和 asset 双重索引的 DataFrame，正是 Alphalens 所需要的因子数据格式。

在 Fama 三因子模型中，使用的规模因子则是在此基础上，按如下方式构建的一组数据。

### 1.2. 计算三因子

在前面得到的 df 的基础上，我们还要先获取样本的收益率，然后才能计算 SMB 因子。

```python
barss = load_bars(start, end, universe)
returns = (barss.groupby(level=1)
                .apply(lambda x: x.close.pct_change())
                .droplevel(0)
)
df["returns"] = returns
```

现在，我们可以轻松地计算 SMB 因子。

```python
def calculate_smb_factor(df):
    factors = []

    for date, group in df.groupby(level='date'):
        # 计算 SMB 因子
        group['smb_quantile'] = pd.qcut(group['mv'], q=2, labels=False)

        sg = group.query('smb_quantile==0')
        bg = group.query('smb_quantile==1')

        # small 和 big 均分权重，但内部按市值分配
        sw = (1 - sg["mv"] / sg["mv"].sum())/(len(sg)-1) / 2
        bw = bg["mv"] / bg["mv"].sum() / 2

        s_returns = np.sum(sg["returns"] * sw)
        b_returns = np.sum(bg["returns"] * bw)
        factors.append((date, s_returns - b_returns))
    
    smb_df = pd.DataFrame(factors, columns=['date', 'SMB'])
    smb_df.set_index('date', inplace=True)
    
    return smb_df

smb_df = calculate_smb_factor(df)
smb_df.tail()
```

这里要注意加权算法。不同的实现对 Fama-French 模型的理解不一样。在聚宽的量化课堂 [^joint-fama] 里，他们使用的是组内等权算法。另外一些人 [^下次一定] 使用的是组内市值加权算法。印象中 BigQuant[^bigquant] 中的一些文章也是使用的组内市值加权算法，但他们的市值加权算法，都是市值越小，权重越小；市值越大，权重越大。

但是，考虑到 Fama-French 的因子设计本来的含义，我们在这里使用的组内市值加权算法与前述几篇文章的不太一样。我们使用的加权算法，在 Small-Cap 组内，市值越小，权重越大；在 Big-Cap 组内，则是市值越大，权重越大。

HML 的计算方法与 SMB 很类似。下面我们把 HML，SMB 和 MKT 三因子通过一个函数实现了。一些文章会使用沪深 300 的收益率来作为 MKT，但实际上，这只有当你的 universe 选定为沪深 300 时才可以。

```python
def calculate_fama3(df):
    factors = []

    for date, group in df.groupby(level='date'):
        group['smb_quantile'] = pd.qcut(group['mv'], q=2, labels=False)

        sg = group.query('smb_quantile==0')
        bg = group.query('smb_quantile==1')

        # small 和 big 均分权重，但内部按市值分配
        sw = (1 - sg["mv"] / sg["mv"].sum())/len(sg) / 2
        bw = bg["mv"] / bg["mv"].sum() / 2

        s_returns = np.sum(sg["returns"] * sw)
        b_returns = np.sum(bg["returns"] * bw)
        smb = s_returns - b_returns

        # 计算 HML 因子
        quantiles = [0, 0.3, 0.7, 1.0]

        # bm_quantile will be 0, 1, 2
        sg['bm_quantile'] = pd.qcut(sg['bm'], q=quantiles, labels=False)

        bg['bm_quantile'] = pd.qcut(bg['bm'], q=quantiles, labels=False)
        
        # 分组
        sl = sg.query('bm_quantile == 0')
        wsl = (1 - sl["mv"]/sl["mv"].sum()) /len(sl) / 4
        bl = bg.query('bm_quantile == 0')
        wbl = bl["mv"]/bl["mv"].sum() / 4

        sh = sg.query('bm_quantile == 2')
        wsh = (1 - sh["mv"]/sh["mv"].sum()) /len(sh) / 4
        bh = bg.query('bm_quantile == 2')
        wbh = bh["mv"]/bh["mv"].sum() / 4

        h_returns = np.sum(sh["returns"] * wsh) + np.sum(bh["returns"] * wbh)
        l_returns = np.sum(sl["returns"] * wsl) + np.sum(bl["returns"] * wbl)
        
        # 计算 HML 因子
        hml = h_returns - l_returns

        # 计算市场因子，采用市值加权法
        mkt = np.sum(group['returns'] * (group["mv"]/group["mv"].sum()))

        factors.append((date, hml, smb, mkt))
    
    df = pd.DataFrame(factors, columns=['date', 'HML', 'SMB', 'MKT'])
    df.set_index('date', inplace=True)
    
    return df

factors = calculate_fama3(df)
factors.tail()
```

这三个因子都是自带收益的，所以，我们可以把它们的收益曲线绘制出来：

```python
cum_returns = (1 + factors).cumprod() - 1
cum_returns.plot()
```

<!--练习
我们也可以用下面的方法来计算三因子：

```python
# 计算市场因子（MKT）
def calculate_mkt(df):
    market_return = df['return'].groupby('date').mean()
    return market_return

# 计算规模因子（SMB）
def calculate_smb(df):
    def smb_group(df):
        df = df.sort_values('mv')
        n = len(df)
        small_cap_group = df.iloc[:int(n * 0.3)]
        big_cap_group = df.iloc[-int(n * 0.3):]
        
        small_cap_return = (small_cap_group['mv'] * small_cap_group['return']).sum() / small_cap_group['mv'].sum()
        big_cap_return = (big_cap_group['mv'] * big_cap_group['return']).sum() / big_cap_group['mv'].sum()
        
        return small_cap_return - big_cap_return
    
    smb = df.groupby('date').apply(smb_group)
    return smb

# 计算价值因子（HML）
def calculate_hml(df):
    def hml_group(df):
        df = df.sort_values('bm')
        n = len(df)
        high_bm_group = df.iloc[-int(n * 0.3):]
        low_bm_group = df.iloc[:int(n * 0.3)]
        
        high_bm_return = (high_bm_group['mv'] * high_bm_group['return']).sum() / high_bm_group['mv'].sum()
        low_bm_return = (low_bm_group['mv'] * low_bm_group['return']).sum() / low_bm_group['mv'].sum()
        
        return high_bm_return - low_bm_return
    
    hml = df.groupby('date').apply(hml_group)
    return hml

# 计算三个因子
mkt = calculate_mkt(df)
smb = calculate_smb(df)
hml = calculate_hml(df)

# 将因子合并到一个 DataFrame 中
factors = pd.DataFrame({
    'MKT': mkt,
    'SMB': smb,
    'HML': hml
})

((1 + factors).cumprod()-1).plot()
```
-->

!!! tip
    HML 因子的结果来自于市净率。市净率一般不需要进行复权处理。

<!--
在 supplements 目录下，我们存有中央财经大学编制的 1994 年以来的五因子数据，我们可以用这些数据与我们这里计算出来的因子进行对比：

```python
cur_dir = os.path.abspath(".")
daily = "../supplements/five-factors/fivefactor_daily.csv"
csv = os.path.join(cur_dir, daily)

five = pd.read_csv(csv)
five.rename(columns={"trddy": "date"}, inplace=True)
five['date'] = pd.to_datetime(five['date'])
five.set_index('date', inplace=True)
```

我们可以采用同样时间段的数据来生成报告，对比一下：

```python
start_date = '2018-01-01'
end_date = '2023-12-31'
sub = five.query("date >= @start_date and date <= @end_date")
cum_returns = (1 + sub).cumprod() - 1
cum_returns[["mkt_rf", "smb", "hml"]].plot()
```
-->

### 1.3. 三因子选股模型

当用三因子模型来选股时，其方法是这样的：

1. 计算出三因子值
2. 计算 universe 中的每个标的日收益率，展开为宽表
3. 以 2）的结果中的每一列为 y, 以三因子为 x，进行回归，得到截距为 alpha.
4. 在所有标的的 alpha 都求出之后，找出阿尔最大的股票组合 [^step4]，即为备选标的。

下面的例子演示了如何通过三因子来选股。

```python
import statsmodels.api as sm
pool = universe[:100]

alphas = {}

for asset in pool:
    asset_returns = returns.xs(asset, level="asset")
    asset_returns, factors_aligned = asset_returns.align(factors.dropna(), join='inner')
    X = sm.add_constant(factors_aligned)
    model = sm.OLS(asset_returns, X)
    results = model.fit()
    alphas[asset] = results.params['const']

alpha_df = pd.DataFrame(list(alphas.items()), columns=['asset', 'alpha'])

alpha_df.nlargest(10, 'alpha')
```

最后输出的 10 支个股即为我们的标的。在实际操作中，不需要使用这么长时间的三因子数据，使用最近 60 期的三因子数据进行回归就足够了。

以上我们是将三因子作为一个整体来运用的，它认为收益应该由三个因子来斛释，不能解释的部分，就是$alpha$。实际上，这三个因子不一定能和谐相处，多元线性回归方法也有其固有缺陷。因此，随着技术的发展，我们现在应该使用机器学习来组合因子，从而有必要把这些因子拿出来，单独进行考察。

## 2. 规模因子

实际上，Fama 三因子中的规模因子，最早是由 Rolf Banz[^banz] 发现的。

![Rolf Banz - 小市值效应](https://images.jieyu.ai/images/2024/09/low-beta-factor.png?width=75%&align=center)

现在，我们把这个因子单独拿出来用 Alphalens 回测下。
<!--
小市值效应中的幸存者偏差：The Delisting Bias in CRSP's Nasdaq Data and Its Implications for the Size Effect" (by Tyler Shumway and Vincent Warther) 

rolf banz 如何错过了低波动因子 https://www.rolfbanz.ch/2012/09/low-beta-anomaly-some-early-evidence/

毕业论文被大佬狂怼： http://www.jieyu.ai/blog/2024/09/12/rolf-banz/
-->

这一次，我们在获取到原始的市值数据的基础上，在进行因子检验之前，我们需要将它对数化。

```python
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)
universe = get_stock_list(start)
df = get_daily_basic(["mv"], start, end, universe)
factor = -1 * np.log(df["mv"])
factor.tail()
```

下面的代码将实现因子检验：

```python
barss = load_bars(start, end, tuple(universe))
prices = barss.price.unstack(level="asset")

merged = get_clean_factor_and_forward_returns(factor, prices)
create_returns_tear_sheet(merged)
```

从结果来看，即使在 2018 年之后，小市值效应还是非常突出，又尤其是从 2021 年以来。当然，以 2024 年的数据来看，这个效应就更加突出了。

## 3. 价值因子

```python
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)
universe = get_stock_list(start)

factor = get_daily_basic(["pb"], start, end, universe) * -1

barss = load_bars(start, end, tuple(universe))
prices = barss.price.unstack(level="asset")

merged = get_clean_factor_and_forward_returns(factor, prices, quantiles=10)
create_returns_tear_sheet(merged)
```

结果表明，直接使用这个因子，效果比较一般。年化 Alpha 只有 4.7%左右。如果是纯多，则年化是 4.2%，但 beta 高到-0.98。

这个因子的底层数据是收益每股净资产和每股市价。每股净资产是每季度更新一次，但不一定变动，市价则是每日更新。因此，在前后两次净资产更新期间，该因子不提供任何信息量，我们处理的都是噪声。

如果我们绘制 bm/pb 与收盘价的关系图，我们会发现两者走势在绝大多数时间是完全一致的，bm/pb 是收盘价的近似函数。因此，该指标的 beta 高达-0.97 也就不奇怪了。

如果我们改用月频或者季频来构建因子，情况会不会好一点？

下面的代码显示了如何构建月频估值因子。实现的关键是在对账面市值比数据重采样时，我们要使用 last，而对 prices 数据重采样时，要使用 open 序列的 first，并且对重采样之后的序列向前移位。

```python
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

universe = get_stock_list(start)
factor = get_daily_basic(["pb"], start, end, universe) * -1

# 构建月度因子
factor = 1 / factor["pb"]
factor = factor.unstack(level="asset").resample("M").last()
factor = factor.stack()

# 重构月度价格
barss = load_bars(start, end, tuple(universe))
prices = (barss.open.
                unstack(level="asset")
                .resample("M")
                .first()
                .shift(-1)
        )
prices.tail()
```

在得到合并的因子与远期收益数据之后，修改列名字：

```python
merged = get_clean_factor_and_forward_returns(factor, prices)
merged.rename(columns = {
    '1D': '21D',
    '5D': '105D',
    '10D': '210D'
},inplace=True)
create_returns_tear_sheet(merged)
```

月度数据检验的结果告诉我们，这个因子的表现仍然不佳。从公司财务的角度来理解，高账面市值比的公司意味着低市净率。但是很多低市净率的公司，都是短期业绩承压的公司，所以短期持有效果一般也不奇怪。从量化的角度来理解，这个因子充满了大量噪声，**它在绝大多数时间只携带了前一天收盘价这样的信息**。因此，没有较强的预测能力，一点也不奇怪。

## 4. 估值因子

一般来说，我们把 PE 值来作为估值因子。市盈率（Price-to-Earnings Ratio，简称 PE）是股票价格与每股收益（Earnings Per Share, EPS）的比率。它反映了市场对公司的估值水平。

$$\text{PE} = \frac{\text{股票价格}}{\text{每股收益}}$$

一般来说，低估值的公司有可能股价上涨，高估值的公司有可能股价下跌，从而完成价格对价值的回归。

但是，我们在使用 PE 值时，要注意这样两点：

1. 周期性行业，往往是高 PE 值（甚至为负数时）时值得买入；低 PE 值是则需要卖出。
2. 不同行业的基准 PE 值大不相同，因此，将不同行业的 PE 值放在一起当作因子比较并不合理，必须要进行行业中性化。
3. 与BM/PB指标一样，PE 值依赖盈利这一财务指标，它的发布周期是季度，因此在两次数据发布之间，它只携带了收盘价这样的信息。

PE 一般是以年为单位发布，这使得它的数据公布较迟缓。因此，我们常常参考另一个指标，即 PE_TTM，即滚动市盈率。在时间周期上，它仍然是以一年为单位进行计算，但是它是在 4 个季度的滑动窗口上计算和发布的，所以，更能及时反映公司的盈利水平。PE_TTM 在其它方面，具有与 PE 一样的优缺点。

考虑到 PE 和 PE_TTM 的这些特性，它并不会是一个好的截面因子，但是，在时序层面上对此进行研究是有效的。

```python
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)
universe = get_stock_list(start, code_only=True)
barss = load_bars(start, end, tuple(universe))

pe = get_daily_basic(["pe_ttm"], start, end, universe) * -1
prices = barss.price.unstack(level="asset")

merged = get_clean_factor_and_forward_returns(pe, prices)
create_returns_tear_sheet(merged)
```

从回测结果看，这是我们目前介绍过的因子中，线性条件符合得最差的之一。从公司财务的角度理解，对资源、有色、化肥等周期股，我们应该在PE最高（即最差的时候进行投资），而在PE最低（即最好的时候卖出），它遵循择时操作策略。而其它的个股，我们应该选择PE低的，这意味着他们的盈利能力更强。

因此，PE能给出的信号，对Alphalens这样的框架来说，是混乱的。

这并不意味着像PE这样的指标没有用处，相反，它们确实非常有用。一是从大盘指数的角度来看，每次大盘PE处在低位时，往往很快（短则一个月，迟则不到两个季度）迎来上涨；反之也亦然。

但是对于个股我们应该如何利用这一指标呢？既然PE里面包含了大量的噪声，那么，我们应该首先把这部分噪声移除掉。既然PE是价格除以收益，那么，我们用PE/close，就消除掉了噪声。然后，我们再来对照去噪后的数据/CLOSE，看看能不能找出规律。

```python
def deepinsight_fundamental(ticker: str, field, start, end, inverse = False):
    df = get_daily_basic([field, "close"], start, end, (ticker,))
    df = df.xs(ticker, level="asset")

    if inverse:
        df[field] = df["close"] / df[field]
    else:
        df[field] = df[field] / df["close"]

    df["d1"] = df[field].diff()
    df.ffill(inplace=True)
    _, ax = plt.subplots(figsize=(15, 5))
    ax.plot(df.index, df["d1"], label=f"{field}_denoise")
    ax2 = ax.twinx()
    ax2.plot(df.index, df["close"], label="close", color="r")
    ax.legend(loc=2)
    ax2.legend(loc=1)
    plt.show()

start = datetime.date(2005, 1, 1)
end = datetime.date(2014, 10,31)
deepinsight_fundamental("002714.XSHE", "pe_ttm", start, end)
```

如果把pe_ttm_denoise的上升边缘看成买入信号，下降边缘看成卖出信号，那么信号还是很明确。

对于这个标的，2014年大约在5元左右出现买入信号，2015年在12月左右出现卖出信号，但错过随后的大涨。2019年大约在40左右出现买入信号，卖出信号则出现在2022年中，价格在60-70元之间。

## 5. 盈利因子

盈利因子（Profitability Factor）是衡量公司盈利能力的一个重要指标。它通常用于股票分析和投资策略中，帮助投资者识别具有较高盈利能力和潜在增长潜力的公司。盈利因子可以基于多种财务指标来构建，常见的包括：

* 净资产收益率（ROE, Return on Equity），其计算公式是
  
  $$ \text{ROE} = \frac{\text{净利润}}{\text{每股净资产}} $$

ROE 衡量的是公司使用股东资本的效率，较高的 ROE 表示公司能够更有效地利用股东资本产生利润。
* 总资产收益率（ROA, Return on Assets），其计算公式为：

  $$ \text{ROA} = \frac{\text{净利润}}{\text{总资产}}$$

ROA 与 ROE 很相似，但有重大区别。两者的区别是，ROA 的分母是总资产，包含了负债。不同的行业，财务指标的评判指标也大不相同，这是一个典型地在寻找 alpha 时，要进行业中性化的例子。

* 营业利润率（Operating Margin），其计算公式是
  $$ \text{营业利润率} = \frac{\text{营业利润}}{\text{营业收入}} $$

营业利润率衡量的是公司在扣除营业成本和费用后，每单位收入产生的营业利润。

* 毛利率（Gross Margin）是在营业利润的基础上，再扣除直接生产成本之后的利润。其计算公式为：
  $$ \text{毛利率} = \frac{\text{毛利润}}{\text{营业收入}} $$
毛利率衡量的是公司在扣除直接生产成本后，每单位收入产生的毛利润。

在tushare中，这些数据可以通过fina_indicator来获取。注意，财务数据的归属日期一定是早于发布日期的。所以，我们在使用时，一定要使用发布日期。

我们通过下面的例子来演示如何获取这些数据:

```python
def get_roe(start, end, universe):
    dfs = []
    index = pd.bdate_range(start, end)
    for code in universe:
        symbol, ext = code.split(".")
        ts_code = f"{symbol}." + {"XSHE": "SZ", "XSHG": "SH"}.get(ext, ext)
        df = pro.fina_indicator(ts_code = ts_code)
        df["date"] = pd.to_datetime(df["ann_date"])
        roe = df.query("date>=@start and date<=@end")
        roe.set_index("date", inplace=True)

        # tushare返回数据存在重复
        roe = roe[~roe.index.duplicated(keep='last')]
        roe = roe.sort_index()
        roe = roe.reindex(index)
        roe["asset"] = code
        roe = roe.ffill()
        dfs.append(roe[["roe", "asset"]])
    df = pd.concat(dfs).set_index("asset", append=True)
    return df

start = datetime.date(2018, 1, 1)
end = datetime.date(2023,12,31)

# 请勿修改此行，否则会导致tushare封禁访问
universe = get_stock_list(start)[:200]

factors = get_roe(start, end, universe)
barss = load_bars(start, end, tuple(universe))
prices = barss["price"].unstack("asset")

merged = get_clean_factor_and_forward_returns(factors, prices)
create_returns_tear_sheet(merged)
```

要对这些数据进行Alphalens因子检验是困难的，需要进行大量的数据填充工作。在示例中，我们先是对获取的每一个资产的ROE数据，在前后两次公告之间，进行了前向填充。如果不这样做，我们得到的索引在freq上将会是None，后面与forward returns进行合并时，会出现无法对齐的情况（请参考Alphalens分析原理章节）。

通过这样的补齐，我们能让Alphalens正常工作了，但是它并没有正确的逻辑。比如，假设PAYH 2023年1季报是4月19日公布的，在公布前后，这个数据是有信息量的；二季报是7月3日公布的；在此期间，我们通过填充，让每一天都有了ROE数据，但这些数据中，多数没有信息量。但如果我们不进行填充，那么会导致Alphalens几乎会丢弃掉全部的数据（因为公司不会在同一天公布财报）。

<!--讲解：日期对齐，因子日期时间选择--未来数据-->

## 6. 投资因子
投资因子是五因子模型中的一个，用来衡量公司的资本支出和资产增长情况。具体来说，投资因子反映了公司投资活动的强度和效率。它定义为公司在一定时期内的资本支出与总资产的比例。具体计算公式为：

$$ \text{投资因子} = \frac{\Delta K + PPE}{K_{t-1}} $$

这里：

$(\Delta K)$ 表示净资本支出（即固定资产增加额）。
$(PPE)$ 表示购置、建造和租赁的财产、厂房和设备。
$(K_{t-1}$) 表示上一期的总资产。

高投资因子表明公司正在积极扩大其资产基础，可能意味着公司对未来增长持乐观态度。低投资因子则可能表明公司处于保守状态，或者市场环境不佳，公司减少投资以控制风险。

## 7. 另类因子
<!-- 掘金冷门数据  -->
另类因子（Alternative Factors）是指那些传统金融模型中未涵盖的、但能够提供额外收益或风险管理信息的因子。这些因子通常来源于非传统的数据源，如社交媒体、卫星图像、网络搜索数据等。另类因子旨在捕捉市场的非传统信号，以提高投资策略的多样性和有效性。

常见的数据来源有社交媒体舆情、卫星图像数据、网络搜索指数、企业内部数据（员工流动、客户反馈）、物联网数据等。

另类因子往往具有可解释性好、数据搜集成本高、生命周期短等等特点。比如，抖音热门舆情热点，与财经相关的舆情常常有很高的可解释性，但是获取成本高、并且抖音平台自身存在时间也不够久。未来如果出现其它更具人气的短视频平台，那么该因子的效率就会下降。类似的情况曾经发生在雪球、淘股吧和东财上面。

### 7.1. 社交媒体情绪因子

主要的财经平台有股吧（东财）、雪球、淘股吧、同花顺等。今日头条、抖音直播间等可关注。总得来说，获取难度较大，文本挖掘和分析难度较高。

tushare提供了新闻接口，它返回的数据有时间索引和文本内容。这里我们简单介绍下，如何分析文本。

!!! attention
    本环境中未安装jieba。如果你想运行下以示例，请自行安装.

    ! pip install jieba
    

下面的例了展示了如何找到最近高频出现的公司名：

```python
import jieba
from collections import Counter

stocks = get_stock_list(datetime.date(2024,11,1), code_only=False)
stocks = set(stocks.name)
for name in stocks:
    jieba.add_word(name)

news = pro.news(src='sina', start_date='2024-11-01', end_date='2024-11-31')
words = jieba.lcut("\n".join(news.content))
word_counts = Counter(words)

labels, counts = [], []
for word, count in word_counts.items():
    if word in stocks and count > 3:
        labels.append(word)
        counts.append(word_counts[word])

plt.figure(figsize=(10, 6))
plt.bar(labels, counts, color='skyblue')
plt.xlabel('词语')
plt.ylabel('出现次数')
plt.title('词频统计条形图')
plt.xticks(rotation=45)  # 旋转 x 轴标签以避免重叠
plt.tight_layout()  # 自动调整子图参数，使之填充整个图像区域
plt.show()
```

<!--作业：移动词频因子-->

### 7.2. 网络流量因子

之前主要是指百度搜索指数，微博热搜等，但这些流量下降较快。搜索指数主要看行业热度、突发事件（比如自然灾害、事故造成公司生产受损，从而竞争对手出现投资机会等）。

近年来，此类数据中，值得关注的有苹果游戏榜单。专注于游戏行业投资者必须要关注榜单变化。

### 7.3. 卫星图像因子

卫星图像和气象数据对农林业、畜牲业生产和价格有非常大的影响。近年来，在欧洲，对接光伏发电的虚拟电厂也大量运用气象数据来预测发电量，进行生产预案。这些是做商品期货必不可少的另类数据。

气象数据在全球是开放数据，但在国内某些气象数据即使能公开得到（在外网），也可能被有关部门认为是涉密，这是大家在使用中要注意的地方。

最大的气象数据源应该是[NOAA](https://www.noaa.gov)，它提供了大约2000亿行数据，70万个压缩包，包含了2.9万个站点从1901年到2018年底的气象数据。

此外还有[怀俄明大学的探空数据](http://weather.uwyo.edu/upperair/bufrraob.shtml)，一天发布两次。

<!--这是 CNN 网络得以应用的地方-->
### 7.4. 专利申请因子

其它还有一些另类因子，比如华泰[^huatai]这篇研报提出了以下因子:

1. 非交易行为数据（资讯点击量、自选股）
2. 比赛数据（私募大赛等）
3. 持仓数据（如外资席位、中信空头指数）等。其中北向资金曾是重要的指标，但因市场影响过大，现已被关闭。

## 8. Footnotes

[^fama]: Eugene F. Fama, Kenneth R. French,Common risk factors in the returns on stocks and bonds,Journal of Financial Economics.[论文下载](https://www.bauer.uh.edu/rsusmel/phd/Fama-French_JFE93.pdf)
[^joint-fama]: 聚宽【量化课堂】[Fama-French 三因子火锅](https://www.joinquant.com/view/community/detail/46084e11dc11457b9390fac67ae9a173)
[^下次一定]: [多因子模型--基于上证 50（学习笔记）](https://zhuanlan.zhihu.com/p/367158551)
[^bigquant]: [Fama-French 五因子模型](https://bigquant.com/wiki/doc/fama-french-McCtWEkXJY) 等。该网站上有多篇关于 Fama-French 模型的文章。
[^step4]: 这里也有选 Alpha 为负且最小的个股作为标的的做法，其原理是认为会发生回归，见聚宽量化课堂。
[^banz]: 小市值效应发表在 [The relationship between return and market value of common stocks](https://www.jieyu.ai/assets/ebooks/The-relationship-between-return-and-market-value-of-common-stocks.pdf) 一文中。
[^huatai]: [掘金冷门因子](https://pdf.dfcfw.com/pdf/H3_AP202204011556464427_1.pdf)
