
# 驭浪者 -- 顶底预测模型

<br>

在上一章中，我们介绍了一个短时的资产定价模型。它是由lightGBM构建的、基于回归任务的模型。它的原理是，我们找出某些特殊场景下的定价规律，根据这些规律来预测未来一个时期的股价（或者涨跌幅），再通过训练，让lightGBM知道在何种情况下可以运用这些规律来预测股价（或者涨跌幅），而何时不能。

基于这样的模型，我们可以买入预测涨幅较高的资产，卖出持仓中，预测不涨甚至下跌的资产，这样就构成了一个完整的交易模型。在这个模型中，我们基于趋势不变的假设。但实际上，股价短期的波动中，还存在另一个可能，即趋势反转：当股价沿着趋势波动到顶点后，它就会像钟摆一样，向着相反的方向运动。

这一章我们将介绍一个基于分类任务的lightGBM模型，探索如何通过分类来预测波动中的顶和底。


## 1. 策略实现

在上一章的模型中，我们对原始数据进行了简单的构造（即通过线性回归预测），就完成了特征和标签的提取。在本章的模型中，我们面临着同样的任务：即如何提取特征和标签，但答案显然是不一样的。

!!! attention
    一些常见的模型会把涨、跌作为一种分类标签。比如，如果涨幅大于零，就标记为标签1；反之，标记为标签0。但是，由于涨跌幅的波动有随机性，在这种方法下，一部分本应属于不涨不跌的记录，也不得不被标注为涨或者跌。因此，为了修正这种误差，他们采用了gap方法，即把绝对值小于某个阈值的记录，标记为0；把涨跌幅大于该阈值的，标记为1（即上涨）；其余的标记为-1（即下跌）。但是，这样仍然会有问题，因为无论阈值设为多少，都会在边界上存在误打标签的情况。<br>实际上，这种标签法扭曲了价格涨跌的本质。价格的涨跌的性质是连续的、而不是离散的。因此，任何试图把涨跌幅本身离散化为标签的作法，都是错误的。

我们的实现方法可以概括为：
1. 通过算法找出历史数据中的出现的顶和底，分别将它们标注成为1和-1，其余记录标记为0。
2. 对每一条记录，寻找提取最可能与预测反转相关的特征。
3. 将上述数据作为训练数据，构建lightGBM分类模型。

我们在前面的课程中介绍了很多因子。其中相当一部分是反转因子。此外，一些动量因子，也可能会对预测反转有所贡献，因为动量最强的时候，反转就不太容易发生。总之，在提取特征这一块，我们是有充足的素材的。

要如何找出历史数据中出现的顶和底呢？

### 1.1. 顶底查找算法

顶底查找算法最原始的思想来自于局部极值的确定。局部极值可以通过比较每个数据点与其相邻数据点的大小来确定。如果一个数据点比其前后相邻的数据点都大，则认为它是一个局部最大值（顶部）；如果比其前后相邻的数据点都小，则认为它是一个局部最小值（底部）。

显然，这种想法过于简陋，一旦数据有噪声，它将寻找出无数个局部极值。因此，我们需要对算法进行一些优化。常见的思路是，在指定的窗口大小内，只允许出现一个极值；只有当一个点超过附近的点达到某个阈值时，才认为它是一个极值。这里又可能将阈值划分为左阈值和右阈值。

寻找顶和底不是我们这里的个别需求，它是一个常用算法。因此，在许多常用Python库中，都有类似的实现，比如，在scipy.signal中，有find_peaks，argrelextrema等等。这里我们推荐的算法是zigzag库中的peak_valley_pivots。

!!! tip
    运行以下代码需要安装zigzag库。请通过 !pip install zigzag-reloaded 来安装。

```python
from zigzag import peak_valley_pivots

def peaks_and_valleys(ts, up_thres=None, down_thres=None):
    ma = moving_average(ts, 5, padding=False).astype(np.float64)

    if up_thres is None:
        pct = ma[1:] / ma[:-1] - 1
        std = np.std(pct)
        up_thres = 2 * std

    if down_thres is None:
        pct = ma[1:] / ma[:-1] - 1
        std = np.std(pct)
        down_thres = -2 * std

    pvs = peak_valley_pivots(ma, up_thres, down_thres)
    pvs[0] = 0
    pvs[-1] = 0
    return np.insert(pvs, 0, [0] * 4)


def show_peaks_and_valleys(ts, up_thres=None, down_thres=None):
    ma = moving_average(ts, 5, padding=True).astype(np.float64)

    pvs = peaks_and_valleys(ts, up_thres=up_thres, down_thres=down_thres)

    plt.plot(np.arange(len(ma)), ts, color="#49DBF5", label="close", alpha=0.2)
    plt.plot(np.arange(len(ma)), ma, color="#D77AF5", label="ma")

    # Plot peaks.
    peak_x = np.argwhere(pvs > 0).flatten()
    peak_y = ts[peak_x]
    plt.plot(peak_x, peak_y * 1.01, "v", label="Peaks", color="#8D50A1")

    # Plot valleys.
    valley_x = np.argwhere(pvs < 0).flatten()
    valley_y = ts[valley_x]
    plt.plot(valley_x, valley_y * 0.995, "^", label="Valleys", color="#36A2B5")

    plt.gca().spines.top.set_visible(False)
    plt.gca().spines.right.set_visible(False)
    plt.legend()


payh = "000001.XSHE"
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
bars = load_bars(start, end, ("000001.XSHE",)).xs(payh, level="asset")

show_peaks_and_valleys(bars["close"])

```

zigzag通过peak_valley_pivots函数，返回的是一组标志，如果标志为1，则表示该点是一个顶点；如果标志为-1，则表示该点是一个底点。其它部分都为0。这里要注意一点，它的返回值中，最开始一个点和最后一个点，一定是1或者-1，但这些标志并不稳定，需要在训练之前去掉这些点。

zigzag的可取之处，就是它使用百分比来作为阈值（而不是绝对值），这更能反应我们的意图。在代码中，还有比较重要的一点，就是它使用了自适应的阈值。这个自适应的阈值，是通过区间涨跌幅的标准差来确定的。

当然，如果我们想把活干得再细致一些，你应该注意到，zigzag使用的阈值是固定的。在一个很长的时间序列中，如果我们分段提取峰谷，这样使用的阈值就会更准确一些。

下面，我们就介绍一个图形化标注工具来实现顶和底的查找，这个工具允许我们对zigzag找出的顶和底进行修正，并且运用了分段查找的方法。

### 1.2. 标注工具

在 AI 训练过程中，图形化标注工具是数据预处理阶段的关键，它能够帮助标注人员对图像、视频、音频等数据进行标注，为模型训练提供高质量的标注数据。有一些有名的图像标注工具，比较LabelImg， LabelMe等。但是，我们仍有必要掌握简单的标注工具的开发，因为我们所处的领域目前还没有这样一款标注工具可用。

作为Python开发者，我们可以使用H2O wave, Plotly Dash或者Streamit来开发此类工具，不过，最简单的工具要属通过notebook中的widgets。

下面，我们先展示这个工具的全部代码，然后进行解释。

<Example id="labeling"/>

```python
import plotly.graph_objects as go
import traceback
from ipywidgets import Button, HBox, VBox, Textarea, Layout, HTML, Output, Box
from IPython.display import display
import arrow

code = "000852.XSHE"
start = datetime.date(2005, 1, 1)
end = datetime.date(2023, 12, 31)

bars = load_bars(start, end, (code,)).xs(code, level="asset").reset_index()

i = 0
frames = bars["date"]
close = bars["close"]
labelled_data = None

last_pv = []

recs = {}


def log(msg):
    global info

    info.clear_output()
    with info:
        if isinstance(msg, Exception):
            traceback.print_exc()
        else:
            print(msg)


def keep(b):
    global peaks_box, valleys_box, recs, last_pv, i, frames

    recs.update({arrow.get(frame).naive: 1 for frame in peaks_box.value.split("\n")})
    recs.update({arrow.get(frame).naive: -1 for frame in valleys_box.value.split("\n")})

    last_valley = arrow.get(valleys_box.value.split("\n")[-1])
    last_peak = arrow.get(peaks_box.value.split("\n")[-1])

    if last_valley > last_peak:
        last_pv = ["谷", last_valley.naive]
    else:
        last_pv = ["峰", last_peak.naive]

    i = np.argwhere(frames == last_pv[1]).flatten()[0]
    show_bars(None)


def save(b):
    global bars, recs, labelled_data, info
    labelled_data = pd.DataFrame(bars)
    labelled_data["flag"] = 0

    for frame, flag in recs.items():
        labelled_data.loc[labelled_data.date == frame, "flag"] = flag

    log(f"存入{len(recs)}条记录到数据文件中")
    with open("pv-labels.pkl", "wb") as f:
        pickle.dump(labelled_data, f)
        log(f"成功保存")

    recs = {}


def goback(b):
    global i, last_pv

    i -= 120
    if i < 0:
        return

    last_pv = []
    show_bars(None)

def show_bars(b):
    global i, frames, close, info, figbox, last_pv

    if len(last_pv) > 0:
        log(f"上一个顶点: {last_pv[0]} {last_pv[1]}")

    if i >= len(bars):
        log("已遍历完成")
        return
    else:
        s = i
        e = i + 120

        try:
            flags = peaks_and_valleys(close[s:e])
            cs = Candlestick(
                bars[s:e].to_records(), height=600, ma_groups=[5, 10, 20], show_rsi=False
            )

            x_peaks = np.argwhere(flags > 0).flatten()
            y = bars[s:e]["high"][x_peaks + i] * 1.005

            cs.add_marks(x_peaks, y, "peaks", marker="triangle-down", color="red")

            x_valleys = np.argwhere(flags < 0).flatten()
            y = bars[s:e]["low"][x_valleys + i] * 0.995
            cs.add_marks(x_valleys, y, "valleys", marker="triangle-up", color="green")
            
            fig = go.FigureWidget(cs.figure)
            figbox.children = (fig,)

            peaks = frames[s:e][x_peaks + i]
            if len(last_pv):
                peaks = peaks[peaks > last_pv[1]]
            peaks_box.value = "\n".join([f"{frame}" for frame in peaks])

            valleys = frames[s:e][x_valleys + i]
            if len(last_pv):
                valleys = valleys[valleys > last_pv[1]]
            valleys_box.value = "\n".join([f"{frame}" for frame in valleys])
        except Exception as e:
            log(e)

backward_button = Button(
    description="上一组",
)

keep_button = Button(description="记录 > 下一组")

save_button = Button(description="保存")

info = Output(layout=Layout(width="40%"))

peaks_box = Textarea(
    value="",
    placeholder="请输入峰值时间，每行一个",
    description="峰值时间",
    layout=Layout(width="40%", height="100px"),
)

valleys_box = Textarea(
    value="",
    placeholder="valley moments here",
    description="请输入谷值时间，每行一个",
    layout=Layout(width="40%", height="100px"),
)

figbox = Box(layout=Layout(width="100%"))
buttons = HBox((backward_button, keep_button, save_button, info))
inputs = HBox((peaks_box, valleys_box))
display(VBox((buttons, inputs, figbox)))

keep_button.on_click(keep)
save_button.on_click(save)
backward_button.on_click(goback)
show_bars(None)
```

当你运行这段代码时，屏幕上会立即出现以下控件和显示区：

1. 第一行由『上一组』、『记录 > 下一组』、『保存』三个按钮和一个信息提示区组成。信息提示区会显示上一组顶点信息，或者类似保存成功等信息。
2. 接下来是两个编辑框。左侧是当前区间内，已经捕捉到的峰值顶点信息，右侧是当前区间内，已经捕捉到的谷值顶点信息。这两个区域的数据都可以手动编辑。你可以增加和删除记录。比如，你可能想把自动寻找到的顶点的前几天的记录也标记为顶点，也可能想删除一些对波动过于敏感的记录。
3. 接下来就是k线显示区。在这里，显示了K线图，以及zigzag自动寻找到的顶点。通过可视化，我们就可以判断zigzag找到的顶点是否正确，以及是否要进行手动修正。

这个小工具的运作方式是，一开始，它会加载最初的120个bars，并标注一些峰谷记录。我们对照下面的k线图来进行校验，增加、删除一些记录，然后按下『记录 > 下一组』按钮，这会加载后面的120个bars（会适当重叠），人工进行新一轮校对，直到走完全部预设区间后，我们就点击『保存』以保存记录。

现在，你可以随意点击几次『记录 > 下一组』按钮，再点击『保存』。然后通过下面的代码来查看保存的记录：

```python
with open("pv-labels.pkl", "rb") as f:
    df = pickle.load(f)

df
```

这些记录中，最重要的是日期和flag，其它信息都可以重建。

现在，我们就来解读一下，这个小工具的基本框架。

首先，我们需要导入 ipywidgets 和 IPython.display 库：

```python
import ipywidgets as widgets

from IPython.display import display
```

display的作用是显示控件。

接下来，我们需要理解布局功能。

#### 1.2.1. 构建基本布局

在ipywidgets中，可以通过HBox和VBox这两种布局控件来构建最基础的布局。我们可以认为，HBox代表网格布局中的一行，而VBox代表网格布局中的一列。每一个HBox或者VBox又能容纳一个或者多个HBox/VBox，通过这样层层嵌套，最终就能构成有一定复杂度的界面。

```python
from ipywidgets import widgets

# 创建两个文本输入框和一个按钮
text_input1 = widgets.Text(description="输入1:")
text_input2 = widgets.Text(description="输入2:")
button = widgets.Button(description="提交")

# 使用 HBox 将它们水平排列
form = widgets.HBox([text_input1, text_input2, button])

display(form)
```

在示例代码[](#example-labeling)中，我们先是用HBox将两个文本输入框组成一行；这两个输入框允许我们直接输入峰谷的日期；然后，用另一个HBox将按钮放在同一行。最后，我们用VBox将这两行，以及一个显示图形的Box容器组合起来，使得它们成一列三行显示。

#### 1.2.2. 初始化部件

每个部件（widget）都可以通过传递参数来初始化，它们的参数属性基本上可以参考同类的Html控件，比如，多数控件都有value, placeholder等属性。比较重要的是，控件往往都有layout属性，它允许我们单独设置控件的样式，如宽度、高度、边距等。

一些默认属性，比如value，它的值与对应的显示内容之间是双向映射关系。也即，如果修改了属性的值，那么界面上显示的内容就会相应改变。除了value之外，一些控件存在自己独特的双向映射属性，比如，在整数滑块(IntSlider)中，存在value, min和max属性。后端修改了min和max之后，前端滑块的范围就会相应改变。对于按钮部件，它存在一个disabled属性，如果修改了它的值，那么前端状态也会相应改变，比如，从能点击变为不能点击，外观也变成看起来处于禁用状态。

部件本身是Python对象，所以，我们可以为它设置任意属性。不过，这样的属性是不支持双向映射的。

#### 1.2.3. 更新部件的属性

在交互中，我们常常需要更新部件的某些属性，并且向用户显示。比如，在示例中，一旦我们按下『记录>下一组』按钮，我们就需要加载新的行情数据，并更新图形。

一般情况下，我们可以通过部件的python对象来引用属性，并且给它赋值。比如，在示例中：

```bash
    peaks_box.value = "\n".join([f"{frame}" for frame in peaks])
```

如果我们更新的属性是双向映射属性，这样，控件的显示就会立即得到更新。在上述示例中，当peaks_box的value属性被更新，我们能观察到控件马上更新了显示。

在这里有一个特殊的控件，即绘制k线图的控件。它从根本上说，是一个plotly的Candlestick，在我们的课程中，对它进行了简单的封装，但仍然使用了Candlestick的名字。

!!! info
    plotly的Candlestick有两个主要的不足，首先，它默认用绿色表示上涨，红色表示下跌；其次，它使用datetime作为x轴，这样会在节假日前后，导致k线图不连续。

在Notebook中，可以直接显示plotly的Candlestick，但是，它不能直接与widgets系统一起使用。在代码中，我们先是通过plotly自身提供的go.FigureWidget对它进行了封装，然后将它作为布局控件figbox的子元素，这样才能正确显示k线图，并使其能响应按钮的更新。

!!! attention
    show_bars这段代码中，涉及到绘制k线图、增加标记的部分，甚至对代码的顺序都有严格要求。这是我们在notebook widgets系统中，使用第三方widgets时，可能要多留意的地方。第三方widgets常常不会像原生widgets那样开箱即用。

### 1.3. 构建模型
#### 1.3.1. 模型基类

```python
import datetime
import logging
from numpy import ndarray
from numpy.typing import ArrayLike, NDArray
import os
import pickle
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from numpy.lib.stride_tricks import as_strided
import lightgbm as lgb
from zigzag import peak_valley_pivots


logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)
mlogger = logging.getLogger("matplotlib")
# supress matplotlib info level logging, which is anoying
mlogger.setLevel(logging.WARNING)


class WaveRiderBase:
    def __init__(self):
        self._model_ = None
        self._name_: str | None = None
        self._desc_: str | None = None

        self._features_ = []
        # for encode/decode categorical features
        self._label_encoders_ = {}

    @property
    def model(self):
        return self._model_

    @property
    def features(self):
        return self._features_

    def save(self, path: str, auto_rename: bool = True, name: str | None = None):
        """将模型保存到指定路径。

        如name未指定，将使用模型名作为文件名。如果同名文件已存在，将自动重命名为当前时间戳。文件名会自动加pkl后缀
        Args:
            path: 保存路径
            auto_rename: 如果同名文件已存在，是否自动重命名
            name: 保存文件名
        """
        if self._model_ is None:
            raise ValueError("请先调用fit方法训练模型")

        if not os.path.exists(os.path.expanduser(path)):
            raise ValueError("保存路径不存在")

        name = name or self._name_
        if name is None:
            raise ValueError("请指定保存文件名")

        file = os.path.join(path, f"{name}.pkl")
        if os.path.exists(file):
            if auto_rename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"{name}_{timestamp}.pkl"
                file = os.path.join(path, name)
                assert not os.path.exists(file)
                logger.warning("保存路径已存在同名文件，已自动重命名为%s", name)
            else:
                raise ValueError("保存路径已存在同名文件，请指定指定文件名")

        with open(file, "wb") as f:
            pickle.dump(
                {
                    "model": self._model_,
                    "name": self._name_,
                    "desc": self._desc_,
                    "save_time": datetime.datetime.now(),
                },
                f,
            )

    def load(self, file_path: str):
        with open(file_path, "rb") as f:
            data = pickle.load(f)
            self._model_ = data["model"]
            self._name_ = data["name"]
            self._desc_ = data["desc"]
            logger.info("加载模型%s成功。", self._name_)

    def rolling_time_series(self, ts: NDArray, win: int) -> NDArray:
        """生成rolling time series

        Args:
            ts: 一维时间序列
            win: 窗口大小
        Returns:
            返回shape为（len(ts) - win + 1, win）的二维numpy数组
        """
        stride = ts.strides
        shape = (len(ts) - win + 1, win)
        strides = stride + stride
        return as_strided(ts, shape, strides)

    def encode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """将类别特征编码。

        类别特征可能需要编码解码。此时需要label_encoders属性保存编码器。此方法会自动保存编码器到label_encoders属性中。
        """
        if name in self._label_encoders_:
            logger.info("已经存在%s特征编码器，直接复用", name)
            return self._label_encoders_[name].transform(feature)

        self._label_encoders_[name] = LabelEncoder()
        return self._label_encoders_[name].fit_transform(feature)

    def decode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """解码经类别编码器编码的序列"""
        if name not in self._label_encoders_:
            raise ValueError(f"不存在{name}特征编码器，无法进行解码")

        return self._label_encoders_[name].inverse_transform(feature)

    def train_test_split(
        self,
        data: pd.DataFrame,
        features: List[str],
        group_id: str | None = None,
        cuts=(0.7, 0.2),
    ) -> Tuple[pd.DataFrame | pd.Series]:
        """对时间序列进行train, valid和test子集划分。

        与sklearn的train_test_split不同，此方法会根据时间进行划分，而不是随机划分。

        请事前对data进行排序。如果group_id为None，则直接对data进行划分；否则，先按group_id进行分组，
        再在组内进行划分，最后合并成与data同样shape的DataFrame

        Args:
            data: 时间序列数据，应包含features中的所有列，以及名为target的列
            group_id: 根据此列进行分组，在组内划分子集
            features: 在data中，哪些列是特征列
            cuts: 训练集、验证集、测试集的比例

        Returns:
            返回X_train, X_valid, X_test, y_train, y_valid, y_test
        """
        logger.info("trained with features: %s", features)

        def cut_time_series(group, cuts):
            itrain = int(len(group) * cuts[0])
            ival = itrain + int(len(group) * cuts[1])

            return (group.iloc[:itrain], group.iloc[itrain:ival], group.iloc[ival:])

        if group_id is None:
            train, valid, test = cut_time_series(data, cuts)
            return (
                train[features],
                valid[features],
                test[features],
                train["target"],
                valid["target"],
                test["target"],
            )

        # 按group_id进行分组，在组内划分，最后合并成与data同样shape的DataFrame
        train, valid, test = [], [], []
        for item in data.groupby(level=group_id).apply(cut_time_series, cuts=cuts):
            train.append(item[0])
            valid.append(item[1])
            test.append(item[2])

        train = pd.concat(train)
        valid = pd.concat(valid)
        test = pd.concat(test)

        return (
            train[features],
            valid[features],
            test[features],
            train["target"],
            valid["target"],
            test["target"],
        )

    def peaks_and_valleys(self, ts, up_thres=None, down_thres=None):
        # 分类任务标签需要从0开始
        if len(ts) < 5 * 4:
            return [1] * len(ts)

        ma = moving_average(ts, 5, padding=False).astype(np.float64)

        if up_thres is None:
            pct = ma[1:] / ma[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std
            down_thres = -2 * std

        if down_thres is None:
            pct = ma[1:] / ma[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std
            down_thres = -2 * std

        pvs = peak_valley_pivots(ma, up_thres, down_thres) + 1
        pvs[0] = 1
        pvs[-1] = 1
        return np.insert(pvs, 0, [1] * 4)

    def features_and_labels(self, barss) -> pd.DataFrame:
        data = barss.groupby("asset", level=1).apply(self.extract_features_and_labels)
        return data.droplevel(0)

    def train(
        self,
        barss: pd.DataFrame,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
        peak_weight: int = 1
    ):
        params = {"random_state": 42}

        train_params["objective"] = "multiclass"
        train_params["metric"] = "multi_logloss"
        # 多分类时，需要设置分类数
        train_params["num_class"] = 3

        if "feval" in train_params:
            feval = train_params["feval"]
            del train_params["feval"]
        else:
            feval = None

        params.update(train_params)
        evals_result = {}

        data = self.features_and_labels(barss)
        X_train, X_val, X_test, y_train, y_val, y_test = self.train_test_split(
            data, self.features
        )

        class_weights = {
            0: peak_weight,
            1: 1,
            2: peak_weight,
        }

        sample_weights = np.array([class_weights[y] for y in y_train])
        train_data = lgb.Dataset(X_train, label=y_train, weight=sample_weights)
        valid_data = lgb.Dataset(X_val, label=y_val)

        if self._model_ is not None:
            logger.warning("Model already trained, retraining...")

        self._model_ = lgb.train(
            params,
            train_data,
            num_boost_round=epochs,
            valid_sets=[valid_data],
            feval=feval,
            categorical_feature=list(self._label_encoders_.keys()),
            callbacks=[
                lgb.early_stopping(early_stop_round),
                lgb.record_evaluation(evals_result),
            ],
        )

        report = self.eval_model(X_test, y_test)
        return self.model, report

    def predict(self, X: ArrayLike) -> ndarray:
        assert self._model_ is not None, "Model not trained"
        return self._model_.predict(X)

    def eval_model(self, X_test, y_true):
        """对模型进行评估"""
        y_pred = self.predict(X_test)
        y_pred = np.argmax(y_pred, axis=1)

        class_report = classification_report(y_true, y_pred,output_dict=True)
        cm = confusion_matrix(y_true, y_pred)

        plt.figure(figsize=(5, 3))

        sns.heatmap(cm, annot=True, fmt="d", cmap="Blues")

        plt.xlabel("Predicted labels")
        plt.ylabel("True labels")
        plt.title("Confusion Matrix")

        # 显示图形
        plt.show()

        return pd.DataFrame(class_report).transpose()

class WaveRiderV1(WaveRiderBase):
    def extract_features_and_labels(self, group) -> pd.DataFrame:
        rsi_win = 6
        if len(group) < rsi_win * 3:
            group["rsi"] = np.nan
        else:
            rsi = ta.RSI(group["close"], rsi_win)
            rsi[: rsi_win * 3] = np.nan
            group["rsi"] = rsi[rsi_win * 3 :]

        group["uo"] = ta.ULTOSC(
            group.high,
            group.low,
            group.close,
            timeperiod1=7,
            timeperiod2=14,
            timeperiod3=28,
        )
        group["wr"] = (group.close - group.high) / (group.high - group.low)
        group["wr_3_high"] = (
            group["high"]
            / group["high"].rolling(window=3, min_periods=3).max().shift(1)
            - 1
        )
        group["wr_3_low"] = (
            group["low"] / group["low"].rolling(window=3, min_periods=3).min().shift(1)
            - 1
        )

        self._features_ = ["wr", "wr_3_high", "wr_3_low", "uo", "rsi"]
        group["target"] = self.peaks_and_valleys(group["close"].values)

        return group[self.features + ["target"]]        


rider = WaveRiderV1()

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 31)

barss = load_bars(start, end)
modle, cr = rider.train(barss, 100)
cr
```

我们得到的分类报告，大致上是，预测为谷底的精确率是13.3%，召回率是0.2%；预测为峰顶的精确率是和召回率都是0。

如何对这个模型进行改进呢？最容易想到的方法就是增加特征。下面，我们就开发第二版，增加一阶动量和二阶动量两个特征，看看是否有改进。

#### 1.3.2. v2

```python
class WaveRiderV2(WaveRiderBase):
    def extract_features_and_labels(self, group) -> pd.DataFrame:
        rsi_win = 6
        if len(group) < rsi_win * 3:
            group["rsi"] = np.nan
        else:
            rsi = ta.RSI(group["close"], rsi_win)
            rsi[: rsi_win * 3] = np.nan
            group["rsi"] = rsi[rsi_win * 3 :]

        group["uo"] = ta.ULTOSC(
            group.high,
            group.low,
            group.close,
            timeperiod1=7,
            timeperiod2=14,
            timeperiod3=28,
        )
        group["wr"] = (group.close - group.high) / (group.high - group.low)
        group["wr_3_high"] = (
            group["high"]
            / group["high"].rolling(window=3, min_periods=3).max().shift(1)
            - 1
        )
        group["wr_3_low"] = (
            group["low"] / group["low"].rolling(window=3, min_periods=3).min().shift(1)
            - 1
        )

        group["d1"] = group.close.pct_change().rolling(window=3, min_periods=3).mean()
        group["d2"] = group.close.pct_change().diff().rolling(window=3, min_periods=3).mean()

        self._features_ = ["wr", "wr_3_high", "wr_3_low", "uo", "rsi", "d1", "d2"]
        group["target"] = self.peaks_and_valleys(group["close"].values)

        return group[self.features + ["target"]]

rider = WaveRiderV2()

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 31)

barss = load_bars(start, end)
modle, cr = rider.train(barss, 100)
cr
```

现在，我们看到，预测谷底的精确率从13.3%提升到40%，召回率从0.2%提升到1.5%。峰值预测的精确率也大幅提高到47.3%。这个进步还是很显著的。我们可以沿着这一思路，把自己的看盘经验逐条加进来。每增加一条经验，模型的性能就有可能进一步提升。

#### 1.3.3. v3
除了增加特征之外，我们还可以尝试对标签进行增强。请看原始的自动标注情况：

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/20250213173426.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

peaks_and_valleys找到的顶底有时会有延迟。标签错误的影响远远大于特征不足的影响。下面，我们尝试对标签进行修正。修正的方法是，在每一个找到的顶点，我们在该点前后2个点内查找真正的顶点，并把新顶点及前后各1个数据点都标注为顶点；对谷底也是同样操作。这样一类，我们也部分减轻了原来存在的类别不平衡问题。


```python
class WaveRiderV3(WaveRiderBase):
    def peaks_and_valleys(self, ts, up_thres=None, down_thres=None):
        """
        使用 zigzag 库生成顶和底的标签，并进行修正。
        """
        if len(ts) < 5:
            return np.ones(len(ts))

        ma = moving_average(ts, 5, padding=False).astype(np.float64)

        if up_thres is None:
            pct = ma[1:] / ma[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std

        if down_thres is None:
            pct = ma[1:] / ma[:-1] - 1
            std = np.std(pct)
            down_thres = -2 * std

        pvs = peak_valley_pivots(ma, up_thres, down_thres)
        pvs[0] = 0
        pvs[-1] = 0
        pvs = np.insert(pvs, 0, [0] * 4)

        # 修正峰值和谷值
        peaks = np.where(pvs > 0)[0]
        valleys = np.where(pvs < 0)[0]

        corrected_pvs = np.zeros_like(pvs)

        for peak in peaks:
            if peak < 3:
                corrected_pvs[peak] = 1
            else:
                # 在峰值附近5个数据点内查找最高点
                window_start = max(0, peak - 2)
                window_end = peak + 2
                max_high_idx = np.argmax(ts[window_start:window_end]) + window_start
                corrected_pvs[max_high_idx] = 1
                # 设置最高点前后各1个点为1
                if max_high_idx > 0:
                    corrected_pvs[max_high_idx - 1] = 1
                if max_high_idx < len(ts) - 1:
                    corrected_pvs[max_high_idx + 1] = 1

        for valley in valleys:
            if valley < 3:
                corrected_pvs[valley] = -1
            else:
                # 在谷值附近5个数据点内查找最低点
                window_start = max(0, valley - 2)
                window_end = valley + 2
                min_low_idx = np.argmin(ts[window_start:window_end]) + window_start
                corrected_pvs[min_low_idx] = -1
                # 设置最低点前后各1个点为-1
                if min_low_idx > 0:
                    corrected_pvs[min_low_idx - 1] = -1
                if min_low_idx < len(ts) - 1:
                    corrected_pvs[min_low_idx + 1] = -1

        return corrected_pvs + 1

    def extract_features_and_labels(self, group) -> pd.DataFrame:
        rsi_win = 6
        if len(group) < rsi_win * 3:
            group["rsi"] = np.nan
        else:
            rsi = ta.RSI(group["close"], rsi_win)
            rsi[: rsi_win * 3] = np.nan
            group["rsi"] = rsi[rsi_win * 3 :]

        group["uo"] = ta.ULTOSC(
            group.high,
            group.low,
            group.close,
            timeperiod1=7,
            timeperiod2=14,
            timeperiod3=28,
        )
        group["wr"] = (group.close - group.high) / (group.high - group.low)
        group["wr_3_high"] = (
            group["high"]
            / group["high"].rolling(window=3, min_periods=3).max().shift(1)
            - 1
        )
        group["wr_3_low"] = (
            group["low"] / group["low"].rolling(window=3, min_periods=3).min().shift(1)
            - 1
        )

        group["d1"] = group.close.pct_change().rolling(window=3, min_periods=3).mean()
        group["d2"] = group.close.pct_change().diff().rolling(window=3, min_periods=3).mean()

        self._features_ = ["wr", "wr_3_high", "wr_3_low", "uo", "rsi", "d2", "d1"]
        group["target"] = self.peaks_and_valleys(group["close"].values)

        return group[self.features + ["target"]]

rider = WaveRiderV3()

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 31)

barss = load_bars(start, end)
modle, cr = rider.train(barss, 100)
cr
```

用修正过的数据进行训练，现在，它找到的波谷达到了75个之多，波峰达到了84个之多，有了大幅增长。波峰和波谷预测的精确率和召回率都进一步增加。

这一结果说明了标签对模型性能影响，也揭示了我们上一节介绍的标注工具的作用。尽管机器标注似乎可以比人工做得更『准确』，但是，它不一定能找出真正有意义、能得到解释的顶和底。

## 2. 算法优化

本章介绍的模型还有不少优化空间。通过加入更多的因子、以及看盘经验，这个模型可以得到很好的优化。在前面我们学习了几百种因子，这些都可以加入进来。

这里介绍一些优化方向：

### 2.1. 样本平衡

在v3版本中，我们对样本平衡进行了一些尝试，但是，最终得到的训练数据，仍然是显著不平衡的，这对机器学习非常不利。我们可以在特征提取之后，手动进行欠采样以平衡类别。

### 2.2. 多周期及微观数据

在示例中，我们只使用了日线数据。有时候，股价见顶是因为股票在分钟（比如30分）、日线、周线、月线的技术指标上同时见顶。如果技术指标在大级别上还没有见顶，只是在次级别上见顶，此时只要市场氛围好，上涨动量足，股价就会在次级别上进行调整，修复技术指标后再次上攻，直到大的级别上技术指标也见顶。基于这个原理，我们在提取特征时，就应该考虑不同周期级别上的特征。

微观数据在判断短期博弈力道上有重要的作用。比如，股价多次上冲前高不过，就往往需要洗盘积蓄力量。在我们的示例中，只判断了上冲的动作，并没有把上冲的次数统计进来。这需要更微观一些的数据。

又比如，高开低走大阴线也往往是短期见顶的特征，这也是一个没有出现在技术指标中的微观特征。

### 2.3. 市场氛围

在讲聚类算法<ref>[聚类：寻找 Pair Trading 标的](17.md)</ref>那一章，我们发现在通过DBSCAN聚类后，往往存在一个size相当大的类。实际上，它们就是没有主力，随市场波动的那些个股。多数个股都是跟随市场波动的，有主力运作的毕竟是少数。因此，我们也可以把在特征中加入多个指数，把指数的顶底也作为特征之一。

### 2.4. id作为特征

有一些个股很明显是有主力运作的。其特征是，它们主业业绩不佳，多年来总会蹭上各种与主营业务无关的热点题材，并且公司还会配合发布一些相关的重组、收购信息配合，但最终证明这些消息都是虚假传闻。

对于这样的股票，主力常常是固定的，操作手法也不会有太大的改变。因此，我们可以把股票id，也即它的证券编码作为特征之一。这样训练出来的模型即能把握共性，也可能根据个股的特征作出响应。
