<!DOCTYPE html><html><head>
      <title>syllabus</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:////Users/<USER>/.vscode/extensions/shd101wyy.markdown-preview-enhanced-0.8.15/crossnote/dependencies/katex/katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:<PERSON>sol<PERSON>,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */
.markdown-preview.markdown-preview {
  /* 基本样式 */
  /* 标题样式 */
  /* 内容样式 */
}
.markdown-preview.markdown-preview img {
  width: calc(100%);
  margin: 0 auto;
  display: block;
}
.markdown-preview.markdown-preview img[alt="25%"] {
  width: 25%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}
.markdown-preview.markdown-preview img[alt="33%"] {
  width: 33%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}
.markdown-preview.markdown-preview img[alt="50%"] {
  width: 50%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}
.markdown-preview.markdown-preview img[alt="66%"] {
  width: 66%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}
.markdown-preview.markdown-preview img[alt="75%"] {
  width: 75%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}
.markdown-preview.markdown-preview img[alt="100%"] {
  width: 75%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}
.markdown-preview.markdown-preview img[alt="R50"] {
  position: relative;
  float: right;
  width: 45%;
  margin-left: 2vw !important;
}
.markdown-preview.markdown-preview img[alt="R33"] {
  position: relative;
  float: right;
  width: 33%;
  margin-left: 2vw !important;
}
.markdown-preview.markdown-preview img[alt="L50"] {
  position: relative;
  float: left;
  width: 45%;
  margin-right: 2vw !important;
}
.markdown-preview.markdown-preview img[alt="L33"] {
  position: relative;
  float: left;
  width: 33%;
  margin-right: 2vw !important;
}
.markdown-preview.markdown-preview .language-tip {
  background-color: rgba(255, 255, 255, 0.05);
  border-left: 0.4em solid #218838;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
  padding: 0 0 1em 0;
  margin: 10px 0;
  border-radius: 4px;
  font-family: sans-serif;
  font-size: 14px;
  line-height: 1.5;
}
.markdown-preview.markdown-preview .language-tip:before {
  content: "💎  Tip";
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
  background-color: rgba(0, 192, 165, 0.1);
  border-bottom: 1px solid rgba(0, 191, 165, 0.2);
  line-height: 2.5em;
  padding-left: 1.5em;
}
.markdown-preview.markdown-preview .language-tip code {
  white-space: pre-wrap;
  /* 使代码块中的文本可以换行 */
  word-wrap: break-word;
  /* 长单词或 URL 地址自动换行 */
  display: block;
  font-family: monospace;
  font-size: 14px;
  color: #333;
  margin-top: 1em;
}

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<style>

.cols {
    column-count: 2;
    column-gap: 2em;
}

h1 {
    font-weight: 400 !important;
}

h2 {
    margin-top: 2em;
}

h3 {
    color: #303030 !important;
    font-weight: 200 !important;
    font-size: 1.2em;
}

h4 {
    color: #808080 !important;
    font-weight: 100 !important;
    font-size: 1em;
    margin-left: 1em;
}

h5 {
    display: none;
}

.module {
    text-align: center;
    font-size: 2em;
    margin: 2em 0;
}

more {
    font-size: 0.75em;
    color: #808080;
    /* border: 1px solid #ccc; */
    margin: 2.5em 0 -1em 0;
    position: relative;
    cursor: pointer;
    /* min-height: 2em; */
    display: inline-block;
}

more::before {
    content: '课程要点 >';
    position: absolute;
    /* top: 50%; */
    /* left: 100%; */
    transform: translate(0, -50%);
    width: 5em;
    height: 3em;
    /* background-color: #ccc; */
    transition: transform 0.3s ease;
}

more.expanded::before {
    content: '';
}

more > p {
    display: none;
    transition: height 0.5s ease; /* 平滑过渡效果 */
}

more.expanded > p {
    display: block;
}

hr {
    height: 1px !important;
    color: #ddd !important;
    border: none;
    background-image: linear-gradient(to right, 
                    rgba(0, 0, 0, 0.2), 
                    rgba(0,0,0,0));
    background-repeat: no-repeat;
    width: 80%;
}

@media only screen and (max-width: 1024px) {
  .md-sidebar-toc {
    display:none;
    width: 0;
  }
  
  .cols {
      column-count: 1;
    }
    
  .markdown-preview {
      left: 0px !important;
      width: 100% !important;
  }
}

</style>
<script>
var sidebarTOCBtn = document.getElementById('sidebar-toc-btn');
document.body.setAttribute('html-show-sidebar-toc', true);

document.addEventListener('DOMContentLoaded', function() {
    const moreElements = document.querySelectorAll('more');

    moreElements.forEach(element => {
        element.addEventListener('click', function() {
            element.classList.toggle('expanded');
        });
    });
});
</script>
<p>§ 因子投资与机器学习策略</p>
<h1 style="text-align:center">课程大纲 </h1>
<div class="cols">
<p><a href="#declaration">大纲说明</a></p>
<h2 id="1-导论">1. 导论 </h2>
<h3 id="11-因子投资的起源">1.1. 因子投资的起源 </h3>
<h3 id="12-寻找-alpha">1.2. 寻找 Alpha </h3>
<h3 id="13-从-capm-拓展到多因子">1.3. 从 CAPM 拓展到多因子 </h3>
<h3 id="14-从因子分析到因子投资">1.4. 从因子分析到因子投资 </h3>
<h3 id="15-从因子模型到交易策略">1.5. 从因子模型到交易策略 </h3>
<h3 id="16-关于课程编排">1.6. 关于课程编排 </h3>
<more>
<p>这门课面向的对象是专业的量化研究员、或者打算向这个方向转岗求职、或者尽管是其它职业，但决心以专业、严谨的态度探索量化研究的学习者。</p>
<p>学完这门课程并完全掌握其内容，你将具有熟练的因子分析能力、掌握领先的机器学习策略构建方法，成为有创新研究能力和比较竞争优势的量化研究员。</p>
<p>课程内容涵盖了因子挖掘、因子检验到构建机器学习模型的过程。如果要独立从事交易，还需要补充学习《量化 24 课》。</p>
</more>
<hr>
<h2 id="2-因子预处理流程">2. 因子预处理流程 </h2>
<h3 id="21-因子数据的来源">2.1. 因子数据的来源 </h3>
<h3 id="22-因子生成">2.2. 因子生成 </h3>
<h3 id="23-因子预处理">2.3. 因子预处理 </h3>
<h4 id="231-异常值处理">2.3.1. 异常值处理 </h4>
<h4 id="232-缺失值处理">2.3.2. 缺失值处理 </h4>
<h4 id="233-分布调整">2.3.3. 分布调整 </h4>
<h4 id="234-标准化">2.3.4. 标准化 </h4>
<h4 id="235-中性化">2.3.5. 中性化 </h4>
<more>
<p>这一章及下一章是因子检验的基础。我们将结合大量的示例代码，介绍因子检验的基本原理与技术实现细节，为后面理解Alphalens因子分析框架打下坚实基础。</p>
</more>
<hr>
<h2 id="3-因子检验方法">3. 因子检验方法 </h2>
<h3 id="31-回归法">3.1. 回归法 </h3>
<h3 id="32-ic-分析法">3.2. IC 分析法 </h3>
<h3 id="33-分层回溯法">3.3. 分层回溯法 </h3>
<h3 id="34-因子检验的代码实现">3.4. 因子检验的代码实现 </h3>
<h4 id="341-生成因子进一步模块化">3.4.1. 生成因子：进一步模块化 </h4>
<h4 id="342-因子预处理接入真实的数据">3.4.2. 因子预处理：接入真实的数据 </h4>
<h4 id="343-计算远期收益">3.4.3. 计算远期收益 </h4>
<h4 id="344-回归分析的实现">3.4.4. 回归分析的实现 </h4>
<h4 id="345-ic-分析法实现">3.4.5. IC 分析法实现 </h4>
<h4 id="346-分层回溯法实现">3.4.6. 分层回溯法实现 </h4>
<h3 id="35-三种方法的区别与联系">3.5. 三种方法的区别与联系 </h3>
<more>
<p>本章介绍了回归法、IC法和分层回溯法的原理及实现代码。这一章完成后，你已经可以自己实现一个简单的因子分析框架了。这对理解Alphalens的实现非常有帮助。</p>
</more>
<hr>
<h2 id="4-初识-alphalens">4. 初识 Alphalens </h2>
<h3 id="41-斜率因子定义和实现">4.1. 斜率因子：定义和实现 </h3>
<h3 id="42-如何为-alphalens-计算因子和收集价格数据">4.2. 如何为 Alphalens 计算因子和收集价格数据 </h3>
<h3 id="43-alphalens-如何实现数据预处理">4.3. Alphalens 如何实现数据预处理 </h3>
<h3 id="44-因子分析与报表生成">4.4. 因子分析与报表生成 </h3>
<h3 id="45-参考文献">4.5. 参考文献 </h3>
<more>
<p>Alphalens将因子检验过程进行了高度的抽象，把我们在前面两章讲到的步骤封装成两个函数，而大量的定制则是通过参数来实现。我们将介绍Alphalens要求的输入数据格式、它是如何通过参数来控制分层、缺失值处理、远期回报计算等行为的。</p>
<p>通过这一章的学习，你将掌握Alphalens最基本的用法。</p>
</more>
<hr>
<h2 id="5-alphalens-报表分析">5. Alphalens 报表分析 </h2>
<h3 id="51-收益分析">5.1. 收益分析 </h3>
<h4 id="511-alpha-和-beta">5.1.1. Alpha 和 Beta </h4>
<h4 id="512-分层收益均值图">5.1.2. 分层收益均值图 </h4>
<h4 id="513-分层收益-violin-图">5.1.3. 分层收益 Violin 图 </h4>
<h4 id="514-因子加权多空组合累计收益图">5.1.4. 因子加权多空组合累计收益图 </h4>
<h4 id="515-收益的分层驱动">5.1.5. 收益的分层驱动 </h4>
<h4 id="516-多空组合收益的稳健性">5.1.6. 多空组合收益的稳健性 </h4>
<h3 id="52-事件分析">5.2. 事件分析 </h3>
<h3 id="53-ic-分析">5.3. IC 分析 </h3>
<h3 id="54-换手率分析">5.4. 换手率分析 </h3>
<h3 id="55-参考文献">5.5. 参考文献 </h3>
<more>
<p>Alphalens的报告并非不言自明。比如，它没有告诉你Alpha和beta各是什么单位，bps单位又是多少；它更不会告诉你，什么样的Alpha是好的，什么样的Alpha则是好到不能相信；有一些报表，它的计算方式与你想像的、或者曾听说的不太一样。</p>
<p>为了准确地理解这些报告，我们使用了三种方式：1. 阅读、调试源码的方式。通过这种方式，我们发现bps是万分之一，定义在plotting.py这个文件中；2. 使用合成数据，这样我们理解了最好的因子理论上应该产生什么样的图表报告；3. 通过Github issues, Quantopian社区Archive的文档，从其它用户的问题中找到答案。</p>
<p>这将是现阶段全网惟一一个真正讲透了Alphalens的教程。</p>
</more>
<hr>
<h2 id="6-alphalens-高级技巧1">6. Alphalens 高级技巧（1） </h2>
<h3 id="61-排除功能性错误">6.1. 排除功能性错误 </h3>
<h4 id="611-过时的-alphalens-版本">6.1.1. 过时的 Alphalens 版本 </h4>
<h4 id="612-maxlossexceederror">6.1.2. MaxLossExceedError </h4>
<h4 id="613-时区问题">6.1.3. 时区问题 </h4>
<h3 id="62-因子的单调性">6.2. 因子的单调性 </h3>
<h3 id="63-再谈收集价格数据">6.3. 再谈收集价格数据 </h3>
<h3 id="64-该如何分析日线以上级别的因子">6.4. 该如何分析日线以上级别的因子？ </h3>
<h3 id="65-深入因子分层">6.5. 深入因子分层 </h3>
<h4 id="651-有确定交易信号的因子">6.5.1. 有确定交易信号的因子 </h4>
<h4 id="652-离散值因子">6.5.2. 离散值因子 </h4>
<more>
<p>这一章我们将介绍如何排除Alphalens在使用中可能遇到的错误，既有程序性的，也有逻辑性的。我们还介绍了如何进行日线以上级别的因子分析。很多网上教程甚至都没意识到这里会存在问题，因为他们从来没有做过这个级别的分析。</p>
<p>我们还深入探讨了Alphalens的分层机制，包括如何处理因子值是离散值的情况。</p>
</more>
<hr>
<h2 id="7-alphalens-高级技巧2">7. Alphalens 高级技巧（2） </h2>
<h3 id="71-重构因子检验过程">7.1. 重构因子检验过程 </h3>
<h3 id="72-参数调优拯救你的因子">7.2. 参数调优：拯救你的因子 </h3>
<h4 id="721-修正因子方向">7.2.1. 修正因子方向 </h4>
<h4 id="722-过滤非线性分层">7.2.2. 过滤非线性分层 </h4>
<h4 id="723-使用最佳分层方式">7.2.3. 使用最佳分层方式 </h4>
<h4 id="724-grid-search">7.2.4. Grid Search </h4>
<h3 id="73-过拟合检测方法">7.3. 过拟合检测方法 </h3>
<h4 id="731-样本外检测">7.3.1. 样本外检测 </h4>
<h4 id="732-绘制参数高原">7.3.2. 绘制参数高原 </h4>
<h3 id="74-关于多空组合的思考">7.4. 关于多空组合的思考 </h3>
<more>
<p>使用Alphalens进行因子检验，就像做一场面试一样，你得尽可能暴露因子的潜能，然后才能评估它的好坏。这一章我们将介绍如何想尽办法把因子的潜能挖掘出来，同时，又不要受过拟合的欺骗。除了样本外检测之外，我们还会教你通过绘制参数高原来评估因子的过拟合程度。</p>
<p>可视化很重要。尤其是你的工作，需要与他人合作时。</p>
</more>
<hr>
<h2 id="8-alpha101-因子介绍">8. Alpha101 因子介绍 </h2>
<h3 id="81-alpha101-因子中的数据和算子">8.1. Alpha101 因子中的数据和算子 </h3>
<h3 id="82-alpha101-因子解读">8.2. Alpha101 因子解读 </h3>
<h3 id="83-如何实现-alpha101-因子">8.3. 如何实现 Alpha101 因子？ </h3>
<more>
<p>Alpha101因子库是World Quant发表于2015年的一个因子库。其中有80%的因子是在世坤正式使用（发布时间）的。我们将介绍如何读懂Alpha101因子的公式，实现它的算子。</p>
<p>整个因子库的实现已经有较好的开源库，我们也将介绍。这会成为你的兵器库中的宝贝之一。</p>
</more>
<hr>
<h2 id="9-talib-技术因子">9. Talib 技术因子 </h2>
<h3 id="91-ta-lib-函数分组">9.1. Ta-lib 函数分组 </h3>
<h3 id="92-冷启动期-unstable-periods">9.2. 冷启动期 (unstable periods) </h3>
<h3 id="93-震荡类指标">9.3. 震荡类指标 </h3>
<h4 id="931-rsi">9.3.1. RSI </h4>
<h4 id="932-adx---平均方向运动指数">9.3.2. ADX - 平均方向运动指数 </h4>
<h4 id="933-apo---绝对价格震荡指标">9.3.3. APO - 绝对价格震荡指标 </h4>
<h4 id="934-ppo---百分比价格震荡指标">9.3.4. PPO - 百分比价格震荡指标 </h4>
<h4 id="935-aroon-振荡器">9.3.5. Aroon 振荡器 </h4>
<h4 id="936-money-flow-index">9.3.6. Money Flow Index </h4>
<h4 id="937-balance-of-power">9.3.7. Balance of Power </h4>
<h4 id="938-williams-r">9.3.8. William's R </h4>
<h4 id="939-stochastic-随机振荡指标">9.3.9. Stochastic 随机振荡指标 </h4>
<h3 id="94-成交量指标">9.4. 成交量指标 </h3>
<h4 id="941-chaikin-ad-line">9.4.1. Chaikin A/D Line </h4>
<h4 id="942-obv">9.4.2. OBV </h4>
<h3 id="95-波动性指标">9.5. 波动性指标 </h3>
<h4 id="951-atr-与-natr---平均真实波幅">9.5.1. ATR 与 NATR - 平均真实波幅 </h4>
<h3 id="96-8-种移动平均线">9.6. 8 种移动平均线 </h3>
<h3 id="97-overlap-研究">9.7. Overlap 研究 </h3>
<h4 id="971-布林带">9.7.1. 布林带 </h4>
<h4 id="972-hilbert-趋势线和-sine-wave-indicator">9.7.2. Hilbert 趋势线和 Sine Wave Indicator </h4>
<h4 id="973-parabolic-sar">9.7.3. Parabolic Sar </h4>
<h3 id="98-momentum-指标">9.8. Momentum 指标 </h3>
<more>
<p>Alpha101因子多数是量价因子，由于可以想到的原因，它没有重复早已存在多年的经典技术因子，但这些因子仍然有它的Alpha存在。这一节我们会简单介绍下talib库，讲解技术指标的冷启动期 -- 可能是一个比较冷的知识，冷启动期不止是NaN，比如，RSI的冷启动期就比较长，是win参数的3倍。</p>
<p>Talib的技术指标很多，我们会每类介绍几个，重点介绍在新的技术条件下，如何翻新这些因子。以RSI为例，我们会讲intelli-RSI，Connor's RSI。这样你不仅得到了一些新因子，还提升了自己创新研究的能力。</p>
<p>即使是一些有经验的人，也可能是初次听说我们讲要介绍的一些因子。比如像Hilbert Sine Wave，这可是在Trading View等平台上比较好卖的付费技术指标之一。</p>
</more>
<hr>
<h2 id="10-其它量价因子">10. 其它量价因子 </h2>
<h3 id="101-小概率事件">10.1. 小概率事件 </h3>
<h3 id="102-最大回撤">10.2. 最大回撤 </h3>
<h3 id="103-pct_rank">10.3. pct_rank </h3>
<h3 id="104-波动率">10.4. 波动率 </h3>
<h3 id="105-z-score">10.5. z-score </h3>
<h3 id="106-夏普率">10.6. 夏普率 </h3>
<h3 id="107-一阶导因子">10.7. 一阶导因子 </h3>
<h3 id="108-二阶导因子">10.8. 二阶导因子 </h3>
<h3 id="109-频域因子">10.9. 频域因子 </h3>
<h3 id="1010-tsfresh-因子库">10.10. TSFresh 因子库 </h3>
<h3 id="1011-行为金融学因子">10.11. 行为金融学因子 </h3>
<h4 id="10111-整数关口因子">10.11.1. 整数关口因子 </h4>
<h4 id="10112-冲压失败因子">10.11.2. 冲压失败因子 </h4>
<h4 id="10113-缺口因子">10.11.3. 缺口因子 </h4>
<h4 id="10114-遗憾规避理论因子">10.11.4. 遗憾规避理论因子 </h4>
<more>
<p>有一些小概率因子很容易做出来。也许正因为是这样的原因，它们没有名字，也没有上论文的机会。但是它们的Alpha真实存在。比如指数单日最大跌幅、最大连续跌幅等等。其背后的原理是极端事件之后的概率回归。</p>
<p>总之，这是比较炫技和创新的一章。我们会介绍二阶导因子、频域因子、行为金融学因子。比如，频域因子是通过快速傅里叶变换或者小波变换，找出主力资金的操作周期来进行预测的因子。在其他人还停留在使用小波平滑噪声的阶段，我们已经开始使用它来探索主力资金的规律了！</p>
</more>
<hr>
<h2 id="11-基本面因子和另类因子">11. 基本面因子和另类因子 </h2>
<h3 id="111-famma-五因子">11.1. Famma 五因子 </h3>
<h4 id="1111-市场因子">11.1.1. 市场因子 </h4>
<h4 id="1112-规模因子">11.1.2. 规模因子 </h4>
<h4 id="1113-价值因子">11.1.3. 价值因子 </h4>
<h4 id="1114-盈利因子">11.1.4. 盈利因子 </h4>
<h4 id="1115-投资因子">11.1.5. 投资因子 </h4>
<h3 id="112-另类因子">11.2. 另类因子 </h3>
<h4 id="1121-社交媒体情绪因子">11.2.1. 社交媒体情绪因子 </h4>
<h4 id="1122-网络流量因子">11.2.2. 网络流量因子 </h4>
<h4 id="1123-卫星图像因子">11.2.3. 卫星图像因子 </h4>
<h4 id="1124-专利申请因子">11.2.4. 专利申请因子 </h4>
<h3 id="113-爬虫的技术路线">11.3. 爬虫的技术路线 </h3>
<more>
<p>这一部分我们讲思路会比较多。因为另类因子要么去买，要么去爬。但我们不想讲爬虫。</p>
</more>
<hr>
<h2 id="12-因子挖掘方法">12. 因子挖掘方法 </h2>
<h3 id="121-新因子从哪里来">12.1. 新因子从哪里来 </h3>
<h3 id="122-网络资源">12.2. 网络资源 </h3>
<h3 id="123-因子正交性检测">12.3. 因子正交性检测 </h3>
<h3 id="124-谈谈因子动物园">12.4. 谈谈因子动物园 </h3>
<more>
<p>这也是谈天谈地比较务虚的一章，但依然干货满满。我们会谈一些找资源的方法，比如怎么找论文、数据等。到现在为止，我们已经介绍了好几百个因子（不算参数和周期），所以，我们也需要看看究竟有多少因子是独立的。所以，我们会介绍正交性检测方法。</p>
</more>
<hr>
<h2 id="13-机器学习概述">13. 机器学习概述 </h2>
<h3 id="131-机器学习分类">13.1. 机器学习分类 </h3>
<h4 id="1311-机器学习-深度学习-强化学习">13.1.1. 机器学习、深度学习、强化学习 </h4>
<h4 id="1312-监督无监督和强化学习">13.1.2. 监督无监督和强化学习 </h4>
<h4 id="1313-回归与分类">13.1.3. 回归与分类 </h4>
<h3 id="132-机器学习模型简介">13.2. 机器学习模型简介 </h3>
<h3 id="133-机器学习三要素">13.3. 机器学习三要素 </h3>
<h3 id="134-机器学习基本流程">13.4. 机器学习基本流程 </h3>
<h3 id="135-机器学习应用场景">13.5. 机器学习应用场景 </h3>
<more>
<p>机器学习快速入门。世界是连续的，还是量子？这是一个古老的哲学问题，也决定了机器学习的基本模型 -- 回归还是分类？</p>
</more>
<hr>
<h2 id="14-机器学习核心概念">14. 机器学习核心概念 </h2>
<h3 id="141-偏差-方差">14.1. 偏差、方差 </h3>
<h3 id="142-过拟合与正则化惩罚">14.2. 过拟合与正则化惩罚 </h3>
<h3 id="143-损失函数-目标函数和度量函数和距离函数">14.3. 损失函数、目标函数和度量函数和距离函数 </h3>
<h4 id="1431-损失函数和目标函数">14.3.1. 损失函数和目标函数 </h4>
<h4 id="1432-度量函数">14.3.2. 度量函数 </h4>
<h4 id="1433-距离函数">14.3.3. 距离函数 </h4>
<more>
<p>这门课程是一门应用课程，不想涉及太多理论，但如果一点原理都不懂，就只能照搬照抄示例，无法进行任何拓展。因此，我们决定选择跟应用层密切相关的基本概念进行讲解 -- 只有了解了这些概念，我们才懂得如何选择目标函数，如何评估策略，如何防止过拟合等等。</p>
</more>
<hr>
<h2 id="15-sklearn-通用工具包">15. SKLearn 通用工具包 </h2>
<h3 id="151-数据预处理preprocessing">15.1. 数据预处理：preprocessing </h3>
<h3 id="152-metrics">15.2. metrics </h3>
<h3 id="153-模型解释与可视化">15.3. 模型解释与可视化 </h3>
<h3 id="154-内置数据集">15.4. 内置数据集 </h3>
<more>
<p>sklearn 是一个非常强大的机器学习库，以丰富的模型和简单易用的接口赢得大家的喜爱。在这一章，我们先向大家介绍 sklearn 的通用工具包 -- 用来处理无论我们采用什么样的算法模型，都要遇到的那些共同问题，比如数据预处理、模型评估、模型解释与可视化和内置数据集。</p>
</more>
<hr>
<h2 id="16-模型优化">16. 模型优化 </h2>
<h3 id="161-优化概述">16.1. 优化概述 </h3>
<h3 id="162-k-fold-cross-validation">16.2. k-fold cross validation </h3>
<h3 id="163-参数搜索">16.3. 参数搜索 </h3>
<h4 id="1631-网格搜索">16.3.1. 网格搜索 </h4>
<h4 id="1632-随机搜索">16.3.2. 随机搜索 </h4>
<h4 id="1633-贝叶斯优化">16.3.3. 贝叶斯优化 </h4>
<h3 id="164-rolling-forecasting">16.4. Rolling Forecasting </h3>
<more>
<p>量化领域的机器学习有它自己的特殊性，比如在交叉验证方面，我们实际上要使用的是一种称为 Rolling Forecasting（也称为 Walk-Forward Optimization 的方法）。</p>
</more>
<hr>
<h2 id="17-聚类寻找-pair-trading-标的">17. 聚类：寻找 Pair Trading 标的 </h2>
<h3 id="171-聚类算法概述">17.1. 聚类算法概述 </h3>
<h3 id="172-hdbscan-算法原理">17.2. HDBSCAN 算法原理 </h3>
<h3 id="173-寻找-pair-trading-标的">17.3. 寻找 Pair Trading 标的 </h3>
<h4 id="1731-hdbscan-示例">17.3.1. HDBSCAN 示例 </h4>
<h4 id="1732-结果评估">17.3.2. 结果评估 </h4>
<h4 id="1733-配对选择">17.3.3. 配对选择 </h4>
<more>
<p>在量化交易中，Pair Trading 是一类重要的套利策略，它的先决条件是找出能够配对的两个标的。这一章我们将介绍先进的 HDBSCAN 聚类方法，演示如何通过它来实现聚类，然后通过 statsmodels 中的相关方法来执行协整对检验，找到能够配对的标的。最后，我们还将演示如何将这一切组成一个完整的交易策略。</p>
<p>这将是你学会的第一个有效的机器学习策略。</p>
</more>
<hr>
<h2 id="18-从决策树到-lightgbm">18. 从决策树到 LightGBM </h2>
<h3 id="181-决策树">18.1. 决策树 </h3>
<h4 id="1811-决策树分类---httpsscikit-learnorgstablemodulestreehtmlclassification--">18.1.1. 决策树分类 <!-- https://scikit-learn.org/stable/modules/tree.html#classification --> </h4>
<h4 id="1812-决策树回归">18.1.2. 决策树回归 </h4>
<h3 id="182-lightgbm">18.2. LightGBM </h3>
<h4 id="1821-熟悉训练数据">18.2.1. 熟悉训练数据 </h4>
<h4 id="1822-构建第一个分类器">18.2.2. 构建第一个分类器 </h4>
<h4 id="1823-可视化特征重要性">18.2.3. 可视化特征重要性 </h4>
<h4 id="1824-查看模型树">18.2.4. 查看模型树 </h4>
<h4 id="1825-交叉验证">18.2.5. 交叉验证 </h4>
<h4 id="1826-调优">18.2.6. 调优 </h4>
<more>
<p>受限于金融数据的高噪声，现阶段端到端的交易策略还不太可行；又受限于标注数据的大小，深度学习等人工智能模型也不适用于交易策略的构建。在机器学习模型当中，目前最优秀的模型就是梯度提升决策树模型。代表实现是XGBoost和LightGBM。</p>
<p>由于LightGBM在多数任务上，无论是速度还是准确率都超越了XGBoost，所以，我们的课程将重点介绍LightGBM。</p>
<p>这一章将完整地介绍LightGBM模型，并且通过示例来演示如何使用、如何inspect和visualize生成的模型，如何执行交叉验证和参数调优。</p>
</more>
<hr>
<h2 id="19-基于-lightgbm-回归模型的价格预测">19. 基于 LightGBM 回归模型的价格预测 </h2>
<h3 id="191-策略原理">19.1. 策略原理 </h3>
<h3 id="192-策略实现">19.2. 策略实现 </h3>
<h3 id="193-策略优化思路">19.3. 策略优化思路 </h3>
<more>
<p>资产定价是量化研究的核心问题之一，如果能够给出资产的合理定价，那么就能给出交易信号。</p>
<p>定价是个回归问题。尽管很难实现端到端的价格预测模型，我们还是以巧妙的构思，做出来一个可以预测未来价格的回归模型（理论上能自洽）。</p>
<p>我们不能保证这个模型总是有效的，有许多改进方案我们还没来得及探索，但是，以此为出发点，你在机器学习交易模型构建上，已经占据了领先优势。</p>
</more>
<hr>
<h2 id="20-基于-lightgbm-分类模型的交易策略">20. 基于 LightGBM 分类模型的交易策略 </h2>
<h3 id="201-策略实现">20.1. 策略实现 </h3>
<h4 id="2011-顶底查找算法">20.1.1. 顶底查找算法 </h4>
<h4 id="2012-标注工具">20.1.2. 标注工具 </h4>
<h5 id="20121-基本布局">20.1.2.1. 基本布局 </h5>
<h5 id="20122-初始化">20.1.2.2. 初始化 </h5>
<h5 id="20123-部件更新">20.1.2.3. 部件更新 </h5>
<h4 id="2013-构建模型">20.1.3. 构建模型 </h4>
<h5 id="20131-模型基类">20.1.3.1. 模型基类 </h5>
<h5 id="20132-v2">20.1.3.2. V2 </h5>
<h5 id="20133-v3">20.1.3.3. V3 </h5>
<h3 id="202-算法优化">20.2. 算法优化 </h3>
<h4 id="2021-样本平衡">20.2.1. 样本平衡 </h4>
<h4 id="2022-多周期及微观数据">20.2.2. 多周期及微观数据 </h4>
<h4 id="2023-市场氛围">20.2.3. 市场氛围 </h4>
<h4 id="2024-id作为特征">20.2.4. id作为特征 </h4>
<more>
<p>在这一章，我们将构建一个基于 LightGBM 分类模型的交易模型。换句话说，它不负责预测价格，但能告诉你应该买入、还是卖出信号。学完这一章，你一定会认同，模型肯定就该这么构建，剩下的都是工作量而已：你需要构建系统、标注数据、构建特征，然后训练模型。</p>
</more>
<hr>
<h2 id="21-未来新世界">21. 未来新世界 </h2>
<h3 id="211-如何获得免费算力---标注数据量决定--">21.1. 如何获得免费算力 <!-- 标注数据量决定 --> </h3>
<h3 id="212-cnn-价格预测">21.2. CNN 价格预测 </h3>
<h4 id="2121-如何为训练提供数据">21.2.1. 如何为训练提供数据 </h4>
<h4 id="2122-如何构建特征数据">21.2.2. 如何构建特征数据 </h4>
<h4 id="2123-如何定义模型">21.2.3. 如何定义模型 </h4>
<h4 id="2124-训练">21.2.4. 训练 </h4>
<h4 id="2125-生产部署">21.2.5. 生产部署 </h4>
<h4 id="2126-cnn的原理与性能优化">21.2.6. CNN的原理与性能优化 </h4>
<h3 id="213-transformer">21.3. Transformer </h3>
<h3 id="214-reinforcement-learning">21.4. Reinforcement Learning </h3>
<h3 id="215-其它重要智能算法">21.5. 其它重要智能算法 </h3>
<h4 id="2151-kalman-filter">21.5.1. kalman filter </h4>
<h4 id="2152-genentic-algo">21.5.2. Genentic Algo </h4>
<more>
<p>前面讲过为什么深度学习还不太适合构建量化交易模型。这一章前面部分，我们会通过一个CNN预测价格的例子，来说明为什么。了解了这些局限之后，也许你能够发明一种新颖的模型，适合量化交易。这一部分没能教你可带走的工具和经验。但是如果你是研究型、创新型的人，你也会觉得这一部分内容也非常有价值。</p>
<p>强化学习是我们比较看好的一个方向，特别是用在商品期货和加密货币交易中。我们会介绍一些入门知识和学习资源。</p>
<p>还有两个重要的智能算法，既不是机器学习，也不是深度学习或者强化学习，但在量化中确实也比较常用，就是kalman filter和genetic algo，不过，这一部分我们没有代码，把更多的探索空间留给了你。。</p>
</more>
</div>
<hr>
<h2 id="declaration">说明</h2>
<p>1. 本大纲并非课程教材目录，比如，课程中许多章节有《延伸阅读》小节，未在此显示。</p>
<p>2. 课程内容还包括习题，未在此显示</p>
<p>3. 课程内容还包括补充材料，比如完整的 Alpha101因子实现代码（从数据获取、因子提取、因子检验到回测）及其它示例代码，未在此显示</p>

      </div>
      <div class="md-sidebar-toc">
<div class="md-toc">
<details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#1-导论" class="md-toc-link"><ol>
<li>导论</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#11-因子投资的起源" class="md-toc-link">
            <p>1.1. 因子投资的起源</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#12-寻找-alpha" class="md-toc-link">
            <p>1.2. 寻找 Alpha</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#13-从-capm-拓展到多因子" class="md-toc-link">
            <p>1.3. 从 CAPM 拓展到多因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#14-从因子分析到因子投资" class="md-toc-link">
            <p>1.4. 从因子分析到因子投资</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#15-从因子模型到交易策略" class="md-toc-link">
            <p>1.5. 从因子模型到交易策略</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#16-关于课程编排" class="md-toc-link">
            <p>1.6. 关于课程编排</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#2-因子预处理流程" class="md-toc-link"><ol start="2">
<li>因子预处理流程</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#21-因子数据的来源" class="md-toc-link">
            <p>2.1. 因子数据的来源</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#22-因子生成" class="md-toc-link">
            <p>2.2. 因子生成</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#23-因子预处理" class="md-toc-link"><p>2.3. 因子预处理</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#231-异常值处理" class="md-toc-link">
            <p>2.3.1. 异常值处理</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#232-缺失值处理" class="md-toc-link">
            <p>2.3.2. 缺失值处理</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#233-分布调整" class="md-toc-link">
            <p>2.3.3. 分布调整</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#234-标准化" class="md-toc-link">
            <p>2.3.4. 标准化</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#235-中性化" class="md-toc-link">
            <p>2.3.5. 中性化</p>

          </a></div>
        </div>
      </details>
    
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#3-因子检验方法" class="md-toc-link"><ol start="3">
<li>因子检验方法</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#31-回归法" class="md-toc-link">
            <p>3.1. 回归法</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#32-ic-分析法" class="md-toc-link">
            <p>3.2. IC 分析法</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#33-分层回溯法" class="md-toc-link">
            <p>3.3. 分层回溯法</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#34-因子检验的代码实现" class="md-toc-link"><p>3.4. 因子检验的代码实现</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#341-生成因子进一步模块化" class="md-toc-link">
            <p>3.4.1. 生成因子：进一步模块化</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#342-因子预处理接入真实的数据" class="md-toc-link">
            <p>3.4.2. 因子预处理：接入真实的数据</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#343-计算远期收益" class="md-toc-link">
            <p>3.4.3. 计算远期收益</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#344-回归分析的实现" class="md-toc-link">
            <p>3.4.4. 回归分析的实现</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#345-ic-分析法实现" class="md-toc-link">
            <p>3.4.5. IC 分析法实现</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#346-分层回溯法实现" class="md-toc-link">
            <p>3.4.6. 分层回溯法实现</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#35-三种方法的区别与联系" class="md-toc-link">
            <p>3.5. 三种方法的区别与联系</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#4-初识-alphalens" class="md-toc-link"><ol start="4">
<li>初识 Alphalens</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#41-斜率因子定义和实现" class="md-toc-link">
            <p>4.1. 斜率因子：定义和实现</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#42-如何为-alphalens-计算因子和收集价格数据" class="md-toc-link">
            <p>4.2. 如何为 Alphalens 计算因子和收集价格数据</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#43-alphalens-如何实现数据预处理" class="md-toc-link">
            <p>4.3. Alphalens 如何实现数据预处理</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#44-因子分析与报表生成" class="md-toc-link">
            <p>4.4. 因子分析与报表生成</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#45-参考文献" class="md-toc-link">
            <p>4.5. 参考文献</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#5-alphalens-报表分析" class="md-toc-link"><ol start="5">
<li>Alphalens 报表分析</li>
</ol>
</a>
          </summary>
        <div>
          <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#51-收益分析" class="md-toc-link"><p>5.1. 收益分析</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#511-alpha-和-beta" class="md-toc-link">
            <p>5.1.1. Alpha 和 Beta</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#512-分层收益均值图" class="md-toc-link">
            <p>5.1.2. 分层收益均值图</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#513-分层收益-violin-图" class="md-toc-link">
            <p>5.1.3. 分层收益 Violin 图</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#514-因子加权多空组合累计收益图" class="md-toc-link">
            <p>5.1.4. 因子加权多空组合累计收益图</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#515-收益的分层驱动" class="md-toc-link">
            <p>5.1.5. 收益的分层驱动</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#516-多空组合收益的稳健性" class="md-toc-link">
            <p>5.1.6. 多空组合收益的稳健性</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#52-事件分析" class="md-toc-link">
            <p>5.2. 事件分析</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#53-ic-分析" class="md-toc-link">
            <p>5.3. IC 分析</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#54-换手率分析" class="md-toc-link">
            <p>5.4. 换手率分析</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#55-参考文献" class="md-toc-link">
            <p>5.5. 参考文献</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#6-alphalens-高级技巧1" class="md-toc-link"><ol start="6">
<li>Alphalens 高级技巧（1）</li>
</ol>
</a>
          </summary>
        <div>
          <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#61-排除功能性错误" class="md-toc-link"><p>6.1. 排除功能性错误</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#611-过时的-alphalens-版本" class="md-toc-link">
            <p>6.1.1. 过时的 Alphalens 版本</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#612-maxlossexceederror" class="md-toc-link">
            <p>6.1.2. MaxLossExceedError</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#613-时区问题" class="md-toc-link">
            <p>6.1.3. 时区问题</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#62-因子的单调性" class="md-toc-link">
            <p>6.2. 因子的单调性</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#63-再谈收集价格数据" class="md-toc-link">
            <p>6.3. 再谈收集价格数据</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#64-该如何分析日线以上级别的因子" class="md-toc-link">
            <p>6.4. 该如何分析日线以上级别的因子？</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#65-深入因子分层" class="md-toc-link"><p>6.5. 深入因子分层</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#651-有确定交易信号的因子" class="md-toc-link">
            <p>6.5.1. 有确定交易信号的因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#652-离散值因子" class="md-toc-link">
            <p>6.5.2. 离散值因子</p>

          </a></div>
        </div>
      </details>
    
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#7-alphalens-高级技巧2" class="md-toc-link"><ol start="7">
<li>Alphalens 高级技巧（2）</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#71-重构因子检验过程" class="md-toc-link">
            <p>7.1. 重构因子检验过程</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#72-参数调优拯救你的因子" class="md-toc-link"><p>7.2. 参数调优：拯救你的因子</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#721-修正因子方向" class="md-toc-link">
            <p>7.2.1. 修正因子方向</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#722-过滤非线性分层" class="md-toc-link">
            <p>7.2.2. 过滤非线性分层</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#723-使用最佳分层方式" class="md-toc-link">
            <p>7.2.3. 使用最佳分层方式</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#724-grid-search" class="md-toc-link">
            <p>7.2.4. Grid Search</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#73-过拟合检测方法" class="md-toc-link"><p>7.3. 过拟合检测方法</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#731-样本外检测" class="md-toc-link">
            <p>7.3.1. 样本外检测</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#732-绘制参数高原" class="md-toc-link">
            <p>7.3.2. 绘制参数高原</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#74-关于多空组合的思考" class="md-toc-link">
            <p>7.4. 关于多空组合的思考</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#8-alpha101-因子介绍" class="md-toc-link"><ol start="8">
<li>Alpha101 因子介绍</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#81-alpha101-因子中的数据和算子" class="md-toc-link">
            <p>8.1. Alpha101 因子中的数据和算子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#82-alpha101-因子解读" class="md-toc-link">
            <p>8.2. Alpha101 因子解读</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#83-如何实现-alpha101-因子" class="md-toc-link">
            <p>8.3. 如何实现 Alpha101 因子？</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#9-talib-技术因子" class="md-toc-link"><ol start="9">
<li>Talib 技术因子</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#91-ta-lib-函数分组" class="md-toc-link">
            <p>9.1. Ta-lib 函数分组</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#92-冷启动期-unstable-periods" class="md-toc-link">
            <p>9.2. 冷启动期 (unstable periods)</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#93-震荡类指标" class="md-toc-link"><p>9.3. 震荡类指标</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#931-rsi" class="md-toc-link">
            <p>9.3.1. RSI</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#932-adx---平均方向运动指数" class="md-toc-link">
            <p>9.3.2. ADX - 平均方向运动指数</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#933-apo---绝对价格震荡指标" class="md-toc-link">
            <p>9.3.3. APO - 绝对价格震荡指标</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#934-ppo---百分比价格震荡指标" class="md-toc-link">
            <p>9.3.4. PPO - 百分比价格震荡指标</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#935-aroon-振荡器" class="md-toc-link">
            <p>9.3.5. Aroon 振荡器</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#936-money-flow-index" class="md-toc-link">
            <p>9.3.6. Money Flow Index</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#937-balance-of-power" class="md-toc-link">
            <p>9.3.7. Balance of Power</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#938-williams-r" class="md-toc-link">
            <p>9.3.8. William's R</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#939-stochastic-随机振荡指标" class="md-toc-link">
            <p>9.3.9. Stochastic 随机振荡指标</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#94-成交量指标" class="md-toc-link"><p>9.4. 成交量指标</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#941-chaikin-ad-line" class="md-toc-link">
            <p>9.4.1. Chaikin A/D Line</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#942-obv" class="md-toc-link">
            <p>9.4.2. OBV</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#95-波动性指标" class="md-toc-link"><p>9.5. 波动性指标</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#951-atr-与-natr---平均真实波幅" class="md-toc-link">
            <p>9.5.1. ATR 与 NATR - 平均真实波幅</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#96-8-种移动平均线" class="md-toc-link">
            <p>9.6. 8 种移动平均线</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#97-overlap-研究" class="md-toc-link"><p>9.7. Overlap 研究</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#971-布林带" class="md-toc-link">
            <p>9.7.1. 布林带</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#972-hilbert-趋势线和-sine-wave-indicator" class="md-toc-link">
            <p>9.7.2. Hilbert 趋势线和 Sine Wave Indicator</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#973-parabolic-sar" class="md-toc-link">
            <p>9.7.3. Parabolic Sar</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#98-momentum-指标" class="md-toc-link">
            <p>9.8. Momentum 指标</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#10-其它量价因子" class="md-toc-link"><ol start="10">
<li>其它量价因子</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#101-小概率事件" class="md-toc-link">
            <p>10.1. 小概率事件</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#102-最大回撤" class="md-toc-link">
            <p>10.2. 最大回撤</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#103-pct_rank" class="md-toc-link">
            <p>10.3. pct_rank</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#104-波动率" class="md-toc-link">
            <p>10.4. 波动率</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#105-z-score" class="md-toc-link">
            <p>10.5. z-score</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#106-夏普率" class="md-toc-link">
            <p>10.6. 夏普率</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#107-一阶导因子" class="md-toc-link">
            <p>10.7. 一阶导因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#108-二阶导因子" class="md-toc-link">
            <p>10.8. 二阶导因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#109-频域因子" class="md-toc-link">
            <p>10.9. 频域因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1010-tsfresh-因子库" class="md-toc-link">
            <p>10.10. TSFresh 因子库</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#1011-行为金融学因子" class="md-toc-link"><p>10.11. 行为金融学因子</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#10111-整数关口因子" class="md-toc-link">
            <p>10.11.1. 整数关口因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#10112-冲压失败因子" class="md-toc-link">
            <p>10.11.2. 冲压失败因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#10113-缺口因子" class="md-toc-link">
            <p>10.11.3. 缺口因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#10114-遗憾规避理论因子" class="md-toc-link">
            <p>10.11.4. 遗憾规避理论因子</p>

          </a></div>
        </div>
      </details>
    
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#11-基本面因子和另类因子" class="md-toc-link"><ol start="11">
<li>基本面因子和另类因子</li>
</ol>
</a>
          </summary>
        <div>
          <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#111-famma-五因子" class="md-toc-link"><p>11.1. Famma 五因子</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1111-市场因子" class="md-toc-link">
            <p>11.1.1. 市场因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1112-规模因子" class="md-toc-link">
            <p>11.1.2. 规模因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1113-价值因子" class="md-toc-link">
            <p>11.1.3. 价值因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1114-盈利因子" class="md-toc-link">
            <p>11.1.4. 盈利因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1115-投资因子" class="md-toc-link">
            <p>11.1.5. 投资因子</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#112-另类因子" class="md-toc-link"><p>11.2. 另类因子</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1121-社交媒体情绪因子" class="md-toc-link">
            <p>11.2.1. 社交媒体情绪因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1122-网络流量因子" class="md-toc-link">
            <p>11.2.2. 网络流量因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1123-卫星图像因子" class="md-toc-link">
            <p>11.2.3. 卫星图像因子</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1124-专利申请因子" class="md-toc-link">
            <p>11.2.4. 专利申请因子</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#113-爬虫的技术路线" class="md-toc-link">
            <p>11.3. 爬虫的技术路线</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#12-因子挖掘方法" class="md-toc-link"><ol start="12">
<li>因子挖掘方法</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#121-新因子从哪里来" class="md-toc-link">
            <p>12.1. 新因子从哪里来</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#122-网络资源" class="md-toc-link">
            <p>12.2. 网络资源</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#123-因子正交性检测" class="md-toc-link">
            <p>12.3. 因子正交性检测</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#124-谈谈因子动物园" class="md-toc-link">
            <p>12.4. 谈谈因子动物园</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#13-机器学习概述" class="md-toc-link"><ol start="13">
<li>机器学习概述</li>
</ol>
</a>
          </summary>
        <div>
          <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#131-机器学习分类" class="md-toc-link"><p>13.1. 机器学习分类</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1311-机器学习-深度学习-强化学习" class="md-toc-link">
            <p>13.1.1. 机器学习、深度学习、强化学习</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1312-监督无监督和强化学习" class="md-toc-link">
            <p>13.1.2. 监督无监督和强化学习</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1313-回归与分类" class="md-toc-link">
            <p>13.1.3. 回归与分类</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#132-机器学习模型简介" class="md-toc-link">
            <p>13.2. 机器学习模型简介</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#133-机器学习三要素" class="md-toc-link">
            <p>13.3. 机器学习三要素</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#134-机器学习基本流程" class="md-toc-link">
            <p>13.4. 机器学习基本流程</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#135-机器学习应用场景" class="md-toc-link">
            <p>13.5. 机器学习应用场景</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#14-机器学习核心概念" class="md-toc-link"><ol start="14">
<li>机器学习核心概念</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#141-偏差-方差" class="md-toc-link">
            <p>14.1. 偏差、方差</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#142-过拟合与正则化惩罚" class="md-toc-link">
            <p>14.2. 过拟合与正则化惩罚</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#143-损失函数-目标函数和度量函数和距离函数" class="md-toc-link"><p>14.3. 损失函数、目标函数和度量函数和距离函数</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1431-损失函数和目标函数" class="md-toc-link">
            <p>14.3.1. 损失函数和目标函数</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1432-度量函数" class="md-toc-link">
            <p>14.3.2. 度量函数</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1433-距离函数" class="md-toc-link">
            <p>14.3.3. 距离函数</p>

          </a></div>
        </div>
      </details>
    
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#15-sklearn-通用工具包" class="md-toc-link"><ol start="15">
<li>SKLearn 通用工具包</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#151-数据预处理preprocessing" class="md-toc-link">
            <p>15.1. 数据预处理：preprocessing</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#152-metrics" class="md-toc-link">
            <p>15.2. metrics</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#153-模型解释与可视化" class="md-toc-link">
            <p>15.3. 模型解释与可视化</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#154-内置数据集" class="md-toc-link">
            <p>15.4. 内置数据集</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#16-模型优化" class="md-toc-link"><ol start="16">
<li>模型优化</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#161-优化概述" class="md-toc-link">
            <p>16.1. 优化概述</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#162-k-fold-cross-validation" class="md-toc-link">
            <p>16.2. k-fold cross validation</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#163-参数搜索" class="md-toc-link"><p>16.3. 参数搜索</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1631-网格搜索" class="md-toc-link">
            <p>16.3.1. 网格搜索</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1632-随机搜索" class="md-toc-link">
            <p>16.3.2. 随机搜索</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1633-贝叶斯优化" class="md-toc-link">
            <p>16.3.3. 贝叶斯优化</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#164-rolling-forecasting" class="md-toc-link">
            <p>16.4. Rolling Forecasting</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#17-聚类寻找-pair-trading-标的" class="md-toc-link"><ol start="17">
<li>聚类：寻找 Pair Trading 标的</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#171-聚类算法概述" class="md-toc-link">
            <p>17.1. 聚类算法概述</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#172-hdbscan-算法原理" class="md-toc-link">
            <p>17.2. HDBSCAN 算法原理</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#173-寻找-pair-trading-标的" class="md-toc-link"><p>17.3. 寻找 Pair Trading 标的</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1731-hdbscan-示例" class="md-toc-link">
            <p>17.3.1. HDBSCAN 示例</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1732-结果评估" class="md-toc-link">
            <p>17.3.2. 结果评估</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1733-配对选择" class="md-toc-link">
            <p>17.3.3. 配对选择</p>

          </a></div>
        </div>
      </details>
    
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#18-从决策树到-lightgbm" class="md-toc-link"><ol start="18">
<li>从决策树到 LightGBM</li>
</ol>
</a>
          </summary>
        <div>
          <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#181-决策树" class="md-toc-link"><p>18.1. 决策树</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1811-决策树分类---httpsscikit-learnorgstablemodulestreehtmlclassification--" class="md-toc-link">
            <p>18.1.1. 决策树分类 <!-- https://scikit-learn.org/stable/modules/tree.html#classification --></p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1812-决策树回归" class="md-toc-link">
            <p>18.1.2. 决策树回归</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#182-lightgbm" class="md-toc-link"><p>18.2. LightGBM</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1821-熟悉训练数据" class="md-toc-link">
            <p>18.2.1. 熟悉训练数据</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1822-构建第一个分类器" class="md-toc-link">
            <p>18.2.2. 构建第一个分类器</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1823-可视化特征重要性" class="md-toc-link">
            <p>18.2.3. 可视化特征重要性</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1824-查看模型树" class="md-toc-link">
            <p>18.2.4. 查看模型树</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1825-交叉验证" class="md-toc-link">
            <p>18.2.5. 交叉验证</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#1826-调优" class="md-toc-link">
            <p>18.2.6. 调优</p>

          </a></div>
        </div>
      </details>
    
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#19-基于-lightgbm-回归模型的价格预测" class="md-toc-link"><ol start="19">
<li>基于 LightGBM 回归模型的价格预测</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#191-策略原理" class="md-toc-link">
            <p>19.1. 策略原理</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#192-策略实现" class="md-toc-link">
            <p>19.2. 策略实现</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#193-策略优化思路" class="md-toc-link">
            <p>19.3. 策略优化思路</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#20-基于-lightgbm-分类模型的交易策略" class="md-toc-link"><ol start="20">
<li>基于 LightGBM 分类模型的交易策略</li>
</ol>
</a>
          </summary>
        <div>
          <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#201-策略实现" class="md-toc-link"><p>20.1. 策略实现</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2011-顶底查找算法" class="md-toc-link">
            <p>20.1.1. 顶底查找算法</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#2012-标注工具" class="md-toc-link"><p>20.1.2. 标注工具</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#20121-基本布局" class="md-toc-link">
            <p>20.1.2.1. 基本布局</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#20122-初始化" class="md-toc-link">
            <p>20.1.2.2. 初始化</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#20123-部件更新" class="md-toc-link">
            <p>20.1.2.3. 部件更新</p>

          </a></div>
        </div>
      </details>
    <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#2013-构建模型" class="md-toc-link"><p>20.1.3. 构建模型</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#20131-模型基类" class="md-toc-link">
            <p>20.1.3.1. 模型基类</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#20132-v2" class="md-toc-link">
            <p>20.1.3.2. V2</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#20133-v3" class="md-toc-link">
            <p>20.1.3.3. V3</p>

          </a></div>
        </div>
      </details>
    
        </div>
      </details>
    <details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#202-算法优化" class="md-toc-link"><p>20.2. 算法优化</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2021-样本平衡" class="md-toc-link">
            <p>20.2.1. 样本平衡</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2022-多周期及微观数据" class="md-toc-link">
            <p>20.2.2. 多周期及微观数据</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2023-市场氛围" class="md-toc-link">
            <p>20.2.3. 市场氛围</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2024-id作为特征" class="md-toc-link">
            <p>20.2.4. id作为特征</p>

          </a></div>
        </div>
      </details>
    
        </div>
      </details>
    <details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#21-未来新世界" class="md-toc-link"><ol start="21">
<li>未来新世界</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#211-如何获得免费算力---标注数据量决定--" class="md-toc-link">
            <p>21.1. 如何获得免费算力 <!-- 标注数据量决定 --></p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#212-cnn-价格预测" class="md-toc-link"><p>21.2. CNN 价格预测</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2121-如何为训练提供数据" class="md-toc-link">
            <p>21.2.1. 如何为训练提供数据</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2122-如何构建特征数据" class="md-toc-link">
            <p>21.2.2. 如何构建特征数据</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2123-如何定义模型" class="md-toc-link">
            <p>21.2.3. 如何定义模型</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2124-训练" class="md-toc-link">
            <p>21.2.4. 训练</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2125-生产部署" class="md-toc-link">
            <p>21.2.5. 生产部署</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2126-cnn的原理与性能优化" class="md-toc-link">
            <p>21.2.6. CNN的原理与性能优化</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#213-transformer" class="md-toc-link">
            <p>21.3. Transformer</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#214-reinforcement-learning" class="md-toc-link">
            <p>21.4. Reinforcement Learning</p>

          </a></div><details style="padding:0;;padding-left:24px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#215-其它重要智能算法" class="md-toc-link"><p>21.5. 其它重要智能算法</p>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2151-kalman-filter" class="md-toc-link">
            <p>21.5.1. kalman filter</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#2152-genentic-algo" class="md-toc-link">
            <p>21.5.2. Genentic Algo</p>

          </a></div>
        </div>
      </details>
    
        </div>
      </details>
    
</div>
</div>
      <a id="sidebar-toc-btn">≡</a>
    
    
    
    
    
    
<script>

var sidebarTOCBtn = document.getElementById('sidebar-toc-btn')
sidebarTOCBtn.addEventListener('click', function(event) {
  event.stopPropagation()
  if (document.body.hasAttribute('html-show-sidebar-toc')) {
    document.body.removeAttribute('html-show-sidebar-toc')
  } else {
    document.body.setAttribute('html-show-sidebar-toc', true)
  }
})
</script>
      
  
    </body></html>