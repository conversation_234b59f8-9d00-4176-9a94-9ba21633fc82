# 14. 机器学习核心概念

在上一章中，我们简单地接触了一个决策树模型。我们看到仅仅通过定义一个决策树对象（DecisionTreeClassifier），然后传入训练数据和标签进行训练（fit），就神奇地生成了一个预测模型。

但在内部，这一切究竟是怎么发生的呢？

机器学习涉及到线性代数、梯度优化、反向传播、激活函数等等基础概念，它的内容已超出了本课的范围。但是，在这些底层概念之上，仍然有一些机器学习的核心概念，与我们构建应用程序密切相关。只有掌握这些知识，我们才会懂得如何选择模型参数、评估和调优模型。

这一章就将为你介绍这些知识。

在机器学习/深度学习中，我们用训练数据集去训练一个模型，通常的做法是定义一个误差函数（后面一般称为损失函数，或者目标函数），通过将这个误差的最小化过程，来提高模型的性能。

这就涉及到了误差及一些相关的概念。

<!-- 线性代数、梯度优化、反向传播、激活函数 -->
<!-- https://www.datacamp.com/tutorial/k-nearest-neighbor-classification-scikit-learn-->
## 1. 偏差、方差、残差和误差

在阅读机器学习相关文献时，我们常常会遇到这样几个相似的概念：偏差（Bias），方差 (Variance)，残差 (Residual) 和误差 (error)。

**偏差**是指模型的预测值与真实值之间的系统性差异。具体来说，偏差衡量的是模型的平均预测值与实际值之间的差距。高偏差通常意味着模型欠拟合（underfitting），即模型过于简单，无法捕捉数据中的复杂模式。

偏差可以用以下公式表示：

$$
\text{Bias} = E[\hat{f}(x)] - f(x)
$$

其中，$E[\hat{f}(x)]$ 是模型预测值的期望， $f(x)$ 是真实值。在有的文献中，偏差被认为是度量测量准确度的量。

!!! note
    这里的 Bias 与技术指标 Bias 在原理上有相似之处，但也有显著的差异。技术指标的 Bias 公式是 $close - MA$。close 是系统对资产价值的一次出价或者预测，MA 则是过去一段时间以来，预测价格的均值，该均值被认为是对资产价值的近似，实际上资产的价值是不可知的。而在机器学习中，真实值是假定已知的。<br><br>另外，在时间序列中运用 Bias 概念，模型预测值的期望如何求得也会是一个问题。人不可能两次踏入同一条河流。历史数据也不存在同一时间点上的多个副本，对未来的预测也不存在多个副本。

**方差**则是指模型预测值的变化程度。具体来说，方差衡量的是模型在不同训练集上的预测值之间的差异。高方差通常意味着模型过拟合（overfitting），即模型过于复杂，对训练数据的噪声和细节过于敏感。

方差可以用以下公式表示：

$$
\text{Variance} = E[(\hat{f}(x) - E[\hat{f}(x)])^2]
$$

其中，$\hat{f}(x)$是模型在特定训练集上的预测值，$E[\hat{f}(x)]$是模型预测值的期望。在有的文献中，方差被认为是度量测量精密度的量。

下面的代码演示了偏差和方差之间的关系：

<Example id=bias-variance-example/>

```python
import matplotlib.pyplot as plt
import numpy as np

# 设置随机种子以确保结果可重复
np.random.seed(42)

# 生成数据
x = np.linspace(0, 10, 100)
true_function = np.sin(x) * 5  # 真实函数

# 定义不同的模型预测值
high_bias_high_variance = np.sin(x) * 3 + np.random.normal(0, 2, size=x.shape)
high_bias_low_variance = np.sin(x) * 3 + np.random.normal(0, 0.2, size=x.shape)
low_bias_high_variance = np.sin(x) * 5 + np.random.normal(0, 2, size=x.shape)
low_bias_low_variance = np.sin(x) * 5 + np.random.normal(0, 0.2, size=x.shape)

# 创建一个包含四个子图的图表
fig, axes = plt.subplots(2, 2, figsize=(12, 10))

# 高偏差、高方差
axes[0, 0].plot(x, true_function, label='真实函数', color='blue')
axes[0, 0].scatter(x, high_bias_high_variance, label='模型预测值', color='green', alpha=0.5)
axes[0, 0].set_title('高偏差、高方差')
axes[0, 0].legend()

# 高偏差、低方差
axes[0, 1].plot(x, true_function, label='真实函数', color='blue')
axes[0, 1].plot(x, high_bias_low_variance, label='模型预测值', color='green', linestyle='--')
axes[0, 1].set_title('高偏差、低方差')
axes[0, 1].legend()

# 低偏差、高方差
axes[1, 0].plot(x, true_function, label='真实函数', color='blue')
axes[1, 0].scatter(x, low_bias_high_variance, label='模型预测值', color='green', alpha=0.5)
axes[1, 0].set_title('低偏差、高方差')
axes[1, 0].legend()

# 低偏差、低方差
axes[1, 1].plot(x, true_function, label='真实函数', color='blue')
axes[1, 1].plot(x, low_bias_low_variance, label='模型预测值', color='green', linestyle='--')
axes[1, 1].set_title('低偏差、低方差')
axes[1, 1].legend()

# 调整布局
plt.tight_layout()

# 显示图表
plt.show()
```

在示例中，演示了火枪手打移动靶的例子。移动靶的运动轨迹可以用$5\times\sin(x)$表示。然后，我们分别通过$3\times\sin(x)+\epsilon$和$5\times\sin(x) + \epsilon$来分别代表火枪手们射中点的轨迹，其中$\epsilon$是噪声。火枪手的射击过程，也是一个『学习』的过程。他们看到目标在移动，预测目标的下一个位置，调整自己的姿势和射击角度，根据射击的结果对射击诸元进行修正。

<!-- BEGIN IPYNB STRIPOUT -->

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/bias-vs-variance.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>偏差与方差关系图</span>
</div>
<!-- END IPYNB STRIPOUT -->

子图 1（左上）是毫无经验的火枪手例子。他的枪械既不精密（方差大，东一榔头西一棒槌），也不准确（总体上要比原函数在 y 的方向上偏下一点）。

子图 2（右上）显示的是一个经验尚可，但火枪准星未校准的例子。火枪手持枪很稳，也很好地跟踪了目标的移动，可是由于火枪准星未校准、或者重力未修正、或者受恒定的风速干扰的原因，最终射中的位置总是系统性地偏离了目标。

子图 3（左下）中的火枪手装备更好（准星很准），但可能他持枪不稳，或者有随机的气流干扰，所以，尽管他瞄得很准，但有时候也会脱靶很远，不过总体来说，比前面两个命中的机会要多一些。

子图 4（右下）中的火枪手占据了天时、地利、人和。也许他的枪跟图 2 的一样，也曾有过准星问题，但他很快修正了枪械本身的误差，多年的训练，使得他出手又快又准。

运用偏差与方差的概念，可以帮助我们发现机器学习中 underfit 和 overfit 的情况。[](#偏差、方差与过拟合的关系）很好地说明了这一点。这张图来自吴恩达的机器学习课程 [^ng]。

![偏差、方差与过拟合的关系](https://images.jieyu.ai/images/2024/11/bias-vs-varians-on-underfit-overfit.jpg?width=75%)

**残差**特指在**训练数据集**中，每个样本的真实值与模型预测值之间的差异。残差可以帮助我们评估模型在训练集上的拟合情况。它可以用以下公式表示：

$$
\text{Residual} = y_i - \hat{y}_i
$$

其中，$y_i$ 是第 $i$ 个样本的真实值，$\hat{y}_i$ 是第$i$个样本的预测值。

!!! note
    Residual 与 Bias 不同之处在于，残差是关于训练阶段的，而偏差是指预测阶段的。残差是单个样本的真实值与模型预测值之间的差异，而 Bias 则是关于模型的预测平均值与真实值之间的系统差异。

**误差**是指模型在新数据上的预测值与真实值之间的差异。误差可以分为两种类型：可减少误差（reducible error）和不可减少误差（irreducible error）。

**可减少误差**：这部分误差可以通过改进模型来减少，包括偏差和方差。
**不可减少误差**：这部分误差是由于数据本身的随机性和噪声引起的，无法通过改进模型来消除。

我们可以通过以下公式来表示误差：

$$
\text{Error} = (E[\hat{f}(x)] - f(x))^2 + E[(\hat{f}(x) - E[\hat{f}(x)])^2] + \sigma^2
$$

其中，$(E[\hat{f}(x)] - f(x))^2$是偏差的平方，$E[(\hat{f}(x) - E[\hat{f}(x)])^2]$是方差，$\sigma^2$ 是不可减少误差。

在机器学习中，模型和数据都会带来偏差。比如，如果真实世界是非线性的，那么当我们使用线性模型来进行训练时，就必然会引入偏差；在图像生成应用中，如果使用的数据集来自早期的互联网，由于当时访问互联网存在成本、技能障碍，因此采集到的图片就会以欧美文化为主，这样模型就无法生成反映其它地方人类生活的图片，这也是一种系统性偏差。

## 2. 过拟合与正则化惩罚

在上一节中，我们已经提到过过拟合和欠拟合。过拟合（Overfitting）是指机器学习模型在训练数据上表现得非常好，但在未见过的新数据上表现较差的现象。换句话说，模型在训练数据上过度学习了细节和噪声，导致其泛化能力下降。过拟合通常发生在模型过于复杂或训练数据量不足的情况下。

我们可以通过以下表现来发现过拟合的存在：

1. 高方差。模型在不同的训练集上的预测值变化很大。
2. 训练误差低，验证误差高：模型在训练集上的误差很小，但在验证集或测试集上的误差较大。

正则化惩罚是一种防止过拟合的技术，通过在损失函数中加入正则化项来限制模型的复杂度。常见的正则化方法包括 L1 正则化和 L2 正则化。

L1 正则化是在损失函数中加入模型参数的绝对值之和。L1 正则化倾向于使某些参数变为零，从而实现特征选择，使模型更稀疏。L2 正则化则是在损失函数中加入模型参数的平方之和。L2 正则化使参数值变小，但不会使参数变为零，从而使模型更加平滑。

要理解正则化惩罚的作用，最好的例子是将 sklearn 提供的 4 种回归模型进行对比。sklearn 针对回归模型，主要提供了以下四种版本：

1. LinearRegression：未施加任何惩罚机制的 Vanilla 版本。
2. Ridge：线性回归，但施加 L2 正则化惩罚。
3. Lasso：线性回归，但施加 L1 正则化惩罚。
4. ElasticNet：线性回归，同时施加 L1 和 L2 正则化惩罚。

下面，我们就生成测试数据集，分别训练这四种模型，然后看它们预测结果的误差。

<Example id=linear-regression-with-regularization/>

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error

# 生成模拟数据
seed = 42
np.random.seed(seed)
X = np.random.rand(100, 1) * 10
y = 2 * X.squeeze() + 1 + np.random.randn(100) * 2

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=seed)

# 初始化模型
models = {
    'Linear Regression': LinearRegression(),
    'Ridge': Ridge(alpha=1.0),
    'Lasso': Lasso(alpha=1.0),
    'ElasticNet': ElasticNet(alpha=1.0, l1_ratio=0.1)
}

# 训练模型并获取预测结果
predictions = {}
for name, model in models.items():
    model.fit(X_train, y_train)
    predictions[name] = model.predict(X_test)
    print(f"{name} mse: {mean_squared_error(y_test, predictions[name]):.3f}")


# 创建 4x3 子图
fig, axes = plt.subplots(4, 3, figsize=(15, 15))

# 排序测试数据以便绘图
sorted_indices = np.argsort(X_test.squeeze())
X_test_sorted = X_test[sorted_indices]

# 绘制每个模型的预测结果并与其它模型进行比较
model_names = list(models.keys())
k = 0
for i in range(4):
    for j in range(4):
        if i == j:
            continue
        else:
            # 绘制当前模型与另一个模型的比较
            model_name_1 = model_names[i]
            model_name_2 = model_names[j]
            y_pred_1_sorted = predictions[model_name_1][sorted_indices]
            y_pred_2_sorted = predictions[model_name_2][sorted_indices]
            row = k // 3
            col = k % 3
            axes[row, col].scatter(X_test, y_test, color='red', label='测试数据')
            axes[row, col].plot(X_test_sorted, y_pred_1_sorted, color='green', label=f'{model_name_1}')
            axes[row, col].plot(X_test_sorted, y_pred_2_sorted, color='blue', linestyle='--', label=f'{model_name_2}')
            axes[row, col].set_title(f'{model_name_1} vs {model_name_2}')
            axes[row, col].legend()
            k += 1

# 调整布局
plt.tight_layout()

# 显示图表
plt.show()
```

<!-- BEGIN IPYNB STRIPOUT -->
<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/comparison-linear-regressions.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
<!-- END IPYNB STRIPOUT -->

可以看出，通过施加正则项惩罚之后，其它三种方法都得到了比普通线性回归法更小的 MSE，也就是，这些模型的预测准确度更高。其中，施加 L2 正则化惩罚的效果会更好。

!!! tip
    在示例中没有体现的一个事实时，施加 L1 正则化惩罚并不总是会得到比无正则化惩罚更好的效果。你可以通过改变随机种子 seed 来观察这种差异。

## 3. 损失函数

在模型训练时，我们通常要指定**损失函数**；在训练完成之后，我们需要通过**度量函数**来对得到的模型实例进行评估。除此之外，我们还常常看到**成本函数**、**目标函数**和**距离函数**等等概念。这些概念都很相似，但各有各的应用场景，我们必须根据不同的场景来仔细选择。

**损失函数** (Loss Function) 是一个使用非常广泛的概念。在数学优化和决策理论中，损失函数是一种将事件或变量的值映射到实数（内在地，该实数意味着该事件的“成本”）的函数。优化过程旨在最小化损失函数。在机器学习的训练过程中，我们通过优化损失函数来训练模型参数。

损失函数计算的是训练中的单个样本的误差。**成本函数** [^ng_cost] 计算的则是整个训练集的损失函数的平均值或总和，用于评估模型在整个训练集上的总体表现，并作为优化过程中的目标。

我们通过以下数据示例来说明成本函数与损失函数之间的关系：

| 样本值      | 预测值            | MSE                                       |
| ----------- | ----------------- | ----------------------------------------- |
| $y_1 = 100$ | $\hat{y}_1 = 90$  | $L(y_1, \hat{y}_1) = (100 - 90)^2 = 100$  |
| $y_2 = 150$ | $\hat{y}_2 = 100$ | $L(y_2, \hat{y}_2) = (150 - 100)^2 = 250$ |
| $y_3 = 200$ | $\hat{y}_3 = 210$ | $L(y_3, \hat{y}_3) = (200 - 210)^2 = 100$ |
| $y_4 = 250$ | $\hat{y}_4 = 240$ | $L(y_4, \hat{y}_4) = (250 - 240)^2 = 100$ |
| $y_5 = 300$ | $\hat{y}_5 = 310$ | $L(y_5, \hat{y}_5) = (300 - 310)^2 = 100$ |

在该训练集上，成本函数取各项 MSE 的平均值，即为 580。

与损失函数、成本函数相比，**目标函数**则是一个更加通用的概念，它的应用场景远远超出了机器学习。仅从数学上讲，损失函数和成本函数的优化方向一定是最小化。但目标函数的优化方向则既可能是最大化，比如，在朴素贝叶斯中，目标是最大化后验概率；在遗传算法中，目标是最大化适应度；在强化学习中，目标是最大化奖励，等等（目标函数的优化方向也可以是最小化）。

与成本（损失）函数相比，目标函数一般会包含正则化项，而损失（成本）函数则只反映了事件的直接成本。

!!! tip
    在机器学习中，有一些模型是允许分别指定损失函数和正则化项的，有的则是要求直接指定目标函数，比如 xgboost。这是我们需要把所有的概念都讲一遍的原因。

在机器学习中，应该如何选择损失函数呢？首先是根据任务分类来选择。任务分类不同，损失函数的选择也会有不同。

### 3.1. 分类问题中的损失函数

在分类问题中，常用的损失函数有：

![](https://images.jieyu.ai/images/2025/02/categorical-loss.jpg)


在图中，最常用的损失函数可能是 Log Loss 和 Hinge Loss。Log Loss 又包含了 Binary Cross Entropy Losss 和 Softmax Loss 两种。Softmax Loss 就是 Softmax Cross Entropy Loss，这是一个常用的简写。它的公式是：

$$\text{Softmax Cross Entropy Loss} = -\frac{1}{N} \sum_{i=1}^{N} \sum_{j=1}^{C} y_{ij} \log(\hat{y}_{ij}) $$

这里 N 是样本数量，C 是类别数量。 $y_{ij}$是第 i 个样本属于第 j 个类别的真实标签（0 或者 1）,$\hat{y}_{ij}$是第 i 个样本属于第 j 个类别的预测概率。

二元分类的交叉熵损失本质上对上述公式的简化。

在 sklearn 中，二元分类和多分类的交叉熵损失函数都是通过 log_loss 来计算的：

```python
from sklearn.metrics import log_loss
import numpy as np

# 假设我们有以下真实标签和预测概率
y_true = [0, 1, 2, 2, 2]  # 真实标签
y_pred = [
    [0.2, 0.1, 0.7],  # 第一个样本的预测概率
    [0.3, 0.4, 0.3],  # 第二个样本的预测概率
    [0.1, 0.8, 0.1],  # 第三个样本的预测概率
    [0.1, 0.1, 0.8],  # 第四个样本的预测概率
    [0.1, 0.1, 0.8]   # 第五个样本的预测概率
]

# 计算 Softmax Cross Entropy Loss
loss = log_loss(y_true, y_pred)
print(f"Softmax Cross Entropy Loss: {loss:.4f}")
```

<!-- BEGIN IPYNB STRIPOUT -->
计算结果为 1.055。
<!-- END IPYNB STRIPOUT -->

这个示例演示了在有三个标签的情况下，对 5 个样本进行预测后，计算多分类交叉熵损失的情况。

<!-- softmax 函数将一个向量转换为另一个向量，使得输出向量的元素都在 [0, 1] 之间，并且所有元素的和为 1。常用于多分类问题中的概率分布。Cross Entropy Loss：衡量两个概率分布之间的差异，常用于分类问题。对于多分类问题，通常使用 Softmax 函数将模型的输出转换为概率分布，然后计算交叉熵损失。Softmax Loss 实际上就是 Softmax Cross Entropy Loss，用于多分类问题中的损失计算。在很多文献和代码实现中，这两个术语经常互换使用。-->

Hinge Loss 是 svm（Support Vector Machine）中的关键工具。SVM 有强大的二元分类能力和简洁的数学模型，svm 的核心在于寻找一个最优的超平面，以最大间隔将不同类别的样本分开。

它的定义是，对于预期输出$t=\pm1$和分类器得分 y，预测 y 的 hinge loss 为：

$$
\ell(y) = \max(0, 1-t \cdot y)
$$

这里$y$是分类器决策函数的原始输出，而不是预测的类别标签。这个函数有这样的特征，当预测正确（即$t$和$y$同号）且$|y|\geq1$时，损失函数最小（即为零）；如果预测错误，则$\ell(y)$随着$y$的增加线性增加，施加的惩罚也越来越大。如果预测的方向正确，但$|y|<1$，也会施加一定的惩罚。

下面的图示可以帮助我们更好地理解 Hinge Loss:

![Hinge Loss, by Qwertyus@wiki](https://images.jieyu.ai/images/2024/11/hinge-loss.jpg)

在 [](#Hinge Loss, by Qwertyus@wiki) 中，竖轴代表了 Hinge Loss，横轴代表了预测值$y$。此图表明损失函数在预测值$y<1$时施加了惩罚，而当预测值$y>1$时，损失函数就是最优化值。通过这种处理，使得 SVM 的解具有稀疏性，即大部分训练样本不会对最终模型产生影响，只有支持向量（即位于间隔边界上的样本）才会对模型参数产生影响，并且对噪声和异常值具有一定的鲁棒性，因为它只关注那些分类错误的样本或分类正确但置信度不高的样本。

!!! tip
    如果把此图进行水平镜像，它就成了神经网络中常用的 ReLu 激励函数。它们都模拟了神经元的工作过程。

### 3.2. 回归问题中的损失函数

回归问题中可以使用的损失函数有 RMSE（均方根误差），MSE（均方误差），MAE（平均绝对误差，及其变种 MAPE，平均绝对百分比误差），Huber Loss，Quantile Loss, Log Cosh Loss 等。

MSE 的公式是：

$$
MSE = \frac{\sum_{i=1}^{n}(y_i-\hat{y_i})^2}{n}
$$

假设真实目标值为 100，多次预测的$\hat{y}$分布在 [-1000,1000] 之间，则 MSE 在预测值为 100 时，达到最小值，如下图所示：

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/meaning-of-mse.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

如果我们把损失函数换成 MAE，真实目标值和预测值分布不变，则损失函数同样在 100 时，达到最小值，变化速度不一样：

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/meaning-of-mae.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

在这两者之中，我们应该如何选择呢？很重要的一点时，如果离群值代表对业务重要的异常情况并且应该被检测到，那么我们应该使用均方误差；如果离群值只是代表了错误的数据，那么我们应该选择平均绝对误差（MAE）作为损失函数（除非有其它理由，导致 MAE 本身不可用，比如在 XgBoost 中就是如此）。

根据数据分布的不同，我们需要使用更适合的损失函数。例如 [^fritz]，如果数据中的 90%观察值的真实目标值为 150，剩余的 10%的观察值在 0 到 30 之间。那么，使用平均绝对误差（MAE）作为损失函数的模型可能会对所有观察值预测为 150，忽视了 10%的异常情况，因为它会试图趋向于中位数值。在同样的情况下，使用均方误差（MSE）的模型会给出许多在 0 到 30 范围内的预测，因为它会向异常值倾斜。这两种结果在许多商业案例中都是不可取的。

这一情况引导着人们探索新的损失函数，于是 Huber Loss, Log-Cosh Loss 和 Quantile Loss（分位数损失函数）诞生了。Huber Loss 和 Log-Cosh Loss 都有二阶导，这是一个可贵的性质，使得它们可以作为 xgboost 等算法的损失函数。

在量化交易领域，很可能分位数损失函数在某些场合下是非常有用的。在讲因子分析时，我们已经充分介绍了分层法在解决非线性问题上的优势，这里也就不赘述了。

最后，我们把 5 种损失函数的比较图如下：

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/five-loss-functions.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

### 3.3. 如何选择损失函数

如何选择损失函数？首先是根据任务类型（分类还是回归），然后再根据模型的数学要求（比如是否要求二次可微）进行筛选，最后，根据数据本身的特性决定选择哪一个。

此外，选择损失函数也要看框架的实现。比如，在 sklearn 中每个模型都绑定了默认的损失函数，即使部分模型允许以参数来选择损失函数，这些损失函数的实现也是内置的。

比如，Hinge Loss 主要与 svm.LinearSVC 模型一起使用。LogisticRegression 默认使用对数损失。如果你要在回归问题中，使用 Huber Loss，那么应该生成一个 HuberRegressor 来使用。

一些更高级的模型，比如 GradientBoostingRegressor，允许我们自己选择更多种的损失函数。在 [](#example-3) 的第 17 行演示了使用 quantile loss 的情况。

<Example id=choose-loss/>

```python
from sklearn.datasets import make_regression
from sklearn.model_selection import train_test_split
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.metrics import mean_squared_error

# 生成模拟数据
X, y = make_regression(n_samples=100, n_features=1, noise=10, random_state=42)

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

sorted_indice = np.argsort(X_test.squeeze())

# 创建并训练 GradientBoostingRegressor 模型
# loss 参数可以为 'quantile', 'squared_error', 'absolute_error'和'huber'
gb = GradientBoostingRegressor(loss='quantile', alpha=0.25)
gb.fit(X_train, y_train)

# 预测
y_pred = gb.predict(X_test)

plt.scatter(X_test, y_test, color='blue', label='Actual')
plt.plot(X_test[sorted_indice], y_pred[sorted_indice], color='red', label='Predicted')

# 计算 MSE
mse = mean_squared_error(y_test, y_pred)
print(f"MSE: {mse:.2f}")
```

GradientBoostingRegressor 模型默认使用 MSE 作为损失函数。但我们也可以使用其他损失函数，比如 quantile loss， huber 等。

!!! note
    在 sklearn 中，sklearn 不将损失函数单独暴露出来，而是选择与模型绑定，这样做可以减少学习的复杂度，降低初学者出错的可能。但在像 pytorch 这样的框架中，这些损失函数是单独的实现。但是，有一些损失函数也有单独实现，它们出现在 metrics 模块中。

此外，模型能应用什么样的损失函数，不仅仅取决于模型的任务分类（回归还是分类），有时候也取决于模型是如何优化的。比如在 xgboost 中，它使用了二阶泰勒展开以及二阶导，因此，MAE 和 MAPE（mean absolute percentage error）这两种损失函数就都不能使用。关于这一点，可以进一步阅读 [不能求二阶导的 metrics，不是好的 objective](http://www.jieyu.ai/blog/2024/07/16/A-Portofolio-Strategy-Based-On-Xgboost-2/) 一文。

## 4. 度量函数

损失函数、成本函数和目标函数都主要在训练阶段使用，帮助模型优化。其中有一些损失函数也可以用来评估模型性能，比如均方根误差（RMSE）。但是，损失函数是供优化用的，它必须满足可以最优化这一数学限制条件。与此相比，度量函数的限制条件就少很多，因此，度量函数的种类也就更多。

在 sklearn 中，度量函数都归类在 metrics 模块中。在该模块中，还存在部分损失函数、距离函数以及少量可视化函数。

![Metrics Module in Sklearn](https://images.jieyu.ai/images/2024/11/sklearn-metrics.png)

我们重点关注 [](#Metrics Module in Sklearn) 中的 score 部分。它是真正意义上的模型评估函数，只不过在这里 sklearn 使用的是score 这个名字。

同样的，根据任务归类不同，我们可以把评估函数归类为分类和回归。在回归类的评估函数中，主要有 mse,rmse,mae 和 r2 等，在介绍损失函数时，我们已经讨论过，这里就不再赘述。我们对分类中的评估函数适当展开一下。

理解分类函数的起点是要理解 TP,TN,FP,FN 等概念。

|          | 预测正类            | 预测负类            |
| -------- | ------------------- | ------------------- |
| 实际正类 | True Positive (TP)  | False Negative (FP) |
| 实际负类 | False Positive (FN) | True Negative (TN)  |

根据这些概念的不同组合，就有了以下评估指标。

### 4.1. 准确率

准确率（Accuracy）是模型预测正确的样本数与总样本数的比值。它的公式如下：

$$\text{Accuracy} = \frac{TP + TN}{TP + TN + FP + FN}$$

记忆时，可以记为主对角线之和除以整体样本数。

<!--为何准确率不适合作为损失函数？首先，它的方向是最大化；其次，即使我们用与之互补的错误率作为loss，也会存在这样的问题，对于单个观察值，错误率的损失函数始终为 1（如果预测类别不匹配标签）或 0（如果预测类别匹配标签）。因此，该函数的导数除了在可忽略的点集上导数为无穷大之外，总是为 0。这排除了任何基于梯度的优化器训练模型的可能性，因为模型参数几乎总是有更新步长为 0，除了步长为无穷大的可数次数。

如果机器学习不基于梯度信息，那么就抛弃了梯度下降、牛顿-拉夫森法等类似的算法，而这些算法往往非常高效。但是随机森林的训练不基于梯度信息，所以，有可能使用准确率。
-->

### 4.2. 精确率

精确率（Precision）是模型预测为正类的样本中，实际为正类的样本数与预测为正类的样本数的比值。它的公式如下：

$$\text{Precision} = \frac{TP}{TP + FP}$$

它与误报率是互补（即加起来为1）的一对。

### 4.3. 召回率

召回率（Recall）是实际为正类的样本中，模型预测为正类的样本数与实际为正类的样本数的比值。它的公式如下：

$$\text{Recall} = \frac{TP}{TP + FN}$$

如果把 Recall 翻译成回忆起，可能更容易理解和记忆。即把一堆样本给分类器看一遍，事后分类器能从所有正类的样本中，回忆起其中的多少个？也可以把它理解成为灵敏度。当灵敏度很高时，模型能回忆起（响应）所有的正类，但可能导致错误判断，从而降低精确率。

在数据标签分布不平衡的情况下，上述指标都有自己的固有问题。因此，一种名为 F1 分数的指标被提了出来。

### 4.4. F1 分数

F1 分数是精确率和召回率的调和平均值，用于综合考虑精确率和召回率。公式如下：

$$
\text{F1 Score} = 2 \times \frac{\text{Precision} \times \text{Recall}}{\text{Precision} + \text{Recall}}
$$

F1 分数在 0 到 1 之间，值越接近 1 表示模型性能越好。F1 分数特别适用于类别不平衡的情况，因为它同时考虑了精确率和召回率。下面，我们举例来说明这些概念如何计算：

|          | 预测正类 | 预测负类 |
| -------- | -------- | -------- |
| 实际正类 | 60       | 20       |
| 实际负类 | 10       | 10       |

我们会得出：

1. $\text{Accuracy} = \frac{60 + 10}{60 + 10 + 20 + 10} = \frac{70}{100} = 0.7$

2. $\text{Precision} = \frac{60}{60 + 20} = \frac{60}{80} = 0.75$

3. $\text{Recall} = \frac{60}{60 + 10} = \frac{60}{70} \approx 0.857$

4. $\text{F1 Score} = 2 \times \frac{0.75 \times 0.857}{0.75 + 0.857} \approx 2 \times \frac{0.64275}{1.607} \approx 0.798$

### 4.5. AUC 和 ROC

在二分类中，模型除了可以直接输出标签（通过 predict 方法）之外，还可以输出一个介于 0 和 1 之间的概率值（通过 predict_proba 方法），以表示某个样本为正类的概率。这个概率值需要通过一个阈值来转换成具体的类别预测。例如，如果阈值设为 0.5，那么当模型输出的概率大于或等于 0.5 时，样本被预测为正类；否则，被预测为负类。

具体这个阈值应该设置为多少呢？最好还是通过数据来说话，于是就有了 AUC（Compute Area Under the Curve）和 ROC（Receiver Operating Characteristic ）曲线。

ROC 曲线通常以 Y 轴表示真正的正例率（TPR），以 X 轴表示假正类率（FPR）。在曲线下的面积就是 AUC。

下面的示例演示了如何根据 ROC 来确定分类时的阈值：

<Example id=roc-curve/>

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import roc_curve, auc

# 生成模拟数据
X, y = make_classification(n_samples=1000, n_features=20, n_classes=2, random_state=42)

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 训练模型
model = LogisticRegression()
model.fit(X_train, y_train)

# 预测概率
y_pred_proba = model.predict_proba(X_test)[:, 1]

# 计算 ROC 曲线
fpr, tpr, thresholds = roc_curve(y_test, y_pred_proba)

# 计算 AUC
roc_auc = auc(fpr, tpr)

# 找到最佳阈值
distances = np.sqrt((1 - tpr)**2 + fpr**2)
best_threshold_index = np.argmin(distances)
best_threshold = thresholds[best_threshold_index]

# 绘制 ROC 曲线
plt.figure()
plt.plot(fpr, tpr, color='darkorange', lw=2, label='ROC curve (area = %0.2f)' % roc_auc)
plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
plt.scatter(fpr[best_threshold_index], tpr[best_threshold_index], color='green', marker='x', label='Best Threshold')
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.title('Receiver Operating Characteristic')
plt.legend(loc="lower right")
plt.show()

print(f"最佳阈值：{best_threshold:.2f}, {fpr[best_threshold_index]:.2f}, {tpr[best_threshold_index]:.2f}")
```

<!-- BEGIN IPYNB STRIPOUT -->
显示结果如[](#ROC)所示：

![ROC](https://images.jieyu.ai/images/2024/11/roc.jpg)
<!-- END IPYNB STRIPOUT -->

## 5. 距离函数

从损失函数到目标函数，它们都是适用于监督学习的，有着明确的优化方向，这几类函数的本质都是计算预测值到真实值之间的距离。在聚类算法中，数据没有标签（即真实值），所以，这几类函数派不上用场了。于是，距离函数就成为聚类的核心算法。

距离函数是一个用于度量两个点之间的距离的函数。在机器学习中，距离函数用于计算两个样本之间的距离，这可以帮助我们进行聚类或者特征提取。

常用的距离函数有：

* 欧几里得距离（Euclidean Distance），$d(x, y) = \sqrt{\sum_{i=1}^{n} (x_i - y_i)^2}$
* 曼哈顿距离（Manhattan Distance），$d(x, y) = \sum_{i=1}^{n} |x_i - y_i|$，它也被称为也称为城市街区距离。
* 余弦相似度（Cosine Similarity）, $\text{similarity} = \cos(\theta) = \frac{\mathbf{A} \cdot \mathbf{B}}{|\mathbf{A}| |\mathbf{B}|}$，它适用于高维度稀疏数据，比如文本数据。
* 杰卡德相似度（Jaccard Similarity，用来计算两个集合的相似度。$\text{similarity} = \frac{|A \cap B|}{|A \cup B|}$。
* 马氏距离（Mahalanobis Distance），$d(x, y) = \sqrt{(x - y)^T S^{-1} (x - y)} $，其中 S 是数据的协方差矩阵。

我们将会在后面的聚类应用中使用距离函数。

!!! note
    除了这些距离函数之外，我们也可以根据应用的具体情况，设计自己的距离函数。但是，距离函数必须满足这样一些基本性质，即非负性、对称性和三角不等式。如果你设计出来的函数不满足这些特性，就把它交给机器学习模型使用，就会导致错误的结果。

## 6. 拓展阅读

关于偏差和方差，也可以阅读 Medium 上的 [理解机器学习算法中的偏差与公平性](https://medium.com/@zhonghong9998/understanding-bias-and-fairness-in-machine-learning-algorithms-f3279e667b10)，其中有一些很好的关于数据中引入偏差的真实例子。 

关于分类问题的损失函数介绍，可以阅读 medium 上的 [理解分类中的损失函数](https://medium.com/@nghihuynh_37300/understanding-loss-functions-for-classification-81c19ee72c2a)。不过，正如我们指出的那样，sklearn 中没有缺少一些分类问题的损失函数定义，所以，文章中引用的代码来自 pytorch 和 tensorflow。你也可以跳过这些代码，因为它们是应用于神经网络的。

关于回归问题的损失函数介绍，可以阅读 fritz.ai 上的 [所有机器学习者都应该了解的 5 种回归损失函数](https://fritz.ai/best-regression-loss-functions/)，以及对应的 [notebook](https://nbviewer.org/github/groverpr/Machine-Learning/blob/master/notebooks/05_Loss_Functions.ipynb)。为防止链接丢失，我们在 supplements 下保存了该 notebook，文件名为 five-loss-functions.ipynb。

## 7. Footnotes

[^ng]: [吴恩达的深度学习课程](https://www.coursera.org/specializations/deep-learning)。该课程是免费课程。
[^ng_cost]: 这里的成本函数的定义来自吴恩达。

[^fritz]: 此段示例和图片来自 [Fritz.ai](https://fritz.ai/best-regression-loss-functions/)
