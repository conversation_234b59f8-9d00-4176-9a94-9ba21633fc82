# 其它量价因子

以下代码为本章初始化代码。

```python
code = "000001.XSHE"
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

PAYH = load_bars(start, end, (code, )).xs(code, level=1)
barss = load_bars(start, end, 2000)
```

后面在演示单个技术指标时，我们将直接使用 PAYH 变量。

## 1. 概率回归因子
<!-- 单个极值事件，比如沪指单日最大跌幅、最大连续跌幅，背后是小概率事件发生后的回归 -->
上一章我们讨论过 Bollinger Band 指标，以及它对应的 zscore 因子。本质上，它是个概率因子，是基于正态分布中，超过 2 个标准差的事件只有 5%的概率，因此一定会回归这一假设。

从这个思路出发，我们可以挖掘更多的小概率事件，生成新的因子。

### 1.1. pct_rank
<!--笔记：高 Alpha 17.1%-->
做多的机会来自于亏损，做空的机会来自于盈利。大道至简，万宗归一，如果我们直接使用单日收益作为因子，会怎样？

这个思想来自于一个很简单的想法。如果某资产单日上涨幅度过大，就有了做空的机会；反之也一样。实际上有一些技术指标跟我们这里的想法比较接近，比如 BOP，UO 等。只不过技术指标都是基于时序的，因此单日振幅无法作为指标。

在构建这个因子的时候，我们首先要结合 A 股的实际情况，做一些预筛选。比如，如果当日已涨停，我们不应该卖出（做空）；当日已跌停，我们不应该买入（做多）。此外，像新股上市，ST 摘帽首日，都会有巨大的振幅，这些情况也要作为极值排除掉。

考虑到数据清洗的难度，我们就简单地通过涨停区间来去掉这些极值。

```python
def calc_single_day_loss(df):
    returns = -1 * df.close.pct_change(1)
    returns[(returns < -0.1) | (returns > 0.1)] = np.nan
    return returns

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)
_ = alphatest(2000, start, end, calc_factor = calc_single_day_loss)
```

在上面的代码中，我们去掉极值的方法是将收益的绝对值大于 10%的设置为 np.nan，从而导致这些记录会在因子分析时，被 Alphalens 丢弃。

另外，考虑到我们是要在下跌时做多，上涨后做空，所以，我们要将收益取负数，这样因子与预期收益才会在方向上一致。

首次运行我们就得到了 4%的年化 Alpha，应该说不算差。我们还注意到，第 10 层的因子的收益率不太好。我们对此的解释是，这一层的资产，就是当天情绪最差的资产。考虑到这个因素，我们把这一层排除在外似乎也合情合理。

于是，我们进行第一轮优化，将因子的 top 分层限制为第 9 层。

```python
_ = alphatest(2000, start, end, calc_factor = calc_single_day_loss, top=9)
```

通过优化后，发现年化 Alpha 达到了 12.9%。不过，不太理想的是，看起来收益主要是做空产生（做空第一层，实际上是做空当天上涨幅度最大的一层）。如果只能单边做多，这个因子的收益又会如何？

```python
_ = alphatest(2000, start, end, calc_factor = calc_single_day_loss, top=9, long_short=False)
```

年化 Alpha 仍然达到了 9.5%。非常理想的因子。下面我们再深入分析下因子分层的情况，以免出现意外：

```python
_ = alphatest(2000, start, end, calc_factor = calc_single_day_loss, top=9, plot_mode="quantiles")
```

从分层图来看，第 1 层有个别因子的值超出了范围，不过这对纯多策略没有影响。

最后一个问题，以纯多策略而论，究竟是下跌的绝对幅度影响着做多的收益，还是资产的排名影响着做多的收益呢？

这个问题可以通过将分层模式设置为按 bins 分层来回答。

```python
bins = np.linspace(-0.1, 0.1, 10)
_ = alphatest(2000, start, end, calc_factor = calc_single_day_loss, top=9, bins=bins)
```

现在，年化 Alpha 大幅度下降。这说明，收益并不是绑定到下跌的绝对幅度的，而是受资产排名的影响。

### 1.2. 连续涨跌次数

我们在 T0 日预测 T1 日的涨跌时，可以记作两种状态，1（涨）, 0（不涨）。如果不考虑资产价格涨跌的根本推动力，那么可以认为取 1 或者取 0 的概率各为 50%。这样 T0 到 T+n 日的状态，就构成了一个由 0,1 组成的随机变量。该随机变量的累计和将服从正态分布。

!!! tip
    注意这里的假设条件只在较短的时间段内成立。在一个极短的时间段内，价格的涨跌不受基本面左右，可以近似为随机事件。

比如，连续两天下跌（上涨）的概率为 25%；连续 4 天下跌（上涨）的概率为 6.25%... 以次类推。这

这个例子说明，如果我们把连续涨跌状态随机变量为作因子，应该能预测次日涨跌。

我们先看如何求出连续涨跌状态。
```python
def calc_continuous_status(df):
    df["ret"] = df.close.pct_change()

    down = df["ret"] < 0
    g = (down.diff() != 0).cumsum()
    g_cumdown = down.groupby(g).cumsum()

    df["status"] = g_cumdown

    up = df["ret"] > 0
    g = (up.diff() != 0).cumsum()
    g_cumup = up.groupby(g).cumsum()
    df.loc[df["status"] == 0, 'status'] = g_cumup * -1
    
    return df

calc_continuous_status(PAYH)[["status", "ret"]]
```

请自行验证输出结果。

现在，我们就把这段代码改造成因子，并进行因子检验：

```python
def calc_continuous_status(df):
    df["ret"] = df.close.pct_change()

    down = df["ret"] < 0
    g = (down.diff() != 0).cumsum()
    g_cumdown = down.groupby(g).cumsum()

    df["status"] = g_cumdown

    up = df["ret"] > 0
    g = (up.diff() != 0).cumsum()
    g_cumup = up.groupby(g).cumsum()
    df.loc[df["status"] == 0, 'status'] = g_cumup * -1
    
    return df["status"]

quantiles = [0, 0.005, 0.05, 0.1,0.4, 0.8,0.85, 0.9, 0.95, 1]
_ = alphatest(2000, start, end, calc_factor = calc_continuous_status, quantiles=quantiles, max_loss=0.99)
```

这也是我们所有示例中，第一次出现指定按分位数分层的情况。如果我们按bins来分层，并且自己指定每个桶的边缘值的话，也是可以的，但我们可能要先做一些统计。

我们这里特殊的分层方式也导致大量的记录被drop掉，在这个地方我们是允许的。所以大家看max_loss，我们已经设置成为99%了。

年化Alpha到了11.3%。不过，这个收益中显然掺入了大量的连板股，所以实盘中不一定能实现。如果我们把bottom设置为2，这样就自动地排除了每天的连板股，不过也排除了一些强势股。这种情况下，年化alpha将只有1.5%。大家可以自己尝试下，仅排除连板股，看看年化alpha有多高。

### 1.3. 连续涨跌幅度

我们考虑一个与 RSI 相近的因子，就是在不回调的情况下，连续上涨（下跌）的幅度。我们猜想，在没有回调的情况下，意味着中间没有筹码换手，因此浮盈的构成比较单一。这种单一结构，到了一定的规模，就形成了抛压。

我们的研究方法也是先构建因子，检验一下方法的正确性。

```python
def calc_continuous_pct(df):
    df["ret"] = df.close.pct_change()
    df["cum_ret"] = 0
    df["cum_direction"] = 0

    df["flag"] = np.where(df.ret > 0, 1, np.where(df.ret < 0, -1, 0))
    current_flag = None
    cum_ret = 1.0

    for i in range(len(df)):
        if df.at[df.index[i], 'flag'] != current_flag and current_flag is not None:
            cum_ret = 1.0
        current_flag = df.at[df.index[i], 'flag']

        if current_flag != 0:
            cum_ret *= (1 + df.at[df.index[i], 'ret'])
            df.at[df.index[i], 'cum_ret'] = cum_ret - 1
            df.at[df.index[i], 'cum_flag'] = current_flag

    return df
```

请自行比较 ret 和 cum_ret 列，验证结果是正确的。

然后我们把它转换为因子，并进行检验：

```python
def calc_continuous_pct(df):
    df["ret"] = df.close.pct_change()
    df["cum_ret"] = 0
    df["cum_direction"] = 0

    df["flag"] = np.where(df.ret > 0, 1, np.where(df.ret < 0, -1, 0))
    current_flag = None
    cum_ret = 1.0

    for i in range(len(df)):
        if df.at[df.index[i], 'flag'] != current_flag and current_flag is not None:
            cum_ret = 1.0
        current_flag = df.at[df.index[i], 'flag']

        if current_flag != 0:
            cum_ret *= (1 + df.at[df.index[i], 'ret'])
            df.at[df.index[i], 'cum_ret'] = cum_ret - 1
            df.at[df.index[i], 'cum_flag'] = current_flag

    return df.cum_ret

_ = alphatest(500, start, end, calc_factor = calc_continuous_pct)
```


因子检验的结果还不错，并且从分层收益均值图看，还有优化空间。

## 2. 波动率（月线）

在前面的课程中，我们已经接触了低波动因子。在这里，为了课程内容的完备性，我们再完整地实现一次。

```python
start = datetime.date(2008,1,1)
end = datetime.date(2022, 12,31)
day_barss = load_bars(start, end, 2000)
barss = (
    day_barss.groupby("asset")
    .apply(
        lambda x: x.droplevel("asset")
        .resample("1M")
        .agg(
            {
                "close": "last",
                "open": "first",
                "high": "max",
                "low": "min",
                "volume": "sum",
                "amount": "sum",
            }
        )
    )
    .swaplevel(0, 1)
)
    
def low_volatility(close: pd.Series, win:int, *args):
    return -1 * close.pct_change().rolling(window=win).std()

factors = []
for group in barss.groupby(level='asset'):
    close = group[1].close

    low_vol = low_volatility(close, 10)
    factors.append(low_vol)
    
factor = pd.concat(factors).rename_axis(index={'frame':'date'})

# 3. 提取价格数据
prices = barss['open'].unstack(level=1).shift(-1)

# 4. 预处理
factor_data = get_clean_factor_and_forward_returns(factor, prices)
factor_data.rename(columns = {
    '1D': '21D',
    '5D': '105D',
    '10D': '210D'
},inplace=True)

# 5. 生成报告
create_returns_tear_sheet(factor_data)
```

!!! tip
    在<ref>[](06.md)</ref>中，我们对低波动因子进行调整时，使用的是方法是：
    $$
    1/(1e-7 + \sigma)
    $$
    这会导致因子值被不合理地缩放，从而影响 Alpha 值（Alpha 的计算依赖线性回归，会受异常值影响）。乘以-1 是安全的做法。

这个因子的年化 Alpha 在 7.4%左右，beta 为-0.15，不算太差。由于 Alphalens 框架的局限，它没能绘制出累积收益图出来。

从分层收益均值图来看，做多和做空对收益的贡献都比较均衡。该因子的 IC 在 0.04，相当可观了。因子换手率是 79%，考虑到我们是月度持有，所以这个换手率可以接受。大家可以在深入研究一下，它应该包括了象长江电力这样的长期牛股。

## 3. 波动率（日线）

我们在日线尺度上再做一下测试。

```python
def low_volatility(close: pd.Series, win:int, *args):
    return 1 / (1e-7 + close.pct_change().rolling(window=win).std())

start = datetime.date(2018,1,1)
end = datetime.date(2023,12,31)
_ = alphatest(2000, start, end, calc_factor = lambda x: low_volatility(x.close, 10), bottom=2)
```

在窗口为 10 的情况下，我们得到的 Alpha 大于53.5%，6 年累计收益是 10 倍。这与low_volatility 的计算中出现的离群值密切相关。当窗口期较短时，可能出现个别个股的波动率倒数非常大，从而严重影响回归的结果。因此，在使用这个指标之前，应该先进行因子的预处理，以排除掉离群值，或者用因子排序来代替。

## 4. z-score

这个因子在上一章，介绍 bollinger band 时已经介绍过了。

## 5. 夏普率

我们在前面看到，收益率可以构建出各种因子，波动率也可以构造成因子，那么，能否把这两者结合起来构成一个因子呢？

答案是肯定的。这个因子就是夏普率。

$$
sharpe = \frac{r - rf}{\sigma}
$$

```python
def calc_sharpe(df, n):
    ret = df.close.pct_change()
    df[['ret_mean', 'std']] = ret.rolling(n).agg(['mean', 'std'])
    return df.ret_mean/df["std"]


_ = alphatest(500, start, end, calc_factor = lambda x: calc_sharpe(x, 21))
```

但是，如果像示例中这样计算因子的话，当窗口比较短的时候，很容易导致 alpha 分析值非常不稳定。因为当窗口很小的时候，df["std"] 有可能很接近于零，从而导致因子值的值非常大。

要解决这个问题，我们需要改用夏普的横截面排序，然后用排序值作为因子来进行分析。这样做并非毫无道理。假设我们管理着一个 FOF，并且有一定的资金量，那么，我们可能希望买一批夏普率排在前面的资产，而不管它们夏普率的绝对值是多少。这样我们的 FOF 在市场上至少有比较优势。

这一次我们不能使用 Alphatest 了。

```python
def calc_sharpe(close, n):
    # 在滑动窗口上计算夏普，在横截面上排序
    ret = close.pct_change()
    rolling_mean = ret.rolling(n).mean()
    rolling_std = ret.rolling(n).std()

    rolling_sharpe = rolling_mean / rolling_std
    factor = rolling_sharpe.rank(axis=1, method='min', pct=True, ascending=False)
    return (factor.reset_index()
            .melt(id_vars='date', var_name="asset", value_name="factor")
            .set_index(['date', 'asset']))
    
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12,31)
barss = load_bars(start, end, 2000)
factor = calc_sharpe(barss["close"].unstack(), 10)
prices = barss["close"].unstack()

merged = get_clean_factor_and_forward_returns(factor, prices, quantiles=None, bins=10)
merged[merged.factor_quantile > 6] = np.nan
create_returns_tear_sheet(merged)
```

现在，我们得到的年化Alpha是6.4%。

## 6. 斜率因子

在<ref>[](04.md)</ref>中，我们已经介绍过斜率因子。为完备起见，我们也将它罗列在此。

```python
from numpy.lib.stride_tricks import as_strided
def rolling_slope(df, win:int):
    close = df.close.to_numpy()
    if len(close) < win:
        return pd.DataFrame(np.full((len(close), ), np.nan), index=df.index)

    stride = close.strides
    
    slopes, _ = -1 * np.polyfit(np.arange(win), 
                           as_strided(close, (len(close)-win+1, win), 
                           stride+stride).T,
                           deg=1)
    left_padd_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_padd_len, 0), mode='constant', constant_values=np.nan)
    return pd.DataFrame(slopes, index=df.index)

_ = alphatest(2000, start, end, calc_factor = lambda x: rolling_slope(x, 30), top=7, long_short=False)
```

在窗口取 30 天，top = 7 的条件下，alpha 年化达到了 26.6%。在纯多情况下，年化收益甚至达到了 27%。

注意到我们在计算斜率因子时，取了原始斜率的相反数。因此，这个因子反映的实际上是一种抄底策略。也就是，在最近 30 天里，下跌越厉害（但去掉下跌最厉害的 30%）的资产，次日上涨收益也较大。这在 A 股似乎比较难得。

## 7. 一阶导因子
斜率的计算有一定的不稳定性。特别是有一些资产在随机波动时，它的拟合直线的误差会比较大。这样会导致我们的因子及其收益也不稳定。如果我们把斜率这个概念泛化一下，它实际上可以理解为一种一阶导数的平均值。

为了讲解其中的原理，我们先合成一段价格序列。

```python
import numpy as np
import matplotlib.pyplot as plt

num_points = 1000 
time = np.linspace(0, 10, num_points)
amplitude_factor = 1 + time  
frequency = 0.3 
phase = 0 
angle_degrees = 15
angle_radians = np.radians(angle_degrees)

y = amplitude_factor * np.sin(2 * np.pi * frequency * time + phase)

linear_trend = np.arange(1000) * 0.05
y += linear_trend

d1 = np.diff(y)
d1 = np.insert(d1, 0, [np.nan])

fig, ax1 = plt.subplots(figsize=(10,6))
line1, = ax1.plot(time, y, label='close')
ax1.set_xlabel('Time')
ax1.set_ylabel('Close')

ax2 = ax1.twinx()
line2, = ax2.plot(time, d1, 'g--', label="d1")
ax2.set_ylabel("d1")

lines = [line1, line2]
labels = [line.get_label() for line in lines]
ax1.legend(lines, labels, loc='upper left')

plt.grid(False)
plt.show()
```

这将显示一段震荡向上的价格序列（蓝色），并且叠加它的一阶导数（绿色）。通过一阶导，我们滤掉了中长期趋势，只留下周期震荡的部分。并且，它的极大值和极小值就与价格序列的高低点一一对应起来（但是有相位差）。既然存在着相关性，显然我们就可以将它作为一种因子。

<!--根据此图，应该找 d1 的最大峰，即时间序列上的 rank-->

```python
def calc_first_derivative(df, win:int):
    df["log"] = np.log(df.close)
    df["diff"] = df["log"].diff()
    return df["diff"].rolling(win).mean() * -1
    
start = datetime.date(2018,1,1)
end = datetime.date(2023,12,31)

_ = alphatest(2000, 
              start, 
              end, 
              calc_factor = lambda x: calc_first_derivative(x, 10))
```

这样我们得到的因子年化是 13.9%，纯多时的收益是 6.1%。

## 8. 二阶导因子

在前面的合成数据图像中，如果对 d1 继续求导，则会得到二阶导数。当二阶导 0 为零是，说明一阶导函数正好处在顶峰或谷底。因此，二阶导也有可能成为一种因子。

向上（或者即将向上）的曲线，其二阶导为正。如果是加速向下（或者即将向下）的，则二阶导为负。于是我们尝试构建一个二阶导因子，希望能找出这些趋势。

```python
def calc_second_derivative(df, win:int):
    df["log"] = np.log(df.close)
    df["d1"] = df["log"].diff()
    df["d2"] = df["d1"].diff()
    return df["d2"].rolling(win).mean() * -1

_ = alphatest(2000, 
              start, 
              end, 
              calc_factor = calc_second_derivative, args=(10,)) 
```

因子年化为 8.6%。

二阶导与价格趋势的关系更为复杂，并且在不同资产间进行比较，也需要找到其合理性。但是，从我们的合成图来看，一阶、二阶导与价格走势确实存在明确的相关性，只是这种相关性主要表现在时间维度上，所以，我们需要更复杂的技巧来将其转化为因子。

## 9. 波谱分析因子

我们在文章 [快速傅利叶变换与股价预测研究](http://www.jieyu.ai/blog/2024/08/26/fft-and-stock-prediction/) 中，介绍了通过 FFT 来提取波动的直流分量，通过直流分量的回归来预测价格方向的方法。

FFT 变换首先可以用来滤波。下面的示例展示了一个简单的滤波操作。

```python
df = PAYH.copy()
close = df.close.to_numpy()
fft_result = np.fft.fft(close)
freqs = np.fft.fftfreq(len(close))

# 逆傅里叶变换
filtered = fft_result.copy()
filtered[20:] = 0
inverse_fft = np.fft.ifft(filtered)

# 绘制原始信号和分解后的信号
plt.figure(figsize=(14, 7))
plt.plot(close, label='Original Close')
plt.plot(np.real(inverse_fft), label='Reconstructed from Sine Waves')
plt.legend()
```

在 FFT 转换之后，fft_result[0] 就保留了该段波形的直流分量。我们按 win 为滑动窗口，提取价格数据进行 FFT 变换，得到一个直流分量序列。

``` python
def calc_wave_energy(df, m, n):
    close = df.close/df.close[0]
    dc = close.rolling(m).apply(lambda x: np.fft.fft(x)[0])
    return -1 * dc.diff()

_ = alphatest(500, 
              start, 
              end, 
              calc_factor=calc_wave_energy, args=(30,10))
```

年化 alpha 达到 13.8%，并且从分层收益均值图来看，非常完美。

!!! tip
    这里我们设置universe仅为500支，是为了减少计算量。如果你将其改为2000支，年化Alpha甚至会达到17%。这也说明这个因子是比较稳定的。

## 10. TSFresh 因子库

TSFresh[^tsfresh] 是一个 Python 包。它自动计算大量时间序列特征。此外，该包包含用于评估此类特征在回归或分类任务中解释力和重要性的方法。

![caption](https://images.jieyu.ai/images/2024/10/tsfresh.png?width=30%&align=center)

我们通过下面的例子，说明如何使用 tsfresh 快速提取特征。

```python
from tsfresh import extract_features

df = load_bars(start, end, 2).reset_index().dropna(how='any')
features = extract_features(df, column_id="asset", column_sort="date")
features.tail()
```

我们要使用的方法是 extract_features。

tsfresh 被设计成支持为多个样本（在这里，即多个资产）的多个时间序列（在这里，即 OHLC）同时提取特征。extract_features 接受一个 DataFrame 作为输入。在量化场景中，我们一般使用由时间和资产名双重索引的 DataFrame 就够了，这也正是 load_bars 所返回的数据格式。

不过，我们需要对 load_bars 返回的数据略微进行一些调整，即将索引转换为普通列，并去掉任何 nan 值。这是 tsfresh 不允许的。

我们可以查看一下传递给 extract_features 的 dataframe 的格式：

```python
df.tail()
```

最终输出了 5480 个特征，大约为每个时间序列（OHLC）生成了 680 个左右的特征。具体到每个时间序列，比如收盘价，我们可以这样查看提取出来的特征：

```python
features.filter(like="close")
```

### 10.1. 定制提取任务

特征提取的工作量很大，tsfresh 会自动使用多进程来完成工作。但尽管如此，如果我们不限制提取的特征，这仍然是一个以数小时为单位的任务。然而，tsfresh 生成的许多特征，在量化中并不适用。因此，我们理所当然地希望减少这个工作量。

首先，我们可以用下面的方法来指定只为部分时间序列提取特征：

```python
extract_features(
    df[["asset", "date", "close", "volume"]], 
    column_id="asset", 
    column_sort="date"
)
```

这样我们就排除了除 close 与 volume 之外的所有列。

其次，我们可以通过 fc_parameters 参数，指定只提取哪些特征。该参数是一个 dict，键为特征名称，值为特征提取参数。

```python
fc_parameters = {
    "length": None,
    "large_standard_deviation": [{"r": 0.05}, {"r": 0.1}]
}

features = extract_features(
    df[["asset", "date", "close", "volume"]], 
    column_id="asset", 
    column_sort="date",
    default_fc_parameters=fc_parameters, 
    n_jobs=1
)

features.columns
```
这一次，我们得到的特征集共有 6 列，其中因为给 large_standard_deviation 传入了两组参数，于是该特征贡献了 2 个特征。由于要处理的数据量比较小，所以我们这次只使用了一个 job。

tsfresh 提供了几个内置的特征集，它们是：

* ComprehensiveFCParameters，默认设置，提取全部特征
* EfficientFCParameters，排除那些提取时耗时间长的特征
* IndexBasedFCParameters，只包含输入要求为 pd.Series 的特征
* MinimalFCParameters，最小集，用于测试
* TimeBasedFCParameters，只包含需要日期时间索引的特征，特别是关于季节、日期计算的。由于交易日历的特殊性，这个集合并不能精确地反映量化场景下的需要。

### 10.2. 滑动窗口下的因子提取

一般情况下，tsfresh 提取的特征都是基于我们输入的 dataframe 的所有行的（即全时间段）。然而，当我们进行因子分析时，往往是基于这个全局时间段中的每一个长度相同的滑动窗口的，为此，tsfresh 提供了工具以便我们将数据进行转换。

假设我们有以下数据：

```python
df = pd.DataFrame({
    "date": [1, 2, 3, 4, 1, 2, 3],
    "asset": ["a", "a", "a", "a", "b", "b", "b"],
    "close": [10, 20, 30, 40, 100, 200, 300]     
})
df
```

我们的特征将会在一个跨度为 2 的时间窗口上进行提取，也就是对资产 b 而言，我们希望生成 (100,200), (200,300) 这样两个序列。这个工具就是 roll_time_series:

```python
from tsfresh.utilities.dataframe_functions import roll_time_series

rolled = roll_time_series(df, 
                          column_id="asset",
                          column_sort="date",
                          min_timeshift=1,
                          max_timeshift=1)

rolled
```

最终资产 a 产生了三个子集，它们的 id 分别是 (a,2), (a,3) 和 (a,4)。参数 min_timeshift 和 max_timeshift 用以控制生成的滑动窗口的大小。这里 min_timeshift=1，意味着我们希望在每个时间点上至少有一段连续的 2 个数据点（即 min_timeshift + 1)。因此，第一个滑动窗口子集，由于其长度只有 1，所以被过滤掉了。这也是为什么我们看到的生成的 a 的子集的 id 从 (a,2) 开始的原因。

### 10.3. 因子分析

基于新生成的滑动窗口数据，现在我们可以提取符合因子检验要求的特征了。下面，我们从头开始演示一次：

```python
from tsfresh import extract_features
from tsfresh.utilities.dataframe_functions import roll_time_series

start = datetime.date(2023,1,1)
end = datetime.date(2023,12,31)
barss = load_bars(start, end, 100)
df = barss.reset_index().dropna(how='any')

rolled = roll_time_series(df[["date", "asset", "close"]],
                          column_id="asset",
                          column_sort="date",
                          min_timeshift=9,
                          max_timeshift=9)

settings = {
    "linear_trend": [{"attr": "slope"}],
    "approximate_entropy": [{"m" : 4, "r": 2}]
}

# 提取特征
features = extract_features(rolled[["date","id", "close"]], 
                            column_id="id", 
                            column_sort="date",                     
                            default_fc_parameters=settings,
                            impute_function=None)
features.tail()
```

现在我们就得到了两列特征，分别为"close__linear_trend___attr_slope"和"close__approximate_entropy___m_4_r_2"。

我们先将 slope 特征提取出来，注意这里的 swaplevel 的调用。

```python
slope_factor = features.filter(like="linear")
slope_factor = slope_factor.swaplevel()
slope_factor.index.set_names(["date", "index"], inplace=True)
slope_factor.tail()
```

现在我们得到的 slope_factor 跟之前熟悉的因子数据一模一样。接下来，就是我们一直以来都熟悉的步骤了：

```python
prices = barss.price.unstack()
merged = get_clean_factor_and_forward_returns(slope_factor, prices)
create_returns_tear_sheet(merged)
```

出于速度的考虑，我们的标的池只使用了 100 支，因此，这里的结果就不分析了。使用同样的方法，我们还可以对特征 approximate_entropy 进行因子分析。

在上面的分析中，我们只使用了原始的 OHLC 数据（具体地说是 close 数据）。但是，对经典的时序分析而言，很多方法要求时间序列符合平稳性特征。所谓的平稳性特征，就是要求时间序列的统计特性（如均值、方差和自协方差）在时间上是恒定的。

显然，OHLC 都不是平稳性序列，在它们之上很多 tsfresh 的特征提取算法是无效的。通过收盘价构建的每日收益序列可以近似地认为是平稳序列，因此，部分特征的构建应该使用收益数据。

!!! tip
    收益数据显然也不是平稳序列，我们只能在较短的时间跨度上把它近似地当成平稳序列。长期来看，由于股市市值与 GDP 成正相关，所以，如果一国的 GDP 是持续上涨的，那么该国的证券市场总值也会是上涨的。这就要求长期来看，收益均值大于零，从而收益序列不满足平稳性要求。

## 11. 行为金融学因子

行为金融学是从交易者的行为以及产生这种行为的心理等动因来解释、研究和预测市场的发展。其挖掘的是市场波动的深层次驱动因素。

关于行为金融学因子的研究比较新，很多都是基于交易经验构建的，没有系统地研究框架。这里我们举几个例子。

### 11.1. 处置效应的 CGO 因子

本段参考了广发证券 [^gf] 的研报及 Hugo 的 Github Repo[^hugo]。

这个因子跟 Alpha042 比较接近。它的公式是：

$$
CGO_t = \frac{P_{close,t-1} - RP_t}{RP_t}
$$

RP 是相对成本的意思。它的核心思想是，当前价格与投资者相对成本之间的差额，就是资本利得突出量 (CGO)[^cgo]。

下面的代码显示了如何构建这个因子。

```python
def calc_turnover_weight(arr: np.array) -> np.array:
    """换手率衰减加权"""
    arr_ = arr.copy()
    arr_[0] = 0

    return np.multiply(np.cumprod(np.subtract(1, np.roll(arr_, -1))[::-1])[::-1], arr)

def calc_rp(turnover: pd.DataFrame, ma_avg: pd.DataFrame) -> pd.Series:
    """
    计算参考价格
    """
    # 换手率衰减加权 这个位置决定了无法滚动 这里传入的数据长度 N 计算处理的就是 N 日的 CGO
    turnover_weighs = turnover.apply(calc_turnover_weight)
    # 归一化
    scale_weights = turnover_weighs / turnover_weighs.sum()

    return (scale_weights * ma_avg).sum()

def calc_roll_rp(df: pd.DataFrame, N: int) -> pd.DataFrame:
    iidx = np.arange(len(df))

    shape = (iidx.size - N + 1, N)

    strides = (iidx.strides[0], iidx.strides[0])

    step = np.lib.stride_tricks.as_strided(
        iidx, shape=shape, strides=strides, writeable=True
    )

    turnover = df["turnover_ratio"]
    ma_avg = df["avg"]

    rp_df = pd.concat(
        (calc_rp(turnover.iloc[i], ma_avg.iloc[i]) for i in step), axis=1
    ).T

    rp_df.index = df.index[N - 1 :]

    return rp_df

def calc_cgo(df: pd.DataFrame, N: int) -> Tuple[pd.DataFrame]:
    rp_df = calc_roll_rp(df, N)
    close = df["close"].reindex(rp_df.index)

    return close / rp_df - 1, rp_df
```

它的输入数据包含了收盘价、移动均值和换手率。在本课程环境中，为了使用 turnover_ratio，我们需要使用 tushare。

这段代码用来获取沪深 300 数据。在它返回时，已经包含了收盘价、移动平均和换手率，并且是 calc_cgo 要求的格式：

```python
def hs300_members():
    date_str = "20231201"
    
    hs300 = pro.index_weight(index_code='000300.SH', start_date=date_str, end_date=date_str)
    
    def translate(code):
        symbol, exchange = code.split(".")
        if exchange == "SH":
            return symbol + ".XSHG"
        elif exchange == "SZ":
            return symbol + ".XSHE"
        elif exchange == "BJ":
            return symbol + ".BJ"
        
    return list(map(translate, hs300["con_code"]))

def fetch_data_for_cgo(start, end, universe, n):
    """从 tushare 获取数据，转换为以下格式：

            ------------------------------------------------------------
            |          |      close    |      avg      | turnover_ratio|
            |    day   |code1|code2|...|code1|code2|...|code1|code2|...|
            ------------------------------------------------------------
            |2020-11-01| 0.1 | 0.3 |...| 0.3 | 0.5 |...|0.03 | 0.9 |...|
            ------------------------------------------------------------
    """
    date_range = pd.bdate_range(start, end)

    barss = []
    for date in date_range:
        df = fetch_daily_basic(date)
        if len(df) == 0:
            continue
        barss.append(df[["trade_date", "close", "turnover_rate_f"]])

    df = pd.concat(barss)
    df.rename(
        columns={"turnover_rate_f": "turnover_ratio", "trade_date": "date"}, inplace=True
    )

    df["date"] = pd.to_datetime(df["date"])
    df = df[df.index.isin(universe)]
    df = df.reset_index().set_index(["asset", "date"])
    df["avg"] = df.groupby(level=0)["close"].rolling(n).mean().droplevel(0)
    reshaped = df.unstack(level="asset")
    return reshaped

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)

# 获取沪深 300 成份股
universe = hs300_members()

barss = fetch_data_for_cgo(start, end, universe, 5)
barss.tail()
```

接下来，我们将计算 CGO 因子：

```python
factor, _ = calc_cgo(barss, 5)
factor = factor.stack()
factor.tail()
```

注意 calc_cgo 返回的数据，在格式上不符合 Alphalens 的要求，所以我们对它进行了 reshape 操作。

在获取价格数据时，我们仍然使用了本地缓存数据。尽管不同的数据源会因为复权的方式不同，有时会导致价格不一致的情况出现，但是，当价格数据只是用来计算收益时，不同的数据源应该都能计算出一致的收益数据。

```python
prices = load_bars(start, end, tuple(universe))["price"].unstack("asset")
prices.tail()
```

最后是因子检验：

```python
merged = get_clean_factor_and_forward_returns(factor, prices)
create_returns_tear_sheet(merged)
```

在一年的测试中，我们得到的年化 alpha 是 9%。在 [BAFE](https://www.joinquant.com/view/community/detail/27009) 的回测中，从 2010 年起到 2020 年的 10 年间，得到的年化收益是 26.75%，累计收益是 902%。

### 11.2. 整数关口因子

整数关口是一种经济学上的常见现象。我们在 [左数效应 整数关口与光折射](http://www.jieyu.ai/blog/2024/01/24/left-side-effect-integer-pressure/) 中有过介绍。这篇文章中介绍了芝加哥大学的 John A. List 和康纳尔大学的 Manoj Thomas 等人在这一效应上的研究。

通俗地说，人们会觉得 3.99 元的商品，要比 4.00 元的商品便宜不少，从而更容易接受。以证券交易的视角来看，可以认为 4 元这样的整数位是一种压力位（如果价格在 4 元下方）或者支撑（如果现价在 4 元上方）。

尽管缺乏系统性地研究，但在证券交易中，整数位的压力与支撑非常明显。它通过散户下单时的取整（以便更容易计算成本）、大 V 预测指数点位等等方式得到强化。

!!! note
    整数关口效应的确存在。但是，作者并没有找到很好的方法来将它因子化。

### 11.3. 压力（支撑）测试因子

在技术指标一节中，我们提到了前高、前低的在指示压力位和支撑位上的重要作用，一些技术指标在构建时，也使用了这些信息。但是，这些信息如此重要，我们有必要进一步将它的信号意义更加明确：如果已知某个点位是压力位，现价位于压力位的下方，向上攻击不能有效突破时，此时下跌的概率会加大。反之亦然。

!!! note
    同样地，作者还在探索将这些事件因子化（至少是特征化）的方法，似乎需要新的（技术）工具，比如强化学习。

<!--冲击前高、前低失败-->
### 11.4. 缺口因子
<!--逢缺必补，是因为我们很多年都是围绕 3000 点震荡，象美国股市，一直创新高-->

如果次日开盘价格不在前一日的 True Range 范围内，就形成了一个缺口。缺口表明投资者在该方向上有较强的一致预期，这种预期往往是由某种强大的事件冲击引发的，从而导致集合竞价时，交易者出现抢筹现象。

在向上跳空的缺口中，缺口是开盘价减去前高；在向下跳空的缺口中，缺口是开盘价减去前低。在实现成为因子时，我们需要将其标准化。

下面的示例演示了缺口的计算：

```python
def calc_gap(df):
    # 计算向上跳空和向下跳空
    up_gap_condition = df["open"] > df["high"].shift(1)
    down_gap_condition = df["open"] < df["low"].shift(1)

    # 计算向上跳空和向下跳空的值
    up_gap_value = (df["open"] / df["high"].shift(1)) - 1
    down_gap_value = (df["open"] / df["low"].shift(1)) - 1

    conditions = [up_gap_condition, down_gap_condition]

    choices = [up_gap_value, down_gap_value]

    df["gap"] = np.select(conditions, choices, default=0)

    return df

df = pd.DataFrame(
    {
        "open": [100, 107, 110, 100, 120],
        "high": [105, 110, 115, 120, 125],
        "low": [95, 100, 105, 110, 115],
    }
)

calc_gap(df)
```

我们通过下面的代码来测试这个因子：

```python
def calc_gap(df):
    # 计算向上跳空和向下跳空
    up_gap_condition = df["open"] > df["high"].shift(1)
    down_gap_condition = df["open"] < df["low"].shift(1)

    # 计算向上跳空和向下跳空的值
    up_gap_value = (df["open"] / df["high"].shift(1)) - 1
    down_gap_value = (df["open"] / df["low"].shift(1)) - 1

    conditions = [up_gap_condition, down_gap_condition]

    choices = [up_gap_value, down_gap_value]

    df["gap"] = np.select(conditions, choices, default=0)

    invalid = np.abs(df.close.pct_change()) > 0.095
    df["gap"][invalid] = 0

    return df["gap"]

start = datetime.date(2018, 1, 1)
end = datetime.date(2023,12,31)

_ = alphatest(2000, start, end, calc_factor=calc_gap, bins=10, long_short=False)
```

很显然，对此因子，我们只能采用 bins 分层法（大量的数据为 0）。

这个因子获得了非常惊人年化收益（31.2%的年化 Alpha）。它在实战中甚至还会获得更高的收益。因为既然这个因子展示出来强大的收益能力，并且我们也知道其原理，显然，一旦收盘前证实缺口存在，我们就应该立即买入（或者卖出），而不是等待第二天开盘。但在因子测试中，我们使用的是第二天开盘买入的方式，这显然慢了一步。

### 11.5. 遗憾规避理论因子

关于遗憾规避理论，请见我们这篇 [文章](http://www.jieyu.ai/blog/2023/12/26/how-regret-factor-get-5.5-sharpe-ratio/) 的讨论，以及 [国金高智威研报](https://www.jieyu.ai/assets/ebooks/%E9%81%97%E6%92%BC%E8%A7%84%E9%81%BF%E5%9B%A0%E5%AD%90.pdf)。

![](https://images.jieyu.ai/images/2023/03/20230322102309.png?width=75%&align=center)

由于该研报中提及的因子需要使用 tick 数据，在我们课程环境中无法验证，同时实现上也没有难度，这里就不给出代码了。需要特别指出的是，研报指出，该因子的夏普率达到了5.5，这一结论是有可能成立的。

## 12. Footnotes

[^tsfresh]: [TSFresh](https://tsfresh.readthedocs.io/en/latest/) 官方文档。
[^gf]: 广发证券 [《行为金融因子研究之资本利得突出量 CGO 与风险偏好》](https://www.jieyu.ai/assets/ebooks/行为金融因子研究之资本利得突出量 CGO 与风险偏好。pdf)
[^hugo]: Hugo 的 [QuantsPlaybook](https://github.com/hugo2046/QuantsPlaybook) 汇集了各类研报及实现。
[^cgo]: Grinblatt（2005）, Prospect theory, mental accounting and momentum. [PDF](https://www.jieyu.ai/assets/ebooks/grinblatt-momentum_JFE.pdf)
