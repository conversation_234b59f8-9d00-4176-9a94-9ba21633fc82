
# ALPHALENS 快速开始

![Alphalens Logo](https://images.jieyu.ai/images/2024/01/alphalens.jpg?width=300&float=left)

Alphalens 是一个用于因子检验的 Python 库。它提供了一系列工具来分析因子检验的结果，实现了我们前面提到的收益回归法、IC 法和分层回溯法。不仅如此，它提供了丰富多彩的可视化报告，以便可以快速、直观地了解因子的绩效。

学习和使用 Alphalens 的另一个重要原因是，Alphalens 为因子检验提供了一个相对正确、可靠的工程实现。因子检验中涉及很多细节，这些细节对结果的影响，往往是差之毫厘，失之千里。

比如，我们在网上看到了很多关于因子检验的文章，但这些例子在细节上不一定正确。我们很快就会看到，因此，学习、理解和运用 Alphalens 来进行因子检验，是得到正确结果最有把握的途径。毕竟，在 quant 开源社区里，又有谁能比得过 Quantopian 的权威呢？更何况，Alphalens 已经被无数人使用，我们可能遇到的任何 corner case，很可能其他人都已经遇到过并提出了反馈。

!!! note
    Alphalens 由 Quantopian 开发并开源。他们开源的产品还有 pypolio,zipline 等。Quantopian 由 John Fawcettt（约翰. 福塞特）创建于 2011 年。Fawcettt 从哈佛大学毕业之后，一直致力于量化分析系统研发，他还是 Tamale 的联创，这家公司于 2008 年被 Advent Software 以 2800 万美金收购。<br><br>2020 年被 Robinhood 收购。当时，Fawcett 谈到这次收购时表示，“当我们开始 Quantopian 时，我们对它有很多远大的梦想。我们成功地在宇宙中留下了痕迹，并让量化金融界的很大一部分了解我们并使用我们的工具”。尽管 Quantopian 被收购，但他们的使命已经成功。点击阅读 [反抗者的崛起！Fawce 和 Quantopian 的量化之路](http://www.jieyu.ai/blog/2024/06/01/john-fawcett-with-quantopian/)

在我们前面的内容中，数据预处理和因子检验花了较多的篇幅，但对因子检验的结果分析着墨不多，因为这需要大量的绘图操作。这也是Alphalens的强项。

Alphalens 把数据预处理和因子检验过程完全封装了起来，使得我们只需要一两个 API 就可以完成这一切。使用 Alphalens，重点在于如何分析它产生的报告。

下面的思维导图展示了 Alphalens 的框架和调用步骤。

![alphalens-frame-work](https://images.jieyu.ai/images/2024/06/alphalens-framework.png?width=75%)

从 [](#alphalens-frame-work) 可以看出，Alphalens 主要由 4 个模块组成，分别是：

1. utils。这个模块提供了一些工具函数，比如对因子分层，计算前向收益，对数据进行预处理等等。我们使用 Alphalens 的入口一般是 get_clean_factor_and_forward_returns，也在这个模块下。
2. tears。这个模块是分析的入口，也是用户接口之一。我们常常使用的 create_full_tear_sheet 也出自这个模块，它用来执行因子检验并生成报告。
3. performance。这个模块不是对外接口。它是因子检验的执行器。我们在前面的课程中看到的因子 IC 检验、收益回归分析等，都由这个模块的相应函数来实现。它被 tears 模块中的方法调用。
4. plotting。Alphalens 为我们承担了大量可视化工作，这部分工作是在 plotting 模块中实现的。它一般多由 performance 模块中的方法来调用。

通过 Alphalens 完成单因子检验只需要三步：

1. 计算因子和收集价格数据。
2. 使用 get_clean_factor_and_forward_returns 函数，将因子值和价格数据进行清洗，计算前向收益，将收益与因子按日期对齐。对应 [](#alphalens-frame-work) 的序号 2。
3. 使用 create_full_tear_sheet 函数，生成报告。对应 [](#alphalens-frame-work) 的序号 3。

下面，我们就通过一个示例，演示通过 Alphalens 进行因子检验，是如何简单、又是如何高效。

## 1. 斜率因子

斜率因子是动量因子中的一种，由 Andreas F. Clenow 首先发表在 Stocks on the Move[^clenow] 一书中。

这个因子比起Carhart[^Carhart]的动量因子更符合普通人的直觉，特别是喜欢看 K 线的投资者，因此也得到了很多人的喜爱。这个因子的实现在 Quantopian 和 QuantConnect[^quantconnect]网站上都有文章讨论。

!!! tip
    我们简单回顾一下 Mark Carhart 提出的动量因子，它是计算单个股票过去一年的回报率，排除最近一个月数据（以防股价操纵），再按回报率排序，取前 10%的股票作为买入信号，末尾 10%的股票作为卖出信号。

在 Andreas Clenow 的策略中，他使用了过去 90 天的年化指数的回归斜率作为动量因子。由于只需要 90 天的数据就能生成交易信号，因此，信号就比卡哈特的动量因子更灵敏一些。

我们这里测试的因子，是用过去 10 天的回归斜率作为动量因子。主要目标是尝试下新的可能性，也是考虑到在 A 股，动量因子的半衰期一般比较短的缘故。

你也可以对代码进行修改，以计算 90 天、或者最近 11 个月的斜率因子。

<Example 1/>

```python
from numpy.lib.stride_tricks import as_strided
from alphalens.utils import get_clean_factor_and_forward_returns
from alphalens.tears import create_full_tear_sheet

def rolling_slope(close: NDArray, win:int, *args):
    if len(close) < win:
        return np.full((len(close), ), np.nan)

    stride = close.strides
    
    slopes, _ = np.polyfit(np.arange(win), 
                           as_strided(close, (len(close)-win+1, win), 
                           stride+stride).T,
                           deg=1)
    left_padd_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_padd_len, 0), mode='constant', constant_values=np.nan)
    return slopes

# 1. 获取行情数据
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 29)
barss = load_bars(start, end, universe=2000)

# 2. 计算因子
factors = []
for group in barss.groupby(level='asset'):
    close = group[1].close

    slopes = rolling_slope(close.to_numpy(), 10)
    factors.append(pd.Series(slopes, index=group[1].index))
    
factor = pd.concat(factors).rename_axis(index={'frame':'date'})

# 3. 提取价格数据
prices = barss['price'].unstack(level=1)

# 4. 预处理
factor_data = get_clean_factor_and_forward_returns(factor, prices)

# 5. 生成报告
create_full_tear_sheet(factor_data)
```

这段代码基本上是按照我们说的三步骤来实现的。但为了方便后续参数调优，我们把第一步又进行了拆解，可以细分为三个步骤：

1. load_bars 函数：获取并缓存原始数据。在寻找因子参数的过程中，原始数据及计算好的因子往往是可以复用的。因此，我们把原始数据缓存起来，这能大大加速因子检验的过程。在此过程中，我们可能还需要进行一些格式转换。
2. rolling_slope函数：我们把因子计算提取成为一个方法，并且作为一个参数传递给 Alphatest 函数。后面我们将看到，有时候是需要根据初次因子检验的结果来调整因子参数的。
3. 获取价格数据。在示例中这一步比较简单，没有另外提取成为函数。

下面，我们就详细介绍每一步操作，重点是弄清楚 factor 和 prices 这两个 DataFrame 的数据接口，以及 get_clean_factor_and_forward_returns 和 create_full_tear_sheet 这两个函数的参数及处理过程。

## 2. 计算因子和收集价格数据
### 2.1. 原始数据：缓存及格式转换

代码很简单。主要作用是避免重复读取行情数据。因为我们在进行参数调优时，很多时候行情数据是复用的。这样一来，进行因子检验的时间就会大大减少。

load_bars 函数的目标是为因子检验准备原始数据。这些数据可能在多个服务器上，也可能就在你本地。不同的数据源给出的数据在格式上往往都不是一致的。一般我们要在这里对其进行转换。

比如，在课程中使用的数据源，它的时间日期字段是 frame，Alphalens 中要求时间日期字段是 date。它的证券代码字段是 sec，在 Alphalens 中要求是 asset。因此，格式转换是必须的。

!!! note
    在本章中，我们通过函数load_bars来加载事先缓存的数据。这对我们课程演示而言是有好处的，它屏蔽了与本章无关的技术细节，使得代码更易阅读和理解，也大大加快了速度。为了让你体会实际工作中，数据转换可能是怎么样的，我们也设计了一个习题，让你来实现一个load_bars，从真正的数据源来加载数据。这个习题也将为你设定一个框架，以后无论数据源是何种格式，只要转换后的格式跟这个习题的结果一致，后面的因子分析步骤就完全无缝衔接。

<!-- 这段代码还进行了一些合并操作。-->

最终我们生成的数据结构，是以日期和 asset 为索引的，包含了原始字段、新增 price 字段的一个 DataFrame，如 [](#原始数据格式)所示：

![原始数据格式](https://images.jieyu.ai/images/2024/08/fa-04-load-bars-result.jpg?width=80%px&align=center)

!!! note
    在 [](#原始数据格式)中有一列名为 factor，它是数据源带来的，在这里是复权因子的意思，不是我们接下来要计算的斜率因子。

### 2.2. 计算因子

第二步，定义因子计算方法。在本例中，我们定义了一个名为 rolling_slope 的函数，输入 close 字段，以及 win 周期，它将以 win 为周期对 close 进行滑动，并计算这个滑动窗口内 close 序列回归拟合线的斜率。

<!-- 这个函数如何工作的，不是重点，可以放在课程中讲解 
可以讲一下 as_strided, linregress 方法

也可以带一句升维的方法
-->

如何计算因子是大家自己要解决的问题。我们重点讨论这一步输出结果应该满足什么样的格式要求。

严格地说，我们应该输出一个以日期和资产标签（asset）为双重索引的 pd.Series，其中 date 为带时区信息的 Timestamp 类型，是第一层索引；asset 为字符串类型，是第二层索引；而 factor 的值为浮点数。

以下展示了计算出的 factor 数据格式一例。

```text
date        asset
2024-07-25  688156.XSHG   -0.052970
2024-07-26  688156.XSHG   -0.031212
2024-07-29  688156.XSHG   -0.025879
2024-07-30  688156.XSHG   -0.016545
2024-07-31  688156.XSHG   -0.010303
Length: 1380, dtype: float64
```

!!! tip
    有时候我们也看到，这一步的输出会处理成一个 DataFrame。无论如何，它的索引必须是日期和资产标签。这样，无论因子列的名字是什么，我们都可以通过 factor[{factor_name}] 来得到一个符合要求的 Series，以传递给 get_clean_factor_and_forward_returns。


### 2.3. 提取价格数据

价格数据已经包含在load_bars 函数返回的结果中，如[](#原始数据格式)所示。但是，为了Alphalens能使用这个数据，我们还要进行进一步转换。

Alphalens要求的价格数据，是所谓的宽表。它是一个以日期为索引，以资产标签值为列的 pd.DataFrame，其中 date 为带时区信息的 Timestamp 对象。所以，如果我们是在做全市场因子分析的话，可能会囊括 A 股大部分股票，这将是一个多达数千列的宽表，如[](#价格数据格式)所示：

![价格数据格式](https://images.jieyu.ai/images/2023/07/prices_df_format.png?width=75%)

[](#example-1)中（<line>第36行</line>）我们使用了一个名为 unstack 的函数。这是 Pandas 库中用于重塑数据的一个非常有用的方法，主要用于将多级索引（层次化索引）的 DataFrame 转换成宽表格式。通常，unstack 方法会将最内层的列索引提升到行索引的位置，从而展平 DataFrame。

!!! tip
    在类似的场景中，我们还可能会使用 pivot 这个函数，但根据我们这里的输入数据格式，使用 unstack 是最简洁的方法。

## 3. 数据预处理

通过上述步骤，我们已经得到了原始因子数据 (factor) 和价格数据 (prices)。

接下来，我们要调用 `get_clean_factor_and_forward_returns` 进行数据预处理，包括处理缺值、中性化、标准化等，并计算前向收益（引用自[](#example-1)<line>第39行</line>）。

```python
from alphalens.utils import get_clean_factor_and_forward_returns

cleaned = get_clean_factor_and_forward_returns(factor, prices)
cleaned
```

我们将看到以下日志信息：

> Dropped 8.0% entries from factor data: 8.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).
max_loss is 35.0%, not exceeded: OK!


最终得到的 clean，形如下表：

![merged factor data](https://images.jieyu.ai/images/2024/06/cleaned-factor.jpg?width=500)

如果我们将这个结果与 Alphalens 的文档进行比较，会发现它缺少了 group 一列：

![Alphalens文档中的merged factor data](https://images.jieyu.ai/images/2024/06/alphalens-merged-factor-data.jpg)

这是为什么呢？这是因为在示例代码中，我们只使用了默认参数，而没有向 get_clean_factor_and_forward_returns 方法提供 group 参数（分组信息）。

那么，get_clean_factor_and_forward_returns 方法究竟有多少参数？这些参数的作用是什么，又将对分析结果产生什么样的影响？

Alphalens 的 get_clean_factor_and_forward_returns 方法总共接受多达 9 个参数：

```python
def get_clean_factor_and_forward_returns(factor,
                                         prices,
                                         groupby=None,
                                         binning_by_group=False,
                                         quantiles=5,
                                         bins=None,
                                         periods=(1, 5, 10),
                                         filter_zscore=20,
                                         groupby_labels=None,
                                         max_loss=0.35,
                                         zero_aware=False,
                                         cumulative_returns=True):
    """将因子数据、价格数据和分组数据整合成为一个包含对齐后的时间资产索引的 DataFrame。"""
```

factor 和 prices 这两个参数我们已经在前面的代码中介绍过了。

### 3.1. 行业中性化

groupby 用来提供分组信息，以实现行业中性化。一般情况下，如果我们要实现行业中性化，应该在 factor 表中增加 sector 一列，该列的数值为整数。因此，合格的 factor 表应具有以下格式：

![行业中性化](https://images.jieyu.ai/images/2024/07/factors-datastructure-with-sector.jpg)

我们注意到，这个表格中 sector 一列使用的是整数作为标签，这方便了 Alphalens 运算，但会导致生成的报告中也使用这些意义不明的数字。因此，Alphalens 允许我们通过 groupby_labels 参数，为 sector 一列提供人类可读的标签。

groupby_labels 参数是一个字典：

```python
sector_labels = dict({
    101: 'BASIC_MATERIALS', 
    102: 'CONSUMER_CYCLICAL', 
    103: 'FINANCIAL_SERVICES', 
    104: 'REAL_ESTATE', 
    205: 'CONSUMER_DEFENSIVE', 
    206: 'HEALTHCARE', 
    207: 'UTILITIES', 
    308: 'COMMUNICATION_SERVICES', 
    309: 'ENERGY', 
    310: 'INDUSTRIALS', 
    311: 'TECHNOLOGY', 
    -1: 'Unknown'
})

```

!!! info
    行业分类数据从你使用的数据源处获取。如果你从事海外市场交易，可以使用的行业分类标准有 GICS[^GICS]、ICB[^ICB] 等。我国境内适用中国国家标准《国民经济行业分类》（GB/T 4754），但更多地，我们使用证监会行业分类，或者申万行业分类。每支股票所属的行业分类，一般可以通过你使用的数据源查询到。

### 3.2. 如何实现分层回溯

quantiles 参数和 bins 参数都与分层回溯的实现有关。这两个参数是互斥的，我们一次只能传入一个。两者都用于帮助 Alphalens 将因子进行分层，那么它们有何区别呢？

这两个参数都可以是整数或列表。最主要的区别是，在默认情况下，quantiles 是按分位数进行等分划分的；而 bin 则是按绝对值进行等间距划分的。因此，这两个参数可以组合出四种情况，我们通过下面的代码演示：

<Example 2 />

```python
import numpy as np
import pandas as pd

rng = np.random.default_rng(78)

factors = rng.normal(size = 15)

# 模拟 QUANTILES = 3
tiers = pd.qcut(factors, 3)
print(tiers.value_counts())

# 模拟自定义分位数组 QUANTILES = [0.25, 0.75]
cut_points = [np.quantile(factors, 0.25), np.quantile(factors, 0.75)]
bins = [factors.min()] + cut_points + [factors.max()]

tiers = pd.cut(factors,bins = bins)
print(tiers.value_counts())

# 模拟 BINS = 3
edges = np.linspace(factors.min(), factors.max(), 3 + 1)
tiers = pd.cut(factors, bins=edges)
print(tiers.value_counts())

# 模拟按 EDGES 进行划分
edges = [-1, 0.12, 1.33, 2.53]
tiers = pd.cut(factors, bins=edges)
print(tiers.value_counts())
```

在 quantiles 和 bins 之外，还有两个参数与分层的实现密切相关。

其一是 binning_by_group。它指示 Alphalens 是否在组内进行分层。这个参数尤其在结合 groupby 参数使用时变得重要。

当 binning_by_group 设为 True 时，因子值的分箱（如通过 quantiles 或 bins 参数定义）将在每个组内独立进行。这意味着，即使在不同的组内因子值的分布可能不同，每个组内的因子值也会根据该组的分布被划分为相同的分位数或区间。这种方法通常用于控制不同组别（如行业、市值等）对因子分析结果的影响，确保组内中性。

例如，如果你正在分析股票因子，而这些股票分布在不同行业中，那么设置 binning_by_group=True 并结合 groupby='sector' 参数使用，将确保每个行业的股票根据该行业内的因子值分布被划分为相同的分位数。这样，即使某些行业的股票因子值普遍较高或较低，也不会影响分位数分析的公平性。

其二是 zero_aware。如果因子值分布在零值两端，那么设置此参数为 True 时，Alphalens 在进行分层时，就会对正负因子值分别分层，再进行组合。

比如，如果以营业利润作为因子，显然一部分因子值为正，一部分为负。当我们指定 zero_aware=True 并且指定 quantiles 为 4 时，Alphalens 就会为利润率为负的股票保留 4 个分位数中的两个，为营业利润为正的股票保留另外两个分位数。这样做可以确保不会在一个分层中同时出现利润为正的股票和利润为负的股票。

<!-- 所以，要得到正确的因子分析结果，真的很复杂 -->
!!! hint
    如果你不太理解 zero_aware 的用处，那么多半是因为你还不需要它。这种情况下，你可以大胆忘掉这个参数。

### 3.3. 去极值、去缺失值

Alphalens只对收益进行去极值（通过filter_zscore参数传入，可选），但不对因子进行去极值操作。filter_zscore的默认值是 20，也就是所有远期收益值的 Z-score 绝对值大于 20 的数据点都将从分析中被排除。

!!! warning
    分析Alphalens的源码可知，此处会引入轻微的look-ahead偏差。Alphalens的文档对此也有说明。

顾名思义，Alphalens 对收益去极值的方法是标准化去极值法。

如果我们要对因子数据进行去极值处理，就需要我们自己完成，再将预处理后的 factors 传给 get_clean_factor_and_forward_returns 方法。

Alphalens 处理缺失值和极值的方法就是删除该记录。这样会导致输出数据比输入数据少一些。这个过程是由 Alphalens 控制的，我们无法干预。我们只能通过 max_loss 这个参数告诉 Alphalens，当这种损失大于 max_loss 时，这个预处理过程就失败了，不要进一步进行分析了。

但是，无论这个值设置为多少，Alphalens 都会输出一条日志，告诉我们在预处理过程中，有多少条数据被 drop 了：

```txt
Dropped 23.8% entries from factor data: 22.4% in forward returns computation 
and 1.3% in binning phase (set max_loss=0 to see potentially suppressed 
Exceptions).
max_loss is 35.0%, not exceeded: OK!
```

根据日志，在这次预处理过程中，有 23.8%的数据被 drop 了，其中 22.4%发生成计算远期收益时，另有 1.3%发生在分层时。根据这些信息，我们应该给 prices 表多增加一些记录，使之完全覆盖 factors 记录。

如果你还想看更具体一点的信息，可以设置 max_loss = 0。

### 3.4. 远期回报的计算

periods 参数用于指定你想要计算的远期收益（forward returns）的时间窗口。periods 接受一个元组或列表，其中的每个元素表示一个持有期长度。

periods 参数对于因子分析非常重要，因为它允许你评估因子在短期、中期和长期的表现。不同的持有期可以揭示因子在不同市场条件下的稳定性和有效性。例如，一个因子可能在短期内表现良好，但在较长的持有期内表现不佳，或者反之亦然。

cumulative_returns 参数告诉 Alphalens 如何计算远期回报。顾名思义，如果一支股票持有了 5 天，简单回报就是用卖出日的价格，除以买入时的价格；累积回报则是用每一天的收益计算复利。

看起来我们关心的远期收益一定是简单回报。为什么还会有 cumulative_returns 这个参数呢？

这是 Alphalens 中一个有点晦涩难懂的地方。在 github 上曾经引起过开发人员之间长时间的辩论[^issue374]。简而言之，如果因子数据与价格数据不同频、或者 Alphalens 与 pyfolio 联动的时候，就需要考虑这个差异。

鉴于这个问题有点复杂，你可能只需要记住以下结论：

如果你的因子是按日频计算的，那么这个参数的取值对结果就没有影响。

get_clean_factor_and_forward_returns 函数将返回类似以下结构的 DataFrame:

![Merged Data](https://images.jieyu.ai/images/2024/07/alphalens-merged-data.jpg)

在这张表中，1D, 5D 和 10D 列分别表示 1 天、5 天和 10 天的远期收益。factor 是原始因子值，factor_quantile 则是因子分层后所在的 quantile。group 的值则是之前 factor 表中的 sector 一列的取值。

## 4. 进行因子分析与生成报表

进行因子分析的过程非常简单，我们只需要将前一个方法的输出传入给 create_full_tear_sheet 就好。

!!! hint
    我们也可以跳过 get_clean_factor_and_forward_returns，直接调用 create_full_tear_sheet 方法，只要输入格式符合要求。但是，如非必要，并不建议这样做，因为错误的输入可能得到似是而非的结果，而你无从辨别它的正确性。通过 get_clean_factor_and_forward_returns 进行数据预处理，在某种程度上，保证了数据的准确性。

如果你运行了[](#example-1)中的代码，就应该已经看到了生成的纷敏复杂的各类报表。下一章，我们就将分析这些报表。

## 5. Reference

1. [Alphalens Questions](https://quantopian-archive.netlify.app/forum/threads/alphalens-questions-thread.html)
2. [Alphalens Github](https://github.com/quantopian/alphalens)
3. [Alphalens Tutorials](https://github.com/quantopian/alphalens/tree/master/alphalens/examples)
4. [Alphalens-reloaded](https://github.com/stefan-jansen/alphalens-reloaded)
5. [Quantopian Research](https://github.com/quantopian/research_public)

## 6. Footnotes

[^Carhart]: Mark M. Carhart, 1997, The Journal of Finance, On Persistence in Mutual Fund Performance. [PDF](https://onlinelibrary.wiley.com/doi/pdfdirect/10.1111/j.1540-6261.1997.tb03808.x)
[^GICS]: Global Industry Classification Standard，由摩根士丹利资本国际公司（MSCI）和标普道琼斯指数公司共同开发，广泛应用于全球金融市场。GICS 分为四个层级：部门（Sector）、行业组（Industry Group）、行业（Industry）和子行业（Sub-Industry）。[GICS 官网地址](https://www.msci.com/our-solutions/indexes/gics)

[^ICB]: Industry Classification Benchmark，由富时罗素（FTSE Russell）发布，也是一个全球认可的行业分类标准，类似于 GICS，但有其自己的分类体系。[ICB 官网地址](https://www.lseg.com/en/ftse-russell)

[^issue374]: [Alphalens开发人员关于cumulative returns的讨论](https://github.com/quantopian/alphalens/issues/374)


[^clenow]: Andreas Clenow是一名小说家，企业家和对冲基金家和首席投资官。[Stocks on the Move: Beating the Market with Hedge Fund Momentum Strategy](https://www.amazon.com/Stocks-Move-Beating-Momentum-Strategies/dp/1511466146)是他2015年发表的一本书。这本书概述了长期投资市场的合理方式。它将引导您了解股票市场的问题以及如何解决这些问题。它将解释如何以相当低的风险实现股市两倍的回报。除此之外，他出版了一部黑色金融小说《最私人的银行》。

[^quantconnect]: QuantConnect是一个在线平台，主要面向量化交易爱好者和专业投资者，提供算法交易服务。它允许用户编写、测试、优化和执行金融市场的交易策略。Clenow的动量因子策略实现可参考[这里](https://www.quantconnect.com/forum/discussion/3136/andreas-f-clenow-momentum/p1)
