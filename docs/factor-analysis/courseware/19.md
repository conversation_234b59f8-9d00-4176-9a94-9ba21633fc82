# 定价未来  —— LightGBM价格预测模型
<br>

## 1. 策略原理

在左右资产短期价格变动的表面原因中，有两种最基本的因素在起作用。第一种是惯性，第二种是回归。量化交易的本质，是认为这两种作用力在短期内是可以计算的，并且系统的状态在被强有力的外界力量改变之前，这些作用力及其运动规律在短期内有效。

在这一章里，我们只讨论如何刻划和利用股价的惯性运动。说起惯性运动，我们最容易联想到的是牛顿第一定律和第二定律。这两个规律在股市中可以有很多运用，比如，我们可以用它来判断是否有主力运作：如果资产的价格是匀速运动，它就是没有主力运作的情况；如果资产价格是加速运动，它就是有主力运作的情况。

在这两种情况下，资产价格的走势分别能拟合成一次曲线（即直线）和二次曲线（即抛物线）。如果我们对股市进行过认真观察，就会发现这两种曲线随处可见。我们已经知道了这两种曲线的方程，因此，如果它们的规律能够得以保持，我们就能推导出下一个时间点的价格。

所以，我们可以提出这样一种策略：

对资产的均线进行多项式拟合，基于得到的曲线公式，推导出下一个时间点的均线预测值，再反推导出对应的价格。使用均线是为了过滤噪声，提高趋势预测的准确性。

这个策略看起来很简单，但是，当我们打算付诸实现时，就会发现，还有这么多情况需要考虑：

1. 我们应该使用窗口为多少的均线？
2. 多项式的最高次数为多少？我们这么说，是因为使用高阶多项式，理论上可以拟合任意的时间序列并且做到误差为零。但这是一个过拟合的情况，无法预言未来。
3. 拟合误差在多少范围内，我们才可以使用这个拟合函数去预测价格？这应该由统计模型来决定。

这些还只是数学问题。要实现这样一个策略，我们首先得了解机器学习模型的能力。万一，如果我们丢一堆数据进去，机器学习就自动把规律找出来了呢？

## 2. lightgbm能自动发现规律吗？

下面，我们就来尝试一下，如果我们直接把收盘价喂给机器学习模型，会发生什么情况。我们要预测的是价格，所以，这是一个回归问题。

下面这段代码，将随机抽取2000支个股一年的数据，将它们转换为一个特征数据集，每一行都是一个收盘价的滑动窗口数据。

```python
from numpy.lib.stride_tricks import as_strided

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 29)
universe = 2000
barss = load_bars(start, end, universe=universe)

def rolling_time_series(ts: NDArray, win: int):
    stride = ts.strides
    shape = (len(ts) - win + 1, win)
    strides = stride + stride
    return as_strided(ts, shape, strides)
    
def rolling_close(group, win, columns):
    index = group.index
    if len(group) < win:
        features = np.full((len(group), win), np.nan)
        df = pd.DataFrame(features, columns=columns, index=index)
        return df
        
    rolled = rolling_time_series(group["close"].values, win)
    padded = np.pad(
        rolled, ((win-1, 0), (0,0)), mode="constant", constant_values=np.nan)
    df = pd.DataFrame(padded, columns=columns, index=index)
    return df

win = 10
feature_cols = [f"c{win-i}" for i in range(win)]

features = barss.groupby(level="asset").apply(rolling_close, win, columns = feature_cols)
features = features.droplevel(0)
features.tail()
```

要调用lightgbm进行训练，我们还需要划分数据集，并将其转换为Dataset格式：

```python
import lightgbm as lgb
def train_test_split(data, feature_cols, cuts = (0.7,0.2)):
    train, valid, test = [], [], []
    def cut_time_series(group, cuts):
        itrain = int(len(group) * cuts[0])
        ival = itrain + int(len(group) * cuts[1])

        return (group.iloc[:itrain], group.iloc[itrain: ival], group.iloc[ival:])

    for item in data.groupby("asset").apply(cut_time_series, cuts=cuts).values:
        train.append(item[0])
        valid.append(item[1])
        test.append(item[2])

    df_train = pd.concat(train)
    df_valid = pd.concat(valid)
    df_test = pd.concat(test)

    return (df_train[feature_cols], df_valid[feature_cols], df_test[feature_cols], 
            df_train["target"], df_valid["target"], df_test["target"])

data=features
data["ret"] = barss["close"].unstack().pct_change().stack()
data["target"] = barss["close"].unstack().shift(-1).stack()
data = data.dropna(subset=["target", "ret"])
data.reset_index(inplace=True)

(X_train, X_val, X_test, 
y_train, y_val, y_test) = train_test_split(data, feature_cols)

train_data = lgb.Dataset(X_train, label=y_train)
valid_data = lgb.Dataset(X_val, label=y_val)
```

这里我们没有使用sklearn的train_test_split，因为它是随机划分数据的。在量化交易场景里，我们往往需要顺序划分数据。同时，我们需要确保是在每一个资产内部进行划分。


在特征中，包含了一些nan值，我们并没有进行任何处理；但对target中的nan进行了处理。lightgbm要求target必须为浮点、整数或者bool，不接受包括nan在类的其它任何类型。但对特征数据并没有要求，它在训练时，可以处理好这些输入。

根据我们划分的方式，如果以一年的数据为例，我们将使用其前8个月的数据进行训练，中间2个月多一点的时间进行验证，用最后一个月左右的数据进行测试。如果模型在测试中有效，就说明模型的泛化能力较强，因为训练方式保证了它会在未来的三个月中都是有效的。我们将在本章都保存与此一致的划分方式。

现在，我们就开始训练这个模型。

```python
from sklearn.metrics import mean_absolute_percentage_error

params = {
    "objective": "regression",
    "metric": "mape",
    "num_leaves": 31,
    "learning_rate": 0.05,
    "random_state": 42,
}

esr = 50
evals_result = {}

num_rounds = 200
model = lgb.train(
    params,
    train_data,
    num_boost_round=num_rounds,
    valid_sets=[valid_data],
    callbacks=[lgb.early_stopping(esr), lgb.record_evaluation(evals_result)],
)

y_pred = model.predict(X_test.values)
mean_absolute_percentage_error(y_test, y_pred)
```

注意我们在生成数据集时，先进行了随机数种子初始化。在训练模型时，又通过`random_state`，传入了另一个随机数种子。我们将在本章的其它实验中，也保持同样的随机数种子，以便结果可以相互比较。

最后，我们得到的mape值是1.8%。怎么看待这个结果呢？它对于我们投资，又意味着什么呢？既然模型预测的误差只有1.8%，那么，在一个做多的策略中，如果我们选择预测价格高于现价1.8%的股票买入，是不是就可以实现盈利了呢？

### 2.1. 适用于量化的模型评估方法

你可能也认识到，事情不会这么简单，毕竟，印钞机不会这么轻易就能制造出来。但是，我们要如何去解释这个结果呢？这一点对我们后面的实验也至关重要：在机器学习社区中广泛流传的那些评估指标，对于量化交易是否也一样适用？

接下来，我们就介绍在量化交易中，真正有效的评估指标。

```python
from matplotlib.dates import WeekdayLocator

def eval_model(model, X_test, data, long_threshold = 0.02, traces:int = 0):
    df = data.rename(columns={"c1": "prev", "target": "actual"})
    df = df.loc[X_test.index]
    
    df["pred"] = model.predict(X_test.values)
    
    error = mean_absolute_percentage_error(df["actual"], df["pred"])

    print(f"mape is {error:.3f}")
                                       
    df["pred_ret"] = df["pred"]/df["prev"] - 1

    long_df = df.query(f"pred_ret > {long_threshold}")

    print(f'actual p&l {long_df["ret"].mean():.2%}')

    if traces > 0:
        row = int(np.sqrt(traces))
        col = int(traces / row)

        if row * col < traces:
            row += 1

        symbols = long_df["asset"].unique()[:traces]
        _, axes = plt.subplots(row, col, figsize=(row * 4, col * 2))
        axes = axes.flatten()
        
        for i, symbol in enumerate(symbols):
            close = df.query(f"asset == '{symbol}'")[["prev", "date"]].set_index("date")
            x = list(close.index.strftime("%m/%d"))
            axes[i].plot(x, close, label="close")

            pred_close = df.query(f"asset == '{symbol}'")["pred"]
            axes[i].plot(x[1:], pred_close[:-1], label="pred")

            locator = WeekdayLocator()
            axes[i].xaxis.set_major_locator(locator)
            
            # mark signals
            signal_dates = df.query(f"(pred_ret > {long_threshold}) & (asset == '{symbol}')")["date"]
            x = [i.strftime("%m/%d") for i in signal_dates]
            y = close.loc[signal_dates]
            axes[i].scatter(x, y, marker='^', color='red')
            axes[i].set_title(symbol)
            axes[i].legend()

        plt.tight_layout()

eval_model(model, X_test, data, traces = 6)
```

这个函数的主要功能有这样几点：
1. 计算出mape指标
2. 计算在预测收益大于指定阈值时，以t0收盘价买入，t1收盘价卖出，得到的p&l均值。这才是我们要追求的目标。
3. 对预测收益大于指标阈值的样本，随机挑选几支进行可视化。

现在我们知道了，尽管模型预测的误差只有1.8%，但实际在投资中，即使我们选择预测收益高于现价1.8%的股票买入，还是会稳定地每次亏损0.74%左右。

### 2.2. 模型为什么不生效？

现在，我们讨论模型为什么不生效，即为什么lightgbm没能从数据中，自动学习到规律的问题。这需要用到我们之前讲过的绘制决策树的技巧。现在，我们把它的第0棵子树绘制出来：

```python
lgb.plot_tree(model, tree_index=0, figsize=(20, 20))
```

在梯度提升树中，第0号树最先被构建出来，它负责捕捉数据中最主要的走势或者模式。其它的树只是负责消化第0号树所不能解释的残差或者少数情况，不能影响大的走势、或者多数场景。

从第0号树可以看出，模型实际上主要是依据c1的值进行预测。这是什么意思呢？它的意思是，如果昨收是c1，那么我就预测明天的收盘价也是c1。由于大量的个股在多数时间内都是低波动的，比如波动在1%以内，因此，如果我们也在1%以内进行预测，这样的策略就能保证预测误差保持在较小的区间。但是，这样无法带来真正的投资机会。

既然决策树只是围绕前一天的收盘价进行预测，显然，它并不会从之前的走势上进行学习，也未能利用横截面数据。

为了更清楚地理解lightgbm在回归任务中是如何进行决策的，我们再补充一个仿真数据的例子。


```python
def generate_data():
    np.random.seed(42)
    c = np.linspace(0, 1, 100)
    p = np.random.rand(100)
    target = c * (1 + p)
    df = pd.DataFrame({'c': c, 'p': p, 'target': target})
    return df

def train_lightgbm_model(df):
    X = df[['c', 'p']]
    y = df['target']
    train_data = lgb.Dataset(X, label=y)
    params = {
       'objective':'regression',
       'metric': 'rmse',
       'num_leaves': 31,
       'learning_rate': 0.05,
       'feature_fraction': 0.9,
       'verbosity': -1
    }
    model = lgb.train(params, train_data, num_boost_round=2)
    return model

df = generate_data()

model = train_lightgbm_model(df)

c = np.linspace(0, 100, 10)
p = np.linspace(1, 1, 10)
test_data = pd.DataFrame({
    "c": c,
    "p": p
})

y_pred = model.predict(test_data)

print(y_pred)
```

在generate_data函数中，我们生成了一个符合 $y = c \times (1 +p)$的数据集。它的取值区间是[0, 1.68]。我们在该数据集上，训练了两轮（这样我们将只生成两棵树），然后，我们在一个更广阔的区间（0，200）中去预测它。

这个函数有两个参数，c和p。为了更容易理解模型的行为，我们保持p不变，c从0线性增长到100。这样，函数值的取值区间就是[0,200]。

从代码运行的结果中我们看到，模型预测只输出了两个不同的值，第一个值是0.681，第二个到以后都是0.7914。为什么会是这样？因为从第二个元素起，特征c的取值就超过了我们训练时c的取值[0,1]。

lightgbm具体是如何计算出0.7914的呢？如果我们把两棵子树都绘制出来，就很容易看到这一点：

```python
lgb.plot_tree(model, tree_index=0, figsize=(6,6))
lgb.plot_tree(model, tree_index=1, figsize=(6,6))
```

现在，请你拿着第二个预测数据点的值 c = 11.11和p = 1，遍历一下这两棵子树，你会发现，最终在两棵子树中，我们都会走到最右下方的子节点，它们的和，正好就是模型预测出来的值0.7914(注意有舍入误差)。这就是梯度提升决策树做回归任务的秘密！

所以，简而言之，梯度提升决策树不能自动学习规律（神经网络则有可能），它是一个分段函数。但如果我们能告诉它在什么区间、该运用什么规律，那么，它就能出色地完成任务！

这是本章最重要的一个结论。我们该如何运用这个结论呢？

## 3. lightgbm base model

在这一章的最后，我们将提出一个真正有投资价值的价格预测模型，在测试中，它每一次交易能得到0.25%的收益率。如果保持这个收益率，按一年250天计算，这个模型能带来80%的收益率。这无疑是非常有价值的一个模型。

但是，掌握它的原理更为重要。因为在复杂的外部环境中，最终要靠你自己去实现投资策略，并响应各种挑战。因此，我们将循序渐近地提出三个模型。通过这三次迭代，你应该能完全掌握通过梯度提升决策树构建价格预测模型的全部技巧。

为了便于理解每次迭代的重点，我们需要把三个模型的公共部分提取出来，作为一个基础模型。

```python
import datetime
import logging
from numpy import ndarray
from numpy.typing import ArrayLike, NDArray
import os
import pickle
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import mean_absolute_percentage_error
from numpy.lib.stride_tricks import as_strided
import lightgbm as lgb
from matplotlib.dates import WeekdayLocator


logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)
mlogger = logging.getLogger("matplotlib")
# supress matplotlib info level logging, which is anoying
mlogger.setLevel(logging.WARNING)


class BaseLGBModel:
    def __init__(self):
        self._model_ = None
        self._name_: str | None = None
        self._desc_: str | None = None

        # for encode/decode categorical features
        self._label_encoders_ = {}

    @property
    def model(self):
        return self._model_

    def save(self, path: str, auto_rename: bool = True, name: str | None = None):
        """将模型保存到指定路径。

        如name未指定，将使用模型名作为文件名。如果同名文件已存在，将自动重命名为当前时间戳。文件名会自动加pkl后缀
        Args:
            path: 保存路径
            auto_rename: 如果同名文件已存在，是否自动重命名
            name: 保存文件名
        """
        if self._model_ is None:
            raise ValueError("请先调用fit方法训练模型")

        if not os.path.exists(os.path.expanduser(path)):
            raise ValueError("保存路径不存在")

        name = name or self._name_
        if name is None:
            raise ValueError("请指定保存文件名")

        file = os.path.join(path, f"{name}.pkl")
        if os.path.exists(file):
            if auto_rename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"{name}_{timestamp}.pkl"
                file = os.path.join(path, name)
                assert not os.path.exists(file)
                logger.warning("保存路径已存在同名文件，已自动重命名为%s", name)
            else:
                raise ValueError("保存路径已存在同名文件，请指定指定文件名")

        with open(file, "wb") as f:
            pickle.dump(
                {
                    "model": self._model_,
                    "name": self._name_,
                    "desc": self._desc_,
                    "save_time": datetime.datetime.now(),
                },
                f,
            )

    def load(self, file_path: str):
        with open(file_path, "rb") as f:
            data = pickle.load(f)
            self._model_ = data["model"]
            self._name_ = data["name"]
            self._desc_ = data["desc"]
            logger.info("加载模型%s成功。", self._name_)

    def rolling_time_series(self, ts: NDArray, win: int) -> NDArray:
        """生成rolling time series

        Args:
            ts: 一维时间序列
            win: 窗口大小
        Returns:
            返回shape为（len(ts) - win + 1, win）的二维numpy数组
        """
        stride = ts.strides
        shape = (len(ts) - win + 1, win)
        strides = stride + stride
        return as_strided(ts, shape, strides)

    def encode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """将类别特征编码。

        类别特征可能需要编码解码。此时需要label_encoders属性保存编码器。此方法会自动保存编码器到label_encoders属性中。
        """
        if name in self._label_encoders_:
            logger.info("已经存在%s特征编码器，直接复用")
            return self._label_encoders_[name].transform(feature)

        self._label_encoders_[name] = LabelEncoder()
        return self._label_encoders_[name].fit_transform(feature)

    def decode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """解码经类别编码器编码的序列"""
        if name not in self._label_encoders_:
            raise ValueError(f"不存在{name}特征编码器，无法进行解码")

        return self._label_encoders_[name].inverse_transform(feature)

    def train_test_split(
        self,
        data: pd.DataFrame,
        features: List[str],
        group_id: str | None = None,
        cuts=(0.7, 0.2),
    ) -> Tuple[pd.DataFrame | pd.Series]:
        """对时间序列进行train, valid和test子集划分。

        与sklearn的train_test_split不同，此方法会根据时间进行划分，而不是随机划分。

        请事前对data进行排序。如果group_id为None，则直接对data进行划分；否则，先按group_id进行分组，
        再在组内进行划分，最后合并成与data同样shape的DataFrame

        Args:
            data: 时间序列数据，应包含features中的所有列，以及名为target的列
            group_id: 根据此列进行分组，在组内划分子集
            features: 在data中，哪些列是特征列
            cuts: 训练集、验证集、测试集的比例

        Returns:
            返回X_train, X_valid, X_test, y_train, y_valid, y_test
        """
        logger.info("trained with features: %s", features)

        def cut_time_series(group, cuts):
            itrain = int(len(group) * cuts[0])
            ival = itrain + int(len(group) * cuts[1])

            return (group.iloc[:itrain], group.iloc[itrain:ival], group.iloc[ival:])

        if group_id is None:
            train, valid, test = cut_time_series(data, cuts)
            return (
                train[features],
                valid[features],
                test[features],
                train["target"],
                valid["target"],
                test["target"],
            )

        # 按group_id进行分组，在组内划分，最后合并成与data同样shape的DataFrame
        train, valid, test = [], [], []
        for item in data.groupby(level=group_id).apply(cut_time_series, cuts=cuts):
            train.append(item[0])
            valid.append(item[1])
            test.append(item[2])

        train = pd.concat(train)
        valid = pd.concat(valid)
        test = pd.concat(test)

        return (
            train[features],
            valid[features],
            test[features],
            train["target"],
            valid["target"],
            test["target"],
        )

    def set_train_data(self, barss: pd.DataFrame, **kwargs):
        pass

    def train(
        self,
        train_data: lgb.Dataset,
        valid_data: lgb.Dataset,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
    ):
        if not "objective" in train_params:
            raise ValueError(
                "请提供objective参数: regression, binary, or multi-class classification."
            )

        params = {"random_state": 42}

        objective = train_params["objective"]
        if objective == "regression" and "metric" not in train_params:
            train_params["metric"] = "l2"

        if objective == "binary" and "metric" not in train_params:
            train_params["metric"] = "binary_logloss"

        if objective == "multiclass" and "metric" not in train_params:
            train_params["metric"] = "multi_logloss"

        if "feval" in train_params:
            feval = train_params["feval"]
            del train_params["feval"]
        else:
            feval = None

        params.update(train_params)
        evals_result = {}

        if self._model_ is not None:
            logger.warning("Model already trained, retraining...")

        self._model_ = lgb.train(
            params,
            train_data,
            num_boost_round=epochs,
            valid_sets=[valid_data],
            feval=feval,
            categorical_feature=list(self._label_encoders_.keys()),
            callbacks=[
                lgb.early_stopping(early_stop_round),
                lgb.record_evaluation(evals_result),
            ],
        )

        return self.model

    def predict(self, X: ArrayLike) -> ndarray:
        assert self._model_ is not None, "Model not trained"
        return self._model_.predict(X)

    def calc_returns(
        self, X_test, y_test, data: pd.DataFrame
    ) -> Tuple[float, pd.DataFrame]:
        """计算预测收益和对应的实际收益率。在eval_model方法中需要这些数据。

            子类一般应该重写此方法。
        Args:
            X_test: 测试集特征
            y_test: 对应于X_test的直值
            data: 原始数据，train_data, X_test, y_test等数据应该直接来源于此数据集，且索引没有任
            何修改。
        Returns:
            返回绝对平均百分比误差(mape)和包含了 true_close, pred_close, true_returns,
            pred_returns, asset，并以X_test的索引为索引的DataFrame
        """
        raise NotImplementedError("子类必须实现此方法")

    def eval_model(
        self,
        X_test,
        y_true,
        data: pd.DataFrame,
        rng=[0.02, 0.05],
        trace: int = 0,
        filter_quantiles: Tuple = (0.05, 0.95),
    ):
        """对模型进行评估

        这里的评估方法更适用于量化。它的原理是，只检验我们感兴趣的信号，看它们的预测结果的准确率，而忽略
        大多数噪声。

        Args:
            X_test: 测试集特征
            y_true: 对应于X_test的直值
            data: 原始数据，train_data, X_test, y_true等数据应该直接来源于此数据集，且索引没有任何修改。
            rng: 关注的预测收益范围，比如[0.02, 0.05]表示我们关注预测收益在2%到5%的范围内。
            show_trace: 是否显示预测结果和真实值的散点图
            filter_quantiles: 过滤掉离群值的范围，比如(0.05,0.95)表示过滤掉前5%和后5%的离群值
        """
        mape, df = self.calc_returns(X_test, y_true, data)
        df["close"] = data["close"].loc[X_test.index]

        # 过滤掉离群值。在训练中，它们的数据较少
        ql, qh = filter_quantiles
        low, high = df["true_close"].quantile(ql), df["true_close"].quantile(qh)
        logger.info("真值不在[%s,%s]区间的个股已被过滤", round(low, 2), round(high, 2))

        valid = df.query(f"(true_close <= {high}) & (true_close >= {low})")

        concerned = valid.query(f"(pred_returns>={rng[0]})&(pred_returns<={rng[1]})")

        plt.scatter(concerned["pred_close"], concerned["true_close"], s=1)

        # qq-plot参考线
        xmin, xmax = min(concerned["pred_close"]), max(concerned["pred_close"])
        plt.plot([xmin, xmax], [xmin, xmax], "r-")

        plt.xlabel("pred close")
        plt.ylabel("actual close")
        plt.title(
            f"Actual P&L: {concerned['true_returns'].mean():.2%}, mape = {mape:.2%}, Pred Ret: {rng[0]:.1%}-{rng[1]:.1%}, "
        )

        if trace == 0:
            return

        if trace == -1:
            trace = len(concerned)

        traces = min(trace, len(concerned))
        row = int(np.sqrt(traces))
        col = traces // row
        if row * col < traces:
            row += 1

        symbols = concerned.index.get_level_values("asset").unique()[:traces]
        _, axes = plt.subplots(row, col, figsize=(col * 4, row * 4))
        axes = axes.flatten()

        for i, symbol in enumerate(symbols):
            close = df.xs(symbol, level="asset")["close"]
            x = list(close.index.strftime("%m/%d"))
            axes[i].plot(x, close, label="close", linewidth=2)

            # 每一个pred_close,实际上对应的是次日日期
            pred_close = df.xs(symbol, level="asset")["pred_close"]
            axes[i].plot(x[1:], pred_close[:-1], label="pred_close", linewidth=1)

            locator = WeekdayLocator()
            axes[i].xaxis.set_major_locator(locator)

            # mark signals
            signal_dates = concerned.xs(symbol, level="asset").index
            x = list(signal_dates.strftime("%m/%d"))
            y = close.loc[signal_dates]
            axes[i].scatter(x, y, marker="^", color="red")
            axes[i].set_title(symbol)
            axes[i].legend()

        plt.tight_layout()
        return concerned
```

基础模型的主要功能有：

1. 把时序滚动数据生成功能、模型评估、数据集划分等功能封装到类中。
2. 新增了模型加载、保存功能，提供了类别编码解码功能。
3. 生成了一个框架，子类可以通过定制set_train_data提供数据、生成特征；通过calc_returns来为eval_model提供数据。

## 4. 基础均线模型

```python
class MARegressionModel(BaseLGBModel):
    def __init__(self):
        super().__init__()
        self._cache_data_ = None
        self._features_ = []

    @property
    def data(self):
        return self._cache_data_

    @property
    def features(self):
        return self._features_

    def train(self, epochs: int = 100):
        params = {
            "metric": "mape",
            "objective": "regression"
        }

        X_train, X_val, X_test, y_train, y_val, y_test = self.train_test_split(
            self.data, self.features,group_id="asset"
        )

        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val)
        super().train(train_data, val_data, epochs, train_params=params)

        self.eval_model(X_test, y_test, self.data, trace=6)

    def set_train_data(self, barss: pd.DataFrame):
        features = [
            (
                barss["close"]
                .unstack()
                .ffill()
                .rolling(win)
                .mean()
                .stack()
                .rename(f"ma{win}")
            )
            for win in (5, 10, 20)
        ]

        features = pd.concat(features, axis=1)
        self._features_ = features.columns.tolist()
        features["target"] = barss["close"].unstack().shift(-1).stack()
        features["close"] = barss["close"]
        self._cache_data_ = features.dropna(subset=["target"])

    def calc_returns(
        self, X_test, y_test, data: pd.DataFrame
    ) -> Tuple[float, pd.DataFrame]:
        y_pred = self.predict(X_test)
        index = X_test.index

        mape = mean_absolute_percentage_error(y_test, y_pred)

        df = data.loc[index]
        prev = df["close"]

        pred_returns = y_pred / prev - 1
        true_returns = y_test / prev - 1

        return mape, pd.DataFrame(
            {
                "true_close": y_test,
                "pred_close": y_pred,
                "true_returns": true_returns,
                "pred_returns": pred_returns,
            },
            index=index,
        )

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
universe = 2000

barss = load_bars(start, end, universe)
mar = MARegressionModel()
mar.set_train_data(barss)
mar.train()
```

与纯粹的价格序列相比，如果我们选择买入预测上涨2%的个股，最终每次投资的实际收益率将会是0.07%，也即它的年化收益为19%左右。在训练中，我们只使用了5，10，20日均线，从理论上讲，lightgbm并不能从中学习到规律。但是，这里也有例外：均线本身是有支撑作用的，因此，在一些时候，最终股价会锚定在均线上，这样就提升了预测的精度。因此，当预测精度得到保证之后，再选择预测上涨的个股买入，得到正收益也并不奇怪了。

我们可以通过下面的代码绘制出决策树，清楚地看到这一点：

```python
lgb.plot_tree(mar.model, tree_index=0, figsize=(15,10))
```

模型几乎是拿5日均线价格当成了预测价格。但是，除此之外，模型并不能学习到别的规律。

下面，我们再实现一个新的模型。

## 5. 加入一点小规律

<!-- 
均线与价格之间的线性变换
如果均线具有稳定性，则价格会向均线回归

首先讲使用纯价格，介绍为何不work
然后讲basemodel和ma regression,结论：跟前一次一样，但仍然不能学习到规律
3，讲v1 model，有所改进。更重要的是，当预测收益大于2.6%时，绝对平均百分比误差低于预测，此时已经有利可图了。我们修改train的参数，设置rng = [0.026, 0.05]，发现此时收益已经达到了0.01%
-->


```python
import datetime
import logging
from numpy._typing import NDArray
from numpy.polynomial.polynomial import polyfit, polyval
import lightgbm as lgb

logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)


class PriceSeerV1(BaseLGBModel):
    def __init__(self):
        super().__init__()
        self._cache_data_ = None
        self._features_ = []

    @property
    def data(self):
        return self._cache_data_

    @property
    def features(self):
        return self._features_

    def extract_feature_ma(self, barss, wins=None):
        wins = wins or [5, 10, 20]
        df = pd.concat(
            [
                (
                    barss["close"]
                    .unstack()
                    .rolling(win)
                    .mean()
                    .stack()
                    .rename(f"ma{win}")
                )
                for win in wins
            ],
            axis=1,
        )

        df.columns = [f"ma{win}" for win in wins]
        return df

    def rolling_polyfit(self, group, win, icol, columns: List[str]):
        index = group.index.get_level_values(0)

        ts = group.iloc[:, icol].to_numpy()
        if len(ts) < win:
            features = np.full((len(ts), 3), np.nan)
            return pd.DataFrame(features, columns=columns, index=index)

        transformed = self.rolling_time_series(ts, win)

        coeff = polyfit(np.arange(win), transformed.T, deg=2)

        ts_hat = polyval(np.arange(win), coeff)
        residuals = transformed - ts_hat
        errors = np.mean(np.abs(residuals / transformed), axis=1)
        features = np.stack([*coeff[1:], errors], axis=1)

        pad_len = len(ts) - len(coeff[0])

        padded = np.pad(
            features, ((pad_len, 0), (0, 0)), mode="constant", constant_values=np.nan
        )

        return pd.DataFrame(padded, index=index, columns=columns)

    def extract_feature_poly(self, ma_features, wins=None):
        wins = wins or [5, 10, 20]
        poly_features = []

        for i, win in enumerate(wins):
            columns = [f"poly_c1_{win}", f"poly_c2_{win}", f"poly_error_{win}"]
            poly_feature = ma_features.groupby(level="asset").apply(
                self.rolling_polyfit, win=win, icol=i, columns=columns
            )
            poly_features.append(poly_feature)

        # make sure the index is date, asset
        return pd.concat(poly_features, axis=1).swaplevel()

    def set_train_data(
        self, barss: pd.DataFrame, ma_wins=(5, 10, 20), poly_wins=(5, 10, 20)
    ):
        mas = self.extract_feature_ma(barss, ma_wins)
        poly_features = self.extract_feature_poly(mas, poly_wins)
        features = pd.concat((mas, poly_features), axis=1)

        self._features_ = features.columns.tolist()

        features["target"] = barss["close"].unstack().shift(-1).stack()
        features["close"] = barss["close"]
        features["asset"] = features.index.get_level_values("asset")

        self._cache_data_ = features.dropna(subset=["target"])

    def train(
        self,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
        **eval_params,
    ):
        params = {"objective": "regression", "metric": "mape"}
        params.update(train_params or {})

        X_train, X_val, X_test, y_train, y_val, y_test = self.train_test_split(
            self.data, self.features,group_id="asset"
        )

        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val)
        super().train(
            train_data,
            val_data,
            epochs,
            early_stop_round=early_stop_round,
            train_params=params,
        )

        self.eval_model(X_test, y_test, self.data, **eval_params)

    def calc_returns(
        self, X_test, y_test, data: pd.DataFrame
    ) -> Tuple[float, pd.DataFrame]:
        y_pred = self.predict(X_test)
        index = X_test.index

        mape = mean_absolute_percentage_error(y_test, y_pred)

        df = data.loc[index]
        prev = df["close"]

        pred_returns = y_pred / prev - 1
        true_returns = y_test / prev - 1

        return mape, pd.DataFrame(
            {
                "true_close": y_test,
                "pred_close": y_pred,
                "true_returns": true_returns,
                "pred_returns": pred_returns,
            },
            index=index,
        )

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
universe = 2000

barss = load_bars(start, end, universe)

seer = PriceSeerV1()
seer.set_train_data(barss)
seer.train(100, trace=6)
```

在这一版中，我们对曲线进行了拟合，把拟合曲线的一次项、二次项和残差项都当成了特征。运行结果有点小意外，收益率居然变为了负数。要如何解释这个结果呢？

原因在于，这个模型预测的误差变大了。上一版的mape是2.48%，这一次的变成了3.44%。预测误差变大，导致收益率下降。

我们把第0号树绘制出来：

```python
lgb.plot_tree(seer.model, tree_index=0, figsize=(30,20))
```

我们看到，它仍然主要在围绕5日均线进行决策，但稍稍加上了一些拟合特征。但是，拟合特征与均线数据之间有量纲差异，所以，对预测结果的贡献并不大。另外，从原理上讲，lightgbm也无法从拟合特征中，推导出价格来。这件事我们得自己来做，让lightgbm只操心是否该用推导出来的价格。



## 6. 最终版本

第一个版本中，存在这样的问题：

首先，我们使用的特征与target实际上没有太强的关联度；另外，存在量纲的问题。我们在前面的课程中讲过，梯度提升决策树不要求标准化，可以接受不同量纲的特征。但是，事无绝对。我们看到，在回归任务中，如果特征之间存在量纲差距，这会影响到它们之间的权重；特征内部之间如果存在量纲差距，也会影响最终的结果。

所以，在这一版，我们进行以下改进：

1. 在特征提取时，直接推导出下一个时间点的价格，同时给出误差。误差项引导lightgbm决定是否使用这个价格。
2. 使用涨跌幅作为特征和目标变量，而不是使用绝对价格。这样一下，不仅消除了量纲问题，也大大压缩了特征空间。

当然，如此一来，我们在计算mape时，也要使用mean_absolute_error，而不是mean_absolute_percentage_error。

```python
from sklearn.metrics import mean_absolute_error


class PriceSeerV2(BaseLGBModel):
    def __init__(self):
        super().__init__()
        self._cache_data_ = None
        self._features_ = []

    @property
    def data(self):
        return self._cache_data_

    @property
    def features(self):
        return self._features_

    def extract_feature_ma(self, barss, wins=None):
        wins = wins or [5, 10, 20]
        df = pd.concat(
            [
                (
                    barss["close"]
                    .unstack()
                    .rolling(win)
                    .mean()
                    .stack()
                    .rename(f"ma{win}")
                )
                for win in wins
            ],
            axis=1,
        )

        df.columns = [f"ma{win}" for win in wins]
        return df

    def rolling_polyfit(
        self, group, win: int, icol, columns: List[str]
    ) -> pd.DataFrame:
        index = group.index.get_level_values(0)

        ts = group.iloc[:, icol].to_numpy()

        if len(ts) < win:
            features = np.full((len(ts), 2), np.nan)
            return pd.DataFrame(features, columns=columns, index=index)

        transformed = self.rolling_time_series(ts, win)
        coeff = polyfit(np.arange(win), transformed.T, deg=2)

        transformed_hat = polyval(np.arange(win + 1), coeff)
        pred = transformed_hat[:, -1]
        ret = pred / ts[win - 1 :] - 1

        ts_hat = transformed_hat[:, -2]
        errors = ts[win - 1 :] - ts_hat
        features = np.stack([ret, errors], axis=1)

        pad_len = win - 1

        padded = np.pad(
            features, ((pad_len, 0), (0, 0)), mode="constant", constant_values=np.nan
        )

        return pd.DataFrame(padded, index=index, columns=columns)

    def extract_feature_poly(self, ma_features, wins=None):
        wins = wins or [5, 10, 20]
        poly_features = []

        for i, win in enumerate(wins):
            columns = [f"poly_ret_{win}", f"poly_error_{win}"]
            poly_feature = ma_features.groupby(level="asset").apply(
                self.rolling_polyfit, win=win, icol=i, columns=columns
            )
            poly_features.append(poly_feature)

        # make sure the index is date, asset
        return pd.concat(poly_features, axis=1).swaplevel()

    def set_train_data(
        self, barss: pd.DataFrame, ma_wins=(5, 10, 20), poly_wins=(5, 10, 20)
    ):
        mas = self.extract_feature_ma(barss, ma_wins)
        features = self.extract_feature_poly(mas, poly_wins)

        self._features_ = features.columns.tolist()

        features["target"] = barss["close"].unstack().pct_change().stack()
        features["close"] = barss["close"]
        features["asset"] = features.index.get_level_values("asset")

        self._cache_data_ = features.dropna(subset=["target"])

    def train(
        self,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
        **eval_params,
    ):
        params = {"objective": "regression", "metric": "mape"}
        params.update(train_params or {})

        X_train, X_val, X_test, y_train, y_val, y_test = self.train_test_split(
            self.data, self.features,group_id="asset"
        )

        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val)
        super().train(
            train_data,
            val_data,
            epochs,
            early_stop_round=early_stop_round,
            train_params=params,
        )

        self.eval_model(X_test, y_test, self.data, **eval_params)

    def calc_returns(
        self, X_test, y_test, data: pd.DataFrame
    ) -> Tuple[float, pd.DataFrame]:
        y_pred = self.predict(X_test)
        index = X_test.index

        mape = mean_absolute_error(y_test, y_pred)

        df = data.loc[index]
        prev = df["close"]

        return mape, pd.DataFrame(
            {
                "true_close": (1 + y_test) * prev,
                "pred_close": (1 + y_pred) * prev,
                "true_returns": y_test,
                "pred_returns": y_pred,
            },
            index=index,
        )


start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
universe = 2000

barss = load_bars(start, end, universe)

seer = PriceSeerV2()
seer.set_train_data(barss)
seer.train(500,trace=6)
```

如果你注意比较几个模型中，输出的QQ-plot图，会发现在这一版中，绝大多数点都处在对角线之上，即当预测值为$\hat{c}$时，实际第二天的收盘价要比这个更高一点。这个特征现在更加明显了。

最后，我们来看一下模型的决策树：

```python
lgb.plot_tree(seer.model, tree_index=0, figsize=(30,20))
```

我们看到，收益主要由5日均线拟合收益决定，但误差更早地参与了决策。


## 7. 拓展阅读

如果你学习课程的目标是为了求职，你可以挑战下简街的这个[竞赛](https://www.kaggle.com/competitions/jane-street-market-prediction)。这个竞赛虽然已经结束，但是，如果你能拿到一个超过leaderboard的分数，对求职会非常有帮助。在学习完这一课后，你应该完全理解了lightgbm做回归任务的原理，完全有能力在leaderboard上beat。


<!--


twosigma contest: https://github.com/jangarong/StockMarketPredictions/tree/master, top 12
-->

