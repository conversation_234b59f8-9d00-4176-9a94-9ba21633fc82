# ALPHA101 因子

<!--

我们在前面的课程中介绍了如何实现因子检验，如何判断因子的好坏。有了这些工具，是时候来实现一些因子了。在所有因子中，最有名的当然是 size, momentum, and value..。这些是学术上给出的因子，它们将会在很长时间内都有效。除此之外，我们还要如何开拓自己的因子库呢？

今天，我们就通过学习 Alpha101，来回答这个问题。

-->

2015 年，Zura  Kahushadze[^zura] 发布了文章 [《101 Formulaic Alphas》](https://arxiv.org/pdf/1601.00991)，它包含了 101 种不同的股票选择因子。

这些因子中，有 80%是当时正在 World Quant 交易中使用的因子。多数因子是短线因子（持有期 0.6~6.4 天），互相关性在 15.9%左右，基本上可以认为是提供了 101 个独立的因子。

<!--
The Sail

A lonely sail seeming white,
In misty haze mid blue sea,
Be foreign gale seeking might?
Why home bays did it flee?

The sail's bending mast is creaking,
The wind and waves blast ahead,
It isn't happiness it's seeking,
Nor is it happiness it's fled!

Beneath are running azure streams,
Above are shining golden beams,
But wishing storms the sail seems,
As if in storms is peace it deems.

-->

该报告发表之后，在业界引起较大反响。目前，根据 Alpha101 生成的因子库，已几乎成为各量化平台、数据提供商和量化机构的必备。

此外，一些机构受此启发，还在此基础上构建了更多因子，比如国泰君安推出的 Alpha 191[^alpha191] 等。这两个因子库都有机构进行了实现。比如 [DolphinDB](https://github.com/dolphindb/DolphinDBModules/tree/master/gtja191Alpha) 和 [聚宽](https://www.joinquant.com/help/api/help#Alpha101:WorldQuant101Alphas%E5%9B%A0%E5%AD%90%E5%87%BD%E6%95%B0%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E) 都提供了这两个因子库。

<!-- 聚宽中这两个因子库是付费的。DolphinDB 本身也是付费的，但它把部分 module 开源了，尚不清楚这些源码能否脱离其数据库运行-->

下面，我们就介绍如何自行实现 Alpha101 因子库，通过实现部分 Alpha101 因子，你将掌握构建因子的一般方法、需要的基础数据结构和编程技巧和最常用的统计学概念和算法实现。这里介绍的方法对 Alpha 191 也同样适用。

## Alpha 101 因子中的数据和算子

在实现 Alpha101 因子之前，我们首先要理解其公式中使用的数据和基础算子。

Alpha101 因子主要是基于价格和成交量构建，只有少部分 Alpha 中使用了基本面数据，包括市值数据和行业分类数据 [^fundmental_data]。

!!! tip
    在 A 股市场，由于财报数据的可信度问题 [^fraut]，由于缺乏 T+0 和卖空等交易机制，短期内由交易行为产生的价格失效现象非常常见。因此，短期量价因子在现阶段的有效性高于基本面因子。

在价量数据中，Alpha101 依赖的最原始数据是 OHLC, volume（成交额）, amount（成交量），turnover（换手率），并在此基础上，计算出来 returns（每日涨跌幅）和 vwap（加权平均成交价格）。

returns 和 vwap 的计算方法如下：

<Example id=vwap/>

```bash
# THE NUMPY WAY
vwap = bars["amount"] / bars["volume"] / 100
returns = bars["close"][1:]/bars["close"][:-1] - 1

# THE PANDAS WAY
vwap = df.amount / df.volume / 100
returns = df.close.pct_change()
```

除此之外，要理解 Alpha101，重要的是理解它的公用算子。在 Alpha101 中，总共有约 30 个左右的算子，其中有一些像 abs, log, sign, min, max 以及数学运算符（+, -, *, /）等都是无须解释的。

下面，我们就先逐一解释需要说明的算子。

### 三目运算符

三目运算符是 Python 中没有，但存在于 C 编程语言的一个算子。这个运算符可以表示为："x ? y : z"，它相当于 Python 中的：

<Example id=ternary/>

```bash
expr_result = None

if x:
    expr_result = y
else:
    expr_result = z
```

### rank
在 Alpha101 中，存在两个 rank，一个是横截面上的，即对同一时间点上 universe 中所有的股票进行排序；另一个是时间序列上的，即对同一股票在时间序列上的排序。

在横截面上的 rank 直接调用 DataFrame 的 rank。比如，

<Example id=rank/>

```python
import pandas as pd

data = {
    'asset': ["000001", "000002", "000004", "000005", "000006"],
    'factor': [85, 92, 78, 92, 88],
    'date': [0] * 5
}
df = pd.DataFrame(data).pivot(index="date", columns="asset", values="factor")

def rank(df):
    return df.rank(axis=1, pct=True, method='min')


rank(df)
```

<!-- 这段代码我们也可以使用 bottlenect 中的 rank_data 来实现
import bottleneck as bn

# 示例数据
data = [85, 92, 78, 92, 88]

# 计算排名
ranked_data = bn.rankdata(data)/len(data)

print(ranked_data)

-->
在上面这段代码中，date 为索引，列名字为各 asset，factor 为其值，此时，我们就可以通 rank(axis=1) 的方法，对各 asset 的 factor 值在截面上进行排序。当我们使用 axis=1 参数时，索引是不参与排序。pct 为 True 表示返回的是百分比排名，False 表示返回的是排名。

有时候我们也需要在时间序列上进行排序，在 Alpha101 中，这种排序被定义为 ts_rank，通过前缀 ts_来与截面上的 rank 相区分。此后，当我们看到 ts_前缀时，也应该作同样理解。

<Example id=ts_rank/>

```python
from bottleneck import move_rank

def ts_rank(df, window=10, min_count=1):
    return move_rank(df, window, axis=0, min_count=min_count)
```

在这里我们使用的是 bottleneck 中的 move_rank，它的速度要显著高于 pandas 和 scipy 中的同类实现。如果使用 pandas 来实现，代码如下：

<Example id=rolling_rank/>

```python
def rolling_rank(na):
    return rankdata(na,method='min')[-1]

def ts_rank(df, window=10):
    return df.rolling(window).apply(rolling_rank)
```

注意第 3 行中的 [-1] 是必须的。

rank 和 ts_rank 的使用在 alpha004 因子中的应用最为典型。这个因子是：

<Example id=alpha004/>

```python
# ALPHA#4	 (-1 * TS_RANK(RANK(LOW), 9))
def alpha004(low):
    return -1 * ts_rank(rank(low), 9)
```

在这里，参数 low 是一个以 asset 为列、日期为索引，当日最低价为值的 dataframe，是一个宽表。下面，我们看一下对参数 low 依次调用 rank 和 ts_rank 的结果。通过深入几个示例之后，我们就很快能够明白 Alpha101 的因子计算过程。

<Example id=alpha004-example/>

```python
from bottleneck import move_rank
from IPython.core.interactiveshell import InteractiveShell
InteractiveShell.ast_node_interactivity = "all"

df = pd.DataFrame(
       [[6.18, 19.36, 33.13, 14.23,  6.8 ,  6.34],
       [6.55, 20.36, 32.69, 14.52,  6.4,  6.44 ],
       [7.  , 20.79, 32.51, 14.56,  6.0 ,  6.54],
       [7.06, 21.35, 33.13, 14.47,  6.5,  6.64],
       [7.03, 21.56, 33.6 , 14.6 ,  6.5,  6.44]], 
       columns=['000001', '000002', '000063', '000066', '000069', '000100'], 
       index=['2022-01-04', '2022-01-05', '2022-01-06', '2022-01-07', '2022-01-10'])

def rank(df):
    return df.rank(axis=1, pct=True, method='min')

def ts_rank(df, window=10, min_count=1):
    return move_rank(df, window, axis=0, min_count=min_count)

df
rank(df)

-1 * ts_rank(rank(df), 3)
```

示例 [](#example-7) 将依次输出三个 DataFrame。我们看到，rank 是执行在行上，它将各股票按最低价进行排序；ts_rank 执行在列上，对各股票在横截面上的排序再进行排序，反应出最低位置的变化。

比如，000100 这支股票，在 2022 年 1 月 4 日，横截面的排位在 33%分位，到了 1 月 10 日，它在横截面上的排位下降到 16.7%。通过 ts_rank 之后，最终它在 1 月 10 日的因子值为 1，反应了它在横截面上排位下降的事实。同理，000001 这支股票，在 1 月 4 日，它的横截面上的排位是 16.7%（最低），而在 1 月 5 日，它的排序上升到 50%，最终它在当日的因子值为-1，反应了它在横截面排序上升的事实。

!!! tip
    通过 Alpha004 因子，我们不仅了解到 rank 与 ts_rank 的用法，也知道了横截面算子与时序算子的区别。此外，我们也了解到，为了方便计算 alpha101 因子，最佳的数据组织方式可能是将基础数据（比如 OHLC）都组织成一个个以日期为索引、asset 为列的宽表，以方便在两个方向上（横截面和时序）的计算。

### ts_*
这一组算子中，除了之前已经介绍过的 ts_rank 之外，还有 ts_max, ts_argmax, ts_argmin, ts_min。这一些算子都有两个参数，首先时时间序列，比如 close 或者 open，然后是滑动窗口的长度。

注意这一类算子一定是在滑动窗口上进行的，只有这样，才不会引入未来数据。

除此之外，其它常用统计函数，比如 min, max, sum, product, stddev 等，尽管没有使用 ts_前缀，只要它们出现时，带了时间参数，此时它们也是时序算子，而不是截面算子。考虑到我们已经通过 ts_rank 详细介绍了时序算子的用法，而这些算子的作用大家也都非常熟悉，这里就从略。

### delay

在 Alpha101 中，delay 算子用来获取 n 天前的数据。比如，

<Example id=delay/>

```python
def delay(df, n):
    return df.shift(n)

data = {
    'date': pd.date_range(start='2023-01-01', periods=10),
    'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109]
}
df = pd.DataFrame(data)

delay(df, 5)
```

如此一来，我们在计算第 5 天的因子时，使用的 close 数据就是 5 天前的，即原来索引为 0 处的 close。

### correlation 和 covariance

correlation 就是两个时间序列在滑动窗口上的皮尔逊相关系数，这个算子可以实现为：

<Example id=correlation/>

```python
def correlation(x, y, window=10):
    return (x.rolling(window)
             .corr(y)
             .fillna(0)
             .replace([np.inf, -np.inf], 0))

def covariance(x, y, window=10):
    return x.rolling(window).cov(y)
```

注意在这里，尽管我们只对 x 调用了 rolling，但在计算相关系数时，经验证，y 也是按同样的窗口进行滑动的。

<!--练习：

比较：

```python
x = pd.Series(np.arange(10))
y = pd.Series([1,2,3,4,5, 4, 3, 2, 1, 0])

x.rolling(5).corr(y)
```

和

```python
for i in range(5, 10):
    xi = x.iloc[i-5:i].values
    yi = y.iloc[i-5:i].values
    print(np.round(np.corrcoef(xi, yi)[0,1], 2))
```

-->

### scale

按照 Alpha101 的解释，这个算子的作用，是将数组的元素进行缩放，使之满足 sum(abs(x)) = k，缺省情况下 k = 1。它可以实现为：

<Example id=scale/>

```python
def scale(df, k=1):
    return df.mul(k).div(np.abs(df).sum())
```

### decay_linear

这个算子的作用是将长度为 d 的时间序列中的元素进行线性加权衰减，使之总和为 1，且越往后的元素权重越大。

<Example id=decay_linear/>

```python
def decay_linear(df, period=10):
    # weighted moving average over the past d days with 
    # linearly decaying weights d, d – 1, …, 1 
    # (rescaled to sum up to 1)
    weights = np.array(range(1, period+1))
    sum_weights = np.sum(weights)
    return df.rolling(period).apply(lambda x: np.sum(weights*x) / sum_weights)
```

!!! warning
    注意 decay_linear 的实现。注释来自于原文，这里的实现与原文说明刚好相反，但多数人的实现都是这样做的。另外，论文中，好几处 decay_linear 在使用时也与说明不一致，主要表现在好几处 alpha 公式中，使用了非整数的 decay_linear，比如 Alpha_98.

### delta
相当于 dataframe.diff()。

### adv{d}

成交量的 d 天简单移动平均。

### signedpower

signedpower(x, a) 相当于 x^a

## Alpha101 因子解读

我们在前面已经解读过了 004 号因子。从实现上来看，确实有它精妙之处。接下来，我们还尝试解读部分因子。熟练掌握这些因子的构造并能理解其原理，对我们自己构造出有效因子会有很大的帮助。

### alpha001

这个因子定义为：

```bash
alpha001: (rank(Ts_ArgMax(SignedPower(((returns < 0) ? stddev(returns, 20) : close), 2.), 5)) -0.5)
```

在理解这些因子时，可以通过格式化、化简的方法使得表达式更清晰、更易理解。我们将上述表达式简化为：

```bash
alpha = rank(
            Ts_ArgMax(
                SignedPower(
                (
                    (returns < 0) ? stddev(returns, 20) : close
                ), 2.), 
            5)
        )
alpha = alpha - 0.5
```

经过格式化之后，我们就比较容易理解了。最后一步显然是在进行中性化。最里层有一个三目表达式，它的含义是，如果 t0 期的收益小于 0（即为下跌），则取前 20 日 returns 的标准差，否则取 t0 期的 close。由于这是一个时间序列操作，我们可以用 np.where 或者 dataframe 的筛选表达式来完成。

```bash
# close, returns 是已知某个 asset 在一段时间里的收盘价和涨跌
tmp = close.copy()
inner[returns < 0] = stddev(returns, 20)
```

在 Alpha101 中的多数三目表达式都可以这样翻译。接下来对刚生成的临时变量进行平方、求时序上的排序，最后在截面上进行排序。于是，整个表达式可以翻译成：

```python
def alpha001(close, returns):
    inner = close.copy()
    inner[returns < 0] = stddev(returns, 20)
    return rank(ts_argmax(inner ** 2, 5)) - 0.5
```

!!! tip
    像这样翻译三目运算符，会修改原始数据，所以，我们事先对 close 进行了拷贝。

在实际实现中，我们往往会一次性地把全部因子都计算出来，因此，像 close, returns 这样的数据，都会作为一个类成员变量传入。具体可以见我们补充资料中的 alpha101 目录。

这个因子的意义是什么呢？这是一个比较难以理解的因子。

一般来说，returns 与 close 之间的数值差非常大。因此，三目运算符的作用实际上是去掉负数，把下跌期间的收盘价改成过去 20 天的收益标准差 -- 如果过去 20 天一直在稳定下跌，那么这个值就会为 0；然后，将这个由标准差和收盘价组成的临时变量平方，再进行时序上的排序。

这里的平方似乎没有意义：对一个全部大于零的序列，无论是否进行平方操作，都不会影响其排序。抛开这一点不谈，这个因子很可能在做这样的事，就是在市场普遍下跌的时候，对当天创近期高点的 asset，由于这些 asset 的 ts_argmax 为 5，为横截面上的最大值，将分配因子值 0.5；对于当天下跌的 asset，则因子值反映了离近期上涨日的距离。比如，asset 是昨天为近期高点的 asset，则 ts_argmax 为 4，将会成为横截面上的第二层。如果近期一直在下跌，则整个序列中都没有 close 值，只有涨跌幅的标准差，此时因子的含义就更复杂了。

!!! tip
    除了这些地方之外，Alpha101 中还存在大量魔术数字 -- 比如在 alpha64 中，就存在着像 0.178404, 16.6208 等意义不明的数字。文档没有说明这些数字的含义，也许只有像拉玛驽金这样的天才可能看懂。

对一些含义不太明朗的因子，我们可以通过 Alphalens 对其进行反复测试，如果数据好，也可以使用。但是对其改进的意义不大。

### Alpha002

这个因子定义为：

```bash
alpha002: 
(-1 * correlation(rank(delta(log(volume), 2)), rank(((close - open) / open)), 6))
```

这是一个含义比较明确的因子，也是一个重要的因子。它是把当日 k 线实体的变化幅度与两日成交量的变化幅度的 6 日相关性作为一个因子。并且认为这个相关度越高，则越倾向于收益已兑现。反之，则认为还存在套利空间。

<!-- 003 意义不大。它使用了 open 这一绝对值，在多数时间，assets 的 open 排序都比较固定，1000 元股永远在百元之前；成交量的排名的变化也相对惰性。所以这个因子的信息率可能不高。-->
### Alpha005

这个因子的定义是：

```bash
Alpha005: 
(rank((self.open - (ts_sum(self.vwap, 10) / 10))) * (-1 * abs(rank((self.close - self.vwap)))))
```

它是用当天开盘价与 10 日移动成本均价之差进行排名，再乘以当日收盘价与成本均价差值的排名的负值得到的一个因子，它是一个预期兑现（或称为反转）因子。这个因子的取值在 [-1, 0] 之间。从效果上看，当天开盘价越高，收盘价低（越是跌破成本均价），就意味着还有未兑现的空间；而收盘价越是高于成本均价，则预期兑现的概率越大。也就是说，高开高走的 asset，后期可能下跌；反之，高开低走，收盘价低于当天均价，或者开盘价越是低于 10 日成本均价，则越可能上涨。

这个因子的择时意味比较强，在不同的市场上，不同的市场阶段，它的表现也会不同。这里重要的是借由它的构思，去理解作者本来想要探索的原驱动力是什么，然后根据市场特点，自己进行修正。

<!-- 009 同样是使用绝对值的因子，因此高价股的因子暴露就高。这样的因子已经很难解释了，只能以测试为准。-->
### Alpha042

这是个比较重要的因子，它有比较明确的行为金融学上的解释。这个因子的定义是：

```bash
Alpha#42: 
(rank((vwap - close)) / rank((vwap + close)))
```

因子值的取值范围是 [-1, 1]。当因子为正数时，表明当天买入者已经被套，反之，表明当天买入者已经盈利。类似的因子在国内券商研报中有所研究，最好的表现能达到 5.5 的夏普 [^高智威]。

### Alpha101

这是个简单但比较实用的因子。它的定义是：

```bash
Alpha#101: 
((close - open) / ((high - low) + .001))
```

它表示的是 k 线实体的百分比，或者说上下影线的高度。取值范围是 [-1, 1]。这个因子需要与其它因子一起使用。

## 开源的 Alpha101 和 Alpha191 因子库

完整探索 Alpha101/191 中的定义的因子的最佳方案是，根据历史数据，计算出所有这些因子，并且通过 Alphalens 甚至 backtrader 对它们进行回测。[popbo](https://github.com/popbo/alphas) 就实现了这样的功能。

运行该程序库需要安装 alphalens, akshare，baostock 以及 jupyternotebook。在进行研究之前，需要先参照其 README 文件进行数据下载和因子计算。然后就可以打开 research.ipynb 进行因子分析。

在我们的补充材料中，提供了该项目的全部源码并且可以在我们的课程环境中运行。

## 拓展阅读

World Quant 是一家成立于 2007 年的对冲基金，由千禧年分拆成立，目前管理着大约 90 亿美元资产。他的创始人是白俄罗斯人 Igor Tulchinsky（伊戈尔。图尔金斯基）。

World Quant 有着相对开放的文化。Alpha 101 因子正是文章发表时，他们正在使用的因子。这篇文章的发表得到了 World Quant 的许可。此外，创始人 Igor 等人还撰写了 [《Finding Alphas: A Quantitative Approach to Building Trading Strategies》](https://www.jieyu.ai/assets/ebooks/finding-alphas-a-quantitative-approach-to-building-trading-strategies.pdf) 这本书，阐述他们对寻找 Alpha 的想法。

WorldQuant 还运营了量化金融领域的重要赛事——WorldQuant Challenge[^iqc]。2015 年，他们还推出了 WorldQuant 大学。国内许多著名的私募机构的创始人、高级管理层都有这家公司任职经历。

## 参考

1. [popbo](https://github.com/popbo/alphas) 的这个库提供了 Alpha101 和 Alpha191 因子库的实现，比较新，但已经获得了接近 200 个星标。该库在 Alpha 101 与 Alpha 191 因子库计算之外，还提供了数据获取（通过免费的 baostock）、因子分析、回测功能。
2. [yutiansut](https://github.com/yutiansut/QAFactor_Alpha101) 的这个库也提供了 Alpha 101 的实现。该库的作者余天，是杭州波粒二象资管公司的 CTO，也是 QuantAxis 的作者，QuantAxis 是非常著名的开源量化框架，现在拥有 8.2k 星标。
3. [果仁网](https://guorn.com/stock/help#chapter3-1-3) 提供了 Alpha101 因子解读，主要是对算子进行了翻译。

## Footnotes

[^zura]: Zura Kahushadze，康奈尔大学物理学博士。原 World Quant Director Manager，现经营自己的公司 Quantigic。他是 Alpha 101 和 151 个交易策略这本书的作者。

[^iqc]: WorldQuant Challenge 是世坤组织的量化大赛，也是入职量化行业的一个敲门砖。它的报名地址在 [这里](https://www.worldquant.com/zh-hans/iqc/)。

[^alpha191]: Alpha 191 因子库一般是指国泰君安在 2017 年发布的《基于短周期价量特征的多因子选投体系》中提出的 191 个因子。研报可以在 [](https://www.jieyu.ai/assets/ebooks/国泰君安－基于短周期价量特征的多因子选股体系。pdf) 下载。

[^fraut]: 据《经济参考报》不完全统计，2023 年全年因涉及财务造假被监管层处罚的上市公司超 30 家。根据 [中国证监会](http://www.csrc.gov.cn/csrc/c100028/c7500745/content.shtml)，2024 年上半年查办财务造假等信息批露违法行为 192 件，处罚责任主体 283 人（家）次，刑事移送 230 人（家）次。

[^fundmental_data]: Alpha58, 63, 100 等使用了行业分类数据。要快速了解哪些因子使用了行业分类数据，在公式里搜索 IndClass。Alpha 56 使用了市值（cap）。

[^高智威]: 高智威在研报 [遗撼规避因子](https://www.jieyu.ai/assets/ebooks/遗撼规避因子.pdf) 中披露，该因子的夏普率达到了 5.5。
