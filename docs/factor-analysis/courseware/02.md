
在上一章中，我们给出了因子分析的一般框架，包括数据获取、因子生成、因子预处理及单因子检验等基本步骤。有一些开源库已经实现了这些功能，比如 Quantopian 的 Alphalens[^alphalens] 和聚宽的 JQFactor[^jqfactor]。

在本课程中我们将介绍 Alphalens 框架。但在此之前，我们会先手动实现一遍因子分析流程，以便更好地理解因子分析框架。

## 1. 因子数据的来源

因子的原始数据来源于交易数据、财务数据、另类数据、公告事件、盈利预期、行业特色、股东持股、公募持仓、新闻舆情、指数成分和衍生产品等。

交易数据、财务数据、新闻舆情等数据会对股价走势产生作用，这一点很容易理解，也是量化研究中的重点。但是，对同样的数据使用相同的技术手段和算法，往往会挖掘出相似的因子，如果大家都使用相同的因子，就可能出现所谓拥挤效应。这就是另类数据的来源。

另类数据是指除传统的交易、财务、股本结构等数据之外，尚未大规模研究和应用的数据。另类数据有时能产生意想不到的效果。比如全球气象数据对预测农作物的产量很重要，这对商品期货、甚至对股票类资产的定价也有重要影响。又比如，苹果 Appstore 榜单的排名对估计游戏公司、电商公司的利润非常重要。再比如，一些人会使用搜索引擎 [^gst] 的趋势榜来创建因子。

!!! tip
    尽管另类数据有时能产生意想不到的效果，但这些数据的效果也有被夸大之嫌。对资金量少的个人投资者或者小型机构，另类数据的性价比并不高。这类数据的购买或者采集成本高，但如果等到价格便宜时，也必然有了很多使用者，也就不再收能到奇效。<br><br>因此，对资金量小的个人投资者或小型机构而言，应该把主要精力放在行情数据和财务数据上，通过改进技术手段、运用新的算法或通过融合、跨界创新来发现这些数据的新价值。事实证明，这一路径是存在的。毕竟，罗尔夫. 班茨 [^banz] 在发现小市值因子时，他引用的数据已经存在了 40 多年了，但其中的小市值因子规律一直没有被人发现。<br><br>数据也可能存在各种偏差，一些市场还可能存在财务数据质量不高的情况，因此个人和小型机构可以把更多精力放在量价因子的挖掘上。毕竟，一切信息最终都将反应在价格上。这个反应的过程，也就是另类数据（内幕消息）传播的过程。

前面提到的数据中，既有结构化数据（如交易、财务数据），也有非结构化数据（如新闻舆情）。对于非结构化的数据，需要先进行结构化处理和数字化处理，才能够进行因子分析。数据结构化，这也是 AI 技术目前可以大显身手的地方之一。

!!! note
    有一些数据源除了原始数据之外，还可以提供已经处理好的因子数据，比如聚宽就提供了基于 Alpha101[^alpha101] 计算的因子库和他们自己的因子库。他们实时监控这些因子的表现，通过因子看板可以了解他们提供的因子库的表现。对于一些市场普遍使用的知名因子，尽管可能实现细节有所不同，但主要思想会是一致的，因此，我们也可以参考它的因子看板，了解市场近期风格的变换。

如果因子数据是动量类和技术指标类的，我们可以根据行情数据在本地自行计算并保存。但是，有一些比较新颖、比较小众的数据，特别是所谓的另类数据，一般就只能通过第三方数据源获取。

关于数据的获取，在我们的《量化 24 课》[^24lectures] 中有专门介绍。在本课程中，我们将混合使用 Akshare, zillionare-omicron 或者预定义的数据集。

## 2. 因子生成

我们在拿到原始数据之后，往往要进行一些预处理工作，才能将其转化为因子。有一些因子，我们可以直接使用原始数据，比如财务类的因子。但也有一些因子，特别是技术类的因子，一般都要经过计算。最终，处理的结果是一个 dataframe，它的索引是时间戳，只有一列值，即因子值。

我们以熟悉的 RSI 为例，看看因子如何提取。在本课程中，我们提供了一个名为 load_bars 的函数，来帮助我们快速提取行情数据。

<Example id="load_bars"/>

```python
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
barss = load_bars(start, end)
barss.tail()
```

输出结果是一个 DataFrame，它的索引是一个多重索引，包含了 OHLC 和 volume（成交量）、amount（成交额）等列。factor 是复权因子，数据已经实现了动态前复权，因此多数情况下，你可以不用管复权的问题。prices 数据对应的是 T+1 期的开盘价。这一点你很容易从输出结果中看出来。

第一次运行会等待几秒钟，不过此后调用的速度就很快了。

下面的例子演示了如何生成一个 RSI 指标因子：

<Example id="rsi"/>

```python
import talib as ta
def calc_rsi(bars, win: int = 6):
    rsi = ta.RSI(bars["close"].astype(np.float64), win)

    return pd.DataFrame(rsi, index=bars.index, columns=["factor"])

code = barss.index.get_level_values(1)[0]
bars = barss.xs(code, level="asset")

calc_rsi(bars)
```

在上面的示例中，我们输入日线行情，取其 6 日 RSI 值作为因子。如果我们输入的行情是其它周期，或者时间窗口是其它周期，这样就构成了一个算法，多个因子。这也是为什么说每家机构在因子数量上统计不可能一致的原因。

在输出结果的结果 df 中，最前面的 6 行取值为 NaN。显然，它们无法对回报贡献任何解释。我们在后面的内容中，将会介绍如何处理它们。

!!! warning
    其实，不仅仅是最前面的 6 行 NaN 无法对收益产生解释。根据 RSI 的算法，它最初的 win * 3 条记录（含 nan）都不够准确。这被称为技术指标的冷启动问题。如果我们对因子评估的要求比较高，可以在提取因子时，也把前面的几行置为 NaN，当成缺失值来处理。

下面是动量因子一例，来自著名的 Alpha101 因子库：

<Example id="alpha001"/>

```python
def alpha001(c, r):
    """(rank(ts_argmax(power(((returns < 0)
        ? ts_std(returns, 20)
        : close), 2.), 5)) -0.5)"""
    c[r < 0] = ts_std(r, 20)
    return (rank(ts_argmax(power(c, 2), 5)).mul(-.5)
            .stack().swaplevel())
```

这段代码暂时还运行不了，因为其中包括的一些函数并不在标准库中。不过别担心！我们后面有专门的章节来介绍 Alpha101。

在上述示例中，我们分别提取了一个技术分析因子和一个动量因子。根据使用的数据不同和算法的不同，我们可以提取非常多的因子。下面是一些经典的因子例子：

- 动量因子 (Momentum)[^momentum]。根据股票的长期表现（比如过去 12 个月的收益回报）来进行打分，并期望股票在未来保持相应的表现。其原理是，如果一支股票在过去较长时间内表现突出，这可能反映了该公司的治理较为优秀，或者行业正处于景气阶段 -- 这些都是我们可以继续看好该公司的因素。
- 均值回归 (Mean reversion)[^mean-reversion]。根据股票短期回报的倒数对股票进行评分，期望股票价格未来能恢复到移动平均值。基于均值回归的策略在日内高频交易中往往有较好的表现。
- 波动率（Volatility）。 通过回报标准差的倒数对股票进行评分。该因子的名字由 Fisher Black 于 1972 年 [^voliatility] 首次提出。1991 年，Haugen 和 Baker[^Huagen] 证实了美国市场存在此效应。优质的股票常常体现出较低的波动性。优质的股票往往有较高股息率，更容易吸引中长期投资资金。一旦这些资金占据主导地位，并且长期持有能得到合理的回报，他们也就不愿意频繁换手，因而降低了波动率。
- 规模因子。这是我们在第一章就介绍过的因子。
- 价值和质量因子。这也是我们在第一章就有所涉及的因子。这主要是一些财务和基本面因子。一些财务指标，比如股息率、PE 值、现金流等，能较好地反映公司的市场竞争力和治理能力。显然，这样的公司具有成长性和持续盈利能力，因此持续获得风险溢价也是题中应有之义。
- 技术分析因子。我们前面提到的 RSI 就是其中一个常用的技术面因子。其它常用因子还有 MACD，布林带，ATR，OBV 等。
- 情绪因子。比如分析师指数，股吧人气榜，北向资金，中信空单动量等。

## 3. 因子预处理

### 3.1. 异常值处理

交易数据中产生的异常值会少一些。但在财务数据和其它数据中，异常值的出现比较常见。比如，如果某公司去年同期净利润接近于 0（或者等于 0），则净利润增长率因子会变成一个异常大的数值（或者无定义）。

因此，在进行后续的回测前，我们要对该类数据做预处理。如果不进行异常值的修正，异常值会显著干扰因子中性化（回归）的结果，也会给 IC 等指标的计算带来误差。

异常值修正的方法大致有以下几种：

##### *******. **均值标准差修正法**

将偏离均值 3 倍标准差的数据拉回 3 倍标准差，这种修正法也称为$3\sigma$法。这是因为，基于正态分布的假设，超过均值$3\sigma$的数值就非常罕见了。

在代码实现上，pandas 和 numpy 都提供了一个名为`clip`的函数，我们可以先计算出$3\sigma$，然后通过`clip`函数将超过$3\sigma$的数据拉回$3\sigma$。

<Example id="clip"/>

```python
import numpy as np

arr = np.append(np.random.randint(1, 3, 20), [15, -10])
std = np.std(arr)
mean = np.mean(arr)

print(mean + std*3)

np.clip(arr, mean - 3 * std, mean + 3 * std)
```

`clip`函数的第一个参数是待处理的数组，第二个参数和第三个参数分别是要替换的边界值。超过这个边界值的数据，都将分别被这两个值替换。

当数据量比较小时，个别的离群值将显著影响到 std 的计算，从而导致 clip 方法失效（即所有的点都落在$\pm3\sigma$内）。大家可以通过缩小第 3 行中，np.random.randint 中的 size 参数，自行尝试下。

##### *******. **分位数修正法**

将数据排序后，按分位数分布，将头尾的数据拉回。这种方法也叫 winsorizing，是根据生物统计学家 Charles Winsor 的名字来命名的。

我们可以使用 scipy.stats.mstats 中的 winsorize 方法来实现。

<Example id="winsorize"/>

```python
from scipy.stats.mstats import winsorize

arr = np.append(np.random.randint(1, 3, 20), [15, -10])
print(arr)
print(winsorize(arr, 0.05).data)
```

winsorize 的第一个参数是待处理的数组；第二个参数，可以是一个数组，也可以是一个标量。比如，如果我们要对称缩尾 10%，则可以传入 [0.1, 0.1]，也可以只传入 0.1。如果要非对称缩尾，则应该使用数组的方式。

winsorize 返回的结果中，我们需要的数据保存在`data`属性中。

##### *******. **中位数极值法**

将偏离中位数`n`倍的数据拉回。一般使用 5 倍左右的数值。

这里需要先介绍绝对中位差（median absolute deviation) 的概念：

$$MAD = median(|X_i - median(X)|)$$

为了能 MAD 当成与标准差$\sigma$估计相一致的估计量，即
$$\hat{\sigma} = k. MAD$$

这里 k 为比例因子常量，如果分布是正态分布，可以计算出：
$$
k = \frac{1}{(\Phi^{-1}(\frac{3}{4}))} \approx 1.4826
$$

基于这个 k 值，取 3 倍则近似于 5。

代码实现如下：

<Example id="mad_clip"/>

```python
from numpy.typing import ArrayLike

def mad_clip(arr: ArrayLike, k: int = 3):
    med = np.median(arr)
    mad = np.median(np.abs(arr - med))
    
    return np.clip(arr, med - k * mad, med + k * mad)

np.random.seed(78)
arr = np.append(np.random.randint(1, 4, 20), [15, -10])
mad_clip(arr, 3)
```

### 3.2. 缺失值处理

因子数据常常出现有缺失值的情况。比如，分析师评级数据会出现一些冷门标的连续好多个季度无人覆盖的情况；技术指标类的因子，常常会有冷启动情况，表现为前几项往往无法计算，于是产生了缺失值。

<!--
大家有空可以看看东财 app，在个股的研报栏下，可能会有评级统计、盈利预测数据。这个数据只展示 1 年的，因此，有一些标的一年内无人评测，就不会有这一项数据。

![](https://images.jieyu.ai/images/2024/05/分析师评级.jpg)

-->
<!--
什么是技术指标的冷启动？比如，我们计算 5 日均线时，前 4 个周期的数据就会是缺失的。只有从第 5 个周期开始，才会有每个周期的数据，这就是冷启动。
-->

!!! tip
    Bryzgalova[^bryzgalova] 的研究表明，超过 70%的公司的基本面因子都存在缺失的情况（他们使用了资产定价中 45 个最流行的特征因子）。

在因子分析时，一般的做法，是将因子中缺失值所在的行完全删除，只保留有效值进行分析。但是，有时候我们需要对缺失值进行替换。一般有延用上一期有效数据、中位数替代法、相似公司替代法等。

对财务因子，我们一般延用上期因子值；其它因子需要根据具体情况具体分析，可以延用上一期值，也可以考虑其它不影响分析的替代值；如果因子值缺失过多，导致覆盖度特别低，则应该考虑剔除。

当我们对缺失值进行替换处理时，需要考虑到这种操作对数据分布的影响。比如，是先进行缺失值处理，再进行去极值；还是先进行去极值，再进行缺失值处理；两者的最终结果可能是不相同的。

!!! tip
    在 Bryzgalova[^bryzgalova] 这篇论文中，他们提出一种新的插补方法，即利用数据的时间序列和横截面依赖性来插补其缺失值。

我们可以通过 pandas 中的 fillna、ffill、bfill、或者 interpolate 方法来对缺失值进行插补。其中 fillna 用以常数替换；ffill 用以前向填充（即使用前一个非 NaN 值来填充当前 NaN 值）；bfill 用以后向填充；interpolate 用以插值填充，填充方法有线性、邻近值等好多种，建议在需要的时候，查阅 Pandas 手册。

!!! warning
    fillna 通过 method 参数提供了 ffill 和 bfill 功能，但现在已不建议使用。

### 3.3. 分布调整

理想情况下，因子在截面上的暴露应呈现正态分布。如果有严重偏离，可以尝试用对数法、开根号方法对原始数据的分布进行调整。具体使用哪一种方法调整，可以先进行分布推断，再尝试进行调整。

市值因子就是一个需要进行分布调整的例子。因为 A 股小票众多，但少数股市值巨大，因此原始的市值因子呈现明显的右偏、尖峰、后尾的分布特点。

我们以下面的示例代码，来验证下对市值进行对数处理前后的效果。

要获得当前所有上市公司的市值，我们可以使用 akshare 的方法：

<Example id="python-market-value-factor" />

```python
import akshare as ak

df = ak.stock_zh_a_spot_em()
df = df[["代码", "流通市值"]].rename(columns = {
    "代码": "code",
    "流通市值": "market_value"
})

```

通过绘制直方图，我们可以快速直观地了解数据分布是否符合正态分布：

```python
df.hist()
```

现在，我们对 market_value 进行对数处理：

```python
import numpy as np

df["log_market_value"] = np.log(df["market_value"])
df.hist(column="log_market_value")
```

对数化之后，我们发现，市值的分布就比较接近正态分布了。

### 3.4. 标准化

<!--前面讲到的几种预处理方法，它的必要性都是理所当然，不言自明的。为什要进行标准化呢？-->

传统的多因子模型都是线性回归模型。标准化的主要是为了去掉多个因子之间量纲不同的差异，以便在线性回归模型中，各个因子的权重可以公平的比较。

在做单因子检验期间，没有必要进行标准化。Alphalens 不会对因子进行标准化，也不要求对因子进行标准化。

但如果我们要从多因子组合成模型，就要视后面的模型算法来决定是否要进行标准化。如果我们采用 decision tree, xgboost 这一类的算法，这些算法对量纲不敏感，因此也不需要标准化；如果是要构建线性回归模型，则必须对因子进行标准化。

标准化的方法，是将上述处理后的因子分布看成近似正态分布，于是可以根据以下公式：

$$
    Z = (x - \mu) / \sigma
$$

就得到 z-score。如果因子符合正态分布，那么 Z 值就是一个符合 N(0,1) 的正态分布。

!!! warning
    进行标准化的前提是因子本身符合或者近似符合正态分布。如果这一个假设无法成立，那么我们对因子直接进行标准化将毫无意义。我们可以通过统计检验来判断这个假设是否成立。这部分知识在《量化二十四课》中有介绍。<br><br>另外，z-score 是一个服从 N(0,1) 的正态分布，并不意味着 z-score 的取值为 (0,1)，它的意思是均值为 0，标准差为 1，或者说，$|Z|$在 [1,2] 区间的概率小于 5%；落在$[2, +\infin]$的概率小于 0.3%。

<!--
多元线性模型：

$$
y = \beta_0 + \beta_1X_1 + \beta_2X_2 + ... + \epsilon \tag 1
$$

$X_i$是资产$i$的收益，$\beta_i$是因子值，从数学上看，它是拟合曲面的斜率。

$$
\hat{\beta_1}(x_1)=\frac{\sum_{i=1}^n(x_{1,i} - \bar{x_1})(y_i - \bar{y})}{\sum_{i=1}^n(x_{1,i} - \bar{x_1})^2}
$$

因此，

$$
\hat{\beta_1}(\alpha x_1)=\frac{\sum_{i=1}^n(\alpha x_{1,i} - \alpha \bar{x_1})(y_i - \bar{y})}{\sum_{i=1}^n(\alpha x_{1,i} - \alpha \bar{x_1})^2} \\
\frac{\alpha \sum_{i=1}^n(x_{1,i} - \bar{x_1})(y_i - \bar{y})}{{\alpha}^2\sum_{i=1}^n(x_{1,i} - \bar{x_1})^2}\\
= \frac{\hat{\beta_1}(x_1)}{\alpha}
$$

因此，缩放不会影响其它斜率的估计量，但确实会影响不同估计量之间的比较。

-->

### 3.5. 中性化

许多因子会因部门（sector）和行业（industry）的不同而有所差异 [^sector-neutralization]。但这些差异并不反映公司的好坏，而只是反映了不同行业运营的不同方式和所处的经济周期的不同。为了提升因子对公司的选择能力，我们就有必要在因子分析过程中，"中和"掉行业/部门对因子暴露的影响。除了行业中性化之外，常做的中性化还有市值中性化。

<!--行业是指一组性质相近的公司；部门则是指对经济体的大的划分。在股票市场中，一般将部门视为广义分类，将行业视为狭义分类。部门分类的例子有第一产业、第二产业等。又比如，交通运输是一个经济部门，该部门包括汽车制造、火车和卡车运输、航空业等，它是第三产业 (tertiary sector) -- investopedia -->

比如，如果我们在 2022 年用市盈率因子选股，我们会发现市盈率最低的（且为正），多是银行股。如果我们凭这个因子来选股，实际上选择的是银行股，其涨跌是受政策宏观调控与经济周期的系统性影响，不能选择出具有独特 alpha 的个股，也不能有效分散风险。

<!--有趣的是，银行股在 2024 年上半年仍然有非常好的涨势-->

行业中性化一般有两种方案。一种是，将股票按行业进行分组，计算出每个行业的因子行业均值，再用每个股票的因子值减去该行业的因子行业均值，这样得到的差值就是我们要的中性化因子，即：

$$
\epsilon = Y - \bar{Y}_{Industry_i}
$$

这里$i\isin{n}$，是第$i$个行业。这种方法简单易懂。

我们也可以使用哑变量线性回归法进行行业中性化。哑变量是分类中常用的虚拟变量，通常取值为 0 或者 1，来反映某个变量是否属于某个类别。

其公式为：

$$
Y = \beta*Industry + \alpha  + \epsilon 
$$

这里的残差$\epsilon$就是行业中性化后的因子。

<!--通过线性回归求截距，是一种去除波动的常用方法，跟 CAPM 是一样的。尽管公式中存在常量$\alpha$项，但在实际计算中，我们不考虑这一项，而是把计算出的截距全部当作中性化后的因子。

由于行业变量是 0 和 1 的哑变量，因此，对因子进行行业中性化也就相当于用因子值减去行业因子均值。但是，使用线性回归的方案，可以与市值中性化一起来进行。
-->

市值中性化回归公式如下：
$$
Y = \beta * \log(MarketValue) + \alpha + \epsilon
$$

我们可以同时进行市值中性化和行业中性化，并且可以对多个因子同时实施。我们先来生成因子。我们仍然使用 [](#example-2) 中的 calc_rsi 来计算因子。

<!--这是因为，上述公式的线性组合仍然是一个线性组合。-->
<Example 8/>

```python
import pandas as pd

universe = (
    '000001.XSHE',
    '000002.XSHE',
    '000004.XSHE',
    '000006.XSHE',
    '000007.XSHE',
    '000008.XSHE',
    '000009.XSHE',
    '000011.XSHE'
)

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)

barss = load_bars(start, end, universe)
factors = []
for asset in universe:
    df = barss.xs(asset, level="asset")
    rsi = calc_rsi(df)
    rsi["asset"] = asset
    factors.append(rsi)

factor = pd.concat(factors)
factor.set_index("asset", append=True, inplace=True)
factor
```

接下来的代码将实现行业中性化：

!!! attention
    以下示例可能需要 tushare 授权（如果数据没有命中缓存的话）。 如果您是课程的高级用户，我们已经提供了 Tushare 的高级账户供您使用。其它用户可以临时设置自己的 tushare token 以运行下面的示例。

    ```python
    import tushare as ts
    tushare.set_token("YOUR TUSHARE TOKEN")

    pro = tushare.pro_api()
    ```

<Example id="neutralize_factors"/>

```python
import pandas as pd
from sklearn.linear_model import LinearRegression

dt = datetime.date(2023, 12, 29)
tm = datetime.datetime(dt.year, dt.month, dt.day)
sectors = load_sectors()

sectors = sectors[sectors.index.isin(factor.index.get_level_values('asset'))]

valuation = fetch_valuation(dt)
valuation = valuation[valuation.index.isin(factor.index.get_level_values(1))]

# GET_DUMMIES 是一个 ONE-HOT 编码的矩阵
sectors = pd.get_dummies(sectors)
lncap = np.log(valuation)

# Y 可以是一个矩阵，但这里我们只有一个因子要中性化，所以是一个 (1,N) 的矩阵
y = factor.xs(tm, level=0)

y = y.dropna(how="any")
    
X = pd.concat([lncap, sectors], axis=1)
X = X.dropna(how="any")

model = LinearRegression(fit_intercept = True)
res = model.fit(X, y)

# 根据公式求残差，这个残差，就是我们新的因子
coef = res.coef_
residue = y - (np.dot(X, coef.T) + res.intercept_)
display(residue)
```

最终我们得到的残差，就是行业中性化后的因子。

!!! tip
    我们常常对市值因子进行分布调整，但要注意，分布调整与这里讲的市值中性化是相区别的。分布调整直接作用在个体市值因子上，这种调整不需要借助整体样本；而市值中性化是对样本减去均值的过程。

在 [](#example-9) 第 27 行代码中，有一个参数`fit_intercept`。它的作用如下图所示：

![](https://images.jieyu.ai/images/2023/06/fit_intercept.png)

这个参数的默认值就是 True。无论市值如何，RSI 都不会为零，所以，它应该有一个截距项。

<!-- 这个图表明，如果 fit_intercept 置为 False，那么，截距会被强制为零；反之，保留截距。回到公式 7，这里的 Y 就是 RSI，当市值为零时，回归后的残差即为 alpha + eplison。由于 RSI 在任何时候，即使是市值为零，它也会有一定的值，所以，这里我们要保留截距项。

另外一个小知识。做回归有多种方案。我们这里用的是 scikit-learn. 也可以用 statsmodels 的 ols. 在用 ols 时，我们要加上一个常量，在 scikit-learn 中则没有。这会导致最终结果不一致吗？

答案是，sklearn 会自己加上常数项。在 statsmodels 中，我们必须手动加上。

下面的代码演示了有没有截距项、加没加常数的区别。

```python
from sklearn.linear_model import LinearRegression
import numpy as np

np.random.seed(1)
bias = 100

X = np.arange(1000).reshape(-1,1)
y_true = np.ravel(X.dot(0.3) + bias)
noise = np.random.normal(0, 60, 1000)
y = y_true + noise

# WITH COLUMN OF ONES
X_with_ones = np.hstack((np.ones((X.shape[0], 1)), X))

for b,data in ((True, X), (False, X), (True, X_with_ones), (False, X_with_ones)):
  lr = LinearRegression(fit_intercept=b)
  lr.fit(data, y)

  print(lr.intercept_, lr.coef_)

中性化的另一个方法是，通过model.predict(X)来求y_pred,然后用 y - y_pred 来计算残差，即得到去均值后的残差。这种方法下，无论fit_intercept的值如何，计算都会是正确的。
```

-->

我们这里介绍的中性化的方法，是通过线性回归实现的。它可以同时实现行业中性化和市值中性化。但是，在 Alphalens 中，行业中性化是通过组内分层实现的。此外，也有减去行业均值的做法 (demean)。

是否应该进行业中性化，学界存在一定的争议。Sina Ehsani[^SEhsani] 等人 2022 年的研究认为，采用多空对冲的投资者需要进行行业中性化，而单纯做多的投资者则应该避免行业中性化。

在外文刊物中，行业一词会分的细一点。我们常常看到的是 sector 和 industry 两个词。industry 是 sector 的进一步细分。类似于我们的申万一级到申万三级这样的细分。在本教程中，我们做的行业中性化，是基于申万一级行业。如果使用申万二级甚至三级行业，它的优势是，同一个二级（或者三级）行业中的公司会比申万一级行业中的公司联系更紧密，因此可以提供更为准确的基准。但是，一些三级（甚至二级）行业规模太小，无法进行有意义的比较。这也是我们要考虑的因素。

关于行业中性化，还可以参考 Quant Rocket 上的这篇文章 [^sector-neutralization]。
    

## 4. 延伸阅读

关于均值回归和优化，也可以阅读 Zura Kakushadze 的这篇文章 [PDF](https://arxiv.org/pdf/1408.2217.pdf)。

在 A 股市场，股息率与低波动也具有风险溢价。曲小雅等人证实，从高股息率投资组合中剔除高波动率股票，可降低投资组合波动率，提高经风险调整的收益率。[PDF](https://www.spglobal.com/spdji/zh/documents/research/research-blending-low-volatility-with-dividend-yield-in-the-china-a-share-market-sc.pdf)

## 5. Footnotes

[^alphalens]: <a href="https://github.com/quantopian/alphalens">Alphalens</a> 是 Quantopian 开发并开源的因子分析框架。
[^jqfactor]: <a href="https://github.com/JoinQuant/jqfactor_analyzer">JQFactor Analysis</a> 是聚宽 (joinquant.com) 开发并开源的因子分析框架。
[^gst]: Jan Jakub Szcygielski, 2023, International Review of Financial Analysis, Google search trends and stock markets: Sentiment, attention or uncertainty? <a href="https://www.sciencedirect.com/science/article/pii/S1057521923000650/pdfft?md5=4fa917b6dd78475044dc7fbab363e8bf&pid=1-s2.0-S1057521923000650-main.pdf">PDF</a>
[^banz]: Rolf Banz, 1981, Journal of Financial Economics, The relationship between return and market value of common stocks. <a href="https://search.dailystocks.com/Banz_sizeeffect_1980.pdf">PDF</a>
[^alpha101]: Zura Kakushadze, 2016, arXiv, 101 Formulaic Alphas. <a href="https://arxiv.org/pdf/1601.00991.pdf">PDF</a>
[^24lectures]: <a href="http://www.jieyu.ai/articles/coursea/24lectures/intro">量化 24 课</a> 是我们推出的涵盖整个量化过程的一门中高级课程，适合私募总、基金经理、独立交易者等需要全流程掌握量化的学员。
[^momentum]: Jegadeesh, Narasimhan, 1993, The Journal of Finance, Returns to Buying Winners and Selling Losers: Implications for Stock Market Efficiency. <a href="https://www.bauer.uh.edu/rsusmel/phd/jegadeesh-titman93.pdf">PDF</a>
[^mean-reversion]: James M. Poterba, Mean Reversion in Stock Prices: Evidence and Implications <a href="https://www.nber.org/system/files/working_papers/w2343/w2343.pdf">PDF</a>
[^voliatility]: Fischer Black, 1972, The Journal of Business. <a href="http://www.stat.ucla.edu/~nchristo/statistics_c183_c283/fischer_black_trace_out.pdf">PDF</a>
[^Huagen]: Haugen, Baker, 1991, The Journal of Portfolio Management, <a href="https://www.pm-research.com/content/iijpormgmt/17/3/35">The efficient market inefficiency of captialization-weighted stock portfolios</a>

[^bryzgalova]: Bryzgalova, Svetlana and Lerner, Sven and Lettau, Martin and Pelger, Markus, Missing Financial Data (May 11, 2022). Available at SSRN: https://ssrn.com/abstract=4106794 or http://dx.doi.org/10.2139/ssrn.4106794 

[^sector-neutralization]: 行业中性化<a href="https://www.quantrocket.com/blog/sector-neutralization/">1</a>, <a href="https://www.quantrocket.com/blog/sector-neutralization/">2</a>

[^SEhsani]:  Ehsani, Sina and Harvey, Campbell R. and Li, Feifei, Is Sector-neutrality in Factor Investing a Mistake? (February 22, 2022). Available at SSRN: https://ssrn.com/abstract=3959116 or http://dx.doi.org/10.2139/ssrn.3959116. <a href="https://www.jieyu.ai/assets/ebooks/Is sector neutrality in factor investing a mistake.pdf">PDF</a>
