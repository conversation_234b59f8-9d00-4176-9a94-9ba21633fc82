# Talib 和技术指标因子


Talib[^talib] 是最重要的技术分析库之一。

技术分析是指通过分析证券的历史交易活动和历史数据（如价格和成交量），来预测未来股价运动的分析方式。它基于这样两个假设，或者说信仰：一是所有跟证券相关的信息，都已包含在价格当中。从这个假设出发，技术分析师使用各种技术指标和图形模式来判断趋势、动量以及隐藏在该支证券运动背后的**情绪**。二是历史总在重复自己。

由于 AI 和算力的增长，技术分析的用法也正在发生变化，特别是运用机器学习来完成多个指标间的组合和权重分配，建立自适应模型越来越普遍。

与技术分析相对的分析手段则是基本面分析。它是通过各种宏观和微观经济学因子，来探寻证券标的的内在价值的，然后将其内在价值与当前价格进行比较，决定交易方向的一种分析方式。

一般而言，技术分析在较短的周期内更加有效，但资金容量小一些；基本面分析只在较长的投资周期中有效，但资金容量大。这是因为，证券的价格终究是由公司的价值来决定的；公司价值的变化必然是一个长周期的过程；从实务上讲，公司财务的批露也是以季为单位。而技术分析赚的是波动的钱，它无法预知一家公司未来的发展状况，因此当我们使用技术分析时，几乎只能着眼于短期波动。

传统上看，技术分析基于技术指标和模式识别，前者如移动均线、摆动类指标、动量类指标等，后者如早晨之星，三只乌鸦，十字星等。不过广义上来讲，凡事基本面分析之外的量化方法，都可以、也应该归类技术分析。

Talib 涵盖了这两方面的分析库，但在课程中，我们只介绍技术指标分析。

!!! info
    在 Python 中，最常用使用的 ta-lib 库是 [TA-Lib-Python](https://github.com/TA-Lib/ta-lib-python)。它是底层 talib c 库的一个封装。在安装时，需要先安装 talib c 库，再安装 Python 接口库。安装指示请见其项目说明。

在演示本章的技术指标时，我们都将使用平安银行的数据。为了使得示例代码更简洁，我们在这里先一次性地将它读取出来：

```python
np.random.seed(78)

code = "000001.XSHE"
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

PAYH = load_bars(start, end, (code, )).xs(code, level=1)
barss = load_bars(start, end, 2000)
```

后面在演示单个技术指标时，我们将直接使用 PAYH 变量。

## Talib 函数分组

Talib 共提供了约 150 多个函数。我们可以通过分组来整体认识它们。下面的代码显示了 Talib 函数的分组。

<Example 1/>

```python
import talib as ta
function_groups = ['Overlap Studies',
                   'Momentum Indicators',
                   'Volume Indicators',
                   'Volatility Indicators',
                   'Price Transform',
                   'Cycle Indicators',
                   'Pattern Recognition',
                   'Statistic Functions',
                   'Math Transform',
                   'Math Operators']

talib_grps = ta.get_function_groups()
cols = ["Function Group", "# Indicators"]
pd.DataFrame([(k, len(v)) for k,v in talib_grps.items()], columns=cols)
```

技术指标可以用来平滑价格、过滤高频噪声，判断价格的运动方向（及强弱），预测反转，划出支撑位和压力位和预测波动率，等等。

## 冷启动期

多数技术指标都是时序指标。时序指标的特点之一是，它需要前面的数据才能计算。例如，移动平均线需要前面的 n 个数据才能计算。这就构成了技术指标的冷启动期，有时候我们也把它称之延时性。

人们在一些技术上尝试各种技术来降低延时性，比如在移动平均线中，采用指数加权的方法，试图更及时地反馈最新的状态。但是，延时性是时序指标的固有缺陷，任何人为的修正都很难得到普适的结果。

!!! tip
    实际上，所有的因子/指标都有延时性，只不过技术指标的延时性是摆在桌面上而已。无论是宏观面的经济指标、还是公司的财务数据，由于统计流程的原因，反映的都是已经过去一段时间的经济、财务运行情况。

如果要计算 n 日移动平均线，在前 n-1 期，其数据都会是 nan，但此后的数据都是有效的、可用的。但也有一些指标，比如 RSI，除了开头窗口期的数据为 nan 之外，之后若干期的 RSI 也是不精确的。如果我们把这些不精确的数值代入到因子检验中，我们得到的结果将是不准确的、无意义的。

这种不精确性是技术指标的计算方法带来的，没有固定的特征。如果我们要使用它们，就必须先深入了解它们背后的算法，通过实验找出经验值。

## 动量、震荡类指标

这一类指标主要有 RSI, ADX, PPO, Aroon, Money Flow Index, Balance of Power, Willian's R 和随机振荡指标等。

### RSI

我们介绍的第一个技术指标，就是 RSI。

RSI 是 Welles Wilder[^wilder] 于 1978 年提出的一个技术指标，发表在《Commodities》（现为《Modern Trader》[^mt]）杂志上。

RSI 是一个振荡指标，它在 0 到 100 之间摆动，非常容易归一化，因此特别适合机器学习。它的计算公式如下：

$$
    RSI = \frac{EMA_{(U,n)}}{EMA_{(U,n)} + EMA_{(D,n)}} * 100%
$$

RSI 虽然称作是相对强弱指标，实际反映的是资产在区间内的赚钱效应。与<b>sortino 指标</b>有一定的相似/关性。当区间内价格全部为下跌时，RSI 与 sortino 一样，取值全为零；但当区间内价格全部为上涨时，RSI 会取上限 100，但<b>sortino 则变为正无穷</b>。

在 talib 中，我们这样计算 RSI：

<Example 2/>

```python
import talib as ta
import numpy as np

close = np.random.random(10)
rsi = ta.RSI(close, 6)
rsi
```

talib 返回的 rsi 值是一个介于 0 到 100 之间的浮点数。在 ta.RSI 中，缺省的时间窗口是 14，不过国内券商在快线 RSI 上一般使用的是 6 个周期。所以，如果我们只使用单个 RSI（即无须象行情软件一样绘制快慢线图）时，应该也使用 6 个周期，这样才会与多数投资者保持一致。


### RSI 因子收益思考

<!-- 笔记： RSI 高Alpha 28% -->
过去一般认为，作为一个震荡指标，应该把 RSI 大于 70 当成超买（即此时应该卖出），小于 30 当成超卖（即此时应该买入）。我们在前面两章已经花了不少篇幅介绍 RSI，并且得到一些不错的结果，比如在按 bins 对 RSI 因子分十层的话，做空第 1 层，做多第 5 层，我们就能在 2018 年到 2023 年的期间，得到年化 Alpha 达到 28.3%的业绩。

在这些参数下，我们究竟是在 RSI 多少时做多的，又是在 RSI 为多少时做空的呢？在前面几章没有明确地回答这个问题，现在，我们就展开一下。

<Example id=calc_rsi/>

```python
from alphalens.plotting import plot_quantile_statistics_table

def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

np.random.seed(78)
bins = np.linspace(0, 100, 11)
alpha, factor = alphatest(2000, start, end, calc_factor=calc_rsi, args=(6,), 
                          top=5, bins=bins, plot_mode='returns')
plot_quantile_statistics_table(factor)
```

这里使用的 alphatest 方法来自<ref>第 7 章</ref>的 [](07.md#calc_rsi)。从现在起，你可以直接在课程中调用它。

跟之前一样，多空组合的累计收益超过了 400%，年化 Alpha 超过 28%。根据 Mean Period Wise Return By Factor Quantile 图表的结果，对资产收益贡献最大的主要是第 1 层和第 2 层（做空），分别贡献 30bps 和 26bps，以及第 5 层有 5bps 的日收益率（做多）。

这三层对应的因子均值可以从 Quantiles Statistics 表格中看到，分别是 7.27, 16.01 和 45（每次运行间，数据可能略有差异）。这里使用的因子是调整后的 RSI，所以，经过换算，实证数据表明，在 A 股 2018 年到 2023 年，RSI 的卖出阈值是 93，买入阈值是 55。注意 Alphalens 计算收益时的交易方法远比技术指标所说的 30 以下买入，70 以上卖出要复杂，因此，这里我们提取的卖出和买入阈值只是对传统方法的类比，实际上我们不能直接按此方法在单资产中实施。

我们看到，调整后的 RSI 能够贡献惊人的收益。显然，并不是所有人都能利用这个因子兑现这里的收益预言。因为它的主要收益来自做空，做空的前提是能够融到券。

那么，我们能否改进 RSI，使得它有可能在做多策略中也能有一席之地呢？我们将在第 12 章探索这个问题。

### Chande Momentum Oscillator (CMO)

CMO 指标由 Tushar Chande[^chande] 提出。它的计算公式是：

$$
 \text{CMO} = 100 \times \frac{\text{Sum of Gains} - \text{Sum of Losses}}{\text{Sum of Gains} + \text{Sum of Losses}} 
$$

显然，它与 RSI 非常接近，只不过 RSI 中使用了移动平均，而 CMO 则没有。我们把这两个指标比较一下：

```python
df = PAYH[-250:].copy()

df['rsi'] = ta.RSI(df.close, 6)
df['cmo'] = ta.CMO(df.close, 6)

ax = df[['rsi','cmo']].plot(figsize=(14,4), secondary_y=['cmo'], style=['-', '.'])
```

你会发现，两者之间相差无几。

### Ultimate Oscillator (UO)

Ultimate Oscillator（终极振荡器）是一种技术分析指标，由 Larry Williams 在 1976 年开发。它旨在通过结合不同时间周期的买入压力来减少虚假信号，从而提供更可靠的超买和超卖信号。Ultimate Oscillator 考虑了三个不同的时间周期，通常为 7 天、14 天和 28 天，以捕捉短期、中期和长期的市场动量。

它的计算方法是：

$$
\text{True Low} = \min(\text{Low}, \text{Previous Close}) \\
\text{True High} = \max(\text{High}, \text{Previous Close}) \\
\text{BP} = \text{Close} - \text{True Low} \\
\text{True Range} = \text{True High} - \text{True Low} \\
\text{Average BP}_n = \frac{\sum_{i=1}^{n} BP_i}{\sum_{i=1}^nTR_i} \\
ULTOSC_t=\frac{4Avg_t(7) + 2Avg_t(14) + Avg_t(28)}{4+2+1} \times 100
$$

我们通过下面的代码来检验这个指标：

```python
df = PAYH.copy()
df['uo'] = ta.ULTOSC(df.high, df.low, df.close, timeperiod1=7, timeperiod2=14, timeperiod3=28)

cols = ['close', 'uo']
ax = df[cols].plot(figsize=(14,4), rot=0, secondary_y='uo', style=['-', '--'])
sns.despine()
plt.tight_layout()
```

由于它是反转指标，因此，当用作因子检验时，我们需要进行方向调整。

```python
_ = alphatest(2000, start, end, calc_factor=lambda x: -1 * ta.ULTOSC(x.high, x.low, x.close, 7, 14, 28))
```

<!-- 笔记：UO因子 高Alpha: 13% -->
这个因子在从 2018 年以来的 6 年间，年化 Alpha 达到了 13%多，显示了终极一词，所言不虚。

### ADX

ADX 由Welles Wilder研发。指标计算比较复杂。它从计算正负方向移动（PLUS_DM/MINUS_DM）开始，再计算正负方向指标，最终得到了 ADX。

$$
\begin{align*}
\text{Up}_t & = P^H_t - P^H_{t-T} \\
\text{Down}_t & = P^L_{t-T} - P^L_t \\
\text{PLUS\_DM}_t & =
\begin{cases}
\text{Up}_t & \text{if } \text{Up}_t > \text{Down}_t \text{ and } \text{Up}_t > 0 \\
0 & \text{otherwise}
\end{cases} \\
\text{MINUS\_DM}_t & = 
\begin{cases}
\text{Down}_t & \text{if } \text{Down}_t > \text{Up}_t \text{ and } \text{Down}_t < 0 \\
0 & \text{otherwise}
\end{cases}
\end{align*}
$$

正负方向指数是 PLUS_DM/MINUS_DM 的移动平均值除以各自的 ATR 得到的。在此基础上，经由下面的公式，就得到了 ADX：

$$
\text{ADX} = 100 \times \text{SMA}(N)_t \left| \frac{\text{PLUS\_DI}_t - \text{MINUS\_DI}_t}{\text{PLUS\_DI}_t + \text{MINUS\_DI}_t} \right|
$$

传统上认为，ADX 有以下指标含义：

| ADX    | 趋势强度                   |
| :----- | :------------------------- |
| 0-25   | 无趋势，横盘整理中         |
| 25-50  | 较强的趋势，可进行趋势交易 |
| 50-75  | 趋势很强                   |
| 75-100 | 趋势极强                   |

```python
df = PAYH.copy()
df['ADX'] = ta.ADX(df.high, df.low, df.close, 14)

ax = df[['close', 'ADX']].plot(figsize=(14, 4), secondary_y='ADX', style=['-', '--'], rot=0)
ax.set_xlabel('')
sns.despine()
plt.tight_layout()

def calc_adx(df, n):
    return ta.ADX(df.high, df.low, df.close, n)

_ = alphatest(2000, start, end, calc_factor=calc_adx, args=(14,))
```

从 alphalens 的测试结果来看，很难直接使用它，这是一个需要调优的因子。

### Moving Average Convergence Divergence (MACD)

MACD 是一种广泛使用的技术分析指标，用于识别趋势的变化和动量。MACD 通过计算两条指数移动平均线（EMA）之间的差异来生成信号。它由三部分组成：

$$
\text{MACD Line} = \text{EMA}{12} - \text{EMA}{26}\\
\text{Signal Line} = \text{EMA}(9, \text{MACD Line})\\
\text{Histogram} = \text{MACD Line} - \text{Signal Line} 
$$

它的计算方法如下：

```python
df = PAYH.copy()

macd, macdsignal, macdhist = ta.MACD(df.close,
                                        fastperiod=12,
                                        slowperiod=26,
                                        signalperiod=9)
df['MACD'] = macd
df['MACDSIG'] = macdsignal
df['MACDHIST'] = macdhist

cols = ["close", "MACD", "MACDSIG", "MACDHIST"]
axes = df[cols].plot(figsize=(14, 8),
               rot=0,
               subplots=True,
                title=cols,
               legend=False)

axes[-1].set_xlabel('')
sns.despine()
plt.tight_layout()
```

从图中看，MACD 与 close 走势很接近，这一点也不奇怪。因为 MACD 就是 close 的一个线性变换。

MACD 的返回值是一个绝对值，它与原始价格相关。因此，MACD 无法直接在不同资产之间进行比较，因此，它不能直接作为因子来使用。

### PPO

PPO（Percentage Price Oscillator）是 APO（Absolute Price Oscillator）的百分比形式。APO 是价格的两个不同滑动窗口（比如 12 天和 26 天）的 EMA 之差。

!!! info
    6, 12, 26 这些魔术数字是交易日历在历史上留下的时代印迹。它反映出每周 5 天工作日制是历史进步的产物。每周工作 6 天，第七天休息，最早反映在《创世纪》中。

但是，在因子分析中，这样的指标是无法使用的，因为不同的资产，它们的 APO 相差很大，也没有可比性。为了在因子分析中能使用 APO 的概念，我们需要使用 PPO。

$$
\text{PPO} = \left( \frac{\text{短期 EMA} - \text{长期 EMA}}{\text{长期 EMA}} \right) \times 100
$$

我们通过下面的代码对 PPO 进行快速检验：

```python
df = PAYH[['high', 'low', 'close']]
df['ppo'] = ta.PPO(df.close, fastperiod=12, slowperiod=26, matype=0)

ax = df[['close', 'ppo']].plot(figsize=(14, 4), secondary_y='ppo', style=['-', '--'], rot=0)
ax.set_xlabel('')
sns.despine()
plt.tight_layout()

def calc_ppo(df, fastperiod, slowperiod, matype):
    return ta.PPO(df.close, fastperiod, slowperiod, matype)

_ = alphatest(2000, start, end, calc_factor=calc_ppo, args=(12, 16, 0))
```

Alphalens 的分析报告表明，较大的 PPO 在一定程度上会成为做空资产的动力。

### Commodity Channel Index(CCI)

CCI 由 Donald Lambert 研发，首次发表于 1980 年的《商品期货》杂志。其公式为：

$$
CCI=\frac{Typical Price - MA}{.015 * Mean Deviation}
$$

其中，

$$
\text{Typical Price}_t=(H_t+L_t+C_t)\div 3 \\
MA = Moving Average \\
Moving Average = (\sum_{i=1}^PTypical Price)\div P \\
Mean Deviation = (\sum_{i=1}^P|Typical Price - MA|)\div P
$$

简单来说，CCI 表示了价格对移动平均线的徧离程度。

!!! tip
    MACD, PPO, CCI 和 BIAS 是一组非常相似的指标，它们的区别主要在于选择的价格序列不同，是否进行了归一化。在本章我们不会介绍 BIAS 指标，这里就顺带提一下。它的公式是：

    $$
    \text{Bias} = \frac{\text{当前价格} - \text{N 日移动平均线}}{\text{N 日移动平均线}} \times 100
    $$

    这个对比给我们提示了创新因子的一个思路。

CCI 使用最高价、最低价和收盘价的平均价作为价格序列的想法，在很多地方都很常见。本质上，它是对 vwap 的一种近似。因此，在有 vwap 数据可用的前提下，直接使用 vwap 数据有可能更好，后者的博弈含义更明确。

CCI 公式当中有一个魔术字，0.15. 它的作用是为了使 CCI 的值标准化到一个合理的范围，并且能在-100和100边界处有信号意义。起初，公式的设计者 lambert 认为，当 CCI 在[-100,100]区间内时，意味着价格在随机波动，是不值得交易的。而只有当 CCI 绝对值超过了 100 时，才认为有趋势出现，即当 CCI 上穿 100 时买入，下穿-100 时卖出。

我们先用一个简单的双轴图观察一下这个指标。

```python
df = PAYH.copy()
df['cci'] = ta.CCI(df.high, df.low, df.close, 14)

axes = df[['close', 'cci']].plot(figsize=(14, 7), subplots=True, title=['PAYH', 'cci'], legend=False)
axes[1].set_xlabel('')
sns.despine()
plt.tight_layout()

_ = alphatest(2000, start, end, calc_factor = lambda x: ta.CCI(x.high, x.low, x.close, 14))
```

看起来效果不是很好。但是，只要对 CCI 的原理略加分析，我们就很容易明白，它不适合直接当成因子来使用。下面，我们就从因子分布的角度来讲一下为什么。

```python
cci = barss.groupby(level="asset").apply(lambda x: ta.CCI(x.high, x.low, x.close, timeperiod=14))
with sns.axes_style('white'):
    sns.distplot(cci)
    sns.despine()
```

我们在前面讲过，如果因子的分布出现双峰，这个因子往往包含了多种因素，它是不纯粹的。现在，我们面临的正是这种情况。在这种情况下，进行因子分析，我们需要先对因子进行“纯化”。

```python
cci = barss.groupby(level="asset").apply(lambda x: ta.CCI(x.high, x.low, x.close, timeperiod=14))
with sns.axes_style('white'):
    sns.distplot(cci[cci> 0])
    sns.despine()
```

现在，我们看到的 cci 的分布就是单峰的了。然后我们对它进行因子检验，看看结果如何：

```python
def calc_cci(df, n):
    cci = ta.CCI(df.high, df.low, df.close, n)
    cci[cci < 0] = np.nan
    return cci * -1
    
alphatest(2000, start, end, calc_factor= calc_cci, args=(14,), max_loss=0.55)
```

<!-- 笔记：CCI 高Alpha 19%-->

Alpha 达到了年化 19%。而且这个因子呈现比较好的正向单调性。这也许是 CCI 如此受人推崇的原因之一。

### Williams' R

Williams' R（Williams Range R）由 Larry R. William[^larry] 发表于 1973 年。

它的计算公式是：

$$
\text{W\%R} = \frac{C_0 - H_n}{H_n - L_n} \times 100\%
$$

William's R 的计算及因子测试代码如下：

```python
df = PAYH.copy()
df['wr'] = ta.WILLR(df.high, df.low, df.close, 14)

ax = df[['close', 'wr']].plot(figsize=(14, 4), secondary_y='wr', style=['-', '--'], rot=0)
ax.set_xlabel('')
sns.despine()
plt.tight_layout()

_ = alphatest(2000, start, end, calc_factor=lambda df: ta.WILLR(df.high, df.low, df.close, 14))
```

Williams' R 的取值范围是-100 到 0，也有一些人在使用时，将 William's R 乘以-1，以符合上扬为超买，下跌为超卖的常识。Talib 中的 WILLR 函数使用的是原本的公式，因此返回值在-100 到 0 之间。一般认为，当 wr 小于-80 时，市场处于超卖状态；当 wr 大于-20 时，市场处于超买状态。因此，当我们使用 wr 作为因子时，需要将 wr 乘以-1，以满足正单调性要求。

当我们把 wr 的负数作为因子时，实际上我们使用的指标是：

$$
\text{W\%R} = \frac{ C_0 - H_n}{H_n - L_n} \times 100\%
$$

这个公式的含义是，当前价格在多大程度上，接近 n 日以来的最高点？当公式取值为 100 时，意味着当前股价是创 n 日新高的，或者说是形成了突破的。这是 wr 的交易含义。

大家可以尝试一下，使用不同的 n 值进行回测，看看结果如何。

### Balance of Power

Balance of Power (BOP) 指标与 William's R 非常相似，它是通过比较收盘价与当日价格区间的相对位置来评估市场上的买方和卖方力量。如果将 WR 中的 n 取值为 1，那么，BOP 与 WR 的差别就仅仅在于分子，一个计算的是收盘价与最高价的差，另一个计算的是开盘价与收盘价的差值。

$$
\text{BOP} = \frac{\text{Close} - \text{Open}}{\text{High} - \text{Low}}
$$

BOP 的计算及因子测试代码如下：

```python
df = PAYH.copy()
df['bop'] = ta.BOP(df.open, df.high, df.low, df.close)
ax = df[['close', 'bop']].plot(figsize=(14,7), rot=0, style=['-', '--'], secondary_y='bop')
sns.despine()
plt.tight_layout()

_ = alphatest(2000, start, end, calc_factor=lambda df: ta.BOP(df.open, df.high, df.low, df.close))
```

BOP 指标的输出是一个介于-1 到 1 之间的实数。当取值为 1 时，它表明当天收了一个光头阳线；当取值为-1 时，表明当天收了一个光头阴线。其它情况则是分别有上下影线的情况。因此，BOP 能较好地反映当天多空力量对比。

要将其作为因子测试，需要对其进行单调性调整，或者线性调整。

### Aroon

Aroon[^aroon] 震荡器由两个基础指标， Aroon 上行、Aroon 下行指标计算而来。

Aroon 的计算公式如下：

$$
Aroon Up = \frac{n - \text{(Periods Since n Period High)}}{n} \times 100 \% \\
Aroon Down = \frac{n - \text{(Periods since n Period Low)}}{n} \times 100 \% \\
Aroon Oscillator = \frac{\text{Aroon Up}}{\text{Aroon Down}}
$$

如果使用 argmax 函数，可能更容易理解这个公式：

$$
Aroon Up = \frac{n - argmax(High, n)}{n} \times 100 \% \\
Aroon Down = \frac{n - argmin(Low, n)}{n} \times 100 \% \\
Aroon Oscillator = \frac{\text{Aroon Up}}{\text{Aroon Down}}
$$

Aroon 振荡器读数高于零表示存在上涨趋势，而低于零的读数表示存在下跌趋势。交易者关注零线穿越，以信号潜在的趋势变化。他们还关注大幅变动，当读数高于 50 或低于-50 时，表示价格变动强烈。

一般情况下，n 取值为 25。

我们通过 talib 的 AROONOSC 函数来计算这个指标：

```python
df = PAYH.copy()

df["aroon"] = ta.AROONOSC(high=df.high, low=df.low, timeperiod=14)
ax = df[['close', 'aroon']].plot(figsize=(14,4), rot=0, style=['-', '--'], secondary_y='aroon')
ax.set_xlabel('')
sns.despine()
plt.tight_layout();

def calc_aroon(df, n):
    return -1 * ta.AROONOSC(high=df.high, low=df.low, timeperiod=n)

_ = alphatest(2000, start, end, calc_factor=calc_aroon, args=(14,), bins=5)
```

!!! attention
    注意这里我们要使用 bins 分层，而不能使用 quantiles 分层。

从 Alphatest 的结果来看，Aroon 是一个可用的指标，达到了设计预期。作为因子使用时，还可以进行分层细化和线性调整。如果是纯多的策略，要关注零线穿越，在这个点上，Aroon 指标某种程度上，确实有黎明的曙光的含义。

### Money Flow Index (MFI)

MFI 结合了价格和成交量的信息，使其成为一种更全面的动量指标。MFI 的取值范围在 0 到 100 之间，可以帮助交易者识别市场趋势的变化和潜在的反转信号。

它的计算方法由以下公式依次决定：

$$
\text{Typical Price} = \frac{\text{High} + \text{Low} + \text{Close}}{3}
$$

$$
\text{Money Flow} = \text{Typical Price} \times \text{Volume}
$$

$$
\text{Money Ratio} = \frac{\text{Positive Money Flow}}{\text{Negative Money Flow}}
$$

$$
 \text{MFI} = 100 - \frac{100}{1 + \text{Money Ratio}}
$$

最后一个公式是一种归一化技巧，请大家注意一下。

一般认为，当 MFI 超过 80 时，市场处于超买状态；当 MFI 低于 20 时，市场处于超卖状态状态。

下面的代码演示了如何计算 MFI 及因子检验：

```python
df = PAYH.copy()

df["mfi"] = ta.MFI(high=df.high, 
                   low=df.low, 
                   close=df.close, 
                   volume=df.volume, 
                   timeperiod=14)

cols = ['close', 'volume']
axes = df[cols].plot(figsize=(14,4), rot=0, subplots=True, title=cols)
df['mfi'].plot(ax=axes[0], secondary_y=True, color='orange', legend='mfi')
axes[-1].set_xlabel('')
sns.despine()
plt.tight_layout();

_ = alphatest(2000, start, end, calc_factor=lambda x: ta.MFI(x.high,x.low,x.close,volume=x.volume, timeperiod=14))
```

在这个指标中，timeperiod 是一个值得调整的参数。不同的市场，资本的耐心程度不一样。

### Stochastic (STOCH)

Stochastic（随机震荡指标）是由 George Lane[^lane] 在 1950 年代开发的。它由以下公式计算：

$$
K = \left( \frac{C_0 - \text{L}{n}}{\text{H}{n} - \text{L}_{n}} \right) \times 100\% \\
D = \text{SMA}(K, m) \times 100\%
$$

这样求出来的指标称为 K，它的移动均值称为 D。K 和 D 的取值区间为 [0,100]。越接近 100，表明上涨趋势越强烈，但可能超买；越接近 0，表示下跌趋势越强烈，可能超卖。接近 50 时，表明市场动量较弱，没有明确的方向。

<!-- 后文：指标如何创新？

这里 K 值的计算，实际上就是 Willian's R 的另一个版本。在 WR 中，使用的是 c - h
-->

STOCK 则由 K 和 D 计算而来。

$$
STOCH = \frac{D}{K}
$$

计算中，有两组时间参数。在计算 K 时的参数 n 被称为快线参数（尽管实际上常常取值 14，比慢线参数要大），计算 D 时的参数常常被称为慢线参数（通常取值 3）。

!!! tip
    我们通过 talib 的 STOCK 方法来计算随机震荡指标，但该函数并不直接返回 stoch 指标，而是返回它的 K 值和 D 值。真正意义上的 stock，需要我们自己计算。

```python
df = PAYH.copy()
slowk, slowd = ta.STOCH(df.high, df.low, df.close)
df['stoch'] = slowd/slowk
ax = df[['close', 'stoch']].plot(figsize=(14,4), rot=0, style=['-', '--'], secondary_y='stoch')
ax.set_xlabel('')
sns.despine()
plt.tight_layout()
```

我们通过下面的代码来进行因子检验：

```python
def calc_stoch(df, n, m):
    k, d = ta.STOCH(df.high, df.low, df.close, fastk_period=n, slowk_period=m)
    return d/k

_ = alphatest(2000, start, end, calc_factor=calc_stoch, args=(14, 3))
```

!!! tip
    在从 2021 年起的 3 年里，随机震荡指标给出了令人难以置信的结果。从分层均值收益图来看，做空的收益虽然高，但并不完全依赖于做空。这个指标值得进一步研究。

<!-- 笔记：指标
def calc_stoch(df, n, m):
    k, d = ta.STOCH(df.high, df.low, df.close, fastk_period=n, slowk_period=m)
    return d/k

start = datetime.date(2021, 1,1)
_ = alphatest(2000, start, end, calc_factor=calc_stoch, args=(14, 3), long_short=False, top=8)

年化 alpha 达到了 66.9%
-->

### Sine Wave

Sine Wave[^sinewave]是John Ehlers开发的一个有交易信号的指标。它的作者介绍，Sine Wave指标相比其他振荡器（如RSI和随机指标）具有一项优势，因为它能够预测而不是等待确认。这假设所测量的相位在过去至少短暂存在，并且将在未来至少短暂持续。 当市场处于趋势模式时，相位会停滞不前，甚至可能有负的变化率。该指标在周期转折点前1/16个周期周期提前给出入场和离场信号，并且在市场处于趋势模式时很少给出错误的锯齿信号。

```python
def super_smoother(data, length):
    """Python implementation of the Super Smoother indicator created by John Ehlers
    """
    ssf = []
    for i, _ in enumerate(data):
        if i < 2:
            ssf.append(0)
        else:
            arg = 1.414 * 3.14159 / length
            a_1 = math.exp(-arg)
            b_1 = 2 * a_1 * math.cos(4.44/float(length))
            c_2 = b_1
            c_3 = -a_1 * a_1
            c_1 = 1 - c_2 - c_3
            ssf.append(c_1 * (data[i] + data[i-1]) / 2 + c_2 * ssf[i-1] + c_3 * ssf[i-2])
    return ssf
    
def ebsw(df, hp_length, ssf_length):
    """Python implementation of Even Better Sine Wave indicator created by John Ehlers
    https://github.com/aticio/legitindicators/blob/main/legitindicators.py
    """
    pi = 3.14159
    alpha1 = (1 - math.sin(2 * pi / hp_length)) / math.cos(2 * pi / hp_length)

    hpf = []

    data = df.close
    for i, _ in enumerate(data):
        if i < hp_length:
            hpf.append(0)
        else:
            hpf.append((0.5 * (1 + alpha1) * (data[i] - data[i - 1])) + (alpha1 * hpf[i - 1]))

    ssf = super_smoother(hpf, ssf_length)

    wave = []
    for i, _ in enumerate(data):
        if i < ssf_length:
            wave.append(0)
        else:
            w = (ssf[i] + ssf[i - 1] + ssf[i - 2]) / 3
            p = (pow(ssf[i], 2) + pow(ssf[i - 1], 2) + pow(ssf[i - 2], 2)) / 3
            if p == 0:
                wave.append(0)
            else:
                wave.append(w / math.sqrt(p))

    factor = pd.DataFrame(wave, index=df.index)
    if getattr(df, 'name', None):
        factor.name = df.name
    return factor * -1

import math
df = PAYH.copy()
df['ebsw'] = ebsw(df, 40, 10)
df[['close', 'ebsw']].plot(figsize=(14,7), secondary_y=["ebsw"])
```

Sine Wave的分布接近二值分布：

```python
sw = barss.groupby(level="asset").apply(lambda x: ebsw(x, 40, 10))
with sns.axes_style('white'):
    sns.distplot(sw)
    sns.despine()
```

我们可以使用 bins = 2的参数来进行因子检验：

```python
_ = alphatest(2000, start, end, calc_factor=ebsw, args=(40, 10), bins=2)
```

经修正后，我们可以得到年化Alpha在3%左右。年化Alpha较低的原因在于，在趋势行情中，sine wave将没有信号。大家也可以自行探索和拓展这个因子的用法。

从经济学原理上来讲，在市场的证券种类保持不变的前提下，一个成功的经济体，市场的资产总值要反应每年创造财富的增加值，因此，它应该有一个恒定的增长率，表现为一条斜向上的直线；同时经济存在周期、投资者对市场的投机也会强化周期，因此市场的资产总值也会呈现周期波动效应。两者叠加起来，就形成螺旋式上升、迂回式前进的格局。

![来自mesasoftware](https://images.jieyu.ai/images/2024/10/john-ehlers-trendlimit.gif)

从这个意义上来说，任何市场的价格波动，理论上都可以分解为直线（而不是直流分量）与一系列正弦波的叠加。

Sine Wave指标只是在这个方向上，迈出了第一步而已。

## 成交量指标

我们前面介绍的技术指标主要以价格数据为基础进行计算。我们也看到，仅仅基于价格信息进行线性变换，最终会导致技术指标彼此具有较强的互相关性，比如 RSI 和 CMO 就是如此。

技术分析有量、价、时、空四大维度。成交量数据则从另一个维度提供了新的信息。在成交量指标中，比较常用的就是换手率、Chaikin A/D Line（柴金累积/派发线）和 OBV。成交量数据一般不能直接用来交易，需要结合其他指标。多因子结合也正是机器学习擅长的地方。

### Chaikin A/D Line

Chaikin A/D Line（柴金累积/派发线）是一种技术分析指标，用于衡量资金流入和流出市场的程度。该指标由 Marc Chaikin[^chaikin] 开发，基于价格和成交量的关系，帮助交易者识别市场趋势和潜在的反转点。

Chaikin A/D Line 的计算公式为：

$$
MFM = \frac{(\text{Close} - \text{Low}) - (\text{High} - \text{Close})}{\text{High} - \text{Low}}\\
MFV = MFM\times \text{Volume} \\
AD_t = AD_{t-1} + MFV_t
$$

这里的 MFM 是 Money Flow Multiplier, Money Flow Volume（MFV）是 MFM 与成交量的乘积，AD 则是 Chaikin A/D。

显然，它也是 vwap 的一种衍生形式。

我们通过下面的代码来演示它的一些特性。

```python
df = PAYH.copy()

df['ad'] = ta.AD(df.high, df.low, df.close, df.volume)
cols = ['close', 'ad']
axes = df[cols].plot(figsize=(14,4), rot=0, subplots=True, title=cols)
axes[-1].set_label('')
sns.despine()
plt.tight_layout()

```

### On Balance Volume (OBV)

<!--后文：第12章，因子创新，使用最大成交量方向来设计一个因子-->
OBV[^granville](On-Balance Volume，平衡交易量)是一种技术分析指标，用于衡量资金流入和流出市场的程度。OBV 通过将成交量与价格变化相结合，帮助交易者识别市场趋势和潜在的反转点。OBV 指标由 Joseph Granville 在 1963 年提出。

它的计算方法是：

$$
\text{OBV}_t = 
\begin{cases}
\text{OBV}_{t-1}+V_t & \text{if }P_t>P_{t-1}\\
\text{OBV}_{t-1}-V_t & \text{if }P_t<P_{t-1}\\
\text{OBV}_{t-1} & \text{otherwise}
\end{cases}
$$

它的初始值一般会设为 0.

我们通过下面的代码来演示它的一些特性。

```python
df = PAYH.copy()

df['obv'] = ta.OBV(df.close, df.volume)
cols = ['close', 'obv']
ax = df[cols].plot(figsize=(14,4), rot=0, secondary_y="obv")
ax.set_xlabel('')
sns.despine()
plt.tight_layout()
```

OBV 取值范围不固定，因此，也无法在不同的资产之间进行比较，从而无法进行因子分析。我们可以尝试一些归一化方法，来产生它的衍生指标，从而使得它们之间具有可比较性。这其中最合理的归一化方法，可能是先将成交量转换为换手率指标。在没有换手率可用的情况下，也可以用成交量的移动平均作为分母来进行归一化。

## 波动率指标

最常用的波动率指标是每日收益的滑动波动率。在 talib 中，还存一个名为 ATR 的指标，被称为真实波动率。它是由 Welles Wilder 提出的。

真实波动率主要用于评估价格变动的幅度。它通过计算一段时间内真实波幅的平均值来反映市场的波动程度。ATR 是一个辅助性的指标，在交易中非常实用，特别是在风控中。

它的计算方式是：

$$
\text{True Range} = \max(H - L, |H - C_{-1}|, |C_{-1} - L|)\\
ATR = SMA(TR, n)
$$

这里$C_{-1}$是前一天的收盘价，n 是参数，常设为 14。

我们通过下面的代码来演示它的一些特性。

```python
df = PAYH.copy()
df['atr'] = ta.ATR(df.high, df.low, df.close)
cols = ['close', 'atr']
axes = df[cols].plot(figsize=(14,7), rot=0, secondary_y="atr")
sns.despine()
plt.tight_layout()
```

在 ATR 的基础上，人们提出了归一化的 ATR，即 NATR：

$$
NATR_t = \frac{ATR_t(T)}{C_t} \times 100
$$

我们可以使用 NATR 指标进行因子检验：

!!! attention
    注意代码中的方向调整

```python
_ = alphatest(2000, start, end, calc_factor=lambda x: -1 * ta.NATR(x.high, x.low, x.close, 14))
```

<!-- 笔记：指标-->
结果表明，在从 2018 年以来的 6 年时间里，年代 Alpha 达到了 6%，不过，相当一部分收益由做空贡献。

## Overlap类指标

这一类的指标的特点是不预测趋势，但预测价格的上下界，因此被称为 Overlap。

### Bollinger Bands

这是John Bollinger[^bollinger]发明的一个技术指标，有着比较坚实的统计学基础。它是以收盘价的简单移动平均，以及两条标准差线组成的。

它的计算公式无须多言，我们直接来看它的特性：

```python
df = PAYH.copy()
s = ta.BBANDS(df.close, timeperiod=2, matype=1)
cols = ['close', 'upper', 'middle', 'lower']

df = df.assign(**dict(zip(cols[1:], s)))
ax = df[-50:][cols].plot(figsize=(14,7), rot=0, lw=1)

ax.set_xlabel('')
sns.despine()
plt.tight_layout()
```

Bollinger Bands还有两个参数，nbdevup和nbdevdn，但一般我们只会使用2，即两个标准差。如果资产价格波动服从正态分布，那么，价格就会以95%的概率，出现在过去20天均值的2个标准差以内。

我们无法直接对这个指标进行因子分析。如果要进行因子分析，我们应该使用z-score分数，实际上与bollinger bands是等价的，只不过表述方式不同而已。

下面，我们就给出z-score因子的因子检验：

```python
def calc_zscore(df, n=20):
    ma = df.close.rolling(n).mean()
    return (df.close - ma) / df.close.rolling(n).std()

zscore = barss.groupby(level="asset").apply(lambda x: calc_zscore(x, 20))
with sns.axes_style('white'):
    sns.distplot(zscore)
    sns.despine()
```

它的分布也会出现双峰。如果我们对因子进行纯化，再进行因子检验，就能得到比较好的结果：

```python
def minus_zscore(df, n=20):
    ma = df.close.rolling(n).mean()
    zscore = (df.close - ma) / df.close.rolling(n).std()
    zscore[zscore > 0] = np.nan
    return zscore

_ = alphatest(2000, start, end, calc_factor=minus_zscore, args=(20,), max_loss=0.5)
```

<!--笔记： 高Alpha 15.3% -->
在2018年到2023年6年间，它的年化alpha达到了15.3%。它的分层收益均值图也很完美。

如果因子取正值部分，因子检验的结果也表现出良好的特性。当然，我们应该在因子值大于3的部分选择做空，这才符合Bollinger Bands的原理。如果不能做空，那也说明，如果我们有持仓，到了这种情况，就必须卖出。

对个股而言，正态性分布条件比较难以满足。但指数分布一般符合广义双曲分布，这是一种近似正态的分布。

### Parabolic SAR

抛物线转向指标由Welles Wilder[^wilder]在1978年提出。因该系统中，停损价位的轨迹类似抛物线，故得名。抛物线转向指标的思想类似于期权理论中的时间衰减概念，这一概念基于“时间是敌人”的想法。因此，除非一种安全资产能够持续产生更多的利润，否则它应该被清算。

该指标的计算公式如下：

$$\text{SAR}_t=\text{SAR}_{t-1} + \alpha (\text{EP}-\text{SAR}_{t-1})$$

这里的EP是Extreme Point的意思。EP（极端点）是在每次趋势期间保存的记录，代表当前上涨趋势中价格达到的最高值——或下跌趋势中的最低值。在每个时期，如果观察到新的最大值（或最小值），则使用该值更新 EP。

α值代表加速度因子。通常，这个初始设置为 0.02，但可以由交易者选择。这个因子每次记录到新的 EP 时增加 0.02，这意味着每次观察到新的 EP，都会使加速度因子上升。然后，加速度会加快，直到 SAR 接近价格。为了防止它变得太大，通常会设置加速度因子的最大值为 0.20。交易者可以根据他们的交易风格和交易的工具来设置这些数字。一般来说，在股票交易中，将加速度因子设置为 0.01 更为理想，这样它就不会对局部下降过于敏感。对于商品或货币交易，首选的值是 0.02。

```python
df = PAYH.copy()
df['sar'] = ta.SAR(df.high, df.low, acceleration=0.02, maximum=0.2)
cols = ['close', 'sar']
axes = df[cols].plot(figsize=(14,7), rot=0, secondary_y="sar")
sns.despine()
plt.tight_layout()
```

从图中可以看出，它的取值跟收盘价接近，因此，无法在不同资产之间进行比较，也不能用于因子分析。下面，我们就介绍如何将其标准化。

```python
def calc_nsar(df, accl=0.02, maximum=0.2):
    sar = ta.SAR(df.high, 
                    df.low,
                    acceleration=accl,
                    maximum=maximum)
    return sar/df.close - 1

nsar = barss.groupby(level="asset").apply(lambda x: calc_nsar(x))
with sns.axes_style('white'):
    sns.distplot(nsar)
    sns.despine()
```

这样得到的因子分布也是双峰的。我们取大于零的部分进行检验：

```python
def calc_nsar(df, accl=0.02, maximum=0.2):
    sar = ta.SAR(df.high, 
                    df.low,
                    acceleration=accl,
                    maximum=maximum)
    nsar = sar/df.close - 1
    nsar[nsar > 0] = np.nan
    return -1 * nsar

_ = alphatest(2000, start, end, calc_factor=calc_nsar, max_loss=0.55)
```

得到的年化Alpha在14%左右。不过，收益主要来自做空。如果取小于零的部分进行检验，年化收益在8%左右。

## 移动平均线

移动平均线属于技术指标预处理的一部分，用来平滑、过滤噪声，它本身不能直接作为因子。

TA-Lib 中提供了很多移动平均线，包括：

* Simple Moving Average (SMA)
* Exponential Moving Average (EMA)
* Weighted Moving Average (WMA)
* Double Exponential Moving Average (DEMA)
* Triple Exponential Moving Average (TEMA)
* Triangle Exponential Moving Average (TRIMA)
* Kaufman Adaptive Moving Average (KAMA)
* MESA Adaptive Moving Average (MAMA)

Trima是对SMA的再次SMA。MESA是John Ehlers[^ehlers]开发的一个移动平均线。在计算中，使用了Hilbert Transform和Instantaneous Trendline。

Hilbert Transform 是一种信号处理技术，用于分析时间序列数据，特别是在金融市场的技术分析中。它通过将原始信号转换为解析信号来提取信号的瞬时相位和瞬时频率。

Instantaneous Trendline[^ehlers]是Hilbert Transform的一个应用，用于生成一个平滑的趋势线，它和EMA一样平滑，但能得到近似于零的延迟。

```python
df = PAYH.copy()
df['ht'] = ta.HT_TRENDLINE(df.close)
ax = df[['close', 'ht']].plot(figsize=(14,7), rot=0)
ax.set_xlabel('')
sns.despine()
plt.tight_layout()
```

除此之外，还存在其它类型的移动平均方法，比如 Hull Moving Average等等。在所有的移动平均方法中，SMA是最忠实于原始数据，保留了最多博弈信号的移动平均方法。EMA在很多技术指标中有使用，MAMA也得到了很多人的推崇。

下面，我们就演示一下所有这些移动均线。

```python
def plot_ma(names, win, *args):
    df = PAYH.copy()[-120:]
    for name in names:
        if name in ("MAMA", "FAMA"):
            mama, fama = ta.MAMA(df.close, fastlimit = args[0], slowlimit=args[1])
            df["MAMA"] = mama
            df["FAMA"] = fama
            continue
        df[name] = getattr(ta, name)(df.close, win)

    cols = ['close', *names]
    ax = df[cols].plot(figsize=(14,7), rot=0)
    sns.despine()
    ax.set_xlabel('')
    plt.tight_layout()


plot_ma(["SMA", "WMA", "TRIMA"], 10)
plot_ma(["EMA", "DEMA", "TEMA"], 10)
plot_ma(["KAMA", "MAMA", "FAMA"], 10, .5, .05)
```

在观察移动均线时，我们主要看两点，第一，它的平滑程度；第二，它的延迟程度。



## Footnotes


[^mt]: Commodities 是一本美印刷的投资月刊。该刊物于 1972 年成立，当时名为《Commodities》。1983 年 9 月更名为《Futures》，2015 年更名为《Modern Trader》。该杂志是期货和期权交易的标准来源，其 SourceBook 网站是美国经纪和相关服务的标准参考资料。
[^talib]: TA-Lib 最早发布于 2001 年。尽管它的大部分功能早已实现和冻结开发，但 TA-Lib 社区仍然在积极开发中。他们最新的动作是将之前依托 SourceForge 管理的项目迁移到了 GitHub，并且使用 Mkdocs -- 这个 Python 项目常见的项目文档构建工具构建了自己的官网。可以预见，在今后一段时间，TA-Lib 的开发将会更活跃，版本 0.5 已在规划之中。一些新的技术指标，比如 Awesome Oscillator 等，可能被加入到新的版本之中。
[^aroon]: Aroon 震荡器是由 Tushar Chande 在 1995 年作为 Aroon 指标系统的一部分开发的。Aroon 这个词来源于梵文，大概意思是“黎明的曙光”。
[^chande]: Tushar Chande 是一名技术分析师，创立了 Aroon，StochRSI，CMO 和 VIDYA 等技术指标。你可以在 [Top Traders Unplugged](https://www.toptradersunplugged.com/guests/tushar-chande/) 上看到他的专访。
[^lane]: George Lane 是证券分析师，作家，教育家。担任过伊利诺伊州“投资教育者公司”的总裁。
[^larry]: Williams Larry, 作家、股票和商品交易者，技术分析师。他一生出版 11 本书，大部分关于股本和商品交易。他创建了许多技术指标，包括 William's R, Ultimate Oscillator 等等，发表于 1973 年出版的《How I made one million dollars last year trading commodities》中。他曾在 1987 年的世界期货交易冠军赛中获得第一名，在 12 个月中，回报达到 11.37 倍。十年后，他的女儿，女演员米歇尔也在同一赛事中获胜（10 倍收益）。他的儿子则出版过一本题为《交易中的心理优势》的书。
[^chaikin]: Marc Chaikin，美国股票分析师，也是 Chiakin Analytics 的创始人。他还创立了 Bomar 证券公司，最终被 Reuters.com 收购，成为该公司高级副总裁和董事。
[^granville]: Joe Granville 于 1960 年代提出这个指标。Joe Granville 是金融作家和演说家，从 1963 年起，出版了《The Granville Market Letter》。到 1982 年，该杂志年收入超过 600 万美元。
[^bollinger]: John Bollinger，交易员、分析师和作家。早年曾在Salomon Brothers等金融机构工作。1980年代发布Bollinger Bands，曾成为当时最优秀的策略之一，并以此成立了自己的投资公司。
[^wilder]: Welles Wilder是著名的技术分析大师，被称为ADX, RSI, ATR和SAR等指标之父。他出身机械工业专业，做过是房地产开发商，最后创立了Trend Research公司。他在1978年出版了《New Concepts in Technical Trading Systems》一书。
[^ehlers]: Hilbert Transform最初由 John Ehlers应用到交易分析当中。他发明了 Hilbert Trendline和Hilber Sine Wave。这些概念在他的著作《Rocket Science For Traders》中首次介绍。他创办了[mesasoftware](https://mesasoftware.com/)
[^sinewave]: 更多关于Sine Wave的介绍，可以见[这篇文章](https://emini-watch.com/trading-indicators/sine-wave-indicator/)

<!-- 

TAlib 缺少 kdj 指标，https://github.com/TA-Lib/ta-lib-python/issues/99

```
def KDJ(H, L, C):
    L5 = pd.rolling_min(L, 9)
    H5 = pd.rolling_max(H, 9)
    RSV = 100 * ((C - L5) / (H5 - L5)).values

    k0 = 50
    k_out = []
    for j in range(len(RSV)):
        if RSV[j] == RSV[j]: # check for nan
            k0 = 1/3 * RSV[j] + 2/3 * k0
            k_out.append(k0)
        else:
            k_out.append(np.nan)
    
    d0 = 50
    d_out = []
    for j in range(len(RSV)):
        if k_out[j] == k_out[j]:
            d0 =1/3 * k_out[j] + 2/3 * d0
            d_out.append(d0)
        else:
            d_out.append(np.nan)
    
    J = (3 * np.array(k_out)) - (2 * np.array(d_out))
    return pd.Series(J,  H.index)
```
-->
