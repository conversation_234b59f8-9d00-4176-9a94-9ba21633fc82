# LightGBM

!!! attention
    如果运行部分单元格时，提示需要安装graphviz，可以在单元格中运行以下命令安装：
    ```bash
    ! apt update -y
    ! apt install graphviz -y

    如果提示有关LightGBM问题，请安装
    !pip install "LightGBM==4.5.0"
    ```


## 1. 决策是如何形成的

决策树是机器学习领域中一种直观且基础的模型，其核心思想是模仿人类做决策的过程。它基于给定的数据集，针对数据所具有的各类特征，构建出一个类似树形的结构。从根节点开始，依据特征的不同取值，不断进行分支划分，每一个分支节点代表着对一个特征的测试，最终的叶节点则对应着决策结果，也就是分类标签（用于分类任务）或预测值（用于回归任务）。

在<ref>[机器学习概述](13.ipynb)</ref>中，我们介绍人工智能的本质时，已经接触过决策树模型。我们演示了决策树模型是如何依据天气和气温，将决策划分为各个分支，最终做出决定的。这是一个分类任务。同样，在回归任务里，比如预测房价，可依据房屋面积、房龄、周边配套设施等特征构建决策树，房价预测值将由叶节点给出。

决策树构建的关键步骤是节点的分裂。在<ref>[机器学习概述](13.ipynb)</ref>里，我们对这一过程中涉及的概念和过程讲得非常简单，现在，我们就来补齐这一部分。

### 1.1. 决策树的构建原理

我们以分类任务为例。初始状态下，我们拿到的训练集，数据是混乱的。它的混乱度可以用熵的概念来描述。

熵原本是一个物理学（热力学）概念，用于描述系统的无序程度或混乱度。克劳德。香农建立信息论时，就把这个概念引入进来，来量化信息的不确定性。

由于熵本来就有表征混乱度的含义，所以，在机器学习中，人们也就很自然地借用这一概念，来表示数据集的混乱程度。换句话说，在一个分类任务中，如果训练集中的样本均匀分布在各个类别上，那么这个数据集就是无序和混乱的，它的熵值较高；反之，如果大部分样本集中在某一类或少数几类，表示数据较为有序，熵值较低，这正好就与原本的物理学概念对应了起来。

对于一个离散型随机变量$X$，其可能取值为 $( x_1, x_2, \ldots, x_n )$，每个取值的概率分别为 $( P(x_1), P(x_2), \ldots, P(x_n) )$，则信息熵 $H(X) $ 定义为：

$$
H(X) = -\sum_{i=1}^{n} P(x_i) \log_b P(x_i)
$$

其中：

$P(x_i)$是随机变量 $X$ 取值为 $ x_i $ 的概率。$\log_b$是以 $b$ 为底的对数，通常使用自然对数$ \ln$或以 2 为底的对数$ \log_2$。在机器学习中，常用 $\log_2 $，这样熵的单位就成了比特（bit）。

基于信息熵的概念，决策树就可以计算前后两次划分之间的信息增益 (gain)。在构建决策树时，我们希望每次划分都能降低信息熵，让数据逐渐变得有序。比如在一个判断邮件是否为垃圾邮件的数据集里，初始时邮件类别（垃圾、正常）混杂，信息熵较高。如果依据邮件里的某个关键词进行划分后，垃圾邮件和正常邮件能更明确地区分，那么这个关键词就有较大的信息增益。

在<ref>[机器学习概述](13.ipynb)</ref>中，决策树使用的是按 gini 系数划分的方法。gini 系数在计算量上会比信息熵略小一些，因为它不需要计算对数。它的定义是这样的：

$$ Gini(D) = 1 - \sum_{i=1}^{k} P_i^2 $$

其中：

$ P_i $ 是类别 $i $ 在数据集 $D$ 中出现的概率。

我们可以这样理解基尼系数：它主要反映从数据集中随机抽取两个样本，二者类别不一致的概率。基尼系数越小，表明数据纯度越高，即同一类别的样本占比越大。比如，当数据集划分到某个叶节点时，此时只有第 i 类样本，因此$P_i$为 1，其它样本的概念都为零。所以，最后计算出的 gini 系数也最小，划分就结束了。

### 1.2. 决策树的优缺点

决策树的结构直观清晰，就像一个流程图，非专业人士也能轻松看懂决策过程，可以处理复杂的非线性因素（比如股价走势），即使数据集中有缺失值和不同量纲的数据也能很好处理，且可视化效果好，非常直观，见下图：

![](https://images.jieyu.ai/images/2025/01/iris-decision-tree-plot.jpg)

但决策树也有较明显的缺点，主要是：
1. 容易过拟合：由于决策树会尽量细分数据，使得每个叶节点的样本尽可能纯，这就容易导致模型过于适应训练数据，对新数据的泛化能力变差。例如在一个小样本的水果分类数据集中，如果过度依据水果表面的细微纹理特征构建决策树，可能在面对新的水果样本时，因这些细微特征的差异而无法准确分类。
2. 对异常值敏感：决策树在构建时，会考虑每一个样本，因此，个别极端的异常值可能会极大地影响决策树的构建路径。比如在一个以身高、体重判断健康状况的数据集里，若混入一个极大的身高值（可能是数据录入错误），可能会使决策树在依据身高特征划分时出现偏差，进而影响整个模型的准确性。
3. 单棵决策树的预测能力有限：仅靠一棵决策树往往难以达到很高的预测准确率，尤其在复杂的现实场景中，数据的特征和关系错综复杂，单棵树很难全面、精准地捕捉这些信息，所以后续需要引入集成学习等方法来提升性能。
4. 无法捕捉时间依赖性，难以处理季节性和周期性模式，这些困难不仅在决策树模型中存在，在它的各种变体，比如随机森林和梯度提升树中同样存在。这需要我们通过特征工程来加以弥补。

## 2. 决策树的改进 - 梯度提升算法

单棵决策树存在容易过拟合和不稳定性等缺点，于是，人们就想到了并行训练多棵决策树，并对每个决策树的预测结果取平均值（回归任务）或投票（分类任务），来构建一个强学习器的方法。这种方法，就称为随机森林。

然而，随机森林在解决一部分问题的同时，也引入了新的问题，即训练速度较慢，资源占用更大（特别是在特征数量或者数据量较大时）。另外，每一棵树之间是独立的，一方面使得整个模型的可解释性变得复杂；另一方面，也会加重类别不平衡问题。在类别不平衡的情况下，即使有少数几棵树正确地识别了样本，最终也会在投票机制中，被多数决策树的结果所主导。这是随机森林并行机制所固有的问题。

于是，人们又提出了梯度提升决策树。它通过迭代地训练弱一系列学习器（通常是决策树），并以梯度下降的方式不断优化损失函数，来构建一个强学习器。GBDT[^GBDT] 在许多机器学习任务中表现出色，尤其是在结构化数据的分类和回归问题上。

它的基本思想是：

1. 初始化模型：首先，用一个简单的模型（如常数）作为初始预测值。
2. 计算负梯度：对于每一轮迭代，计算当前模型在训练数据上的残差（即真实值与预测值之间的差异）。这些残 差可以看作是损失函数关于当前预测值的负梯度。
3. 拟合弱学习器：训练一个新的弱学习器（通常是决策树），使其尽可能好地拟合这些负梯度。
4. 更新模型：将新训练的弱学习器加入到现有模型中，调整其权重（通常由学习率控制），从而更新整体模型的预测值。
5. 重复迭代：重复上述步骤，直到达到预设的最大迭代次数或满足其他停止条件。

以『串行』方式将多个决策树联结起来，通过逐步优化损失函数，GBDT 能够捕捉数据中的复杂模式，从而获得较高的预测准确性。并且，与单棵决策树相比，GBDT 通过组合多个弱学习器，具有更好的泛化能力，减少了过拟合的风险。

尽管 GBDT 是一种复杂的模型，但与随机森林相比，它仍然可以通过可视化决策树来理解模型的决策过程，因此具有更好的可解释性。

当然，由于决策树之间以『串行化』的方式联结，因此每一棵树的训练都依赖于前一棵树的结果，降低了并行化计算的能力，所以，在大规模数据集上的训练速度较慢。不过，随着 LightGBM 等更先进的模型的出现，现在梯度提升决策树在训练上也可以利用 GPU 进行并行运算。

梯度提升算法是一类算法的总称，比较重要的实现有：
1. Sklearn 中的 GradientBoostingRegressor 和 GradientBoostingClassifier 实现。它在性能上不如 XGBoost 和 LightGBM，但简单易用，且与 scikit-learn 生态系统无缝集成，因此可以作为很好的教学系统。
2. XGBoost。这是最先发展起来的、有工业强度的一个 GBDT 模型。
3. LightGBM。LightGBM 是微软开发的一个模型，相比 XGBoost，它通过直方图算法将连续特征离散化为有限个区间（即直方图），从而加速了最佳分裂点的查找过程，减少了内存占用。此外，与 XGBoost 不同，它采用了优先扩展叶子节点的策略，从而使得模型能更快收敛并获得更高的精度。不过，在一些算法上，各种模型都会相互借鉴，随着新版本的发布，也可能出现此消彼长的情况。
4. CatBoost。这是由 Yandex[^yandex] 开发的一种 GBDT 模型，以满足自身在搜索引擎优化、广告推荐等业务场景中的需求。一般认为，CatBoost 在分类任务上的表现优于 XGBoost 和 LightGBM。特别是，它采用了一种有序的目标统计（Ordered Target Statistics）来处理类别型特征，巧妙地避开了传统处理方式（比如独热编码）可能带来的维度灾难问题。

## 3. XGBoost

在 XGBoost[^xgboost] 诞生之前，梯度提升决策树（GBDT）已经由 Friedman 在 1999 年提出，作为一种迭代的集成学习算法，在许多任务中表现优异，但在实际应用中存在计算效率低、难以处理大规模数据等问题。

陈天奇在华盛顿大学攻读博士学位期间，鉴于当时现有分类器如 GBDT 效率较低等情况，决定自己开发一个基于决策树的提升算法，这便是 XGBoost。它基于 GBDT 进行了重要的性能优化，通过对计算速度、内存效率、并行计算和正则化等方面的改进，解决了 GBDT 在大数据环境中的性能瓶颈问题。

XGBoost 一经提出，一度在 Kaggle 等数据科学竞赛中表现出色，成为表格类数据机器学习算法中霸榜的存在。因此，XGBoost 获得了巨大的声誉和关注，并且在工业界广泛使用。

以下是一个 XGBoost 完成分类任务的简单示例：

<Example id="xgboost"/>

```python
import xgboost as xgb
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score

# 加载数据集
iris = load_iris()

X, y = iris.data, iris.target

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建 DMatrix 数据结构
dtrain = xgb.DMatrix(X_train, label=y_train)
dtest = xgb.DMatrix(X_test, label=y_test)

# 设置参数
params = {
    'objective': 'multi:softmax',  # 多分类任务
    'num_class': 3,                # 类别数
    'max_depth': 3,                # 树的最大深度
    'eta': 0.1,                    # 学习率
    'eval_metric': 'mlogloss'      # 评估指标
}

# 训练模型
num_rounds = 100
bst = xgb.train(params, dtrain, num_rounds)

# 预测
preds = bst.predict(dtest)

# 计算准确率
accuracy = accuracy_score(y_test, preds)
print(f'Accuracy: {accuracy:.2f}')
```

xgboost 提供了丰富的可视化功能，帮助我们理解和对训练调优。

<Example id="xgb-plot-importance"/>

```python
# 获取特征重要性
importance = bst.get_score(importance_type='weight')
importance = dict(sorted(importance.items(), key=lambda item: item[1], reverse=True))

# 打印特征重要性，这些数值也会显示在下面的图表中
print(importance)

# 绘制特征重要性图
plt.figure(figsize=(10, 6))
xgb.plot_importance(bst, importance_type='weight')
plt.title('Feature Importance')
plt.show()
```

在图中，每一个 bar 都代表一个特征的权重（在 iris 数据集中，共有 4 个特征）。权重代表了特征在模型构建过程中被用来进行分裂的次数。例如，一个特征被用于树的节点分裂的次数越多，它的权重可能就越高，这意味着这个特征在构建决策树时被频繁使用，对模型的结构形成有较大的贡献。

在`plot_importance`方法中，`importance_type`参数除了可以传入`weight`之外，还可以传入`gain`、`cover`和`total_gain`等。关于`gain`（增益）的含义，我们在之前讲信息增益时已经介绍过了。

在 Sklearn 中，Decision Tree 在训练完成之后，能绘制出整个决策树图，令人印象深刻。XGBoost 也提供了类似的功能，不过，它只生成了树状图的信息结构，最终的渲染部分则是交给了第三方工具来完成。请看以下示例：

<Example id="xgb-plot-tree"/>

```python
plt.figure(figsize=(20, 10))
xgb.plot_tree(bst, num_trees=0)
plt.title('Tree Visualization')
plt.show()
```

要执行上以示例，请确保已在本地安装了`graphviz`这个工具。

此外，对分类任务，我们一般都需要在训练完成之后，在测试集上预测并绘制困惑矩阵图，这些就都可以借助我们前面课程中讲过的 sklearn 中的可视化功能来完成。

## 4. LightGBM

LightGBM 问世于 2017 年，比 XGBoost 要晚几年。在它问世之前，XGBoost 一直是 Kaggle 等竞赛社区霸榜的存在，但 LightGBM 发布之后，XGBoost 就失去了榜首的位置。一般认为，LightGBM 能在训练任务上快 2-10 倍，并能提供同等的高准确性。不过，各个模型之间也会相到借鉴对方的优点，因此，这个评价也只能反映一段时间的状况。

### 4.1. dataset 对象

LightGBM 之所以高效，部分原因也在于，它实现了一种高效的数据格式 -- Dataset。在 Dataset 内部，实现了一些优化策略，比如对数据的存储布局、索引方式等进行优化，使得在训练模型时，尤其是面对海量数据，可以快速地进行数据读取与遍历，减少不必要的内存开销与计算耗时。

Dataset 是 LightGBM 惟一接受的数据格式，但它本身可以由多种其它类型创建而来，比如 hdf5[^hdf5] 格式，并且可以支持流式读取，而并不需要一次性将全部数据加载入内存。

下面，我们就以成年人收入数据集为例，介绍 dataset 的相关使用。我们先通过 pandas 下载和查看数据：

<Example id="LightGBM-dataset"/>

```python
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns

# 加载数据集
# 数据来自于 https://archive.ics.uci.edu/ml/machine-learning-databases/adult/adult.data
# 为加快速度，已缓存
url = "/data/ro/adult.data"
column_names = [
    "age", "workclass", "fnlwgt", "education", "education-num", "marital-status",
    "occupation", "relationship", "race", "sex", "capital-gain", "capital-loss",
    "hours-per-week", "native-country", "income"
]
data = pd.read_csv(url, header=None, names=column_names, na_values=" ?", skipinitialspace=True)

# 查看数据集的基本信息
print(data.head())
print(data.info())
```

我们看到其中有一些数据是类别类型的，还存在一些缺失值。缺失值需要进行预处理，在接下来的示例中，它将使用众数填充。LightGBM 支持类型数据『直接』参与训练，而无需事先进行额外的编码（比如 one-hot），但是需要先将类型特征数据转换为整数。这项工作，我们将通过 sklearn 中的 LabelEncoder 来实现。

!!! tip
    在这个场景下，使用众数填充是一个合理的选择。但在处理证劵数据时，前向填充（价格）、行业中值（基本面）可能更为合理。但是，在LightGBM中，缺失值处理并非必须。


下面的示例演示了构建 dataset 及预处理方法：

```python
# 处理缺失值
imputer = SimpleImputer(strategy='most_frequent')
data_imputed = pd.DataFrame(imputer.fit_transform(data), columns=data.columns)

# 确保数值列的数据类型正确
numerical_features = ["age", "fnlwgt", "education-num", "capital-gain", "capital-loss", "hours-per-week"]
data_imputed[numerical_features] = data_imputed[numerical_features].astype(float)

# 编码类别型特征
categorical_features = [
    "workclass", "education", "marital-status", "occupation", "relationship",
    "race", "sex", "native-country"
]

label_encoder = LabelEncoder()
for feature in categorical_features:
    data_imputed[feature] = label_encoder.fit_transform(data_imputed[feature])

# 编码目标变量
data_imputed['income'] = label_encoder.fit_transform(data_imputed['income'])

# 确认数据类型
print(data_imputed.dtypes)

# 划分训练集和测试集
X = data_imputed.drop('income', axis=1)
y = data_imputed['income']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建 LightGBM 数据集
train_data = lgb.Dataset(X_train, 
                         label=y_train, 
                         categorical_feature=categorical_features,
                         free_raw_data=False)
test_data = lgb.Dataset(X_test, 
                        label=y_test, 
                        categorical_feature=categorical_features, 
                        reference=train_data,
                        free_raw_data=False)
```

在最后生成 Dataset 时，我们传入了 categorical_feature 参数，用于指定哪些列是类别型特征。这是 LightGBM 中启用类别型特征的关键参数。

### 4.2. 训练模型

接下来，我们就定义如何训练模型了。

```python
# 设置参数
params = {
    'objective': 'binary',
    'metric': ['binary_logloss', 'auc'],
    'boosting_type': 'gbdt',
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.9,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': 1
}

# 定义 early stopping 回调函数
early_stopping_rounds = 50
evals_result = {}

num_rounds = 1000
bst = lgb.train(
    params,
    train_data,
    num_boost_round=num_rounds,
    valid_sets=[test_data],
    feval=None,
    init_model=None,
    categorical_feature=categorical_features,
    callbacks=[lgb.early_stopping(stopping_rounds=early_stopping_rounds),
                lgb.record_evaluation(evals_result)]
)
```

在这里，我们看到通过 params 指定了一些超参数。其中，目标函数指定为`binary`。成人收入数据集是一个典型的二分类任务，目标是预测一个成年人的收入是否超过 50k，因此，我们应该选择`binary`作为目标函数。

在评估函数上，这里我们使用了一个数组，包含了`binary_logloss`和`auc`（这是 kaggle 等平台上，二分类任务使用最多的评估指标），除此之外，还可以选择 `F1 score`等。这些内容我们在<ref>[第14课](14.md)</ref>中已经学习过了。

LightGBM 支持多种 Boosting 算法，包括`gbdt`、`dart`、`goss`和`rf`。其中 gbdt 是默认算法，可以不用指定。如果需要强调防止过拟合，可以考虑使用`dart`，但训练速度可能会变慢；对特别大的数据集，要加速训练或者减少内存占用，可以使用`goss`参数。一般不推荐使用`rf`，`rf`在这里是随机森林的缩写。当我们使用这个参数时，实际上就放弃了提升算法。

在训练时，我们指定了两个 callback 方法，其中一个是 early_stopping，它的作用是，监控训练时验证集上的评估指标。当指标在一定的轮数内没有得到改善时，就停止训练（而不是仍然走完 num_boost_round 的次数）；另一个 record_evaluation，它的作用是，在训练时，每轮迭代后，都会记录当前损失函数的值，并保存到 history 中。此后我们可以用来绘制学习曲线。

!!! tip
    early stopping 是防止过拟合的重要手段。如果模型在验证集上的性能不再提升，继续训练可能会导致模型过度拟合训练数据。它监控的评估指标来自于参数 metric。如果 metric 传入的是一个数组，则第一个指标会被 early stopping 使用。


这里还有许多其它参数，我们就放到参数优化部分讲解。

### 4.3. 模型评估与可视化

这是一个分类模型，因此，我们对训练的效果的评估，可以使用之前学习过的 sklearn 中的评估方法来进行评估：

<Example id="visualization"/>

```python
from sklearn.metrics import accuracy_score, classification_report,confusion_matrix

# 预测
preds = bst.predict(X_test, num_iteration=bst.best_iteration)
preds_binary = [1 if pred > 0.5 else 0 for pred in preds]

# 计算准确率
accuracy = accuracy_score(y_test, preds_binary)
print(f'Accuracy: {accuracy:.2f}')

# 打印分类报告
print('\nClassification Report:')
print(classification_report(y_test, preds_binary))

# 绘制混淆矩阵
conf_matrix = confusion_matrix(y_test, preds_binary)
plt.figure(figsize=(8, 6))
sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues', xticklabels=['<=50K', '>50K'], yticklabels=['<=50K', '>50K'])
plt.xlabel('Predicted Label')
plt.ylabel('True Label')
plt.title('Confusion Matrix')
plt.show()
```

如果对模型效果不满意，我们就需要进行参数优化。在此之前，我们需要深刻理解训练出来的模型，这样优化过程才能更加有的放矢。

与 xgboost 一样，LightGBM 也提供了 Feature Importance 可视化工具：

<Example id="lgbm-feature-importance"/>

```python
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns

# 获取特征重要性
importance = bst.feature_importance(importance_type='split')  # 也可以使用 'gain', 'cover', 'total_gain'
feature_names = bst.feature_name()

# 创建特征重要性字典
feature_importance_dict = dict(zip(feature_names, importance))

# 按重要性排序
feature_importance_dict = dict(sorted(feature_importance_dict.items(), key=lambda item: item[1], reverse=True))

# 打印特征重要性
print(feature_importance_dict)

# 绘制特征重要性图
plt.figure(figsize=(10, 6))
sns.barplot(x=list(feature_importance_dict.values()), y=list(feature_importance_dict.keys()))
plt.title('Feature Importance (split)')
plt.show()
```

LightGBM 是基于梯度提升的决策树模型，很自然地，我们也希望绘制出它的树结构。LightGBM 提供了 plot_tree() 函数以绘制单棵决策树。与 xgboost 一样，都需要依赖 graphviz 库。

<Example id="lgbm-show-tree"/>

```python
lgb.plot_tree(bst, tree_index=0, figsize=(30,20))
plt.show()
```

这样就会绘制第 0 棵树的结构出来。如果我们是在训练一个交易策略，使用的特征是技术因子，你就能清晰地看到，最终的决策是如何通过这些技术因子来决定的。

在示例 [][#lgbm-show-tree] 中，我们只显示了第 0 棵树的结构。一个训练好的模型中，应该有多少棵树呢？在标准的梯度提升模型中，一轮训练就应该生成一棵树，所以，`num_boost_round`的数量就决定了树的数量的上限。如果我们使用了 early stop 方法，那么，树的上限将会由 best_iteration 返回，这个数值是小于等于 num_boost_round 的。

知道了这个上限，如果有必要，我们就可以通过循环，将所有的树结构都显示出来。

<Example id="lgbm-best-iteration"/>

```python
print(bst.best_iteration)
assert bst.best_iteration <= bst.num_trees()

# for i in range(bst.best_iteration):
for i in range(3):
    lgb.plot_tree(bst, tree_index=i, figsize=(30,20))
```

plot_tree 绘制的是训练的最终结果。我们还可以通过 plot_metric，观察训练过程。

<Example id="lgbm-plot-metric">

```python
lgb.plot_metric(evals_result, metric="auc")
```

<!--讲解：回调函数的使用-->

我们还可以通过直方图来理解特征在决策树构建时的作用。

<Example id="lgbm-split-value">

```python
lgb.plot_split_value_histogram(bst, feature="age", bins="auto")
```

在绘制决策分裂直方图时，我们通过 feature 参数，传入要呈现的特征。在绘制出来的图形中，横坐标表示特征的分裂值（split values）。这些值是决策树在该特征上进行节点分裂的依据，或者说，进行样本划分的边界点。纵坐标则代表该分裂值出现的频率或者次数，它表明该特征的某个分裂值进行节点分裂的频繁程度。较高的纵坐标值意味着在构建决策树时，该分裂值被更多次地用于划分数据。

比如，在上图中，最高的 bar 分别大约为 28 和 36，表明在这个年龄前后，人们的收入差异扩大，而 70~90 之间的 bar 最低，表明在这个年龄段，大家的收入差异就很小了。

从形态上看，如果直方图是单峰的，说明模型在该特征上倾向于使用某个特定区域的分裂值。如果出现多峰分布，则说明模型在该特征的多个不同区间都频繁地进行分裂，即该特征与目标变量之间存在复杂的非线性关系。

如果将多个特征的直方图相比较，可以了解哪些特征在模型构建过程中起到更关键的作用。具有更高频率分裂值（更高的纵坐标）和更广泛分布（跨越更多分裂值）的特征可能对模型的决策过程更为重要。如果两个特征的直方图布很相似，也可能说明这两个特征变量之间存在协同作用，可以考虑降维过滤掉不必要的特征。

具体到量化交易策略来说，特征分裂直方图能很好地说明我们前期收集的因子中，哪些是比较有效的，哪些则是贡献比较小的。

### 4.4. 交叉验证

LightGBM 提供了交叉验证功能，可以快速评估模型的泛化能力。

<Example id="lgbm-cr2025-01-12oss-validation"/>

```python
# 进行交叉验证
early_stopping_rounds = 50
evals_result = {}
cv_results = lgb.cv(
    params,
    train_data,
    num_boost_round=1000,
    nfold=5,  # 5 折交叉验证
    stratified=True,  # 分层抽样
    shuffle=True,
    metrics=['binary_logloss', 'auc'],
    callbacks = [lgb.early_stopping(stopping_rounds=early_stopping_rounds),
                lgb.record_evaluation(evals_result)],
    seed=42
)

# 打印交叉验证结果
print("CV Results:", cv_results)

# 绘制交叉验证结果
plt.figure(figsize=(12, 6))

# 绘制 binary_logloss
results = {
    'binary_logloss-mean': np.array(cv_results['valid binary_logloss-mean']),
    'binary_logloss-stdv': np.array(cv_results['valid binary_logloss-stdv']),
    'auc_mean': np.array(cv_results['valid auc-mean']),
    'auc_stdv': np.array(cv_results['valid auc-stdv'])
}
plt.subplot(1, 2, 1)
plt.plot(results['binary_logloss-mean'], label='Mean')
plt.fill_between(range(len(results['binary_logloss-mean'])), 
                 results['binary_logloss-mean'] - results['binary_logloss-stdv'], 
                 results['binary_logloss-mean'] + results['binary_logloss-stdv'], 
                 alpha=0.3)
plt.title('Cross-Validation - binary_logloss')
plt.xlabel('Number of Boosting Rounds')
plt.ylabel('binary_logloss')
plt.legend()

# 绘制 auc
plt.subplot(1, 2, 2)
plt.plot(results['auc_mean'], label='Mean')
plt.fill_between(range(len(results['auc_mean'])), 
                 results['auc_mean'] - results['auc_stdv'], 
                 results['auc_mean'] + results['auc_stdv'], 
                 alpha=0.3)
plt.title('Cross-Validation - auc')
plt.xlabel('Number of Boosting Rounds')
plt.ylabel('auc')
plt.legend()

plt.tight_layout()
plt.show()
```

在图中，我们绘制了 cross validation 中得到的两个指标的均值，以及它们的标准差。直观来看，如果这个包络带比较窄，则说明在不同的分区验证集上，模型的表现都比较稳定。因此，我们就可以利用这一组参数来训练出最终的模型。

LightGBM 在进行交叉验证时，使用的数据集划分方法是 k 折交叉验证，并且可以实现分层抽样。我们在机器学习基础理论部分讲过，这种方式不适用于证券行情类的数据。所以，如果我们要以行情数据及其因子为特征，那么就要自己实现划分。

### 4.5. 参数优化

```python
import optuna
def objective(trial):
    # 定义超参数搜索空间
    params = {
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'num_leaves': trial.suggest_int('num_leaves', 20, 31),
        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1, log=True),
        'feature_fraction': trial.suggest_float('feature_fraction', 0.7, 1.0),
        'bagging_fraction': trial.suggest_float('bagging_fraction', 0.7, 1.0),
        'bagging_freq': trial.suggest_int('bagging_freq', 1, 10),
        'min_child_samples': trial.suggest_int('min_child_samples', 20, 100),
        'verbose': -1
    }

    # 训练模型
    bst = lgb.train(
        params,
        train_data,
        num_boost_round=50,
        valid_sets=[test_data],
        feval=None,
        init_model=None,
        feature_name='auto',
        categorical_feature=categorical_features,
        callbacks=[lgb.early_stopping(stopping_rounds=20)]
    )

    # 返回评估指标
    return bst.best_score['valid_0']['auc']

# 创建 Optuna 研究对象
study = optuna.create_study(direction='maximize')

# 运行超参数搜索
study.optimize(objective, n_trials=5)

# 打印最佳参数和最佳得分
print("Best parameters:", study.best_params)
print("Best AUC:", study.best_value)
```

目标函数的默认优化方向是最大化，所以，这里我们选择 auc 作为评估指标也是恰如其份。

在这段代码中，我们使用了全局变量 train_data 来引入训练数据集。这次训练的时间会长不少。在训练结束后，我们将使用 best_params 来重新定义和训练模型。

num_leaves 是控制树的形态的关键参数之一。它的作用是控制每棵树的最大叶子节点数。较大的值可以提高模型的复杂度和拟合能力，但也可能导致过拟合。

feature_fraction（特征子采样比例）是每次构建树时随机选择的特征比例。通过引入随机性，可以减少过拟合并提高泛化能力。在调优中，如果模型在验证集上的表现不够好，可以尝试进一步降低；如果模型的表现已经很好，可以保持当前值或适当增加，以平衡模型的复杂度和稳定性。

bagging_fraction（样本子采样比例），每次迭代时随机选择的样本比例。类似于特征子采样，样本子采样也可以减少过拟合并提高泛化能力。

bagging_freq（样本子采样的频率），控制样本子采样的频率。设置为 0 表示不进行样本子采样；设置为正整数表示每隔多少次迭代进行一次样本子采样。

learning_rate 控制每次迭代时权重更新的步长。较小的学习率可以使模型更稳定，但需要更多的迭代次数；较大的学习率可以加快收敛速度，但可能导致不稳定。这些是在机器学习基础理论部分讲过的内容。

在这里，我们看到， optuna在学习率的搜索上，使用了一种对数算法，因此，最终采样到的学习率将不会是像0.01, 0.02, 0.03,...这样的均匀分布，而是会在更接近0.01的位置处多采样。这样有利于减少搜索次数，提高成功率。

对于复杂的模型，学习率对训练的成功往往起到至关重要的作用。有时候，我们也可以单独对它进行优化。

在高级深度学习框架 fast ai 中，存在着一个find_lr的方法，可以帮助我们寻找合适的学习率。LightGBM中并没有实现类似的方法，不过，我们也可以通过下面的代码来手动实现：

<Example id="LightGBM-find-lr">

```python
import lightgbm as lgb
from sklearn.metrics import accuracy_score
import numpy as np

train_data = lgb.Dataset(X_train, label=y_train)
val_data = lgb.Dataset(X_test, label=y_test)

learning_rates = np.linspace(1e-2,1e-1,100)
scores = []
best_accuracy = 0
best_lr = None
for lr in learning_rates:
    params = {'objective': 'binary', 'metric': 'binary_logloss', 'learning_rate': lr}
    model = lgb.train(params, train_data, num_boost_round=50)
    y_pred = model.predict(X_test)
    y_pred_binary = np.round(y_pred)
    accuracy = accuracy_score(y_test, y_pred_binary)
    scores.append(accuracy)
    if accuracy > best_accuracy:
        best_accuracy = accuracy
        best_lr = lr

clear_output()
plt.plot(learning_rates, scores)
plt.xlabel('Learning Rate')
plt.ylabel('Accuracy')
plt.scatter(best_lr, best_accuracy, color='red', marker='o')
```

在这个图中，我们输出的是不同的学习率下，最终得到的auc的结果。最终，我们应该在图中梯度上升最快的区域内确定学习率的调优区间。

下面的图可能会更好地说明learning rate的作用，以及应该如何选择learing rate。与上面的示例不同，这次我们绘制的是学习率与损失函数的曲线，根据 Fast AI,我们应该选择梯度下降最陡的线性区。

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/find-lr.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

### 4.6. sklearn 接口

LightGBM 提供了两种风格的 Python 接口，一种是所谓的原生接口；另一种是 sklearn 风格的接口。下面，我们将演示如何使用 sklearn 风格的接口。

<Example id="LightGBM-sklearn-interface">

```python
from lightgbm import LGBMClassifier
from sklearn.metrics import accuracy_score
from sklearn.model_selection import GridSearchCV
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
import joblib

# 加载数据
iris = load_iris()
data = iris.data
target = iris.target

# 划分训练数据和测试数据
X_train, X_test, y_train, y_test = train_test_split(data, target, test_size=0.2)

# 模型训练
gbm = LGBMClassifier(num_leaves=31, learning_rate=0.05, n_estimators=20)
gbm.fit(X_train, y_train, eval_set=[(X_test, y_test)])

# 模型存储
joblib.dump(gbm, "loan_model.pkl")
# 模型加载
gbm = joblib.load("loan_model.pkl")
# 模型预测
y_pred = gbm.predict(X_test, num_iteration=gbm.best_iteration_)

# 模型评估
print("The accuracy of prediction is:", accuracy_score(y_test, y_pred))

# 特征重要度
print("Feature importances:", list(gbm.feature_importances_))

# 网格搜索，参数优化
estimator = LGBMClassifier(num_leaves=31)
param_grid = {"learning_rate": [0.01, 0.1, 1], "n_estimators": [20, 40]}
gbm = GridSearchCV(estimator, param_grid)
gbm.fit(X_train, y_train)
print("Best parameters found by grid search are:", gbm.best_params_)

```

不过，在这种情况下，将无法使用 Early Stopping。

## 5. 拓展阅读

这一课的重点在于讲解 LightGBM，对 XGBoost 和 LightGBM 的比较着墨不多。如果你对 LightGBM 相比较于 XGBoot 进行了哪些性能优化感兴趣，推荐阅读这篇知乎文章：[深入理解 LightGBM](https://zhuanlan.zhihu.com/p/99069186)。这篇文章解释了 LightGBM 是如何通过直方图进行内存优化，为什么 leaf-wise 的生长算法会比 XGBoost 的 level-wise 生长算法更高效，以及 cache 命中等细节。

## 6. Footnotes

[^GBDT]: 梯度提升的想法起源于 Leo Breiman 对于 boosting 可以被解释为合适成本函数的优化算法的观察。 之后，Jerome H. Friedman 于 1999 年和 2001 年，开发了显式回归梯度提升算法，即 GBDT。
[^xgboost]: XGBoost 有较高的学术影响力，其相关论文 [A Scalable Tree Boosting System](https://arxiv.org/abs/1603.02754) 自发表之后，被引用超过 49000 次。
[^hdf5]: HDF5 是一种用于存储和交换超大数据集的文件格式。LightGBM 支持通过 Sequence 接口，在训练时分批读入数据。在 [github](https://github.com/microsoft/LightGBM/blob/master/examples/python-guide/dataset_from_multi_hdf5.py) 上有此示例。
