# 解读 Alphalens 报表
<br>
解读 Alphalens 报表是件很考验经验的活儿。一方面，即使我们输入的因子数据或者价格数据是错误的，Alphalens 也会照常生成报表，但这些结论无疑是似是而非的。另一方面，即使这个过程完全正确，要作出因子的质量好坏这一结论也不容易：因为我们看到的报表种类繁多，需要了解每一个指标的实现方式才能读懂它，了解它的单位和经验值，并且不同的指标之间，也可能自相矛盾。

!!! tip
    所谓因子的质量，一般是指因子的预测能力的强与弱，而不是它的收益能力。两个因子相比较，我们总是更看好预测能力强的，而不是收益能力强、但不稳定、不可靠的因子。因为收益除了与因子本身相关，还与杠杆相关。此外，预测能力强的因子，如果收益是负的，我们只需要对因子进行简单变换，就可以得到一个稳健的正收益因子。

Alphalens 生成的报表种类很多，我们将分两章来进行讲解。在讲解过程中，我们将结合报表的解读，讲解如何判断因子的质量。

在这一章，我们将假定分析过程都是正确的。然后将在<chap>第 7 章</chap>，详细介绍因子分析过程中可能犯的错误。

在上一章的 [示例 4-1](04.md#example-4-1) 中，我们已经生成了若干报表。我们将按照这些报表的顺序来进行，但不一定原样使用它生成的报表。

<!-- todo: 后面呼应 -->
最先出现的是分层统计图()。我们将在<ref>第6章</ref>的[有确定交易含义的因子](06.md#有确定交易含义的因子)一节中，再详细介绍它的作用。

## 1. 收益分析

### 1.1. Alpha 和 Beta

紧接着分层统计图出现的是 Alpha & Beta 报表。

![Alpha & Beta](https://images.jieyu.ai/images/2024/07/returns-analysis.jpg?width=500)

<!-- todo: 所有的这些收益分析，都是由 tears.create_returns_tear_sheet 生成的 -->

<!-- todo: 后面呼应 

Alphalens 在进行年化时，它是按 factor_data 的列名字来判断 periods 并决定如何年化的。比如，如果列名字是 1D、5D 和 10D，那么它就会用 252 天分别除于 1, 5, 10 来计算年化复利。在我们的例子中，尽管列名字是 1D、5D 和 10D，但实际上是 1Month, 5Month 和 10Month，所以，在这种情况下的年化是错误的。
-->

这个表格首先给出的是年化 Alpha 和 Beta。这里的数值究竟是什么单位？比如，从表中可以读出，年化 Alpha 为-0.214，这究竟是-21.4%呢，还是-0.214%呢？

Alphalens 并没有文档详细说明这些，不过，通过分析它的源代码，我们了解到这里的-0.214 是-21.4%，而不是-0.214%。所以，我们可以这样理解这个因子，它会在市场波动之外，额外再一年亏掉 21.4%。以此而论，显然这是非常糟糕的结果。

!!! tip
    年化 Alpha 一般取值在多少以内，就算是正常的？一般来说，10%以上的 Alpha 就是相当不错的。从 1965 年到 2021 年，巴菲特的年化超额收益（相对于标普 500 指数）大约为 9.6%。因此，如果我们得到的年化 Alpha 显著高于 10%，这不一定是错误，但是一定要多加小心，防止引入错误。

根据 Beta 的定义，它就是一个系数，所以一般没有百分比一说。因此，这里的 Beta 就是浮点数所表示的数值本身。Beta 取值为 1 时，意味着与市场强相关，Beta 取值为-1 时，意味着与市场强负相关。但这并不意味着 Beta 的绝对值不可以超过 1。

<!--

在内部，收益由 performance.factor_alpha_beta 求得。它先是计算了整个 universe 的日均 (period-wise) 收益作为市场基准收益，然后计算了因子加权收益，再以市场收益为 x，加权收益为 y，通过 statsmodels.OLS 计算回归系数，得到 alpha 和 beta

最后，根据 period 的周期，将 alpha 调整为年化。见 performance.factor_alpha_beta, line 299~330

Alphalens 的因子加权收益计算法已经考虑了多空组合的情况。它的权重调整方法，如果 long_short 指定为 True，那么在计算因子加权时，会先将因子去均值（demeaning) 化，再计算因子绝对值之和，以构建一个多空等权、美元中性的资产组合）

-->

[](#Alpha & Beta) 中的后面三行，代表在分层情况下，最好的一组、最差的一组的收益及它们之间的离差。

Mean Period Wise Return Top Quantile 这一行的意思是，如果我们持有最好的一组，那么它在 1D、5D 和 10D 的远期收益将会是多少（平均值）。在示例中，分别是 3.147、-0.208、-0.317。与此类似，Mean Period Wise Return Bottom Quantile 则意味着，如果我们持有最差的一组，那么它在 1D、5D 和 10D 的远期收益将会是多少。在示例中，分别是-7.997、-5.561 和 -2.476。它们的单位都是 bps。

!!! info
    注意 bps 这个单位。它是 basis points（即基点）的意思。根据 Alphalens 的定义，1 bps等于0.01%（即万分之一）。因此，3.147bps 就等于 0.03147%。这个定义出现在 plotting.py 文件中。

最后一行，指的是 top 分层与 bottom 分层之间的离差 (spread)。

### 1.2. 分层收益均值图

[](#分层收益均值图)显示的是 Mean Period Wise Return By Factor Quantile 柱状图。这个图扩展了 Returns Analysis 表中的第三行、第四行。它把所有分层的收益都以一个柱状图显示出来了。这个图跟 [](#Alpha & Beta) 来自同一次测试。

它是通过计算每个分位每天的平均远期回报，并最终对它们进行平均而生成的。这个图表告诉我们，如果我们持有特定分层（即属于该分层的所有股票）1 天、5 天或 10 天，平均可以获得多少回报。

![分层收益均值图](https://images.jieyu.ai/images/2024/08/mwpr-poor.jpg?width=500)

可以看出，最左边和最右边 bar 正好对应了 [](#Alpha & Beta) 中的 Bottom 和 Top 层，而其它部分则是 [](#Alpha & Beta) 中没有的。那么多出来的部分将给我们提供什么信息？

通过这个图，我们可以评估因子分层与收益之间是否具有良好的线性关系，或者说，因子是否具有较好的预测能力。我们知道，只有因子（或者分层）与收益之间有线性关系，至少是满足某种单调性，这个因子才值得我们去关注。在这种情况下，我们做多 top 分层，同时做空 bottom 层，就可以得到一个稳健的收益。

因此，[](#分层收益均值图)就是表现不佳的分层收益图的例子。当我们看到这样的图的时候，就应该明白，继续看其它的报表已经没有意义了。

那么，预测能力强的因子，它的分层收益均值图[^predictive-vs-non-predictive]，应该是什么样子的？下面给出的参考示例：

![预测能力强的因子分层收益均值图](https://images.jieyu.ai/images/2024/08/mwpr-good.jpg?width=500)

!!! tip
    这个图是如何构造出来的？显然，如果我们的因子中含有未来数据，那么它将会有非常强的预测能力。因此，我们可以用未来 5 天的收益作为因子，就能构造出一个“预测能力强的因子”。我们后面还将使用这种构造方法生成的其它报告来作为对比。

这个图还能告诉我们，对某一个分层，持有不同的时间（即 1D、5D 和 10D)，分别会得到什么样的回报，从而可以清晰地了解要使用最大化回报，我们需要持有证券多长时间。

这里有一个值得提及的细节：我们不能直接比较 1 天的平均回报与其他天数（如 5 天）的平均回报。为了合理地比较不同时间段，AlphaLens 显示的是回报率。回报率是指如果回报以稳定的速度增长，每天的回报将会是多少。比如，这里的周期是 1D、5D 和 10D，因此，Alphalens 显示的就是分别持有 1 天、5 天和 10 天收益的日平均收益率。

所以，在这个图中，以最高分层为例，持有 1 天的收益率最高，持有 10 天的日均收益率最低；但这不意味着持有 10 天的总收益率会小于 1 天。

<!--
!!! info
    在 Alphalens 的教程中，提到了在同一分层中，如何选择持有周期以使得收益最大化。文档 [^comparison-hold-days] 本身的表述不是很清楚，甚至可能有点前后矛盾。
-->

### 1.3. 分层收益 Violin 图

通过 [](#分层收益均值图) 我们只能看到因子分层的均值，但看不到其分布。如果不检查分布，我们的观点就很可能被个别离群值左右。

因此，Alphalens 还绘制了 violin 图，如 [](#分层收益 Violin 图) 所示，来显示每个分层收益的密度分布。

![分层收益 Violin 图](https://images.jieyu.ai/images/2024/07/fig-05-03.jpg?width=500)

如何解读收益分析中的 Violin 图？这里我们仍然要从两方面来解读，首先，因子是否有预测能力？

我们再拿一个预测能力强的因子的 Violin 图来对照一下：

![预测能力强的因子 Violin 图](https://images.jieyu.ai/images/2024/08/mwpr-violin-redictive.jpg?width=500)

显然，预测能力强的因子，它的 violin 图也应该单调递增。

但就具体的单个 violin 图而言，什么样的形态才是好的？

在 [](#分层收益 Violin 图) 中，均值是没有显示的，它只标记了 25%，50%（中位数）, 75%分位数的位置。如果我们把均值、中位数等数值比较起来看，就更能看出因子的稳健性。

如果中位数大于零，那么该因子分层的胜率将大于 50%；对 top quantile 来说，在中位数大于零的情况下，violin 图越细、越高，说明因子的稳健性越好。同样地，在多空组合下，如果 bottom quantile 的中位数小于零，violin 图越细、越高，说明因子的稳健性更强。

如果 Violin 图很宽（fat），且集中分布在零值附近，均值是由个别离群值拉起来的，这样的分布就是不稳健的。

!!! tip
    正常情况下，我们看到的 violin 图可能都是纺锤形。如果出现哑铃形，即数据具有双峰分布，往往表明因子不够纯粹。比如，不同的市场或者资产类别对因子的响应不同；又或者因子在某些时期表现更好，而在其它时期表现不佳。

### 1.4. 因子加权多空组合累计收益

因子加权多空组合累计收益图（Factor Weighted Long/Short Portfolio Cumulative Return <1D Period>）从另一个角度来展示因子与收益的关系。

如 [](#多空组合累计收益) 所示，它模拟了如果我们使用因子值作为持仓权重，交易整个股票组合时的回报情况。**在这个图表中没有分位的概念**，所有的股票都参与了交易，但持仓权重不同。

尽管实际策略通常只会交易顶部分层和底部分层的股票（并且这样通常会有更好的表现），而不是组合中的全部股票，但这个图表为我们提供了一个整体上因子预测能力的直观感受。

因子在整个股票池中越具有一致性，这个图表显示出的结果就越好。在这个图表中看到的一致性越多，我们就越相信因子能够解释股票的回报。此外，如果因子在整个股票池中具有一致性，我们可能希望按照因子值来加权组合中的持仓，而不是采用等权重的方案。

![多空组合累计收益](https://images.jieyu.ai/images/2024/07/fig-05-04.jpg?width=500)

从这个图我们可以看出，该因子在 2020 年之前表现一般，但在 2020 年之后出现了加速。为什么会出现这种情况？这就是我们要去进一步探索的地方。

<!--
如果我们没有做行业中性化，并且出现了因子在某个时间段对某个行业暴露度比较高的情况，就有可能出现上面这种情况。比如，因子在某个周期性行业上暴露比较高，而某个时段，正好是这个周期性行业的低谷，这样收益就低，过一段时间，行业引来复苏，因子收益就变高。
-->
!!! tip
    这里的收益不能直接等同于实盘收益。它是没有计入滑点和手续费的。

### 1.5. 查看收益的驱动分层

[](#多空组合累计收益) 可以看成是策略的累计收益率。而 [](#收益驱动分解图) 将向我们显示策略收益的驱动因子分层是哪一个：

![收益驱动分解图](https://images.jieyu.ai/images/2024/07/fig-05-05.jpg?width=500)

在这张图中，红色的是 bottom 层，深蓝色是 top 层。令人意外地是，我们看到，在 2015 年之前，为策略提供收益的是 bottom 层，top 层则是贡献了亏损；在 2015 年之后，情况刚好反了过来。

[](#收益驱动分解图) 揭示了隐藏在 [](#多空组合累计收益)中的矛盾。如果这两张报表出自同一个因子的话，它表明，2020 年之前，bottom 分组本应该是收益的贡献者，但在累计收益中，我们做空了 bottom 组，从而导致总体收益变差；在 2020 年之后，bottom 组的收益趋向负面，因此，通过做空它，策略取得了额外的收益，导致 2020 年以来，策略总体累计收益大幅上升。

从这两张图来看，我们还可以得出一个结论，该因子仅仅是一个风格因子，而不是真正的 Alpha 因子--它并不是全时段都有效的。

与之对照，一个预测能力强的因子的收益驱动分层图是这样的：

![好的收益驱动分解图](https://images.jieyu.ai/images/2024/08/good-returns-driver.jpg?width=500)

<!--
Alpha 因子，即反映驱动公司健康成长动力的因子，比如科技创新力、管理能力、品牌等；风格因子，则是与公司成长有相关性的因子。前者是因果关系，后者是相关性关系。
-->

### 1.6. 多空组合收益的稳健性

如 [](#分层离差均值图) 所示，Quantile Mean Return 图展示了 top 分层与 bottom 分层之间的收益差随时间的变化图。收益差的单位是 bps。在显示均值之外，它还显示了误差包络图 (error band) 及移动平均线。

!!! tip
    默认情况下，error band 显示的是一个标准差。

![分层离差均值图](https://images.jieyu.ai/images/2024/07/fig-05-06.jpg?width=500)

对于多空策略来说，从这个图我们可以看出策略在不同时间点上的稳定性。默认地，它有三个版本，即按 1D、5D 和 10D 分别计算的离差收益。

显然，我们希望看到一个 mean returns spread 大于零，且数值越大越好，但其波动较小，方差小的情况。对于 1 month moving average 同样如此。

![好的分层离差均值图](https://images.jieyu.ai/images/2024/08/fa-05-predictive-spread.jpg?width=500)

<!--
[^comparison-hold-days]: 见 [mean-period-wise-return-by-factor-quantile](https://github.com/quantopian/alphalens/blob/master/alphalens/examples/alphalens_tutorial_on_quantopian.ipynb#mean-period-wise-return-by-factor-quantile) 一节。-->

好，到此为止，我们就讲完了横截面上的收益分析。这一组分析的特点是，它们都是由函数 alphalens.tears.create_returns_tear_sheet 来生成的。因此，如果我们只关注收益分析的话，我们也可以得到清洗好的 factor_data 之后，不调用 create_full_tear_sheet, 而仅调用这个函数来进行分析。

## 2. Event Study

尽管 Alphalens 主要用于截面分析。但出于以下原因，我们仍然需要事件研究：

1. 从另一个侧面验证分析过程是否正确。
2. 在默认的 Alphalens 分析中，我们只知道 1 天、5 天和 10 的远期收益，但我们也想知道，此前几天，此后 1 天、2 天，...10 天的情况。这些数据有可能帮助我们进行调优。
3. 对单资产进行因子分析。

这时候我们就要用到 Alphalens 的事件分析。它仍然是一种收益分析，但维度不同。

与事件分析相关的函数有两个，一个是 create_event_returns_tear_sheet，另一个是 create_event_study_tear_sheet，后者输出内容更多一些。

尽管 Alphalens 提供了事件分析函数，但它的支持并非端到端的 -- 即我们不能复用之前的 factor_data，而必须自己先把`事件`定义出来。

!!! tip
    按照 Alphalens 的要求，events 数据中，只能包含信号发生日的记录，其它记录要么删除掉，要么置为 NaN。这里我们是将其删除掉了。Alphalens 还有另外一个要求，就是参与事件分析的 event 如果是做多信号，它们必须为正；如果是做空信号，它们必须为负。这就是为什么我们不能复用之前的 factor_data 的原因。

<!-- 这一点是很多教程，包括 Alphalens 自己的教程都没有明确介绍的。Alphalens 的教程中，尽管代码中已经包含了定义事件这一步骤，但并没有明确指出来，如果你看得不仔细，就会忽略这一步，导致错误的结果。-->

对 [示例 4-1](04.md#example-4-1) 中定义的因子而言，当我们只考虑做多时，事件是当一个因子从任何一个分层进入 top 分层时的情况。

因此，我们必须先把这部分情况找出来，如 [](#example-1) 所示。

<Example id=rolling_slope/>

```python
from numpy.lib.stride_tricks import as_strided
from alphalens.utils import get_clean_factor_and_forward_returns

def rolling_slope(close: NDArray, win:int, *args):
    if len(close) < win:
        return np.full((len(close), ), np.nan)

    stride = close.strides
    shape = (len(close) - win + 1, win)
    strides = stride + stride
    transformed = as_strided(close, shape, strides)
    
    slopes, _ = np.polyfit(np.arange(win), transformed.T, deg=1)

    left_pad_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_pad_len, 0), mode='constant', constant_values=np.nan)
    return slopes

def wrapper(group):
    slopes = rolling_slope(group["close"].to_numpy(), 10)

    index = group.index.get_level_values(0)
    df = pd.DataFrame(slopes, index=index, columns=["factor"])
    return df

# 1. 获取行情数据
start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 12, 29)
barss = load_bars(start, end, universe=("000001.XSHE",))

# 2. 计算因子
factors = barss.groupby(level='asset').apply(wrapper)
factors = factors.swaplevel(0,1)

# 3. 提取价格数据
prices = barss['price'].unstack(level=1)

# 5. 提取 EVENTS
prev_factor = factors.shift(1)
events = factors[(factors.factor>5e-3) & (prev_factor.factor < 0)]
events.tail()
```

这段代码主体跟 [示例 4-1](04.md#example-4-1) 差不多，但我们只传入了一支资产。同时，在结束时，我们没有调用 create_full_tear_sheet，而是通过已有数据提取了事件。

然后，我们要基于新生成的 events，运行 get_clean_factor_and_forward_returns，生成新的 event_data。这一次，我们把 events 作为输入，prices 仍然可以复用，但只能指定一个分层。

!!! tip
    事件分析的关键：
    1. 只保留事件发生的数据，其它数据丢弃。
    2. 要确保做多策略的因子为正；做多策略的因子为负。
    3. 生成事件后，仍然要调用get_clean_factor_and_forward_returns，并且一定只能指定一个分层。

当新的 factor_data 生成之后，我们就可以调用 create_event_study_tear_sheet 来生成事件分析报告。

<Example id=create_event_study_tear_sheet/>

```python
from alphalens.tears import create_event_study_tear_sheet
event_data = get_clean_factor_and_forward_returns(events, 
                                                  prices, 
                                                  quantiles=1, 
                                                  periods=(1,2,3,4,5,6,10), 
                                                  filter_zscore=None)
create_event_study_tear_sheet(event_data, prices, avgretplot=(2,10))
```

<!--
![Average Cumulative Returns by Quantile](https://images.jieyu.ai/images/2024/08/event-study.jpg)
-->

这个图表展示了因子信号发出前几天和后几天的因子表现概览（通过设置 avgretplot=(2,10)）。在 X 轴上，我们看到的是时间（信号发出前后的天数）：垂直线位于第 0 天，表示信号发出的时刻（即因子计算的时刻），我们可以从 2 天前到 10 天后这段时间内观察到平均累计回报。

第二个图显示的是信号发出时刻前后的每日平均回报。

create_event_returns_tear_sheet 有一些参数，大家可以自己尝试一下。Event study，并不是 Alphalens 的重点，文档与实际运行上，存在着一些差异，这可能是版本的问题。所以，在使用 Event Study 时，要适当谨慎一些。
<!--
create_event_returns_tear_sheet 有一些参数，大家可以自己尝试一下。关于 Event study，感觉并不是 Alphalens 的重点，文档与实际运行上，存在着一些差异，这可能是版本的问题。

相关部分的文档，Alphalens 一共提供了三个，分别是：

1. https://github.com/quantopian/alphalens/blob/master/alphalens/examples/event_study.ipynb 这个需要依赖 pandas_datareader, 但现在已没有可用的数据源。因此这个例子是无法运行的。
2. https://github.com/quantopian/alphalens/blob/master/alphalens/examples/event_study_synthetic_data.ipynb 这个不依赖于真实数据，以保证可以运行
3. https://github.com/quantopian/alphalens/blob/master/alphalens/examples/alphalens_tutorial_on_quantopian.ipynb 这是一个全面的文档，中间有一部分讲了 event study

-->

## 3. IC 分析[^ICExample]
Information Coefficient (IC) 是一种用于评估两个变量之间关联性的统计指标。它与回归分析、分层分析之间的关系，我们在<chap>第 2 章</chap>中已经介绍过了。

在 Alphalens 的报表中，IC 分析实际上是由以下函数生成的：

```markdown
al.tears.create_information_tear_sheet(factor_data,
                                       group_neutral=False,
                                       by_group=True)
```

它首先会生成 [](#IC 分析概览)图：

![IC 分析概览](https://images.jieyu.ai/images/2024/08/ic-overall.jpg?width=300)

如果你对我们在<chap>第 2 章</chap>中讲到的知识有所遗忘，我们在这里略微回顾一下。IC 的计算是通过因子值与远期收益之间的 Spearman 相关性来计算的，这是一种秩相关。如果 IC 为 1，则意味着因子值的相对顺序与未来收益相同；而-1 则相反。在一个多空策略中，重要的是股票之间的相对表现，因此，IC 就为我们提供了一个构建多空策略的指标。

在 [](#IC 分析概览)中，我们看到了各期因子排名与收益相关性的系数均值。在这个图中，我们看到 IC 的均值基本上在 0 附近。在一般的相关性分析中，我们会就此认为两个系列不相关。但是，在金融领域，情况则要复杂得多。

Alphalens 的文档认为，这个均值在 0.1~0.3 之间就是表明因子有良好的预测能力，但实际上，如果这个值能达到 0.02 以上，就是非常值得我们考虑的因子。缺少明确地、理论上得到认可的参考标准，是 IC 法的缺陷之一。

Risk-Adjusted IC 是因子的信息系数（IC）除以其标准误差的结果，通常用来衡量因子在考虑风险后预测能力的稳定性。可以简单地把它与夏普率类比。这个数值是越大越好。

t 统计量是用来检验因子 IC 是否显著不同于零的统计量。它是用各期的 IC 值，通过 stats.ttest_1samp 来计算的。通常情况下，t 统计量的绝对值大于 2 表示统计上显著。在 [](#IC 分析概览)，t 值为 2.23，表明因子IC具有较高的可信度。

p 值是衡量 IC 均值是否显著不同于零的概率。它是 t 统计量的伴随概率。较低的 p 值（通常小于 0.05 或 0.01）表明 IC 的均值显著不同于零，因子具有较好的预测能力。在 [](#IC 分析概览)，p 值为 0.027，表明因子IC具有较高可信度。

IC Skew 用来衡量 IC 分布的不对称程度。负值表示 IC 分布偏向于正向，而正值则表示偏向于负向。负偏度通常被认为是好的，因为它表明因子倾向于产生更多的正 IC 值，这意味着因子更有可能正确预测市场方向。但是，在某些极端情况下可能出现较大的回撤。

 IC Kurtosis 衡量 IC 分布的尾部厚度和尖峰程度，它计算出的来是超额峰度。大于零的峰度意味着 IC 分布有较厚的尾部和更高的峰值。小于零的峰度表明数据分布比正态分布更平缓，尾部更轻，因子的稳定性更好。

 <!-- 正态分布的峰度为 3 这几个指标都是求出每日的 IC 系数后，通过 scipy.stats 的相关函数计算出来的，因此具体算法可以参照 scipy 文档-->

接着会按 periods，生成各期的收益信息图，如 [](#1D 收益信息系数图)所示。

![1D 收益信息系数图](https://images.jieyu.ai/images/2024/08/IC-1D.jpg)

阅读这张图，主要也是看波动大小、均值，以及在不同时间点上的异常情况。这部分需要我们随后重点研究。

接下来，会显示 IC 的直方图和 QQ 图，它们是根据 periods 参数，分别绘制的。因此，默认情况下，我们会看到三组直方图 + QQ 图。

![IC 直方图和 QQ 图](https://images.jieyu.ai/images/2024/08/ic-hist-qq.jpg)

均值、标准差、峰度和偏度可以通过直方图直观地展示出来。QQ 图则展示了 IC 的分布是与正态分布有多接近。如果 IC 的分布就是正态分布的话，那么 QQ 图就是一条直线。

!!! tip
    QQ 图常用来判断两个随机变量是否属于同一种分布。如果以两个变量的相同分位数的值分别作为 (x,y), 绘制出来的图是一条直线，则说明两个分布是同分布的。这是巧妙利用人类视觉强大的模式识别能力来解决数学问题的一个例子。

在多数情况下，我们倾向于正态分布，因其性质稳定且可预测。然而，若直方图呈现右偏或 QQ 图显示S型曲线，在投资中也是我们乐于见到的（但此时的 t 值、p 值可能较差）。

<!-- 不是说非正态分布不好，而是说，如果我们得到的分布不是正态分布，那么它会有很多性质不为我们所知，我们很多工具是基于正态分布的，那么这些工具也将不能使用，我们将进入未知水域。未知水域并不是不好，只是未知而已。 -->

如果我们在进行数据预处理时，传入了分组信息，那么 Alphalens 在进行 IC 分析时，还能按组给出 IC 分析结果，如 [](#分组 IC 信息)所示：

![分组 IC 信息](https://images.jieyu.ai/images/2024/08/ic-by-group.jpg?width=75%)

## 4. 换手率分析

换手率图表显示了在一个因子分层中，相对于前一周期不在该分层中的比例。

![分层均换手率](https://images.jieyu.ai/images/2024/08/alphalens-turnover-overall.jpg?width=300)

在概略图之后，Alphalens 将为我们绘制不同周期下，top 和 bottom 分层的换手率时序图。

![1D 分层换手率](https://images.jieyu.ai/images/2024/08/1d-top-bottom-quantile-turnover.jpg?width=75%)

在考虑将信号实际应用于策略时，换手率是一个重要的因素。这个图表展示了你的因子中最高和最低量化等级的换手率，这是采用多空策略时，实际上会进行交易的两个股票篮子。过高的换手率会通过佣金成本侵蚀策略利润。

除此之外，Alphalens 还可以绘制分周期的因子换手率自相关图，如 [](#因子换手率自相关)所示：

![因子换手率自相关](https://images.jieyu.ai/images/2024/08/1D-factor-rank-autocorrelation.jpg?width=75%)

因子自相关是当前因子值排名与其前一个值之间相关性的度量，其计算背后的思路是为了提供另一个衡量因子分层换手率的方法。如果自相关性低，这意味着当前因子值与前一个值关系不大，且从一个时间段到下一个时间段，投资组合的位置频繁变化。如果因子的下一个值显著受其前一个值的影响，这意味着因子的排名方案更加一致（尽管这对预测相对价格变动的能力没有影响）。

这部分报表都是通过函数 create_turnover_tear_sheet 来实现的。

## 5. Reference

关于 Event Study, Alphalens 一共提供了三个示例文档，分别是：

1. [Event Study](https://github.com/quantopian/alphalens/blob/master/alphalens/examples/event_study.ipynb)，需要依赖 pandas_datareader，但现在已没有可用的数据源。因此这个例子是无法运行的。
2. [Event Study Synthetic Data](https://github.com/quantopian/alphalens/blob/master/alphalens/examples/event_study_synthetic_data.ipynb)，使用合成数据
3. [Quantopian Tutorial](https://github.com/quantopian/alphalens/blob/master/alphalens/examples/alphalens_tutorial_on_quantopian.ipynb)，Alphalens 教程，中间一部分涉及 Event Study.

<!--

以下是关于 Alphalens 中累积收益率计算的一段注释：

由于我们通过 Pipeline 每天计算因子值，如果计算累积收益率的周期大于一天，则一天内的收益会与随后几天的收益重叠。这是因为新的每日因子值是在之前的收益实现之前计算出来的。如果我们有了新的因子值但还没有平掉之前的头寸，我们应该如何交易新的因子值呢？

让我们以一个为期五天的周期为例。一种解决方案是每五天交易一次因子值而不是每天交易。不幸的是，这种方法有几个缺点。其中之一是，根据算法开始的日期不同，我们会得到不同的结果；但更重要的是，《资产管理的基本法则》指出：

[ \text{IR} = \text{IC} \sqrt{\text{BR}} ]

我们做的独立投注越多，我们得到的信息比率（IR）就越高。一年中有 252 个交易日，如果我们每五天重新平衡一次，那么我们就有大约 (252/5 = 50) 次投注，而不是 252 次。我们不想通过减少投注次数来降低我们的表现，因此这里有一个解决方案：

给定一个周期 N，累积收益率是通过构建和平均 N 个交错的子组合来计算的（每个子组合在后续的周期 1, 2, …, N 开始），每个子组合每 N 个周期重新平衡一次，并且各自交易初始资本的 1/N。这相当于一个将初始资本分成 N 份并独立交易每一份子资本的算法，每个子资本每 N 天重新平衡一次。由于子组合是在连续的日期开始的，因此该算法每天都会进行交易。

相比于每 N 天交易一次因子的算法，这种方法提高了信息比率（IR），降低了波动性，收益不受开始日期的影响，并且非常重要的是，算法的容量增加了 N 倍，因为资本被分配给了 N 个子组合，滑点影响也减少了 N 倍。

## 习题：

1. 换手率是如何计算的？

```python
def quantile_turnover(quantile_factor, quantile, period=1):
    quant_names = quantile_factor[quantile_factor == quantile]
    quant_name_sets = quant_names.groupby(level=['date']).apply(
        lambda x: set(x.index.get_level_values('asset')))

    name_shifted = quant_name_sets.shift(period)

    new_names = (quant_name_sets - name_shifted).dropna()
    quant_turnover = new_names.apply(
        lambda x: len(x)) / quant_name_sets.apply(lambda x: len(x))
    quant_turnover.name = quantile
    return quant_turnover
```

通过这道题，练习apply函数，复习get_level_values等函数。
-->

## Footnotes

[^predictive-vs-non-predictive]: [Alphalens示例：好因子、坏因子](https://github.com/quantopian/alphalens/blob/master/alphalens/examples/predictive_vs_non-predictive_factor.ipynb)

[^ICExample]: 本节示例来自[Alphalens Tutorial](https://github.com/quantopian/alphalens/blob/master/alphalens/examples/alphalens_tutorial_on_quantopian.ipynb)
