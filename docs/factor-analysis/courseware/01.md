# 导论

因子投资 (factor investing) 是一种投资方法，它认为资产回报可分解为公司特质中可量化的因子。换句话说，找到有效的因子，并将它们以恰当的方式组合起来，就能形成有效的投资策略。

因子投资发端于上世纪马科维茨创立的现代投资组合理论（MPT）[^Markwitz]及威廉·夏普等人创立的资本资产定价模型（CAPM）[^CAPM]。此后，Stephen Ross [^ross] 发现了 CAPM 理论不能解释的异象，于是将证券回报拓展为多个因子来解释，从而开启了多因子投资之路。

## 1. CAPM 与因子投资的起源

CAPM 理论认为资产的预期回报由无风险利率和市场风险溢价（即 $\beta$ 回报）共同构成。CAPM 被认为是现代金融学的七个基本理论之一。威廉. 夏普也因此获得了 1990 年的诺贝尔经济学奖。

CAPM 假设投资者是理性的，希望获得最大化回报并尽可能降低风险。因此，CAPM 模型的目标是计算相对于无风险利率的给定风险溢价，以及投资者可以预期获得的回报。

CAPM 用公式表示如下：

$$
E(R_i) = R_f + \beta(E(R_m) - R_f)
$$

其中：

- $E(R_i)$是资产的预期回报。
- $R_f$是无风险利率，一般使用国债收益率。
- $E(R_m)$是市场的预期回报，常常使用指数的历史回报均值来估计。
- $\beta_i$是风险系数，由以下公式计算：

$$
\beta_i=\frac{Cov(R_i,R_m)}{Var(R_m)}
$$

我们通过一个例子来解读这个公式：如果标普 500 的整体回报率是 12.4%，无风险利率为 0%，而 APPL 的𝛽为 1.1，那么投资者买入 APPL 时期望获得 13.7%的回报，以补偿承担的额外风险。

在上述公式中，需要理解和求解以下概念：

### 1.1. 无风险利率 (Risk-Free Rate)

当投资者决定买入股票等高风险资产时，其目的是为了获得高于无风险资产的收益。一般认为，银行存款利率和国债收益率都是无风险的。不过，国债收益率往往会高于同期银行存款利率，所以我们常把国债收益率当成无风险利率。

不同期限的国债收益率是不同的，一般对标时使用与风险资产预期投资时间相同的期限即可。比如，如果是流动性好的股票，也不打算长期持有，则我们可以使用一年期国债利率作为无风险利率。如果是不动产，则可以使用 5 年期国债利率作为对标的无风险利率。

### 1.2. 市场回报 $R_m$

市场回报率（表示为$r_m$）是指市场上所有证券的平均回报。在很多策略中，我们常常只选择一部分资产作为标的，并使用对应的指数代替市场回报。比如，如果我们是做沪深 300 的指增策略，就可以使用沪深 300 成份股指数作为市场回报来进行比较。在 A 股，依照公司规模从大到小，常见的指数有上证 50、沪深 300、中证 500、中证 1000、中证 2000 等。

### 1.3. 𝛽 贝塔
𝛽 是衡量股票相对于整体市场（例如沪深 300 指数）波动性的指标。换句话说，𝛽 代表回归线的斜率，即市场回报与个股回报的关系。

CAPM 中使用 𝛽 来描述系统风险（或称市场风险）与资产预期回报之间的关系。根据定义，整个市场的贝塔值为 1.0。

* 如果个股的 Beta = 1.0，这意味着其价格与市场完全相关
* 如果 Beta < 1.0（称为"防御型"），这表明该证券理论上的波动性低于市场
* 如果 Beta > 1.0（称为"激进型"），则表明资产价格比市场波动更大

## 2. 寻找 Alpha

有一个与因子投资密切相关的术语，称为 Alpha，这里我们需要介绍一下。

最初，Alpha 是一种基于 CAPM 模型推导出的，用来衡量策略表现的指标，出现在 1968 年麦克. 詹森（Michael Jensen）[^mjessen] 发表的博士论文中，后来也常常被称为 Jensen's Alpha。在这篇论文中，他提出了一种基于 CAPM 模型，用组合（或者投资）的预期回报与实际收益之间的残差来衡量策略表现的方法。这个残差，就被称为 Alpha 回报，也常常被称为超额收益。Alpha 的计算公式是：

$$
\alpha = R_p - E(R_i) = R_p - \beta(E(R_m) - R_f) - R_f
$$
    
这里的$E(R_m)$，就是 CAPM 公式要求的资产预期收益。
    
随着时间的推移，Alpha 一词的用法也越来越广泛。在世坤（WorldQuant），他们认为，Alpha 是一种可以为投资组合增值的交易信号，他们甚至认为 Alpha 是算法、源代码及配置参数的组合。因此，Alpha 也就有了因子的含义。我们常常听说的 Alpha101 因子中的 Alpha，差不多就是这个意思。
    
在 QuantoPian，他们把自己的因子分析框架叫 Alphalens，也包含了寻找 Alpha、即寻找交易信号的含义。Alphalens 也是我们这门课要重点介绍的技术框架之一。

总之，因子与 Alpha 是密不可分的两个概念。因子挖掘，就是为了寻找带 Alpha 的因子；当我们说寻找 Alpha 时，很多时候其实是在说寻找新的因子或者新的交易信号。因此，我们一般也无须区分这些概念之间的区别。

## 3. 从 CAPM 拓展到多因子

CAPM 关于资产回报的模型实际上是提出了一个市场风险因子，但这个因子难以完全解释资产回报的构成。

学术界在接受和熟悉 CAPM 模型之后，也渐渐发现一些 CAPM 不能解释的现象，这些现象被称为异象（anomalies）。对这些异象的研究，就导致了一个个新的因子被发掘出来。随着因子越来越多，于是也就有了因子动物园一说。

1976 年，Stephen Ross 拓展了 CAPM 理论，发表了一篇关于套利定价的理论 [^ross](即 Arbitrage Pricing Theory，简称 APT)的论文。在这篇论文中，他首次提出，证券回报应该由多个因子来解释，从而开启了多因子投资的理论研究。

1981 年，Rolf Banz 发表了一篇论文 [^banz]，基于他对纽交所长达 40 年的数据的研究发现，小市值股票月均收益率比其他股票高 0.4%，从而确立了股票的规模因子：小公司的股票在长期内优于大公司的股票。

1992 年起，尤金·法玛 (Eugene F. Fama) 和肯尼斯·弗伦奇 (Kenneth French) 发表了他们的开创性三因子论文 [^fama]，在市场因子之外，又增加了规模和价值因子。在其论文中，他们使用市值或者流通市值来作为规模因子，使用账面市值比（类似于 PB）作为价值因子 [^blackrock]。

随后，Sheridan Titman 和 Narasimhan Jegadeesh 的研究表明 [^momentum]，投资高动量股票存在溢价，这就发展出来动量因子。

!!! note
    一些文献认为 Momentum 因子由 Mark M. Carhart 等人于 1997 年提出。但实际上 Titman 等提出的时间更早。Carhart 在其论文中也引用了 Titman 的论文。但 Momemtum 因子广泛为人接受则是因为 Carhat 的工作。

2015 年，尤金·法玛和肯尼斯·弗伦奇又把三因子模型拓展为五因子模型 [^fama-5]，即增加了盈利能力和投资能力因子。

2017 年，Stambaugh 和 Yuan[^yuan] 等人又提出了包含两个错误定价的 4 因子模型。这篇论文在附录中提出了构建错误定价因子的 11 种异常现象，并在附录 2 中综合 73 种异常现象。这些进一步拓展了因子挖掘的线索。

上面列举的只是一部分重要和广为人知的因子及模型。实际上，这些因子模型仍不能完全解释股票的未来收益。学术上把这些不能被因子模型所解释的股票特征称为异象 (anomalies)。每一个异象被研究之后，就可能提出一些新的因子。因子越来越多，这种现象被称为因子动物园。

上面列举的，主要是因子投资在学术研究上的进展。实践中的量化投资往往要优先于学术进展。并且，在量化投资中，我们可能用到的因子远远不只这些。根据我们对 2023 年私募投资策略的分析（来源于私募策略路演报告），中频量化策略一般会使用 300 个以上的因子。当然，每家私募在因子数量的计数方法上并不一致：同一算法、不同周期的因子，有的会记为一个因子，有的则会记为多个因子，我们大致知道这一体量就可以了。高频策略方面，有的已经开启了人工智能因子挖掘，据报告，多的有 20 万个因子。不过，这究竟是一种宣传策略，还是确有其事，目前还无法得到证实。

在今天，我们大致可以把因子分类为基本面（质量）类、风险类（波动率、方差）、动量类、成长（财务指标增长率）类、技术指标类因子和另类因子。

从根本上说，因子是关于市场如何运作的想法。可以推断出无数的想法、假设或规则，并且随着新数据和市场知识的快速增加，可能性的数量也在不断增长。这些想法中的每一个都可能是有效的因子，但很多都不是。

为了评估和比较因子对利润的贡献能力，就需要有**体系化的因子分析方法**，即因子分析框架。在本课程中，我们将使用 Alphalens 作为我们的因子分析框架。

因子分析是从原始数据中加工提炼特征，与标的收益进行相关性分析（即因子检验），以得出因子对收益的预见能力的方法体系。下图分割线左侧部分显示了这个体系的一般工作和步骤：

![从因子到投资策略](https://images.jieyu.ai/images/2024/02/factor-analysis-framework.png?width=75%)

## 4. 从因子到因子投资

在上一节，我们介绍了从单因子发展到因子动物园的历程，并且给出了评估单因子有效性的一般方法。通过因子分析，我们可以识别出有效的因子。但是，每个因子的作用周期、收益能力、波动性方面往往都千差万别。

比如，有两个因子，因子分析显示，它们都能有效地预测未来股价的走势。但是，它们的周期可能并不同步。有可能因子 A 与因子 B 在当前周期下，都预示着标的价格在未来会上涨，也可能两者的看法完全相反。这种情况下，我们应该如何做出决定呢？如果我们手里有多个因子，情况会变得更加复杂。

因此，即使单个因子都是有效的，但随着因子越来越多，一个问题被摆上台面：我们已经拥有了成百上千的因子，在这个因子动物园中，到底哪些特征是真的给未来的收益预测提供了独立的信息，而哪些特征则是冗余的？当投资者拥有如此之多的因子时，又该如何做出正确的决策？

此外，如果我们要把所有有效地因子都利用起来进行投资，还涉及到一个如何组合它们的问题。这实际上是一个如何找到合适的工具来处理成百上千个特征的高维信息，实现信息的去噪提纯的问题。

传统的多因子模型的构建方法主要是线性拟合。这种方法无法完美地协调多个因子之间的相互作用关系，现在已经完全被机器学习所取代了。机器学习与传统的多因子模型方法相比，有着突出的优点：

1. 传统计量经济学方法假设因子与股票的收益率之间是简单的线性关系。显然这一假设不是正确的，是囿于计算技术的限制，不得已进行的一种简化处理。而机器学习，尤其是神经网络模型，理论上能够拟合任何线性的、非线性的复杂函数。在多因子组合中，生成的模型归根结底是一个因子权重集，这在神经网络中被称为参数。今天的大模型的参数可以达到数 B 之多，这是传统的线性拟合无法想像的。

2. 机器学习算法拥有更多的对抗过拟合的技术，比如正则化、交叉验证 (cross-validation) 等等，有能力生成更稳健的模型。

当然，机器学习并非万能灵药，它也不能完全解决过拟合、模型不够稳健等问题，但总的来说，已经在技术上向前推进了一大步。

!!! tip
    今天，人工智能已经推进到大模型时代。机器学习也进一步向前发展为神经网络。基于深度学习的 AI 在图像识别等任务上已经碾压人类，在语言模型上正取得重大突破。通过 NLP 技术来进行事件驱动投资也有不错的成绩。尽管如此，在资产管理领域、特别是价格预测方面，在许多任务上还无法超越经典的机器学习算法，特别是像 XGBoost 或者 LightGBM 这样的 ensemble model。

## 5. 从因子模型到交易策略

在因子分析时，为了简化问题，我们使用的是无费用、静态价格和不限量成交的模式。但是，在真实的交易中，必然会存在交易手续费、价格滑点、交易限制（比如 A 股的 T+1 限制、委托量不足、涨跌停限价）等约束。为了回答我们的模型能否在真实的交易中盈利的问题，我们还需要通过回测来验证模型的有效性。回测框架比因子分析框架更为复杂，因而可以为我们模拟出一个接近真实的交易环境来验证模型。

如果回测验证模型有效，我们就可以加上仓位管理和风险控制，将其投入实盘交易中。在《打开量化投资的黑箱》[^blackbox] 一书中，介绍了一种量化交易策略的基本结构，如下图所示：

![量化交易策略的基本结构](https://images.jieyu.ai/images/2024/02/量化交易策略的基本结构.jpg?width=400)

这里我们又一次见到了 Alpha 这个词。在这里，它应该是指任何有效地、能战胜市场的策略，也包括我们通过机器学习组装的多因子策略模型。在回测过程中，已经考虑到了交易成本对策略的影响，对中小资金而言，通过在回测中设置滑点、施加成交量限制基本上就足够了。但对于大的资金，往往还需要实现自己的拆单、跟单算法，以减少委托对价格的冲击，这就是交易成本模型需要完成的工作。

至此，我们就介绍完了从因子分析到交易策略的全过程。

## 6. 关于本课程的编排和学习

这门课面向的对象是专业的量化研究员、或者打算向这个方向转岗求职、或者尽管是其它职业，但决心以专业、严谨的态度探索量化研究的学习者。学完这门课程并完全掌握其内容，你将具有熟练的因子分析能力、掌握领先的机器学习策略构建方法，成为有创新研究能力和比较竞争优势的量化研究员。

### 6.1. 课程内容
课程内容涵盖了因子挖掘、因子检验到构建机器学习模型的过程。课程内容主要由三部分组成：

#### 6.1.1. 因子检验方法

只有掌握了因子检验的方法，我们才能判断挖掘出的因子是否有效。因此，因子检验方法是本课程的起点，从第 2 章开始，到第 7 章结束，共 6 个章节。

![](https://images.jieyu.ai/images/2024/01/alphalens.jpg?float=left&width=200)

我们将从介绍因子检验的原理入手，手动实现因子检验的各个流程；再介绍开源因子分析框架 Alphalens。我们不仅会介绍如何把 Alphalens 用起来，还会重点介绍如何解读它的报表、如何进行参数调优和排错。这部分包含了大量业界经验、正反例对比，内容之深、之新，是你目前在网络上无法看到的。

当你懂得如何通过报表来判断因子的好坏、如何灵活运用 Alphalens 以揭露隐藏在大量数据之下的因子与收益的关系的时候，你就真正成长为因子分析高手。

#### 6.1.2. 因子挖掘

第 8 章到第 12 章构成了课程的第二部分。

因子挖掘，或者说特征工程，是构建交易策略的重要一环，也是量化研究员最日常的工作项。我们将介绍Alpha 101 因子、Ta-lib 和技术指标因子、行为金融学因子、基本面因子、另类因子。

如果掌握这些因子还嫌不够，我们还将在第 12 章介绍因子挖掘方法论。你可能是从我们发表在网上的各种因子与策略挖掘的精彩文章吸引，从而找到我们的，在这里，我们将把掌握的资源和方法论全部教授给你。

![课程中介绍的因子](https://images.jieyu.ai/images/2024/08/factor-nav.jpg?width=400)

在介绍 Alpha 101 因子时，我们把重点放在如何理解它的数据格式和实现算子上。这是理解 Alpha 101 的基础，掌握了这些算子，你就完全能够读懂和实现全部 Alpha 101 因子。然后，我们会介绍其中的几个因子。我们会告诉你如何阅读它复杂的表达式，如何理解作者的思路和意图。

在实现 Alpha 101 因子上，由于已经有许多开源的实现存在，因此，我们不打算重新发明轮子，而是向你介绍一个我们认为实现最完整、正确率最高的一个开源库，并在我们的附录中可以直接上手使用它。此后，你可以把它纳入你的量化兵器库。

<!-- ![Hilbert Sine Wave](https://images.jieyu.ai/images/2024/08/better-sing-wave-indicator.jpg?width=500) -->

在第 9 章，我们将介绍 Ta-lib 库以及它实现的一些技术指标。我们将介绍均线、Overlap、Momemtum、成交量和波动性等 20 个左右的指标。有一些你可能已经比较熟悉了，比如均线，也有一些你可能不太熟悉，比如基于希尔伯特变换的趋势线和 Sine Wave Indicator（如[](#Hilbert Sine Wave)所示）。和其它章节一样，我们仍然会保持足够的研究深度，会介绍像冷启动期、如何将老的技术指标翻新应用（以 RSI 为例）等等。

在第 10 章，我们将介绍基本面因子和另类因子。由于数据获取的难度和质量问题，我们将以介绍原理为主，不一定都给出代码实现。


在第 11 章，我们介绍不属于任何归类，但仍然很重要的因子，比如小概率事件（黑天鹅）因子；我们会引入导数概念，介绍两个有效的一阶导、二阶导动量因子；时频变换构造频哉因子；我们还将介绍一些行为金融学因子，这是当前金融学的热门概念，在短线交易中非常有用。

![傅立叶时频变换](https://images.jieyu.ai/images/2024/08/fft-decomposite.jpg?width=500)


#### 6.1.3. 构建基于机器学习的交易策略

<!-- ![](https://images.jieyu.ai/images/2024/08/machine-learning.jpg?width=500) -->


这一部分我们先快速介绍机器学习的核心概念（第 14 章）。我们会介绍损失函数、目标函数、度量函数、距离函数、偏差、方差、过拟合与正则化惩罚等核心概念。这些概念是机器学习核心概念中偏应用层面一些的概念，是我们不得不与之打交道的概念。

<!-- ![机器学习基础概念](https://images.jieyu.ai/images/2024/08/loss-objective-metrics.jpg?width=500) -->


!!! tip
    如果你需要深入理解机器学习和神经网络、自己能发明新的网络模型和机器学习算法，那么你还需要补充线性代数、梯度优化、反向传播和激活函数等知识。不过，对掌握我们这门课程，达到熟练运用已知的算法模型并会调参调优，掌握我们介绍的概念就足够了。

![Scikit-Learn Logo](https://images.jieyu.ai/images/2024/08/sklearn-logo.png?width=250&float=left)

本课程选择的机器学习库是 sklearn。sklearn 是一个非常强大的机器学习库，以丰富的模型和简单易用的接口赢得大家的喜爱。在第 15 章，我们先向大家介绍 sklearn 的通用工具包 -- 用来处理无论我们采用什么样的算法模型，都要遇到的那些共同问题，比如数据预处理、模型评估、模型解释与可视化和内置数据集。

<!-- ![Cross Validation](https://images.jieyu.ai/images/2024/08/k-fold-cross-validation.png?width=500) -->
第 16 章我们会介绍模型优化方法，这是多数从事机器学习与人工智能的人所能掌握的核心技能，也是我们做出一个优秀的机器学习交易模型的关键之一。我们将演示如何使交叉验证、如何使用网格搜索 (GridSearch)、随机搜索 (RandomizedSearch) 等方法。

<!-- ![Rolling Forecasting](https://images.jieyu.ai/images/2024/08/walk-forward-optimization.webp?width=500) -->

量化领域的机器学习有它自己的特殊性，比如在交叉验证方面，我们实际上要使用的是一种称为 Rolling Forecasting（也称为 Walk-Forward Optimization 的方法）。我们将在第 16 章的最后部分，详细介绍这种方法以及它的实现。

接下来我们介绍一个聚类算法（第 17 章）。在量化交易中，Pair Trading 是一类重要的套利策略，它的先决条件是找出能够配对的两个标的。这一章我们将介绍先进的 HDBSCAN 聚类方法，演示如何通过它来实现聚类，然后通过 statsmodels 中的相关方法来执行协整对检验，找到能够配对的标的。最后，我们还将演示如何将这一切组成一个完整的交易策略。


在第 18 章，我们将介绍 XGBoost，这是一种梯度提升决策树模型。由于金融数据高噪声的特性、以及难以获得大量有效标注数据原原因，使得梯度提升决策树模型目前仍然是在量化交易领域应用最广泛、效果最好的机器学习模型。

![XGBoost Model](https://images.jieyu.ai/images/2024/08/scheme-of-xgbost-model.jpg?width=500)


我们会先从决策树模型讲起，介绍 XGBoost 一路走来的优化历程。然后以一个详尽的例子，介绍如何使用 XGBoost，训练一个模型并深入到它的内部：我们将可视化这个模型的重要特征、绘制出它的模型树。最后我们以介绍在 XGBoost 如何进行交叉验证和调优结束。

在做了大量理论与实操的学习之后，我们已经完成了所有铺垫，现在是时候学习如何构建基于 XGBoost 的量化交易策略了。我们将抛弃几乎是无效的端到端训练的方式（即输入价格，希望能预测出下一个价格），改用一些探索性、但更加有效的策略示例。

我们将在第 19 章里，介绍基于 XGBoost 回归模型，如何构建一个价格预测模型。我们会介绍策略原理、实现和优化方案。尽管我们构建的是一个价格预测模型，但它决非你在网上看到的那种端到端的 toy 类型的模型！

另一个模型将在第 20 章里介绍，它是基于 XGBoost 的分类模型构建的一个交易模型。换句话说，它不负责预测价格，但能告诉你应该买入、还是卖出。在本章中，我们还要介绍如何制作标注工具

这两个示例指出了在目前条件下，应该如何使用机器学习构建策略的根本方法 -- 由于金融数据饱含噪声，所以我们不能指望端到端的模型能够工作。但如果我们能清晰地定义问题，找出有效特征，机器学习就会非常强大！这将是你在市场上战胜他人的利器。

第 21 章我们会对 XGBoost 模型进行更深入一些的拷问。我们介绍另一个梯度提升决策树模型的实现，即由微软开发的 LightGBM。一般认为，它在性能上要强于 XGBoost，内存占用更小，在其它指标上与 XGBoost 相比各有千秋。

![LightGBM, by Hossain@medim](https://images.jieyu.ai/images/2024/08/lightGBM-by-Hossain-medium.jpeg?width=400)

我们已经介绍了三个非常实用的例子，涵盖了套利交易、价格预测和交易模型。但是，资产管理中还有一个重要的课题，就是组合管理。基于机器学习，我们如何实现组合管理？我们也将在这一章回答这个问题。

我们的课程将结束于第 22 课。我们将介绍深度学习的先驱--CNN 网络在 K 线模型识别上的应用。我不认为 CNN 网络在 K 线模型识别上有任何优势，我会详细地介绍为什么。但是，也许你有兴趣解决这个问题，所以，我还是会介绍 CNN 在 k 线识别上的一般做法。

比起深度学习，我更看好强化学习在交易方面的应用。在加密货币、商品期货上，重要的不是选择投资品种，而是根据市场的变化决定交易时机、仓位、杠杆，这天然就是一个强化学习问题。我将介绍强化学习的基本概念、相关学习资源。

最后，还有两个无法归入到上面所有这些类别 -- 无论是机器学习、深度学习还是强化学习，但仍然非常重要的智能算法 -- Kalman Filter 和 Genentic Algorithm。

整个课程的大纲可以在[这里](https://www.jieyu.ai/articles/coursea/factor-ml/syllabus.html)查阅。

### 6.2. 课程编排

在学习本课程之前，学员需要掌握的 Python 编程基础，包括：

1. Python 基础语法和常用库，包括时间日期、字符串、文件 IO、列表、字典、模块导入、typing等等。
2. 统计学知识。需要有大学基础的统计学知识，对一些基础概念有初步了解，这部分内容在《量化 24 课中有详细讲解》。
3. Jupyter Notebook。我们提供了《Notebook 入门》和《Notebook 高级技巧》供大家学习。
4. Numpy 和 Pandas。需要有入门级的知识。课程中使用了大量 Numpy 和 Pandas 高级技巧，如果没有事先掌握，会增加阅读示例代码（包括听课）的难度。建议在上本课时，同时学习我们的《量化交易中的 Numpy 和 Pandas》课程（免费）。我们也会在讲课中讲解一些语法知识，但不是课程重点。
5. 对机器学习、神经网络有一定的认识。在我们讲解机器学习核心理论时，这会帮助你跟上进度。

如果不满足前两个条件，学习本课程将比较有困难。如果不满足后面三个条件，你仍然可以学习本课程，但需要多花一些时间来熟悉这些内容。

课程正文内容以应用为主，对机器学习的核心理论，只讲到在应用中必须接触的部分。在几乎每一章都提供了大量拓展阅读材料和注释，供希望在具体细节或者底层体系上深入研究的同学。对这部分内容，没有时间的同学可以跳过，不影响课程学习效果。

课程附有大量习题。习题的目的是：

1. 部分示例中涉及一些编程技巧，在量化研究中比较常用，所以编入习题强化记忆。
2. 部分话题的结论是开放性的、探索性的，不适合作为正式内容讲授。
我们为本课程精心准备了大量的习题。你应该充分利用这些习题来巩固和拓展自己学到的知识与技能。这些习题多数是自动批改的，因此你可以及时了解到自己知识掌握的程度。

习题分发、提交作业和获取老师批阅结果流程都是自动化的，通过 Nbgrader 来实现。如果你之前没有接触过 Nbgrader，可以在 [课程须知](../课程须知.ipynb) 的关于作业一节中掌握它的使用方法。

本课程只涵盖了量化交易中的部分知识。如果要独立从事交易或者做完量化全栈工作，建议补充学习 [《量化 24 课》](https://www.jieyu.ai/articles/coursea/24lectures/intro/)。本课程与《量化 24 课》的区别是，本课程内容更为专精，《量化 24 课》内容更广泛，涵盖更全面。

### 6.3. 课程目标

在学完本课程之后，你将会获得以下能力（或工具）：

1. 掌握 Alphalens 因子分析框架，并在工作中运用。
2. 懂得如何阅读 Alphalens 的分析报表，根据报表判定因子有效性。
3. 懂得如何运用 Alphalens 挖掘因子的价值。
4. 在你的量化兵器库中，加入 Alpha101 因子库、Ta-lib 因子库、TsFresh 因子库及其它我们介绍的过的 10 余中因子。
5. 掌握因子挖掘方法论，通过变更交易品种、交易周期、因子参数等方法，扩充自己的因子库（需要完全掌握统计基础知识）。
6. 能实现统计套利 (Pair Trading) 交易策略，有能力在众多标的中搜索出可以配对的品种。
7. 掌握 XGBoost 模型，以及如何基于 XGBoost 构建股票价格预测模型、交易模型和资产组合。这将很可能是你今后工作的起点和竞争优势。
   
### 6.4. 其它

在本课程中，我们引用了众多学术论文。这里有必要强调一下学术视角与产业视角之间的差异。学术论文旨在突出其学术价值，因此更加侧重于理论抽象与形式化推导，这样的研究往往能够得出具有更强普适性和超越时空局限性的结论。然而，这也意味着它们有可能与现实世界脱节，难以直接应用于具体的、即时的投资决策中。事实上，不乏有学者在学术领域取得了辉煌成就，但在个人投资方面却遭遇挫折的例子，这种情况并不少见。

!!! tip
    现代金融理论有时候是过于注重数学了，却忘了即使没有高等数学，人类的经济活动也已经存在了数千年。杨振宁曾经评论弦论道，弦论过于依赖于数学上的美丽，而忽视了物理学中实验验证的重要性。这种洞见不应该只限于物理学。

比如，芒格经常讲，有的诺贝尔经济学奖获得者自己根本做不好投资，不得已卖掉自己的资产管理公司，转而委托伯克希尔. 哈撒韦来管理他们的资产。

这里我们举一个真实的例子，以说明学术届与产业界在策略研究上，分别应该采用什么样的态度，两者应该有什么样的差别。

在 Liu 等人 [^liu] 的论文《Size and value in China》中，他们排除了市值最小的 30%的股票，因为在论文发表期间及之前的一段时间内，由于 IPO 采用的是审核制，导致一些小市值公司具有壳价值。从学术研究的角度来看，这种排除是有其合理性的；但从投资的角度来看，正是这一制度赋予了这些小市值公司独特的溢价，将它们排除在外可能并不明智。

因此，我们需要正确对待学术论文（以及券商研报），即便是本课程中介绍的学术论文也不例外。在学习这些论文时，要坚持独立思考的原则，特别注意论文中的假设条件是否能在现实中得到满足，切勿机械地照搬论文结论。总的来说，这些研究的治学方法、思路和技巧是我们学习和借鉴的重点。

另外，大量的学术研究集中在横截面因子的研究上，对时序因子（多数时序因子是量价技术因子）研究的偏少一些。这两个研究领域各有优缺点，我们不能简单地认为哪一种更好。更何况，结合到我们具体的投资领域，可选择性就没那么多。比如，如果我们是做加密货币或者期货投资，那么研究时序因子就几乎是唯一的选择。

因此，本课程尽管是以因子分析来命名，也以因子投资作为开头语，但资产定价理论并不是量化交易的全部理论，我们也没有把课程内容局限在资产定价的窠臼中。在投资实践中，资产定价、统计套利、CTA、机器学习，...，所有这些，只要能拿来使用的都会使用，这才是鲜活的现实。

## 7. 拓展阅读

1. Mark M. Carhart, 1997, The Journal of Finance, On Persistence in Mutual Fund Performance. 该文进一步证实了 Jegadeesh 等人提出的 Momentum 效应，但提出了不同的解释。该文还指出一个事实，对共同基金的研究，很多数据库是存在幸存者偏差的。作者因此自己构建了一个共同基金的数据库，以消除幸存者偏差。这项工作可能是该论文生产过程中最费时间的部分。也可能正是这个原因，使得这篇论文具有了独特的价值，获得了超过 2000 次引用。[PDF](https://onlinelibrary.wiley.com/doi/pdfdirect/10.1111/j.1540-6261.1997.tb03808.x)
2. Jianan Liu et al, 2019, Journal of Financial Economics, Size and Value in China. 关于规模与价值因子在中国市场表现的研究。[PDF](https://hub.hku.hk/bitstream/10722/273695/1/content.pdf?accept=1)
3. 林晓明，华泰金工，2017, 五因子模型 A 股实证研究. [PDF](https://crm.htsc.com.cn/doc/2017/10750101/328d4a72-d8d7-4bc5-91a3-c23fd922a5f5.pdf)
4. 侯科伟、陈雪和张璐，2015, A comparison of new factor models. [PDF](https://deliverypdf.ssrn.com/delivery.php?ID=513103103009123000104104091087102074097042014048023025121114077117000103113096073124027012010041026121034089076031125114113008017009044015049100024113070083029084068065013083112121088081089102095007125074124026005114097066125083024124117085015104028083&EXT=pdf&INDEX=TRUE)

## 8. Footnotes

[^mjessen]: Michael Jessen，美国当代著名的金融经济学家，他在公司治理、代理理论、资本结构理论等方面，对金融与经济学领域作出了诸多贡献。Michael C. Jensen, "The Performance of Mutual Funds in the Period 1945-1964," *Journal of Finance* 23, no. 2 (1968): 389-416. <a href="http://e-m-h.org/Jens68.pdf">PDF</a>

[^ross]: Stephen Ross， MIT 斯隆管理学院金融经济学教授。罗斯提出了 Arbitrage Pricing Theory 和二项式期权定价模型（与 John Carrington Cox、Mark Rubinstein 等人一起）。他是风险中性定价这一基本金融概念的创始人。Stephen A Ross, The arbitrage theory of capital asset pricing, Journal of Economic Theory, 1976, https://doi.org/10.1016/0022-0531(76)90046-6. <a href="https://www.top1000funds.com/wp-content/uploads/2014/05/The-Arbitrage-Theory-of-Capital-Asset-Pricing.pdf">PDF</a>

[^banz]: Rolf Banz, 1981, Journal of Financial Economics, The relationship between return and market value of common stocks. <a href="https://search.dailystocks.com/Banz_sizeeffect_1980.pdf">PDF</a>

[^fama]: Eugene F. Fama, 1993, Journal of Financial Economics, Common risk factors in the returns on stocks and bonds. <a href="https://www.bauer.uh.edu/rsusmel/phd/Fama-French_JFE93.pdf">PDF</a>

[^momentum]: Jegadeesh, Narasimhan, 1993, The Journal of Finance, Returns to Buying Winners and Selling Losers: Implications for Stock Market Efficiency. <a href="https://www.bauer.uh.edu/rsusmel/phd/jegadeesh-titman93.pdf">PDF</a>

[^blackrock]: 对价值因子，贝莱德 Black Rock）有不同的看法，他们把价格因子当成价值因子，把 PE 当作质量因子的一部分。贝莱德（使用的五因子是 Value, Quality, Momentum, Size 和 Minimum Volatility。这里需要对价值因子 (Value) 和质量因子作出解释。他们把价值投资视为在购物中心讨价还价。例如，如果您对不同价位的两双鞋没有其它偏好的话，就应该购买两双中更便宜的鞋。因此，价值因子被定义为同行业中，价格更低的股票。质量因子则是能反映公司盈利能力、低杠杆等特质的那些因子，即市盈率、市净率、市销率、现金流等。<a href="https://www.ishares.com/us/insights/what-is-factor-investing">更多</a>

[^fama-5]: Eugene F. Fama, 2015, Journal of Financial Economics, A five-factor asset pricing model. <a href="https://tevgeniou.github.io/EquityRiskFactors/bibliography/FiveFactor.pdf">PDF</a>

[^blackbox]: 打开量化投资的黑箱，里什.纳兰著。Wiley 出版。中文版由机械工业出版社出版。

[^yuan]: Stambaugh, Yuan, 2017, Review of Financial Studies, Mispricing Factors <a href="https://www.nber.org/system/files/working_papers/w21533/w21533.pdf">PDF</a>

[^liu]: Liu, Stambaugh, Yuan, 2019, Size and value in China. <a href="https://www.sciencedirect.com/science/article/pii/S0304405X19300625#sec0003">PDF</a>

[^2015a]: Hou, Xue, Zhang, 2015, Review of Financial Studies, Digesting Anomalies: An Investment Approach. <a href="https://academic.oup.com/rfs/article/28/3/650/1574802">PDF</a>

[^hou2021]: Hou, Mo, Xue, Zhang, Review of Finance, An Augmented q-Factor Model with Expected Growth. <a href="https://academic.oup.com/rof/article/25/1/1/5727769">PDF</a>

[^Markwitz]: Markowitz 的 MPT 理论（Portfolio Selection, The Journal of Finance, 1952）是现代金融的 7 个基本理论之一。该理论提出了均值-方差优化框架，这是多因子模型的基础之一。MPT 强调通过分散化投资来降低风险，并提出了投资者应该如何构建投资组合以获得最佳风险收益比的观点。马科维茨于 1990 年获得诺贝尔经济学奖。

[^CAPM]: CAPM 模型威廉. 夏普，也是现代金融的七个基本理论之一。它引入了一个单一的风险因子——市场风险，用于解释资产的预期收益率。这为后来的多因子模型奠定了基础。威廉. 夏普也于 1990 年，与他的老师马科维茨一起，获得了诺贝尔经济学奖。Capital Asset Prices Model: A Theory of Market Equilibrium Under Conditions of Risk, William Sharpe， The Journal of Finance, 1964。
