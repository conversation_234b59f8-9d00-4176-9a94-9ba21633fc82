# ALPHALENS 高级技巧

在前面的章节中，我们讨论了如何分析 Alphalens 报表，已经涉及了一些比较深入的话题，比如如何判断因子有效——包括有哪些经验值；比如某些指标在 Alphalens 内部是如何计算的，等等。

弄懂 Alphalens 在内部是如何计算的非常重要。例如，如果 Alphalens 的收益计算是基于多空组合的，而你不了解这一点，在一个只能做多的市场中使用这个策略，就可能失败。

在这一章，我们还将继续深挖 Alphalens 的一些内部机制，通过一些示例，让你完全掌握 Alphalens，懂得如何灵活运用 Alphalens，去解锁隐藏在层层迷雾之下的因子与收益之间的关系。

但是，我们首先从<chap>第 5 章</chap>开头没讲完的话题说起，即，在 Alphalens 分析过程中，如果出错会怎样？又可能在哪些方面出错呢？

## 1. 功能性错误

在前面的课程中，我们运行 Alphalens 一直都很顺利。但实际上，当你自己进行实操时，很可能会遇到一些简单、但常见的功能性错误。

### 1.1. 过时的 Alphalens 版本

如果你是自学 Alphalens，那么你可能遇到的第一个问题就是，当调用 create_full_tear_sheet 时，出现以下错误：

![Alphalens 版本过时](https://images.jieyu.ai/images/2024/08/alphalens-out-of-date.jpg?width=75%)

!!! note
    在我们的课程环境中，你不会看到此警告，是因为我们已经完成了版本更新。

这个错误是由于 Alphalens 版本过时导致的。

Alphalens 使用了早期版本的 pandas（0.25），但是，我们安装在同一个虚拟环境中的其它 Python 库，很可能将 Pandas 更新到了较新的 2.x 版本，这样就导致 Alphalens 的错误。Alphalens 最初是由 Quantopian 公司开发的。在理想状态下，Quantopian 应该及时更新 Alphalens 中使用的 Pandas 版本。但是，在 Quantopian 被收购之后，他们就不再维护之前的开源库了，也就导致了 Alphalens 失去了更新。

不过，在 Quantopian 被收购之后，其它公司（比如 Quantconnect, Quantrocket 等）承担起了维护 Quantopian 开源库的任务，修复了这个问题。但是，其他人无法以 Quantopian 的身份来发布这些维护，因此，这些更新都只能以新的库名字来发布，这样导致我们通过以下命令来安装的 Alphalens 版本就是过时的版本：

```bash
!pip install alphalens
```

我们最常用的一个，是 Alphalens-reloaded[^alphalens-reloaded]，由 ML4Trading 团队维护。要安装这个版本，请使用以下命令：

```bash
!pip install alphalens-reloaded
```

!!! note
    如果你是在 notebook 中，当已经出现 [](#Alphalens 版本过时）的错误后再来修复，请先卸载 Alphalens，然后再安装 Alphalens-reloaded。并且要重启 kernel。

### 1.2. MaxLossExceedError

我们在前面讲过，Alphalens 在计算因子（调用 get_clean_factor_and_forward_returns）时，会对缺失进行丢弃，从而导致输出的数据记录少于输入数据记录。当这种丢弃超过一定的比例时，继续分析就不再有意义。

所以，在这种情况下，Alphalens 会抛出一个 MaxLossExceedError 的错误：

Dropped 36.3% entries from factor data: 34.7% in forward returns computation and 1.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).

在 [](#example-1) 中，如果我们将 end 的时间改到距今天不足 10 个月，就会很容易出现上述提示。这段提示已经比较清晰地告诉我们，主要原因在计算远期收益时，时间跨度不足。因此，我们改正的方法也很简单，就是把 end 向前推，使得时间跨度长到足够覆盖最后一期因子的远期收益计算周期，这样就可以避免因远期收益无法计算导致的 drop。

### 1.3. 时区问题

在示例中，我们加载行情数据是通过函数 load_bars 来实现的。它将返回一个 DataFrame，其索引是 date 和 asset，列名分别为 OHLC，price 等。

在这个函数中，无论行情数据本来是日期 (datetime.date) 还是时间戳 (datetime.datetime)，load_bars 都会将其转换成为时间戳，并且带有时区信息。

如果你是自己实现 load_bars 的话，也需要遵循这个约定。否则，Alphalens 在对齐因子数据与远期收益时，就会报告以下错误：

![时区问题](https://images.jieyu.ai/images/2023/07/index_has_no_attribute_tz.png?width=500)

目前很多数据源提供的行情数据，在日期字段上甚至使用的是字符串表示的，更不要说带时区信息了。但是，Alphalens 拒绝对 naive 时间对象进行比较。所以，尽管我们处理 A 股数据时，由于都在同一时区，时区信息并不重要，但我们在将因子传递给 alphalens 之前，还是得加上时区信息。

## 2. 因子的单调性

前面我们已经或多或少提及过，因子与收益之间，应该满足单调性原则。这里我们联系理论，再强调一下。

<!-- 从 Alphalens 的参考文档中，我们可以了解到，因子数据应该具有单调性，并且，一个好的因子是，因子值越大，对应的收益率也越大。考虑到这门课程的专业性，我们介绍一点原理。-->

如果我们回到最基本的因子分析的数学公式，就很容易理解因子值应该具有的数学特性：

$$
\alpha = R_p - E(R_i) = R_p - \beta(E(R_m) - R_f) - R_f
$$

在这个式子中，$ \alpha $ 是我们通过回归分析要得到的结果（在 Alphalens 报表中，显示为 Returns Analysis 中的 Anna Alpha 项）；$R_m$是多个股票的组合收益，由 Alphalens 自行计算得出，一般称为前向收益；$\beta$则是因子值，或者说是所谓的因子暴露，需要我们来提供。

很显然，我们进行回归分析的前提是因子值与组合收益之间存在线性关系。只有我们提供的因子值数据与组合收益之间存在线性关系，那么这个拟合求解才是有效的，否则，即使我们一定能得到某个结果，这个结果也没有意义。

<!-- 讲解，提供 y=ax + b 直线及噪音图；和 y = xsinx + b 直线及噪音图。说明两者数据交换求解是无意义的。-->

!!! hint
    当然，在因子分析中，绝对的线性关系几乎不存在，我们都是在允许适度的噪声下来讨论这个问题的。

如果一个因子有效，即它与组合收益之间存在线性关系（不考虑噪声），但我们在数据加工处理过程中，丢失了这种线性关系，也会得到因子无效的结论。

Alphalens 在进行因子检验时，也隐含了这个要求。不仅如此，它还假设因子与组合收益之间是单调递增关系，即因子值越大，组合的收益就会越高。只有在这种情况下，因子报表在解读上才有一致性。

现在，让我们回到 [示例 4-1](04.md#example-4-1) 中，注意看 rolling_slope 函数：

```python
import numpy as np
from numpy.typing import NDArray
from numpy.lib.stride_tricks import as_strided
def rolling_slope(close: NDArray, win:int, *args):
    stride = close.strides

    slopes, _ = np.polyfit(np.arange(win), 
                           as_strided(close, (len(close)-win+1, win), 
                           stride+stride).T,
                           deg=1)
    left_padd_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_padd_len, 0), mode='constant', constant_values=np.nan)
    return slopes
```

如果我们这里不是使用np.polyfit来实现这个因子，而是使用scipy.stats中的linregress，我们的实现可能会是这样：

```python
def moving_slope(close: NDArray, win:int, *args):
    # 创建滑动窗口的视图
    shape = (win, close.size - win + 1)
    strides = (close.itemsize, close.itemsize)
    close_windows = np.lib.stride_tricks.as_strided(close, shape=shape, strides=strides)
    
    # 对每个窗口应用linregress，获取斜率
    slopes = np.apply_along_axis(lambda x: linregress(np.arange(win), x)[0], axis=0, arr=close_windows)
    
    left_padd_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_padd_len, 0), mode='constant', constant_values=np.nan)
    return slopes
```

两种方法，计算出来的因子，互为相反数关系。因此，如果rolling_slope算出来的因子与收益是正相关关系，那么moving_slope算出来的因子与收益就是负相关关系。在2024年1月到2024年7月间，后者的分层因子收益均值图类似下图：

![moving_slope,2024/1/1-2024/7/1](https://images.jieyu.ai/images/2024/10/mono-decrease-factor.jpg)

这种情况下，Alpha 收益将为负数，累计收益图也很不好。但这不意味着因子本身不行，只是刚好原始因子与组合收益存在负相关关系。实际上，moving_slope与rolling_slope，背后的交易原理都是一致的，只不过计算方法不同，导致取值成了相反数。在使用moving_slope的算法时，将其返回值乘以-1，就可以将因子修复。

更有说服力的例子是 RSI 和低波动率因子。对 RSI 因子来说，一般认为，RSI 值过大是超买信号，应该减仓或者卖出；RSI 值过小是超卖信号，应该加仓或者买入。因此，RSI的原始值与远期收益之间是负相关关系，需通过取反修正（如 factor = -RSI）以匹配检验要求。但是Alphalens在进行分层检验时，最基本的思想就是对因子值大的资产做多；对因子值小的资产做空；刚好与RSI本来的思想相反。


低波动因子也是如此，一般来说，波动率越低，公司的业绩可能越稳定；因此，它与远期收益之间也是负相关关系。

如果我们不进行修正，那么因子分析将是无效的。

我们对因子进行修正，从而得出了更好的结果，这是不是过拟合呢？确实要注意，**修正因子可能导致过拟合**。比如，在我们的示例中，使用的数据集尽管达到了 4000 支，可谓没有采样偏差了，但是，在时间跨度上却很短，只有半年。也许，在多数时间里，是未修正的 slope 因子能得到更好的结果。而我们的修正，只是使得这个因子在局部时间上表现更优，这就是过拟合。

但是，是否存在过拟合不是我们一开始要考虑的问题。一开始我们要考虑的是，如何把想办法因子与收益之间确实存在的线性关系揭露出来。然后，再拉长测试时间，选择不同的 universe（或者更大的）来测试，看看是否存在过拟合问题。

## 3. 使用哪一个价格？

Alphalens 在计算 T0 期 1D 的远期收益（其它各期与此类似）时，是用 T1 的 price 与 T0 的 price 来计算的。这个远期收益的具体含义是什么，取决于我们在调用 get_clean_factor_and_forward_returns 函数时，通过 price 参数传递给它的价格数据是如何获取的。

从<chap>第 4 章</chap>，我们都是从一个事先缓存的数据集中开始分析。这个数据集已经包含了用以计算远期收益的价格数据。有一些细节隐藏在这个数据集中。如果你已经完成了<chap>第 1 章</chap>的练习，那么你应该知道，其中的价格数据是这样得来的：

```markdown
df['price'] = df.open.shift(-1)
```

也就是说，我们把原本 T1 期的开盘价，赋值给了 T0 期的 price。这意味着，T0 期 1D 的远期收益是通过 T2 期的开盘价与 T1 期的开盘价计算出来的。

我们这里特别强调这个细节，是很有可能你会直接把 df['close'] 作为 price 参数传给 get_clean_factor_and_forward_returns。这意味着我们将使用 T1 期的 close 与 T0 期的 close 来计算 1D 的远期收益。

这样做就可能引起未来数据泄露问题。因为我们计算 T0 的因子时，都是基于 T0 已结束、已收盘时取得的各种数据。既然已收盘，我们就不可能再以 T0 的价格买入。下一个最邻近的买入点就是 T1 的开盘价。

!!! info
    多数场合下，我们不应该使用 T0 的 close 作为买入价。但是，事无绝对。如果你管理的资金规模小，使用 T0 的 close 作为买入价，也是可以的。这要求我们在尾盘集合竞价期间就能计算出因子，得到交易信号。尽管在集合竞价期间看到的价格与最终收盘价可能还有一些差异，但是，对小资金量是可以接受的，你可以把它当成小小的滑点。

## 4. 日线以上级别的因子

到目前为止，我们讨论的因子都是日线级别的。但是，一些基本面类的因子常常要以月度、甚至季度为单位来进行分析。因为基本面的数据往往是以季度为单位来更新和发布的，当然也就难以对日线级别的股价波动产生影响（除了财报发布前后几天）。

看起来无论因子是哪一个级别，因子分析的原理都不会有任何区别，所以，为什么我们要专门用一个小节来讨论呢？

这是因为，由于 Alphalens 的实现原因，导致日线级别的因子分析与日线以上级别的因子分析有一些微妙的差别，并且容易引入错误。

让我们从一个示例开始。

<Example 1/>

```python
#示例 6-1
from alphalens.utils import get_clean_factor_and_forward_returns
from alphalens.tears import create_returns_tear_sheet

import pandas as pd

np.random.seed(78)

def load_month_bars(start, end, universe=100):
    barss = load_bars(start, end, universe=universe)

    def resample(g):
        df = g.droplevel('asset')
        df['date'] = df.index
        df = df.resample('M').agg({
            'date': 'last',
            'close': 'last',
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'volume': 'sum',
            'amount': 'sum'
            }).set_index('date')
        df["price"] = df.open.shift(-1)
        return df.dropna()

    df = barss.groupby('asset').apply(resample)
    return df.swaplevel(0,1).dropna()

def low_volatility(close: pd.Series, win:int, *args):
    return 1 / (1e-7 + close.pct_change().rolling(window=win).std())

# 1. 获取行情数据
start = datetime.date(2018, 1, 4)
end = datetime.date(2023, 12, 29)
barss = load_month_bars(start, end, universe=100)

# 2. 计算因子
factors = []
for group in barss.groupby(level='asset'):
    close = group[1].close

    low_vol = low_volatility(close, 10)
    factors.append(low_vol)
    
factor = pd.concat(factors).rename_axis(index={'frame':'date'})

# 3. 提取价格数据
prices = barss['price'].unstack(level=1)

# 4. 预处理
factor_data = get_clean_factor_and_forward_returns(factor, prices, max_loss=0.4)

# 5. 生成报告
create_returns_tear_sheet(factor_data)
```

<!--
这一段代码中使用了循环，对初学者来讲这样更容易理解。但它有更简洁的写法，当你熟悉 pandas 的一些操作之后，更简洁的写法反而会降低认知负担。我们将在第 7 章来解决这个循环。
-->
报表都很正常地输出了，没有任何问题。年化收益数据有点离谱，不过，这很可能是因为我们的universe中只有100支股票引起的，目前倒也不用担心。

但是，如果我们深入到每一步的细节，我们就很快发现问题。

首先，我们注意到收益分析中的时间单位仍然是 1D、5D 和 10D，按理说，这些时间单位应该被改为 1M（M 代表月），5M 和 10M，对吧？

实际上，在 Alphalens 中，并没有这样的记法。如果你是在检验一个日内因子，那么可能会看到诸如 30m, 1h 之类的列名字，但永远不会看到 1M, 5M 等等。

这会有什么问题吗？还是说，仅仅只是一个名字的问题？

为了回答这个问题，我们先来看一下 prices 数据。

```python
prices.tail(20)
```

我们看到，有一些行包含了大量的 NaN 数据，并且这些行都有一个规律，它们的日期都不是月度的收盘日。因此对这些月份来说，就存在着多条 prices 数据。

计算远期收益是由 get_clean_factor_and_forward_returns 完成的，它通过参数 periods 来指定计算远期收益的窗口，默认是 (1, 5, 10)，但这个参数并没有携带单位。所以，Alphalens 在计算远期收益时，就只能按记录条数来算。因此，在计算 2022 年 6 月 30 日（的 1，5，10 远期收益时，它可能会把 2022 年 7 月 22 日、7 月 29 日都分别当成一个月，而实际上这里还没有跨月。

因此，在进行月度因子检验时，如果我们仍然按照之前的方式操作，基础的远期收益计算就会出错，进而导致后续步骤也会出错。

这里给出月度因子的正确实现方法。

<!-- 习题

在计算月度因子收益时，为了计入月末效应，我们可能希望以每个月的开盘价买入，以收盘价卖出，而不是以下个月的开盘价卖出。这在Alphalens中要如何实现？

-->


<Example 2/>

```python
from alphalens.utils import get_clean_factor_and_forward_returns
from alphalens.tears import create_returns_tear_sheet

def low_volatility(close: pd.Series, win:int, *args):
    return 1 / (1e-7 + close.pct_change().rolling(window=win).std())

np.random.seed(78)
start = datetime.date(2018, 1, 4)
end = datetime.date(2023, 12, 29)
day_barss = load_bars(start, end, 100)
barss = (
    day_barss.groupby("asset")
    .apply(
        lambda x: x.droplevel("asset")
        .resample("1M")
        .agg(
            {
                "close": "last",
                "open": "first",
                "high": "max",
                "low": "min",
                "volume": "sum",
                "amount": "sum",
            }
        )
    )
    .swaplevel(0, 1)
)

factors = []
for group in barss.groupby(level='asset'):
    close = group[1].close

    low_vol = low_volatility(close, 10)
    factors.append(low_vol)
    
factor = pd.concat(factors).rename_axis(index={'frame':'date'})

# 3. 提取价格数据
prices = barss['open'].unstack(level=1).shift(-1)

# 4. 预处理
factor_data = get_clean_factor_and_forward_returns(factor, prices, max_loss=0.4)

# 5. 生成报告
factor_data.rename(columns = {
    '1D': '21D',
    '5D': '105D',
    '10D': '210D'
},inplace=True)

# 5. 生成报告
create_returns_tear_sheet(factor_data)
```

[](#example-2) 的代码复用了 [](#example-1) 前半段，但有两个关键不同。

<!-- 
在第 23 行、25~28 行，我们打印了重采样前后两个集合的差集，以及重采样后的 freq。重采样过程中丢失了 187 条左右的记录。最后，重采样记录的 freq 为 MonthEnd
-->

这一次得到的年化收益与上一个示例中的差别很大，但各组报表之间是自洽的。

这里的核心改动有两点，其一，在计算合并的因子收益之前，我们对数据进行了重采样，使得每支资产的每一项数据，都对齐到月末。这就保证了基础的远期收益计算是正确的。

其二，在生成合并的因子之后，我们对列名进行了修改，将 1D, 5D, 10D 修改为 21D, 105D, 210D。这就保证了年化收益的计算是正确的。

!!! warning
    仍然不要相信这里的因子检验结果！我们在此留了一处错误，特意放到第10课修正。

<!--
pages/06/40.md
-->


## 5. 深入因子分层

我们在<ref>[](04.md)</ref>中的<ref>如何实现分层回溯</ref>一节中，已经介绍了因子分层的具体细节，指出参数 quantiles 和 bins 控制着因子分层，但并没有探讨为什么要调节这两个参数。

我们需要调节这两个参数，原因是，因子与远期收益之间的关系是复杂的。通过调节这两个参数，从而改变分层的方式，有可能最好地暴露因子与收益之间的关系。

下面，我们就举两个例子。

### 5.1. 有确定交易含义的因子

橫截面因子分析的逻辑是，因子与收益呈线性关系，总是买入因子最强的一组、卖出因子最弱的一组，这样无论市场好坏，策略相对于市场总是有超额的。

在这种假设下，按分位数分层就是一个不错的选择：无论某个时间段的市场情况（因子整体均值和中位数值是高还是低）如何，按照分位数分层，我们总能划分出若干样本数相同的分层。

但这种方法就不适合有确定交易含义的因子。以 RSI 为例，一般认为超过 80 就是超买，需要减仓或者卖出。我们先不管这个论断是否正确，我们只看在 Alphalens 中，如果采用分位数分层的模式，它的交易模式还能与这个论断一致吗？

我们以 RSI 因子为例进行说明。

<Example 3/>

```python
from alphalens.plotting import plot_quantile_statistics_table
def calc_rsi(close, win):
    # 对因子进行修正，使得它与远期收益的在理论上呈现正相关关系
    return 100 - ta.RSI(close.astype(np.float64), win)

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)

barss =  load_bars(start, end, 1000)

factors = (barss.groupby(level='asset')
                .apply(lambda group: calc_rsi(group.close, 6))
                .droplevel(level=0))
prices = barss['price'].unstack(level=1)

factor_data = get_clean_factor_and_forward_returns(factors, prices)

# 绘制分层统计图
plot_quantile_statistics_table(factor_data)

# 生成收益分析报告
create_returns_tear_sheet(factor_data)
```

这一次，我们在第 20 行的位置，插入了一个分层统计图。通过这个图，我们看到一个"奇怪"的现象 [^quantile-overlapping]，那就是每个分层之间的因子值出现了重叠 (overlapping)。比如，第一层的 RSI 是从 0 到 68.96；第二层则是从 12.52 到 777.33；第 5 层则是从 20.45 到 83.24。为什么分层会出现这样的重叠呢？

原来，Alphalens 是按天，而不是按输入数据的全生命期来进行统计并分层的。按天进行统计避免了引入前视偏差。但是，如果 T0 这一天市场普涨，那么有可能多数股的 RSI 都比较大，此时就会出现即使是第一层，它的最大 RSI 值也会比较高，正如这里显示的 max = 68.96 所示。反之，如果这一天市场普跌，那么就可能最高一层的 RSI 也比较小，正如这里显示的第 5 层，min=39.93（对应于当天最大的RSI为60.07）。

Alphalens 会按每一天的因子进行分层后，再把所有的第一层合并起来计算 min, max, mean 等数值，于是就出现了表中的因子值在各个分层之间重叠的情况。

尽管 Alphalens 没有做错，但这样的分层并不符合我们的意愿：在 RSI 80 以上做空，20 以下做多。由于我们的因子是 100 - rsi，所以，我们的 RSI 交易理论实际上是要求，在因子 20 以下买入，在 80 以上卖出。

但是，第一个分层就从 0 跨越到了 63.6。Alphalens 会对第一层做空，但实际上，只应该对其中的部分（<20）做空。因此，在这里，按分位数进行分层的方式是不合理的。

对像 RSI 这样有着明确交易信号意义、并且上下界确定的因子，我们可以使用按 bins 进行分层，并且自己传入恰当的分层边界值。

下面，我们就改用 bins 分层方法试验一下：

<Example 4/>

```python
bins = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]

factor_data = get_clean_factor_and_forward_returns(factors, prices, quantiles=None,bins=bins)
# 绘制分层统计图
plot_quantile_statistics_table(factor_data)
```

现在，我们再来查看分层统计图。一共是 10 个分层，再也没有出现因子值之间相互重叠的情况了。

现在当我们做多 top 分层，最空 bottom 分层时，最后执行的就是我们真正想要的策略。我们看一下收益分析报告：

```python
# 生成收益分析报告
create_returns_tear_sheet(factor_data)
```

在进行细分之后，我们也发现，RSI 总体上与收益存在线性关系，但在两极出现不协调的情况。正是这种不协调，导致整个策略的收益并不好。

### 5.2. 如果因子是离散值

如果因子是分类数据，那么能使用 Alphalens 来进行因子分析吗？答案是肯定的，这时也要借助 bins 分层。

因子是分类数据的情况，多见于文本情况分析等案例。我们假设有以下分类：

*  5 - Strongest positive sentiment.
*  4 - Very strong, positive, sentiment.
*  3 - Strong, positive sentiment.
*  2 - Substantially positive sentiment.
*  1 - Barely positive sentiment.
*  0 - Neutral sentiment
* -1 - Sentiment trending into negatives.
* -2 - Weak negative sentiment.
* -3 - Strongest negative sentiment.

此时如果使用 quantile 分类，将可能导致多个分类被 Alphalens 分配到同一层。借助 bins 分层，我们可以这样做：

```python
bins = np.linspace(-3.5, 5.5, 10)
bins
```

这样就可以完美地将离散值因子按它们自己的分类进行分层了。

## 6. Footnotes

[^alphalens-reloaded]: Alphalens Reloaded 仓库在 [这里](https://github.com/stefan-jansen/alphalens-reloaded)，当前版本 0.4.4，已支持到 Python 3.12。

[^issue-335]: Alphalens 建议在计算月度、年度的远期收益时，使用 21D 或者 252D，请见 [issue-335](https://github.com/quantopian/alphalens/issues/335)。但是，这个建议并不适用所有情况。每个月的月末在交易上会是比较重要的时段。采用固定天数则可能会跳过这个时段，进入下一月。

[^quantile-overlapping]: [为什么 Alphalens 分层会出现重叠？](https://quantopian-archive.netlify.app/forum/threads/why-do-alphalens-quantiles-overlap.html)

<!--

习题：

实现月度低波动因子

-->
