# 12. 因子挖掘创新方法

<br>

挖掘新因子就是一种创新。在此过程中，我们要运用新技术、开拓新思路、跨界融合，从不同的维度上进行尝试。

传统的因子挖掘主要依赖于经济理论和交易经验（这仍然很重要），但随着计算机和大数据技术的兴起，一部分人转向了数据驱动方法，他们通过聚类、主成分分析等方法，从大量特征中提取代表性的因子。

由于计算技术的限制，传统的因子模型假设因子与资产收益之间存在线性关系。有了机器学习手段的加持，我们现在可以使用如神经网络、决策树和随机森林等，可以更准确地捕捉因子与收益之间的复杂关系。

此外，我们还可以拓展金融市场数据的来源，融合财务报表、新闻报道、社交媒体、卫星图像等多种渠道。通过多源数据融合，可以提取出更加全面和多维度的因子。

<!-- 新的技术发展、跨界融合 
时装轮回 -- 很多因子是风格因子。一段时间不好，过一段时间就好了
进入到不同的交易品种。不是所有的因子都在所有的市场和交易品种上被认真的尝试过
进入不同的频率。有一些因子在宏观上不行，但在高频中就可能有效。
因子不是一切

从其它领域借用名词： 信息断食 https://www.wenxuecity.com/news/2024/09/15/125777979.html
-->

## 1. 第一性原理与创新

第一性原理（First Principles）是一种思考方法，通过将复杂的问题分解为最基本的组成部分，然后从这些基本原理出发重新构建解决方案。这种方法的核心在于不依赖于类比或惯例，而是从最基本的事实出发，逐步推导出新的结论。

亚里士多德最早提出了第一性原理的概念，他认为通过理解事物的基本性质和原因，可以更好地理解和解决问题。

在当代，可能埃隆·马斯克（Elon Musk）是现代最著名的应用第一性原理的人物之一。他在特斯拉和 SpaceX 的成功中，多次运用这一方法来打破传统思维，实现技术创新。

此外，苹果公司划时代的电子产品，ipod，也是运用第一性原理进行发明的著名案例。ipod 在当年解决了把 1000 首歌放进一个小盒子，从而可以让音乐爱好者沉浸在音乐里的需求。

在同时代，最好的产品是索尼的 walkman，他们运用翻带技术，也实现了在磁带录音机上不间断听歌的需求，但是，一盒磁带毕竟只能存 12 首左右的歌，因此，仍然不能完全满足发烧友沉浸式体验的需求。苹果没有尝试扩大磁带信息容量的办法，他们换了一条赛道，但是，技术变了，但用户的核心需求仍然是创新的锚定物。

<!--找从牛仔裤掏 ipod 的视频-->

乔布斯更是经常拿汽车大王福特来举例，解释了什么是第一性原理。

!!! tip
    “If I had asked people what they wanted, they would have said faster horses.” -- Henry Ford

如果不是运用第一性原理，从基本原理和用户的根本需求出发，福特大概就不会发明汽车，而是会选择去培育更好、更快的马，在驿道上部署换马站 -- 这让人想起换电站。

具体到因子挖掘和策略研究上来，第一性原理又该如何体现呢？运用第一性原理，就是要在熟悉前人的研究方法，又打破前人研究的窠臼，从价格的涨跌入手，倒推价格变化的原因。

如果我们秉持这样的思路，就能发现这样一些适用于交易的『第一性』原理：

1. 驱动价格涨跌最直接的因素是资金。无论有多少利好消息、基本面看涨消息，最终都要看是主买的资金多、还是主卖的资金多。
2. 价格的方向是由资金里的主力决定的。因为当个体处于群体中时，其行为和决策受到群体影响，往往会失去理性，变得更加情绪化和冲动。个体往往会受到他人引导，最终会跟随头羊的方向前进，这就是羊群效应。
3. 驱动资金买入或者卖出的因素有多种，本质上看，都是基于『这笔交易应该赢利』而做出的。但是，不同运作周期的资金，对交易应该何时盈利，应该承受什么样的风险，看法上会有显著不同。越是短线的资金，出发点和落脚点越是靠近博弈；越是长线的资金，出发点和落脚点越是靠近价值，忽略短期的波动。
4. 价格的波动是由不同周期的资金共同作用引起的。长期运作的资金，对于价格的影响近似于线性，短期运作的资金，对于价格的影响近似于正弦波。
5. 价格运动只有两种最基本的形式，惯性和摆动。

在我们挖掘因子和策略时，也应该从这些第一性原理出来，看看价格的运动是如何由这些第一性原理驱动的。

## 2. 因子都挖完了吗？


事实上，一些显而易见的因子，甚至已经呼之欲出了，有时候也要等待很长时间才能走出深闺。比如， Rolf Banz 在 1981 年发表小市值因子时，低波动因子的一个证据就已经出现在他论文中的一张图里了：

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/09/low-beta-factor.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>收益与市值分导关系</span>
</div>

在这张图中，Banz 把资产按市值大小分成了 5 组，第一组是市值最小的一组，第 5 组是市值最大的一组。很显然，其它几组的收益都与市值大小有着线性关系，但第一组，即小市值组有更大的月度收益，不能被线性回归。于是，Banz 提出了这种异象应该用小市值因子来解释。

但此图中还隐藏了另一个异象。在图中，资产实际上是被分成了 25 组。在按市值划分的每个组内，Banz 又按波动率将资产分成了 5 组（在图上纵向分布）。第一组是波动率最大的一组，第 5 组则是波动率最小的一组。很显然，在每一个组合中，低波动率的资产收益都大于高波动率的资产。

实际上，这里出现了另外一个因子，就是低波动因子。我们回测过，日线低波动因子可以达到 16.4%的年化 Alpha。月线可以达到 6.3%左右的年化，因子 IC 是 0.04，相当可观。

它已经呼之欲出了，但直到 10 年之后，Haugen 和 Baker 才发现和命名了低波动因子。

这个故事说明在因子与策略研究上，即使在技术和研究手段没有发生重大变革的情况下，都还存在大量被遗漏的明珠，更何况新的技术手段出现之后？

具体来说，这种创新主要来源于：

1. 运用新的技术手段来挖掘数据。在手工处理数据时代，人类能处理数据的规模是有限的；后来有了计算机的算力加持，但挖掘数据的方法并没有根本改变；现在，我们有了人工智能，无论是通过人工智能直接发现数据背后的规律，还是通过人工智能来改进工具，在技术手段上都有了相当大的进步。
2. 运用新的技术手段来表达数据规律。技术指标出现的年代较早，同时，它的受众是普通交易者（相对于程序交易），这就要求它编制的接口必须尽可能地简单，并且适合图表化。因此，我们看到的多数技术指标，要么通过归一化，设置了固定的数值边界作为交易信号（RSI, CCI 等）；要么通过两条线的交叉来决定交易信号（KDJ, MACD 等）。现在我们使用计算机，就不再需要信号的可视化。数据只要表达成计算机能理解、能处理的样子就行。少一层表达和转换，就少一些信息损失。现在，我们可以改造传统技术指标，使之更适合计算机处理。
3. 跨界融合。比如，生物基因技术是 20 世纪 80 年代兴起的，后来人们把基因这一概念到金融领域，出现了遗传算法。包括近年来使用的卫星图像、互联网搜索指数等另类数据，也是一种跨界融合。
4. 抢先进行工程化实现。比如，talib 实际上只包含了部分出现较早的技术指标。还有一些较新的技术指标，我们自己进行实现并运用，相对于其它人来说，也是一种创新。

关于第4点，我们适当展开一下。实际上，近年来，业界关于新因子、新技术指标的挖掘一直没有停止过，但这些创新，要么还停留在学术论文阶段，要么已经成为付费因子/策略，从而不为大众所熟悉。比如，talib实现的技术指标是开源和免费的，但还有以下因子，并未在talib中实现：

1. [Awesome Oscillator](https://www.ifcmarkets.hk/en/ntx-indicators/awesome-oscillator)
2. [Relative Volatility Index](https://www.tradingview.com/support/solutions/43000594684-relative-volatility-index/)
2. [Relative Vigor Index](https://www.investopedia.com/terms/r/relative_vigor_index.asp)
8. [Average Daily Range](https://tw.tradingview.com/scripts/adr/)
9. [Williams Alligator](https://www.investopedia.com/articles/trading/072115/exploring-williams-alligator-indicator.asp)
10. [Connors RSI](https://www.backtrader.com/recipes/indicators/crsi/crsi/)
11. [Smoothed Moving Average](https://trendspider.com/learning-center/what-is-the-smoothed-moving-average-sma/)
12. [PVT](https://www.tradingview.com/support/solutions/43000502345-price-volume-trend-pvt/)

<!--
```python https://github.com/TA-Lib/ta-lib-python/issues/622
def PVT(c, v):
    return np.cumsum(v[1:] * np.diff(c) / c[:-1])
```
-->

我们列举出来的因子都附有链接，大家可以在自己的因子库中实现这些算法，从而实现对同行的超越。

前面讲的以道为主，下面，我们就以技术指标的迭代和创新为例，讲一些术的东西，看看指术指标是如何迭代的，以及我们还有哪些可能的创新思路。

## 3. 技术指标的迭代和创新

在讲<ref>[技术指标](09.md)</ref>那一章，我们已经看到了一些技术指标是如何推陈出新的。比如，我们谈到了利用不同价格序列之间的差异来构造的一些振荡（动量指标），它们都是基于价值回归的原理，但构造方法各不相同。

我们通过下面的表格来回顾一下这个历程。注意，在最后一行，我们加入了 Bill Williams 于 1995 年发明的 Awesome Oscillator。

| 指标名称               | 公式                                                        | 发明时间     | 发明者         |
| ---------------------- | ----------------------------------------------------------- | ------------ | -------------- |
| **BIAS**               | $Bias = \frac{(Close - MA(Close, N))}{MA(Close, N)}$        | 早于 1960 年 | N/A            |
| **APO**                | ${短期 EMA} - {长期 EMA}$                                   | 1970 年代    | Gerald Appel   |
| **PPO**                | $\frac{\text{短期 EMA} - \text{长期 EMA}}{\text{长期 EMA}}$ | 1970 年代    | Gerald Appel   |
| **MACD**               | EMA(Close, 12) - EMA(Close, 26)                             | 1970 年代    | Gerald Appel   |
| **CCI**                | $\frac{Typical Price - MA}{.015 * Mean Deviation}$          | 1980 年代    | Donald Lambert |
| **Awesome Oscillator** | $SMA(5) - SMA(34)$                                          | 1995 年      | Bill Williams  |

我们看到，关于振荡器的设计，迭代跨越了半个世纪之久，但构造手法无非就是这样三点：

1. 选用不同的时间序列。在 BIAS 中，使用的是收盘价和收盘价的简单移动平均；在 APO、PPO 直到 MACD，只使用了指数移动平均序列；在 CCI 中，Larry Williams 把压力位（最高价）和支撑位（最低价）也考虑了进来；而在 Awesome Oscillator 中，Bill Williams 使用的是中间价（即 (high + low) / 2）。
2. 使用的平滑方式不同。BIAS 使用的是无平滑的 close 和简单移动平均；CCI 和 AO 使用的都是简单移动平均；而 MACD 使用的则是指数移动平均。
3. 是否进行『归一化』？在 BIAS, PPO 和 CCI 中，都实现了某种形式的归一化，而其它的技术指标则没有。

这仅仅是对于振荡器设计的分析。在整个技术指标的迭代中，人们按照量、价、时、空的多个维度，在价的维度上先后使用了以下时间序列：

1. 收盘价
2. Typical Price，即 (high + low + close) / 3
3. 中间价，即 (high + low) / 2
4. 成交量加权平均价，即 vwap。
5. 上述时间序列的移动平均。

!!! tip
    Awesome Oscillator 是较新的指标，还没出现在 talib 库中，不过它的计算很简单，并不需要借助 talib。<br>
    下面，我们就对这个较新的振荡指标进行因子检验。同样地，由于 AO 是一个绝对值指标，在进行因子检验之前，我们得对它进行『归一化』:<br>
    ```python
    def calc_pao(df):
        mp = (df.high + df.low) / 2
        mp5 = mp.rolling(5).mean()
        mp34 = mp.rolling(34).mean()

        pao = (mp5 - mp34)/(mp5+mp34) * -1
        return pao

    start = datetime.date(2018, 1, 1)
    end = datetime.date(2023, 12, 31)

    np.random.seed(78)
    alphatest(2000, start, end, calc_pao)
    ```<br>
    运行结果表明，年化 alpha 在 11.2%左右，但是主要依靠做空高位股。

### 3.1. 量的维度

在量的维度上，我们接触过了换手率、Chaikin A/D Line（柴金累积/派发线）和 OBV。换手率和 OBV 是纯粹的量的维度，Chiakin 线则是把量和价联系起来，试图找出现金流的方向。

结合前面我们讲的第一性原理中的几条规则，我们会发现，这里显然存在一个羊群效应的成交量指标，即最大成交量方向。

这个因子也可以叫做主力因子。它的构造原理是，价格的方向是由资金里的主力决定的。因为当个体处于群体中时，其行为和决策受到群体影响，往往会失去理性，变得更加情绪化和冲动。个体往往会受到他人引导，最终会跟随头羊的方向前进，这就是羊群效应。

主力才能决定方向，决定方向的力量也就是主力。因此，主力方向是可以建模的。

```python
def max_volume_direction(df_, win=40):
    df = df_.copy()
    df.index = np.arange(len(df))
    df["flag"] = np.select([df["close"] > df["open"], df["close"] < df["open"]], [1, -1], 0)
    df["move_vol_avg"] = df["volume"].rolling(window=win, min_periods=win).mean().shift(1)
    df["argmax"] = df['volume'].rolling(win, min_periods=win).apply(lambda x: x.idxmax())
    df.fillna(0, inplace=True)
    df["move_vol_max"] = df.apply(lambda row: df.loc[row['argmax'], 'volume'], axis=1)
    df['vr'] = df['volume'] / df['move_vol_avg'] * df["flag"]
    df["span"] = df.index - df["argmax"]

    def calc_rolling_net_balance(df):
        pos = df["argmax"].iloc[-1] + 1
        sub = df.loc[pos:,]
        return (sub["flag"] * sub["volume"]).sum() / df["move_vol_max"].iloc[-1]

    balances = []
    for sub in df.rolling(win):
        b = calc_rolling_net_balance(sub)
        balances.append(b)

    df["move_balance"] = balances

    return df.replace([np.inf, -np.inf], np.nan)
```

我们简单地看下它的效果：

```python
def test(bars, thresh=5):
    df = max_volume_direction(bars, 40)

    cs = Candlestick(bars.reset_index(), height=750)
    # add up markers
    x = df[df.vr > thresh].index
    y = df[df.vr > thresh]["close"] * 1.05
    cs.add_marks(x, y, name="up", marker="triangle-up")

    # add down markers
    x = df[df.vr < -thresh].index
    y = df[df.vr < -thresh]["close"] * 0.95
    cs.add_marks(x, y, name="down", marker="triangle-down", color="green")

    cs.plot()

start = datetime.date(2023,5,1)
end = datetime.date(2023, 11,18)

code = "001300.XSHE"
bars = load_bars(start, end, (code,)).xs(code, level="asset")
test(bars)
```

这里使用的是日线，所以主力方向判断不够准确。如果使用30分钟线，则准确率会提升不少。你可以找一支近期波动比较大的个股，通过下面的代码来获取30分钟线试试：

```bash
import akshare as ak

code = "sz002466"
bars = ak.stock_zh_a_minute(symbol=code, period="30", adjust="qfq")
bars = bars[-150:].copy()

bars.rename(columns={"day": "date"}, inplace=True)
bars.set_index("date", inplace=True)
bars["volume"] = bars.volume.astype(int)
test(bars)
```

在2024年11月中旬运行这段代码时，我们会得到这样的输出：

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/zlyz-tqly.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>主力因子</span>
</div>

我们看到，图中出现三个向上的箭头。在前两个箭头出现时，股价还处在低位，主力试盘后，并未出现大的抛盘，于是主力再次进行了拉升。最后一次拉升后，出现明显的打压信号，后面股价随之下跌。

由于akshare能获取的30分钟线的数据量有限，所以，当你运行这段代码时，得到的数据与示例中的不一样，因此，它能绘制出来的信号也会完全不同。

<!-- 习题 将 max_volume_direction 进行因子检验，修改算法，计算两个 -->
请大家自行尝试，进行因子检验。

### 3.2. 价的维度

在价的维度上，因子挖掘相对充分一些，但进入到 2000 年之后，John Ehlers 还是提出了一个新的技术指标，即 Relative Vigor Index。

它的公式是：

$$
NUMERATOR = \frac{a + (2\times b) + (2\times c) + d}{6} \\
DENOMINATOR = \frac{e + (2\times f) + (2\times g) + h}{6} \\
\\
RVI = \frac{SMA of NUMERATOR for N periods}{SMA of DENOMINATOR for N periods} \\
Signal Line = \frac{RVI + (2 \times i) + (2 \times j) + k}{6}
$$

其中，

$$
a = Close(0) - Open(0) \\
b = Close(0) - Open(-1) \\
c = Close(0) - Open(-2) \\
d = Close(0) - Open(-3) \\
e = High(0) - Low(0) \\
f = High(0) - Low(-1) \\
g = High(0) - Low(-2) \\
h = High(0) - Low(-3) \\
i = RVI(-1) \\
j = RVI(-2) \\
k = RVI(-3) \\
$$

这个指标同样不在 talib 中，不过实现起来也很简单。它与 SO（Stochastic Oscillator）比较接近，但是，它是使用的 Close 和 Open 来进行比较，而不是 Close 与最低价比较。我们前面讲过，Close 与 Open 的比较有买卖力道的含义。在 RVI 中，它连续考察了三期的买卖力道。

<!--习题，实现 RVI-->

### 3.3. 时间维度

时间维度同样是一个重要的分析角度，它关注的是市场行为的时间周期和时间结构。时间维度的因子可以帮助交易者识别市场周期、季节性效应、时间窗口等，从而更好地把握市场节奏和趋势。

首先值得讨论的因子是节日效应、交割日效应、财报批露效应。

在 A 股，年度周期上，有比较明显的春节效应，又称春播行情。它的成因主要由以下几方面构成：

1. 4 季度末的银行存准金考核，往往导致市面上银根较紧，出现下跌，创造出上涨空间。
2. 季节性流动性宽松。比如，每年的新增贷款，为了便于安排生产，因此在一季度放款力度最大。散户因为年终奖等原因，资金面也更宽裕。
3. 节后迎来两会，是政策、利好集中释放期，有利于出货。

交割日效应 [^zillionare_1] 是指在 A 股中，每逢 ETF 期权交割、A50 指数交割和股指期货交割时，A 股的波动会加大，并且一般表现为下跌的现象。

财报批露 [^zillionare_2] 效应的研究一直在延伸中。1981 年，当时在普渡大学的 William Kross 发表了名为《Earnings and Annoucement Time Lags》的论文，在研究了 108 家公司（从 200 家初始样本中筛选），共 432 个观察值后，得出这样一个结论：财报发布得越晚，就越有可能包含了不好的消息。如果实际发布的时间比预告发布时间晚一周或者更久，那么信号意义就越强烈。

1999 年， Mark Bagnoi 发表了《A day late, A penny short》的论文，进一步得出结论说，如果公司预告在某天发布财报，而实际上却错过了这个日期，那么平均每延迟一天，意外收益每股约降低一美分。

我们追溯到的最晚的研究直到 2018 年。Travis L. Johnson 在这一年发表了《Time Will Tell: Information in the Timing of Scheduled Earnings News》。他的结论是，发布财报预告日期预示着企业的盈利消息，但市场往往会等到公告时才对其做出反应，也就是市场并不是强有效的，给我们提供了足够的时间去套利。

从量价数据的角度来看，时间维度上的指标似乎并不多。一方面，通过研究集合竞价、开盘 5 分钟 [^orb] 和前半小时等特殊交易时段，我们可以拓展出新的因子。

另一方面，时间维度主要通过某个信号出现后，到现在的 bar 的个数来度量，然后把这个数值与收益关联起来，以构成指标（因子）。对此我们略加展开：

我们在之前讨论过连续涨跌次数因子，这就是一种时间维度。其它常见的时间维度因子还有，成交量是多少周期以来的最大（最小）值？发生金叉、死叉以来，经过了多久？距离上次新高、新低以来经过了多少天（Alpha 001）等等。

著名的 Connor's RSI[^connor]，就是在 RSI 的基础上，增加了时间维度和概率判断。在 Connor's RSI 中，时间维度是通过 streaks 指标来表示的。这里的 Streaks，就是我们之前介绍的连续涨跌次数。

下面，我们就简单介绍一下 Connor's RSI 的算法：

```bash
CRSI(3, 2, 100) = [RSI(3) + RSI(Streak, 2) + PercentRank(100)] / 3
```

这里的核心是计算 Streak，代码如下：

```python
def find_runs(x) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """Find runs of consecutive items in an array.

    Args:
        x: the sequence to find runs in

    Returns:
        A tuple of unique values, start indices, and length of runs
    """

    # ensure array
    x = np.asanyarray(x)
    if x.ndim != 1:
        raise ValueError("only 1D array supported")
    n = x.shape[0]

    # handle empty array
    if n == 0:
        return np.array([]), np.array([]), np.array([])

    else:
        # find run starts
        loc_run_start = np.empty(n, dtype=bool)
        loc_run_start[0] = True
        np.not_equal(x[:-1], x[1:], out=loc_run_start[1:])
        run_starts = np.nonzero(loc_run_start)[0]

        # find run values
        run_values = x[loc_run_start]

        # find run lengths
        run_lengths = np.diff(np.append(run_starts, n))

        return run_values, run_starts, run_lengths

def streaks(close):
    result = []
    conds = [close[1:]>close[:-1], close[1:]<close[:-1]]

    flags = np.select(conds, [1,-1], 0)

    # FIND_RUNS 函数来自大富翁量化框架。它的作用是划分数组中
    # 连续出现的相同值。是量化中非常基础的一个函数。
    v, _, l = find_runs(flags)
    for i in range(len(v)):
        if v[i] == 0:
            result.extend([0] * l[i])
        else:
            result.extend([v[i] * x for x in range(1, (l[i] + 1))])
            
    return np.insert(result, 0, 0)
```

如果你看明白了这段代码，你就明白，它实际上是在计算连续涨跌的bar数（时间），然后期待回归的发生。

### 3.4. 空间维度

空间维度主要关注价格的波动范围和位置。我们已经讨论过的因子有 WR, ART，波动率，以及随机振荡器和通道线等。除了这些之外，股价常常会做三角形整理，因此我们也可以把峰谷回归线的延长线作为预测空间的因子。

在预测空间时，人们常常使用基于斐波那契数列的比例（如 0.382、0.618、1.000 等），用于预测价格的回调和支持/阻力位。

关于空间，有一个很容易想到的指标是最大回撤，这里也特别说明一下。它是一个相对静态的指标，在最大回撤创出来之后，在此后上涨的每一期内，最大回撤并不会变更，因此它并不适合作为因子。

<!--
从原理上分析，最大回撤不适合做因子。

```python
df = PAYH.copy()[-120:]

df["MAX"] = df.close.cummax()
ivalley = np.argmax(df.MAX - df.close)
ipeak = np.argmax(df.close[:ivalley])

# 找出最大回撤区间
max_drawdown = df.close.iloc[ivalley]/df.close.iloc[ipeak] - 1
peak_date = df.index[ipeak]
trough_date = df.index[ivalley]

# 绘制图形
plt.figure(figsize=(14, 7))
plt.plot(df.index, df.close, label='close')
plt.plot(df.index, df.MAX, label='cum max', linestyle='--')

fill_rng = df[ipeak: ivalley + 1]
plt.fill_between(fill_rng.index, fill_rng.close, fill_rng.MAX, where=(fill_rng.close < fill_rng.MAX), color='red', alpha=0.3)

plt.scatter([peak_date, trough_date], [df.loc[peak_date, 'close'], df.loc[trough_date, 'close']], color='red', zorder=5)

plt.annotate(f'Peak: {peak_date.strftime("%Y-%m-%d")}', (peak_date, df.loc[peak_date, 'close']), textcoords="offset points", xytext=(0,10), ha='center')
plt.annotate(f'Trough: {trough_date.strftime("%Y-%m-%d")}', (trough_date, df.loc[trough_date, 'close']), textcoords="offset points", xytext=(0,10), ha='center')

plt.title(f'Max Drawdown: {max_drawdown:.2%}')
plt.xlabel('Date')
plt.ylabel('close')
plt.legend()

plt.show()
```
-->



<!-- 改造传统技术指标、论文、同行、路演交流--》
<!-- 从哪里找论文 金融顶刊-->
<!-- 从自己或者别人的交易经验中来 -->
<!-- 从涨停、强势个股中来。对非常强的个股，前面介绍的因子往往都是不能用的，有它们自己的技术局限。要构建自己的市场指标，比如涨停家数、上涨家数-->

<!-- 

创新来自于边缘地带。创新来自于“混搭”。罗素选择的是如何用逻辑解释数学，这一独特的角度才使他最终成为一代宗师。我们讲到，从来就没有什么新技术，创新来自于继承和综合。很多学哲学的朋友觉得罗素的学生维特根斯坦更牛，也有人觉得另一位哲学家弗雷格比罗素的思想更早。的确，罗素就像一块海绵，一直在从别人那里吸取知识，随时准备改变自己的想法，罗素后期受维特根斯坦影响很大。但是，罗素的特点是集大成，他的整合能力更强，所以，综合来看，我们应该承认，罗素对哲学的影响更大。

尤其要熟悉罗素开创的数理逻辑。

罗素认为，我们的日常语言很混乱，容易误导，吵了半天，其实大家说的不是一回事，这很容易产生坏的哲学，逻辑则可以澄清和消除这些误解，更好地处理抽象的概念。

其次著名心理学家卡尼曼指出，我们的大脑中有系统一和系统二。系统二就包括了逻辑推理，但这不是我们天生就熟悉的，所以特别需要后天去学习和锻炼。

新的技术指标：

Awesome Oscillator https://www.ifcmarkets.hk/en/ntx-indicators/awesome-oscillator
Relative Volatility Index https://www.tradingview.com/support/solutions/43000594684-relative-volatility-index/ RVI 指标首次出现在 1993 年的《Technical Analysis of Stocks & Commodities》杂志上。
Relative Vigor Index: https://www.investopedia.com/terms/r/relative_vigor_index.asp
Average Daily Range: https://tw.tradingview.com/scripts/adr/
Williams Alligator: https://www.investopedia.com/articles/trading/072115/exploring-williams-alligator-indicator.asp
Connors RSI: 
Smoothed Moving Average: https://trendspider.com/learning-center/what-is-the-smoothed-moving-average-sma/
PVT: https://www.tradingview.com/support/solutions/43000502345-price-volume-trend-pvt/

```python https://github.com/TA-Lib/ta-lib-python/issues/622
def PVT(c, v):
    return np.cumsum(v[1:] * np.diff(c) / c[:-1])
```

-->

## 4. 因子正交性检测
<!-- https://github.com/stefan-jansen/machine-learning-for-trading/blob/f652d79ab2f137d75d554af2cc437a5512b16069/24_alpha_factor_library/04_factor_evaluation.ipynb -->

如果我们有多个因子，很自然我们会好奇其中有多少是独立的，又有多少是相似的。回答这个问题的最佳方法是使用协方差矩阵。

假设我们的因子库中已经有了 5 个因子，分别是 rsi, aroon, ppo, cci 和 Stochastic Oscillator。分别用以下代码来计算：

```python
def calc_rsi(df, n=6):
    return ta.RSI(df.close, n)

def calc_aroon(df, n=14):
    return ta.AROONOSC(high=df.high, low=df.low, timeperiod=n)

def calc_ppo(df):
    return ta.PPO(df.close, 12, 16, 0)

def calc_cci(df):
     return ta.CCI(df.high, df.low, df.close, 14)

def calc_stoch(df):
    k, d = ta.STOCH(df.high, df.low, df.close, fastk_period=14, slowk_period=3)
    return d/k
```

为简单起见，我们把参数都固化了。

现在，我们想往因子库里加入新因子，比如 Awesome Oscillator。这个因子的实现如下：

```python
def calc_pao(df):
        mp = (df.high + df.low) / 2
        mp5 = mp.rolling(5).mean()
        mp34 = mp.rolling(34).mean()

        pao = (mp5 - mp34)/(mp5+mp34) * -1
        return pao
```

我们通过下面的代码，将所有的因子集合在同一个 DataFrame 中，并且使用日期+asset 作为索引：

```python
np.random.seed(78)

start = datetime.date(2023,1,1)
end = datetime.date(2023,12,31)
barss = load_bars(start, end, 10)

data = {}
for func in (calc_rsi, calc_aroon, calc_ppo, calc_cci, calc_stoch, calc_pao):
    factor = (
        barss.groupby(level="asset")
        .apply(lambda x: func(x))
        .droplevel(level=0)
    )
    data[func.__name__.split("_")[1]] = factor

factors = pd.DataFrame(data)
factors.tail()
```

现在，我们可以使用协方差矩阵来评估这些因子之间的相关性。

```python
corr = factors.corr(method="spearman")
corr
```

为使得这个结果更好看一点，我们可以加上可视化效果：

```python
mask = np.triu(np.ones_like(corr, dtype=np.bool8))
fig, ax = plt.subplots(figsize=(12,10))
cmap = sns.diverging_palette(10, 220, as_cmap=True)

sns.heatmap(corr, mask=mask, cmap=cmap, center=0,
            square=True, linewidths=.5, cbar_kws={"shrink": .5})
sns.clustermap(corr, cmap=cmap)
fig.tight_layout()
```

!!! tip
    因子相关性受样本影响很大。在这里，我们只使用了 10 个样本，只取样了一年的时间。显然，随着样本增加，因子值也会变化，从而因子的相关系数也会改变。

运用这里的方法，我们可以检测出Alpha101中，究竟有多少个因子是独立的，哪些因子是相似的。下面这个图来自于 Stephan Jansen[^jansen]的研究。

![](https://images.jieyu.ai/images/2024/11/corr-alpha-101.png)

这个图显示了技术指标与 alphal101 之间的相关性，也同样来自Stephan Jansen。

![](https://images.jieyu.ai/images/2024/11/alpha101-corr-ta.png)

## 5. 因子动物园

作为本教程因子分析部分的结束章，这里我们也提一下因子动物园这个常见概念，并且介绍一些学界提出的较新的因子。

寻找哪些因子（factor）对股票收益率具有解释能力是实证资产定价（Empirical Asset Pricing）领域最核心的问题。从上世纪 70 年代到现在，已经有超过 400 个 factor 在金融顶刊上发表。被提出的因子越来越多，因子的有效性也逐渐成了研究对象。

2011 年，John Cochrane 在美国金融协会主席演讲时，用 factor zoo 来描述因子研究的现状，并提出了因子有效性研究中的三个重要问题，即哪些因子是独立的；哪些因子是重要的；因子驱动资产价格的原因是什么。

当然，对我们来讲，判断因子的独立性肯定是必要的。因子的重要性应该由它的Alpha以及稳健性相关指标来衡量。至于因子驱动资产价格的原因，则可以在先把好的因子用起来的基础上，再进行研究。

学界到底提出了多少个因子呢？从下面的表格[^gj]可见一斑。

| Acronym    | Author(s)                           | Date, Journal | Definition of the characteristic-based anomaly variable                                                                                                                                                                                                                                 |
| ---------- | ----------------------------------- | ------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| acc        | Sloan                               | 1996, TAR     | Annual income before extraordinary items (ib) minus operating cash flows (oancf) divided by average total assets (at); if oancf is missing then set to change in act - change in che - change in lct++ change in dlc++ change in txp-dp                                                 |
| aeavol     | Lerman, Livnat, and Mendenhall      | 2008, WP      | Average daily trading volume (vol) for 3 days around earnings announcement minus average daily volume for 1-month ending 2 weeks before earnings announcement divided by 1-month average daily volume. Earnings announcement day from Compustat quarterly (rdq)                         |
| age        | Jiang, Lee, and Zhang               | 2005, RAS     | Number of years since first Compustat coverage                                                                                                                                                                                                                                          |
| agr        | Cooper, Gulen, and Schill           | 2008, JF      | Annual percent change in total assets (at)                                                                                                                                                                                                                                              |
| baspread   | Amihud and Mendelson                | 1989, JF      | Monthly average of daily bid-ask spread divided by average of daily spread                                                                                                                                                                                                              |
| beta       | Fama and MacBeth                    | 1973, JPE     | Estimated market beta from weekly returns and equal weighted market returns for 3 years ending month tt-1 with at least 52 weeks of returns                                                                                                                                             |
| bm         | Rosenberg, Reid, and Lanstein       | 1985, JPM     | Book value of equity (ceq) divided by end of fiscal year-end market capitalization                                                                                                                                                                                                      |
| bm_ia      | Asness, Porter, and Stevens         | 2000, WP      | Industry adjusted book-to-market ratio                                                                                                                                                                                                                                                  |
| cash       | Palazzo                             | 2012, JFE     | Cash and cash equivalents divided by average total assets                                                                                                                                                                                                                               |
| cashdebt   | Ou and Penman                       | 1989, JAE     | Earnings before depreciation and extraordinary items (ib++dp) divided by avg. total liabilities (lt)                                                                                                                                                                                    |
| cashpr     | Chandrashekar and Rao               | 2009, WP      | Fiscal year-end market capitalization plus long-term debt (dltt) minus total assets (at) divided by cash and equivalents (che)                                                                                                                                                          |
| cfp        | Desai, Rajgopal, and Venkatachalam  | 2004, TAR     | Operating cash flows divided by fiscal-year-end market capitalization                                                                                                                                                                                                                   |
| cfp_ia     | Asness, Porter and Stevens          | 2000, WP      | Industry adjusted cfp                                                                                                                                                                                                                                                                   |
| chatoia    | Soliman                             | 2008, TAR     | 2-digit SIC - fiscal-year mean-adjusted change in sales (sale) divided by average total assets (at)                                                                                                                                                                                     |
| chcsho     | Pontiff and Woodgate                | 2008, JF      | Annual percent change in shares outstanding (csho)                                                                                                                                                                                                                                      |
| chempia    | Asness, Porter, and Stevens         | 1994, WP      | Industry-adjusted change in number of employees                                                                                                                                                                                                                                         |
| chfeps     | Hawkins, Chamberlin, and Daniel     | 1984, FAJ     | Mean analyst forecast in month prior to fiscal period end date from I/B/E/S summary file minus same mean forecast for prior fiscal period using annual earnings forecasts                                                                                                               |
| chinv      | Thomas and Zhang                    | 2002, RAS     | Change in inventory (inv) scaled by average total assets (at)                                                                                                                                                                                                                           |
| chmom      | Gettleman and Marks                 | 2006, WP      | Cumulative returns from months tt-6 to tt-1 minus months tt-12 to tt-7                                                                                                                                                                                                                  |
| chpmia     | Soliman                             | 2008, TAR     | 2-digit SIC - fiscal-year mean adjusted change in income before extraordinary items (ib) divided by sales (sale)                                                                                                                                                                        |
| chtx       | Thomas and Zhang                    | 2011, JAR     | Percent change in total taxes (txtq) from quartertt-4 to tt                                                                                                                                                                                                                             |
| cinvest    | Titman, Wei, and Xie                | 2004, JFQA    | Change over one quarter in net PP&E (ppentq) divided by sales (saleq) - average of this variable for prior 3 quarters; if saleq== 0, then scale by 0.01                                                                                                                                 |
| convind    | Valta                               | 2016, JFQA    | An indicator equal to 1 if company has convertible debt obligations                                                                                                                                                                                                                     |
| currat     | Ou and Penman                       | 1989, JAE     | Current assets / current liabilities                                                                                                                                                                                                                                                    |
| depr       | Holthausen and Larcker              | 1992, JAE     | Depreciation divided by PP&E                                                                                                                                                                                                                                                            |
| disp       | Diether, Malloy, and Scherbina      | 2002, JF      | Standard deviation of analyst forecasts in month prior to fiscal period end date divided by the absolute value of the mean forecast; if meanest== 0, then scalar set to 1. Forecast data from I/B/E/S summary files                                                                     |
| divi       | Michaely, Thaler, and Womack        | 1995, JF      | An indicator variable equal to 1 if company pays dividends but did not in prior year                                                                                                                                                                                                    |
| dolvol     | Chordia, Subrahmanyam, and Anshuman | 2001, JFE     | Natural log of trading volume times price per share from month tt-2                                                                                                                                                                                                                     |
| dy         | Litzenberger and Ramaswamy          | 1982, JF      | Total dividends (dvt) divided by market capitalization at fiscal year-end                                                                                                                                                                                                               |
| ear        | Kishore et al.                      | 2008, WP      | Sum of daily returns in three days around earnings announcement. Earnings announcement from Compustat quarterly file (rdq)                                                                                                                                                              |
| egr        | Richardson et al.                   | 2005, JAE     | Annual percent change in book value of equity (ceq)                                                                                                                                                                                                                                     |
| ep         | Basu                                | 1977, JF      | Annual income before extraordinary items (ib) divided by end of fiscal year market cap                                                                                                                                                                                                  |
| fgr5yr     | Bauman and Dowen                    | 1988, FAJ     | Most recently available analyst forecasted 5-year growth                                                                                                                                                                                                                                |
| gma        | Novy-Marx                           | 2013, JFE     | Revenues (revt) minus cost of goods sold (cogs) divided by lagged total assets (at)                                                                                                                                                                                                     |
| grCAPX     | Anderson and Garcia-Feijoo          | 2006, JF      | Percent change in capital expenditures from yeartt-2 to year tt                                                                                                                                                                                                                         |
| grltnoa    | Fairfield, Whisenant, and Yohn      | 2003, TAR     | Growth in long-term net operating assets                                                                                                                                                                                                                                                |
| herf       | Hou and Robinson                    | 2006, JF      | 2-digit SIC - fiscal-year sales concentration (sum of squared percent of sales in industry for each company).                                                                                                                                                                           |
| hire       | Bazdresch, Belo, and Lin            | 2014, JPE     | Percent change in number of employees (emp)                                                                                                                                                                                                                                             |
| idiovol    | Ali, Hwang, and Trombley            | 2003, JFE     | Standard deviation of residuals of weekly returns on weekly equal weighted market returns for 3 years prior to month end                                                                                                                                                                |
| ill        | Amihud                              | 2002, JFM     | Average of daily (absolute return / dollar volume).                                                                                                                                                                                                                                     |
| indmom     | Moskowitz and Grinblatt             | 1999, JF      | Equal weighted average industry 12-month returns                                                                                                                                                                                                                                        |
| invest     | Chen and Zhang                      | 2010, JF      | Annual change in gross property, plant, and equipment (ppegt) ++ annual change in inventories (invt) all scaled by lagged total assets (at)                                                                                                                                             |
| IPO        | Loughran and Ritter                 | 1995, JF      | An indicator variable equal to 1 if first year available on CRSP monthly stock file                                                                                                                                                                                                     |
| lev        | Bhandari                            | 1988, JF      | Total liabilities (lt) divided by fiscal year-end market capitalization                                                                                                                                                                                                                 |
| lgr        | Richardson et al.                   | 2005, JAE     | Annual percent change in total liabilities (lt)                                                                                                                                                                                                                                         |
| maxret     | Bali, Cakici, and Whitelaw          | 2011, JFE     | Maximum daily return from returns during calendar monthtt-1                                                                                                                                                                                                                             |
| mom12m     | Jegadeesh                           | 1990, JF      | 11-month cumulative returns ending one month before month end                                                                                                                                                                                                                           |
| mom1m      | Jegadeesh and Titman                | 1993, JF      | 1-month cumulative return                                                                                                                                                                                                                                                               |
| mom36m     | Jegadeesh and Titman                | 1993, JF      | Cumulative returns from monthstt-36 to tt-13                                                                                                                                                                                                                                            |
| ms         | Mohanram                            | 2005, RAS     | Sum of 8 indicator variables for fundamental performance                                                                                                                                                                                                                                |
| mve        | Banz                                | 1981, JFE     | Natural log of market capitalization at end of month tt-1                                                                                                                                                                                                                               |
| mve_ia     | Asness, Porter, and Stevens         | 2000, WP      | 2-digit SIC industry-adjusted fiscal year-end market capitalization                                                                                                                                                                                                                     |
| nanalyst   | Elgers, Lo, and Pfeiffer            | 2001, TAR     | Number of analyst forecasts from most recently available I/B/E/S summary files in month prior to month of portfolio formation. nanalyst set to zero if not covered in I/B/E/S summary file                                                                                              |
| nincr      | Barth, Elliott, and Finn            | 1999, JAR     | Number of consecutive quarters (up to eight quarters) with an increase in earnings (ibq) over same quarter in the prior year                                                                                                                                                            |
| operprof   | Fama and French                     | 2015, JFE     | Revenue minus cost of goods sold - SG&A expense - interest expense divided by lagged common shareholders’ equity                                                                                                                                                                        |
| orgcap     | Eisfeldt and Papanikolaou           | 2013, JF      | Capitalized SG&A expenses                                                                                                                                                                                                                                                               |
| pchcapx_ia | Abarbanell and Bushee               | 1998, TAR     | 2-digit SIC - fiscal-year mean-adjusted percent change in capital expenditures (capx)                                                                                                                                                                                                   |
| pctacc     | Hafzalla, Lundholm, and Van Winkle  | 2011, TAR     | Same as acc except that the numerator is divided by the absolute value of ib; if ib== 0 then ib set to 0.01 for denominator                                                                                                                                                             |
| pricedelay | Hou & Moskowitz                     | 2005, RFS     | The proportion of variation in weekly returns for 36 months ending in monthtt explained by 4 lags of weekly market returns incremental to contemporaneous market return                                                                                                                 |
| quick      | Ou and Penman                       | 1989, JAE     | (current assets - inventory) / current liabilities                                                                                                                                                                                                                                      |
| rd         | Eberhart, Maxwell, and Siddique     | 2004, JF      | An indicator variable equal to 1 if R&D expense as a percentage of total assets has an increase greater than 5%.                                                                                                                                                                        |
| rd_mve     | Guo, Lev, and Shi                   | 2006, JBFA    | R&D expense divided by end-of-fiscal-year market capitalization                                                                                                                                                                                                                         |
| realestate | Tuzel                               | 2010, RFS     | Buildings and capitalized leases divided by gross PP&E                                                                                                                                                                                                                                  |
| retvol     | Ang et al.                          | 2006, JF      | Standard deviation of daily returns from month tt-1                                                                                                                                                                                                                                     |
| roaq       | Balakrishnan, Bartov, and Faurel    | 2010, JAE     | Income before extraordinary items (ibq) divided by one quarter lagged total assets (atq)                                                                                                                                                                                                |
| roavol     | Francis et al.                      | 2004, TAR     | Standard deviation for 16 quarters of income before extraordinary items (ibq) divided by average total assets (atq)                                                                                                                                                                     |
| roeq       | Hou, Xue, and Zhang                 | 2015 RFS      | Earnings before extraordinary items divided by lagged common shareholders’ equity                                                                                                                                                                                                       |
| roic       | Brown and Rowe                      | 2007, WP      | Annual earnings before interest and taxes (ebit) minus nonoperating income (nopi) divided by non-cash enterprise value (ceq++lt-che)                                                                                                                                                    |
| rsup       | Kama                                | 2009, JBFA    | Sales from quarter t minus sales from quarter tt-4 (saleq) divided by fiscal-quarter-end market capitalization (cshoq * prccq)                                                                                                                                                          |
| salecash   | Ou and Penman                       | 1989, JAE     | Annual sales divided by cash and cash equivalents                                                                                                                                                                                                                                       |
| secured    | Valta                               | 2016, JFQA    | Total liability scaled secured debt                                                                                                                                                                                                                                                     |
| sfe        | Elgers, Lo, and Pfeiffer            | 2001, TAR     | Analysts mean annual earnings forecast for nearest upcoming fiscal year from most recent month available prior to month of portfolio formation from I/B/E/S summary files scaled by price per share at fiscal quarter end                                                               |
| sgr        | Lakonishok, Shleifer, and Vishny    | 1994, JF      | Annual percent change in sales (sale)                                                                                                                                                                                                                                                   |
| sin        | Hong & Kacperczyk                   | 2009, JFE     | An indicator variable equal to 1 if a company’s primary industry classification is in smoke or tobacco, beer or alcohol, or gaming                                                                                                                                                      |
| SP         | Barbee, Mukherji, and Raines        | 1996, FAJ     | Annual revenue (sale) divided by fiscal year-end market capitalization                                                                                                                                                                                                                  |
| std_dolvol | Chordia, Subrahmanyam, and Anshuman | 2001, JFE     | Monthly standard deviation of daily dollar trading volume                                                                                                                                                                                                                               |
| stdcf      | Huang                               | 2009, JEF     | Standard deviation for 16 quarters of cash flows divided by sales (saleq); if saleq== 0, then scale by 0.01. Cash flows defined as ibq minus quarterly accruals                                                                                                                         |
| sue        | Rendelman, Jones, and Latane        | 1982, JFE     | Unexpected quarterly earnings divided by fiscal-quarter-end market cap. Unexpected earnings is I/B/E/S actual earnings minus median forecasted earnings if available, else it is the seasonally differenced quarterly earnings before extraordinary items from Compustat quarterly file |
| tang       | Almeida and Campello                | 2007, RFS     | Cash holdings ++ 0.715 ×× receivables ++0.547 ×× inventory ++ 0.535 ×× PPE/ totl assets                                                                                                                                                                                                 |
| tb         | Lev and Nissim                      | 2004, TAR     | Tax income, calculated from current tax expense divided by maximum federal tax rate, divided by income before extraordinary items                                                                                                                                                       |
| turn       | Datar, Naik, and Radcliffe          | 1998, JFM     | Average monthly trading volume for most recent 3 months scaled by number of shares outstanding in current month                                                                                                                                                                         |
| zerotrade  | Liu                                 | 2006, JFE     | Turnover weighted number of zero trading days for most recent 1 month                                                                                                                                                                                                                   |

这一篇是关于因子挖掘与创新的。需要强调的是，这里的方法论不仅仅适用于因子挖掘，我们也可以把它用在策略上。在当下，尽管挖掘新的因子是仍然大有可为，但遍地的黄金，一定是散落在通过机器学习组装因子，形成策略这条赛道上。

## 6. 拓展阅读

进行创新之前，需要熟悉行业的现状和最新进展 -- 要站在巨人的肩膀上。本课程提供了大量的辅助阅读材料（文中链接和脚注），除此之外，还有以下资源可供参考：

### 6.1. 金融顶刊

1. <red>Journal of Finance</red> 创刊于 1946 年，是由美国金融协会（American Finance Association）主办的一份顶级学术期刊。它不仅吸引了世界各地顶尖学者的投稿，也是衡量学术成就和职业发展的重要指标。许多诺贝尔经济学奖得主的早期工作都曾在该期刊上发表，比如作为现代金融基石的现代资产组合理论（Markowitz）、资本资产定价模型 (William Sharpe)、MM 定理（Merton H. Miller）、有效市场假说（Eugene Fama）等。
2. <red>Journal of Financial Economics</red>. Rolf Banz， 1981 年发表《The relationship between return and market value of common stocks》（即小市值因子），就是选择的这一家。2015 年 Famma 发表五因子模型，也是这家期刊。
3. <red>Review of Financial Studies</red>，由牛津出版，是一本同行评审学术期刊。它在 2020 年的影响因子为 5.814，在商业、金融类排名 5/110。《Market Liquidity and Funding Liquidity》一文最早就发表在这里。在 google 学术上，它有超过 6500 次引用。
4. <red>Journal of Portfolio Management</red>，专注于投资组合管理的实践和理论，包括量化策略和风险管理。
5. <red>Journal of Financial and Quantitative Analysis</red>，简写 JFQA，发表金融经济领域理论和实证研究。
6. <red>Journal of Empirical Finance</red>，强调实证研究，包括市场效率、资产定价和投资策略的实证分析。
7. <red>The Journal of Trading</red>，关注交易策略、市场动态和交易技术的期刊，不过已停刊。同类型的杂志有<red>《The Traders' Magazine》</red>。

### 6.2. 网络资源

1. [聚宽因子看板](https://www.joinquant.com/view/factorlib/list) 聚宽跟踪了他们实现的各类因子的表现。每类因子都有简单介绍。我们可以从中发现近期表现较好的因子。找到这些目标后，你可以购买他们的因子库，也可以自己实现。
2. [Reseach Gate](https://www.researchgate.net) 是一个全球学术资源平台，它有超过 2.5 亿篇论文。其中有一些论文是可以免费下载的。
3. [Science Redirect](https://www.sciencedirect.com) 收录了 4912 种杂志和 35，025 本书。
4. Arbitrageur Magazine， 这是一本专注于量化金融和算法交易的在线杂志，经常报道行业内的专家和他们的观点。
5. Wilmott Magazine 是一个知名的量化金融社区，其杂志和论坛经常发布关于量化交易策略、模型和人物的深度文章。双月刊
6. Quantitative Finance Stack Exchange，虽然主要是问答形式，但这里有很多量化金融专家参与，可以了解到行业的最新动态和人物。

### 6.3. 其它

石川博士的大作[因子投资：方法与实践](https://www.scribd.com/document/673047999/%E5%9B%A0%E5%AD%90%E6%8A%95%E8%B5%84-%E6%96%B9%E6%B3%95%E4%B8%8E%E5%AE%9E%E8%B7%B5)在因子挖掘的发展史及经典方法上讲得比较透彻，推荐阅读。

## 7. Footnotes

[^connor]: 关于 Connor's RSI，可以参见 [backtrader](https://www.backtrader.com/recipes/indicators/crsi/crsi/) 和 [Nirvana System](https://www.nirvanasystems.com/connors-rsi/)。在后者网站上，Connor's RSI 策略套件的年订阅费是$995。根据 Nirvana System 的报告，这个策略年化收益达到了 25%。

[^zillionare_1]: [交割日魔咒](http://www.jieyu.ai/blog/2024/03/27/why-A-share-crash-at-mar-27/) 这篇文章详细地介绍了交割日计算方法。
[^zillionare_2]: 我们在 [这篇文章](http://www.jieyu.ai/blog/2024/08/08/time-will-tell/) 里，介绍了 William Kross, Mark Bagnoi 等人在财报批露效应等方向上的研究。
[^orb]: 在文章 [ORB! Alpha 年化达到 36%](http://www.jieyu.ai/blog/2024/06/15/5-min-orb-strategy/) 这篇文章中，我们介绍了 Carlo Zarattini 等人斩获 Quantpedia 2023 年大赛第三名的策略。
[^gj]: [The-Characteristics-that-Provide-Independent-Information-about-Average-Monthly-Stock-Returns](http://www.jieyu.ai/assets/ebooks/the-characteristics-that-provide-independent-information-about-average-monthly-stock-returns.pdf)
[^jansen]: Stefan Jansen，技术作家，他开设有Machine learning for trading的量化课程。他还是Alphalens-reloaded等几个开源量化库的维护者。
