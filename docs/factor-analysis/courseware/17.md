
# 聚类：寻找 Pair Trading 标的

<br>

配对交易是一种交易策略，由摩根士丹利的量化分析师在 20 世纪 80 年代首创。

该策略涉及监控两只历史上相关性较强的证券，并监控两者之间的价格差。一旦价格差超出一个阈值（比如n倍的标准差），价格回归就是大概率事件，于是交易者就做空价格高的一支，做多价格低的一支，从而获利。

这种策略允许交易者在几乎任何市场条件下获利，因此一直是大型资产管理机构的主流策略。

例如，通用汽车和福特生产类似的产品（汽车），因此基于整体汽车市场，它们的股价走势相似。如果通用汽车的股价显著上涨而福特的股价保持不变，采用对冲交易策略的人会卖出通用汽车的股票并买入福特的股票，假设股价会回到历史平衡点。如果通用汽车的股价下跌，投资者会获利；如果福特的股价上涨，投资者也会获利。

这个策略的正确性和有效性无可质疑，它几乎像数学定理一样完美。但是，如何才能找到这样的配对股资产呢？

显然，同行业的几家龙头公司，它们之间常常满足这样的关系。但是，正因为几乎所有人都知道其中存在着套利机会，市场的有效性就会在一定程度上对这种套利空间进行压缩。比如，如果你对过去几年的建设银行和工商银行执行配对交易策略的话，年化利润将不超过1%。

而且，公司的业务形态总在发生变化，过去同质化竞争的企业，可能突然就因为收购、开拓新业务而不再是同一赛道的战友。老的赛道，也随时可能有新的掠食者加入进来。历史既在重复自己，也在开拓新的可能。

作为量化人，我们能够纯粹依赖数据分析，更加敏锐、快速找到这些新的机会吗？

答案是肯定的：我们可以仅凭资产的价格走势，通过协整检验，来发现可能的配对资产。

## 1. 平稳时间序列和协整检验

协整检验是一种统计方法，用于检查两个或多个非平稳时间序列是否存在长期均衡关系。即使这些序列本身是非平稳的，它们的线性组合可能是平稳的。这种长期均衡关系意味着，当一个序列偏离其长期均衡状态时，另一个序列也会相应地偏离，并且它们最终会回归到均衡状态。协整检验常用于配对交易策略中，以识别潜在的配对标的。

### 1.1. 平稳时间序列及ADF检验

协整检验涉及到平稳时间序列(Stationary Time Series)的概念。平稳时间序列是指其统计特性（如均值、方差、自协方差等）在时间上是不变的。具体来说，平稳时间序列满足以下条件：

均值（Mean）：时间序列的均值是一个常数，不随时间变化。
方差（Variance）：时间序列的方差是一个常数，不随时间变化。
自协方差（Autocovariance）：时间序列的自协方差只依赖于时间间隔（滞后阶数），而不依赖于具体的时间点。

平稳时间序列通常更容易建模和预测，因为其统计特性在不同时间点上是一致的。不满足上述条件的时间序列，就是非平稳时间序列。显然，股票资产价格就是非平稳时间序列的一例。

正是基于平稳时间序列通常更容易建模和预测的特性，人们才发明了通过配对资产对冲，来得到平稳时间序列进行交易预测的方法。

对平稳时间序列，一般我们通过ADF检验来进行验证。

ADF检验是由 David A. Dickey[^dickey] 和 Wayne A. Fuller 发明的一种平稳时间序列检验方法。它通过对时间序列数据进行回归分析来判断序列是否存在单位根。如果存在单位根，那么时间序列是非平稳的；如果不存在单位根，则时间序列是平稳的。

显然，根据定义，正态分布是一种平稳时间序列（注意这个说明并不严谨）。下面，我们就先生成一个正态分布的随机变量，并在时间上进行展开，然后对它进行ADF检验：

<Example id=adf/>

```python
from statsmodels.tsa.stattools import adfuller

# 生成平稳时间序列（白噪声）
np.random.seed(0)
n = 1000
stationary_series = np.random.normal(loc=0, scale=1, size=n)

# 绘制平稳时间序列
plt.figure(figsize=(12, 6))
plt.plot(stationary_series, label='平稳时间序列')
plt.title('平稳时间序列')
plt.legend()
plt.show()

# 进行ADF检验
adf_result = adfuller(stationary_series)

print("平稳时间序列 ADF检验结果:")
print(f"ADF Statistic: {adf_result[0]}")
print(f"p-value: {adf_result[1]}")
print(f"Critical Values: {adf_result[4]}")
```

我们得到的p值为0，小于广泛接受的阈值0.05，这说明该序列通过了平稳性检验。

下面的代码分别对资产价格及其收益进行了检验。检验结果表明，资产价格往往不是平稳序列，而其收益一般可以近似看成平稳序列。

<Example id=price-test/>

```python
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.tsa.stattools import adfuller

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 30)
barss = load_bars(start, end, ('000001.XSHE',))

close = barss.xs("000001.XSHE", level=1).close
returns = close.diff().dropna()

# 绘制平稳时间序列
plt.figure(figsize=(12, 6))
ax1 = plt.gca()
ax1.plot(close, label='Price')
ax1.grid(False)

ax2 = ax1.twinx()
ax2.plot(returns, label="Returns", color='orange')
ax2.grid(False)

lines, labels = ax1.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()
ax1.legend(lines + lines2, labels + labels2, loc='best')

plt.show()

# 进行ADF检验
result1 = adfuller(close)
result2 = adfuller(returns)

df = pd.DataFrame({
    "ADF Stat": (result1[0], result2[0]),
    "P-Value": (result1[1], result2[1]),
    "Critical Values 1%": (result1[4]["1%"], result2[4]["1%"]),
    "Critical Values 5%": (result1[4]["5%"], result2[4]["5%"]),
    "Critical Values 10%": (result1[4]["10%"], result2[4]["10%"]),
     }, index=["close", "returns"])

df.T
```

<!-- BEGIN IPYNB STRIPOUT -->
![](https://images.jieyu.ai/images/2025/01/adf-test-of-payh.jpg)
<!-- END IPYNB STRIPOUT -->

结果表明，价格序列是非平稳的(p值远大于0.05)，而收益序列是平稳的(p值远小于0.05)。我们还可以得出这样的结论，即非平稳时间序列可以转换为平稳时间序列。

### 1.2. 构造平稳时间序列

我们在前面的示例中看到了非平稳时间序列可以转换为平稳时间序列。在示例中，我们使用的方法是求其一阶差分。如果有两个资产序列，它们的总体走势长期保持一致，我们也可以通过这两个时间序列，构造出一个平稳时间序列。

下面，我们以建设银行和工商银行为例，说明如何构造出一个平稳时间序列。

<Example id=how-to-construct/>

```python
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.tsa.stattools import adfuller

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 30)

gsyh = "601398.XSHG"
jsyh = "601939.XSHG"

barss = load_bars(start, end, (gsyh,jsyh))

pair1 = barss.xs(gsyh, level=1).close
pair2 = barss.xs(jsyh, level=1).close

# 绘制平稳时间序列
plt.figure(figsize=(12, 6))
ax1 = plt.gca()
ax1.plot(pair1, label=gsyh)
ax1.grid(False)

ax2 = ax1.twinx()
ax2.plot(pair2, label=jsyh, color='orange')
ax2.grid(False)

lines, labels = ax1.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()
ax1.legend(lines + lines2, labels + labels2, loc='best')

plt.show()
```

<!-- BEGIN IPYNB STRIPOUT -->
![](https://images.jieyu.ai/images/2025/01/jsyh-vs-gsyh.jpg)
<!-- END IPYNB STRIPOUT -->

可以看出，两者的走势非常接近，因此，它们之间的价格差就很有可能是一个平稳时间序列。不过，一般情况下，我们不能直接使用两者之间的价差，这里还涉及到一个对冲比的概念。

对资产序列A和B，如果A的价格是B的价格的10倍，那么，即使A与B的走势相接近，序列$A-B$也不构成平稳时间序列，因为此时$A-B\approx A\times 0.9$，如果此时A不是平稳序列，因此$A\times0.9$也不可能是平稳序列。但是$A - 10\times B$则可能是平稳序列。这里的10，就称为对冲比，Hedge Ratio。

计算Hedge Ratio需要对A和B进行线性回归，取其斜率即为对冲比。

<Example id=hedge-ratio/>

```python
from statsmodels.tsa.stattools import adfuller, coint
import statsmodels.api as sm

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 30)

gsyh = "601398.XSHG"
jsyh = "601939.XSHG"

barss = load_bars(start, end, (gsyh, jsyh))

pair1 = barss.xs(gsyh, level=1).close
pair2 = barss.xs(jsyh, level=1).close

def hedge_ratio(price1: NDArray, price2: NDArray) -> float:
    X = sm.add_constant(price1)
    model = sm.OLS(price2, X).fit()
    return model.params[1]

hr = hedge_ratio(pair1, pair2)
print(f"hedge_ratio为:{hr:.2f}")
spreads = pair2 - pair1 * hr
result = adfuller(spreads)
if result[1] < 0.05:
    print(f"p-value: {result[1]:.2f} < 0.05, 平稳序列")
else:
    print(f"p-value: {result[1]:.2f} > 0.05 非平稳序列")
```

由于构造出来的spreads是平稳序列，也就是，它的均值、标准差不随时间而变化，因此，一旦spread偏均值，就必然发生回归。因此，我们可以在spread偏离均值的时候，做空其中一支，反向做多另外一支，直到均值回归时平仓，从而实现套利。

### 1.3. 协整检验

显然，并不是所有的资产都可以两两配对，通过价差来构造平稳时间序列。实际上，在上例中，即使我们只取2022年到2023年这两年间的数据，我们会发现，建设银行与工商银行尽管走势仍然很接近，但却无法构造出一个具有时间平稳性的spreads序列。

为了避免盲目地构建价差序列，我们可以使用协整检验进行预检验。

<Example id=cointegration/>

```python
from statsmodels.tsa.stattools import coint

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 30)

gsyh = "601398.XSHG"
jsyh = "601939.XSHG"
barss = load_bars(start, end, (gsyh, jsyh))

pair1 = barss.xs(gsyh, level=1).close
pair2 = barss.xs(jsyh, level=1).close

tstat, p_value, *_ = coint(pair1, pair2)

if p_value < 0.05:
    print(f"p-value: {p_value:.2f}, 协整序列")
else:
    print(f"p-value: {p_value:.2f}, 非协整序列")
```

在通过协整检验之后，我们再来通过线性回归，构造价差序列。

## 2. 聚类算法

有了协整性检验和价差构建方法之后，理论上我们已经可以实施一个配对交易算法了：获取所有的资产价格，然后对它们进行两两协整性检验，如果两支资产是协整的，那么我们就可以构造出一个平稳时间序列，从而进行套利。

但是，在实操中，我们不可能对所有的资产进行两两协整性检验，因为这需要计算量非常大。

如果对N支资产，我们直接通过协整性分析来寻找配对，这样需要计算:
$$\frac{N\times(N-1)}{2}$$
次协整性检验。如果我们有100个样本，那么，协整计算量将为4950次。如果我们把A股的所有资产（包括股票和基金）、商品期货全部纳入跟踪范围，大约是1万支左右，全部扫描一次，要进行两两协整运算，计算量将是4900多万次。

如果我们能先对这些资产进行粗略的分类，然后在分类中进行两两协整性检验呢？

![](https://images.jieyu.ai/images/2024/12/clustering.jpg)

假设我们可以将样本聚类成K个簇，假设每簇内标的数量分别为$( N_1, N_2, \ldots, N_K )$，且$ ( \sum_{i=1}^{K} N_i = N )$，那么，我们要进行协整性计算的量将变为:
$$\sum_{i=1}^{K} \binom{N_i}{2} = \sum_{i=1}^{K} \frac{N_i (N_i - 1)}{2}$$

在K较大时，计算量就远小于直接进行两两协整性分析。同样地，假如我们有100个样本，能够平均分配为10个簇，现在的计算就是450次，减少了90%还多。当然，这一切都建立在聚类操作本身速度够快的假设之上。


### 2.1. 从KMeans到HDBSCAN

K - Means 是一种基于划分的聚类算法。聚类的目标是将数据集划分为不同的组（簇），使得同一簇内的数据点相似度尽可能高，而不同簇之间的数据点相似度尽可能低。
算法步骤是：
1. 初始化：首先确定要划分的簇的数量。然后随机选择个数据点作为初始的聚类中心（质心）。
2. 分配数据点：对于数据集中的每个数据点，计算它到个聚类中心的距离（通常使用欧几里得距离）。将数据点分配到距离它最近的聚类中心所属的簇。
3. 更新聚类中心：在所有数据点分配完成后，重新计算每个簇的质心。
4. 迭代收敛：重复分配数据点和更新聚类中心这两个步骤，直到聚类中心不再发生明显变化（通常通过设定一个收敛阈值来判断，如两次迭代中质心的移动距离小于某个值）或者达到最大迭代次数。

K-Means的算法原理直观易懂，计算效率比较高，同时，对球形分布的数据聚类效果较好。但它也有明显的缺点，最突出的是，需要预先指定簇的数量k，对初始聚类中心敏感，而这些都是预先难以估计的。此外，它不能处理处理非球形的数据分布和复杂形状的数据，对噪声和离群点敏感（即受离群值影响）。

<!--k-means不能容忍噪声。它一定要工作到把最后一个点归为止-->

为了解决这些问题，人们又提出了层次聚类算法（Hierarchical Clustering Algorithm）和DBSCAN（Density - Based Spatial Clustering of Applications with Noise）算法。

这里简单介绍下DBSCAN算法。它是一种基于密度的聚类算法。它定义了两个重要的参数：$\epsilon$（邻域半径）和 $minPts$（最小点数）。如果一个数据点的邻域内包含至少个数据点，则该点被称为核心点。一个簇是由一个核心点以及与它密度相连的所有点组成的最大集合。密度相连是指通过一系列的核心点可以从一个点到达另一个点。不属于任何簇的点被视为噪声点。

DBSCAN算法的优点是，它不需要预先指定簇的数量，可以自动确定最佳的簇数量，并且可以发现任意形状的数据分布，包括球形、非球形、环形等复杂形状的簇。对于包含噪声的数据有很好的处理能力，能够有效地识别并分离出噪声点。

但是，DBSCAN不能很好地处理密度差异较大的数据。如果数据集中不同区域的密度相差很大，可能会导致聚类效果不佳。此外，它对超参数$\epsilon$和$minPts$的比较敏感。不同的参数设置可能会产生不同的聚类结果，需要用户根据数据的特点和经验进行合理选择。

为了改进DBSCAN，人们又发明了HDBSCAN[^McInnes]（Hierarchical Density - Based Spatial Clustering of Applications with Noise）。


<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/how-hdbscan-work.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

HDBSCAN具有层次聚类的特点。它通过构建一个基于密度的层次结构来克服 DBSCAN 的这一问题。这个层次结构以树状图（dendrogram）的形式呈现，能够展示不同密度水平下的聚类情况。用户可以根据实际需求在这个层次结构中选择合适的聚类划分，使得聚类结果更加灵活和稳定。

在sklearn中并没有现成的hdbscan算法实现。我们一般是通过hdbscan[^hdbscan]这个python库来使用它。

<!-- BEGIN IPYNB STRIPOUT -->
![](https://images.jieyu.ai/images/2025/01/performance-hdbscan.jpg)

根据hdbscan自己的测试，在对30000及更多数据点进行聚类时，hdbscan只需要几秒钟。只有k-means算法比它更快。
<!-- END IPYNB STRIPOUT -->

下面，我们就尝试一下这个算法。

<Example id="hdbscan"/>

```python
import hdbscan
import pandas as pd
from sklearn.manifold import TSNE
import plotly.express as px

start = datetime.date(2022, 1, 1)
end = datetime.date(2023,12,31)

barss = load_bars(start, end, 2000)

closes = barss["close"].unstack().ffill().dropna(axis=1, how='any')

# 使用 HDBSCAN 进行聚类
clusterer = hdbscan.HDBSCAN(min_cluster_size=3, min_samples=2)
cluster_labels = clusterer.fit_predict(closes.T)

# 将聚类结果添加到 DataFrame 中
clustered = closes.T.copy()
clustered['cluster'] = cluster_labels

# 剔除类别为-1的点，这些是噪声，而不是一个类别
clustered = clustered[clustered['cluster'] != -1]
clustered_close = clustered.drop("cluster", axis=1)

# 使用t-SNE进行降维
tsne = TSNE(n_components=3, random_state=42)
tsne_results = tsne.fit_transform(clustered_close)

# 将t-SNE结果添加到DataFrame中
reduced_tsne = pd.DataFrame(data=tsne_results, columns=['tsne_1', 'tsne_2', 'tsne_3'], index=clustered_close.index)
reduced_tsne['cluster'] = clustered['cluster']

fig_tsne = px.scatter_3d(
    reduced_tsne, 
    x='tsne_1', y='tsne_2', z='tsne_3',
    color='cluster', 
    title='t-SNE Clustering of Stock Returns',
    labels={'tsne_1': 't-SNE Component 1', 'tsne_2': 't-SNE Component 2'}
)

fig_tsne.layout.width = 800
fig_tsne.layout.height = 600

fig_tsne.show()
```

从结果中可以看出，除一个数量特别多的簇（在示例中是第17簇， 大约有1200个样本）之外，还有一些簇，大小在10左右。现在，我们选择其中的第12簇来进一步讨论。

首先，我们把它们的走势都绘制出来：

<Example id=cluster-12/>

```python
plt.figure(figsize=(12,10))
cluster_12 = clustered.query("cluster == 12").index.tolist()
for code in cluster_12:
    bars = barss.xs(code, level=1)["close"]
    plt.plot(bars)
```

<!-- BEGIN IPYNB STRIPOUT -->
![](https://images.jieyu.ai/images/2025/01/cluster-12-all.jpg)
<!-- END IPYNB STRIPOUT -->

直观印象告诉我们，hdbscan干得不错！但是，在这一簇里，是否存在协整对呢？这还需要通过进一步检验来确认。

<Example id=confirm-coint/>

```python
pairs = []

for i in range(len(cluster_12)):
    for j in range(i + 1, len(cluster_12)):
        pair1 = cluster_12[i]
        pair2 = cluster_12[j]
        price1 = barss.xs(pair1, level=1)["close"].ffill().dropna()
        price2 = barss.xs(pair2, level=1)["close"].ffill().dropna()
        minlen = min(len(price1), len(price2))
        t, p, *_ = coint(price1[-minlen:], price2[-minlen:])
        if p < 0.05:
            pairs.append((pair1, pair2))

row = max(1, len(pairs) // 3)
col = len(pairs) // row

if row * col < len(pairs):
    row += 1

cells = row * col

fig, axes = plt.subplots(row, col, figsize=(col * 3,row * 3))
axes = np.array(axes).flatten()
fig.suptitle('Cointegrated Pairs')

plot_index = 0
for pair1, pair2 in pairs:
    ax = axes[plot_index]
    
    price1 = barss.xs(pair1, level=1)["close"]
    price2 = barss.xs(pair2, level=1)["close"]
    
    ax.plot(price1, label=pair1)
    ax.plot(price2, label=pair2)
    ax.set_title(f'{pair1[:-5]} & {pair2[:-5]}')
    ax.set_xticks([])
    plot_index += 1


plt.tight_layout()
plt.show()
```

我们对所有的组合逐一进行遍历，运用 coint 方法精准判别其中的协整对，一旦确认，便即刻留存中间结果。待全部组合筛查完毕，我们将所有匹配的对子绘制成细致的子图。


<!-- BEGIN IPYNB STRIPOUT -->
![](https://images.jieyu.ai/images/2025/01/cluster-12-one-on-one.jpg)
<!-- END IPYNB STRIPOUT -->

其中部分子图所呈现的规律极具吸引力。在绝大多数时段里，图中的两条曲线宛如默契十足的舞者，步伐协调一致，紧紧相随；然而，在为数不多的几个瞬间，它们却如同脱缰的野马，短暂偏离既定轨迹，而后又仿若被一股无形之力牵引，迅速回归正道。

这些脱轨时刻，正是猎豹静候已久的战机。

## 3. 数据降维

在前一小节的例子中，出于可视化的目的，我们使用了T-SNE对数据进行了降维。在聚类分析中，我们也常常看到降维算法的运用，其中最常用的，就是主成分分析。

主成分分析（Principal Component Analysis，简称 PCA）是一种多元统计分析方法。它的主要目的是通过线性变换将原始数据转换为一组新的变量，这些新变量被称为主成分。主成分是原始变量的线性组合，并且按照方差从大到小依次排列。

为了更好地理解PCA分析，我们可以设想一个图片压缩的例子。

<Example id=image-compression/>

```python
from sklearn.datasets import load_sample_image
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline

# 1. 加载图像数据
china = load_sample_image("china.jpg")
origin_img = np.mean(china, axis=2).astype(np.uint8)
h, w = origin_img.shape

variance = 0.95
pipeline = Pipeline([
    ("scaler", StandardScaler()),
    ("pca", PCA(n_components=variance))
])

# 2. 拟合并转换数据
compressed = pipeline.fit_transform(origin_img)
n_features = compressed.shape[1]
print(f"分解后的主成分个数：{n_features}")

# 3. 计算压缩比
compression_ratio = (h * n_features + n_features * w)/ (h * w)
print(f"Compression ratio: {compression_ratio:.2f}")

# 4. 可视化压缩后的图像
reconstructed = pipeline.inverse_transform(compressed)

# 转换image三通道
reconstructed_3d = reconstructed[:, :, np.newaxis]
gray_image = np.clip(np.repeat(reconstructed_3d, 3, axis=2), 0, 255)

# 绘制原始图像和压缩后的图像
fig, axes = plt.subplots(1, 2, figsize=(12, 6))
axes[0].imshow(china)
axes[0].set_title("Original Image")
axes[0].axis('off')

axes[1].imshow(gray_image.astype(np.uint8))
axes[1].set_title(f"Reconstructed Image ({variance:.0%} Variance)")
axes[1].axis('off')

plt.tight_layout()
plt.show()
```

<!-- BEGIN IPYNB STRIPOUT -->
![](https://images.jieyu.ai/images/2025/01/summer-palace.jpg)
<!-- END IPYNB STRIPOUT -->

!!! tip
    请学员自行修改第12行的`variance`参数，以观察不同压缩比的图像还原度和压缩比。

我们看到，在保留99%的方差时，图像还原度很高，但此时我们要保留的数据只有原始数据的66%。如果我们容忍更大的失真，比如，只保留95%的方差，此时只需要保留原始数据的21%。

假设我们有n支股票，取它们过去m个时间点的价格数据，这就形成了一个$n*m$的矩阵。我们可以把每一列看成一个"feature"，采用PCA对其进行降维，以加快后续处理的速度。从我们前面的图像压缩例子来看，我们可以把维度降低到之前的10%左右，但仍然保留着原来数据95%以上的特征信息。因此，在需要速度的场合，这无疑是有用的。PCA本身的计算只涉及到一些矩阵运算（包括协方差），在现代计算机（以及Python中），这些运算都能利用硬件指令，速度非常快。

## 4. 带聚类算法的配对交易策略框架

做好了所有的准备之后，现在我们将来实现一个带聚类算法的配对交易框架。这个框架包括以下步骤。

1. 收集数据，提取特征并进行数据预处理，得到特征数据集。这里可能需要去掉缺失值，离群值、进行缩放等操作。
2. 对特征数据进行降维。
3. 对降维后的特征数据进行聚类。
4. 在每个聚类中，选择具有较高协整性和平稳性的配对
5. 构建交易策略。

### 4.1. 构建特征数据集

在前面的例子中，我们直接使用了价格数据，也可以使用技术指标或者基本面数据。在使用价格数据时，我们是把每一个交易日的价格看成是一种特征。如果我们还要加上这些时间点上的技术指标或者基本面数据，显然，每一个样本的特征，就构成了一个二维数组。

无论是PCA还是聚类算法，要求的输入格式都常常是二维数据，每个样本占一行。因此，样本的特征必须重塑成一维数组。

在进行降维时，PCA算法对每个样本，要求的输入是一维数组，因此，当我们有多个特征，从而构成二维数组时，可以将其转换为一维数组。

一些例子会使用每日收益，或者股价的对数来作为聚类特征。从前面的例子中，我们已经知道，每日收益序列接近平稳序列（股价对数相当于每日收益），因此，聚类算法就难以区分出尽可能多的簇来，这对我们寻找协整对是不利的。


### 4.2. 降维

在前面的示例中，我们使用的close数据跨越了两年，相当于使用了近500个特征。在这种情况下，对约2000支个股进行聚类分析，在课程环境下运行时间是2秒左右，这是一个完全可以接受的时间。

但如果我们要进行更长时间跨度的聚类分析，或者引入更多特征，这时就可能需要进行降维。

这个方法可写成：

<Example id=reduce-dimension/>

```python
def reduce_dimension(features: pd.DataFrame, variance: float = 0.999)->pd.DataFrame:
    """对特征数据进行降维

    Args:
        features (pd.DataFrame): 特征数据，要求索引为样本id（比如symbol）,特征为列
        variance: 降维时保留的方差百分比。
    returns:
        pd.DataFrame: 降维后的特征数据，索引为样本id，列为降维后的主成分
    """
    scaler = StandardScaler()
    pca = PCA(n_components=variance)
    pipeline = Pipeline([("scaler", scaler), ("pca", pca)])
    reduced = pipeline.fit_transform(features)
    reduced = pd.DataFrame(data=reduced, index=features.index)

    logger.info(f"降维效果：{pca.n_components_}/{features.shape[1]}")
    return reduced

start = datetime.date(2022, 1, 1)
end = datetime.date(2023,12,31)

barss = load_bars(start, end, 2000)

closes = barss["close"].unstack().ffill().dropna(axis=1, how='any')
reduce_dimension(closes.T, 0.999)
```

我们给出的默认参数是0.999。以价格序列为例，在此情况下，PCA对特征的压缩比仍然达到了35/484。

### 4.3. 聚类

我们通过下面的代码来实现聚类，返回聚类结果。

<Example id=hdbscan-clustering/>

```python
def hdbscan_clustering(features: pd.DataFrame, 
               min_clusters: int=3, 
               min_samples: int = 2)-> List[List[Any]]:
    """按输入的特征，运用hdbscan进行聚类
        
    Args:
        features: 特征数据，要求索引为样本id（比如symbol）,特征为列
        min_clusters: 最小分类数
        min_samples: 分类中的最小样本数
    Returns:
        返回聚类结果，各个聚类的样本id列表，按元素多少从小到大排序
    """
    clusterer = hdbscan.HDBSCAN(min_cluster_size=min_clusters, 
                                min_samples=min_samples)
    labels = clusterer.fit_predict(features)
    df = pd.DataFrame(labels, columns=["label"], index = features.index)
    valid = df.query("label != -1")
    result = valid.groupby("label").apply(lambda x: x.index.tolist())
    return sorted(result, key=lambda item: len(item))

start = datetime.date(2022, 1, 1)
end = datetime.date(2023,12,31)

barss = load_bars(start, end, 2000)

closes = barss["close"].unstack().ffill().dropna(axis=1, how='any')
clusters = hdbscan_clustering(closes.T)
```

在这个聚类结果中，我们已经过滤掉了噪声样本（即无法聚类的样本）。

### 4.4. 寻找配对

接下来，我们就需要在聚类结果中，寻找协整对。

<Example id=find-pairs/>

```python
import itertools
import statsmodels.tsa.stattools as ts
import statsmodels.api as sm

def hedge_ratio(price1: NDArray|pd.Series, price2: NDArray|pd.Series, use_log=False) -> float:
    """计算两个序列的对冲比率

    Args:
        price1 (NDArray): 序列一
        price2 (NDArray): 序列二
        use_log (bool, optional):当为True时，计算对数价格的对冲比率. Defaults to False.

    Returns:
        float: 对冲比率
    """
    if use_log:
        price1 = np.log(price1)
        price2 = np.log(price2)

    if isinstance(price1, pd.Series):
        price1 = price1.values

    if isinstance(price2, pd.Series):
        price2 = price2.values
        
    X = sm.add_constant(price1)
    model = sm.OLS(price2, X).fit()
    if isinstance(model.params, pd.Series):
        return model.params.iloc[1]
    else:
        return model.params[1]

def halflife_and_hurst(spreads: NDArray|pd.Series, max_lag: int=20):
    """计算spreads序列的hurst exponent指数和半衰期
        传入的spreads必须能通过adfuller检验。
        本算法来自https://github.com/bartchr808/Quantopian_Pairs_Trader/tree/master及
        https://www.quantstart.com/articles/Basics-of-Statistical-Mean-Reversion-Testing/

    Args:
        spreads: hedge之后的残差序列
        max_lag: 最大自相关延时跨度

    """
    if isinstance(spreads, pd.Series):
        spreads = spreads.values

    # hurst
    lags = range(2, max_lag)
    tau = [np.sqrt(np.std(np.subtract(spreads[lag:], spreads[:-lag]))) for lag in lags]

    hurst, *_ = np.polyfit(np.log10(lags), np.log10(tau), 1)
    hurst *= 2

    # 半衰期
    lag = np.roll(spreads, 1)
    lag[0] = 0
    ret = spreads - lag
    ret[0] = 0

    lag2 = sm.add_constant(lag)
    model = sm.OLS(ret, lag2)
    res = model.fit()
    phi = res.params[1]

    if phi >= 0:
        raise ValueError(f"phi（{phi}）应该小于零。请确保spreads为平稳序列")
    half_life = -np.log(2) / phi

    return half_life, hurst

            
def find_pairs(barss: pd.DataFrame, clusters: List[List[str]]) -> pd.DataFrame:
    pairs = []
    for stocks in clusters:
        if len(stocks) < 2:
            continue  # 至少需要两只股票进行协整性检验

        for pair in itertools.combinations(stocks, 2):
            stock1, stock2 = pair

            # 获取两只股票的价格序列
            price1 = barss.xs(stock1, level=1).close.ffill().dropna()
            price2 = barss.xs(stock2, level=1).close.ffill().dropna()

            min_common_len = min(len(price1), len(price2))
            price1 = price1[-min_common_len:].values
            price2 = price2[-min_common_len:].values
                
            _, coint_p_value, *_ = ts.coint(price1, price2)

            if coint_p_value >= 0.05: # 非协整对
                continue

            # 计算spreads，进一步检验协整性
            hr = hedge_ratio(price1, price2)
            
            spreads = price2 - hr * price1
            _, p_value, *_ = ts.adfuller(spreads)

            if p_value >= 0.05: # 残差序列不平稳
                continue

            try:
                half_life, hurst = halflife_and_hurst(spreads)
            except ValueError:
                continue
    
            pairs.append(
                (stock1, stock2, hr, hurst, half_life, coint_p_value)
            )

    return pd.DataFrame(pairs, columns=["pair1", "pair2", "hedge_ratio", "hurst", "half_life", "coint_p_value"])

# 为节省时间，只计算第一个聚类
find_pairs(barss, [clusters[0]])
```

这里出现了两个新的概念，即半衰期(half-time)和 hurst exponent指数。当我们在配对交易开仓时，希望价差快速回归均值，以便平仓兑现利润。回归的速度就可以使用半衰期来表示。

Hurst[^hurst]指数是用来衡量时间序列的长期记忆性的指标。如果hurst指数小于0.5，表明序列具有均值回归特性；如果等于0.5，表明序列是无记忆的，具有随机游走特性；如果大于0.5，则表明序列可能保持当前的趋势。这一指标也可以用在纯多头策略。在多头策略上涨过程中，如果hurst大于0.5，表明上涨趋势将持续，此时可以适当加仓。


### 4.5. 执行交易

在执行交易时，其逻辑比较类似于布林带策略。因为我们构建的spreads序列是一个平稳序列且符合正态分布，因此，我们一般以当前的spread超过spread均值的n倍标准差来判断是否开仓，并在spread回归到均值时平仓。也有进一步将其简化为对z-score分数判断的做法。

<Example id=z-score/>

```python
def next(close1, close2, delta: float = 1, epsilon: float = 0.01):
    hr = hedge_ratio(price1, price2)
    
    spreads = price2 - hr * price1
    mean_spread = np.mean(spreads)
    std_spread = np.std(spreads)

    spread = spreads[-1]
    if (spread > mean_spread + delta * std_spread):
        print(f"做多 {close1}，做空 {close2}")
    elif (spread < mean_spread - delta * std_spread):
        print(f"做空 {close1}，做多 {close2}")
    elif (abs(spread - mean_spread) < epsilon):
        print("平仓")
```

在实际的交易中，我们还要考虑其它结束交易的情况，比如，在我们入场时，资产是协整的，但运行一段时间之后，就有可能变得不再协整。此时我们就需要考虑退出交易。

## 5. 配对交易实例

下面，我们提供一个基于backtrader的策略实例。对不熟悉backtrader用法的同学，我们在《量化24课》中有详细介绍如何使用。

```python
import datetime
import itertools
import logging
from typing import Any, List, Tuple

import backtrader as bt
import hdbscan
import numpy as np
import pandas as pd
import pyfolio as pf
import statsmodels.api as sm
import statsmodels.tsa.stattools as ts
from backtrader.feeds import PandasData
from freezegun import freeze_time
from numpy.typing import NDArray
from sklearn.decomposition import PCA
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler

logging.basicConfig(level=logging.INFO, format='%(asctime)s %(message)s', datefmt="%Y/%m/%d")
logger = logging.getLogger("pairtrade")


def is_notebook():
    try:
        shell = get_ipython().__class__.__name__
        if shell == 'ZMQInteractiveShell':
            return True  # Jupyter notebook or qtconsole
        elif shell == 'TerminalInteractiveShell':
            return False  # Terminal running IPython
        else:
            return False  # Other type (?)
    except NameError:
        return False  # Probably standard Python interpreter

class PairTradingStrategy(bt.Strategy):
    params = dict(
        half_life_thresh=10,
        periods=250,
        delta=2
    )

    def __init__(self):
        # status: 0 未持仓 -1 做多pair1, 做空pair2中；1 做空pair1, 做多pair2中
        self.status = 0

        self.pair1 = self.datas[0]
        self.pair2 = self.datas[1]

        log_price1 = np.log(list(self.pair1.close)[: self.p.periods])
        log_price2 = np.log(list(self.pair2.close)[: self.p.periods])

        # 计算对数价格的hedge_ratio
        hr_log = self.hedge_ratio(log_price1, log_price2)

        # 计算spread
        self.spreads = (log_price2 - hr_log * log_price1).tolist()

        self.mean_spread = np.mean(self.spreads)
        self.std_spread = np.std(self.spreads)

    def log(self, msg, *args, **kwargs):
        """日志记录工具，支持自动记录交易时间，而不是执行回测时的系统时间"""
        dt = kwargs.get("dt")
        if dt is None:
            dt = self.datas[0].datetime.datetime()
        else:
            dt = bt.num2date(dt)
            del kwargs["dt"]

        with freeze_time(dt):
            logger.info(msg, *args, **kwargs)

    def notify_order(self, order):
        if order.status in [bt.Order.Submitted, bt.Order.Accepted]:
            return  # Await further notifications

        if order.status == order.Completed:
            name = order.data._name
            price = order.executed.price
            size = order.executed.size
            
            if order.isbuy():
                self.log("买入 %s(%s, %s)", name, price, size, dt = order.executed.dt)
            else:
                self.log("卖出 %s(%s, %s)", name, price, size, dt = order.executed.dt)

    def notify_trade(self, trade):
        if trade.isclosed:
            name = trade.data._name
            price = trade.price
            size = trade.size
            pnl = trade.pnlcomm
            self.log("交易结束 %s(%s, %s, %s)", name, price, size, pnl, dt = trade.dtclose)

    @classmethod
    def reduce_dimension(cls, features: pd.DataFrame, variance: float = 0.999)->pd.DataFrame:
        """对特征数据进行降维

        Args:
            features (pd.DataFrame): 特征数据，要求索引为样本id（比如symbol）,特征为列
            variance: 降维时保留的方差百分比。
        returns:
            pd.DataFrame: 降维后的特征数据，索引为样本id，列为降维后的主成分
        """
        scaler = StandardScaler()
        pca = PCA(n_components=variance)
        pipeline = Pipeline([("scaler", scaler), ("pca", pca)])
        reduced = pipeline.fit_transform(features)
        reduced = pd.DataFrame(data=reduced, index=features.index)

        logger.info("降维效果：%s/%s", pca.n_components_, features.shape[1])
        return reduced
    
    @classmethod
    def hdbscan_clustering(cls, features: pd.DataFrame,
                min_clusters: int=3,
                min_samples: int = 2,
                reduce_before_clustering: bool=False,
                **kwargs)->List[Any]:
        """按输入的特征，运用hdbscan进行聚类
            
        Args:
            features: 特征数据，要求索引为样本id（比如symbol）,特征为列
            min_clusters: 最小分类数
            min_samples: 分类中的最小样本数
            reduce_before_clustering: 是否要在聚类前进行降维
            kwargs: 如果reduce_before_clustering为真，则接受variance参数，表示降维保留的方差百分比
        Returns:
            返回聚类结果，key为聚类编号，value为该聚类中的样本id列表（即feature.index）
        """
        if reduce_before_clustering:
            variance = kwargs.get("variance", 0.999)
            features = cls.reduce_dimension(features, variance)

        clusterer = hdbscan.HDBSCAN(min_cluster_size=min_clusters, 
                                    min_samples=min_samples)
        labels = clusterer.fit_predict(features)
        df = pd.DataFrame(labels, columns=["label"], index = features.index)
        valid = df.query("label != -1")
        result = valid.groupby("label").apply(lambda x: x.index.tolist())

        return sorted(result, key=lambda item: len(item))

    @classmethod
    def hedge_ratio(cls, price1: NDArray|pd.Series, price2: NDArray|pd.Series, use_log=False) -> float:
        """计算两个序列的对冲比率

        Args:
            price1 (NDArray): 序列一
            price2 (NDArray): 序列二
            use_log (bool, optional):当为True时，计算对数价格的对冲比率. Defaults to False.

        Returns:
            float: 对冲比率
        """
        if use_log:
            price1 = np.log(price1)
            price2 = np.log(price2)

        if isinstance(price1, pd.Series):
            price1 = price1.values # type: ignore

        if isinstance(price2, pd.Series):
            price2 = price2.values # type: ignore
            
        X = sm.add_constant(price1)
        model = sm.OLS(price2, X).fit()
        if isinstance(model.params, pd.Series):
            return model.params.iloc[1]
        else:
            return model.params[1]

    @classmethod
    def halflife_and_hurst(cls, spreads: NDArray|pd.Series, max_lag: int=20):
        """计算spreads序列的hurst exponent指数和半衰期
            传入的spreads必须能通过adfuller检验。
            本算法来自https://github.com/bartchr808/Quantopian_Pairs_Trader/tree/master及
            https://www.quantstart.com/articles/Basics-of-Statistical-Mean-Reversion-Testing/

        Args:
            spreads: hedge之后的残差序列
            max_lag: 最大自相关延时跨度

        """
        if isinstance(spreads, pd.Series):
            spreads = spreads.values # type: ignore

        # hurst
        lags = range(2, max_lag)
        tau = [np.sqrt(np.std(np.subtract(spreads[lag:], spreads[:-lag]))) for lag in lags]

        hurst, *_ = np.polyfit(np.log10(lags), np.log10(tau), 1)
        hurst *= 2

        # 半衰期
        lag = np.roll(spreads, 1)
        lag[0] = 0
        ret = spreads - lag
        ret[0] = 0

        lag2 = sm.add_constant(lag)
        model = sm.OLS(ret, lag2)
        res = model.fit()
        phi = res.params[1]

        if phi >= 0:
            logger.warning("phi %s 应该小于零。halflife计算失败，已置为999")
            half_life = 999
        else:
            half_life = -np.log(2) / phi

        return half_life, hurst

    @classmethod
    def find_pairs(cls, barss: pd.DataFrame, clusters: List[List[str]]) -> pd.DataFrame:
        pairs = []
        for stocks in clusters:
            if len(stocks) < 2:
                continue  # 至少需要两只股票进行协整性检验

            for pair in itertools.combinations(stocks, 2):
                stock1, stock2 = pair

                # 获取两只股票的价格序列
                price1 = barss.xs(stock1, level=1).close.ffill().dropna()
                price2 = barss.xs(stock2, level=1).close.ffill().dropna()

                min_common_len = min(len(price1), len(price2))
                price1 = price1[-min_common_len:].values
                price2 = price2[-min_common_len:].values
                    
                _, coint_p_value, *_ = ts.coint(price1, price2)

                if coint_p_value >= 0.05: # 非协整对
                    continue

                # 计算spreads，进一步检验协整性
                hr = cls.hedge_ratio(price1, price2)
                
                spreads = price2 - hr * price1
                _, p_value, *_ = ts.adfuller(spreads)

                if p_value >= 0.05: # 残差序列不平稳
                    continue

                try:
                    half_life, hurst = cls.halflife_and_hurst(spreads)
                except ValueError:
                    continue
        
                pairs.append(
                    (stock1, stock2, hr, hurst, half_life, coint_p_value)
                )

                if len(pairs) >= 10:
                    break

        return pd.DataFrame(pairs, columns=["pair1", "pair2", "hedge_ratio", "hurst", "half_life", "coint_p_value"])

    def close_position(self, reason: str):
        if self.status != 0:
            self.order_target_percent(self.pair1, target=0)
            self.order_target_percent(self.pair2, target=0)
            self.log(reason)

        self.status = 0

    def next(self):
        if len(self.pair1) < self.p.periods:
            return
        
        # 获取最新数据
        price1 = np.array(self.pair1.close.get(size=self.p.periods))
        price2 = np.array(self.pair2.close.get(size=self.p.periods))

        hr = self.hedge_ratio(price1, price2)
        spreads = price2 - hr * price1
        halflife, hurst = self.halflife_and_hurst(spreads)
        
        # 不满足协整条件时，不开仓
        if self.status == 0 and (hurst >= 0.5 or halflife >= self.p.half_life_thresh):
                return
            
        mean_spread = np.mean(spreads)
        std_spread = np.std(spreads)

        spread = spreads[-1]
        self.spreads.append(spread)

        # self.log("spread %s, mean %s, std %s, upper %s, lower %s", spread, mean_spread, std_spread, mean_spread + self.p.delta * std_spread, mean_spread - self.p.delta * std_spread)
        
        # 出场条件：spread从上方回归
        if self.status == 1 and spread < mean_spread:
            self.close_position("spread从上方回归，退出")
            return
        
        # 出场条件：spread从下方回归
        if self.status == -1 and spread > mean_spread:
            self.close_position("spread从上方回归，退出")
            return

        if self.status != 0:
            return
        
        # 入场条件：如果spread大于均值 delta 倍标准差，则做多pair1，做空pair2
        if self.status == 0 and (spread > mean_spread + self.p.delta * std_spread):
            self.log("入场条件1")
            self.order_target_percent(self.pair1, target=0.5)
            self.order_target_percent(self.pair2, target=-0.5)
            self.status = 1
        
        # 入场条件：如果spread小于均值 delta 倍标准差，则做空pair1，做多pair2
        if self.status == 0 and (spread < mean_spread - self.p.delta * std_spread):
            self.log("入场条件2")
            self.order_target_percent(self.pair1, target=-0.5)
            self.order_target_percent(self.pair2, target=0.5)
            self.status = -1

    @classmethod
    def backtest(
        cls,
        universe: List[str]|int,
        start: datetime.datetime,
        end: datetime.datetime,
        cash: float = 1_000_000,
        commission: float = 1e-4,
        periods: int = 250,
        delta: float = 1,
        half_life_thresh = 10
    ):
        cerebro = bt.Cerebro()

        barss = load_bars(start, end, universe)
        closes = barss["close"].unstack().ffill().dropna(axis=1, how='any')

        clusters = cls.hdbscan_clustering(closes.T)
        cls.pairs = cls.find_pairs(barss, clusters)

        if len(cls.pairs) == 0:
            raise ValueError("No cointegrated pairs found")

        pair1, pair2 = cls.pairs.iloc[0][["pair1", "pair2"]]
        data = PandasData(dataname=barss.xs(pair1, level=1))
        cerebro.adddata(data, name=pair1)

        data = PandasData(dataname=barss.xs(pair2, level=1))
        cerebro.adddata(data, name=pair2)

        cerebro.addstrategy(PairTradingStrategy, periods=periods, delta=delta, half_life_thresh= half_life_thresh)
        cerebro.broker.set_cash(cash)
        cerebro.broker.setcommission(commission)

        if is_notebook():
            cerebro.addanalyzer(bt.analyzers.PyFolio)
        return cerebro.run(), cerebro

if not is_notebook():
    def load_bars(start: datetime.date, end: datetime.date, universe: List[str]) -> pd.DataFrame:
        raise NotImplementedError("load_bars函数需要自行实现")

strategies, cerebro = PairTradingStrategy.backtest(
    500,
    datetime.date(2021, 1, 1),
    datetime.date(2023, 12, 31),
    delta=0.5,
    half_life_thresh = 20
)


if is_notebook():
    pyfoliozer = strategies[0].analyzers.getbyname('pyfolio')
    returns, positions, transactions, gross_lev = pyfoliozer.get_pf_items()
    pf.tears.create_full_tear_sheet(
        returns,
        positions=positions,
        transactions=transactions,
    )
else:
    cerebro.plot()
```


回测结果表明，该策略实现了20.89%的年化收益，从2021年到2023年底，累计收益72.7%，不过最大回撤也达到了24.57%。这对机构来讲当然无法接受。

如果你要在本地运行此程序，需要实现自己的load_bars函数，以加载你的数据。如果打算将此策略实盘，你还需要进行一些移植工作。


你可以通过backtrader的参数优化工具来优化参数，主要是delta、half_life_thresh参数。此外，平仓条件也可以根据你的需求进行修改。另外，在寻找出来的多个配对中，我们只抽取了第一对进行了交易。你也可以尝试选择更好的配对（比如hurst最小，或者half_life最小）。


## 6. 延伸阅读

### 6.1. 更多聚类算法的实现

sklearn提供了十三个不同类别的聚类算法。尽管我们在课程中没有讲解这些算法，但并不意味着他们没有实用价值。相反地，这些聚类算法中，有许多是针对特定任务专门设计的（例如共聚类和双聚类，或者聚类特征而不是数据点）。因此，如果你对数据有足够的了解，你可以根据数据的类型或数据的重要属性，或者需要进行的聚类类型来缩小适合的聚类算法范围。在[『Comparing Python Clustering Algorithms』](https://nbviewer.org/github/scikit-learn-contrib/hdbscan/blob/master/notebooks/Comparing%20Clustering%20Algorithms.ipynb)这篇文章中，你可以看到作者对各种聚类算法的讨论和比较。如果你想深入理解HDBSCAN的工作原理，可以进一步阅读[『How HDBSCAN Workds』](https://nbviewer.org/github/scikit-learn-contrib/hdbscan/blob/master/notebooks/How%20HDBSCAN%20Works.ipynb)这篇文章。

### 6.2. 配对交易示例

在backtrader中，有一个[配对交易](https://github.com/mementum/backtrader/blob/master/contrib/samples/pair-trading/pair-trading.py)的示例。它的核心思想是，如果两支股票是协整的，那么，它们的之间的价差就满足正态分布。如果在某个时间点上，价差大于$\delta$个标准差，就出现了套利机会。它的计算是通过OLS_TransformationN这个指标在初始化时完成的，此后，只需要在next方法中，通过读取zscore分数来判断是否有开仓条件即可。

要值得注意的是，实现中可能存在错误。比如，它设置了一个名为orderid的变量以控制当前是否开仓，但该变量从未赋值过非None值，也就是并不会起到控制开仓的作用。

### 6.3. 价格还是对数价格？

受条件限制，作者并没有实际从事配对交易的经验，配对交易也无开源且权威的实现。本章的示例系作者根据自己对相关理论的理解，部分算法参考相对权威的实现完成的。

在这些参考资料中，我们常常看到一些示例使用价格，一些示例使用对数价格（或者收益）。

可以明确的是，在进行聚类时，应该使用价格序列作为特征（而不是对数价格，仅就价格与对数价格比较而言，并不排除其它特征的意思）。

在计算协整对及对冲比时，可以肯定地说，我们也应该使用原始价格序列（非对数）来计算 `hedge_ratio`。这是因为协整性检验的核心是检查两个时间序列是否存在长期均衡关系，而这种关系通常是基于原始价格而非对数价格建立的。

一些示例在计算半衰期及Hurst指数时，会使用对数价格，其正确性尚待验证。因为我们基于协整关系构建的spreads序列已经是平稳时间序列，其回归半衰期已经可以确定，并且含义更明确，更符合交易时的需求。

## 7. Footnotes

[^hurst]: 关于Hurst指数的原理及应用，可以阅读[Hurst指数及其应用](https://pictureperfectportfolios.com/how-to-use-the-hurst-exponent-strategy-in-trading/)。此外，关于其计算，一些人会使用[hurst](https://github.com/Mottl/hurst)这个库。
[^pair-trading]: https://github.com/bartchr808/Quantopian_Pairs_Trader和medium文章：https://medium.com/@bart.chr/pairs-trading-for-algorithmic-trading-breakdown-d8b709f59372
[^dickey]: David Dickey, 出生于1945年，爱荷华州立大学统计学博士。Wyne Fuller是爱荷华州立大学的杰出教授，两人是师徒关系，合作发明了平稳时间序列的单位根检验方法，使得研究者能够更准确地判断时间序列的平稳性，进而在经济、金融、工程等众多领域广泛应用于时间序列建模、预测以及因果关系分析等方面。这一检验方法在计量经济学和时间序列统计领域具有深远的影响。
[^hdbscan]: hdbscan在[github](https://github.com/scikit-learn-contrib/hdbscan)上收获了2.8k stars。
[^McInnes]: HDBSCAN算法由Leland McInnes, John Healy于2017年提出。论文发表在IEEE期刊上，在[arxiv](https://arxiv.org/abs/1705.07321)上可以下载论文副本。
