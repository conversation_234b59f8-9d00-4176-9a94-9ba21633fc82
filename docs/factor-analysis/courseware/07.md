# 参数调优和过拟合检测

我们进行因子分析的目的，不是为了证明哪些因子不能用，而是要尽可能地揭示因子与收益之间的关系，找出因子在什么参数、什么样的条件下对收益有较强的预测能力。因此，参数调优（或者寻找最佳参数）本身就是因子分析的一环。

当然，在找到最优参数之后，我们还要进行过拟合检测。

## 1. 重构因子检验过程

为了方便进行参数调优，我们先将 Alphalens 因子检验过程进一步简化和抽象化，再来通过示例讲解参数调优的方向和作用。

<Example id=alphatest/>

```python
from typing import Callable, Tuple, Any
from alphalens.tears import create_full_tear_sheet,create_returns_tear_sheet
from alphalens.utils import get_clean_factor_and_forward_returns
from alphalens.performance import factor_alpha_beta

def alphatest(universe: int|Tuple[str], start: datetime.date, end: datetime.date, calc_factor: Callable[[NDArray, Tuple[Any, ...]], Any], args=(), bins=None, top=-1, bottom=-1, plot_mode='returns', **kwargs):
    """因子检验

    Args:
        universe: 样本数量，或者指定的资产池
        start: 开始日期
        end: 结束日期
        calc_factor: 计算因子的函数
        args: 计算因子的函数的参数
        bins: number of cut by bins, or cut bin edges
        top: top 分层
        bottom: bottom 分层
        plot_mode: 绘图模型。 
            'full' -> create_full_tear_sheet, 
            'returns' -> create_returns_tear_sheet, 
            'quantiles' -> plot_quantile_statistics_table,
            'none' -> 不绘图，只返回回测结果。
    kwargs:
        传递给 get_clean_factor_and_forward_returns 的参数
    """
    # 加载数据
    barss = load_bars(start, end, universe)

    # 计算因子
    factors = (barss.groupby(level='asset')
                    .apply(lambda x: calc_factor(x, *args))
                    .droplevel(level=0)
                )

    # 提取价格数据
    prices = barss['price'].unstack(level=1)

    long_short = kwargs.pop('long_short', True)

    # 预处理
    if bins is not None:
        factor_data = get_clean_factor_and_forward_returns(factors, prices, quantiles=None, bins=bins, **kwargs)
    else:
        if 'quantiles' in kwargs:
            quantiles = kwargs.pop('quantiles')
        else:
            quantiles = 10
        factor_data = get_clean_factor_and_forward_returns(factors, prices, quantiles=quantiles, **kwargs)

    if top != -1:
        factor_data = factor_data[factor_data.factor_quantile <= top]
    if bottom != -1:
        factor_data = factor_data[factor_data.factor_quantile >= bottom]

    alpha = factor_alpha_beta(factor_data, demeaned = long_short)

    if plot_mode == 'quantiles':
        plot_quantile_statistics_table(factor_data)
    elif plot_mode == 'returns':
        create_returns_tear_sheet(factor_data, long_short=long_short)
    elif plot_mode == 'full':
        create_full_tear_sheet(factor_data, long_short=long_short)
    else:
        pass
    return alpha, factor_data
```

这个函数做了这样几件事：

* 一、按 asset 分组计算因子，结果保存在 factors 中。我们把计算因子的方法抽象成一个函数，这个函数是通过 calc_factor 作为参数传入的。这个函数在计算中所需要的参数，也通过 args 参数传入。这是在很多框架中都可以看到的用法。另外，我们在这里通过 groupby.apply，消除了循环。这是我们在上一章承诺要完成的工作。

* 二、简化了 quantiles 与 bins 参数的指定。如果我们在调用 alphatest 时传入了 bins 参数，则会自动指定 quantiles=None。

* 三、允许指定 top 与 bottom 参数来过滤因子，只保留其中线性部分。

* 四、在生成报告时，可以通过 plot_mode 参数来指定是否显示全部报告（full）、只显示收益报告（returns）、只显示分层统计报表，或者不绘制任何图表。无论是哪一种情况，它都会返回 alpha 数据和合并后的 factor_data。这样一来，我们就可以先只生成收益报告快速探索策略，最后再生成完整的报告。

此外，它还允许传入 long_short 参数，来指定是否构建多空组合，或者只执行单边交易。

现在，我们就可以利用这个函数，来实现因子参数调优了。我们将使用 RSI 因子进行参数调优。

## 2. 参数调优

### 2.1. 修正因子方向

<Example id=calc_rsi/>
```python
def calc_rsi(df, n):
    return ta.RSI(df.close.astype(np.float64), n)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

np.random.seed(78)
_ = alphatest(2000, start, end, calc_factor=calc_rsi, args=(6,) )
```

第一次运行时，我们得到的结果并不理想，它的年化收益率为-0.066%。原因是，我们没有对 RSI 进行校正，就直接把它当成了因子。而理论上，RSI 与收益是负相关的。

这是我们的第一次运行。然后我们修改因子的构成，使用 100 - RSI 作为因子，再运行一次：

<Example id=calc_rsi_v2/>

```python
def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

_ = alphatest(2000, start, end, calc_factor=calc_rsi, args=(6,) )
```

这次我们得到了更好一些的结果，至少年化收益成了正数。

如果我们仔细观察分层收益均值图（Mean Period Wise Return By Factor Quantile），我们会发现，在两端因子收益率为负，而中间分层的收益率为正，整体上 RSI 因子与收益并不是线性关系，只在局部存在线性关系。

换句话说，我们应该在 RSI=50 时做多，而在 RSI=90 或者 10 时做空。

### 2.2. 过滤非线性分层

现在，我们把因子的非线性相关部分过滤掉，也就是只保留分层中的 1~5 层。

<Example id=filter_nonlinear/>

```python
_ = alphatest(2000, start, end, calc_factor=calc_rsi, args=(6,), top=5 )
```

这一次，年化 Alpha 达到了 20.9%。对照因子加权多空组合收益图，我们发现从 2018 年以来，执行这个策略的累计收益达到了 2.9 倍，并且曲线很平稳。分层累计收益图的排列也很整齐。

### 2.3. 使用最佳分层方式

我们在上一章中，已经讨论过，对 RSI 这样有明确交易含义、且上下界确定的指标，可能使用 bins 分层更为合理，这样会避免分层重叠。前面的例子中，我们并没有传入 bins 参数，所以，alphatest 函数仍然使用的是按分位数分层方法。

现在，我们就来测试一下，如果使用 bins 分层，效果能否进一步提升。

<Example id=use_bins/>

```python
bins = np.linspace(0, 100, 10)
_ = alphatest(2000, start, end, calc_factor=calc_rsi, args=(6,), top=5,bins=bins )
```

现在，年化 Alpha 进一步提升了 3.2%，达到了 24.1%。6 年的累计收益达到了 3.5 倍。

### 2.4. 通过 grid search 搜索最佳参数

之前的调整主要是基于 Alphalens 的分析过程。但我们也应该利用上述方法来寻找 RSI 的最佳参数。比如，我们可以分别测试 RSI 窗口为 3、7、10 等各期的值，看看哪个参数的因子表现更好。

我们使用 3 到 20 的参数进行测试：

<Example id=grid_search/>

```python
bins = np.linspace(0, 100, 11)

alphas = []
np.random.seed(78)
for win in range(3, 21):
    alpha, _ = alphatest(2000, start, end, calc_factor=calc_rsi, args=(win,), top=5,bins=bins,plot_mode='None' )
    alphas.append((win, alpha.iloc[0,0]))

clear_output()
pd.DataFrame(alphas, columns=['win', 'alpha']).set_index('win').plot(title='Alpha')
```

从结果可以看出，当 win 达到 18 时达到最高（每次运行，universe 可能会有不同，此数字也因此可能有变化），并在这一带基本保持稳定。

## 3. 过拟合检测

通过上面的调参，我们已经得到了非常理想的结果。但是，这些结果能不能靠得住？有没有过拟合的情况发生？这就需要进行过拟合检测。

!!! info
    这里有一个关于过拟合的例子 [^find-alphas]。<br>
    2006 年世界杯足球比赛之前发现了一个明显的 3964 公式。阿根廷曾于 1978 年和 1986 年夺得冠军，总计 3964 年；德国在 1974 年和 1990 年获胜，加起来的数字相同；巴西在 1970 年和 1994 年获胜，并在 1962 年和 2002 年再次获胜。这个公式看起来很漂亮，直到“统计学家”试图用它来预测 2006 年冠军。他们声称世界杯将由 1958 年夺冠的巴西夺得，但最终却颁给了意大利。毫不奇怪，这一“规则”在 2010 年也失败了，当时西班牙成为冠军俱乐部的新成员。<br>这个例子警示我们，由于算力和数据规模的提升，纯粹基于数字发现“规律”的可能性也大大增加，但当我们找出一个“规律”之后，一定要回到它的金融意义上来。

### 3.1. 绘制参数高原

参数高原 [^Chiyuan][^ChiJin](Parameter Plateau）理论来自于机器学习，它是指在参数空间中存在的一片相对平坦的区域。在这片区域内，目标函数（如损失函数)的变化不大。这意味着在高原上的不同参数设置对于模型的表现差异很小。高原的存在，一般意味着模型在参数空间中的表现是稳定的。

我们可以将若干次因子检验的参数与结果绘制成为一个三维图，通过观察找到最稳健的参数组。

在 RSI 的例子中，主要参数只有一个，就是 RSI 窗口。我们在<ref>寻找因子参数</ref>一节中，已经显示了一个二维图。从图中可以看出，参数在 18 处达到最大值，并且前后 2 个点之间，都存在一个相对平坦的区域。因此，我们可以选择 18 作为计算 RSI 的最佳参数。

为了演示 3D 参数高原的例子，我们把 top 参数也纳入进来，演示一下：

<Example id=plateau/>

```python
from itertools import product
import plotly.graph_objects as go

np.random.seed(78)

def calc_rsi(df, n):
    return 100 - ta.RSI(df.close, n)

start = datetime.date(2017, 1, 1)
end = datetime.date(2022, 12, 31)
bins = np.linspace(0, 100, 10)

alphas = []

win = np.arange(6,21)
top = [4, 5,6,7]

for win_, top_ in product(win, top):
    alpha, _ = alphatest(500, start, end, 
                      calc_factor=calc_rsi, args=(win_,), 
                      top=top_, 
                      bins=bins,
                      plot_mode=None)
    alphas.append(alpha.iloc[0,0])

# 创建 3D 表面图
z = np.array(alphas).reshape((len(win), len(top)))

hover_info = 'Top: %{x}<br>Win: %{y}<br>Alpha: %{z}<extra></extra>'
fig = go.Figure(data=[go.Surface(x=top, 
                                y=win, 
                                z=z, 
                                colorscale='Viridis',    
                                hovertemplate=hover_info,
                )])

# 设置图表布局
fig.update_layout(
    title='Parameter Plateau',
    scene=dict(
        xaxis_title='Win',
        yaxis_title='Top',
        zaxis_title='Alpha Value'
    ),
    height=700,
    margin=dict(l=65, r=50, b=65, t=90)
)

# 显示图表
clear_output()
fig.show()
```

这个例子以交互式方式，演示了如何绘制 3D 的参数高原图。你可以点击 Orbital Rotation 按钮之后，对图形进行拖拽旋转，从而更立体地观察它。如果我们有更多的采样点，这个图形会更容易理解。但大致上，它也演示出来参数高原的形态，从这个图来看，最佳参数应该在 win=13, top=4 这一带。

到此为止，我们已经演示了当有一个参数、两个参数时，如何展示参数高原；如果参数大于 2，那么即使是 3D 图，也无法同时绘制出这么多参数与收益之间的关系。此时就只能分多次来绘图了。

### 3.2. 样本外检测

样本外检测是指把整个数据集划分为互不重叠的训练集和测试集，在训练集上训练模型，在测试集上进行验证。如果模型在测试集上也表现良好，就认为该模型没有过拟合 (overfit)。

在我们前面的试验中，我们只使用了 2018 年到 2022 年底的数据。这样就留出来 2023 年的数据可以作为样本外检测。我们之前测试时，得到的最好参数是使用分层 4，RSI 窗口为 13。现在，我们就用这一组参数，但使用样本外数据来运行一次：

<Example id=out-of-sample/>

```python
np.random.seed(78)

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)

win, top = (13, 4)
bins = np.linspace(0, 100, 11)
_ = alphatest(2000, start, end, calc_factor=calc_rsi, args=(win,), top=top, bins=bins)
```

我们得到的年化alpha是39.2%左右。12个月的累积收益也是 40%左右。

如果我们把时间往前拉一点，对从 2005 年到 2023 年期间的数据进行回测，我们会得到年化 18.1%的收益率。累计实现了 17.5 倍收益，不过，仅靠这一个因子，会出现波动率较大的情况。请读者自行测试。在 A 股，还有小市值因子能够达到类似的收益率。

!!! warning
    不要把这里因子检验的收益率当成实际收益率。因子检验过程中没有计算手续费和滑点。如果你管理着一大笔投资，滑点往往是很可观的，甚至完全可能导致收益翻转。此外，在因子检验中，我们计算的都是多空组合，但是到目前为止，融券标的只占 A 股的一半左右，并且对个人和中小机构来说，融券并不容易。融券的成本也需要考虑进来。

## 4. 关于多空组合的一些思考

在这一章中，我们通过参数优化，最终似乎发现了一个有效的策略，它从 2005 年以来到 2024 年 7 月为止，始终保持了很好的 Alpha 与年化收益。但是，我们这里使用是多空组合。要把这个策略付诸实盘，就需要实盘中存在做空的条件。

我们在前面也提及过，在 A 股，对个股进行做空并不容易。个股首先必须要是融券标的，其次，即使是融券标的，你也要考虑能否成功融券以及融券成本如何。

因此，如果我们探索出来的因子要用以 A 股投资，往往就只能考虑单边做多的情况。在函数 alphatest 中，我们传入 long_short = False，就可以测试单边做多的情况：

```python
np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

bins = np.linspace(0, 100, 11)
_ = alphatest(2000, start, end, calc_factor=calc_rsi, args=(6,), top = 5, bins=bins, long_short=False)
```

现在你会看到，年化 Alpha 只有 5.7%。

这个结果给我们几个提示：

* 一是 RSI 策略仍然有效，尽管它的年化 Alpha 只有 5.7%，但一个简单的因子，单边做多能有这样的 Alpha 收益，也完全能够接受。
* 二是多空收益与单多收益之差提示我们，在 A 股，RSI 高位显然是一个有效的见顶指标。我们应该把这一指标加入自己的策略中。

## References

[Finding Alphas: A Quantitative Approach to Building Trading Strategies](http://www.jieyu.ai/assets/ebooks/finding-alphas-a-quantitative-approach-to-building-trading-strategies.pdf)，由 Wolrd Quant 的创始人 Igor Tulchinshky 等人撰写，Wiley 出版。这本书涵盖了寻找 Alpha 的方方面面，包括如何处理数据、评估因子、控制偏差以及各种场景下的因子设计等等。可以作为本教程的补充和拓展材料。

[样本外测试](https://www.buildalpha.com/out-of-sample-testing/) 详细介绍了过拟合、曲线拟合和样本外测试概念，推荐阅读。

## 5. Footnotes

[^Chiyuan]: [理解深度学习需要重新思考泛化](https://arxiv.org/abs/1611.03530)，Chiyuan Zhang 等。

[^ChiJin]: [如何有效逃离鞍点](https://arxiv.org/abs/1703.00887), Chi Jin 等。

[^find-alphas]: 引用来自 [Finding Alphas: A Quantitative Approach to Building Trading Strategies](http://www.jieyu.ai/assets/ebooks/finding-alphas-a-quantitative-approach-to-building-trading-strategies.pdf)

<!--
对这几章进行总结。

最后，我们实现了一个名为 Alphatest 的函数，它对因子检测流程进行了进一步的封装，很适合我们进行参数调优。
-->
