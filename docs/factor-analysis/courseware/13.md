# 机器学习概述
<br>
机器学习是人工智能的一个子集。人工智能是指使计算机系统能够执行通常需要人类智能才能完成的任务的技术和方法。人工智能涵盖了多种技术和子领域，如机器学习、深度学习、自然语言处理、计算机视觉、专家系统等。

人工智能的概念正式提出是在1956年达特茅斯的夏季人工智能研究会[^turing]上。达特茅斯学院(Dartmouth College)虽小，但却是常春藤大学之一。这次会议由约翰·麦卡锡发起，克劳德.香农等人参与了研讨会。约翰·麦卡锡是计算机科学家和认知科学家，也是人工智能学科的创始人之一。他还是著名的编程语言Lisp的发明者，早期许多人工智能系统和算法都是用Lisp语言编写的。

![达特茅斯AI研讨会](https://images.jieyu.ai/images/2024/11/dartmouth-ai-summer-research.jpg?width=50%)

发起会议的约翰·麦卡锡最初认为，只要集中全美最好的几位科学家，大概只要8周就能攻克人工智能问题。不想从1956年发起的宏伟梦想，经过近70年的筚路蓝缕，今天仍然只能算是半道其中。

这一路上，就像是光的微粒说与波动说的争论一样，关于人工智能的发展方向，也一直存在两种主要的思想，即人工智能应该基于规则还是基于数据来构建模型？两种思想之间的争斗跌宕起伏，最终在三位华人科学家的加持下，数据派占据了上风，同时铸就了当代人工智能波澜壮阔的发展。

![Santiago Ramón](https://images.jieyu.ai/images/2024/11/cajal-chick-cerebellum.jpg?width=50%)

人工智能最先从仿生学得到启发。1890年代，Santiago Ramón Cajal(圣地亚哥·拉蒙·卡哈尔)提出了神经元学说，后来被Camillo Golgi（Camillo Golgi）通过高尔基染色法所证实。1943年，Warren MeCulloch(沃伦.麦卡洛克)和Walter Pitts（沃尔特.皮茨）将复杂的神经元电化学过程简化为相对简单的信号交换[^activation]，最终为人工智能仿生打下坚实的基础。


![高尔基染色和海马体](https://images.jieyu.ai/images/2024/11/高尔基染色法和海马体.jpg?width=50%)


1958年，Frank Rosenblatt（弗兰克·罗森布拉特）根据仿生学原理，提出了感知机（Perceptron），这是最早的神经网格模型之一，感知机能够通过学习线性分类器来解决二分类问题。Rosenblatt的感知机是个天才的发明，因为当时的计算技术还没有数字化，Rosenblatt训练感知机的过程，都是靠手动切换开关完成的。尽管很原始，但通过训练，感知机最终获得了形状识别的可靠能力。

![感知机示意图](https://images.jieyu.ai/images/2024/11/perceptron.png?width=50%)

感知机一度被誉为重大的技术进步。但这一发明过于超前，世界还未为它的诞生做好准备 -- 直到50年以后，人们才明白，神经网络需要数字化输入输出设备、大量的算力和存储，而最终还需要海量的数据。毕竟，人脑有超过1000亿个神经元组成，在1958年，人类只能模拟大脑容量的几亿分之一。

因此，感知机带来的热浪仅持续不到一年，就受到猛烈的攻击。其中最大的反对者，正是达特茅斯AI研讨会的发起人之一马文.明斯基，和另一位数学家、计算机科学的先驱 -- 西摩.佩珀特。他们在1969年出版了一本名为《感知机》的书，抨击感知机缺乏严谨的理论基础 -- 实际上直到今天，人工智能仍然不能为自己找到坚实的数学基石，在很多时候，它的行为仍然是一个黑盒子 -- 这一点倒是很像大脑。

感知机是基于数据的机器学习模型。在感知机遭到重创之后，机器学习阵线不得不沉寂十多年之久。而在此期间，知识工程与专家系统则占据了风头。其中最有名的，可能是一个名为内科医生-I的程序，它的数据库中包含了500种疾病描述和3000种疾病表现。但是，由于现实世界太复杂，基于规则的模型能够很好地处理检索，但在推理上就显得呆板而肤浅，并且规则越多，就越难相容，于是很快也败下阵来。

专家系统是当年人类实现人工智能最后的希望。因此，专家系统的落败，也导致人工智能的发展陷入低谷，进入了冰冻的寒武纪。然而，正如地球经历寒武纪一样，在封冻的冰层之下，进化正在加速，一些革命性的突破，即将到来。

![Geoffrey Hinton 2024年诺贝尔物理学奖](https://images.jieyu.ai/images/2024/11/geoffrey-hinton.png?width=50%)

1986年，杰弗里·辛顿[^hinton]（Geoffrey Hinton）、大卫·鲁梅尔哈特（David Rumelhart）和罗恩·威廉姆斯（Ron Williams）发表了反向传播算法。这是深度学习的奠基之作，它使得多层神经网络的训练成为可能，从理论上，我们向模拟有1000亿神经元的大脑迈出了至关重要的一步。但深度学习的时代并没有立刻到来，人工智能还被封印在厚厚的冰雪之下。

又过去了10多年。1998年，Yann LeCun(杨立昆)提出了LeNet，这是最早的卷积神经网络，也是人类历史上第一个有实际用途的神经网络。它被广泛应用于全美的自动提款机上，用来读取支票上的数字。Yann LeCun的成功再次掀起了人工智能浪潮，也最终把机器学习路线重新带回到人们的视野中来。

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/lenet.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>LeNet, by Yann LeCun</span>
</div>

Yann LeCun的成功还无法产生势如破竹、摧枯拉朽般的攻势。相反地，在短暂的热闹之后，人工智能的研究似乎再次进入休眠期。不过，这一次它只是小憩了4年，很快，它将被AlexNet唤醒，随后便如河出伏流，一泄千里。

而在这次复苏的背后，两位华人科学家--黄仁勋和李飞飞则是最重要的幕后英雄。前者以英伟达的显卡和Cuda引擎为深度学习提供了强大的算力，后者则以ImageNet数据集，为卷积神经网络提供了土壤和营养。

!!! info
    <div style='width:15%;float:left;padding: 0.5rem 1rem 0 0;text-align:center'>
    <img src='https://images.jieyu.ai/images/2024/11/the-worlds-i-see.png'>
    </div>
    尽管李飞飞荣获了美国工程院院士、医学科学院院士和艺术与科学学院院士，但世界可能仍然大大低估了她的主要贡献 -- ImageNet的重要性。
    
    如果没有第谷长达20余年的天文观测，就不会有开普勒三大定律，也就不会有牛顿第三定律。牛顿曾说自己是站在了巨人的肩膀上，他所说的巨人当然不是莱布尼茨，而是第谷和开普勒。
    
    李飞飞正是当代的第谷。

AlexNet也是一个卷积神经网络，它由Alex Krizhevsky, Ilya Sutskever和Geoffrey Hinton提出，在2012年的ImageNet竞赛中，取得了85%的识别准确率，比上一届的冠军整整提升了10%！LeNet只能运用在一个很小的场景下，处理很小规模的数据集，而AlexNet则是在超过1000个分类的图片上进行的识别，它的成功显然更具现实意义和突破性。AlexNet让基于数据驱动的机器学习路线加冕成为王者。直到今天，尽管机器学习还存在种种不足，我们离通用人工智能还不知道有多长的距离，但是，似乎再也没有声音质疑机器学习，而要改用基于规则的专家系统了。

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/alexnet.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>AlexNet</span>
</div>

下一个突破则是机器视觉对人类的超越。根据研究，在ImageNet数据集上人类的分类能力极限是5.1%的错误率。一旦机器视觉的错误率低于这个指标，也就战胜了人类，意味着人工智能在视觉上的应用完全成熟。

这个决定性的胜利是由华人科学家何恺明在2016年取得的。他通过深度残差网络，将神经网络的层数增加到了惊人的（与当时的水平相比）152层，但通过巧妙的设计，允许输入数据在训练阶段绕过其中的某些层，从而部分解决了深层网络中的梯度消失问题。

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/resnet-18.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>ResNet-18</span>
</div>

最终，resnet在识别错误率降到了4.5%，显著地超越了人类的极限！再也没有任何理由怀疑和拒绝人工智能的应用了！

到此为止，机器学习路线取得了压倒性的胜利，人工智能研究就进入加速时代。在短短的几年后，甚至自然语言理解也被突破，以transformer为代表的架构在自然语言理解、文生图、文生视频、编程等多个领域都取得了成功。

行百里者半九十。人类的终极愿景 -- 通用人工智能（AGI）何时能产生还未可知。人工智能在未来应该成为人类的伴侣，但此刻，人类的亚当还没能造出自己的夏娃。此外，尽管当代人工智能模型已经极为强大，但它内部的运行机制仍然没有坚实的理论基础[^hrx]，AI引起的伦理问题才刚刚暴露，这些都是未来有待我们攻克的一道道关卡。

## 1. 机器学习的本质

在前面的概述中，我们介绍了人工智能的发展，其中提到基于规则和基于数据的机器学习是两种最根本的思路。这究竟意味着什么呢？

假设我们要完成一个最简单的人工智能，根据天气判断是否适合出门。基于规则的人工智能很简单，我们需要把每一种条件和相应的动作进行编程：


```mermaid
flowchart LR
A{天气} --> B(晴天) --> C{温度}
C -- 高温 --> E(不出门)
C -- 正常 --> G(出门)
A -- 阴天 --> I(出门)
A -- 雨天 --> K(不出门)

style E fill:#f96,stroke:#333,stroke-width:4px,shape:circle
style G fill:#f96,stroke:#333,stroke-width:4px,shape:circle
style I fill:#f96,stroke:#333,stroke-width:4px,shape:circle
style K fill:#f96,stroke:#333,stroke-width:4px,shape:circle
```

本质上，它们就是一堆if-else。如果我们把上面的图实现出来，那么，这样的人工智能就是基于规则的。

基于数据的机器学习最终也要生成类似的规则，只不过，所有的规则不是通过编程得到，而是通过数据训练，最终模型找到了一个能正确预测结果的函数。下面，我们就来训练一个能达到同样目的的决策树模型。

<Example id="tree"/>

```python
import numpy as np
import pandas as pd
from sklearn.tree import DecisionTreeClassifier, plot_tree
import matplotlib.pyplot as plt

# 创建示例数据
data = {
    '天气': ['晴', '晴', '晴', '晴', '阴', '阴', '雨', '雨'],
    '气温': ['高温', '高温', '舒适', '凉爽', '凉爽', '凉爽', '凉爽', '凉爽'],
    '宜出行': [0, 0, 1, 1, 1, 1, 0, 0]
}

df = pd.DataFrame(data)

# 将分类变量转换为数值
df['天气'] = df['天气'].map({'晴': 0, '阴': 1, '雨': 2})
df['气温'] = df['气温'].map({'高温': 0, '舒适': 1, '凉爽': 2})

X = df[['天气', '气温']]
y = df['宜出行']

# 创建并训练决策树模型
clf = DecisionTreeClassifier()
clf.fit(X, y)

# 可视化决策树
plt.figure(figsize=(8,12))
plot_tree(clf, filled=True, feature_names=['天气', '气温'], class_names=['诸事不宜', '宜出行'])
plt.title('今日宜出行')
plt.show()

# 预测是否出门
weather = "晴"
temp = "高温"

sample = pd.DataFrame([(weather, temp)], columns=["天气", "气温"])
sample['天气'] = sample['天气'].map({'晴': 0, '阴': 1, '雨': 2})
sample['气温'] = sample['气温'].map({'高温': 0, '舒适': 1, '凉爽': 2})

prediction = clf.predict(sample)
if prediction[0] == 1:
    print(weather, temp, "宜出行")
else:
    print(weather, temp, "诸事不宜")
```

第30行显示了我们训练出来的决策树模型的样子。尽管我们现在还不能完全理解这个模型，但有一点很清晰，我们训练出来的模型，确实具备了执行if-else的判断所需要的逻辑。

接下来的第32行到43行，我们构造了一个输入事件，然后通过`clf.predict`方法，对输入的新事件进行了预测。我们的输入是『晴』和『高温』，模型告诉我们『诸事不宜』。

这是一个非常简单的例子，但重要的是，我们没有编写任何逻辑，只是告诉它一些过去的经验（输入数据），它就能够根据这些经验做出正确的决定。更奇妙的是，如果我们不是在写一个预测明天出行的机器人，而是要实现一个预测明天股价涨跌的机器人，我们同样不需要写任何逻辑，只需要告诉这个模型，什么样的前提条件出现，股价就会涨，什么样的前提条件出现，股价就会跌。然后机器人在遇到类似的情况时，就能做出正确的决定。

如果存在这样一个模型的话，那么，任何一个交易员，不需要懂得高深的计算机知识，只要非常擅长交易和总结，就能把自己的交易经验转换成一个交易机器人，并且，随着交易经验的增长，我们还可以重新训练机器人，而并不需要修改模型本身，就可以让机器人更加智能。这就是机器学习的魅力所在。

而且，在基于规则的算法中，规则越多，规则之间的调和就越是困难。但对于机器学习来说，一般情况下，数据却是越多越好！


## 2. 机器学习分类和模型选择

在本课程中，我们将只会接触到机器学习中的一小部分。因此，有必要在这里将机器学习的全部图景作一个概略的介绍。

!!! tip
    在任何时候，完全精准的分类都是不可能的，我们在对因子和策略进行分类时强调了这一点，对机器学习进行分类我们同样要强调这一点。事实上，任何一个对象，都可能同时具有多个类别，甚至这些类别还是有层级关系的。如果我们无法准确地定义一个对象，至少说明这个对象正处在生机勃勃的幼年期，而不是垂垂老矣。

### 2.1. 机器学习、深度学习、强化学习

这是我们常常遇到的三个概念。机器学习既是其它两个概念的父级概念，是所有使计算机能够在不进行明确编程的情况下从数据中学习的方法的总称，同时，我们也把这类方法中，不使用深层神经网络的算法和模型称为机器学习。

这些机器学习算法主要有线性回归、逻辑回归、决策树、随机森林、支持向量机 (SVM)、K近邻 (KNN) 等。我们可以简单地认为，包含在著名的机器学习库-scikit-learn 中的算法和模型都是狭义上的机器学习。

与狭义的机器学习不同，深度学习主要通过多层神经网络（通常称为深度神经网络）来学习数据的复杂表示。常见的算法和模型包括卷积神经网络 (CNN)、循环神经网络 (RNN)、长短时记忆网络 (LSTM)、生成对抗网络 (GAN) 等。它的特征是能够学习到数据中最复杂的特征，同时也需要大量的数据和更长时间的训练。

强化学习是一种通过与环境交互来学习最优行为策略的方法。它通过试错的方式，使代理（agent）在环境中采取行动以最大化累积奖励。常见的算法和模型有Q-learning、深度Q网络 (DQN)、策略梯度方法 (Policy Gradients)、演员-评论家方法 (Actor-Critic) 等。

强化学习与深度学习最主要的区别在于数据的来源。在传统深度学习中，数据通常是预先收集好的，模型通过这些固定的数据集进行训练。数据集可以是静态的，也可以是动态生成的，但生成过程与模型的训练过程是分离的。而在强化学习中，数据是通过与环境的交互逐步生成的。每次交互产生的状态、动作和奖励构成了训练数据的一部分。这些数据是动态生成的，模型在与环境的交互过程中不断学习和优化。

以交易员的视角来看，可能深度学习适合训练选股模型，而强化学习则更适合训练交易机器人，它能在选中的股票池中，根据市场的变化，决定何时加仓、减仓和平仓。

在我们要用人工智能解决某个具体问题时，往往首先要选择是使用机器学习，还是深度学习或者强化学习。首先，如果问题的规模较小，也难以获得大量的数据，那么我们会选择机器学习。如果问题的规模比较复杂，又能获得大量的训练数据，我们就会根据数据是预先收集好的，还是动态生成的来选择深度学习还是强化学习。

<!--https://www.showmeai.tech/article-detail/185-->
### 2.2. 监督、无监督和强化学习

根据学习范式和要得到的最终结果的不同，学习算法分为监督学习、无监督学习、强化学习等。

监督学习中，训练集有标签。学习的目标就是要通过训练，使得模型在测试集上预测的结果与真实结果（标签）尽可能一致。它用来帮助我们解决目标已经明确的问题。

在无监督学习中，训练集没有标签。换句话说，我们对数据集能表现出什么样的规律和特征，只有非常模糊的猜想。需要依靠模型自己去发现数据中的内在结构。这类算法主要用在数据探索上，具体任务是聚类、降维和密度估计。典型算法包括K-means、层次聚类、主成分分析（PCA）、自编码器、生成对抗网络（GAN）等。

在强化学习中，数据没有明确的标签，但有一个奖励信号来指导学习过程，智能体（agent）通过与环境（environment）的交互，学习如何采取行动以最大化累积奖励。

显然，如果我们对数据能提供的结论一无所知，还处在探索阶段，显然我们要通过无监督学习去进行数据探索；如果数据能打出明确的标签，则适合使用监督学习。

在量化交易中，看上去我们很容易给数据打上标签，比如，可以用次日的涨跌幅来作为标签，所以这更应该是一个监督学习问题。但实际上，由于金融数据具有高噪声的特点，数据标注比我们想像得要难得多。所以，在时间维度上预测股价涨跌的任务，也许更适合强化学习。

### 2.3. 回归与分类

回归与分类是机器学习模型按其完成任务进行的分类。任何一个有用的机器学习模型，归根结底都是对现实世界的一个建模。如果我们对世界刨根问底，追踪溯源，这世界究竟应该是什么样子的呢？

这是一个哲学问题。哲学里有几个基本问题，一是世界是物质第一性，还是意识第一性的问题；另外一个问题，就是自然是连续的，还是离散的问题。这刚好就对应了机器学习的两大任务模型。显然，这一点也不奇怪，毕竟，我们是要针对现实世界建立少数几个通用的模型，然后通过喂给它不同的数据，就能解决不同领域的问题，因此，这些模型也自然而然地，直到最底层的哲学层面才会出现真正的差别。

如果哲学看起来太深奥，那我们先看看机器学习中的回归和分类的具体含义究竟是什么。

机器学习模型的最终用途是要进行某种预测。如果预测值的分布在实数域且是连续的，那么这就是一个回归任务。我们判断回归任务完成得好坏，是通过求预测值到真实值之间的某种距离。

如果预测值的分布是少数有限的、离散的实数，那么，这就是一个分类任务，这些离散的实数代表了某种类别，比如对与错、东西南北、物体的分类等等。我们判断分类任务完成得好坏，是通过计算预测值和真实值之间的信息熵（更具体地说，是通过准确率、召回率等）。

从机器学习要完成的任务来看，所有的机器学习模型都被归类为回归或分类任务。以量化交易而言，当你在机器学习、深度学习和强化学习中选定了大框架，并且根据数据的特点确定了学习范式之后，最重要、也最常面临的问题，就是要确实究竟使用回归模型还是分类模型，因为几乎所有的算法（模型）都同时提供这两种任务模型。

在[](#example-12-1)中，我们使用的是决策树模型中的分类任务模型。我们也可以使用决策树模型中的回归任务模型，比如，通过日历和天气是否晴朗来预测次日的最高温。最高温是一个浮点数，理论上它有无数多个取值，因此，这不是一个分类任务。

正确确定任务模型至关重要。因为机器学习模型都会利用**损失函数**来进行优化，而分类任务与回归任务的损失函数通常是不一样的。你不能对一个分类任务算距离，也不应该对一个回归任务算信息熵。



## 3. 机器学习基本流程
<!-- https://scikit-learn.org/1.4/tutorial/basic/tutorial.html -->

机器学习算法的流程分为四个步骤：

### 3.1. 定义问题

首先，我们要清楚地定义我们要解决的问题，确定问题是分类还是回归，了解可获得的训练数据集的规模和特征，然后通过下面的模型选择器，来决定将要使用什么样的算法，达到什么样的目标。

[](#如何选择正确的模型)给出了一个选择模型流程图[^sklearn-estimator]。

![如何选择正确的模型](https://images.jieyu.ai/images/2024/11/how-to-choose-estimator.jpg?width=75%)

### 3.2. 准备数据

在这一步，我们要准备训练数据集。从现实世界采集过来的数据往往不能直接用以训练，我们需要把它转换成符合sklearn要求的格式和精度，进行各种标准化（根据算法要求）预处理，划分训练集、验证集和测试集。

### 3.3. 训练模型

在训练数据准备好之后，我们就可以开始模型训练。在训练过程中，我们要观察损失函数的变化，决定何时停止训练。并且可能要通过多组超参数进行搜索，以找到最优的超参数组合。在训练完成后，我们还要保存训练的结果，以备今后使用。

在训练过程中，我们可能常常会用到各种可视化方案来观察模型的学习情况。

### 3.4. 模型评估

模型训练是使用训练数据和验证数据。模型评估则是在事前划出的测试集上，进行得分测试。由于测试数据完全没有参与过训练过程，因此，在测试集上进行评估，可以防止过拟合发生。

### 3.5. 新样本预测

模型通过评估以后，就可以在生产环境下，用作预测。

## 4. 机器学习应用场景

图像识别的应用在十年前就已经成熟了。这些应用包括人脸识别（身份验证、支付等）和车牌识别等，近年来，目标分割在自动驾驶中也起到重要作用。通过Mask R-CNN或者U-Net等神经网络，自动驾驶的汽车能自动识别并跟踪路上运行的其它汽车、行人以及道路分割线。在农业上，利用机械手加图像识别来采摘成熟的蔬果已经有了成熟的应用。

在医疗上，图像识别已经用来查看X光片，检测黑色素瘤等皮肤病变，通过显微镜图像分析细胞和组织的结构和特征等等。2024年5月8日，Google DeepMind发布了AlphaFold[^alphfold] 3。这是一个重要的模型里程碑，能以前所未有的精度预测所有生命分子的结构和相互作用，为科学家们提供了一个强大的工具，以更精确地理解疾病机制并开发新的治疗药物。借助Alpha Fold 3, 过去10个博士生5年才能解决的一个大复合物结构问题，现在只需要一到两周就能完全。科学研究的迭代周期大大缩短。Alpha Fold 3基于的是 Transformer+Diffusion架构。

语音识别用来将语音转换成为文本。这可以用以视频打字幕、生成会议记录等等应用上。Open公司开源的Whisper模型就是一个非常出色的语音识别模型，它可以实现语音与文本之间的互转，多角色配音等。

生成式AI使得信息得以在不同模态间自由转换，比如从文本到图像、从蛋白质到文本、从文本到蛋白质，乃至从文本到化学品等。Transformer是生成式AI中最重要的一种架构。chatGPT正是基于这一架构。

目前，基于生成式AI，人工智能编程助手已经相当好用，它在解决简单和具体的问题上，已显著超过普通程序员。人们正在尝试通过文本生成短视频的领域，并且要求生成的视频符合物理原理。

在量化交易中，机器学习（广义）的运用越来越广泛。比如，通过NLP分析上市公司的财报和相关新闻，将非结构化数据提取为结构化数据，舆情分析等等。通过机器学习来判断证券价格走势等等。当然，在量化交易中，AI的运用可能永远只是辅助，基于这样两个根本原理：

1. 机器没有权力成为财富的所有人。这可能不仅仅是一种伦理和法律要求，也是宇宙的法则。
2. 量子效应。如果你有足够多的资本投入来研发交易模型，那么，这个模型一旦成功，你也肯定（也必须）有足够多的资金投入交易。而过多的资金影响价格，从而会破坏你的进场条件和退场条件。

所以，对个人和中小型私募而言，在量化赛道深度机器学习是非常值得的，它可以帮助你迅速成长起来。由于前两个原理，这条赛道永远不会有大鲨鱼进来，当然也培育不出来蓝鲸。

## 5. 拓展阅读

最早的人工智能程序可能是Eliza[^eliza]，第一个聊天机器人。它是由MIT的Joseph Weizebaum教授开发的，使用的正是Lisp语言。下面是它与一个年青女病人之间的对话（程序的回应使用大写）:

```text
Men are all alike.
IN WHAT WAY
They’re always bugging us about something or other.
CAN YOU THINK OF A SPECIFIC EXAMPLE
Well, my boyfriend made me come here.
YOU BOYFRIEND MADE YOU COME HERE
He says I’m depressed much of the time.
I AM SORRY TO HEAR YOU ARE DEPRESSED
It’s true. I am unhappy.
DO YOU THINK COMING HERE WILL HELP YOU NOT TO BE UNHAPPY
I need some help, that much seems certain.
WHAT WOULD IT MEAN TO YOU IF YOU GOT SOME HELP
Perhaps I could learn to get along with my mother.
TELL ME MORE ABOUT YOUR FAMILY
My mother takes care of me.
WHO ELSE IN YOU FAMILY TAKES CARE OF YOU
My Father.
YOUR FATHER
You are like my father in some ways.
WHAT RESEMBLANCE DO YOU SEE
You are not very aggressive but I think you don’t want me to notice that.
WHAT MAKES YOU THINK I AM NOT VERY AGGRESSIVE
You don’t argue with me.
WHY DO YOU THINK I DON’T ARGUE WITH YOU
You are afraid of me.
DOES IT PLEASE YOU TO BELIEVE I AM AFRAID OF YOU
My father is afraid of everybody.
WHAT ELSE COMES TO MIND WHEN YOU THINK OF YOUR FATHER
Bullies.
DOES THAT HAVE ANYTHING TO DO WITH THE FACT THAT YOUR BOYFRIEND MADE YOU COME HERE
```

以当时的算力和数据，Joseph Weizebaum的这个脚本绝对可以称得上是天才之作。当然，它仍然是计算机与神经病之间的对话，没有任何可以当真的东西。


而早期最有影响的人工智能程序，可能是SHRDLU[^feifeili]。该程序 是 1968 年至 1970 年间由特里·温格多在麻省理工学院开发的早期自然语言理解计算机程序。在该程序中，用户与计算机进行对话，移动物体，命名集合并查询简化“积木世界”的状态，基本上是一个装有不同块的虚拟盒子。

这是它能生成的一段对话示例，其中反映了一定的逻辑推理能力：

```txt
人：拿起一个大的红色的方块。
计算机: 好的。
人：握住金字塔。
计算机：我说的是哪一个金字塔？
人(改变主意)：找到一个比你手中持有的更高的方块，并将其放入盒子中。
计算机: 好的。
人：盒子里面装的是什么？
计算机：蓝色金字塔和蓝色方块。
人：金字塔是被什么支撑的？
计算机: 盒子
人：有多少个方块不在盒子里？
计算机：四个。
人：他们中至少有一个比我让你挑选的那个窄吗？
计算机: 是的，红色的立方体。
人: 它被支撑吗？
计算机: 是的，在桌子上。
人：桌子能捡起积木吗？
计算机: 不能。
人：金字塔能被一块石头支撑吗？
计算机: 是的。
人：一个金字塔能支撑另一个金字塔吗？
计算机：我不知道。
人：堆叠两个金字塔。
计算机：我不能。
```

这段对话很长，你可以在[这里](https://en.wikipedia.org/wiki/SHRDLU)阅读它。这段对话反映了SHRDLU具有一定的逻辑推理、算术能力和空间推理能力。

然而，SHRDLU看似精巧，实则笨拙，它不能理解自然语言，只能理解特定格式的指令，也只能回答关于金字塔和方块的问题，并且一旦输入有一点偏离，它就可能给出十分荒谬的回答。


## 6. Footnotes

[^turing]: 关于人工智能的缘起和发展，可以参考图灵出版社出版的《人工智能简史》第2版，以及李飞飞的自传《我看见的世界》。
[^eliza]: Eliza来自萧伯纳的戏剧《卖花女》中的一个下层女子，这名女子的职业是在街头卖花，但她憧憬着学会上流社会说话的腔调和词汇，以便将来可以开一个花店。这是一个绝好的比喻，因为计算机程序本应该使用刻板的程序设计语言，而现在它要学着说人类的语言。更多内容，请阅读[遐思录](https://zhuanlan.zhihu.com/p/31122403)。

[^feifeili]: 在李飞飞的自传《我看见的世界》中，提起了这个程序。《我看见的世界》是一本非常值得一读的好书，它既是李飞飞的个人史，也是一部波澜壮阔的人工智能发展史。该书文采斐然，也不乏哲理和洞见。这里随手摘录一段：想象一下这样的情景：这个世界上不存在任何感觉，甚至都不能用“黑暗”一词来描述，因为与之对应的“光明”概念尚未被构想出来。在这个世界里，什么都看不到、听不到、感觉不到，而所谓的“活着”不过就是新陈代谢的过程。

[^activation]: 作者相信，正是计算机科学家不断深入和对齐神经科学，人工智能中广泛使用的激活函数才从数学上完美的sigmoid函数，切换到数学上不完美，但更贴近神经元信号交换本质的ReLU函数。

[^hinton]: 辛顿因为其对人工智能的重大贡献，获得了2024年的诺贝尔物理学奖。他与霍普菲尔德网络的发现者John Hopfield共享了这一殊荣。John Hopfield是波兰裔美国光谱学家，他在1982年发表的对关联神经网络的研究，重新点燃了AI冬天里的一把火。

[^sklearn-estimator]: 截图来自[sklearn](https://scikit-learn.org/stable/machine_learning_map.html)
[^hrx]: 黄仁勋在2024年11月23日，接受香港科技大学的博士学位授予仪式上，谈到：AI目前尚未能从第一性原理中直接得出答案，它是通过观察数据来学习和得出结论的。因此，它并非模拟第一性原理的求解器，而是在模仿智能、模仿物理。
[^alphafold]: 2023年的拉斯克基础医学研究奖授予了 Demis Hassabis和John Jumper，以表彰他们发明了人工智能系统——AlphaFold，该系统解决了从氨基酸一维序列预测蛋白质三维结构这一长期存在的挑战。拉斯克基础医学研究奖被认为是诺贝尔奖的风向标。不过，2024年的诺贝尔医学奖还是颁给了维克多·安布罗（Victor Ambros）和加里·鲁夫昆（Gary Ruvkun），以表彰他们在microRNA及其在转录后基因调控中的作用的发现。据信这是一种能治疗癌症的技术。
