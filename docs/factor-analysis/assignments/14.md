# 第 14 课习题

在课程中，我们介绍了机器学习中偏差、方差、残差和误差等核心概念。这节课的练习中，我们就尝试一下使用这些概念来对股票进行分析。

这个练习共分这样几步：

1. 数据加载与预处理
2. 模型训练与偏差、方差分析
3. 残差分析
4. 模型复杂度与偏差—方差权衡
5. 使用不同模型进行比较
6. 过拟合与正则化惩罚

## 1. 习题 1：数据加载与预处理
使用 akshare 库获取沪深 300 指数的历史数据，对数据进行预处理，包括处理缺失值和提取特征。

```python
import akshare as ak
import pandas as pd

# 获取沪深 300 指数历史数据
hs300_df = ak.stock_zh_index_daily(symbol="sh000300")

# 处理缺失值
hs300_df = hs300_df.dropna()

# 将日期列转换为日期类型
hs300_df['date'] = pd.to_datetime(hs300_df['date'])

# 提取特征，这里以收盘价为例
# BEGIN SOLUTION
features = hs300_df[['close']]
# END SOLUTION

# 创建目标变量，例如预测下一天的收盘价
hs300_df['Next_Close'] = hs300_df['close'].shift(-1)
target = hs300_df['Next_Close'][:-1]
features = features[:-1]

print("特征数据形状:", features.shape)
print("目标数据形状:", target.shape)
```

## 2. 习题 2：模型训练与偏差、方差分析
使用简单的线性回归模型对上述数据进行训练，并分析模型的偏差和方差。

```python
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
import numpy as np

# 划分训练集和测试集
# BEGIN SOLUTION
X_train, X_test, y_train, y_test = train_test_split(features, target, test_size=0.2, random_state=42)
# END SOLUTION

# 训练线性回归模型
# BEGIN SOLUTION
model = LinearRegression()
model.fit(X_train, y_train)
# END SOLUTION

# 在训练集和测试集上进行预测
# BEGIN SOLUTION
y_train_pred = model.predict(X_train)
y_test_pred = model.predict(X_test)
# END SOLUTION

# 计算训练集和测试集的均方误差
train_mse = mean_squared_error(y_train, y_train_pred)
test_mse = mean_squared_error(y_test, y_test_pred)

# 计算偏差和方差（简单近似）
bias = np.mean((y_test - np.mean(y_test_pred))**2)
variance = np.var(y_test_pred)

print("训练集均方误差:", train_mse)
print("测试集均方误差:", test_mse)
print("偏差:", bias)
print("方差:", variance)
```

## 3. 习题 3：残差分析
计算线性回归模型在训练集上的残差，并绘制残差图。

```python
import matplotlib.pyplot as plt

# 计算训练集上的残差
# BEGIN SOLUTION
residuals = y_train - y_train_pred
# END SOLUTION

# 绘制残差图
# BEGIN SOLUTION
plt.scatter(y_train_pred, residuals)
plt.axhline(y=0, color='r', linestyle='--')
plt.xlabel('预测值')
plt.ylabel('残差')
plt.title('训练集残差图')
plt.show()
# END SOLUTION
```

## 4. 习题 4：模型复杂度与偏差 - 方差权衡
使用多项式回归，尝试不同的多项式次数，观察偏差和方差的变化。

```python
from sklearn.preprocessing import PolynomialFeatures

degrees = [1, 2, 3, 4, 5]
train_mses = []
test_mses = []
biases = []
variances = []

for degree in degrees:
    # 创建多项式特征
    poly_features = PolynomialFeatures(degree=degree)
    X_train_poly = poly_features.fit_transform(X_train)
    X_test_poly = poly_features.transform(X_test)
    
    # 训练多项式回归模型
    # BEGIN SOLUTION
    model = LinearRegression()
    model.fit(X_train_poly, y_train)
    # END SOLUTION
    
    # 在训练集和测试集上进行预测
    y_train_pred = model.predict(X_train_poly)
    y_test_pred = model.predict(X_test_poly)
    
    # 计算训练集和测试集的均方误差
    # BEGIN SOLUTION
    train_mse = mean_squared_error(y_train, y_train_pred)
    test_mse = mean_squared_error(y_test, y_test_pred)
    # END SOLUTION
    
    # 计算偏差和方差（简单近似）
    # BEGIN SOLUTION
    bias = np.mean((y_test - np.mean(y_test_pred))**2)
    variance = np.var(y_test_pred)
    # END SOLUTION
    
    train_mses.append(train_mse)
    test_mses.append(test_mse)
    biases.append(bias)
    variances.append(variance)

# 绘制偏差和方差随多项式次数的变化图
# BEGIN SOLUTION
plt.figure(figsize=(12, 6))
plt.subplot(1, 2, 1)
plt.plot(degrees, biases, label='偏差')
plt.plot(degrees, variances, label='方差')
plt.xlabel('多项式次数')
plt.ylabel('偏差/方差')
plt.title('偏差和方差随多项式次数的变化')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(degrees, train_mses, label='训练集均方误差')
plt.plot(degrees, test_mses, label='测试集均方误差')
plt.xlabel('多项式次数')
plt.ylabel('均方误差')
plt.title('训练集和测试集均方误差随多项式次数的变化')
plt.legend()

plt.tight_layout()
plt.show()
# END SOLUTION
```

## 5. 习题 5：使用不同模型进行比较
尝试使用决策树回归模型对金融数据进行训练，并与线性回归模型进行比较，分析偏差和方差。

```python
from sklearn.tree import DecisionTreeRegressor

# 训练决策树回归模型
# BEGIN SOLUTION
dt_model = DecisionTreeRegressor(random_state=42)
dt_model.fit(X_train, y_train)
# END SOLUTION

# 在训练集和测试集上进行预测
y_train_pred_dt = dt_model.predict(X_train)
y_test_pred_dt = dt_model.predict(X_test)

# 计算训练集和测试集的均方误差
train_mse_dt = mean_squared_error(y_train, y_train_pred_dt)
test_mse_dt = mean_squared_error(y_test, y_test_pred_dt)

# 计算偏差和方差（简单近似）
bias_dt = np.mean((y_test - np.mean(y_test_pred_dt))**2)
variance_dt = np.var(y_test_pred_dt)

print("决策树模型训练集均方误差:", train_mse_dt)
print("决策树模型测试集均方误差:", test_mse_dt)
print("决策树模型偏差:", bias_dt)
print("决策树模型方差:", variance_dt)

# 与线性回归模型进行比较
print("\n线性回归模型训练集均方误差:", train_mse)
print("线性回归模型测试集均方误差:", test_mse)
print("线性回归模型偏差:", bias)
print("线性回归模型方差:", variance)
```
