# 第 15 课习题

在课程中，我们介绍了机器学习中偏差、方差、残差和误差等核心概念。这节课的练习中，我们就尝试一下使用这些概念来对股票进行分析。

1. **Sklearn 概述**：Sklearn 是全面的机器学习库，包含监督与无监督学习算法，提供数据预处理、模型评估和选择等工具，具有简洁 API、丰富文档和示例，适合初学者。
2. **数据集**
    - **内置数据集**：包括 Iris、Digits、加州房价数据集和 MNIST 数据集，用于教学和测试。
    - **人工合成数据集**：通过以make_为前缀的函数生成，如make_classification。
3. **数据预处理**
    - **标准化**：使用StandardScaler，使数据符合标准正态分布，提升模型表现，但对非正态分布数据效果不佳。
    - **缩放**：介绍了 MinMaxScaler、MaxAbsScaler、RobustScaler、PowerTransformer 和 QuantileTransformer 等缩放器，各有特点和适用场景。
    - **逆变换**：接触过的变换器支持逆变换，对部分模型正确输出结果很重要。

通过前面的学习，我们已经深入了解了 Sklearn 通用工具包的强大功能，包括其丰富的数据集和多样的数据预处理方法。这些知识为我们进行实际的机器学习项目奠定了坚实的基础。接下来，让我们将目光投向金融领域，运用 Akshare 获取真实的金融数据，并结合 Sklearn 的各种工具和算法，完成一系列具有挑战性的课后习题，进一步巩固所学知识，提升我们解决实际问题的能力。

## 1. 习题 1：数据加载与探索

**问题描述**

使用 Akshare 获取某只股票（代码为000651）的历史行情数据，然后使用 Sklearn 的内置数据集加载方式的思路，将其整理成特征矩阵 X 和目标向量 y。目标向量 y 为下一个交易日的收盘价是否上涨（上涨为 1，否则为 0）。

请补全下面的代码：

```python
import akshare as ak
import numpy as np
from sklearn.model_selection import train_test_split

# 获取股票历史行情数据
stock_df = ak.stock_zh_a_hist(symbol="000651", period="daily", start_date="20230101", end_date="20231231", adjust="")

# 提取特征和目标
features = stock_df[['开盘', '最高', '最低', '成交量']].values
target = np.where(stock_df['收盘'].shift(-1) > stock_df['收盘'], 1, 0)[:-1]
features = features[:-1]

# 划分训练集和测试集
# BEGIN SOLUTION
X_train, X_test, y_train, y_test = train_test_split(features, target, test_size=0.2, random_state=42)
# END SOLUTION

print("训练集特征形状:", X_train.shape)
print("训练集目标形状:", y_train.shape)
print("测试集特征形状:", X_test.shape)
print("测试集目标形状:", y_test.shape)
```

## 2. 习题 2：数据预处理
**问题描述**

对习题 1 中获取的股票数据进行标准化处理，并比较标准化前后使用逻辑回归模型的性能。

请补全下面的代码：

```python
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score

# 标准化处理
scaler = StandardScaler()
# BEGIN SOLUTION
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)
# END SOLUTION

# 训练未标准化数据的逻辑回归模型
lr_unscaled = LogisticRegression()
lr_unscaled.fit(X_train, y_train)
y_pred_unscaled = lr_unscaled.predict(X_test)
accuracy_unscaled = accuracy_score(y_test, y_pred_unscaled)

# 训练标准化数据的逻辑回归模型
lr_scaled = LogisticRegression()
lr_scaled.fit(X_train_scaled, y_train)
y_pred_scaled = lr_scaled.predict(X_test_scaled)
accuracy_scaled = accuracy_score(y_test, y_pred_scaled)

print("未标准化数据的准确率:", accuracy_unscaled)
print("标准化数据的准确率:", accuracy_scaled)
```

## 3. 习题 3：模型选择与调优
**问题描述**

使用网格搜索来调优支持向量机（SVM）模型的参数，以提高对股票涨跌预测的准确率。

```python
from sklearn.svm import SVC
from sklearn.model_selection import GridSearchCV

# 定义参数网格
param_grid = {
    'C': [0.1, 1, 10],
    'kernel': ['linear', 'rbf']
}

# 创建 SVM 模型
# BEGIN SOLUTION
svm_model = SVC()
# END SOLUTION

# 创建网格搜索对象，进行网格搜索
# BEGIN SOLUTION
grid_search = GridSearchCV(svm_model, param_grid, cv=5)
grid_search.fit(X_train_scaled, y_train)
# END SOLUTION

# 输出最佳参数和最佳得分
print("最佳参数:", grid_search.best_params_)
print("最佳得分:", grid_search.best_score_)

# 使用最佳模型进行预测
# BEGIN SOLUTION
best_model = grid_search.best_estimator_
# END SOLUTION

y_pred_best = best_model.predict(X_test_scaled)
accuracy_best = accuracy_score(y_test, y_pred_best)
print("最佳模型的准确率:", accuracy_best)
```

## 4. 习题 4：特征选择
**问题描述**

使用 Sklearn 的特征选择方法（如 SelectKBest）选择最重要的特征，并比较选择前后模型的性能。

请补全下面的代码：

```python
from sklearn.feature_selection import SelectKBest
from sklearn.feature_selection import f_classif

# 选择最重要的 2 个特征
# BEGIN SOLUTION
selector = SelectKBest(score_func=f_classif, k=2)
# END SOLUTION

X_train_selected = selector.fit_transform(X_train_scaled, y_train)
X_test_selected = selector.transform(X_test_scaled)

# 训练选择特征后的逻辑回归模型
# BEGIN SOLUTION
lr_selected = LogisticRegression()
lr_selected.fit(X_train_selected, y_train)
# END SOLUTION

y_pred_selected = lr_selected.predict(X_test_selected)
accuracy_selected = accuracy_score(y_test, y_pred_selected)

print("选择特征前的准确率:", accuracy_scaled)
print("选择特征后的准确率:", accuracy_selected)
```

## 5. 习题 5：回归问题
**问题描述**

使用 Akshare 获取某只股票的历史行情数据，预测下一个交易日的收盘价（回归问题）。使用线性回归模型，并评估模型的性能。

请补全下面的代码：

```python
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error

# 获取股票历史行情数据
stock_df = ak.stock_zh_a_hist(symbol="600519", period="daily", start_date="20200101", end_date="20230101")

# 提取特征和目标
features = stock_df[['开盘', '最高', '最低', '成交量']].values[:-1]
target = stock_df['收盘'].values[1:]

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(features, target, test_size=0.2, random_state=42)

# 对 X_train 和 X_test 进行标准化处理
# BEGIN SOLUTION
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)
# END SOLUTION

# 训练线性回归模型
lr_regression = LinearRegression()
lr_regression.fit(X_train_scaled, y_train)

# 进行预测
y_pred = lr_regression.predict(X_test_scaled)

# 评估模型性能
# BEGIN SOLUTION
mse = mean_squared_error(y_test, y_pred)
# END SOLUTION
print("均方误差:", mse)
```