# 第 8 讲：Alpha 因子实现和因子检验

Alpha042因子的定义如下：

```
(rank((vwap - close)) / rank((vwap + close)))
```

请你在课程环境下，完成该因子的实现和因子检验。

```python
# MANUAL ANSWER points=7

np.random.seed(78)
def rank(df):
# 要求，实现行内排序，返回值为百分比
# BEGIN SOLUTION
    return df.rank(axis = 1, method="min", pct=True)
# END SOLUTION

def alpha042(vwap, close):
# 要求, 实现alph042因子
# BEGIN SOLUTION
    return rank(vwap - close) / rank(vwap + close)
# END SOLUTION

start = datetime.date(2022, 1, 1)
end = datetime.date(2022, 12, 31)

bars = load_bars(start, end, 500)
close = bars.close.unstack("asset")
returns = close.pct_change()

factor = alpha042(close, returns)

# 计算因子。要求得到的因子由date和asset双重索引。
# 提示：使用melt函数
# BEGIN SOLUTION
factor = (factor.reset_index()
          .melt(id_vars='date', var_name="asset", value_name="factor")
          .set_index(["date", "asset"])
         )
# END SOLUTION
prices = bars.price.unstack(level=1)

# 请调用get_clean_factor_and_forward_returns进行预处理，得到merge_factor
# BEGIN SOLUTION
merged_factor = get_clean_factor_and_forward_returns(factor, prices, quantiles=None, bins=5)
# END SOLUTION

create_returns_tear_sheet(merged_factor)
```

请回答以下问题：

1. 本因子中，调用get_clean_factor_and_forward_returns时按quantiles分层，为何会出错？
2. 在代码调通后，将起始时间改为2008，此时会出现max_loss_error。为什么？
3. alpha042中，如果去掉因子计算中的rank，即直接返回(vwap - close) / (vwap + close)，结果会怎么样？请练习并回答为什么。


```md
<!-- MANUAL GRADED ANSWER points=3 -->
1. 第42号因子的计算结果尽管是浮点数，但本质上是离散值。对离散值按quantiles分层，难以达到quantiles分层后，每一层样本数大致相同的要求，因而出错。
2. 这个因子在计算时，执行了行内排列。在2008年时，A股的资产数还比较少，但计算因子时，即使这些资产在2008年没有上市，它们也会成为输入数据中的一列，而在这些时间段内，它们的价格都为nan，因此，最终生成的因子中，对应资产的因子值都是nan，直到它们上市为止。所以直到最后一个资产上市之日，之前的因子都会被alphalens剔除。数据预处理中常常会蕴藏着大量脏活。
3. alpha042中的排序是必要的。但是，Alphalens框架在进行因子检验时，还会对因子再进行一次排序。这实际上是执行了双重排序。只不过，在alpha042中，它是对分子、分母分别排序再相除，这就引入了新的解释，不完全等同于(vwap - close) / (vwap + close)。
```
