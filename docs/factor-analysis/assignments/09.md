# 第9课 习题

## 1. RSI的冷启动期有多长?

在课程中，我们讲到，技术指标一般都有冷启动期。有一些是由于缺少初始数据造成的（比如5日简单移动平均，前四期是无法计算的）；但有一些指标，本身是递归计算的，即T期指标依赖于T-1期指标，而T-1期又依赖于T-2期指标，依此类推，这样就导致它的冷启动期会比较长。

现在，就请你来探索RSI的冷启动期究竟会有多长。

怎么建模这个问题呢？

我们可以这样思考这个问题。冷启动带来的影响是，由于缺少初始数据以及传递依赖造成的，过了n期之后，数据就不再受n期之前的数据影响。换言之，假设有一个指标f是在窗口为n下计算的，如果n==3，那么以下两组数据计算出来的结果，从第3期以后，就应该是完全一样的。

```
a1 = [np.nan, 1, 2, 1, 2, 3]
a2 = [0, 1, 2, 1, 2, 3]
```

此时应该有$f(a1)[3:] = f(a2)[3:]$。换句话说，如果我们把a2中的0号元素换成任意一个数字，得到的数组$\hat{a1}$，它计算出来的指标f都应该与直接用a2计算出来的f完全一致。

在Python中，我们用浮点数比较来表示两个数相等，假设两个数之间的绝对值差小于$\epsilon$，那么我们就认为这两个数相等。

```python
# AUTOGRADE SOLUTION
%precision 1

np.random.seed(24)

arr0 = np.random.randint(10, 20, 100)
rsi = ta.RSI(arr0.astype(np.float64), 6)

arr1 = arr0.copy()
arr1[0] = 1
rsi1 = ta.RSI(arr1.astype(np.float64), 6)

# 要求：通过循环，找到第一个满足条件的i，使用rsi[i]和rsi1[i]的差值小于1e-2
# 注意：ra.RSI返回的RSI是百分比值，请转换为0-1之间的浮点数后再计算
# BEGIN SOLUTION
for i in range(100):
    if np.isnan(rsi[i]):
        continue
    if abs((rsi[i] - rsi1[i]) / 100) < 1e-2:
        print(i, rsi[i], rsi1[i])
        break
# END SOLUTION
```

你得到的结果应该是27.

```python
# AUTOGRADE TEST
assert i == 27
```

这个等待期比较长。我们也可以接受3e-2的差距，这时的等待期是18个周期。

**思考：**

```md
<!-- MANUAL GRADE QUESTION 2 points -->
1. 在前面的因子分析中，我们并没有考虑RSI的冷启动期。这会对因子分析的结果造成多大影响？请全面分析。
2. 如果要使用得RSI与行情软件上看到的一致，这个冷启动期应该是多长？
```

**你的答案**

```md
<!-- MANUAL GRADED ANSWER -->
1. 如果所有的资产的IPO日都在回测时间之前，那么，大概前面18个数据不准确，如果回测期足够长，这些影响可以忽略。但如果回测期随时有新的资产上市交易，那么因子检验将持续受到这部分数据的影响。
2. 行情软件的RSI精度是小数点的后三位（百分数1位），所以，要与行情软件保持一致，就必须保证误差在1e-3以内，需要等待60个周期。
```

## 2. RSI的钝化

任何一个技术指标，都会对特定的区间敏感，而对另一些区间不敏感。以 RSI 举例来说，在 30 到 70 之间，股价上涨 1%，RSI 的变动可能会比较大；而当 RSI 到了 90 之后，股价即使上涨 5%，RSI 的变动也会非常小。这种情况被称为钝化。

指标的钝化不利于量化。比如，如果指标（因子）与收益之间是线性变化的，那么，如果能预测出指标，也就能反推价格；即使指标的预测必须加上误差范围，反推出来的价格的误差范围也将是线性的、可预测的。但在钝化的情况下，在指标预测上很小的误差，也会导致价格预测的误差范围很大，从而失去预测价值。

以因子分析的理论来说，一旦指标进入钝化区，因子的预测能力就会下降。从下图可以看出，在 1012 日前后，RSI 进入钝化区，此时股价每日大涨，但 RSI 指标只会略微上涨。

![](https://images.jieyu.ai/images/2024/10/rsi-passivation.png)

我们来看一个真实的例子，请你绘制出上图。

```python
# MANUAL ANSWER 5 points
start = datetime.date(2023,1,1)
end = datetime.date(2023, 12, 31)

code = "603178.XSHG"
barss = load_bars(start, end, (code, ))
bars = barss.xs(code, level=1).query("date<='2023-10-30'")

bars["rsi"] = ta.RSI(bars.close, 6)
bars["return"] = bars.close.pct_change() * 100
df = bars[["close", "return", "rsi"]][-20:]

# 请使用DataFrame的API，绘制一个双轴图，close与rsi在一个y轴上，return在另一个y轴上。
# BEGIN SOLUTION
ax = df.plot(figsize=(10, 6), secondary_y=['rsi'], mark_right=False)
# END SOLUTION

# 添加标题和标签
ax.set_title('Close, RSI and Return')
ax.set_xlabel('日期')
ax.right_ax.set_ylabel('RSI')
ax.set_ylabel('Close/Return')
plt.show()
```

## 3. 由指标反推价格

震荡类的指标会呈现比较明显的周期性。比如，RSI只能在0到100之前取值。因此，在震荡行情中，RSI达到前期高点后，就会向下，因此，前期高点就成为预测当前RSI最大值的一个参考。对于预测RSI最小值也是一样。

你可以仔细观察RSI与close的对照图，会发现这种规律出现的比较频繁。

如果我们能预测出RSI的最大值，能否由此反推对应的收盘价呢？如果能，那么，我们就应该在预测的收盘价处埋单卖出。

我们先来制造一个合成数据：

```python
# READ-ONLY
%precision 2
np.random.seed(78)
returns = np.random.normal(0, 0.02, size=64)

close = (1 + returns).cumprod() * 10
rsi = ta.RSI(close, 6)

fig, ax1 = plt.subplots()
line1, = ax1.plot(close, 'g-')
ax1.set_ylabel('close')

ax2 = ax1.twinx()
line2, = ax2.plot(rsi)
ax2.set_ylabel('rsi')

lines = [line1, line2]
ax1.legend(lines, ['close', 'rsi'])
plt.show()
hrsi = np.round(np.nanmax(rsi), 1)
```

前期RSI的高点是85.1。

假设我们打算在RSI达到85.1时就卖出，此时的价格应该是多少？

提示：你可以从RSI的公式进行推导，也可以使用暴力破解方法。

```python
# AUTOGRADE ANSWER

# 要求：计算出RSI达到85.15时的价格，保存在 hclose中
# BEGIN SOLUTION
for c in np.arange(close, close * 1.2, 0.01):
    data = np.insert(close, len(close), c)
    rsi = ta.RSI(data, 6)[-1]
    returns = c/close[-1] - 1
    if rsi > hrsi:
        hclose = c
        print(f"{c:.02f} {rsi:.1f} {returns:.2%}")
        break
# END SOLUTION
```

你的结果应该是13.32。

```python
# AUTOGRADE TEST
assert abs(hclose - 13.32) < 0.01
```

如果我们对RSI的误差达到1，利润误差会达到多少？


