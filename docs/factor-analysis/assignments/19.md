# 第19课习题

**教案总结**

在前面的课程中，我们学习了如何围绕 LightGBM 构建股票价格预测模型。首先了解了基于股价惯性运动的策略原理，这个原理就像是为我们打开了一扇观察股票价格变动的窗口，让我们知道可以从哪些方面去思考价格走势。接着，我们直接用收盘价数据来训练 LightGBM 模型，其中还详细探讨了数据处理和划分的过程，这就好比是为模型准备了一顿营养丰富的 “大餐”，让它能够更好地 “学习”。

本课中，我们又介绍了适用于量化交易的模型评估方法，这能帮助我们判断模型的优劣。然而，在实际应用中我们发现模型并不总是生效，经过分析得知梯度提升决策树是分段函数，需要我们指定区间规律才能更好地发挥作用。我们还提出了构建有投资价值模型的思路，并给出了基础模型代码。

通过这些内容的学习，相信大家已经对整个流程有了较为清晰的认识。接下来，让我们通过一系列的习题来巩固所学知识，看看大家是否真正掌握了利用 LightGBM 构建股票价格预测模型的技巧。

**知识点总结**
1. **策略原理**：利用股价惯性运动，对资产均线多项式拟合预测价格，需考虑均线窗口、多项式次数、拟合误差范围。
2. **数据处理**
    - 用`as_strided`生成收盘价滑动窗口特征数据集。
    - 计算收益率和目标价格，处理`target`中的`nan`值。
3. **数据集划分**：自定义`train_test_split`函数，按顺序在每个资产内部划分训练集、验证集和测试集。
4. **模型训练**：使用LightGBM进行回归训练，设置参数，添加早停和评估记录回调。
5. **模型评估**：自定义`eval_model`函数，计算MAPE指标，评估预测收益大于阈值时的实际盈亏，并可视化部分样本。
6. **模型分析**：绘制决策树分析模型决策依据，指出梯度提升决策树是分段函数，需指定区间规律。
7. **基础模型**：封装`BaseLGBModel`类，包含模型保存、加载、特征编码解码、时间序列划分等功能。 

## 1. 习题1：数据加载与处理
编写代码，修改数据加载的时间范围为 2022 年 1 月 1 日到 2022 年 12 月 31 日，并随机抽取 1500 支个股的数据。然后使用窗口大小为 15 的均线生成特征数据集。

```python
from numpy.lib.stride_tricks import as_strided
import pandas as pd
import numpy as np
from datetime import date

# 修改时间范围和个股数量
start = date(2022, 1, 1)
end = date(2022, 12, 31)
universe = 1500
barss = load_bars(start, end, universe=universe)

# BEGIN SOLUTION
def rolling_time_series(ts: np.ndarray, win: int):
    stride = ts.strides
    shape = (len(ts) - win + 1, win)
    strides = stride + stride
    return as_strided(ts, shape, strides)

def rolling_close(group, win, columns):
    index = group.index
    if len(group) < win:
        features = np.full((len(group), win), np.nan)
        df = pd.DataFrame(features, columns=columns, index=index)
        return df
    rolled = rolling_time_series(group["close"].values, win)
    padded = np.pad(
        rolled, ((win - 1, 0), (0, 0)), mode="constant", constant_values=np.nan)
    df = pd.DataFrame(padded, columns=columns, index=index)
    return df

# 完成代码，生成特征数据集
win = 15
feature_cols = [f"c{win - i}" for i in range(win)]
features = barss.groupby(level="asset").apply(rolling_close, win, columns=feature_cols)
features = features.droplevel(0)
features.tail()
# END SOLUTION
```

## 2. 习题 2：数据集划分
实现一个新的数据集划分函数，要求将数据按 60%、20%、20% 的比例划分为训练集、验证集和测试集，并且确保在每个资产内部进行划分。同时，添加一个参数 shuffle，当 shuffle 为 True 时，对每个资产的数据进行打乱后再划分。

```python
def train_test_split(data, feature_cols, cuts=(0.6, 0.2), shuffle=False):
    train, valid, test = [], [], []
    def cut_time_series(group, cuts, shuffle):
        if shuffle:
            group = group.sample(frac=1)
        itrain = int(len(group) * cuts[0])
        ival = itrain + int(len(group) * cuts[1])
        return (group.iloc[:itrain], group.iloc[itrain:ival], group.iloc[ival:])

    for item in data.groupby("asset").apply(cut_time_series, cuts=cuts, shuffle=shuffle).values:
        train.append(item[0])
        valid.append(item[1])
        test.append(item[2])

    df_train = pd.concat(train)
    df_valid = pd.concat(valid)
    df_test = pd.concat(test)

    return (df_train[feature_cols], df_valid[feature_cols], df_test[feature_cols],
            df_train["target"], df_valid["target"], df_test["target"])

# 假设 data 是之前生成的特征数据集
data = features
data["ret"] = barss["close"].unstack().pct_change().stack()
data["target"] = barss["close"].unstack().shift(-1).stack()
data = data.dropna(subset=["target", "ret"])
data.reset_index(inplace=True)

# 调用函数进行划分
# BEGIN SOLUTION
(X_train, X_val, X_test,
y_train, y_val, y_test) = train_test_split(data, feature_cols, shuffle=True)
# END SOLUTION
```

## 3. 习题 3：LightGBM 模型训练
修改 LightGBM 模型的参数，将 num_leaves 改为 50，learning_rate 改为 0.03，random_state 改为 123。然后训练模型，并计算测试集上的平均绝对百分比误差（MAPE）。

```python
import lightgbm as lgb
from sklearn.metrics import mean_absolute_percentage_error

params = {
    "objective": "regression",
    "metric": "mape",
    "num_leaves": 50,
    "learning_rate": 0.03,
    "random_state": 123,
}

esr = 50
evals_result = {}

num_rounds = 200
# 训练集：train_data;验证集：valid_data
# BEGIN SOLUTION
train_data = lgb.Dataset(X_train, label=y_train)
valid_data = lgb.Dataset(X_val, label=y_val)
# END SOLUTION

# 完成代码，训练模型
model = lgb.train(
    params,
    train_data,
    num_boost_round=num_rounds,
    valid_sets=[valid_data],
    callbacks=[lgb.early_stopping(esr), lgb.record_evaluation(evals_result)],
)

y_pred = model.predict(X_test.values)
mape = mean_absolute_percentage_error(y_test, y_pred)
print(f"MAPE: {mape:.3f}")
```

## 4. 习题 4：模型评估函数扩展
扩展 eval_model 函数，添加一个新的功能：计算并打印出所有预测收益率大于 long_threshold 的股票的夏普比率。夏普比率的计算公式为：$SharpeRatio=\frac{σ_p}{E(R_p−R_f)}$，其中 $E(R_p−R_f)$ 是投资组合的超额收益率，$σ_p$ 是投资组合的标准差。假设无风险利率 $R_f$ 为 0。

```python
from matplotlib.dates import WeekdayLocator
import numpy as np
import pandas as pd
from sklearn.metrics import mean_absolute_percentage_error

def eval_model(model, X_test, data, long_threshold=0.02, traces=0):
    df = data.rename(columns={"c1": "prev", "target": "actual"})
    df = df.loc[X_test.index]
    df["pred"] = model.predict(X_test.values)
    error = mean_absolute_percentage_error(df["actual"], df["pred"])
    print(f"mape is {error:.3f}")
    df["pred_ret"] = df["pred"] / df["prev"] - 1
    long_df = df.query(f"pred_ret > {long_threshold}")
    print(f'actual p&l {long_df["ret"].mean():.2%}')

    # 计算夏普比率
    if len(long_df) > 0:
        excess_returns = long_df["ret"]
        # BEGIN SOLUTION
        sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns)
        # END SOLUTION
        print(f"Sharpe Ratio: {sharpe_ratio:.3f}")

    if traces > 0:
        row = int(np.sqrt(traces))
        col = int(traces / row)
        if row * col < traces:
            row += 1
        symbols = long_df["asset"].unique()[:traces]
        _, axes = plt.subplots(row, col, figsize=(row * 4, col * 2))
        axes = axes.flatten()
        for i, symbol in enumerate(symbols):
            close = df.query(f"asset == '{symbol}'")[["prev", "date"]].set_index("date")
            x = list(close.index.strftime("%m/%d"))
            axes[i].plot(x, close, label="close")
            pred_close = df.query(f"asset == '{symbol}'")["pred"]
            axes[i].plot(x[1:], pred_close[:-1], label="pred")
            locator = WeekdayLocator()
            axes[i].xaxis.set_major_locator(locator)
            signal_dates = df.query(f"(pred_ret > {long_threshold}) & (asset == '{symbol}')")["date"]
            x = [i.strftime("%m/%d") for i in signal_dates]
            y = close.loc[signal_dates]
            axes[i].scatter(x, y, marker='^', color='red')
            axes[i].set_title(symbol)
            axes[i].legend()
        plt.tight_layout()

# 调用评估函数
# BEGIN SOLUTION
eval_model(model, X_test, data, traces=6)
# END SOLUTION
```

## 5. 习题 5：综合应用
结合前面的代码，实现一个完整的流程：从数据加载、特征生成、数据集划分、模型训练到模型评估。并且在评估时，分别计算不同 long_threshold（如 0.01、0.02、0.03） 下的实际盈亏和夏普比率，并绘制一个柱状图展示不同阈值下的实际盈亏。

```python
# BEGIN SOLUTION
from numpy.lib.stride_tricks import as_strided
import pandas as pd
import numpy as np
from datetime import date
import lightgbm as lgb
from sklearn.metrics import mean_absolute_percentage_error
import matplotlib.pyplot as plt
from matplotlib.dates import WeekdayLocator

# 数据加载
start = date(2023, 1, 1)
end = date(2023, 12, 29)
universe = 2000
barss = load_bars(start, end, universe=universe)

# 特征生成
def rolling_time_series(ts: np.ndarray, win: int):
    stride = ts.strides
    shape = (len(ts) - win + 1, win)
    strides = stride + stride
    return as_strided(ts, shape, strides)

def rolling_close(group, win, columns):
    index = group.index
    if len(group) < win:
        features = np.full((len(group), win), np.nan)
        df = pd.DataFrame(features, columns=columns, index=index)
        return df
    rolled = rolling_time_series(group["close"].values, win)
    padded = np.pad(
        rolled, ((win - 1, 0), (0, 0)), mode="constant", constant_values=np.nan)
    df = pd.DataFrame(padded, columns=columns, index=index)
    return df

win = 10
feature_cols = [f"c{win - i}" for i in range(win)]
features = barss.groupby(level="asset").apply(rolling_close, win, columns=feature_cols)
features = features.droplevel(0)

# 数据集划分
def train_test_split(data, feature_cols, cuts=(0.7, 0.2)):
    train, valid, test = [], [], []
    def cut_time_series(group, cuts):
        itrain = int(len(group) * cuts[0])
        ival = itrain + int(len(group) * cuts[1])
        return (group.iloc[:itrain], group.iloc[itrain:ival], group.iloc[ival:])

    for item in data.groupby("asset").apply(cut_time_series, cuts=cuts).values:
        train.append(item[0])
        valid.append(item[1])
        test.append(item[2])

    df_train = pd.concat(train)
    df_valid = pd.concat(valid)
    df_test = pd.concat(test)

    return (df_train[feature_cols], df_valid[feature_cols], df_test[feature_cols],
            df_train["target"], df_valid["target"], df_test["target"])

data = features
data["ret"] = barss["close"].unstack().pct_change().stack()
data["target"] = barss["close"].unstack().shift(-1).stack()
data = data.dropna(subset=["target", "ret"])
data.reset_index(inplace=True)

(X_train, X_val, X_test,
y_train, y_val, y_test) = train_test_split(data, feature_cols)

# 模型训练
params = {
    "objective": "regression",
    "metric": "mape",
    "num_leaves": 31,
    "learning_rate": 0.05,
    "random_state": 42,
}

esr = 50
evals_result = {}

num_rounds = 200
train_data = lgb.Dataset(X_train, label=y_train)
valid_data = lgb.Dataset(X_val, label=y_val)

model = lgb.train(
    params,
    train_data,
    num_boost_round=num_rounds,
    valid_sets=[valid_data],
    callbacks=[lgb.early_stopping(esr), lgb.record_evaluation(evals_result)],
)

# 模型评估
thresholds = [0.01, 0.02, 0.03]
actual_pls = []
sharpe_ratios = []

def eval_model(model, X_test, data, long_threshold=0.02, traces=0):
    df = data.rename(columns={"c1": "prev", "target": "actual"})
    df = df.loc[X_test.index]
    df["pred"] = model.predict(X_test.values)
    error = mean_absolute_percentage_error(df["actual"], df["pred"])
    print(f"mape is {error:.3f}")
    df["pred_ret"] = df["pred"] / df["prev"] - 1
    long_df = df.query(f"pred_ret > {long_threshold}")
    actual_pl = long_df["ret"].mean()
    print(f'actual p&l {actual_pl:.2%}')
    if len(long_df) > 0:
        excess_returns = long_df["ret"]
        sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns)
        print(f"Sharpe Ratio: {sharpe_ratio:.3f}")
    else:
        sharpe_ratio = np.nan
    return actual_pl, sharpe_ratio

for threshold in thresholds:
    actual_pl, sharpe_ratio = eval_model(model, X_test, data, long_threshold=threshold)
    actual_pls.append(actual_pl)
    sharpe_ratios.append(sharpe_ratio)

# 绘制柱状图
plt.bar([str(thr) for thr in thresholds], actual_pls)
plt.xlabel('Long Threshold')
plt.ylabel('Actual P&L')
plt.title('Actual P&L at Different Long Thresholds')
plt.show()
# END SOLUTION
```