# 第16课习题

**背景**

在金融市场分析中，利用历史数据构建合适的模型以预测资产价格走势是常见且重要的任务。本次习题将借助 Akshare 库获取金融数据，运用不同的优化算法和交叉验证方法对模型进行训练和评估，加深对模型优化和交叉验证概念的理解。


## 1. 习题 1：优化算法概念理解

请简要解释以下优化算法的核心思想，并说明其优缺点：
- 批量梯度下降（Batch Gradient Descent, BGD）
- 随机梯度下降（Stochastic Gradient Descent, SGD）
- 动量法（Momentum）
- AdaGrad
- 牛顿法

**答案示例**
- 批量梯度下降（BGD）
    - 核心思想：在每一次迭代中，使用整个训练数据集来计算梯度，并沿着负梯度方向更新模型参数，以最小化损失函数。
    - 优点：收敛到全局最优解的稳定性较高，因为它使用了所有数据的信息。
    - 缺点：训练速度慢，尤其是在数据集较大时，每次迭代都需要计算整个数据集的梯度，计算成本高。
- 随机梯度下降（SGD）
    - 核心思想：在每一次迭代中，随机选择一个样本计算梯度，并根据该梯度更新模型参数。
    - 优点：训练速度快，因为每次只使用一个样本进行计算，能够快速更新参数。
    - 缺点：收敛路径不稳定，容易陷入局部极小值，并且由于每次只使用一个样本，梯度的估计可能存在较大噪声。
- 动量法（Momentum）
    - 核心思想：在梯度下降的基础上，引入动量项，通过累积历史梯度信息来调整参数更新的方向和幅度。动量项使得参数更新在相似方向上加速，而在不同方向上减速。
    - 优点：能够加速收敛，尤其是在梯度方向变化较大的情况下，有助于更快地跳出局部极小值，找到全局极小值。
    - 缺点：需要额外的超参数（动量系数）进行调整，如果设置不当，可能会影响收敛效果。
- AdaGrad
    - 核心思想：自适应地调整每个参数的学习率。对于频繁更新的参数，学习率逐渐减小；对于不频繁更新的参数，学习率保持较大。通过累积历史梯度信息来实现这种自适应调整。
    - 优点：适合处理稀疏数据，能够自动调整不同参数的学习率，提高优化效率。
    - 缺点：学习率可能会过早地减小到非常小的值，导致收敛速度变慢，尤其是在训练后期。
- 牛顿法
    - 核心思想：利用二阶导数信息（Hessian 矩阵）来更新模型参数。通过求解目标函数的二阶泰勒展开式的最小值来确定参数的更新方向。
    - 优点：收敛速度快，能够更快地收敛到全局极小值，尤其是在目标函数具有较好的凸性时。
    - 缺点：计算复杂度高，需要计算和存储 Hessian 矩阵，对于大规模数据集来说，内存和计算开销都非常大。
  
## 2. 习题 2：交叉验证方法选择
以下是几种不同类型的数据集和任务，请根据这些信息选择合适的交叉验证方法，并简要说明理由：
- 一个普通的图像分类数据集，样本数量适中，类别分布较为均衡。
- 一个医疗诊断数据集，样本数量较少，且不同疾病类别的样本数量差异较大。
- 一个股票价格预测数据集，数据具有明显的时间顺序，需要根据历史数据预测未来价格。

**答案示例**
- 普通图像分类数据集
    - 选择方法：k - fold 交叉验证。
    - 理由：数据集样本数量适中且类别分布均衡，k - fold 交叉验证可以充分利用数据集，减少因数据划分带来的偏差，通过多次训练和验证，更准确地估计模型的泛化能力。
- 医疗诊断数据集
    - 选择方法：Stratified K - Fold Cross - Validation（分层 k 折交叉验证）。
    - 理由：样本数量较少且类别分布不均衡，分层 k 折交叉验证可以保证每个子集（折）中各类别的样本比例与原始数据集相同，避免因抽样过程导致某些类别在训练集或验证集中缺失，从而更公平地评估模型性能。
- 股票价格预测数据集
    - 选择方法：时间序列交叉验证（Time Series Cross - Validation）或滚动预测（Rolling Forecasting）。
    - 理由：数据具有明显的时间顺序，使用普通的 k - fold 交叉验证可能会打乱数据的时间顺序，导致未来数据的问题。时间序列交叉验证和滚动预测可以保证数据的时间顺序不被打乱，更符合股票价格预测的实际需求。滚动预测还能更好地利用数据之间的时间关联，逐步更新模型进行预测。

## 3. 习题 3：代码实现小批量梯度下降

使用 Python 手动实现小批量梯度下降（Mini - Batch Gradient Descent, MBGD）算法，对一个简单的线性回归模型进行训练。假设数据集为 X（特征矩阵）和 y（目标向量），模型的损失函数为均方误差（Mean Squared Error, MSE）。要求：
1. 实现一个函数 mini_batch_gradient_descent，接受 X、y、学习率 learning_rate、批量大小 batch_size 和迭代次数 epochs 作为参数。
2. 在函数内部，使用小批量梯度下降算法更新模型参数，并返回训练后的模型参数和每次迭代的损失值。

```python
import numpy as np

def mini_batch_gradient_descent(X, y, learning_rate=0.01, batch_size=32, epochs=100):
    m, n = X.shape
    # 初始化模型参数
    theta = np.zeros((n, 1))
    losses = []
    for epoch in range(epochs):
        # 随机打乱数据
        # BEGIN SOLUTION
        permutation = np.random.permutation(m)
        # END SOLUTION
        
        X_shuffled = X[permutation]
        y_shuffled = y[permutation]
        for i in range(0, m, batch_size):
            # 选取小批量数据
            # BEGIN SOLUTION
            X_batch = X_shuffled[i:i + batch_size]
            y_batch = y_shuffled[i:i + batch_size]
            # END SOLUTION
            
            # 计算预测值
            y_pred = np.dot(X_batch, theta)
            # 计算误差
            error = y_pred - y_batch
            # 计算梯度
            gradient = 2 * np.dot(X_batch.T, error) / batch_size
            # 更新参数
            theta = theta - learning_rate * gradient
        # 计算当前迭代的损失值
        y_pred_all = np.dot(X, theta)
        loss = np.mean((y_pred_all - y) ** 2)
        losses.append(loss)
    return theta, losses
```

```python
# 示例数据
X = np.random.rand(100, 2)
y = np.random.rand(100, 1)

# 调用函数进行训练
theta, losses = mini_batch_gradient_descent(X, y, learning_rate=0.01, batch_size=16, epochs=50)
print("Trained parameters:", theta)
```

## 4. 习题 4：留一法交叉验证实现
使用 Python 实现留一法交叉验证（Leave - One - Out Cross - Validation, LOOCV），对一个简单的逻辑回归模型进行评估。假设数据集为 X（特征矩阵）和 y（目标向量），使用 sklearn 中的 LogisticRegression 模型。要求：
1. 实现一个函数 loocv，接受 X 和 y 作为参数。
2. 在函数内部，使用留一法交叉验证计算模型的平均准确率，并返回该值。

```python
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score

def loocv(X, y):
    n = X.shape[0]
    accuracies = []
    for i in range(n):
        # 划分训练集和验证集
        X_train = np.delete(X, i, axis=0)
        y_train = np.delete(y, i)
        X_test = X[i].reshape(1, -1)
        y_test = y[i].reshape(1,)
        # 创建并训练模型
        model = LogisticRegression()
        model.fit(X_train, y_train)
        # 预测并计算准确率
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        accuracies.append(accuracy)
    # 计算平均准确率
    average_accuracy = np.mean(accuracies)
    return average_accuracy

# 示例数据
X = np.random.rand(20, 5)
y = np.random.randint(0, 2, 20)

# 调用函数进行留一法交叉验证
# BEGIN SOLUTION
average_accuracy = loocv(X, y)
# END SOLUTION
print("Average accuracy using LOOCV:", average_accuracy)
```

## 5. 习题 5：滚动预测代码实现
使用 Python 实现滚动预测（Rolling Forecasting）方法，对一个时间序列数据集进行模型评估。假设数据集为 X（特征矩阵）和 y（目标向量），使用 sklearn 中的 LinearRegression 模型。要求：
1. 实现一个函数 rolling_forecasting，接受 X、y、初始训练集大小 initial_train_size 和滚动步长 step_size 作为参数。
2. 在函数内部，使用滚动预测方法对模型进行训练和预测，并返回每次预测的误差值。

```python
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
import numpy as np

def rolling_forecasting(X, y, initial_train_size=10, step_size=1):
    m = X.shape[0]
    errors = []
    train_size = initial_train_size
    while train_size < m:
        # 划分训练集和验证集
        X_train = X[:train_size]
        y_train = y[:train_size]
        X_test = X[train_size:train_size + step_size]
        y_test = y[train_size:train_size + step_size]
        # 创建并训练模型
        model = LinearRegression()
        model.fit(X_train, y_train)
        # 预测并计算误差
        y_pred = model.predict(X_test)
        error = mean_squared_error(y_test, y_pred)
        errors.append(error)
        # 更新训练集大小
        train_size += step_size
    return errors

# 示例数据
X = np.random.rand(50, 3)
y = np.random.rand(50)

# 调用函数进行滚动预测
# BEGIN SOLUTION
errors = rolling_forecasting(X, y, initial_train_size=20, step_size=2)
# END SOLUTION
print("Errors in rolling forecasting:", errors)
```

## 6. 习题6：综合练习

**习题要求**

1. 数据获取与预处理
    - 使用 Akshare 库获取某只股票（例如贵州茅台，股票代码 600519.SH）的历史日线数据。
    - 提取关键特征，如开盘价、最高价、最低价、收盘价和成交量等。
    - 计算技术指标，如简单移动平均线（SMA）和相对强弱指数（RSI），并将其作为额外特征。
    - 对特征进行标准化处理，以避免特征量纲差异对模型训练的影响。

2. 模型构建与训练
    - 构建一个线性回归模型，使用标准化后的特征来预测股票的收盘价。
    - 分别使用小批量梯度下降（Mini - Batch Gradient Descent, MBGD）和 Adam 优化算法对模型进行训练。
    - 手动实现小批量梯度下降算法，设置合适的学习率和批量大小；使用 torch.optim.Adam 实现 Adam 优化算法。

3. 交叉验证
    - 对于普通数据集，使用 k - fold 交叉验证方法评估模型的性能。
    - 考虑到金融数据的时间序列特性，使用时间序列交叉验证（Time Series Cross - Validation）评估模型性能。
    - 分别计算两种交叉验证方式下，MBGD 和 Adam 优化算法训练的模型的平均验证误差。

4. 模型调优
    - 针对 MBGD 优化算法，尝试不同的学习率和批量大小；针对 Adam 优化算法，尝试不同的学习率和 betas 参数。
    - 记录不同参数组合下的验证误差，选择最优的参数组合。

5. 结果可视化
    - 绘制 MBGD 和 Adam 优化算法在训练过程中的损失曲线。
    - 绘制两种优化算法在 k - fold 交叉验证和时间序列交叉验证下的平均验证误差对比图。
    - 绘制预测值与真实值的对比图。

```python
# BEGIN SOLUTION
import akshare as ak
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import KFold
from sklearn.model_selection import TimeSeriesSplit
import torch
import torch.nn as nn
import matplotlib.pyplot as plt


# 1. 数据获取与预处理
def get_and_preprocess_data():
    # 获取股票历史日线数据
    stock_data = ak.stock_zh_a_hist(symbol="000651", period="daily", start_date="20230101", end_date="20231231", adjust="")
    
    # 提取关键特征
    features = stock_data[['开盘', '最高', '最低', '收盘', '成交量']].values
    print("关键特征形状:", features.shape)

    # 计算简单移动平均线（SMA）
    sma_5 = stock_data['收盘'].rolling(window=5).mean().dropna().values
    sma_10 = stock_data['收盘'].rolling(window=10).mean().dropna().values
    features = features[9:, :]
    sma_5 = sma_5[4:]
    sma_10 = sma_10
    
    # 计算相对强弱指数（RSI）
    delta = stock_data['收盘'].diff()
    up = delta.clip(lower=0)
    down = -delta.clip(upper=0)
    avg_up = up.rolling(window=14).mean()
    avg_down = down.rolling(window=14).mean()
    rs = avg_up / avg_down
    rsi = 100 - (100 / (1 + rs))
    rsi = rsi.dropna().values
    rsi = rsi[5:]
    print("features:",features.shape,"sma_5:",sma_5.shape,"sma_10:",sma_10.shape,"rsi:",rsi.shape)
    
    # 找到最小长度
    min_length = min(features.shape[0], sma_5.shape[0], sma_10.shape[0], rsi.shape[0])
    
    # 裁剪数组
    features = features[:min_length]
    sma_5 = sma_5[:min_length].reshape(-1, 1)
    sma_10 = sma_10[:min_length].reshape(-1, 1)
    rsi = rsi[:min_length].reshape(-1, 1)

    features = np.hstack((features, sma_5, sma_10, rsi))
    print("拼接后的数组形状:", features.shape)
    
    target = features[:, 3]
    features = features[:, [0, 1, 2, 4, 5, 6, 7]]
    
    # 特征标准化
    scaler = StandardScaler()
    features = scaler.fit_transform(features)
    
    return features, target

# 2. 模型构建与训练
class LinearRegressionModel(nn.Module):
    def __init__(self, input_size):
        super(LinearRegressionModel, self).__init__()
        self.linear = nn.Linear(input_size, 1)

    def forward(self, x):
        out = self.linear(x)
        return out


# 手动实现小批量梯度下降
def mini_batch_gradient_descent(X, y, learning_rate=0.01, batch_size=32, epochs=100):
    m, n = X.shape
    X = torch.tensor(X, dtype=torch.float32)
    y = torch.tensor(y, dtype=torch.float32).reshape(-1, 1)
    model = LinearRegressionModel(n)
    criterion = nn.MSELoss()
    losses = []
    for epoch in range(epochs):
        for i in range(0, m, batch_size):
            X_batch = X[i:i + batch_size]
            y_batch = y[i:i + batch_size]
            outputs = model(X_batch)
            loss = criterion(outputs, y_batch)
            model.zero_grad()
            loss.backward()
            with torch.no_grad():
                for param in model.parameters():
                    param -= learning_rate * param.grad
        losses.append(loss.item())
    return model, losses


# 使用 Adam 优化算法
def adam_optimizer(X, y, learning_rate=0.01, epochs=100, betas=(0.9, 0.999)):
    m, n = X.shape
    X = torch.tensor(X, dtype=torch.float32)
    y = torch.tensor(y, dtype=torch.float32).reshape(-1, 1)
    model = LinearRegressionModel(n)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, betas=betas)
    losses = []
    for epoch in range(epochs):
        outputs = model(X)
        loss = criterion(outputs, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        losses.append(loss.item())
    return model, losses


# 3. 交叉验证
def k_fold_cross_validation(X, y, optimizer_type, **kwargs):
    k = 5
    kf = KFold(n_splits=k, shuffle=True, random_state=42)
    validation_errors = []
    for train_index, val_index in kf.split(X):
        X_train, X_val = X[train_index], X[val_index]
        y_train, y_val = y[train_index], y[val_index]
        if optimizer_type == 'MBGD':
            model, _ = mini_batch_gradient_descent(X_train, y_train, **kwargs)
        elif optimizer_type == 'Adam':
            model, _ = adam_optimizer(X_train, y_train, **kwargs)
        X_val = torch.tensor(X_val, dtype=torch.float32)
        y_val = torch.tensor(y_val, dtype=torch.float32).reshape(-1, 1)
        with torch.no_grad():
            predictions = model(X_val)
            val_error = ((predictions - y_val) ** 2).mean().item()
        validation_errors.append(val_error)
    return np.mean(validation_errors)


def time_series_cross_validation(X, y, optimizer_type, **kwargs):
    tscv = TimeSeriesSplit(n_splits=5)
    validation_errors = []
    for train_index, val_index in tscv.split(X):
        X_train, X_val = X[train_index], X[val_index]
        y_train, y_val = y[train_index], y[val_index]
        if optimizer_type == 'MBGD':
            model, _ = mini_batch_gradient_descent(X_train, y_train, **kwargs)
        elif optimizer_type == 'Adam':
            model, _ = adam_optimizer(X_train, y_train, **kwargs)
        X_val = torch.tensor(X_val, dtype=torch.float32)
        y_val = torch.tensor(y_val, dtype=torch.float32).reshape(-1, 1)
        with torch.no_grad():
            predictions = model(X_val)
            val_error = ((predictions - y_val) ** 2).mean().item()
        validation_errors.append(val_error)
    return np.mean(validation_errors)


# 4. 模型调优
def model_tuning(X, y):
    # 调优 MBGD
    learning_rates_mbgd = [0.001, 0.01, 0.1]
    batch_sizes = [16, 32, 64]
    best_error_mbgd_kf = float('inf')
    best_params_mbgd_kf = {}
    best_error_mbgd_ts = float('inf')
    best_params_mbgd_ts = {}
    for lr in learning_rates_mbgd:
        for bs in batch_sizes:
            error_kf = k_fold_cross_validation(X, y, 'MBGD', learning_rate=lr, batch_size=bs, epochs=100)
            if error_kf < best_error_mbgd_kf:
                best_error_mbgd_kf = error_kf
                best_params_mbgd_kf = {'learning_rate': lr, 'batch_size': bs}
            error_ts = time_series_cross_validation(X, y, 'MBGD', learning_rate=lr, batch_size=bs, epochs=100)
            if error_ts < best_error_mbgd_ts:
                best_error_mbgd_ts = error_ts
                best_params_mbgd_ts = {'learning_rate': lr, 'batch_size': bs}
        
    # 调优 Adam
    learning_rates_adam = [0.001, 0.01, 0.1]
    betas_list = [(0.9, 0.999), (0.95, 0.999), (0.9, 0.99)]
    best_error_adam_kf = float('inf')
    best_params_adam_kf = {}
    best_error_adam_ts = float('inf')
    best_params_adam_ts = {}
    for lr in learning_rates_adam:
        for betas in betas_list:
            error_kf = k_fold_cross_validation(X, y, 'Adam', learning_rate=lr, epochs=100, betas=betas)
            if error_kf < best_error_adam_kf:
                best_error_adam_kf = error_kf
                best_params_adam_kf = {'learning_rate': lr, 'betas': betas}
            error_ts = time_series_cross_validation(X, y, 'Adam', learning_rate=lr, epochs=100, betas=betas)
            if error_ts < best_error_adam_ts:
                best_error_adam_ts = error_ts
                best_params_adam_ts = {'learning_rate': lr, 'betas': betas}
                
    print("Best parameters for MBGD (k - fold):", best_params_mbgd_kf)
    print("Best validation error for MBGD (k - fold):", best_error_mbgd_kf)
    print("Best parameters for MBGD (time series):", best_params_mbgd_ts)
    print("Best validation error for MBGD (time series):", best_error_mbgd_ts)
    print("Best parameters for Adam (k - fold):", best_params_adam_kf)
    print("Best validation error for Adam (k - fold):", best_error_adam_kf)
    print("Best parameters for Adam (time series):", best_params_adam_ts)
    print("Best validation error for Adam (time series):", best_error_adam_ts)
    return best_params_mbgd_kf, best_params_mbgd_ts, best_params_adam_kf, best_params_adam_ts


# 5. 结果可视化
def visualize_results(X, y, best_params_mbgd_kf, best_params_mbgd_ts, best_params_adam_kf, best_params_adam_ts):
    # 绘制 MBGD 和 Adam 优化算法在训练过程中的损失曲线
    model_mbgd, losses_mbgd = mini_batch_gradient_descent(X, y, **best_params_mbgd_kf)
    model_adam, losses_adam = adam_optimizer(X, y, **best_params_adam_kf)
    plt.figure(figsize=(12, 6))
    plt.plot(losses_mbgd, label='MBGD Loss')
    plt.plot(losses_adam, label='Adam Loss')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.title('Training Loss Curves')
    plt.legend()
    plt.show()
    
    # 绘制两种优化算法在 k - fold 交叉验证和时间序列交叉验证下的平均验证误差对比图
    error_mbgd_kf = k_fold_cross_validation(X, y, 'MBGD', **best_params_mbgd_kf)
    error_mbgd_ts = time_series_cross_validation(X, y, 'MBGD', **best_params_mbgd_ts)
    error_adam_kf = k_fold_cross_validation(X, y, 'Adam', **best_params_adam_kf)
    error_adam_ts = time_series_cross_validation(X, y, 'Adam', **best_params_adam_ts)
    labels = ['MBGD (k - fold)', 'MBGD (time series)', 'Adam (k - fold)', 'Adam (time series)']
    errors = [error_mbgd_kf, error_mbgd_ts, error_adam_kf, error_adam_ts]
    plt.figure(figsize=(12, 6))
    plt.bar(labels, errors)
    plt.xlabel('Optimizer and Cross - Validation Type')
    plt.ylabel('Average Validation Error')
    plt.title('Average Validation Errors Comparison')
    plt.show()
    
    # 绘制预测值与真实值的对比图
    X = torch.tensor(X, dtype=torch.float32)
    y = torch.tensor(y, dtype=torch.float32).reshape(-1, 1)
    with torch.no_grad():
        predictions_mbgd = model_mbgd(X)
        predictions_adam = model_adam(X)
    plt.figure(figsize=(12, 6))
    plt.plot(y.numpy(), label='True Values')
    plt.plot(predictions_mbgd.numpy(), label='MBGD Predictions')
    plt.plot(predictions_adam.numpy(), label='Adam Predictions')
    plt.xlabel('Time')
    plt.ylabel('Stock Price')
    plt.title('True Values vs Predictions')
    plt.legend()
    plt.show()

if __name__ == "__main__":
    features, target = get_and_preprocess_data()
    best_params_mbgd_kf, best_params_mbgd_ts, best_params_adam_kf, best_params_adam_ts = model_tuning(features, target)
    visualize_results(features, target, best_params_mbgd_kf, best_params_mbgd_ts, best_params_adam_kf, best_params_adam_ts)

# END SOLUTION
```