# 第 17 课习题

在课程中，我们介绍了配对交易策略，围绕如何寻找合适的配对标的展开，深入探讨了平稳时间序列、协整检验以及聚类算法等关键内容。

1. **配对交易策略基础**
    - 配对交易策略由摩根士丹利量化分析师在 20 世纪 80 年代首创，通过监控两只历史相关性强的证券价格差，当价格差超出阈值时进行买卖操作以获利。
    - 传统寻找配对标的的方法是选择同行业龙头公司，但这种方式的套利空间已被市场压缩，且公司业务变化会影响配对关系，因此需要依靠数据分析挖掘新机会。
2. **平稳时间序列与协整检验**
    - 平稳时间序列：统计特性（均值、方差、自协方差）在时间上保持不变，股票价格通常是非平稳序列，而收益近似平稳序列。通过 ADF 检验判断序列是否平稳，若存在单位根则为非平稳序列，反之则为平稳序列。
    - 构造平稳时间序列：对于走势长期一致的两个资产序列，可通过引入对冲比构造平稳时间序列。对冲比通过对两个资产序列进行线性回归，取斜率得到。
    - 协整检验：用于检查两个或多个非平稳时间序列是否存在长期均衡关系。在进行价差序列构造前，先进行协整检验可避免盲目操作。
3. **聚类算法**
    - 直接对所有资产进行两两协整性检验计算量巨大，通过聚类算法先对资产进行粗略分类，再在分类中进行两两协整性检验可大幅减少计算量。
    - K - Means 算法：基于划分的聚类算法，原理直观、计算效率高，但需预先指定簇数量，对初始聚类中心敏感，无法处理非球形分布和复杂形状数据，对噪声和离群点敏感。
    - DBSCAN 算法：基于密度的聚类算法，无需预先指定簇数量，能发现任意形状的数据分布，可处理噪声数据，但对密度差异较大的数据处理不佳，对超参数敏感。
    - HDBSCAN 算法：改进的 DBSCAN 算法，具有层次聚类特点，通过构建基于密度的层次结构克服了 DBSCAN 的问题，聚类结果更灵活稳定。

为了帮助大家巩固所学知识，以下是一些相关的课后习题。请认真完成，答案将在后续课程中详细讲解，希望大家通过这些习题加深对配对交易策略及相关分析方法的理解和应用。

## 1. 习题 1：平稳时间序列的生成与检验

要求：
1. 使用 akshare 库获取某只股票（如 '000001.SZ'）一段时间（如 2023 年 1 月 1 日至 2023 年 12 月 31 日）的收盘价数据。
2. 计算该股票收盘价的每日收益率序列，此序列近似平稳时间序列。
3. 绘制该收益率序列的图表。
4. 对收益率序列进行 ADF 检验，并输出检验结果（包括 ADF 统计量、p 值和临界值）。
5. 根据检验结果判断该序列是否平稳。

```python
import akshare as ak
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.tsa.stattools import adfuller

# 获取股票数据
stock_data = ak.stock_zh_a_hist(symbol='000001', start_date='20230101', end_date='20231231')
close_prices = stock_data['收盘'].astype(float)

# 计算每日收益率
# BEGIN SOLUTION
returns = close_prices.pct_change().dropna()
# END SOLUTION
```

```python
# 绘制收益率序列图表
plt.figure(figsize=(12, 6))
plt.plot(returns, label='Daily Returns')
plt.title('Daily Returns of Stock 000001')
plt.legend()
plt.show()
```

```python
# 进行 ADF 检验
# BEGIN SOLUTION
adf_result = adfuller(returns)
# END SOLUTION

print("平稳时间序列 ADF检验结果:")
print(f"ADF Statistic: {adf_result[0]}")
print(f"p-value: {adf_result[1]}")
print(f"Critical Values: {adf_result[4]}")
```

```python
# 判断序列是否平稳
if adf_result[1] < 0.05:
    print("该序列通过平稳性检验，是平稳序列。")
else:
    print("该序列未通过平稳性检验，是非平稳序列。")
```

## 2. 习题 2：金融资产价格与收益的平稳性检验

**要求**
1. 使用 akshare 库获取两只不同股票（如 '000001.SZ' 和 '600000.SH'）在 2023 年 1 月 1 日至 2023 年 12 月 31 日的收盘价数据。
2. 分别计算这两只股票收盘价的每日收益率。
3. 绘制这两只股票收盘价和收益率的图表，要求使用双坐标轴显示。
4. 分别对这两只股票的收盘价和收益率进行 ADF 检验，并将检验结果存储在一个 Pandas 数据框中输出。

```python
import akshare as ak
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.tsa.stattools import adfuller

# 获取股票数据
stock1_data = ak.stock_zh_a_hist(symbol='000001', start_date='20230101', end_date='20231231')
stock2_data = ak.stock_zh_a_hist(symbol='600000', start_date='20230101', end_date='20231231')

close1 = stock1_data['收盘'].astype(float)
close2 = stock2_data['收盘'].astype(float)

returns1 = close1.pct_change().dropna()
returns2 = close2.pct_change().dropna()
```

```python
# 绘制图表
fig, axs = plt.subplots(2, 1, figsize=(12, 12))

# 第一只股票
ax1 = axs[0]
ax1.plot(close1, label='Stock 000001 Price')
ax1.grid(False)
ax2 = ax1.twinx()
ax2.plot(returns1, label="Stock 000001 Returns", color='orange')
ax2.grid(False)
lines, labels = ax1.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()
ax1.legend(lines + lines2, labels + labels2, loc='best')
ax1.set_title('Stock 000001 Price and Returns')

# 第二只股票
ax3 = axs[1]
ax3.plot(close2, label='Stock 600000 Price')
ax3.grid(False)
ax4 = ax3.twinx()
ax4.plot(returns2, label="Stock 600000 Returns", color='orange')
ax4.grid(False)
lines3, labels3 = ax3.get_legend_handles_labels()
lines4, labels4 = ax4.get_legend_handles_labels()
ax3.legend(lines3 + lines4, labels3 + labels4, loc='best')
ax3.set_title('Stock 600000 Price and Returns')

plt.show()
```

```python
# 进行 ADF 检验
# BEGIN SOLUTION
result1_close = adfuller(close1)
result1_returns = adfuller(returns1)
result2_close = adfuller(close2)
result2_returns = adfuller(returns2)
# END SOLUTION
```

```python
df = pd.DataFrame({
    "Stock 000001 Close": (result1_close[0], result1_close[1], result1_close[4]["1%"], result1_close[4]["5%"], result1_close[4]["10%"]),
    "Stock 000001 Returns": (result1_returns[0], result1_returns[1], result1_returns[4]["1%"], result1_returns[4]["5%"], result1_returns[4]["10%"]),
    "Stock 600000 Close": (result2_close[0], result2_close[1], result2_close[4]["1%"], result2_close[4]["5%"], result2_close[4]["10%"]),
    "Stock 600000 Returns": (result2_returns[0], result2_returns[1], result2_returns[4]["1%"], result2_returns[4]["5%"], result2_returns[4]["10%"]),
}, index=["ADF Stat", "P-Value", "Critical Values 1%", "Critical Values 5%", "Critical Values 10%"])

print(df)
```

## 3. 习题 3：配对资产平稳时间序列的构造
1. 使用 akshare 库获取两只具有一定相关性的股票（如 '601398.SH' 和 '601939.SH'）在 2023 年 1 月 1 日至 2023 年 12 月 31 日的收盘价数据。
2. 绘制这两只股票的价格走势图表，要求使用双坐标轴显示。
3. 对这两只股票的收盘价进行线性回归，计算对冲比。
4. 根据计算得到的对冲比构造平稳时间序列，并对该平稳时间序列进行 ADF 检验，输出检验结果。

```python
import akshare as ak
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import statsmodels.api as sm
from statsmodels.tsa.stattools import adfuller

# 获取股票数据
stock1_data = ak.stock_zh_a_hist(symbol='601398', start_date='20230101', end_date='20231231')
stock2_data = ak.stock_zh_a_hist(symbol='601939', start_date='20230101', end_date='20231231')

pair1 = stock1_data['收盘'].astype(float)
pair2 = stock2_data['收盘'].astype(float)
```

```python
# 绘制价格走势图表
plt.figure(figsize=(12, 6))
ax1 = plt.gca()
ax1.plot(pair1, label='601398.SH')
ax1.grid(False)
ax2 = ax1.twinx()
ax2.plot(pair2, label='601939.SH', color='orange')
ax2.grid(False)
lines, labels = ax1.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()
ax1.legend(lines + lines2, labels + labels2, loc='best')
plt.title('Stock 601398.SH and 601939.SH Price Trends')
plt.show()
```

```python
# 计算对冲比
def hedge_ratio(price1, price2):
    # BEGIN SOLUTION
    X = sm.add_constant(price1)
    model = sm.OLS(price2, X).fit()
    # END SOLUTION
    return model.params[1]
    
hr = hedge_ratio(pair1, pair2)
print(f"hedge_ratio为:{hr:.2f}")
```

```python
# 构造平稳时间序列
spreads = pair2 - pair1 * hr
```

```python
# 进行 ADF 检验
result = adfuller(spreads)
if result[1] < 0.05:
    print(f"p-value: {result[1]:.2f} < 0.05, 平稳序列")
else:
    print(f"p-value: {result[1]:.2f} > 0.05 非平稳序列")
```

## 4. 习题4：聚类与协整
问：聚类与协整是什么关系？为什么在聚类之后，还要再进行一次协整检验，才能确定协整对？不可以直接应用聚类的结果两两配对构建策略吗？
答：聚类与协整是两个不同的概念。

找出一个强势股，通过聚类后，观察它们起涨前有何共同点。

在课程示例中，实现以下功能：

1. 在开仓时，选择half_life_time最小的，Husrt最小的和偏离值最大的
2. 平仓后，重新评估，看能否换一个标的开仓

```python
# BEGIN SOLUTION
import akshare as ak
import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
from statsmodels.tsa.stattools import coint
import matplotlib.pyplot as plt


# 计算 half_life_time
def half_life_time(spread):
    lag = spread.shift(1)
    delta = spread - lag
    lag = lag[1:]
    delta = delta[1:]
    model = np.polyfit(lag, delta, 1)
    halflife = -np.log(2) / model[0]
    return halflife


# 计算 Hurst 指数
def hurst(ts):
    lags = range(2, 100)
    tau = [np.sqrt(np.std(np.subtract(ts[lag:], ts[:-lag]))) for lag in lags]
    poly = np.polyfit(np.log(lags), np.log(tau), 1)
    return poly[0] * 2.0


# 获取股票数据
def get_stock_data(symbols, start_date, end_date):
    data_dict = {}
    for symbol in symbols:
        try:
            df = ak.stock_zh_a_hist(symbol=symbol, start_date=start_date, end_date=end_date)
            if not df.empty:
                df['日期'] = pd.to_datetime(df['日期'])
                df.set_index('日期', inplace=True)
                data_dict[symbol] = df['收盘']
        except Exception as e:
            print(f"Error getting data for {symbol}: {e}")
    return pd.DataFrame(data_dict)


# 聚类函数
def perform_clustering(data, n_clusters=2):
    kmeans = KMeans(n_clusters=n_clusters)
    kmeans.fit(data.T)
    labels = kmeans.labels_
    clusters = {}
    for i, label in enumerate(labels):
        if label not in clusters:
            clusters[label] = []
        clusters[label].append(data.columns[i])
    return clusters


# 主函数
def main():
    # 股票代码列表
    symbols = ['600000', '600004', '600006', '600007', '600008']
    start_date = '20230101'
    end_date = '20231231'

    # 获取股票数据
    stock_prices = get_stock_data(symbols, start_date, end_date)
    stock_prices.dropna(axis=1, inplace=True)

    # 聚类
    clusters = perform_clustering(stock_prices)

    # 存储每个聚类的结果
    cluster_results = {}
    for cluster, stocks in clusters.items():
        cluster_results[cluster] = []
        for i in range(len(stocks)):
            for j in range(i + 1, len(stocks)):
                stock1 = stock_prices[stocks[i]]
                stock2 = stock_prices[stocks[j]]
                spread = stock1 - stock2
                hl = half_life_time(spread)
                hurst_index = hurst(spread)
                deviation = np.abs(spread.iloc[-1] - spread.mean())
                cluster_results[cluster].append({
                    'pair': (stocks[i], stocks[j]),
                    'half_life_time': hl,
                    'hurst': hurst_index,
                    'deviation': deviation
                })

    print("-----------cluster_results-----------")
    print(cluster_results.items())
    
    # 开仓逻辑
    current_position = None
    for cluster, results in cluster_results.items():
        sorted_results = sorted(results, key=lambda x: (x['half_life_time'], x['hurst'], -x['deviation']))
        print("sorted_results:",sorted_results)
        best_pair = sorted_results[0]['pair']
        if current_position is None:
            current_position = best_pair
            print(f"Opened position on {current_position}")

    # 模拟平仓和重新评估
    for _ in range(5):  # 模拟 5 次交易周期
        # 模拟平仓
        if current_position is not None:
            print(f"Closed position on {current_position}")
            current_position = None

        # 重新评估开仓
        for cluster, results in cluster_results.items():
            sorted_results = sorted(results, key=lambda x: (x['half_life_time'], x['hurst'], -x['deviation']))
            best_pair = sorted_results[0]['pair']
            if current_position is None:
                current_position = best_pair
                print(f"Opened position on {current_position}")


if __name__ == "__main__":
    main()
# END SOLUTION
```