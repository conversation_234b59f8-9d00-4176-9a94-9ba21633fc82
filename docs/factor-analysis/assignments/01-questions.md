None
# 如何访问数据

本练习的目标是帮你熟悉在课程环境下，如何访问数据。对应的文档是 supplements/data.ipynb。

## 1. 如何获取日线行情数据？

在本环境中，我们提供了从2005年到2023年底的全部日线数据，通过 load_bars 访问。
### 1.1. 随机获取N支股票数据



[答案参考：01.ipynb的单元格solution-cell-8](../answers/01.ipynb#solution-cell-8)

```{.python .input}
# 目标： 熟悉 load_bars 随机获取股票数据
# 要求： 随机获取 5 支个股，2022年到2023年底的日线数据，返回的结果存放在变量 df 中,显示最后10行
start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 12, 29)

# BEGIN SOLUTION
# 请按要求补全代码，并删除抛出异常的代码行。
raise NotImplementedError
# END SOLUTION

```

### 1.2. 获取指定股票池数据



[答案参考：01.ipynb的单元格solution-cell-8](../answers/01.ipynb#solution-cell-8)

```{.python .input}
# 目标： load_bars 获取指定股票池数据
# 要求： 获取 ("000001.XSHE", "600000.XSHG") 从2022年到2023年的全部日线数据，返回结果存放在变量 df 中，显示最后5行。

start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 12, 29)
universe = (
    "000001.XSHE",
    "600000.XSHG",
)

# BEGIN SOLUTION
# 请按要求补全代码，并删除抛出异常的代码行。
raise NotImplementedError
# END SOLUTION

```

## 2. 探索返回数据格式
### 2.1. 获取某一天的行情数据



[答案参考：01.ipynb的单元格solution-cell-8](../answers/01.ipynb#solution-cell-8)

```{.python .input}
# 目标: 掌握 pandas 多重索引和 xs 方法
# 要求：从前面生成的 df 中获取2023年12月25日这一天的行情数据

# 你可以把这里的时间换成 datetime.date 试一试，从而更好地理解 df 的数据结构
frame = datetime.datetime(2023, 12, 25)

# BEGIN SOLUTION
# 请按要求补全代码，并删除抛出异常的代码行。
raise NotImplementedError
# END SOLUTION

```

### 2.2. 获取某支股票的所有数据



[答案参考：01.ipynb的单元格solution-cell-8](../answers/01.ipynb#solution-cell-8)

```{.python .input}
# 目标: 掌握 pandas 多重索引和 xs 方法
# 要求： 从前面生成的 df 中，获取 000001.XSHE 的所有数据，保存在 df_01中

code = "000001.XSHE"

# BEGIN SOLUTION
# 请按要求补全代码，并删除抛出异常的代码行。
raise NotImplementedError
# END SOLUTION

```

### 2.3. 理解 price 数据

为方便因子分析，在返回数据中，我们特别增加了 price 一列，它就是 df.open[1:]。请用一行代码验证这个结论。



[答案参考：01.ipynb的单元格solution-cell-8](../answers/01.ipynb#solution-cell-8)

```{.python .input}
# BEGIN SOLUTION
# 请按要求补全代码，并删除抛出异常的代码行。
raise NotImplementedError
# END SOLUTION

```

## 3. 获取行业分类数据

练习使用 load_sectors 来获取行业分类数据。



[答案参考：01.ipynb的单元格solution-cell-8](../answers/01.ipynb#solution-cell-8)

```{.python .input}
# 目标： 获取行业分类数据，保存在 sectors 变量中, 并且显示最后5行

# BEGIN SOLUTION
# 请按要求补全代码，并删除抛出异常的代码行。
raise NotImplementedError
# END SOLUTION

```

## 4. 获取基本面数据

练习使用 fetch_daily_basic 来获取基本面数据，分析返回的结果。请自行查阅 tushare 文档，以了解各字段的含义。



[答案参考：01.ipynb的单元格solution-cell-8](../answers/01.ipynb#solution-cell-8)

```{.python .input}
# 获取 2023年12月29日，所有个股的基本面数据，保存到 fundamentals 变量中

# BEGIN SOLUTION
# 请按要求补全代码，并删除抛出异常的代码行。
raise NotImplementedError
# END SOLUTION

```

### 4.1. 获取个股的基本面数据

在刚刚得到的 fundamentals 表格中，查找个股的数据。



[答案参考：01.ipynb的单元格solution-cell-8](../answers/01.ipynb#solution-cell-8)

```{.python .input}
# 获取 603878 这支股票的 PE 值， 换手率，流通市值

# BEGIN SOLUTION
# 请按要求补全代码，并删除抛出异常的代码行。
raise NotImplementedError
# END SOLUTION

```

---

<b>版权声明</b><br>本课程全部文字、图片、代码、习题等所有材料，除声明引用外，版权归<b>匡醍</b>所有。所有草稿版本均通过第三方git服务进行管理，作为拥有版权的证明。未经作者书面授权，请勿引用和传播。联系我们：公众号
Quantide
