# 第 2 课习题

## 1. 市盈率和分位数

### 1.1. 研究上证的市盈率

要求：使用 akshare API 获取上证指数的市盈率，分析上证指数市盈率的中位数，25%分位和75%分位。

问题：2024年9月，沪指处在哪个位置？

```python
import akshare as ak

# 使用 akshare API 获取上证指数的市盈率，将日期设置为索引，重命名为 date, 列名字使用 price, pe
# BEGIN SOLUTION
pe = ak.stock_market_pe_lg(symbol="上证")
pe.set_index("日期", inplace=True)
pe.index.name = "date"
pe.rename(columns={"平均市盈率": "pe", "指数": "price"}, inplace=True)
pe.tail(15)
# END SOLUTION
```

```python
# ** 测试 ***
### BEGIN HIDDEN TESTS
assert len(pe) == 316
assert np.isclose(pe.loc[datetime.date(2024,8,30), "price"], 2842.21)
assert np.isclose(pe.loc[datetime.date(2024,8,30), "pe"], 12.16)
### END HIDDEN TESTS
```

![50%](https://images.jieyu.ai/images/2025/04/001.png)

注：本课程参考的是2025年4月之前的数据。

### 1.2. 找出市盈率的 25%， 50% 和 75% 分位

```python
# 分析 pe 的25%， 50%和75%分位，保存在数组 percentiles 中

# BEGIN SOLUTION
percentiles = []
for i in range(1, 4):
    percentiles.append(pe["pe"].quantile(i/4))

percentiles
# END SOLUTION
```

```python
# ** 测试 **
# np.testing.assert_array_almost_equal(percentiles, [13.915, 17.2, 33.0725], decimal=2)
```

![](https://images.jieyu.ai/images/2025/04/002.png)

注：本课程参考的是2025年4月之前的数据。

### 1.3. 2024年8月，上证处于什么位置？

```python
# 请通过2024年8月的上证指数，反求它的历史分位数。

# BEGIN SOLUTION
rank = pe.rank().loc[datetime.date(2024,8,30), "pe"]
percentile = rank / len(pe)
percentile
# END SOLUTION
```

所以，仅根据统计数据看，在2024年8月底，沪指是高还是低？

如果仅根据统计数据来看，显然沪指在2024年8月底，是被低估了，换句话说，这里出现了买入机会。但是，分位数是静态的，它不能反映数据的运行趋势。

请运行下面的代码，并认真回答问题。

```python
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

fig, ax1 = plt.subplots(figsize=(60,6))

color = "tab:blue"
ax1.plot(pe["price"], label="Index", color=color)
ax1.set_xlabel("Year")
ax1.set_ylabel("Index", color="tab:blue")
ax1.xaxis.set_major_locator(mdates.MonthLocator(bymonth=[2, 5, 8, 11]))
ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m')) 

color = "tab:red"
ax2 = ax1.twinx()
ax2.plot(pe["pe"], label="PE", color=color)
ax2.set_ylabel("PE", color=color)

for i in range(1, 4):
    quantile = pe["pe"].quantile(i/4)
    ax2.axhline(quantile, color='gray', linestyle='--', label=f"{i/4:02.0%}")

plt.title("Index vs PE")

fig.tight_layout()
for date in pe.index[::3]:
    ax1.axvline(date, color='gray', linestyle=':', linewidth=0.5)
plt.gcf().autofmt_xdate()

plt.legend(loc="upper left")
plt.show()
```

**思考：**

* 从沪指与PE的总体趋势来看，它们存在怎样的相关性？反映了一种什么样的现象？
* 在哪些阶段，PE走势与沪指完全一致？在这种走势中，谁是因变量，谁是自变量？它反应什么样的现象？
* 大约从2023年8月起，似乎可以看出，沪指的下降速度快于PE的下降速度。比如，2024年2月，沪指出现阶段低点，PE也出现阶段低点。但2024年9月，沪指创新低之后，PE并没有创新低。这种背离反映了什么现象？能否用一个常见的术语来表述它？
* 从上图中，你还可以发现哪些异象？能得出什么结论？

**思考：**

```md
<!-- MANUAL GRADE QUESTION 4 points -->
1. 从沪指与PE的总体趋势来看，它们存在怎样的相关性？反映了一种什么样的现象？
2. 在哪些阶段，PE走势与沪指完全一致？在这种走势中，谁是因变量，谁是自变量？它反应什么样的现象？
3. 大约从2023年8月起，似乎可以看出，沪指的下降速度快于PE的下降速度。比如，2024年2月，沪指出现阶段低点，PE也出现阶段低点。但2024年9月，沪指创新低之后，PE并没有创新低。这种背离反映了什么现象？能否用一个常见的术语来表述它？
4. 从上图中，你还可以发现哪些异象？能得出什么结论？
```

**你的答案**

```md
<!-- MANUAL GRADED ANSWER -->
* 从1999年看起，总体上存在沪指上涨，PE下降的趋势。反映了由于资产供应的绝对量增加，资产泡沫不断被挤出的趋势。
* 在1999年到2001年，2005年8月到2008年5月，以及2014年5月到1015年5月等时间段PE的走势与沪指完全一致。在这种走势中，由于市场炒作十分激烈，公司盈利能力的变化相对于股价的变化而言，完全可以忽略不计，PE的走势完全由股价决定，从而PE曲线与股价走势极为相似。一旦出现这种现象，就说明市场已经过度投机了。
* 在2024年9月，沪指创新低之后，PE并没有创新低，出现了背离。这种情况其实从峰值分析也可以看出来。比如，2024年6月，PE值创2023年8月以来的新高，但沪指却低于2023年8月后任何一个高点。这种背离可以用低市盈率陷阱来描述。这个指标提醒我们，当前指数虽然在下跌，但上市公司的盈利能力也可能在下降，从而出现指数小涨，PE大涨；指数跌得多，PE跌得少的情况。
```

## 2. 缺失值填充

在课程中我们提到， 有一些公司财务数据会有缺值，这种情况下，可以drop掉，也可以使用行业中值来进行替换。

请实现如何使用行业中值来进行替换。

这里我们将复习之前练习过的 load_sectors 和 fetch_daily_basic 这两个函数。

```python
from functools import partial

date = datetime.date(2023,12,29)
sectors = load_sectors()
fundamentals = fetch_daily_basic(date)

# 按行业进行归类，求均值。

# 首先，生成一个新的 dataframe，将行业分类附加到此dataframe上。变量名使用 df
# BEGIN SOLUTION
df = fundamentals.join(sectors)
df.drop("trade_date", axis=1, inplace=True)
# END SOLUTION

# 对新生成后的 df，按 sectors 进行 groupby, 得到行业均值， 保存在 sector_mean 中
# BEGIN SOLUTION
sector_mean = df.groupby("sector").mean()
# END SOLUTION

# 现在，对 df 中的缺值进行替换
def replace_with_mean(col, row):
    try:
        if pd.isna(row[col]):
            return sector_mean.loc[row.sector, col]
        else:
            return row[col]
    except Exception:
        return None
        
numeric_columns = df.select_dtypes(include=[np.number]).columns

# 请使用上面的 replace_with_mean 函数，只使用一个循环，实现全表的缺失值替换
# BEGIN SOLUTION
for col in numeric_columns:
    df[col] = df.apply(partial(replace_with_mean, col), axis=1)
# END SOLUTION
df
```

```python
# ** 验证 **
assert np.isclose(df.loc["688307.XSHG", "dv_ratio"], sector_mean.loc["计算机", "dv_ratio"], 2)
```