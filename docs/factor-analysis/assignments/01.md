# 如何访问数据

本练习的目标是帮你熟悉在课程环境下，如何访问数据。对应的文档是 supplements/data.ipynb。

## 1. 如何获取日线行情数据？

在本环境中，我们提供了从2005年到2023年底的全部日线数据，通过 load_bars 访问。
### 1.1. 随机获取N支股票数据

```python
# 目标： 熟悉 load_bars 随机获取股票数据
# 要求： 随机获取 5 支个股，2022年到2023年底的日线数据，返回的结果存放在变量 df 中,显示最后10行
start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 12, 29)

# BEGIN SOLUTION
df = load_bars(start, end, 5)
df.tail(10)
# END SOLUTION
```

### 1.2. 获取指定股票池数据

```python
# 目标： load_bars 获取指定股票池数据
# 要求： 获取 ("000001.XSHE", "600000.XSHG") 从2022年到2023年的全部日线数据，返回结果存放在变量 df 中，显示最后5行。

start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 12, 29)
universe = (
    "000001.XSHE",
    "600000.XSHG",
)

# BEGIN SOLUTION
df = load_bars(start, end, universe)
df.tail()
# END SOLUTION
```

## 2. 探索返回数据格式
### 2.1. 获取某一天的行情数据

```python
# 目标: 掌握 pandas 多重索引和 xs 方法
# 要求：从前面生成的 df 中获取2023年12月25日这一天的行情数据

# 你可以把这里的时间换成 datetime.date 试一试，从而更好地理解 df 的数据结构
frame = datetime.datetime(2023, 12, 25)

# BEGIN SOLUTION
df.xs(frame, level="date")
# END SOLUTION
```

### 2.2. 获取某支股票的所有数据

```python
# 目标: 掌握 pandas 多重索引和 xs 方法
# 要求： 从前面生成的 df 中，获取 000001.XSHE 的所有数据，保存在 df_01中

code = "000001.XSHE"

# BEGIN SOLUTION
df_01 = df.xs(code, level="asset")
df_01
# END SOLUTION
```

### 2.3. 理解 price 数据

为方便因子分析，在返回数据中，我们特别增加了 price 一列，它就是 df.open[1:]。请用一行代码验证这个结论。

```python
# BEGIN SOLUTION
np.allclose(df_01.open[1:], df_01.price[:-1])
# END SOLUTION
```

## 3. 获取行业分类数据

练习使用 load_sectors 来获取行业分类数据。

```python
# 目标： 获取行业分类数据，保存在 sectors 变量中, 并且显示最后5行

# BEGIN SOLUTION
sectors = load_sectors()
sectors.tail(5)
# END SOLUTION
```

## 4. 获取基本面数据

练习使用 fetch_daily_basic 来获取基本面数据，分析返回的结果。请自行查阅 tushare 文档，以了解各字段的含义。

```python
# 获取 2023年12月29日，所有个股的基本面数据，保存到 fundamentals 变量中

# BEGIN SOLUTION
date = datetime.date(2023, 12, 29)
fundamentals = fetch_daily_basic(date)
fundamentals.tail()
# END SOLUTION
```

### 4.1. 获取个股的基本面数据

在刚刚得到的 fundamentals 表格中，查找个股的数据。

```python
# 获取 603878 这支股票的 PE 值， 换手率，流通市值

# BEGIN SOLUTION
pe, turnover, mv = fundamentals.loc["603878.XSHG",["pe", "turnover_rate_f", "circ_mv"]]
pe, turnover, mv
# END SOLUTION
```
