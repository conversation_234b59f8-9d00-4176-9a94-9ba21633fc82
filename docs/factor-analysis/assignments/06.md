# 第 6 讲：Alphalens 高级技巧

这一章介绍的是 Alphalens 中的高级技巧。

这一讲的难点是理解索引的频率推断。索引有点类似于绘图中的轴。每一种轴、或者索引都有自己的逻辑，通过这种逻辑，能生成指定范围内的所有合法数值。索引并不是一组简单的，索引的频率与 pandas 中的 Timedelta 是既关联、又不相同的概念。索引的频率可以是秒、分钟、小时、天和月等，但 Timedelta 则只能是日及日以下级别。我们将在此提供一些这方面的练习。

在这一讲中，我们还比较详细地介绍了月线级别下的因子检验。我们把日内因子的检验作为练习题。

## 1. 索引的频率推断

在 Alphalens 中，远期收益的名字由参数 periods 推断而来。这里面用到了时间戳、时间跨度、日历等概念。我们通过此小小练习来熟悉这些概念。

首先我们从 freq 属性讲起。这是一个在 DatetimeIndex, Period, Timedelta 等对象中都存在的属性。它代表时间戳的精度，换句话说，就是从一个时间戳开始，到下一个时间戳结束时，会用去的时间。这个时间可能是秒，也可能是自然天，或者工作日。

在 Pandas 中，并没有正式的 frequency 的定义。Frequency 一词也几乎只在 pandas.tseries.frequencies 这个模块中出现过。很多时候，它是 offset 的别名。在 frequencies 这个模块中，最重要的方法是 infer_freq, get_period_alias,to_offset 等。get_period_alias, to_offset 将 Period, offset 和 frequency 等概念联系起来。

在 pandas.tseries 模块下，定义了各种 offset，比如 BusinessDay, BMonthEnd，CustomBusinessDay 等。在 DatetimeIndex, Period 等对象中，看到的 freq 属性，实际上就是这些对象的实例。

```python
# AUTOGRADE ANSWER
from IPython.core.interactiveshell import InteractiveShell
InteractiveShell.ast_node_interactivity = "all"

from pandas.tseries.offsets import BDay, Day, MonthEnd
from pandas.tseries.frequencies import infer_freq

# 要求：生成一个自然日序列的 DatetimeIndex，从 2024 年 9 月 25 日开始，到 10 月 5 日结束，保存在 dates 中
# BEGIN SOLUTION
dates = pd.date_range(start='2024-09-25', end='2024-10-5')
# END SOLUTION
dates

infer_freq(dates)

# 要求：请解释以下各行的输出
dates[2] + Day(1)
dates[2] + BDay(1)
dates[2] + MonthEnd(1)
```

```bash
# MANUAL GRADED ANSWER 1 point
dates[2] 对应着 2024 年 9 月 27 日。移动一个自然日，得到 9 月 28 日，移动一个工作日，得到 9 月 30 日。移动到月末，得到 9 月 30 日。

```

```python
# AUTOGRADE TEST 1 point
### AUTOTEST dates
```

**生成工作日序列**

```python
# AUTOGRADE ANSWER
# 要求：生成工作日序列的 DatetimeIndex，从 2024 年 9 月 25 日开始，到 10 月 5 日结束，保存在 bdates 中

# BEGIN SOLUTION
bdates = pd.bdate_range(start='2024-9-25', end='2024-10-5')
# END SOLUTION
bdates
```

```python
# AUTOGRADE TEST 1 point
### AUTOTEST bdates
```

在 Alphalens 中会根据传入的 factor 和 price 中的日期索引，推断出一个自定义的 trading calendar，该 calendar 是一个 CustomBusinessDay 类型。下面的练习将帮助你熟悉这个类型。

```python
# AUTOGRADE ANSWER

from pandas.tseries.offsets import CustomBusinessDay

holidays = ['2024-10-01', 
            '2024-10-02', 
            '2024-10-03', 
            '2024-10-04', 
            '2024-10-05', 
            '2024-10-06', 
            '2024-10-07'
        ]

# 以下请勿修改 freq 变量
freq = CustomBusinessDay(holidays=holidays, weekmask="Mon Tue Wed Thu Fri")
start = datetime.date(2024, 9, 25)
end = datetime.date(2024, 10, 15)

# 要求：生成一个 DatetimeIndex, 保存在 cdates 中，区间为 [start, end]，freq 为 freq
# BEGIN SOLUTION
cdates = pd.date_range(start, end, freq=freq)
# END SOLUTION
cdates

cdates[2]

# 要求：对 9 月 27，移动两个 CDay，赋值给 oct8
# BEGIN SOLUTION
oct8 = cdates[2] + freq * 2
# END SOLUTION

# 生成一个从 start 到 end 的因子
dates = pd.date_range(start, end)
factor = pd.Series(np.arange(len(dates)), index=dates)
factor

# 要求：对 factor 进行预处理，过滤掉非交易日的记录。
# 提示：请使用本段定义的变量 freq，用一行代码实现
# BEGIN SOLUTION
factor.asfreq(freq)
# END SOLUTION
```

```python
# AUTOGRADE TEST 2 points
### AUTOTEST cdates; factor; oct8
```

## 2. 日内因子检验

通过这个练习，帮助你熟悉 Alphalens 中的日内因子检验。

首先我们为日内因子检验生成一些合成数据。我们使用的方法是，先生成基础数据（日线），再在此基础上，构造日内数据。

请结合每段输出，自行分析程序的逻辑。

```python
# READONLY
from pandas import (DataFrame, date_range)
from numpy import nan

# 构造基础数据（日线）

start = '2024-9-25'
end = '2024-12-10'

price_index = date_range(start=start, end=end, freq=freq)

price_index.name = 'date'
tickers = ['A', 'B', 'C', 'D', 'E', 'F']
data = [[1.0025**i, 1.005**i, 1.00**i, 0.995**i, 1.005**i, 1.00**i]
        for i in range(1, 51)]
base_prices = DataFrame(index=price_index, columns=tickers, data=data)

base_prices.head()
```

```python
# READONLY
factor_end = datetime.date(2024, 11, 12)
factor_index = date_range(start, factor_end, freq=freq)
factor_index.name = 'date'
factor = DataFrame(index=factor_index, columns=tickers,
                   data=[[3, 4, 2, 1, nan, nan], [3, nan, nan, 1, 4, 2],
                         [3, 4, 2, 1, nan, nan], [3, 4, 2, 1, nan, nan],
                         [3, 4, 2, 1, nan, nan], [3, 4, 2, 1, nan, nan],
                         [3, nan, nan, 1, 4, 2], [3, nan, nan, 1, 4, 2],
                         [3, 4, 2, 1, nan, nan], [3, 4, 2, 1, nan, nan],
                         [3, nan, nan, 1, 4, 2], [3, nan, nan, 1, 4, 2],
                         [3, nan, nan, 1, 4, 2], [3, nan, nan, 1, 4, 2],
                         [3, nan, nan, 1, 4, 2], [3, nan, nan, 1, 4, 2],
                         [3, nan, nan, 1, 4, 2], [3, nan, nan, 1, 4, 2],
                         [3, nan, nan, 1, 4, 2], [3, nan, nan, 1, 4, 2],
                         [3, 4, 2, 1, nan, nan], [3, 4, 2, 1, nan, nan],
                         [3, 4, 2, 1, nan, nan], [3, 4, 2, 1, nan, nan],
                         [3, 4, 2, 1, nan, nan], [3, 4, 2, 1, nan, nan],
                         [3, 4, 2, 1, nan, nan], [3, 4, 2, 1, nan, nan],
                         [3, nan, nan, 1, 4, 2], [3, nan, nan, 1, 4, 2]])

factor.head()
```

接下来，我们构造日内数据价格数据：

```python
# AUTOGRADE ANSWER
today_open = base_prices.copy()

# 要求：将 today_open 索引的时间调整到 9:30
# BEGIN SOLUTION
today_open.index += pd.Timedelta('9h30m')
# END SOLUTION

# every day, after 1 hour from open all stocks increase by 0.1%
today_open_1h = today_open.copy()
today_open_1h.index += pd.Timedelta('1h')
today_open_1h += today_open_1h*0.001

# every day, after 3 hours from open all stocks decrease by 0.2%
today_open_3h = today_open.copy()
today_open_3h.index += pd.Timedelta('3h')
today_open_3h -= today_open_3h*0.002

# prices DataFrame will contain all intraday prices
prices = pd.concat([today_open, today_open_1h, today_open_3h]).sort_index()

prices.head()
```

```python
"""检查 prices 是否正确，1 point"""
### AUTOTEST prices.head(1); prices.tail(1)
```

现在，我们来练习如何求 periods = (7,) 时的实际经过的时间跨度。这也是 Alphalens 内部使用的机制。

```python
# 求 periods = 7 时的时间跨度

start = pd.to_datetime('2024-09-27 09:30')
istart = prices.index.get_loc(start)
iend = istart + 7
end = prices.index[iend]

# 现在，我们来求以交易日历计，start 与 end 之间隔了多久
# 要求：求出 start,end 之间实际的交易时长，保存在 delta 中
# 提示，请利用 date_range, Timedelta, Timedelta.components, freq.is_on_offset
# BEGIN SOLUTION
trading_days = pd.date_range(start, end, freq=freq).shape[0] - 1
if not freq.is_on_offset(start):
    trading_days -= 1

timediff = end - start
delta = pd.Timedelta(days=trading_days, hours = timediff.components.hours)
delta
# END SOLUTION
```

你得到的答案应该是 2 天 1 小时。Alphalens 会把这个时长转换为 2D1H，作为远期收益的列名。

```python
# AUTOGRADE TEST points=2
### AUTOTEST delta
```

现在，我们将因子与价格对齐，并生成 Alphalens 要求的格式：

```python
# AUTOGRADE ANSWER

# 要求 1： 将因子时间对齐到每日上午 9:30
# 要求 2： 将索引名设置为 date 和 asset

# BEGIN SOLUTION
factor.index += pd.Timedelta('9h30m')
factor = factor.stack()
factor.index = factor.index.set_names(['date', 'asset'])
# END SOLUTION
```

```python
"""检查 factor 是否正确，1 point"""
### AUTOTEST factor.head(1); factor.tail(1)
```

因子预处理：

```python
factor_data = get_clean_factor_and_forward_returns(
    factor,
    prices,
    quantiles=4,
    periods=(1, 2, 3, 7), 
    filter_zscore=None)
factor_data.tail()
```

请回答以下问题：

1. factor 的索引的 freq 是什么？
2. 当传入 periods 为 1，2，3，7 时，为何远期收益的列名字分别为 1h, 3h，1D 和 2D1h？
3. 在日内因子计算时，factor 的索引数远少于 prices 的索引数，为何没有引起 MaxLossExceedError?

```bash
# MANUAL GRADED ANSWER points=1

1. factor 的索引的 freq 是'D'。这并不是常规情况。
2. periods 参数是没有单位的。在内部，Alphalens 算得 1 期、2 期、3 期和 7 期的 Timedelta 为 1 小时、3 小时、1 天和 2 天 1 小时，然后格式化为 1h, 3h, 1D 和 2D1h。
3. MaxLoss 是根据因子的 index 的 size 来计算的。如果在分层和远期收益对齐时，所有的因子数据都利用起来，就不会有 loss。
```

最后，我们看一下最终分析结果。

```python
create_full_tear_sheet(factor_data, long_short=False)
```

要注意的是，这里的年化收益仍然是错的。如果这个值很重要，我们可以手工纠正它。Alphalens 的错误在于，在给定的示例中，一天只有 3 小时的交易时长，因此，一天只能交易 3 次；全年只能交易 252 * 3 次。而 Alphalens 是这样计算交易次数的：

```python
# READONLY
trades = pd.Timedelta("252Days") / pd.Timedelta("1h")
trades
```

现在，请你出手来纠正 Alphalens 的错误！

```python
alphalens_ann_1h = 420.986
wrong_ann_1h_factor =  pd.Timedelta("252Days") / pd.Timedelta("1h")

# 请计算出正确的年化因子，保存为 correct_ann_1h_factor
# BEGIN SOLUTION
correct_ann_1h_factor =  pd.Timedelta("252Days") / pd.Timedelta("1h") / 8
# END SOLUTION

# 现在，我们由 alphalens_ann_1h 反推 1h 的收益率，保存在 ret_1h 中
# BEGIN SOLUTION
ret_1h = alphalens_ann_1h ** (1/wrong_ann_1h_factor) - 1
# END SOLUTION

# 现在，我们将 1h 收益率年化，结果保存在 correct_ann_1h 中
# BEGIN SOLUTION
correct_ann_1h = (1 + ret_1h) ** correct_ann_1h_factor - 1
# END SOLUTION
```

```python
# AUTOGRADE TEST points=2
### AUTOTEST correct_ann_1h
```

