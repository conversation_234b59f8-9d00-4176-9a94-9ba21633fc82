# 第18课 LightGBM

## 1. 决策树总览

在课程中我们讲到，可以通过plot_tree的方法，绘制出单棵树的决策过程，但是，lightgbm也提供了一个trees_to_dataframe的方法，可以将树的信息转换成为dataframe，方便后续分析。

现在，我们就来实现这一功能。

<!--这部分使用成人收入数据来训练一个模型-->

<!--这里实现单棵树的信息提取，最终合并成为一个dataframe-->

```python
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
import lightgbm as lgb

# 加载数据集
url = "/data/ro/adult.data"
column_names = [
    "age", "workclass", "fnlwgt", "education", "education-num", "marital-status",
    "occupation", "relationship", "race", "sex", "capital-gain", "capital-loss",
    "hours-per-week", "native-country", "income"
]
data = pd.read_csv(url, header=None, names=column_names, na_values=" ?", skipinitialspace=True)

# 处理缺失值
imputer = SimpleImputer(strategy='most_frequent')
data_imputed = pd.DataFrame(imputer.fit_transform(data), columns=data.columns)

# 确保数值列的数据类型正确
numerical_features = ["age", "fnlwgt", "education-num", "capital-gain", "capital-loss", "hours-per-week"]
data_imputed[numerical_features] = data_imputed[numerical_features].astype(float)

# 编码类别型特征
categorical_features = [
    "workclass", "education", "marital-status", "occupation", "relationship",
    "race", "sex", "native-country"
]

label_encoder = LabelEncoder()
for feature in categorical_features:
    data_imputed[feature] = label_encoder.fit_transform(data_imputed[feature])

# 编码目标变量
data_imputed['income'] = label_encoder.fit_transform(data_imputed['income'])

# 划分训练集和测试集
X = data_imputed.drop('income', axis=1)
y = data_imputed['income']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建 LightGBM 数据集
train_data = lgb.Dataset(X_train, 
                         label=y_train, 
                         categorical_feature=categorical_features,
                         free_raw_data=False)
test_data = lgb.Dataset(X_test, 
                        label=y_test, 
                        categorical_feature=categorical_features, 
                        reference=train_data,
                        free_raw_data=False)

# 设置参数
params = {
    'objective': 'binary',
    'metric': ['binary_logloss', 'auc'],
    'boosting_type': 'gbdt',
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.9,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': 1
}

# 定义 early stopping 回调函数
early_stopping_rounds = 10
evals_result = {}

num_rounds = 1000
bst = lgb.train(
    params,
    train_data,
    num_boost_round=num_rounds,
    valid_sets=[test_data],
    feval=None,
    init_model=None,
    feature_name='auto',
    categorical_feature=categorical_features,
    callbacks=[lgb.early_stopping(stopping_rounds=early_stopping_rounds),
                lgb.record_evaluation(evals_result)]
)

# 提取树信息并转换为 DataFrame
tree_df = bst.trees_to_dataframe()

# 显示前几行数据
print(tree_df.head())
```


## 2. 完成下面的代码，实现forward forecasting验证

forward forecasting是我们在第16课中讲到的概念。比起k-fold等交叉验证方法，它更适用于时间序列预测问题。现在，请完成此习题，同时实现参数优化和交叉验证。

```python
# BEGIN SOLUTION
import numpy as np
from sklearn.datasets import make_classification
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import GridSearchCV
import matplotlib.pyplot as plt

# 生成类别不平衡的三分类数据集
X, y = make_classification(
    n_samples=500,        # 样本总数
    n_features=10,         # 特征数量
    n_informative=5,      # 有用的特征数量
    n_redundant=5,         # 冗余特征数量
    n_classes=3,           # 类别数量
    weights=[0.05, 0.3, 0.65], # 类别不平衡比例
    flip_y=0.01,           # 标签噪声比例
    random_state=42        # 随机种子
)

# 定义初始窗口大小
initial_window = 100

# 初始化预测值和实际值列表
predictions = []
actuals = []

# 定义要优化的参数
param_grid = {
    'C': [0.1, 1, 10],
    'penalty': ['l1', 'l2']
}

for i in range(initial_window, len(X)):
    X_train = X[:i]
    y_train = y[:i]
    X_test = X[i:i+1]
    y_test = y[i:i+1]

    # 创建 LogisticRegression 模型
    model = LogisticRegression(max_iter=200)

    # 使用 GridSearchCV 进行参数优化
    grid_search = GridSearchCV(model, param_grid, cv=3)
    grid_search.fit(X_train, y_train)
    
    # 获取最优模型
    best_model = grid_search.best_estimator_
    
    # 进行预测
    y_pred = best_model.predict(X_test)
    
    predictions.append(y_pred[0])
    actuals.append(y[i])

# 计算准确率
correct_predictions = np.sum(np.array(predictions) == np.array(actuals))
accuracy = correct_predictions / len(predictions)
print(f"Accuracy: {accuracy}")

# 可视化预测结果和实际结果
plt.plot(actuals, label='Actual')
plt.plot(predictions, label='Predicted')
plt.xlabel('Time Step')
plt.ylabel('Class')
plt.title('Forward Forecasting Validation')
plt.legend()
plt.show()
# END SOLUTION
```
