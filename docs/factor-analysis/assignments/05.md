# 第 5 讲 Alphalens 报表分析习题

<!-- TODO: 将此题移动到第 4 章 -->

## 1. 行业中性化

在课程中，我们没有演示如何实现行业中性化。这部分我们留到习题中。

在 Alphalens 中，行业中性化是通过在 get_clean_factor_and_forward_returns 中设置 groupby, groupby_labels 参数，并在 create_full_tear_sheet 中设置 group_neutral=True 与 by_group=True 来实现的。

现在，我们就来练习如何实现这个功能。这个练习共有以下步骤：

1. 读取行业分类数据，并转化为整数编码。
2. 将行业分类数据附加到 factor 中。
3. 调用 Alphalens，获得行业中性化的相关报表。

我们在第 4 章行业中性化一节中，提到：

```md
```` {note}
我们注意到，这个表格中，sector 一列使用的是整数作为标签，这方便了 Alphalens 运算，但是会导致生成的报告中，也要使用这些意义不明的数字。因此，Alphalens 允许我们通过 groupby_labels 参数，为 sector 一列提供人类可读的标签。
````
```

我们从数据源得到的行业分类数据往往并非整数编码，而是字符串格式。如果我们直接传入字符串列，会导致 Alphalens 报告出错。

因此，我们首先练习如何将行业分类数据转化为整数编码。

```python
# AUTOGRADE ANSWER
def get_encoded_sectors():
    sectors = load_sectors()
    unique_sectors = sectors.unique()

    # 请生成 label_to_cat（字典）, 保存行业标签（字符串）到行业编码（整数）的映射
    # 请生成 cat_to_label（字典）, 它刚好是 label_to_cat 的逆，即以 label_to_cat 的值为键，
    # 以它的键为值，这个字典将用于生成报告时，实现行业编码到行业名称的映射。
    # 生成 encoded_sectors，它的键与 sectors.to_dict() 相同，但其值已转化为行业编码。
    # 行业编码方案可以任意，但在一次因子分析中，该方案必须前后保持一致。

    # BEGIN SOLUTION
    label_to_cat = {value: i for i, value in enumerate(unique_sectors)}
    cat_to_label = {v:k for k,v in label_to_cat.items()}
    encoded_sectors = {k:label_to_cat[v] for k,v in sectors.to_dict().items()}
    # END SOLUTION

    return encoded_sectors, cat_to_label
```

```python
# TEST, 2 points
sectors, cat_to_label = get_encoded_sectors()
sector = cat_to_label[sectors["000001.XSHE"]]
assert isinstance(sectors["000001.XSHE"], int), "sector should be encoded as integer"
assert sector == "银行"
```

第二步，我们给 factor 表附加行业信息。为此我们先要生成因子数据 -- factor。

```python
# READ ONLY
from numpy.lib.stride_tricks import as_strided
def rolling_slope(close: NDArray, win:int, *args):
    if len(close) < win:
        return np.full((len(close), ), np.nan)

    # 将 CLOSE 转换为滑动窗口二维数组，保存在 TRANSFORMED 变量中
    stride = close.strides
    shape = (len(close) - win + 1, win)
    strides = stride + stride
    transformed = as_strided(close, shape, strides)
    
    slopes, _ = np.polyfit(np.arange(win), transformed.T, deg=1)

    # POLYFIT 返回的一维数组长度不及 CLOSE, 需要左补全（用 NP.NAN 填充）
    left_pad_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_pad_len, 0), mode='constant', constant_values=np.nan)
    return slopes

def wrapper(group):
    slopes = rolling_slope(group["close"].to_numpy(), 10)

    # 将 SLOPES 转换为 DATAFRAME, 以日期为索引，列名为 FACTOR，保存为 DF
    index = group.index.get_level_values(0)
    df = pd.DataFrame(slopes, index=index, columns=["factor"])
    return df

# 1. 获取行情数据
start = datetime.date(2023, 7, 1)
end = datetime.date(2023, 12, 29)
universe = ("000001.XSHE", "000002.XSHE", "000004.XSHE", "600000.XSHG")
barss = load_bars(start, end, universe=universe)

factor = barss.groupby(level='asset').apply(wrapper)
```

运行上述单元格后，我们得到了 factor 变量。现在，我们给它增加行业编码列。

```python
# AUTOGRADE ANSWER
from functools import partial
def add_sector_info(factor, encoded_sectors):
    def to_cat(encoded_sectors, g):
        # 要求：利用encoded_sectors表，将传入的group转换为行业编码
        # HINT: 你可能需要进行索引调整
        # BEGIN SOLUTION
        df = pd.DataFrame(g).droplevel("asset")
        df["sector"] = encoded_sectors[g.name]
        # END SOLUTION
        df.columns = ["factor", "sector"]
        
        return df

    # 要求： 利用groupby.apply，调用to_cat函数，实现行业编码转换
    # Hint：你可能要利用partial函数
    # BEGIN SOLUTION
    df = factor.groupby("asset").apply(partial(to_cat, encoded_sectors))
    # END SOLUTION
    return df.swaplevel(0,1)
```

```python
# AUTO TEST 4 points
factor_with_sector = add_sector_info(factor, sectors)

cat = factor_with_sector.loc[('2023-12-29', "000001.XSHE"), "sector"]
assert cat_to_label[cat] == "银行"
factor_with_sector.tail()
```

我们看到，factor_with_sector 与 factor 相比，多了一行，该列的值都是整数。现在，我们来调用 Alphalens，获得行业中性化的相关报表。

```python
# AUTOGRADE ANSWER
from alphalens.tears import create_information_tear_sheet
from alphalens.utils import get_clean_factor_and_forward_returns

prices = barss['price'].unstack(level=1)
merged_factor = get_clean_factor_and_forward_returns(factor_with_sector["factor"], prices, groupby=factor_with_sector["sector"], groupby_labels=cat_to_label)

create_information_tear_sheet(merged_factor, by_group=True, group_neutral=True)
```

在输出的最后部分，你将看到 Information Coefficient By Group 报表。你也可以把 create_information_tear_sheet 换成熟悉的 create_full_tear_sheetb 函数。

恭喜！你现在已经完全掌握了 Alphalens 中行业中性化。这段代码可以在工作复用，你唯一需要做的事情就是，根据你的数据源，提供 load_sectors 实现！

## 2. 计算换手率

因子分层换手率是操作实务中的一个概念，介绍它的实现的文章很少。要了解清楚这个概念，最好是自己实现一遍。

下面的代码是 Alphalens 中计算因子分层换手率的实现。这段代码比较有技巧，对于我们熟练掌握 Pandas 非常有帮助。

```python
# AUTO GRADE ANSWER
def quantile_turnover(quantile_factor: pd.Series, quantile: int, period=1):
    quant_names = quantile_factor[quantile_factor == quantile]

    # 要求：生成 quantile 对应的每日资产列表，保存在变量 quant_name_sets 中，该变量是 Series 类型
    # 以日期为索引，以每日资产集合 (set 类型）为值

    # BEGIN SOLUTION
    quant_name_sets = quant_names.groupby(level=['date']).apply(
        lambda x: set(x.index.get_level_values('asset')))
    # END SOLUTION

    # 将 quant_name_sets 进行移位，使得我们在 T1 期索引上，也能得到 T0 期的资产集
    # 结果保存在 name_shifted 中
    name_shifted = quant_name_sets.shift(period)

    # 要求：找出 T1 期和 T0 期的差集，得到每一期要调入的新资产，保存在 new_names 中
    # new_names 的类型仍为 Series 类型
    # 练习两个 Series 如何求差集，这是非常棒的技巧。

    # BEGIN SOLUTION
    new_names = (quant_name_sets - name_shifted).dropna()
    # END SOLUTION

    # 要求：请使用两个 apply，一行代码实现 turnover 计算。注意：这个技巧会很常用。
    # 将计算结果保存在 quant_turnover 变量中，该变量将是 DataFrame 类型
    # 提示：通过 new_names, quant_name_sets 求每日换手率

    # BEGIN SOLUTION
    quant_turnover = new_names.apply(
        lambda x: len(x)) / quant_name_sets.apply(lambda x: len(x))
    # END SOLUTION
    
    # 将 quantile 的值作为 Series 的 Axis 标签
    quant_turnover.name = quantile
    return quant_turnover
```

下面的代码用来生成数据和调用我们刚实现的方法。

```python
# AUTO GRADE SOLUTION
start = datetime.date(2023, 7, 1)
end = datetime.date(2023, 12, 29)
universe = 1000

np.random.seed(78)

barss = load_bars(start, end, universe=universe)

factor = barss.groupby(level='asset').apply(wrapper)
factor = factor.swaplevel(0,1)
prices = barss.price.unstack(level="asset")

merged_factor = get_clean_factor_and_forward_returns(factor, prices)
merged_factor.tail()

turnovers = {}
for q in range(1, 6):
    # 要求：调用 quantile_turnover，结果保存到 turnover 中。本练习帮助你理解 quantile_turnover 的参数
    # BEGIN SOLUTION
    turnover = quantile_turnover(merged_factor["factor_quantile"], q)
    # END SOLUTION
    turnovers[q] = np.nanmean(turnover)
```

以下单元格为测试。

```python
# TEST 2 points
np.testing.assert_array_almost_equal(list(turnovers.values()), [0.15, 0.31, 0.36, 0.32, 0.17], 2)
```

## 3. 概念复习

在课程中，我们介绍了很多指标的单位和经验值。这个练习将帮助你进一步熟悉这些指标。

1. 在 Alpha & Beta 收益分析中，如果因子的 1D 年化 Alpha 是 2.25，这个收益与 1 年期国债 (2.3%) 比，哪一个高？
2. 在 IC 分析中，IC 系数为 0.03，不考虑 t 值和 p 值，这是否是一个值得考虑的因子？
3. 在 IC 分析中，因子的峰度为-0.2，请解释为什么会出现负数。就此项指标而言，这是个好因子还是坏因子？
4. 在因子分析中，换手率高是好还是坏？
5. 2024年9月27日，央行宣布，公开市场利率由1.7%调整为1.5%，这是下调了多少个基点(bps)?
   
**请在下面的单元格里，写下你的答案**

<!-- MANUAL GRADE ANSWER 2 points -->

1. 年化Alpha 2.25相当于 225%。因此要比国债收益高。
2. IC 大于0.02就值得考虑，0.05以上就算好因子了。
3. 因子的峰度为-0.2，说明它是超额峰度，并且非常接近正态分布。我们喜欢正态分布，它们更稳定，可预测。
4. 因子分析中的换手率越高，引入的手续费越高，但收益并不会因换手率增加而增加，相反地，综合收益会下降，因为要扣除手续费。
5. 这是下调了20个基点。

## 4. 拓展

### 4.1. 拓展1

我们讲过，在使用Alphalens的Event Study可能要谨慎一点。为了彻底弄清楚Event Study，并且便于对Alphalens的分析结果进行对照，我们设计了这个练习。

练习的最终结果，将可以使用在你的日常工作中。

我们仍以10天期的rolling_slope作为因子。我们的目标是，在因子预处理完成，得到分层之后，我们手工计算指定分层在此后1~10天的累积收益率。如果实现是正确的，就可以与Alphalens中的结果进行对照。

设计这样一个函数： event_study, 输入、要实现的功能及输出如下:

1. 输入为factor_data和prices,其中factor_data为series，多重索引，索引为date和asset，值为因子值。prices为一个宽表，date为索引，factor_data中的asset为列，值为价格。
2. 对factor_data中的每一个date，取出当天所有的asset，在prices中，找出对应assst前2天的价格，分别记为p_2,p_1,当天的价格，记为p0,此后10天的价格，分别记为p1,p2,...p10
3. 以p0为基准，分别计算p_2,p_1,p1,p2,p3,p4,p5,p6,p7,p8,p9,p10的累积收益率，记为r_2,r_1,r1,r2,r3,r4,r5,r6,r7,r8,r9,r10
4. 将r_2, r_1, ..., r10的每个值，分别与对应date/asset的factor值相乘，得到wr_2, wr_1, wr0, wr1, wr2, wr3, wr4, wr5, wr6, wr7, wr8, wr9, wr10
5. 输出上述结果为date, asset, wr_2, wr_1, wr0, wr1, wr2, wr3, wr4, wr5, wr6, wr7, wr8, wr9, wr10为新的dataframe中的一行，这个dataframe记为result。result的索引为date, asset, 列名为wr_2, wr_1, wr0, wr1, wr2, wr3, wr4, wr5, wr6, wr7, wr8, wr9, wr10
6. 将result的所有列求均值，并且绘制成折线图

```python
# BEGIN SOLUTION
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from numpy.typing import NDArray
from numpy.lib.stride_tricks import as_strided

def rolling_slope(close: NDArray, win:int, *args):
    stride = close.strides

    slopes, _ = np.polyfit(np.arange(win), 
                           as_strided(close, (len(close)-win+1, win), 
                           stride+stride).T,
                           deg=1)
    left_padd_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_padd_len, 0), mode='constant', constant_values=np.nan)
    return slopes

def gen_stock_price(nstocks=10, dates=60, vol=0.1):
    mean = 0
    std = vol / 3
    pct = np.random.normal(mean, std, size=(nstocks, dates))
    prices = np.cumprod(1 + pct, axis=0) * np.abs(np.random.normal(size=(nstocks,1)) * 100)

    return np.around(prices, 2)


def calculate_cumulative_returns(prices, date, asset):
    # 提取当前日期的价格
    p0 = prices.loc[date,asset]
    
    # 提取过去两天的价格
    p_1 = prices.loc[date - pd.Timedelta(days=1), asset]
    p_2 = prices.loc[date - pd.Timedelta(days=2), asset]

    
    # 提取未来十天的价格
    future_prices = prices.loc[date + pd.Timedelta(days=1):date + pd.Timedelta(days=11), asset]
    
    # 计算累积收益率
    r_2 = (p_2 / p0 - 1)
    r_1 = (p_1 / p0 - 1)
    r1 = (future_prices.iloc[0] / p0 - 1)
    r2 = (future_prices.iloc[1] / p0 - 1)
    r3 = (future_prices.iloc[2] / p0 - 1)
    r4 = (future_prices.iloc[3] / p0 - 1)
    r5 = (future_prices.iloc[4] / p0 - 1)
    r6 = (future_prices.iloc[5] / p0 - 1)
    r7 = (future_prices.iloc[6] / p0 - 1)
    r8 = (future_prices.iloc[7] / p0 - 1)
    r9 = (future_prices.iloc[8] / p0 - 1)
    r10 = (future_prices.iloc[9] / p0 - 1)
    
    return r_2, r_1, 0, r1, r2, r3, r4, r5, r6, r7, r8, r9, r10

def calculate_weighted_cumulative_returns(factor_value, cumulative_returns):
    weighted_returns = [factor_value * r for r in cumulative_returns]
    return weighted_returns

def event_study(factor_data: pd.Series, prices: pd.DataFrame,start,end):
    # Step 4: 构建结果DataFrame
    columns = ['wr_2', 'wr_1', 'wr0', 'wr1', 'wr2', 'wr3', 'wr4', 'wr5', 'wr6', 'wr7', 'wr8', 'wr9', 'wr10']
    result = pd.DataFrame(columns=columns)
    # 设置多重索引
    result.index = pd.MultiIndex.from_tuples(result.index, names=['date', 'asset'])
    
    factor_data_group = factor_data.groupby(level="date")
    
    for date, assets in factor_data_group:
        if date - pd.Timedelta(days=2) < start or date + pd.Timedelta(days=10) > end:
            continue
        for asset, factor_value in assets.items():
            print("asset:",asset,"factor_value:",factor_value)
            # 计算累积收益率
            cumulative_returns = calculate_cumulative_returns(prices, date, asset[-1])
            
            # 计算加权累积收益率
            weighted_returns = calculate_weighted_cumulative_returns(factor_value, cumulative_returns)
            
            # 添加到结果DataFrame
            for i in range(len(columns)):
                result.loc[asset, columns[i]] = weighted_returns[i]
    

    # Step 5: 计算均值并绘制折线图
    mean_returns = result.mean()
    mean_returns.plot(kind='line')
    plt.title('Mean Weighted Cumulative Returns Over Time')
    plt.xlabel('Days')
    plt.ylabel('Weighted Cumulative Return')
    plt.show()

    return result

# 示例数据
dates = pd.date_range(start='2023-01-01', end='2023-01-31')
start = dates[0]
end = dates[-1]
assets = ['A', 'B', 'C']
prices = pd.DataFrame(np.random.rand(len(dates), len(assets)), index=dates, columns=assets)
print("---------------prices---------------")
print(prices)

factor_data = pd.Series(np.random.rand(len(dates) * len(assets)), index=pd.MultiIndex.from_product([dates, assets]), name='factor')
factor_data.index.names = ["date", "asset"]

# 调用函数
result = event_study(factor_data, prices,start,end)
# END SOLUTION
```

### 4.2. 拓展2

Alphalens并没有真正给出做多top分层，做空bottom分层时的累计收益。它是把从bottom到top分层的所有资产都纳入持仓，但分配不同的权重来计算的累计收益。只不过，在它的计算方式中，两端的资产分配的权重多、中间的资产分配的权重少。

但这种计算方式与实盘相比，是存在差距的。在实盘中，我们更常用的方式可能是，仅做多top分层，同时做空bottom分层（取决于资产品种能否做空），在分层内部，等权重分配资金。

请写出这种方式下，计算累计收益的实现代码。你可以仅实现1天期的累计收益。

```python
# BEGIN SOLUTION
import pandas as pd
import numpy as np

def calculate_cumulative_returns(prices, factor, num_groups=5):
    """
    计算仅做多top分层，同时做空bottom分层的1天期累计收益

    :param prices: pandas.DataFrame，包含资产价格的DataFrame，索引为日期，列名为资产代码
    :param factor: pandas.Series，包含每个资产因子值的Series，索引为资产代码
    :param num_groups: int，分层数量，默认为5
    :return: float，1天期累计收益
    """
    # 根据因子值进行分层
    quantiles = pd.qcut(factor, num_groups, labels=False)

    # 找到top分层和bottom分层的资产
    top_assets = factor[quantiles == num_groups - 1].index
    bottom_assets = factor[quantiles == 0].index

    # 计算top分层和bottom分层的资产数量
    num_top_assets = len(top_assets)
    num_bottom_assets = len(bottom_assets)

    # 计算top分层和bottom分层的等权重
    top_weight = 1 / num_top_assets if num_top_assets > 0 else 0
    bottom_weight = -1 / num_bottom_assets if num_bottom_assets > 0 else 0

    # 计算下一天的价格变化率
    returns = prices.pct_change().dropna()

    # 获取第一天的收益数据
    first_day_returns = returns.iloc[0]

    # 计算top分层和bottom分层的加权收益
    top_returns = first_day_returns[top_assets] * top_weight
    bottom_returns = first_day_returns[bottom_assets] * bottom_weight

    # 计算累计收益
    cumulative_return = top_returns.sum() + bottom_returns.sum()

    return cumulative_return


# 示例数据
np.random.seed(42)
dates = pd.date_range(start='2024-01-01', periods=2)
assets = ['A', 'B', 'C', 'D', 'E']
prices = pd.DataFrame(np.random.rand(2, 5), index=dates, columns=assets)
factor = pd.Series(np.random.rand(5), index=assets)

# 计算累计收益
cumulative_return = calculate_cumulative_returns(prices, factor)
print(f"1天期累计收益: {cumulative_return * 100:.2f}%")
# END SOLUTION
```

<!--

VERSION 2:

Alphalens并没有真正给出做多top分层，做空bottom分层时的累计收益。它是把从bottom到top分层的所有资产都纳入持仓，但分配不同的权重来计算的累计收益。只不过，在它的计算方式中，两端的资产分配的权重多、中间的资产分配的权重少。

但这种计算方式与实盘相比，是存在差距的。在实盘中，我们更常用的方式可能是，仅做多top分层，同时做空bottom分层（取决于资产品种能否做空），在分层内部，等权重分配资金。

请写出这种方式下，计算累计收益的实现代码。你可以仅实现1天期的累计收益。
-->
