# 第 3 课习题

这一章重点介绍的知识点有pandas的索引操作、pandas的塑形(reshape)操作，以及如何理解numpy中的axis。这一课的练习将帮助你熟练掌握这些知识点。

在Alphalens因子检验中，我们将频繁接触到这些知识。

## 1. 创建一个有多级索引的DataFrame

我们先来生成一个简单的行情数据集。

```python
import pandas as pd
import numpy as np

dates = pd.date_range('2023-01-01', '2023-01-05').repeat(2)
df = pd.DataFrame(
    {
        "date": dates,
        "asset": ["000001", "000002"] * 5,
        "close": (1 + np.random.normal(0, scale=0.03,size=10)).cumprod() * 10,
        "open": (1 + np.random.normal(0, scale=0.03,size=10)).cumprod() * 10,
        "high": (1 + np.random.normal(0, scale=0.03,size=10)).cumprod() * 10,
        "low": (1 + np.random.normal(0, scale=0.03,size=10)).cumprod() * 10
    }
)
df.tail()
```

## 2. 理解Pandas索引和多重索引

```python
# 要求：将 date 字段设为索引，使用就地修改

# BEGIN SOLUTION
df.set_index('date', inplace=True)
# END SOLUTION
```

```python
# test
assert df.index.name == 'date'
```

现在，我们将date和asset设置为多重索引，并且要求asset排在前面。

```python
# 要求：设置asset, date为索引，且asset在前。结果保存在df1中

# BEGIN SOLUTION
df1 = df.set_index(['asset', df.index])
# END SOLUTION
df1.tail()
```

现在，我们取索引中asset的值，找出unique的值。

```python
# 要求：从df1的asset索引中，找出有多少个unique的asset，保存在assets变量中

# BEGIN SOLUTION
assets = df1.index.get_level_values('asset').unique()
# END SOLUTION
print(assets)
```

```python
# test
np.testing.assert_array_equal(assets, ["000001", "000002"])
```

现在，我们将索引改个名字。请把索引中的"date"改为"frame"。

```python
# 要求：将df1中的索引date改为frame，保存在df2中。
# BEGIN SOLUTION
df2 = df1.rename_axis(index={"date": "frame"})
# END SOLUTION
```

现在，请交换df1中的索引次序，即将索引改为"date", "asset"。请使用最简洁的方法，即使用pandas专门为这种场景设计的方法。

```python
# 要求：将df1索引交换后的dataframe用df3保存
# BEGIN SOLUTION
df3 = df1.swaplevel(0,1)
# END SOLUTION
df3.tail()
```

```python
# TEST
assert df3.index.names == ['date', 'asset']
```

这里有好几种方法，你使用的是哪一种？请查一下pandas文档，或者我们网站上的[文章](http://www.jieyu.ai/blog/2024/08/25/effective-pandas-1/)，看看你是否掌握了所有的方法，并了解其内部原理。

## 3. Reshape Pandas DataFrame

在课程中我们已经看到好几处 DataFrame 重塑操作。原因是，在不同的库之间传递数据时，它们对格式要求各不相同，因此需要进行转换。此外，在因子检验中，我们常常要进行横截面分析，即，对某一日，取当日所有资产的某个数据进行分析（比如方差、均值、aggregate等）。通过重塑操作，将数据转换为横截面数据，便于进行向量化地分析，以提高分析速度，同时避免产生前视偏差。

在pandas中，提供了pivot, unstack, stack和melt等方法，用于实现重塑操作。此外，还有一个pivot_table方法，用以在重塑的过程中，还允许进行一些聚合运算。

### 3.1. 从长表到宽表

请使用unstack和pivot方法，分别实现长表到宽表的转换。

```python
# 请你从df3中，提取close列，重塑为宽表（即以日期为索引、资产代码为列名，close为值）的表。
# 要求使用两种方法完成转换。

# 这里是第一种方法，结果保存在df20中

# BEGIN SOLUTION
df20 = df3["close"].unstack()
# END SOLUTION

# 这里是第二种方法，结果保存在df21中

# BEGIN SOLUTION
df21 = df3.reset_index().pivot(index="date", columns="asset", values="close")
# END SOLUTION
```

```python
# TEST
assert df20.index.name == "date"
assert df21.index.name == "date"
assert np.all(df20.columns == ["000001", "000002"])
assert np.all(df21.columns == ["000001", "000002"])
```

### 3.2. 从宽表到长表

显然，我们也需要从宽表到长表的逆转换。在本练习中，你也需要用两种方法来实现。

```python
# 方案一：将df20转换成为以date,asset为索引的长表，保存在df30中
# BEGIN SOLUTION
df30 = df20.stack("asset")
# END SOLUTION
df30.tail()
```

```python
# TEST
assert np.all(df30.index.names == ["date", "asset"])
```

```python
# 方案二: 将df21转换成以asset, date为索引的长表，保存在df31中

# BEGIN SOLUTION
assets = tuple(df21.columns.tolist())
tmp = df21.reset_index()
tmp = tmp.melt(id_vars='date', value_vars=assets, var_name="asset", value_name='factor')
df31 = tmp.set_index(['asset', 'date'])
# END SOLUTION
df31
```

```python
# AUTO TEST

assert np.all(df31.index.names == ["asset", "date"])
```

## 4. Axis in Numpy/Pandas

在本章的mad_clip和zscore计算中，都实现了向量化运算。即，输入数据由date索引，列为各个asset的代码，如下图所示：

![](https://images.jieyu.ai/images/2024/09/barss-price-pivoted.jpg)

假设现在要对每一行进行zscore化。如果不使用向量化，我们就要使用for循环:

```md
````python
from scipy.stats import zscore

for i, row in df.iterrows():
    score = zscore(row.to_numpy())
````
```

这样的方法比较耗时，代码也不简洁。在课程中，我们使用的方法是`zscore(df, axis=1)`。它会在行的维度上进行计算，即对每一行进行zscore化。

请以np.max为例，探索在numpy/pandas中，axis参数的含义。

```python
# 使用df21为输入，求全表最大值，保存在max_all中

# BEGIN SOLUTION
max_all = np.max(df21)
# END SOLUTION
```

```python
# 使用df21为输入，求每一行最大值，保存在max_row中

# BEGIN SOLUTION
max_row = np.max(df21, axis=1)
# END SOLUTION
max_row
```

```python
# 使用df21为输入，求每一列最大值，保存在max_col中

# BEGIN SOLUTION
max_col = np.max(df21, axis=0)
# END SOLUTION
max_col
```

```python
# AUTO TEST

assert np.isclose(max_all, 10.11, 2)
assert np.allclose(max_row, [10.12, 9.69, 9.84, 10.1, 10.8], 2)
assert np.allclose(max_col, [10.7, 10.92], 2)
```

## 5. 分层回溯的实现

在课程中，示例 3-9 演示了这样一个分层回溯算法。

```md
````python
from collections import defaultdict
import matplotlib.pyplot as plt
def hierarchical_test(factor, returns):
    tier_returns = defaultdict(list)
    for date in factor.index.get_level_values("date"):
        y = returns.loc[date]
        x = factor.xs(date, level="date")

        grouper = pd.qcut(x.values, 3, labels=False)
        groups = x.T.groupby(grouper).apply(lambda z: z.index)

        for i, group in enumerate(groups):
            tier_returns[f"{i}"].append(np.mean(y[group].values))

    tier_returns = pd.DataFrame(tier_returns) + 1
    return tier_returns.cumprod()

tier_returns = hierarchical_test(factor, returns)
tier_returns.plot()
````
```

这段代码的输入是factor和returns（假设它们在时间上已经对齐）。算法的主要思想是

1. 对factor中的每一行（即每一天）进行遍历
   a. 提取出当天的factor和returns
   b. 将factor值分成3个组，计算出各组的成份（即symbols的列表）
   c. 对于每个组，根据symbol找出对应的returns(从`y`中)，求得均值，保存到tier_returns中对应分组标签下。
2. 遍历结束后，tier_returns是一个DataFrame，其中每一行表示一个时间点，每一列则是一个分组，列名字即为分层标签。
3. 对tier_returns进行累乘，得到各分组每天的累计收益。

请仔细阅读示例代码，思考下面的问题并完成代码。

1. 在tier_returns中，每个分层的标签是以groups的顺序来决定的。算法中，是如何保证这个顺序就是分层的顺序的？
2. 请代入一些真实的数据进行尝试，验证分层及对应收益的计算是正确的。

这段代码的关键是第10到14行，对因子进行正确分层，并且得到每个分层的资产代码。一旦得到了这个数据，由于returns的索引就是资产代码，所以进一步获得每个分层的平均收益就易如反掌。

首先，我们为此示例准备一些数据，请运行下面的代码。

```python
import pandas as pd
import numpy as np
data  =[-0.68,0.4,-0.38,-1.09,0.71,
      -0.34,0.05,0.72,2.53,-0.48,0.53,0.89]

factor = pd.DataFrame({
    "factor": data,
    "symbol": [f"{i:06d}.SZ" for i in np.arange(len(data))]
})

factor.set_index("symbol", inplace=True)
factor.tail()
```

假设factor已经是某天的因子数据，对应示例 3-9中的一次内循环。现在，我们要将它按分位数分成三层，并且得到每一层的资产列表。

```python
grouper = pd.qcut(factor["factor"], 3, labels=False)

# BEGIN SOLUTION
groups = factor.groupby(grouper).apply(lambda z: z.index)
# END SOLUTION
for group in groups:
    print(group)
```