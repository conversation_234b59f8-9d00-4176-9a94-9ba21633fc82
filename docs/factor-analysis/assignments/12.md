# 第12课练习

## 1. 题目

在课程中，我们提到，<PERSON> 在2000年之后，还提出了一个新的技术指标，即 Relative Vigor Index。

它的公式是：

$$
NUMERATOR = \frac{a + (2\times b) + (2\times c) + d}{6} \\
DENOMINATOR = \frac{e + (2\times f) + (2\times g) + h}{6} \\
\\
RVI = \frac{SMA of NUMERATOR for N periods}{SMA of DENOMINATOR for N periods} \\
Signal Line = \frac{RVI + (2 \times i) + (2 \times j) + k}{6}
$$

其中，

$$
a = Close(0) - Open(0) \\
b = Close(0) - Open(-1) \\
c = Close(0) - Open(-2) \\
d = Close(0) - Open(-3) \\
e = High(0) - Low(0) \\
f = High(0) - Low(-1) \\
g = High(0) - Low(-2) \\
h = High(0) - Low(-3) \\
i = RVI(-1) \\
j = RVI(-2) \\
k = RVI(-3) \\
$$

这个指标同样不在 talib 中，不过实现起来也很简单。它与 SO（Stochastic Oscillator）比较接近，但是，它是使用的 Close 和 Open 来进行比较，而不是 Close 与最低价比较。我们前面讲过，Close 与 Open 的比较有买卖力道的含义。在 RVI 中，它连续考察了三期的买卖力道。

请实现此因子，并进行因子检验。

## 2. 参考答案

```python
import pandas as pd

def calc_rvi(bars, n=14):
    """计算 Relative Vigor Index (RVI)
    
    Args:
        bars: 行业数据
        n: 计算 RVI的周期，默认为 14
    Return:
        包含 'RVI' 和 'Signal_Line' 列的数据框
    """
    # 计算 a, b, c, d
    a = bars['close'] - bars['open']
    b = bars['close'] - bars['open'].shift(1)
    c = bars['close'] - bars['open'].shift(2)
    d = bars['close'] - bars['open'].shift(3)
    
    # 计算 e, f, g, h
    e = bars['high'] - bars['low']
    f = bars['high'] - bars['low'].shift(1)
    g = bars['high'] - bars['low'].shift(2)
    h = bars['high'] - bars['low'].shift(3)
    
    # 计算 NUMERATOR 和 DENOMINATOR
    numerator = (a + 2 * b + 2 * c + d) / 6
    denominator = (e + 2 * f + 2 * g + h) / 6
    
    # 计算 RVI
    rvi = numerator.rolling(window=n).mean() / denominator.rolling(window=n).mean()
    
    return rvi

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 29)

alphatest(2000, start, end, calc_rvi,top=6)
```
