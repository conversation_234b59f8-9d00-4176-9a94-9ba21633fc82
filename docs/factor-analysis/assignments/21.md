# 第 21 课习题

在金融量化交易领域，合理运用人工智能模型并充分利用免费算力资源是非常重要的。本习题将结合第 21 课中的知识点，让你完成一个完整的量化交易模型构建与训练过程。

## 1. 任务 1：数据获取与预处理
- 利用 akshare 库获取中国股票市场的历史 K 线数据，选取至少 5 只不同的股票，时间范围为最近 5 年。
- 对获取的数据进行清洗，处理缺失值和异常值。
- 将处理后的数据绘制为 K 线图，并使用 CNN 网络对 K 线模式进行识别，判断是否属于某种旗形整理。

```python
from datetime import date
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mplfinance.original_flavor import candlestick_ohlc
import matplotlib.dates as mdates
from sklearn.model_selection import train_test_split
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense

# 1. 数据获取
start = date(2018, 1, 1)
end = date(2023, 1, 1)
universe = 5
barss = load_bars(start, end, universe=universe)

# 2. 数据预处理
# 处理缺失值
barss = barss.dropna(subset=['open', 'high', 'low', 'close'])

# 处理异常值，过滤价格小于 0 的行
price_cols = ['open', 'high', 'low', 'close']
for col in price_cols:
    barss = barss[barss[col] > 0]

# 3. 绘制 K 线图
assets = barss.index.get_level_values('asset').unique()
for asset in assets:
    asset_data = barss.xs(asset, level='asset')
    ohlc = asset_data[['open', 'high', 'low', 'close']].reset_index()
    ohlc['date'] = pd.to_datetime(ohlc['date'])
    ohlc['date'] = ohlc['date'].map(mdates.date2num)

    fig, ax = plt.subplots()
    candlestick_ohlc(ax, ohlc.values, width=0.6, colorup='g', colordown='r')
    ax.xaxis_date()
    plt.title(f'K 线图 - {asset}')
    plt.xlabel('日期')
    plt.ylabel('价格')
    plt.show()

# 4. 使用 CNN 网络对 K 线模式进行识别
sequence_length = 20
X = []
y = []

for asset in assets:
    asset_data = barss.xs(asset, level='asset')
    num_samples = len(asset_data) - sequence_length + 1
    for i in range(num_samples):
        sample = asset_data.iloc[i:i + sequence_length][['open', 'high', 'low', 'close']].values
        X.append(sample)
        # 此处标签需根据旗形整理的定义来确定，这里先设为 0
        y.append(0)

X = np.array(X)
y = np.array(y)

# 调整输入数据的形状以适应 CNN 输入 [样本数, 时间步长, 特征数, 1]
X = np.expand_dims(X, axis=-1)

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 构建 CNN 模型
# BEGIN SOLUTION
model = Sequential()
model.add(Conv2D(32, (3, 3), activation='relu', input_shape=(sequence_length, 4, 1)))
model.add(MaxPooling2D((2, 2)))
model.add(Flatten())
model.add(Dense(64, activation='relu'))
model.add(Dense(1, activation='sigmoid'))
# END SOLUTION

# 编译模型
model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])

# 训练模型
model.fit(X_train, y_train, epochs=10, batch_size=32, validation_data=(X_test, y_test))

# 评估模型
test_loss, test_acc = model.evaluate(X_test, y_test)
print(f"测试集损失: {test_loss}, 测试集准确率: {test_acc}")
```

## 2. 任务 2：Transformer 架构应用
- 使用 Transformer 架构处理股票价格走势的时间序列数据，预测未来 10 个交易日的股票价格。
- 对比 Transformer 架构与传统的 RNN 模型在处理时间序列数据上的性能差异。

```python
# 任务 2：Transformer 架构应用
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, SimpleRNN
from tensorflow.keras.layers import Input, MultiHeadAttention, LayerNormalization, Dropout
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.losses import MeanSquaredError
from sklearn.metrics import mean_squared_error

# 5. 使用 Transformer 架构预测未来 10 个交易日的股票价格
# 准备时间序列数据
def prepare_time_series_data(data, sequence_length, prediction_length):
    X = []
    y = []
    for i in range(len(data) - sequence_length - prediction_length + 1):
        X.append(data[i:i + sequence_length])
        y.append(data[i + sequence_length:i + sequence_length + prediction_length].flatten())
    return np.array(X), np.array(y)

# 归一化处理
scaler = MinMaxScaler()
asset_data = barss.xs(assets[0], level='asset')
close_prices = asset_data['close'].values.reshape(-1, 1)
scaled_close_prices = scaler.fit_transform(close_prices)

# 准备数据
sequence_length = 20
prediction_length = 10
X_ts, y_ts = prepare_time_series_data(scaled_close_prices, sequence_length, prediction_length)

# 划分训练集和测试集
X_train_ts, X_test_ts, y_train_ts, y_test_ts = train_test_split(X_ts, y_ts, test_size=0.2, random_state=42)

# Transformer 架构
def transformer_block(inputs, num_heads, key_dim, ff_dim, dropout=0):
    # 多头注意力层
    # BEGIN SOLUTION
    attn_output = MultiHeadAttention(num_heads=num_heads, key_dim=key_dim)(inputs, inputs)
    attn_output = Dropout(dropout)(attn_output)
    out1 = LayerNormalization(epsilon=1e-6)(inputs + attn_output)
    # END SOLUTION
    
    # 前馈网络
    # BEGIN SOLUTION
    ff_output = Dense(ff_dim, activation="relu")(out1)
    ff_output = Dense(inputs.shape[-1])(ff_output)
    ff_output = Dropout(dropout)(ff_output)
    # END SOLUTION
    return LayerNormalization(epsilon=1e-6)(out1 + ff_output)

# 构建 Transformer 模型
def build_transformer_model(input_shape, num_heads, key_dim, ff_dim, num_transformer_blocks, dropout=0):
    inputs = Input(shape=input_shape)
    x = inputs
    for _ in range(num_transformer_blocks):
        x = transformer_block(x, num_heads, key_dim, ff_dim, dropout)
    x = Flatten()(x)
    outputs = Dense(prediction_length)(x)
    model = Model(inputs=inputs, outputs=outputs)
    return model

# 定义模型参数
num_heads = 2
key_dim = 32
ff_dim = 32
num_transformer_blocks = 2
dropout = 0.1

# 构建并编译 Transformer 模型
transformer_model = build_transformer_model(X_train_ts[0].shape, num_heads, key_dim, ff_dim, num_transformer_blocks, dropout)
transformer_model.compile(optimizer=Adam(learning_rate=0.001), loss=MeanSquaredError())

# 训练 Transformer 模型
transformer_model.fit(X_train_ts, y_train_ts, epochs=20, batch_size=32, validation_data=(X_test_ts, y_test_ts))

# 预测未来 10 个交易日的股票价格
last_sequence = scaled_close_prices[-sequence_length:].reshape(1, sequence_length, 1)
transformer_predictions = transformer_model.predict(last_sequence)
transformer_predictions = scaler.inverse_transform(transformer_predictions.reshape(-1, 1)).flatten()

# 6. 使用传统的 RNN 模型进行预测
# 构建 RNN 模型
# BEGIN SOLUTION
rnn_model = Sequential()
rnn_model.add(SimpleRNN(64, input_shape=(sequence_length, 1)))
rnn_model.add(Dense(prediction_length))
rnn_model.compile(optimizer=Adam(learning_rate=0.001), loss=MeanSquaredError())
# END SOLUTION

# 训练 RNN 模型
rnn_model.fit(X_train_ts, y_train_ts, epochs=20, batch_size=32, validation_data=(X_test_ts, y_test_ts))

# 预测未来 10 个交易日的股票价格
rnn_predictions = rnn_model.predict(last_sequence)
rnn_predictions = scaler.inverse_transform(rnn_predictions.reshape(-1, 1)).flatten()

# 7. 对比 Transformer 架构与传统的 RNN 模型在处理时间序列数据上的性能差异
y_test_actual = scaler.inverse_transform(y_test_ts.reshape(-1, 1)).reshape(y_test_ts.shape)
transformer_mse = mean_squared_error(y_test_actual.flatten(), transformer_model.predict(X_test_ts).flatten())
rnn_mse = mean_squared_error(y_test_actual.flatten(), rnn_model.predict(X_test_ts).flatten())

print(f"Transformer 模型的均方误差: {transformer_mse}")
print(f"RNN 模型的均方误差: {rnn_mse}")

if transformer_mse < rnn_mse:
    print("Transformer 模型在处理时间序列数据上的性能优于 RNN 模型。")
else:
    print("RNN 模型在处理时间序列数据上的性能优于 Transformer 模型。")
```

## 3. 任务 3：强化学习训练
- 运用强化学习训练一个用于交易的智能体，根据股票价格和市场信息做出买入和卖出决策。
- 设计合适的奖励函数，以最大化交易收益。

```python
# 从 gymnasium 导入 Env, Box, Discrete
import gymnasium as gym
from gymnasium.spaces import Discrete, Box
# 其他导入保持不变
from datetime import date
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mplfinance.original_flavor import candlestick_ohlc
import matplotlib.dates as mdates
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, SimpleRNN
from tensorflow.keras.layers import Input, MultiHeadAttention, LayerNormalization, Dropout
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.losses import MeanSquaredError
from sklearn.metrics import mean_squared_error
from stable_baselines3 import PPO
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.env_checker import check_env

# 8. 强化学习训练
class StockTradingEnv(gym.Env):
    def __init__(self, data):
        self.data = data
        self.index = 0
        self.balance = 100000  # 初始资金
        self.shares_held = 0
        self.action_space = Discrete(3)  # 0: 不操作, 1: 买入, 2: 卖出
        self.observation_space = Box(low=0, high=np.inf, shape=(1,))

    def step(self, action):
        current_price = self.data[self.index]
        next_price = self.data[self.index + 1] if self.index < len(self.data) - 1 else current_price

        if action == 1 and self.balance >= current_price:
            # 买入
            self.shares_held = self.balance / current_price
            self.balance = 0
        elif action == 2 and self.shares_held > 0:
            # 卖出
            self.balance = self.shares_held * current_price
            self.shares_held = 0

        # 奖励函数：交易收益
        reward = (self.balance + self.shares_held * next_price) - (self.balance + self.shares_held * current_price)

        self.index += 1
        terminated = self.index >= len(self.data) - 1
        truncated = False  # 这里假设没有截断情况
        info = {}

        return np.array([current_price]), reward, terminated, truncated, info

    def reset(self, seed=None, options=None):
        self.index = 0
        self.balance = 100000
        self.shares_held = 0
        return np.array([self.data[self.index]]), {}

    def render(self, mode='human'):
        print(f"当前资金: {self.balance}, 持有股票数量: {self.shares_held}")


# 创建环境
env = StockTradingEnv(close_prices.flatten())
check_env(env)

# 创建向量环境
vec_env = make_vec_env(lambda: env, n_envs=1)

# 训练 PPO 智能体
# BEGIN SOLUTION
model = PPO('MlpPolicy', vec_env, verbose=1)
model.learn(total_timesteps=10000)
# END SOLUTION

# 测试智能体
obs, _ = env.reset()
for _ in range(len(close_prices) - 1):
    action, _states = model.predict(obs)
    obs, rewards, terminated, truncated, info = env.step(action)
    if terminated or truncated:
        obs, _ = env.reset()
```