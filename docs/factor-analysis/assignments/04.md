# 第 4 课习题

这一讲中，我们重点介绍了如何解决因子计算中，可能遇到的性能问题。

首先，我们使用 as_strided 将数组进行升维，然后利用 numpy/scipy 的向量化计算能力 -- 即它们可以对一个二维数组，在列或者行的方向上展开并行计算 -- 来提升运算速度。

其次，为了消除多资产因子计算时的循环，我们介绍了 groupby.apply。它将原始因子 DataFrame 按资产划分成分组，在每个组内分别计算因子--这一步可以利用向量化来加速--然后 groupby.apply 将计算结果合并成一个 DataFrame。

第一步能显著提升因子计算速度，第二步也有加速（通过消除 Python 侧的循环），但主要是简化代码，由于 pandas 不能利用多核（注：部分场合下可以利用多核，比如读取 csv 文件），所以，在这一阶段，仍有进一步提高速度的可能。

这一课的练习，我们将继续探讨 as_strided 的用法，以及如何进一步提高运算性能。

## 1. as_strided

as_strided 是 numpy 的一个函数，它提供了原数组的新视图，使得数组的维度和步长可以满足我们想要的计算需求。在课程中，我们介绍到，要将一个 np.arange(8) 的一维数组，转换成滑动窗口下、shape 为 (6,3) 的二维数组，可以使用 as_strided，但指定正确的步长是关键。

### 1.1. 正确理解 strides

现在，我们就来看一个例子，如果步长指定错了，会得到什么样的错误结果。

```python
# READONLY
from numpy.lib.stride_tricks import as_strided
import numpy as np

arr = np.arange(8)

rolling = as_strided(arr, shape=(6,3), strides=(24, 8))
print("错误的 strides 产生错误的结果：\n", rolling)

rolling = as_strided(arr, shape=(6,3), strides=(8,8))
print("正确的 strides 产生正确的结果：\n", rolling)
```

QUESTION: 请解释要生成 (6,3) 的二维数组，每个元素的 itemsize 也是 8，为何步长指定为 (24,8) 却得到了错误的结果？

**你的答案:**

```md
<!-- MANUAL GRADE ANSWER 1 points -->

as_strided 生成的是一个视图，并没有创建一个新的数组。当我们指定 strides 为 (24,8) 时，意味着每“生成”一个新的行，as_stride 都要在原数组上，往后跨 24 个字节，即 3 个整数。所以，生成的第一行是 [0,1,2]，第二行是 [3,4,5]。这些数字虽然错误，但仍然在落在原数组上，所以有值。但在第三行的最后一个数字时，就超越了原数组，进行未知区域，因此我们得到了随机数。
```

### 1.2. 利用 as_strided，计算移动平均

请完成下面的代码，计算**窗口大小**为 5 的移动平均线。

```python
# AUTO GRADE ANSWER
from numpy.lib.stride_tricks import as_strided
import numpy as np
# 请将结果保存在变量 ma 中。

arr = np.arange(8)
# BEGIN SOLUTION
ma = np.mean(as_strided(arr, shape=(4, 5), strides=(8,8)), axis=1)
# END SOLUTION
print(ma)
```

```python
# test 1 points
np.testing.assert_array_equal(ma, [2,3,4,5])
```

## 2. 多资产因子计算

### 2.1. groupby.apply

在教材中，计算多资产的因子时，我们使用的是 for 循环。这段代码如下：

```
# READ-ONLY
# 2. 计算因子
factors = []
for group in barss.groupby(level='asset'):
    close = group[1].close

    slopes = rolling_slope(close.to_numpy(), 10)
    factors.append(pd.Series(slopes, index=group[1].index))
```

在讲课时，我们介绍了如何使用 groupby.apply 来消除循环。现在，请你来完成代码。

```python
# MANUAL GRADE ANSWER 5 points
import time

def rolling_slope(close: NDArray, win:int, *args):
    if len(close) < win:
        return np.full((len(close), ), np.nan)

    # 将 CLOSE 转换为滑动窗口二维数组，保存在 TRANSFORMED 变量中
    # BEGIN SOLUTION
    stride = close.strides
    shape = (len(close) - win + 1, win)
    strides = stride + stride
    transformed = as_strided(close, shape, strides)
    # END SOLUTION
    
    slopes, _ = np.polyfit(np.arange(win), transformed.T, deg=1)

    # POLYFIT 返回的一维数组长度不及 CLOSE, 需要左补全（用 NP.NAN 填充）
    # 请将 SLOPES 补全为 LEN(CLOSE) 长度，保存为 SLOPES
    # BEGIN SOLUTION
    left_pad_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_pad_len, 0), mode='constant', constant_values=np.nan)
    # END SOLUTION
    return slopes

def wrapper(group):
    slopes = rolling_slope(group["close"].to_numpy(), 10)

    # 将 SLOPES 转换为 DATAFRAME, 以日期为索引，列名为 FACTOR，保存为 DF
    # BEGIN SOLUTION
    index = group.index.get_level_values(0)
    df = pd.DataFrame(slopes, index=index, columns=["factor"])
    # END SOLUTION
    return df

# 1. 获取行情数据
start = datetime.date(2023, 12, 1)
end = datetime.date(2023, 12, 29)
universe = 2000
barss = load_bars(start, end, universe=universe)

t0 = time.time()
factors = barss.groupby(level='asset').apply(wrapper)
print(f"Pandas Groupby.apply[{universe}] 耗时：{time.time() - t0}")

factors.tail()

PD_VERSION = pd.__version__
```

在课程环境中运行时，因子计算本身的耗时应该在 1.3 秒左右。这是同时计算 2000 个资产的速度。因此，如果把 A 股全部资产计算一次，也只需要 4 秒钟。

### 2.2. 使用 modin

Modin 是 pandas 的 drop-in 替换品。Pandas 是单线程的（在多数任务上），Modin 则可以让你使用上多核。它能更好地用以较大的数据集。在 Modin 的背后，是 Dask 或者 Ray。后者是构建了 ChatGPT 分布式计算平台的框架。

请查询 Modin 官方文档，在你的课程环境中，安装带 Ray 支持的 Modin 版本。

```python
# MANUAL GRADE ANSWER 1 points
# BEGIN SOLUTION
!pip install modin[ray]
# END SOLUTION
```

现在，请使用 modin 来重写上面的多资产因子计算代码。

```python
# READ ONLY
import ray
ray.init(num_cpus=8, object_store_memory=1e9, ignore_reinit_error=True)
```

```python
# MANUAL GRADE ANSWER 1 points
# 导入 MODIN，以替换 PANDAS
# BEGIN SOLUTION
import modin.pandas as pd
# END SOLUTION

t0 = time.time()
factors = barss.groupby(level='asset').apply(wrapper)
print(f"Pandas Groupby.apply[{universe}] 耗时：{time.time() - t0}")

factors.tail()
MODIN_VERSION = pd.__version__
```

```python
# TEST 检验我们是否在使用 MODIN 1 points
assert MODIN_VERSION == PD_VERSION
```

比较耗时的结果可能让你有点失望。使用 modin 之后，运算速度竟然大幅下降了。

没关系，这只是因为我们的计算量太小了。Modin 在启动时，大量的时间花在启动 Ray 平台上，以及 docker 容器本身带来了一些限制。

### 2.3. 使用 duckdb 或者 polars

如果你对进一步提升性能感兴趣，可以考虑使用 duckdb 或者 polars。duckdb 和 polars 在启动并行运算时，并不需要启动额外的进程，也就不需要进程间的数据交换，因此适用场景下，性能会比 modin 强很多。

提示：如果你使用 duckdb，你可能需要使用 UDF（user define function）。
