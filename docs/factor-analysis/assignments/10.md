# 第10课 习题

## 1. 连续涨跌状态因子

在课程中，我们提到，连续涨跌状态因子的计算比较有技巧。现在，我们就来练习下。

习题目标是，给定一支股票的OHLC数据，找出它的连续状跌状态。比如，如果有下面的数据：

```python
# READ ONLY
np.random.seed(78)

code = "000001.XSHE"
start = datetime.date(2023, 1, 10)
end = datetime.date(2023, 12, 31)

df = load_bars(start, end, (code, )).xs(code, level=1)
df.tail()
```

我们将得到以下表格：

| date       | close | ret       | status |
| ---------- | ----- | --------- | ------ |
| 2023-12-20 | 9.03  | NaN       | 0      |
| 2023-12-21 | 9.17  | 0.015504  | -1     |
| 2023-12-22 | 9.20  | 0.003271  | -2     |
| 2023-12-25 | 9.19  | -0.001087 | 1      |
| 2023-12-26 | 9.10  | -0.009793 | 2      |
| 2023-12-27 | 9.12  | 0.002198  | -1     |
| 2023-12-28 | 9.45  | 0.036184  | -2     |
| 2023-12-29 | 9.39  | -0.006349 | 1      |

现在，请你来完成这个算法。

```python
# AUTOGRADE ANSWER
def calc_continuous_status(df):
    df["ret"] = df.close.pct_change()

    down = df["ret"] < 0

    # 通过cumsum统计累计下跌的天数, 保存为g
    # BEGIN SOLUTION
    g = (down.diff() != 0).cumsum()
    # END SOLUTION

    # 通过g对down进行分组，在组内统计累计下跌的天数，保存为g_cumdown
    # BEGIN SOLUTION
    g_cumdown = down.groupby(g).cumsum()
    # END SOLUTION

    df["status"] = g_cumdown

    up = df["ret"] > 0

    # 通过cumsum统计累计上涨的天数, 保存为g
    # BEGIN SOLUTION
    g = (up.diff() != 0).cumsum()
    # END SOLUTION

    # 通过g对up进行分组，在组内统计累计上涨的天数，保存为g_cumup
    # BEGIN SOLUTION
    g_cumup = up.groupby(g).cumsum()
    # END SOLUTION

    df.loc[df["status"] == 0, 'status'] = g_cumup * -1
    
    return df["status"]
```

```python
# AUTO TEST, 6 points
### AUTOTEST calc_continuous_status(df)[-20:]
```

## 2. 思考题

在视频中，我们指出，对于直线与周期函数合成的函数，其导数有去直流分量（即去掉直线）的作用，并且，它能提前若干个相位发现周期函数的波峰与波谷。如下图所示：


<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/10/relation-between-d1-and-d2.jpg'>
<span style='font-size:0.6rem'></span>
</div>

在图中，原序列为d1，导函数为d2，我们发现，d2与d1的差别就是相位差而已。它们有良好的相关性。

我们的问题是，既然通过求导就能很好地发现原函数的波峰与波谷，这不相当于找到了一个能预见未来的函数吗？为何一阶导因子还只能得到11.6%左右的年化Alpha(尽管可以优化到20%左右)?

**你的答案**
```md
<!-- MANUAL GRADE ANSWER  4 points-->
原因1, 无论在A股市场，还是美股市场，始终只有一小部分股有长期资金在有计划地运作。大部分股、大部分时间，个股都是在无序交易中。因此，也就没有明显的规律可挖掘。
原因2，如果导数能预测未来趋势有前提条件，原函数必须是周期函数，并且在未来一段时间必须保持之前的周期趋势。
```

## 3. 拓展题

在教材中，我们给出了两种计算连续累积收益的方法，但它们都不是性能上最优的。最优的方案应该是通过log(close)变换后，将收益的累乘变成累加，从而我们就可以使用我们在练习一中使用的技巧。

如果你对此感兴趣，可以尝试一下。此题我们不会提供标准答案。

<!-- MANUAL GRADE ANSWER  2 bonus points-->

