# 第 7 讲：matplotlib 绘图技巧

在教材中，我们介绍了使用plotly来绘制3D参数高原的方法。有时候我们可能必须要使用matplotlib来绘图。现在，就请你完成这个练习。

```python
# READ ONLY
from itertools import product
import plotly.graph_objects as go
 
np.random.seed(78)

def calc_rsi(df, n):
    return 100 - ta.RSI(df.close, n)

start = datetime.date(2017, 1, 1)
end = datetime.date(2022, 12, 31)
bins = np.linspace(0, 100, 10)

alphas = []

win = np.arange(6,10)
top = [4,5,6]

for win_, top_ in product(win, top):
    alpha, _ = alphatest(100, start, end, 
                      calc_factor=calc_rsi, args=(win_,), 
                      top=top_, 
                      bins=bins,
                      plot_mode=None)
    alphas.append(alpha.iloc[0,0])

clear_output()
```

```python
fig = plt.figure()

# 创建 3D 表面图， 保存在ax中。
# BEGIN SOLUTION
ax = fig.add_subplot(111, projection='3d')
# END SOLUTION

# reshape alphas，将其保存在变量z中。
# BEGIN SOLUTION
z = np.array(alphas).reshape((len(win), len(top)))
# END SOLUTION

# 生成top和win两个轴的数据，保存在X, Y中
# 注意：根据后面的x,ylabel来决定轴数据分配
# BEGIN SOLUTION
X, Y = np.meshgrid(top, win)
# END SOLUTION

# 调用plot_surface绘图
# BEGIN SOLUTION
surf = ax.plot_surface(X, Y, z, cmap='viridis')
# END SOLUTION

# READ ONLY
fig.colorbar(surf, shrink=0.5, aspect=5)

# 设置俯仰角为30度，方位角为60度
ax.view_init(elev=30, azim=60)
ax.set_title('RSI 参数高原图')
ax.set_xlabel('top')
ax.set_ylabel('win')
ax.set_zlabel('Alpha')

plt.show()
```
