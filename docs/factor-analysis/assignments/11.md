# 第 11 讲 ：基本面和另类因子

<br>

## 1. 央财的5因子数据库

这一课的练习中，我们将介绍一个五因子数据源，你可以利用它来完成自己的研究。

这个数据源来自中央财经大学，数据回溯到1994年。根据它的readme，这些数据又来自于国泰君安。

下面，请你读取这份数据，并且计算在HML, SMB和MKT因子的累积收益率。


```python
# AUTOGRADE ANSWER
cur_dir = os.path.abspath(".")
daily = "../../../supplements/five-factors/fivefactor_daily.csv"   # 这里可能需要更换一下路径
csv = os.path.join(cur_dir, daily)

# 要求：读取csv文件，并且将索引重命名为date，timestamp类型
# BEGIN SOLUTION
five = pd.read_csv(csv)
five.rename(columns={"trddy": "date"}, inplace=True)
five['date'] = pd.to_datetime(five['date'])
five.set_index('date', inplace=True)
# END SOLUTION
five
```

测试如下：

```python
# AUTO TEST,  points 1
### AUTOTEST five.tail()
```

现在，我们来看看投资这些因子，将获得的收益。

```python
start_date = '2018-01-01'
end_date = '2023-12-31'
sub = five.query("date >= @start_date and date <= @end_date")
```

请基于sub，绘制出mkt_rf, smb和hml三列的累积收益图。请使用dataframe自带的绘图函数。

**你的答案**

<!-- MANUAL GRADE ANSWER  1 points-->
```python
import pandas as pd
import matplotlib.pyplot as plt

# 计算累积收益
cumulative_returns = (1 + sub[['mkt_rf', 'smb', 'hml']]).cumprod()

# 设置日期为索引
cumulative_returns.index = sub.index

# 绘制累积收益图
cumulative_returns.plot(title='累积收益图', figsize=(12, 6))
plt.xlabel('日期')
plt.ylabel('累积收益')
plt.legend(['MKT - RF', 'SMB', 'HML'])
plt.grid(True)
plt.show()
```

## 2. 词频因子

在课程中，我们介绍了通过tushare的新闻接口，获得新闻文本，再通过jieba分词，得到每天出现在新闻中的上市公司名单及词频。我们可以近一步把它构建成为词频因子。

tushare新闻接口对单次数据吞吐量及并发量都做了限制，所以，本练习请离线自行完成，本课程环境使用人数众多，不一定保证能及时运行完成。我们提供参考答案，请在supplements/word-count.ipynb中查看。

### 2.1. 获取新闻文本数据


我们可以通过tushare的news接口来获取新闻。请写下调用方法。

```md
<!-- MANUAL GRADE ANSWER 1 points-->
news = pro.news(src='sina', 
                date=start,
                end_date=end,
)
```

我们把获取的新闻数据先保存到本地，以免后面还可能进行其它挖掘。

要求：实现一个fetch_news方法，输入参数为start, end（待获取新闻的起止时间），获取的数据写入到$data_home目录下，文件名为{start}-{end}.news.csv：

**你的答案**
<!-- MANUAL GRADE ANSWER  3 points-->
```python
def fetch_news(start, end):
    # tushare对新闻接口调用次数及单次返回的新闻条数都有限制
    # 我们姑且设置为每30天做为一批次调用
    # 如果是production code，需要仔细调试这个限制，以免遗漏新闻
    date_range = pd.date_range(start=start, end=end)
    dates = pd.DataFrame([], index = date_range)
    freq = '30D'
    grouped = dates.groupby(pd.Grouper(freq=freq))
    groups = [group for _, group in grouped][::-1]

    for group in groups:
        period_start, period_end = group.index[0], group.index[-1]
        start = period_start.strftime('%Y%m%d')
        end = period_end.strftime('%Y%m%d')

        news = pro.news(src='sina', 
                        date=start,
                        end_date=end,
        )

        csv_file = os.path.join(data_home, f"{start}-{end}.news.csv")
        news.to_csv(csv_file)
        # 每小时能访问20次
        time.sleep(181)
```

现在，调用fetch_news,检查你的$data_home目录下，是否生成了若干{start}-{end}.news.csv文件。

在统计新闻中，上市公司出现的词频时，我们需要先给jieba增加关键词，以免被误分。比如，如果不添加关键词『万科A』，那么它一定会被jieba分解为万科和A两个词。

我们通过init方法来实现：

```python
# MANUAL GRADE ANSWER 1 points
def init():
    stocks = get_stock_list(datetime.date(2024,11,1), code_only=False)
    stocks = set(stocks.name)
    for name in stocks:
        # 要求: 把name加到jieba的自定义词库中
        # BEGIN SOLUTION
        jieba.add_word(name)
        # END SOLUTION
        
    return stocks
```

这里得到的证券列表，后面还要使用，所以作为函数返回值。

接下来，就是统计词频了。

**要求**：
写一个count_words的方法，输入为news和stocks。stocks为股票列表，它由get_stock_list获得。

news是一个DataFrame，它是从$data_home/*.news.csv读取得到的。它每一行都是一条新闻记录，该记录有date, content等字段。content是文本格式。

通过count_words，我们得到每个news中包含上市公司名字的词频。返回一个DataFrame，每一行是一支股票，每一列是每日的词频。参考结果：

```
                     word  count
date                            
2024-11-13 04:53:02  水晶光电      1
2024-11-13 04:51:03  浙商证券      1
2024-11-13 04:51:03  深圳新星      1
2024-11-13 02:06:41  中信证券      1
2024-11-13 02:06:41  华泰证券      1
```

**你的答案**
<!-- MANUAL GRADE ANSWER 2 points-->
```python
def count_words(news, stocks)->pd.DataFrame:
    # BEGIN SOLUTION
    data = []
    for dt, content, _ in news.to_records(index=False):
        words = jieba.cut(content)
        word_counts = Counter(words)
        for word, count in word_counts.items():
            if word in stocks:
                data.append((dt, word, count))
    df = pd.DataFrame(data, columns=['date', 'word', 'count'])
    df["date"] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    # END SOLUTION
    return df
```

然后我们对已下载的新闻进行分析，统计每日词频和移动均值。最终我们应该得到类似下面的输出：

```
df.tail()
                 count     ma_30     ma_60  ma_250
date       word                                   
2024-11-13 黑芝麻      66  2.200000  1.100000   0.264
           鼎泰高科     11  0.366667  0.183333   0.044
2023-12-29 龙源电力     36       NaN       NaN     NaN
2024-11-13 龙版传媒     11  0.366667  0.183333   0.044
           龙芯中科     11  0.366667  0.183333   0.044
```


**你的答案**
<!-- MANUAL GRADE ANSWER 2 points-->
```python
def count_words_in_files(stocks, ma_groups=None):
    # BEGIN SOLUTION
    ma_groups = ma_groups or [30, 60, 250]
    # 获取指定日期范围内的数据
    results = []

    files = glob.glob(os.path.join(data_home, "*.news.csv"))
    for file in files:
        news = pd.read_csv(file, index_col=0)

        df = count_words(news, stocks)
        results.append(df)

    df = pd.concat(results)
    df = df.sort_index()
    df = df.groupby("word").resample('D').sum()
    df.drop("word", axis=1, inplace=True)
    unstacked = df.swaplevel().unstack(level="word").fillna(0)
    for win in ma_groups:
        df[f"ma_{win}"] = unstacked.rolling(window=win).mean().stack()
    
    # END SOLUTION
    
    return df

count_words_in_files(stocks)
```
