<mxfile host="app.diagrams.net" modified="2024-02-21T09:05:56.312Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:122.0) Gecko/20100101 Firefox/122.0" etag="c_BVswpUbcM-f5Qp9A43" version="23.1.5" type="device">
  <diagram name="第 1 页" id="OUW2WI1Uj0DLiXIp_DjS">
    <mxGraphModel dx="954" dy="589" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="VfaMSCVObt2lvbHtnSOK-4" value="原始数据获取" style="html=1;shadow=0;dashed=0;align=center;verticalAlign=middle;shape=mxgraph.arrows2.arrow;dy=0;dx=10;notch=10;hachureGap=4;" vertex="1" parent="1">
          <mxGeometry x="48" y="80" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-5" value="因子生成" style="html=1;shadow=0;dashed=0;align=center;verticalAlign=middle;shape=mxgraph.arrows2.arrow;dy=0;dx=10;notch=10;hachureGap=4;" vertex="1" parent="1">
          <mxGeometry x="181" y="80" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-6" value="因子预处理" style="html=1;shadow=0;dashed=0;align=center;verticalAlign=middle;shape=mxgraph.arrows2.arrow;dy=0;dx=10;notch=10;hachureGap=4;" vertex="1" parent="1">
          <mxGeometry x="309" y="80" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-8" value="单因子检验" style="html=1;shadow=0;dashed=0;align=center;verticalAlign=middle;shape=mxgraph.arrows2.arrow;dy=0;dx=10;notch=10;hachureGap=4;" vertex="1" parent="1">
          <mxGeometry x="437" y="80" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-9" value="收益模型" style="html=1;shadow=0;dashed=0;align=center;verticalAlign=middle;shape=mxgraph.arrows2.arrow;dy=0;dx=10;notch=10;hachureGap=4;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="572" y="80" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-10" value="风险模型" style="html=1;shadow=0;dashed=0;align=center;verticalAlign=middle;shape=mxgraph.arrows2.arrow;dy=0;dx=10;notch=10;hachureGap=4;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="700" y="80" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-11" value="&lt;div&gt;数据源连接&lt;/div&gt;&lt;div&gt;（Akshare/聚宽）&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="48" y="180" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-12" value="量价数据" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="48" y="285" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-13" value="财务数据" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="48" y="375" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-14" value="其它数据" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="48" y="485" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-15" value="定义算法" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="181" y="180" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-16" value="生成因子" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="181" y="290" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-17" value="按截面保存因子" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="181" y="375" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-20" value="缺失值处理" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="309" y="180" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-21" value="去极值" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="309" y="282" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-22" value="中性化" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="309" y="383" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-23" value="标准化" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="309" y="485" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-25" value="因子统计分析" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="437" y="180" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-26" value="行业市值中性" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="437" y="256" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-27" value="T检验" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="437" y="333" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-28" value="分层回测" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="437" y="485" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-29" value="IC检验" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="437" y="409" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-30" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="557" y="521" as="sourcePoint" />
            <mxPoint x="557" y="71" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-31" value="多重共线性" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="572" y="180" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-32" value="大类因子合成" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="572" y="256" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-33" value="因子正交" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="572" y="333" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-34" value="组合权重优化" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="572" y="485" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-35" value="多因子模型构建" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="572" y="409" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-36" value="协方差矩阵估计" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="700" y="180" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-37" value="因子暴露控制" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="700" y="256" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-38" value="模型构建" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="700" y="333" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VfaMSCVObt2lvbHtnSOK-39" value="业绩归因" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#a8a8a8;fontColor=#999999;" vertex="1" parent="1">
          <mxGeometry x="700" y="409" width="100" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
