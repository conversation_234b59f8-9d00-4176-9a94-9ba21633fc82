---
title: 一周交一次作业，顶得住就来学！
date: 2024-09-24
category: career&figure
slug: 
motto: 
img: https://images.jieyu.ai/images/university/yale-new-haven-autumn.jpeg
stamp_width: 60%
stamp_height: 60%
tags: []
---

有人问，自学量化交易太难坚持了，能不能组织学习小组一起学习? 其实，我们有了直播课程，也有了答疑群，离学习小组，就只差一个作业系统的距离了。

如果有这样一个系统，让学员能定时交作业，得到老师的批改和反馈，这样学习就有了进度标尺和掌握度标尺，效果就会好很多。这就是一种学习小组的形式。

---

在QuanTide之前的课程中，虽然也引入了习题，但并没有交作业的概念。因为缺少一个系统的作业分发、收集、批改和反馈机制。学员人数一多，分发和批改作业就变得实际上难以执行，所以一直是只布置了习题，没有催作业、批改作业的过程，习题是统一讲解。


后来刷到nbgrader，了解到UC berkley也在用这个系统，就决定尝试一下。花了两周左右的时间，把它集成到了课程环境中。

运行了几周，也得到学员的反馈，直接和间接的，效果确实不错，跟大家汇报一下。

## 习题内容？

习题内容是对教材、视频讲解的延伸和递进。

每一课的教材、视频和习题，都是围绕同一个主题，但又有自己独特的内容。

![](https://images.jieyu.ai/images/hot/course/factor-ml/video-cover.jpg)

视频是讲教材中的难点和重点，通过视频、声音、动画、现场编码的方式使得课程内容更容易理解；习题会选择同一个主题，在教材和视频中有所涉及、但又没全讲的内容，让学员通过练习和自已的尝试，加深对主题的理解。同时，学员程度不一样，习题可以放一些拓展和延伸话题，让不同程度学员可以自行选择。

比如，第二课教材讲到了CAPM，但没有展开。我们就在这一课习题里介绍了四种不同的回归计算方法，作为对CAPM的拓展；回归方法是因子检验的基础方法，有必要让大家对其有透彻了解。

---

实际上，四种方法的数学原理完全一致，但在实现上却存在微妙的差别。

因此，如果你按照自己的理解来套用不熟悉的实现，很可能会出错。比如statsmodels中的OLS.fit与sklearn中的LinearRegression.fit，不仅函数名相同，参数也相同，但它们传参的顺序不一样，同样的输入，会得到不同的结果。

所以，习题某种程度上是另一份教材。一门课程，实际上提供了三倍的内容。


这是课程中的【真题】一例：

【习题引用开始】
<hr>

## 2. 多资产因子计算

### 2.1. groupby.apply

在教材中，计算多资产的因子时，我们使用的是 for 循环。这段代码如下：

```python
# READ-ONLY
# 2. 计算因子
factors = []
for group in barss.groupby(level='asset'):
    close = group[1].close

    slopes = rolling_slope(close.to_numpy(), 10)
    factors.append(pd.Series(slopes, index=group[1].index))
```

在讲课时，我们介绍了如何使用 groupby.apply 来消除循环。现在，请你来完成代码。

---

```python
import time

def rolling_slope(close: NDArray, win:int, *args):
    if len(close) < win:
        return np.full((len(close), ), np.nan)

    # 将 CLOSE 转换为滑动窗口二维数组，保存在 TRANSFORMED 变量中
    # YOUR CODE HERE
    raise NotImplementedError()
    
    slopes, _ = np.polyfit(np.arange(win), transformed.T, deg=1)

    # POLYFIT 返回的一维数组长度不及 CLOSE, 需要左补全（用 NP.NAN 填充）
    # 请将 SLOPES 补全为 LEN(CLOSE) 长度，保存为 SLOPES
    # YOUR CODE HERE
    raise NotImplementedError()
    return slopes

def wrapper(group):
    slopes = rolling_slope(group["close"].to_numpy(), 10)

    # 将 SLOPES 转换为 DATAFRAME, 以日期为索引，列名为 FACTOR，保存为 DF
    # YOUR CODE HERE
    raise NotImplementedError()
    return df

# 1. 获取行情数据
start = datetime.date(2023, 12, 1)
end = datetime.date(2023, 12, 29)
universe = 2000
barss = load_bars(start, end, universe=universe)

t0 = time.time()
factors = barss.groupby(level='asset').apply(wrapper)
print(f"Pandas Groupby.apply[{universe}] 耗时：{time.time() - t0}")

factors.tail()

PD_VERSION = pd.__version__
```

---

在课程环境中运行时，因子计算本身的耗时应该在 1.3 秒左右。这是同时计算 2000 个资产的速度。因此，如果把 A 股全部资产计算一次，也只需要 4 秒钟。

### 2.2. 使用 modin

Modin 是 pandas 的 drop-in 替换品。Pandas 是单线程的（在多数任务上），Modin 则可以让你使用上多核。它能更好地用以较大的数据集。在 Modin 的背后，是 Dask 或者 Ray。后者是构建了 ChatGPT 分布式计算平台的框架。

请查询 Modin 官方文档，在你的课程环境中，安装带 Ray 支持的 Modin 版本。

```python
# 导入 MODIN，以替换 PANDAS
# YOUR CODE HERE
raise NotImplementedError()

t0 = time.time()
factors = barss.groupby(level='asset').apply(wrapper)
print(f"Pandas Groupby.apply[{universe}] 耗时：{time.time() - t0}")

factors.tail()
MODIN_VERSION = pd.__version__
```

比较耗时的结果可能让你有点失望。使用 modin 之后，运算速度竟然大幅下降了。

---

没关系，这只是因为我们的计算量太小了。Modin 在启动时，大量的时间花在启动 Ray 平台上，以及 docker 容器本身带来了一些限制。

2.3. 使用 duckdb 或者 polars

如果你对进一步提升性能感兴趣，可以考虑使用 duckdb 或者 polars。duckdb 和 polars 在启动并行运算时，并不需要启动额外的进程，也就不需要进程间的数据交换，因此适用场景下，性能会比 modin 强很多。

提示：如果你使用 duckdb，你可能需要使用 UDF（user define function）。

【习题引用结束】

<hr>

从第2课到第4课，我们讲的都是因子检验的原理和实现技术，所以，我们需要了解单资产因子计算、单资产滑动窗口下的因子计算以及多资产的因子计算。

写出高性能的代码对学员的编码要求较高。对代码能力强的学员，应该引导他们在课程的基础上，适当拓展得远一些；但对代码能力弱一些的学员，应该帮助他们就在pandas的框架里，通过反复练习，掌握基础、功能够用、性能基本满足要求的实现。因此习题就按层层递进的方式来编排。

---

对代码基础一般的学员，需要掌握如何通过pandas的groupby.apply来实现多资产因子计算。实现了这一步，对全A超5000个资产同时计算因子，也只需要4秒钟，这个速度肯定是够用了。掌握了这些，足以在工作中**独挡一面**。

在此基础上，我们也进一步引导大家都掌握modin。

对少数学有余力的学员，则可以根据我们的提示，自行探索duckdb和polars，这样可以在毫秒级尺度上，完成这些计算。掌握了这些，你就可以成为工作中的**承重墙**！

**如果你正在寻找量化岗位工作，可能会问，这些练习题的难度怎么样**？这是我们一名正在应聘某私募的学员的面试真题，当时他在理解试题上遇到困难，向老师求助：

![](https://images.jieyu.ai/images/2024/09/interview-questions.png)

可以看出来，我们的初级题就完全覆盖了这些面试真题。把课程学完，再加上掌握一些面试宝典，可以轻松驾驭任何面试！

---

## 作业流程

习题分自动评分习题和人工阅卷习题。

自动评分习题都是代码实现题，每一道题后面都附有测试。学员可以在提交前，反复尝试以找出正确答案。人工阅卷题则多是文本回答，也有部分代码题。每周一前，老师会发布最新课程习题，通过下面的菜单下载习题：

![](https://images.jieyu.ai/images/2024/09/fetch-assignments.jpg)

作业下载后，直接在课程环境中打开。每道习题都给出了足够的提示，并且都是层层递进的，也因此大大降低了难度。你只需要根据提示，把自己的代码实现写在 YOUR CODE HERE 的位置，并删除 raise NotImplementedError() 这行代码。

---

下图显示了其中的一道作业。

![](https://images.jieyu.ai/images/2024/09/how-exercise-looklike.jpg)


每一道题在发布前，都有了答案（或者参考答案），作业提交后，老师进行批改并发送反馈之后，学员就可以看到答案（或者参考答案），以及老师的评语。

![](https://images.jieyu.ai/images/2024/09/exercise-comment.jpg)

--- 

很多时候，学员的实现只是功能够用，但不是最高效地实现方式，这时候就可以从老师评语中得到帮助，

比如，这是对一位同学的批改情况，老师提供了更高效、简练的实现：

![](https://images.jieyu.ai/images/2024/09/exercise-comment-2.jpg)

尽管这是一道自动评分题，但老师还是查看了学员的实现，并提供了修改意见。

有了这样的交互，学员很快就能成为量化高手啦！不过，一周要交一次作业，你敢挑战吗？
