# 欢迎加入QuanTide量化课程！

<img src="https://images.jieyu.ai/images/hot/quantfans.png?1" width="150px" align="right">

Hi! 我是助教宽粉，跟您一样，也是正在学习量化。

## 1. 正在开讲的量化课程

### 1.1. 量化人的Numpy和Pandas

Numpy与Pandas是量化领域不可缺少的工具。课程的特点是，紧密结合量化场景来讲解Numpy&Pandas知识，确保把量化场景中高频使用的API、以及使用它们需要理解的前置知识原理讲透，在工作中见名知义，能灵活组合使用。

本课程适合尚处在入门期的量化学习者和数据科学的学习者。如果已经是从业多年的老量化人，但感觉自己的程序性能不高、不简洁，那很可能也需要回锅学习。

### 1.2. 量化24课

量化24课是一门中级课程，涵盖了量化交易全流程，学完后将拥有完整的量化技能，可以独立从事量化交易。适合独立交易者、打算转量化的码农、正在从事主观交易的基金、私募从业者。这门课开课以来，有不少私募总、基金业者、海外名校博士及互联网大厂员工在学习。

这门课程可以在[这里预览](http://ke.quantide.cn/course/l24/preview)，密码是 quantide

### 1.3. 因子分析与机器学习策略

因子分析与机器学习策略（即**本课程**）是一门2025年2月推出的高级课程。适合已经掌握了一定量化交易知识，打算在因子和策略方向上持续深耕的学习者。

该课程前1/3将为您介绍超过400+因子（含付费因子），如何进行因子检验及发现属于自己的新因子。在拥有了大量可用因子的基础上，后2/3课程将介绍如何用最先进的机器学习模型，将这些因子组装成策略。最终，您将带走350+因子（有代码）和三个可以实用的机器学习模型。

## 2. 如何使用本环境

### 2.1. 文件说明

<i> 我们提供了多个环境，不同环境的课程不一样，数据和程序库也不同，请注意区分。</i>

1. 课程指南： 即本文件。
2. courseware: 课程的 notebook。
3. assignments: 课程作业，问题在questions目录下，答案在answers目录下。您可以随时从问题跳转到答案上。
4. supplements: 课程的补充阅读材料。
5. noteboo入门及notebook高级技巧：本教程通过 Jupyter Notebook 来构建和组织。如果您对 Jupyter Notebook比较陌生，请先阅读 Notebook 入门.ipynb这个文件；即使您对 Jupyter Notebook 非常熟悉，也建议您阅读 notebook高级技巧.ipynb 这个文件，您一定能找到新的知识点。

### 2.2. 课程环境说明

在此环境中，您将能利用以下功能：

1. 利用Alphalens进行因子分析。
2. 编写和运行机器学习策略。
3. 使用2018年到2023年的A股所有日线数据（高级用户可以使用从2005年到2023年的日线数据）（以下不再特别说明）。
4. 高级用户可以使用我们提供的 Tushare Pro 账号，获取高级别的商用数据（以下不再说明）。


### 2.3. 环境中的公共数据和API

在本环境中，我们提供了2005年到2023年的A股所有日线数据，其它数据可以通过akshare和Tushare 来获取。数据读取方法在 <span style="font-size: 16px;color:blue;text-decoration:underline">[如何访问数据](supplements/data.ipynb)</span> 文件中。

以下是通过 `load_bars` API 获取的数据的示例：

```python
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
barss = load_bars(start, end, ("000001.XSHE", ))
barss.tail(2)
```

更多使用方法，请见 <span style="font-size: 16px;color:blue;text-decoration:underline">[如何访问数据](supplements/data.ipynb)</span> 文件中。

## 3. 金融平民化运动

我们非常认同 Fawcett (Quantopian CEO)关于金融平民化的理念。我们认为，在国内，金融平民化运动应该包含两方面的内容，一方面要致力于传播正规的量化交易知识与理念，减少普通人被割韭菜的机会；另一方面，要提供低价甚至免费的工具、书籍和课程，让更多的人有机会学习到正规的量化课程。

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/06/20250702093056.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>Fawcett - Quantopian创始人</span>
</div>

为了践行金融平民化理念，我们已经提供了以下免费产品和服务：

### 3.1. 开源量化库

从2019年以来，我们就开始Zillionare量化框架的开发，前后投入约300万元。这个项目已经发布了2.0，采用先进的时序数据库，在运行中，已经管理了超过40亿条行情数据。

除了Zillionare之外，我们还开发了许多其它开源项目。

* [Python Project Wizard](https://pypi.org/project/ppw/)，已经成为许多人开启新Python项目的起手式。
* [Moonshot](https://github.com/zillionare/moonshot)，Alphalens在月度因子分析上有一些缺陷。这个库补齐了这块短板。
* [Quantstats-Reloaded](https://github.com/zillionare/quantstats-reloaded)，Quantstats是一个非常常用的量化策略评估及可视化工具。它已经在Python 3.12以上版本中不能使用了。我们接手维护了这个产品。

### 3.2. 免费书籍

我们出版了《Python高效编程实战指南》（机械工业出版社），根据我们与版社达成的协议，这本书的电子版可以在我们的[网站上](https://www.jieyu.ai/articles/python/best-practice-python/chap01/)免费阅读。

### 3.3. 免费课程

#### 3.3.1. 量化交易场景下的Numpy和Pandas

这是量化人必须掌握的量化母语。这门课程我们免费提供网页版（在有些平台，因为无法放链接，可能会以一元课的形式发布），您可以在[匡醍官网](https://www.jieyu.ai/)免费阅读。

#### 3.3.2. 因子分析与机器学习策略

我们免费开放了这门课程的部分视频（在有些平台，因为无法放链接，可能会以一元课的形式发布）您可以在[QuanTide@B站](https://space.bilibili.com/1229001873)观看。

但是我们也必须生产付费产品。我们的付费产品主要是基于提供的计算资源、数据、实用模型和辅导来进行定价。**感谢我们的付费学员！正是你们的赞助，我们才能有时间做一些免费的项目。**

## 4. Follow us

<div style="width: 100%;margin-bottom: 20px;">
<h3>Quantide@小红书 （5万粉）</h3>

<div style='width:120px;float:left;padding: 0.5rem 1rem 0 0;text-align:center'>
<img src='https://images.jieyu.ai/images/hot/xhs-logo.jpg'>
</div>


我们在[小红书](https://www.xiaohongshu.com/user/profile/5ba12feef7e8b9437f3aca0c)上有接近5万人关注。如果您按量化关键词进行搜索，再按用户排序，一眼就能看到我们。

欢迎点击[Follow](https://www.xiaohongshu.com/user/profile/5ba12feef7e8b9437f3aca0c)

</div>


<div style="width: 100%;margin: 60px 0;text-align: left;">
<h3>Quantide@公众号（万粉）</h3>

<div style='width:120px;float:left;padding: 0.5rem 1rem 0 0;text-align:center'>
<img src='https://images.jieyu.ai/images/hot/logo/gzh.jpg'>
</div>

图文为主，也包含『量化好声音』播客。

内容涵盖 Python 编程和量化策略研究、量化框架开发。

通过公众号后台消息，可以联系到课程助理。欢迎扫码关注。
</div>

<div style="width: 100%;margin-bottom: 20px;">

<h3>Quantide@知乎 （万粉）</h3>

<div style='width:120px;float:left;padding: 0.5rem 1rem 0 0;text-align:center'>
<img src='https://images.jieyu.ai/images/hot/logo/zhihu.png'>
</div>

我们在[知乎](https://www.zhihu.com/people/hbaaron)上有1.2万粉丝。开设有『大富翁之路』、『Daily Dose of Python』和『遐思录』等专栏。

欢迎点击[Follow](https://www.zhihu.com/people/hbaaron)
</div>
