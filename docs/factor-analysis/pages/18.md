---
aspectRatio: 16/9
title: 第 18 课 LightGBM
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--
大家好，我们开始上课了。

在量化交易中，我们用到的因子数据，特别是排序后的各种因子，本质上是一种表格型数据。适用于这种类型数据的人工智能算法主要有梯度提升决策树算法，比如xgboost和lightgbm，以及深度学习算法，tabnet。tabnet中的tab是table的意思。

但是在小规模数据集和带较强噪声的数据集上，xgboost和lightgbm到目前为止，仍然是最好的模型。而量化交易中我们能得到的数据，刚好具有小规模和带强噪声的特点。

现在，我们就正式开始今天的课程，为大家介绍地球最强表格型数据集的机器学习算法LightGBM。
-->

---
title: /01 决策原理
layout: section
---

<!--
在第13章我们就已经演示了一个最基础的决策树模型，但没有深入它的原理。在这节课里，因为要涉及到优化问题，所以，我们有必要再回到决策树的构造原理上，去解释清楚其中的一些概念。
-->

---
title: 决策树的构造原理
src: 18/10.md
---

---
title: 决策树的优缺点
src: 18/20.md
---

---
title: /02 决策树的改进
layout: section
---

<!-- 既然单棵树存在着不稳定、不能拟合复杂规律的问题，很自然地，人们想到，能否使用多棵树来解决这个问题呢？

-->

---
title: 随机森林
src: 18/27.md
---

---
title: 梯度提升算法
src: 18/30.md
---

---
title: XGBoost
src: 18/33.md
---


---
title: LightGBM
layout: section
---

<!--现在，我们就进入这一章的重点，也就是LightGBM。-->

---
title: Dataset
src: 18/40.md
---

---
title: 训练模型
src: 18/43.md
---

<!--
现在，我们就完成了本章所需要的几乎一切预备知识。 下面，我们来看如何组装这些知识，形成一个策略。
-->

---
title: 模型评估与可视化
src: 18/46.md
---

---
title: 交叉验证
src: 18/49.md
---

<!--
现在，我就带大家看一个完整的实例。这个例子在教材中有，同时也提取出来，放在了补充材料中。
-->

---
title: 参数优化
src: 18/50.md
---

---
title: Sklearn风格的API
src: 18/55.md
---

---
title: end
layout: end
src: 18/60.md
clicks: 1
---
