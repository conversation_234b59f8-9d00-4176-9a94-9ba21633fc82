---
aspectRatio: 16/9
title: 第 16 课 模型优化方法
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--
大家好，我们开始上课了。今天课程的内容是如何进行模型优化。

这节课是机器学习基础理论部分的最后一节课。学完这一课，尽管不能说精通机器学习，因为我们没有介绍构建一个新的机器学习模型的底层能力，但我们在创建模型和训练时，就基本上不会再遇到新的概念了，我们基本上能看懂各个参数的含义，也大致知道如何选择。

好，现在我们就正式进入今天的课程。
-->

---
title: /01 训练算法的优化
layout: section
---

<!--
今天课程的主题是优化。我们将简单介绍训练算法的优化以及超参数优化，以及与此相关的交叉验证。我们先从训练算法的优化开始。
-->

---
title: 更底层的概念
src: 16/10.md
---

---
title: 算法优化
src: 16/20.md
---

---
title: /02 交叉验证
layout: section
---

---
title: k-fold
src: 16/30.md
---

---
title: Stratified K-Fold
src: 16/40.md
---

---
title: 留一法
src: 16/50.md
---

---
title: 时间序列交叉验证
src: 16/60.md
---

---
title: 滚动预测
src: 16/70.md
---

---
title: /03 参数搜索
layout: section
---

<!--在交叉验证一节中，我们已经看到，在训练 LogisticRegression 模型时，最大迭代次数会影响模型的性能。这个最大迭代次数，max_iter，一般被称作超参数。超参数（Hyperparameters）是指在机器学习模型训练之前就已经设定好的参数，它们决定了模型的结构和行为，但不是从训练数据中直接学习得到的。

超参数是由研究人员根据经验手动设定的。这种方法不够系统，有可能错过最佳组合。因此，人们发明了参数搜索技术。
-->

---
title: 网格搜索
src: 16/80.md
---

---
title: 随机搜索
src: 16/83.md
---

---
title: 贝叶斯优化
src: 16/86.md
---


---
title: end
layout: end
src: 16/90.md
clicks: 1
---
