---
aspectRatio: 16/9
title: 第 14 课 机器学习核心概念
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--

大家好，我们开始上课了。

第13课中，我们回顾了人工智能的发展历史，也通过决策树的例子，探讨了机器学习的本质。我们知道机器学习实际上是通过数据来寻找逻辑，把一堆if-else变成了数值之间的比较，从而生成了一个复杂的函数。

但是，机器学习又是如何仅仅是通过看数据，就能学习到逻辑的呢？这中间还缺了不少关键概念。这一课，我们就来补齐其中的核心概念

当然，由于课程时长的限制，我们不可能从上到下把全部机器学习的概念都讲完。那我们要如何取舍呢？我们取舍的标准是，选取机器学习中偏应用这一侧的核心概念。如果不懂得这些概念，你大概率无法选择模型的参数、无法理解优化过程。但在这些概念之下，还有线性代数、梯度优化、反向传播等更基础的概念，这些我们就不得不囫囵吞下了。

好，下面我们就开始今天的课程。

-->

---
title: /01 误差
layout: section
---

<!--
我们人类常常是从错误中学习和进步的。机器学习也是一样，各种与错误相关的概念，在机器学习中特别重要。

下面，我们就来一一介绍这些概念。
-->

---
title: 偏差、方差、残差和误差
src: 14/10.md
---


---
title: /02 过拟合与正则化
layout: section
---


---
title: 过拟合与正则化惩罚
src: 14/20.md
---

---
title: /03 损失函数
layout: section
---

---
title: 损失函数、成本函数和目标函数
src: 14/30.md
---

---
title: 分类问题中的损失函数
src: 14/33.md
---

---
title: 回归问题中的损失函数
src: 14/40.md
---

---
title: 如何选择损失函数
src: 14/50.md
---

---
title: /04 度量函数
layout: section
---

<!--度量函数 metrics，或者有时候我们把它称为评估函数，是用来评价一个经过训练后的模型的性能好坏的。

很显然，像MSE这样的损失函数本身就可以评价模型的好坏，对吧？如果一个模型预测值与真实值之前的MSE很小，显然这个模型也就很好。并且，我们在训练时，也是根据损失函数的值来决定是不是要停止训练的。

那么，为什么还要多此一举，再定义评估函数这个概念呢？

从数学上讲，损失函数是供优化用的，它必须满足可以最优化这一数学限制。寻找局部最优解的方法就是牛顿法、梯度下降等等。要能实施这些方法都是有条件的。

度量函数则在打破这些限制，为我们提供了更广泛的评估能力。一旦打破了这些限制，我们也就不能拿它们当损失函数来使用了。
-->

---
title: 准确率、精确率、召回率、F1分数
src:  14/60.md
---

---
title: AUC、ROC和困
src:  14/70.md
---

---
title: /05 距离函数
layout: section
---

<!--
从损失函数到目标函数，它们都是适用于监督学习的，有着明确的优化方向，这几类函数的本质都是计算预测值到真实值之间的距离。在聚类算法中，数据没有标签（即真实值），所以，这几类函数派不上用场了。于是，距离函数就成为聚类的核心算法。

下面，我们就介绍距离函数。
-->

---
title: 距离函数
src: 14/80.md
---

---
title: end
layout: end
src: 14/90.md
clicks: 1
---
