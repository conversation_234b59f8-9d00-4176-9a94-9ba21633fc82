---
aspectRatio: 16/9
title: 第 7 课 Alphalens 参数调优
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
defaults:
    layout: two-cols
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
---

<!--

大家好，我们开始上课了。

因子分析绝不是有一个想法，编码成因子，用 Alphalens 检验并生成报告这么简单。

它本质上是一个探索过程，在这个过程中，Alphalens 是一个工具，而不是一个裁定者。我们需要借助 Alphalens 给出的各种报表、包括中间结果，来修正我们的设想，把分析推向深入，直到最终找到因子与收益之间的本质联系。

这一讲，我们就来介绍如何通过调节参数，从而得出最有利的结论。

当然，我们也要时刻警惕过拟合像小偷一样溜了进来。对抗小偷的武器，就是参数高原检测和样本外检测。

好，现在我们就进入正题。

-->

---
title: 重构因子检验流程
src: 07/10.md
---

---
title: 参数调优
layout: section
---

# 02 参数调优

<!--
在准备好了Alphatest函数之后，我们就开始调参之旅
-->

---
title: 修正因子方向
src: 07/20.md
---

---
title: 过滤非线性分层
src: 07/30.md
---

---
title: 使用最佳分层方式
src: 07/40.md
---

---
title: 使用 grid search 搜索最佳参数
src: 07/50.md
---

---
layout: section
title: 过拟合检测
---

# 03 过拟合检测

<!--
我们刚刚尝试了一系列的优化动作，包括调整因子计算方法、修改分层方法、只取部分分层参与检验等等。

最终我们在2000支样本的基础上，在2018年到2023年的6年间，得到RSI因子的年化Alpha超过34%，累计收益高达3.5倍。

当我们看到这么好的结果时，很自然就会问，这个结果是我该得的吗？

这是一个很严肃的问题。如果我们做出了错误的判断，就会把一个错误的因子纳入实盘，从而引起亏损。

现在，我们就来介绍如何确定我们得到的报告是真实的、鲁棒的，而不是过拟合产生的。
-->

---
title: 参数高原
src: 07/60.md
---

---
title: 样本外检测
src: 07/70.md
---

---
title: 关于多空组合的思考
src: 07/80.md
---

---
title: thanks
src: 07/90.md
layout: end
clicks: 1
---
