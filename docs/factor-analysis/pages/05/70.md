---
clicks: 13
layout: two-cols
---

<div class="abs top-25% left-55% w-80% h-full" v-motion
    :enter="{x: '0%', y:'0%'}"
    :click-1="{x: '-70%', y:'-25%'}">

```python {lines:false}
create_information_tear_sheet(factor_data,
                                group_neutral=False,
                                by_group=True)
```
</div>


::right::

<v-switch>

<template #1-11>

![IC 分析概览](https://images.jieyu.ai/images/2024/08/ic-overall.jpg?width=300)
</template>

<template #1>

<v-drag pos="0,78,476,60">
<Box />
</v-drag>
</template>

<template #2>

<v-drag pos="11,164,476,60">
<Box />
</v-drag>
</template>

<template #3>
<v-drag pos="29,206,476,60">
<Box />
</v-drag>

<FlashText font-size="2vw" class="top-50% left--80%">stats.ttest_1samp</FlashText>
</template>

<template #4>
<v-drag pos="-7,245,476,60">
<Box />
</v-drag>
</template>

<template #5-8>
<v-drag pos="-3,286,476,60">
<Box />
</v-drag>
</template>

<template #6>
<CountdownTimer :at=6 :count=15 q="skew是正数好，还是负数好？"/>
</template>

<template #8-11>
<v-drag pos="4,332,476,60">
<Box />
</v-drag>
</template>

<template #9>
<CountdownTimer :at=9 :count=10 q="正态分布的峰度是多少？"/>
</template>

<template #10>
<FlashText font-size="2vw" class="top-40% left--80%">原始峰度</FlashText>
<FlashText font-size="2vw" class="top-50% left--80%">超额峰度 Excess Kurtosis</FlashText>
<FlashText font-size="2vw" class="top-60% left--80%">stats.kurtosis</FlashText>
</template>

<template #11>

![1D 收益信息系数图](https://images.jieyu.ai/images/2024/08/IC-1D.jpg)

</template>

<template #12>

![IC 直方图和 QQ 图](https://images.jieyu.ai/images/2024/08/ic-hist-qq.jpg)
</template>

<template #13>

![分组 IC 信息](https://images.jieyu.ai/images/2024/08/ic-by-group.jpg?width=75%)
</template>
</v-switch>

<!--
Alphalens中，API create_information_tear_sheet创建IC分析报告。这里有三个参数，含义与同类接口相同，我们就不解释了。要注意这里的factor_data是调用get_clean_factor_and_forward_returns之后的返回值。

这个API的输出结果如右图所示。

[click]

IC均值就是我们通常所说的因子的IC。Alphalens 的文档认为，这个均值在 0.1~0.3 之间就是表明因子有良好的预测能力，但实际上，如果这个值能达到 0.02 以上，就是非常值得我们考虑的因子。达到0.1以上是比较困难的。

[click]

Risk-Adjusted IC 就是用IC除以其标准差的结果，也就是用图中的第一行除以第二行，得到的结果。会有一点小误差，是四舍五入造成的。

这个数值通常用来衡量因子在考虑风险后，它的预测能力的稳定性。这个数值是越大越好。要理解这个指标，可以把它与夏普率类比。夏普指标怎么算的？收益除以标准差。

[click]
 
t 统计量是用来检验因子 IC 是否显著不同于零的统计量。

它是用各期的 IC 值，通过 stats.ttest_1samp 来计算的。

这一组里很多指标，比如t值、p值、skew和kurtosis，都是用的scipy 的 stats 模块。


通常情况下，t 统计量的绝对值大于 2, 就表示统计上显著，也就是这个因子的IC值是可靠的。

[click]
 
p 值是 t 统计量的伴随概率，一般要小于0.05，这个因子才可用。

[click]

IC Skew 用来衡量 IC 分布的不对称程度。在我们的示例中，IC为正，偏度为负，大家说一说，这种情况是好还是坏？

[click]

大家可以把答案发在聊天区。我们等待15秒钟。

[click]

答案是，偏度为负数一般更好。因为它表明因子倾向于产生更多的正 IC 值，在多数时间收益较高。但是，在某些极端情况下可能出现较大的回撤。

[click]

峰度表明衡量 IC 分布的尾部厚度和尖峰程度。这里问一个问题，正态分布的峰度是多少？请大家打出数字。我们给10秒钟的时间。

[click]

我们揭晓答案。正态分布的峰度，未调整前是3，调整之后是0。

[click]

这也就引出来超额峰度的概念，跟我们这里的kurtosis的值密切相关。当我们得到一个随机变量的峰度时，很显然我们会下意识地将它与正态分布相比较。但是，如果正态分布自己的峰度是3，这个数字就比较奇怪。我们更喜欢像0，1等等这样的数字。

于是就有了超额峰度一说。将一个分布的峰度减去3，就转换成了超额峰度。所以，在超额峰度概念下，正态分布的峰度是0。

很自然我们还要问，Alphalens使用的是原始峰度，还是超额峰度？Alphalens是通过scipy.stats模块来计算kurtosis的，这个函数计算出来的峰度是超额峰度。

所以，我们这里得到的小于零的kurtosis，实际上表明它非常接近正态分布。

那么峰度是大于零好，还是小于零好？答案是，在因子分析中，超额峰度小于零更好。小于零的峰度表明数据分布比正态分布更平缓，尾部更轻，因子的稳定性更好。

[click]

接下来，IC分析还会输出这些图表。

首先是各期的收益IC图。它实际上是把IC均值在时间上进行了展开。阅读这张图，主要也是看波动大小、均值，以及在不同时间点上的异常情况。

[click]

接下来是IC直方图和QQ图。收益IC图是对第一个图中，IC均值的时间展开；IC直方图是对第一个图中，哪些指标的展开？

IC直方图是对偏度和峰度的可视化表示。

QQ图帮助我们检查IC的正态性。如果IC是正态的，那么右图应该是一条45度的直线。

如果绘制出来的不是直线，那么我们会期待它是一条s形曲线。这表明 IC 分布的尾部更胖并且包含更多信息。

[click]

这里展示的数据，是没有行业中性化的。如果我们在预处理时，传入了分组信息，那么，我们还会得到按组进行的IC分析报告，

-->
