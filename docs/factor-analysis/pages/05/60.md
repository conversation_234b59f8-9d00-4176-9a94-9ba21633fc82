---
clicks: 4
---

<div class="abs top-15% w-40%">

## 事件分析步骤

<v-clicks>

### 1. 生成事件
<div/>

### 2. 预处理
### 3. 生成报告
</v-clicks>
</div>

<NoteCell class="abs top-10% w-60% left-40%"
        :enter="{opacity: 1}"
        :click-3="{opacity: 0}">

```python {all|29|38-41}{maxHeight: '400px',lines:true,at:1}
from numpy.lib.stride_tricks import as_strided
from alphalens.utils import get_clean_factor_and_forward_returns

def rolling_slope(close: NDArray, win:int, *args):
    if len(close) < win:
        return np.full((len(close), ), np.nan)

    stride = close.strides
    shape = (len(close) - win + 1, win)
    strides = stride + stride
    transformed = as_strided(close, shape, strides)
    
    slopes, _ = np.polyfit(np.arange(win), transformed.T, deg=1)

    left_pad_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_pad_len, 0), mode='constant', constant_values=np.nan)
    return slopes

def wrapper(group):
    slopes = rolling_slope(group["close"].to_numpy(), 10)

    index = group.index.get_level_values(0)
    df = pd.DataFrame(slopes, index=index, columns=["factor"])
    return df

# 1. 获取行情数据
start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 12, 29)
barss = load_bars(start, end, universe=("000001.XSHE",))

# 2. 计算因子
factors = barss.groupby(level='asset').apply(wrapper)
factors = factors.swaplevel(0,1)

# 3. 提取价格数据
prices = barss['price'].unstack(level=1)

# 5. 提取 EVENTS
prev_factor = factors.shift(1)
events = factors[(factors.factor>5e-3) & (prev_factor.factor < 0)]
events.tail()
```
</NoteCell>


<NoteCell class="abs top-10% w-60% left-40%"
        :enter="{opacity: 0}"
        :click-3-4="{opacity: 1}">

```python {all}
event_data = get_clean_factor_and_forward_returns(events, 
                                                  prices, 
                                                  quantiles=1, 
                                                  periods=(1,3,5), 
                                                  filter_zscore=None)
event_data.tail()
```
</NoteCell>

<NoteCell class="abs top-10% w-60% left-40%"
        :enter="{opacity: 0}"
        :click-4-5="{opacity: 1}">

```python {all}
from alphalens.tears import create_event_study_tear_sheet

create_event_study_tear_sheet(event_data, prices, avgretplot=(2,10))
```
</NoteCell>

<!--

Alphalens 主要用于截面分析。在截面分析中，没有时间的概念，每一次分析，都是基于同一时间点的多资产因子数据，除非是因子本身引入了时间的概念。

在另一个维度上，即时间的维度上进行的分析，在Alphalens中被称为事件分析。事件可以是任何东西，但最终能触发我们策略交易的信号。

下面，我们通过代码，来看该如何实现事件分析。

这段代码主体跟之前差不多，也是先提取因子和价格数据。但在最后部分，我们生成了事件数据。

[click]

还有一点不同，在这里我们只传入了一支资产。这里主要是为了回答同学们的提问，实际上，事件分析中，仍然可是传入多资产。这样事件分析的结果，更能说明因子的有效性。

[click]

中间完全一样的部分就直接跳过了。在最后，我们要生成事件数据。

我们先要定义信号。在这里，当因子由负数变为大于0.5%，就发出一个信号。因子仍然是斜率因子。

在信号定义之后，我们就通过信号来提取事件数据。

现在我们运行一下代码，看看生成的events的模样。

这样生成的结果是一些时间上不连续的记录。其它数据被丢弃了。这样才满足Alphalens的分析要求：在事件发生之日，要求有因子数据；在其它日期，要么丢弃这些数据，要么用np.Nan 填充。

但是price数据必须一直都存在。这个可以理解吗？如果price数据不连续，就可能导致远期收益计算上的错误。

[click]

这是预处理部分。这个方法我们已经很熟悉了。它有很多功能，但在这里，它的主要作用就是把因子数据与远期收益合并对齐。这里一定要注意，我们必须把quantiles设置为1.

现在我们运行一下，看看合并后的数据是什么样的。

我们看到，Alphalens已经帮我们计算出了事件发生后几天的收益，并与因子数据进行了合并。我们还看到此时的factor_quantile都是1，这是正常的。

[click]

最后，我们调用这个函数进行事件分析就可以了。

我们运行一下，看看它会生成哪些报表。

第一个报表是事件的时间分布图。我们能从事发现哪些信息呢？我们可以从中发现这样一些信息，比如事件分布是否均匀，是否是周期性事件，事件最近是否消失？总体上讲，它帮助我们了解该交易信号的频率。

这些报表中，可能最重要的就是累计回报图。

事件分析不是Alphalens的重点。如果大家要做事件分析，使用backtrader可能更容易。我们的大富翁量化框架用来做回测也很容易。这些回测框架在报表上，都要比这里的更为丰富。

-->
