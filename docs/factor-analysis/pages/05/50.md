---
clicks: 2
---

<div class="abs top-10% w-50%">

## 图解

<v-clicks>

## 计算方法 <!-- error band-->
## Good vs Poor
</v-clicks>
</div>

<div class="abs top-10% w-60% left-40%">

![分层离差均值图](https://images.jieyu.ai/images/2024/07/fig-05-06.jpg?width=500)
</div>

<div class="abs top-55% w-50% left-50%" v-click="2">

![好的分层离差均值图](https://images.jieyu.ai/images/2024/08/fa-05-predictive-spread.jpg?width=500)
</div>

<!--

分层离差均值图展示了 top 分层与 bottom 分层之间的收益差随时间的变化。它由三个元素组成，首先是离差均值；然后是离差均值的1月移动平均，最后，它还显示了误差带（error band)。

收益差的单位是 bps。

[click]

这里的spread是用的top分层与bottom分层之间的收益差。分层收益采用的是等权配置法。

如果我们是要为多空策略寻找因子，我们希望top分层与bottom分层之间的spread大，波动小，在不同的时间点上，都表现得很稳定。

[click]

一张"好“的分层离差均值图应该是什么样子呢？下面的图同样取自有未来数据的因子。可以看出，它的均值远高于零轴，error band很窄，移动均线接近于直线。

-->
