---
clicks: 4
---

<div class="abs top-15% w-40%">

## Alpha的单位

<v-switch>

<template #1-5>

## 从Alpha到年化Alpha

</template>

<template #2-5>

## 正常的Alpha取值区间?
</template>

<template #3-5>

## Beta的单位
</template>

<template #4-5>

## 分层收益均值
### bps
</template>

</v-switch>
</div>

<!--right-->

<div class="abs top-15% w-50% left-50%" v-motion
    :enter="{opacity: 1}"
    :click-1="{opacity:0}"
    :click-2="{opacity:1}">

![](https://images.jieyu.ai/images/2024/07/returns-analysis.jpg)
</div>

<Table v-motion class="abs left-50% top-15% w-50%"
    :enter="{opacity: 0}"
    :click-1-2="{opacity:1}">

```yaml
head: date,000001,000002,...,688188,1D
body:
    - 2018/3/15,50,60,...,80,0.03
```
</Table>

<v-drag-arrow color="red" pos="742,190,3,203" v-click="[1,2]"/>
<v-drag-arrow color="red"  v-click="[1,2]" pos="707,419,-427,0"/>

<div class="abs top-50% w-50% left-50%" v-motion v-click="[1,2]">

$$
    y = \alpha + \beta.x + \epsilon
$$
</div>

<div class="abs top-70% w-50% left-74%" v-motion v-click="[1,2]">

alpha
</div>

<Numbers data="0.009,0.01,0.23,...,-0.04,-0.1" 
    v-motion v-click="[1,2]"
    flag="vertical"
    label="Alphas"
    labelPos="left"
    class="abs top-45% w-10% left-20%"/>

<!--
首先来看阿尔法和贝塔.

第一个问题，这里阿尔法的单位是多少？也就是说1D 5D 10D， 也就是说，1D 5D和10D，
他们对应的数值是已经转化成百分比了，还是说是没有转换为百分比的？

我们会问这个问题，是因为当我们在别的地方讨论年化Alpha时，它常常被表示成为一个百分比。

也就是说，这里的 是说这里的-0.214到底是负21.4%还是-21.4%？

答案是这里的-0.214就是负21.4%。 也就是说他是没有经过转换的。

Alphalens的文档没有说明，但是通过阅读他的代码，确实就是如此。 所以如果我们要拿Alphalens的分析结果去做交流，那么我们需要把它转化成为百分比。

[click]

第二个问题，这里的阿尔法是年化Alpha。如果我们要把正在分析的因子跟其他因子的Alpha进行比较，首先我们要确保Alpha数值都是年化的

这个年化是如何进行的？我们先回顾一下，在第二章和第三章里讲的阿尔法是如何计算的。在第二章和第三章里讲的阿尔法算法，也正是Alphalens的算法。

我们是对每一个日期，求横截面上所有因子对收益的回归，这样就得到了对应日期的阿尔法。

我们对因子表的每一行进行遍历，回归得到的阿尔法就形成了一个阿尔法的序列，然后我们就可以求它的均值。

于是我们最终得到了因子的alpha均值。要将他年化，我们就知道一年有多少个交易日，Alphalens默认的是252个交易日。

所以这个年化就是1+alpha再进行252次乘方。

这里讲的是因子是按日计算的情况。但如果因子和收益是按月计算的，这个地方就会有问题。在Alphalens当中，远期收益是没有月这个单位的。我们在后面还会再详细讨论这个问题。

[click]

第三个问题，阿尔法的取值区间正常是多少？知道这个区间，我们就能够一眼识别出，因子检验过程当中是否发生了错误。

如果因子的阿尔法年化能够到10%以上，就可以认为这是一个不错的阿尔法，因为从1965年到2021年，相对于标普500指数，巴菲特的年化超额收益大约是9.6%。

所以，如果我们得到的年华阿尔法显著高于10%，特别是如果在一个很长的时间区间，回测出来的alpha还能到这个数字就一定要小心了。这不一定是错误，但是提醒我们，要小心了。

[click]

第4个问题，beta的单位是多少？根据贝塔的定义，他就是一个系数，所以就没有百分比一说。

因此这里的贝塔就是浮点数表示的数值本身。

从我们跑出的结果来看，这里的贝塔非常接近于零， 所以市场波动对该因子几乎没有影响

[click]

接下来三行数据，显示了分层收益

首先top分层的收益均值。首先，我们要理解bps这个单位。bps是basis points的意思，即基点。一般来说，一个基点等于万分之一。

所以，在这里，最高分层持有一日的收益率均值是万分之3.147。

在它下面一行，就是最低分层持有一日的收益率。

最后一行，是指最高分层与最低分层的均值差，这里显示的spread是万分之11。这个数值相当于多少呢？ 如果我们每天做多最低的一层，做空最高的一层，那么252天累积下来大约会有2%等级的收益。
-->
