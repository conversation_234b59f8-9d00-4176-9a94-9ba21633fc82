---
clicks: 11
---

<style scoped>
h2, h3 {
    margin-left: 0;
}
</style>

<div class="abs top-10% w-40%">

### 遗留问题

<ol>
<li v-mark.red="{at:[1,7], type:'box'}">因子标准化会影响Alpha吗？</li>
<li v-mark.red="{at:[7,8],type:'box'}">因子标准化会影响到IC值吗？</li>
<li v-mark.red="{at:8,type:'box'}">因子标准化会影响到分层回溯收益吗？</li>
</ol>

$$
E(R_i) = R_f + \beta(E(R_m) - R_f)
$$

</div>

<v-drag-arrow color="red" pos="201,248,0,92" v-click="1"/>

<div class="abs top-60% w-40%">

$$
y = \beta x + \alpha + \epsilon
$$
</div>

<NoteCell class="abs top-10% w-50% left-50%"
            v-motion 
            :enter="{x: '0%'}"
            :click-1="{x:'100%'}">

```python
pd.DataFrame({
    'factor': np.arange(10),
    'scaled_factor': np.arange(10) / 1.1,
    'returns': 0.5 * np.arange(10) + 3
})
```
</NoteCell>

<NoteCell class="abs top-10% w-50% left-50%" 
          v-motion 
          :enter="{x: '100%'}"
          :click-2-7="{x:'0%'}">

```python {all|9|10,7}{lines:true,at:3}
import matplotlib.pyplot as plt
x = np.arange(10)
beta = 0.5
alpha = 3

y = beta * x + alpha
scaled_x = x / 2

plt.plot(x, y, label="原始因子")
plt.plot(scaled_x, y, label="标准化因子")
```
</NoteCell>

<CountdownTimer :at="5" :count="5" q="因子缩小 2倍 后做回归，Alpha应该是多少？"/>

<NoteCell class="abs top-10% w-50% left-50%" 
          v-motion 
          :enter="{x: '100%'}"
          :click-9="{x:'0%'}">

```python  {11}{lines:true,at:3}
import matplotlib.pyplot as plt
x = np.arange(10)
beta = 0.5
alpha = 3

y = beta * x + alpha
scaled_x = x / 2

plt.plot(x, y, label="原始因子")
plt.plot(scaled_x, y, label="标准化因子")
plt.plot(x, y * 2, label="放大收益")
```
</NoteCell>

<CountdownTimer :at="10" :count="5" q="放大收益，会不会影响到Alpha?<br>Y/N"/>

<!--
上一节课有同学提了一个问题。就是因子的标准化会不会影响到收益分析？

在回答问题之前，我们先回顾一下这个问题的背景：Alphalens是不对因子进行标准化的。它也不对收益进行标准化，但是它允许我们对收益数据去极值。

在这个背景下，有同学提出，因子标准化会不会影响到收益分析？这是一个好问题。

具体地说，我们还可以把这个问题分成三个子问题，即：

1. 因子标准化会影响到Alpha收益吗？

2. 因子标准化会影响到IC值吗？

3. 因子标准化会影响到分层回溯收益吗？

我们通过一个小实验来回答第一个问题。假设我们有一个因子分析表，它有因子列、缩放后的因子列，以及returns列。

这份数据可以用右边的代码生成，我们运行一下，把数据打印出来。

我们在因子分析中使用的数据与这个大致相仿，只是格式有不同而已。

[click]

在求Alpha收益时，我们实际上是在做回归运算。截距就是alpha。因此，就这份数据，我们可以简化一下分析过程。

我们可以分别做两次回归，看看结果是否相同 。

[click]

这是左图公式的代码实现。在代码中，x即为因子，y则是对应的收益。alpha和beta是参数，这里分别是是3和0.5

[click]

我们以(x,y)为输入作一次回归，显然，此时的截距应该是3，也就是此时通过回归求得的alpha为3

[click]

然后，我们保持y不变，相当于保持因子分析中的收益不变，以scaled_x，即缩小两倍后的因子作为输入再做一次回归，此时的截距，也即alpha应该是多少？

[click]

我们给5秒钟时间大家思考一下，可以把答案写在聊天区。

[click]

现在我们把代码运行一下。

我们看到两次回归得到的截距是一样的，也就是alpha没有因子的缩放而改变。

那么什么改变了？beta变了。beta反映的是因子受市场波动影响的程度，也是重要的分析指标之一。

不同的因子，它们的量纲常常会不一样，因此受市场波动的影响也会不一样。而且，因子预处理的过程也会带来量纲的变化。所以，因子的alpha是可以相互比较的，但比较beta似乎没有太大的意义，因为一个预处理过程就可以改变beta。

[click]

关于这个问题，我们回顾一下IC的定义。它是一个spearman相关系数。本质上是pearson相关系数。而pearson相关是满足缩放不变性和平移不变性的。所以标准化对它没有影响。

关于pearson相关性的特性，在量化24课中有介绍。这里就不展开了。

[click]

第三个问题，因子标准化会影响到分层回溯收益吗？

如果我们回顾一下前面讲的分层回溯的实现，就会明白，因子标准化不改变因子分层结构，而分层回溯的收益计算，只取决于分层结构，所以，因子标准化不会影响到分层回溯的收益。

把上述知识点复习一遍之后，我们可以得到结论，因子做不做标准化，都不会影响到收益分析。但是，alpha分析依赖于线性回归，而线性回归依赖于正态分布，所以，如果我们要做alpha分析，必须进行分布调整。

[click]

刚刚我们讨论的问题是对因子进行缩放，或者说sfyw化，是否会影响收益分析。

那如果我们放大收益呢? 这会不会影响到alpha?

 [click]

我们给5秒钟大家思考下。在聊天区打出y或者n

[click]

现在我们来揭晓谜底。

这里的绿色线是收益放大后的回归线。

我们看到，我们把收益放大一倍，结果alpha也放大了一倍。因此，如果放大收益，alpha就会增长。这也是为什么上一节课中，我们讲到，要对收益数据去极值。因为这些极值会显著影响到alpha。
-->
