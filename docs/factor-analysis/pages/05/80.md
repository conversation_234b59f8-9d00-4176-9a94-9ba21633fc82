---
clicks: 3
layout: two-cols
---

<div class="abs w-full h-full" v-motion
    :enter="{x:'50%', y:150, scale:1.2}"
    :click-1="{scale:0}">

```python {lines:false}
from alphalens.tears import create_turnover_tear_sheet

create_turnover_tear_sheet(factor_data, turnover_periods=None)
```
</div>


<v-clicks at="1" every="2">

## Turnover Analysis
## Rank Autocorrelation
## Top/Bottom Quantile Turnover
</v-clicks>


::right::

<v-switch>

<template #1>

<v-drag pos="0,72,441,45">
<Box />
</v-drag>

![分层均换手率](https://images.jieyu.ai/images/2024/08/alphalens-turnover-overall.jpg?width=300)
</template>

<template #2>

![因子换手率自相关](https://images.jieyu.ai/images/2024/08/1D-factor-rank-autocorrelation.jpg?width=75%)
</template>

<template #3>

![1D 分层换手率](https://images.jieyu.ai/images/2024/08/1d-top-bottom-quantile-turnover.jpg?width=75%)
</template>
</v-switch>

<!--
在分层回溯中，需要将前一周期不在top和bottom分层的股票买入进来，将这一期不在top/bottom的股票卖出去。这就构成了换手。换手并不会促进因子收益。因为我们计算收益，本来就是按分层内的股票的日收益来计算的。反而换手率会因为提高佣金成本而侵蚀策略利润。

Alphalens在计算分层收益时，是不会考虑手续费的。所以，就有必要把实际换手率报告出来。

[click]

这是一个概略报告。我们看一下第一条记录，它意味着对10D的持有期，会有7.2%的资产是新进仓位。

[click]

因子自相关是当前因子值排名与其先前值之间的相关性度量。这个指标为什么会出现在这里？

因为它从另一个角度，给出了的仍然是衡量因子分位数换手率的方法。如果自相关性较低，则意味着该因子的当前值与之前的值关系不大，并且投资组合头寸在不同时间段内频繁变化。反之，因子排名在上一期和本期之间，变化就不大。

[click]

top/bottom分层换手率图。这张图是最高分层与最低分层换手率在时间上的展开。对这种时序展开图，我们主要看它的走势、稳定性和时间区间之间的一致性，找到异常区间。

-->
