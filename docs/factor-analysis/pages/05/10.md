---
clicks: 2
---

<div v-motion class="abs h-full w-full" 
    :enter="{x: 300,y:200, scale:1}" 
    :click-1="{x:-120,y:0, scale:0.8}"
    :click-3="{scale: 0}">

## create_returns_tear_sheet
</div>


<NoteCell v-motion class="abs left-40% w-60% top-10%"
        :enter="{opacity:0}"
        :click-1-10="{opacity:1}">

```python {all|42,43}{maxHeight: '400px',lines:true,at:2}
from numpy.lib.stride_tricks import as_strided
from alphalens.utils import get_clean_factor_and_forward_returns
from alphalens.tears import create_returns_tear_sheet

def rolling_slope(close: NDArray, win:int, *args):
    if len(close) < win:
        return np.full((len(close), ), np.nan)

    stride = close.strides
    shape = (len(close) - win + 1, win)
    strides = stride + stride
    transformed = as_strided(close, shape, strides)
    
    slopes, _ = np.polyfit(np.arange(win), transformed.T, deg=1)

    left_pad_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_pad_len, 0), mode='constant', constant_values=np.nan)
    return slopes

def wrapper(group):
    slopes = rolling_slope(group["close"].to_numpy(), 10)

    index = group.index.get_level_values(0)
    df = pd.DataFrame(slopes, index=index, columns=["factor"])
    return df

# 1. 获取行情数据
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 29)
barss = load_bars(start, end, universe=2000)

# 2. 计算因子
factors = barss.groupby(level='asset').apply(wrapper)
factors = factors.swaplevel(0,1)

# 3. 提取价格数据
prices = barss['price'].unstack(level=1)

# 4. 预处理
factor_data = get_clean_factor_and_forward_returns(factors, prices)

# 5. 生成报告
create_returns_tear_sheet(factor_data)
```
</NoteCell>

<!--
我们在上一章使用到的报表分析接口，是create_full_tear_sheet。实际上，它是由若干个子接口构成的。

从现在开始，我们先把create_full_tear_sheet放在一边，先来接触这些子接口。

这样我们讨论到哪个接口，就只生成哪个接口对应的报表，避免信息冗余。

阿尔法和贝塔是收益分析的一部分，所以，我们要通过这个接口：create_returns_tear_sheet来生成相关的报表

[click]

这段代码综合了第4课示例4-1和练习中的优化部分。但在最后部分，我们用create return tear sheet来替换了create_full_tear_sheet。

[click]

现在我们就来运行一下， 看在这个AP当中，具体的包含了哪些报表？

大概生成了8个报表。接下来，我们就开始解读这8个报表。
-->
