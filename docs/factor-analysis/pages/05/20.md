---
clicks: 5
---

<div class="abs top-15% w-50%">

## 计算方法
<v-clicks>

## 这张图揭示了什么信息？
<div/>

## 持有性价比
</v-clicks>
</div>

<div class="abs top-15% w-50% left-50%" v-motion
    :enter="{opacity: 1}"
    :click-2="{opacity: 0}"
    :click-3="{opacity: 1}"
    >

![](https://images.jieyu.ai/images/2024/08/mwpr-poor.jpg)
</div>

<div class="abs top-15% w-50% left-50%" v-motion
    :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}">

![](https://images.jieyu.ai/images/2024/08/mwpr-good.jpg)
</div>

<CountdownTimer :at=4 :count=10 q="哪一个说法是正确的？"/>

<!--
前面的表格中, 展示了分层回溯中，最高分组跟最低分组各自的收益均值。这张图就把所有分层的收益都以柱状图的形式展现出来了。

他的绘制方法是，对1到5的每个分层，取持有期一天、五天、10天计算分层的收益均值，然后绘制成柱状图。

在计算分层的收益时，组内的资产平均分配仓位。

[click]

那么除了展示了之前没有的中间分层的收益之外，他还能告诉我们哪些信息？

读这张图，我们要以整体的视角来把握它，我们在前面讲过一个好的因子，或者说一个预测能力强的因子，它应该是线性递增的；反应在这张图上就应该是左低右高。

下面我们就来看一个预测能力特别强的因子，他的分层均值收益图应该是什么样子？

[click]

这张图左低右高，完美对称，它是怎么构造出来的呢？如果因子包含了未来数据，那么他就会有非常强的预测能力，对吧？

因此要构造这样一张图，我们可以用未来五天的收益作为因子，显然这个因子就会有很强的预测能力。

最终我们就会得到这样一个分层均值收益图。

所以当你看到如此完美的一个分层均值收益图，那么你就要想一想，是否引入了未来数据。

[click]

这张图还能告诉我们，对每一个分层的因子，持有不同的天数，它的收益性价比如何？ 我们以第二组为例， 这一组都是正收益但是一天的收益率要比10天的收益率高，这个怎么理解？

这里有两个说法，大家看哪一个是对的？

第一，持有10天的累计收益率比持有一天的要低；
第二，持有10天的日均收益率比持有一天的收益率要低。

大家回答1或者2就好。

[click]

请大家把答案写到留言板上。我们给10秒钟。两个选择是

第一，持有10天的累计收益率比持有一天的要低；
第二，持有10天的日均收益率比持有一天的要低。

[click]

 好，这里是Alphalens比较微妙的地方，也是我们平常在其他别的教程或者网络上很难看到的，需要深入Alphalens才能知道究竟。这里的答案是二。

当然，一旦我们说破答案，这里就非常容易理解，既然在这里要进行比较，那么我们拿10天的累计收益率跟一天的收益率，两者是不具有可比性的，或者说，比完也得不到有用的结论。

 但是，当我们把10天的日均收益率跟一天的日均收益率相比较的时候，如果10天的日均收益率更低，那么显然我们就应该每天进行换仓，这样连续10天的累计收益率就会比不换仓、一直持有10天的收益率要高，这样我们就得到了一个有用的结论。
-->
