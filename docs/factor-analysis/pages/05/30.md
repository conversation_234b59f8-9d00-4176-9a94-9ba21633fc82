---
clicks: 2
---

<div class="abs top-15% w-50%">

## 什么是 Violin 图

<v-clicks>

## 单 Violin 分析
## 整体分析
</v-clicks>
</div>

<div class="abs top-15% w-50% left-50%" v-motion
    :enter="{opacity: 1}"
    :click-3="{opacity: 0}"
    >

![分层收益 Violin 图](https://images.jieyu.ai/images/2024/07/fig-05-03.jpg?width=500)
</div>

<div class="abs top-50% w-50% left-50%" v-motion
    :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}"
    >

![预测能力强的因子 Violin 图](https://images.jieyu.ai/images/2024/08/mwpr-violin-redictive.jpg?width=500)
</div>

<!-- 

Violine图是箱形图和density plot的结合。它是把密度图旋转90度，再对称展开，这样就形成了一个类似violine形状的图形。然后，再把箱形图的几条特征线，也就是25%分位、50%分位和75%分位叠加上去。

[click]

在violine图中，越宽的地方，表明该区域样本数越多，越窄的地方，表明该区域样本数越少。结合分位数来看，如果是数值高的分层，我们希望它的中位数在y=0上方，越大越好；如果是数值低的分层，我们希望它的中位数在y=0下方，越小越好。

如果概率密度图不是按x轴对称的，也就是有偏度的情况下，我们希望该偏度对投资是有利的。

此外，如果概率密度图是比较奇怪的分布，比如双峰，就要考虑因子本身不纯粹。正常情况下，单因子的收益分布应该类似正态分布。

[click]

刚刚讲的是从单个violin本身来分析。如何从整体上来把握这张图呢？我们还是先进行一个对照。

下面这张图，是一个引入了未来数据、预测能力强的因子的Violin图。可以看出它在整体上，也要遵循左低右高。另外，我们只看持有5日的收益这一组。这一组是用自己来预测自己，预测能力是最强的。我们发现它的分布区间很窄，这也是不太正常的情况。因为它反映了因子的预测能力太强。我们知道，天底下没有这样的好事。
-->
