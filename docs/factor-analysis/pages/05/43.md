---
clicks: 4
---

<div class="abs top-15%">


## 图解 <!-- 等权 -->

<v-clicks>

## 计算方式
## Good vs Poor
</v-clicks>
</div>

<div class="abs top-10% left-50% w-50%" v-motion
    :enter="{opacity:1}"
    :click-3="{opacity:1}">

![](https://images.jieyu.ai/images/2024/07/fig-05-05.jpg)
</div>

<div class="abs top-55% left-50% w-50%" v-motion
    :enter="{opacity:0}"
    :click-2-3="{opacity:1}">

![好的收益驱动分解图](https://images.jieyu.ai/images/2024/08/good-returns-driver.jpg?width=500)
</div>

<CountdownTimer :at=3 :count=5 q="这是什么类型的因子？" />

<!--

这张图是对上一张图的分解，但是计算方式和指标上都有所不同。

这张图中，纵坐标是累计回报的对数，这是跟上一张图不一样的第一点。所以，我们不能直接根据这张图来计算多空分层的收益。

这张图帮助我们理解收益是由哪些分层驱动的，以及各个分层在不同的时期表现如何，它们是如何相互作用，最终影响到整体收益的。

[click]

在计算方式上，它是先进行分层，在每个分层内，资产持仓是平均分配，而不是因子加权。所以，它对离群值不像上一张图那么敏感。

[click]

现在我们将这个因子与一个预测能力很强的因子进行对比。与其它指标不同，通常我们希望分层累计回报图能向下面这张图尽可能地靠拢。

这两张图的区别是，下面的图每个分层都具有良好的单调性，并且不交叉。而在上面的图中，分层的累计收益发生了交叉。在2015年之前，第1层贡献了做多收益，第5层贡献了做空收益；而在2015年之后，事情刚好反过来。

大家思考一下，这个因子是什么类型的因子？我们可以得到什么结论？

[click]

给大家5秒钟思考一下。

[click]

我们可以把这个因子称为风格因子。它不是全时段有效，但在比较长的一段时间内，它有着比较稳定的规律，也可以对收益进行预测。最重要的是，它可以用来指示市场的风格转换。

如果我没记错的话，这张图来自于一个低波动因子。它反应了市场在2015年前后经历了风格转换。在2005年之后，低波动率的白马股开始受到资金的追捧。

真正的Alpha因子，它与收益的关系应该是因果关系；而风格因子与收益的关系是相关关系。但是，所有的统计分析方法，只能揭示相关性，不能揭示因果性。

-->
