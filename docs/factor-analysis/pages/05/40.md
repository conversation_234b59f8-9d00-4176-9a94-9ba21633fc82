---
clicks: 1
---

<div class="abs top-15%">

## 因子加权多空组合收益图

<v-clicks>

## 计算方法
</v-clicks>
</div>

<div class="abs top-15% left-50%">

![多空组合累计收益](https://images.jieyu.ai/images/2024/07/fig-05-04.jpg?width=500)
</div>


<!-- 

这个图是多空组合累计收益图。这个类型的图只有1天周期的。它是模拟每日调仓换股，经年累月计算出来的累计收益。

这张图的纵坐标是绝对收益，可以把它当净值来理解。

在这个图中，我们希望看到累计收益对时间的函数左低右高，持续稳健增长的。这种趋势如果能在不同的时间段越具有一致性，那么这个因子就越好。


大家猜一猜，它是怎么计算的？

[click]

按理说，在完成了alpha收益分析之后，我们希望看到一个多空分层的累计收益图。 也就是做多因子值最大的一层，做空因子值最小的一层， 然后来计算这种操作方式下的累计收益。

所以我们会期待这个图是按这种方法来计算的收益。 但是Alphalens在这里的计算方式有所不同。
 
Alphalens的计算方法仍然是一种多空投资组合。其中因子值高于日均值的每项资产都持有多头，而因子值低于日均值的每项资产都持有空头。
 
投资组合是因子加权的，在因子值的两端，资产分配的权重高；在因子值的中间，持有资产的权重低，但可能仍然会持有一定的仓位，而不是完全不持有。 我们在前面介绍的分层回溯，多空组合方法中，是要做多因子值最大的一层，并且做空因子值最小的一层，中间的我们是不投资的，这是两者的区别
 
这种方式还会带来另外一个问题，就是累积回报对少数离群值更敏感。因为它们对应的资产的权重会更高。

所以，我们应该使用分位数累积回报图来补充这个图。这就是我们接下来要介绍的分析报告。在分位数累积回报计算中，在分配资产仓位时，采用的是等权重的方法，从而对离群值就没那么敏感。

-->
