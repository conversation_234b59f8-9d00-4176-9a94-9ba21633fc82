---
aspectRatio: 16/9
title: 第 17 课 基于聚类算法的配对交易策略
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--
大家好，我们开始上课了。今天的课程内容是如何实现基于聚类的配对交易策略。这是大家期待以久的内容了。在之前的课程中，我们都只给出了策略的一部分，或者是一个因子，或者是一个决策条件，但是，我们始终没有完整地执行过一次回测。

这样做的原因很简单，当然是出于教学的需要。现在，我们已经几乎完成了所有准备，是时候告诉大家如何编写完整的策略了。在今天的课程里，我们仍然从最基本的概念讲起，但在最后，会给大家展示一个基于聚类算法的完整的交易策略。这个策略我们使用了backtrader的框架来实现。backtrader的用法我们在量化24课中有讲解，如果学习本章有困难，可以报名我们的《量化24课》

好，现在我们就进入今天的课程。
-->

---
title: /01 平稳时间序列和协整检验
layout: section
---

<!--
我们先从配对交易和平稳时间序列的概念讲起
-->

---
title: 引子
src: 17/10.md
---

---
title: 平稳时间序列及其检验
src: 17/20.md
---

---
title: 构造平稳时间序列
src: 17/23.md
---

---
title: 协整检验
src: 17/26.md
---

---
title: /02 聚类算法
layout: section
---

---
title: 从kmeans到hdbscan
src: 17/30.md
---

---
title: 数据降维
src: 17/33.md
---

---
title: /03 配对交易策略框架
layout: section
---

<!--
现在，我们就完成了本章所需要的几乎一切预备知识。 下面，我们来看如何组装这些知识，形成一个策略。
-->

---
title: 策略框架
src: 17/40.md
---

---
title: 配对交易策略实例
layout: section
---

<!--
现在，我就带大家看一个完整的实例。这个例子在教材中有，同时也提取出来，放在了补充材料中。
-->

---
title: 配对交易策略实例
src: 17/50.md
---



---
title: end
layout: end
src: 17/60.md
clicks: 1
---
