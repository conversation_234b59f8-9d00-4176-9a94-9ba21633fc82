---
aspectRatio: 16/9
title: 第4课 Alphalens快速入门
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
---

<!--
大家好！

前面两讲我们介绍了因子检验的原理，并且演示了如何自己写一些简单的代码来执行因子检验。

这两讲的作用主要是帮助我们理解原理和底层的技术。但在实际应用中，我们一般会通过框架来实现因子的快速检验。

框架的优点是稳健、功能丰富、可视化程度高，并且性能也往往优于我们自己的实现。

从这一讲开始，我们就介绍因子检验中，最有名的一个开源工具库， Alphalens。

在开始之前，先介绍一下上期课程的几个遗留问题

上次课程在演示中，出了一些小问题。现在，这两课都已修复，补录了部分内容，第2课已经上传，第3课正在上传中。

第3课的作业已经部置，主要是对这一课介绍的索引、dataframe reshape等知识点进行复习，请大家课后自己练习一下。

第2课的作业也已经批改，反馈也已发出，请大家查收一下。有一些作业自已本地验证不过的，请联系上下文，看一下是否在变量名上用得与要求的不一致。一般情况下，练习要求会进行说明，不过，说明与代码的一致性难以绝对保证。

第2课的习题还是比较有意思的。关于上证市盈率的，这里到底是机会，还是陷阱？有的同学回答得很不错，我也受到启发。

另外，之前的课程中提到，第3课最后的示例代码是不是有问题？需要验证。经过验证，这段代码是正确的，请大家放心食用。

好，我们就转入正题
-->

---
src: 04/10.md
title: QuantoPian和Alphalens
---

---
src: 04/20.md
title: 斜率因子
---

---
src: 04/30.md
title: 斜率因子
---

---
src: 04/40.md
title: 提取价格数据
---

---
src: 04/50.md
title: get_clean_factor_and_forward_returns
---

---
src: 04/60.md
title: 因子分析
---
