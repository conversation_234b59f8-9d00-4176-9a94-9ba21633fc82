---
aspectRatio: 16/9
title: 第 10 课 TsFresh及其它因子
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--

大家好，我们开始上课了。

通过Alpha101, Alpha191和talib技术指标库，我们已经接触到了300多个因子，其中有一些在全时段都具有有效性。

但这并不意味着所有的因子都已被挖掘完。在这一讲，我们将介绍tsfresh库，这是个非常著名的时间序列特征提取库，将为我们带来超过700个因子，或者说特征。

此外，我们还将介绍几个基于行为金融学理论构建的因子，它们有很好的解释性和可观察性，在短线交易中非常有效。

还有一些因子非常简单，在理论上没有太多研究价值，因此只作为交易员之间的经验流传。但是，我们做量化毕竟是要做工程实现，而不是理论研究，所以，我们也将学习这些因子。

好，我们现在就进入今天的课程。

-->

---
title: /01 其它量价因子
layout: section
---

<!--这一部分我们介绍的是理论研究价值可能不高，但比较实用的因子-->

---
title: 概率回归因子 - pct
src: 10/10.md
---

---
title: 概率回归因子 - 连续涨跌次数
src: 10/20.md
---

---
title: 概率回归因子 - 连续涨跌幅度
src: 10/30.md
---

---
title: 策略评估指标作为因子
src: 10/33.md
---

---
title: 斜率和导数因子
src: 10/40.md
---

---
title: 波谱分析因子
src: 10/50.md
---

---
title: /02 TSFresh 库
layout: section
---

<!--
TSFresh 是一个 Python 包。它能自动计算大量时间序列特征。此外，该包还提供了在回归或分类任务进行评估的方法
-->

---
title: 如何提取特征
src: 10/60.md
---

---
title: 滑动窗口下的因子提取
src: 10/63.md
---

---
title: 因子分析
src: 10/66.md
---

---
title: /03 行为金融学因子
layout: section
---

<!--
行为金融学是从交易者的行为以及产生这种行为的心理等动因来解释、研究和预测市场的发展。其挖掘的是市场波动的深层次驱动因素。

在A股，由于市场一直围绕3000点进行波动，因此，博弈比长期持有往往更有效益。这也决定了行为金融学因子的重要性和有效性。

关于行为金融学因子的研究比较新，很多都是基于交易经验构建的，没有系统地研究框架。这里我们举几个例子。

-->

---
title: 处置效应 - CGO因子
src: 10/70.md
---

---
title: 整数关口因子
src: 10/73.md
---

---
title: 缺口因子
src: 10/79.md
---


---
title: thanks
src: 10/90.md
layout: end
clicks: 1
---
