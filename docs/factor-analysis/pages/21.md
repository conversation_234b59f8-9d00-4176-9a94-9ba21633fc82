---
aspectRatio: 16/9
title: 第 21 课 未来新世界
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--
今天是因子分析与机器学习策略的最后一课。我们讨论一下更高级的人工智能算法在量化交易中的应用。我们主要讨论CNN、transformer，对强化学习也做很简单的介绍。
-->

---
layout: section
title: 免费GPU算力
---

<!--
这一章我们将要探索一些比较先进的人工智能算法在量化交易中的应用。在前面的章节中我们讲过，当我们特指人工智能时，我们可能是在说包括深度学习、强化学习在内的，需要超大数据集和超强算力的这一类模型。

为了练习这些模型，我们就需要一些GPU算力。但是，GPU算力并不便宜，有没有可能获得免费的GPU算力呢？

这一节，我们将介绍三个方案，其中包括一个本地方案。

-->

---
src: 21/10.md
title: Google Colab
---

---
src: 21/20.md
title: Kaggle
---

---
src: 21/23.md
title: Mac arm系列
---

---
layout: section
title: CNN价格预测
---

<!--就量化交易而言，可能值得探索的架构，或者说模型可能CNN， transformer, 强化学习，大语言模型等。我们在网上可能看到很多通过RNN网络，特别是像LSTM或者GRU这样的架构来预测股票价格的例子，这些例子大家都不用关心。这些架构有着天然的缺陷，现在已基本被transformer代替了。

下面，我们就先从CNN讲起，然后介绍transformer，增强学习。大语言模型现在很火，但还看不出来在量化交易中特别重要的应用。为什么这么说呢，幻方是deep seek这个大模型的开发者，LLM有没有用，看看他们的资管规模变化就知道了。

-->

---
src: 21/30.md
title: CNN简介
---

---
src: 21/33.md
title: 数据准备
---

---
src: 21/36.md
title: 构建模型
---

---
src: 21/37.md
title: 模型训练
---

---
src: 21/39.md
title: CNN能干什么？
---

---
layout: section
title: Transformer
---

<!-- 金融数据本质上是一种时间序列，数据之间存在某种因果关系，而这种因果关系，是CNN难以学习到的。一种专门为时间序列设计的模型，即RNN，循环神经网络被提了出来。不过，它在处理长时间序列时，存在梯度消失或者梯度爆炸的问题，难以捕捉长距离的依赖关系，即使是后来的改进版本，比如LSTM， GRU也难以避免。

2017年，Google实验室发表论文，attention is all you need,提出了transformer架构，为现在盛极一时的大语言模型奠定了基础。在金融数据处理上，它可以直接计算序列中任意两个位置之间的依赖关系，能够更好地捕捉股价数据中的长期依赖，例如过去几个月甚至几年的市场动态对当前股价的影响。

下面，我们就来介绍transformer架构 -->

---
src: 21/40.md
title: Transformer架构简介
---

---
src: 21/43.md
title: Transformer量化模型实例
---

---
layout: section
title: 强化学习
---

<!--由于deep seek的出圈，强化学习继2017年alpha go之后，再次吸引住了人工智能界的目光。当然，即使没有deep seek的走红，强化学习也应该在量化交易中占有重要的一席之地，因为它的核心理念，跟我们的交易实在太相似了。-->

---
src: 21/50.md
title: 强化学习简介
---

---
src: 21/60.md
title: 猎场的故事
---

---
title: end
layout: end
src: 21/90.md
clicks: 1
---
