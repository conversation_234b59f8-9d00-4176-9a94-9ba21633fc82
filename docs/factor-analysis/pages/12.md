---
aspectRatio: 16/9
title: 第 12 课 因子挖掘方法
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--

大家好，我们开始上课了。

今天是我们课程第一部分结尾的一课。从下一课起，我们就要讲机器学习了。

我们前面讲了大量的因子，但我们也知道，最重要的是要掌握方法论和拥有必要的资源，从而能够在变化的局势下，找出不变的规律加以运用。

这一课，我们将通过回顾技术因子的迭代之路，从量、价、时、空四个维度，谈谈如何挖掘出新的因子。

因子多了，难免就会打架。这一课，我们还将介绍因子动物园的概念，并且介绍如何保证因子的独立性。

在这一课的教材中，我们插入了大量的网络资源，此外，还提供了80多个新的因子的索引。不过，这些内容我们就不在课堂上讲了，大家可以自行阅读教材。

这一课教材中，有一部分内容讲的是『道』相关的东西。这部分没必要展开，大家看教材就可以了，因此，这部分内容也不会在今天的课堂上讲。

好，下面我们就开始今天的课程。

-->

---
title: /01 技术指标的创新
layout: section
---

<!--
这一节对应课本中的第三节。这一节我们将以几个振荡器为例，介绍这类因子的迭代创新的过程，探索一下因子构建的思路。
-->

---
title: 振荡器的迭代
src: 12/10.md
---


---
title: 量的维度
src: 12/20.md
---

---
title: 价的维度
src: 12/30.md
---

---
title: 时间维度
src: 12/40.md
---

---
title: 空间维度
src: 12/50.md
---

---
title: /02 因子正交性检测
layout: section
---

---
title: 因子正交性检测
src: 12/60.md
---

---
title: /03 因子动物园
layout: section
---

---
title: 因子动物园
src:  12/70.md
---

---
title: end
layout: end
src: 12/80.md
clicks: 1
---
