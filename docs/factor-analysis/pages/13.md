---
aspectRatio: 16/9
title: 第 13 课 机器学习概述
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--

大家好，我们开始上课了。

在前面的课程中，我为大家介绍了可能超过上千个因子。有一些同学动手能力比较强，私下跟我交流，通过其中的一些因子，特别是我原创的一些因子，在手动选股中，取得了比较好的效果。但是，对于如何将这些因子组合成为策略，在策略层面上进行回测，以至于进入实盘，这个流程还不清楚。

在我们已知的技术中，alphalens只能进行单因子分析。backtrader根本不涉及到因子和策略的构建。那么，我们将如何来解决这个问题呢？

这正是我们因子分析与机器学习课程的第二部分要解决的问题。

好，下面我们就开始今天的课程。

-->

---
title: /01 机器在学啥？
layout: section
---

<!--
人工智能从1956年起发端，经过多次路线斗争，最终基于数据的机器学习模式全面胜出。

可是，机器学习究竟是怎么一回事？为什么机器看了很多数据，就能学习到逻辑并且进行推理呢？

这是我们这一部分要介绍的内容。不过，我们要先从人工智能的起源开始讲起。 
-->

---
title: 人工智能的起源
src: 13/10.md
---


---
title: 机器学习的本质
src: 13/20.md
---

---
title: /02 分类和模型选择
layout: section
---

<!--
我们刚刚接触了一个机器学习的例子。很可能你会问，是不是只要掌握这一个模型，然后通过在不同的数据集上进行训练，就能解决所有问题呢？

我想，大家可能也知道答案。模型本身是通用的，通过在不同数据集上进行训练，就能解决不同的问题。但不会是所有问题。

机器学习有很多分类。不同的分类，能解决的问题、对数据和算力的要求也是不一样的。接下来，我们就聊聊机器学习的分类。
-->

---
title: 机器学习、深度学习和强化学习
src: 13/30.md
---

---
title: 监督、无监督和强化学习
src: 13/40.md
---

---
title: 回归与分类
src: 13/50.md
---

---
title: /03 机器学习流程
layout: section
---

---
title: 机器学习的基本流程
src:  13/60.md
---

---
title: /04 应用场景
layout: section
---

---
title: 机器学习应用场景
src: 13/70.md
---

---
title: end
layout: end
src: 13/80.md
clicks: 1
---
