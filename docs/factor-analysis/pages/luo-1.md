
## 学员问题
## 快速阅读代码

Q: 请阅读代码，逐函数分析，给出函数调用链，使用思维导图表示
A: 该策略是一个 红利优选量化策略 ，主要流程为：

- 每月最后一个交易日调仓
- 筛选股息率前500的股票
- 应用多个因子（财务稳定性、股东变化、现金流等）进行评分
- 选择得分最高的30只股票等权配置
- 与中证红利指数进行业绩比较
所有函数按照数据获取→股票筛选→因子计算→组合构建→收益计算的逻辑顺序执行。


主函数调用链
│
└── main()
    │
    └── backtest_strategy(start_date, end_date)
        ├── get_trade_date(start_date, end_date)
        │
        ├── build_stock_pool_total(rebalance_dates, se_trade)
        │   ├── get_select_stock(prev_trade_date, df_total_stock_init, df_namechange, df_dividend_init)
        │   │   └── get_init_stock(df_total_stock_init, df_namechange, prev_trade_date)
        │   │
        │   └── calc_stock_zscore(df_stock, df_bao_dividend, prev_trade_date)
        │       └── get_DailyBasic_calc(prev_trade_date, df_stock)
        │           └── get_DailyBasic_calc_muti(prev_trade_date, df_stock, num_threads=4)
        │
        ├── calculate_strategy_returns(portfolio, current_date, next_date)
        │
        └── get_CSI_Dividend_returns(first_rebalance_date, end_date)

Q: 这段代码有什么明显的错误吗？无论是语法上、功能上，还是就策略实现上
A: 1. 高优先级 ：修复 all_stock_returns 未定义错误
2. 高优先级 ：统一路径分隔符处理
3. 中优先级 ：规范日期格式处理
4. 中优先级 ：改进数据单位检测逻辑
5. 低优先级 ：优化因子权重设置和性能问题
这些错误中，全局变量未定义是最严重的，会直接导致程序崩溃，需要优先修复

评论： 静态走读，质量不太高。

评论： AI可以做什么？原代码中，数据目录都是临时拼接的，为了能在我本地运行，代码要修改25处左右。如果我按我本地环境修改了，将来交给学员，他还得再把这25处改回去。这个过程繁琐、也可能出错。
我先让AI统一路径分隔符，并且设置数据文件根目录，让代码能运行起来。这步重构是必要的。重构之后，不仅代码在我这边可以运行起来，就是交给学员之后，他也只需要修改一下数据文件根目录，就可以同样运行起来了。

## 分函数阅读代码及讨论

### get_trade_date

讨论： get_trade_date的作用？在月度调仓策略中，trade_date还重要吗？
