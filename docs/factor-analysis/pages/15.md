---
aspectRatio: 16/9
title: 第 15 课 SKLearn通用模块
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--
大家好，我们开始上课了。

在学习了机器学习的核心概念之后，我们就可以介绍机器学习的常用库了。SKLearn无疑是所有机器学习库中，最全面、最易于使用的机器学习库。它提供了内置数据、数据预处理、多种机器学习算法、多种模型评估方法和可视化方法。

在本课程中，我们将主要介绍最先进的梯度提升决策树模型，这些模型并不包含在SKlearn当中。但是，sklearn中的一些公共模块，无论是从教育的角度还是从应用的角度，都是有必要掌握的。

这一课，我就带领大家学习这些模块
-->

---
title: /01 数据集
layout: section
---

<!--
scikit-learn 提供了一些常用的内置数据集，这些数据集可以直接通过 sklearn.datasets 模块加载。在教学、测试和快速原型开发中，我们都离不开这些数据集，所以，这一节课，我们先从数据集讲起。
-->

---
title: 内置数据集
src: 15/10.md
---

---
title: 外部数据集
src: 15/13.md
---

---
title: 人工合成数据集
src: 15/16.md
---

---
title: /02 预处理模块
layout: section
---

---
title: 标准化
src: 15/20.md
---

---
title: 缩放
src: 15/30.md
---

---
title: 逆变换
src: 15/40.md
---

---
title: 编码器
src: 15/50.md
---

---
title: /04 度量模块
layout: section
---

<!--
我们在讲解度量函数的概念时，已经介绍了一些常见的度量函数。在这里，我们简单地过一下，主要是看看metrics模块在sklearn中的层次结果和自身组成。
-->

---
title: metrics
src: 15/60.md
---

---
title: /05 模型解释与可视化
layout: section
---

---
title: ROCCurveDisplay
src:  15/70.md
---

---
title: 混淆矩阵
src:  15/73.md
---

---
title: 决策边界图
src: 15/76.md
---

---
title: end
layout: end
src: 15/80.md
clicks: 1
---
