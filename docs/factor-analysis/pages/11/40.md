---
clicks: 9
layout: two-cols
right: 55%
---

<div class='abs flex items-center justify-center h-full w-960px' v-motion
     :enter='{opacity: 1, x:0}'
     :click-1='{ x: -200}'
     :click-2='{ x: -250}'
     :click-3="{opacity: 0}">

$$
\text{市净率} = \frac{\text{每股市价}}{\text{每股净资产}} 
$$
</div>

<div class='abs flex items-center justify-center h-full w-960px' v-motion
     :enter='{opacity: 0, x: 200}'
     :click-1='{ opacity: 1}'
     :click-2='{ x: 250}'
     :click-3="{opacity: 0}">

$$\text{PE} = \frac{\text{每股市价}}{\text{每股收益}}$$
</div>

<div class='abs flex items-center justify-center h-full w-960px' v-motion
     :enter='{opacity: 0}'
     :click-2='{ opacity: 1}'
     :click-3="{ opacity: 0}">

$$ \text{ROE} = \frac{\text{净利润}}{\text{每股净资产}} $$
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-3='{ opacity: 1}'>

## 价值因子的检验问题
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-3='{ opacity: 1}'>
    
### 噪声过多
</div>

<div class='abs mt-40' v-motion
     :enter='{opacity: 0}'
     :click-8='{ opacity: 1}'>

### 没有日频数据
</div>

::right::

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-3-4='{ scale: 1}'>

```python
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)
universe = get_stock_list(start, code_only=True)
barss = load_bars(start, end, tuple(universe))

pe = get_daily_basic(["pe_ttm"], start, end, universe) * -1
prices = barss.price.unstack(level="asset")

merged = get_clean_factor_and_forward_returns(pe, prices)
create_returns_tear_sheet(merged)
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-4-5='{ scale: 1}'>

```python
def deepinsight_fundamental(ticker: str, field, start, end, inverse = False):
    df = get_daily_basic([field, "close"], start, end, (ticker,))
    df = df.xs(ticker, level="asset")

    if inverse:
        df[field] = df["close"] / df[field]
    else:
        df[field] = df[field] / df["close"]

    df["d1"] = df[field].diff()
    df.ffill(inplace=True)
    _, ax = plt.subplots(figsize=(15, 5))
    ax.plot(df.index, df["d1"], label=f"{field}_denoise")
    ax2 = ax.twinx()
    ax2.plot(df.index, df["close"], label="close", color="r")
    ax.legend(loc=2)
    ax2.legend(loc=1)
    plt.show()

start = datetime.date(2005, 1, 1)
end = datetime.date(2014, 10,31)
deepinsight_fundamental("002714.XSHE", "pe_ttm", start, end)
```
</NoteCell>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-5-7='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/11/mygf-pe-diff-close.jpg)
</div>


<FlashText v-click="[6,7]"
           class='abs mt-90 text-center w-full text-3xl'>

$$
ind = \frac{d}{dt}(PE/CLOSE)
$$
</FlashText>


<div class='abs ' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/11/mygf-pe-diff-2018.jpg)
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-8='{ opacity: 1}'>

```python{all|3,14-15,17}{maxHeight: '420px',at:9}
def get_roe(start, end, universe):
    dfs = []
    index = pd.bdate_range(start, end)
    for code in universe:
        symbol, ext = code.split(".")
        ts_code = f"{symbol}." + {"XSHE": "SZ", "XSHG": "SH"}.get(ext, ext)
        df = pro.fina_indicator(ts_code = ts_code)
        df["date"] = pd.to_datetime(df["ann_date"])
        roe = df.query("date>=@start and date<=@end")
        roe.set_index("date", inplace=True)

        # tushare返回数据存在重复
        roe = roe[~roe.index.duplicated(keep='last')]
        roe = roe.sort_index()
        roe = roe.reindex(index)
        roe["asset"] = code
        roe = roe.ffill()
        dfs.append(roe[["roe", "asset"]])
    df = pd.concat(dfs).set_index("asset", append=True)
    return df

start = datetime.date(2018, 1, 1)
end = datetime.date(2023,12,31)

# 请勿修改此行，否则会导致tushare封访问
universe = get_stock_list(start)[:200]

factors = get_roe(start, end, universe)
barss = load_bars(start, end, tuple(universe))
prices = barss["price"].unstack("asset")

merged = get_clean_factor_and_forward_returns(factors, prices)
create_returns_tear_sheet(merged)
```
</div>

<!--
价值因子在fama三因子中，指的是HML因子。如果我们在别的地方见到这个词，那它可能指的一类跟公司财务相关的指标。


账面市值比是市净率的倒数。这是市净率的定义。市净率是市价，也就是收盘价与每股净资产的比。这个因子当中，每股净资产是财务指标，每个季度才公布一次。所以，在数据公布的两次间隙中，市净率并不包含比收盘价更多的信息

或者说，我们可以这样认为，市净率指标一年只有4次携带信息，其它时间的数据都是噪声。

此外，不同的行业，市净率之间没有可比性。比如娱乐业、游戏行业，这些都是轻资产公司；而工业制造企业，都是重资产公司，有厂房、机器等。因此要使用这个指标，是需要进行行业中性化的，这在FAMA三因子模型中是看不到的。


[click]

PE又称估值因子。它是每股市价除以每股收益。所以，它跟PB有一样的特性。一年只有4次携带信息；不同行业的市盈率不可比。


[click]

ROE指标是净利润除以每股净资产。这个指标每个季度才公布一次，没有日频的读数。

[click]

<run></run>

我们看到这个结果很一般。那么问题出在哪里呢？


[click]

<run></run>

我们说，价值因子在日频噪声很大。现在，我们就来讨论一下，这个噪声倒底有多大。

我们看到，PE确实在几个时间点上有信号，其它时间点上，数据的变动都是收盘价的变化带来的。

[click]

这是某生猪企业2005年到2014年间的数据的部分截图。红色的是每日收盘价，蓝色的除噪后的指标。

这个指标是这样计算的，先将PE除以close，再求导数。


[click]

这样我们就得到了真实盈利能力变化的信号。在这张图上，我们看到三个信号。变动比较小的可以忽略，可以认为是信号强度不够。

现在，大于零的信号表明该企业盈利能力变差了，小于零的信号表明盈利能力变好。

我们看到，对强周期行业，盈利越差的时候，就是股价上行的时候。因为在盈利差的时候买入，拉抬股价，后面盈利变好，会有源源不断的好消息出来，便于派发筹码。

[click]

这是该股2005到2023年底的全景图。我们看，基本上遵循了这个规律，大亏大涨，大赚大跌。


[click]

我们再来看一个ROE的例子。前面的例子中，尽管PE一年只有4次信号，但数据源已经把它处理成了每天都有数据了。这为数据带来了大量的噪声，但从形成上看，它们是可以用alphalens进行回测了。

但是，如果我们拿的是ROE数据的话，你会发现，它无法用alpahlens进行回测。因为，我们取到的数据，在时间上是离散的，这会导致alphalens在计算前向收益并与因子对齐时，报无法推断索引周期的问题，这是一个我们在前面着重讲解过的错误。


[click]

这段代码中，我们对ROE数据进行了补齐，这样就可以回测了。但是由于我们补齐了大量的噪声，所以回测也没有意义。

这是我们在使用财务因子时面临的情况。所以，在量化中使用财务因子作用大吗？

这是一个有争议的话题。财务因子辅助决策是必要的，在使用它的过程中，包含了很多探索、对比、去伪存真的操作，你要象财务专家一样去发现数据背后的意义或者问题。

但是，在量化中，由于操作频率一般都远高于1季度一次，所以，这些数据即使有信号意义，也只有那么几次。量化的作用是补足人处理数据能力不足的短板。如果一个数据一个季度才出一次，好几个季度连起来看才有分析价值，也不一定要用量化的方式来处理。

-->
