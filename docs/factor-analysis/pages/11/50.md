---
clicks: 5
layout: two-cols
right: 60%
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1}'
     :click-10='{ opacity: 0}'>

## 社交媒体情绪因子
## 网络流量因子
## 卫星图像因子
## 专利申请因子
</div>

::right::

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1-3='{ scale: 1}'>

```python
pro.news(src='sina', start_date='2024-01-01', end_date='2024-11-08')
```
</NoteCell>

<FlashText v-click="[2,3]"
           class='abs mt-1/3 text-center w-full text-3xl'>

! pip install jieba
</FlashText>


<NoteCell class='abs  w-full'
          :enter='{ scale: 0}'
          :click-3-4='{ scale: 1}'>

```python
import jieba
from collections import Counter

stocks = get_stock_list(datetime.date(2024,11,1), code_only=False)
stocks = set(stocks.name)
for name in stocks:
    jieba.add_word(name)

news = pro.news(src='sina', start_date='2024-11-01', end_date='2024-11-31')
words = jieba.lcut("\n".join(news.content))
word_counts = Counter(words)

labels, counts = [], []
for word, count in word_counts.items():
    if word in stocks and count > 3:
        labels.append(word)
        counts.append(word_counts[word])

plt.figure(figsize=(10, 6))
plt.bar(labels, counts, color='skyblue')
plt.xlabel('词语')
plt.ylabel('出现次数')
plt.title('词频统计条形图')
plt.xticks(rotation=45)  # 旋转 x 轴标签以避免重叠
plt.tight_layout()  # 自动调整子图参数，使之填充整个图像区域
plt.show()
```
</NoteCell>

<div class='abs mt-20 ml-10' v-motion
     :enter='{opacity: 0, scale: 0.8}'
     :click-4-5='{ opacity: 1}'>

| date       | 000001 | 000002 | ... | 600000 |
| ---------- | ------ | ------ | --- | ------ |
| 2024-11-01 | 0      | 2      | ... | 5      |
| 2024-11-02 | 1      | 3      | ... | 4      |
| 2024-11-03 | 2      | 4      | ... | 3      |

</div>

<!--

另类因子（Alternative Factors）是指那些传统金融模型中未涵盖的、但能够提供额外收益或风险管理信息的因子。这些因子通常来源于非传统的数据源，如社交媒体、卫星图像、网络搜索数据等。另类因子旨在捕捉市场的非传统信号，以提高投资策略的多样性和有效性。


[click]

<run></run>

这是tushare的新闻数据接口。

[click]

拿到这个数据之后，普通处理可以通过jieba进行分词。jieba的分词效果非常不错，我们还可以自定义词典，来加强它的分词效果


[click]

这个例子演示了如何查找最近一段时间高频出现的公司。一般来说，任何商品，展示量跟成交量往往是成正比的，所以，如果一家公司的名字频繁出现，那么它的成交量也会变大。当然，我们需要有更好的方法来分析，这种成交是拉抬股价，还是会降价销售。

但是，这个例子至少可以作为人工分析的辅助，帮我们找到热点。


[click]

那么如果要更『量化』一点，应该如何操作呢？

这里提出一个模型，留给大家在练习题中实现。

我们可以取每一天的新闻，使用示例中的代码，把各个公司的词频提取出来。这样就生成了一个以公司为列，以日期为索引的dataframe，再对这个dataframe求30日、一个季度和一年的移动平均。

这样就建立了一个模型。然后大家可以看看，如果当天的词频超过了移动平均，会发生什么。如果用因子分析的方法的话，建议使用当日词频对移动平均的差值作为因子值。

[click]

新闻或者社媒数据的主要问题是，不方便回测。它的信息在时间标记上很难准确。比如，在我们使用的tushare中的news接口，它确实是有了时间标记了，但是，这个新闻会不会是滞后的数据？这跟新闻采播流程有关系。好多新闻你看是今天发出来的，但它可能是前几天的事。而这个事如果很重要，公开新闻有可能有点晚了，也可能不晚。这个点上比较难拿捏。一方面，我们得到消息是越早越好，另一方面，如果消息没有广泛传播，它的作用又不大。所以，这方面的关系，还需要大家自己去研究。我这里只是提醒一下，会有这样一个维度。

之前有象互动易这样的平台，董秘在上面答问，然后股价马上可以拉起来的。这样的数据很直观，但毕竟也不是正道，做不长久。

其它的我们在这里就不介绍了。大家可以看看教材，我们提供了一些另类因子的数据源。

-->
