---
clicks: 20
layout: two-cols
---

<div class="abs ml-10 mt-10" v-motion :enter="{opacity: 1}"
    :click-2="{opacity: 0}">

<div>
<img style='border-radius:50%;width:300px;height:300px' src='https://images.jieyu.ai/images/2023/10/Eugene_Fama.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>尤金.法玛</span>
</div>
</div>

<div class='abs w-full h-full flex ml-40 mt-30' v-motion
     :enter='{opacity:0, y:0}'
     :click-2='{ opacity: 1}'
     :click-3='{y: -80}'
     :click-10="{opacity:0}">

$$
E(R_{it}) - R_{ft} = \beta_iE(R_{mt} - R_{ft})+s_i^E(SMB_t) + h_i^E(HML_t) + \epsilon_{it}
$$

</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-3-8='{ opacity: 1}'>

<div class='abs w-960 mt-40 ml-50 text-gray-500' v-motion
     :enter='{opacity: 0}'
     :click-3='{ opacity: 1}'>

* $R_{it}$表示资产 i 在时间 t 时的收益率
</div>

<div class='abs w-960 mt-50 ml-50 text-gray-500' v-motion
     :enter='{opacity: 0}'
     :click-4='{ opacity: 1}'>

* $R_{ft}$表示时间 t 的无风险收益率
</div>

<div class='abs w-960 mt-60 ml-50 text-gray-500' v-motion
     :enter='{opacity: 0}'
     :click-5='{ opacity: 1}'>

* $R_{mt}$表示时间 t 时的市场收益率；
</div>

<div class='abs w-960 mt-70 ml-50 text-gray-500' v-motion
     :enter='{opacity: 0}'
     :click-6='{ opacity: 1}'>

* $SMB_t$表示时间 t 时，做多小市值、做空大市值股票的组合收益率
</div>

<div class='abs w-960 mt-80 ml-50 text-gray-500' v-motion
     :enter='{opacity: 0}'
     :click-7='{ opacity: 1}'>

* $HML_t$表示时间 t 时，做多高账面市值，做空低账面市值股票的组合收益
</div>
</div>

<FlashText v-click="[7,8]"
           class='abs mt-90 ml-90 w-960 text-3xl'>

账面市值比 = 1 / PB
</FlashText>

<div class='abs w-full mt-30 ml-50' v-motion
     :enter='{opacity: 0}'
     :click-9-10='{ opacity: 1}'>

| HML<br>SMB | H(30%) | M(40%) | L(30%) |
| ---------- | ------ | ------ | ------ |
| S(50%)     | S/H    | S/M    | S/L    |
| B(50%)     | B/H    | B/M    | B/L    |

</div>

<div class='abs w-180%' v-motion
     :enter='{opacity: 0}'
     :click-10='{ opacity: 1}'
     >

```python{all|4|5-8|10-12|14-16|18-24|26-35|37-41|43-44|all}{maxHeight: '420px',at:12}
def calculate_fama3(df):
    factors = []

    for date, group in df.groupby(level='date'):
        group['smb_quantile'] = pd.qcut(group['mv'], q=2, labels=False)

        sg = group.query('smb_quantile==0')
        bg = group.query('smb_quantile==1')

        # small 和 big 均分权重，但内部按市值分配
        sw = (1 - sg["mv"] / sg["mv"].sum())/len(sg) / 2
        bw = bg["mv"] / bg["mv"].sum() / 2

        s_returns = np.sum(sg["return"] * sw)
        b_returns = np.sum(bg["return"] * bw)
        smb = s_returns - b_returns

        # 计算 HML 因子
        quantiles = [0, 0.3, 0.7, 1.0]

        # bm_quantile will be 0, 1, 2
        sg['bm_quantile'] = pd.qcut(sg['bm'], q=quantiles, labels=False)

        bg['bm_quantile'] = pd.qcut(bg['bm'], q=quantiles, labels=False)
        
        # 分组
        sl = sg.query('bm_quantile == 0')
        wsl = (1 - sl["mv"]/sl["mv"].sum()) /len(sl) / 4
        bl = bg.query('bm_quantile == 0')
        wbl = bl["mv"]/bl["mv"].sum() / 4

        sh = sg.query('bm_quantile == 2')
        wsh = (1 - sh["mv"]/sh["mv"].sum()) /len(sh) / 4
        bh = bg.query('bm_quantile == 2')
        wbh = bh["mv"]/bh["mv"].sum() / 4

        h_returns = np.sum(sh["return"] * wsh) + np.sum(bh["return"] * wbh)
        l_returns = np.sum(sl["return"] * wsl) + np.sum(bl["return"] * wbl)
        
        # 计算 HML 因子
        hml = h_returns - l_returns

        # 计算市场因子，采用市值加权法
        mkt = np.sum(group['return'] * (group["mv"]/group["mv"].sum()))

        factors.append((date, hml, smb, mkt))
    
    df = pd.DataFrame(factors, columns=['date', 'HML', 'SMB', 'MKT'])
    df.set_index('date', inplace=True)
    
    return df

factors = calculate_fama3(df)
factors.tail()ail()
```
</div>

::right::

<div class="abs w-full mt-30 left-0" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />有效市场假说提出者
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> 2013年诺奖
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>MIT博士
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>1963年起，芝大布斯商学院任教
</div>

<div class="abs w-full mt-30 left-0" v-motion :enter="{opacity: 0}"
    :click-1-2="{opacity: 1}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />1993： Fama-French三因子
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> 2015: Fama-French五因子 
</div>

<div class='abs h-100% w-700px bg-purple-100 p-10 top-0 left--30' v-motion
     :enter='{opacity: 0, scale:0.6}'
     :click-11-12='{ opacity: 1, scale:0.6}'>

| date       | asset       | mv           | bm       | returns   |
| ---------- | ----------- | ------------ | -------- | --------- |
| 2018-01-02 | 600230.XSHG | 1.265892e+06 | 0.192979 | NaN       |
| 2018-01-02 | 600237.XSHG | 3.047596e+05 | 0.403633 | NaN       |
| 2018-01-02 | 002465.XSHE | 1.826103e+06 | 0.352473 | NaN       |
| 2018-01-02 | 002443.XSHE | 3.558068e+05 | 0.925241 | -0.001458 |
| 2018-01-02 | 600050.XSHG | 1.356296e+07 | 1.138304 | 0.002288  |

</div>

<div class='abs h-100% w-700px bg-purple-100 p-10 top-0 left--30' v-motion
     :enter='{opacity: 0, scale:0.6}'
     :click-20='{ opacity: 1}'>

| date       | HML       | SMB       | MKT       |
| ---------- | --------- | --------- | --------- |
| 2023-12-25 | -0.002110 | -0.005378 | 0.002374  |
| 2023-12-26 | 0.002207  | -0.002949 | -0.007422 |
| 2023-12-27 | -0.000253 | 0.001726  | 0.005480  |
| 2023-12-28 | -0.004973 | 0.000403  | 0.016263  |
| 2023-12-29 | -0.004232 | 0.004653  | 0.007702  |

</div>

<!--
三因子模型是尤金.法玛在1993年提出的。它是对CAPM模型的继承和改进。它保留了CAPM中的市场因子，然后加入了rolf banz提出的小市值因子，再加上价值因子，就构成了三因子模型。

尤金.法玛2013年获得了诺奖，他的主要贡献是有效市场假说，一般简称为EMH。

[click]

三因子是尤金.法玛与肯尼斯·弗伦奇共同提出的。他相当于尤金.法玛的后辈，1986年获得MIT的博士，后来在耶鲁大学、达特茅斯大学任教。

他们于2015年共同提出了五因子模型。

下面，我们来看三因子模型是如何构成的


[click]

这是三因子模型的公式。公式表明，资产的价格应该由市场因子、小市值因子和价值因子共同决定。

[click]

这里R_it是资产i在时间t时的收益率

[click]

R_ft是时间t时的无风险收益率，一般使用国债收益率来表示。这种做法在banz或者尤金.法玛他们的论文里面明确提到了。

[click]

R_mt是时间t时的市场收益率，它有多种算法。比如，你可以把沪深300的收益率当成市场收益率，也可以把你选定的一篮子资产池的收益当成市场收益率。在计算一揽子资产池收益率时，可以使用等权，也可以使用市值加权。

[click]

SMB_t是时间t时，做多小市值、做空大市值股票的组合收益率。要注意的是，它是基于市值构建出来的一个因子，已经揉合进来了组合和资产收益，我们一会再具体地看它是如何构建的。

[click]

HML是价值因子，它基于账面市值比这个财务因子。简单地说，账面市值比是我们熟知的另一个财务因子，市净率的倒数。


[click]

在三因子的构建过程中深受风险对冲的影响。在SMB和HML中，都有一个字母M，它是Minus，即减法的意思。SMB是做多小市值，做空大市值，两者收益相减。HML是做多高账面市值，做空低账面市值，两者收益相减。


[click]

具体来说，它首先对资产按市值和账面市值比进行分层。按市值分为两层，各占50%；按账面市值比分为三层，两端各占30%。

[click]

我们来看看这个因子在代码层面上应该如何实现。


[click]

首先，我们要构建这样一个dataframe， 它由date和asset索引，包含了市值mv，账面市值比bm和当日收益,它会是因子计算函数的参数。


[click]

然后我们对每一天进行遍历，计算每天的因子

[click]

我们使用qcut来进行分组。

[click]

在计算分组的收益时，我们需要考虑各个资产的分配权重问题。这里有等权的方法，也有按市值分配权重的方法。聚宽有一个量化课堂，在他们那里使用的是等权。宽邦的课程里，使用的好像是按市值分配权重。

我们这里使用的是按市值分配权重的方案。但是有一点不同的是，其他人在分配权重时，无论是小市值组还是大市值组，都是市值越大，权重越高。

在我们这里使用的是，大市值组，市值越大，权重越高；在小市值组，市值越小，权得越高。似乎这样更符合三因子的思想。


[click]

接下来就是计算每组的收益，以及最终的SMB因子。


[click]

现在计算HML因子。这次分组时，因为不是等分，所以，要传入分位数数组


[click]

接下来，根据组号筛选记录，计算权重。注意在HML因子计算中，除了中间的两组不参与之外，其它4组都要参与。

我们计算权重时，是把每一组都当成一个整体来计算权重，一共有四组，所以，最终要把权重除以4，这样才能保证各组权重之和仍然为1.


[click]

这段代码是在计算高账面市值比这一组的收益，以及低账面市值比这一组的收益，最后相减得到HML因子。


[click]

计算市场因子


[click]

最后，把所有的记录合并在一起，就生成了三因子。可以看出来，这里的因子，与我们在alphalens中要求的因子是不一样的。它实际上相当于alphalens在调用get_clean_factor_and_forward_returns之后的1D的收益。

这里有一个小问题，留给大家思考。在介绍整个流程时，我们没有提到收益率是如何计算的。在我们的示例代码中，使用的是当日收盘价与前一日收盘价计算出来的。

但是，这样的算法对吗？还是应该用T2开盘价除以T1开盘价，就象我们在alphalens中所做的那样？

好，这个问题留给大家自己思考。

-->
