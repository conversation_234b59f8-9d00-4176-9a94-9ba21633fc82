---
clicks: 2
---

<div class='abs mt-30 w-900px' v-motion
     :enter='{opacity: 1}'
     :click-4='{ opacity: 0}'>

```python{all|8|12}
import statsmodels.api as sm
pool = universe[:100]

alphas = {}

for asset in pool:
    asset_returns = returns.xs(asset, level="asset")
    asset_returns, factors_aligned = asset_returns.align(factors.dropna(), join='inner')
    X = sm.add_constant(factors_aligned)
    model = sm.OLS(asset_returns, X)
    results = model.fit()
    alphas[asset] = results.params['const']

alpha_df = pd.DataFrame(list(alphas.items()), columns=['asset', 'alpha'])

alpha_df.nlargest(10, 'alpha')
```
</div>

<!--
三因子模型是一个完整的选股模型。它的基本做法是，用个股与三因子做回归运算。如果个股的收益完全被三因子解释，那么，回归的截距就会是零，就没有alpha。

接下来有两种做法。

一种认为，应该选择alpha大于零的进行交易。另一种认为，应该选择alpha小于零的进行交易，因为总会有回归。

关于这个问题，大家自己有了解就行了。在聚宽的课程里，他们是买入alpha小于零的。回测结果很不错。

但是从根本上说，三因子本不应该有太出色的表现。市场因子是没有alpha的，这个大家能同意吧？账面市值比在日线级别其实是没有太多意义的，我们接下来就会证明为什么。

所以，三因子里面，真正能打的实际上只有一个小市值因子。

从另外的角度来看，线性回归天生有很多困难。金融活动中的噪声太多了。在大部分时间里，大多数资产里，你看到的，都是噪声。所以，这不是回归可以运用的场合。

所以，这里我们只简单讲一点代码技巧。


[click]

这里注意有一个align函数，它的作用是将收益与因子，在时间索引上对齐。


[click]

回归后的alpha就是常数项，也就是截距。
-->
