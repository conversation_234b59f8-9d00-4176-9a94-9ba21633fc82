---
clicks: 2
---

<NoteCell class='abs mt-10 w-full p-10'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python{all|5|all}
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)
universe = get_stock_list(start)
df = get_daily_basic(["mv"], start, end, universe)
factor = -1 * np.log(df["mv"])
factor.tail()
```
</NoteCell>

<NoteCell class='abs mt-10 w-full p-10'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'>

```python
barss = load_bars(start, end, tuple(universe))
prices = barss.price.unstack(level="asset")

merged = get_clean_factor_and_forward_returns(factor, prices, quantiles = 10)
create_returns_tear_sheet(merged)
```
</NoteCell>

<!--
在三因子模型中，Fama他们是这样运用小市值因子的。将资产按市值分为两组，然后再求每组的收益，这个收益就是小市值因子。

实际上，我们可以直接把市值当成因子，Alphalens会自动帮我们计算每个分层的收益，通过回归求出alpha。


[click]

注意我们没有直接使用市值作为因子，而是先对它取了对数。如果不这样做，大家知道公司的市值相差非常大，相当于两头有极值，这是不利于线性回归的。FAMA没有这个问题，是因为他先做了分层，然后在分层内部进行的权重分配，所以情况会好一些。

[click]

<run></run>

我们用这段代码来回测一下。结果显示，年化alpha 8.7%，可以接受。需要再看一下换手率。从分层均值收益图来看，线性化特征比较好，收益也主要靠做多小市值产生。

2018年到2021年间收益一盘。刚好这几年也正是白马股大行其道的时候，但从2021年至2023年，小市值因子一直不错，4年取得1.7倍的收益
-->
