---
clicks: 3
---

<NoteCell layout="horizontal"
          class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-4='{ scale: 0}'>

```python{all|8|10-17|19}{at:1}
from sklearn.datasets import load_iris
from sklearn.linear_model import LogisticRegression
from sklearn.inspection import DecisionBoundaryDisplay

iris = load_iris()
X = iris.data[:, :2]

classifier = LogisticRegression().fit(X, iris.target)

disp = DecisionBoundaryDisplay.from_estimator(
    classifier,
    X,
    response_method="predict",
    xlabel=iris.feature_names[0],
    ylabel=iris.feature_names[1],
    alpha=0.5,
)

disp.ax_.scatter(X[:, 0], X[:, 1], c=iris.target, edgecolor="k")
plt.show()
```
</NoteCell>

<!--

<run></run>

我们在讲标准化那一节，手搓了一个绘制决策边界的函数。现在，我们就来看看，sklearn为我们准备了什么样的方法。


[click]

它的使用方法很简单。首先，我们训练一个分类器。这里使用的是逻辑回归分类器。

[click]

然后，调用from_estimator方法，就生成了一个disp对象。

[click]

最后，调用这个对象的sactter方法，就绘制出了图形。

-->
