---
clicks: 3
---

<div class='abs mt-50 w-90% ml-10' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

```python
from sklearn.datasets import make_classification

X, y = make_classification(n_samples=100, n_features=2, n_informative=2, n_redundant=0, random_state=42)
X, y
```
</div>

<NoteCell layout='horizontal' class='abs mt-20 w-full'
          :enter='{ scale: 0}'
          :click-1-2='{ scale: 1}'>

```python
from sklearn.datasets import make_regression
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split

X, y = make_regression(n_samples=100, 
                        n_features=2, 
                        n_informative=2, 
                        noise=0.1, 
                        random_state=42)
(X_train, 
 X_test, 
 y_train, 
 y_test) = train_test_split(X, y, test_size=0.2)

model = LinearRegression()
model.fit(X_train, y_train)

y_pred = model.predict(X_test)
plt.plot(y_pred, y_test)
```
</NoteCell>

<NoteCell layout='horizontal' 
          class='abs mt-10 w-full'
          :enter='{scale: 0}'
          :click-2-3='{ scale: 1}'>

```python
import matplotlib.pyplot as plt
from sklearn.datasets import make_moons

# 生成 make_moons 数据集
X, y = make_moons(n_samples=100, noise=0.1, random_state=42)

# 可视化数据集
plt.figure(figsize=(8, 6))
plt.scatter(X[:, 0], X[:, 1], c=y, cmap=plt.cm.Set1, edgecolor='k', s=50)
plt.title("Generated Moons Dataset")
plt.xlabel("Feature 1")
plt.ylabel("Feature 2")
plt.show()
```
</NoteCell>

<NoteCell layout='horizontal' 
          class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-3-4='{ scale: 1}'>

```python
import matplotlib.pyplot as plt
from sklearn.datasets import make_circles

# 生成 make_circles 数据集
X, y = make_circles(n_samples=100, 
                    noise=0.1, 
                    factor=0.5, 
                    random_state=42)

# 可视化数据集
plt.figure(figsize=(8, 6))
plt.scatter(X[:, 0], 
            X[:, 1], 
            c=y, 
            cmap=plt.cm.Set1, 
            edgecolor='k', 
            s=50)
plt.title("Generated Circles Dataset")
plt.xlabel("Feature 1")
plt.ylabel("Feature 2")
plt.show()
```
</NoteCell>

<!--
有时候，我们需要一些人工合成数据。比如，出于教学的目的，我在本课程中，为了介绍confusion matrix，我就需要一个多分类的数据集。

sklearn为我们提供了这些工具。它们是sklearn.datasets中，以make_开头的API。

在这个例子中，make_classification为我们生成了一个有100个样本，2个特征的分类数据集。


[click]

我们还可以调用make_regression，帮我们生成用于线性回归任务的数据集。


[click]

可以通过Make_moons来生成一个具有月牙形状的二分类数据集。像svm这样的分类算法，就应该有能力区分这种数据。


[click]

可以通过make_circles来生成一个具有圆圈形状的二分类数据集。同样地，我们可以用它来检验一些高级算法。

-->
