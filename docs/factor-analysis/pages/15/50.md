---
clicks: 3
layout: two-cols
right: 60%
---

<div class='abs' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

## LabelEncoder
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{ opacity: 1}'>

## LabelEncoder
## OrdinalEncoder
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1}'>

## LabelEncoder
## OrdinalEncoder
## OneHotEncoder
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{ opacity: 1}'>

## LabelEncoder
## OrdinalEncoder
## OneHotEncoder
## MultiLabelBinarizer
</div>

::right::

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
from sklearn.preprocessing import LabelEncoder

le = LabelEncoder()
labels = ['red', 'green', 'blue', 'red', 'green']
encoded_labels = le.fit_transform(labels)
print("编码后的标签：", encoded_labels)
print("解码后的标签：", le.inverse_transform(encoded_labels))
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1-2='{ scale: 1}'>

```python
from sklearn.preprocessing import OrdinalEncoder
import pandas as pd

df = pd.DataFrame({
    'color': ['red', 'green', 'blue', 'red', 'green'],
    'size': ['S', 'M', 'L', 'XL', 'XS']
})

oe = OrdinalEncoder()
encoded_df = oe.fit_transform(df)
print("编码后的数据：\n", encoded_df)
print("解码后的数据：\n", oe.inverse_transform(encoded_df))
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-2-3='{ scale: 1}'>

```python
from sklearn.preprocessing import OneHotEncoder
import pandas as pd

df = pd.DataFrame({
    'color': ['red', 'green', 'blue', 'red', 'green'],
    'size': ['S', 'M', 'L', 'XL', 'XS']
})

ohe = OneHotEncoder()
encoded_array = ohe.fit_transform(df).toarray()
print("编码后的数据：\n", encoded_array)
print("解码后的数据：\n", ohe.inverse_transform(encoded_array))
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-3-4='{ scale: 1}'>

```python
from sklearn.preprocessing import MultiLabelBinarizer

mlb = MultiLabelBinarizer()
labels = [['湖北'], ['深圳'], ['广东'], ['深圳', '广东']]
encoded_labels = mlb.fit_transform(labels)
print("编码后的标签：\n", encoded_labels)
print("解码后的标签：", mlb.inverse_transform(encoded_labels))
```
</NoteCell>

<!--

我们刚刚讲的内容，多数是操作在数据集中的data部分，即X部分。关于标签，或者说target部分，y部分，我们要掌握哪些技巧呢？

很多时候，我们会用字符串来给数据打标签。但在训练之前，这些字符串标签必须转换为数字，才能用于模型训练。这就是编码器要完成的工作。

LabelEncoder 将类别值转换为整数值。每个类别都会被分配一个唯一的整数标识符。

[click]

OrdinalEncoder 类似于 LabelEncoder，但它可以处理多列数据，适用于 DataFrame。


[click]

OneHotEncoder 将类别值转换为 one-hot（独热码） 编码形式，即每个类别对应一个二进制向量中的一个位置，该位置的值为 1，其余位置的值为 0。

<run></run>

这里我们要解读一下它的结果。dataframe共有两个特征，color和size。color有3类，size有5类，样本则是5个。所以，生成的编码数据是5*8，即5行8列，前三列是颜色的独热编码，后五列是尺寸的独热编码。

所以，尽管我们在一行中，可能看到多于1个有，但是，在每个类别中，仍然只会出现一次1.


[click]

我们面临的可能是多分类问题，即一个样本可以属于多个类别。比如，一支股票可能属于多个概念板块。这时候，我们就需要MultiLabelBinarizer来帮我们编码。

<run></run>

我们看到，最后一个样本，即属于深圳，又属于广东，于是它的标签就是110


-->
