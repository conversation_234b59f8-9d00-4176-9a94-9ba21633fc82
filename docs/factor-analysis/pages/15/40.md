---
layout: two-cols
---

<NoteCell class='abs w-90%'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression

X = np.linspace(1, 10, 100)
y = X * 2 + np.random.normal(size=100)

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)

model1 = LinearRegression()
model1.fit(X_train.reshape((-1,1)), y_train.reshape((-1, 1)))

y_pred = model1.predict(y_test.reshape((-1,1)))
plt.plot(y_pred, y_test)
plt.title("without scaling")
```
</NoteCell>

::right::

<NoteCell class='abs w-90%'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
x_scaler = MinMaxScaler()
y_scaler = MinMaxScaler()

X_train_scaled = x_scaler.fit_transform(X_train.reshape((80,1)))
y_train_scaled = y_scaler.fit_transform(y_train.reshape((80,1)))

model2 = LinearRegression()
model2.fit(X_train_scaled, y_train_scaled)

X_test_scaled = x_scaler.transform(X_test.reshape((20,1)))
y_pred_scaled = model2.predict(X_test_scaled)
y_pred = model2.predict(X_test.reshape((20,1)))
plt.scatter(y_test, y_scaler.inverse_transform(y_pred_scaled), color='g', label="scaling back")
plt.scatter(y_test, y_pred, label="no scaling back")
plt.plot(y_test, y_test, label="true values")
plt.legend()
```
</NoteCell>

<!--

<run></run>

对回归模型来说，如果在训练时使用了缩放，那么缩放并预测的结果，如果不进行逆变换，得到的结果就可能跟真实结果相去甚远。

左图是一个qq图。当预测值跟真实值一致时，以预测值和真实值为坐标绘制的图形，就应该是一条45度角的直线。这是理想的情况。

右图则显示了三个对象。45度的直线用来标示目标；两个散点序列则分别是在预测之后，没有逆变换的情况下，和逆变换之后的结果。

我们看到，如果不进行scaling back，那么预测值序列就会与目标值相去甚远。

-->
