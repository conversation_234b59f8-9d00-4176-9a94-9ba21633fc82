---
clicks: 2
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

## 加州房价数据集

* <material-symbols-light-dataset-outline-rounded class='text-blue-400 animate-bounce mr-2' />20640个样本
* <mdi-flask-empty-outline class='text-blue-400 animate-bounce mr-2' />8个特征
* <material-symbols-target class='text-blue-400 animate-bounce mr-2' />一个目标变量
</div>
<div class='abs mt-20 ml-120 w-50%' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

```python
from sklearn.datasets import fetch_california_housing
housing = fetch_california_housing(data_home='/data')
X, y = housing.data, housing.target
print(X.shape, y.shape)
print(housing.feature_names)
print(housing.DESCR)
```
</div>

<div class='abs z--1 mt-10' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/12/califonia.jpg)
</div>

<div class='abs mt-40' v-motion
     :enter='{opacity: 0}'
     :click-1-3='{ opacity: 1}'>

## MNIST和OpenML平台

* <mdi-chart-timeline-variant-shimmer class='text-blue-400 animate-bounce mr-2' /> 杨立昆, LeNet-5
* <material-symbols-light-dataset-outline-rounded class='text-purple-500 animate-bounce mr-2' /> 1万张图像, 28*28像素
* <mdi-order-numeric-ascending class='text-blue-400 animate-bounce mr-2' /> 0~9的数字共十个类别
</div>

<div class='abs ml-120 mt-50 w-50%' v-motion
     :enter='{opacity: 0}'
     :click-1-3='{ opacity: 1}'>

```python
from sklearn.datasets import fetch_openml
mnist = fetch_openml('mnist_784', version=1)
X, y = mnist.data, mnist.target
```

</div>

<FlashText v-click="[2,3]"
           class='abs mt-1/3 text-center w-full text-3xl'>

https://openml.org

~6000数据集
</FlashText>


<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{ opacity: 1}'>

</div>

<!--
加州房价数据集包含 20640 个样本，每个样本有 8 个特征（如人口、中位年龄等）和一个目标变量（房价中位数）。

这个数据集不是sklearn的内置数据集，所以它的api是fetch_前缀开头的。当我们调用这个API时，它会会检查本地是否有这个数据集的缓存，如果没有，就去远程服务器上下载；如果有则直接读取本地缓存。

这也是现今大模型框架的常用做法。大家注意，这个API有一个参数data_home，通过这个参数我们设置数据要缓存的地方。如果没有指定，就会是sklearn默认的缓存地址。

实际上，sklearn中之前还有一个房价数据集，叫波士顿房价数据集。但是它的特征中包含了人种、犯罪率这样一些数据，所以后来遭到非议被下架了。


[click]

MNIST数据集来源于美国国家标准与技术研究院，它的首字母缩写正好是NIST。MNIST则是由杨立昆根据这个数据集创建的。他使用MNIST提出了LeNet-5模型，这是人类历史上第一个用来解决了实际问题的多层神经网络。

从示例可以看出，sklearnk中的数据来源于open ml


[click]

当我们给fetch_openml传入openml网站上，数据集对应的ID时，sklearn就会帮我们下载并缓存数据集。

通过openml，sklearn一下子就给我们提供了接近6000个数据集。

除此之外,sklearn还提供了其它数据集，比如20 Newsgroup，它由2000篇电子新闻文章组成，可以作为文本处理的任务的训练数据。covtype，一个森林植被覆盖的数据集。这些数据集的获取，都是通过fetch_前缀开头的API来得到的。


-->
