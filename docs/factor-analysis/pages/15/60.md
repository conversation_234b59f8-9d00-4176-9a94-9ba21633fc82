
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/sklearn-metrics.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>


<!--

在 Sklearn 中，它是把评估函数 (scoring)、成对度量 (pairwise metrics) 和距离函数放在同一个模块 -- metrics 下，在机器学习核心概念中，我们是分开讲的。

metrics模块是 sklearn 的顶层模块。在这个模块中，分别为分类、回归和聚类任务提供了多个评估函数。

这里我们注意plotting这个子模块。在plotting归类中，同一任务往往存在两类对象。比如，对于混淆矩阵而言，就同时存在confusion_matrix函数和ConfusionMatrixDisplay类。前者提供数据，后者进行具体的绘图操作。接下来，我们就详细介绍如何使用这些可视化工具。

-->
