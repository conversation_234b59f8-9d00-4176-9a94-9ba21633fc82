---
clicks: 3
layout: two-cols
right: 60%
---

<div class='abs' v-motion
     :enter='{opacity: 1, x: 350, y:150}'
     :click-1='{x:0,y:20}'
     :click-3='{ opacity: 0}'>

## RocCurveDisplay

</div>


<div class='abs mt-5' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{ opacity: 1}'>

## RocCurveDisplay
## PrecisionRecallDisplay
</div>
::right::

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1-3='{ scale: 1}'>

```python{all|11}{at:2}
from sklearn.datasets import fetch_openml
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.pipeline import make_pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import RocCurveDisplay, roc_curve

X, y = fetch_openml(data_id=1464, return_X_y=True)
X_train, X_test, y_train, y_test = train_test_split(X, y, stratify=y)

clf = make_pipeline(StandardScaler(), LogisticRegression(random_state=0))
clf.fit(X_train, y_train)

y_score = clf.decision_function(X_test)

fpr, tpr, _ = roc_curve(y_test, y_score, pos_label=clf.classes_[1])
RocCurveDisplay(fpr=fpr, tpr=tpr).plot()
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-3-4='{ scale: 1}'>

```python
from sklearn.metrics import (PrecisionRecallDisplay, 
                             precision_recall_curve)

prec, recall, _ = precision_recall_curve(y_test, 
                                         y_score, 
                                         pos_label=clf.classes_[1])

PrecisionRecallDisplay(precision=prec, recall=recall).plot()
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-='{ scale: 1}'>

```python

```
</NoteCell>
<!--
模型的可视化一般是模型自带的功能。比如，我们在第 13 课中，已经看到了决策树自带了可视化功能。

但是，在模型之外，也有一些通用的可视化工具。

[click]

<run></run>
ROC curve我们在上一课中已经介绍过了。不过这里的代码略有不同。直到第16行，代码还是一样。我们通过roc_curve方法得到了fpr和tpr的坐标。

但在绘制ROC曲线图时，之前我们是调用matplotlib手动绘制的。现在，我们可以借助RocCurveDisplay这个对象，一行代码实现绘制。

[click]

这里我们还看到它使用了pipeline创建了一条流水线，以便依次对数据进行缩放和训练。使用流水线不仅使代码简洁，它还带来一个额外的好处，就是我们在进行预测时，不需要额外对 X_test 进行缩放了。

[click]

PrecisionRecallDisplay是类似于ROC的一种曲线。

<run></run>

-->
