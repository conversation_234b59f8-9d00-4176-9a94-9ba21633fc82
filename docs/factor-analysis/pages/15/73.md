---
clicks: 5
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1, x: 100, scale: 0.8}'
     :click-1='{scale: 0.5, x:-100}'>

![](https://images.jieyu.ai/images/2024/12/3-classes-confustion-matrix.jpg)
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, scale: 0.8}'
     :click-1='{ opacity: 1, x:500, y: 100}'
     :click-4='{scale:0}'>

$$
accuracy = \frac{C_{11} + C_{22} + C_{33}}{total} = 98/150 = 0.653
$$
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, scale: 0.8}'
     :click-2='{ opacity: 1, x:500, y: 200}'
     :click-4='{scale:0}'>

$$
precision_A = \frac{C{{11}}}{C_{21} + C_{22} + C_{31}} = 32/53=0.064
$$
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, scale: 0.8}'
     :click-3='{ opacity: 1, x:500, y: 300}'
     :click-4='{scale:0}'>

$$
recall_A = \frac{C_{11}}{C_{11} + C_{12} + C_{13}} = 32 / 50 = 0.64
$$ 
</div>

<NoteCell class='abs mt-10 w-55% ml-45%'
          :enter='{ scale: 0}'
          :click-4='{ scale: 1}'>

```python{all|28-33}{at:5}
import numpy as np
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import ConfusionMatrixDisplay, confusion_matrix
import matplotlib.pyplot as plt


X, y = make_classification(
    n_samples=1000,
    n_features=20,
    n_informative=3,
    n_redundant=10,
    n_classes=3,
    random_state=42,
)

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

# 使用随机森林分类器进行训练
clf = RandomForestClassifier(random_state=42)
clf.fit(X_train, y_train)

y_pred = clf.predict(X_test)

# 计算混淆矩阵
cm = confusion_matrix(y_test, y_pred)

# 绘制混淆矩阵
disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=clf.classes_)
disp.plot(cmap=plt.cm.Blues)
plt.title("Confusion Matrix for Three-Class Classification")
plt.grid(False)
plt.show()
```
</NoteCell>


<!--
分类模型的性能可以通过准确率、精确率和召回率来评估。当这些指标都很高时，我们称该模型为“表现良好”的模型。但是，如果这些指标不够好，我们就想知道，究竟错在哪儿了，这就是混淆矩阵的作用。


[click]


在这个混淆矩阵中，在对角线上的元素代表了正确分类的样本数量。比如，单元格$C_{11}$代表了 A 类样本被正确预测为类别 A 的数量，$C_{22}$和$C_{33}$以此类推。根据这三个单位格的值，我们可以求出模型的准确率为：



[click]

从纵向看，每一列代表了一项预测结果。单元格$C_{21}$代表了 B 类样本被预测为 A 类的数量 -- 这是一个False Positive，单元格$C_{31}$则代表了 C 类样本被预测为 A 类的数量 -- 这是另一个 False Positive。


[click]

从横向看，每一行代表了一类真实样本。单元格$C_{12}$代表了A类样本被预测成为B类的数量 -- 这是一个False Negative。单元格${C_{13}}$代表了A类样本被预测成为C类的数量 -- 这是另一个False Negative。

False Negative意味着模型没有『回想』起它应该记得的正样本，所以，这样我们就可以算出召回率.


[click]

<run></run>

下面我们看看如何绘制出这个图。


[click]

绘图的关键也是两步，首先调用confustion_matrix函数，这个函数将生成具体的分析报告的数据。然后调用它的视图对象来完成最终的绘制工作。

-->
