---
clicks: 9
---

<NoteCell layout='horizontal'
          class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-9='{ scale: 0}'>

```python{all|8-12|14-16|18-20|22-23|25-30|32-53|55-62|64-71}{at:1}
import numpy as np
import matplotlib.pyplot as plt
from sklearn import svm
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler

# 生成一个简单的二分类数据集
X, y = make_classification(n_samples=100, 
                           n_features=2, 
                           n_informative=2, 
                           n_redundant=0, 
                           random_state=42)

# 增加一个高方差特征
X_high_variance = np.hstack([X, (X[:, 0] * 100)
                    .reshape(-1, 1)])

# 创建 SVM 模型
clf_no_scaling = svm.SVC(kernel='linear')
clf_with_scaling = svm.SVC(kernel='linear')

# 拟合模型，不进行特征缩放
clf_no_scaling.fit(X_high_variance, y)

# 对特征进行标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X_high_variance)

# 拟合模型，进行特征缩放
clf_with_scaling.fit(X_scaled, y)

# 绘制决策边界
def plot_decision_boundary(X, y, clf, title, scaler=None):
    h = .02  # step size in the mesh
    x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
    y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                         np.arange(y_min, y_max, h))
    
    # 创建一个网格，包含所有三个特征
    grid_points = np.c_[xx.ravel(), 
                        yy.ravel(), 
                        np.zeros_like(xx.ravel())]
    
    Z = clf.predict(grid_points)
    Z = Z.reshape(xx.shape)
    plt.contourf(xx, yy, Z, alpha=0.8)
    plt.scatter(X[:, 0], X[:, 1], c=y, 
                edgecolors='k', marker='o')
    plt.title(title)
    plt.xlabel('Feature 1')
    plt.ylabel('Feature 2')
    plt.show()

# 绘制未缩放特征的决策边界
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plot_decision_boundary(X_high_variance, 
                       y, 
                       clf_no_scaling, 
                       'Decision Boundary without Scaling', 
                       None)

# 绘制缩放特征的决策边界
plt.subplot(1, 2, 2)
plot_decision_boundary(X_scaled, 
                       y, 
                       clf_with_scaling, 
                       'Decision Boundary with Scaling', 
                       scaler)
plt.show()
```
</NoteCell>

<NoteCell class='abs mt-10 w-90% ml-5%'
          :enter='{ scale: 0}'
          :click-9-10='{ scale: 1}'>

```python
from sklearn.datasets import fetch_california_housing
from sklearn.preprocessing import StandardScaler

pd.options.display.float_format = "{:.2f}".format

data_home = os.environ.get('coursea_datahome', '/data')
dataset = fetch_california_housing(data_home=data_home)
X_housing, y_housing = dataset.data, dataset.target
feature_names = dataset.feature_names
scaled = StandardScaler().fit_transform(X_housing)
pd.DataFrame(scaled, columns = feature_names).describe()
```
</NoteCell>

<!--
如果一个数据集有多个特征，换我们量化人熟悉的语言，就是有多个因子。在这种情况下，如果单个特征不服从（大致上）标准正态分布，它们的表现往往会不够好。比如，目标函数中使用的一些元素，比如支持向量机的 RBF 核或者线性模型的 L1 和 L2 正则化器，假设所有特征都围绕零中心，或者具有相同数量级的方差。如果一个特征的方差比其它特征大几个量级，则该特征可能会主导目标函数。

这里我们通过一个例子来介绍下它的作用。

[click]

在这个例子中，我们先是生成了一个二分类数据，它有两个特征。现在，这两个特征都是符合正态分布的。

[click]

接下来，我们给它增加第三个特征，这个特征具有较高的方差，使得它的量纲与前两个不太一样

[click]

接下来，我们定义两个SVM模型，针对两种情况分别进行训练。

[click]

第一种情况，是直接对数据进行训练。

[click]

第二种情况，我们先将数据进行标准化，再进行训练

[click]

这一段代码是用来增强显示效果的，同时它也是一个非常常用的功能，叫做决策边界图。大家感兴趣的可以自己研究下代码。在后面，我们还会讲这个功能。

[click]

接下来，我们绘制出第一种情况下的分类和决策边界图

[click]

这里是绘制经过标准化之后的分类和决策边界图

现在，我们运行一下，看看结果。上方的图是未经标准化的，下方的图是经过标准化的。可以看出，未进行标准化之前，分类误差比较大。经过标准化之后，分类就很准确了。

[click]

我们刚刚看到了，某些情况下，标准化是非常有用的。但是，标准化究竟是一种什么样的变换呢？

下面，我们通过加州房产数据集来演示一下。

<run></run>

可以看出，转换后的平均收入特征的数据大多位于 [-2, 4] 范围内，而转换后的平均房屋占用率（AveOccup）数据则被压缩在较小的 [-0.2, 0.2] 范围内。如果这些特征本身符合正态分布，那么，50%分位处的数值应该是 0，因此，这些特征本身并不符合正态分布。

对不符合正态分布的数据，强行进行标准化调整，会带来什么后果呢？我们看到，不同特征之间的尺度仍然不太一致，并且，尽管调整后的 AveOccup 特征主要分布在 [-0.2,0.2] 的狭窄空间里，但是，它的最大值却高达 119.42。因此，对不符合正态分布的数据进行强调，无论是在特征内部，还是在特征间，可能仍然无法保证尺度的平衡。

因此，我们还要进一步寻找其它能调和特征尺度平衡的方法。
-->
