---
clicks: 16
layout: two-cols
right: 60%
---

<div class='abs' v-motion
     :enter='{opacity: 1}'
     :click-11='{ opacity: 0}'>

## MinMaxScaler
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-11-12='{ opacity: 1}'>

## MinMaxScaler
## MaxAbsScaler
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-12-13='{ opacity: 1}'>

## MinMaxScaler
## MaxAbsScaler
## RobustScaler
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-14-15='{ opacity: 1}'>

## MinMaxScaler
## MaxAbsScaler
## RobustScaler
## PowerTransformer
</div>

<div class='abs ml-100' v-motion
     :enter='{opacity: 0, y:-30}'
     :click-15-16='{ opacity: 1}'>

## PowerTransformer
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-16-17='{ opacity: 1}'>

## MinMaxScaler
## MaxAbsScaler
## RobustScaler
## PowerTransformer
## QuantileTransformer
</div>

<div class='abs mt-50 ml-80' v-motion
     :enter='{opacity: 1}'
     :click-1='{ x: -300}'
     :click-2='{ opacity: 0}'>
$$
\hat{X}= \frac{X - min(X)}{max(X) - min(X)}
$$
</div>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-15-16='{ scale: 1}'>

```python
from sklearn.datasets import fetch_california_housing

data_home = os.environ.get('coursea_datahome', '/data')
dataset = fetch_california_housing(data_home=data_home)
X_housing, y_housing = dataset.data, dataset.target
feature_names = dataset.feature_names

pd.DataFrame(X_housing, columns = feature_names).hist()
```
</NoteCell>

::right::

<div class='abs mt-55' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{ opacity: 1}'>

$$
William's R = \left( \frac{\text{Highest High} - \text{Close}}{\text{Highest High} - \text{Lowest Low}} \right) \times -100
$$
</div>

<NoteCell class='abs w-full'
          :enter='{ scale: 0}'
          :click-2-6='{ scale: 1}'>

```python{all|8,13|9,14|all}{at:3}
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler

# 生成示例数据
data = np.array([[10, 0.1], [50, 0.5], [90, 0.9]])

# 标准化
scaler_standard = StandardScaler()
data_standardized = scaler_standard.fit_transform(data)
print("标准化后的数据：\n", data_standardized)

# 缩放
scaler_minmax = MinMaxScaler()
data_scaled = scaler_minmax.fit_transform(data)
print("缩放后的数据：\n", data_scaled)
```
</NoteCell>

<NoteCell class='abs w-full'
          :enter='{ scale: 0}'
          :click-6-11='{ scale: 1}'>

```python{all|1|3-5|7,8|10,11}{at:7}
data = np.array([[10, 0.1], [50,0.5], [90,0.9]])

scaler_minmax = MinMaxScaler()
data_scaled = scaler_minmax.fit_transform(data)
print("缩放后的数据：\n", data_scaled)

print("对超出范围的数据进行缩放:\n",
      scaler_minmax.transform([[200, -0.5]]))

print("逆变换:\n",
      scaler_minmax.inverse_transform([[1.5,-0.5]]))
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-12-13='{ scale: 1}'>

```python
from sklearn.preprocessing import RobustScaler
from sklearn.datasets import fetch_california_housing
from sklearn.preprocessing import StandardScaler

pd.options.display.float_format = "{:.2f}".format

data_home = os.environ.get('coursea_datahome', '/data')
dataset = fetch_california_housing(data_home=data_home)
X_housing, y_housing = dataset.data, dataset.target

transformer = RobustScaler()
scaled = transformer.fit_transform(X_housing)
feature_names = dataset.feature_names

pd.DataFrame(scaled, columns = feature_names).describe()
```
</NoteCell>

<div class='abs ml--50 mt-10' v-motion
     :enter='{opacity: 0}'
     :click-13-14='{ opacity: 1}'>

StandardScaler
</div>

<div class='abs ml-50 mt-10' v-motion
     :enter='{opacity: 0}'
     :click-13-14='{ opacity: 1}'>

RobustScaler
</div>

<div class='abs flex justify-between left--100 mt-30' v-motion
     :enter='{opacity: 0}'
     :click-13-14='{ opacity: 1}'>

<img src="https://images.jieyu.ai/images/2024/12/california-standard-scaler.jpg" style="width: 45%">
<img src="https://images.jieyu.ai/images/2024/12/california-robust-scaler.jpg" style="width: 45%">

</div>

<NoteCell class='abs mt-14 w-80%'
          :enter='{ scale: 0,x:50}'
          :click-14='{ scale: 1}'
          :click-15='{x:150}'
          :click-16='{scale:0}'>

```python
from sklearn.preprocessing import PowerTransformer

dataset = fetch_california_housing(data_home='/data')
X_housing, y_housing = dataset.data, dataset.target
feature_names = dataset.feature_names

transformer = PowerTransformer()
scaled = transformer.fit_transform(X_housing)

df = pd.DataFrame(scaled, columns = feature_names)
df.hist()
```
</NoteCell>

<div class='abs ml-10' v-motion
     :enter='{opacity: 0, scale:1, y:-200}'
     :click-16-17='{ opacity: 1, scale:0.68}'>

![](https://images.jieyu.ai/images/2024/12/comparison-between-robust-quantile.jpg)

</div>

<!--
MinMaxScaler 是一种常见的缩放器，它将数据缩放到给定的范围，通常是 [0, 1]。它能把数据映射到给定的范围，但不调整数据的分布


[click]

大家还有印象吧，跟William's R很相似。

[click]

这里我们复习一下sklearn中的缩放器的语法。

[click]

先是定义一个缩放器

[click]

然后调用fit_transform，就会得到结果。这个缩放器变量不能丢弃，我们在训练阶段中使用了缩放器，那么在预测阶段，我们也应该使用同样的缩放器对数据进行预处理，得到的预测结果，还要逆变换回去。

[click]

<run></run>

现在我们来看看，两个缩放器的作用。

通过使用min-max缩放，无论之前的数据分布如何，都被缩放到了[0,1]的范围内。

[click]

min-max缩放有两个重要的缺点。第一点，它不能改变数据的分布。如果我们的算法比较简单，不能进行非线性拟合，这就是比较重要的缺点；

第二点就是，它不能缩放和逆缩放超出min-max范围的数据。

假设我们要训练一个模型M。训练数据是1990年到1999年的万科A。在训练前，我们先使用minmax缩放器对数据进行了缩放。然后我们对2018年的万科A股价进行预测。此时万科A已上涨了10倍之多。

在进行预测时，我们要用同一个缩放器，先对2018年的股价进行同样的缩放，然后才能用模型M进行预测，得到预测结果之后，还要再逆变换回去得到预测的股价。

这个过程中会发生什么情况呢？

[click]

我们假设这是万科A在90年代时的股价数据。输入数据有两个特征，特征1的最大最小值是10, 90；特征2的最大最小值是 0.1, 0.9。

[click]

我们用输入数据训练了一个minmax缩放器 scaler_minmax。它记忆了一些事情。同时我们还训练了模型M，不过，在代码中就没有体现出来，因为此时我们并不关心模型。


[click]

现在是2018年，万科的股价数变变成了【200,-0.5】。我们现在处在预测阶段。

在预测之前，我们要使用同一个缩放器，对输入数据进行缩放。它会把200变换为2.375, -0.5变换为-0.75。

[click]

如果预测出来的值是1.5和-0.5，那么，逆变换的结果会是130和-0.3.

现在，我们要问一个问题。无论是200,还是130，这些都是之前minmax scaler未曾看到过的数据。那它是如何推导出来这些数据范围以外的数据的呢？

实际上，在内部，minmax scaler使用了线性外推机制。但是，这肯定不是我们想要的结果。因为如果数据能够这样线性外推，那我们还需要复杂的算法干什么？

当然，有一些算法下，可以使用min-max缩放，比如线性回归。对能使用线性回归的算法而言，做不做缩放都ok。

所以，在资产定价中，我们一般是不能使用min-max缩放的。理论上，一家公司只要经营得法，它一直在创造财富，那么它的市值就应该一直上涨，涨上天去都是合理的。这个方法用来处理鸢尾花数据很合适。因为鸢尾花的花瓣长度，在可见的时间尺度内，并不会显著变异。

也适合用来对图像数据进行缩放。因为在RGB颜色空间里，每个像素的取值已经被人为规定为0到255.

那么标准化不会有同样的问题吗？

标准化也会有同样的问题，只是程度会轻很多。如果同一样本，前后两段时间序列的分布不再近似于正态分布，那么标准化就会出问题。

[click]

maxabscaler缩放方式与minmaxscaler类似，但它的值域会落在[-1,1]。

[click]

这是另一个缩放器，RobustScaler。我们看到运行一下。<run></run>

[click]
 
它跟标准化的结果有所不同，比如，标准化后的均值都为零，而RobustScaler后的均值则不为零。标准化后，中位数不为零，但RobustScaler后中位数为零。

如果我们修改加州房产数据集，增加更多的outlier，你会发现，尽管整体的均值发生了改变，但中心区域，即25%到75%的数据分布不受影响。25分位数仍然 是-0.45，75分位数仍然是0.55。

[click]

正态分布在机器学习中有着非常大的优势。但是，如果一个数据集本身不服从正态分布，我们有没有办法将它转换为正态分布呢？

我们在第2课的视频中有介绍过，比如cox-box方法。不过，sklearn提供了更简单易用的API。就是这个PowerTransformer。

[click]

<run></run>

我们仍以加州房产数据为例，看看 PowerTransformer 的转换能力如何。

从直方图来看，转换能力还是不错的。

但是，sklearn还有万能转换器，能将几乎所有的分布转换成为正态分布。

[click]

这个缩放器就是QuantileTransformer。这个图对比展示了6种分布，对数正态分布、卡方、威布尔等6种分布，分别经过PowerTransformer和QuantileTransformer转换后的结果。

PowerTransformer实际上有两种算法，一种是cox-box,另一种是yeo-johnson算法，所以，每一个样本，实际上显示为4行，最后一行都是quantile_transform的结果。可以看出，这个结果相当令人满意。

为什么quantile_transform转换如此令人满意呢？这个要从第一性原理来研究，其实它是对数学技巧要求最低的一种变换。因为它只需要把原始分布的分位数，映射到标准正态分布的分位数上，这样就完成了。

有时候大道至简。这一点在投资中仍然是适用的。
-->
