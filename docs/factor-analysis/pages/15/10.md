---
clicks: 7
---

<div class='abs w-30% mt-30 ml-20' v-motion
     :enter='{opacity: 1}'
     :click-7='{ opacity: 0}'>


![](https://images.jieyu.ai/images/2024/12/iris-virginica.jpg)
</div>

<div class='abs ml-120 mt-50' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

* <mdi-chart-timeline-variant-shimmer class='text-blue-400 animate-bounce mr-2' /><PERSON>/<PERSON>
* <mdi-leaf class='text-blue-400 animate-bounce mr-2' />150个样本
* <mdi-leaf-maple class='text-red-500 animate-bounce mr-2' />4个特征
* <material-symbols-light-category class='text-purple-500 animate-bounce mr-2' />三个分类标签
</div>

<div class='abs w-50% ml-110 mt-50' v-motion
     :enter='{opacity: 0}'
     :click-1='{ opacity: 1}'
     :click-3='{ y:-80}'
     :click-7="{opacity:0}">

```python{all|1,2|3,4|5|6|7}{at:2}
from sklearn.datasets import load_iris
iris = load_iris()
X, y = iris.data, iris.target
print(X[:5, :],"\n\n", y[:5])
print(f"\n{iris.feature_names}")
print(f"\n{iris.target_names}")
print(f"\n{iris.DESCR[:200]}")
```
</div>

<div class='abs w-50% ml-110 mt-80' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{ opacity: 1}'
     >

```md
[[5.1 3.5 1.4 0.2]
 [4.9 3.  1.4 0.2]
 [4.7 3.2 1.3 0.2]
 [4.6 3.1 1.5 0.2]
 [5.  3.6 1.4 0.2]] 
```

```md
[0 0 0 0 0]
```
</div>

<FlashText v-click="[3,4]"
           class='abs mt-1/3 ml-200 w-full text-3xl'>
data
</FlashText>

<FlashText v-click="[3,4]"
           class='abs mt-108 ml-200 w-full text-3xl'>
target
</FlashText>

<div class='abs ml-110 mt-80' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{ opacity: 1}'>

```md
['sepal length (cm)', 'sepal width (cm)', 'petal length (cm)' ...]
```
</div>

<div class='abs mt-80 ml-110' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{ opacity: 1}'>

```md
['setosa' 'versicolor' 'virginica']
```
</div>

<div class='abs mt-80 ml-110' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{ opacity: 1}'>

```md
Iris plants dataset
--------------------

...

The famous Iris database, first used by Sir R.A. Fisher. The
dataset is taken from Fisher's paper. Note that it's the 
same as in R, but not as in the UCI Machine Learning 
Repository, which has two wrong data points.
...
```
</div>

<div class='abs mt-15 w-50%' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{ opacity: 1}'>

## Digits数据集

</div>

<div class='abs mt-40 ' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{ opacity: 1}'>

* <mdi-account class='text-blue-400 animate-bounce mr-2' />土耳其学者 E. Alpyadin, 1998年
* <material-symbols-light-dataset-outline-rounded class='text-blue-400 animate-bounce mr-2' /> 1797个样本，10个分类
</div>
<div class='abs ml-140 mt-55 w-30%' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/12/digit-0.jpg)
</div>

<div class='abs ml-120 mt-20 w-50%' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{ opacity: 1}'>

```python
from sklearn.datasets import load_digits

digits = load_digits()
print(digits.data.shape)
plt.gray()
plt.matshow(digits.images[0])
plt.show()
```
</div>


<!--
iris数据集是机器学习社区中非常有名的一个数据集。它是由生物学家Edgar Anderson收集的，后由Fisher整理发布于1936年。Fisher是著名的统计学家，他被称为一已之力，构建了现代统计学的人。我们熟知的多个统计分布，比如F-分布，都是他发明或者以他的名字命名的。

这个数据集共有150个样本，4个特征，包含了三个种类的鸢尾花。因此，这个数据集的标签也是三类。


[click]

在sklearn中，我们可以通过这段代码加载和查看数据。


[click]

加载数据的API归类在datasets模块中。然后通过load_iris()函数加载数据


[click]

最重要的属性是 data和target，分别包含特征和标签。


[click]

data属性就是特征数据，是一个二维数组，每一行是一个样本，每一列是一个特征。这些特征的名字，在iris数据集中，通过feature_names属性获得。


[click]

target属性是标签数据，它是一个一维数组，每一个元素，对应着data中的一行，就是该样本的标签。这些标签的名字，在iris数据集中，通过target_names属性获得。

[click]

最后，iris数据集还提供了一个descr属性，记录了数据的来源，分布情况等等。

在机器学习中，数据集扮演了非常重要的角色，我们自己平时也可能要搜集、整理和构造自己的数据集。而且数据集还涉及到版本管理，还是一个有一定复杂度的工程问题。

在这里我们详细介绍iris数据库，也正是借他山之石的目的。在这里我们学习了一种数据集的组织方式，后面我们自己构建简单的数据集时，就可以使用这种方式。


[click]

digits数据集由土耳学者E. Alpyadin于1998年整理，是一个手写数字数据集。它有1797个样本，每张图片是一个8x8的灰度图，共64个像素。每张图片对应一个数字，数字0-9

在sklearn中，还有其它内置数据集，这些数据集都是通过以load_为前缀的函数进行加载。
-->
