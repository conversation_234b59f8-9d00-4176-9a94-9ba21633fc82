<div class="mt-10 ml-5">

## 数据集
## 预处理
### 标准化
### 缩放
## 逆变换
## 编码器
## 度量模块
## 可视化
</div>

<!--
今天的课程的主要内容就到这里。现在，请大家在留言区提出问题。在大家提问题的同时，我对今天的课程进行一个小结。

今天我们主要介绍了sklearn当中的一些通用模块。这些模块，即使我们在使用其它机器学习模型框架时，很多也可以继续使用。同时，SKLEARN的API设计具有良好的风格和一致性，它们也是初学者入门时，理解相关概念的最佳指南。

我们首先介绍了sklearn中的数据集。这包括了内置数据集、外部数据集，以及人工合成数据集。人工合成数据集在我们快速验证概念和模型时非常好用。在今天的课程中，我们就多次调用这一功能。

我们收集得到的数据不一定刚好就适合用于训练。为此，sklearn提供了预处理模块 - preprocessing。在这个模块中，提供了标准化、缩放、编码器等功能。

这一节的重点是理解缩放的副作用。我们重点讲解了为什么象min max这样的缩放器，不适合在多数量价相关的量化策略中使用。我们还介绍了 quantiletransformer,它是一个几乎能将任何形态的数据分布转换为标准正态分布的转换器。

我们在之前已经花比较多的篇幅讲解了度量函数。在这一课，我们介绍了sklearn中的metrics模块的构成，并重点对它提供的可视化功能进行了介绍。

好，今天的课程就到这里，谢谢大家！

-->
