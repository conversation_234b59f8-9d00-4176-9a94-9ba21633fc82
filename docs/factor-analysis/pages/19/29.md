---
clicks: 10
---

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'>

```python
import pickle
import lightgbm as lgb

with open("/tmp/p7-model.pkl", "rb") as f:
    model = pickle.load(f)
```
</NoteCell>

<NoteCell outputMt="-300px" outputMl="-100px" scaleImg="55%"
          class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
lgb.plot_tree(model, tree_index=0, figsize=(15,12))
```
</NoteCell>

<NoteCell layout='horizontal' outputMt='3rem'
          class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'
          :click-5="{scale: 0}">

```python{all|1-8|28-33|35-37}{at: 2}
def generate_data():
    np.random.seed(42)
    c = np.linspace(0, 1, 100)
    p = np.random.rand(100)
    target = c * (1 + p)
    df = pd.DataFrame({'c': c, 'p': p, 'target': target})
    return df

def train_lightgbm_model(df):
    X = df[['c', 'p']]
    y = df['target']
    train_data = lgb.Dataset(X, label=y)
    params = {
       'objective':'regression',
       'metric': 'rmse',
       'num_leaves': 31,
       'learning_rate': 0.05,
       'feature_fraction': 0.9,
       'verbosity': -1
    }
    model = lgb.train(params, train_data, num_boost_round=2)
    return model

df = generate_data()

model = train_lightgbm_model(df)

c = np.linspace(0, 100, 10)
p = np.linspace(1, 1, 10)
test_data = pd.DataFrame({
    "c": c,
    "p": p
})

y_pred = model.predict(test_data)

print(y_pred)
```
</NoteCell>

<div class='abs ml-150 mt-50' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>

$$
y = c * (1 + p)
$$
</div>

<NoteCell scaleImg="70%"
          layout='horizontal'
          class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-5-11='{ scale: 1}'>

```python
lgb.plot_tree(model, tree_index=0, figsize=(5,5))
lgb.plot_tree(model, tree_index=1, figsize=(5,5))
```
</NoteCell>

<div class='abs w-50% left-50%' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{opacity: 1}'>

<v-drag-arrow color='red' pos="81,274,15,-99"/>

</div>

<div class='abs w-50% left-50%' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{opacity: 1}'>

<v-drag-arrow color='red' pos="81,274,115,-89"/>

</div>

<div class='abs w-50% left-50%' v-motion
     :enter='{opacity: 0}'
     :click-8-11='{opacity: 1}'>

<v-drag-arrow color='red' pos="75,274,210,-66"/>

</div>

<div class='abs w-50% left-50%' v-motion
     :enter='{opacity: 0}'
     :click-9-11='{opacity: 1}'>

<v-drag-arrow color='red' pos="74,275,206,143"/>

</div>

<FlashText v-click="[10,11]"
           class='abs mt-60 left-55% text-center w-50% text-1xl'>

0.7648 + 0.0266 == 0.7914
</FlashText>

<!--
我们刚刚训练出来的模型是一个亏钱模型。这并不意外。但是我们得弄清楚，lightgbm为什么不能从中学习到规律。

要查明真相，我们的第一个技巧是，绘制出决策树。

在前面的训练中，模型生成了多棵决策树。根据梯度提升决策树模型的原理，我们知道，第0棵子树几乎决定了决策的整个框架和走向，其它的子树，只是在设法消化第一棵子树的残差。

所以，我们先绘制第0棵子树，看看它到底做了什么。

节点信息表明了树在分裂时，使用的特征以及分裂依据。我们看到，在大部分节点，都只是依据特征c1。按我们的编码规则，c1就是前一天的收盘价。也就是说，在大部分决策里，模型只是把前一天的收盘价当成预测值。

这样的结果是什么？大量的个股在多数时间内都是低波动的，比如波动在1%以内，因此，当我们直接把昨收价当成预测结果时，误差也自然就限定在1%以内。看上去是个不错的结果，其实全无用处。

既然决策树只是围绕前一天的收盘价进行预测，显然，它并不会从之前的走势上进行学习，也未能利用时间序列数据。


[click]

为了更清楚地理解lightgbm在回归任务中是如何进行决策的，我们再补充一个仿真数据的例子。

通过这个例子，我们将不仅看到lightgbm是如何进行决策的，还能看到它是如何计算出最终的预测值的。


[click]

这段代码根据这个公式来生成数据。它有两个特征c和p。在生成训练数据时，c的取值是[0,1]，线性增长，p的取值也是[0,1]，但是是随机分布。


[click]

在预测时，我们使用的c和p有所不同。我们让c在0到100间取10个数，第一个数是0，第二个是11.11,以此类推。而p则取值为常量。这样我们就能观察到单变量变化下，模型应该如何进行预测。


[click]

<run></run>

我们运行一下，看看结果。

一个很明显的事实是，模型只预测出了两个值。从第二个值开始，也就是从c等于11开始，无论c如何变化，它都只能输出相同的值。

这种情况在前面的对照图中也出现过。在前面的图中，我们提到过，部分预测值在较长的时间里是保持不变的。

这是为什么呢？


[click]

<run></run>

我们需要绘制出它的子树，对照子树来解释。在训练中，我们指定的轮数是2轮，所以，最终的模型将包含两棵子树。

在进行预测时，输入的c是11 。


[click]

[click]

[click]

当c=11时，在第一棵子树中，最终会走到最右下的节点，此时我们得到值0.765

[click]


在第二棵子树中，它也会走到相同的位置，此时得到的值就是0.027

[click]

两者相加，就是最终的预测值，0.7914。

这就是梯度提升决策树做回归任务的秘密。

所以，梯度提升决策树并不能自动学习规律，它是通过训练，把预测值分配到叶子节点上去的一种方法，训练的过程就是不断地调节路径的过程，以决定在预测时，将以什么样的路径，寻址到对应的叶子节点上。

它的预测过程，更像是一个复杂分段函数的执行过程。

这是本章最重要的一个结论。我们该如何运用这个结论呢？

方法之一，就是我们在前面的原理部分所讲的，我们通过曲线拟合找到规律，根据规律预测价格，然后把拟合过程中的参数、误差作为特征来训练lightgbm。然后在预测时，我们把同样的事情再做一遍。

下面，我们就用具体的例子来说明如何使用这个方法。
-->
