---
clicks: 10
---

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-0='{ scale: 1}'>

```python
import pickle
from numpy.lib.stride_tricks import as_strided
import lightgbm as lgb
from sklearn.metrics import mean_absolute_percentage_error


np.random.seed(78)
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 29)
universe = 2000
barss = load_bars(start, end, universe=universe)

def rolling_time_series(ts: NDArray, win: int):
    stride = ts.strides
    shape = (len(ts) - win + 1, win)
    strides = stride + stride
    return as_strided(ts, shape, strides)
    
def rolling_close(group, win, columns):
    index = group.index
    if len(group) < win:
        features = np.full((len(group), win), np.nan)
        df = pd.DataFrame(features, columns=columns, index=index)
        return df
        
    rolled = rolling_time_series(group["close"].values, win)
    padded = np.pad(
        rolled, ((win-1, 0), (0,0)), mode="constant", constant_values=np.nan)
    df = pd.DataFrame(padded, columns=columns, index=index)
    return df

win = 10
feature_cols = [f"c{win-i}" for i in range(win)]

features = barss.groupby(level="asset").apply(rolling_close, win, columns = feature_cols)
features = features.droplevel(0)

def train_test_split(data, feature_cols, cuts = (0.7,0.2)):
    train, valid, test = [], [], []
    def cut_time_series(group, cuts):
        itrain = int(len(group) * cuts[0])
        ival = itrain + int(len(group) * cuts[1])

        return (group.iloc[:itrain], group.iloc[itrain: ival], group.iloc[ival:])

    for item in data.groupby("asset").apply(cut_time_series, cuts=cuts).values:
        train.append(item[0])
        valid.append(item[1])
        test.append(item[2])

    df_train = pd.concat(train)
    df_valid = pd.concat(valid)
    df_test = pd.concat(test)

    return (df_train[feature_cols], df_valid[feature_cols], df_test[feature_cols], 
            df_train["target"], df_valid["target"], df_test["target"])

data=features
data["ret"] = barss["close"].unstack().pct_change().stack()
data["target"] = barss["close"].unstack().shift(-1).stack()
data = data.dropna(subset=["target", "ret"])
data.reset_index(inplace=True)

(X_train, X_val, X_test, 
y_train, y_val, y_test) = train_test_split(data, feature_cols)

train_data = lgb.Dataset(X_train, label=y_train)
valid_data = lgb.Dataset(X_val, label=y_val)
```
</NoteCell>

<NoteCell layout='horizontal' outputWidth='40%' class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-6='{ scale: 0}'>

```python{all|2|3|4,5|6|all}{at:1}
params = {
    'objective': 'regression',
    'metric': 'mape',
    'num_leaves': 31,
    'learning_rate': 0.1,
    'random_state': 42
}

esr = 50
evals_result = {}

num_rounds = 500
model = lgb.train(
    params,
    train_data,
    num_boost_round=num_rounds,
    valid_sets=[valid_data],
    callbacks = [lgb.early_stopping(esr), 
                 lgb.record_evaluation(evals_result)]
    )

y_pred = model.predict(X_test.values)
mean_absolute_percentage_error(y_test, y_pred)
```
</NoteCell>

<NoteCell layout='horizontal' outputMt="2rem" class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-6='{ scale: 1}'>


```python{all|20-24|38|54|all}{at: 7,maxHeight: 450}
from matplotlib.dates import WeekdayLocator

def eval_model(model, X_test, data, threshold, traces):
    df = data.rename(columns={"c1": "prev", "target": "actual"})
    df = df.loc[X_test.index]
    
    df["pred"] = model.predict(X_test.values)
    
    error = mean_absolute_percentage_error(df["actual"], df["pred"])

    print(f"mape is {error:.3f}")
                                       
    df["pred_ret"] = df["pred"]/df["prev"] - 1

    long_df = df.query(f"pred_ret > {threshold}")

    print(f'actual p&l {long_df["ret"].mean():.2%}')

    if traces > 0:
        row = int(np.sqrt(traces))
        col = int(traces / row)

        if row * col < traces:
            row += 1

        symbols = long_df["asset"].unique()[:traces]
        _, axes = plt.subplots(row, col, figsize=(row * 4, col * 2))
        axes = axes.flatten()
        
        for i, symbol in enumerate(symbols):
            close = (df.query(f"asset == '{symbol}'")[["prev", "date"]]
                       .set_index("date")
                    )
            x = list(close.index.strftime("%m/%d"))
            axes[i].plot(x, close, label="close")

            pred_close = df.query(f"asset == '{symbol}'")["pred"]
            axes[i].plot(x[1:], pred_close[:-1], label="pred")

            locator = WeekdayLocator()
            axes[i].xaxis.set_major_locator(locator)
            
            # mark signals
            filter = (df["pred_ret"] > threshold) & (df["asset"] == symbol)
            signal_dates = df[filter]["date"]
            x = [i.strftime("%m/%d") for i in signal_dates]
            y = close.loc[signal_dates]
            axes[i].scatter(x, y, marker='^', color='red')
            axes[i].set_title(symbol)
            axes[i].legend()

        plt.tight_layout()

eval_model(model, X_test, data, threshold = 0.02, traces = 6)

with open("/tmp/p7-model.pkl", "wb") as f:
    pickle.dump(model, f)
```

</NoteCell>

<!--
<run></run>

这段代码是执行训练的。


[click]

简单介绍下参数。第一项是objective，这是每次训练前，我们都需要指定的。


[click]


评估函数使用是的mape，也就是绝对平均百分比函数。如果objective指定为回归,缺省的评估函数是rmse。rmse作为损失函数不错，但在量化交易中作为评估函数，会有一些不太直观。mape可以反映出预测值与实际值的百分比差距。如果我们预测的是绝对价格，那么mape在量化中是比较适合的；如果我们预测的是涨跌幅，那么mae，即平均绝对误差更合适。



[click]

这两个参数实际上是默认参数。

[click]

这里我们指定了随机种子。在本章中，我们在提取数据时，指定了随机种子，在训练时，也指定了随机种子，这样一来，我们就可以在多个模型间进行相互比较。

其它部分没有新的知识点。


[click]

现在，我们看看运行的结果。从结果上看，mape值为1.8%。这个结果是什么意思呢，它是好是坏？

它能告诉我们，如果我们拿这个模型预测出来的结果去投资，就能做到盈利呢？

实际上，mape指标并不能回答这些问题。机器学习社区提供的指标，没有一个能回答这个问题。

要回答这个问题，我们得回到第一性原理上来，自己推导出新的指标。

大家思考一下，当我们训练出来一个模型之后，我们会如何使用它？我们会把股票池里的样本取出来，用跟训练时同样的方法，去提取特征，然后让模型来预测，对吧？

这是第一步。拿到预测结果时，我们会对预测结果进行排序，并且从预测涨幅最高的开始买入，忽略预测涨幅在2%以下的个股。

但是mape指标是怎么计算的？它是把所有的预测结果都纳入计算当中。这会产生一些偏差。

比如，如果对预测上涨2%以上的个股，模型的准确率很差，而对2%以下的个股，模型的准确率较高，那么，mape统计出来的模型的误差可能会很小，但是当我们用以投资时，就会发现，这个指标没有意义。因为在我们关注的高涨跌幅部分，它的预测都是错的。

也有可能情况反过来。

无论是哪一种情况，我们已经充分说明了，mape是机器学习中所有评估指标中，可能最贴近量化交易的指标，但它仍然不适用。

我们必须得发明自己的指标。

[click]

<run></run>

这段示例代码是接着上一个示例的，也就是用的数据和模型都是之前运行的结果。

它的基本想法是，用户指定一个关注的区间，然后计算出在该区间内，所有个股的实际收益率平均值。并且，它还会抽样绘制一些个股的走势图，方便我们直观地看到预测值是多少，实际收盘价是多少。

代码上需要讲解的不多。我们可以看下如何动态调整子图的行、列数。


[click]

假设我们要绘制n个子图，要让这些图形的长宽比最恰当的方式，可能就是先对n开方，以此作为行数的基数，然后再对可能溢出的情况进行一些调整。

[click]

此外，存在一个如何正确地标记出买入点的问题。在对照图中，我们绘制了收盘价折线图和预测价格折线图。对每一个预测价格，我们希望在同一位置上看到它的真值，而买入点则应该是前一天。

所以，在这段代码中，我们对x的坐标进行了一个平移，以反应我们刚刚讲过的逻辑。


[click]

调用这个方法时，我们要传入训练好的model, 测试集，原始数据集， 关注的个股的阈值，以及想要显示对照走势的个股的支数。

注意这里的data并不是最原始的数据集。它是在数据集划分之前的那个数据集，应该有与训练集、验证集和测试集一致的索引。

[click]


现在，我们结合结果来讲解一下，这个图怎么看。

首先，它跟之前一样，输出了mape值。然后，它输出了预测上涨2%以上个股的实际收益，这里是-0.82%。

我们看下下面的对照图。蓝色的线是股价的实际走势，黄色线是预测的走势。红色标记是买入点。如果在买入点之后，蓝色线往上，这次交易就可能盈利，反之，则会亏损。

我们还看到，有几张图黄色的线在较长的时间内是平的，这意味着模型的预测固定在这个价格上。

好，现在我们终于有了一个可用的评估指标了。实际上，我们应该修改它，并且通过训练参数中的feval参数传递给lightgbm，以便参与运算。不过，在本课中，为简便起见，我们将在训练时使用mape，而在最终的评估时，使用这个自定义的指标。

-->
