---
clicks: 10
---

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'>

```python
import datetime
import logging
from numpy import ndarray
from numpy.typing import ArrayLike, NDArray
import os
import pickle
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import mean_absolute_percentage_error
from numpy.lib.stride_tricks import as_strided
import lightgbm as lgb
from matplotlib.dates import WeekdayLocator


logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)
mlogger = logging.getLogger("matplotlib")
# supress matplotlib info level logging, which is anoying
mlogger.setLevel(logging.WARNING)


class BaseLGBModel:
    def __init__(self):
        self._model_ = None
        self._name_: str | None = None
        self._desc_: str | None = None

        # for encode/decode categorical features
        self._label_encoders_ = {}

    @property
    def model(self):
        return self._model_

    def save(self, path: str, auto_rename: bool = True, name: str | None = None):
        """将模型保存到指定路径。

        如name未指定，将使用模型名作为文件名。如果同名文件已存在，将自动重命名为当前时间戳。文件名会自动加pkl后缀
        Args:
            path: 保存路径
            auto_rename: 如果同名文件已存在，是否自动重命名
            name: 保存文件名
        """
        if self._model_ is None:
            raise ValueError("请先调用fit方法训练模型")

        if not os.path.exists(os.path.expanduser(path)):
            raise ValueError("保存路径不存在")

        name = name or self._name_
        if name is None:
            raise ValueError("请指定保存文件名")

        file = os.path.join(path, f"{name}.pkl")
        if os.path.exists(file):
            if auto_rename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"{name}_{timestamp}.pkl"
                file = os.path.join(path, name)
                assert not os.path.exists(file)
                logger.warning("保存路径已存在同名文件，已自动重命名为%s", name)
            else:
                raise ValueError("保存路径已存在同名文件，请指定指定文件名")

        with open(file, "wb") as f:
            pickle.dump(
                {
                    "model": self._model_,
                    "name": self._name_,
                    "desc": self._desc_,
                    "save_time": datetime.datetime.now(),
                },
                f,
            )

    def load(self, file_path: str):
        with open(file_path, "rb") as f:
            data = pickle.load(f)
            self._model_ = data["model"]
            self._name_ = data["name"]
            self._desc_ = data["desc"]
            logger.info("加载模型%s成功。", self._name_)

    def rolling_time_series(self, ts: NDArray, win: int) -> NDArray:
        """生成rolling time series

        Args:
            ts: 一维时间序列
            win: 窗口大小
        Returns:
            返回shape为（len(ts) - win + 1, win）的二维numpy数组
        """
        stride = ts.strides
        shape = (len(ts) - win + 1, win)
        strides = stride + stride
        return as_strided(ts, shape, strides)

    def encode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """将类别特征编码。

        类别特征可能需要编码解码。此时需要label_encoders属性保存编码器。此方法会自动保存编码器到label_encoders属性中。
        """
        if name in self._label_encoders_:
            logger.info("已经存在%s特征编码器，直接复用")
            return self._label_encoders_[name].transform(feature)

        self._label_encoders_[name] = LabelEncoder()
        return self._label_encoders_[name].fit_transform(feature)

    def decode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """解码经类别编码器编码的序列"""
        if name not in self._label_encoders_:
            raise ValueError(f"不存在{name}特征编码器，无法进行解码")

        return self._label_encoders_[name].inverse_transform(feature)

    def train_test_split(
        self,
        data: pd.DataFrame,
        features: List[str],
        group_id: str | None = None,
        cuts=(0.7, 0.2),
    ) -> Tuple[pd.DataFrame | pd.Series]:
        """对时间序列进行train, valid和test子集划分。

        与sklearn的train_test_split不同，此方法会根据时间进行划分，而不是随机划分。

        请事前对data进行排序。如果group_id为None，则直接对data进行划分；否则，先按group_id进行分组，
        再在组内进行划分，最后合并成与data同样shape的DataFrame

        Args:
            data: 时间序列数据，应包含features中的所有列，以及名为target的列
            group_id: 根据此列进行分组，在组内划分子集
            features: 在data中，哪些列是特征列
            cuts: 训练集、验证集、测试集的比例

        Returns:
            返回X_train, X_valid, X_test, y_train, y_valid, y_test
        """

        def cut_time_series(group, cuts):
            itrain = int(len(group) * cuts[0])
            ival = itrain + int(len(group) * cuts[1])

            return (group.iloc[:itrain], group.iloc[itrain:ival], group.iloc[ival:])

        if group_id is None:
            train, valid, test = cut_time_series(data, cuts)
            return (
                train[features],
                valid[features],
                test[features],
                train["target"],
                valid["target"],
                test["target"],
            )

        # 按group_id进行分组，在组内划分，最后合并成与data同样shape的DataFrame
        train, valid, test = [], [], []
        for item in data.groupby(group_id).apply(cut_time_series, cuts=cuts):
            train.append(item[0])
            valid.append(item[1])
            test.append(item[2])

        train = pd.concat(train)
        valid = pd.concat(valid)
        test = pd.concat(test)

        return (
            train[features],
            valid[features],
            test[features],
            train["target"],
            valid["target"],
            test["target"],
        )

    def set_train_data(self, barss: pd.DataFrame, **kwargs):
        pass

    def train(
        self,
        train_data: lgb.Dataset,
        valid_data: lgb.Dataset,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
    ):
        if not "objective" in train_params:
            raise ValueError(
                "请提供objective参数: regression, binary, or multi-class classification."
            )

        params = {"random_state": 42}

        objective = train_params["objective"]
        if objective == "regression" and "metric" not in train_params:
            train_params["metric"] = "l2"

        if objective == "binary" and "metric" not in train_params:
            train_params["metric"] = "binary_logloss"

        if objective == "multiclass" and "metric" not in train_params:
            train_params["metric"] = "multi_logloss"

        if "feval" in train_params:
            feval = train_params["feval"]
            del train_params["feval"]
        else:
            feval = None

        params.update(train_params)
        evals_result = {}

        if self._model_ is not None:
            logger.warning("Model already trained, retraining...")

        self._model_ = lgb.train(
            params,
            train_data,
            num_boost_round=epochs,
            valid_sets=[valid_data],
            feval=feval,
            categorical_feature=list(self._label_encoders_.keys()),
            callbacks=[
                lgb.early_stopping(early_stop_round),
                lgb.record_evaluation(evals_result),
            ],
        )

        return self.model

    def predict(self, X: ArrayLike) -> ndarray:
        assert self._model_ is not None, "Model not trained"
        return self._model_.predict(X)

    def calc_returns(
        self, X_test, y_test, data: pd.DataFrame
    ) -> Tuple[float, pd.DataFrame]:
        """计算预测收益和对应的实际收益率。在eval_model方法中需要这些数据。

            子类一般应该重写此方法。
        Args:
            X_test: 测试集特征
            y_test: 对应于X_test的直值
            data: 原始数据，train_data, X_test, y_test等数据应该直接来源于此数据集，且索引没有任
            何修改。
        Returns:
            返回绝对平均百分比误差(mape)和包含了 true_close, pred_close, true_returns,
            pred_returns, asset，并以X_test的索引为索引的DataFrame
        """
        raise NotImplementedError("子类必须实现此方法")

    def eval_model(
        self,
        X_test,
        y_true,
        data: pd.DataFrame,
        rng=[0.02, 0.05],
        trace: int = 0,
        filter_quantiles: Tuple = (0.05, 0.95),
    ):
        """对模型进行评估

        这里的评估方法更适用于量化。它的原理是，只检验我们感兴趣的信号，看它们的预测结果的准确率，而忽略
        大多数噪声。

        Args:
            X_test: 测试集特征
            y_true: 对应于X_test的直值
            data: 原始数据，train_data, X_test, y_true等数据应该直接来源于此数据集，且索引没有任何修改。
            rng: 关注的预测收益范围，比如[0.02, 0.05]表示我们关注预测收益在2%到5%的范围内。
            show_trace: 是否显示预测结果和真实值的散点图
            filter_quantiles: 过滤掉离群值的范围，比如(0.05,0.95)表示过滤掉前5%和后5%的离群值
        """
        mape, df = self.calc_returns(X_test, y_true, data)
        df["close"] = data["close"].loc[X_test.index]

        # 过滤掉离群值。在训练中，它们的数据较少
        ql, qh = filter_quantiles
        low, high = df["true_close"].quantile(ql), df["true_close"].quantile(qh)
        logger.info("真值不在[%s,%s]区间的个股已被过滤", round(low, 2), round(high, 2))

        valid = df.query(f"(true_close <= {high}) & (true_close >= {low})")

        concerned = valid.query(f"(pred_returns>={rng[0]})&(pred_returns<={rng[1]})")

        plt.scatter(concerned["pred_close"], concerned["true_close"], s=1)

        # qq-plot参考线
        xmin, xmax = min(concerned["pred_close"]), max(concerned["pred_close"])
        plt.plot([xmin, xmax], [xmin, xmax], "r-")

        plt.xlabel("pred close")
        plt.ylabel("actual close")
        plt.title(
            f"Actual P&L: {concerned['true_returns'].mean():.2%}, mape = {mape:.2%}, Pred Ret: {rng[0]:.1%}-{rng[1]:.1%}, "
        )

        if trace == 0:
            return

        if trace == -1:
            trace = len(concerned)

        traces = min(trace, len(concerned))
        row = int(np.sqrt(traces))
        col = traces // row
        if row * col < traces:
            row += 1

        symbols = concerned.index.get_level_values("asset").unique()[:traces]
        _, axes = plt.subplots(row, col, figsize=(col * 4, row * 4))
        axes = axes.flatten()

        for i, symbol in enumerate(symbols):
            close = df.xs(symbol, level="asset")["close"]
            x = list(close.index.strftime("%m/%d"))
            axes[i].plot(x, close, label="close")

            # 每一个pred_close,实际上对应的是次日日期
            pred_close = df.xs(symbol, level="asset")["pred_close"]
            axes[i].plot(x[1:], pred_close[:-1], label="pred_close")

            locator = WeekdayLocator()
            axes[i].xaxis.set_major_locator(locator)

            # mark signals
            signal_dates = concerned.xs(symbol, level="asset").index
            x = list(signal_dates.strftime("%m/%d"))
            y = close.loc[signal_dates]
            axes[i].scatter(x, y, marker="^", color="red")
            axes[i].set_title(symbol)
            axes[i].legend()

        plt.tight_layout()
        return concerned
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          scaleImg="0.5"
          :enter='{ scale: 1}'
          :click-10='{ scale: 0}'>


```python{all|33-51|50|48|15-29|21-28|29|51-75|76-85}{at: 1,maxHeight: 500}
class MARegressionModel(BaseLGBModel):
    def __init__(self):
        super().__init__()
        self._cache_data_ = None
        self._features_ = []

    @property
    def data(self):
        return self._cache_data_

    @property
    def features(self):
        return self._features_

    def train(self, epochs: int = 100):
        params = {
            "metric": "mape",
            "objective": "regression"
        }

        X_train, X_val, X_test, y_train, y_val, y_test = self.train_test_split(
            self.data, self.features
        )

        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val)
        super().train(train_data, val_data, epochs, train_params=params)

        self.eval_model(X_test, y_test, self.data, trace=6)

    def set_train_data(self, barss: pd.DataFrame):
        features = [
            (
                barss["close"]
                .unstack()
                .ffill()
                .rolling(win)
                .mean()
                .stack()
                .rename(f"ma{win}")
            )
            for win in (5, 10, 20)
        ]

        features = pd.concat(features, axis=1)
        self._features_ = features.columns.tolist()
        features["target"] = barss["close"].unstack().shift(-1).stack()
        features["close"] = barss["close"]
        self._cache_data_ = features.dropna(subset=["target"])

    def calc_returns(
        self, X_test, y_test, data: pd.DataFrame
    ) -> Tuple[float, pd.DataFrame]:
        y_pred = self.predict(X_test)
        index = X_test.index

        mape = mean_absolute_percentage_error(y_test, y_pred)

        df = data.loc[index]
        prev = df["close"]

        pred_returns = y_pred / prev - 1
        true_returns = y_test / prev - 1

        return mape, pd.DataFrame(
            {
                "true_close": y_test,
                "pred_close": y_pred,
                "true_returns": true_returns,
                "pred_returns": pred_returns,
            },
            index=index,
        )


np.random.seed(78)
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
universe = 2000

barss = load_bars(start, end, universe)

mar = MARegressionModel()
mar.set_train_data(barss)
mar.train()
```

</NoteCell>

<NoteCell class='abs mt-10 w-full'
          scaleImg="0.5"
          :enter='{ scale: 0}'
          :click-10-11='{ scale: 1}'>

```python
lgb.plot_tree(mar.model, tree_index=0, figsize=(15,10))
```
</NoteCell>

<!--

我们刚刚介绍的base model还仅仅只是一个框架，其中有一些接口必须留到子类中去实现，比方说，如何增加训练数据，如何提取特征，如何计算收益。

现在，我们现在就来介绍， 现在我们就来看，如何定义子类，子类与基类如何相互配合。

<run></run>


[click]

很显然， 当我们使用不同的特征进行训练，我们就会得到不同的模型，所以实际上是特征定了模型，因此这件事情必须在子类当中完成。

在这里，我们提取了5，10，20日均线，然后把次日的收盘价作为目标。


[click]

我们还添加了当日的收盘价，最后，把数据集缓存起来。

我们缓存的数据有两个用途。一是打算传递给train方法，在那里，它将调用train_test_split方法，对数据进行划分和训练。二是将传递给eval_models方法。为了绘制走势及交易信号图，我们需要收盘价数据，这是第50行代码的由来。

但是，在这个模型中，我们并不希望把close作为特征。


[click]

所以，我们通过第48行，事先把特征列名保存起来。这个保存的特征名，也可以在我们保存模型时，一起存储。


[click]

我们还要改写train方法。在此时，模型是分类还是回归已经确定，而且我们也知道了什么样的评估指标合适，因此，这部分就可以写死在train方法中。

[click]

这是将我们前面提取的特征，转换为可用以训练的输入。然后调用父类的方法进行训练。

[click]

最后，在训练完成时，自动调用eval_model方法。

[click]

另一个需要自己实现的方法是calc_returns。在这个模型中，我们预测出来的结果是股价，因此，我们计算相对收益误差时，需要调用mean_absolute_percentage_error这个方法。

[click]

最后，我们加载原始数据，实例化模型，通过set_train_data向模型提供数据，最后调用train方法进行训练。

[click]

现在我们来看一下结果。它的误差比使用收盘价序列的要大一些。但是当我们投资于预测能上涨超过2%的股票时，意外地获得了正收益。

这个结果并非完全偶然。首先，我们注意到，很多时候，均线价格是一种价格锚点，收盘价收在均线价格附近是很常见的。

其次，如果股价跌破均线，那么出于回归的原因，当股价真的收在均线上时，此时就获得了正的收益。

这是我们介绍这个子类的原因，并不完全是出于技术上的考虑。我也是希望通过这个例子，让大家理解应该如何把交易原理转化为机器学习问题。


[click]

<run></run>

最后，我们也展示一下0号决策树，看看主宰模型的特征是什么。

从结果看，最重的要特征是5日均线。这也验证了我们的说法，这一版的模型，预测的是均值回归。
-->
