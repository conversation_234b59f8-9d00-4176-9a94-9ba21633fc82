---
clicks: 2
---

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-5='{ scale: 0}'>

```python{all|3-10|all}{at:1}
import lightgbm as lgb
def train_test_split(data, feature_cols, cuts = (0.7,0.2)):
    train, valid, test = [], [], []
    def cut_time_series(group, cuts):
        itrain = int(len(group) * cuts[0])
        ival = itrain + int(len(group) * cuts[1])

        return (group.iloc[:itrain], group.iloc[itrain: ival], group.iloc[ival:])

    for item in data.groupby("asset").apply(cut_time_series, cuts=cuts).values:
        train.append(item[0])
        valid.append(item[1])
        test.append(item[2])

    df_train = pd.concat(train)
    df_valid = pd.concat(valid)
    df_test = pd.concat(test)

    return (df_train[feature_cols], df_valid[feature_cols], df_test[feature_cols], 
            df_train["target"], df_valid["target"], df_test["target"])

data=features
data["ret"] = barss["close"].unstack().pct_change().stack()
data["target"] = barss["close"].unstack().shift(-1).stack()
data = data.dropna(subset=["target", "ret"])
data.reset_index(inplace=True)

(X_train, X_val, X_test, 
y_train, y_val, y_test) = train_test_split(data, feature_cols)

train_data = lgb.Dataset(X_train, label=y_train)
valid_data = lgb.Dataset(X_val, label=y_val)
```
</NoteCell>



<!--

我们已经有了特征数据。现在，需要将其进行划分，转换为lightgbm支持的格式。这是上一课已经讲过的内容了。

在这里，我们介绍下train_test_split函数。

这个函数有两个主要功能，第一，它会将数据集划分为训练集、验证集和测试集。在之前的课程中，我们只使用了训练集和验证集。为什么要有测试集呢？

原因是，验证集也参与了调优，它可能会引导过拟合的出现。所以，如果我们在验证集之外，还有一个测试集，是在训练时完全没见过的，并且模型在这个数据集上的表现也很好，那么，我们就可以相信，这个模型是可以泛化的。

这一点很重要。在机器学习中，我们很容易由于编程的错误，引入未来数据。如果使用了未来数据，那么模型在验证集上很容易表现很好。测试集的存在，保证了一旦出现这种错误，我们就能发现它。因为未来数据存在时，模型是不会有泛化能力的。

第二，它实现了组内的时间序列划分。


[click]

这里也没太多技巧。主要提一下，groupby.apply允许函数返回元组。


[click]

最后，我们把train, valid, test的信息合并成dataframe就可以了。
-->
