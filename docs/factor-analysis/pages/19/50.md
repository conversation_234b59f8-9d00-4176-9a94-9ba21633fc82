---
clicks: 6
---

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'>

```python
import datetime
import logging
from numpy import ndarray
from numpy.typing import ArrayLike, NDArray
import os
import pickle
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import mean_absolute_percentage_error
from numpy.lib.stride_tricks import as_strided
import lightgbm as lgb
from matplotlib.dates import WeekdayLocator


logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)
mlogger = logging.getLogger("matplotlib")
# supress matplotlib info level logging, which is anoying
mlogger.setLevel(logging.WARNING)


class BaseLGBModel:
    def __init__(self):
        self._model_ = None
        self._name_: str | None = None
        self._desc_: str | None = None

        # for encode/decode categorical features
        self._label_encoders_ = {}

    @property
    def model(self):
        return self._model_

    def save(self, path: str, auto_rename: bool = True, name: str | None = None):
        """将模型保存到指定路径。

        如name未指定，将使用模型名作为文件名。如果同名文件已存在，将自动重命名为当前时间戳。文件名会自动加pkl后缀
        Args:
            path: 保存路径
            auto_rename: 如果同名文件已存在，是否自动重命名
            name: 保存文件名
        """
        if self._model_ is None:
            raise ValueError("请先调用fit方法训练模型")

        if not os.path.exists(os.path.expanduser(path)):
            raise ValueError("保存路径不存在")

        name = name or self._name_
        if name is None:
            raise ValueError("请指定保存文件名")

        file = os.path.join(path, f"{name}.pkl")
        if os.path.exists(file):
            if auto_rename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"{name}_{timestamp}.pkl"
                file = os.path.join(path, name)
                assert not os.path.exists(file)
                logger.warning("保存路径已存在同名文件，已自动重命名为%s", name)
            else:
                raise ValueError("保存路径已存在同名文件，请指定指定文件名")

        with open(file, "wb") as f:
            pickle.dump(
                {
                    "model": self._model_,
                    "name": self._name_,
                    "desc": self._desc_,
                    "save_time": datetime.datetime.now(),
                },
                f,
            )

    def load(self, file_path: str):
        with open(file_path, "rb") as f:
            data = pickle.load(f)
            self._model_ = data["model"]
            self._name_ = data["name"]
            self._desc_ = data["desc"]
            logger.info("加载模型%s成功。", self._name_)

    def rolling_time_series(self, ts: NDArray, win: int) -> NDArray:
        """生成rolling time series

        Args:
            ts: 一维时间序列
            win: 窗口大小
        Returns:
            返回shape为（len(ts) - win + 1, win）的二维numpy数组
        """
        stride = ts.strides
        shape = (len(ts) - win + 1, win)
        strides = stride + stride
        return as_strided(ts, shape, strides)

    def encode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """将类别特征编码。

        类别特征可能需要编码解码。此时需要label_encoders属性保存编码器。此方法会自动保存编码器到label_encoders属性中。
        """
        if name in self._label_encoders_:
            logger.info("已经存在%s特征编码器，直接复用")
            return self._label_encoders_[name].transform(feature)

        self._label_encoders_[name] = LabelEncoder()
        return self._label_encoders_[name].fit_transform(feature)

    def decode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """解码经类别编码器编码的序列"""
        if name not in self._label_encoders_:
            raise ValueError(f"不存在{name}特征编码器，无法进行解码")

        return self._label_encoders_[name].inverse_transform(feature)

    def train_test_split(
        self,
        data: pd.DataFrame,
        features: List[str],
        group_id: str | None = None,
        cuts=(0.7, 0.2),
    ) -> Tuple[pd.DataFrame | pd.Series]:
        """对时间序列进行train, valid和test子集划分。

        与sklearn的train_test_split不同，此方法会根据时间进行划分，而不是随机划分。

        请事前对data进行排序。如果group_id为None，则直接对data进行划分；否则，先按group_id进行分组，
        再在组内进行划分，最后合并成与data同样shape的DataFrame

        Args:
            data: 时间序列数据，应包含features中的所有列，以及名为target的列
            group_id: 根据此列进行分组，在组内划分子集
            features: 在data中，哪些列是特征列
            cuts: 训练集、验证集、测试集的比例

        Returns:
            返回X_train, X_valid, X_test, y_train, y_valid, y_test
        """

        def cut_time_series(group, cuts):
            itrain = int(len(group) * cuts[0])
            ival = itrain + int(len(group) * cuts[1])

            return (group.iloc[:itrain], group.iloc[itrain:ival], group.iloc[ival:])

        if group_id is None:
            train, valid, test = cut_time_series(data, cuts)
            return (
                train[features],
                valid[features],
                test[features],
                train["target"],
                valid["target"],
                test["target"],
            )

        # 按group_id进行分组，在组内划分，最后合并成与data同样shape的DataFrame
        train, valid, test = [], [], []
        for item in data.groupby(group_id).apply(cut_time_series, cuts=cuts):
            train.append(item[0])
            valid.append(item[1])
            test.append(item[2])

        train = pd.concat(train)
        valid = pd.concat(valid)
        test = pd.concat(test)

        return (
            train[features],
            valid[features],
            test[features],
            train["target"],
            valid["target"],
            test["target"],
        )

    def set_train_data(self, barss: pd.DataFrame, **kwargs):
        pass

    def train(
        self,
        train_data: lgb.Dataset,
        valid_data: lgb.Dataset,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
    ):
        if not "objective" in train_params:
            raise ValueError(
                "请提供objective参数: regression, binary, or multi-class classification."
            )

        params = {"random_state": 42}

        objective = train_params["objective"]
        if objective == "regression" and "metric" not in train_params:
            train_params["metric"] = "l2"

        if objective == "binary" and "metric" not in train_params:
            train_params["metric"] = "binary_logloss"

        if objective == "multiclass" and "metric" not in train_params:
            train_params["metric"] = "multi_logloss"

        if "feval" in train_params:
            feval = train_params["feval"]
            del train_params["feval"]
        else:
            feval = None

        params.update(train_params)
        evals_result = {}

        if self._model_ is not None:
            logger.warning("Model already trained, retraining...")

        self._model_ = lgb.train(
            params,
            train_data,
            num_boost_round=epochs,
            valid_sets=[valid_data],
            feval=feval,
            categorical_feature=list(self._label_encoders_.keys()),
            callbacks=[
                lgb.early_stopping(early_stop_round),
                lgb.record_evaluation(evals_result),
            ],
        )

        return self.model

    def predict(self, X: ArrayLike) -> ndarray:
        assert self._model_ is not None, "Model not trained"
        return self._model_.predict(X)

    def calc_returns(
        self, X_test, y_test, data: pd.DataFrame
    ) -> Tuple[float, pd.DataFrame]:
        """计算预测收益和对应的实际收益率。在eval_model方法中需要这些数据。

            子类一般应该重写此方法。
        Args:
            X_test: 测试集特征
            y_test: 对应于X_test的直值
            data: 原始数据，train_data, X_test, y_test等数据应该直接来源于此数据集，且索引没有任
            何修改。
        Returns:
            返回绝对平均百分比误差(mape)和包含了 true_close, pred_close, true_returns,
            pred_returns, asset，并以X_test的索引为索引的DataFrame
        """
        raise NotImplementedError("子类必须实现此方法")

    def eval_model(
        self,
        X_test,
        y_true,
        data: pd.DataFrame,
        rng=[0.02, 0.05],
        trace: int = 0,
        filter_quantiles: Tuple = (0.05, 0.95),
    ):
        """对模型进行评估

        这里的评估方法更适用于量化。它的原理是，只检验我们感兴趣的信号，看它们的预测结果的准确率，而忽略
        大多数噪声。

        Args:
            X_test: 测试集特征
            y_true: 对应于X_test的直值
            data: 原始数据，train_data, X_test, y_true等数据应该直接来源于此数据集，且索引没有任何修改。
            rng: 关注的预测收益范围，比如[0.02, 0.05]表示我们关注预测收益在2%到5%的范围内。
            show_trace: 是否显示预测结果和真实值的散点图
            filter_quantiles: 过滤掉离群值的范围，比如(0.05,0.95)表示过滤掉前5%和后5%的离群值
        """
        mape, df = self.calc_returns(X_test, y_true, data)
        df["close"] = data["close"].loc[X_test.index]

        # 过滤掉离群值。在训练中，它们的数据较少
        ql, qh = filter_quantiles
        low, high = df["true_close"].quantile(ql), df["true_close"].quantile(qh)
        logger.info("真值不在[%s,%s]区间的个股已被过滤", round(low, 2), round(high, 2))

        valid = df.query(f"(true_close <= {high}) & (true_close >= {low})")

        concerned = valid.query(f"(pred_returns>={rng[0]})&(pred_returns<={rng[1]})")

        plt.scatter(concerned["pred_close"], concerned["true_close"], s=1)

        # qq-plot参考线
        xmin, xmax = min(concerned["pred_close"]), max(concerned["pred_close"])
        plt.plot([xmin, xmax], [xmin, xmax], "r-")

        plt.xlabel("pred close")
        plt.ylabel("actual close")
        plt.title(
            f"Actual P&L: {concerned['true_returns'].mean():.2%}, mape = {mape:.2%}, Pred Ret: {rng[0]:.1%}-{rng[1]:.1%}, "
        )

        if trace == 0:
            return

        if trace == -1:
            trace = len(concerned)

        traces = min(trace, len(concerned))
        row = int(np.sqrt(traces))
        col = traces // row
        if row * col < traces:
            row += 1

        symbols = concerned.index.get_level_values("asset").unique()[:traces]
        _, axes = plt.subplots(row, col, figsize=(col * 4, row * 4))
        axes = axes.flatten()

        for i, symbol in enumerate(symbols):
            close = df.xs(symbol, level="asset")["close"]
            x = list(close.index.strftime("%m/%d"))
            axes[i].plot(x, close, label="close", linewidth=2)

            # 每一个pred_close,实际上对应的是次日日期
            pred_close = df.xs(symbol, level="asset")["pred_close"]
            axes[i].plot(x[1:], pred_close[:-1], label="pred_close", linewidth=1)

            locator = WeekdayLocator()
            axes[i].xaxis.set_major_locator(locator)

            # mark signals
            signal_dates = concerned.xs(symbol, level="asset").index
            x = list(signal_dates.strftime("%m/%d"))
            y = close.loc[signal_dates]
            axes[i].scatter(x, y, marker="^", color="red")
            axes[i].set_title(symbol)
            axes[i].legend()

        plt.tight_layout()
        return concerned
```
</NoteCell>

<NoteCell scaleImg="70%"
          class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-6='{ scale: 0}'>

```python{all|53-57|90|128|135-136|all}{maxHeight: 500}
from sklearn.metrics import mean_absolute_error
from numpy.polynomial.polynomial import polyfit, polyval

class PriceSeerV2(BaseLGBModel):
    def __init__(self):
        super().__init__()
        self._cache_data_ = None
        self._features_ = []

    @property
    def data(self):
        return self._cache_data_

    @property
    def features(self):
        return self._features_
    
    def extract_feature_ma(self, barss, wins=None):
        wins = wins or [5, 10, 20]
        df = pd.concat(
            [
                (
                    barss["close"]
                    .unstack()
                    .rolling(win)
                    .mean()
                    .stack()
                    .rename(f"ma{win}")
                )
                for win in wins
            ],
            axis=1,
        )

        df.columns = [f"ma{win}" for win in wins]
        return df
    
    def rolling_polyfit(
        self, group, win: int, icol, columns: List[str]
    ) -> pd.DataFrame:
        index = group.index.get_level_values(0)

        ts = group.iloc[:, icol].to_numpy()

        if len(ts) < win:
            features = np.full((len(ts), 2), np.nan)
            return pd.DataFrame(features, columns=columns, index=index)

        transformed = self.rolling_time_series(ts, win)
        coeff = polyfit(np.arange(win), transformed.T, deg=2)

        transformed_hat = polyval(np.arange(win + 1), coeff)
        pred = transformed_hat[:, -1]
        ret = pred / ts[win - 1 :] - 1

        ts_hat = transformed_hat[:, -2]
        errors = ts[win - 1 :] - ts_hat
        features = np.stack([ret, errors], axis=1)

        pad_len = win - 1

        padded = np.pad(
            features, ((pad_len, 0), (0, 0)), mode="constant", constant_values=np.nan
        )

        return pd.DataFrame(padded, index=index, columns=columns)

    def extract_feature_poly(self, ma_features, wins=None):
        wins = wins or [5, 10, 20]
        poly_features = []

        for i, win in enumerate(wins):
            columns = [f"poly_ret_{win}", f"poly_error_{win}"]
            poly_feature = ma_features.groupby(level="asset").apply(
                self.rolling_polyfit, win=win, icol=i, columns=columns
            )
            poly_features.append(poly_feature)

        # make sure the index is date, asset
        return pd.concat(poly_features, axis=1).swaplevel()

    def set_train_data(
        self, barss: pd.DataFrame, ma_wins=(5, 10, 20), poly_wins=(5, 10, 20)
    ):
        mas = self.extract_feature_ma(barss, ma_wins)
        features = self.extract_feature_poly(mas, poly_wins)

        self._features_ = features.columns.tolist()

        features["target"] = barss["close"].unstack().pct_change().stack()
        features["close"] = barss["close"]
        features["asset"] = features.index.get_level_values("asset")

        self._cache_data_ = features.dropna(subset=["target"])

    def train(
        self,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
        **eval_params,
    ):
        params = {"objective": "regression", "metric": "mape"}
        params.update(train_params or {})

        X_train, X_val, X_test, y_train, y_val, y_test = self.train_test_split(
            self.data, self.features
        )

        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val)
        super().train(
            train_data,
            val_data,
            epochs,
            early_stop_round=early_stop_round,
            train_params=params,
        )

        self.eval_model(X_test, y_test, self.data, **eval_params)

    def calc_returns(
        self, X_test, y_test, data: pd.DataFrame
    ) -> Tuple[float, pd.DataFrame]:
        y_pred = self.predict(X_test)
        index = X_test.index

        mape = mean_absolute_error(y_test, y_pred)

        df = data.loc[index]
        prev = df["close"]

        return mape, pd.DataFrame(
            {
                "true_close": (1 + y_test) * prev,
                "pred_close": (1 + y_pred) * prev,
                "true_returns": y_test,
                "pred_returns": y_pred,
            },
            index=index,
        )

np.random.seed(78)
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
universe = 2000

barss = load_bars(start, end, universe)

seer = PriceSeerV2()
seer.set_train_data(barss)
seer.train(100, trace=6)
```

</NoteCell>


<NoteCell class='abs mt-10 w-full'
          scaleImg="70%"
          :enter='{ scale: 0}'
          :click-6='{ scale: 1}'>

```python
lgb.plot_tree(seer.model, tree_index=0, figsize=(30,20))
```
</NoteCell>



<!--
<run></run>

这里我们只讲几个重点。

[click]

注意看，这里我们提取的特征变成了多项式预言的收益，而不是它预言的绝对价格。

[click]

预测目标也换成了实际收益率。

[click]

这个地方也要进行修改。当我们对收益求mae时，实际上得到的就是上一版的mape

[click]

然后我们根据收益，计算出绝对价格，主要是后面的模型评估时，需要用到这两项。

[click]

现在，我们来看一下运行结果。

我们先看收益。

如果我们根据这个模型，买入预测涨跌幅在2%到5%之间的个股，它的误差是1.12%，也就是说，这个误差是小于我们买入时的预期区间的。

在这种情况下，它获得了平均3%的收益。这是一个非常好的结果，它意味着在不计算手续费和滑点的情况下，按一年可以交易250次计算，最终将获得1千多倍的收益。这个已经超过涨停板指数了。

看上去这个结果不太可能，但实际上还有更高的。如果我们把买入区间设置在预测涨5%以上，那么我们得到的实际收益大概在8%左右，不过，此时每天可买入的标的就变少了。

我统计了一下，我们使用的X_test，大概包含了33个交易日。其中实际涨幅大于2%的，每天大概是160支，预测出来涨幅大于2%的，是96支左右。所以，这个模型是把其中的一半找出来了。

然后我们再来解读下这张QQ图。

红线是表明预测价格等于实际价格。在红线的上方，表明实际的价格还要高于预测价格。在这张图中，多数点仍然落在红线下方，但比之前的模型已经强了不少。这说明模型的系统偏差降低了。

我们随机抽取一部分买入样本看看。蓝色的线是实际价格，橙线是预测价格。红色标记是买入点。在买入点之后，如果蓝色的线是上涨的，那么实际收益就是正的，反之就是负的。

我们把出现在最右边的点先不看，因为它们的涨跌还不清楚。从中间的点来看，模型的预测能力确实很强。


[click]

<run></run>

不过，我们还要检查一下决策树的情况，只有所有的结果都能相互印证，这样才能形成完整的证据链，表明这个模型是正确的。

我们看到了什么？现在，拟合误差几乎主宰了决策。在树的尽头，叶子结点上，则是对应的预测结果。它的大意是说，如果5日均线很好地拟合了二次曲线，并且二次项为正的多少，是加速上涨，一次项是多少，这种情况下，均线就有可能涨多少。

这正是我们一开始提出的关于策略原理的假设。我们要的正是这样一个分段函数，我们知道所有的规律，只是不知道，在大数据统计下，在某个区间，究竟是5日均线支撑起作用，还是10日均线支撑起作用。

lightgbm帮我们完美地实现了这个统计。

-->
