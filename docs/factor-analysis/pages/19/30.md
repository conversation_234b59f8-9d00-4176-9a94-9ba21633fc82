---
clicks: 8
---

<div v-motion class='abs mt-10 left-32% w-70%'
          :enter='{ opacity: 1}'
          :click-10='{ opacity: 0}'>

```python{all|8-9,18-59|61-80|82-127|128-129|131-176|178-180|187-258|196,182-185}{maxHeight: '500px',at:1}
mlogger = logging.getLogger("matplotlib")
# supress matplotlib info level logging, which is anoying
mlogger.setLevel(logging.WARNING)

class BaseLGBModel:
    def __init__(self):
        self._model_ = None
        self._name_: str | None = None
        self._desc_: str | None = None

        # for encode/decode categorical features
        self._label_encoders_ = {}

    @property
    def model(self):
        return self._model_

    def save(self, path: str, 
                   auto_rename: bool = True, 
                   name: str | None = None):
        if self._model_ is None:
            raise ValueError("请先调用fit方法训练模型")

        if not os.path.exists(os.path.expanduser(path)):
            raise ValueError("保存路径不存在")

        name = name or self._name_
        if name is None:
            raise ValueError("请指定保存文件名")

        file = os.path.join(path, f"{name}.pkl")
        if os.path.exists(file):
            if auto_rename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"{name}_{timestamp}.pkl"
                file = os.path.join(path, name)
                assert not os.path.exists(file)
                logger.warning("保存路径已存在同名文件，已自动重命名为%s", name)
            else:
                raise ValueError("保存路径已存在同名文件，请指定指定文件名")

        with open(file, "wb") as f:
            pickle.dump(
                {
                    "model": self._model_,
                    "name": self._name_,
                    "desc": self._desc_,
                    "save_time": datetime.datetime.now(),
                },
                f,
            )

    def load(self, file_path: str):
        with open(file_path, "rb") as f:
            data = pickle.load(f)
            self._model_ = data["model"]
            self._name_ = data["name"]
            self._desc_ = data["desc"]
            logger.info("加载模型%s成功。", self._name_)

    def rolling_time_series(self, ts: NDArray, win: int) -> NDArray:
        stride = ts.strides
        shape = (len(ts) - win + 1, win)
        strides = stride + stride
        return as_strided(ts, shape, strides)

    def encode_feature(self, name: str, 
                             feature: pd.Series):
        if name in self._label_encoders_:
            logger.info("已经存在%s特征编码器，直接复用")
            return self._label_encoders_[name].transform(feature)

        self._label_encoders_[name] = LabelEncoder()
        return self._label_encoders_[name].fit_transform(feature)

    def decode_feature(self, name: str, feature: pd.Series):
        if name not in self._label_encoders_:
            raise ValueError(f"不存在{name}特征编码器，无法进行解码")

        return self._label_encoders_[name].inverse_transform(feature)

    def train_test_split(
        self,
        data: pd.DataFrame,
        features: List[str],
        group_id: str | None = None,
        cuts=(0.7, 0.2),
    ) -> Tuple[pd.DataFrame | pd.Series]:
        def cut_time_series(group, cuts):
            itrain = int(len(group) * cuts[0])
            ival = itrain + int(len(group) * cuts[1])

            return (group.iloc[:itrain], 
                    group.iloc[itrain:ival], 
                    group.iloc[ival:]
                    )

        if group_id is None:
            train, valid, test = cut_time_series(data, cuts)
            return (
                train[features],
                valid[features],
                test[features],
                train["target"],
                valid["target"],
                test["target"],
            )

        train, valid, test = [], [], []
        for item in data.groupby(group_id).apply(cut_time_series, cuts=cuts):
            train.append(item[0])
            valid.append(item[1])
            test.append(item[2])

        train = pd.concat(train)
        valid = pd.concat(valid)
        test = pd.concat(test)

        return (
            train[features],
            valid[features],
            test[features],
            train["target"],
            valid["target"],
            test["target"],
        )

    def set_train_data(self, barss: pd.DataFrame, **kwargs):
        pass

    def train(
        self,
        train_data: lgb.Dataset,
        valid_data: lgb.Dataset,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
    ):
        params = {"random_state": 42}

        objective = train_params["objective"]
        if objective == "regression" and "metric" not in train_params:
            train_params["metric"] = "l2"

        if objective == "binary" and "metric" not in train_params:
            train_params["metric"] = "binary_logloss"

        if objective == "multiclass" and "metric" not in train_params:
            train_params["metric"] = "multi_logloss"

        if "feval" in train_params:
            feval = train_params["feval"]
            del train_params["feval"]
        else:
            feval = None

        params.update(train_params)
        evals_result = {}

        if self._model_ is not None:
            logger.warning("Model already trained, retraining...")

        self._model_ = lgb.train(
            params,
            train_data,
            num_boost_round=epochs,
            valid_sets=[valid_data],
            feval=feval,
            categorical_feature=list(self._label_encoders_.keys()),
            callbacks=[
                lgb.early_stopping(early_stop_round),
                lgb.record_evaluation(evals_result),
            ],
        )

        return self.model

    def predict(self, X: ArrayLike) -> ndarray:
        assert self._model_ is not None, "Model not trained"
        return self._model_.predict(X)

    def calc_returns(
        self, X_test, y_test, data: pd.DataFrame
    ) -> Tuple[float, pd.DataFrame]:
        raise NotImplementedError("子类必须实现此方法")

    def eval_model(
        self,
        X_test,
        y_true,
        data: pd.DataFrame,
        rng=[0.02, 0.05],
        trace: int = 0,
        filter_quantiles: Tuple = (0.05, 0.95),
    ):
        mape, df = self.calc_returns(X_test, y_true, data)
        df["close"] = data["close"].loc[X_test.index]

        # 过滤掉离群值。在训练中，它们的数据较少
        ql, qh = filter_quantiles
        low, high = df["true_close"].quantile(ql), df["true_close"].quantile(qh)
        logger.info("真值不在[%s,%s]区间的个股已被过滤", round(low, 2), round(high, 2))

        valid = df.query(f"(true_close <= {high}) & (true_close >= {low})")

        concerned = valid.query(f"(pred_returns>={rng[0]})&(pred_returns<={rng[1]})")

        plt.scatter(concerned["pred_close"], concerned["true_close"], s=1)

        # qq-plot参考线
        xmin, xmax = min(concerned["pred_close"]), max(concerned["pred_close"])
        plt.plot([xmin, xmax], [xmin, xmax], "r-")

        plt.xlabel("pred close")
        plt.ylabel("actual close")
        plt.title(
            f"Actual P&L: {concerned['true_returns'].mean():.2%}," \
            f"mape = {mape:.2%}, Pred Ret: {rng[0]:.1%}-{rng[1]:.1%}"
        )

        if trace == 0:
            return

        if trace == -1:
            trace = len(concerned)

        traces = min(trace, len(concerned))
        row = int(np.sqrt(traces))
        col = traces // row
        if row * col < traces:
            row += 1

        symbols = concerned.index.get_level_values("asset").unique()[:traces]
        _, axes = plt.subplots(row, col, figsize=(col * 4, row * 4))
        axes = axes.flatten()

        for i, symbol in enumerate(symbols):
            close = df.xs(symbol, level="asset")["close"]
            x = list(close.index.strftime("%m/%d"))
            axes[i].plot(x, close, label="close")

            # 每一个pred_close,实际上对应的是次日日期
            pred_close = df.xs(symbol, level="asset")["pred_close"]
            axes[i].plot(x[1:], pred_close[:-1], label="pred_close")

            locator = WeekdayLocator()
            axes[i].xaxis.set_major_locator(locator)

            # mark signals
            signal_dates = concerned.xs(symbol, level="asset").index
            x = list(signal_dates.strftime("%m/%d"))
            y = close.loc[signal_dates]
            axes[i].scatter(x, y, marker="^", color="red")
            axes[i].set_title(symbol)
            axes[i].legend()

        plt.tight_layout()
        return concerned
```
</div>

<div class='abs w-30% left-0 mt-15' v-motion
     :enter='{opacity: 1}'
     :click-10='{opacity:0}'>

## 基本功能
### 模型的加载与保存
### 特征提取
### 训练与预测
### 模型评估
</div>

<div class='abs w-30% left-0 mt-15' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity:1}'>

<h2>基本功能</h2>
<h3 style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3)">模型的加载与保存</h3>
<h3>特征提取</h3>
<h3>训练与预测</h3>
<h3>模型评估</h3>
</div>

<div class='abs w-30% left-0 mt-15' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity:1}'>

<h2>基本功能</h2>
<h3>模型的加载与保存</h3>
<h3 style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3)">特征提取</h3>
<h3>训练与预测</h3>
<h3>模型评估</h3>
</div>

<div class='abs w-30% left-0 mt-15' v-motion
     :enter='{opacity: 0}'
     :click-3-7='{opacity:1}'>

<h2>基本功能</h2>
<h3>模型的加载与保存</h3>
<h3>特征提取</h3>
<h3 style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3)">训练与预测</h3>
<h3>模型评估</h3>
</div>

<div class='abs w-30% left-0 mt-15' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{opacity:1}'>

<h2>基本功能</h2>
<h3>模型的加载与保存</h3>
<h3>特征提取</h3>
<h3>训练与预测</h3>
<h3 style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3)">模型评估</h3>
</div>

<!--
base model主要提供了这些通用功能。


[click]

基本上是比较常规的操作。对一个模型进行多次训练是比较常见的事，所以，我们一是要在保存模型时，增加一些说明，比如训练时间、对应的数据集情况等。模型的参数不用另外保存，在lightgbm训练出来的model中，这些参数是可以获取的。

另外的功能就是自动重命名。好，这些不太重要，我们就不展开讲了。

[click]

rolling_time_series我们之前已经见过了。这里只是把它封装进来。

encode_feature和decode_feature只是很简单的封装。为什么需要进行简单封装？因为对每一个类别的特征，如果它是categorical的，我们都要给它分配单独的编码器，并且要将它通过fit训练之后的状态保存起来，这样，后续才能逆变换回去。

[click]

这里也只是把我们前面实现过的函数封装起来。

[click]

这是一个接口，需要子类来实现。它的作用是，为模型添加数据，并且实现特征提取。

[click]

train方法主要是对参数进行检查，补充一些默认参数。此外，由于增加early_stop，增加训练过程中的评估指标记录基本上是必选，所以，我们也把它封装起来。

注意我们还在这里增加了random_state的设置。因为我们打算把几个模型进行比较，所以，要确保它们的各种设置、随机状态是一致的。

[click]

这个封装主要是为了方便记忆。


[click]

这部分也是把之前的实现封装起来，实现模型评估。


[click]

稍有不同的是，后面的模型既可能是预测价格，也可能是预测收益。具体怎么计算误差和收益，需要子类来实现。 所以，我们设计了一个calc_returns的接口，每个子类，都要实现这个接口，然后它在eval_model中会被自动调用。

calc_returns需要返回一个误差项，以及一个dataframe。这个dataframe中，至少需要有预测价格、真实价格、预测收益率、真实收益率等列。

-->
