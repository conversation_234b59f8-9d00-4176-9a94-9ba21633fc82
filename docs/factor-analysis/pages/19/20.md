---
clicks: 0
---

<NoteCell layout="horizontal" class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
from numpy.lib.stride_tricks import as_strided

np.random.seed(78)
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 29)
universe = 500
barss = load_bars(start, end, universe=universe)

def rolling_time_series(ts: NDArray, win: int):
    stride = ts.strides
    shape = (len(ts) - win + 1, win)
    strides = stride + stride
    return as_strided(ts, shape, strides)
    
def rolling_close(group, win, columns):
    index = group.index
    if len(group) < win:
        features = np.full((len(group), win), np.nan)
        df = pd.DataFrame(features, columns=columns, index=index)
        return df
        
    rolled = rolling_time_series(group["close"].values, win)
    padded = np.pad(rolled, 
                    ((win-1, 0), (0,0)), 
                    mode="constant", 
                    constant_values=np.nan)
    df = pd.DataFrame(padded, columns=columns, index=index)
    return df

win = 10
feature_cols = [f"c{win-i}" for i in range(win)]

features = (barss.groupby(level="asset")
                 .apply(rolling_close, win, columns = feature_cols))
features = features.droplevel(0)
features.tail()
```
</NoteCell>

<!--

<run></run>

既然机器学习能力这么强大，多项式拟合又非常简单。那么，很自然地，我们会想，能否直接把一堆价格数据丢给lightgbm，让它自己去解方程，算拟合误差，决定要不要使用拟合曲线的预测值呢？

我们通过这个例子来探索一下，lightgbm，或者说机器学习，倒底能干什么，是怎么干的。在前面的章节里，我们主要是以分类进行举例的，所以，大家对lightgbm是如何完成分类任务的，应该比较清楚了。但是，它是如何完成回归任务的呢？

这是例子中的第一段，用来提取特征。

我们看到，它将每支个股的收盘价，按10天的窗口进行滑动，从而得到每一天的特征。
这个特征就是从当天往回取10天的收盘价。

我们看到，沿着右上到左下的对角线，这些价格是一样的。这表明我们的滑动操作是正确的。


-->
