---
clicks: 2
---

<div class='abs mt-30 ml-10' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>
<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/cycloid.gif?1'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>Adapt from Curiosa Mathematica</span>
</div>
</div>

<div class='abs mt-30 ml-150' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

<div style='width:100%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/foucault_pendulum_animated.gif?1'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>DemonDeLuxe@wiki</span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0, x:0}'
     :click-1='{opacity: 1}'
     :click-2='{x: -480}'>
<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/curve-fitting.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-50 ml-150' v-motion
     :enter='{opacity: 0}'
     :click-2='{opacity: 1}'>

$$
\hat{y} = a + bx + cx^2 + \epsilon
$$

</div>

<!--
使用物理学的理论来进行量化研究是业内非常普遍的一种现象。顶级对冲基金招募人才时，会很看重物理专业的学生。最终，他们也把最新、最高深的数学和物理知识带入了量化交易。

但我们也不要忘记，股市是一个人为的市场，是各种各样的投资者通过自己独特的交易行为，共同为资产定价。所以，在这个市场上讲什么语言，不是看哪种语言的语法更科学、更严谨，关键看哪种语言的群众基础更好。

所以，我们一方面要对新技术保持开放心态，另一方面，也要对经典的理论和大众的经验保持敬畏，要能穿透现实的迷雾，回归到事物的本原。

如果我们不考虑资产价格波动的根本原理，那么可以简单地认为，在左右资产短期价格变动的表面原因中，有两种最基本的因素在起作用。第一种是趋势，或者称为惯性，第二种是反转，或者称为回归。

我们认为量化交易能够成功，实际上是认为这两种作用力可以计算的，并且在计算出来之后的较短的一段时间里，这些作用力及其运动规律都是不变的，直到系统的状态被强有力的外界力量改变。


[click]

这一课，我们就要利用惯性原理，来从过去的股价走势，去预测未来。

这是在行情图中，非常常见的均线的走势。这两张图给了我们一个猜想：如果股价走势能够拟合成直线，或者二次曲线，那么，我们就能推导出下一刻的价格。至于这种趋势能否保持，那就要看运气，或者看统计数据了。


[click]

另一方面，当我们决定对一个时间序列进行曲线拟合时，几乎一定会得到一个结果。因此，我们也要判断它返回的误差，以决定是否能相信这个拟合的结果。

这就是我们策略的基本原理：如果通过数学计算发现价格序列出现了某种规律，我们就按这个规律来推测下一个价格。并且根据误差来决定是否相信推测的结果，然后把最终的收益交给运气。后面两项都涉及到统计分析，这里我们就使用机器学习。

-->
