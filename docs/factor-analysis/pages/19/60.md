---
clicks: 1
---

<div class="mt-10 ml-5">

## 从一个假设开始
## 机器能发现规律吗？
## 模型基类
## 最终幻想
</div>

<!--
今天的主要课程内容就到这里。现在， 请大家提出问题。在大家准备问题的同时，我为大家总结今天的课程。

这节课我们从一个假设开始，这个假设就是股价的运动在多数时间是惯性和往复运动，我们可以通过拟合均线的方法，来预测下一个时间点的均线值，而均线值又可以反推出股价。

但是，要如何把这个原理付诸实践呢？机器能否直接从价格中学习到这个规律？机器学习能力的边界又在哪里？这是这堂课第二节的内容。

我们分别通过真实的数据和人造数据，通过绘制决策树图，完全理解了lightgbm在回归任务中，是如何求出预测数值的，也明白了lightgbm所做的，只不过是一个分段函数。它就像一棵圣诞树，叶子结点上，挂着我们要收获的礼物。可是，这些礼物是平果还是玩具，得看我们往上挂什么。

为了进一步说明这个原理，我们设计了三个示例。为了方便比较三个示例，我们构建了一个基类，把共同的代码放在基类里，然后把特征提取、收益计算等方法放在子类里。

最后，在最后的一个例子中，我们给圣诞树挂上了神秘的礼物，于是，我们得到了甚至是超出期望的惊喜。

好，今天的课程就上到这里，谢谢大家！

-->
