---
clicks: 9
---

<div class='abs w-50% ml-50 mt-20' v-motion
     :enter='{opacity: 1}'
     :click-5='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/12/roc-auc.jpg)
</div>

<FlashText v-click="[1,2]"
           class='abs mt-1/3 ml-80 text-center w-full text-3xl'>

predict
</FlashText>

<FlashText v-click="[2,3]"
           class='abs ml-80 mt-1/3 text-center w-full text-3xl'>

predict_proba
</FlashText>

<FlashText v-click="[3,4]"
           class='abs mt-120 text-center w-full text-3xl'>

AUC: Compute Area Under the Curve
</FlashText>

<FlashText v-click="[4,5]"
           class='abs mt-120 text-center w-full text-3xl'>

ROC: Receiver Operating Characteristic 
</FlashText>

<div class='abs w-full h-80% mt-20' v-motion
     :enter='{opacity: 0}'
     :click-5='{ opacity: 1}'>

```python {all|5|7,8|10,11|14-16|22-24}{maxHeight: '450px',at:6}
model = LogisticRegression()
model.fit(X_train, y_train)

# 预测概率
y_pred_proba = model.predict_proba(X_test)[:, 1]

# 计算 ROC 曲线
fpr, tpr, thresholds = roc_curve(y_test, y_pred_proba)

# 计算 AUC
roc_auc = auc(fpr, tpr)

# 找到最佳阈值
distances = np.sqrt((1 - tpr)**2 + fpr**2)
best_threshold_index = np.argmin(distances)
best_threshold = thresholds[best_threshold_index]

print(f"最佳阈值：{best_threshold:.2f}, {fpr[best_threshold_index]:.2f}, {tpr[best_threshold_index]:.2f}")

# 在最新的预测中，运用best_threshold

y_pred = model.predict_proba(X_test[?])
if y_pred >= best_threshold:
    print("预测为正类")
```
</div>



<!--
在二分类中，模型除了可以直接输出标签（通过 predict 方法）之外，

[click]


还可以输出一个介于 0 和 1 之间的概率值（通过 predict_proba 方法），

[click]

以表示某个样本为正类的概率。这个概率值需要通过一个阈值来转换成具体的类别预测。例如，如果阈值设为 0.5，那么当模型输出的概率大于或等于 0.5 时，样本被预测为正类；否则，被预测为负类。

具体这个阈值应该设置为多少呢？最好还是通过数据来说话，于是就有了 AUC


[click]

AUC（Compute Area Under the Curve）和 ROC（Receiver Operating Characteristic ）曲线。

在ROC图中，y轴是真正例比率，x轴是假正例比率。曲线下面的就是AUC。AUC值越大，模型预测越准确。


[click]

通过这个图，我们还可以算出在该模型中，确定分类的最佳阈值，它就是ROC曲线上，到点(0,1）距离最短的那个点。


[click]

在使用时，我们首先在测试集上，预测出所有的分类概率值。


[click]

然后通过roc_curve函数，得到fpr, tpr和对应的thresholds。这三个都是数值，并且等长。


[click]

fpr和tpr相当于坐标，有了这个坐标，通过积分就可以算出auc。


[click]

这里计算roc曲线上的点，到点(0,1)的欧氏距离。最终反查出最佳的阈值。


[click]

有了这个阈值之后，在生产中进行预测时，我们就可以像这样来进行分类。这样做会比直接调用`predict`方法更准确。

-->

