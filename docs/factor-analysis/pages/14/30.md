---
clicks: 2
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

## 损失函数
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{ opacity: 1}'>

## 损失函数
## 成本函数
</div>

<div class='abs ml-80 mt-15' v-motion
     :enter='{opacity: 1}'
     :click-2='{ opacity: 0}'>

| 样本值      | 预测值            | MSE                                       |
| ----------- | ----------------- | ----------------------------------------- |
| $y_1 = 100$ | $\hat{y}_1 = 90$  | $L(y_1, \hat{y}_1) = (100 - 90)^2 = 100$  |
| $y_2 = 150$ | $\hat{y}_2 = 100$ | $L(y_2, \hat{y}_2) = (150 - 100)^2 = 250$ |
| $y_3 = 200$ | $\hat{y}_3 = 210$ | $L(y_3, \hat{y}_3) = (200 - 210)^2 = 100$ |
| $y_4 = 250$ | $\hat{y}_4 = 240$ | $L(y_4, \hat{y}_4) = (250 - 240)^2 = 100$ |
| $y_5 = 300$ | $\hat{y}_5 = 310$ | $L(y_5, \hat{y}_5) = (300 - 310)^2 = 100$ |

</div>

<div class='abs text-red text-2xl ml-190 mt-90' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{ opacity: 1}'>

580
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1}'>

## 损失函数
## 成本函数
## 目标函数
</div>

<div class='abs ml-80 mt-30 w-50%' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/12/comparison-loss-cost-obj.jpg)
</div>


<!--

在模型训练时，我们通常要指定**损失函数**；在训练完成之后，我们需要通过**度量函数**来对得到的模型实例进行评估。除此之外，我们还常常看到**成本函数**、**目标函数**和**距离函数**等等概念。这些概念都很相似，我们常常看到人们不加区别地使用它们，但是，一旦落实到具体的模型实现上，尽管模型的作者仍然可能从概念上不加区分地实现它们，但在实现上，却必然是严格地实现了某一种定义。所以，我们必须清楚地了解这些区别。

损失函数指的是单个样本的误差，有点类似于残差。但我们一般可能使用误差的平方来表示损失。


[click]

成本函数则是整个训练集损失函数的平均值或总和。在这个例子中，我们把MSE这一列所有的值平均起来，就是成本函数的值，在这里它是580

在机器学习中，通过对损失函数和成本函数的优化，最终就会得到一个最优的模型。

[click]

目标函数是模型优化中的对象。与成本函数不同的是，目标函数的优化既可能是最小化，也可能是最大化。比如，在强化学习中，模型优化的方向就是要最大化奖励。

这是如果几个概念同时出现时，我们要了解它们之间是有这样的区别的。如果我们单独使用某一个概念，那么它的具体函数，就要根据使用它的场景来确定了。

比如，在sklearn当中，像GradientBoostingClassifier和tensorflow中，它要求的优化函数通过loss参数传入进去，而在xgboost中，优化函数则需要通过objective参数传入进去，但本质上都是要求传入供优化的函数。

那么，损失函数要怎么实现呢？具体到不同的领域，损失函数是不同的。在机器学习中，很可能多数人能做的创新主要就是设计损失函数，因为机器学习模型的设计难度就更高了。

但是，尽管如此，还是会存在一些损失函数，能在多数场景下使用。它们主要根据机器学习任务，可以归为两类，一类是分类问题中可以使用的损失函数，另一类是回归问题中可以使用的损失函数。

-->
