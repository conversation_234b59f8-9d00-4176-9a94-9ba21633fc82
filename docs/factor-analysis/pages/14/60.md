---
clicks: 7
---

<div class='abs mt-50' v-motion
     :enter='{opacity: 1, x: 300}'
     :click-1='{ opacity: 0}'
     :click-2='{ opacity: 1, x: 10}'
     :click-3='{ opacity: 0}'
     :click-4='{ opacity: 1}'
     :click-5='{ opacity: 0}'
     :click-6='{ opacity: 1}'
     :click-7='{ opacity: 0}'>

|          | 预测正类            | 预测负类            |
| -------- | ------------------- | ------------------- |
| 实际正类 | True Positive (TP)  | False Negative (FP) |
| 实际负类 | False Positive (FN) | True Negative (TN)  |

</div>

<div class='abs mt-60' v-motion
     :enter='{opacity: 0, x: 300}'
     :click-1='{ opacity: 1}'
     :click-2='{ x: 600}'
     :click-3='{ opacity: 0}'>

$$\text{Accuracy} = \frac{TP + TN}{TP + TN + FP + FN}$$
</div>

<v-drag-arrow color='red' v-motion :enter="{opacity:0}" :click-2-3='{opacity:1}' pos="96,257,309,92"/>

<div class='abs mt-60' v-motion
     :enter='{opacity: 0, x:300}'
     :click-3='{ opacity: 1}'
     :click-4='{ x: 600}'
     :click-5='{ opacity: 0}'>

$$\text{Precision} = \frac{TP}{TP + FP}$$
</div>

<v-drag :enter="{opacity:0}"
        :click-4-5='{opacity:1}'
        v-motion 
        pos="93,254,342,48">
    <Box />
</v-drag>

<div class='abs mt-60' v-motion
     :enter='{opacity: 0, x:300}'
     :click-5='{ opacity: 1}'
     :click-6='{ x: 600}'
     :click-7='{ opacity: 0}'>

$$\text{Recall} = \frac{TP}{TP + FN}$$
</div>

<v-drag :enter="{opacity:0}"
        :click-6-7='{opacity:1}'
        v-motion 
        pos="93,250,170,100">
    <Box />
</v-drag>

<div class='abs mt-60' v-motion
     :enter='{opacity: 0, x:300}'
     :click-7-8='{ opacity: 1}'>

$$
\text{F1 Score} = 2 \times \frac{\text{Precision} \times \text{Recall}}{\text{Precision} + \text{Recall}}
$$
</div>

<div class='abs mt-50' v-motion
     :enter='{opacity: 0, x:300}'
     :click-8='{ opacity: 1}'>

|          | 预测正类 | 预测负类 |
| -------- | -------- | -------- |
| 实际正类 | 60       | 20       |
| 实际负类 | 10       | 10       |
</div>

<!--
我们先来看分类问题中的度量函数。在一个二分类问题中，会出现以下四种情况，

即将实际上为正类的样本预测为正类，以及将实际上为负类的样本预测为负类。这是预测正确的两种情况。 

但也可能存在，将实际上为正类的样本预测为负类，以及将实际上为负类的样本预测为正类。这是预测错误的两种情况。

在多次预测中，这四种情况可能都会有读数。因此，我们可以通过这些读数和读数的组合，来评估预测的准确性。


[click]

Accuracy，一般翻译为准确率，它是把正类预测对的个数，加上把负类预测对的个数，除以总样本数得到的数值。


[click]

也就是主对角线上之和，除以样本总数。

为何准确率不适合作为损失函数？首先，它的方向是最大化；其次，即使我们用与之互补的错误率作为loss，也会存在这样的问题，对于单个观察值，错误率的损失函数始终为 1（如果预测类别不匹配标签）或 0（如果预测类别匹配标签）。因此，该函数的导数除了在可忽略的点集上导数为无穷大之外，总是为 0。这排除了任何基于梯度的优化器训练模型的可能性，因为模型参数几乎总是有更新步长为 0，除了步长为无穷大的可数次数。

[click]

精确率是说，如果你把一个样本预测为正类，或者说阳性，这个预测会有多精确？如果没有假阳性，就是100%精确。假阳性越多，就越不精确。就相当于用霰弹枪去打目标，每一粒弹珠最终都会命中目标，但只有少数才是我们想要的目标。


[click]

它的计算只需要用图中的第一行数据。


[click]

召回率是指在预测中，有一些阳性被回忆起来了，还有一些没有被回忆起来，当成了阴性。显然，这些阴性是假阴性。那么，以正确回想起来的个数，除以实际阳性的个数，就是召回率。


[click]

召回率的计算，用的是第一列的数据。

我们看到，准确率、召回率和精确率各有用处。如果只使用其中一个，在数据集不平衡的情况下，都有可能错误评估模型。

[click]

在这种情况下，F1-score就被发明出来，用于综合考虑精确率和召回率。


F1 Score的取值也是在[0,1]之间，越大越好。


-->
