---
clicks: 5
---

<div class='abs mt-15' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

## 偏差
</div>
<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{ opacity: 1}'>

## 偏差
## 方差
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{ opacity: 1}'>

## 偏差
## 方差
## 残差
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{ opacity: 1}'>

## 偏差
## 方差
## 残差
## 误差
</div>

<div class='abs h-full  w-full flex flex-col items-center justify-center' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

$$
Bias = E[\hat{f(x)}] - f(x)
$$
</div>

<div class='abs h-full  w-full flex flex-col items-center justify-center' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{ opacity: 1}'>

$$
Variance = E[(\hat{f(x)} - E[\hat{f(x)}])^2]
$$
</div>

<div class='abs mt-15 flex flex-col justify-center' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1}'>

![50%](https://images.jieyu.ai/images/2024/11/bias-vs-variance.jpg)
</div>


<div class='abs mt-25' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{ opacity: 1}'>

![偏差、方差与过拟合的关系](https://images.jieyu.ai/images/2024/11/bias-vs-varians-on-underfit-overfit.jpg?width=75%)

</div>

<div class='abs w-full h-full flex flex-col items-center justify-center' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{ opacity: 1}'>

$$
Residual = y_i - \hat{y_i}
$$
</div>

<div class='abs w-full h-full flex flex-col items-center justify-center' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{ opacity: 1}'>

$$
\text{Error} = (E[\hat{f}(x)] - f(x))^2 + E[(\hat{f}(x) - E[\hat{f}(x)])^2] + \sigma^2
$$

</div>

<!--
偏差是模型预测能力的系统性的偏差。显然，既然是系统性的偏差，而测量都会有误差，所以用真实值与预测值的均值差来表示偏差就比较科学


[click]

方差则是指模型预测值的变化程度。具体来说，方差衡量的是模型在不同训练集上的预测值之间的差异。高方差通常意味着模型过拟合（overfitting），即模型过于复杂，对训练数据的噪声和细节过于敏感。


[click]

偏差与方差有何区别呢？我们可以通过火枪手打移动靶的例子来说明两者关系。

这里有四种情况，第一种是高方差、高偏差。这是一个毫无经验的火枪手的例子。他的火枪疏于保养，枪法也没有准头，所以射出去时东一榔头西一棒的，总是脱靶。


右上角这位枪手，打得其实很准，但是他的枪可能准星有点小问题，所以，总是一致性地偏向中间

左下角的枪手有一把好枪，至少比右上的枪手的要好。但是很可惜，他的准头不够好，不过偶尔也能命中。

右下角的枪手就占据了天时、地利和人和。他几乎每一发子弹，都命中了目标。这是枪没问题，射手也厉害。

了解偏差与方差的概念，对我们有什么帮助呢？


[click]

这张图来自吴恩达的机器学习课程。它显示了一个二分类器的各种结果。

显然，第二个子图的分类器，正是我们要寻找的分类器。它的偏差和方差都很小。在第三个子图中，看起来它正确地把每一个样本进行了分类，但显然有点过头了，这就是过拟合。此时预测值的方差也加大了。因此，方差大的时候，可能预示着模型出现了过拟合的情况。

在第一个子图中，我们看到有许多样本被错误地分类了，这是偏差比较大的情况。这往往是欠拟合，可能是模型太简单了，无法学习到复杂的特征。


[click]

残差特指在训练数据集中，每个样本的真实值与模型预测值之间的差异。残差可以帮助我们评估模型在训练集上的拟合情况。

所以残差跟偏差既相似又不同。首先它们作用的范围不同。用机器学习的话来讲，一个是局限在训练集上，一个是在测试集上。

其次，偏差是关于预测值的平均值的。而残差则是关于单次测量的。


[click]

误差是模型在新数据上的预测值与真实值之间的差异。一般用这个公式来表示，即偏差的平方，方差和不可减少的误差。

在机器学习中，模型和数据都会带来偏差。比如，如果真实世界是非线性的，而我们训练时使用了线性模型，就必然会引入偏差；在图像生成应用中，如果使用的数据集来自早期的互联网，由于当时访问互联网存在成本、技能障碍，因此采集到的图片就会以欧美文化为主，这样模型就无法生成反映其它地方人类生活的图片，这也是一种系统性偏差。

在量化交易中，我们采集数据时，也可能引来各种偏差。最常见的是幸存者偏差。比如，我们拿今天的股票列表来进行回测，这就引入了幸存者偏差。因为那些因为业绩不好、已经退市的股票，是不会出现在今天的股票列表上的。

当然我们去研究基金时，这类由于数据采集造成的偏差就更为严重。
-->
