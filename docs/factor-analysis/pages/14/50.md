---
clicks: 3
---

<div class='abs w-50% ml-50 mt-30' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/12/how-to-choose-loss-function.jpg)
</div>


<div class='abs mt-30 w-full' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{ opacity: 1}'>

```python
from sklearn.ensemble import GradientBoostingRegressor

GradientBoostingRegressor(
    *,
    loss='squared_error',
    learning_rate=0.1,
    n_estimators=100,
    ...
)

loss : {'squared_error', 'absolute_error', 'huber', 'quantile'},             default='squared_error'
    Loss function to be optimized. 'squared_error' refers to the squared
    error for regression. 'absolute_error' refers to the absolute error of
    regression and is a robust loss function. 'huber' is a
    combination of the two. 'quantile' allows quantile regression (use
    `alpha` to specify the quantile).
```
</div>

<div class='abs mt-30 w-full' v-motion
     :enter='{opacity: 0}'
     :click-2='{ opacity: 1}'>

```python
from xgboost import XGBRegressor

XGBRegressor(
    *,
    objective: Union[str, xgboost.sklearn._SklObjWProto, Callable[[Any, Any], Tuple[numpy.ndarray, numpy.ndarray]], NoneType] = 'reg:squarederror',
    **kwargs: Any,
) -> None
```
</div>

<FlashText v-click="[3,4]"
           class='abs mt-1/3  w-100 ml-10 text-3xl '>

reg:sequarederror
</FlashText>

<FlashText v-click="[3,4]"
           class='abs mt-1/3 w-100 ml-90 text-3xl'>

reg:sequaredlogerror
</FlashText>

<FlashText v-click="[3,4]"
           class='abs mt-1/3 w-100 ml-180 text-3xl'>

reg:logistic
</FlashText>

<FlashText v-click="[3,4]"
           class='abs mt-2/5 w-100 ml-180 text-3xl'>

reg:absoluteerror
</FlashText>

<FlashText v-click="[3,4]"
           class='abs mt-2/5 w-100 ml-90 text-3xl'>

reg:quantileerror
</FlashText>

<FlashText v-click="[3,4]"
           class='abs mt-2/5 w-100 ml-10 text-3xl'>

reg:pseudohubererror
</FlashText>

<!--

我们简单介绍了这么多损失函数，那么，在实际应用中要如何选择呢？

当然首先还是看我们要解决的任务是分类还是回归。模型定下来之后，有的模型是自带损失函数的，比如，linearsvc，它默认的损失函数就是hingeloss，没有别的选项。

我们前面介绍过的4种线性回归模型，它们的损失函数也是默认的MSE，无法更改。甚至施加的正则化不同，模型也是换了名字。

有一些高级一点的模型，允许我们自己指定损失函数，甚至自己定义损失函数。


[click]

在梯度提升模型中，我们可以通过loss参数，指定不同的损失函数，这里主要是4类。squared_error就是MSE， absolute_error就是MAE。其它两种也是我们刚刚提到的类型。


[click]

我们再来看xgb中的回归模型中的损失函数。

xgb很强大，也更加灵活。它本身定义了多个损失函数，也允许我们自定义损失函数并传入。在这里，传入的参数是objective，而不是损失函数，但大家的含义差不多，都是模型用来优化、最小化的那个函数。


[click]

它有这些内置的损失函数，这里我们只简单提一下。后面有机会再讲。

注意这里出现了一个reg:absoluteerror，右下角的那个。我们前面提到过，有的模型比如xgbboost使用了二阶导，因此就不能用MAE这样的损失函数，这里却出现了这样一个参数，这是怎么回事呢？

这就是沟通的复杂性，一个词，不同的人讲它的时候，含义可能完全不一样。xgboost在这里并不是直接使用的MAE，而是加上了lasso正则化的MAE。
-->
