---
clicks: 9
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

## 欧几里得距离
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-1-3='{ opacity: 1}'>

## 欧几里得距离
## 曼哈顿距离
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-3-5='{ opacity: 1}'>

## 欧几里得距离
## 曼哈顿距离
## 余弦相似度
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{ opacity: 1}'>

## 欧几里得距离
## 曼哈顿距离
## 余弦相似度
## 皮尔逊距离
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-6-8='{ opacity: 1}'>

## 欧几里得距离
## 曼哈顿距离
## 余弦相似度
## 皮尔逊距离
## 杰卡尔德相似度
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-8-10='{ opacity: 1}'>

## 欧几里得距离
## 曼哈顿距离
## 余弦相似度
## 皮尔逊距离
## 杰卡尔德相似度
## 马氏距离
</div>

<div class='abs mt-20 ml-80' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

$d(x, y) = \sqrt{\sum_{i=1}^{n} (x_i - y_i)^2}$
</div>

<div class='abs mt-30 ml-80' v-motion
     :enter='{opacity: 0}'
     :click-1='{ opacity: 1}'
     :click-3='{ opacity: 0}'>

$d(x, y) = \sum_{i=1}^{n} |x_i - y_i|$
</div>

<div class='abs ml-100 mt-50 w-80' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/12/euclidean-vs-manhattan.jpg)
</div>

<div class='abs ml120 mt-30 w-50' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{ opacity: 1}'>

$$
Cos(A, B) = \frac{A \cdot B}{|A| |B|}
$$
</div>

<div class='abs ml-60 mt-30 w-80' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/12/two-klines.jpg)
</div>

<div class='abs ml-150 mt-30 w-80' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/12/two-kline-sorted.jpg)
</div>

<div class='abs mt-30 ml-100' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{ opacity: 1}'>

$$r_{xy} = \frac{\sum_{i=1}^{n} (x_i - \bar{x})(y_i - \bar{y})}{\sqrt{\sum_{i=1}^{n} (x_i - \bar{x})^2 \sum_{i=1}^{n} (y_i - \bar{y})^2}}$$
</div>

<div class='abs mt-50 ml-120' v-motion
     :enter='{opacity: 0, y: 0}'
     :click-6='{ opacity: 1}'
     :click-7='{ y:-100}'
     :click-8='{ opacity: 0}'>

$$
J(A, B) = \frac{|A \cap B|}{|A \cup B|}
$$
</div>

<div class='abs ml-120 mt-50' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{ opacity: 1}'>

|       | A   | B   | C   | D   | E   |
| ----- | --- | --- | --- | --- | --- |
| 股票1 | 0   | 0   | 1   | 1   | 0   |
| 股票2 | 1   | 0   | 1   | 0   | 1   |

</div>

<div class='abs mt-50 ml-120' v-motion
     :enter='{opacity: 0}'
     :click-8='{ opacity: 1}'>

$$
{\displaystyle D_{M}({\vec {x}})={\sqrt {({\vec {x}}-{\vec {\mu }})^{T}\Sigma ^{-1}({\vec {x}}-{\vec {\mu }})}}}
$$
</div>

<FlashText v-click="[9,10]"
           class='abs mt-80 ml-80 w-full text-3xl'>

from scipy.spatial.distance import mahalanobis
</FlashText>

<!--
欧几里得距离。RMSE实际上就是一种欧几里得距离。


[click]

欧氏距离是两点之间的直线距离。

[click]

在图中，它是AB两点之间的直线距离。而曼哈顿距离则是先从A到C，在从C到B之间的距离和。

很显然，当我们为出租车司机进行路线优化时，就不能使用欧氏距离，而要使用曼哈顿距离。

[click]

余弦相似距离处理像文本匹配这样的高维度数据时，比较常用。我们在第11课的练习题中有提到过，当大家用TF-IDF求得文档的特征向量之后，就可以用余弦相似度来寻找相似文档，实现文档查重、类似文档推荐等功能

[click]

如果我们要寻找相似k线，可以使用余弦相似度吗？

如果我们使用余弦相似度，那么，对一条长度为20的k线，我们实际上是把它当成20维的高维向量来处理。这样与另一条k线进行相似度比较时，我们是计算两个高维向量之前的余弦相似度。所以它的物理含义，与我们在图中看到的k线之间的比较是不一样的。

在这两个图中，右图是左图中的一个序列重新进行了排序之后生成的图像。显然，重新排序之后，两条k线就不再相似了。但是，如果计算余弦距离，左右两种情况都会接近于1.


[click]


但是，如果我们计算协相关系数，左图协相关系数接近1，说明两者正相关；而右图的相关系数只有1%不到，表明两者不相关。所以，协相关系数能比余弦距离更好地揭示两条k线的相似性。


[click]

杰卡尔德相似度是关于集合的相似度。


[click]

假如我们在市场上发现一个好的标的，连续几天涨停了。我们研究股票的维度有ABCDE 5个维度。如果股票1就是这个连续几天涨停的个股，现在我们要问，股票2会不会跟它类似？

这样，我们也要按ABCDE 5个维度来提取股票2的特征向量，然后就可以用杰卡尔德相似度来进行比较了。

在这个例子中，两支股票只有在维度c上都是1，它们的并集长度是4，所以，相似度是0.25。根据这个结果，我们很可能不会选择第二支股票。

当然，我们也可以使用余弦相似度、皮尔逊相关系数来判断两者的相似性


[click]
马氏距离是规范化的主成分空间中的欧氏距离。它有一些优美的特性，比如与尺度无关，因为它计算的是点p相到于分布D的平均值偏离了多少个标准差。所以，这个过程中，就把量纲去掉了。

在sklearn中没有马氏距离的实现。我们可以借助scipy中的方法。


[click]



-->



