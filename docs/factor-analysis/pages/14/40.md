---
clicks: 5
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

## MSE
</div>


<div class='abs ml-120 mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

$$
MSE = \frac{\sum_{i=1}^{n}(y_i-\hat{y_i})^2}{n}
$$
</div>

<div class='abs w-100 ml-100 mt-50' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/11/meaning-of-mse.jpg)
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-1-3='{ opacity: 1}'>

## MSE
## MAE
</div>

<div class='abs w-100 ml-100 mt-30' v-motion
     :enter='{opacity: 0}'
     :click-1='{ opacity: 1}'
     :click-2='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/11/meaning-of-mae.jpg)
</div>

<div class='abs ml-80 mt-10' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1}'>

<div style='width:60%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/meaning-of-mse.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

<div style='width:60%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/meaning-of-mae.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-20 w-80% ml-10%' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/12/fritz.jpg)
</div>

<FlashText v-click="[3,4]"
           class='abs mt-1/3 text-center w-full text-3xl'>

https://fritz.ai/best-regression-loss-functions/
</FlashText>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-4='{ opacity: 1}'>

## MSE
## MAE
## RMSE
## Hubber Loss
## Log-Cosh Loss
## Quantile Loss
</div>


<div class='abs ml-100 mt-20 w-50%' v-motion
     :enter='{opacity: 0}'
     :click-5='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/11/five-loss-functions.jpg)
</div>
<!--

假设真实目标值为 100，多次预测的y_hat分布在 [-10000,10000] 之间，则 MSE 在预测值为 100 时，达到最小值，这就是MSE的优化过程。


[click]

MSE是把误差进行了平方。我们也可以只使用一阶范数，即误差的绝对值。我们常用MAE，即平均绝对误差来表示。


[click]

这两者之间要如何选择呢？我们看到，两个损失函数有着不同的数学性质。MSE更平滑一些，并且它有二阶导；MAE没有二阶导。有一些模型在优化过程中，会利用损失函数的二阶导来快速地收敛。所以，在这些模型中，MAE就是不能使用的。我们后面要学到的xgboost就不能使用MAE。

另一方面，MSE会使得预测结果更倾向于异常值，而MAE则会使预测结果倾向于平均值。


[click]

关于这两个函数对离群值的选择性的讨论，大家可以看看fritz上的文章，讲得比较细致、透彻。链接在课程的footnotes中。

[click]

在我刚刚提到的文章中，它还讨论了其它损失函数，比如Huber Loss, Log-Cosh loss和Quantile Loss。

特别是quantile loss，它可能在量化交易中比较有用。因为我们从因子分析就可以看出，很多时候，数据的绝对值在投资中不重要，它们的相对排名更重要。因为资产总要投资，所以，即使市场不好，也要选择相对较强的投一点，跑赢大市就行了

从quantile loss上也可以看出，作为量化人，我们在运用机器学习时，应该如何创新。相信这样的空间还很广阔。


[click]

这是5种损失函数的图形。更详细的解读大家可以参考我刚刚介绍的那篇文章。

-->
