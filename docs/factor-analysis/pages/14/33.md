---
clicks: 5
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/12/categorical-loss-func.jpg)
</div>

<FlashText v-click="[1,2]"
           class='abs mt-1/4 text-center w-full text-3xl'>

from sklearn.metrics import log_loss
</FlashText>

<div class='abs ml-50 mt-10' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1}'>

$$
\text{Softmax Cross Entropy Loss} = -\frac{1}{N} \sum_{i=1}^{N} \sum_{j=1}^{C} y_{ij} \log(\hat{y}_{ij})
$$

</div>

<div class='abs w-full h-full mt-40' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1}'>

```python
from sklearn.metrics import log_loss
import numpy as np

# 假设我们有以下真实标签和预测概率
y_true = [0, 1, 2, 2, 2]  # 真实标签
y_pred = [
    [0.2, 0.1, 0.7],  # 第一个样本的预测概率
    [0.3, 0.4, 0.3],  # 第二个样本的预测概率
    [0.1, 0.8, 0.1],  # 第三个样本的预测概率
    [0.1, 0.1, 0.8],  # 第四个样本的预测概率
    [0.1, 0.1, 0.8]   # 第五个样本的预测概率
]

# 计算 Softmax Cross Entropy Loss
loss = log_loss(y_true, y_pred)
print(f"Softmax Cross Entropy Loss: {loss:.4f}")
```
</div>

<div class='abs mt-40' v-motion
     :enter='{opacity: 0, x:300}'
     :click-3='{ opacity: 1}'
     :click-4='{ x: 10}'
     :click-5='{ opacity: 0}'>

### Softmax
### Softmax Cross Entropy Loss
### Categorical Cross-Entropy Loss
</div>

<div class='abs ml-120 mt-40' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{ opacity: 1}'>

$$
\sigma(z)j = \frac{e^{z_j}}{\sum_{k=1}^{K}e^{z_k}} 
$$
</div>

<div class='abs mt-50 ml-10' v-motion
     :enter='{opacity: 0}'
     :click-5='{ opacity: 1}'>

$$
\ell(y) = \max(0, 1-t \cdot y)
$$

</div>

<div class='abs w-50% ml-50% mt-20' v-motion
     :enter='{opacity: 0}'
     :click-5='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/11/hinge-loss.jpg)
</div>

<!--
当我们确定要使用分类任务模型时，有的模型会允许我们指定损失函数，甚至允许我们传入自已定义的损失函数。

在分类任务中可以使用的损失函数大致如这个图所示。同样，这里的许多概念仍然都是有歧义的。它把损失函数主要归为二分类、多分类和其它。


[click]

但是，在sklearn中，二分类和多分类都是通过log_loss这个方法来实现的。因为本质上，二分类就是多分类的特殊情况，是一种简化。


[click]

它们的公式都是一样的


[click]

这里我们还要辨析三个概念。其中后面两个概念是完全等同的。softmax比较复杂。


[click]

它是将一个向量转换为另一个概率向量，并且转换后每个元素取值在[0,1]之间，且元素之和为1的函数。

在公式中，z是一个k维向量，左边是softmax对向量Z的第j个元素的输出。

这个函数在深度学习中也广泛使用，常常作为多分类问题中的输出层。

但是，在某些场景下，当我们说softmax时，也可能就是指的softmax cross entropy loss，所以，此时这三者完全是一样的。


[click]

接下来我们要介绍一下Hinge Loss，主要是它的性质比较特殊，有点类似于ReLu，从仿生学的角度来看，我觉得它比较像神经元的工作模型。

左边是Hinge loss的公式，右边是它的图形。

这里$y$是分类器决策函数的原始输出，而不是预测的类别标签。这个函数有这样的特征，当预测正确（即$t$和$y$同号）且$|y|\geq1$时，损失函数最小（即为零）；如果预测错误，则$\ell(y)$随着$y$的增加线性增加，施加的惩罚也越来越大。如果预测的方向正确，但$|y|<1$，也会施加一定的惩罚。

在图中，竖轴代表了 Hinge Loss，横轴代表了预测值$y$。此图表明损失函数在预测值$y<1$时施加了惩罚，而当预测值$y>1$时，损失函数就是最优化值。通过这种处理，使得 SVM 的解具有稀疏性，即大部分训练样本不会对最终模型产生影响，只有支持向量（即位于间隔边界上的样本）才会对模型参数产生影响，并且对噪声和异常值具有一定的鲁棒性，因为它只关注那些分类错误的样本或分类正确但置信度不高的样本。

如果我们把这个图进行水平镜像，你会发现，它就是深度学习中常用的激活函数ReLu
-->
