
<div class="mt-10 ml-5">

## 误差
### 偏差
### 方差
### 残差
## 过拟合与正则化
## 损失函数
## 度量函数
## 距离函数
</div>

<!--
机器学习的核心是训练与评估。而训练的核心，又是围绕损失函数进行优化的。

要定义损失函数和评估函数，我们就要先理解什么是误差，以及不同场合下，误差的各种细分概念。于是我们讲到了偏差、方差、残差。

为了发现和防止偏差，人们发明了正则化方法。在这一节我们介绍了L1正则化、L2正则化。这两种正则化又分别被称为LASSO正则化和Ridge正则化。我们有时候也同时施加这两种正则化。对应地，在sklearn的线性回归中，就有了LinearRegression、Lasso、Ridge、ElasticNet这几种模型。

有了这些概念的准备，我们就学习了损失函数。在狭义上，它是指单个预测值与测量量值之间的误差。从系统整体上看，所有损失函数的和就成为系统的成本函数。再加上正则化惩罚，就成为模型训练时，进行优化的目标函数。

但是目标函数有更广泛的应用场景。损失函数的优化方向是最小化，而目标函数既可能是最小化优化，也可能优化方向是最大化。

在训练阶段，我们需要损失函数，而训练完成后，我们通过度量函数来评估模型的好坏。损失函数必须在数学上满足像牛顿法、梯度下降法得以应用的一些条件，比如可导可微；而评估函数则没有此要求。

在机器学习中，多数人创新或者说解决一个新问题的方式，是定义自己的损失函数和评估函数，因为设计一个新模型的难度是比较大的。损失函数和评估函数更抽象地来看，是一种距离函数。设计距离函数，要满足它的性质，比如非负、自反性、对称性。我们在这里介绍了包括欧氏距离在内的6种距离函数。它们是我们寻找相似股票时，必须使用的法宝。

好，今天的课程就到这里。我们下次课再见！

-->
