---
clicks: 7
---

<div class='abs mt-15' v-motion
     :enter='{opacity: 1}'
     :click-3='{ opacity: 0}'>

## 过拟合表象
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-3-5='{ opacity: 1}'>

## 过拟合表象
## 防止方案
### L1正则化
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-5-7='{ opacity: 1}'>

## 过拟合表象
## 防止方案
### L1正则化
### L2正则化

</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{ opacity: 1}'>

### LinearRegression
### Lasso
### Ridge
### ElasticNet
</div>

<FlashText v-click="[1,2]"
           class='abs mt-20 text-center w-full text-3xl'>
高方差
</FlashText>

<FlashText v-click="[2,3]"
           class='abs mt-20 text-center w-full text-3xl'>
训练误差低 验证误差高
</FlashText>

<div class='abs w-full h-full flex flex-col items-center justify-center' v-motion
     :enter='{opacity: 0}'
     :click-3-5='{ opacity: 1}'>

$$
\widetilde{J}(\theta) = J(\theta) + \lambda \sum_{i=1}^n |\theta_i|
$$
</div>

<FlashText v-click="[4,5]"
           class='abs mt-1/3 text-center w-full text-3xl'>

Least Absolute Shrinkage and Selection Operator (LASSO)
</FlashText>

<div class='abs w-full h-full flex flex-col items-center justify-center' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{ opacity: 1}'>

$$
\widetilde{J}(\theta) = J(\theta) + \lambda \sum_{i=1}^n \theta^2_i
$$
</div>


<FlashText v-click="[5,6]"
           class='abs mt-1/3 text-center w-full text-3xl'>

Ridge Regularization
</FlashText>

<div class='abs w-100 ml-100 mt-20' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{ opacity: 1}'>

| 模型              | 实现             | 正则化 |
| ----------------- | ---------------- | ------ |
| Linear Regression | LinearRegression | 无     |
| Lasso             | Lasso            | L1     |
| Ridge             | Ridge            | L2     |
| Elastic Net       | ElasticNet       | L1+L2  |
</div>

<div class='abs mt-15 ml-25' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{ opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/comparison-linear-regressions.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>



<!--
我们已经提到过过拟合和欠拟合。过拟合（Overfitting）是指机器学习模型在训练数据上表现得非常好，但在未见过的新数据上表现较差的现象。换句话说，模型在训练数据上过度学习了细节和噪声，导致其泛化能力下降。过拟合通常发生在模型过于复杂或训练数据量不足的情况下。

[click]

我们常常可以从两个表象上来推测发生了过拟合。第一个就是训练集上出现的高方差。


[click]

第二就是，在训练和验证集上都表现很好，偏差和方差都很低，但是验证误差高。

为了防止过过拟合，人们发明了两种通用的方法。其一是L1正则化

[click]

L1正则化又叫Lasso正则化。即


[click]

least absolute shrinkage and selection operator

它的做法是在原损失函数基础上，增加各项参数的绝对值。它有什么作用呢？L1正则化倾向于使某些参数变为零，从而实现特征选择。


[click]

第二种方法是L2正则化。

L2 正则化也称为岭回归正则化


它是在损失函数中加入模型参数的平方之和。L2 正则化使参数值变小，但不会使参数变为零，从而使模型更加平滑。

关于L1和L2正则化是如何使模型更加平滑的，会涉及到导数和梯度计算，如果你对此感兴趣，可以自己对这两个公式进行求导推导。


[click]

除此之外，还有同时施加L1和L2正则化的方法。

在sklearn当中，它依据线性回归施加正则化方法的不同，分别称为LinearRegression, Lasso, Ridge和ElasticNet四种模型。


[click]

在课程中，我们有一段代码，演示了通过这四种模型进行回归分析的情况。这里我们把对照结果显示一下。

虽然从这个例子来看，施加L2正则化与无正则化结果比较接近，效果好像看不出来，但如果我们计算出各自的预测均方差损失，还是会发现它们之前有些小的差距。


-->
