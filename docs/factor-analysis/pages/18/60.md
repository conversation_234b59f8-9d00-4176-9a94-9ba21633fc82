---
clicks: 1
---

<div class="mt-10 ml-5">

## 决策是如何形成的
### 构造原理
### 决策树的优缺点
## 改进决策树 -- 梯度提升算法
### XGBoost
## Lightgbm
### dataset
### 模型训练
### 可视化
### 优化
</div>

<!--
今天的主要课程内容就到这里。现在， 请大家提出问题。在大家准备问题的同时，我为大家总结今天的课程。

今天课程的主要内容是讲解Lightgbm。它是一种梯度提升的决策树。为了理解为什么我们需要发明这样一种算法，我们先回顾了决策树是如何构造的，它存在哪些问题。

然后，我们介绍了如何改进决策树。一种方案是随机森林，它同时训练多棵决策树，然后对各棵子树的决策进行汇总。但是，随机森林算法也存在不少问题。

于是，人们发明了梯度提升算法。它主要是通过训练新的决策树，来尝试消化上一轮迭代中，存在的残差的方法。

基于梯度提升的思想，现在比较重要的梯度提升算法有sklearn的，xgboost，lightgbm和catboost。一般情况下，lightgbm在训练效率和最终结果上表现更好，所以，这节课的重点是介绍lightgbm。

在lightgbm这一节中，我们介绍了最核心的步聚，即如何构建dataset,如何进行训练，如何对训练结果进行观察，如何进行优化。

最后，我们也介绍了lightgbm的sklearn接口，在这部分，最重要的可能是掌握如何保存一个模型，并在生产环境中加载和使用模型。

好，今天的课程就上到这里，谢谢大家！
-->
