---
clicks: 2
---

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns

url = "/data/adult.data"
column_names = [
    "age", "workclass", "fnlwgt", "education", "education-num", "marital-status",
    "occupation", "relationship", "race", "sex", "capital-gain", "capital-loss",
    "hours-per-week", "native-country", "income"
]
data = pd.read_csv(url, header=None, names=column_names, na_values=" ?", skipinitialspace=True)

imputer = SimpleImputer(strategy='most_frequent')
data_imputed = pd.DataFrame(imputer.fit_transform(data), columns=data.columns)

# 确保数值列的数据类型正确
numerical_features = ["age", "fnlwgt", "education-num", "capital-gain", "capital-loss", "hours-per-week"]
data_imputed[numerical_features] = data_imputed[numerical_features].astype(float)

# 编码类别型特征
categorical_features = [
    "workclass", "education", "marital-status", "occupation", "relationship",
    "race", "sex", "native-country"
]

label_encoder = LabelEncoder()
for feature in categorical_features:
    data_imputed[feature] = label_encoder.fit_transform(data_imputed[feature])

# 编码目标变量
data_imputed['income'] = label_encoder.fit_transform(data_imputed['income'])

# 确认数据类型
print(data_imputed.dtypes)

# 划分训练集和测试集
X = data_imputed.drop('income', axis=1)
y = data_imputed['income']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建 LightGBM 数据集
train_data = lgb.Dataset(X_train, 
                         label=y_train, 
                         categorical_feature=categorical_features,
                         free_raw_data=False)
test_data = lgb.Dataset(X_test, 
                        label=y_test, 
                        categorical_feature=categorical_features, 
                        reference=train_data,
                        free_raw_data=False)

params = {
    'objective': 'binary',
    'metric': ['binary_logloss', 'auc'],
    'boosting_type': 'gbdt',
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.9,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': 1
}
```
</NoteCell>

<NoteCell layout='horizontal' class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-3='{ scale: 0}'>

```python{all|4|8,9}{at:1}
# 进行交叉验证
esr = 10
evals_result = {}
cv_results = lgb.cv(
    params,
    train_data,
    num_boost_round=100,
    nfold=3,  # 5折交叉验证
    stratified=True,  # 分层抽样
    shuffle=True,
    metrics=['binary_logloss', 'auc'],
    callbacks = [lgb.early_stopping(stopping_rounds=esr),
                lgb.record_evaluation(evals_result)],
    seed=42
)

# 打印交叉验证结果
print("CV Results:", cv_results)

# 绘制交叉验证结果
plt.figure(figsize=(12, 6))

# 绘制 binary_logloss
results = {
    'binary_logloss-mean': np.array(cv_results['valid binary_logloss-mean']),
    'binary_logloss-stdv': np.array(cv_results['valid binary_logloss-stdv']),
    'auc_mean': np.array(cv_results['valid auc-mean']),
    'auc_stdv': np.array(cv_results['valid auc-stdv'])
}
plt.subplot(1, 2, 1)
plt.plot(results['binary_logloss-mean'], label='Mean')
plt.fill_between(range(len(results['binary_logloss-mean'])), 
                 results['binary_logloss-mean'] - results['binary_logloss-stdv'], 
                 results['binary_logloss-mean'] + results['binary_logloss-stdv'], 
                 alpha=0.3)
plt.title('Cross-Validation - binary_logloss')
plt.xlabel('Number of Boosting Rounds')
plt.ylabel('binary_logloss')
plt.legend()

# 绘制 auc
plt.subplot(1, 2, 2)
plt.plot(results['auc_mean'], label='Mean')
plt.fill_between(range(len(results['auc_mean'])), 
                 results['auc_mean'] - results['auc_stdv'], 
                 results['auc_mean'] + results['auc_stdv'], 
                 alpha=0.3)
plt.title('Cross-Validation - auc')
plt.xlabel('Number of Boosting Rounds')
plt.ylabel('auc')
plt.legend()

plt.tight_layout()
plt.show()
```
</NoteCell>

<!--
<run></run>

交叉验证的概念我们之前已经讲过了。这一节我们主要讲，在lightgbm中如何实现。

由于交叉验证很重要，所以，lightgbm自身内建了这个功能。


[click]

lightgbm用cv代替了train方法，以实现交叉验证。


[click]

这两个参数用来指定如何进行交叉验证划分。它们的含义我们在前面的课程中已经讲过了。

我们页面设置的参数比较小，是为了能迅速看到效果。这些并不是推荐参数。

好，现在我们看下结果。这两个图怎么理解？

在图中，我们绘制了cross validation中得到的两个指标的均值，以及它们的标准差。直观来看，如果这个包络带比较窄，则说明在不同的分区验证集上，模型的表现都比较稳定。因此，我们就可以利用这一组参数来训练出最终的模型。

当然，尽管lightgbm提供了交叉验证，它的这个交叉验证方法对我们可能并不太适用。因为我们前面也讲过，k-fold交叉验证不适用于证券行情数据。

-->
