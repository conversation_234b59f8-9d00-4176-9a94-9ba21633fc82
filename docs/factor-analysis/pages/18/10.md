---
clicks: 4
---

<div class='abs w-full mt-25' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/entropy.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>图片来源：阮一峰</span>
</div>
</div>

<div class='abs mt-50 ml-100' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

## 信息熵
</div>

<div class='abs mt-60 ml-80' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

$$
H(X) = -\sum_{i=1}^{n} P(x_i) \log_b P(x_i)
$$

</div>

<div class='abs mt-50 ml-100' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>

## 信息增益
</div>

<div class='abs mt-50 ml-80' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>

$$ Gini(D) = 1 - \sum_{i=1}^{n} P(x_i)^2 $$
</div>

<div class='abs mt-50 ml-80' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>

<div style="border: 2px solid grey;text-align:center;padding: 5px;width: 80px">分类</div>

<div style="position:absolute; left: 250px; top:0;border: 2px solid red;text-align:center;padding: 5px;width: 80px">回归</div>

<div style="position:absolute; left: -100px; top:100px;border: 2px solid grey;text-align:center;padding: 5px;width: 80px">信息熵</div>
<div style="position:absolute; left: 50px; top:100px;border: 2px solid grey;text-align:center;padding: 5px;width: 80px">Gini</div>
<div style="position:absolute; left: 250px; top:100px;border: 2px solid red;text-align:center;padding: 5px;width: 80px">MSE</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>

<v-drag-arrow color='red' pos="356,242,-60,56"/>
<v-drag-arrow color='red' pos="359,243,52,59"/>
<v-drag-arrow pos="609,240,0,61"/>
</div>

<!--
决策树究竟是如何构建出来的呢？我们以分类任务为例。初始状态下，我们拿到的训练集，数据是混乱的。它的混乱度可以用熵的概念来描述。熵原本是一个物理学（热力学）概念，用于描述系统的无序程度或混乱度。

在一个分类任务中，如果训练集中的样本均匀分布在各个类别上，那么这个数据集就是无序和混乱的，它的熵值较高；反之，如果大部分样本集中在某一类或少数几类，表示数据较为有序，熵值较低，这正好就与原本的物理学概念对应了起来。

熵要如何计算呢？


[click]

对一个离散型的随机变量，它的取值可能是x_1, x_2, x_n, 每一个取值，出现的概率分别是p_1, p_2, p_n。那么，信息熵就可以用这个公式计算。如果我们把取值看成是分类类别，那么当随机变量X只取其中一个值，即在该分类上的概率为1，在其它分类上的概率是0，此时信息熵为0，也就是最纯的状态。

如果预测不对，随机变量的取值就可能在各个分类之间均匀分布，此时信息熵最大化。


[click]

基于这个信息熵的概念，我们就可以计算每一次划分后，树的信息熵。前后两次信息熵的差值，就是这次划分带来的信息增益。如此一来，只要机器学习沿着熵减的方向去优化，最终我们就能得到一个高度有序的树。


[click]

在第13课，决策树优化的目标是gini系数，而不是信息熵。它是另一种衡量数据集纯度的指标，反映了从数据集中随机抽取两个样本，它们属于不同类别的概念。显然，基尼系数越小，数据集的纯度越高，即同一类别的样本占比越大。

从计算性能上看，基尼系数只需要进行二次函数计算，比起信息熵，计算速度要快很多，但是，它的精度也会差一些。


[click]

刚刚讲的这些是分类任务。如果是回归任务，那么分类指标就是mse
-->
