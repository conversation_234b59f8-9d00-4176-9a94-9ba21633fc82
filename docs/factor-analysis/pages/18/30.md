---
clicks: 9
---


<div class='abs mt-20' v-motion
     :enter='{opacity: 1, scale: 1,x:0,y:0}'
     :click-1='{scale: 0.8,x:150,y:-10}'
     :click-6='{opacity:0}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/gradient-boosting-algo.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>图片来源: Hemashreekilari@medium</span>
</div>
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-1-6='{opacity: 1}'>

### 1. 初始化模型
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-1-6='{opacity: 1}'>

### 1. 初始化模型
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-2-6='{opacity: 1}'>

### 1. 初始化模型
### 2. 计算负梯度
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-3-6='{opacity: 1}'>

### 1. 初始化模型
### 2. 计算负梯度
### 3. 拟合弱学习器
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-4-6='{opacity: 1}'>

### 1. 初始化模型
### 2. 计算负梯度
### 3. 拟合弱学习器
### 4. 更新模型
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{opacity: 1}'>

### 1. 初始化模型
### 2. 计算负梯度
### 3. 拟合弱学习器
### 4. 更新模型
### 5. 重复迭代
</div>


<div class='abs flex justify-center items-center h-full w-full' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/scikit-learn-logo.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>版权归scikit-learn开发者所有</span>
</div>

</div>


<div class='abs flex justify-center items-center h-full w-full' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{opacity: 1}'>

<div style='width:60%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/xgboost-logo.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>版权归xgboost开发者所有</span>
</div>

</div>

<div class='abs flex justify-center items-center h-full w-full' v-motion
     :enter='{opacity: 0}'
     :click-8-9='{opacity: 1}'>
<div style='width:60%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/lightgbm-logo.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>版权归LightGBM开发者所有</span>
</div>

</div>

<div class='abs flex justify-center items-center h-full w-full' v-motion
     :enter='{opacity: 0}'
     :click-9-10='{opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/catboost-logo.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>版权归Yandex所有</span>
</div>
</div>



<!--

梯度提升决策树大致上是这样一种算法，可以用以下5步来描述


[click]

第1步，模型初始化时，用一个简单的模型（如常数）作为初始预测值。


[click]

第2步，对于每一轮迭代，计算当前模型在训练数据上的残差（即真实值与预测值之间的差异）。这些残 差可以看作是损失函数关于当前预测值的负梯度。


[click]

第3步，训练一个新的弱学习器（通常是决策树），使其尽可能好地拟合这些负梯度。


[click]

第4步，将新训练的弱学习器加入到现有模型中，调整其权重（通常由学习率控制），从而更新整体模型的预测值。


[click]

然后重复以上步骤，直到达到预设的最大迭代次数或满足其他停止条件。

在随机森林中，训练过程是并行的。但在梯度提升中，需要进行多轮迭代，每一轮新的迭代都是为了消解前一轮中剩余的残差。因此，它在训练中的并行化程度低一些，这会影响到训练速度。


[click]

梯度提升算法是一类算法的总称，比较重要的实现有4种。首先是sklearn的实现。

在sklearn中，存在GradientBoostingRegressor和GradientBoostingClassifier，它们都是梯度提升算法，分别用于回归和分类。它们在性能上不如其它三个，但是简单易用，且与 scikit-learn 生态系统无缝集成，因此可以作为很好的教学系统


[click]

xgboost，这是最先发展起来的、有工业强度的一个 GBDT 模型。它在2014年发布之后，在kaggle上表格类数据竞赛中，霸榜了接近4年，直到2017年微软的lightGBM问世，这把交椅才让出来。


[click]

LightGBM 是微软开发的一个模型，相比 XGBoost，它通过直方图算法将连续特征离散化为有限个区间（即直方图），从而加速了最佳分裂点的查找过程，减少了内存占用。此外，与 XGBoost 不同，它采用了优先扩展叶子节点的策略，从而使得模型能更快收敛并获得更高的精度。不过，在一些算法上，各种模型都会相互借鉴，随着新版本的发布，也可能出现此消彼长的情况。

关于lightGBM的优化原理，我这里讲的比较简单，毕竟我们是做交易策略的，可能大家对机器学习更深入的东西，一开始没有那么大的兴趣。但是这些东西实际上非常重要。因此，如果大家有兴趣，可以参考知道上的一篇文章，链接我们放在这一章的拓展阅读中。


[click]

这是出现更晚一点的一个模型，由俄罗斯的yandex公司开发。这家公司的业务相当于中国的百度，在技术上很厉害。他们知名的产品还有clickhouse。一般认为，CatBoost 在分类任务上的表现优于 XGBoost 和 LightGBM，这是因为yandex自身业务需求决定的。catboost在能力上的侧重性，我们从它的名字上也可以看出一点端倪。这里的cat应该是category的意思

在我们的课程中，我们将只介绍xgboost和lightGBM，并且把重点放在lightGBM上。

-->
