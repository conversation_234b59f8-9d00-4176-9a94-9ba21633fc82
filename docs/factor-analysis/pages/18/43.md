---
clicks: 16
---

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 1}'>

```python
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns

# 加载数据集
url = "/data/ro/adult.data"
column_names = [
    "age", "workclass", "fnlwgt", "education", "education-num", "marital-status",
    "occupation", "relationship", "race", "sex", "capital-gain", "capital-loss",
    "hours-per-week", "native-country", "income"
]
data = pd.read_csv(url, header=None, names=column_names, na_values=" ?", skipinitialspace=True)

imputer = SimpleImputer(strategy='most_frequent')
data_imputed = pd.DataFrame(imputer.fit_transform(data), columns=data.columns)

# 确保数值列的数据类型正确
numerical_features = ["age", "fnlwgt", "education-num", "capital-gain", "capital-loss", "hours-per-week"]
data_imputed[numerical_features] = data_imputed[numerical_features].astype(float)

# 编码类别型特征
categorical_features = [
    "workclass", "education", "marital-status", "occupation", "relationship",
    "race", "sex", "native-country"
]

label_encoder = LabelEncoder()
for feature in categorical_features:
    data_imputed[feature] = label_encoder.fit_transform(data_imputed[feature])

# 编码目标变量
data_imputed['income'] = label_encoder.fit_transform(data_imputed['income'])

# 划分训练集和测试集
X = data_imputed.drop('income', axis=1)
y = data_imputed['income']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建 LightGBM 数据集
train_data = lgb.Dataset(X_train, 
                         label=y_train, 
                         categorical_feature=categorical_features,
                         free_raw_data=False)
test_data = lgb.Dataset(X_test, 
                        label=y_test, 
                        categorical_feature=categorical_features, 
                        reference=train_data,
                        free_raw_data=False)
```
</NoteCell>

<NoteCell outputMt="2rem" layout="horizontal" outputWidth="60%" class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 1}'>

```python{all|3|4|4|4|5|5|15,28|16,29|18-30|24|25|26|30|23}{at:1}
# 设置参数
params = {
    'objective': 'binary',
    'metric': ['binary_logloss', 'auc'],
    'boosting_type': 'gbdt',
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.9,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': 1
}

# 定义 early stopping 回调函数
esr = 10
evals_result = {}

num_rounds = 1000
bst = lgb.train(
    params,
    train_data,
    num_boost_round=num_rounds,
    valid_sets=[test_data],
    feval=None,
    init_model=None,
    categorical_feature=categorical_features,
    callbacks=[lgb.early_stopping(stopping_rounds=esr),
                lgb.record_evaluation(evals_result)]
)
print(evals_result)
```
</NoteCell>

<div class='abs ml-50% mt-20 w-50%' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/learning-curve.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>学习曲线</span>
</div>
</div>

<FlashText v-click="[5,6]"
           class='abs mt-50 text-center w-full text-3xl'>

gbdt | dart | goss | rf
</FlashText>

<FlashText v-click="[14,15]"
           class='abs mt-1/3 text-center w-full text-3xl'>
valid_names = ["test"]
</FlashText>


<!--
<run></run>

数据加载好了，现在我们进行训练。

[click]

这是指定目标函数。成人收入数据集是一个典型的二分类任务，目标是预测一个成年人的收入是否超过 50k，因此，我们应该选择`binary`作为目标函数。


[click]

评估函数参数中，我们这里指定了一个数组，其中包含两个评估函数，分别是`binary_logloss`和`auc`。这两个函数我们在第14课中有介绍。

metric参数只传入一个评估函数，此时以字符串格式传入。

在训练阶段，传入评估函数，作用何在？这里有好几个作用。

[click]

第一个作用是，记录训练过程中每个迭代的结果，后面可以用以绘制学习曲线。学习曲线可以直观地了解模型是否在朝着正确的方向学习，即误差是否在逐渐减小。


[click]


第二个作用是，如果我们要指定early stopping，那么是需要使用评估函数的结果的。

什么是early stopping呢? 模型在训练时，实际上自己是不知道何时应该停下来的。一般让模型训练停下来，有两种方法，第一个，是指定迭代次数，到了这个点，无论此时的模型是否是最优的，都要停下来；第二个是，如果经过一段时间的训练，发现评估函数的结果并没有发生显著变化，那么再训练下去，可能意义就不大了，此时就应该停止训练了。

因此，early stopping的实现，是需要评估函数支持的。那么early stopping在实现时

如果我们在metric这个方法中，传入了一个数组，它应该如何做决定呢？

大家猜一下？

默认请情况下，它会按第一个评估函数的结果来判断是否要停止训练。如果你要同时使用多个评估函数
的结果来共同决定是否early stopping，那么，在训练时有一个参数可以改写这个设定。大家需要的时候自己查一下。


[click]

LightGBM 支持多种 Boosting 算法，包括`gbdt`、`dart`、`goss`和`rf`。

[click]

其中 gbdt 是默认算法，可以不用指定。

其它几个值是什么意思呢？

如果需要强调防止过拟合，可以考虑使用`dart`，但训练速度可能会变慢；

对特别大的数据集，要加速训练或者减少内存占用，可以使用`goss`参数。

一般不推荐使用`rf`，`rf`在这里是随机森林的缩写。当我们使用这个参数时，实际上就放弃了提升算法。


[click]

其它的参数我们先不介绍，等到参数调优时再介绍。

这两行是指定early stopping的参数。这里esr为10，也就是说，如果metric的值在10个迭代后没有发生变化，那么就停止训练。

注意这里的10并不是推荐值，只是为了加快演示的速度。

[click]

这两行是在训练过程中，记录评估指标。lightgbm并不会默认地记录训练时，每个迭代的评估指标，需要我们手动调用一下。

这个调用也是通过指定callback函数实现的。大家看到，参数名是callbacks，所以，这里接受一个数组。

这部分代码很常用，是需要记忆的。

我们还看到，record_evaluation这个函数，接受一个类型为集合的参数。我们一般传入空集合进去，在训练结束时，这个集合中，就会包含在params中metric参数中，传入的那些评估函数的值。


[click]

最后，我们通过调用lgb.train来启动一次训练。训练集通过第2个参数传入，验证集通过关键字参数valid_sets传入。


[click]

这个参数是供我们传入自定义的评估函数的，它可以是一个函数。函数的签名大家可以在需要时，查下文档。

[click]

init_mode可以是字符串，也可以是一个lightgbm模型，或者路径。当指定时，训练将从这个模型的状态开始，继续训练。也就是说，lightgbm是支持增量训练的。

[click]

同样，我们需要继续指定categorical_feature参数，指定哪些列是类别型特征。

[click]

最后，我们看一下evals_result的内容。

我们看到，它的格式有点奇怪。它只有一个key，这个key也不太好理解，valid_0，这是什么意思呢？


[click]

答案在第23行。我们可以给训练过程中传入多个验证集。每一个验证集，都有自己的记录结果。所以，valid_0中的valid，是指验证集的意思。在调用lgb.train时，我们除了可以指定valid_sets，还可以指定valid_names参数，用来给验证集进行命名。

如果我们在这里加一个：


[click]

再次训练，我们就会发现，evals_result的key,就成了test，这一点大家自己验证下。


[click]

最后，我们解读下运行结果。

第一行，告诉我们训练数据集中，正负样本的个数。通过这个信息可以大致了解数据的类别不平衡程度，在后续分析模型性能、调整参数或者进行数据预处理时（如考虑是否要进行过采样、欠采样等操作来平衡类别）会用到

第二行是多线程相关提示。LightGBM 在这里自动选择了按行的多线程方式来进行训练，同时告知了测试这种多线程方式带来的额外开销时间是 0.04 秒。

第三行告诉我们，总的分箱数。这个数值可以作为后续调优或者理解模型对数据处理精细程度的一个参考

第4行，帮助我们理解训练数据的规模和维度

第5行，计算出模型的初始分数，是模型训练的起点，后续的迭代训练会基于这个初始状态逐步调整模型的预测值

第6行，提示我们遇到early stopping,训练结束。最后，它输出了此时的损失函数值，以及评估指标。

-->
