---
clicks: 0
---

<div class='abs mt-30 ml-50' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/random-forest.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>图片来源: miro@meium</span>
</div>
</div>

<!--

于是，人们就想到了并行训练多棵决策树，并对每个决策树的预测结果进行汇总，从而构建出一个强学习器的方法。

怎么进行汇总呢？如果是回归任务，就通过取平均值来进行汇总；如果是分类任务，就采用投票的方法。在这个图示任务中，它执行的是一个分类任务。其中两棵树的投票是B，一棵树的投票是A，因此，最后这个样本的分类就是B。

这种方法，就称为随机森林。

然而，随机森林在解决一部分问题的同时，也引入了新的问题。比如，它的训练速度较慢，资源占用更大。

另外，每一棵树之间是独立的，一方面使得整个模型的可解释性变得复杂；另一方面，也会加重类别不平衡问题。在类别不平衡的情况下，即使有少数几棵树正确地识别了样本，最终也会在投票机制中，被多数决策树的结果所主导。这是随机森林并行机制所固有的问题。

于是，人们又提出了梯度提升决策树。
-->
