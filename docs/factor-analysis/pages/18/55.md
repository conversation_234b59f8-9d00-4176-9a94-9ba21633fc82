---
clicks: 4
---

<div class='abs w-full mt-15' v-motion
     :enter='{opacity: 1}'
     :click-10='{opacity: 0}'>

```python{all|1,17|18,25|39-41|21-23}{maxHeight: '450px'}
from lightgbm import LGBMClassifier
from sklearn.metrics import accuracy_score
from sklearn.model_selection import GridSearchCV
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
import joblib

# 加载数据
iris = load_iris()
data = iris.data
target = iris.target

# 划分训练数据和测试数据
X_train, X_test, y_train, y_test = train_test_split(data, target, test_size=0.2)

# 模型训练
gbm = LGBMClassifier(num_leaves=31, learning_rate=0.05, n_estimators=20)
gbm.fit(X_train, y_train, eval_set=[(X_test, y_test)], early_stopping_rounds=5)

# 模型存储
joblib.dump(gbm, 'loan_model.pkl')
# 模型加载
gbm = joblib.load('loan_model.pkl')
# 模型预测
y_pred = gbm.predict(X_test, num_iteration=gbm.best_iteration_)

# 模型评估
print('The accuracy of prediction is:', accuracy_score(y_test, y_pred))

# 特征重要度
print('Feature importances:', list(gbm.feature_importances_))

# 网格搜索，参数优化
estimator = LGBMClassifier(num_leaves=31)
param_grid = {
    'learning_rate': [0.01, 0.1, 1],
    'n_estimators': [20, 40]
}
gbm = GridSearchCV(estimator, param_grid)
gbm.fit(X_train, y_train)
print('Best parameters found by grid search are:', gbm.best_params_)
```
</div>

<!--

lightgbm提供了两种风格的接口。我们在前面看到的都是所谓native接口，也就是属于它自己风格的接口。但是，由于sklearn社区的强大影响力，并且sklearn风格的API确实在命名和参数设计上有它的优势，所以,无论是xgboost还是lightgbm，都提供了sklearn风格的接口。


[click]

所谓sklearn风格的接口，实际上就是，先定义是分类还是回归模型。这些参数需要暴露出来，作为优化器进行优化的选项。


[click]

然后在训练时调用fit，在预测时调用predict。


[click]

并且在优化方法上，支持GridSearchCV和RandomizedSearchCV等。


[click]

最后我们看一下这个API。 joblib是之前是sklearn中的一个模块，现在已经独立成一个单独的库。作用是用来保存和加载训练模型。它是基于pickle格式的，但在内存占用上可能比pickle更高效一些。

凡是用pickle保存和加载的模型，都要注意，前后的使用环境要一致，否则就会出问题。因此，我们需要使用python的虚拟环境来运行训练

-->
