---
clicks: 12
---

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns

url = "/data/ro/adult.data"
column_names = [
    "age", "workclass", "fnlwgt", "education", "education-num", "marital-status",
    "occupation", "relationship", "race", "sex", "capital-gain", "capital-loss",
    "hours-per-week", "native-country", "income"
]
data = pd.read_csv(url, header=None, names=column_names, na_values=" ?", skipinitialspace=True)

imputer = SimpleImputer(strategy='most_frequent')
data_imputed = pd.DataFrame(imputer.fit_transform(data), columns=data.columns)

# 确保数值列的数据类型正确
numerical_features = ["age", "fnlwgt", "education-num", "capital-gain", "capital-loss", "hours-per-week"]
data_imputed[numerical_features] = data_imputed[numerical_features].astype(float)

# 编码类别型特征
categorical_features = [
    "workclass", "education", "marital-status", "occupation", "relationship",
    "race", "sex", "native-country"
]

label_encoder = LabelEncoder()
for feature in categorical_features:
    data_imputed[feature] = label_encoder.fit_transform(data_imputed[feature])

# 编码目标变量
data_imputed['income'] = label_encoder.fit_transform(data_imputed['income'])

# 确认数据类型
print(data_imputed.dtypes)

# 划分训练集和测试集
X = data_imputed.drop('income', axis=1)
y = data_imputed['income']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建 LightGBM 数据集
train_data = lgb.Dataset(X_train, 
                         label=y_train, 
                         categorical_feature=categorical_features,
                         free_raw_data=False)
test_data = lgb.Dataset(X_test, 
                        label=y_test, 
                        categorical_feature=categorical_features, 
                        reference=train_data,
                        free_raw_data=False)
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 1}'
          :click-6='{scale: 0}'
          :click-8='{scale: 1 }'>

```python{all|8|10|11|12|9|9|9|13|2-31|33-34|37|39-41}
import optuna
def objective(trial):
    # 定义超参数搜索空间
    params = {
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'num_leaves': trial.suggest_int('num_leaves', 20, 31),
        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1, log=True),
        'feature_fraction': trial.suggest_float('feature_fraction', 0.7, 1.0),
        'bagging_fraction': trial.suggest_float('bagging_fraction', 0.7, 1.0),
        'bagging_freq': trial.suggest_int('bagging_freq', 1, 10),
        'min_child_samples': trial.suggest_int('min_child_samples', 20, 100),
        'verbose': -1
    }

    # 训练模型
    bst = lgb.train(
        params,
        train_data,
        num_boost_round=500,
        valid_sets=[test_data],
        feval=None,
        init_model=None,
        feature_name='auto',
        categorical_feature=categorical_features,
        callbacks=[lgb.early_stopping(stopping_rounds=20)]
    )

    # 返回评估指标
    return bst.best_score['valid_0']['auc']

# 创建 Optuna 研究对象
study = optuna.create_study(direction='maximize')

# 运行超参数搜索
study.optimize(objective, n_trials=25)

# 打印最佳参数和最佳得分
print("Best parameters:", study.best_params)
print("Best AUC:", study.best_value)
```
</NoteCell>

<div class='abs mt-20' v-motion
     :enter='{scale: 0}'
     :click-6-7='{scale: 1}'>

```python

learning_rates = np.linspace(1e-2,1e-1,100)
scores = []
best_accuracy = 0
best_lr = None
for lr in learning_rates:
    params = {'objective': 'binary', 
              'metric': 'binary_logloss', 
              'learning_rate': lr
              }
    model = lgb.train(params, train_data, num_boost_round=50)
    y_pred = model.predict(X_test)
    y_pred_binary = np.round(y_pred)
    accuracy = accuracy_score(y_test, y_pred_binary)
    scores.append(accuracy)
    if accuracy > best_accuracy:
        best_accuracy = accuracy
        best_lr = lr
```
</div>

<div class='abs w-full ml-60 mt-20' v-motion
     :enter='{scale: 0}'
     :click-6-7='{scale: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/learning-rate-by-auc.jpg'>
</div>
</div>

<div class='abs mt-20 w-full' v-motion
     :enter='{scale: 0}'
     :click-7-8='{scale: 1}'>

<div style='width:60%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/find-lr.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>find_lr(), from fast.ai</span>
</div>
</div>

<!--

<run></run>

如何进行参数优化呢？这一节，我们将使用optuna，这是前面讲过的方法。除了讲如何把它与lightgbm结合起来之外，我们还要再了解一些参数的作用。


[click]

num_leaves，这个参数是控制树的形态的关键参数之一。它的作用是控制每棵树的最大叶子节点数。较大的值可以提高模型的复杂度和拟合能力，但也可能导致过拟合。


[click]

feature_fraction，这个参数是每次构建树时随机选择的特征比例。通过引入随机性，可以减少过拟合并提高泛化能力。在调优中，如果模型在验证集上的表现不够好，可以尝试进一步降低；如果模型的表现已经很好，可以保持当前值或适当增加，以平衡模型的复杂度和稳定性。


[click]

bagging_fraction，每次迭代时随机选择的样本比例。类似于特征子采样，样本子采样也可以减少过拟合并提高泛化能力。


[click]

bagging_freq，控制样本子采样的频率。设置为 0 表示不进行样本子采样；设置为正整数表示每隔多少次迭代进行一次样本子采样。


[click]

learning_rate控制每次迭代时权重更新的步长。较小的学习率可以使模型更稳定，但需要更多的迭代次数；较大的学习率可以加快收敛速度，但可能导致不稳定。这些是在机器学习基础理论部分讲过的内容。

学习率的确定，对复杂模型来说非常重要。在这里，optuna已经通过对数搜索来确定学习率，一般来说，这是性价比最高的一种方式。但是，如果事情进展的不顺利，我们也可以预先单独实现最佳学习率区间的搜索。


[click]

这段代码就是一段预先搜索学习率的算法。它先是定义一个学习率的线性空间，然后，以学习率为唯一参数，进行训练，并保存学习率和评估指标。

最后，在循环结束后，把auc对学习率的曲线绘制出来。

运行时间会比较长，所以，这里我们直接给运行结果。代码在课件中有，大家可以自己运行。


[click]

在高级深度学习框架中，比如fast ai中，它提供了find_lr这个方法，可以帮我们找到一个合适的学习率。算法比我们这里的要精致一些。如果大家在工作中感到有需要，可以自己去学习下。

这个图使用的损失函数对学习率，所以，我们要找梯度下降最快的区间，相当于图中的紫色点。我们寻找最佳学习率，可以在这个点附近的区间里进行尝试。
 

[click]

min_child_samples是一个用于控制决策树生长的参数。它规定了每个叶子节点（子节点）至少需要包含的样本数量。

[click]

其它部分我们在前面的课程中讲过，这里简单复习一下。核心是定义自己的目标函数，在这个函数中，我们完成模型的训练和验证，返回模型的评估指标。


[click]

在objective中，我们返回的是auc，这个值越大越好，所以，在创建study时，我们指定方向为最大化


[click]

然后我们调用optimize方法，启动优化。这里设置的trials是25，也就是会进行25轮随机搜索，但在每轮搜索下面，会有一些小的迭代。注意这个值与lightgbm没有关系。


[click]

最后， 最佳参数保存在best_params中，最佳得分保存在best_value中。如果我们对这个结果满意，就可以用这个最佳参数再次训练模型并保存。

-->
