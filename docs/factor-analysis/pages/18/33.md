---
clicks: 6
---

<NoteCell outputMt="2rem" outputMl="2rem" class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-10='{ scale: 0}'>

```python{all|7-12|15-16|18-28|29|32|34-36}{at:1}
import xgboost as xgb
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score

# 加载数据集
iris = load_iris()

X, y = iris.data, iris.target

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建 DMatrix 数据结构
dtrain = xgb.DMatrix(X_train, label=y_train)
dtest = xgb.DMatrix(X_test, label=y_test)

# 设置参数
params = {
    'objective': 'multi:softmax',  # 多分类任务
    'num_class': 3,                # 类别数
    'max_depth': 3,                # 树的最大深度
    'eta': 0.1,                    # 学习率
    'eval_metric': 'mlogloss'      # 评估指标
}

# 训练模型
num_rounds = 100
bst = xgb.train(params, dtrain, num_rounds)

# 预测
preds = bst.predict(dtest)

# 计算准确率
accuracy = accuracy_score(y_test, preds)
print(f'Accuracy: {accuracy:.2f}')
```
</NoteCell>

<!--

在 XGBoost 诞生之前，梯度提升决策树已经由 Friedman 在 1999 年提出，作为一种迭代的集成学习算法，在许多任务中表现优异，但在实际应用中存在计算效率低、难以处理大规模数据等问题。


[click]

<run></run>

这段代码演示了使用xgboost的例子。示例中我们使用了鸢尾花数据集，这里的划分api是我们之前讲过的


[click]

在训练之前，我们要通过dbmatrix来加载数据。DMatrix 是 XGBoost 中用于数据存储的一种专用数据结构。它的设计目的是高效地存储和处理大规模的数据集。可以更紧凑地存储数据，减少内存开销。


[click]

这里是一些参数设置。在这里我们不打算详细介绍每个参数，像目标函数、评估函数这样的参数，我们已经在之前的课程中介绍过，这里只是应用；其它的我们会在lightgbm中讲解。


[click]

现在开始训练。这里是调用xgb的原生接口。它也有类似于sklearn那样的接口，也就是先定义分类器或者回归器，再调用fit和predict方法这样的使用方式


[click]

这是使用训练出来的模型，进行预测。


[click]

最后，对预测结果进行评估，以检验模型的表现。

-->
