---
clicks: 10
---

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns

# 加载数据集
url = "/data/ro/adult.data"
column_names = [
    "age", "workclass", "fnlwgt", "education", "education-num", "marital-status",
    "occupation", "relationship", "race", "sex", "capital-gain", "capital-loss",
    "hours-per-week", "native-country", "income"
]
data = pd.read_csv(url, header=None, names=column_names, na_values=" ?", skipinitialspace=True)
```
</NoteCell>

<NoteCell class='abs mt-30 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
print(data.head())
print(data.info())
```
</NoteCell>

<FlashText v-click="[2,3]"
           class='abs mt-1/3 text-center w-full text-3xl'>

from sklearn.impute import SimpleImputer
</FlashText>

<div v-motion class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'>

```python{all|1-3|1-3|5-7|9-17|19-20|23|24|25|27-36|35}{at:1,maxHeight: '450px'}
# 处理缺失值
imputer = SimpleImputer(strategy='most_frequent')
data_imputed = pd.DataFrame(imputer.fit_transform(data), columns=data.columns)

# 确保数值列的数据类型正确
numerical_features = ["age", "fnlwgt", "education-num", "capital-gain", "capital-loss", "hours-per-week"]
data_imputed[numerical_features] = data_imputed[numerical_features].astype(float)

# 编码类别型特征
categorical_features = [
    "workclass", "education", "marital-status", "occupation", "relationship",
    "race", "sex", "native-country"
]

label_encoder = LabelEncoder()
for feature in categorical_features:
    data_imputed[feature] = label_encoder.fit_transform(data_imputed[feature])

# 编码目标变量
data_imputed['income'] = label_encoder.fit_transform(data_imputed['income'])

# 划分训练集和测试集
X = data_imputed.drop('income', axis=1)
y = data_imputed['income']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建 LightGBM 数据集
train_data = lgb.Dataset(X_train, 
                         label=y_train, 
                         categorical_feature=categorical_features,
                         free_raw_data=False)
test_data = lgb.Dataset(X_test, 
                        label=y_test, 
                        categorical_feature=categorical_features, 
                        reference=train_data,
                        free_raw_data=False)
```
</div>

<!--
<run></run>

首先，我们要了解dataset对象。

在讲解xgboost时，我们看到xgboost使用了一个数据结构，在训练之前，对原始数据进行转换。经过转换后，这样的数据格式更节省内存，提高了训练速度。

在lightgbm中，也存在着同样的数据结构。并且，我们前面讲过，lightgbm是在xgboost上进行优化的，其中一些优化就反应在前期的数据预处理上。


[click]

这次我们将使用成年人的收入数据集。这个数据集有14个特征，包括收入，年龄，性别，教育，职业等。它的target数据是一个字符串，取值分别为<=50k和>50k

现在我们预览一下这个数据集。


[click]

<run></run>

现在，我们看在lightgbm中，如何使用dataset。

首先，我们要通过SimpleImputer来处理缺失值。这里是使用众数策略进行填充。我们在课程比较早的时候讲过，如果是行情数据，一般使用前向填充；如果是基本面数据，一般使用行业中位数填充。

但是，要强调的是，处理缺失值，对lightgbm不是必须的。这是基于决策树类算法的一个优势。


[click]

SimpleImputer是sklearn中的一个预处理工具。在sklearn.impute模块下。它提供了均值、中位数、众数和常数填充法，同时也允许你传入一个回调函数，自己完成缺失值填充。


[click]

在成年人收入数据中，有许多特征本身是数值型的，但可能是以object格式存在的，我们需要先将它们转换为float类型


[click]

lightgbm的一大优势就是可以很好地处理类别型数据，无须事先将它们编码成为one-hot编码。不过，尽管如此，它并直接接收字符串类型的类别数据，我们必须将它转换成为离散数值型数据。这里我们使用了sklearn.preprocessing中的LabelEncoder类。这个类是我们在之前的课程中介绍过的。

要注意这一步是必须的，不少初学者在这里，会被lightgbm的文档所误导，以为lightgbm可以直接处理字符串类型的类别数据。


[click]

目标变量也是categorical的，我们也要使用LabelEncoder将其转换为数值型。


[click]

data数据中，既包含了特征数据，也包含了目标变量。所以，当我们提取特征数据时，需要通过drop方法，将目标变量从data中删除。

[click]
这一步是提取目标变量，即y


[click]

这里是通过sklearn的方法，划分训练集和测试集。

[click]

现在，我们就可以创建作为训练用的dataset和用作测试的dataset了。

请注意这里的第30行和第34行，我们特别声明了哪些字段是类别数据。如果不进行这样的声明，训练仍然可以进行，但结果肯定不够精确。这是因为，类别型特征未正确处理，就会导致特征分割不合理，训练时间延长，泛化能力也会降低。


[click]

注意这里的reference参数。它的作用是要保证类别型的特征在训练集和测试集上的编码一致。显然，如果不一致，就必然导致预测时错误。另外，两者可以共享训练集的类别编码，一定程度上减少了内存使用。

大家可能会好奇，这里为什么会是一个问题？因为数据集划分是随机的。我举一个简单的例子，如果特征有1-10共10个类别，但是在测试集中，只出现了8个，那么lightgbm如何知道是总共只有8个类别，还是有10个类别，但是只出现了8个？


-->
