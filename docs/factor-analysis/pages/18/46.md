---
clicks: 8
---

<div v-motion class='abs mt-10' 
    :enter="{ opacity: 1, width:'100%'}"
    :click-1="{width: '50%'}"
    :click-2="{width: '0%'}">

```python
# 预测
preds = bst.predict(X_test, num_iteration=bst.best_iteration)
preds_binary = [1 if pred > 0.5 else 0 for pred in preds]

# 计算准确率
accuracy = accuracy_score(y_test, preds_binary)
print(f"Accuracy: {accuracy:.2f}")

# 打印分类报告
print("\nClassification Report:")
print(classification_report(y_test, preds_binary))

# 绘制混淆矩阵
conf_matrix = confusion_matrix(y_test, preds_binary)
plt.figure(figsize=(8, 6))
sns.heatmap(
    conf_matrix,
    annot=True,
    fmt="d",
    cmap="Blues",
    xticklabels=["<=50K", ">50K"],
    yticklabels=["<=50K", ">50K"],
)
plt.xlabel("Predicted Label")
plt.ylabel("True Label")
plt.title("Confusion Matrix")
plt.show()
```
</div>

<div class='abs w-50% mt-10 ml-50%' v-motion
     :enter='{opacity: 0, scale: 0.7}'
     :click-1-2='{opacity: 1}'>

![](https://images.jieyu.ai/images/2025/01/classfication-report.jpg)
</div>

<div class='abs w-50% mt-50 ml-50%' v-motion
     :enter='{opacity: 0, scale:0.7}'
     :click-1-2='{opacity: 1}'>

![](https://images.jieyu.ai/images/2025/01/lgbm-confustion-matrix.jpg)
</div>

<NoteCell layout="horizontal" outputWidth="40%" 
          outputMt="2rem"
          class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-2-5='{ scale: 1}'>

```python{all|8|8}{at:3}
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns
import pickle

bst = pickle.load(open('/data/ro/fa-chap18-bst.pkl', 'rb'))
# 获取特征重要性, 也可以使用 'gain', 'cover', 'total_gain'
importance = bst.feature_importance(importance_type='split') 
feature_names = bst.feature_name()

# 创建特征重要性字典
feature_importance_dict = dict(zip(feature_names, importance))

# 按重要性排序
feature_importance_dict = dict(sorted(feature_importance_dict.items(),
                              key=lambda item: item[1], reverse=True))

# 打印特征重要性
print(feature_importance_dict)

# 绘制特征重要性图
plt.figure(figsize=(10, 6))
sns.barplot(x=list(feature_importance_dict.values()), 
            y=list(feature_importance_dict.keys()))
plt.title('Feature Importance (split)')
plt.show()
```
</NoteCell>

<FlashText v-click="[4,5]"
           class='abs mt-50 ml-30 text-center w-full text-3xl'>

split | gain | cover | total_gain
</FlashText>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-5-6='{ scale: 1}'>

```python
lgb.plot_tree(bst, tree_index=0, figsize=(30,20))
plt.show()
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-5-8='{ scale: 1}'>

```python{all|1|2}{at:6}
assert bst.best_iteration <= bst.num_trees()
print(bst.best_iteration)

# for i in range(bst.best_iteration):
for i in range(2):
    lgb.plot_tree(bst, tree_index=i, figsize=(30,20))
```
</NoteCell>

<div class='abs w-full mt-60' v-motion
     :enter='{opacity: 0}'
     :click-6-8='{opacity: 1}'>

```python{all|4|10}{at:6}
bst = lgb.train(
    params,
    train_data,
    num_boost_round=500,
    valid_sets=[test_data],
    feval=None,
    init_model=None,
    feature_name='auto',
    categorical_feature=categorical_features,
    callbacks=[lgb.early_stopping(stopping_rounds=20)]
)
```
</div>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-8-9='{ scale: 1}'>

```python
lgb.plot_split_value_histogram(bst, feature="age", bins="auto")
```
</NoteCell>

<!--
我们刚刚训练的是一个分类模型，因此，我们就可以使用sklearn中，已经学习过的评估方法来进行评估。比如，可以绘制困惑矩阵。


[click]

这是我们之前已经熟悉的报告了。


[click]

<run></run>


如果你的训练集包含了100个因子，用机器学习的语言来说，包含了100个特征，那么，很可能你会好奇，这100个特征，它们是同等重要的吗？

此时，我们就可以借助feature_importance方法来获得相关的信息。

[click]

importance_type参数有4种可能的取值，分别是


[click]

这里我们使用的是split，它表明，特征在决策树分裂过程中的次数。

这里的gain，大家应该能猜到是什么意思吧？它的含义是增益，就是信息增益。是我们在这节课开篇时就讲到的。

cover是什么含义呢，比如，在一个有 1000 个样本的数据集上构建 LightGBM 模型。特征 B 在所有决策树分裂过程中，总共涉及了 700 个样本，而其他特征涉及的样本数量较少。这意味着特征 B 的cover类型的重要性得分会较高。这个叫cover

其它的部分我们就不解释了，这基本上是一个绘图问题。


[click]

<run></run>

lightgbm是树模型，因此，我们也希望绘制出LightGBM的树结构，以便清晰地看到决策过程。

lightgbm中，我们只能一棵树一棵树地绘制，没有办法绘制总览图。因此，我们在调用Plot_tree时，需要传入tree_index.

如果我们是在训练一个交易策略，使用的特征是技术因子，你就能清晰地看到，最终的决策是如何通过这些技术因子来决定的。

在这个示例中，我们只显示了第0棵树的结构。一个训练好的模型中，应该有多少棵树呢？


[click]

在标准的梯度提升模型中，一轮训练就应该生成一棵树，所以，在训练时，传入的 num_boost_round的数量就决定了树的数量的上限。这个值，可以通过num_trees来获取 。


[click]

如果我们使用了early stop方法，那么，树的上限将会由best_iteration返回，这个数值是小于等于num_boost_round的


[click]

<run></run>

我们前面说，lightgbm是按直方图进行特征分裂的，究竟是怎么一回事？我们通过这个可视化方法来进行解释。

在绘制图形时，我们通过feature参数，传入要呈现的特征。在绘制出来的图形中，横坐标表示特征的分裂值（split values）。这些值是决策树在该特征上进行节点分裂的依据，或者说，进行样本划分的边界点。

纵坐标则代表该分裂值出现的频率或者次数，它表明该特征的某个分裂值进行节点分裂的频繁程度。较高的纵坐标值意味着在构建决策树时，该分裂值被更多次地用于划分数据。

比如，在图中，最高的bar分别大约为28和36，表明在这个年龄前后，人们的收入差异扩大，而70~90之间的bar最低，这说明什么？这说明在这个年龄段，大家的收入差异就很小了。

从形态上看，如果直方图是单峰的，说明模型在该特征上倾向于使用某个特定区域的分裂值。如果出现多峰分布，则说明模型在该特征的多个不同区间都频繁地进行分裂，即该特征与目标变量之间存在复杂的非线性关系。

-->
