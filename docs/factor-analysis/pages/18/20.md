---
clicks: 4
---

<div class='abs' v-motion
     :enter='{opacity: 1, scale: 1,x:0}'
     :click-1='{scale: 0.5,x:200}'
     :click-5='{opacity: 0}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/iris-decision-tree-plot.jpg
'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-1-5='{opacity: 1}'>

### 过拟合
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-2-5='{opacity: 1}'>

### 过拟合
### 异常值敏感
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-3-5='{opacity: 1}'>

### 过拟合
### 异常值敏感
### 单棵决策树的预测能力有限
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>

### 过拟合
### 异常值敏感
### 单棵决策树的预测能力有限
### 无法捕捉时间依赖性
</div>


<!--
决策树的结构直观清晰，就像一个流程图。以这张图为例，这是在鸢尾花数据集上进行分类任务，训练出来的一棵决策树。

它的可解释性非常好，非专业人士也能轻松看懂决策过程。决策树也可以处理复杂的非线性因素（比如股价走势），即使数据集中有缺失值和不同量纲的数据也能很好处理。


[click]

但决策树也有明显的缺点。首先是容易过拟合。决策树在构建时，会考虑每一个样本，会尽量细分数据，使得每个叶节点的样本尽可能纯。因此如果我们不对分枝树进行控制的话，过拟合几乎是必然的。但是，分支树又是凭经验才能判断出来的一个超参数


[click]

同样是由于决策树在构建时，会考虑每一个样本，因此它对异常值也是敏感的。我们在第13课，讲过，对于它没见过的数据，按gini系数进行分裂时，它会通过均值来确定两个节点的边界值。因此，这个分裂点就会严重受异常值影响。


[click]

单棵树很难拟合出来复杂的非线性函数。这一点是需要集成学习方法来提升的


[click]

无法捕捉时间依赖性。决策树能较好地从表格型数据中学习到规律。表格数据的特点，一般是一个样本占一行，列为其特征。所以，在这种结构中，天然也是不存在时间序列信号和特征的。但是，我们在进行量化交易，运用决策树模型时，就会潜在地把时间序列数据当成表格型数据处理，所以会让决策树去完成超出它能力范围内的任务。

不过，关于这一点，今天我们要介绍的决策树的各个变种，都无法解决这一困难。


-->
