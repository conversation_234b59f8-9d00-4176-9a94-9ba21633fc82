---
clicks: 5
---

<div class='abs w-full text-center mt-25' v-motion
     :enter='{opacity: 1,x:0, scale: 1}'
     :click-1='{scale: 0.7, x:-250}'
     :click-3='{scale: 0}'>
<div style='width:66%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/kaggle-logo.jpeg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs left-50% w-40% mt-42' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>
<div style='width:100%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/kaggle-competition-janestreet.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs left-50% mt-55' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>

* <mdi-television-shimmer class='text-red-400 animate-bounce mr-2' />Since 2018
* <mdi-wifi-marker class='text-yellow-400 animate-bounce mr-2' />40 Hours/Week
* <mdi-devices class='text-blue-400 animate-bounce mr-2' />GPU Type: T4*2 | P100 | V3-8
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/kaggle-switch-gpu.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs ml-30 mt-20' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>
<div style='width:60%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/kaggle-model-download.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>


<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{opacity: 1}'>

```bash
pip install kaggle

kaggle datasets download -d your_username/your_dataset
```
</div>


<!--
另外一个方案就是kaggle。这是一家2010年成立于澳大利亚墨尔本的公司。主要为数据科学家、机器学习工程师和数据分析师等提供举办机器学习竞赛、托管数据库、编写和分享代码的平台。

kaggle非常重要，哈佛、牛津、斯坦福等名校均有开设 Kaggle-in-class 课程项目。在kaggle竞赛中取得好成绩会为求职、申请学校、科研项目等增加竞争力


[click]


在kaggle上也常常有量化竞赛，我们在教材中附有链接。当前正在进行中的一个比赛就是简街的，学完了我们的课程，大家可以试试看，应该很有竞争力了。


[click]

Kaggle也是大约从2018年起提供免费的GPU算力.

现在，一个新注册用户大约每周可以免费使用40小时的GPU。但在使用GPU之前，一般需要先进行手机短信验证。

[click]

跟colab一样，缺省状态下，使用CPU进行计算。我们可以先在这个环境下，把程序调通。然后再切换到GPU。

这个截屏显示了如何进行切换。

[click]

使用kaggle，我们也会面临与colab一样的问题,就是如何上传自己的数据，如何下载训练好的模型。

kaggle允许我们自己创建数据集。数据集可以被多个notebook，甚至其它用户使用。也可以在notebook的右侧面板中，直接上传文件。

这个截图显示了如何操作。如果你已经创建了数据集，就可以点击Add Input来使用已经定义的数据集。如果数据只被这一个notebook使用，那么就可以直接上传文件。

notebook已经连接的数据文件会出现在/kaggle/input/目录下。

要下载数据或者模型，可以在这个面板的output一节中，选择文件，通过界面操作来下载。

[click]

如果模型太大，通过浏览器下载就可能意外中断。kaggle也提供了通过API来下载的方式。

我们要先安装kaggle这个python库，然后通过它提供的命令来进行下载。当然，我们要先申请API token，这样才能通过鉴权。
-->
