---
clicks: 5
---

<div class='abs mt-20' v-motion
     :enter='{scale: 1, x: 150}'
     :click-1='{scale: 0.6, x:-100}'
     :click-6='{scale:0}'>

<div style='width:100%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/apple-m1-neural-engine.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs ml-50% mt-35 w-50%' v-motion
     :enter='{opacity: 0}'
     :click-1-3='{opacity: 1}'>

```python{all|7,13}{at:2}
import torch

if __name__ == '__main__':
  # Check if MPS backend is available in PyTorch
  if torch.backends.mps.is_available():
      # Create a vector on the MPS device
      x = torch.ones(1, device='mps')
      print(x)
  else:
      print('MPS device not found.')


model.to('mps')
```
</div>

<div class='abs ml-50% w-50% mt-40' v-motion
     :enter='{opacity: 0}'
     :click-3-6='{opacity: 1}'>

### 性能： Apple m4 $\approx RTX 3070 \times 0.5$
### 统一内存模型
### Torch, Tensorflow, Onnx
</div>

<!--
接下来我们介绍一个可能很多人还没意识到的方案。就是很多人可能已经有了apple的sillicon芯片的机器，但不知道它已经可以用来跑深度学习了。因为大家可能从来没听说过，mac的机器有显卡这一说。

2021年，Apple发布了基于arm的m1芯片，随后又相继推出m2和m4等芯片。这些芯片有着强大的GPU计算能力。

[click]

使用条件，首先python要3.9以上，并且mac os要在12.3以上。mac 的m系列支持三种GPU计算的后端，分别是mps, mlx和coreml.其中使用mps可能是兼容性最好的一种。

这段代码能用来检测你的系统是否支持mps. 我现在使用的还是apple m1的笔电，这个检测是能通过的。


[click]


怎么用呢？首先需要在定义每一个张量时，将设备指定为mps，最后通过model.to指定训练设备。这跟我们用CUDA上运行是一致的。

[click]

Arm系列的GPU性能没有cuda强，但也不弱。以mac mini m4的芯片来说，大致相当于RTX 3070的50%。


[click]


但是，它支持统一内存模型，也就是可以拿内存当显存用。这样，一些对显存要求高一些的模型，在推理阶段，就可以使用apple arm系列来运行。


[click]

目前它支持的深度学习框架有torch, tensorflow和onnx，还是比较全面的。

-->
