---
clicks: 4
---

<div class='abs mt-30 ml-50' v-motion
     :enter='{opacity: 1}'
     :click-4='{opacity: 1}'>
<div style='width:60%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/reinforcement-interpret.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>
<div class='abs mt-20 ml-5' v-motion
    :enter='{opacity: 1}'
    :click-4='{opacity: 0}'>

## <span v-motion :enter='{textShadow: ""}' :click-1-2='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>状态空间</span>
## <span v-motion :enter='{textShadow: ""}' :click-2-3='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>动作空间</span>
## <span v-motion :enter='{textShadow: ""}' :click-3-4='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>奖励函数</span>
</div>
 
<!--

强化学习有以下核心概念

[click]

状态空间：它是环境中所有可能状态，在量化交易中，可以看成是价量、指标、持仓、市场氛围等


[click]

动作空间：它是环境对智能体可用的动作，在量化交易中，可以看成是仓位、开平仓、止盈止损等

[click]

奖励函数：它是智能体在每个状态下对环境的反馈，在量化交易中，可以看成是收益、loss、交易成本等

从理念上看，强化学习并不预测价格，它只是尝试尽可能地从环境中获得最大收益。这一点很重要。这表明它有可能穿越牛熊，并及时对市场做出反应。

[click]

具体要怎么实现呢？上周跟一位私募合伙人交流了一下。他之前在美国管理过基金，回国后在某FOF基金工作。这两年亲自下场，业绩相当不错。他们的策略，我想大家很快就会在路演中看到披露，那时候我才方便讲得更详细一些。

在这里，我只能透露这样一些信息，第一，圈内比较领先的私募确实都在研究强化学习做交易。
第二点，大道至简。他们有一个很简洁高效的架构，就定义若干个agent，每个agent就是一个交易员，但它们有自己的思想和策略。比如，有的交易员只会做止损止盈操作，有的只会做突破后的跟进，有的只会做某个技术指标。

然后让强化学习框架来训练它们，给它们分配仓位。如果交易员A现在仓位10%，下一步仓位为20%，那么交易员A就要执行买入操作。如果下一步仓位为0%，那么就要执行清仓操作。这样，功夫主要看如何实现agent。这些Agent是他自己在实现，融入了他多年的交易实战经验，强化学习框架部分倒是可以让别人来写。

好，关于强化学习我就讲完了。从CCN到transformer到强化学习，我们讲的越来越简单，为什么呢？

-->
