---
clicks: 22
---

<div class='abs mt-10 w-full' v-motion
     :enter='{opacity: 1}'
     :click-22='{opacity: 0}'
     >

```python{all|2|4-6|8|11-12|14-15|18|19|21|22|24-25|26-27|20,25,28|31|34|38|39|40|39-43|45-47|50-57|59-73}{maxHeight: '450px'}
def train(self, barss, epochs=50, batch_size=64,patience=5):
    features, labels = self.features_and_labels(barss)

    train_data, val_data, test_data = self.train_test_split(
        features, labels, bs=batch_size
    )

    self.build_model()

    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(self._model_.parameters(), lr=0.001)

    best_val_loss = float('inf')
    early_stop_counter = 0

    # 训练模型
    for epoch in range(epochs):
        self._model_.train()
        running_loss = 0.0
        for batch_features, batch_labels in train_data:
            optimizer.zero_grad()
            # 展平卷积层的输出
            outputs = self._model_(batch_features).view(batch_features.size(0), -1)
            loss = criterion(outputs.squeeze(), batch_labels.float())
            loss.backward()
            optimizer.step()
            running_loss += loss.item()

        # 打印每个epoch的损失
        print(f"Epoch [{epoch+1}/{epochs}], Loss: {running_loss/len(train_data):.4f}")

        # 验证模型
        self._model_.eval()
        val_loss = 0.0
        val_preds = []
        val_true = []
        with torch.no_grad():
            for val_features, val_labels in val_data:
                val_outputs = self._model_(val_features).view(val_features.size(0), -1)
                val_loss += criterion(val_outputs.squeeze(), val_labels).item()
                val_preds.extend(val_outputs.squeeze().cpu().numpy())
                val_true.extend(val_labels.cpu().numpy())

        val_loss /= len(val_data)
        val_mape = mean_absolute_error(val_true, val_preds)
        print(f"Validation Loss: {val_loss:.4f}, Validation MAPE: {val_mape:.4f}")

        # Early stopping
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            early_stop_counter = 0
        else:
            early_stop_counter += 1
            if early_stop_counter >= patience:
                print(f"Early stopping at epoch {epoch+1}")
                break

    # 模型评估
    self._model_.eval()
    test_loss = 0.0
    test_preds = []
    test_true = []
    with torch.no_grad():
        for test_features, test_labels in test_data:
            test_outputs = self._model_(test_features).view(test_features.size(0), -1)
            test_loss += criterion(test_outputs.squeeze(), test_labels).item()
            test_preds.extend(test_outputs.squeeze().cpu().numpy())
            test_true.extend(test_labels.cpu().numpy())

    test_loss /= len(test_data)
    test_mape = mean_absolute_error(test_true, test_preds)
    print(f"Test Loss: {test_loss:.4f}, Test MAPE: {test_mape:.4f}")
```
</div>


<FlashText v-click="[10,11]"
           class='abs mt-40 ml-50 text-center w-full text-3xl'>

nn.Module \_\_call__
</FlashText>

<div class='abs w-full mt-20' v-motion
     :enter='{opacity: 0}'
     :click-22-23='{opacity: 1}'>

```markdown
Epoch [1/50], Loss: 0.0008
Validation Loss: 0.0006, Validation MAPE: 0.0168
Epoch [2/50], Loss: 0.0008
Validation Loss: 0.0006, Validation MAPE: 0.0170
Epoch [3/50], Loss: 0.0008
Validation Loss: 0.0006, Validation MAPE: 0.0167
Epoch [4/50], Loss: 0.0008
Validation Loss: 0.0007, Validation MAPE: 0.0169
Epoch [5/50], Loss: 0.0008
Validation Loss: 0.0006, Validation MAPE: 0.0166
Epoch [6/50], Loss: 0.0008
Validation Loss: 0.0007, Validation MAPE: 0.0169
Early stopping at epoch 6
Test Loss: 0.0007, Test MAPE: 0.0166
```
</div>

<!--
接下来我们看看如何进行训练。这段代码的框架不仅可用以cnn网络的训练，它实际上也是torch的一个通用的训练框架。


[click]

这是提取特征和标签

[click]

进行数据集划分，以及转换为Dataloader

[click]

这是我们刚刚讲过的，构建模型。

[click]

定义损失函数和优化器。优化器是我们新接触的概念，在讲机器学习时，我们讲过有好几种优化算法，比如SGD，Adam，等等。但是在lightGBM中，我们并没有显示地生成过优化器。这是因为lightGBM只有一种优化算法，就是梯度提升。

我们说深度学习要比机器学习复杂，表现在哪些方面？这就是其中之一。


[click]

这两行是为了实现early stopping。


[click]

在lightgbm中，我们只需要定义要训练的轮数，框架会自动帮我们做循环。torch是一个比较底层的框架，给了我们很大的控制权，所以，这个按训练轮数的循环也要自己来实现。

在每一轮的循环中，要做哪些事情呢？我们要按批次加载数据。计算每一批的损失，并且将所有批准的损失加总，作为一轮的损失。

[click]

这里的train方法的作用，也与之前我们在lightgbm中看到的不一样。在torch中，模型有两种模式，即训练模型和评估模式。在驱动它工作之前，我们必须主动设置它的模式。所以，这一行并不是在训练，而只是切换了模型。

这两种模式有何区别呢？比如，在训练模式下，Dropout层会随机丢弃一部分神经元，以防止过拟合。而在评估模式下，Dropout层不会丢弃任何神经元，而是保持所有神经元激活。此外，还有一些其它的区别。

[click]

现在，我们在一轮训练中，分批进行迭代。

[click]

作用是清空优化器中的梯度缓存。在每个batch开始时，需要清空之前的梯度，以避免梯度累积。如果不清空，梯度会累加，导致模型训练不稳定。

[click]

注意看这里有一个比较奇怪的地方，就是model本来是一个对象，而不是一个函数。但在这里，我们却是把它当函数使用的。

为什么可以这么用呢？这是因为nn.Module的实例都有一个__call__方法，有这个方法的对象，都能当成函数用。

nn.Module的call方法要做哪些事情？这个方法实际上会调用forward等其它方法。调用forward方法的结果就是返回最终的输出，相当于sklearn中的predict。

这里的view方法的作用是将卷积层的输出展开为二维张量，以便后续计算损失。这里只是展示一下view的用法，由于下一行对outputs调用了squeeze方法，所以实际上，这里对view的调用不是必须的。

[click]

这两步就是进行训练了，step方法的作用是更新梯度。实际上，前向传播、反向传播和参数更新一起，共同组成了训练。

[click]

我们刚刚计算的损失是每一个批次的损失，这里是要计算每一轮训练的损失。epoch与batch的区别在于，在一个epoch中，所有的数据都得到了一次训练。

[click]

这里输出了每一轮训练的损失。

[click]

在每一轮训练完成之后，我们需要进行一次验证，像early stop等等功能，就要在这个阶段实现。在验证之前，我们要先设置为评估模式。这一行就是起到这个作用。

[click]

我们需要暂时禁用梯度计算。这是因为，接下来的代码会调用前向传播，而前向传播就会计算梯度。但此时计算梯度是不必要的，会浪费算力。

[click]

这里也有一个循环。跟之前的循环不一样的是，它是按单条记录进行迭代的，而不是按批迭代。
与lightGBM相比，这些操作都过于底层和繁琐。所以，为了简化这些操作，有人开发了一个名为fastai的项目，对这些进行了封装。fastai是相对于torch的。对另一个深度学习框架而言，对应的是keras。

[click]

这是计算每一条记录的损失。

[click]

最终，我们计算了在整个验证集上的损失，得到了预测值

[click]

有了预测值和真值，我们就可以计算出绝对百分比误差。这也是使用sklearn的评估方法。所以，sklearn在人工智能有着基础的定位，无论是机器学习还是深度学习，我们都绕不开它。

[click]

这段是实现early stopping。

[click]

这是整个训练完成之后，在测试集上进行评估。

到此为止，我们就完成了在torch中的训练流程。大致上，整个流程是进行特征提取、训练数据集的构建、模型定义和训练这些步骤，与机器学习相对应。

但是，torch是一个偏底层的框架，我们看到，在每一个阶段，它都允许我们进行非常灵活的定制，因此代码量也更大、更容易出错。

[click]

这个模型层数比较小，所以，如果输入数据量不大，也能较快出结果。在我们的课程环境中，没有给大家安装torch，不推荐大家去运行这段代码。但是，如果你确实好奇想试试，也可以自己安装torch，运行一下。

运行我们课程的示例，大概需要35秒钟。

-->
