---
clicks: 4
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>
<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/cs231n-2019-lecture-5-40.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>


<div class='abs w-full text-center text-6xl mt-45' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

层次化抽象和模式规纳
</div>

<div class='abs mt-15 ml-10' v-motion
     :enter='{opacity: 0}'
     :click-2-5='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/rotation-scale-translation-invariance.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<FlashText v-click="[3,4]"
           class='abs mt-60 text-center w-full text-3xl'>

平移不变性 缩放不变性 旋转不变性
</FlashText>

<!--

在我们刚刚的示例中，我们得到的MAPE大约是1.7%。我们在前面分析过，这个数值是不理想的。这究竟是我们的网络设计得太简单，还是CNN本身有着某种固有的不足，从而导致它不适合在量化交易中实现回归任务呢？


[click]

我的看法是，CNN不适合实现回归任务。因为CNN的本质是层次化抽象和模式规纳，它不适合从时间序列中，发现类似如果过去怎么样，现在环境和状态又怎么样，所以未来会怎么样的规律。

但是，如果我们使用CNN来做k线模式识别会如何？这方面值得大家去探索。在量化研究中，我们要敢于研究别人做得少的工作。人迹罕至的道路，从看风景的角度，往往能提供更好的旅程。

但是，很可能我们要把CNN与其它网络、或者梯度提升决策树这样的模型联合起来，才能得到更精确的结果。注意我这句话的意思。

[click]

CNN是一个高度容缺的网络。即使图像中有缺失、有噪声、有变形，它也能很好的处理，并得到正确的结果。

这个图也很好地说明了这个意思。CNN网络天然地被设计成为，数字3无论被旋转、被平移还是被缩放，还是增加了噪声，它都应该被识别成为数字3。

[click]

这被称为图像语义上的平移不变性、旋转不变性和缩放不变性。这是一种伟大的能力。但如果用错了地方呢？

比如，在量化交易中，很多时候，两个模式之间可能有99%以上的相似度，但最后的1%才决定了两种模式的区别。但CNN天生会忽略这1%的细节。

比如，旗形整理与三角形整理，区别仅在于在进入整理之前，是否有一个快速下跌或者上涨的过程，即是否有旗杆。如果一个旗形整理由20个bar组成，那么旗杆部分往往只占一到两个bar，这就容易被CNN容错。

[click]

又比如， 如果我们把左上的图逆时针转90度，它就是一个M头；如果顺时针转90度，它就是一个W底。而CNN网络被要求将这三个图，都识别成为数字3！

所以，CNN的架构有很多可以值得借鉴的地方，特别是它的层次化抽象，很类似于量化交易中的不同级别的周期。但是，你很可能得自己设计一些新的架构。

当然，无论如何，如果你希望更深入地使用深度学习来进行量化交易，都可以从CNN入手。因为CNN网络出现的最早，对它的解剖是最充分的，也容易可视化，所以，是一个非常好的起点。
-->
