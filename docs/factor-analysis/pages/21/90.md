<div class="mt-10 ml-5">

## 免费的算力
## CNN网络
## Transformer
## 强化学习
</div>

<!--
今天我们主要讲了这样几个方面的内容。重点是通过CNN网络，介绍了如何通过pytorch来构建象cnn, transformer这样的深度学习模型。

由于标注数据困难，端到端的预测成功率很低，所以，这一章的例子，主要是供大家学习深度学习模型使用，实战价值并不大。

最后，介绍了一下业内在跟进的强化学习方向。随着算力成本下降，技术的扩散，现在做强化学习研究，也许有不错的收获。但总体上来说，现阶段基于梯度提升的决策树模型应该是性价比最高的方式。

到这里为止，我们的课程就结束了。我们的两门课程会继续维护，但今年的重点会放在升级我们的量化框架上，希望能为大家提供一个性能更高、维护和使用成本更低廉的量化框架，欢迎大家持续关注。

由于今天是最后一课，所以问答时间我们留长一点。希望交流的同学可以留下来继续交流。
-->
