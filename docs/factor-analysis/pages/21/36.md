---
clicks: 9
---

<div class='abs w-full mt-10' v-motion
     :enter='{opacity: 1}'
     :click-10='{opacity: 0}'>

```python{all|2|3|4|5|13|16-22|17|18|23}{at:1}
def build_model(self):
    model = nn.Sequential(
        nn.Conv1d(self.channels, 32, kernel_size=8),
        nn.ReLU(),
        nn.MaxPool1d(2),

        nn.Conv1d(32, 64, kernel_size=5),
        nn.ReLU(),
        nn.MaxPool1d(2),

        nn.Conv1d(64, 64, kernel_size=3),
        nn.ReLU(),
        nn.AdaptiveAvgPool1d(1),  # 这一步会将每个样本压缩成一个特征向量
    )

    fc = nn.Sequential(
        nn.Flatten(),  # 添加展平层
        nn.Linear(64, 16),
        nn.ReLU(),
        nn.Linear(16, 1)
    )

    self._model_ = nn.Sequential(model, fc)
```
</div>

<!--

首先要了解torch中，用来定义CNN网络的一些基本要件。当然有一些也不仅仅是CNN网络在用。比如ReLu， Linear等等，几乎所有网络都在用。


[click]

Sequential是一种线性组装器。它把多个网络组件连接起来。它还有一个重要功能，就是实现前向传播。这个概念我们就不仔细介绍了。


[click]

这是1维卷积。

[click]
这是激活层


[click]

这是池化层。它是选取窗口中的最大值作为输出值，把其它数值作为噪声过滤了。这也是神经网络神奇的地方，完全是大力出奇迹。通过这样一些方法，加上梯度优化、前向传播、反向传播，就实现了权重网络的特征化。感兴趣的同学可以自己多体会下。

[click]

这是另外一种池化方案。主要作用是对输入数据进行自适应的平均池化操作，能够将输入数据的维度调整为指定的输出大小，而无需手动指定池化核大小、步长等参数

[click]

这是在定义全连接层。全连接层是模型中的最后一层。之前的的各层，学习到的特征都是局部的、分散的。只有通过全连接层，才能将之前各层提取的局部特征进行整合，把不同位置、层次的特征组合起来，形成对输入数据的全局特征表示，使网络可以从整体上理解输入数据的结构和模式。

[click]

在数组维度上，之前都是多维的数据，在这里要转换成为一维。然后才能进行全连接。

[click]
实现全连接中的线性变换。一般会与激活函数配合使用。

[click]

最后，我们将所有这些构建组合成一个模型。在这里我们把全连接层与其它部分分开，是因为全连接层一般比较固定，方便调试和扩展。

-->
