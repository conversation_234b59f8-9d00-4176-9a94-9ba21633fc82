---
clicks: 18
---

<div class='abs mt-10' v-motion
     :enter='{opacity: 1}'
     :click-20='{opacity: 0}'>

```python{all|8|30|44|86|30-42|33|34|35-37|35-37|36-37|38,40-42|86-123|87-94|97|98-101|102|8-27|104-123}{maxHeight: '450px'}
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.metrics import mean_absolute_error

# 定义 Transformer 编码器模型
class TransformerEncoderModel(nn.Module):
    def __init__(self, input_dim, d_model, nhead, num_layers, output_dim):
        super(TransformerEncoderModel, self).__init__()
        self.embedding = nn.Linear(input_dim, d_model)
        self.pos_encoder = PositionalEncoding(d_model)
        encoder_layers = nn.TransformerEncoderLayer(d_model, nhead)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layers, num_layers)
        self.decoder = nn.Linear(d_model, output_dim)

    def forward(self, src):
        batch_size, seq_len, input_dim = src.shape
        # 调整形状以适合线性层
        src = src.view(batch_size * seq_len, input_dim)
        src = self.embedding(src)
        # 恢复形状
        src = src.view(batch_size, seq_len, -1)
        src = self.pos_encoder(src)
        output = self.transformer_encoder(src)
        output = self.decoder(output[:, -1, :])  # 取最后一个时间步的输出
        return output

# 定义位置编码类
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe.unsqueeze(0))

    def forward(self, x):
        x = x + self.pe[:, :x.size(1)]
        return x

def train_test_split(X, y, seq_len, cuts=(0.7, 0.2)):
    train_size, val_size = cuts
    # train test split
    total = len(X)
    X = X[["open", "high", "low", "close", "volume"]]
    train_pos = int(total * train_size)
    val_pos = train_pos + int(total * val_size)

    assert train_pos > seq_len

    X_train = []
    y_train = []
    for i in range(seq_len, train_pos):
        data = X.iloc[i-seq_len:i]
        # normalize
        data = data/data.iloc[-1]
        X_train.append(data.values)
        y_train.append(y.iloc[i])

    ...

    return (torch.tensor(X_train, dtype=torch.float32),
            torch.tensor(y_train, dtype=torch.float32).unsqueeze(1),
            torch.tensor(X_val, dtype=torch.float32),
            torch.tensor(y_val, dtype=torch.float32).unsqueeze(1),
            torch.tensor(X_test, dtype=torch.float32),
            torch.tensor(y_test, dtype=torch.float32).unsqueeze(1))
    
def eval(model, X_test, y_test):
    with torch.no_grad():
        outputs = model(X_test)
        mae = mean_absolute_error(y_test.numpy(), outputs.numpy())

        df = pd.DataFrame({
            "actual": y_test.numpy().flatten(),
            "predicted": outputs.numpy().flatten()
        })

        display(df)
        
        print(f'MAE: {mae:.02%}')

def train(start, end, symbol, epochs = 100):
    barss = load_bars(start, end, (symbol, ))
    bars = barss.xs(symbol, level=1)

    # 提取收盘价
    target = bars.price.pct_change().shift(-1).dropna()

    X_train, y_train, X_val, y_val, X_test, y_test = train_test_split(bars[:-1], target, seq_len=30)
    print(X_train.shape, X_val.shape, X_test.shape)

    # 初始化模型
    input_dim = X_train.shape[2]
    d_model = 128
    nhead = 8
    num_layers = 2
    output_dim = 1
    model = TransformerEncoderModel(input_dim, d_model, nhead, num_layers, output_dim)

    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    # 训练模型
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X_train)
        loss = criterion(outputs, y_train)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            val_outputs = model(X_val)
            val_loss = criterion(val_outputs, y_val)
            mae = mean_absolute_error(y_val.numpy(), val_outputs.numpy())
        
        print(f'Epoch {epoch + 1}/{epochs}, Train Loss: {loss.item():.3f}, Val Loss: {val_loss.item():.3f}, Val MAE: {mae:.02%}')

    eval(model, X_test, y_test)

train(datetime.date(2023, 1, 1), datetime.date(2023, 12, 29), "000001.XSHE")
```
</div>

<div class='abs mt-65 ml-120' v-motion
     :enter='{opacity: 0}'
     :click-9-10='{opacity: 1}'>
<div style='width:70%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/position-encoding-transformer.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs ml-100 mt-40' v-motion
     :enter='{opacity: 0}'
     :click-10-11='{opacity: 1}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/hilbert-transform.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<!--

下面，我们就介绍如何transformer架构来构建一个股票价格预测模型。这个模型没有任何实用价值，主要是帮助大家理解原理。这个例子在我们的课件环境中也是可运行的，不过速度可能有点慢，所以，我们也不推荐大家运行。

[click]


在这个例子中，主要组件有EncoderModel本身。在语言模型中，一个transformer架构常常由两个编码器组成，encoder和decoder，但在量化交易中，一般只需要使用encoder就够了。


[click]

位置编码器需要自己来实现。

[click]

数据集分割、转换数据格式总是需要的。


[click]
最后，就是如何对模型进行训练。

[click]

接下来我们就深入到具体的细节 之中。

根据上一张slide的介绍，我们是不需要构建embedding层的，因为embedding的目标是为了将语言文字编码。而股票价格本身就是浮点数，可以直接进行训练。当然，一些预处理，比如缩放还是需要的。

所以，我们首先要自己构建的，是位置编码器。

[click]
这是为将要生成的位置编码申请内存。

[click]

这是生成位置。这样生成的位置是一个从0开始，直到max_len的整数序列。我们知道，这样一个序列是没有办法参与训练的，对吧？因为跟其它特征比，量纲不对。

[click]

所以，接下来的三行就很关键，它将通过sin 和 cos 来将位置编码归一化，同时，非常巧妙的是，这两个函数又是周期性的。通过给于不同的频率，它们的周期长度就会不一样，所以，就能很好地捕获特征中重复出现的规律。比如，诗词音乐，都有同期性的重复，但不同的格律，不同的词牌，对重复的周期要求不一样。这些就非常适合用这种编码方式来学习。

[click]

这个图我们之前见过一次。这三行代码就是它的一个实现。

[click]

这两行是分别把计算的特征值中，正弦赋值给偶数列，余弦赋值给奇数列。同时使用正弦和余弦编码，可能是受到希尔伯特变换的启发。

[click]

位置距离并不是模型参数，但需要参与计算，所以，要将它注册成缓冲区。

然后在前向传播中，与传入的特征数据一起进行计算。


[click]

现在，我们来看看如何训练这个模型。

[click]

这是拿到行情数据，提取特征和标签，进行划分。注意这里我们没有进行任何缩放操作，这是会影响到模型表现的。

[click]

这是输入特征的维度。在这里我们使用了OHLC和volume等5个维度的数据。

[click]

d_model是模型的维度，也就是每个时间步、或者说每个位置的维度，nhead是多头注意力的头数，num_layers是编码器的层数，output_dim是输出维度。

[click]

现在，是构建模型的一步。

[click]

我们看看这个模型是如何构建的。我们看到，它有embedding层，位置编码层，编码器，解码器，以及线性层。

[click]

最后，训练过程跟之前的CNN差不多。这里就不逐行解释了。

这个模型训练下来，绝对平均百分比误差在3~5%左右。可以改进的点很多，比如，要对特征数据进行缩放，统一量纲，增加更多的编码器层，增加更多的特征维度，等等。跟一般的教程只使用一个输入维度不一样，这个示例已经使用了5个输入维度。因此，我们可以很容易地把像RSI,macd这样的技术指标也加入进来。

如果它还能优化，为什么我不提供一个表现更好的例子呢？这个我们放到最后讲。

-->
