---
clicks: 6
---

<div class='abs mt-20' v-motion
    :enter='{opacity: 1}'
    :click-6='{opacity: 0}'>

## <span v-motion :enter='{textShadow: ""}' :click-1-2='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>Embedding</span>
## <span v-motion :enter='{textShadow: ""}' :click-2-3='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>位置编码</span>
## <span v-motion :enter='{textShadow: ""}' :click-3-4='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>自注意力机制</span>
## <span v-motion :enter='{textShadow: ""}' :click-4-5='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>前馈神经网络</span>
## <span v-motion :enter='{textShadow: ""}' :click-5-6='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>编码器层堆叠</span>
</div>

<div class='abs mt-20 ml-40' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/word-embedding.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-20 ml-40' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/position-encoding-transformer.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-20 ml-40' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>
<div style='width:60%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/self-attention.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{opacity: 1}'>

<div style='width:90%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/transformer-explainer.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>Transformer Explainer</span>
</div>

</div>


<FlashText v-click="[6,7]"
           class='abs mt-1/3 text-center w-full text-3xl'>

https://poloclub.github.io/transformer-explainer/
</FlashText>

<!--
这些是Transformer的核心概念


[click]

由于它一开始就是一种语言模型，所以，embedding这一概念几乎是它独有的。embedding的作用就是把词、或者词素编码成向量。但在这个编码过程中，已经考虑了词的语义。embedding在量化交易中不重要，我们仍然要构建embedding层，但只是为了符合transformer架构形式上的需要。


[click]

在transformer中，位置编码有至关重要的作用。在语言模型中，一句话的语义不仅仅由词来决定，还取决于词与词间的位置顺序。比如，人类战胜了病毒，这句话中，如果把两个名词的位置对调，语义就会完全不同。在量化交易中，两个bar一涨一跌，是先涨后跌，还是先跌后涨，它的含义也完全不同，所以，这是量化交易与语言模型相同的地方。


[click]

自注意力机制是 Transformer 架构中的核心组件，它主要用于在处理序列数据时，让模型能够自适应地关注输入序列中的不同位置信息，以更好地捕捉序列中的长程依赖关系。在实现细节上，主要是通过query, key, value这样三处向量来实现

在RNN中，模型通过自循环来学习位置顺序及其影响，但也带来了梯度消失等问题。而在Transformer中，通过自注意力机制，词与词之间的顺序被编码成为特征，使得模型能够并行处理序列中的不同位置，从而很好地解决了梯度消失的问题。

[click]

前馈神经网络位置自注意力机制之后，它接收自注意力机制输出的信息。


[click]

最后，通过编码器层堆叠，形成一个深度的编码器结构。使得模型能够从输入序列中提取到越来越抽象和高级的特征。


[click]

如果我们要深入学习transformer，可以参考下这个工具

-->
