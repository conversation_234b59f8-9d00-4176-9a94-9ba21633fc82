---
clicks: 9
---

<div class='abs mt-10 w-full' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

```python
import torch
from torch.utils.data import Dataset

class FinancialDataset(Dataset):
    def __init__(self, features, labels):
        self.features = torch.FloatTensor(features)
        self.labels = torch.FloatTensor(labels)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.labels[idx]
```
</div>

<div class='abs mt-10 w-full' v-motion
     :enter='{opacity: 0}'
     :click-1-4='{opacity: 1}'>

```python {all|12-14|16-18}{at:2}
def train_test_split(
    self,
    data: NDArray,
    target: pd.Series,
    cuts=(0.7, 0.2),
    bs=64
) -> Tuple[DataLoader, DataLoader, DataLoader]:
    n = len(data)
    itrain = int(n * cuts[0])
    ival = itrain + int(n * cuts[1])

    train_data = FinancialDataset(data[:itrain], target.iloc[:itrain])
    val_data = FinancialDataset(data[itrain:ival], target.iloc[itrain:ival])
    test_data = FinancialDataset(data[ival:], target.iloc[ival:])

    return DataLoader(train_data, batch_size=bs, shuffle=False), 
            DataLoader(val_data, batch_size=bs, shuffle=False), 
            DataLoader(test_data, batch_size=bs, shuffle=False)
```
</div>


<div class='abs mt-10 w-full' v-motion
     :enter='{opacity: 0}'
     :click-4-10='{opacity: 1}'>

```python{all|2|7-9|23|25}{at:5}
def features_and_labels(self, barss):
    labels = barss["price"].unstack().pct_change().shift(-1).stack()

    # common index
    ci = None

    channels = ["open", "close", "high", "low"]
    data = [[]] * len(channels)
    for i, col in enumerate(channels):
        df = barss[col].unstack().ffill().stack()
        # df_ = (df/df.iloc[-1, :]).dropna().stack()

        rolled = df.groupby("asset").apply(
            lambda x: self.rolling_time_series(x, self.win)
        ).droplevel(0)

        if ci is None:
            ci = labels.index.intersection(rolled.index)

        data[i] = rolled.loc[ci]

    labels = labels.loc[ci]
    data = np.stack([*data], axis=2)
    # cnn 要求的shape是（batch_size, channels, seq_len）
    data = np.transpose(data, (0, 2, 1))
    return data, labels
```
</div>

<div class='abs ml-50% w-45% mt-20% shadow-md border border-yellow-400' v-motion
     :enter='{opacity: 0}'
     :click-8-10='{opacity: 1}'>

```python{all|3}{at:8}
def build_model(self):
    model = nn.Sequential(
        nn.Conv1d(self.channels, 32, kernel_size=8),
        nn.ReLU(),
        nn.MaxPool1d(2),

        nn.Conv1d(32, 64, kernel_size=5),
        nn.ReLU(),
        nn.MaxPool1d(2),

        nn.Conv1d(64, 64, kernel_size=3),
        nn.ReLU(),
        nn.AdaptiveAvgPool1d(1),  # 这一步会将每个样本压缩成一个特征向量
    )
```
</div>

<div class='abs ml-5% w-40% mt-10% shadow-md border border-blue-400' v-motion
     :enter='{opacity: 0}'
     :click-9-10='{opacity: 1}'>

```python
class Conv1d(
    in_channels: int,
    out_channels: int,
    kernel_size: _size_1_t,
    stride: _size_1_t = 1,
    padding: _size_1_t | str = 0,
    dilation: _size_1_t = 1,
    groups: int = 1,
    bias: bool = True,
    padding_mode: str = "zeros",
    device: Unknown | None = None,
    dtype: Unknown | None = None
)
```
</div>

<v-drag-arrow v-click='[8,10]' color='red' pos="345,490,248,-231"/>

<v-drag-arrow v-click='[9,10]' color='red' pos="588,253,-155,-5"/>

<!--
我们首先看数据准备部分。

一般而言，我们都会像这样定义一个dataset类。它继承至Dataset。它看上去很简单，它有两个属性，features和labels。此外，要实现两个方法，即__len__和__getitem__。

实现这两个方法之后，我们就可以对它的实例调用len方法，以及通过下标索引来获取对应的feature和label了。

它更重要的作用是与DataLoader一起使用。


[click]

这是每个模型都要实现的train_test_split方法。在这个方法里，传入特征和标签数据，然后按照金融数据的要求，对它进行划分。


[click]

在划分之后，我们先是构造了若干个dataset


[click]

然后将它赋值给DataLoader，在训练时，我们就会对DataLoader进行迭代，从而得到对应的feature和label，然后交给CNN去训练。

DataLoader的主要作用有按批提供数据。这使得它不需要提前把所有数据都加载到内存。当然，这也取决于dataset的实现。在我们的例子中，这一优点实际上没有体现出来。

另外，它还有一个重要功能，就是对数据进行随机打乱。在这里我们是设置为了False，主要是强调有这样一个参数，但实际上我们的训练数据，在这里是独立的，所以，是允许打乱的。不能打乱的，是训练集、验证集和测试集之间的时间先后顺序。

[click]

刚刚讲的都是训练CNN网络时，准备数据要做的常规操作。具体到这个任务，我们要如何提取特征和标签呢？


[click]

这一步是为每个资产生成T+1的收益率，然后，我们通过shift(-1)，让收益率与特征对齐。

[click]

channel是CNN中特有的术语，因为图像的RGB色彩空间是三通道的，它们的信息既独立又相关。在这里，我们把OHLC4组特征，看成是4个通道。

[click]

中间的一些常规操作，我们在之间介绍过类似的代码，这里就不讲了。

这里是把各个通道的数据组合起来。现在我们得到了一个三维数组。


[click]

这里的transpose是比较关键的一步。因为这里的data数据，即特征数据，将会 成为模型第一个卷积层的输入，所以，它的形状必须要与第一个卷积层能接受的相一致。

在build_model方法中，我们将要通过Conv1d来定义一个一维卷积。它的签名是这样的

[click]

它的第一个参数是输入通道数，第二个参数是输出通道数。在内部，Conv1d要求输入的形状是
(batch_size, channels, seq_len）

这里batch_size对应样本的数量维度，channels对应输入通道的数量维度，seq_len对应序列长度维度。

这里通过transpose，我们将本来的通道维度，从第三维调整到了第二维，与特征维度进行了对调。

接下来，我们就看看模型该如何构建。


-->
