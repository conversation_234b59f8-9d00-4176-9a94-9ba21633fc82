---
clicks: 7
---

<div class='abs' v-motion
     :enter='{opacity: 1, scale: 1, x:0}'
     :click-1='{scale: 0.5, x: -250}'
     :click-2='{scale: 0}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/google-colab-logo.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs left-50% mt-50' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

* <mdi-television-shimmer class='text-red-400 animate-bounce mr-2' />Since 2018
* <mdi-wifi-marker class='text-yellow-400 animate-bounce mr-2' />20~30Hours/Week
* <mdi-devices class='text-blue-400 animate-bounce mr-2' />GPU Type: T4/V2-8
</div>


<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/google-colab.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

<div class='abs text-3xl w-full text-center'>
https://colab.research.google.com/
</div>

</div>

<div class='abs mt-10' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/google-colab-welcome.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/colab-change-to-gpu.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-30 ml-30' v-motion
     :enter='{opacity: 0}'
     :click-5-8='{opacity: 1}'>

```python{all|1-2|4}{at: 6}
from google.colab import drive
drive.mount('/content/drive')

data.to_csv('/content/drive/MyDrive/processed_file.csv', index=False)
```
</div>

<!--
首先是Google Colab。它是一种在线的jupyter notebook，支持多人登录，编写和运行notebook，跟我们课程环境类似。


[click]

Google colab大约是从2018年起，提供免费GPU算力的。当前提供T4和V2-8两种GPU。

T4是指英伟达的Tesla T4 GPU, 算是比较老的GPU了，生产于2018年。V2-8是Google自己设计的一种神经网络处理器。


[click]

Google colab怎么访问呢？你可以直接访问这个网址，也可以通过google drive来进入。

这张图显示了通过google drive进行colab的菜单。


[click]
这是刚进入colab时的界面。

最前面的是一个新建notebook的对话框。选择一个notebook之后，就进入到大家熟悉的notebook界面。

这个notebook默认使用CPU。我们可以先在CPU环境下，以非常小的数据量调通程序，然后加大数据量并切换到GPU环境下进行训练。


[click]

这个截屏显示了切换操作。


[click]

在colab中，可以自由安装python库。需要介绍的是，如何加载自己的数据和保存模型。


[click]

这就要用到google drive。你要先把数据上传到google drive，然后通过这两行代码把google drive挂载到这个目录下。


[click]

这行代码演示了google drive加载后，如何访问其中的数据。

-->
