---
clicks: 2
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

<div style='width:66%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/cnn-feature-hirearchy-visulization.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

<div class="w-full text-center">Convolutional Neron Network</div>
</div>

<div class='abs w-60% ml-35% mt-20' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

<div style='width:100%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/cnn-framework.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-30 ml-5' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

## 卷积层
## 激活函数
## 池化层
## 全连接层
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/all-triangles.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<!--
CNN，即卷积神经网络，是人类最早发明的、并且得到实用的深度学习网络。

这张图抽象了CNN的架构。不过，我们要注意这张图有不准确的地方。主要是最左边的几层。最左边的卷积核实际上只能学习到一些边缘特征，比如边缘、角、线段等，是无法得到整体轮廓的。


[click]

它的最核心的概念是卷积层， 激活函数，池化和全连接。

它通过卷积核在图像上滑动来检测局部特征。通过池化层来过滤掉不重要的噪声

通过多个卷积、池化和激活层的组合，就实现了越靠近输入层的地方，CNN越注重局部和细节的特征；越靠近输出层的地方，CNN越能抽象出全局性的特征这样的功能。

在最后，通过一个全连接层，把提取的特征整合起来，就实现了分类或者回归的任务。

我们在量化24课中也介绍过卷积，比如，它可以用来求滑动窗口下的移动平均值。在那里，卷积核的权重是固定的；但在CNN中，通过反向传播，从而能学习到卷积核的权重，使得它们对特定的特征敏感。

所以，同样是卷积，用法是完全不一样的。

如果你对如何理解CNN感兴趣，最好的课程之一是斯坦福CS231N，由李飞飞和他的学生安德烈.卡巴斯基讲授。此外，关于反向传播等更基础的概念，你可以参考吴恩达的机器学习课程。这些都是免费课程。


[click]

CNN在量化交易中有哪些作用呢？当然最可行的应用是k线的模型识别，比如，识别像图中这样的三角形整理。

但是，这意味着需要大量的手工标注数据集。如果能使用算法来自动化标注的话，似乎我们又没必要再借助CNN了，对吧？

所以，为了演示CNN的用法，我在这里举一个有一定复杂度的价格预测模型。

这个例子是这样的，我们要用标的的过去若干天的OHLC数据，来预测它的次日收益。我们将使用4个通道和一维卷积来实现这个任务。

完整的示例代码在教材里。但是，请大家不要在课程环境中运行。

-->
