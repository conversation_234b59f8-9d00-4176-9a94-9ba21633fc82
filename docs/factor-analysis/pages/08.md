---
aspectRatio: 16/9
title: 第 8 课 Alpha101
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
defaults:
    layout: two-cols
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
---

<!--

大家好，我们开始上课了。

通过前面的课程，我们完全掌握了因子分析以及Alphalens框架。从今天起，我将为大家开启因子动物园的大门，见识数百个各具神通的因子。

这一讲我们介绍的是著名的Alpha101因子系列。

-->

---
title: Alpha101因子的作者
src: 08/10.md
---

---
title: /01 数据和算子
layout: section
---

<!--
要理解Alpha101因子，我们首先要了解它所用的数据和算子。Alpha101一共使用了30多个算子，有一些像abs, log, sign, min, max和数学运算符号，我们都接触过，它们在Alpha101中与在别处的用法都是一样的，所以，在这一节中，我们将只介绍一些特殊的算子。
-->

---
title: "Alpha 101因子要求的数据"
src: 08/20.md
---

---
title: 算子
src: 08/30.md
---

---
title: 从公式到代码
src: 08/40.md
---

---
title: "从Alpha101到Alphalens"
src: 08/50.md
---

---
title: "Alpha042因子"
src: 08/60.md
---

---
title: "Alpha101因子库的实现"
src: 08/70.md
---

---
title: "课程环境下的Alpha101/191"
src: 08/80.md
---

---
title: thanks
src: 08/90.md
layout: end
clicks: 1
---
