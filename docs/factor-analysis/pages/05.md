---
aspectRatio: 16/9
title: 第5课 Alphalens报表分析
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
---

<!--
在上一讲，我们介绍了使用Alphalens的基本步骤，最终调用create_full_tear_sheet生成了许多报表，但我们没有讲应该如何阅读这些报表。

这一节课，我们就来介绍如何阅读和分析这些报表。

解读 Alphalens 报表是件很考经验的活儿。一方面，即使我们输入的因子数据或者价格数据是错误的，Alphalens 也会照常生成报表，但这些结论无疑是似是而非的。那么，我们能从报表看出来因子分析过程出错了吗？

另一方面，即使这个过程完全正确，要作出因子是好是坏这一结论也不容易。因为我们看到的报表是纷繁复杂、甚至是可能自相矛盾的。

这一讲，我们就来介绍解读Alphalens报表时，究竟有哪些窍门。
-->

---
src: 05/05.md
title: 关于量纲的问题
---

---
src: 05/10.md
title: 收益分析
---

---
src: 05/15.md
title: Alpha、Beta和分层收益均值
---

---
src: 05/20.md
title: 分层收益柱状图
---

---
src: 05/30.md
title: 因子分层Violine图
---

---
src: 05/40.md
title: 因子加权多空组合累计收益
---

---
src: 05/43.md
title: 分层累计回报收益
---

---
src: 05/50.md
title: 分层离差均值图
---

---
src: 05/60.md
title: 事件分析
---

---
src: 05/70.md
title: IC分析
---

---
src: 05/80.md
title: 换手率分析
---

---
src: 05/90.md
title: thanks
---


<!--
https://github.com/quantrocket-codeload/quant-finance-lectures/blob/master/quant_finance_lectures/Lecture38-Factor-Analysis-with-Alphalens.ipynb
-->
