---
aspectRatio: 16/9
title: 第6课 Alphalens 高级技巧
seq: 因子分析与机器学习策略
layout: cover
theme: ./
monaco: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
lineNumbers: true
defaults:
  layout: two-cols
---

<!--
前面的课程中，我们掌握了因子分析原理、熟悉了Alphalens的基本功能，以及如何读懂Alphalens的报表。

这一讲，我们将介绍一些高级技巧。

比如，如何排除Alphalens分析中的故障。

又比如，在我们之前的课程中，都是以日线因子为例来进行讲解的。如果我们要使用日内因子，或者月线因子，会不会有所不同？这部分是网上没有的资料。

在这一讲，我们还将了解pandas中一些跟时间有关的比较深入概念。只有掌握这些概念，我们才能理解Alphalens在非日线因子分析上的一些行为。这些概念也会是我们本课练习的重点。

好，我们就开始今天的课程！
-->

---
title: 功能性错误
layout: section
---

# 01 功能性错误

<!--

我们先看Alphalens分析中，常见的功能性错误。由于是功能性地，所以我们比较容易发现出错了。

-->

---
title: 版本依赖问题
src: 06/10.md
---

---
title: MaxLossError
src: 06/13.md
---

---
title: 时间问题
src: 06/16.md
---

---
layout: section
---

# 非功能性错误

<!--
接下来，我们看一组非功能性错误。当这些错误发生时，不会有报错，但它可能导致得出错误的分析结论。

-->
---
title: 因子的单调性
src: 06/20.md
---

---
title: 重访Prices参数
src: 06/30.md
---

---
layout: section
---

# 非日线因子

---
title: 非日线级别的因子
src: 06/40.md
---

---
layout: section
---

# 深入因子分层

<!-- 

在第4讲，我们介绍了分层逻辑中，by quantiles和by bins的不同作用。Alphalens设计的这两种分层方法，在因子检验中都有非用不可的理由。

接下来，我们就深入到Alphalens的分析过程，看看这两种方式，在实际因子检验中有何不同。

-->

---
title: 深入因子分层
src: 06/50.md
---

---
title: thanks
src: 06/60.md
---
