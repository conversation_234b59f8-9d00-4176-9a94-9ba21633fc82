---
clicks: 20
right: 60%
---

<div v-motion :enter="{opacity: 0}" :click-7="{opacity: 1}">

## pct_change(period)
</div>

<div class="w-70%" v-motion :enter="{opacity: 0}" :click-10-11="{opacity: 1}">

```
2020-07-24          NaN  
2020-07-27          NaN  
2020-07-31     0.000000  
2020-08-26     0.000000  
2020-08-27    -0.101053  
2020-08-31     0.000000  
2020-09-25    -0.223653  
2020-09-30     0.000000  
2020-10-29    -0.230769 
```
</div>

::right::

<NoteCell :enter="{scale: 1}"
          :click-3="{scale: 0}"
          hideOutput>

```python {all|6,8,15}{at:2}
def load_month_bars(start, end, universe=100):
    barss = load_bars(start, end, universe=universe)

    def resample(g):
        df = g.droplevel('asset')
        df['date'] = df.index
        df = df.resample('M').agg({
            'date': 'last',
            'close': 'last',
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'volume': 'sum',
            'amount': 'sum'
            }).set_index('date')
        df["price"] = df.open.shift(-1)
        return df.dropna()

    df = barss.groupby('asset').apply(resample)
    return df.swaplevel(0,1).dropna()

start = datetime.date(2005, 1, 4)
end = datetime.date(2023, 7, 31)

universe = ('300590.XSHE', '601166.XSHG')
barss = load_month_bars(start, end, universe)
barss.head(10)
```
</NoteCell>

<NoteCell :enter="{scale: 0}" :click-3-7="{scale: 1}">

```python {all|25,26|all}{maxHeight:'450px', at:4}
from alphalens.utils import get_clean_factor_and_forward_returns
from alphalens.tears import create_returns_tear_sheet

def load_month_bars(start, end, universe=100):
    barss = load_bars(start, end, universe=universe)

    def resample(g):
        df = g.droplevel('asset')
        df['date'] = df.index
        df = df.resample('M').agg({
            'date': 'last',
            'close': 'last',
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'volume': 'sum',
            'amount': 'sum'
            }).set_index('date')
        df["price"] = df.open.shift(-1)
        return df.dropna()

    df = barss.groupby('asset').apply(resample)
    return df.swaplevel(0,1).dropna()
    
def low_volatility(close: pd.Series, win:int, *args):
    return 1 / (1e-7 + close.pct_change().rolling(window=win).std())

np.random.seed(78)
start = datetime.date(2018, 1, 4)
end = datetime.date(2023, 12, 29)

barss = load_month_bars(start, end, 1000)

factors = []
for group in barss.groupby(level='asset'):
    close = group[1].close

    low_vol = low_volatility(close, 10)
    factors.append(low_vol)
    
factor = pd.concat(factors).rename_axis(index={'frame':'date'})

# 3. 提取价格数据
prices = barss['open'].unstack(level=1).shift(-1)

# 4. 预处理
factor_data = get_clean_factor_and_forward_returns(factor, prices)

# 5. 生成报告
create_returns_tear_sheet(factor_data)
```
</NoteCell>

<v-drag v-motion pos="380,51,211,54" :enter="{opacity: 0}" :click-6-7="{opacity: 1}">
<Box />
</v-drag>

<div class="abs top-0%" v-motion :enter="{scale: 0}" :click-7-12="{scale: 1}">

```python {all|5|9|all}{at:8}
def compute_forward_returns(...):
    ...
    for period in sorted(periods):
        if cumulative_returns:
            returns = prices.pct_change(period)
        else:
            returns = prices.pct_change()

        forward_returns = returns.shift(-period).reindex(factor_dateindex)
    ...
```
</div>

<NoteCell :enter="{scale: 0}" :click-12-15="{scale: 1}">

```python {all|2-19|23-37}{maxHeight:'450px', at:13}
np.random.seed(78)
day_barss = load_bars(start, end, 100)
barss = (
    day_barss.groupby("asset")
    .apply(
        lambda x: x.droplevel("asset")
        .resample("1M")
        .agg(
            {
                "close": "last",
                "open": "first",
                "high": "max",
                "low": "min",
                "volume": "sum",
                "amount": "sum",
            }
        )
    )
    .swaplevel(0, 1)
)


factors = []
for group in barss.groupby(level='asset'):
    close = group[1].close

    low_vol = low_volatility(close, 10)
    factors.append(low_vol)
    
factor = pd.concat(factors).rename_axis(index={'frame':'date'})

# 3. 提取价格数据
prices = barss['open'].unstack(level=1).shift(-1)

# 4. 预处理
factor_data = get_clean_factor_and_forward_returns(factor, prices, max_loss=0.36)
factor_data.tail()
```
</NoteCell>

<NoteCell :enter="{scale: 0}" :click-15="{scale: 1}">

```python
# 5. 生成报告
create_returns_tear_sheet(factor_data)
```
</NoteCell>

<NoteCell :enter="{scale: 0}" :click-16="{scale: 1}">

```python
factor_data.rename(columns = {
    '1D': '21D',
    '5D': '105D',
    '10D': '210D'
},inplace=True)

# 5. 生成报告
create_returns_tear_sheet(factor_data)
```
</NoteCell>

<div class="abs mt-50" v-motion :enter="{scale: 0}" :click-17="{scale: 1}">

```python
freq_adjust = pd.Timedelta("252Days") / pd.Timedelta(period)

alpha_beta.loc["Ann. alpha", period] = (1 + alpha) ** freq_adjust - 1
alpha_beta.loc["beta", period] = beta
```
</div>

<CountdownTimer :at=20 :count=15
                q="如果因子是日内级别，哪些说法是正确的?<br><br>1. 年化收益也是错的<br>2. 累积收益也是错的<br>3. Alphalens能正确处理"/>

<!--

<run></run>

到目前为止，我们都是在日线级别上计算因子的。但是，对于基本面因子，在日线级别上计算并没有什么意义，因为基本面因子的数据，往往是以季度为单位的。

假设我们有一个月线因子，要通过Alphalens进行因子检验，会与之前的步骤有什么不同吗？

我们通过示例来看一下。这一次，我们将使用低波动因子。

我们首先要获取月线数据。在课程环境中，我们不能直接获取月线数据，需要通过日线进行数据转换。

[click]

在这段代码中，我们读取了日线数据，通过resample函数，将其转换成为月线数据。不过，为了保持数据的真实性，我们在日期处理上，进行了特殊处理。

[click]

在这段代码中，我们先是新增了一个date列，它的值来自于日期索引。在重采样时，我们使用'last'方法，保留了该资产每月最后一个交易日的日期，最后，我们用重采样之后的date列，作为新的索引。

resample本身就能把索引重采样成月，这里为什么要多此一举呢？

我们先看看运行后的结果。我们看到第一个资产，2017年1月，结束于26日，而不是31日。4月结束于28日，也不是自然月的月末。

也就是说，通过这里的特殊处理，我们得到了一个真实的交易日历。如果使用pandas的默认重采样方法，那么这里的索引值，都会使用自然日历的月末值。

[click]

<run></run>

现在，我们就来定义月线低波动因子。

[click]

这里注意两点。我们假定波动率越低，收益越好，因此，我们要对波动率取倒数。第二，在取倒数时，为了避免除数为零，我们给标准差加上了一个极小值。

这里的做法是正确的吗？大家思考一下。

首先，std函数的返回值是非负数，所以，我们加上1e-7，就能保证除数一定大于零。

其次，上一次课我们讲过，因子的量纲对收益分析没有影响。所以，我们是可以改变除数的。

[click]

其它细节跟之前我们学习过的一样。现在，我们来看看运行结果。

我们先看总览。大家觉得这里有不对劲儿的地方吗？

[click]

我们发现这里的年化Alpha，它的列名字仍然是1D，5D和10D。但我们实际上计算的是月线因子，所以，这里的1D是什么意思呢？是买入后确实只持有了一天，还是实际上是按月计算并年化的，只是在列名字上没有调整？

这就涉及到Alphalens是如何计算远期收益以及Alpha年化的。

[click]

这是Alphalens在计算远期收益时的代码。所以，它的思路是


[click]

首先，对prices表格，应用pct_change(period)来求得period期的收益。这里没有任何时间单位的概念。无论我们在period中，传入的是1， 5， 10意味着是1天，5 天，还是1月5月，在这里都被简化为1行或者5行。

[click]

然后，通过shift操作，把收益数据对齐到起点。

最后，它通过reindex操作，把日期对齐到因子表。

[click]

这种算法在月线因子的情况下，会出现什么问题？现在，左边显示了计算过程中，生成的远期收益。

这个结果本来有接近1000列，因为我们有1000个资产，但为了演示方便，只取了索引和其中一列。

我们看到，在7月有3条记录，在8月有3条记录，在9月有2条记录。为什么会这样呢？

因为由于停牌的原因，不同资产的月度收盘日不一样，所以无论是factor数据，还是prices数据，都可能出现一个月存在多条记录的情况。所以在计算1月收益时，本来跨一行应该就满一个月的，实际上计算的是几天的收益，而且是随机的；跨5行本来应该算得的5月收益的，实际上只是持有一个多月的收益。

因此，几乎没有任何一条记录，是真正月度意义上的远期收益。

看到这里，显然，我们也没必要继续后面的因子分析了，因此后面的数据肯定错了。

[click]

所以，尽管月度、季度因子在基本面分析中非常常见，但Alphalens对它们支持得不太好。他们推荐的workaround是，将一个月转换成21天。

也就是，当我们实际上想使用1Mon, 5Month和10Month时，我们需要在periods中传入21D, 105D和210D。

[click]

具体怎么操作呢？这个是在Alphalens的文档中没有提到的，也是网上搜索不到的。

通过对Alphalens的源码分析，我们给出如下解决方案。

[click]

首先，我们要从数据源头开始治理，把数据对齐到自然月结束。

注意这里的采样与开头的采样不同之处就在于，我们没有干预索引的生成。在这种情况下，重采样的数据，就会自然对齐到自然月结尾。

[click]

因子计算和价格数据的提取方式仍然不变。现在，我们看一下生成的factor_data。

我们看到，所有的日期都已对齐到自然月结尾。远期收益已经是按月计算的了，这点大家可以自行校对一下。

远期收益的列名字仍然是1D, 5D和10D。

Alphalens在调用get_clean_forward_returns时，已经进行了periods单位的推断，但是由于跨月推断存在很多困难，它没能推断出这里的单位是月，所以，尽管数据都是按月传递的，数据之间的间隔都是月，它推导出来的单位仍然是D。

到这一步，我们实际上修复了最关键的一步，让Alphalens在基础收益方面做出了正确的计算。但是，如果我们直接调用create_returns_tear_sheet，会发现年化收益还是错误的。

[click]

<run></run>

为什么这么说？我们运行一下，看看收益分析的结果。

我们看到这个年化收益太高了，并且与分层收益均值图、与累计收益图都相矛盾。显然，这仍然是错误的。

那我们应该如何修复这个问题呢？

[click]

<run></run>

修正月度因子最关键的一步来了，就是把前面得到的factor_data数据中的列重新命名。

在这里，我们把1D改成了21D，5D改成了105D，10D改成了210D。这样在后面的收益分析中，跟年化相关的数据就变正确了。

我们来检查一下运行结果。现在，年化收益正常多了，而且与其它报告之间，也不存在明显的矛盾了。


这样做为什么会生效呢？

[click]

这是因为在输出报表时，Alphalens会依赖列名字来计算年化因子。这是Alphalens中，对alpha进行年化的代码。

从代码可以看出，当列名字是1D时，它的年化因子就是252，尽管在我们这个例子中，年化因子本来应该是12。

那如果我们把列名字改为1M、5M和10M，会不会更好？

[click]

答案是，不可以，我们看到年化因子是通过TimeDelta的类型来计算的，它就不支持日以上的单位。时间计算实际上非常复杂，甚至可以这么说，时间不是一个数学计算问题，它是一个法律和治理的问题。所以，pandas才会做出这样的选择。

[click]

在量化24课中，我们在第一部分就讲，交易日历是行情数据源的基本功能之一，原因也在于此。因为交易日历并不是可以计算的，它必须依靠数据库。所以，当你判断一个量化库是否功能完整，也可以看看它是否提供了交易日历相关的运算。

[click]

最后一个问题，当Alphalens应用于日内因子时，下列哪些说法是正确的？

1. 年化收益是错的
2. 累积收益是错的
3. 以上都不对，Alphalens能正确处理日内因子。

给大家15秒钟思考，并给出答案。

答案是，1。只有年化收益受影响。其它部分不受影响。 
-->
