---
layout: end
clicks: 1
---

<div class="abs top-0% scale-80%">

## 1. 功能性错误
### 1.1. 过时的Alphalens版本
### 1.2. MaxLossExceedError
#### 数据不足
#### 离散值因子分布失衡
### 1.3. 时间问题
## 2. 因子的单调性
## 3. 重访Prices参数
## 4. 非日线级别的因子
## 5. 深入因子分层
### 5.1. 有确定交易含义的因子
### 5.2. 离散值因子
</div>

<!--

这一章介绍的是Alphalens中的高级技巧之一。主要是如何排除因子分析中的常见错误。

我们介绍了功能性的、非功能性的错误，以及非日线情况下，因子分析中可能出现的错误。

这一讲的难点是理解索引的频率推断。无论是在MaxLossExceedError中，还是在非日线级别的因子中，我们都看到索引的频率推断可能引起一些问题。

我们再总结一下。索引并不是一组简单的数据。它有点类似于绘图中的轴。每一种轴、或者索引都有自己的逻辑，从而能生成指定范围内的所有合法数值。

记住这一点，你就能理解为什么factor数据与price数据对齐时可能出错。

此外，我们还要理解，索引的频率与pandas中的Timedelta是既关联、又不相同的概念。索引的频率可以是秒、分钟、小时、天和月等，但Timedelta则只能是日及日以下级别。关于这部分知识点，我们已经设计成了练习。相信练习完成之后，大家基本上能够精通Pandas的这些概念，并在工作中熟练使用。

-->
