---
clicks: 9
right: 60%
---

<div class="abs mt-30 ml-60 w-full" v-motion :enter="{opacity: 1}" :click-1="{opacity: 0}">

## by quantiles, or by bins?
</div>

<div class="abs mt-30 ml-60 w-full" v-motion :enter="{opacity: 0}" :click-1-2="{opacity: 1}">

## 有确定交易含义的因子
</div>

<div class="abs mt-30 ml-80 w-full" v-motion :enter="{opacity: 0}" :click-7-8="{opacity: 1}">

## 离散值因子
</div>

<div class="abs mt-30 w-full" v-motion :enter="{opacity: 0}" :click-5-7="{opacity: 1}">

## Overlap
</div>

<div class="abs bottom-10 ml-10"
    v-motion :enter="{opacity: 0}" :click-9="{opacity: 1}">

```python
bins = np.linspace(-3.5, 5.5, 10)
```
</div>

::right::

<NoteCell :enter="{opacity: 0}" :click-2-6="{opacity: 1}" class="z-100" hideOutput>

```python {all|1,18,19}{maxHeight:'450px', at: 3}
from alphalens.plotting import plot_quantile_statistics_table

def calc_rsi(close, win):
    return 100 - ta.RSI(close.astype(np.float64), win)

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)

barss =  load_bars(start, end, 100)

factors = (barss.groupby(level='asset')
                .apply(lambda group: calc_rsi(group.close, 6))
                .droplevel(level=0))
prices = barss['price'].unstack(level=1)

factor_data = get_clean_factor_and_forward_returns(factors, prices)

# 绘制分层统计图
plot_quantile_statistics_table(factor_data)
```
</NoteCell>

<v-drag pos="-8,137,376,54" v-motion :enter="{scale: 0}" :click-4-5="{scale: 1}">
<Box />
</v-drag>

<v-drag pos="100,136,185,231" v-motion :enter="{scale: 0}" :click-5-6="{scale: 1}">
<Box />
</v-drag>

<NoteCell :enter="{scale: 0}" :click-6-7="{scale: 1}" class="z-100" hideOutput>

```python {all|16,20,21}{at: 6}
from alphalens.plotting import plot_quantile_statistics_table

def calc_rsi(close, win):
    return 100 - ta.RSI(close.astype(np.float64), win)

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)

barss =  load_bars(start, end, 100)

factors = (barss.groupby(level='asset')
                .apply(lambda group: calc_rsi(group.close, 6))
                .droplevel(level=0))
prices = barss['price'].unstack(level=1)

bins = np.linspace(0, 100, 10)
factor_data = get_clean_factor_and_forward_returns(
                                                   factors, 
                                                   prices, 
                                                   quantiles=None, 
                                                   bins=bins)

# 绘制分层统计图
plot_quantile_statistics_table(factor_data)
```
</NoteCell>

<div class="abs mt-10" v-motion 
    :enter="{x:0,opacity:0}" 
    :click-8="{x:-80,opacity:1}"
    :click-9="{x:-350,opacity:1}">

* 6 - Strongest positive sentiment.
* 5 - Extremely strong, positive, sentiment.
* 4 - Very strong, positive, sentiment.
* 3 - Strong, positive sentiment.
* 2 - Substantially positive sentiment.
* 1 - Barely positive sentiment.
* 0 - Neutral sentiment
* -1 - Sentiment trending into negatives.
* -2 - Weak negative sentiment.
* -3 - Strongest negative sentiment.

</div>

<div class="abs mt--10" v-motion 
    :enter="{x: 0, scale:0}" 
    :click-9-10="{x:150,scale:0.8}">

<Table>

```yaml
head: bin edge,factor,factor quantile
body:
    - -3.5,-3,1
    - -2.5, -2,2
    - -1.5, -1,3
    - -0.5, 0,4
    - 0.5, 1,5
    - 1.5, 2,6
    - 2.5, 3,7
    - 3.5, 4,8
    - 4.5, 5,9
    - 5.5, 6,10
```
</Table>
</div>

<!--


橫截面因子分析的逻辑是，因子与收益呈线性关系，总是买入因子最强的一组、卖出因子最弱的一组，这样无论市场好坏，策略相对于市场总是有超额的。

在这种假设下，按分位数分层就是一个不错的选择：无论某个时间段的市场情况（因子整体均值和中位数值是高还是低）如何，按照分位数分层，我们总能划分出若干样本数相同的分层。


[click]

但是，对于RSI这类已经有明确交易含义的因子，如果我们仍然按分位数进行分层，就会面临一个矛盾。

现在，我们来看一个例子

[click]

<run></run>

这段代码使用的是RSI因子。我们已经很熟悉这段代码了。

[click]

略有不同的是，这次我们调用了一个名为plot_quantile_statistics_table的函数。

我们在之前已经看到过它的输出结果。只要调用create_full_tear_sheet，这个方法就会被调用，并且它的输出将会是第一个报表。这也说明了分层在因子检验中的基础地位。

现在我们来查看结果。

[click]

我们看到，第一层的最大值就已经到了66，第三层的最大值到了83。这是因子值，对应的RSI则分别是34和27。从传统意义上讲，这都属于超卖阶段，但在因子分析中，我们却要把它当成卖出信号。

显然，这里就出现了一个矛盾。即使最后因子检验的结果表明因子有效，也违背了我们对RSI因子进行检验的初衷。我们挖掘出来的，一定是某种别的因子。我们固然欢迎这种意外之喜，但首要任务还是先对RSI因子作出一个结论。

[click]

其次，这里还出现了因子分层重叠。为什么会出现overlapping?是我们计算出错了吗？

这种情况是正常的。因为分层是按日期进行的。几乎在每一天，都会有RSI超80的资产，也会有RSI低于20的资产。只不过受市场影响，RSI的中位数会发生偏移而已。

其实，这个问题在所有的因子分析中，只要按by quantiles进行分层，就几乎一定分出现overlapping。只不过，如果我们使用的因子本身就有信号含义时，这个问题就变突出了。或者说，这种重叠让我们觉得有问题了。

从根本上说，对本身有交易信号含义的因子，我们希望按因子的绝对值进行分层，而不是按它们的相对位置进行分层。按quantiles分层，就是按相对位置进行分层。

[click]

<run></run>

现在，我们再按by bins对RSI因子进行分层。请大家注意，当我们按bins进行分层时，quantiles参数就一定要传入None

现在我们检查一下结果。

因子之间分层重叠问题不存在了。现在，如果我们做多第9层，那么就是相当于买入RSI在10左右的资产；做空第1层，相当于卖出RSI在93左右的资产。这就和RSI的信号一致了。

当我们从by quantiles换到by bins分层时，收益也会得到提升，我们将在第7课结合调优再展示这个结果。

[click]

在本讲的第三张slide，我们提到了如果因子值是离散的，那么错误的分层会导致max loss error的问题。现在，我们就来正式讨论这个问题。

如果我们在做一个文本情感分类因子，那么，因子数据很可能是这样的

[click]

要对这样的因子进行分层，我们要使用by bins的分层方式。

[click]

我们通过linspace方法，在各个因子中间生成了边界值，刚好把它们隔开。
-->
