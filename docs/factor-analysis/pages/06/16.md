---
clicks: 5
right: 60%
---

## No attribue 'tz'

<v-clicks>

## Inferred Freq Error
</v-clicks>

::right::

<div class="abs p-3" v-motion :enter="{opacity: 1}" :click-1="{opacity: 0}">

![](https://images.jieyu.ai/images/2023/07/index_has_no_attribute_tz.png)
</div>

<NoteCell :enter="{opacity: 0}" :click-1-5="{opacity: 1}">

```python {all|9-16,20-21}{maxHeight: '450px'}
price = {'A': [76.930, 76.730, 76.850, 76.750, 76.750, 76.880, 76.925], 
         'AAL': [30.240, 30.210, 30.210, 30.200, 30.150, 30.245, 30.315]}
factor = {'A': [0,0,0,0], 'AAL': [0,0,-0.142857,0]}

start = datetime.datetime(2019,11,28)
endp = start + datetime.timedelta(minutes=6)
ends = start + datetime.timedelta(minutes=3)

pricings = pd.DataFrame.from_dict(price)
pricings['date'] = pd.date_range(start, endp, freq="1min")

factor = pd.DataFrame.from_dict(factor)
factor['date'] = pd.date_range(start, ends, freq="1min")

pricings.set_index(['date'], inplace=True)
factor.set_index(['date'], inplace=True)

factor = factor.stack()

print(pricings.index.freq)
print(factor.index.levels[0].freq)


factor_data = get_clean_factor_and_forward_returns(factor,
                                                    pricings,
                                                    quantiles=None,
                                                    bins = 2,
                                                    periods=(1,2))

```
</NoteCell>

<CountdownTimer :at=3 q="哪一种说法是正确的？<br>1. 重采样 <br>2. 窗口操作 <br>3. 数据对齐和完整性检查"/>


<NoteCell :enter="{scale: 0}" :click-5-6="{scale: 1}">

```python {all|10,11,14,15,18,19}{maxHeight: '450px'}
price = {'A': [76.930, 76.730, 76.850, 76.750, 76.750, 76.880, 76.925], 
         'AAL': [30.240, 30.210, 30.210, 30.200, 30.150, 30.245, 30.315]}
factor = {'A': [0,0,0,0], 'AAL': [0,0,-0.142857,0]}

start = datetime.datetime(2019,11,28)
endp = start + datetime.timedelta(minutes=6)
ends = start + datetime.timedelta(minutes=3)

pricings = pd.DataFrame.from_dict(price)
pricings['date'] = pd.date_range(start, endp, freq="1min")
factor = pd.DataFrame.from_dict(factor)
factor['date'] = pd.date_range(start, ends, freq="1min")

pricings.set_index(['date'], inplace=True)
factor.set_index(['date'], inplace=True)

factor = factor.stack()

pricings.index.freq = 'T'
factor.index.levels[0].freq = 'min'

factor_data = get_clean_factor_and_forward_returns(factor,
                                                    pricings,
                                                    quantiles=None,
                                                    bins = 2,
                                                    periods=(1,2))

```
</NoteCell>

<!--

Alphalens在计算时，对factor和price数据的日期索引有一些隐含要求。

第一个要求就是日期索引必须是datetime.datetime类型，或者说timestamp类型，而不能是object或者date类型。
并且这个timestamp必须是带时区的。日期类型是没有时区的。

右边的错误，正是因为在factor数据中，日期索引没有时区属性导致的。如果你使用的是date类型，而不是datetime，或者是不带时区的datetime，就会抛出这个异常。还有一种情况，就是索引的顺序错误。正确的索引顺序是 date, asset，如果这个顺序错了，也会报这个错误。


[click]

第二个要求，是要正确指定时间索引的freq属性。如果freq属性错误，Alphalens又不能从给出的索引数据中，推断出frequency属性，就会报索引推断错误。

这段代码就显示了一个索引频率推断错误的例子。它使用的是日内数据，但有一些数据无法对齐，从导致Alphalens推断失败。

[click]

现在高亮的代码生成了两个dataframe, pricings和factor。

并且给它们都增了一个叫date的列。这一列的数据，是通过pandas.date_range函数生成的。在生成这个序列时，我们指定了freq参数。

然后将date序列设置为数据表的索引。

在第20,21行，代码将显示两个表格的索引的freq属性。

我们<run/>一下这段代码，

我们检查一下结果。两个表格的索引的freq属性都是None。在这里已经出现了索引推断失败，只不过pandas允许这种情况出现。

但在get_clean_factor_and_forward_returns函数中，需要将两张表的记录对齐，这是一个join操作。这种情况下，需要生成索引的全部取值，但在freq=None的情况下，就无法完成这个任务。

[click]

freq属性在索引中起到什么作用呢？大家思考一下。我们给三个说法，大家可以在评论区回答一下，哪一个是正确的

1. 方便进行索引重采样
2. 方便进行窗口操作
3. 方便数据对齐和完整性检查

[click]

这些说法都是正确的。你可以把索引想像成为绘图中的轴。

如果知道一个轴的起点、终点和标尺单位，就可以绘制一条轴；相反，如果不知道标尺单位信息，仅有一组数据，我们是无法绘制出一条轴的，因为我们只只到它的起点和终点，但无法生成这条轴上的其它有效数据。

日期、时间索引也有类似的性质。

[click]

现在，我们把索引的freq设置为'1min'。在pandas中，有一个简便的写法，就是用T来代替1分钟。

我们再<run/>一次，这次运行结果就正常了。

这一部分的内容可能会比较难懂，当你经常使用alphalens来进行因子检验时，特别是在数据的频率不是日频时，就会常常遇到这个异常。那时候，你可以再回来看看这一节。并且，我们在练习题中加入了这部分内容。通过练习，你将会深入理解frequency属性。

-->
