---
clicks: 5
layout: default
---

<div class="abs left-30% mt-20"
     v-motion
     :enter="{opacity: 1}"
     :click-1="{opacity: 0}">

$$
\alpha = R_p - \beta(E(R_m) - R_f) - R_f
$$

</div>


<div class="abs w-80% h-full left-10% top-15%"
     v-motion
    :enter="{opacity: 0}"
    :click-1-4="{opacity: 1}"
    >

```python {all|all|6-8}
def rolling_slope(close: NDArray, win:int, *args):
    strides = (close.itemsize, close.itemsize)
    shape = (win, close.size - win + 1)
    close_windows = as_strided(close, shape=shape, strides=strides)

    slopes, _ = np.polyfit(np.arange(win), 
                           close_windows.T,
                           deg=1)
    return slopes
```

```python {all|6-8}
def moving_slope(close: NDArray, win:int, *args):
    strides = (close.itemsize, close.itemsize)
    shape = (win, close.size - win + 1)
    close_windows = as_strided(close, shape=shape, strides=strides)
    
    slopes = np.apply_along_axis(lambda x: linregress(np.arange(win), x)[0], 
                                 arr=close_windows,
                                 axis=0)
    return slopes
```
</div>

<div class="abs top-25%"
     v-motion
    :enter="{opacity: 0}"
    :click-4="{opacity: 1}">

![moving_slope,2024/1/1-2024/7/1](https://images.jieyu.ai/images/2024/10/mono-decrease-factor.jpg)
</div>

<div class="abs color-red text-4xl"
     v-motion
     :enter="{opacity: 0}"
     :click-5="{opacity: 1}">

<div class="abs ml-60 mt-15 w-400px">RSI</div>
<div class="abs ml-120 mt-15 w-400px">Low Volatility</div>
</div>

<!--

前面我们已经或多或少提及过，因子与收益之间，应该满足单调性原则。

当我们计算因子Alpha时，背后的数学原理是因子与收益之间存在线性关系。单调性是线性的必要条件。

在我们计算IC和分层收益时，我们放宽了线性相关的要求，但仍然要求单调性 <draw/>

这句话要正确理解。我们在这里说线性和单调性时，是从整体上把握的，是去掉噪声之后，或者说指的是拟合函数要满足这样的条件，而不是数据本身。

[click]

有时候会出现这样的情况，因子从原理上讲是有意义的，但涉及到具体实现，有可能把它实现成了负相关的因子，最终导致模型结果不好被抛弃。

这里我们举一个具体的例子。

这里有两段代码。它们是根据Andreas Clenow提出的斜率因子的原理，做出的两种不同的实现。

[click]

在上面的代码中，我们使用了np.polyfit来计算斜率。polyfit是一个多项式拟合函数，当参数deg=1时，就是线性拟合，此时它应该返回斜率和截距。

[click]

下面这段代码是利用scipy的linregress函数来计算斜率。linregress返回的是一个包含斜率和截距的元组。

两段代码大同小异，但是两个函数对输入的参数处理方式不同，导致求出来的斜率，正好是相反数关系。

因此，如果斜率因子与收益总体上是线性相关关系，那么两种方法实现的因子，必有一个是负相关关系。

[click]

当我们拿2024年1月到7月的数据进行因子检验时，发现下面的这个因子与收益呈明显地负相关关系。

因此，如果我们在因子分析过程中看到这样的图，并不意味着因子的原理错了，而是我们没有正确地实现因子，没能满足因子的单调上升的要求。

[click]

还有一些因子，如果直接按其字面意义来实现的话，那么它们是与收益负相关的。也就是说，在正式进行因子检验之前，我们是需要进行调整的。

比如，RSI是一个在0和100间分布的因子。传统上认为，RSI超过70或者80是卖出信号，低于30或者20是买入信号。如果这个规律仍然存在，那么意味着RSI与收益之间是负相关的关系。因此，如果我们直接把RSI值当成因子来进行检验，就不会得到期望的结果。

又比如低波动因子。一般来说，波动率越低，公司的业绩可能越稳定；公司的业绩不稳定、展望负面的公司，它的波动率也越高。因此，波动率与远期收益之间也是负相关关系。

-->
