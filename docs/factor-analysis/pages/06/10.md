---
clicks: 1
---

<div v-motion
    :enter="{scale: 1}"
    :click-1="{scale: 0}">

## Alphalens 版本问题

pip install alphalens

requires: pandas >= 0.18
</div>

<div v-motion
    :enter="{scale: 0}"
    :click-1-2="{scale: 1}">

## Alphalens Reloaded

by <PERSON>@ml4trading

version: 0.4.4
</div>

::right::

<div v-motion
    :enter="{scale: 1}"
    :click-1="{scale: 0}">

![](https://images.jieyu.ai/images/2024/08/alphalens-out-of-date.jpg)
</div>


<div v-motion
    :enter="{scale: 0}"
    :click-1-2="{scale: 1, y: -300}">

![](https://ml4trading.io/static/home/<USER>/about.png)
</div>

<!--

如果我们安装alphalens时，使用的是pip install alpalens命令，我们就会遇到这个错误。

Alphalens是从2017年起开始开发的。它最后的版本是0.4，发布于2020年4月30日。在此之后，它的开发者 -- Quantopian在同年11月停止了运营。于是Alphalens 0.4也就成为了最后一版。

但在04版发布时，pandas的1.0才刚刚发布不久，Alphalens没能赶上这个大版本。因此，Alphalens依赖的pandas还是比较旧的版本，大约是0.18。所以，在我们现在使用Alphalens时，就会遇到一个依赖问题。Alphalens并没有锁定对pandas的版本依赖，于是，安装时就会安装最新的pandas版本。

但pandas从1.0起，就去掉了早期版本中的Index的一个方法，即get_values。这样就导致了程序出错。

[click]

要修正这个问题，我们就要使用alphalens-reloaded这个库。这个库是alphalens的一个fork，修复了pandas版本依赖的问题及其它错误。

它的开发者是stephan jessen。他自己有一个机器学习策略的课程，大家也可以抽空看看。我在编写教材时也有参考他的课程。相比较而言，他的课程内容更面面俱到。

但是全面和深度是相互矛盾的一对儿。我们这门课程的内容会更深入一些，确保大家学完之后，就能够精通这个领域。

在quantopian终止运营之后，ml4t团队就接手维护了quantopian开发的几个库，比如alphalens, zipline, empyrical等。
-->
