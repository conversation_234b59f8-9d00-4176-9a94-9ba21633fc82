---
clicks: 9
right: 65%
---

<div v-motion :enter="{opacity: 1}" :click-3="{opacity: 0}">

## 1. 数据不足

</div>

<v-switch>
<template #3-10>

## 2. 分层边界值重复
</template>

<template #8-10>

### 2.1 减小分层数
### 2.2 改用bins分组   
</template>

</v-switch>



::right::

<NoteCell :enter="{scale: 1}" :click-2="{scale: 0}">

```python {all|6,7,24}{maxHeight: '450px',at:1}
from alphalens.utils import get_clean_factor_and_forward_returns

def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

start = datetime.date(2023, 7, 1)
end = datetime.date(2023, 12, 31)

np.random.seed(78)
barss = load_bars(start, end, 20)

# 计算因子
factors = (barss.groupby(level='asset')
            .apply(lambda x: calc_rsi(x, 6))
            .droplevel(level=0)
            )

prices = barss['price'].unstack(level=1)

merged = get_clean_factor_and_forward_returns(
                                            factors, 
                                            prices, 
                                            quantiles=5,
                                            periods=(1, 5, 60))

```
</NoteCell>

<NoteCell :enter="{scale: 0}" :click-2-3="{scale: 1}">

```python {all|24}{maxHeight: '450px',at:1}
from alphalens.utils import get_clean_factor_and_forward_returns

def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

start = datetime.date(2023, 7, 1)
end = datetime.date(2023, 12, 31)

np.random.seed(78)
barss = load_bars(start, end, 20)

# 计算因子
factors = (barss.groupby(level='asset')
            .apply(lambda x: calc_rsi(x, 6))
            .droplevel(level=0)
            )

prices = barss['price'].unstack(level=1)

merged = get_clean_factor_and_forward_returns(
                                            factors, 
                                            prices, 
                                            quantiles=5,
                                            periods=(1, 5, 10))

```
</NoteCell>

<v-drag-arrow color="red" pos="537,210,-73,212" v-click="[1,2]" />
<v-drag-arrow color="red" pos="536,210,-243,-84" v-click="[1,2]" />

<NoteCell :enter="{scale: 0}" :click-6-8="{scale: 1}">

```python {all}{maxHeight: '420px'}
def calc_factor(df, n=6, win=20):
    rsi = ta.RSI(df.close, n)

    rsi_rank = win - rsi.rolling(window=win).apply(lambda x:x.rank().iloc[-1])
    return pd.DataFrame({
        "rank": rsi_rank,
        }, index=df.index)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 29)

# 计算因子
factors = (barss.groupby(level='asset')
                .apply(lambda x: calc_factor(x))
                .droplevel(level=0)
            )

# 提取价格数据
prices = barss['price'].unstack(level=1)

factor_data = get_clean_factor_and_forward_returns(factors, prices, max_loss=0)
```
</NoteCell>

<NoteCell :enter="{scale: 0}" :click-3-6="{scale: 1}">

```python {all|4|all}{maxHeight: '420px',at:4}
def calc_factor(df, n=6, win=20):
    rsi = ta.RSI(df.close, n)

    rsi_rank = win - rsi.rolling(window=win).apply(lambda x:x.rank().iloc[-1])
    return pd.DataFrame({
        "rank": rsi_rank,
        }, index=df.index)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 29)

# 计算因子
factors = (barss.groupby(level='asset')
                .apply(lambda x: calc_factor(x))
                .droplevel(level=0)
            )

# 提取价格数据
prices = barss['price'].unstack(level=1)

factor_data = get_clean_factor_and_forward_returns(factors, prices)
```
</NoteCell>

<!--
第二个常见错误，是MaxLossExceedError。 MaxLossExceed 最常出现的原因又有两个

第一个是因子测试的时间跨度太短，在去掉缺失数据，加上为生成远期收益保留的时间后，数据量就不够了。

我们 <run/> 一下右边的代码，就会看到一个 MaxLossExceed错误。

[click]

原因是在这里，我们只使用了大约半年的数据，而远期收益最长却设置了3个月，再加上计算RSI时，产生的NAN，导致有超过50%的数据被丢弃，从而超过了阈值。

如果我们把periods参数改为(1,5,10)，就会看到这个错误会消失。

[click]

我们修改后再<run/>一下。现在，max loss就没有超过阈值了。

[click]

另一个原因会复杂一点。它发生在因子是离散值、且分布特别不均衡的情况下。在这种情况下，如果指定按quantiles进行分层，就会出现边界值重复，最终导致分层失败，也会报这个错误。

我们看一下右边的例子。

[click]

这次我们使用的因子是RSI排序因子，它不是取每一期的RSI，而是取当前的RSI，在过去win期中的排序。这里我们使用的win是20，所以，最终得到的因子是是0到19的整数值。

[click]

这段代码与上一个示例完全一样，甚至在条件上还有所放宽。惟一不同的是，最终生成的因子是离散值，也就是整数值。


我们先<run/>一下，看看会出什么状况。

报出了MaxLossExceed错误，有28.4%的数据在远期收益对齐时被drop掉。

但我们注意到，与之前的示例相比较，新增加了一个分层阶段的损失，大约24.4%。

分层阶段的损失是怎么回事呢？在它的日志里有进一步提示。

这句话告诉我们通过将max_loss设置为0，就可以看到更多的信息。

[click]

现在，我们就按提示修改代码，在调用get_clean_factor_and_forward_returns函数的时候，加上max_loss=0这个参数。

再<run/>一次

注意看，错误类型变成了ValueError。我们把错误信息滚动到最后，就可以看到很详尽的提示。

[click]

这段提示是什么意思呢？我们看到，它在分层边界上，出现了两个19。分层的边界值是不允许重复的。

我们前面讲过，by quantiles要保证分层后，每个分层的样本数大致相同。现在的问题是，因子值为19的太多了，如果不把它们分成几层，就会导致每一层的样本数不相等；但如果把它们分成多层，又出现了边界值重复问题。

遇到这类问题，一般有以下两种方式解决。

[click]

第一个方案，可以减少quantiles数量。不过，这只在分层本来就很多的情况下才适用。在示例中，最初的quantiles也就只有5层，所以，在本例中不适用。

[click]

第二个解决方案，是使用bins来代码quantiles。quantiles分层要保证每个分层样本数大致相等，限制比较多，使用bins分层就没有这个限制。

-->
