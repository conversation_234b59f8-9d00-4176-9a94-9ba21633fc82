---
clicks: 5
---

<div class="abs w-full mt-40 flex items-center justify-center"
     v-motion
     :enter="{opacity: 1}"
     :click-1="{opacity: 0}">

get_clean_factor_and_forward_returns(factor, prices,...)

</div>

<div class="abs mt-40 ml-80 w-full"
     v-motion
     :enter="{opacity: 0}"
     :click-1-2="{opacity: 1}">

## Open Vs Close
</div>

<div class="abs mt-40 ml-80 w-full"
     v-motion
     :enter="{opacity: 0}"
     :click-2-3="{opacity: 1}">

## Open Vs Close
## Cheat On Close (COC)
</div>

<div class="abs mt-40 ml-85 w-full"
     v-motion
     :enter="{opacity: 0}"
     :click-3-5="{opacity: 1}">

## 时间对齐
</div>

<div class="abs mt-50 ml-82 w-full"
     v-motion
     :enter="{opacity: 0}"
     :click-4-5="{opacity: 1}">

prices = df.open.shift(-1)
</div>

<CountdownTimer :at=5 :count=15 q="使用收盘价，选择哪一个？：<br>1. df.close<br>2. df.close.shift(-1)<br>3. df.close.shift(1)"/>

<!--

在第4章我们详细地介绍了这个方法的其它函数，但对factor和prices参数，只讲了它的格式问题。

现在，我们来讨论prices数据究竟应该是什么数据。

[click]

我们的第一直觉可能是，要使用与它的索引时间对应的收盘价格。但是，因子数据一般是根据收盘价格计算的。因此，我们不可能做到既以收盘价来计算因子，又以收盘价来买入。因为收盘价一旦报出，就意味着当期的交易结束了。

因此，我们一般使用下一期的开盘价来计算。这是距因子计算完成后，距离信号发出最近的时间点。

[click]

当然，不是绝对不能使用当期收盘价来作为买入价。在backtrader中，就存在这样的设置，允许你以cheat on close的价格来买入。

如果我们策略的资金量小，这样是允许的。以A股为例，如果我们从第14：56分结束时，开始计算策略信号，此时价格基本固定，我们还有3分钟的时间下单。在正式下单前，如果价格发出较大变化，我们还可以对个别标的放弃，让其它标的顶上来。所以，是可能以收盘价买入的。

[click]

假设我们已经决定要使用下一期的开盘价，那么，我们如何将这个数据传给Alphalens呢？

在Alphalens中，计算远期收益的数据是通过prices传入的，它只包含纯粹的数字，而没有任何元数据信息。所以，它可以是当期的收盘价，可以是下一期的开盘价，也可能是下一期的收盘价，全靠我们通过prices的索引和数值来决定。

Alphalens的约定是，在prices中的T0的价格，会对应到因子数据中的T0时间点。

[click]

因此，如果我们要使用下一期的开盘价，假设是通过df.open来表示的，那么我们应该传入df.open.shift(-1)。

这样，我们才能把因子的T0与开盘价的T1对齐。

[click]

现在请大家思考一个问题。如果我们决定使用收盘价，而不是开盘价，那么，我们如何将这个数据传入Alphalens呢？请大家思考15秒。

好，答案是，我们一般直接使用df.close，即使用当期的收盘价。这里绝对不能使用的是第3个选项，因为它会导致未来数据。

第二个选项一般情况下不推荐，但在信号出现后几天才交易，这样的情况也是有的，所以Alphalens并不禁止这样的用法。

-->
