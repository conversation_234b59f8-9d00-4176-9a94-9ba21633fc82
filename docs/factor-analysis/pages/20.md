---
aspectRatio: 16/9
title: 第 20 课 驭浪者 -- 顶底预测模型
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--
在上一章中，我们介绍了一个定价模型。它是基于这样的假设，如果股价的波动能够拟合成直线或者二次曲线，那么我们就能根据曲线方程，对下一个价格进行预测。线性回归在投资中运用的非常广泛，所以，很自然地，这个模型有一定的预测能力，因为它是基于线性回归，并进行了一些站得住脚的改进。

但不是所有的波动都满足线性回归。趋势的尽头，就是反转。如何预测反转呢？

这就是我们今天的模型要解决的问题。这个模型也是对lightGBM分类任务的一次应用。
-->

---
src: 20/10.md
title: 策略原理
---

---
src: 20/20.md
title: 顶底查找算法
---

---
src: 20/30.md
title: 标注工具
---

---
src: 20/40.md
title: 模型基类
---

---
src: 20/50.md
title: 增加特征 - V2
---

---
src: 20/60.md
title: 调整标签
---

---
src: 20/70.md
title: 算法优化
---

---
title: end
layout: end
src: 20/80.md
clicks: 1
---
