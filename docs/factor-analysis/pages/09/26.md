---
clicks: 1
---

<div class="abs" v-motion :enter="{opacity:1, scale:1,x:200, y:60}"
    :click-1="{scale: 0.6,x:-100, y:-80}">

$$
\begin{align*}
\text{Up}_t & = P^H_t - P^H_{t-T} \\
\text{Down}_t & = P^L_{t-T} - P^L_t \\
\text{PLUS\_DM}_t & =
\begin{cases}
\text{Up}_t & \text{if } \text{Up}_t > \text{Down}_t \text{ and } \text{Up}_t > 0 \\
0 & \text{otherwise}
\end{cases} \\
\text{MINUS\_DM}_t & = 
\begin{cases}
\text{Down}_t & \text{if } \text{Down}_t > \text{Up}_t \text{ and } \text{Down}_t < 0 \\
0 & \text{otherwise}
\end{cases}
\end{align*}
$$

$$
\text{ADX} = 100 \times \text{SMA}(N)_t \left| \frac{\text{PLUS\_DI}_t - \text{MINUS\_DI}_t}{\text{PLUS\_DI}_t + \text{MINUS\_DI}_t} \right|
$$
</div>

<div class="abs" v-motion :enter="{opacity: 0,x:400}"
    :click-1="{opacity: 1}">

![](https://images.jieyu.ai/images/2024/10/adx-handwriting.png)
</div>

<!--
ADX 由Welles Wilder研发。这个指标的计算比较复杂。我们看这个示意图来说明

[click]

我们以5天为周期。它是先找出过去5天的最高点，划一条到今天最高点的线，这就是上涨趋势线。找出过去5天的最低点，划一条到今天最低点的线，这就是下跌趋势线。

如果上涨趋势线大于零，并且它的绝对值大于下跌趋势，那么，上涨趋势成立；

如果下跌趋势线小于零并且其绝对值大于上涨趋势下，那么下跌趋势成立。

到目前为止，它实际上是把这一区间当成一个三角形整理来看待，并且确定它的方向。

然后通过ATR来去掉量纲。我们今天在后面的章节会讲ATR这个指标。

最后进行归一化和滑动平均，就得到了这个指标。

如果说上一节我们讲的ultra oscilator是在找压力和支撑，从而看趋势反转，那么这个指标就是在找趋势。

从因子测试的角度来看，似乎很难直接使用它。这也可能跟使用的参数相关。我在测试中使用的移动平均是14天，在A股，可能只有少数标的能有这么长的趋势。大家可以自己换参数试试。

-->
