---
clicks: 4
layout: default
---

<div class="abs top-20%" v-motion :enter="{opacity: 1}" :click-1="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/bollinger-band-visualize.jpg)
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:200, y:100,boxShadow:''}" 
        :click-1="{opacity: 1, scale:1.2, boxShadow: '10px 10px 20px #888',zIndex:1}"
        :click-2="{x: -100, boxShadow:'', zIndex:-1, opacity: 0.3}"
        :click-3="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/bbands-alpha.jpg)
</div>


<div class="abs w-50%" v-motion :enter="{opacity: 0, x:500, y:100,zIndx:-1,boxShadow:''}" 
    :click-1="{opacity:0.3}"
    :click-2="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888'}"
    :click-3="{opacity:0.3, x: -100,zIndex:-1,boxShadow:''}"
    :click-4="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/bbands-mean-wise.jpg)
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:500,y:100,boxShadow:'',zIndex:-2}"
    :click-2="{opacity: 0.3}"
    :click-3="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888', zIndex:1}"
    :click-4="{opacity:0.3, x: -100,zIndex:-1,boxShadow:''}"
    :click-5="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/bbans-cumulative.jpg)
</div>

<!--

这是John Bollinger发明的一个技术指标，有着比较坚实的统计学基础。它是以收盘价的简单移动平均，以及两条标准差线组成的。

我们一般不直接使用bollinger band作为因子。因为它类似于ultimate oscillator，交易信号发生在资产价格穿越上下轨时。它有一个很好的等价因子，即zscore，所以，我们可以使用zscore来进行因子分析。

使用zscore时，因子也会出现双峰特征，这个跟ultimate oscillator是一样的。所以，我们也要对它进行纯化。


[click]

经过纯化，我们能得到非常好的结果。


[click]
这是alpha

[click]
这是分层均值收益

[click]
这是累计收益

-->
