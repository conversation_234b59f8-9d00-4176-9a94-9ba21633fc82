---
clicks: 12
layout: default
---

<div class="abs w-full" v-motion :enter="{opacity: 1}"
    :click-3="{opacity:0}">

$$
\text{Typical Price}_t=(H_t+L_t+C_t)\div 3 \\
Moving Average = (\sum_{i=1}^PTypical Price) \\
Mean Deviation = (\sum_{i=1}^P|Typical Price - Moving Average|)
$$

</div>

<FlashText class="abs text-4xl left-180 w-full" v-motion 
        :enter="{opacity: 0}"
        :click-1-2="{opacity: 1}">vwap</FlashText>


<div class="abs w-full top-70" v-motion :enter="{opacity: 0}"
    :click-2-3="{opacity:1}">

$$
CCI=\frac{Typical Price - MA}{.015 * Mean Deviation}
$$
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:200, y:100,boxShadow:''}" 
        :click-3-4="{opacity: 1, scale:1.2, boxShadow: '10px 10px 20px #888',zIndex:1}">

![](https://images.jieyu.ai/images/2024/10/cci-payh.jpg)
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:200, y:100,boxShadow:''}" 
        :click-4-5="{opacity: 1, scale:1.2, boxShadow: '10px 10px 20px #888',zIndex:1}">

![](https://images.jieyu.ai/images/2024/10/cci-alpha-no-adjust.jpg)
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:200, y:100,boxShadow:''}" 
        :click-5-6="{opacity: 1, scale:1.2, boxShadow: '10px 10px 20px #888',zIndex:1}">

![](https://images.jieyu.ai/images/2024/10/cci-distplot-no-adjust.jpg)
</div>

<div class="abs w-50% left-30% top-20%" v-motion :enter="{opacity:0}"
    :click-6-7="{opacity: 1}">

```python
def calc_cci(df, n):
    cci = ta.CCI(df.high, df.low, df.close, n)
    cci[cci < 0] = np.nan
    return cci * -1
```
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:200, y:100,boxShadow:''}" 
        :click-7-8="{opacity: 1, scale:1.2, boxShadow: '10px 10px 20px #888',zIndex:1}">

![](https://images.jieyu.ai/images/2024/10/cci-displot-pured.jpg)
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:200, y:100,boxShadow:''}" 
        :click-8="{opacity: 1, scale:1.2, boxShadow: '10px 10px 20px #888',zIndex:1}"
        :click-9="{x: -100, boxShadow:'', zIndex:-1, opacity: 0.3}"
        :click-10="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/cci-annual-alpha-with-long-short.jpg)
</div>


<div class="abs w-50%" v-motion :enter="{opacity: 0, x:500, y:100,zIndx:-1,boxShadow:''}" 
    :click-8="{opacity:0.3}"
    :click-9="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888'}"
    :click-10="{opacity:0.3, x: -100,zIndex:-1,boxShadow:''}"
    :click-11="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/cci-pured-mean-period-wise-return.jpg)
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:500,y:100,boxShadow:'',zIndex:-2}"
    :click-9="{opacity: 0.3}"
    :click-10="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888', zIndex:1}"
    :click-11="{opacity:0.3, x: -100,zIndex:-1,boxShadow:''}"
    :click-12="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/cci-long-short-cumulative.jpg)
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, boxShadow:'', x:600, y:45,zIndex:-3}" 
    :click-10="{opacity: 0.3}"
    :click-11="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888', zIndex:1}"
    :click-12="{opacity: 0}">

![](https://images.jieyu.ai/images/2024/10/cci-pured-annual-alpha.jpg)
</div>

<div class="abs w-70% left-15% top-20%" v-motion :enter="{opacity: 0}"
    :click-12="{opacity:1}">

```python
def calc_cci(df, n):
    cci = ta.CCI(df.high, df.low, df.close, n)
    cci[cci < 0] = np.nan
    return cci * -1
    
alphatest(2000, start, end, calc_factor= calc_cci, args=(14,), max_loss=0.55)
```
</div>

<!--
CCI 是Donald Lambert 发明的技术指标，首次发表于 1980 年的《商品期货》杂志。

跟我们前面介绍的指标相比，它比较新颖之处，在于使用了所谓的典型价格，即最高价、最低价和收盘价的平均值。

[click]

typical price实际上是对vwap的一种模拟。是在数据不完整时的权宜之计。

[click]

其它部分比较类似于PPO。在计算偏离时，PPO使用长短均线之差，而CCI用的是典型价格与它的移动平均之差，这一点类似于bias。

最后，公式除于偏离值，实现去量纲的操作，再通过魔术数字0.015完成了一个缩放。这个缩放的目的，是为了让指标刚好在穿越正负100时，能恰当地发出信号。

但不会所有的资产都能通过这样的缩放，达到指标在穿越正负100时，能发出交易信号，对吧？所以，这就是我们做量化的意义所在。因为量化可以设置自适应参数。


[click]

这是CCI指标图。

这个指标的用法是，在图中，当指标下穿-100时，出现卖出信号，如绿线所示；当指标上穿100时，出现买入信号，如红线所示。

我们接下来看看直接把这个指标当成因子，进行因子检验的情况。

[click]

看起来数据不太好。这是指标本身不行，还是另有原因？我们先看下它的密度分布图。

[click]

从密度图看，出现了双峰。我们之前的课程讲过，双峰是因子不够纯粹，出现了两种主导因素引起的。大家结合CCI的构建原理，是不是很容易理解这一点？

CCI在构造时，有意让因子在正负100两侧分布，并且只在指标穿越正负100时才有交易意义。

所以，我们要用拿CCI当因子用，就必须先对其进行“纯化”，只取其中的一侧。

[click]

纯化的方法也很简单，就是直接去掉零值一侧的数据。这里我们是把负数去掉。

通过将负的因子设置为np.nan，就会导致alphalens在计算时，将其去掉。这是我们之前讲alphalesn框架时讲过的，大家还记得吗？

[click]

好，经过纯化之后，因子的密度分布图就会呈现这个样子。

[click]

现在我们再来进行因子检验，结果就会非常棒

[click]

分层收益均值图接近完美。大家注意到没有，这里的第1层与第10层的绝对值比较接近，也就是，这个因子的收益不是以做空为主的。

[click]

累计收益图也接近完美。6年收益接近3倍，波动小。这些都是在多空组合情况下得到的。下面我们看下纯多组合。

[click]

纯多组合的年化也能到11.2%。这与我们之前看多空组合时，分层收益均值图的结果是相一致的。一个因子，能在纯多情况下，年化达到这么高的水平，确实是非常优秀的。

大家可以自行验证下，并且深入研究下它的换手率，每次调仓时，是否存在不可买入的情况，等等。

[click]

最后说明一下，因子测试时的一个小知识点。在这里，我们要把max_loss设置得大一点，因为我们干掉了一半的因子。

-->
