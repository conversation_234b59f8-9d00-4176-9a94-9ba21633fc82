---
clicks: 4
---

<div class="abs mt-30" v-motion :enter="{scale: 1}"
    :click-6='{scale: 0}'>

$$
RSI = \frac{EMA_{(U,n)}}{EMA_{(U,n)} + EMA_{(D,n)}} * 100%
$$

</div>

::right::

<div class="abs mr-5" v-motion :enter="{scale: 1}" :click-1="{scale: 0}">

![](https://images.jieyu.ai/images/2023/07/welles_wilder.png)
</div>

<div class="abs mt-30" v-motion :enter="{scale: 0}"
    :click-1-2='{scale: 1}'>

$$
Sortino = \frac{R_p - r_f}{std(R_{down})}
$$
</div>

<div class="abs" v-motion :enter="{scale: 0}"
    :click-2-5='{scale: 1}'>

![](https://images.jieyu.ai/images/2024/10/predict-price-by-rsi-high.jpg)
</div>



<div class="abs" v-motion :enter="{scale: 0}"
    :click-3-4="{scale: 1}">

<v-drag-arrow pos="0,144,166,33" color="red"/>
<v-drag-arrow pos="0,144,169,-95" color="red"/>

<v-drag-arrow pos="200,100,95,29" color="blue"/>
<v-drag-arrow pos="201,100,96,-54" color="blue"/>

<v-drag-arrow pos="280,82,66,-41" color="orange"/>
<v-drag-arrow pos="279,82,70,-20" color="orange"/>
</div>

<div class="abs" v-motion :enter="{scale: 0}"
    :click-4-5="{scale: 1}">

<v-drag-arrow pos="299,-25,51,57" color="orange"/>
<v-drag-arrow pos="200,-43,211,74" color="red"/>

</div>

<!--
RSI指标是Welles Wilder的发明。Wilder发明了许多技术指标，其中RSI, ADX, ATR、抛物线转向等最为著名，被称为RSI之父。福布斯曾称赞他是最杰出的技术交易员。他著有《技术交易系统的新概念》一书。

RSI的取值范围在0到100之间，因此天生适合作为机器学习的因子之一。它被称为相对强弱指标，本质上反映的是资产在区间内的赚钱效应。

[click]

我们可以把这个公式与sortino相对比。右边就是sortino的计算公式。Sortino是一种策略评估指标，是夏普指标的改进版，它也可以作为因子使用。

两者使用的符号体系不同，但本质是都是基于收益数据来进行构建的。在分子构成上，RSI只考虑正收益；sortino考虑的是综合收益；在分母构成上，RSI是把两种收益的绝对值都考虑进来，而sortino只考虑负收益。

但它们本质上计算的都是正收益与负收益之间的某个比值。这个值越大，资产的价格表现就越强。


关于RSI因子，我们前面已经举例讨论了很多次。在这些例子中，我们揭示出了RSI因子强大的盈利能力。其中最好的结果是，在过去6年里，达到年化30%的收益。

由于RSI的因子分析和优化我们已经讨论了很多次，在这里我们就一笔带过。

[click]

关于这个因子，我们设计了一些练习，除了冷启动期、钝化区之外，我们还设计了这样一道题，帮助大家深入理解这个因子。

这道题是这样的，在上升趋势中，资产价格可能会不断创新高，但RSI的取值则是会周而复始的变化。每次达到前期高点附近就可能回调。

[click]

大家从这张图上也可以看出来。多数时候，股价阶段性新高时，也是RSI达到局部极值的时候。

如果我们以RSI前期极值作为下一次RSI的高点，是不是就可以预测出下一次股价能达到的局部高点呢？这也是后面我们进行机器学习时，进行价格预测的一个理论基础。

[click]

这道题的问题是，假设下一期的RSI会到达红色箭头所指的位置，即前期的RIS高点，那么能否反推出来此时的价格是多少？

好，请大家在作业题中去回答这个问题。

除了RSI，对于其它别的因子，也建议按我们练习题的思路，自己深入推演一下因子。这对大家自己优化因子分析、优化策略都会有非常大的帮助。

-->
