---
clicks: 3
layout: default
---

<div class="abs h-full" v-motion :enter="{opacity:1, scale: 0.4, y:-100, x:-200}" :click-1="{scale: 0}">

![](https://m.media-amazon.com/images/I/51FBUORLOBL._AC_UF1000,1000_QL80_.jpg)
<!-- ![](https://images.jieyu.ai/images/2024/10/gerald-appel.jpg) -->
</div>

<div class="abs w-50% left-50% top-30%" v-motion :enter="{opacity:1}" :click-1="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />MACD
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> APO/PPO
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>创立《系统与预测》杂志
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>创立Signalert公司

</div>

<NoteCell init class="hide">

```python
start = datetime.date(2023, 7, 1)
end = datetime.date(2023, 12, 31)
barss = load_bars(start, end, ("000001.XSHE", ))
PAYH = barss.xs("000001.XSHE", level=1)

df = PAYH.copy()
```
</NoteCell>

<div class="abs" v-motion :enter="{opacity:0, scale:1,x:300, y:60}"
    :click-1="{opacity: 1}"
    :click-3="{scale: 0}">

$$
\text{MACD Line} = \text{EMA}{12} - \text{EMA}{26}\\
\text{Signal Line} = \text{EMA}(9, \text{MACD Line})\\
\text{Histogram} = \text{MACD Line} - \text{Signal Line} 
$$
</div>

<div class="abs w-60% mt-40" v-motion :enter="{opacity:0, scale:1,x:200, y:60}"
    :click-2="{opacity: 1}">

```python
macd, macdsignal, macdhist = ta.MACD(df.close,
                                        fastperiod=12,
                                        slowperiod=26,
                                        signalperiod=9)
df['MACD'] = macd
df['MACDSIG'] = macdsignal
df['MACDHIST'] = macdhist
```
</div>

<NoteCell class="abs w-80% h-80% left-10%" v-motion :enter="{opacity:0, y:60}"
    :click-3="{opacity: 1}">

```python
df = PAYH.copy()

macd, macdsignal, macdhist = ta.MACD(df.close,
                                        fastperiod=12,
                                        slowperiod=26,
                                        signalperiod=9)
df['MACD'] = macd
df['MACDSIG'] = macdsignal
df['MACDHIST'] = macdhist

cols = ["close", "MACD", "MACDSIG", "MACDHIST"]
axes = df[cols].plot(figsize=(14, 8),
               rot=0,
               subplots=True,
                title=cols,
               legend=False)

axes[-1].set_xlabel('')
sns.despine()
plt.tight_layout()
```
</NoteCell>



<!--
MACD 是一种广泛使用的技术分析指标，用于识别趋势的变化和动量。它是由Gerald Appel在1979年开发的. Gerald Appel还是APO/PPO指标的发明者，紧接着我们就会介绍这两个指标。


[click]

MACD 通过计算两条指数移动平均线（EMA）之间的差异来生成信号。它由三部分组成，即macd， signal line和histogram。

对于这个指标，我们要注意两点。第一，我们要关注talib是如何计算和如何返回结果的。

[click]

talib的macd函数会返回三个值，分别对应于MACD, signal Line 和 Histogram，这点跟其它指标不一样。很多指标都是只返回一个序列的。

我们来看一下talib中macd的计算结果 

[click]

<run></run>

在这里，hist线被画成了折线。在行情软件中，一般会绘制成为柱状线，如果大于零，会显示为红色，否则为绿色。一些人会看红柱变为绿柱时，认为出现信号 。

第二点, macd是一个绝对值，不同的资产，它的macd的取值范围也大不相同。因此macd无法直接当成因子使用。

当然，这不妨碍我们把它发出来的信号，作为一个特征，在机器学习策略中使用它。

-->
