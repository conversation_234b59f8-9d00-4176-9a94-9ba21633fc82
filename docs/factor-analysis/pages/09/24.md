---
clicks: 8
---

<NoteCell init class="hide">

```python
start = datetime.date(2023, 7, 1)
end = datetime.date(2023, 12, 31)
barss = load_bars(start, end, ("000001.XSHE", ))
PAYH = barss.xs("000001.XSHE", level=1)

df = PAYH.copy()
```
</NoteCell>

<div class="abs ml-10" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

![](https://images.jieyu.ai/images/2024/10/larry-williams.jpg)
</div>


<div class="abs ml-10" v-motion :enter="{opacity: 0, scale:1}"
    :click-1-2="{opacity: 1, scale:1.1}">

![](https://images.jieyu.ai/images/2024/10/michell-williams.jpg)
</div>

<div class="abs w-full left-55" v-motion :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}">

$$
\text{True Low} = \min(\text{Low}, \text{Previous Close}) \\
\text{True High} = \max(\text{High}, \text{Previous Close}) \\
\text{BP} = \text{Close} - \text{True Low} \\
\text{True Range} = \text{True High} - \text{True Low} \\
\text{Average BP}_n = \frac{\sum_{i=1}^{n} BP_i}{\sum_{i=1}^nTR_i} \\
ULTOSC_t=\frac{4Avg_t(7) + 2Avg_t(14) + Avg_t(28)}{4+2+1} \times 100
$$
</div>

<div class="abs w-full left-70" v-motion :enter="{opacity: 0}" :click-3-4="{opacity: 1}">

![](https://images.jieyu.ai/images/2024/10/ultimate-oscillator.jpg)
</div>

<NoteCell class="abs w-150% mt-10" v-motion :enter="{opacity: 0}"
    :click-4-5="{opacity: 1}">

```python
df['uo'] = ta.ULTOSC(df.high, df.low, df.close, timeperiod1=7, timeperiod2=14, timeperiod3=28)

cols = ['close', 'uo']
ax = df[cols].plot(figsize=(14,4), rot=0, secondary_y='uo', style=['-', '--'])
sns.despine()
plt.tight_layout()
```
</NoteCell>

<div class="abs w-full" v-motion :enter="{opacity: 0,x:250,y:100,boxShadow:''}" 
        :click-5="{opacity: 1, scale:1.2, boxShadow: '10px 10px 20px #888',zIndex:1}"
        :click-6="{x: -100, boxShadow:'', zIndex:-1, opacity: 0.3}"
        :click-7="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/uo-alpha.jpg)
</div>


<div class="abs w-full" v-motion :enter="{opacity: 0, x:500,y:100,zIndx:-1,boxShadow:''}" 
    :click-5="{opacity:0.3}"
    :click-6="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888'}"
    :click-7="{opacity:0.3, x: -100,zIndex:-1,boxShadow:''}"
    :click-8="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/uo-quantile-returns.jpg)
</div>

<div class="abs w-full" v-motion :enter="{opacity: 0, x:500,y:100,boxShadow:'',zIndex:-2}"
    :click-6="{opacity: 0.3}"
    :click-7="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888', zIndex:1}"
    :click-8="{opacity:0.3, x: -100,zIndex:-1,boxShadow:''}">

![](https://images.jieyu.ai/images/2024/10/uo-cumulative-returns.jpg)
</div>

<div class="abs w-full" v-motion :enter="{opacity: 0, boxShadow:'', x:600, y:45,zIndex:-3}" 
    :click-7="{opacity: 0.3}"
    :click-8="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888', zIndex:1}"
    :click-9="{opacity: 0}">

![](https://images.jieyu.ai/images/2024/10/uo-factor-distplot.jpg)
</div>


::right::

<div class="abs w-full mt-20 left-0" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />William's R
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> Ultimate Oscillator
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>How I made one million dollars last year trading commodities
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>1987期货交易大赛冠军，11.37倍回报
</div>

<div class="abs w-full mt-20" v-motion :enter="{opacity: 0}"
    :click-1-2="{opacity: 1}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />Michelle Williams
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>1997期货交易大赛冠军，10倍回报


<FlashText class="abs mt-20 left-10 text-2xl">www.worldcupchampionships.com</FlashText>
</div>

<!--
Ultimate Oscillator（终极振荡器）是由 Larry Williams 在 1976 年发表的。

Larry是个牛人，不打嘴炮的那种。他发明了William's R和 ultimate ocsillator这样两个指标。著有《我如何在去年的期货交易中赢得百万美元》一书。他还是1987年世界期货交易大赛的冠军。在这场比赛中，他以11.37倍回报获得冠军。

更牛的是，在交易上，他们家可谓是一门三杰。

[click]

这是他女儿，michelle williams。她是知名的女演员，前后拿了4个奥斯卡最佳女配提名。更厉害的是，她在1997年也获得了世界期货交易大赛的冠军，同样斩获了10倍收益。在这个大赛的历史上，有这样收益的，总共只有三人，他们家占了俩。

这件事说明，老williams的一些交易技巧，历经10年仍然非常有效。

老williams的儿子是位心理医生，著有《交易中的心理优势》一书。如果你立志在量化交易上做出自己的成绩，不妨抽时间研究一下这些牛人的成功秘密。

[click]

这是指标的计算公式。

它旨在通过结合不同时间周期的买入压力来减少虚假信号，从而提供更可靠的超买和超卖信号。Ultimate Oscillator 考虑了三个不同的时间周期，通常为 7 天、14 天和 28 天，以捕捉短期、中期和长期的市场动量。

这个公式计算步骤比较多，主要有true low, true high和true ange, bull power等概念。

[click]

用这个图来解释会更清楚。所谓的true range，就是把前收也考虑进行，与当天的最高价、最低价一起，来求一个最大振幅。然后计算从true low到现价的一个涨幅，作为看涨力道。

最后，用看涨力道除以真实波幅，再在一定窗口期内做平均，这样就得到了归一化的看涨力道均值。

最后，它结合长中短三个周期平均，生成最终的指标。

从构造方法来讲，它与RSI最重要的区别是，加入了high和low两个序列的数据。

做过交易的人知道，关键时刻最高价和最低价，都是多空博弈出来的，它是隐含了重要信息的。如果实时盯过盘口的人，可能感受更深。

像最高点，它是主力一口气向上吃掉多少筹码才拿到的这个最高点。上面的筹码吃不掉，最高价就定在这个地方。吃不掉的筹码就是压力位，是更大的资金的成本或者其它什么心理价位。

因此，ultimate oscillator的信息量与RSI相比，是包含了更多的信息量的。希望这部分解读，能对大家今后探索因子起到一定的启迪作用。

[click]

这段代码演示了实际中的uo指标，看起来是什么样的。从视觉上看起来，它跟RSI差不多，都是在一定区间震荡的。

<run></run>

[click]

这个因子在回测中的表现如何？在回测中，从2018年到2023年的6年中，它的alpha年化达到了13.7%，表现还是很优秀的。

[click]

不过因子收益主要由做空贡献。大家这张分层收益图，收益主要由第1层做空时贡献。在纯多的情况下，alpha并不高，只有1.6%，收益主要由beta贡献，所以组合收益的波动比较大。

[click]

在多空组合下，6年的收益达到了2.2倍。

[click]

最后我们看一下因子密度分布图。看上去很符合正态分布。

-->
