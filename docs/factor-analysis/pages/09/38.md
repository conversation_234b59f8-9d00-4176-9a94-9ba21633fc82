---
clicks: 3
---

<div class="abs top-20% left-40%" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

$$
Aroon Up = \frac{n - \text{(Periods Since n Period High)}}{n} \times 100 \% \\
Aroon Down = \frac{n - \text{(Periods since n Period Low)}}{n} \times 100 \% \\
Aroon Oscillator = \frac{\text{Aroon Up}}{\text{Aroon Down}}
$$
</div>

<div class="abs top-20% left-50%" v-motion :enter="{opacity: 0}"
    :click-1-2="{opacity: 1}">

$$
Aroon Up = \frac{n - argmax(High, n)}{n} \times 100 \% \\
Aroon Down = \frac{n - argmin(Low, n)}{n} \times 100 \% \\
Aroon Oscillator = \frac{\text{Aroon Up}}{\text{Aroon Down}}
$$
</div>

<div class="abs top-40% left-40%" v-motion :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}">

```python
def calc_aroon(df, n):
    return -1 * ta.AROONOSC(high=df.high, low=df.low, timeperiod=n)

_ = alphatest(2000, start, end, calc_factor=calc_aroon, args=(14,), bins=5)
```

</div>

<div class="abs w-full top-20% left-50%" v-motion :enter="{opacity: 0}"
    :click-3="{opacity: 1}">

![](https://images.jieyu.ai/images/2024/10/aroon-alpha.jpg)

</div>

<!--

Aroon指标是Tushar Chande提出的另一个因子。Aroon是梵语，意思是黎明的曙光的意思。

这个公式看上去比较复杂。我们改用python语言描述一下。

[click]

现在看得比较清楚，它是在进行坐标运算。是用当前周期分别到前高和前低的周期数来生成一个比值。它表明的是，当前位置是离前高近，还是离前低的时间近。

所以，这个指标跟前面有很大的不同。我们常说，技术指标分析有四大维度，量、价、时、空。这个指标是基于时间维度的。它是基于当前周期与前高、前低的位置，找预测趋势的方向。

[click]

这个指标在talib中的函数叫AROONOSC.

[click]

运行回测的结果，表明这个因子的Alpha在5%左右，是一个可用的因子。这个因子大家可以用我们今天讲的方法，从密度分布，从分层多方面尝试一下优化。

-->
