---
clicks: 4
---

<div class="abs" v-motion :enter="{opacity: 1}"
    :click-3="{opacity: 0}">

![](https://images.jieyu.ai/images/2024/10/<PERSON>-<PERSON><PERSON>s.png)
</div>

<div class="abs w-full left-45% top-20%" v-motion
    :enter="{opacity: 0}"
    :click-3-4="{opacity: 1, scale: 1.5}">

![](https://images.jieyu.ai/images/2024/10/sine-wave-visualize.jpg)
</div>

<div class="abs w-150 top-20 left-30%" v-motion
    :enter="{opacity: 0}"
    :click-4="{opacity: 1}">

```python
sw = barss.groupby(level="asset").apply(lambda x: ebsw(x, 40, 10))
with sns.axes_style('white'):
    sns.distplot(sw)
    sns.despine()

_ = alphatest(2000, start, end, calc_factor=ebsw, args=(40, 10), bins=2) 
```
</div>

::right::

<div class="abs w-full mt-40 left-0" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />Sine Wave
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> MAMA/FAMA
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>President of MESA software
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>Rocket Science For Traders
</div>



<FlashText class="abs text-4xl left-0 top-40" v-motion
    :enter="{opacity: 0}"
    :click-1-2="{opacity: 1}">www.mesasoftware.com</FlashText>

<div class="abs left-0 top-40" v-motion
    :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}">

![](https://images.jieyu.ai/images/2024/10/john-ehlers-trendlimit.gif)
</div>



<!--
Sine Wave是John Ehlers开发的一个有交易信号的指标。

John Ehlers是MESA软件的首席科学家和总裁。他曾是雷神的高级研究员，雷神应该是一家军工企业。他退休后开始研究交易。他是数字信号处理专家，所以写了很多关于使用DSP进行量化交易算法的文章。他的书籍包括MESA，交易市场周期，交易者的火箭科学等。

[click]

在他的网站上，分享了很多他关于技术分析的文章。由于他深厚的DSP背景，所以在信号处理方面有自己很深刻的见解。

[click]

这张图就来自于他网站上的一篇文章，我非常喜欢他的风格和这张图所表达的洞见。

我一直认为，一个成熟的市场，它的总市值应该反应GDP的增长，因此，在总体上它应该是向上的。大家去看A股20年的年线，它确实表现出来这样的规律。

但是，投资者的风格、资金管理的周期不同，加上经济本身也有周期，就会导致资产价格会叠加周期性波动。

所以，如果我们以波形的角度来考察资产价格的变化，它就应该被分解为一条向上的直线，再叠加许多角度偏转了的正弦波。我想，我们做量化最终不能违背经济规律。我们寻找的规律，无非是经济规律的一部分，在某个时间点上一种体现。

[click]

从图形上看，这个指标确实出现明显的周期性特征，就象一个方波。

Sine wave这个指标没有出现在talib库中。它依赖的希尔伯特变换，以及瞬时趋势线的计算都出现在了这个库中。这可能是某些知识产权方面的原因。这个指标是trading view上的付费指标。

[click]

在教材里，我给大家提供了一个其他人的实现，是否完全还原了sine wave指标，我不太确定。这里主要是帮大家打开思路。

最后，这个因子的年化alpha是3%左右，不算特别好。主要原因可能在于它信号出得比较少。它只在震荡期有效。

-->
