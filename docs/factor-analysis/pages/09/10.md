---
clicks: 5
right: 60%
---

## Talib 功能分组

<v-switch>
<template #1-10>

## 冷启动期
</template>

<template #5-10>

## 钝化区
</template>
</v-switch>

::right::

<NoteCell v-motion :enter="{scale: 1}" :click-1="{scale: 0}">

```python
import talib as ta
function_groups = ['Overlap Studies',
                   'Momentum Indicators',
                   'Volume Indicators',
                   'Volatility Indicators',
                   'Price Transform',
                   'Cycle Indicators',
                   'Pattern Recognition',
                   'Statistic Functions',
                   'Math Transform',
                   'Math Operators']

talib_grps = ta.get_function_groups()
cols = ["Function Group", "# Indicators"]
pd.DataFrame([(k, len(v)) for k,v in talib_grps.items()], columns=cols)
```
</NoteCell>

<Array data="1,2,3,4,5,6,7" class="abs ml-20 mt-10 h-100px w-350px" 
        v-motion 
        :enter="{scale: 0}"
        :click-1-4="{scale: 1}"/>

<Array data="nan,nan,nan,nan,3,4,5" class="abs ml-20 mt-40 h-100px w-350px" 
        v-motion 
        :enter="{scale: 0}"
        :click-2-4="{scale: 1}"/>

<FlashText class="abs mt-80 left-50 text-3xl"
        v-motion :enter="{scale: 0}" :click-3-4="{scale: 1}">Max_Loss</FlashText>

<FlashText class="abs mt-10 text-4xl"
        v-motion :enter="{scale: 0}" :click-4-5="{scale: 1}">从第几个数起<br>你计算的RSI才会与行情软件一致？</FlashText>

<div v-motion class="abs"
    :enter="{scale: 0}"
    :click-5-6="{scale: 1}">

![](https://images.jieyu.ai/images/2024/10/rsi-passivation.png)
</div>



<!--

talib主要有两大类功能。第一类是技术指标类，第二类是模式识别类。所谓的模式识别，就是指的像早晨之星，三只乌鸦这样的形态识别。通过前面的学习，大家应该了解到，很难把形态识别的结果当成因子。因为他们在取值上是离散的和稀疏的，不便于进行分层回测。但是，这并不意味着形态识别没有价值，只是它们不太适用用alphalens来进行回测而已。

所以，在本课中，我们主要介绍talib的技术指标类函数。

在我们的课程环境下已经安装好了talib。如果大家自己不会安装的话，可以课后联系我。

<run></run>

talib对它实现的方法进行了归类。对我们来说，可能数学变换类、数学运算类和统计类不需要了解，在本章中，不需要了解模型识别类，其它的都有必要了解。

[click]

在深入到具体的技术指标之前，我们先来介绍两个基础概念，冷启动期和钝化区。在因子分析中，两者都会对分析结果造成一定的影响，在实盘中，可能造成的影响就更大。

我们先看一个简单的示例，求移动平均。如果我们对这样一组数据，求它的5日平均，就会得到下面的结果

[click]

在结果中，前4个数据都是nan。也就是，要得到有意义的移动平均值，必须等待4个周期。那么前4个周期就是冷启动期。

[click]

冷启动期会导致因子分析时，记录被丢掉，从而对max_loss产生贡献。

[click]

但是冷启动期不仅仅只有这些。有一些技术指标是递归计算的，比如RSI，T期的指标依赖于T-1期，T-1期又依赖于T-2期，以此类推。很少有文献去研究这种传递依赖的影响有多大。

我们在练习中也设计了一个思考题，引导大家深入思考，它倒底会有多大影响。

[click]

我们再来看钝化的概念。在这个图当中，蓝色的线是收盘价，黄色的线是RSI，绿色的线是每日收益。

我们看到，大约在10月15日以后，收盘价仍然在直线上涨，但对应的RSI几乎一动不动。技术指标的变化不能反映价格的变化，这种情况就是钝化。

在传统的技术分析中，指标一旦钝化，就会失去信号响应能力。

那么它对因子分析有何影响？它对分层模型的影响有限，只要计算还能分辨出两个数值之间的差，也就是说，t1和t2之间的差值只要能大于1e-7，分层模型就能继续工作。但是此时的IC模型和线性回归模型都会失灵。

好，这是很多技术指标都有的两个特性。我们先介绍到这里。

-->
