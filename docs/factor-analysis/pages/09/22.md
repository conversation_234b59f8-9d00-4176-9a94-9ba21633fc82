---
clicks: 3
layout: default
---

<NoteCell init class="hide">

```python
start = datetime.date(2023, 7, 1)
end = datetime.date(2023, 12, 31)
barss = load_bars(start, end, ("000001.XSHE", ))
PAYH = barss.xs("000001.XSHE", level=1)

df = PAYH.copy()
```
</NoteCell>

<div class="abs mt-10 left-50" v-motion :enter="{scale: 1,x:0, y:100}" 
    :click-1="{x: -600, scale:0.5}"
    :click-2-3="{scale:1, x:0}">

$$
 \text{CMO} = 100 \times \frac{\text{Sum of Gains} - \text{Sum of Losses}}{\text{Sum of Gains} + \text{Sum of Losses}} 
$$

</div>

<div class="abs h-70% w-50%" v-motion :enter="{x: 1600, scale:1}" 
        :click-1="{x:0}"
        :click-2="{scale:0}">

<FlashText class="abs w-full left-150 mt-30 text-3xl">CMO</FlashText>
<FlashText class="abs w-full left-150 mt-40 text-3xl">Aroon</FlashText>
<FlashText class="abs w-full left-150 mt-50 text-3xl">stockRSI</FlashText>
<FlashText class="abs w-full left-150 mt-60 text-3xl">VIDYA</FlashText>

![](https://images.jieyu.ai/images/2024/10/tushar-chande.png)

</div>

<div class="abs mt-60 left-50" v-motion :enter="{scale: 0}" :click-2-3="{scale:1}">
$$
RSI = \frac{EMA_{(U,n)}}{EMA_{(U,n)} + EMA_{(D,n)}} * 100%
$$
</div>

<NoteCell class="abs mt-10 w-80% h-80%" v-motion :enter="{scale: 0}" :click-3="{scale:0.9}">

```python
df['rsi'] = ta.RSI(df.close, 6)
df['cmo'] = ta.CMO(df.close, 6)

ax = df[['rsi','cmo']].plot(figsize=(14,10), secondary_y=['cmo'], style=['-', '.'])
```
</NoteCell>

<!-- 
这是Tushar Chande提出来的一个因子。

[click]

Tushar Chande是一名技术分析师，他发明了 Aroon，StochRSI，CMO等技术指标。他自己先后创设了好几家资管公司。我们再回到CMO公式

[click]

如果你对RSI的公式还有印象的话，你会发现，CMO的公式与RSI的公式很像，对吧？

它们的主要差别是，RSI在分子上只使用了正收益，CMO使用了两者的差分，缩小了因子。另外，一个使用EMA，一个使用sum，sum实际上等价于简单平均，对吧？所以,RSI会更灵敏一点。

我们在本课程中，有点打破talib文档的归类方式，我们会把各种相似的指标放在一起讲。一方面，方便大家举一返三，快速掌握这些同类的因子；二来帮助大家了解各类因子是如何发明的、如何迭代的，这对我们自己未来进行研究很有好处。

[click]

闲话少说，我们通过这段代码来验证一下，看看它们之间的差异究竟有多大。

<run></run>

图中的实线是RSI，红点则是CMO。从这个图来看，我们简直要怀疑代码中是否把两个指标弄混了。但事实上就是，CMO几乎就是RSI的线性缩放。

好，既然如此，我们也就不多介绍这个指标了。

-->
