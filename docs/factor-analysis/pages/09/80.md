---
clicks: 4
---

<NoteCell init class="hide">

```python
np.random.seed(78)

code = "000001.XSHE"
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

barss = load_bars(start, end, (code, ))
PAYH = barss.xs(code, level=1).loc["2021-01-01":,]

def plot_ma(names, win, *args):
    df = PAYH.copy()[-120:]
    for name in names:
        if name in ("MAMA", "FAMA"):
            mama, fama = ta.MAMA(df.close, fastlimit = args[0], slowlimit=args[1])
            df["MAMA"] = mama
            df["FAMA"] = fama
            continue
        df[name] = getattr(ta, name)(df.close, win)

    cols = ['close', *names]
    ax = df[cols].plot(figsize=(14,7), rot=0)
    sns.despine()
    ax.set_xlabel('')
    plt.tight_layout()
```
</NoteCell>



<div class="abs top-20%" v-motion :enter="{color: 'black',scale:1,x:0,y:0}"
    :click-1="{scale: 0.7, x:-70, y:-50, color:'red'}"
    :click-2="{color:'black'}">

* Simple Moving Average (SMA)
* Weighted Moving Average (WMA)
* Triangle Exponential Moving Average (TRIMA)
</div>
<div class="abs top-40%" v-motion :enter="{color: 'black',scale:1,x:0,y:0}"
    :click-1="{scale: 0.7, x:-70, y:-80}"
    :click-2="{color:'red'}"
    :click-3="{color:'black'}">

* Exponential Moving Average (EMA)
* Double Exponential Moving Average (DEMA)
* Triple Exponential Moving Average (TEMA)
</div>
<div class="abs top-60%" v-motion :enter="{color: 'black',scale:1,x:0,y:0}"
        :click-1="{scale: 0.7, x:-70, y:-110}"
        :click-3="{color:'red'}">

* Kaufman Adaptive Moving Average (KAMA)
* MESA Adaptive Moving Average (MAMA)
* Fractal Adaptive Moving Average（FAMA）
</div>

::right::

<NoteCell class="abs w-170 left--50 top-10" v-motion :enter="{scale:0}" :click-1-2="{scale:1}">

```python
plot_ma(["SMA", "WMA", "TRIMA"], 10)
```
</NoteCell>

<NoteCell class="abs w-170 left--50 top-10" v-motion :enter="{scale:0}" :click-2-3="{scale:1}">

```python
plot_ma(["EMA", "DEMA", "TEMA"], 10)
```
</NoteCell>

<NoteCell class="abs w-170 left--50 top-10" v-motion :enter="{scale:0}" :click-3-4="{scale:1}">

```python
plot_ma(["KAMA", "MAMA", "FAMA"], 10, .5, .05)
```
</NoteCell>

<div class="abs left--20" v-motion :enter="{scale:0}" :click-4-5="{scale:1}">

![](https://images.jieyu.ai/images/2023/06/ma-phase-lag.png)
</div>

<!--移动平均线属于技术指标预处理的一部分，用来平滑、过滤噪声，它本身不能直接作为因子。
这里我们主要展示一下各种移动平均线的形态。

[click]

这一组是简单移动平均，SMA
加权移动平均，WMA。加权的方式我们在alpha101中讲过，是一种线性衰减的方式，即以n周期为例的话，每一期的权重依次是1/n,2/n,...直到n-1/n。
trima是在sma的基础上，再运用一次sma

[click]
这一组是指数加权移动平均。它们的特点是，使得最近的元素权重占比更大，从而提高响应灵敏度。

[click]

KAMA是Perry Kaufman发明的自适应移动平均线算法。一般的移动均线在计算时，滑动窗口的大小是不变的。但在kama中，这个窗口会根据市场噪声和波动率来进行调整。

MAMA和FAMA都是John Ehlers发明的移动均线算法。在talib当中，它们是成对计算的。在计算中，使用了希尔伯特变换。FAMA在计算中还利用了分形计算的特点。

MAMA发表于2001年，是一个相当新的算法，它也是一种自适应均线，据称能降低均线与真实价格之间的延时。

[click]

什么是均线与真实价格之间的延时呢？大家注意看这张图。蓝色线是收盘价序列，红色线是它的简单移动平均。我们注意看两个时间序列趋势改变的地方，即它们的顶点。均线出信号要比收盘价序列晚了10天，这10天就是移动平均线的窗口。

在量化交易中，我们可能要利用均线来平滑噪声，然后找到像图中这样的拐头的趋势；但是，延迟又导致了信号出的太晚，信号的作用下降。

KAMA、MAMA等着手解决的就是这样的问题。

有这么多均线算法，到底要用哪一个？

在使用中，如果我们要直接解决一个具体的问题，刚好KAMA,MAMA这些可用，我们就应该使用它们；但是，如果我们是在开发一个基础库，还不清楚它要解决什么具体问题，就应该主要使用SMA，以保留更多的原始信息

SMA包含了真实成本价信息，其它的算法或多或少都改变了这一信息。在开发底层应用时，过早优化是错误的。

-->


