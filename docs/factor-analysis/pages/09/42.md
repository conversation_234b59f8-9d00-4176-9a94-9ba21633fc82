---
clicks: 1
layout: default
---

<div class="abs top-20% w-full left-5%">
$$
K = \left( \frac{C_0 - \text{L}{n}}{\text{H}{n} - \text{L}_{n}} \right) \times 100\% \\
D = \text{SMA}(K, m) \times 100\%
$$
</div>

<div class="abs w-70% left-20% top-40%" v-motion :enter="{opacity: 0}" :click-1="{opacity:1}">

```python
def calc_stoch(df, n, m):
    k, d = ta.STOCH(df.high, df.low, df.close, fastk_period=n, slowk_period=m)
    return d/k

_ = alphatest(2000, start, end, calc_factor=calc_stoch, args=(14, 3))
```
</div>

<!--
这是George Lane于1950年代发明的一个指标。它非常接近William's R，特别是在K值计算部分。不过它最后除了一个k值的移动平均，这使得对它的理解变得困难了。

这个指标当成因子，在2021年到2023年间的回测中，表现不错。但长期表现一般。

[click]

在计算时，要注意talib中的STOCK函数并不是直接返回stoch指标，而是返回k,d序列，我们要自己计算stoch值。
-->
