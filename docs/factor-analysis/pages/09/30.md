---
clicks: 5
layout: default
---

<div class="abs left-30% top-20%" v-motion :enter="{opacity: 1}"
    :click-2="{opacity: 0}">

$$
\text{PPO} = \left( \frac{\text{短期 EMA} - \text{长期 EMA}}{\text{长期 EMA}} \right) \times 100\%
$$
</div>

<div class="abs left-30% top-40%" v-motion :enter="{opacity: 0}"
    :click-1-2="{opacity: 1}">

$$
\text{MACD Line} = \text{EMA}{12} - \text{EMA}{26}\\
$$
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:200, y:100,boxShadow:''}" 
        :click-2="{opacity: 1, scale:1.2, boxShadow: '10px 10px 20px #888',zIndex:1}"
        :click-3="{x: -100, boxShadow:'', zIndex:-1, opacity: 0.3}"
        :click-4="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/ppo-alpha.jpg)
</div>


<div class="abs w-50%" v-motion :enter="{opacity: 0, x:500, y:100,zIndx:-1,boxShadow:''}" 
    :click-2="{opacity:0.3}"
    :click-3="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888'}"
    :click-4="{opacity:0.3, x: -100,zIndex:-1,boxShadow:''}"
    :click-5="{opacity:0}">

![](https://images.jieyu.ai/images/2024/10/ppo-mean-wise.jpg)
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, x:500,y:100,boxShadow:'',zIndex:-2}"
    :click-3="{opacity: 0.3}"
    :click-4="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888', zIndex:1}"
    :click-5="{opacity:0.3, x: -100,zIndex:-1,boxShadow:''}">

![](https://images.jieyu.ai/images/2024/10/ppo-cumulative.jpg)
</div>

<div class="abs w-50%" v-motion :enter="{opacity: 0, boxShadow:'', x:600, y:45,zIndex:-3}" 
    :click-4="{opacity: 0.3}"
    :click-5="{opacity:1, scale:1.2, x:250, boxShadow: '10px 10px 20px #888', zIndex:1}"
    :click-6="{opacity: 0}">

![](https://images.jieyu.ai/images/2024/10/ppo-distplot.jpg)
</div>

<!--

PPO（Percentage Price Oscillator）是用长短均线之差进行某种缩放后的结果。它的思想是，资产的价值可以用它的价格均线来描述。因此，短均线应该向长均线回归。

所以，上涨趋势中，PPO为正，下跌趋势中，PPO为负。如果进行因子检验，我们需要将PPO取反，因为我们需要在PPO达到最大值时做空，在PPO最小的时候做多。

PPO是APO（Absolute Price Oscillator）的百分比形式。这里的分子，就是APO。

[click]

而APO实际上就是MACD。我们前一节讲过，MACD不能用来作为因子，必须找到某种办法去掉量纲，以便在不同的资产之间进行比较。PPO就提供了这样一个方案。

[click]

这是ppo的年化alpha。参数是多空组合，从2018年到2023年。beta几乎为零，所以它的波动比较小。

[click]

这是分层收益均值图。它的问题也是，收益主要来自于做空高位股。在A股，高位做空容易，低位做多难，所以易跌难涨。这是一个通病。但如果我们把这个指标运用到期货或者加密货币上，应该有较好的结果。

[click]

这是累积收益图。

[click]

这是因子密度分布图。从这个图来看，我们在进行因子检验时，应该可以通过调整分层方式，来获得更好的收益。

-->
