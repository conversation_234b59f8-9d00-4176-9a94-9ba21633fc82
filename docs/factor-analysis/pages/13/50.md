---
clicks: 1
---

<FlashText v-click="[0,1]"
           class='abs mt-1/4 text-center w-full text-3xl'>
自然是连续的，还是不连续的？
</FlashText>

<Profile class='abs w-200px h-200px mt-40'
         :enter='{opacity: 0,x:200}'
         :click-1='{opacity: 1}'
         img=''
         color="#f0f0f0"
         row1='回归'
         row2=''
         row3=''/>

<Profile class='abs w-200px h-200px mt-40'
         :enter='{opacity: 0,x:600}'
         :click-1='{opacity: 1}'
         img=''
         color="#101010"
         row1='分类'
         row2=''
         row3=''/>

<!--
任何一个有用的机器学习模型，归根结底都是对现实世界的一个建模。如果我们对世界刨根问底，追踪溯源，这世界究竟应该是什么样子的呢？

我们可以对这个问题有若干种回答，但其中之一，一定是基于自然是连续的、还是离散的这一问题的回答。

关于这个问题的回答，贯穿了光的波动说、粒子说的斗争，最终导致了量子力学的诞生。但这个问题影响的不只是理论物理，也影响到机器学习模型的设计。

为什么呢？

这涉及到机器学习模型的底层设计中的两个问题，一个是损失函数，一个是评估函数。我们会在第14课中介绍这两个概念。在这里，我们要知道，如果我们建模的对象是离散且有限的数值，和连续的数值，在损失函数和评估函数的设计上会大有不同。


[click]

一般来说，我们按机器学习的任务，可以把模型划分为两类，回归任务和分类任务。如果模型要预测的是房价或者股价，我们判断预测是否准确的方式，就是计算预测值与真实值之间的距离。这个距离可以是绝对值，可以是百分比绝对值，也可以是方差，或者平方误差。

如果我们要判断预测一张图片是一只猫还是一只狗呢？假设图片给出了一个预测，我们如何判断这个预测是否准确呢？这里的评估函数肯定不能像回归任务那样了。对分类任务，我们要通过计算预测值和真实值之间的信息熵来进行判断和优化。

好，到此为止，我们就基本上对机器学习的所有类型有了一个最简单的理解。

那么，我们应该如何进行模型的选择呢？
-->
