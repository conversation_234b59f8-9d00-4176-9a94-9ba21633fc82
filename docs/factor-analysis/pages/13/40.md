---
clicks: 3
---

<Profile class='abs w-200px h-200px'
         :enter='{opacity: 0,x:400, y:200}'
         :click-0-1='{opacity: 1}'
         img=''
         color="#f9c74f"
         row1='监督学习'
         row2=''
         row3=''/>
<Profile class='abs w-200px h-200px'
         :enter='{opacity: 0,x:400, y:200}'
         :click-1='{opacity: 1}'
         :click-2='{x:100}'
         img=''
         color="#f9c74f"
         row1='监督学习'
         row2='训练集有标签'
         row3=''/>

<Profile class='abs w-200px h-200px'
        :enter='{opacity: 0,x:600,y: 200}'
        :click-2='{opacity: 1}'
        :click-3='{x:400}'
        img=''
        color="#43aa8b"
        row1='无监督学习'
        row2='训练集没有标签'
        row3=''/>

<Profile class='abs w-200px h-200px'
         :enter='{opacity: 0,x:700,y:200}'
         :click-3='{opacity: 1}'
         img=''
         color="#003f88"
         row1='强化学习'
         row2='无标签，但有奖励'
         row3=''/>


<!--

按照学习的方式，我们可以把模型分为监督学习、无监督学习、强化学习。


[click]

监督学习：训练集有标签，通过标签来训练模型。所谓有标签，就是指给定的特征数据，我们知道它的预测结果。比如，在前面的决策树例子中，如果是晴天、气温为凉爽，那么结果应该是可以出门。这个结果就是一种标签。标签可以是离散值，比如分类的情况，也可以是连续值，比如回归的情况。


[click]

有时候，即使数据还没有标签，我们也希望能对其进行探索，希望得出有用的结论。这种情况下，机器学习也能派上用场。此时的学习就是无监督学习。


[click]

强化学习没有标签，但它有奖励，会激励模型往奖励最大的方向去优化。


在量化交易中，初学者可能常常会认为很容易给数据打标签。我当初从人工智能转到量化，就是误以为量化领域，数据标签是天然的，其实不是这样。

所谓标签，并不仅仅是指每一行特征数据，都有一个关联的y值。关键是，你在这个阶段进行的这种关联，它有没有意义，特征与标签之间是否存在比较强的关联。这是量化交易中使用人工智能的困难所在。
-->
