---
clicks: 6
---

<div class='abs mt-15 ml-5% w-36%' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/11/nvidia-gpu.jpg?1)
</div>

<div class='abs mt-15 w-35% ml-58%' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/11/imagenet-dataset.jpeg)
</div>

<FlashText v-click="[0,1]"
           class='abs mt-1/5 text-center w-full text-3xl'>
机器学习
</FlashText>

<div class='abs h-80% mt-15 ml-25' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/11/decision-on-outing.jpg)
</div>

<div class='abs w-full mt-20' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1, width: "100%"}'
     :click-4='{opacity:1, x: 0, width: "50%"}'>

```python
def decision_on_outing(weather, temperature):
    if temperature == '高温':
        return "诸事不宜"
    elif weather == '雨':
        return '诸事不宜'
    else:
        return "宜出行"

decision_on_outing("晴", "舒适")
```
</div>

<div class='abs mt-10 w-full' v-motion
          :enter='{ scale: 0, width:"100%"}'
          :click-3='{ scale: 1}'
          :click-4='{ scale: 0}'
          :click-5='{ scale:1, width:"50%"}'>

```python{all|7-13|15-20}{maxHeight:'450px',at:5}
import numpy as np
import pandas as pd
from sklearn.tree import DecisionTreeClassifier, plot_tree
import matplotlib.pyplot as plt

# 创建示例数据
data = {
    '天气': ['晴', '晴', '晴', '晴', '阴', '阴', '雨', '雨'],
    '气温': ['高温', '高温', '舒适', '凉爽', '凉爽', '凉爽', '凉爽', '凉爽'],
    '宜出行': [0, 0, 1, 1, 1, 1, 0, 0]
}

df = pd.DataFrame(data)

# 将分类变量转换为数值
df['天气'] = df['天气'].map({'晴': 0, '阴': 1, '雨': 2})
df['气温'] = df['气温'].map({'高温': 0, '舒适': 1, '凉爽': 2})

X = df[['天气', '气温']]
y = df['宜出行']

# 创建并训练决策树模型
clf = DecisionTreeClassifier()
clf.fit(X, y)

# 可视化决策树
plt.figure(figsize=(8,12))
plot_tree(clf, filled=True, feature_names=['天气', '气温'], class_names=['诸事不宜', '宜出行'])
plt.title('今日宜出行')
plt.show()

# 预测是否出门
weather = "晴"
temp = "高温"

sample = pd.DataFrame([(weather, temp)], columns=["天气", "气温"])
sample['天气'] = sample['天气'].map({'晴': 0, '阴': 1, '雨': 2})
sample['气温'] = sample['气温'].map({'高温': 0, '舒适': 1, '凉爽': 2})

prediction = clf.predict(sample)
if prediction[0] == 1:
    print(weather, temp, "宜出行")
else:
    print(weather, temp, "诸事不宜")
```
</div>

<div class='abs w-40% ml-55% mt-15' v-motion
     :enter='{opacity: 0}'
     :click-4='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/11/decision-tree-learned.jpg?1)
</div>


<!--
我们花了比较多的时间来回顾人工智能的发展。为什么要这么安排呢？因为人类探索人工智能的过程，就是逐步认识智能的本质的过程。

现在，我们已经基本清楚，人工智能的实现，需要依靠极高的算力和海量的数据，然后通过机器学习来训练模型。



现在，我们再深入一点，看看这个学习过程倒底是如何完成的。

关于这一部分，大家也可以参考吴恩达的机器学习课程。在他的课程中，讲解了一个机器学习实现直线拟合过程的例子。

[click]

在这里，我们想介绍一个更容易理解、也有一定实际意义的例子。这就是机器学习中的决策树。

假设我们要完成一个非常简单的应用，就是要根据第二天的天气情况来决定是否适合出门，这个图给出了决策的依据，输入参数会有天气和温度，其中天气就分了晴天,阴天和雨天三种数据输入;温度就有高温和正常两种输入 然后如果我们在已知第二天是什么样的天气，什么样温度的情况下，他就可以帮我们做出决策。

这个功能很简单，所以，代码也不复杂

[click]

就是一些简单的if else语句。但是，随着条件的增加，它的复杂度会迅速增加，从而导致编程变得困难，甚至是不可能。


[click]

现在，我们来看看使用机器学习会是什么情况。这是机器学习的代码，我们先看看它的运行结果，一会儿再回头来分析代码


[click]

这是通过刚刚的代码训练来出的决策树的可视化模型。我们看到，它首先根据温度来进行判断。在这一步，它看到了8个样本，根据气温是否<=0.5，可以分为两种情况。

第一种情况，它看到了2个样本，结果是诸事不宜。

第二种情况，它看到了6个样本，但gini系数大于零，所以还需要进一步细分

接下来的判断条件是天气。根据天气的取值是否<=1.5，又分成了两种情况，左边的样本有4个，右边的样本有2个。

最下面的结点gini值为零，划分完比。

这棵树的结构，与我们左边代码的结构完全一致，不同的是，判断条件不一样。左边的代码是通过相等来进行比较，而右边则是把气温、天气这样的变量进行了数值化，然后通过临界值进行比较。


[click]

我们再回到决策树的代码，看看它具体是如何实现的。

首先，我们搜集所有的训练数据，用dataframe表示。


[click]

最重要的一步，是将数据预处理成为模型能可以处理的格式。在这里，是把文本数据数值化。我们前面讲过，当我们手工编写规则时，条件判断可以通过符号相等来判断，但在决策树中，它是根据数值的分布来进行学习、最终生成规则的。

在这一步之后，我们看到，高温的取值为0 ，其它的取值在1之上。所以，在第一次分裂时，按所温等0.5来划分，高温天气就进入了左边的路径，而其它天气就进入了右边的路径。

0.5是怎么来的？它是分裂点的中值。这样，也为模型将来识别未曾看到的数据留下了空间。

在第二步，是要按天气进行划分。天气变量共有三个数值，0，1，2。模型看到，如果把0和1归为一类，2归为另一类，则gini值最小。最后，它取两类的边界值的中点，得到分裂条件为1.5。

这次划分之后，所有的子数据集的不纯度(gini)都变成为0，划分结束。

这样，决策树模型就学习到了全部的分割条件。

当然，这是一个最粗略的梗概，还有很多细节没有讲。比如，如何计算gini系数？这些相关的概念，我们会在第14课来讲解。

-->
