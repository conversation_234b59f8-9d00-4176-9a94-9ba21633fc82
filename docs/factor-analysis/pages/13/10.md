---
clicks: 11
---


<div class='abs mt-30' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/dartmouth-ai-summer-research.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>达特茅斯AI研讨会</span>
</div>
</div>

<div class='abs flex flex-col items-center justify-center w-full h-full z-100' v-motion
    :enter='{opacity: 0}'
    :click-1='{ opacity: 1}'
    :click-2='{ opacity: 0}'>
<div>
<mdi-location class="text-blue-400 animate-bounce mr-2" />
</div>
<div class="text-white">达特茅斯学院<br>新罕布什尔州</div>
</div>

<div class='abs mt--10' v-motion
     :enter='{opacity: 0}'
     :click-1='{ opacity: 1}'
     :click-2='{ opacity: 0.2}'
     :click-3='{ opacity: 0}'>

![](https://images.jieyu.ai/images/university/Dartmouth_campus_2007.jpg?width=75%%&align=center)

</div>

<div class='abs w-full' v-motion
     :enter='{opacity: 0}'
     :click-2='{ opacity: 1}'
     :click-3='{ opacity: 0}'>

<div class='abs w-50% left-0'>

<div style=';text-align:center;margin: 0 auto 1rem'>
<img src='https://upload.wikimedia.org/wikipedia/commons/thumb/4/49/John_McCarthy_Stanford.jpg/1280px-John_McCarthy_Stanford.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>


<div class="abs w-50% mt-30 left-55%" v-motion
    :enter='{opacity: 0}'
    :click-2='{ opacity: 1}'
    :click-3='{ opacity: 0}'>

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />John McCarthy

<br>
<br>

* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> 人工智能之父、AI术语提出者
* <mdi-computer class="text-red-400 animate-bounce mr-2"/>LISP语言发明者
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>图灵奖
</div>

</div>


<div class='abs mt-40 w-full' v-motion
     :enter='{opacity: 0,x:0, scale:1}'
     :click-3='{ opacity: 1}'
     :click-6='{x: -300}'
     :click-7='{x: -600}'
     :click-8='{x: -1200}'
     :click-9='{x: -1500}'
     :click-10='{x: -1800}'
     :click-11='{scale: 0}'>


<Profile class="abs w-200px h-200px"
    :enter="{opacity: 0,x:400}"
    :click-3="{opacity: 1}"
    :click-4="{x: 100}"
    img="https://images.jieyu.ai/images/2024/11/高尔基染色法和海马体.jpg"
    row1="神经元"
    row2="1890~1943"
    row3="圣地亚哥·卡哈尔提出神经元说。<br>Camillo Golgi发明高尔基染色法。<br>麦卡洛克和皮茨将神经元电化学过程简化为信号交换"/>

<Profile class="abs w-200px h-200px"
    :enter="{opacity: 0,x:400}"
    :click-4="{opacity: 1}"
    :click-5="{x: 400}"
    img="https://images.jieyu.ai/images/2024/11/perceptron.png"
    row1="感知机"
    row2="1958年"
    row3="弗兰克·罗森布拉特提出感知机<br>今天我们仍然可以在sklearn中找到它的身影"/>


<Profile class="abs w-200px h-200px"
    :enter="{opacity: 0,x:700}"
    :click-5="{opacity: 1}"
    :click-6="{x: 700}"
    img="https://images.jieyu.ai/images/2024/11/lisp-machine.jpg"
    row1="符号主义"
    row2="1970~"
    row3="专家系统<br>语义网<br>XCON"/>

<Profile class="abs w-200px h-200px"
    :enter="{opacity: 0,x:1000}"
    :click-6="{opacity: 1}"
    :click-7="{x: 1000}"
    img="https://images.jieyu.ai/images/2024/11/geoffrey-hinton.png"
    row1="反向传播"
    row2="1986"
    row3="杰弗瑞.辛顿<br>反向传播为多层神经网络奠基<br>辛顿获得2024年诺贝尔物理奖"/>

<Profile class="abs w-200px h-200px"
    :enter="{opacity: 0,x:1300}"
    :click-6="{opacity: 1}"
    :click-7="{x: 1300}"
    img="https://images.jieyu.ai/images/2024/11/yann-lecun.jpg"
    row1="LeNet"
    row2="1998"
    row3="杨立昆提出LeNet网络<br>成功识别了支票签名"/>

<Profile class="abs w-200px h-200px"
    :enter="{opacity: 0,x:1600}"
    :click-6="{opacity: 1}"
    :click-7="{x: 1600}"
    img="https://images.jieyu.ai/images/2024/11/jensen_huang.jpg"
    row1="CUDA"
    row2="2006"
    row3="黄仁勋<br>英伟达发布支持CUDA"/>

<Profile class="abs w-200px h-200px"
    :enter="{opacity: 0,x:1900}"
    :click-6="{opacity: 1}"
    :click-7="{x: 1900}"
    img="https://images.jieyu.ai/images/2024/11/lifeifei.jpg"
    row1="ImageNet"
    row2="2009"
    row3="李飞飞<br>ImageNet初版上线"/>

<Profile class="abs w-200px h-200px"
    :enter="{opacity: 0,x:2200}"
    :click-6="{opacity: 1}"
    :click-7="{x: 2200}"
    img="https://images.jieyu.ai/images/2024/11/alex-krizhevsky.jpg"
    row1="AlexNet"
    row2="2012"
    row3="Alex Krizhevsky<br>在2012年的ImageNet竞赛中，AlexNet获得85%的识别准确率"/>

<Profile class="abs w-200px h-200px"
    :enter="{opacity: 0,x:2500}"
    :click-6="{opacity: 1}"
    :click-7="{x: 2500}"
    img="https://images.jieyu.ai/images/2024/11/heminkai.jpg"
    row1="ResNet"
    row2="2016"
    row3="何明恺<br>在2012年的ImageNet竞赛中，AlexNet获得85%的识别准确率"/>

</div>

<div class='abs w-full h-60% left-35% z-100' v-motion
     :enter='{opacity: 0}'
     :click-11='{ opacity: 1}'>

<video src="https://images.jieyu.ai/images/2024/11/Tesla-Optimus.mp4" controls/>
</div>

<!--
人工智能一词起源于1956年，在达特茅斯学院（Dartmouth）的AI研讨会上，由John McCarthy。


[click]

达特茅斯学院位于美国东北部新罕布什尔州，是一所顶尖的私立研究型大学，是8所常春藤高校之一。学校比较小，但却也很有特色。它有教会背景，校训翻译成为中文是空谷绝音，就来自圣经中的某一段。

达特茅斯引用它作为校训，旨在强调其教育理念和使命，即在知识的荒漠中发出理性的声音，引导学生追求真理和智慧。


[click]

这位就是达特茅斯会议的发起者，John McCarthy博士。他有人工智能之父的称号，也是LISP语言发明者。人工智能这一术语，也就是 artificial intelligence,就是他最早提出来的。

最初他认为，只要集中全美最好的几位科学家，大概只要8周就能攻克人工智能问题。谁能想到，从1956年发起的宏伟梦想，经过近70年的艰难探索，中间经历过几次潮起潮落，到今天我们还仍然在路上。

[click]

从今天来看，人工智能起源于卡哈尔提出神经元说，这是1900世纪末的事。后来高尔基发明了高尔基染色法，证实了神经元的存在。1943年，科学家将神经元电化学过程简化为信号交换，这为后来人工神经网络的发展打下基础。直到今天，人工智能仍然在从神经科学中寻找答案。

[click]

1956年的达特茅斯会议掀起了第一次人工智能浪潮。两年之后，弗兰克·罗森布拉特提出感知机，这是最早的、也是最简单的人工神经网络。人工智能从一开始就存在两大技术流派，一方是所谓的连接主义，即通过连接神经元来构建网络，通过从数据中学习这种连接。感知机属于连接主义。


[click]

很快，人们发现感知机非常局限。于是人工智能中的另一派，符号主义开始占据上风。在这一时期，出现了语义网、专家系统。语义网后来发展为知识图谱，直到2018年google的bert模型发布之前，一直都很有影响力。

这一时期，还存在一个名为xcon的专家系统，它由卡耐基.梅隆大学研发，用来帮助用户采购电脑硬件时，回答一些配置问题。它问世之后，几年处理了超过80万笔订单，准确度超过95%，是比较成功的一个系统。这个图中的计算机，正是运行XCON的一台机器。

但是，人们也慢慢发现，通过规则来构建专家系统，实施的代价太大，要为整个世界建模，几乎不可能。


[click]

在1980年代，人工智能迎来第二次浪潮。其中辛顿与霍普菲尔德几乎同时发现了具有学习能力的深层神经网络，辛顿还发现了反向传播算法。他在2018年获得图灵奖，在2024年，与霍普菲尔德一起，获得了诺贝尔物理学奖。

不过，第二次人工智能浪潮也很快退潮。人工智能研究再次进入寒冬。


[click]

1998年，杨立昆提出LeNet网络，成功识别了支票签名。这是最早的多层神经网络，也是人类历史上第一个有实际用途的神经网络。Yann LeCun的成功再次掀起了人工智能浪潮，同时也把机器学习路线重新带回到人们的视野中来。

不过，跟之前一样，杨立昆的成功，仍然未能取得决定性的胜利。人工智能距离真正的成功，还差两个决定性的东西。


[click]

这就是硬件和数据。2006年，英伟达推出第一代支持CUDA的GPU。在游戏时代，人们发现，生成计算机图像时，各个像素的渲染是可以并行处理的，所以一种比CPU简单、但支持并行计算的处理器-GPU就诞生了。最早的人工智能是从计算机视觉上突破的，原因之一就是对图像的很多处理都可以并行化，这正好用上了GPU。

2009年，李飞飞发布ImageNet，ImageNet是一个用于计算机视觉的公开数据集。现代深度学习的两支翅膀，数据和算法在这一刻齐备了，命运的齿轮开始转动。


[click]

2012年，人工智能迎来了一个里程碑。辛顿和他的学生Alex， Ilya等人发布了AlexNet，在ImageNet大赛上获得冠军。最重要的是，它使用了深层神经网络，取得了85%的识别准确率，比上一年的冠军提升了10%。

与LeNet相比，AlexNet是在超过1000个分类图片上取得的成绩，因此具有突破意义。AlexNet也让连接主义，或者说基于数据驱动的机器学习路线加冕成为王者。从此，以深度学习为代表的人工智能势如破竹，人工智能的迎来了春天。


[click]

2016年，这是人工智能的又一个里程碑。华人科学家何明恺在ImageNet竞赛中，以超越人类的识别准确率夺得冠军。同时，他构建的resnet--深度残差网络也发展到了惊人的152层。在人工智能研究上，科学家们取得了压倒性的胜利，从此，人工智能进入高速发展阶段。

再往后面的事可能大家都知道了。最重要的标志是2023年发布的chatgpt,标志机器在语言理解方面取得了重大的突破。


[click]


这是人工智能的最新进展，来自特斯拉的机器人--擎天柱。在演示中，它精准地接住了人类抛过来的网球。物体识别、轨迹跟踪和手部动作都配合得非常好。


-->
