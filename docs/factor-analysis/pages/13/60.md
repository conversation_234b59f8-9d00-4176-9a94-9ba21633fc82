---
clicks: 5
---

<div class='abs mt-15' v-motion
     :enter='{opacity: 1}'
     :click-2='{ opacity: 0}'>

## 1. 定义问题 选择模型
</div>

<div class='abs w-50% ml-45% mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/11/how-to-choose-estimator.jpg)
</div>

<FlashText v-click="[0,1]"
           class='abs mt-2/5 text-center w-full text-3xl'>

https://scikit-learn.org/stable/machine_learning_map.html
</FlashText>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-2='{ opacity: 1}'>

## 1. 定义问题 选择模型
## 2. 准备数据
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-3='{ opacity: 1}'>

## 1. 定义问题 选择模型
## 2. 准备数据
## 3. 训练模型
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-4='{ opacity: 1}'>

## 1. 定义问题 选择模型
## 2. 准备数据
## 3. 训练模型
## 4. 模型评估
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-4='{ opacity: 1}'>

## 1. 定义问题 选择模型
## 2. 准备数据
## 3. 训练模型
## 4. 模型评估
## 5. 应用模型
</div>

<div class='abs w-60% ml-40%' v-motion
     :enter='{opacity: 0}'
     :click-1='{ opacity: 1}'>

```python{all|17|1-14|18|20-24|26-38}{maxHeight:'450px', at:1}
data = {
    '天气': ['晴', '晴', '晴', '晴', '阴', '阴', '雨', '雨'],
    '气温': ['高温', '高温', '舒适', '凉爽', '凉爽', '凉爽', '凉爽', '凉爽'],
    '宜出行': [0, 0, 1, 1, 1, 1, 0, 0]
}

df = pd.DataFrame(data)

# 将分类变量转换为数值
df['天气'] = df['天气'].map({'晴': 0, '阴': 1, '雨': 2})
df['气温'] = df['气温'].map({'高温': 0, '舒适': 1, '凉爽': 2})

X = df[['天气', '气温']]
y = df['宜出行']

# 创建并训练决策树模型
clf = DecisionTreeClassifier()
clf.fit(X, y)

# 可视化决策树
plt.figure(figsize=(8,12))
plot_tree(clf, filled=True, feature_names=['天气', '气温'], class_names=['诸事不宜', '宜出行'])
plt.title('今日宜出行')
plt.show()

# 预测是否出门
weather = "晴"
temp = "高温"

sample = pd.DataFrame([(weather, temp)], columns=["天气", "气温"])
sample['天气'] = sample['天气'].map({'晴': 0, '阴': 1, '雨': 2})
sample['气温'] = sample['气温'].map({'高温': 0, '舒适': 1, '凉爽': 2})

prediction = clf.predict(sample)
if prediction[0] == 1:
    print(weather, temp, "宜出行")
else:
    print(weather, temp, "诸事不宜")
```
</div>

<!--
第一步，就是要定义我们要解决的问题，根据我们要解决的问题和拥有的数据规模、算力，来选择合适的模型。

如果数据量比较大，算力也够，那么就可以选择深度学习；如果数据量比较小，那么就应该选择机器学习。

如果数据有标签，那么就选择监督学习，如果没有标签，但能够定义出来奖励函数，也可以考虑强化学习。

最后，再看任务是什么。如果是回归任务，就选择回归模型；如果是分类任务，就选择分类模型。

这个图来自sklearn的官网，大家可以抽空看看，帮助我们选择模型。

[click]

这段代码选择了决策树模型

[click]

第2步就是准备训练数据。我们在决策树的例子中已经看到，这将包括数据的搜集、转换、标注。此外，我们常常还需要根据选择的模型，进行各种预处理。在决策树模型中，它允许不同的物理量使用不同的量纲，但多数模型需要我们进行数据标准化或者归一化。


[click]

接下来当然是训练模型。大家可以记一下这个API。sklearn里所有的模型，训练都是调用这个API


[click]

然后是模型评估。在这一步，我们往往会借助各种可视化方案来探测模型，以了解训练过程。

一般来说，我们会绘制学习曲线、观察损失函数的变化，决定何时停止训练，以及使用多组参数进行搜索。

经过能数搜索、评估之后，我们就会选出性能最好的一个实例保存下来，其它的就可以丢掉了。现在很多人即使不是做人工智能的，也可能在本地部署过开源的大模型，我们常常会看到checkpoint文件，这就是训练好的模型实例。

[click]

最后，就是部署和应用模型，帮助我们解决实际问题。
-->
