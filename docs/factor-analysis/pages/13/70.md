

<div class='abs ml-35% mt-10' v-motion
     :enter='{opacity: 1}'
     :click-0='{ opacity: 1}'>

<video src="https://images.jieyu.ai/images/2024/11/Tesla-Optimus.mp4" controls autoplay muted loop/>
</div>
<div class='abs ml-0% mt-10' v-motion
     :enter='{opacity: 1}'
     :click-0='{ opacity: 1}'>

<video src="https://images.jieyu.ai/images/2024/11/Tesla-Optimus.mp4" controls autoplay muted loop/>
</div>

<div class='abs ml-70% mt-10' v-motion
     :enter='{opacity: 1}'
     :click-0='{ opacity: 1}'>

<video src="https://images.jieyu.ai/images/2024/11/Tesla-Optimus.mp4" controls autoplay muted loop/>
</div>

<!--
关于AI的应用场景，我们不详细介绍了。一句话，过去我们有互联网+，未来则是AI+。

在生物制药领域，alphafold能更快地预测蛋白质的空间结构，一个alphafold，相当于成百倍、成千倍地培养了顶级生物科学的博士生。

生成式AI是现在所有博主都在使用的工具了。

那么在量化交易领域又会如何呢？我们今天学量化，会不会被某个大模型所代替？这里跟大家分享一点心得，量化交易可能是智能时代，高科技人士最后的避难所。

我的判断主要有两点依据，一是机器没有权力成为财富的所有人。这可能不仅仅是一种伦理和法律要求，也是宇宙的法则。所以，在财富领域，不可能存在机器赶人的情况。

有没有可能人利用机器赶人呢？

也不大可能，主要原因是证券交易中也存在着量子效应。如果你有足够多的资本投入来研发交易模型，那么，这个模型一旦成功，你也肯定（也必须）有足够多的资金投入交易。而过多的资金影响价格，从而会破坏你的进场条件和退场条件。这就像量子效应一样，当你观察一个光子时，你就会改变光子的状态。因为观察这件事，是通过另一个光子与被观察的光子发生碰撞来实现的。交易也是这样。如果所有人在市场上不出手，就不会产生价格；但一旦大的资金出手，就会改变上一次定下来的价格。

所以，过大的资金其实是无法利用量化交易的。
-->
 