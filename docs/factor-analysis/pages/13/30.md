---
clicks: 1
---


<Profile class='abs w-200px h-200px'
         :enter='{opacity: 1,x:400, y:200}'
         :click-1='{opacity: 1, y:50}'
         :click-2='{opacity: 0}'
         img=''
         color='#fdc500'
         row1='Machine Learning'
         row2=''
         row3=''/>
<div class='abs mt-70' v-motion
     :enter='{opacity: 0}'
     :click-1='{ opacity: 1}'
     :click-2='{opacity: 0}'>

<Profile class='abs w-200px h-200px'
         :enter='{opacity: 1,x:400}'
         img=''
         color="#fdc500"
         row1='Machine Learning'
         row2='机器学习'
         row3=''/>

<Profile class='abs w-200px h-200px'
         :enter='{opacity: 1,x:100}'
         img=''
         color="#003f88"
         row1='Deep Learning'
         row2='深度学习'
         row3=''/>

<Profile class='abs w-200px h-200px'
         :enter='{opacity: 1,x:700}'
         img=''
         color="#43aa8b"
         row1='Reinforce Learning'
         row2='强化学习'
         row3=''/>
</div>

<!--
Machine Learning是人工智能的一个子概念，尽管在未来，它可能会成为人工智能的惟一实现方式，但是，通过回顾人工智能的历史我们可以看到，人工智能曾经是有多种实现方式的。


[click]

这是我们常常会遇到的三个概念。从广义上讲，机器学习是不进行明确的编程，就能从数据中学习规则的方法的总称。从狭义上讲，我们也把这类方法中，不使用深层神经网络的算法和模型都称为机器学习。

深度学习主要通过多层神经网络来学习数据的复杂表示，需要大量的数据和更长时间的训练。

强化学习是一种通过与环境交互来学习最优行为策略的方法。它通过试错的方式，使代理（agent）在环境中采取行动以最大化累积奖励。

在我们要用人工智能解决某个问题时，首先要决定的是，要使用机器学习，还是深度学习或者强化学习。

如果问题的规模较小，也难以获得大量的数据，那么我们会选择机器学习。如果问题的规模比较复杂，又能获得大量的训练数据，我们就会根据数据是预先收集好的，还是动态生成的来选择深度学习还是强化学习。

在本课程中，我们选择讲授机器学习模型，原因就是，属于金融领域的imagenet，我不知道有没有，至少开源的没有。


[click]


-->
