<div class="mt-10 ml-5">

## 交叉验证
### k-fold
### stratified k-fold
### leave-one-out
### 时间序列交叉验证
### 滚动预测
## 参数优化
### 网格搜索
### 随机搜索
### 贝叶斯优化
</div>

<!--
今天的主要课程内容就到这里。现在， 请大家提出问题。在大家准备问题的同时，我为大家总结今天的课程。

今天的内容是关于机器学习模型优化的。我们主要介绍了训练算法的优化方法、交叉验证和超参数优化。重点是后面两部分。对训练算法的优化，我们做到见名知义，知道如何搭配其它参数即可。

交叉验证是在数据集不大的情况下，增加我们对超参数调优信心的一种方法。我们从最基础的k-fold开始，抛出问题，解决问题，然后再指出方法不足和改进点，从而引入新的方法。

要注意的是，金融数据分析中，我们更倾向于使用滚动预测方法。

每个训练模型都有自己的超参数，它们对模型的性能有重大影响。为了找出最佳超参数，我们有网格搜索、随机搜索和贝叶斯优化等方法。网格搜索最直观，效果最好，但耗时最多。贝叶斯优化是一个不错的折衷。

好，今天的课程就到这里，我们下次课程再见。
-->
