---
clicks: 7
---

<NoteCell 
          class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, accuracy_score

iris = load_iris()
X, y = iris.data, iris.target

(X_train, 
 X_test, 
 y_train, 
 y_test) = train_test_split(X, 
                            y, 
                            test_size=0.2,
                            random_state=42)

model = LogisticRegression(max_iter=5)

model.fit(X_train, y_train)
y_pred = model.predict(X_test)

print("Accuracy with default hyper parameters:\n", 
      accuracy_score(y_pred, y_test))
```
</NoteCell>

<NoteCell layout="horizontal"
          outputMt="1rem"
          outputWidth="50%"
          class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1-2 ='{ scale: 1}'>

```python
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, accuracy_score

iris = load_iris()
X, y = iris.data, iris.target

(X_train, 
 X_test, 
 y_train, 
 y_test) = train_test_split(X, 
                            y, 
                            test_size=0.2,
                            random_state=42)

model = LogisticRegression(max_iter=5)

model.fit(X_train, y_train)
y_pred = model.predict(X_test)

print("Accuracy with default hyper parameters:\n", 
      accuracy_score(y_pred, y_test))
```
</NoteCell>

<NoteCell class='abs mt-20 w-full'
          :enter='{ scale: 0}'
          :click-2-3='{ scale: 1}'>

```python
# 定义参数网格
param_grid = {
    'C': [0.1, 1, 10, 100],
    'solver': ['liblinear', 'lbfgs', 'newton-cg', 'sag', 'saga'],
    'penalty': ['l1', 'l2', 'elasticnet', 'none'],
    "max_iter": np.linspace(5, 50, 5).astype(int),
}
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-3-4='{ scale: 1}'>

```python
model = LogisticRegression()
grid_search = GridSearchCV(
    estimator=model,
    param_grid=param_grid,
    scoring='accuracy',
    cv=5,
    n_jobs=-1,
    verbose=2
)

grid_search.fit(X_train, y_train)
clear_output()
```
</NoteCell>

<NoteCell class='abs mt-20 w-full'
          :enter='{ scale: 0}'
          :click-4-5='{ scale: 1}'>

```python
print("Best Cross-validation Score:", grid_search.best_score_)

# 输出最佳参数组合
print("Best Parameters:", grid_search.best_params_)
```
</NoteCell>

<NoteCell outputMt="2rem"
          class='abs mt-20 w-full'
          :enter='{ scale: 0}'
          :click-5-6='{ scale: 1}'>

```python
best_model = grid_search.best_estimator_
y_pred = best_model.predict(X_test)

# 打印分类报告
print("Classification Report:")
print(classification_report(y_test, y_pred))

# 打印测试集上的准确率
print("Test Accuracy:", accuracy_score(y_test, y_pred))
```
</NoteCell>

<div class='abs mt-20 w-full' v-motion
     :enter='{opacity: 0, scale: 0}'
     :click-6-7='{opacity: 1, scale: 1}'>

```python
param_grid = {
    'C': [0.1, 1, 10, 100],
    'solver': ['liblinear', 'lbfgs', 'newton-cg', 'sag', 'saga'],
    'penalty': ['l1', 'l2', 'elasticnet', 'none'],
    "max_iter": np.linspace(5, 50, 5).astype(int),
}
```
</div>

<div
    class='abs mt-10' v-motion
    :enter='{opacity: 0, scale:0}'
    :click-6-7='{opacity: 1, scale: 1}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/hot/qa.png'>
</div>
</div>

<NoteCell class='abs mt-30 w-full'
          :enter='{ scale: 0}'
          :click-7-8='{ scale: 1}'>

```python
model = LogisticRegression()

model.get_params()
```
</NoteCell>

<!--
在参数搜索的各种方案中，网格搜索是最直观、实现最简单的一种方法。它是通过穷举法（即网格搜索）遍历所有可能的参数组合，从而找到最优的超参数设置。

具体怎么实现呢？在sklearn中，它为我们提供了名为GridSearchCV的方法。它不仅实现了网格搜索，还实现了交叉验证，这就是为什么它的名字既包括了GridSearch,又包含了CV的原因。

[click]

现在，我们以鸢尾花数据集为例，来看如何使用GridSearchCV进行超参数优化。

<run></run>

我们先生成基准测试。当max_iter为5的时候，准确率是0.77


[click]

<run></run>

现在，我们就来搜索最佳参数了。除了max_iter之外，LogisticRegression还有其他超参数，我们也选其中一些进行尝试。

[click]

接下来我们就定义模型

这里最关键的是要定义GridSearchCV对象，它是参数搜索任务的执行者。

在这里我们要传入机器学习模型，通过estimator参数。我们还要传入参数搜索空间，通过param_grid参数,传入我们刚刚定义的对象。我们还要指定评估函数，交叉验证的折数等。

最后，我们调用fit方法，传入训练数据，开始搜索。


[click]

最后，最佳参数保存在best_params_属性中。最佳模型保存在best_estimator_属性中。


[click]

<run></run>

现在， 我们就来验证下这个最佳模型成色如何。看起来我们确实找到了最佳参数，因为所有指标都达到了理论上的最佳状态。


[click]

现在我们再回到当初定义param_grid的地方。我们要如何知道一个模型有哪些超参数可以调优呢？

不同的机器学习框架的实现是不一样的。但在sklearn中，它的实现相当一致，就是每个模型都要定义get_params方法。


[click]

<run></run>
然后我们调用这个方法，就能知道模型有哪些超参数 

-->
