---
clicks: 1
---

<div class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python{all|18}{maxHeight: '450px',at:1}
from scipy.stats import randint
from scipy.stats import reciprocal
from sklearn.model_selection import RandomizedSearchCV
from sklearn.linear_model import LogisticRegression

# 定义参数分布
param_dist = {
    'C': reciprocal.rvs(1e-2, 1e2,size=1000),# 对数均匀分布
    'solver': ['liblinear', 'lbfgs', 'newton-cg', 'sag', 'saga'],
    'penalty': ['l1', 'l2', 'elasticnet', 'none'],
    "max_iter": randint(5, 50),
}

model = LogisticRegression()
random_search = RandomizedSearchCV(
    estimator=model,
    param_distributions=param_dist,
    n_iter=20,  # 尝试的参数组合数量
    scoring='accuracy',
    cv=5,
    n_jobs=-1,
    verbose=2,
    random_state=42
)

random_search.fit(X_train, y_train)

# random_search 有大量的警告
clear_output()

# 输出最佳交叉验证得分
print("Best Cross-validation Score:", random_search.best_score_)

# 输出最佳参数组合
print("Best Parameters:", random_search.best_params_)
```
</div>

<!--

网络搜索是一种穷举法。很好用，但是参数组合数太大时，优化就非常耗时。这时，就出现了随机搜索。

它的用法跟GridSearchCV几乎完全一样，但有一个不同


[click]

我们需要指定随机尝试的次数。当我们指定n_iter为20次，cv为5时，就意味着要对模型训练100次。与网格相比，训练时长大大降低了。

但是，如果你运行一下，就会发现，每次运行得到的最佳参数组合是不一样的。这说明，随机搜索并不一定能找到全局最优解。

于是，人们又发明了贝叶斯优化。

-->
