---
clicks: 11
---


<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'>

```python
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score

(X_train, 
 X_test, 
 y_train, 
 y_test) = train_test_split(X, y, random_state=78, test_size=0.2)
```
</NoteCell>

<div class='abs mt-25' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/optuna.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>Credit: Optuna</span>
</div>
</div>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'>

```python{all|4-24|26,27|29,30|34-38|40-50|4|5-9|10-18|20-24|all}{maxHeight: '450px',at:2}
import optuna
from sklearn.utils._param_validation import InvalidParameterError
from sklearn.linear_model import LogisticRegression

def objective(trial)->float:
    # 定义要优化的超参数
    C = trial.suggest_loguniform('C', 1e-2, 1e2)
    solver = trial.suggest_categorical('solver', ['liblinear', 'lbfgs', 'newton-cg', 'sag', 'saga'])
    penalty = trial.suggest_categorical('penalty', ['l1', 'l2', 'elasticnet', 'none'])
    max_iter = trial.suggest_int('max_iter', 5, 50)

    # 创建模型
    model = LogisticRegression(C=C, solver=solver, penalty=penalty, max_iter=max_iter)

    # 训练模型
    try:
        model.fit(X_train, y_train)
    except (InvalidParameterError, ValueError):
        return

    # 预测并计算准确率
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)

    return accuracy

# 创建 Optuna 学习器
study = optuna.create_study(direction='maximize')

# 开始优化
study.optimize(objective, n_trials=100)

clear_output()

# 输出最佳参数组合
print("Best Parameters:", study.best_params)

# 输出最佳准确率
print("Best Accuracy:", study.best_value)

# 使用最佳模型进行预测
best_model = LogisticRegression(**study.best_params).fit(X_train, y_train)

print("Best model accuracy on test set:", accuracy_score(y_test, best_model.predict(X_test)))

# 打印分类报告
print("Classification Report:")
print(classification_report(y_test, y_pred))

# 打印测试集上的准确率
print("Test Accuracy:", accuracy_score(y_test, y_pred))
```
</NoteCell>

<!--

贝叶斯优化是一种更复杂的优化方法，利用概率模型来估计参数组合的质量。它通过逐步缩小搜索空间，可以在较少的评估次数内找到较优的参数组合。

在slkearn中并没有贝叶斯优化的实现。但有一些第三方库可以帮我们完成优化任务。这里我介绍 optuna这个库。

它在github上有超过1万的star，并且已经支持了python 3.13，因此，它的社区是相当活跃的。


[click]

在使用上，比GridSearchCV略微复杂一点。


[click]

首先，我们要定义一个目标函数。每个机器学习模型都有自己的目标函数。为了优化机器学习模型的参数，optuna把模型的评分和它的参数联系起来，作为一个优化的目标函数。这一部分是我们自己要去定义的。

[click]

然后创建一个学习器，这一步跟网格搜索差不多。不同的是，我们前面讲过，目标函数的优化方向有两个，可以是最大化，也可以是最小化。所以，在这里，我们要指定优化方向。

我们前面在定义目标函数时，返回的是准确率。准确率当然是越大越好，所以，这里优化的方向就是最大化。


[click]

然后调用optimize方法进行优化。这里我们要指定迭代次数。optuna也许在此之前就结束搜索，但不会超过这个限制。


[click]

搜索完成，最佳参数和最佳评分分别保存在study.best_params和study.best_value中。与网格搜索不同，它不保存最佳模型。


[click]

所以，为了得到最佳模型，我们需要重新使用最佳参数来训练模型。


[click]

显然，在optuna中，优化的关键是我们如何自定义这个目标函数。现在，我们再回过头来，看看它的要求。

首先，这个函数接受一个名为trial的参数，并返回一个浮点数值。


[click]

trial参数最重要的方法就是以suggest开头的这些方法。我们通过它向optuna请求训练参数。最终，这些方法将返回一个参数值，这些值是optuna在内部根据过去训练的结果建议的。


[click]

然后我们用optuna返回的参数值创建模型，进行训练。


[click]

最后，我们用刚训练出来的模型进行预测，计算准确率，作为目标函数的返回值。在optuna内部，它会记录目标函数的返回值，以及参数组合，并执行内部算法，以便在下一次目标函数请求参数时，返回更好的参数组合。

[click]

Optuna实现的优化并不完全是Bayes优化，而是它的一个改进版本。它与网格搜索相比，可以大大缩小优化时长，但是，它没有实现交叉验证。这些可以在目标函数中，我们自己来实现


另外，大家在实现optuna优化时，要注意，在目标函数中，数据集是以全局变量的方式传入的。如果我们直接运用optuna，一般不会是问题。如果我们对优化过程进行了一些包装的话，还需要自己通过闭包的方式，把数据集变量局部化，这样更可靠一点。

-->
