---
clicks: 19
---

<div class='abs mt-20 w-full' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

<div style='width:40%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/small-datasets.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>credit: 豆包</span>
</div>
</div>

<div class='abs w-full mt-30' v-motion
     :enter='{opacity: 0,x:0, scale:1}'
     :click-1='{opacity: 1}'
     :click-2='{x: -250, scale:0.8}'
     :click-8='{scale: 0}'>
<div style='width:40%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/k-fold.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>credit: miro@medium</span>
</div>
</div>


<div class='abs mt-30 ml-120' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>

### 1. 将数据<span style="color:red"> 随机 </span>分成k个大小的子集
</div>

<div class='abs mt-30 ml-120' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>

### 1. 将数据<span style="color:red"> 随机 </span>分成k个大小的子集
### 2. 对每一个子集 <span style='color:red'>$i$</span>
</div>

<div class='abs mt-30 ml-120' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>

### 1. 将数据<span style="color:red"> 随机 </span>分成k个大小的子集
### 2. 对每一个子集 <span style='color:red'>$i$</span>
#### a. 使用子集 <span style='color:red'>$i$</span> 作为验证集
</div>

<div class='abs mt-30 ml-120' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{opacity: 1}'>

### 1. 将数据<span style="color:red"> 随机 </span>分成k个大小的子集
### 2. 对每一个子集 <span style='color:red'>$i$</span>
#### a. 使用子集 <span style='color:red'>$i$</span> 作为验证集
#### b. 使用剩余的 <span style='color:red'>$k-1$</span> 个子集作为训练集
</div>

<div class='abs mt-30 ml-120' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{opacity: 1}'>

### 1. 将数据<span style="color:red"> 随机 </span>分成k个大小的子集
### 2. 对每一个子集 <span style='color:red'>$i$</span>
#### a. 使用子集 <span style='color:red'>$i$</span> 作为验证集
#### b. 使用剩余的 <span style='color:red'>$k-1$</span> 个子集作为训练集
#### c. 训练并计算误差
</div>

<div class='abs mt-30 ml-120' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{opacity: 1}'>

### 1. 将数据<span style="color:red"> 随机 </span>分成 <span style='color:red'>$k$</span> 个大小的子集
### 2. 对每一个子集 <span style='color:red'>$i$</span>
#### a. 使用子集 <span style='color:red'>$i$</span> 作为验证集
#### b. 使用剩余的 <span style='color:red'>$k-1$</span> 个子集作为训练集
#### c. 训练并计算误差
### 对所有的 <span style='color:red'>$k$</span> 个误差，进行统计分析
</div>

<NoteCell layout='horizontal' outputMt='5rem' class='abs mt-20 w-full'
          :enter='{ scale: 0}'
          :click-8-9='{ scale: 1}'>

```python
import numpy as np
from sklearn.datasets import make_classification
from collections import Counter
import matplotlib.pyplot as plt

# 生成类别不平衡的三分类数据集
X, y = make_classification(
    n_samples=500,        # 样本总数
    n_features=10,         # 特征数量
    n_informative=5,      # 有用的特征数量
    n_redundant=5,         # 冗余特征数量
    n_classes=3,           # 类别数量
    weights=[0.05, 0.3, 0.65], # 类别不平衡比例
    flip_y=0.01,           # 标签噪声比例
    random_state=42        # 随机种子
)

# 检查类别分布
class_distribution = Counter(y)
print("Class distribution:", class_distribution)
```
</NoteCell>


<NoteCell layout='horizontal' outputMt="-1rem" class='abs mt-30 w-full'
          :enter='{ scale: 0}'
          :click-9-10='{ scale: 1}'>

```python
plt.figure(figsize=(8, 4))
plt.hist(y, 
         bins=3, 
         rwidth=0.8, 
         align='left', 
         edgecolor='black'
         )
plt.xticks([0, 1, 2])
plt.xlabel('Class')
plt.ylabel('Number of samples')
plt.title('Class Distribution')
plt.show()
```
</NoteCell>

<NoteCell layout="horizontal" 
          outputMt="1rem" 
          outputWidth="40%" 
          class='abs mt-30 w-full'
          color="red"
          :enter='{ scale: 0}'
          :click-10-12='{ scale: 1}'>

```python{all|10}{at:11}
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score

(X_train, 
 X_test, 
 y_train, 
 y_test) = train_test_split(X, y, random_state=78, test_size=0.2)

model = LogisticRegression(max_iter = 5)
model.fit(X_train, y_train)

y_pred = model.predict(X_test)
accuracy_score(y_test, y_pred)
```
</NoteCell>

<NoteCell layout="horizontal" 
          outputMt="1rem" 
          outputWidth="40%" 
          class='abs mt-30 w-full'
          color="red"
          :enter='{ scale: 0}'
          :click-12-13='{ scale: 1}'>

```python{all|10}{at:12}
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score

(X_train, 
 X_test, 
 y_train, 
 y_test) = train_test_split(X, y, random_state=78, test_size=0.2)

model = LogisticRegression(max_iter = 50)
model.fit(X_train, y_train)

y_pred = model.predict(X_test)
accuracy_score(y_test, y_pred)
```
</NoteCell>

<NoteCell layout='horizontal'
          class='abs mt-10 w-full'
          outputMt="1rem" 
          outputWidth="40%"
          :enter='{ scale: 0}'
          :click-13-19='{ scale: 1}'>

```python{all|1-4|5|8-18|20-22}{at:14}
from sklearn.model_selection import KFold

# 创建 KFold 对象，设置 K=5
kf = KFold(n_splits=5, shuffle=True, random_state=42)
accuracies = []

# 进行 K 折交叉验证
for train_index, test_index in kf.split(X):
    X_train, X_test = X[train_index], X[test_index]
    y_train, y_test = y[train_index], y[test_index]
    
    model = LogisticRegression(max_iter=50)
    model.fit(X_train, y_train)
    
    # 预测并计算准确率
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    accuracies.append(accuracy)

# 输出每折的准确率和平均准确率
print("KFold Accuracies:", accuracies)
print("KFold Average Accuracy:", np.mean(accuracies))
```
</NoteCell>

<div class='abs mt-10' v-motion
    :enter='{scale: 0}'
    :click-18-20='{scale: 1}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/hot/qa.png'>
</div>
</div>

<!--
如果我们只有很小的一个数据集，在训练时，我们还必须得将它划分为训练集、验证集和测试集，然后用训练集和验证集对模型进行调优，最后用测试集来评估模型的性能。

可是当数据集本身就很小时，还要进行这样的划分，就会使得训练集的数据量变得非常少，这无疑会降低模型的泛化能力，导致模型的性能降低，也使得我们对训练出来的模型信心不足。

那么，这种情况下，我们应该怎么办呢？

[click]

一种称为k-fold的方法可以帮助我们。它的基本步骤是


[click]

将数据集随机分成 k 个大小相等或者相近、互斥的子集


[click]

对于每一个子集i


[click]

将它作为验证集，

[click]

并使用剩余的k-1个子集作为训练集

[click]

然后计算每个子集上的验证误差。这样我们将得到k个验证误差

[click]

最后，我们对这k个验证误差进行统计分析，一般是求均值。因为k的取值不会很大，所以其它别的统计方法用不起来。


[click]

我们通过一个例子来看K-fold应该如何使用。在这里，我们人工合成了一个不平衡的三分类数据集。我们运行一下，看看它的分类情况。


[click]

我们也可以通过直方图来查看其分布，这样更直观，也是常用的数据探索方法。


[click]

<run></run>

我们先来看看对这个数据集，进行一次随机训练，结果如何。

我们得到的准确率是0.7.记住这个数字。


[click]


在这次训练中，有一个特别的参数，max_iter，我们给到它的值是5。这个参数被称为超参数，它是用来定义模型的行为的参数，对模型的性能有重要影响。但它又不是通过数据训练出来的，而是根据我们的经验、对模型的理解指定的，所以，被称为超参数。

我们先不去探究它的含义是什么，但是，让我们好奇的是，如果我们换一个数值，模型的准确率会不会有不同？


[click]

<run></run>

我们把它的值改为50，再运行一下。现在的准确率是074，有一些提高。也许进一步加大max_iter的值，准确率还能进一步提高。但是，我们可以在实际生产中，使用 50，甚至更大的 max_iter 来训练模型，并使用训练后的模型吗？我们要如何回答这个问题？

这就是我们要使用k-fold的场景之一。通过k-fold划分，我们就从原始的小数据集中，变出来k个新的数据集，可以进行k次训练。如果最优的超参数在这k次训练中，都得到一致的好的结果，我们就能建立起对这个超参数的信心。


[click]

kfold及交叉验证要如何实现呢？我们通过这个例子来说明。


[click]

首先，我们从sklearn中导入KFold，并实例化一个对象。这里我们打算把数据分成5折，并且将数据进行随机打散。


[click]

然后我们定义一个数组，我们将用它来保存每次训练得到的准确率。


[click]

然后我们调用kf.split来对训练数据进行划分。这个函数将会返回记录的索引，而不是记录本身。我们通过这些索引来取出数据。

此后就是跟平常一样进行训练，拿到模型的评估指标，保存到之前定义的accuracies数组中。


[click]

最后，我们计算所有训练的准确率的均值。我们现在运行一下，看看结果。

均值是0.742，跟之前的准确率差不多。各个子集的准确率之间，方差不算大。


[click]

我们从accuracies数组中可以看到，其中有几折的准确率达到了0.76。现在有一个问题，我们能不能把这几折训练出来的模型，当成最终模型呢？

或者说，k-fold的结果，应该怎么运用呢？


[click]

答案时，k-fold是用来进行模型评估的。如果一个模型在k-fold的结果中，都表现很好，那么这个模型就是好的，我们选择的超参数，泛化能力会比较强。但是，我们最终要保存的模型，是要拿这些超参数，在全量的数据集上进行训练得到的，不能使用k-fold的中间结果。


[click]




-->

