---
clicks: 2
---

<div class='abs mt-40 w-full' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

```
from sklearn.model_selection import KFold

# 创建 KFold 对象，设置 K=5
kf = KFold(n_splits=5, shuffle=True, random_state=42)
```
</div>

<div class='abs w-full mt-20' v-motion
     :enter='{opacity: 0}'
     :click-1='{opacity: 1}'>

```python{all|3,6}{at:2}
from sklearn.model_selection import TimeSeriesSplit

tscv = TimeSeriesSplit(n_splits=5)
accuracies = []

for train, test in tscv.split(X, y):
    X_train = X[train]
    y_train = y[train]
    X_test = X[test]
    y_test = y[test]
    
    model = LogisticRegression(max_iter=200)
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    
    accuracy = accuracy_score(y_test, y_pred)
    accuracies.append(accuracy)
    
print("Accuracies:", accuracies)
print("Average Accuracy:", np.mean(accuracies))
```
</div>

<!--
k-fold 方法可以运用在普通的数据集上。它在划分数据时，只保证了每个 fold 的之间的样本数量大致相等，但是往往打乱了顺序。

这对普通的机器学习任务并没有影响。但在量化交易中，我们处理的数据常常是有时间上的先后顺序的。如果我们用来训练的数据晚于预测的数据，这很可能会带来未来数据，同时，如果特征中有一些因子是基于时序构建的，这样前后颠倒也会导致逻辑混乱。


[click]

因此，我们需要专门为时间序列数据的交叉验证发明新的方法。在 Sklearn 中提供了 TimeSeriesSplit 方法来帮助我们完成数据的划分，并保证时间顺序不被打乱。

[click]

从使用上来看，它与kfold十分相似。实现细节上的不同，被sklearn隐藏起来了。


-->
