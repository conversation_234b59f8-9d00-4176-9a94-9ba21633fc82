---
clicks: 8
---

<div class='abs mt-15 w-full' v-motion
     :enter='{opacity: 1, x:350, y: 200}'
     :click-1='{opacity: 0}'>

## 一阶优化算法
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

## 一阶优化算法
### 梯度下降
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>

## 一阶优化算法
### 梯度下降
### 动量法
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>

## 一阶优化算法
### 梯度下降
### 动量法
### 自适应梯度下降
</div>


<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>

## 一阶优化算法
### 梯度下降
### 动量法
### 自适应梯度下降
### MSProp
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{opacity: 1}'>

## 一阶优化算法
### 梯度下降
### 动量法
### 自适应梯度下降
### MSProp
### Adam
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{opacity: 1}'>

## 一阶优化算法
## 二阶优化算法
### 牛顿法
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{opacity: 1}'>

## 一阶优化算法
## 二阶优化算法
### 牛顿法
### 拟牛顿法
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-8-9='{opacity: 1}'>

## 一阶优化算法
## 二阶优化算法
## 零阶优化算法
### 粒子群优化算法
### 遗传算法
</div>

<FlashText v-click="[1,2]"
           class='abs mt-30 text-center w-full text-3xl'>

批量梯度下降 Batch Gradient Descent
</FlashText>

<FlashText v-click="[1,2]"
           class='abs mt-50 text-center w-full text-3xl'>

小批量梯度下降 Mini-Batch Gradient Descent, MBGD
</FlashText>

<FlashText v-click="[1,2]"
           class='abs mt-70 ml-20 text-center w-full text-3xl'>
随机梯度下降 Stochastic Gradient Descent, SGD
</FlashText>

<FlashText v-click="[2,3]"
           class='abs mt-50 ml-20 text-center w-full text-3xl'>

动量法 Momentum Gradient Descent
</FlashText>

<div class='abs mt-20 ml-40 w-full' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/gradient-func.jpg'>
</div>
</div>

<FlashText v-click="[3,4]"
           class='abs mt-50 ml-40 text-center w-full text-3xl'>

AdaGrad
</FlashText>

<FlashText v-click="[4,5]"
           class='abs mt-50 ml-20 text-center w-full text-3xl'>
RMSProp Root Mean Square Propagation
</FlashText>

<FlashText v-click="[5,6]"
           class='abs mt-50 ml-20 text-center w-full text-3xl'>

Adam Adaptive Moment Estimation
</FlashText>


<FlashText v-click="[7,8]"
           class='abs mt-50 text-center w-full text-3xl'>

BFGS / L-BFGS
</FlashText>

<!--
刚刚我们简单地介绍了梯度下降。这是机器学习中最基本的优化算法。它也有许多版本。根据优化过程中使用的梯度信息，可以分为一阶优化算法、二阶优化算法和零阶优化算法。

我们先来看一阶优化算法。


[click]

它又有三种版本。从上到下，训练速度依次提升，但随机梯度下降收敛路径不稳定，容易陷入局部最优。在创建模型时，有时候我们会看到这样的参数选择。

[click]

动量法是在梯度下降的基础上，增加了动量项来加速梯度降的一种方法。


[click]

AdaGrad 是一种自适应学习率的优化算法，通过动态调整每个参数的学习率来加速收敛。在右图中，显然我们会希望最初的学习率可以大一点，但在逼近底部时，学习率小一点，这样可以避免一下子越过最低点，反而不能收敛的情况。这就是自适应学习率的由来。


[click]

在AdaGrad的基础上，人们又推出了 RMSProp， 它通过引入指数加权平均来平滑梯度平方的累加和，避免学习率过早减小。


[click]

把动量法和 RMSProp 的优点结合起来，就是Adam算法。Adam 是目前最常用的优化算法之一


[click]

如果我们利用目标函数的二阶导数，就可能更快地收敛到全局极小值，因此，这一类优化算法就称为二阶优化算法。但是，它需要利用海森矩阵，计算量较大。


[click]

于是，人们发明了拟牛顿法。它不需要精确计算hessian矩阵，而通过近似 Hessian 矩阵来降低计算复杂度。它又可分 BFGS 和 L-BFGS 两种方法。L-BFGS 适用于大规模数据集，是实际应用中最常用的拟牛顿法。

在xgboost中，就实现了二阶优化算法。这也是为什么它不能使用像MAE这样的函数作为损失函数的原因。


[click]

零阶优化算法主要涉及粒子群优化和遗传算法。

好，到这里为止，我们就大致了解了各种优化算法。问题的引起是因为我们要通过梯度下降方法来将梯度函数最小化，而在实际工程问题中，存在梯度消失、梯度爆炸、局部最优等情形，于是就产生了各种优化算法。

我们只需要大致知道每种算法的优缺点，和它们的应用场景，能在创建模型时，知道如何选择，特别是如何搭配损失函数即可。这一节我们也讲得比较浅显。
-->
