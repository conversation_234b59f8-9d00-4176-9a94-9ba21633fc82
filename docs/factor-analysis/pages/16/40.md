---
clicks: 0
---

<NoteCell init
          class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-='{ scale: 1}'>

```python
from sklearn.datasets import make_classification
from collections import Counter
import matplotlib.pyplot as plt

# 生成类别不平衡的三分类数据集
X, y = make_classification(
    n_samples=500,        # 样本总数
    n_features=10,         # 特征数量
    n_informative=5,      # 有用的特征数量
    n_redundant=5,         # 冗余特征数量
    n_classes=3,           # 类别数量
    weights=[0.05, 0.3, 0.65], # 类别不平衡比例
    flip_y=0.01,           # 标签噪声比例
    random_state=42        # 随机种子
)
```

</NoteCell>
<NoteCell class='abs mt-10 w-full'
          layout="horizontal"
          outputMt="1rem"
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score
from sklearn.linear_model import LogisticRegression

skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

stratified_accuracies = []

# 进行分层 K 折交叉验证
for train_index, test_index in skf.split(X, y):
    X_train, X_test = X[train_index], X[test_index]
    y_train, y_test = y[train_index], y[test_index]
    
    model = LogisticRegression(max_iter=200)
    model.fit(X_train, y_train)
    
    # 预测并计算准确率
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    stratified_accuracies.append(accuracy)

# 输出每折的准确率和平均准确率
print("StratifiedKFold Accuracies:", stratified_accuracies)
print("StratifiedKFold Average Accuracy:", np.mean(stratified_accuracies))
```
</NoteCell>

<!--
k-fold 改善了数据集过拟合时如何评估模型的问题。但是，数据集还可能存在样本分布不均衡的问题。如果样本又少，分布又不均衡，那么，有没有可能在k-fold中抽样时，导致分布更加不均衡？

这种可能性是存在的。所以，人们又发明了分层抽样的交叉验证。

分层抽样的交叉验证在使用上跟普通的K-fold没有太大的区别

<run></run>

但是，经过分层抽样的交叉验证，我们对max_iter的值就更加有信心了。
-->
