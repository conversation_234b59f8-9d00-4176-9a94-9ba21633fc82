---
clicks: 6
---

<div class='abs w-full mt-30' v-motion
     :enter='{opacity: 1, x:0, scale: 1}'
     :click-1='{x: -300, scale: 0.7}'
     :click-8='{opacity: 0}'>
<div style='width:500px;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/tsfresh-rolling-forecasting.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>图片来源：tsfresh 文档</span>
</div>
</div>

<div class='abs mt-25 w-55% ml-45%' v-motion
     :enter='{opacity: 0}'
     :click-1-8='{opacity: 1}'>

```python{all|6|7-9|11-16|6-9|11-16|all}{at:1}
initial_window = 50

predictions = []
actuals = []

for i in range(initial_window, len(X)):
    X_train = X[:i]
    y_train = y[:i]
    y_test = y[i:i+1]
    
    model = LogisticRegression(max_iter=200)
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    
    predictions.append(y_pred[0])
    actuals.append(y[i])

print("Average Accuracy:", accuracy_score(predictions, actuals))
```
</div>



<!--
上一节讲到的时间序列交叉验证方式，在量化交易中仍有不足之处。它每一个子集k的数据，都跟前一个子集一点重叠都没有。换句话说，两个子集之间的纽带被切断了。

但这并不是金融数据本来该有的状态。金融数据天然地，k时刻与k-1时刻之间，应该相互关联。这个关联，可以通过数据重叠来重建。


[click]

对数据集进行循环划分


[click]

初始训练集包含前 n 个数据点，验证集包含接下来的一个或几个数据点。


[click]

在初始训练集上训练模型，并在验证集上进行预测


[click]

将验证集中的数据点加入训练集


[click]

继续训练模型，并在下一个验证集上进行预测。


[click]

重复上述步骤，直到遍历完所有数据点。

在这里，代码只演示了验证集只包含一个数据点的情况。我们也可以很容易扩展到多个数据点。

-->
