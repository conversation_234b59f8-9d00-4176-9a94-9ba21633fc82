---
clicks: 2
---

<div class='abs w-full mt-30' v-motion
     :enter='{opacity: 1,x:0, scale:1}'
     :click-1='{x: -250, scale: 0.8}'
     :click-2='{opacity: 0}'
    >
<div style='width:40%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/k-fold.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>credit: miro@medium</span>
</div>
</div>


<div class='abs w-full mt-30' v-motion
     :enter='{opacity: 0,x:250, scale:0.8}'
     :click-1='{opacity: 1}'
     :click-2='{scale: 1, x:0}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/loocv.webp'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>credit: ISLR</span>
</div>
</div>

<!--
在k-fold中，如果样本数是n，那么每一折的样本数就是n/k。然后我们取其中的任一折作为验证集，其余的k-1折作为训练集。

可以推论，验证集的样本数越小，折数就越多。


[click]

当样本数取极限1时，k-fold就变为留一法。

[click]

优点是几乎完全利用了所有数据，减少了因数据划分带来的偏差。缺点是计算成本极高，尤其是在数据集较大时。

-->
