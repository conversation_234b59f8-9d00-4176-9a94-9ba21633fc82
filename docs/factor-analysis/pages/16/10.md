---
clicks: 5
layout: two-cols
right: 60%
---

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-1-5='{opacity: 1}'>

## 梯度和学习率
</div>

<div class='abs mt-50 ml-20' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

$$f(x) = WX + b$$
</div>

<div class='abs mt-25 ml-20' v-motion
     :enter='{opacity: 0}'
     :click-2-4='{opacity: 1}'>

$$f(x) = WX + b$$
$$f'(x) = WX + b$$
$$J_\theta = f'(x) - f(x)$$
</div>

<div class='abs mt-25 ml-20' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>

$$f(x) = WX + b$$
$$f'(x) = WX + b$$
$$J_\theta = f'(x) - f(x)$$
$$ G = \nabla_W J(W) = \frac{\partial J(W)}{\partial W}$$
</div>


<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

## 梯度和学习率
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{opacity: 1}'>

## 梯度和学习率
## 局部极小值
## 全局最小值
</div>


::right::

<div class='abs mt-20' v-motion
     :enter='{opacity: 1, x:-200, scale:1}'
     :click-1='{ x: 0, scale: 0.8}'
     :click-4='{opacity: 0}'>

<div style='width:100%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/gradient-descent-london-fod.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>
credit: www.artrabbit.com/events/gradient-descent</span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-4-6='{opacity: 1}'>
<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/gradient-func.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>梯度函数</span>
</div>
</div>

<!--
尽管我们说过，这门课不会讲机器学习底层的概念，不过在这个地方，我们也略微提一下，方便理解算法优化。

[click]

万物皆函数。机器学习是要通过数据训练来发现数据的规律。这个规律，我们就假设可以通过一个函数来定义。对于每一个输入的自变量x，函数f(x)会返回一个因变量y。


[click]

假设我们能通过训练找到一个函数f'(x)，两个函数之间的差值，就是损失函数。如果损失函数的输出值非常小，我们就认为训练出来了函数fx的近似模型。

我们不知道f'(x)是多少，也不知道损失函数应该是什么样子，因此，也无法用数学方法来求解损失函数的最小值。但是，我们可以逐步逼近的方法，使得f'(x)与f(x)的差值，也就是损失函数的值尽可能小。


[click]


我们的做法是，随机初始化一个权重网络W，然后让它与X相乘。然后通过不断地改变W的值，计算f'(x)是否与f(x)更加接近了。这里要确定的是，该如何改变W中的值呢？


[click]

这个方法就是梯度下降。我们定义了一个梯度函数，通过一些复杂的数学变换，将参数权重网络、训练数据和损失函数三者关联起来。通过优化梯度函数，就可以使得损失函数最小化。

结合右图来说，我们是沿着梯度函数下降的方向来更新参数。在更新参数时，涉及到每次更新参数的多少问题，它是由学习率和该时刻的梯度共同决定的。

在右图中，两点之间的x的距离就是学习率，两点之间的y的距离就是要更新的参数的delta。因此，学习率越大，每次权重更新的就越快；但有可能在底部一下子越过最优化点，甚至导致无法收敛；如果学习率过小，每次更新的步长太小，则不仅训练速度慢，也可能陷入局部优化。

在我们的示例中，梯度函数是光滑的，可导的，所有有全局最小值。但在工程中，即使函数本身是光滑的，也可能我们采样到的数据是有噪声的，导致反映出来的曲线不平滑。这样就可能在训练中导致遇到局部最小值后，训练就停下来。


[click]

因此，就有了局部极小值和全局最小值的概念。训练算法的优化，就是要以最快的速度找到全局极小值，避免陷入局部极小值。
-->
