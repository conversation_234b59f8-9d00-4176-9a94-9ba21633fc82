---
aspectRatio: 16/9
title: 第 19 课 定价未来 - 用LightGBM预测价格
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
lineNumbers: true
drawings:
  enabled: true
  persist: false
  presenterOnly: false
  syncAll: true
  zIndex: 99
---

<!--
大家好，我们开始上课了。

今天的课程中，我会介绍如何用lightgbm来预测价格，从原理到实现，到结果的分析。最终我给出来的实现版本可能会让大家感到惊喜，不过，最关键的还是要掌握我们课程中介绍的原理、思路、方法和技术，这样才有能力去应对变幻莫测的未来

我在格物这家公司做量化的时候，公司因为刚起步，还没有自己的量化it系统，我的精力就主要放在系统开发上。当时为了迅速打开局面，收购了一些机器学习策略，在2021年底，有一个策略做到两周涨了75%。但是，进入到2022年3月以后，市场环境转差，这个策略就开始赔钱。我们没有源码，对方一时也查不出原因，最终我们在这个策略上反而是赔了一些钱。

我讲这个例子，是想再次跟大家强调，赚钱的饭碗必须得自己端着，我们这个课程也从来不是以承诺收益来获客的。当然，如果你确实觉得课程好，也欢迎推荐自己的朋友来学习。如果策略有效，帮大家获得了收益，也可以跟我们讨论进一步合作。

-->

---
title: 从一个假设开始
src: 19/10.md
---


---
title: /02 机器的能力边界
layout: section
---

<!--
原理并不复杂。但要获得成功，我们首先需要理解，机器学习能做什么，不能做什么。
-->

---
title: 从纯粹的价格出发
src: 19/20.md
---

---
title: 如何划分数据
src: 19/23.md
---

---
title: 新问题：评估函数不够用了
src: 19/26.md
---


---
title: 模型为何不生效？
src: 19/29.md
---

---
title: /03 base model
layout: section
---

<!--
接下来的内容，本来可以直接给出一个标准答案。但是，为了大家能够好地理解如何运用lightgbm构建价格预测模型。我决定还是多举几个例子，通过这些例子的反复比较，大家可能会更好地理解相关的机制。

为了能简洁地表达每个例子之间的区别和联系，我们需要先将它们之间共同的部分抽象出来，生成一个基类。

这就是我们这一节要介绍的一个base model.
-->

---
title: 模型的基本功能
src: 19/30.md
---

---
title: 示例：如何实现一个子类
src: 19/39.md
---

---
title: /04 加一点规律
layout: section
---

<!--
我们刚刚实现的版本，它的主要目的是为了演示如何写一个子类，从而实现完整的模型功能。我们看到，通过基类与子类的划分，子类的工作量就很小了。

不过，我们说不能依靠lightgbm来学习规律，lightgbm只相当于一个分段函数，我们要自己把规律找出来，lightgbm擅长的是，找到对应区间应该使用什么规律。具体来说，要如何实施呢？

现在，我们就来进行第一次尝试。
-->

---
title: 加一点规律
src: 19/40.md
---

---
title: 最终幻想
layout: section
---

<!--
这一版我们将进行两个重要改进。

第一，我们消灭量纲差异。我们抛弃绝对数值，无论是在特征、还是目标上，都使用百分比。第二，我们直接计算出来多项式拟合预言的数值是多少，然后计算出多项式拟合预言的涨跌幅，把它作为特征。同样地，我们让lightgbm通过多项式拟合的误差来决定，是否要使用这个预言。
-->

---
title: 最终幻想
src: 19/50.md
---

---
title: end
layout: end
src: 19/60.md
clicks: 1
---
