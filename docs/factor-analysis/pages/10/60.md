---
clicks: 6
---

<div class='abs mt-30' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>


<FlashText class="text-center w-full text-3xl pb-3">https://tsfresh.readthedocs.io/</FlashText>

![50%](https://images.jieyu.ai/images/2024/10/tsfresh.png?width=30%&align=center)

</div>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'
          :click-5='{ scale: 0}'>

```python
from tsfresh import extract_features

df = load_bars(start, end, 2).reset_index().dropna(how='any')
features = extract_features(df, column_id="asset", column_sort="date")
print(f"总特征数：{len(features.columns)}")
features.tail()
```
</NoteCell>

<FlashText v-click="[1,2]"
           class='abs mt-1/3 text-center w-full text-3xl'>
! pip install tsfresh
</FlashText>

<div class='abs mt-50 flex w-full justify-center' v-motion
     :enter='{opacity: 0, scale: 1}'
     :click-3-4='{ opacity: 1, scale: 0.8}'>

<div>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th>date</th>
      <th>asset</th>
      <th>open</th>
      <th>high</th>
      <th>low</th>
      <th>close</th>
      <th>volume</th>
      <th>amount</th>
      <th>price</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>2023-12-22</td>
      <td>603558.XSHG</td>
      <td>10.01</td>
      <td>10.14</td>
      <td>9.84</td>
      <td>9.90</td>
      <td>4532920.0</td>
      <td>45185078.72</td>
      <td>9.83</td>
    </tr>
    <tr>
      <td>2023-12-25</td>
      <td>603558.XSHG</td>
      <td>9.83</td>
      <td>9.91</td>
      <td>9.58</td>
      <td>9.73</td>
      <td>6863000.0</td>
      <td>66436479.00</td>
      <td>9.72</td>
    </tr>
    <tr>
      <td>2023-12-26</td>
      <td>603558.XSHG</td>
      <td>9.72</td>
      <td>9.85</td>
      <td>9.58</td>
      <td>9.63</td>
      <td>3617702.0</td>
      <td>35051773.62</td>
      <td>9.61</td>
    </tr>
    <tr>
      <td>2023-12-27</td>
      <td>603558.XSHG</td>
      <td>9.61</td>
      <td>9.74</td>
      <td>9.50</td>
      <td>9.66</td>
      <td>3731500.0</td>
      <td>35943641.00</td>
      <td>9.66</td>
    </tr>
    <tr>
      <td>2023-12-28</td>
      <td>603558.XSHG</td>
      <td>9.66</td>
      <td>9.70</td>
      <td>9.30</td>
      <td>9.51</td>
      <td>9874200.0</td>
      <td>93108759.00</td>
      <td>9.52</td>
    </tr>
  </tbody>
</table>
</div>
</div>

<div class='abs w-full mt-40' v-motion
     :enter='{opacity: 0}'
     :click-5='{ opacity: 1}'>

```python
extract_features(
    df[["asset", "date", "close", "volume"]], 
    column_id="asset", 
    column_sort="date"
)
```
</div>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-6='{ scale: 1}'>

```python
fc_parameters = {
    "length": None,
    "large_standard_deviation": [{"r": 0.05}, {"r": 0.1}]
}

features = extract_features(
    df[["asset", "date", "close", "volume"]], 
    column_id="asset", 
    column_sort="date",
    default_fc_parameters=fc_parameters, 
    n_jobs=1
)

features.columns
```
</NoteCell>

<!--
tsfresh能自动提取大约600个左右的因子。一些比较新的因子，是基于最新发表的论文，tsfresh也在文档中提供了到原始论文的链接。


[click]

在我们的服务器上，忘记安装tsfresh了。我们后面会更新构建脚本，新同学的环境会事先安装好。如果大家在运行这一段代码时出错，请自己安装一下


[click]

核心方法是extract_features。最基本的参数是输入数据df, column_id 和column_sort.

我们先看第一个参数，我们应该如何提供数据给extract_features呢？

[click]

一个普通的、扁平的dataframe就可以。注意在这个表格中，我们是允许提供多个资产的，只是不能设置为多重索引。

那么这种情况下，我们也会有一个问题，tsfresh如何区分哪些数据属于某一个资产呢？毕竟，多数运算都只有在资产组内进行才有意义。

答案是使用column_id参数。这个参数用来告诉tsfresh，哪一列是资产的id。有了它，在进行计算时，tsfresh会自己进行分组。

还有一个问题，时间顺序对tsfresh很重要。因此，tsfresh需要我们告诉它，哪一列可以用来对输入行进行排序。

[click]

<run></run>

现在我们运行一下。它的运行比较花时间，所以，它会给出一个进度条。我们现在看到，进度条的总任务数是14，正好对应2*7，即两支资产，每支资产有7列。这些都可以成为独立的任务。

总共提取了5480个特征。相当于为每一个输入列，提取了约800个特征。如果这些特征都能作为因子的话，那么，我们一下子就获得了约800个因子。


[click]

但是，不是每一个特征都会成为有效因子。而且，一个resonable的作法是，当我们理解了一个特征之后，才考虑将其作为因子，而不是一股脑地把所有的特征都当作因子。当你不了解一个因子时，贸然把它加入到策略中，几乎一定是有害的，因为好的因子并不是唾手可得的。

另外一方面，tsfresh的运行很花时间。至少在试验阶段，我们也需要知道如何压缩任务。

首先，排除掉不重要的列，我们是在tsfresh之外来做这件事。大家注意看第2行，方法是不向tsfresh提供这些数据。


[click]

然后，我们可以通过default_fc_parameters来指定要生成哪些特征。

这个参数要求输入一个字典，key是特征名，value要么为None,要么为一个数组，允许我们指定多组参数。比如，在这里，lsd特征有一个参数r，我们给它设置了两组参数，分别是0.05和0.1

由于我们输入数据有两个时间序列，close和volume，所以，最终将生成 6个特征，其中length一个，lsd两个。

<run></run>

现在速度快了很多，因为我们只要求它生成两类特征，一个是length, 另一个是lsd。除此之外，大家注意看，我们将njobs设置为1，这样也避免了多进程。

当然，如果计算量很大，我们还是要使用多进程的。

最后，生成的特征共有6列，这与我们的预期一致。
-->
