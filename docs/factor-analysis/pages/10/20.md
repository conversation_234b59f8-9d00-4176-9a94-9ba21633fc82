---
clicks: 4
layout: default
---

<NoteCell class="abs" init>

```python
code = "000001.XSHE"
start = datetime.date(2023, 12, 20)
end = datetime.date(2023, 12, 31)

PAYH = load_bars(start, end, (code, )).xs(code, level=1)
barss = load_bars(start, end, 2000)
```
</NoteCell>

<div class='abs w-30% h-50% left-35% top-20%'
     v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/10/galton-board.gif)
</div>

<NoteCell class='mt-10 abs'
          :enter="{ scale: 0, width:'100%'}"
          :click-1="{ scale: 1}"
          :click-2="{width: '50%'}"
          :click-3="{width:'100%'}"
          :click-4="{scale:0}">

```python
def calc_continuous_status(df):
    df["ret"] = df.close.pct_change()

    down = df["ret"] < 0
    g = (down.diff() != 0).cumsum()
    g_cumdown = down.groupby(g).cumsum()

    df["status"] = g_cumdown

    up = df["ret"] > 0
    g = (up.diff() != 0).cumsum()
    g_cumup = up.groupby(g).cumsum()
    df.loc[df["status"] == 0, 'status'] = g_cumup * -1
    
    return df["status"]

_ = alphatest(2000, start, end, calc_factor = calc_continuous_status, bins=10)
```
</NoteCell>

<!-- 习题 -->

<div class='abs w-50% left-50% mt-10' v-motion
     :enter='{opacity: 0}'
     :click-2='{ opacity: 1}'
     :click-3='{ opacity: 0}'
     >


<table border="1" class="dataframe scale-80">
  <thead>
    <tr style="text-align: right;">
      <th>date</th>
      <th>status</th>
      <th>ret</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>2018-01-02</th>
      <td>0</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>2018-01-03</th>
      <td>1</td>
      <td>-0.027007</td>
    </tr>
    <tr>
      <th>2018-01-04</th>
      <td>2</td>
      <td>-0.006001</td>
    </tr>
    <tr>
      <th>2018-01-05</th>
      <td>-1</td>
      <td>0.003774</td>
    </tr>
    <tr>
      <th>2018-01-08</th>
      <td>1</td>
      <td>-0.025564</td>
    </tr>
  </tbody>
</table>
</div>

<NoteCell class='mt-10 abs w-full'
          :enter="{ scale: 0}"
          :click-4="{scale:1}">

```python
def calc_continuous_status(df):
    df["ret"] = df.close.pct_change()

    down = df["ret"] < 0
    g = (down.diff() != 0).cumsum()
    g_cumdown = down.groupby(g).cumsum()

    df["status"] = g_cumdown

    up = df["ret"] > 0
    g = (up.diff() != 0).cumsum()
    g_cumup = up.groupby(g).cumsum()
    df.loc[df["status"] == 0, 'status'] = g_cumup * -1
    
    return df["status"]

quantiles = [0, 0.005, 0.05, 0.1,0.4, 0.8,0.85, 0.9, 0.95, 1]
_ = alphatest(2000, start, end, calc_factor = calc_continuous_status, quantiles=quantiles, max_loss=0.99, bottom=2)
```
</NoteCell>


<!--
有很多人相信随机游走学说，如果随机游走学说能够应用在股票市场上，那么就会出现这样的结果:

任何一个标的，他每一天的涨或者跌都是独立随机事件。如果我们统计连续涨跌的次数，这个变量N就会服从正态分布。

这个原理我们可以通过图中的galton board来模拟。

因此，一旦某个标的连续涨跌的天数太长，他就应该发生回归，因为沿着之前的方向继续涨会继续跌是小概率事件。

基于这个原理，我们把这个随机变量也做成因子进行检验试一试。


[click]

<run></run>

这段代码就是比较有技巧，我会留给大家作为练习。

统计状态连续次数的算法在量化中是一个常用的算法，比如，我们常常要统计N连阳，多少个连续涨停等等，都会用到这个算法

在我们的quantide weekly周刊上面已经发表了这个算法，所以这里就不仔细讲了。


[click]

右边是因子计算的中间结果，我们可以看到，第二行这里是下跌，所以因子值是1；第三行连续下跌两天了，所以因子值就是2；

接下来变成上涨，所以因子值重新计数，变为-1

在这里，我们使用了正负号来表明是上涨还是下跌。

注意因子值是离散值，所以，我们要按by bins的方式分层。

[click]

好，接下来我们看他的运行结果。

在因子计算的过程中，我们已经把因子值调整成为上涨最多的，因子会分配到第一层，下跌最多的会分配到第二层，以此类推。

但是从结果上，因子不太理想。 我们来分析一下。

我们先看因子一到2层，他们对收益的贡献是正的，但是在因子检验当中，我们是要对这部分做空的。 对因子值比较大的2层，他们对收益的贡献是负的，但在因子检验中，我们对这部分却是做多的。

不仅如此，我们这个因子的原理就是极端情况下的回归，所以我们只是把因子均分成10层是远远不够的，因为极端情况的样本数会非常的少。 现在我们就把方案优化一下。

[click]

<run></run>

这也是我们所有示例中，第一次出现指定按分位数分层的情况。如果我们按bins来分层，并且自己指定每个桶的边缘值的话， 也是可以的，但我们可能要先做一些统计。

这里要讲的第二个地方，就是我们这一种特殊的分层会导致大量的记录会被drop掉，在这个地方我们是允许的。 所以大家看max_loss，我们已经设置成为99%了。

现在我来看一下运行结果，年化阿尔法到了15.3%。由于我们指定的bottom等于2，所以自动的排除了每天的连板股。所以这个结果在实盘中是可以实现的。

如果说这个因子要什么问题，那他最大的问题就是交易信号会比较少。 这点我们可以从累计收益当中看出来。

-->
