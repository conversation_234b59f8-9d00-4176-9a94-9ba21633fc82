---
layout: default
---

<div class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
def calc_continuous_pct(df):
    df["ret"] = df.close.pct_change()
    df["cum_ret"] = 0
    df["cum_direction"] = 0

    df["flag"] = np.where(df.ret > 0, 1, np.where(df.ret < 0, -1, 0))
    current_flag = None
    cum_ret = 1.0

    for i in range(len(df)):
        if df.at[df.index[i], 'flag'] != current_flag and current_flag is not None:
            cum_ret = 1.0
        current_flag = df.at[df.index[i], 'flag']

        if current_flag != 0:
            cum_ret *= (1 + df.at[df.index[i], 'ret'])
            df.at[df.index[i], 'cum_ret'] = cum_ret - 1
            df.at[df.index[i], 'cum_flag'] = current_flag

    return df.cum_ret

np.random.seed(78)
_ = alphatest(2000, start, end, calc_factor = calc_continuous_pct)
```
</div>

<!--

这个因子的构造跟上一个因子差不多，只不过他把次数改成了幅度。 所以在原理上，他又有一点类似于RSI。

这个地方我要讲一下，因子计算的算法不是最优的，这里最优的算法是先对收盘价取对数，再进行diff, 对diff判断方向，再跟上一个因子的计算方法一样，同方向的diff进行累加，这样速度会快不少。

这个因子跟上一个因子一样，也需要对分层进行仔细的调节。一方面要避免买入涨停板个股，另一方面要注意，只有极少数情况下这个因子才应该被交易。

总得来说，这三个因子都有较高的胜率，但是因子原理决定了，它们的交易信号比较少。所以，可以作为策略的补充。
-->
