---
clicks: 9
---

<div class="" v-motion :enter="{y: 200, x:250, opacity: 1}"
    :click-1="{y: 100}"
    :click-3='{opacity: 0}'>

## 沪指下跌4%时，能否抄底?

</div>

<div class="abs" v-motion :enter="{y: 150, opacity: 0}"
    :click-1="{opacity: 1}"
    :click-2="{opacity: 0}">

![33%](https://images.jieyu.ai/images/2023/06/sh_histo_pdf.png)
</div>


<NoteCell class='abs w-full' color="red"
          :enter='{ scale: 0}'
          :click-2-3='{ scale: 1}'>

```python
from scipy.stats import genhyperbolic
import akshare as ak

df = ak.stock_zh_index_daily(symbol="sh000001")
start = datetime.date(2018, 1, 1)

df = df.query('date >= @start')
pct = df.close.pct_change(1)

params = genhyperbolic.fit(pct[1:])
rv = genhyperbolic(*params)
print(f"继续下跌的概率为：{rv.cdf(-0.04):.2%}")
```
</NoteCell>


<NoteCell class='abs w-full' :enter="{scale: 0}"
          :click-3-7="{scale:1}">

```python {all|2|3|all}{at: 4}
def calc_single_day_loss(df):
    returns = -1 * df.close.pct_change(1)
    returns[(returns < -0.1) & (returns > 0.1)] = np.nan
    return returns

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)
np.random.seed(78)

_ = alphatest(2000, start, end, calc_factor = calc_single_day_loss)
```
</NoteCell>


<NoteCell class='abs w-full' :enter="{scale: 0}"
          :click-7="{scale:1}"
          >

```python {all|2|3|all}{at: 4}
def calc_single_day_loss(df):
    returns = -1 * df.close.pct_change(1)
    returns[(returns < -0.1) & (returns > 0.1)] = np.nan
    return returns

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)
np.random.seed(78)

_ = alphatest(2000, start, end, calc_factor = calc_single_day_loss, top=9)
```
</NoteCell>

<FlashText v-click="[8,9]"
    class="abs mt-1/3 text-center w-full text-3xl">%psource alphatest</FlashText>

<!--
之前我在招策略研究员的时候，我会考他们这样的问题：如果现上证指数下跌到4%，那么他继续下跌的概率有多大，能不能抄底了？通过这个问题我会考察他对PDF、CDF和ECDF的理解。


[click]

关于这个问题的详细讨论在我的公众号文章上面有介绍

最后我们证明了沪指符合广义双曲分布，所以当沪指下跌超过4%的时候，还会继续下跌的概率，实际上就是求广义双曲分布在负无穷大到-0.04之间的累计密度分布

[click]

<run></run>

我们运行一下，看看实际的结果应该是多少?

我们得到的结果是0.4%左右。也就是此时买入，大约99.6%的概率会盈利。 大家如果要验证的话，也可以从PCT这个数组里找出所有下跌超过4%的情况，然后处于所有的记录数，你会发现计算结果跟我们这里得到的结论是相一致的。

这个结论就启发我们是不是可以在个股上面也运用这个原理，把它构建成为一个因子。

[click]

<run></run>

 这就是我们构建的因子。这个因子非常简单，就是直接把资产的每日收益率作为因子。不过，有两点小调整


[click]

第一，我们要取收益的相反数作为因子。因为我们的目标是，如果当天涨幅过大，就做空；如果跌幅太大，就做多。因此这个方向是需要调整的

[click]

第二，考虑到跌停板上的个股还会继续下跌，涨停板上的个股也会继续上涨，所以我们需要把这部分给刨除掉。

排除涨停板跟跌停板应该使用历史的涨跌停数据，在这里为简单起见，我们就是过滤了涨跌幅绝对值大于10%的数据，在实际工作中应该使用更精确的涨跌停数据。

你的数据源应该提供历史的涨跌停价格，然后用每日收盘价跟涨跌停价格进行比较，如果相等就意味着当天有涨跌停。


[click]

现在我们来看看它的运行结果。

我们看到年化阿尔法是7.8%。

从因子分层来看，基本上是满足线性递增的要求的。

第10层有点不太理想，在我们已经排除了跌停的个股的情况下，第10层还是不太理想，这说明市场上的参与者，他们在抢反弹的情况下，是不会考虑最弱的标的的，所以我们也可以把这部分去掉，进行一个简单的优化。


[click]

<run></run>

 我们的优化方案就是加上top等于9的限制，具体是怎么实现的？大家可以去看一下alphatest的代码。 


[click]


 在notebook中，大家只要在单元格里面输入%psource 就可以显示函数的源代码。 这个技巧在我们notebook高级技巧那个文档里面有记录。


[click]

好，现在我们看到年化alpha是18.7%，非常优秀了。即使没有条件做空在存多的条件下，它的年化阿尔法也能够到14.6%。 这里我们就不重新运行了，大家自己回测一下
-->
