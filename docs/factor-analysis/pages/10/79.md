---
clicks: 3
---

<div class='abs mt-30' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![33%](https://images.jieyu.ai/images/2024/10/gap.jpg)

</div>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'
          :click-3='{scale: 0}'>

```python
def calc_gap(df):
    # 计算向上跳空和向下跳空
    up_gap_condition = df["open"] > df["high"].shift(1)
    down_gap_condition = df["open"] < df["low"].shift(1)

    # 计算向上跳空和向下跳空的值
    up_gap_value = (df["open"] / df["high"].shift(1)) - 1
    down_gap_value = (df["open"] / df["low"].shift(1)) - 1

    conditions = [up_gap_condition, down_gap_condition]

    choices = [up_gap_value, down_gap_value]

    df["gap"] = np.select(conditions, choices, default=0)

    return df

df = pd.DataFrame(
    {
        "open": [100, 107, 110, 100, 120],
        "high": [105, 110, 115, 120, 125],
        "low": [95, 100, 105, 110, 115],
    }
)

calc_gap(df)
```
</NoteCell>

<div class='abs ml-60% mt-30 bg-white' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{ opacity: 1}'>

<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th>open</th>
      <th>high</th>
      <th>low</th>
      <th>gap</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>100</td>
      <td>105</td>
      <td>95</td>
      <td>0.000000</td>
    </tr>
    <tr>
      <td>107</td>
      <td>110</td>
      <td>100</td>
      <td>0.019048</td>
    </tr>
    <tr>
      <td>110</td>
      <td>115</td>
      <td>105</td>
      <td>0.000000</td>
    </tr>
    <tr>
      <td>100</td>
      <td>120</td>
      <td>110</td>
      <td>-0.047619</td>
    </tr>
    <tr>
      <td>120</td>
      <td>125</td>
      <td>115</td>
      <td>0.000000</td>
    </tr>
  </tbody>
</table>
</div>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-3='{ scale: 1}'>

```python
def calc_gap(df):
    # 计算向上跳空和向下跳空
    up_gap_condition = df["open"] > df["high"].shift(1)
    down_gap_condition = df["open"] < df["low"].shift(1)

    # 计算向上跳空和向下跳空的值
    up_gap_value = (df["open"] / df["high"].shift(1)) - 1
    down_gap_value = (df["open"] / df["low"].shift(1)) - 1

    conditions = [up_gap_condition, down_gap_condition]

    choices = [up_gap_value, down_gap_value]

    df["gap"] = np.select(conditions, choices, default=0)

    invalid = np.abs(df.close.pct_change()) > 0.095
    df["gap"][invalid] = 0

    return df["gap"]

start = datetime.date(2018, 1, 1)
end = datetime.date(2023,12,31)

_ = alphatest(2000, start, end, calc_factor=calc_gap, bins=10, long_short=False)
```
</NoteCell>

<!--

如果次日开盘价格不在前一日的 True Range 范围内，就形成了一个缺口。缺口表明投资者在该方向上有较强的一致预期，这种预期往往是由某种强大的事件冲击引发的，从而导致集合竞价时，交易者出现抢筹现象。

在向上跳空的缺口中，缺口是开盘价减去前高；在向下跳空的缺口中，缺口是开盘价减去前低。在实现成为因子时，我们需要将其标准化。


[click]

这段代码实现了缺口的计算。我们来看看它是如何完成的。


[click]

首先，它标记所有的向上缺口

然后标记所有的向下缺口

在第7，8行，计算出缺口的涨跌比。

然后它使用numpy中的select 方法，将这两组数据、以及没有缺口的情况组装起来，成为一个数组。这个计算是高度向量化的，所以很快。


[click]

<run></run>

大家觉得这么简单的因子，会有效果吗？我们来回测一下看看。

大家注意看，这里使用的分层方法是by bins。为什么呢？大家想一想。

这是因为缺口相对来讲还是比较少的情况。所以，因子数据中，大量的数据会是零。如果我们使用by quantiles，就容易出现分层失败的情况。

这是尽管因子是浮点数，但by quantiles也会出现分层失败的一例。

现在结果出来了，我们看一下，结果非常惊人，年化达到了42.4%,6年累计收益达到了6倍。并且我们还可能取得更好的成绩。

大家觉得为什么？

第一，这里的第10层收益是负的，我们其实可以把它优化掉。

第二，我们做实盘时，会采用与alphalens不一样的方法。在Alphalens中，如果缺口发生在t0期，我们是以t1期的开盘价买入，t2期开盘卖出。
但在实盘交易中，由于缺口是开盘价打出来的，所以，我们一定是t0期的开盘后立即买入，甚至是集合竞价买入，这会比第二天买入更好。

所以，现在你不妨思考一下，为什么有一些股会在集合竞价快要结束时，猛地拉上来。

好，这就是我们今天讲的最后一个因子。

在教材中，还有更多的因子。比如，我们讲了国金证券的高智威的研报，遗憾规避理论因子。本质上它也是基于vwap的一个因子。我们这里不讲，因为它需要tick级数据，我们没办法验证。但原理是对的。

根据高智威，该因子达到了5.5的夏普率。

-->
