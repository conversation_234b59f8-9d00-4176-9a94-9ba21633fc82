---
clicks: 6
---
<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-5='{ scale: 0}'>

```python {all|7|9-13|15-18|20-26}
from tsfresh import extract_features
from tsfresh.utilities.dataframe_functions import roll_time_series

start = datetime.date(2023,1,1)
end = datetime.date(2023,12,31)
barss = load_bars(start, end, 100)
df = barss.reset_index().dropna(how='any')

rolled = roll_time_series(df[["date", "asset", "close"]],
                          column_id="asset",
                          column_sort="date",
                          min_timeshift=9,
                          max_timeshift=9)

settings = {
    "linear_trend": [{"attr": "slope"}],
    "approximate_entropy": [{"m" : 4, "r": 2}]
}

# 提取特征
features = extract_features(rolled[["date","id", "close"]], 
                            column_id="id", 
                            column_sort="date",                     
                            default_fc_parameters=settings,
                            impute_function=None)
features.tail()
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-5-6='{ scale: 1}'>

```python
slope_factor = features.filter(like="linear")
slope_factor = slope_factor.swaplevel()
slope_factor.index.set_names(["date", "index"], inplace=True)
slope_factor.tail()
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-6='{ scale: 1}'>

```python
prices = barss.price.unstack()
merged = get_clean_factor_and_forward_returns(slope_factor, prices)
create_returns_tear_sheet(merged)
```
</NoteCell>

<!--
现在，我们来看如何用tsfresh提取特征，并进行因子检验。

<run></run>

[click]

注意这里的dropna操作。tsfresh要求输入数据没有任何缺失值。

[click]

这一段是在生成滑动窗口数据。


[click]

这一次，我们只用两个特征。一个是斜率，另一个是最大近似熵。注意第16行的attr参数。

在前面，我们介绍了如何给特征设置参数。当时我们讲的是输入参数，即tsfresh在计算特征时，需要的那些参数。但是，有一些计算会有不止一个输出，象这里的斜率就是这样

在tsfresh内部，斜率计算要么就是使用scipy来计算的，要么是使用一样的算法，总之，它的输出有slope, intercept, r_value, p_value, std_err等等。tsfresh通过attr来让我们决定哪些值被保留。


[click]

这一段已经讲过了，就是开始提取特征。注意我们输入的数据是rolled，而不是原始的dataframe了。


现在我们检查输出结果。我们看到了两列数据。


[click]

我们先将 slope 特征提取出来，注意这里的 swaplevel 的调用。

在第一行，这里有一个filter操作。当我们使用tsfresh时，这个操作会很常用。因为tsfresh提出的特征实在是太多了，而且列的名字又很长，所以，我们要取某些列，可以用filter来过滤一下。


第2行也要注意。我们进行swap操作的原因是，tsfresh的输出结果，在索引上与alphalens的要求不一致。


[click]

最后，就是我们已经熟悉的操作了。我们来看一下检验结果。

Alpha是11.6%。这是我们熟悉的一个结果。大家可以在本章中，讲斜率因子的地方，把参数换成10日窗口，不去掉任何分层，多空组合看一下结果是否大致相同。

-->
