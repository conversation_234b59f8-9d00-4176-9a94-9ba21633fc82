---
clicks: 4
layout: default
---

<NoteCell class="abs" init>

```python
np.random.seed(78)

code = "000001.XSHE"
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

PAYH = load_bars(start, end, (code, )).xs(code, level=1)
barss = load_bars(start, end, 2000)
```
</NoteCell>

<NoteCell class='abs mt-10 w-95%'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
from numpy.lib.stride_tricks import as_strided
def rolling_slope(df, win:int):
    close = df.close.to_numpy()
    if len(close) < win:
        return pd.DataFrame(np.full((len(close), ), np.nan), index=df.index)

    stride = close.strides
    
    slopes, _ = -1 * np.polyfit(np.arange(win), 
                           as_strided(close, (len(close)-win+1, win), 
                           stride+stride).T,
                           deg=1)
    left_padd_len = len(close) - len(slopes)
    slopes = np.pad(slopes, (left_padd_len, 0), mode='constant', constant_values=np.nan)
    return pd.DataFrame(slopes, index=df.index)

np.random.seed(78)
_ = alphatest(2000, start, end, calc_factor = lambda x: rolling_slope(x, 30), top=7, long_short=False)
```
</NoteCell>

<NoteCell class='abs mt-10 w-95%'
          :enter='{ scale: 0}'
          :click-1-2='{ scale: 1}'
          >

```python {all}
import numpy as np
import matplotlib.pyplot as plt

num_points = 1000 
time = np.linspace(0, 10, num_points)
amplitude_factor = 1 + time  
frequency = 0.3 
phase = 0 
angle_degrees = 15
angle_radians = np.radians(angle_degrees)

y = amplitude_factor * np.sin(2 * np.pi * frequency * time + phase)

linear_trend = np.arange(1000) * 0.05
y += linear_trend

d1 = np.diff(y)
d1 = np.insert(d1, 0, [np.nan])

fig, ax1 = plt.subplots(figsize=(10,6))
line1, = ax1.plot(time, y, label='close')
ax1.set_xlabel('Time')
ax1.set_ylabel('Close')

ax2 = ax1.twinx()
line2, = ax2.plot(time, d1, 'g--', label="d1")
ax2.set_ylabel("d1")

lines = [line1, line2]
labels = [line.get_label() for line in lines]
ax1.legend(lines, labels, loc='upper left')

plt.grid(False)
plt.show()
```
</NoteCell>

<!--练习题： 为什么从图中看出，一阶导出预测股价的高峰与低谷了，但年化收益还不高？答案：这说明不是所有的个股都是这种震荡向上趋势。第二，趋势的延续性并不好-->

<NoteCell class='abs mt-10 w-95%'
          :enter='{ scale: 0}'
          :click-2='{ scale: 1}'
          :click-3='{ scale: 0}' >

```python
def calc_first_derivative(df, win:int):
    df["log"] = np.log(df.close)
    df["diff"] = df["log"].diff()
    return df["diff"].rolling(win).mean() * -1
    
start = datetime.date(2018,1,1)
end = datetime.date(2023,12,31)
np.random.seed(78)
_ = alphatest(2000, start, end, calc_factor = lambda x: calc_first_derivative(x, 10))
```
</NoteCell>


<NoteCell class='abs mt-10 w-95%'
          :enter='{ scale: 0}'
          :click-3='{ scale: 1}'
          :click-4='{ scale: 0}'
          >

```python {all}
import numpy as np
import matplotlib.pyplot as plt

num_points = 1000 
time = np.linspace(0, 10, num_points)
amplitude_factor = 1 + time  
frequency = 0.3 
phase = 0 
angle_degrees = 15
angle_radians = np.radians(angle_degrees)

y = amplitude_factor * np.sin(2 * np.pi * frequency * time + phase)

linear_trend = np.arange(1000) * 0.05
y += linear_trend

d1 = np.diff(y)
d1 = np.insert(d1, 0, [np.nan])
d2 = np.diff(d1)
d2 = np.insert(d2, 0, [np.nan])

fig, ax1 = plt.subplots(figsize=(10,6))
# line1, = ax1.plot(time, y, label='close')
line2, = ax1.plot(time, d1, 'g--', label="d1")

ax1.set_xlabel('Time')
ax1.set_ylabel('d1')

ax2 = ax1.twinx()
line3, = ax2.plot(time, d2, 'r--', label="d2")
ax2.set_ylabel("d2")

lines = [line2, line3]
labels = [line.get_label() for line in lines]
ax1.legend(lines, labels, loc='upper left')

plt.grid(False)
plt.show()
```
</NoteCell>
<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-4='{ scale: 1}'>

```python
def calc_second_derivative(df, win:int):
    df["log"] = np.log(df.close)
    df["d1"] = df["log"].diff()
    df["d2"] = df["d1"].diff()
    return df["d2"].rolling(win).mean() * -1

np.random.seed(78)
_ = alphatest(2000, start, end, calc_factor = calc_second_derivative, args=(10,), top=9) 
```
</NoteCell>

<!--

<run></run>

斜率因子是我们之前讲因子分析原理时，介绍过的因子。这里出于完备性考虑，略为提一下。

我们看到，在取30天窗口计算斜率，去掉头部了30%的情况下，即使是在纯多情况下，它的年化alpha都是很高的。

这个因子我们取了斜率的相反数。也就是下跌较多的个股，博一天反弹是比较有机会的。

斜率因子的缺点是，它可能不稳定。因为斜率是通过最小二乘回归拟合出来的。这个拟合的过程中，可能误差比较大。也就是斜率的数值不一定正确。

基于一个可能不正确的因子，计算出来结果我们一定是要小心的。


[click]

<run></run>

所以，我看看能否把这个概念泛化一下，以解决这种不稳定性问题。

如果我们把斜率概念泛化一下，其实斜率是一阶导数的平均值。我们先来看看导数的实际意义。

这段代码将生成一个震荡向上的时间序列。它是一条向上的直线，叠加振幅不断扩大的正弦波生成的。如果我们常看k线的话，你会发现慢牛股往往都是这样的走势。

在图中，蓝色是我们模拟的股价序列，绿色则是该股价的导数。很显然，通过求导的过程，我们去掉了常数，或者说直流分量，只保留了周期震荡的部分。

我们发现，绿色的线（也就是导数线）与蓝色的线在波峰、波谷上是一一对应的，并且导数线领先于股价。既然存在相关性，显然我们就可以把它当成一种因子。


[click]

<run></run>

现在我们来检验一下，一阶导能否成为一种因子。

结果表明，它的年化alpha达到了13%。在纯多的条件约束下，收益也达到了7.9%。说明因子是有效的。

这里给大家提个问题，从前一张图来看，一阶导能预测股价的高峰与低谷，这相当于掌握了未来函数了，为什么年化还只有13%呢？这个问题，我们留作思考题，在练习中请大家回答。


[click]

<run></run>

既然一阶导有效，我们会很自然地想到，二阶导是否也可以呢？我们先通过这段代码，检视一下二阶导跟一阶导的关系。

这个图更加清晰地提示了导函数与原函数之间的关系。如果原函数存在周期，那么导函数就可以提前几个相位预测原函数的波峰与波谷。这是非常重要的性质。

另外，我们注意在导数为零的位置，恰好就是原函数最高点或者最低点的位置。

[click]

<run></run>

年化是8.9%，如果我们对它进行一些优化，即去掉第10层，这样可以得到年化19.5%的效果。请大家自己验证一下。

-->
