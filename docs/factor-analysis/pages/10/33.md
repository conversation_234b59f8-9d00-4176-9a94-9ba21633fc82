---
clicks: 4
---

<div class='abs flex justify-center items-center h-full w-full' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

$$
sharpe = \frac{r - rf}{\sigma}
$$
</div>

<NoteCell class='abs mt-10 w-full p-2'
          :enter='{ scale: 0}'
          :click-1='{ scale: 1}'
          :click-2='{ scale: 0}'>

```python
def calc_sharpe(df, n):
    ret = df.close.pct_change()
    df[['ret_mean', 'std']] = ret.rolling(n).agg(['mean', 'std'])
    return df.ret_mean/df["std"]

np.random.seed(78)
_ = alphatest(500, start, end, calc_factor = lambda x: calc_sharpe(x, 10))
```
</NoteCell>

<NoteCell class='abs mt-10 w-full p-2'
          :enter='{ scale: 0}'
          :click-2='{ scale: 1}'
          :click-3='{ scale: 0}'>

```python
def calc_sharpe(df, n):
    ret = df.close.pct_change()
    df[['ret_mean', 'std']] = ret.rolling(n).agg(['mean', 'std'])
    return df.ret_mean/df["std"]

np.random.seed(78)
_ = alphatest(500, start, end, calc_factor = lambda x: calc_sharpe(x, 10),plot_mode="quantiles")
```
</NoteCell>

<NoteCell class='abs mt-10 w-full p-2'
          :enter='{ scale: 0}'
          :click-3='{ scale: 1}'
          :click-4='{ scale: 0}'>

```python
def calc_sharpe(df, n):
    ret = df.close.pct_change()
    df[['ret_mean', 'std']] = ret.rolling(n).agg(['mean', 'std'])
    return df.ret_mean/df["std"]

np.random.seed(78)
_ = alphatest(500, start, end, calc_factor = lambda x: calc_sharpe(x, 10),bottom=2, top=9)
```
</NoteCell>


<NoteCell class='abs mt-10 w-full p-2'
          :enter='{ scale: 0}'
          :click-4='{ scale: 1}'>

```python
def calc_sharpe(close, n):
    # 在滑动窗口上计算夏普，在横截面上排序
    ret = close.pct_change()
    rolling_mean = ret.rolling(n).mean()
    rolling_std = ret.rolling(n).std()

    rolling_sharpe = rolling_mean / rolling_std
    factor = rolling_sharpe.rank(axis=1, method='min', pct=True, ascending=False)
    return (factor.reset_index()
            .melt(id_vars='date', var_name="asset", value_name="factor")
            .set_index(['date', 'asset']))
    
np.random.seed(78)
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12,31)
barss = load_bars(start, end, 2000)
factor = calc_sharpe(barss["close"].unstack(), 10)
prices = barss["close"].unstack()

merged = get_clean_factor_and_forward_returns(factor, prices, quantiles=None, bins=10)
merged[merged.factor_quantile > 6] = np.nan
create_returns_tear_sheet(merged)
```
</NoteCell>


<!--
在我们购买基金的时候，我们会看他的夏普、sortino，maxdrawdown, calmar等等指标，既然这些指标本身就是评估策略指标，那么是否我们可以把它直接当成因子？

这节我们就来讨论这个问题，看看如果把策略评估指标当成因子有哪些注意事项？

我们选择的指标是夏普，即平均收益率除以波动率。


[click]

<run></run>

这是计算夏普因子的代码。它先是求出每日收益，再在此基础上，通过滑动窗口，在滑动窗口内计算平均收益和波动，再用平均收益除以波动，就得到了夏普。

理论上夏普越高越好，所以，这里我们也不需要对因子进行调整。

现在我们来看一下结果。年化收益率达到了28.7%。真有这么好吗？大家怎么看？

这个结果是有问题的。在任何以波动率为分母的因子中，我们都不能直接使用该因子，因为在较短的时间窗口内，波动率可能会非常小，导致因子值非常大。这会影响到年化alpha。


[click]

我们把生成的因子数据拿出来分析一下。大家注意看，因子值最小的有-54，最大的到了4000多。Alphalens是不对因子值做去极值处理的。这一点我们在前面讲它的原理时指出过，这项工作需要我们自己来做。

我们可以通过在alphatest中，指定bottom和top分层的方法来简单去极值。这在探索过程中是适用的。但是严格地讲，我们还是应该使用中位数去极值法。

这两者有何区别？

通过bottom和top分层去极值，我们可能是泼水把孩子也泼出去了。

[click]

好，现在我们来看看通过bottom和top分层去极值之后，这个因子的收益如何。


看来通过bottom和top分层去极值，可能把好的部分也去掉了。

[click]

对这种因子，正确的处理方法是对因子先进行横截面上的排序。这样就避免了极值的问题。

现在我们看到，10日期的夏普因子的年化alpha是6%左右。没有期待中的那么好。

-->
