---
clicks: 7
layout: two-cols
---

<NoteCell class="abs" init>

```python
np.random.seed(78)

code = "000001.XSHE"
start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

PAYH = load_bars(start, end, (code, )).xs(code, level=1)
```
</NoteCell>

<div class='abs mt-10' v-motion
          :enter='{ scale: 1, width: "950px"}'
          :click-1='{ scale: 0, width: "500px"}'
          :click-2='{width: "450px"}'
          :click-3='{ scale: 0}'
          :click-6='{scale: 1}'>

```python
def calc_wave_energy(df, m, n):
    dc = df.close.rolling(m).apply(lambda x: np.fft.fft(x)[0])
    return dc * -1

_ = alphatest(2000, 
              start, 
              end, 
              calc_factor=calc_wave_energy, 
              args=(30,10))
```
</div>

<NoteCell class='abs w-full' v-motion
     :enter='{opacity: 0, width: "950px"}'
     :click-1='{ opacity: 1}'
     :click-6="{scale:0}">

```python {all|2|3|5-8|all}
close = PAYH.copy().close
fft_result = np.fft.fft(close)
freqs = np.fft.fftfreq(len(close))

# 逆傅里叶变换
filtered = fft_result.copy()
filtered[20:] = 0
inverse_fft = np.fft.ifft(filtered)
inversed = pd.Series(inverse_fft.real, index=close.index)

# 绘制原始信号和分解后的信号
plt.figure(figsize=(14, 7))
plt.plot(close, label='Original Close')
plt.plot(inversed, label='Reconstructed from Sine Waves')
plt.legend()
```
</NoteCell>

::right::

<div class='abs mt--3 flex justify-center w-full' v-motion
     :enter='{opacity: 0, scale: 1}'
     :click-6-7='{ opacity: 1, scale: 0.7}'>


<table border="1" class="z-table">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>1D</th>
      <th>5D</th>
      <th>10D</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Ann. alpha</td>
      <td>0.095</td>
      <td>0.092</td>
      <td>0.090</td>
    </tr>
    <tr>
      <td>beta</td>
      <td>-0.005</td>
      <td>0.041</td>
      <td>0.067</td>
    </tr>
    <tr>
      <td>Mean Period Wise Return Top Quantile (bps)</td>
      <td>3.954</td>
      <td>4.023</td>
      <td>4.054</td>
    </tr>
    <tr>
      <td>Mean Period Wise Return Bottom Quantile (bps)</td>
      <td>-4.837</td>
      <td>-4.858</td>
      <td>-4.818</td>
    </tr>
    <tr>
      <td>Mean Period Wise Spread (bps)</td>
      <td>8.790</td>
      <td>9.018</td>
      <td>9.062</td>
    </tr>
  </tbody>
</table>

</div>

<div class='hide abs mt-3 flex justify-center w-full' v-motion
     :enter='{opacity: 0, x:-20}'
     :click-7='{ opacity: 1}'>

![](https://images.jieyu.ai/images/2024/10/fft-mean-period-wise-return-by-quantile.png)
</div>

<FlashText v-click="[5,6]"
           class='abs mt-1/3 text-center w-full text-3xl'>
快速傅里叶变换与股价预测研究
</FlashText>


<!--
从直觉上看，使用波谱分析的方法来构建因子非常自然。因为经济是有周期的，交易更是有周期的。不过在量化交易中运用波谱分析，有它的难度。

以人声的波谱分析来说，它的频率有着固定的范围，能量也有固定的范围，换句话说，它们是平稳序列。但证券价格不是。我们多次讲过这个观点，股票价格是震荡向上的随机序列，只要国家经济还在发展。

这里我提出一个因子，主要是引导大家入门。

这个因子的原理是把股价当成一种波动，对它按30天为滑动窗口，进行波谱分析，提取直流分量（即频率为0的分量）作为因子。


这里进行波谱分析的关键是快速傅里叶变换，它的实现函数是np.fft.fft

我们先简单地介绍下fft转换。


[click]

这一行代码是将时间序列变换成频谱，也就是所谓的时频变换。变换后的结果是一个复数数组，其中实部是频谱，虚部是频谱的偏移。

该数组是按频率由小大到排列的，也就是数组的开始部分是低频信号，结尾部分是高频信号。元素的取值是该信号的能量。一般我们把高频信号当成时噪声。 在这个数组当中零号元素有特殊的含义，它的频率是零赫兹，也就是他是一种直流分量。



[click]

这一行是生成频率的代码。注意它只与时间序列本身的长度有关系。也就是一个序列如果长度为30个时间单位，那么我们认为它的最高频率是30次。至于该频率实际上有没有信号，要看前一个数组对应位置的数值，如果是非零，就认为该频率的波存在。


[click]

这一部分我们对转换后的频率信号进行简单处理。我们将20号以后的数组元素置为零。这样就实现了滤波。

然后我们通过ifft将处理后的信号逆变换回来，再重建时间序列。

[click]

现在，我们运行一下，对比一下滤波前后的图像。

<run></run>

我们看到图像更平滑了。所以这也是一种均线平滑的方法。好，关于FFT我们就介绍到这里。

现在我们思考一个问题，将价格序列进行时频变换后，得到的直流分量，意味着什么？

这里有一个猜想，如果我们把一次振动看成一次交易 -- 买入时导致股价上升，卖出时导致股价下跌回到起点 -- 这就是一种振动，对吧？

那么，高频振动就对应着高频交易，低频振动就对应着低频交易。如果在该窗口期没有做任何交易的资金，它们就是长线资金，是信号中的直流分量。直流分量的能量越大，高频振动的能量越小，股价就越稳定。

现在，我们再进一步思考，如果在t0期直流分量的能量为e0，在t1期的能量变为e1，那么，两者的差值意味着什么？这就意味着有新的长线资金进来了。那么，股价就应该看涨。这是在时序方向上的。


[click]

不过，在这里我们先不看时序方向上的变化，在我们公众号上有一期文章对此进行了研究，大家可以找来看看。这就是我们通过频谱分析来构建因子的基本想法。

fft变换比较费时间，这里我们就不运行了，大家自己在我们服务器上运行吧。我们直接看结果。

[click]

这是年化Alpha，很意外我们就得到了9.5%的年化。


[click]

我们再来看分层收益均值图。我们从未得到过如此完美的图形。它简直就像是合成出来的。


-->
