---
clicks: 3
---

<div class='abs mt-20' v-motion
     :enter='{opacity: 1, x: 400}'
     :click-1='{ x: 200}'
     :click-3="{opacity: 0}">

<table border="1" class="z-table scale-90">
  <thead>
    <tr style="text-align: right;">
      <th>date</th>
      <th>asset</th>
      <th>close</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>1</td>
      <td>a</td>
      <td>10</td>
    </tr>
    <tr>
      <td>2</td>
      <td>a</td>
      <td>20</td>
    </tr>
    <tr>
      <td>3</td>
      <td>a</td>
      <td>30</td>
    </tr>
    <tr>
      <td>4</td>
      <td>a</td>
      <td>40</td>
    </tr>
    <tr>
      <td>1</td>
      <td>b</td>
      <td>100</td>
    </tr>
    <tr>
      <td>2</td>
      <td>b</td>
      <td>200</td>
    </tr>
    <tr>
      <td>3</td>
      <td>b</td>
      <td>300</td>
    </tr>
  </tbody>
</table>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, x:0}'
     :click-2-3='{ opacity: 1, x: 600}'>
<table border="1" class="z-table scale-70">
  <thead>
    <tr style="text-align: right;">
      <th>date</th>
      <th>asset</th>
      <th>close</th>
      <th>id</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>1</td>
      <td>a</td>
      <td>10</td>
      <td>(a, 2)</td>
    </tr>
    <tr>
      <td>2</td>
      <td>a</td>
      <td>20</td>
      <td>(a, 2)</td>
    </tr>
    <tr>
      <td>2</td>
      <td>a</td>
      <td>20</td>
      <td>(a, 3)</td>
    </tr>
    <tr>
      <td>3</td>
      <td>a</td>
      <td>30</td>
      <td>(a, 3)</td>
    </tr>
    <tr>
      <td>3</td>
      <td>a</td>
      <td>30</td>
      <td>(a, 4)</td>
    </tr>
    <tr>
      <td>4</td>
      <td>a</td>
      <td>40</td>
      <td>(a, 4)</td>
    </tr>
    <tr>
      <td>1</td>
      <td>b</td>
      <td>100</td>
      <td>(b, 2)</td>
    </tr>
    <tr>
      <td>2</td>
      <td>b</td>
      <td>200</td>
      <td>(b, 2)</td>
    </tr>
    <tr>
      <td>2</td>
      <td>b</td>
      <td>200</td>
      <td>(b, 3)</td>
    </tr>
    <tr>
      <td>3</td>
      <td>b</td>
      <td>300</td>
      <td>(b, 3)</td>
    </tr>
  </tbody>
</table>
</div>

<FlashText v-click="[2,3]"
           class='abs mt-1/4 text-center w-full text-3xl'>
roll_time_series
</FlashText>

<v-drag-arrow v-click="[2,3]" class="abs" pos="379,289,241,-3" color="red" />

<div class='abs flex justify-center w-full mt-30' v-motion
     :enter='{opacity: 0}'
     :click-3='{ opacity: 1}'>

```python
from tsfresh.utilities.dataframe_functions import roll_time_series

rolled = roll_time_series(df, 
                          column_id="asset",
                          column_sort="date",
                          min_timeshift=1,
                          max_timeshift=1)
```
</div>

<!--
extract_features是基于我们输入的数据的全体进行统计和运算的。但在因子分析中，我们要得不仅仅是某一个时间点的分析结果，我们要的是一段时间内的分析结果，也就是需要基于滑动窗口提取特征并进行连续的分析。

tsfresh为此提供了一个名为roll_time_series的工具，帮我们把数据展开成滑动窗口的形式。

[click]

这里有a和b两种资产。a有4个时间点的数据，b有3个时间点的数据。我们打算做时间窗口为2的滑动。这将为资产a产生三组新的数据，为资产b产生两组新的数据。


[click]

右侧显示了最终的结果。现在，我们来看看tsfresh如何实现这个功能。


[click]

前三个参数跟extract_features是一样的。我们要注意看第4和第5个参数。它实际上是在告诉tsfresh，在最初的冷启动阶段要如何生成滑动窗口数据。

比如，在前面的例子中，我们要生成的滑动窗口长度是2, 这样在t0时间点上，进入滑动窗口的数据只有一个。此时要不要把这条记录保留下来？

min_timeshift决定了要不要保留。这里有一点奇怪的地方，如果滑动窗口是n，希望所有窗口都包含n条数据，那么min_timeshift需要取值为 n-1。这也是参数名叫timeshift的原因。

由于我们是要取固定大小的时间窗口，所以, max_timeshift与min_timeshift就取一样的数值。

-->
