---
clicks: 2
---

<div class='abs w-40% mt-20' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/10/guangfa-paper.jpg)
</div>

<div class='abs w-50% left-50% mt-30 scale-120' v-motion
     :enter='{opacity: 1}'
     :click-1='{ opacity: 0}'>

![](https://images.jieyu.ai/images/2024/10/hugo-2046.jpg)
</div>

<div class='abs flex justify-center w-full items-center h-full' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{ opacity: 1}'>

$$
CGO_t = \frac{P_{close,t-1} - RP_t}{RP_t}
$$
</div>

<div class='abs flex w-full h-full justify-center items-center' v-motion
     :enter='{opacity: 0}'
     :click-2='{ opacity: 1}'>

<FlashText>Year of 2023</FlashText>
<div>
<table border="1" class="scale-80">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>1D</th>
      <th>5D</th>
      <th>10D</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>Ann. alpha</th>
      <td>0.090</td>
      <td>0.101</td>
      <td>0.047</td>
    </tr>
    <tr>
      <th>beta</th>
      <td>-0.024</td>
      <td>-0.037</td>
      <td>0.278</td>
    </tr>
    <tr>
      <th>Mean Period Wise Return Top Quantile (bps)</th>
      <td>-0.038</td>
      <td>0.827</td>
      <td>0.734</td>
    </tr>
    <tr>
      <th>Mean Period Wise Return Bottom Quantile (bps)</th>
      <td>-5.755</td>
      <td>-4.966</td>
      <td>-10.276</td>
    </tr>
    <tr>
      <th>Mean Period Wise Spread (bps)</th>
      <td>5.717</td>
      <td>5.778</td>
      <td>-0.807</td>
    </tr>
  </tbody>
</table>
</div>
</div>

<!--
第一个例子是处置效应。主要参考了广发证券的这篇研报，以及Hugo在github上的这个repo。

处置效应由Grinblatt提出于2005年，是行为金融学的一个应用。


[click]

它可以用这个公式来描述。其中RP是参考价格。实际上，我们在讲技术分析因子那一章，已经看到过很多类似的公式，比如将这里的rp换成均线，就成为了BIAS。

核心就是如何确实参考价格。我们前面见过了使用high, low和close的方法计算典型价格的，也见过了使用vwap来计算人均成本的。


[click]

这里的参考价格又是什么呢？它实际上是更精确地vwap。具体的地计算比较复杂。在教材里有全部代码，大家自己运行一下。

这里我们直接给结果。这是用的2023年的数据进行的回测，年化alpha是9%。这个结果还可以，因为2023年全年是下跌状态。

根据我们引用的参考文献，他们回测出来的收益，从2010年到2020年10年间，年化是26.75%，累计收益是902%。这个结果并不奇怪。

-->
