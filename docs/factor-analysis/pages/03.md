---
aspectRatio: 16/9
title: 第3课 单因子检验的原理及实现
seq: 因子分析与机器学习策略
layout: cover
theme: ./
sync: true
---

<!--
大家好！

上一讲我们介绍了因子预处理的原理和方法。这一讲，我们将介绍后面的工作，也就是如何进行因子检验。

在这一讲中，我们还会再回顾因子预处理的某些环境，因为，这一次处理问题的规模已经变了。

好，具体有哪些不同，我们先卖个关子。
-->

---
src: 03/10.md
title: 回归法
---

---
src: 03/20.md
title: IC法和分层回溯法
---

---
src: 03/30.md
title: 构建动量因子
---

---
src: 03/40.md
title: 行业分类及哑变量
---

---
src: 03/50.md
title: 市值数据
---

---
src: 03/60.md
title: get_clean_factor
---

---
src: 03/70.md
title: 计算远期收益
---

---
src: 03/75.md
title: 回归分析
---

---
src: 03/80.md
title: IC分析
---

---
src: 03/82.md
title: 分层回溯
---

---
src: 03/83.md
title: 总结
---
