---
clicks: 6
---

<style scoped>

div.text h2, h3{
    text-align: center;
    margin: 0 0 2rem 0;
}

div.text p{
    font-size: 0.8rem; 
    text-align: center;
    padding: 0 2rem;
}
</style>


<div class="abs left-0% w-40% grid place-items-center h-full"
    v-motion :enter="{opacity: 1}" :click-2="{opacity: 0}"> 

<div class="text">

## QUANTOPIAN

### Quant + Utopian

<p>我们的使命是打破量化圈的封闭，使得它能对所有人开放</p>
<p>众包阿尔法是一个登月计划，但 quantopian 成功地在宇宙中留下了痕迹，并让量化金融界的很大一部分了解我们并使用我们的工具</p>

</div>
</div>

<div class="abs left-0% w-40% grid place-items-center h-full"
    v-motion :enter="{opacity: 0}" :click-2-4="{opacity: 1}"> 

<div class="text">

## ALPHALENS

### 3.3K stars!

<p>开源因子检验框架</p>
<p>回归、IC、分层和事件分析</p>
<p>可靠、正确、标准</p>

</div>

</div> <!--left-->

<div class="abs left-50% w-40% grid place-items-center h-full" 
    v-motion :enter="{opacity: 1}" :click-1="{opacity: 0}">


![](https://images.jieyu.ai/images/2024/06/quantopian.png)

</div>

<div class="abs left-50% w-40% grid place-items-center h-full" 
    v-motion :enter="{opacity: 0}" :click-1-2="{opacity: 1}">


![](https://images.jieyu.ai/images/2024/06/john-fawcett-2.png)

</div>

<div class="abs left-50% w-40% grid place-items-center h-full" 
    v-motion :enter="{opacity: 0}" :click-2-3="{opacity: 1}">


![](https://images.jieyu.ai/images/2024/01/alphalens.jpg)

</div>


<div class="abs left-40% w-60% grid place-items-center h-full" 
    v-motion 
    :enter="{scale: 0,x:0, width: 540}" 
    :click-3-4="{scale: 1}"
    :click-4-7="{x:-350, width: 900, scale:1}">


![](https://images.jieyu.ai/images/2024/06/alphalens-framework.png)

</div>

<div class="abs top-10% left-40% color-red" v-motion
    :enter="{opacity: 0}" 
    :click-4-7="{opacity: 1}">

<div class="text-4xl"> 1. 生成因子</div>
</div>

<div class="abs top-15% left-65% color-red" v-motion
    :enter="{opacity: 0}" 
    :click-5-7="{opacity: 1}">

<v-drag v-motion class="abs left-10% top-10% w-200px h-80px" pos="61,38,264,47">
<Box class="abs w-200px h-80px"/>
</v-drag>

<div class="text-4xl"> 2. 预处理+远期收益</div>
</div>

<div class="abs top-60% left-70% color-red" v-motion
    :enter="{opacity: 0}" 
    :click-6-7="{opacity: 1}">

<div class="text-4xl"> 3. 分析报表</div>
<v-drag v-motion class="abs left-10% top-10% w-200px h-80px" pos="0,-50,182,50">
<Box class="abs w-200px h-80px"/>
</v-drag>
</div>

<!--
Alphalens是由Quantopian开发的用于分析因子的框架。Quantopian是一个量化众包平台，国内聚宽等平台都是 Quantopian 的 分叉。

[click]

Quantopian的创始人是Fawcett，毕业于哈佛，专业是四大天坑之一的材料学。他的第一份工作是软件开发，做的是视频编码项目。这是一个比较徧算法的项目，也为他转入量化打下了基础 ，所以后来他顺利地在一家对冲基金找到了一份分析师的工作。

在对冲基金工作期间，他接触到了许多物理学、计算化学和信号处理领域的博士，发现他们都有这样的想法，就是通过海量数据，建立模型来挖掘未发现的投资机会。

但即使这些人有很强的编程和算法能力，也对量化交易非常感兴趣，但由于之前没有投资经验，他们不得不被挡在传统的金融机构门外。于是，他萌生了创建一家量化众包平台的想法，这就是Quantopian的由来。

Quantopian从2011年起开始运营，2020年11月终止运营。失败的原因众说纷纭，有人认为，quantopian的名字也要担一部分责任。quantopian一词来源于 Quant + 乌托邦，所以起名字也是一门玄学。

所以，quantopian的结局多少有一点悲剧色彩。Fawce 承诺要为所有人提供一个实现他们梦想的机会，但 Quantopian 自己最终却活在了梦想的光环之外。

[click]

Alphalens从2017年起开始开发，在github上有3.3k的stars。它最后的版本是0.4，2020年4月30日发布。这时pandas的1.0才刚刚发布不久。因此，Alphalens依赖的pandas还是比较旧的版本，大约是0.22。所以，在使用Alphalens时，大家常常会遇到一个问题，就是由于pandas后来升级引起的，我们在第5讲再详细说明。

[click]

Alphalens把我们在前面两讲中，进行因子检验的过程，进行了封装，因此在使用上，就变得非常简单。总共是三步：


[click]

第一步，生成因子

[click]

第二步，调用API进行因子预处理、计算远期收益

[click]

第三步，分析报表

我们先从第一步看起
-->
