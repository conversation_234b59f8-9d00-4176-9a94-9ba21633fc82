---
clicks: 1
---

<style scoped>
.map {
    background-image:url('https://images.jieyu.ai/images/2024/06/alphalens-framework.png');
}

.whole {
    background-size: contain;
    background-repeat: no-repeat;
}

.local-1 {
    border-radius: 50%;
    background-position: 88% 60%;
    border: 1px solid green;
}
</style>

<div class="map whole abs bottom-0 left-0 w-40% h-40% flex flex-col items-center justify-center">
<div class="w-200px h-200px map local-1"/>
</div>

<div class="abs top-10% left-45% w-60%">

## create_full_tear_sheet

```python
merged_factor = get_clean_factor_and_forward_returns(factor, prices)

create_full_tear_sheet(merged_factor)
```

</div>

<div class="abs top-10% left-0">

```python {all|2|3|4|5}{lines:true,at:1}
create_full_tear_sheet(
                    factor_data,
                    long_short=True,
                    group_neutral=False,
                    by_group=False,
                )
```
</div>

<!--

在我们得到merged_factor之后，进行因子分析就非常简单，就是调用create_full_tear_sheet这个函数。

这是函数的签名，但在这一讲，我们只了解第一个参数，即factor_data就好，其它参数，我们会在分析报表时，再进行介绍。
-->
