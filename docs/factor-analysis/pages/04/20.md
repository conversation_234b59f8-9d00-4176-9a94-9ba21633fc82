---
clicks: 9
---

<div v-motion
    :enter="{opacity: 1}"
    :click-3="{opacity: 0}">

<div class="abs w-40% mt-25 left-0">

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/09/andreas-clenow.png'>
<span style='font-size:0.6rem'>Author & Chief Investment Officer</span>
</div>
</div><!--left-->

<div class="abs w-50% left-50% mt-25" v-motion
    :enter="{opacity: 1}"
    :click-1="{opacity: 0}">


<div style='height:300px;width:200px;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/09/stocks-on-the-move.jpg'>
<span style='font-size:0.6rem'>Stocks on the Move</span>
</div>

</div>

<div class="abs w-50% left-50% mt-25" v-motion
    :enter="{opacity: 0}"
    :click-1-2="{opacity: 1}">

<div style='width:250px;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/09/trading-evolved.jpg'>
<span style='font-size:0.6rem'>Trading Evolved</span>
</div>

</div>

<div class="abs w-50% left-50% mt-25" v-motion
    :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}">

<div style='height:300px;width:200px;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/09/a-most-private-bank.jpg'>
<span style='font-size:0.6rem'>A Most Private Bank</span>
</div>

</div>

</div><!-- 克莱劳 -->

<div class="abs w-50% left-0 top-15% h-full">

<v-clicks at="2" depth="3" every="2">

## 斜率计算
## 滑动窗口斜率计算
### 单资产

<div/>
<div/>
<div/>
<div/>

### 单资产 > loop

</v-clicks>

</div> <!-- end left -->

<!-- 3 -->

<NoteCell class="abs w-65% left-35% top-10% h-full"
    v-motion
    :enter="{scale: 0}"
    :click-3-9="{scale: 1}">

```python {all|4|7-11|13-19|all|18-19}{lines:true,at:4}
import matplotlib.pyplot as plt

def calc_slope(close):
    slope, _ = np.polyfit(np.arange(len(close)), close, deg=1)
    return slope

start = datetime.date(2023, 12, 1)
end = datetime.date(2023, 12, 29)
barss = load_bars(start, end, universe=("000001.XSHE", ))
bars = barss.xs("000001.XSHE", level="asset")
close = bars["close"][-10:]

intercept = close[0]
slope = calc_slope(close)

y_hat = intercept + slope * np.arange(len(close))

plt.plot(close, label="close")
plt.plot(close.index, y_hat, label="y_hat")
```

</NoteCell>

<!--
替换为以下代码，修复回归线不为直线的问题

x = [str(t) for t in close.index]

plt.plot(x, close, label="close")
plt.plot(x, y_hat, label="y_hat")
plt.xticks(rotation=45)
plt.show

-->

<!-- countdown -->

<CountdownTimer :count=20 class="abs left-50% w-200px h-100px" :scale=0.5 q="问题：为什么不是直线"
    v-motion
    :enter="{scale: 0}"
    :click-7-8="{scale: 1}"/>

<!-- 循环计算 -->

<NoteCell class="abs w-65% left-35% top-10% h-full"
    v-motion
    :enter="{scale: 0}"
    :click-9-10="{scale: 1}">

```python {18-21}{lines:true}
import time
def calc_slope(close):
    slope, _ = np.polyfit(np.arange(len(close)), close, deg=1)
    return slope

start = datetime.date(2023, 12, 1)
end = datetime.date(2023, 12, 29)
barss = load_bars(start, end, universe=("000001.XSHE", ))
bars = barss.xs("000001.XSHE", level="asset")
close = bars["close"][-10:]

intercept = close[0]
slope = calc_slope(close)

slopes = []
t0 = time.time()

for i in range(0, 10):
    close = bars["close"][-20+i:-10+i]
    slope = calc_slope(close)
    slopes.append(slope)
print(f"time cost: {(time.time()-t0)* 1000:.1f}ms")
print("slopes:", np.around(slopes, 4))
```
</NoteCell>

<!--

这一步实际上不属于Alphalens，但我们生成的因子，要符合Alphalens格式要求。

这一节我们介绍一个新的因子，斜率因子。

斜率因子是动量因子的一种。它是由Andreas Clenow首先提出的，发表在他的Stocks on the Move一书中。

这本书是畅销书之一，在亚马逊股票类图书中排300名。在这本书中，Andreas Clenow介绍了一个基于规则的动量系统，例子是基于excel的。

[click]

这本书出版于2019年。与stock on the move不同的是，这本书中的例子改用python来实现。是一本500页的书，涵盖了从端到端构建回测的整个过程。

[click]

这是一本小说。以他的经验，讲述了在现实中，瑞士金融中的那些黑暗、粗犷和愤世嫉俗的一面。

正是因为这些畅销书的加持，clenow的斜率因子是比较有影响力的，在trading view， Quantopian的文档示例中，都有这个因子的实现。因此我们也要了解和研究这些有影响力的因子。

斜率因子有非常直观的视觉解释，就是n日收盘价的回归线。这条回归线越陡，资产上涨速度越快、动能越足。而且由于它显而易见的视觉效果，对 retail trader（散户）也有较好的吸引力，是人气聚集的地方。

[click]

现在我们就来看如何实现这一因子。

这段代码主要实现三个功能，计算斜率、取数据和绘制回归线图

[click]

这里我们使用了numpy的polyfit函数，这个函数可以计算一个直线的斜率和截距。

在这里介绍它，是为了后面计算滑动窗口下的斜率因子，以使得性能最大化。

[click]

这一段不用解释了，它的作用是取数据

[click]

我们来看看slope的实际意义。我们把拟合曲线绘制出来，叠加到收盘价上。

我们把这段代码运行一下。

这里出现一个有意思的现象。我们拟合出来的是一条直线，但为什么在图中却变成了一条折线？这是哪里的算法出错了？

[click]

给大家20秒思考。我们再回到代码上来。看看这段代码，究竟什么地方出错了？

[click]

问题出在x轴上。第18行，我们给plot函数，传入的第一个参数，即x轴数据，它的格式是日期时间。

matplotlib在绘图时，会按自然日历的tick数来等分x轴，但在量化交易中，显然我们要使用交易日历。从自然日历的角度来看，这个交易日历是不连续的。

那怎么修复这个问题呢？

只要我们把日期时间转换成为字符串，再以此数组为x轴，matplotlib就会把字符串数组当成category数据，这样每个tick之间就是等长的了

[click]

calc_slope函数只求出了一个资产在某个时间点上的斜率。如果我们要求该资产过去一段时间的移动斜率，应该如何操作呢？我们当然可以通过这样的循环来进行计算。

我们运行一下，看看这段代码要花多久。


花了5ms，看上去并不慢。但我们只算了10个时间点的数据。如果我们要计算250个时间点，就要用上1.25秒。

-->

