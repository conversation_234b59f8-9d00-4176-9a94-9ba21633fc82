---
clicks: 18
preload: false
---

<div class="abs mt-10">

## 斜率计算
## 滑动窗口斜率计算
### 单资产
### 单资产 > loop
### 单资产 > 向量式

<v-switch>

<template #3-8> 

#### as_strided 

</template>

<template #4-8> 

#### Array.strides
</template>

<template #6>

<div class="abs color-red left-800px top-10% w-100px z-10">
8<br>
(8, )
</div>
</template>

<template #7-8>

<div class="abs color-red left-800px top-35% w-100px z-10">
(8,8)
</div>
</template>

<template #8>

<div class="abs color-red left-800px top-85% w-100px z-10">
(8,8)
</div>
</template>

<template #10-11>

<CountdownTimer autoplay :count=15 class="abs top-10% left-350px z-1"/>
</template>

<template #12> 

### 多资产 > groupby.apply

<div class="abs top-15% left-400px w-500px h-full">

![](https://images.jieyu.ai/images/2024/09/load_barss.png)
</div>
</template>

<template #13-19>

### 多资产 > groupby.apply
</template>
</v-switch> <!--left-->

</div>

<NoteCell class="abs w-65% left-35% top-10% h-full"
    v-motion
    :enter="{scale:1}"
    :click-3="{scale:0}">

```python {all|3-20}{lines:true,at:1}
from numpy.lib.stride_tricks import as_strided
import time
def rolling_slope(close: NDArray, win:int, *args):
    if len(close) < win:
        return np.full((len(close), ), np.nan)

    stride = close.strides
    
    slopes, _ = np.polyfit(np.arange(win), 
                           as_strided(close, (len(close)-win+1, win), 
                           stride+stride).T,
                           deg=1)
    left_padd_len = len(close) - len(slopes)
    slopes = np.pad(slopes, 
                    (left_padd_len, 0), 
                    mode='constant', 
                    constant_values=np.nan)
    return slopes

# 1. 获取行情数据
start = datetime.date(2023, 12, 1)
end = datetime.date(2023, 12, 29)
barss = load_bars(start, end, universe=("000001.XSHE", ))
bars = barss.xs("000001.XSHE", level="asset")

t0 = time.time()
slopes = rolling_slope(bars["close"].to_numpy(), 10)
print(f"time cost: {(time.time() - t0) * 1000:.2f}ms")
print(slopes)
```

</NoteCell>

<div class="abs flex flex-col items-center justify-between left-40% bg-blue-200"
    v-motion :enter="{scale:0}"
    :click-2-3="{scale:1}">


<Numbers :data="[1,2,3,4,5,6,7]" class="bg-blue mt-10"/>
<v-drag-arrow color="red" pos="75,86,96,80"/>
<Numbers :data="[1,2,3]" class="mt-20 bg-red"/>
<Numbers :data="[2,3,4]" class="bg-red"/>
<Numbers :data="[3,4,5]" class="bg-red"/>
<Numbers :data="[4,5,6]" class="bg-red"/>
<Numbers :data="[5,6,7]" class="mb-10 bg-red"/>

$$

(n, 1) => ((n-w+1), w)
$$
</div>

<NoteCell class="abs w-65% left-35% top-10% h-full"
    v-motion
    :enter="{scale: 0}"
    :click-3-4="{scale:1}"
>

```python {all}{lines:true}
from numpy.lib.stride_tricks import as_strided

# as_strided(a, shape, strides)

arr = np.arange(1, 8)
stride = arr.strides
win = 3
as_strided(arr, (len(arr) - win + 1, win), (stride + stride))
```

</NoteCell>



<NoteCell class="abs w-65% left-35% top-10% h-full"
    v-motion
    :enter="{scale: 0}"
    :click-4-12="{scale:1}"
>

```python {all|1-4|6-8|10-13|15-16|15-16|15-16|all}{lines:true,at:5}
arr = np.arange(1, 8)
stride = arr.strides
print(f"items size of orignin array: {arr[0].itemsize}")
print(f"stride of original array is {stride}")

win = 3
strides = (stride + stride)
print(f"input strides param is {strides}")

transformed = as_strided(arr, (len(arr) - win + 1, win), strides=strides)
print(transformed)

print(f"strides of transformed is: {transformed.strides}")

copy = transformed.copy()
print(copy.strides)
```

</NoteCell>

<NoteCell class="abs w-65% left-35% top-10% h-full"
    v-motion
    :enter="{scale: 0}"
    :click-13-19="{scale:1}"
>

```python {all|2-17|19-23|20|21-22|25-37}{lines:true,at:14}
import time
def rolling_slope(close: NDArray, win:int, *args):
    if len(close) < win:
        return np.full((len(close), ), np.nan)

    stride = close.strides
    
    slopes, _ = np.polyfit(np.arange(win), 
                           as_strided(close, (len(close)-win+1, win), 
                           stride+stride).T,
                           deg=1)
    left_padd_len = len(close) - len(slopes)
    slopes = np.pad(slopes, 
                    (left_padd_len, 0), 
                    mode='constant', 
                    constant_values=np.nan)
    return slopes

def wrapper(group):
    slopes = rolling_slope(group["close"].to_numpy(), 10)
    index = group.index.get_level_values(0)
    df = pd.DataFrame(slopes, index=index, columns=["factor"])
    return df

# 1. 获取行情数据
start = datetime.date(2023, 12, 1)
end = datetime.date(2023, 12, 29)
universe = 200
barss = load_bars(start, end, universe=universe)

t0 = time.time()
factors = barss.groupby(level='asset').apply(wrapper)
print(f"time cost: {(time.time() - t0) * 1000:.2f}ms")
print(f"got {len(factors)} records.")

factors.tail()
```
</NoteCell>

<!--
我们可以利用np.polyfit的向量化能力来进行加速。

polyfit的向量化计算能力，是指它可以接受一个二维数组为输入，以行为单位计算斜率。

**我们先来看这段代码的运行结果**。

耗时是1ms。加速是2倍多。输入的数据行数越多，这个加速就越明显。

[click]

这段代码是我们今天这一讲重点之一，如何运用向量化计算来加速。

做量化交易时，大家都会感觉有性能上的需求。通过向量化来提升性能，就是我们在因子分析、量化交易中可能常常要用的技巧。

在这里，要实现向量化，应该从哪里入手？

假设numpy的polyfit能够按行为单位计算斜率。我们现在有一个长度为n的一维数组，希望在它上面以滑动窗口w为尺寸，计算斜率。

[click]

我们以一个只有7个元素，滑动窗口为3的场景为例。

我们需要将 7*1 的一维数组，转换为5 * 3的二维数组

[click]

这个工作，由as_strided来实现。

这段代码中，shape参数不难理解。从上一节我们知道，它的维度应该如此。

但是这里出现了一个陌生的词，strides和stride。这是什么意思呢？

[click]

arr.strides 属性表示数组元素在内存中的步长，即从一个元素到下一个元素所需的字节数。

我们通过这段代码来快速了解下相关的属性。

[click]

arange生成的对象是8字节的整数。所以，第3行的结果应该是8

第4行，结果是多少？我们说过，数组的strides是元素的itemsize

 [click]

所以，第4行的结果是一个一元组，即(8,)。
第7行，两个元组相加，最后得到的结果是多少？

[click]

结果是一个二元组。

第10行，从一维数组生成二维数组

大家猜一下，新生成的二维数组，它的strides是多少？

[click]

还是（8，8），跟输入参数相同。

如果我们把transform数组复制一份，它的strides也应该是(8,8)，对不对？

[click]

我们暂停15秒。让大家思考一下，想出正确答案的，可以留言。

[click]

我们运行一下结果看看

[click]

transormed的副本，它的strides竟然跟transformed不一样。

这一点是不是不好理解？按理说，transformed这个数组，在列的方向上，到下一个元素的步长是1步，8个字节；在行的方向上，到下一个元素的步长是3步，应该是24个字节才对。

原因是 as_strided 并不会改变原有数组，它只是返回数组的一个视图。而原有数组的strides，就是（8，），尽管shape变了，但strides要保持不变。这一点仍然不太好理解，不过我们可以把它当成as_strided的一种约定来记忆。

而transformed的副本，是一个物理上存在的数组。因此，它的步长就是(24,8)，和我们理解的一致。

好，关于as_strided我们就讲这么多。掌握这个用法之后，我们就可以向量化很多滑动窗口操作。因为无论是numpy还是scipy，它们都有按某个轴进行操作的能力。

[click]

到目前为止，我们进行的计算都是针对单个资产的。

但在因子分析中，我们常常要执行横截面上的操作。也就是说，要对所有资产都计算因子，然后再进行比较和排序。

那么，在多资产的情况下，又应该如何计算斜率呢？

这是load_bars的输出结果的数据格式。它是一个双重索引的dataframe。显然，我们不能直接取close列来计算滑动斜率，这样会导致不同资产会混在一起计算。

这是一个使用groupby的典型例子。如果我们把这个dataframe按asset进行group，就会得到若干个分组，每个分组都只包含某个资产的行情数据

[click]

这段代码将会同时计算多个资产的因子。

[click]
 
这里的rolling_slope与我们之前看到的完全一样。

[click]

但我们新增加了一个wrapper方法，它将接受groupby的输出，计算出斜率因子，再转换成dataframe.

[click]

第19行，我们这里对输入参数进行转换，再调用rolling_slope来计算滑动斜率因子。

[click]

第20行运行的结果是一个numpy数组，我们要将其转换成为dataframe，这样groupby.apply会自动将这些dataframe合并

[click]

现在，我们来看看运行结果。这里我们同时计算了200个资产的因子，耗时在0.2秒左右。如果要计算5000个资产，大约需要5秒钟。当然，我们对这个速度也不算满意。它应该还能被提速100倍左右。
-->
