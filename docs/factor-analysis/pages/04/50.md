---
clicks: 13
---

<style scoped>
.map {
    background-image:url('https://images.jieyu.ai/images/2024/06/alphalens-framework.png');
}

.whole {
    background-size: contain;
    background-repeat: no-repeat;
}

.local-1 {
    border-radius: 50%;
    background-position: 90% -10%;
    border: 1px solid green;
}
</style>

<div class="map whole abs bottom-0 left-0 w-40% h-40% flex flex-col items-center justify-center">
<div class="w-200px h-200px map local-1"/>
</div>

<div class="abs top-10% left-0">

```python {all|1-3|4|10|6,7|6,7|6,7|5,12|5,12|8,9,13|8,9,13|8,9,13|11|all}{lines:true,at:1}
def get_clean_factor_and_forward_returns(
                        factor,
                        prices,
                        groupby=None,
                        binning_by_group=False,
                        quantiles=5,
                        bins=None,
                        periods=(1, 5, 10),
                        filter_zscore=20,
                        groupby_labels=None,
                        max_loss=0.35,
                        zero_aware=False,
                        cumulative_returns=True)
```
</div>

<div class="abs top-10% left-50% w-50%">

<v-switch>
<template #1 >

![75%](https://images.jieyu.ai/images/2023/07/factor_df_format.png)

![75%](https://images.jieyu.ai/images/2024/09/barss-price-pivoted.jpg)
</template>

<template #2-4>

## groupby

<div>

![](https://images.jieyu.ai/images/2024/07/factors-datastructure-with-sector.jpg)
</div>
</template>

<template #3>

## groupby_labels
</template>

<template #4>

## binning by quantile

<NoteCell class="w-full h-full">

```python {all}{lines:true,at:1}
import pandas as pd

data  =[-0.68,0.4,-0.38,-1.09,0.71,
      -0.34,0.05,0.72,2.53,-0.48,0.53,0.89]
df = pd.DataFrame(data, columns=['factor'])

df["quantile"] = pd.qcut(df["factor"], q = 5)
df
```
</NoteCell>

</template>

<template #5-6>

## binning by bins

<NoteCell class="w-full h-full">

```python {all}{lines:true,at:1}
import pandas as pd

data  =[-0.68,0.4,-0.38,-1.09,0.71,
      -0.34,0.05,0.72,2.53,-0.48,0.53,0.89]
df = pd.DataFrame(data, columns=['factor'])

df["quantile"] = pd.cut(df["factor"], bins=3, 
                        labels=[0, 1, 2],
                        include_lowest=True)
df
```
</NoteCell>
</template>

<template #7>

## binning_by_group
</template>

<template #8>

## zero_aware
</template>

<template #9>

## periods
</template>

<template #10>

## filter_zscore

<CountdownTimer autoplay :count=15 class="abs top-10%  z-1" q="为什么要对收益去极值？"/>
</template>

<template #11>

## cumulative_returns
</template>

<template #12>

## max_loss


```txt
Dropped 23.8% entries from factor data: 22.4% in forward returns 
computation and 1.3% in binning phase (set max_loss=0 to see 
potentially suppressed Exceptions).
max_loss is 35.0%, not exceeded: OK!
```
</template>

<template #13>

![](https://images.jieyu.ai/images/2024/07/alphalens-merged-data.jpg)
</template>
<!-- v-switch-->
</v-switch>
</div>


<div class="abs top-50% left-40% w-50%"
    v-motion
    :enter="{scale:0}"
    :click-4-5="{scale:1}">

<Numbers :data="[-0.68,0.4,-0.38,-1.09,0.71,-0.34,0.05,0.72,2.53,-0.48,0.53,0.89]"
       label="factors" 
        class="w-full top-10% scale-50%"/>


<Numbers :data="[-0.68,-0.38,-1.09,-0.48]"
       label="33%分位" 
        class="mt-10 w-full left-45% scale-50%"/>

<Numbers :data="[0.4,-0.34,0.05,0.53]"
       label="66%分位" 
        class="ml-20 w-full left-70% scale-50%"/>

<Numbers :data="[0.71,0.72,2.53,0.89]"
       label="100%分位" 
        class="ml-40 w-full left-60% scale-50%"/>

<v-drag-arrow color="blue" pos="166,48,9,69"/>
<v-drag-arrow color="blue" pos="217,46,-16,68"/>
<v-drag-arrow color="blue" pos="249,47,-20,69"/>
<v-drag-arrow color="blue" pos="410,46,-142,71"/>
</div>

<div class="abs top-60% left-40% w-50%"
    v-motion
    :enter="{scale:0}"
    :click-5-6="{scale:1}">

<Numbers :data="[-0.68,0.4,-0.38,-1.09,0.71,-0.34,0.05,0.72,2.53,-0.48,0.53,0.89]"
       label="factors " 
        class="w-full top-10% scale-50%"/>

<Numbers :data="[-0.68,-0.38,-1.09,-0.34,0.05,-0.48]"
       label="<0.117 "
        class="mt-5 w-full left-45% scale-50%"/>

<Numbers :data="[0.40,0.71,0.72,0.53,0.89]"
       label="<1.323 " 
        class="mt--5 ml-20 w-full left-45% scale-50%"/>
<Numbers :data="[2.53]"
       label="<2.53" 
        class="mt--5 ml-40 w-full left-45% scale-50%"/>

<v-drag-arrow color="red" pos="382,47,-27,139"/>
<v-drag-arrow color="green" pos="192,46,45,99"/>
<v-drag-arrow color="green" pos="272,41,-6,99"/>
<v-drag-arrow color="green" pos="355,41,-63,102"/>
<v-drag-arrow color="green" pos="439,40,-119,103"/>
<v-drag-arrow color="green" pos="464,38,-111,101"/>
</div>

<div class="abs top-5% left-45% w-50% text-center"
    v-motion
    :enter="{scale:0}"
    :click-6-7="{scale:1}">
<v-drag pos="30,184,100,100">
<Ellipse class="abs w-full h-full" >分层</Ellipse>
</v-drag>

<v-drag pos="155,132,100,100">
<Ellipse class="abs w-full h-full" >by quantiles</Ellipse>
</v-drag>

<v-drag pos="161,278,100,100">
<Ellipse class="abs w-full h-full" >by bins</Ellipse>
</v-drag>


<v-drag pos="313,130,100,100">
<Ellipse class="abs w-full h-full" >int</Ellipse>
</v-drag>

<v-drag pos="319,279,100,100">
<Ellipse class="abs w-full h-full" >array</Ellipse>
</v-drag>

<v-drag-arrow pos="114,265,55,46"/>
<v-drag-arrow pos="121,208,37,-25"/>
<v-drag-arrow pos="250,179,72,139" color="red"/>
<v-drag-arrow pos="250,179,68,-3" color="red"/>
<v-drag-arrow pos="258,320,64,-1" color="blue"/>
<v-drag-arrow pos="257,321,59,-144" color="blue"/>
</div>

<!--
现在我们就正式进入Alphalens的世界了。左下角这张图我们已经见过一次了。在通过Alphalens进行因子检验的流水作业中，我们现在处在第二步。

第二步的工作，是get_clean_factor_and_forward_returns这个函数来完成的。

它的主要作用是，将我们整理的因子数据和价格数据，进行预处理，以便进行分析。

这个预处理包括了生成远期收益，将收益与因子对齐，因子分层、处理缺失值等等操作。这个函数的功能比较复杂，因此参数与很多。

[click]

第一、二两个参数是输入数据参数。右图显示了这两个数据应该具有的数据格式，上面的是因子数据，下面的是价格数据。

[click]

第三个参数 groupby，用来进行行业中性化。它要么是一个series，此时它也要和factor一样，由日期和资产双重索引，并且与factor一一对应；要么是一个字典。当使用字典时，就默认资产的行业分类在分析期间没有变化过。

[click]

通过groupby参数指定的sector，要求为整数或者category类型。在生成报表时，我们更希望显示行业名称，而不是行业编号。这个信息要通过groupby_labels参数来提供。

这个参数只有在groupby存在时，才有意义。

[click]

这两个参数指示Alphalens如何进行分层。这两个参数是互斥的，默认是按quantiles，也就是分位数进行分层；如果要按bins进行分层，则需要显式地将quantiles设置为None，再传入bins参数。

我们先看按分位数进行分层。

右图显示了按分位数分层的例子。如果我们传入的quantiles是一个整数，意味着要把目标数组按分位数等分，每个分层将有大致相同的样本量。

右下是最终的分层结果。如果我们仔细观察的话，就会发现，这三组数据按分位数是等分，但它们之间的跨度并不相同。第一组跨度是0.71，第二组跨度是0.87，第二组跨度则是1.82.

如果我们要使得各个组的跨度相同，这种分割法就是按bins进行分层。

[click]

这一组显示了按bins进行分层的例子。与上一例相比，各个分层之间的跨度相同，都是1.2左右，但也正因为这样，每个分层的样本量也并不相同。

在示例中，无论是quantiles参数，还是bins参数，除了可以传入整数外，都可以传入一个数组，这样可以对数据进行任意分割。

[click]

我们总结一下，这两个参数，导致我们有四种不同的分层方式。我们会在后面看到这些方式在分析中都有用途。

[click]

这两个参数，是与分层密切相关的参数。

binning_by_group，如果为True，则分层将在组内进行。这是Alphalens做中性化的一种方式。在每个行业分类内部进行分层，再按分层标签跨行业抽取样本，显然我们就把行业的影响去掉了。

[click]

有一些因子的取值区间会跨越零的分界线。而且取正的值与取负的值之间，它们的信号意义并不连续。比如，像盈利指标，-0.05与正的0.02之间，看上去差别很小，但却反映了公司的财务状况有本质上的不同。

但是，如果我们要Alphalens按分位数进行分层的话，它有可能把上述两个值都分到一个分位组中，从而在操作上对应相同的操作信号。显然这是不合适的。

在这种情况下，zero_aware参数就派上用场了。当我们指定zero_aware为True时，Alphalens就会在零线两端分别分组。比如，如果我们要将数据按分位数4等分的话，此时，alphalens就会把零线之上的两等分，零线之下的也进行两等分，从而保证零线两侧的数据不会被分到同一组内。

[click]

现在在左侧高亮的这一组参数，是与收益计算相关的。

periods参数，用以指示计算多少期的远期收益。在第3讲我们自己的实现中，我们只做了1期的收益计算。通过这个参数，Alphalens允许我们同时计算多期的收益，这样可以估算出因子的持续影响力。

[click]

我们在第3讲中讲过，框架是不会对因子进行标准化的。这些工作，需要我们自己来实现。

那么，这里的filter_zscore的作用是什么呢？

它是用来排除远期收益中的异常值的。它把远期收益看成一个正态分布，如果某个资产的某个远期收益超过均值的20个标准差，那么这一期数据就会被drop掉。

对收益去极值，必要性和合理性在哪里？我们给15秒钟，请大家思考一下。

答案是，在因子分析中，个别暴涨暴跌的资产收益，会对整体收益分析有较大影响。如果我们不把这些因素去掉，那么一定会带偏因子分析的方向。

在这些地方，Alphalens的考虑非常周到，如果我们自己来实现，不一定能避开这些pitfall。

[click]

这个参数用来告诉Alphalens如何计算远期回报。如果因子的周期是日内的，那么这个参数会影响到Alphalens的收益计算。

这是 Alphalens 中一个有点晦涩难懂的地方。我们一般保持默认值即可。如果你对此感兴趣，我们这一节的脚注里，提供了更多信息。

[click]

在get_clean_factor_and_forward_returns计算中，抛弃一些数据是很正常的事。比如，因子数据不一定完全与price数据对齐了，没对齐的部分，即有因子数据，但没有对应的远期收益，此时因子数据记录就必须被丢弃。

如果被丢弃的数据超过一定的限度，那么继续分析就没有任何意义。这就是max_loss的作用。

无论max_loss是否被超出，预处理过程都会给出这样的日志信息。

从这则日志我们可以得到哪些信息？

首先，预处理丢掉的数据在阈值以内，操作成功

其次，在预处理过程中，丢掉了23.8%的记录。其中有1.3%是在分层过程中丢掉的。

如果这个数字过大，我们就要考察数据的分布，选择合适的分层方法。

[click]

最后，我们将得到这样一个dataframe。有时候我们也称之为merged_factor，因为它合并了因子和远期收益。

现在，我们就进入到最终的分析与报表。
-->
