

<div class="abs top-10%">

## 价格数据
</div>

<NoteCell class="abs w-65% left-35% top-10% h-full"
    v-motion
    :enter="{scale:1}"
    :click-3="{scale:0}">

```python {all}{lines:true,at:1}
start = datetime.date(2023, 12, 1)
end = datetime.date(2023, 12, 29)
universe = 200
barss = load_bars(start, end, universe=universe)
barss["price"].unstack(level=1)
```
</NoteCell>

<!--

这部分是我们之前讲过的。在load_bars的输出中，已经包含了价格数据。它的格式是双重索引。


在第5行，我们通过barss["price"]取得的是一个双重索引的Series，对它进行索引展开，就得到了一个以资产代码为列的宽表。

这就是Alphalens需要的格式。
-->

