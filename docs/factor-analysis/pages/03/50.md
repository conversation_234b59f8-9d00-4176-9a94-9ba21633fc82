---
click: 3
---

<div class="abs mt-20" v-motion
    :enter="{x:0, width: '100%'}"
    :click-1="{x: 400, width: '60%'}">

```python {all|19}{lines:true}
def get_valuation(instruments: Tuple[str], days: List[datetime.date]):
    """获取对数化后的各 instrument 对应日期的市值

    Args:
        instruments: 股票代码
        days: 日期列表，与因子生成日期对应
    Return:
        返回以日期、instrument 为索引，对数化市值为单元格值的 DataFrame
    """
    dfs = []
    for day in days:
        valuation = fetch_valuation(day)
        filtered = valuation[valuation.index.isin(instruments)]
        filtered = filtered.to_frame("market_value")
        filtered["date"] = day
        dfs.append(filtered)

    df = pd.concat(dfs)
    df.set_index(["date", df.index], inplace=True)
    return df
```
</div>

<div class="abs w-35% h-full mt-30" v-motion
    :enter="{ opacity: 0}"
    :click-1="{ opacity: 1}"
    >

## set_index

```python

df.set_index(["date", df.index], inplace=True)
df.reset_index().set_index(["date", "asset"])
df.set_index("asset", append=True)
```
</div>

<!--
如何获取市值数据我们在上一讲中也讲过了。不过这个示例有所不同。它要求我们获取一段时间区间里的资产的市值，所以，这里使用了循环和拼接。

[click]

在第19行，我们又一次见到了set_index方法。这一次，我们仍然是传入了一个数组，完全替换了原来的索引。只不过， 在这个数组中，既有列标签，也有df.index这样的数组型的对象。

最后，左边总结了到目前为止，我们已经见过的3种用法
-->
