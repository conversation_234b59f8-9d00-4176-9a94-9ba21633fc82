---
clicks: 4
---

<div class="abs mt-10" v-motion
    :enter="{width: '100%', x:0}"
    :click-2="{width:'60%', x:400}">

```python {all|14-16|14-16|17|all}{lines: true}
def compute_forward_return(factor: pd.DataFrame):
    """计算远期收益

    Args:
        factor: 经过处理后的因子，从中提取日期及instrument信息
    """
    instruments = factor.index.get_level_values("asset")
    days = factor.index.get_level_values("date")

    start = days[0].date()
    end = days[-1].date()

    # 由于缺少基础库支持，这里我们只能不精确地往后延若干天，再截断
    end_ = end + timedelta(days=10)

    barss = load_bars(start, end_, tuple(instruments))
    df = barss.pivot_table(index="date", columns="asset", values="price")
    returns = df.pct_change()
    return returns.loc[start:end]
```
</div>

<div class="abs mt-5 w-40%" v-motion
    :enter="{opacity: 0}"
    :click-2-4="{opacity: 1}">

![](https://images.jieyu.ai/images/2024/09/load_barss.png)

<v-drag-arrow color="red" pos="135,158,0,115"/>
</div>

<div class="abs w-40% mt-62" v-motion
    :enter="{opacity: 0}"
    :click-3-4="{opacity: 1}">

### pct_change(periods=1,5,10,...)

![](https://images.jieyu.ai/images/2024/09/barss-price-pivoted.jpg)
</div>

<div class="abs w-40% mt-30" v-motion
    :enter="{opacity: 0}"
    :click-4-5="{opacity: 1}">

![](https://images.jieyu.ai/images/2024/05/forward-returns.jpg)
</div>

<!--
我们已经计算出了因子。为了进行因子检验，我们还差什么数据?

对，就是收益数据。为简单起见，这里我们只计算了1日的收益数据。

假设因子数据的最后一期为t，那么，我们要计算因子的表现，就还必须有t+1和t+2期的开盘价。这样，我们可以在t+1时，开盘价买入，并在t+2时，以开盘价卖出。

所以，要计算出与因子对应的收益，我们就必须取到t+2期的开盘价。

 [click]
第14-16行，我们取出现在factor中的那些资产对应的行情数据。

在时间上，本来我们只需要取到factor对应的时间的t+2就好，但是，我们无法计算t+2具体是哪一天。为什么？

因为要完成这个计算，就必须有交易日历和相应的工具库。

在量化24课的环境中，我们提供了omicron这个工具，但在这里没有。所以，我们粗略地把结束日期加了10个自然日。

如果遇到春节或者国庆，这段代码有可能不能正常工作，对吧? 所以，要做量化交易，除了策略之外，你还必须有大量的基础库。

[click]

大家还记得load_bars获得的行情数据长什么样儿吗？

 大家思考下，对这样一个数据结构，如何快速地计算出每个资产的涨跌幅？

[click]

为了能利用向量化运算的能力，我们得先把其中的价格字段提取出来，并且转换成为这样的宽表。

显然，当我们完成了这个转换之后，就可以调用pct_change来计算1日、5日或者多日的收益率了，这只需要传入periods参数即可

[click]

最终，函数会返回一张这样的表。它以日期为索引，资产代码为列名，单元格值为因子值。
-->
