<div class="abs w-full mt-10">

```python {all|14|1|18-23|23}{lines:true}
from scipy.stats import spearmanr

def ic_test(factor, returns):
    factor = factor.dropna()
    returns = returns.dropna()
    factor_dates = factor.index.get_level_values("date")
    valid_dates = returns.index.drop(returns.index.difference(factor_dates))
    ic = []

    for date in valid_dates:
        y = returns.loc[date]
        x = factor.xs(date, level="date")

        r = spearmanr(x.values, y.values)[0]
        ic.append(r)

    ic = np.asarray(ic)
    print(ic)
    print(f"mean(IC): {np.mean(ic)}")
    print(f"IC 标准差：{np.std(ic)}")
    print(f"IC > 0: {sum(ic > 0)/len(ic)}")
    print(f"abs(IC) > 0.02: {sum(abs(ic)>0.02)/len(ic)}")
    print(f"IR: {np.mean(ic)/np.std(ic)}")

# 使用上一节生成的 FACTOR 和 RETURNS
ic_test(factor, returns)
```
</div>

<!--
IC分析的前面的步骤与回归分析是一样的。区别在于如何进行检验。

回归分析是将因子值与收益进行线性回归。IC要求我们进行相关性分析。

[click]

在示例中，我们通过spearman来做秩相关分析。

[click]

spearman这个方法来自scipy这个库

[click]

这也是一些常规的统计方法。注意最后一行，这里计算了IR

[click]

这就是信息比率的定义
-->
