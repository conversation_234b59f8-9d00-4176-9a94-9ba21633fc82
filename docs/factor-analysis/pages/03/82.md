---
clicks: 8
---

<div class="abs left-60% top-10%">


<v-clicks>

### 0. 传入对齐的factor和收益数据

### 1. 对factor中的每一行进行遍历


### 2. 取当天的因子和收益数据

### 3. 分层，取得每个分层的资产


### 4. 每个分层的每日均收益


### 5. 计算每个分层的累计收益

</v-clicks>
</div>

<div class="abs w-60% top-10% w-55%">

```python {all|18,3|5|6,7|9,10|12,13|15,16}{lines:true,at:1}
from collections import defaultdict
import matplotlib.pyplot as plt
def hierarchical_test(factor, returns):
    tier_returns = defaultdict(list)
    for date in factor.index.get_level_values("date"):
        y = returns.loc[date]
        x = factor.xs(date, level="date")

        grouper = pd.qcut(x.values, 3, labels=False)
        groups = x.T.groupby(grouper).apply(lambda z: z.index)

        for i, group in enumerate(groups):
            tier_returns[f"{i}"].append(np.mean(y[group].values))

    tier_returns = pd.DataFrame(tier_returns) + 1
    return tier_returns.cumprod()

tier_returns = hierarchical_test(factor, returns)
tier_returns.plot()
```
</div>

<div class="abs left-60% top-10%" v-motion
    :enter="{scale: 0}"
    :click-7="{scale: 1}">

![](https://images.jieyu.ai/images/2024/05/tier-returns.jpg)
</div>

<!--
无论是回归法还是IC法，都假定了在截面上因子暴露与远期收益之间存在线性关系。但实际上两者的关系完全有可能是非线性的。于是，一种名为分层回测法的方法被提了出来。

分层回测法就是依照因子值对股票进行分组，构建投资组合进行回测。分层测试法与回归法、IC 分析相比，能够更好地发掘因子对收益预测的非线性规律。也就是，如果分层测试结果显示，top分组长期稳定地好于middle, bottom分组长期稳定地弱于Middle分组，则该因子就有较好的预测能力。

但同样情况下，如果是用回归法和 IC 法来进行分析，该因子是有可能被判定为无效因子的，因为这两者的前提要求更高。

分层测试在模型构建上与前两者也有所不同，它有一个明显地换仓期，即在每个截面期核算因子值，构建分层组合，在截面期下一个交易日按当日收盘价换仓，并且可以引入交易费用，从而比前两种方法更贴近实操。

[click]

这个函数接受的参数是factor和returns，要求在日期上已经对齐。

[click]

函数有两层循环。第一层大循环是对因子数据中的每一行进行的

[click]

进入循环后，先通过日期来获取某一天的因子和收益数据。由于因子和收益数据格式不同，所以取数据的方法也不同

[click]

第9行和第10行是在进行因子分层。我们先生成一个分组器，然后再对因子数据进行分组，通过apply获取组内元素的代码。

[click]

接下来是个小循环。我们设定的是3个分层，所以这个循环是3次。

这里i是分组的标签，group就是分组中的资产代码。由于收益数据的索引就是资产代码，所以，这里y[group].values就是该分层所有资产的收益数据。

[click]

在大循环走完之后，tier_returns就保存了每日、每个分层的收益均值。所以，我们可以通过cumprod来计算出每个分层的累计收益

这里我们没有加入交易费用。如果需要，就在第15行处减去交易费用就可以了

好，第3讲我们就介绍到这里。
-->
