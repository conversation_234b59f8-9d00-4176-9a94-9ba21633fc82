---
clicks: 3
---

<div class="abs w-full mt-10" v-motion
    :enter="{opacity: 1}"
    :click-3="{opacity: 0}">

```python {all|12|15}{lines:true}
def regression_test(factor, returns):
    factor = factor.dropna()
    returns = returns.dropna()
    factor_dates = factor.index.get_level_values("date")
    valid_dates = returns.index.drop(returns.index.difference(factor_dates))
    factor_return = []
    t = []
    for date in valid_dates:
        y = returns.loc[date]
        x = factor.xs(date, level="date")

        model = sm.RLM(y.values.T, x.T.values, M=sm.robust.norms.HuberT())
        res = model.fit()

        factor_return.append(res.params.item())
        t.append(res.tvalues.item())

    return factor_return, t
```
</div>


<div class="abs w-full mt-10" v-motion
    :enter="{opacity: 0}"
    :click-3-6="{opacity: 1}">

```python {all|12|15}{lines:true}

fr, t = regression_test(factor, returns)
print(fr)
print(t)

print(np.mean(np.abs(t)))
print(np.sum(np.abs(t) > 2)/len(t))
```
</div>

<!--
现在，我们来分析动量因子，对收益的贡献是多少，也就是做一个回归分析。

在本章一开头，我们就介绍了回归分析的原理。就是以每一期的多个资产的因子值为自变量，以多个资产的收益为因变量进行线性回归，这样得到的斜率，即为因子收益。

[click]

这一次，我们使用的是statsmodels。在讲中性化时，我们介绍的是sklearn的 regression方法。

这些方法本质上没有区别。我们在第一章的练习题中，要求大家以4种不同的方式来计算CAPM模型，也验证了这一点。

但是，细微的差别也很容易导致我们出错。有没有人能讲一下其中的差别？

ok，最重要的区别是自变量与因变量的传入顺序。在sklearn中，自变量一般命名为X，因变量命名为y，在fit方法中，X在前，y在后。

在statsmodels中，自变量命名为exdog，因变量命名为endog，在fit方法中，因变量在前，自变量在后。

所以，这样会产生什么样的问题？

如果一条直线的自作斜率不是45度角，那么，你交换x与y，会发生什么？这会导致新旧直线以45度直线形成镜像。所以，最终斜率也完全不一样。

[click]

在这一行中，res.params就是拟合得到的系数。我们的方程是一次的，所以，只有一个系数项和一个常数项，常数项是截距。但是在这里是没有截距的，所以，我们通过res.params.item()取到的，就只有系数，即斜率。

最后，我们把所有的斜率和t值都添加到数组中，将数组返回。

[click]

现在，真正的分析才刚刚开始。

有了每一期的因子回报数据和t值数据，我们就可以研究它的统计特性，从而知道这个因子回报是否有效，是否可靠。

不过，我们这里的分析也显得太简陋了。这也是为什么我们要使用框架的原因。
-->
