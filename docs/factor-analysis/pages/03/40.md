---
clicks: 5
---

<div class="abs mt-10 left-40% w-60%">

```python {all|12|13|17|19|23}{lines: true}
def get_sector_dummies(universe: List[str], days: List[datetime.date]):
    """获取行业哑变量表

    返回结果是一个以 asset, date 为索引，行业名称为列，0/1 为值的 dataFrame。
    注意本实现中，我们使用的是申万一级行业分类。
    Args:
        universe: 股票列表，同因子生成范围
        days: 日期列表，同因子生成日期。
    Return:
        以 asset, date 为索引，行业名称为列，0/1 为值的 dataFrame
    """
    sectors = load_sectors()
    filtered = sectors[sectors.index.isin(universe)]

    dfs = []
    for day in days:
        df = filtered.to_frame(name="sector")
        df["date"] = day
        df = df.reset_index().set_index(["date", "asset"])
        dfs.append(df)

    df = pd.concat(dfs)
    return pd.get_dummies(df, columns=["sector"])
```
</div>

<div v-motion class="abs w-35% mt-30"
    :enter="{opacity: 0}"
    :click-1-5="{opacity: 1}">

![](https://images.jieyu.ai/images/2024/09/load-sectors-result.png)
</div>

<div v-motion class="abs w-35% mt-20"
    :enter="{opacity: 0}"
    :click-5-6="{opacity: 1}"
    >

## One-Hot 编码
One-Hot编码的结果是，每个资产有且仅有一列取值为1，其余列取值为0。

![](https://images.jieyu.ai/images/2024/09/get-dummies-result.png)
</div>

<!--
这部分对应教材4.2节。这个函数是为行业中性化做准备的。

[click]

这个函数的作用是获取个股的行业分类信息。我们在上一讲中已经见过了。左图是它的结果。它是一个以资产代码为索引、只有一列的 Series

[click]

load_sectors把所有的个股的资料都加载了，但我们只关心在unverse中的那些。

所以，这里我们通过isin这个函数来进行过滤。这是一个在使用上非常高频的函数。

[click]

为了后面操作方便，我们需要把series转换成dataframe.

可以像这里一样，使用to_frame方法。所以我们今天讲了很多转换函数。

[click]

这里我们还见到了reset_index这个方法。这一步在干什么呢？

在上一行时，我们已经得到了一个以资产代码为索引、包含date和sector这样两列的dataframe.

我们希望把它转换成以date和asset为多重索引的新的dataframe。

上一小节我们实现由单级索引到多级索引的转换，也是使用的set_index，但通过append参数，把asset追加到多重索引中。

大家思考一下，这里为什么不用同样的方法呢？

原因是，待操作的dataframe，它的索引是asset。如果我们追加一个label为date的索引，两个索引的顺序就会是asset, date。但我们想要的顺序是date, asset。

所以，我们先通过reset_index，将date从索引变换为一个普通列。然后就可以通过set_index的语法，将一个列标签数组传递进来，完成多重索引的设置。

[click]

最后，我们需要将文本标签转换成为one-hot的标签。pandas已经提供了这个功能。机器学习库中，sklearn也提供了这个功能。

最终我们将得到这样的结果。
-->
