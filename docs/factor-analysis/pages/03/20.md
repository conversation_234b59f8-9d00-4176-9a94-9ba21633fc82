---
clicks: 2
---

<div class="abs w-50% mt-20">

<div v-motion
    :enter="{ y: 0 }"
    :click-3="{y: -720}">

## IC

</div>

<div v-motion
    :enter="{ y: 720 }"
    :click-1-3="{y: 20}">

## Rank IC
</div>

<div v-motion
    :enter="{ y: 720 }"
    :click-2-3="{y: 40}">

## 分层回溯
</div>

</div> <!-- left -->


<div class="abs left-50% w-50% mt-20 ml--40" v-motion
    :enter="{y:0}"
    :click-2="{y: -720}">

$$
\rho = Pearson(F^T, R^T)
$$
</div>

<div class="abs left-50% w-50% mt-20 ml--40" v-motion
    :enter="{ y: 720 }"
    :click-1-2="{y: 80}">

$$
\rho = Spearman(F^T, R^T)
$$
</div>

<!--
回归分析的问题是，它的条件太严苛了。资产价格的波动，往往是由多个因素共同地、复杂地决定的，因此，很多情况下，线性回归的前提并不一定存在。

于是，人们又发明了IC法。通俗地说，它是对因子-收益的相关性检验。它不再要求我们能从因子直接推导出收益，而是要求因子能说明收益的变化，与因子的变化会有多强的关联度。

IC的求法就是，对每一个时间T，以该期因子为一个变量，收益率为另一个变量，求pearson相关系数。

[click]

IC法的问题是，它仍然执念于预测绝对收益这件很困难的事。

为什么这么说？

因为它的本质是，因子值与收益值对相关系统的贡献仍然很大。

但在实际交易中，我们会遇到这样的情况，冠军与亚军的100米速度只差百分之一秒，但两个人的获得的资金和荣誉却是天壤之别。

这种现象在自然界普遍存在、在社会中普遍存在。它也应该存在于交易之中。

但是，这个差距，在pearson相关性中，是很不显著的。我们需要把这个差异突出出来。

那么，我们应该如何为此建模？

统计学家Spearman已经为我们准备好了相关的工具，即秩相关系数。

在量化领域，我们把这样求得的收益与因子的相关性，称为Rank IC

[click]

即使有了IC和RANKIC，我们对因子与收益的绑定似乎仍然过强。毕竟，如果一个因子有效，我们又能长时间、大范围地进行测试的话，我们就没有必要在初期加入这么强的约束。毕竟，是金子总会发光。

于是，关于分层的一个想法就被提了出来。我们在前面介绍过，这个想法可能最早是 Rolf Banz 付诸实施的。

在他的论文里，他把资产按市值大小分为了5组，然后用了30年的数据来进行测试。
-->
