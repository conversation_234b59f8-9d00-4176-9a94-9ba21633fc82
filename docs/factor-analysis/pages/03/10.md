---
clicks: 3
---

<div class="abs h-100% w-50%">

<v-clicks>

</v-clicks>

<div v-motion class="abs mt-30"
    :enter="{x: 0}"
    :click-1="{x: -1280}">

$$
E(R_i) = R_f + \beta(E(R_m) - R_f)
$$
</div>

<div v-motion class="abs"
    :enter="{y: 720}"
    :click-1-4="{y: 100}">

假设我们有$n$个资产，它们在时刻$t_0$的日收益率为：

$$
[r_1, r_2, ..., r_n]
$$
</div>

<div v-motion class="abs"
    :enter="{y: 720}"
    :click-2-4="{y: 200}">

假设在同一时刻，我们有另外一组变量，取值为：

$$
[X_1, X_2, ..., X_n]
$$
</div>



</div>

<!-- right -->


<div class="abs w-50% left-50% mt-15" >


<div v-motion
    :enter="{x: 1}"
    :duration=500
    :click-4="{x: 1280}">

![](https://images.jieyu.ai/images/2023/06/fit_intercept.png)
</div>




<v-drag-arrow color="red" pos="363,36,2,322" style="zIndex: 100" v-motion
    :enter="{opacity: 0}"
    :click-3-4="{opacity: 1}"/>



<div class="abs" v-motion
    :enter="{scale: 0}">

![](https://images.jieyu.ai/images/2024/05/factor-analysis-regression-approach.jpg)
</div>

</div>

<!--
关于因子检验，人们先后发明了三种方法，即回归法、IC法（包含rank IC)和分层回溯。

在第一课的习题中，我们给出了一道关于CAPM的练习。


CAPM把期望收益解释为无风险利率与组合的期望收益与市场因子的乘积之和。

因此，在完美的市场运行中，截距就是rf，beta就是右图直线的斜率，直线反应了整个公式。如果截距不能完全用rf解释，那么它就包含了alpha。

这是线性回归的一个运用。

[click]

现在，假设我们有n个资产，它们在时刻t0的日收益率为r1，r2，...，rn。我们把Rn称为因变量。

[click]

我们有另外一组自变量, x0, x1,...xn，如果我们以xi为横坐标，对应的ri的纵坐标，显然，我们也会得到类似右图中的散点图。

如果这些点满足线性回归的条件，我们就能够为其绘制出一条趋势线出来。这个趋势线的斜率，就是因子对收益的影响能力。

比如，因子是按橫坐标从左到右排列的，左边最小，右边最大。如果斜率是正的，那么，因子越小，对应的资产收益率越低；反之，就越高。

[click]

现在，我们来看其中的一个点，横坐标为780的这一系列点。

这里的780是因子值。在这一条竖线，聚集着许多点，它们是由因子值相同、但收益不同的资产组成的。这一点可以理解吗？

如果收益完全由因子值决定，那么会出现什么情况？

这条竖线就不会存在。在t0时刻，因子值取到780的资产，收益都应该落在自作自作斜线与竖线的交叉点上。

正是收益不能完全由我们正在考察的这个因子决定，所以，才会在每一个这样的竖线上，都出现了一些离散的点。

于是，我们就可以用这个离散程度，来决定因子的可信度。
-->
