---
clicks: 2
---

<div class="abs w-40% mt-10" v-motion
    :enter="{y: 720}"
    :click-1-2="{y: 0}">

## xs

<img src="https://images.jieyu.ai/images/2024/09/pandas-xs.jpg" style="margin-bottom: 60px"/>

<v-drag-arrow color="red" pos="38,158,2,141"/>

![](https://images.jieyu.ai/images/2024/09/pandas-xs-result.jpg)

</div>


<div class="abs w-40% mt-20" v-motion
    :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}">

## set_index

### 改变索引
### 增加索引，成为多重索引


</div>

<!-- left -->
<div class="abs left-40% mt-10 w-60%">

```python {all|17|23}{lines: true,maxHeight: '100%'}
def build_momentum_factor(universe: Tuple[str], 
                          start: datetime.date, 
                          end: datetime.date):
    """构建 10 日动量因子。

    Args:
        instruments: 股票代码
        start: 起始日期
        end: 结束日期
    Return:
        以 date,asset 为索引，mom 和 price 为列的 DataFrame
    """
    factors = []

    barss = load_bars(start, end, universe)
    for asset in universe:
        bars = barss.xs(asset, level="asset")
        bars["mom"] = bars["close"].pct_change(10)
        bars["asset"] = asset
        factors.append(bars[["asset", "mom", "price"]])
    
    factor = pd.concat(factors)
    factor.set_index("asset", append=True, inplace=True )
    return factor

universe = ("000001.XSHE", "000002.XSHE", "000004.XSHE")
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
build_momentum_factor(universe, start, end)
```
</div>

<!--
动量因子最初是Titman提出来的。但Carhat让它变得为人广泛接受。

动量因子最初是这样构建的，以11个月为窗口，计算资产的收益率作为因子。在我们这里的实现中，我们简单地使用10天为例。

这是教材中的示例 3 - 1
这段代码里，我们主要讲解两个方法。

[click]

第一个方法，xs，我们上一课介绍过，这里再复习一下。它实际上完成了左图这样的变换。

它在一个多重索引的dataframe中，提取level为给定值，并且该level的值等于给定值的所有行

[click]

我们常用 set_index来变更index。这里的变更，是指带数据的变更。我们改用另一列、或者另几列来设置索引，在示例3-3中，我们可以看到同时设置几列为索引的示例 ；在现有的index基础上增加一列，成为多重索引；这里的例子就是使用的这个语法。

我们甚至还可以使用一个全新的数据序列来作为新索引，它们并不需要已经在dataframe中存在。

我们为什么需要这些操作？大家可以举出来一些例子吗？

主要原因是，我们常常会在不同的python库之间传递数据，因此，数据格式就往往需要进行转换。

set_index是用来改变索引的数据的。有时候，我们需要改变索引的名字。此时就要用df.index.name = 来修改。
-->
