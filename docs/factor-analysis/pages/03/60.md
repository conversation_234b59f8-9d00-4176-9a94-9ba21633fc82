---
clicks: 7
---

<div class="abs w-35% h-80% mt-20 left-0">

<div v-motion :enter="{opacity: 1}" :click-6="{opacity: 0}">

## get_clean_factor
</div>

<div v-motion :enter="{opacity: 0}" :click-1-6="{opacity: 1}">

### 1. 去极值
</div>


<div v-motion :enter="{opacity: 0}" :click-2-6="{opacity: 1}">

### 2. 去缺失值
</div>

<div v-motion :enter="{opacity: 0}" :click-3-6="{opacity: 1}">

### 3. 标准化
</div>

<div v-motion :enter="{opacity: 0}" :click-4-6="{opacity: 1}">

### 4. 为中性化准备数据
</div>

<div v-motion :enter="{opacity: 0}" :click-5-6="{opacity: 1}">

### 5. 多重线性回归中性化
</div>

<div v-motion :enter="{opacity: 0}" :click-6-7="{opacity: 1}" class="abs top-0% mb-20">

![](https://images.jieyu.ai/images/2024/09/before-unstack.png)

<v-drag-arrow pos="155,120,-1,87" color="red"/>
</div>


<div v-motion :enter="{opacity: 0}" :click-6-7="{opacity: 1}" class="abs top-45%">

![](https://images.jieyu.ai/images/2024/09/stack-result.jpg)
</div>

</div><!--left-->

<div class="abs w-60% mt-10 left-40% h-80%">

```python {all|14|17|20|22-34|36-50|51}{maxHeight:'100%',lines:true}
def get_clean_factor(factor: pd.DataFrame,
                    neutral_mkt = True,
                    neutral_sector = False)->pd.DataFrame:
    """对原生因子`factor`进行预处理：去极值、标准化、中性化、缺失值处理
    Args:
        factor: 提取的原生因子
        neutral_mkt: 是否要做市值中性化
        neutral_sector: 是否要做行业中性化
    Return:
        处理后的因子。以日期为索引，instrument 为列，单元格值为因子值

    """
    # 去极值
    factor = mad_clip(factor['mom'].unstack(level='asset'))
    
    # 缺失值处理。如果在这一步不进行处理，则后面的标准化，特别是中性化将无法计算
    factor = handle_missed(factor)

    # 对因子进行标准化. ZSCORE 可以直接处理 DATAFRAME
    factor = zscore(factor, axis=1)
    
    instruments = factor.columns.to_list()
    days = factor.index.to_list()

    cap = pd.DataFrame([])
    if neutral_mkt:
        cap = np.log(get_valuation(instruments, days))

    industry = pd.DataFrame([])
    if neutral_sector:
        industry = get_sector_dummies(instruments, days)
        
    X = pd.concat((cap, industry), axis=1)
    X = X.dropna(how = "any", axis=0)

    model = LinearRegression(fit_intercept=False)
    residues = []

    for date, group in X.groupby(by="date", group_keys=True):
        Xi = group.droplevel(0)
        Xi = Xi[Xi.index.isin(instruments)]
        y = factor[factor.index == date].T

        res = model.fit(Xi, y)
        coef = res.coef_

        residue = y - np.dot(Xi, coef.T)
        residues.append(residue)

    clean_factor = pd.concat(residues, axis=1)
    return clean_factor.T.stack(level='asset')
```
</div>

<!--
[click]
去极值。我们在前面已经见过mad_clip这个方法。

大家可以比较一下，这一章的去极值的实现，与上一章发生了细微的差别。主要表现是，多了一个axis参数。

为什么会多出来这样一个参数？

上一章的去极值，我们只需要处理一天的数据。而这一次，我们的因子检验发生在一个区间上，数据多出了一个维度。在这种情况下，为了利用向量化运算，我们必须使用一些技巧。

这一部分我们先不讲，如果大家反馈觉得需要讲，我们再讲。

[click]

处理缺失值

[click]

标准化。



[click]

请大家去看一下get_valuation的具体实现
注意市值是与日期密切相关的。有的股票可以一个月翻倍，一个季度涨十倍，也可以一个季度跌去 80%以上。因此，它的市值变化是很大的，我们必须使用因子对应日期的市值来进行中性化

[click]

[click]

在前面部分，我们将数据展开成了宽表；现在，我们通过stack方法把它转换多重索引的表格
-->
