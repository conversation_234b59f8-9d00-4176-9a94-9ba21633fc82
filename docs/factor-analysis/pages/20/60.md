---
clicks: 5
---

<div class='abs mt-20' v-motion
     :enter='{scale: 1}'
     :click-1='{scale: 0}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/20250213173426.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 0}'>

```python
import datetime
import logging
from numpy import ndarray
from numpy.typing import ArrayLike, NDArray
import os
import pickle
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from numpy.lib.stride_tricks import as_strided
import lightgbm as lgb
from zigzag import peak_valley_pivots


logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)
mlogger = logging.getLogger("matplotlib")
# supress matplotlib info level logging, which is anoying
mlogger.setLevel(logging.WARNING)


class WaveRiderBase:
    def __init__(self):
        self._model_ = None
        self._name_: str | None = None
        self._desc_: str | None = None

        self._features_ = []
        # for encode/decode categorical features
        self._label_encoders_ = {}

    @property
    def model(self):
        return self._model_

    @property
    def features(self):
        return self._features_

    def save(self, path: str, auto_rename: bool = True, name: str | None = None):
        """将模型保存到指定路径。

        如name未指定，将使用模型名作为文件名。如果同名文件已存在，将自动重命名为当前时间戳。文件名会自动加pkl后缀
        Args:
            path: 保存路径
            auto_rename: 如果同名文件已存在，是否自动重命名
            name: 保存文件名
        """
        if self._model_ is None:
            raise ValueError("请先调用fit方法训练模型")

        if not os.path.exists(os.path.expanduser(path)):
            raise ValueError("保存路径不存在")

        name = name or self._name_
        if name is None:
            raise ValueError("请指定保存文件名")

        file = os.path.join(path, f"{name}.pkl")
        if os.path.exists(file):
            if auto_rename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"{name}_{timestamp}.pkl"
                file = os.path.join(path, name)
                assert not os.path.exists(file)
                logger.warning("保存路径已存在同名文件，已自动重命名为%s", name)
            else:
                raise ValueError("保存路径已存在同名文件，请指定指定文件名")

        with open(file, "wb") as f:
            pickle.dump(
                {
                    "model": self._model_,
                    "name": self._name_,
                    "desc": self._desc_,
                    "save_time": datetime.datetime.now(),
                },
                f,
            )

    def load(self, file_path: str):
        with open(file_path, "rb") as f:
            data = pickle.load(f)
            self._model_ = data["model"]
            self._name_ = data["name"]
            self._desc_ = data["desc"]
            logger.info("加载模型%s成功。", self._name_)

    def rolling_time_series(self, ts: NDArray, win: int) -> NDArray:
        """生成rolling time series

        Args:
            ts: 一维时间序列
            win: 窗口大小
        Returns:
            返回shape为（len(ts) - win + 1, win）的二维numpy数组
        """
        stride = ts.strides
        shape = (len(ts) - win + 1, win)
        strides = stride + stride
        return as_strided(ts, shape, strides)

    def encode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """将类别特征编码。

        类别特征可能需要编码解码。此时需要label_encoders属性保存编码器。此方法会自动保存编码器到label_encoders属性中。
        """
        if name in self._label_encoders_:
            logger.info("已经存在%s特征编码器，直接复用")
            return self._label_encoders_[name].transform(feature)

        self._label_encoders_[name] = LabelEncoder()
        return self._label_encoders_[name].fit_transform(feature)

    def decode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """解码经类别编码器编码的序列"""
        if name not in self._label_encoders_:
            raise ValueError(f"不存在{name}特征编码器，无法进行解码")

        return self._label_encoders_[name].inverse_transform(feature)

    def train_test_split(
        self,
        data: pd.DataFrame,
        features: List[str],
        group_id: str | None = None,
        cuts=(0.7, 0.2),
    ) -> Tuple[pd.DataFrame | pd.Series]:
        """对时间序列进行train, valid和test子集划分。

        与sklearn的train_test_split不同，此方法会根据时间进行划分，而不是随机划分。

        请事前对data进行排序。如果group_id为None，则直接对data进行划分；否则，先按group_id进行分组，
        再在组内进行划分，最后合并成与data同样shape的DataFrame

        Args:
            data: 时间序列数据，应包含features中的所有列，以及名为target的列
            group_id: 根据此列进行分组，在组内划分子集
            features: 在data中，哪些列是特征列
            cuts: 训练集、验证集、测试集的比例

        Returns:
            返回X_train, X_valid, X_test, y_train, y_valid, y_test
        """
        logger.info("trained with features: %s", features)

        def cut_time_series(group, cuts):
            itrain = int(len(group) * cuts[0])
            ival = itrain + int(len(group) * cuts[1])

            return (group.iloc[:itrain], group.iloc[itrain:ival], group.iloc[ival:])

        if group_id is None:
            train, valid, test = cut_time_series(data, cuts)
            return (
                train[features],
                valid[features],
                test[features],
                train["target"],
                valid["target"],
                test["target"],
            )

        # 按group_id进行分组，在组内划分，最后合并成与data同样shape的DataFrame
        train, valid, test = [], [], []
        for item in data.groupby(level=group_id).apply(cut_time_series, cuts=cuts):
            train.append(item[0])
            valid.append(item[1])
            test.append(item[2])

        train = pd.concat(train)
        valid = pd.concat(valid)
        test = pd.concat(test)

        return (
            train[features],
            valid[features],
            test[features],
            train["target"],
            valid["target"],
            test["target"],
        )

    def peaks_and_valleys(self, ts, up_thres=None, down_thres=None):
        # 分类任务标签需要从0开始
        if len(ts) < 5 * 4:
            return [1] * len(ts)

        mas = moving_average(ts, 5, padding=False).astype(np.float64)

        if up_thres is None:
            pct = mas[1:] / mas[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std
            down_thres = -2 * std

        if down_thres is None:
            pct = mas[1:] / mas[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std
            down_thres = -2 * std

        pvs = peak_valley_pivots(mas, up_thres, down_thres) + 1
        pvs[0] = 1
        pvs[-1] = 1
        return np.insert(pvs, 0, [1] * 4)

    def features_and_labels(self, barss) -> pd.DataFrame:
        data = barss.groupby("asset", level=1).apply(self.extract_features_and_labels)
        return data.droplevel(0)

    def train(
        self,
        barss: pd.DataFrame,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
    ):
        params = {"random_state": 42}

        train_params["objective"] = "multiclass"
        train_params["metric"] = "multi_logloss"
        # 多分类时，需要设置分类数
        train_params["num_class"] = 3

        if "feval" in train_params:
            feval = train_params["feval"]
            del train_params["feval"]
        else:
            feval = None

        params.update(train_params)
        evals_result = {}

        data = self.features_and_labels(barss)
        X_train, X_val, X_test, y_train, y_val, y_test = self.train_test_split(
            data, self.features
        )

        train_data = lgb.Dataset(X_train, label=y_train)
        valid_data = lgb.Dataset(X_val, label=y_val)

        if self._model_ is not None:
            logger.warning("Model already trained, retraining...")

        self._model_ = lgb.train(
            params,
            train_data,
            num_boost_round=epochs,
            valid_sets=[valid_data],
            feval=feval,
            categorical_feature=list(self._label_encoders_.keys()),
            callbacks=[
                lgb.early_stopping(early_stop_round),
                lgb.record_evaluation(evals_result),
            ],
        )

        report = self.eval_model(X_test, y_test)
        return self.model, report

    def predict(self, X: ArrayLike) -> ndarray:
        assert self._model_ is not None, "Model not trained"
        return self._model_.predict(X)

    def eval_model(self, X_test, y_true):
        """对模型进行评估"""
        y_pred = self.predict(X_test)
        y_pred = np.argmax(y_pred, axis=1)

        class_report = classification_report(y_true, y_pred, output_dict=True)
        cm = confusion_matrix(y_true, y_pred)

        plt.figure(figsize=(5, 3))

        sns.heatmap(cm, annot=True, fmt="d", cmap="Blues")

        plt.xlabel("Predicted labels")
        plt.ylabel("True labels")
        plt.title("Confusion Matrix")

        # 显示图形
        plt.show()

        return pd.DataFrame(class_report).transpose()
```
</NoteCell>

<NoteCell layout='horizontal'
          class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1-5='{ scale: 1}'>

```python{all|2|32-45|all}{at:1, maxHeight: 450}
class WaveRiderV3(WaveRiderBase):
    def peaks_and_valleys(self, ts, up_thres=None, down_thres=None):
        """
        使用 zigzag 库生成顶和底的标签，并进行修正。
        """
        if len(ts) < 5:
            return np.ones(len(ts))

        mas = moving_average(ts, 5, padding=False).astype(np.float64)

        if up_thres is None:
            pct = mas[1:] / mas[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std

        if down_thres is None:
            pct = mas[1:] / mas[:-1] - 1
            std = np.std(pct)
            down_thres = -2 * std

        pvs = peak_valley_pivots(mas, up_thres, down_thres)
        pvs[0] = 0
        pvs[-1] = 0
        pvs = np.insert(pvs, 0, [0] * 4)

        # 修正峰值和谷值
        peaks = np.where(pvs > 0)[0]
        valleys = np.where(pvs < 0)[0]

        corrected_pvs = np.zeros_like(pvs)

        for peak in peaks:
            if peak < 3:
                corrected_pvs[peak] = 1
            else:
                # 在峰值附近5个数据点内查找最高点
                win_start = max(0, peak - 2)
                win_end = peak + 2
                max_high_idx = np.argmax(ts[win_start:win_end]) + win_start
                corrected_pvs[max_high_idx] = 1
                # 设置最高点前后各1个点为1
                if max_high_idx > 0:
                    corrected_pvs[max_high_idx - 1] = 1
                if max_high_idx < len(ts) - 1:
                    corrected_pvs[max_high_idx + 1] = 1

        for valley in valleys:
            if valley < 3:
                corrected_pvs[valley] = -1
            else:
                # 在谷值附近5个数据点内查找最低点
                win_start = max(0, valley - 2)
                win_end = valley + 2
                min_low_idx = np.argmin(ts[win_start:win_end]) + win_start
                corrected_pvs[min_low_idx] = -1
                # 设置最低点前后各1个点为-1
                if min_low_idx > 0:
                    corrected_pvs[min_low_idx - 1] = -1
                if min_low_idx < len(ts) - 1:
                    corrected_pvs[min_low_idx + 1] = -1

        return corrected_pvs + 1

    def features_and_labels(self, group) -> pd.DataFrame:
        rsi_win = 6
        if len(group) < rsi_win * 3:
            group["rsi"] = np.nan
        else:
            rsi = ta.RSI(group["close"], rsi_win)
            rsi[: rsi_win * 3] = np.nan
            group["rsi"] = rsi[rsi_win * 3 :]

        group["uo"] = ta.ULTOSC(
            group.high,
            group.low,
            group.close,
            timeperiod1=7,
            timeperiod2=14,
            timeperiod3=28,
        )
        group["wr"] = (group.close - group.high) / (group.high - group.low)
        group["wr_3_high"] = (
            group["high"]
            / group["high"].rolling(window=3, min_periods=3).max().shift(1)
            - 1
        )
        group["wr_3_low"] = (
            group["low"] / group["low"].rolling(window=3, min_periods=3)
                                       .min().shift(1)
            - 1
        )

        group["d1"] = (group.close
                            .pct_change()
                            .rolling(window=3, min_periods=3)
                            .mean())
        group["d2"] = (group.close
                            .pct_change()
                            .diff()
                            .rolling(window=3, min_periods=3)
                            .mean())

        ma5 = group.close.rolling(window=5).mean()
        ma5_d1 = (ma5/ma5.iloc[-1]).diff()
        group["ma5_d1"] = ma5_d1
        group["ma5_d2"] = ma5_d1.diff()
        self._features_ = ["wr", "wr_3_high", "wr_3_low",
                           "uo", "rsi", "d2", "d1", "ma5_d1", "ma5_d2"]
        group["target"] = self.peaks_and_valleys(group["close"].values)
        group["target"] = self.peaks_and_valleys(group["close"].values)

        return group[self.features + ["target"]]

rider = WaveRiderV3()

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 31)

np.random.seed(78)
barss = load_bars(start, end)
modle, cr = rider.train(barss, 100)
cr
```

</NoteCell>

<div class='abs ml-150 mt-20' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/v2-with-mad1-mad2.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-15' v-motion
     :enter='{scale: 0}'
     :click-5-6='{scale: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/20250214120607.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs' v-motion
     :enter='{scale: 0}'
     :click-5-6='{scale: 1}'>

<v-drag-arrow color='red' pos="336,66,-22,42"/>
<v-drag-arrow color='red' pos="336,66,24,77"/>
<v-drag-arrow color='red' pos="334,67,76,11"/>
<v-drag-arrow color='red' pos="337,68,240,72"/>
<v-drag-arrow color='red' pos="336,67,205,101"/>
</div>


<!--
我们再回顾下之前见过的这张图。在这张图中，算法标注的第2个波峰如箭头所示，但实际见顶是在前两天，或者前三天。

这样会有什么问题？

这意味着在算法标注为顶点的地方，并没有出现波峰的特征，而在出现波峰特征的地方，我们又没有把它标注为顶点。 实际上，这是一个错误的标注，在机器学习当中，错误的标注带来的影响，要比样本量不足还要大。

下面，我们看看，如果能够纠正这些标注错了的地方，结果会如何。

[click]

<run></run>

在基类中，存在着peaks_and_valleys方法，它是用zigzag对5日均线查找波峰与波谷，得到的标签。现在，我们要改写这个方法，对标签进行修正。


[click]

修正算法如下，我们先仍然按照之前的算法查找波峰与波谷。在这些波峰与波谷找出来之后，对每一个波峰，我们在波峰附近5个数据点内查找最高点，并把波峰修改到这个新的位置。对波谷也是类似处理。


[click]

现在，我们来看看运行结果。

现在我们看到，它找到的波谷达到了17个之多，波峰达到了10个之多。

在进行标签校正之后，能找到的波峰与波谷成倍增长。而误报率却保持不变。这充分说明了打对标签的重要性。这也是为什么我们之前需要介绍手工标注工具的原因。

现在，精确率达到了32%，召回率达到了4.3%和2.6%。


[click]

如果我们在此基础上，进一步增加特征，那么，这些指标还会继续提高。比如，我们可以把均线拐头的特征加上。在测试中，加上均线拐头特征之后，这个模型的精确率达到40%、30.5%，召回率达到了3.9%/4.7%。

[click]

回想我们一开始就讲过的这一特征，高开大阴线。实际上，到目前为止，我们都没有使用这一特征。

如果加入这个特征，那么找到的波峰还会增加，并且误报率会降低。我们把它当成作业，请大家自己完成。当然，按我们V3版的标注方法，会利用不上这个特征，因为以波峰为例，高开低走的情况出现后，我们的算法一定是把前一天标注为顶点，而不是当天。你需要适当调整标注方法，以均线波峰附近的最高价的最大值位置作为顶点，而不是收盘价的最大值位置作为顶点。

-->
