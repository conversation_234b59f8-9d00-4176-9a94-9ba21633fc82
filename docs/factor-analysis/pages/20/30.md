---
clicks: 23
---

<FlashText v-click="[0,1]"
           class='abs mt-40 text-center w-full text-3xl'>

LabelImg <br><br> LabelMe
</FlashText>

<div class='abs w-20% mt-30' v-motion
     :enter='{opacity: 0, x: 350, scale: 1}'
     :click-1='{opacity: 1}'
     :click-2='{x: 20, scale: 0.7}'
     :click-4='{scale: 0}'>

![](https://images.jieyu.ai/images/2025/02/h2owave.jpg)
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0, x: 350}'
     :click-2='{opacity: 1}'
     :click-3='{x: 700, scale: 0.8}'
     :click-4='{scale: 0}'>

![](https://images.jieyu.ai/images/2025/02/plotly-dash.png)
</div>

<div class='abs mt-10' v-motion
     :enter='{opacity: 0, scale:0.8, x: 300}'
     :click-3='{opacity: 1}'
     :click-4='{scale: 0}'>

![](https://images.jieyu.ai/images/2025/02/streamlit-logo.png)
</div>


<div class='abs mt-20' v-motion
     :enter='{opacity: 0, x:0}'
     :click-4-7 ='{opacity: 1}'
     :click-19='{opacity: 1, x: 250}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/label-tools.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<v-drag-arrow v-click='[5,6]' color='red' pos="353,258,41,19"/>
<v-drag-arrow v-click='[6,7]' color='red' pos="458,246,-50,24"/>
<v-drag-arrow v-click='[4,5]' color='red' pos="458,246,-40,40"/>

<div v-motion class='abs mt-10 w-full'
          :enter='{ opacity: 0}'
          :click-7-8='{ opacity: 1}'>

```python
import ipywidgets as widgets

from IPython.display import display
```
</div>

<div v-motion class='abs mt-10 w-full'
          :enter='{ opacity: 0}'
          :click-8-13='{ opacity: 1}'>

```python{all|4|6|9}{at: 9}
import ipywidgets as widgets
from IPython.display import display

text_input1 = widgets.Text(description="输入1:")
text_input2 = widgets.Text(description="输入2:")
button = widgets.Button(description="提交")

# 使用 HBox 将它们水平排列
form = widgets.HBox([text_input1, text_input2, button])

display(form)
```
</div>

<div class='abs mt-80' v-motion
     :enter='{opacity: 0}'
     :click-12-13='{opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/hbox-layout.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>


<div class='abs mt-30' v-motion
     :enter='{opacity: 0, x:220}'
     :click-13='{opacity: 1}'
     :click-14='{x: 0}'
     :click-15='{opacity:0}'>

<div style='width:60%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/ipywidgets-gridbox.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-25' v-motion
     :enter='{opacity: 0, x: 500}'
     :click-14-15='{opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/ipywidgets-layout-the-rest.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-10 w-full' v-motion
     :enter='{opacity: 0}'
     :click-15-17='{opacity: 1}'>

```python{all|5}{at:16}
peaks_box = Textarea(
    value="",
    placeholder="请输入峰值时间，每行一个",
    description="峰值时间",
    layout=Layout(width="40%", height="100px"),
)
```
</div>

<div v-motion class='abs mt-10 w-full'
          :enter='{ scale: 0, width: "100%"}'
          :click-17='{ scale: 1}'
          :click-19='{width: "50%"}'>

```python{all|152-154|34-49|37-38|48-49|34|94-96,106-108}{maxHeight: '450px',at:18}
import plotly.graph_objects as go
import traceback
from ipywidgets import Button, HBox, VBox, Textarea, Layout, HTML, Output, Box
from IPython.display import display
import arrow

code = "000852.XSHE"
start = datetime.date(2005, 1, 1)
end = datetime.date(2023, 12, 31)

bars = load_bars(start, end, (code,)).xs(code, level="asset").reset_index()

i = 0
frames = bars["date"]
close = bars["close"]
labelled_data = None

last_pv = []

recs = {}


def log(msg):
    global info

    info.clear_output()
    with info:
        if isinstance(msg, Exception):
            traceback.print_exc()
        else:
            print(msg)


def keep(b):
    global peaks_box, valleys_box, recs, last_pv, i, frames

    recs.update({arrow.get(frame).naive: 1 for frame in peaks_box.value.split("\n")})
    recs.update({arrow.get(frame).naive: -1 for frame in valleys_box.value.split("\n")})

    last_valley = arrow.get(valleys_box.value.split("\n")[-1])
    last_peak = arrow.get(peaks_box.value.split("\n")[-1])

    if last_valley > last_peak:
        last_pv = ["谷", last_valley.naive]
    else:
        last_pv = ["峰", last_peak.naive]

    i = np.argwhere(frames == last_pv[1]).flatten()[0]
    show_bars(None)


def save(b):
    global bars, recs, labelled_data, info
    labelled_data = pd.DataFrame(bars)
    labelled_data["flag"] = 0

    for frame, flag in recs.items():
        labelled_data.loc[labelled_data.date == frame, "flag"] = flag

    log(f"存入{len(recs)}条记录到数据文件中")
    with open("pv-labels.pkl", "wb") as f:
        pickle.dump(labelled_data, f)
        log(f"成功保存")

    recs = {}


def goback(b):
    global i, last_pv

    i -= 120
    if i < 0:
        return

    last_pv = []
    show_bars(None)


def show_bars(b):
    global i, frames, close, info, figbox, last_pv

    if len(last_pv) > 0:
        log(f"上一个顶点: {last_pv[0]} {last_pv[1]}")

    if i >= len(bars):
        log("已遍历完成")
        return
    else:
        s = i
        e = i + 120

        try:
            flags = peaks_and_valleys(close[s:e])
            cs = Candlestick(
                bars[s:e], height=600, ma_groups=[5, 10, 20]
            )

            x_peaks = np.argwhere(flags > 0).flatten()
            y = bars[s:e]["high"][x_peaks + i] * 1.005

            cs.add_marks(x_peaks, y, "peaks", marker="triangle-down", color="red")

            x_valleys = np.argwhere(flags < 0).flatten()
            y = bars[s:e]["low"][x_valleys + i] * 0.995
            cs.add_marks(x_valleys, y, "valleys", marker="triangle-up", color="green")

            fig = go.FigureWidget(cs.figure)
            figbox.children = (fig,)

            peaks = frames[s:e][x_peaks + i]
            if len(last_pv):
                peaks = peaks[peaks > last_pv[1]]
            peaks_box.value = "\n".join([f"{frame}" for frame in peaks])

            valleys = frames[s:e][x_valleys + i]
            if len(last_pv):
                valleys = valleys[valleys > last_pv[1]]
            valleys_box.value = "\n".join([f"{frame}" for frame in valleys])
        except Exception as e:
            log(e)


backward_button = Button(
    description="前一组",
)

keep_button = Button(description="记录 > 下一组")

save_button = Button(description="存盘")

info = Output(layout=Layout(width="40%"))

peaks_box = Textarea(
    value="",
    placeholder="请输入峰值时间，每行一个",
    description="峰值时间",
    layout=Layout(width="40%", height="100px"),
)

valleys_box = Textarea(
    value="",
    placeholder="valley moments here",
    description="请输入谷值时间，每行一个",
    layout=Layout(width="40%", height="100px"),
)

figbox = Box(layout=Layout(width="100%"))
buttons = HBox((backward_button, keep_button, save_button, info))
inputs = HBox((peaks_box, valleys_box))
display(VBox((buttons, inputs, figbox)))

keep_button.on_click(keep)
save_button.on_click(save)
backward_button.on_click(goback)
show_bars(None)
```
</div>

<v-drag-arrow v-click='[19,20]' color='red' pos="641,31,2,46"/>

<!--
我们刚刚使用了zigzag来寻找顶和底。由于使用了正确的自适应参数，看起来这些顶和底找得还不错。但是，对有经验的看盘人来说，可能希望自己来标注顶和底。这就需要一个标注工具。

标注工具在人工智能中并不是什么新鲜事儿。最早在李飞飞他们做imagenet时，就有自己开发的标注工具。现在比较有名的开源标注工具有labelImg和LabelMe等。



但是，在量化领域，目前似乎还没有这样的一款标注工具可用，因为我们提出来的这个顶底预测模型本身也是很新的研究。所以，我们需要自己开发这样的标注工具。它要能加载行情数据，生成k线，并且把我们提取的特征也标注在k线图上，以便我们观察。
 
[click]

作为Python开发者，我们可以使用 H2O Wave


[click]

 Plotly Dash
 
 
[click]
 
和Streamlit等工具来开发标注工具，这样开发出来的标注工具更美观、更强大。

但是，对于简单的任务，我们也可以直接使用jupyter notebook的ipywidgets来实现。


[click]

这就是通过 ipywidgets 来实现的一个标注工具。它共有三行。
第一行，是一组导航按钮。
第二行，显示了当前算法提取的顶和底的时间戳信息。它是两个可编辑的文本框，所以，我们可以手动增加和删除其中一些时间戳。

第三行，是一个画布，它显示了对应区间的k线图，并且画出了图像中顶和底的位置。

在这个图中，大家看第二个顶点，就是现在箭头所指的地方。算法标的顶点比实际顶点晚了两个周期。如果我们能手工标的话，


[click]


有的人会倾向于标注在涨停位置，


[click]



有的人会倾向于标在涨停后，次收收跌的位置。所以，这就是手工标注工具的作用。

[click]

现在，我们就来介绍下ipywidgets系统。

首先，我们要引入ipywidgets这个库。


[click]

在widgets模块中，存在许多控件，它们跟HTML控件基本上是一一对应关系。


[click]

比如，我们要定义一个文本输入框，就使用Text控件


[click]

要添加一个按钮，就使用Button控件。


[click]

这些控件如何排列呢？这就需要用到ipywidgets中的layout控件。 这里的HBox就是一个layout控件。它使得多个元素在水平方向上排成一行。


[click]

下面的截图就是这段代码的输出。

在我们的标注工具中，使用的layout控件还有VBox。HBox与VBox支持相互嵌套，它们自身也支持相互嵌套，因此，仅仅通过HBox和VBox，就可以实现比较复杂的布局。

除此之外，ipywidgets还提供了其它布局控件，比如


[click]

gridbox


[click]


还有很多其它布局，比喻手风琴布局，tab布局等。


[click]

每个控件都会有一些属性。绝大多数控件都会有value这个属性。在一些交互式控件中，它代表控件的输入值。这些属性往往是双向绑定的。也就是说，如果我们通过python代码修改了它们的值，那么，这个改变会在界面上自动反映出来，不需要我们做任何事情去手动更新；如果用户通过交互改变了控件的状态，比如，在这个text控件中，用户进行了输入操作，那么，它的value属性也会立刻改变，以反应最新的状态。

[click]

除了value之外，其实重要的、常用的属性可能就是layout属性。一般来说，我们可以通过layout属性来控制控件的宽度和高度，外边距，对齐、排列方式等。如果你对html和样式表有所了解，会发现这些概念不难理解。

总之，如果你要控制一个控件的显示方式，就可以尝试使用layout属性。

[click]

接下来，比较重要的是如何实现事件驱动，比如，当我们点击下一组按钮后，如何加载并显示新的区间内的k线。


[click]

每个widgets都有自己支持的一些事件。比如，响应鼠标点击。事件响应被设计为回调函数，当我们调用onclick方法时，可以传入一个事件回调函数，系统会控件的点击事件与这个回调函数进行绑定。此后，当用户点击这个控件时，回调函数就被调用，获得处理消息的机会。


[click]

我们看一下keep_button控件关联的消息处理情况。这个控件就是正中间的『记录 > 下一组』按钮。


[click]

当用户点击这个按钮时，需要读取控件的值，即value属性，进行解析，把时间戳信息保存起来。


[click]

并且更新跟显示区间相关的一个指针。
当这个指针更新后，再调用show_bars，show_bars就能读到新的显示区间的行情数据。在show_bars方法中，它会显示k线图，并且自动提取波峰与波谷信息。


[click]


消息处理函数还会接受一个参数，即事件发生时的控件实例。一般情况下，我们不需要使用这个参数。


[click]

最后讲一下第三方控件如何使用。在这个小工具中，k线图的绘制是通过plotly的Candlestick来完成的。但是, ipywidgets组件不能直接使用第三方组件，会在layout，交互上出现问题。

所以，plotly又提供了一个名为FigureWidget的组件，要通过它先把Candlestick封装一次。即使这样，要实现行情数据与显示的双向绑定还有一些困难，所以，在示例代码中，使用的是每次重新创建FigureWidget组件。

这里特别说明一下，以防大家读示例代码时，会觉得奇怪。当然这里也可能存在双向绑定，从而不需要反复创建FigureWidget的方法。大家可以自己探索下。

这个工具在我们今天的课程中不会使用，但如果我们要做出高质量的模型，可能就需要开发一些类似的工具，把自动标注和手动标注结合起来。

接下来，我们就进入今天的正题，如何做一个顶底预测的模型。
-->
