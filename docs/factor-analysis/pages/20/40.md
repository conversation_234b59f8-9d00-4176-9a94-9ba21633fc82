---
clicks: 14
---

<NoteCell layout='horizontal' outputWidth="40%" class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 1}'>

```python{all|255|291,298|179,202|174-177|256-276|188-189|189|190-191|256-263|265-272|273|274-283|285|286}{at: 1, maxHeight: 450}
import datetime
import logging
from numpy import ndarray
from numpy.typing import ArrayLike, NDArray
import os
import pickle
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from numpy.lib.stride_tricks import as_strided
import lightgbm as lgb
from zigzag import peak_valley_pivots


logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)
mlogger = logging.getLogger("matplotlib")
# supress matplotlib info level logging, which is anoying
mlogger.setLevel(logging.WARNING)


class WaveRiderBase:
    def __init__(self):
        self._model_ = None
        self._name_: str | None = None
        self._desc_: str | None = None

        self._features_ = []
        # for encode/decode categorical features
        self._label_encoders_ = {}

    @property
    def model(self):
        return self._model_

    @property
    def features(self):
        return self._features_

    def save(self, path: str, auto_rename: bool = True, name= None):
        if self._model_ is None:
            raise ValueError("请先调用fit方法训练模型")

        if not os.path.exists(os.path.expanduser(path)):
            raise ValueError("保存路径不存在")

        name = name or self._name_
        if name is None:
            raise ValueError("请指定保存文件名")

        file = os.path.join(path, f"{name}.pkl")
        if os.path.exists(file):
            if auto_rename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"{name}_{timestamp}.pkl"
                file = os.path.join(path, name)
                assert not os.path.exists(file)
                logger.warning("保存路径已存在同名文件，已自动重命名为%s", name)
            else:
                raise ValueError("保存路径已存在同名文件，请指定指定文件名")

        with open(file, "wb") as f:
            pickle.dump(
                {
                    "model": self._model_,
                    "name": self._name_,
                    "desc": self._desc_,
                    "save_time": datetime.datetime.now(),
                },
                f,
            )

    def load(self, file_path: str):
        with open(file_path, "rb") as f:
            data = pickle.load(f)
            self._model_ = data["model"]
            self._name_ = data["name"]
            self._desc_ = data["desc"]
            logger.info("加载模型%s成功。", self._name_)

    def rolling_time_series(self, ts: NDArray, win: int) -> NDArray:
        stride = ts.strides
        shape = (len(ts) - win + 1, win)
        strides = stride + stride
        return as_strided(ts, shape, strides)

    def encode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        if name in self._label_encoders_:
            logger.info("已经存在%s特征编码器，直接复用")
            return self._label_encoders_[name].transform(feature)

        self._label_encoders_[name] = LabelEncoder()
        return self._label_encoders_[name].fit_transform(feature)

    def decode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        if name not in self._label_encoders_:
            raise ValueError(f"不存在{name}特征编码器，无法进行解码")

        return self._label_encoders_[name].inverse_transform(feature)

    def train_test_split(
        self,
        data: pd.DataFrame,
        features: List[str],
        group_id: str | None = None,
        cuts=(0.7, 0.2),
    ) -> Tuple[pd.DataFrame | pd.Series]:
        logger.info("trained with features: %s", features)

        def cut_time_series(group, cuts):
            itrain = int(len(group) * cuts[0])
            ival = itrain + int(len(group) * cuts[1])

            return (group.iloc[:itrain], 
                    group.iloc[itrain:ival], 
                    group.iloc[ival:])

        if group_id is None:
            train, valid, test = cut_time_series(data, cuts)
            return (
                train[features],
                valid[features],
                test[features],
                train["target"],
                valid["target"],
                test["target"],
            )

        # 按group_id进行分组，在组内划分，最后合并成与data同样shape的DataFrame
        train, valid, test = [], [], []
        for item in (data.groupby(level=group_id)
                         .apply(cut_time_series, cuts=cuts)):
            train.append(item[0])
            valid.append(item[1])
            test.append(item[2])

        train = pd.concat(train)
        valid = pd.concat(valid)
        test = pd.concat(test)

        return (
            train[features],
            valid[features],
            test[features],
            train["target"],
            valid["target"],
            test["target"],
        )

    def peaks_and_valleys(self, ts, up_thres=None, down_thres=None):
        # 分类任务标签需要从0开始
        if len(ts) < 5 * 4:
            return [1] * len(ts)

        mas = moving_average(ts, 5, padding=False).astype(np.float64)

        if up_thres is None:
            pct = mas[1:] / mas[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std
            down_thres = -2 * std

        if down_thres is None:
            pct = mas[1:] / mas[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std
            down_thres = -2 * std

        pvs = peak_valley_pivots(mas, up_thres, down_thres) + 1
        pvs[0] = 1
        pvs[-1] = 1
        return np.insert(pvs, 0, [1] * 4)

    def features_and_labels(self, barss) -> pd.DataFrame:
        data = (barss.groupby("asset", level=1)
                     .apply(self.extract_features_and_labels))
        return data.droplevel(0)

    def train(
        self,
        barss: pd.DataFrame,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
    ):
        params = {"random_state": 42}

        train_params["objective"] = "multiclass"
        train_params["metric"] = "multi_logloss"
        # 多分类时，需要设置分类数
        train_params["num_class"] = 3

        if "feval" in train_params:
            feval = train_params["feval"]
            del train_params["feval"]
        else:
            feval = None

        params.update(train_params)
        evals_result = {}

        data = self.features_and_labels(barss)
        X_train, X_val, X_test, y_train, y_val, y_test = self.train_test_split(
            data, self.features
        )

        train_data = lgb.Dataset(X_train, label=y_train)
        valid_data = lgb.Dataset(X_val, label=y_val)

        if self._model_ is not None:
            logger.warning("Model already trained, retraining...")

        self._model_ = lgb.train(
            params,
            train_data,
            num_boost_round=epochs,
            valid_sets=[valid_data],
            feval=feval,
            categorical_feature=list(self._label_encoders_.keys()),
            callbacks=[
                lgb.early_stopping(early_stop_round),
                lgb.record_evaluation(evals_result),
            ],
        )

        report = self.eval_model(X_test, y_test)
        return self.model, report

    def predict(self, X: ArrayLike) -> ndarray:
        assert self._model_ is not None, "Model not trained"
        return self._model_.predict(X)

    def eval_model(self, X_test, y_true):
        """对模型进行评估"""
        y_pred = self.predict(X_test)
        y_pred = np.argmax(y_pred, axis=1)

        class_report = classification_report(y_true, y_pred, output_dict=True)
        cm = confusion_matrix(y_true, y_pred)

        plt.figure(figsize=(5, 3))

        sns.heatmap(cm, annot=True, fmt="d", cmap="Blues")

        plt.xlabel("Predicted labels")
        plt.ylabel("True labels")
        plt.title("Confusion Matrix")

        # 显示图形
        plt.show()

        return pd.DataFrame(class_report).transpose()


class WaveRiderV1(WaveRiderBase):
    def extract_features_and_labels(self, group) -> pd.DataFrame:
        rsi_win = 6
        if len(group) < rsi_win * 3:
            group["rsi"] = np.nan
        else:
            rsi = ta.RSI(group["close"], rsi_win)
            rsi[: rsi_win * 3] = np.nan
            group["rsi"] = rsi[rsi_win * 3 :]

        group["uo"] = ta.ULTOSC(
            group.high,
            group.low,
            group.close,
            timeperiod1=7,
            timeperiod2=14,
            timeperiod3=28,
        )
        group["wr"] = (group.close - group.high) / (group.high - group.low)
        group["wr_3_high"] = (
            group["high"]
            / group["high"].rolling(window=3, min_periods=3).max().shift(1)
            - 1
        )
        group["wr_3_low"] = (
            group["low"] / group["low"].rolling(window=3, min_periods=3)
                                       .min().shift(1)
            - 1
        )

        self._features_ = ["wr", "wr_3_high", "wr_3_low", "uo", "rsi"]
        group["target"] = self.peaks_and_valleys(group["close"].values)

        return group[self.features + ["target"]]


rider = WaveRiderV1()

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 31)

np.random.seed(78)
barss = load_bars(start, end)
modle, cr = rider.train(barss, 100)
cr
```
</NoteCell>


<FlashText v-click="[7,8]"
           class='abs mt-40 ml-180 w-full text-3xl'>

multi-error <br><br> auc-mu
</FlashText>

<!--

<run></run>

这个模型在构建上，与上一章有类似的地方。


[click]


大致的框架是，使用者要从基类派生出一个子类，提供一个名为 extract_features_and_labels的实现。


[click]

然后，我们在子类的实例上调用train方法，传入行情数据。


[click]

train方法由基类提供。它会调用名为 features_and_labels的方法，实现特征和标签的提取。features_and_labels仍然是基类的方法，基类提供了实现。


[click]

这个方法会对传入的行情数据，按asset进行分组，再对每个分组调用子类实现的extract_features_and_labels方法。

当然，这样做是有局限的。这样使得我们无法利用截面信息。如果大家在训练自己的模型时，需要利用截面信息，需要修改这一部分的逻辑。


[click]

子类的重点是如何提取特征。但在此之前，基类的训练方法有几个参数会与上一章有所不同，我们要先交代一下。

[click]

我们会用0, 2和1 作为标签，分别表示波谷、波峰和中间状态。因此一共有三个标签，这是一个多分类任务，所以，目标函数要指定为multiclass

与之对应的，评价指标是multi_logloss。


[click]

此外，还有multi_error和auc_mu可以使用。


[click]

当目标函数是多分类时，必须要指定分类数。


[click]

下面，我们就看如何为模型提供数据。这是加入了rsi的特征。


[click]

加入ultra oscillator


[click]

这个特征实际是判断当天是否有长上影线。


[click]

判断是否触及近期高点、低点不破


[click]

告诉基类，这个模型中，哪些列是特征


[click]

打标签。


现在我们来解读一下结果。

从日志可以看出，训练还没有完成，是被限制了。这实际上是收敛速度不够快。

我们主要看看混淆矩阵。在实际标签为0，也就是波谷的位置，模型预测到了两个，其余1007个预测成了1；在实际标签为2，也就是波峰的位置，模型一个也没找出来，全部归类成了类别1。

而在实际标签为1，也就是既不是波峰、也不是波谷的位置处，有13处被预测为波谷，这会导致我们提前抄底，导致损失；有3处被预测为波峰，这会导致我们提前止盈，导致利润受损。

总得来说，这个结果不太理想。下面，我们就看看如何改进。


-->
