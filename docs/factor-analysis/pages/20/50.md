---
clicks: 3
---

<NoteCell init class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1='{ scale: 0}'>

```python
import datetime
import logging
from numpy import ndarray
from numpy.typing import ArrayLike, NDArray
import os
import pickle
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from numpy.lib.stride_tricks import as_strided
import lightgbm as lgb
from zigzag import peak_valley_pivots


logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)
mlogger = logging.getLogger("matplotlib")
# supress matplotlib info level logging, which is anoying
mlogger.setLevel(logging.WARNING)


class WaveRiderBase:
    def __init__(self):
        self._model_ = None
        self._name_: str | None = None
        self._desc_: str | None = None

        self._features_ = []
        # for encode/decode categorical features
        self._label_encoders_ = {}

    @property
    def model(self):
        return self._model_

    @property
    def features(self):
        return self._features_

    def save(self, path: str, auto_rename: bool = True, name: str | None = None):
        """将模型保存到指定路径。

        如name未指定，将使用模型名作为文件名。如果同名文件已存在，将自动重命名为当前时间戳。文件名会自动加pkl后缀
        Args:
            path: 保存路径
            auto_rename: 如果同名文件已存在，是否自动重命名
            name: 保存文件名
        """
        if self._model_ is None:
            raise ValueError("请先调用fit方法训练模型")

        if not os.path.exists(os.path.expanduser(path)):
            raise ValueError("保存路径不存在")

        name = name or self._name_
        if name is None:
            raise ValueError("请指定保存文件名")

        file = os.path.join(path, f"{name}.pkl")
        if os.path.exists(file):
            if auto_rename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                name = f"{name}_{timestamp}.pkl"
                file = os.path.join(path, name)
                assert not os.path.exists(file)
                logger.warning("保存路径已存在同名文件，已自动重命名为%s", name)
            else:
                raise ValueError("保存路径已存在同名文件，请指定指定文件名")

        with open(file, "wb") as f:
            pickle.dump(
                {
                    "model": self._model_,
                    "name": self._name_,
                    "desc": self._desc_,
                    "save_time": datetime.datetime.now(),
                },
                f,
            )

    def load(self, file_path: str):
        with open(file_path, "rb") as f:
            data = pickle.load(f)
            self._model_ = data["model"]
            self._name_ = data["name"]
            self._desc_ = data["desc"]
            logger.info("加载模型%s成功。", self._name_)

    def rolling_time_series(self, ts: NDArray, win: int) -> NDArray:
        """生成rolling time series

        Args:
            ts: 一维时间序列
            win: 窗口大小
        Returns:
            返回shape为（len(ts) - win + 1, win）的二维numpy数组
        """
        stride = ts.strides
        shape = (len(ts) - win + 1, win)
        strides = stride + stride
        return as_strided(ts, shape, strides)

    def encode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """将类别特征编码。

        类别特征可能需要编码解码。此时需要label_encoders属性保存编码器。此方法会自动保存编码器到label_encoders属性中。
        """
        if name in self._label_encoders_:
            logger.info("已经存在%s特征编码器，直接复用")
            return self._label_encoders_[name].transform(feature)

        self._label_encoders_[name] = LabelEncoder()
        return self._label_encoders_[name].fit_transform(feature)

    def decode_feature(self, name: str, feature: pd.Series) -> pd.Series:
        """解码经类别编码器编码的序列"""
        if name not in self._label_encoders_:
            raise ValueError(f"不存在{name}特征编码器，无法进行解码")

        return self._label_encoders_[name].inverse_transform(feature)

    def train_test_split(
        self,
        data: pd.DataFrame,
        features: List[str],
        group_id: str | None = None,
        cuts=(0.7, 0.2),
    ) -> Tuple[pd.DataFrame | pd.Series]:
        """对时间序列进行train, valid和test子集划分。

        与sklearn的train_test_split不同，此方法会根据时间进行划分，而不是随机划分。

        请事前对data进行排序。如果group_id为None，则直接对data进行划分；否则，先按group_id进行分组，
        再在组内进行划分，最后合并成与data同样shape的DataFrame

        Args:
            data: 时间序列数据，应包含features中的所有列，以及名为target的列
            group_id: 根据此列进行分组，在组内划分子集
            features: 在data中，哪些列是特征列
            cuts: 训练集、验证集、测试集的比例

        Returns:
            返回X_train, X_valid, X_test, y_train, y_valid, y_test
        """
        logger.info("trained with features: %s", features)

        def cut_time_series(group, cuts):
            itrain = int(len(group) * cuts[0])
            ival = itrain + int(len(group) * cuts[1])

            return (group.iloc[:itrain], group.iloc[itrain:ival], group.iloc[ival:])

        if group_id is None:
            train, valid, test = cut_time_series(data, cuts)
            return (
                train[features],
                valid[features],
                test[features],
                train["target"],
                valid["target"],
                test["target"],
            )

        # 按group_id进行分组，在组内划分，最后合并成与data同样shape的DataFrame
        train, valid, test = [], [], []
        for item in data.groupby(level=group_id).apply(cut_time_series, cuts=cuts):
            train.append(item[0])
            valid.append(item[1])
            test.append(item[2])

        train = pd.concat(train)
        valid = pd.concat(valid)
        test = pd.concat(test)

        return (
            train[features],
            valid[features],
            test[features],
            train["target"],
            valid["target"],
            test["target"],
        )

    def peaks_and_valleys(self, ts, up_thres=None, down_thres=None):
        # 分类任务标签需要从0开始
        if len(ts) < 5 * 4:
            return [1] * len(ts)

        mas = moving_average(ts, 5, padding=False).astype(np.float64)

        if up_thres is None:
            pct = mas[1:] / mas[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std
            down_thres = -2 * std

        if down_thres is None:
            pct = mas[1:] / mas[:-1] - 1
            std = np.std(pct)
            up_thres = 2 * std
            down_thres = -2 * std

        pvs = peak_valley_pivots(mas, up_thres, down_thres) + 1
        pvs[0] = 1
        pvs[-1] = 1
        return np.insert(pvs, 0, [1] * 4)

    def features_and_labels(self, barss) -> pd.DataFrame:
        data = barss.groupby("asset", level=1).apply(self.extract_features_and_labels)
        return data.droplevel(0)

    def train(
        self,
        barss: pd.DataFrame,
        epochs: int,
        early_stop_round: int = 100,
        train_params: dict = {},
    ):
        params = {"random_state": 42}

        train_params["objective"] = "multiclass"
        train_params["metric"] = "multi_logloss"
        # 多分类时，需要设置分类数
        train_params["num_class"] = 3

        if "feval" in train_params:
            feval = train_params["feval"]
            del train_params["feval"]
        else:
            feval = None

        params.update(train_params)
        evals_result = {}

        data = self.features_and_labels(barss)
        X_train, X_val, X_test, y_train, y_val, y_test = self.train_test_split(
            data, self.features
        )

        train_data = lgb.Dataset(X_train, label=y_train)
        valid_data = lgb.Dataset(X_val, label=y_val)

        if self._model_ is not None:
            logger.warning("Model already trained, retraining...")

        self._model_ = lgb.train(
            params,
            train_data,
            num_boost_round=epochs,
            valid_sets=[valid_data],
            feval=feval,
            categorical_feature=list(self._label_encoders_.keys()),
            callbacks=[
                lgb.early_stopping(early_stop_round),
                lgb.record_evaluation(evals_result),
            ],
        )

        report = self.eval_model(X_test, y_test)
        return self.model, report

    def predict(self, X: ArrayLike) -> ndarray:
        assert self._model_ is not None, "Model not trained"
        return self._model_.predict(X)

    def eval_model(self, X_test, y_true):
        """对模型进行评估"""
        y_pred = self.predict(X_test)
        y_pred = np.argmax(y_pred, axis=1)

        class_report = classification_report(y_true, y_pred, output_dict=True)
        cm = confusion_matrix(y_true, y_pred)

        plt.figure(figsize=(5, 3))

        sns.heatmap(cm, annot=True, fmt="d", cmap="Blues")

        plt.xlabel("Predicted labels")
        plt.ylabel("True labels")
        plt.title("Confusion Matrix")

        # 显示图形
        plt.show()

        return pd.DataFrame(class_report).transpose()
```
</NoteCell>

<NoteCell layout='horizontal' class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 1}'>

```python{all|32-44|45-47|all}{maxHeight: 450 }
class WaveRiderV2(WaveRiderBase):
    def features_and_labels(self, group) -> pd.DataFrame:
        rsi_win = 6
        if len(group) < rsi_win * 3:
            group["rsi"] = np.nan
        else:
            rsi = ta.RSI(group["close"], rsi_win)
            rsi[: rsi_win * 3] = np.nan
            group["rsi"] = rsi[rsi_win * 3 :]

        group["uo"] = ta.ULTOSC(
            group.high,
            group.low,
            group.close,
            timeperiod1=7,
            timeperiod2=14,
            timeperiod3=28,
        )
        group["wr"] = (group.close - group.high) / (group.high - group.low)
        group["wr_3_high"] = (
            group["high"]
            / group["high"].rolling(window=3, min_periods=3).max().shift(1)
            - 1
        )
        group["wr_3_low"] = (
            group["low"] / group["low"]
            .rolling(window=3, min_periods=3)
            .min().shift(1)
            - 1
        )

        group["d1"] = (group.close
                            .pct_change()
                            .rolling(window=3, min_periods=3)
                            .mean()
                      )
        group["d2"] = (
            group.close
                 .pct_change()
                 .diff()
                 .rolling(window=3, min_periods=3)
                 .mean()
        )
        
        self._features_ = ["wr", "wr_3_high", "wr_3_low", 
                           "uo", "rsi", "d2", "d1"]
        group["target"] = self.peaks_and_valleys(group["close"].values)

        return group[self.features + ["target"]]

rider = WaveRiderV2()

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 31)

np.random.seed(78)
barss = load_bars(start, end)
modle, cr = rider.train(barss, 100)
cr
```
</NoteCell>

<!--

<run></run>

这是第二版的实现。要增加一个新的实现，我们只需要改写这个方法即可。

[click]


我们增加了一阶动量和二阶动量，并且都只使用了最近3个周期的窗口。较短的窗口对短期的趋势会更灵敏。


[click]

同样的，我们告诉基类，这一版的特征列的名字。

这是提取标签，暂时保持不变。


[click]

现在我们看一下结果。从混淆矩阵来看，改进比较明显。现在，它找到的波谷数增加到了6个，找到的波峰数增加到了1个。

大家可能会想，这个改进是不是也太小了？实际上不是，因为我们找的是波峰和波谷，它的特点是，这样的样本本来就少，但是，一旦你找准了，有可能是一波C浪，这样的利润就相当可观。

当然，从日志来看，训练还没有最后收敛。所以，加大训练轮数，有可能得到更好的结果。


-->
