---
clicks: 3
---

<div class='abs mt-15' v-motion
     :enter='{opacity: 1}'
     :click-3='{opacity: 0}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/20250214120607.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

<v-drag-arrow color='red' pos="336,66,-22,42"/>
<v-drag-arrow color='red' pos="336,66,24,77"/>
<v-drag-arrow color='red' pos="334,67,76,11"/>
<v-drag-arrow color='red' pos="337,68,240,72"/>
<v-drag-arrow color='red' pos="336,67,205,101"/>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>

<v-drag-arrow color='red' pos="326,92,82,271"/>
<v-drag-arrow color='red' pos="326,92,87,-16"/>
<v-drag-arrow color='red' pos="711,202,-77,-72"/>
<v-drag-arrow color='red' pos="711,202,-77,167"/>

</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-3='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/20250214123359.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-3='{opacity: 1}'>

<v-drag-arrow color='red' pos="394,226,76,-126"/>
<v-drag-arrow color='red' pos="394,226,157,-96"/>
</div>

<!--
在这张图中，我们从形态上可以看出一些见顶的信号，比如，高开大阴线。

[click]

在这张图中，所有的高开大阴线，其后都跟随了一到两天的调整，有的甚至成为局部的高点。

[click]

从RSI特征来看，3月9号的RSI与6月10日的RSI都是区间内的最高值。所以，3月9号形成局部高点后，也通过RSI记录下这支股的多空力道平衡点，大约在80多一些。然后到了6月10日，当天最高点RSI达到或者超过了3月9日的RSI，所以，股价随后也进行了调整。


[click]

在这张图中，我们看到，如果5日均线发生拐头，那么可能会形成局部峰值或谷底。如果等均线完全拐头，可能会有点晚，但是，我们知道，在此之前，均线的二阶导应该在一段时间内为负数，最新一期的一阶导也会是小于等于零，这样均线就比较确定是拐头了。所以，这也是一个可以预测顶底的特征。

我们还可以把其它看盘经验也当成特征，让机器学习来验证这些经验是否准确，实现的概率大还是小。

那么，我们要如何实现这个模型呢？

我们可以把每一个看盘经验当成一个特征，也就是训练数据中的一列，这个特征可以是离散的，比如，高开大阴线就是一个离散特征，它的取值要么是1，要么是0。当然，我们也可以把它处理成连续的，毕竟，阴线和阳线容易定义，而什么是大、多大才是大就没那么容易定义了。

这是如何构成特征。那如何生成标签呢？这是与上一章不同的地方。我们可以借助自动化算法来实现顶底标注，然后把底标记为0，顶标记为2，其它标记为1。

那么，要如何查找k线图中的顶和底呢？

-->
