---
clicks: 7
---

<div class='abs' v-motion
     :enter='{opacity: 1}'
     :click-4='{opacity:0}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/peaks-valleys-drawing.jpeg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs ml-105 mt-30' v-motion
     :enter='{opacity: 0}'
     :click-2-4='{opacity: 1}'>
P0
</div>
<FlashText v-click="[1,2]"
           class='abs mt-1/3 text-center w-full text-3xl'>

find_peaks <br><br> argrelextrema <br><br> find_peaks_cwt
</FlashText>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-2-4='{opacity: 1}'>

<v-drag-arrow color='red' pos="430,220,-72,0"/>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>

<v-drag-arrow color='red' pos="439,220,111,1"/>
</div>


<FlashText v-click="[4,5]"
           class='abs mt-40 text-center w-full text-4xl'>

ZigZag
</FlashText>

<FlashText v-click="[4,5]"
           class='abs mt-60 text-center w-full text-3xl'>

peak_valley_pivots(close, up_thresh, down_thresh)

</FlashText>

<NoteCell layout='horizontal' outputMt="3rem" outputWidth="60%" class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-5='{ scale: 1}'>

```python{all|4|6-9}{at:6}
from zigzag import peak_valley_pivots

def peaks_and_valleys(ts, up_thres=None, down_thres=None):
    mas = moving_average(ts, 5, padding=False).astype(np.float64)

    if up_thres is None:
        pct = mas[1:] / mas[:-1] - 1
        std = np.std(pct)
        up_thres = 2 * std

    if down_thres is None:
        pct = mas[1:] / mas[:-1] - 1
        std = np.std(pct)
        down_thres = -2 * std

    pvs = peak_valley_pivots(mas, up_thres, down_thres)
    pvs[0] = 0
    pvs[-1] = 0
    return np.insert(pvs, 0, [0] * 4)

def show_peaks_and_valleys(ts, up_thres=None, down_thres=None):
    ma = moving_average(ts, 5, padding=True).astype(np.float64)

    pvs = peaks_and_valleys(ts, up_thres, down_thres)

    plt.plot(np.arange(len(ma)), ts, color="#49DBF5", 
             label="close", alpha=0.2)
    plt.plot(np.arange(len(ma)), ma, color="#D77AF5", label="ma")

    # Plot peaks.
    peak_x = np.argwhere(pvs > 0).flatten()
    peak_y = ts[peak_x]
    plt.plot(peak_x, peak_y * 1.01, "v", 
             label="Peaks", color="#8D50A1")

    # Plot valleys.
    valley_x = np.argwhere(pvs < 0).flatten()
    valley_y = ts[valley_x]
    plt.plot(valley_x, valley_y * 0.995, "^", 
             label="Valleys", color="#36A2B5")

    plt.gca().spines.top.set_visible(False)
    plt.gca().spines.right.set_visible(False)
    plt.legend()

payh = "000001.XSHE"
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
bars = load_bars(start, end, ("000001.XSHE",)).xs(payh, level=1)

show_peaks_and_valleys(bars["close"])
```
</NoteCell>

<!--
顶底查找算法是数字信号处理中的常用算法。在scipy库中的signal模块下，有很多此类的实现，

[click]

比如find_peaks， argrelextrema等等，此外还有基于小波实现的一些算法。

[click]

这些算法的基本思想是，在指定的点p0上，如果该点大于前后两个点，分别都超过了阈值，则认为该点是一个波峰。


[click]


我们还可以对该算法进行扩展。除了看前后各一个点之外，还可以是向前查找n个点，向后查找m个点。n+m+1一般被称为波峰的窗口。

这类算法一般只找波峰，不找波谷。因此为了查找波谷，我们还要对时间序列适当进行变换处理。

[click]

不过，这类算法一般应用在平稳时间序列上，所以，算法对阈值的要求一般是使用绝对值。由于我们要处理不同的证券品种，同一品种在不同的时间段，波动范围也很不一致，所以，这些算法并不太好用。在这里比较适用的算法是来自zigzag库的一个函数，叫做peaks_valley_pivots。

它的特点是，up_thresh和down_thresh都是相对值，而不是绝对值，这在我们的场景下更为适用。

[click]

<run></run>

peaks_valley_pivots有一些特性需要介绍下。第一，它的波峰和波谷总是成对出现的；第二，它总是给序列的第一个元素和最后一个元素都打上标记。我们要注意这两处的标记是不稳定的。比如，如果序列的最后一个元素标记成为1，这表明它是一个波峰，但如果此后继续上涨，再用peaks_valley_pivots来检测，peaks_valley_pivots就会把它标记成为0。

关于这段代码，我们有两点要讲解。


[click]


第一点，由于使用的移动平均，所以，它寻找出来的顶和底是有延迟的。我们也许需要对它寻找出来的顶和底进行修正。


[click]


第二点，如何确定up threshold和down threshold? 大家注意看第6~9行，我们先是将ts求5日均线，这个过程是去掉噪声。然后，我们计算出每日涨跌幅，以这个涨跌幅的2倍标准差，作为up threshold和down threshold。这里实际上暗含了把波动当成正态分布的假设。所以，这个算法，应用在指数上，寻找指数的顶和底，会非常准。

-->
