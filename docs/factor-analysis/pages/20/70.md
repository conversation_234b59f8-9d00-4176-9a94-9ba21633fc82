---
clicks: 9
layout: two-cols
right: 60%
---

<div class='abs' v-motion
     :enter='{opacity: 1}'
     :click-10='{opacity: 0}'>

## <span v-motion :enter='{textShadow: ""}' :click-1-3='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>样本平衡</span>
## <span v-motion :enter='{textShadow: ""}' :click-6-7='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>标签/特征校准</span>
## <span v-motion :enter='{textShadow: ""}' :click-7-8='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>多周期及微观数据</span>
## <span v-motion :enter='{textShadow: ""}' :click-8-9='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>截面数据</span>
## <span v-motion :enter='{textShadow: ""}' :click-9-10='{textShadow: "1px 1px 3px rgba(0,0,0,0.3)"}'>市场氛围</span>
</div>

::right::

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-1-5='{opacity: 1}'>

```python{all|7,11-15|17|18}{at:2}
def train(
    self,
    barss: pd.DataFrame,
    epochs: int,
    early_stop_round: int = 100,
    train_params: dict = {},
    peak_weight: int = 1
):
    ...

    class_weights = {
        0: peak_weight,
        1: 1,
        2: peak_weight,
    }

    sample_weights = np.array([class_weights[y] for y in y_train])
    train_data = lgb.Dataset(X_train, label=y_train, weight=sample_weights)
```
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-5-6='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/v3-peak-weight-3.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<v-drag-arrow v-click='[4,5]' color='red' pos="500,257,-28,56"/>


<div class='abs mt-10' v-motion
     :enter='{opacity: 0}'
     :click-7-8='{opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/20250216151844.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-9-10='{opacity: 1}'>
<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/20250216153755.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{opacity: 1}'>
<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/02/20250214120607.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{opacity: 1}'>

<v-drag-arrow color='red' pos="326,92,91,185"/>
<v-drag-arrow color='red' pos="326,92,92,-25"/>
<v-drag-arrow color='red' pos="100,100,122,-86"/>
<v-drag-arrow color='red' pos="100,100,119,174"/>
</div>

<!--
从v1到v3版，我们在数据质量上都面临着一个问题，即样本存在显著的不平衡。

纠正样本不平衡的方法，我们在前面介绍过imblearn这个库，通过重采样的方法来实现样本平衡。

这里我们介绍另一种方法，即通过调整各个分类的权重，使得模型在学习时，更多关注少数类别的样本。


[click]

这个方法是在构造训练集时，传入weight参数。


[click]

首先，我们定义各个类别的参数。这里的peak_weight由调用者传入，以方便进行参数优化。每一个波峰都会对应着一个波谷，所以，两者数量一致，可以共享同一个权重。我们把其它状态的权重设置为1，这样如果peak_weight=1，就相当于没有修改权重；如果peak_weight大于1，则加大了少数类别的权重。


[click]

然后我们生成与y_train一一对应的权重序列。

[click]

在构建train_data时，我们多传入一个参数，即weight，把刚刚生成的权重序列传入进来。

注意，这个权重与校验数据集无关，更与测试数据集无关。它只在训练时需要。

[click]

这是在v3模型下，将Peak_weight设置为3时的结果。我们看到召回率进一步上升了。当然，这个权得不能设得过大，否则召回率的提升，会以精确率的下降为代价。


[click]

这部分我们之前已经讲过了，标签和特征一定要对齐。并且通过调整标签，收到了明显的效果。这是一个看起来不值一提，但实际工作中，我们常常不加思索地犯这样的错误。

比如，我们一开始就提到了RSI特征，并且在模型中也运用了RSI特征。但实际上，我们运用RSI特征的方法，几乎是不生效的。

在这里，我们认为，RSI代表了投资者对收益和风险的一种平衡。每支股票都有自己的RSI顶。前一个波峰记录了这个RSI顶，此后当RSI再次触及这个顶的附近时，就有可能发生反转。

但是，大家去看看我们的示例代码，并没有反映我们这里所说的关系。它没有用上一次波峰时的RSI作为参考。示例代码没有这样做，一方面是出于简化的考虑，同时也给大家留下了优化空间。


[click]

在上涨一段时间后，有时候会做短期调整，然后继续上涨，有时候则会下跌，较长时间内处于熊市。实际上，如果我们用多周期来分析，就更容易解释这种现象。当我们观察到日线上涨一段时间，进行调整之后，还能继续上涨，这很可能是周线或者月线级别上，个股趋势还没走完；如果调整之后转跌，则有可能是在更大级别上，趋势也已经走完了。

因此，我们在提取特征时，也可以考虑更大级别和更次级别的周期上的特征。比如，如果我们从日线的角度看，无法理解股价上攻时，为什么总是无法突破，那就可能是在周线或者月线上，上方存在着均线压力。这些人的资金量更大，不消耗掉他们的筹码，是很难突破的。

在微观级别上也存在很多有用的特征。比如，我们前面提到的高开大阴线。这是比所有技术指标都要低一个级别的特征，因为它只需要两个周期的数据就可以构造出来，但是，从我们前面的举例来看，有效性是很强的。


[click]

我们在前面讲了很多因子。在因子分析中，它们的表现是很有效的，在上一个模型中，一阶、二阶导因子也非常有效。但在这个模型中，大家会不会感觉这些因子没那么有效了？比如，我们用了RSI和UO，但在第一版，因子有效性几乎为零。

为什么会出现这样的情况？因为我们做因子分析时，用的不是这些因子的绝对值，而是它们的排名。这是alphalens的工作方式。这个市场上，主力资金常常是照着教科书工作的。但我们在模型中使用的是绝对值，而不是排名。这里用截面数据会不会更好呢？大家可以自己尝试下。

[click]

多数个股是随市场大流波动的。当市场预期走强时，资金流入股市，就会找一些补涨的个股。所以，市场在前，这些个股在后。它们可能滞后于市场几分钟，也可能滞后几天。所以，市场氛围指标在分类任务中也有效。

这些指标有每日涨跌家数比，涨停家数比，大盘技术指标等等。
-->
