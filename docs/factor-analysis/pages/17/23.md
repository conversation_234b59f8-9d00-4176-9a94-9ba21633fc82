---
clicks: 5
---

<div class='abs ml-100 mt-50' v-motion
     :enter='{scale: 1}'
     :click-1='{scale: 0}'>

$$A \approx 10\times B$$

</div>

<div class='abs ml-100 mt-50' v-motion
     :enter='{scale: 0}'
     :click-1-2='{scale: 1}'>

$$A \approx 10\times B$$

$$A - B \approx 0.9 \times A$$
</div>

<NoteCell outputMt="0" layout='horizontal' class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-2-6='{ scale: 1}'>

```python{all|15-18|20-22|23-27}{at: 3}
import statsmodels.api as sm
from statsmodels.tsa.stattools import adfuller, coint

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 30)

gsyh = "601398.XSHG"
jsyh = "601939.XSHG"

barss = load_bars(start, end, (gsyh, jsyh))

pair1 = barss.xs(gsyh, level=1).close
pair2 = barss.xs(jsyh, level=1).close

def hedge_ratio(price1: NDArray, price2: NDArray) -> float:
    X = sm.add_constant(price1)
    model = sm.OLS(price2, X).fit()
    return model.params[1]

hr = hedge_ratio(pair1, pair2)
print(f"hedge_ratio为:{hr:.2f}")
spreads = pair2 - pair1 * hr
result = adfuller(spreads)
if result[1] < 0.05:
    print(f"p-value: {result[1]:.2f} < 0.05, 平稳序列")
else:
    print(f"p-value: {result[1]:.2f} > 0.05 非平稳序列")
```
</NoteCell>


<!--

现在，我们知道了平稳序列有很多好的特性。而股价序列并不是平稳序列，但可以通过价差来构造出一个平稳序列。

但是，应该如何构造呢？是直接相减吗？

答案是No。这里存在一个对冲比的概念。

假设A和B是两支股票的价格序列，并且A的价格大约是B的10倍。


[click]

那么，A与B的价差就是A的0.9倍。这是一个线性变换。因为A是非平稳序列，所以，0.9倍的A也是非平稳序列。A与B之间的那点随机性，无法与0.9倍的A进行比较。

所以，我们要计算出A与B之间的对冲比，然后用A减去B与对冲比的积，这样才能完全对冲掉A与B之间走势一致性的部分，只留下随机噪声。

[click]

<run></run>

这段代码演示了如何计算对冲比和构建价差。在最后，我们还将进行平稳性检验，以确保结果是正确的。

[click]

对冲比要通过线性回归来计算。在线性回归的结果中，0号元素是截距，1号元素是斜率，在这个场景下，就是对冲比。


[click]

在得到对冲比之后，我们使用这个公式来计算价差。注意这里约定俗成的写法是pair2 - pair1 * hr，如果你写反了，那么后续在利用这个结果时，就会不一样，这是我们要注意的地方。

[click]

这是在对生成的价差进行ADF检验。

现在，我们就构造出来了一个完美的平稳序列。由于它的均值、标准差等特性不随时间变化，所以，我们就可以在spread偏离均值的时候，做空其中一支，做多另外一支。此时无论市场走势如何，都不会影响到我们的收益，因为风险被完美的对冲掉了。
-->
