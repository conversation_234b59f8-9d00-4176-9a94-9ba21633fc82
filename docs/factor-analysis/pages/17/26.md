---
clicks: 1
---

<NoteCell outputMt =  '0' layout='horizontal' class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-2='{ scale: 0}'>

```python{all|1,13}{at:1}
from statsmodels.tsa.stattools import coint

start = datetime.date(2021, 1, 1)
end = datetime.date(2023, 12, 30)

gsyh = "601398.XSHG"
jsyh = "601939.XSHG"
barss = load_bars(start, end, (gsyh, jsyh))

pair1 = barss.xs(gsyh, level=1).close
pair2 = barss.xs(jsyh, level=1).close

tstat, p_value, *_ = coint(pair1, pair2)

if p_value < 0.05:
    print(f"p-value: {p_value:.2f}, 协整序列")
else:
    print(f"p-value: {p_value:.2f}, 非协整序列")
```
</NoteCell>

<!--


<run></run>

在statsmodels中，有一个coint函数，就是专门用来实现这项工作的。


[click]

coint返回的结果是一个数组，其中第一个元素是t值，第二个元素是p值，我们一般只关注p值就可以了。

在示例中，我们使用的是两支银行股来进行检验的。时间跨度是三年，检验结果表明，它们是协整的。但是，如果我们把时间缩短，或者换一个时间段，它们也可能不是协整的。

另外，在银行股中，有一些几乎跟其它股票都不构成协整对，这样的股票，可能存在趋势性的交易机会。

-->
