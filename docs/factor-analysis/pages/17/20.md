---
clicks: 10
---

<div class='abs mt-12' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/spreads-of-two-pair.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>

### 均值恒定
### 方差恒定
### 自协方差只依赖于时间间隔
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-2-3='{opacity: 1}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/all-kinds-of-timeseries.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-3-4='{opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/自相关-vs-非自相关.jpg'>

</div>
</div>

<FlashText v-click="[3,4]"
           class='abs mt-100 text-center w-full text-3xl'>

from statsmodels.graphics.tsaplots import plot_acf
</FlashText>

<div class='abs mt-25' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/dickey-and-fuller.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>by Marco Tavora@towarddatascience</span>
</div>
</div>

<NoteCell layout='horizontal' class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-5-10='{ scale: 1}'>

```python{all|6|8-13|15-16|18-21}{at: 6}
from statsmodels.tsa.stattools import adfuller

# 生成平稳时间序列（白噪声）
np.random.seed(0)
n = 1000
stationary_series = np.random.normal(loc=0, scale=1, size=n)

# 绘制平稳时间序列
plt.figure(figsize=(12, 6))
plt.plot(stationary_series, label='平稳时间序列')
plt.title('平稳时间序列')
plt.legend()
plt.show()

# 进行ADF检验
adf_result = adfuller(stationary_series)

print("ADF检验结果:")
print(f"ADF Statistic: {adf_result[0]}")
print(f"p-value: {adf_result[1]}")
print(f"Critical Values: {adf_result[4]}")
```
</NoteCell>

<NoteCell layout='horizontal' class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-10-11='{ scale: 1}'>

```python
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.tsa.stattools import adfuller

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 30)
barss = load_bars(start, end, ('000001.XSHE',))

close = barss.xs("000001.XSHE", level=1).close
returns = close.diff().dropna()

# 绘制平稳时间序列
plt.figure(figsize=(12, 6))
ax1 = plt.gca()
ax1.plot(close, label='Price')
ax1.grid(False)

ax2 = ax1.twinx()
ax2.plot(returns, label="Returns", color='orange')
ax2.grid(False)

lines, labels = ax1.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()
ax1.legend(lines + lines2, labels + labels2, loc='best')

plt.show()

# 进行ADF检验
result1 = adfuller(close)
result2 = adfuller(returns)

df = pd.DataFrame({
    "ADF Stat": (result1[0], result2[0]),
    "P-Value": (result1[1], result2[1]),
    "Critical Values 1%": (result1[4]["1%"], result2[4]["1%"]),
    "Critical Values 5%": (result1[4]["5%"], result2[4]["5%"]),
    "Critical Values 10%": (result1[4]["10%"], result2[4]["10%"]),
     }, index=["close", "returns"])

df.T
```
</NoteCell>


<!--
这是我们刚刚见过的图。在图中，绿色的线表示两支资产之间的价差。我们前面说过，如果它是平稳序列，那么，当价差短期偏均值时，就一定会发生回归。

那么，什么是平稳时间序列呢？


[click]

平稳时间序列有这样几个主要的特点，即均值恒定、方差恒定和自协方差只依赖于时间间隔。

[click]

左上的子图就是一个平稳序列，又称作白噪声序列。我们在这里提及这个概念，是因为配对的价差序列，正好是用有序对冲了有序，只留下来白噪声，而我们要利用的，正是白噪声的特性。

这个图是利用正态分布在时间维度上的展开绘制出来的。

右上是一个均值恒定、但方差不恒定的非平稳时间序列的例子。随着时间的推移，价差序列的方差会越来越大。

左下则是一个方差恒定，但均值不恒定的例子。随着时间的推移，价差序列的均值会越来越大。

右下角是一个自协方差不恒定的例子。这个例子没有前面的例子那样容易理解。这里我们不深究数学原理了，我们从实证的角度来说明，自协方差恒定是什么样子


[click]


这两个图，都是用statsmodels中的函数plot_acf来绘制的。如果时间序列是自协相关的，那么，它就应该像左图。否则就可能像右图。

[click]

那么，具体来说，我们应该如何检验一个时间序列是时间平稳序列呢？我们一般使用ADF检验。它是由统计学家David Dickey和Wayne Fuller提出来的。

左边的是Fuller,右边的是Dickey。这两人是师徒关系，在爱荷华州立大学时，Dickey是博士生，Fuller是教授。他们俩携手合作，发明了平稳时间序列的单位根检验方法，就使得研究者能够更准确地判断时间序列的平稳性。这一检验方法在计量经济学和时间序列统计领域具有深远的影响。

[click]

我们看到，ADF检验的p值为0，一般认为p值小于0.05，则认为时间序列是平稳的。

[click]

我们来读一下代码。这里我们生成的是一个白噪声序列，显然它是平稳的。


[click]

这部分是可视化

[click]

这里我们用statsmodels中的函数adfuller来检验时间序列是否平稳。

[click]

检验的结果是一个数组，第一个元素是t统计量，第二个元素是p值。一般我们关注p值就可以了。


[click]

<run></run>

在量化交易中，常见的时间序列有股价序列和收益序列。现在，我们来利用adf检验来判定这两个序列是否是平稳序列。

第一列是股价序列的检验结果 ，第二列是收益序列的检验结果。

以我们已经学到的知识来看，股价序列的p值大于0.05；因此，它不是平稳序列。而收益序列的p值小于0.05，因此，它也是平稳序列。

收益序列能通过平稳性检验，因此，我们就可以利用平稳序列的一些统计特性来构造因子。比如，涨跌幅也应该发生均值回归。当然，这只是较弱的一个因素。


显然，不是所有的资产都可以构造出时间平稳的价差序列。我们要如何进行筛选呢？这时我们一般会使用协整检验。



-->
