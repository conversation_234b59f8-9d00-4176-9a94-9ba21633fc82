---
clicks: 7
---

<div class='abs mt-15' v-motion
     :enter='{opacity: 1}'
     :click-1='{opacity: 0}'>
<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<!-- <img src='https://images.jieyu.ai/images/2025/01/resolution-size.jpg'> -->
<img src='https://p3.itc.cn/images01/20210425/d6ecbf4a572740a9a979f25d8b29c978.jpeg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-1-2='{opacity: 1}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/summer-palace-origin.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>Summer Palace</span>
</div>
</div>

<NoteCell outputMt="2rem" outputMl="1rem" class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-2='{ scale: 1}'>

```python{all|7|12-15|11,14|18|19|}{at:2}
from sklearn.datasets import load_sample_image
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline

# 1. 加载图像数据
china = load_sample_image("china.jpg")
origin_img = np.mean(china, axis=2).astype(np.uint8)
h, w = origin_img.shape

variance = 0.99
pipeline = Pipeline([
    ("scaler", StandardScaler()),
    ("pca", PCA(n_components=variance))
])

# 2. 拟合并转换数据
compressed = pipeline.fit_transform(origin_img)
n_features = compressed.shape[1]
print(f"分解后的主成分个数：{n_features}")

# 3. 计算压缩比
compression_ratio = (h * n_features + n_features * w)/ (h * w)
print(f"Compression ratio: {compression_ratio:.2f}")

# 4. 可视化压缩后的图像
reconstructed = pipeline.inverse_transform(compressed)

# 转换image三通道
reconstructed_3d = reconstructed[:, :, np.newaxis]
gray_image = np.clip(np.repeat(reconstructed_3d, 3, axis=2), 0, 255)

# 绘制原始图像和压缩后的图像
fig, axes = plt.subplots(1, 2, figsize=(12, 6))
axes[0].imshow(china)
axes[0].set_title("Original Image")
axes[0].axis('off')

axes[1].imshow(gray_image.astype(np.uint8))
axes[1].set_title(f"Reconstructed Image ({variance:.0%} Variance)")
axes[1].axis('off')

plt.tight_layout()
plt.show()
```
</NoteCell>

<!--
当我们下载一部清晰度为2k的电影时，如果文件大小是1G，那么，当清晰度是4k时，文件大小会变为4G，如果清晰度是8k，则文件大小会变为16G。这就是一种维度灾难。

同样的事情，也可能发生在量化中。所以，在这里我们也讲一下降维。

我们这里讲降维，主要是如果你去看网上的资料，跟聚类、配对交易相关联的，几乎一定会有降维。

但是，我们也要强调，这些观点不一定正确。对hdbscan来说，我们刚才的例子，使用了接近500个特征，运算速度并不慢。所以，除非我们要更大维度的特征集，是否要进行降维，是个见仁见智的问题

又比如，对证券时序数据，降维的方法也不一定是TESNE或者PCA，也可以重采样。把分钟数据采样成日线，日线采样成周线，也有它的道理。


[click]

不过，为完备起见，我们还是讲一下PCA降维，以防未来能用得着。在sklearn的数据集中，有这样一张图片。

大家知道这是哪里吗？这是颐和园。现在，我们就以这张图片为例，讲解PCA降维。在数据分析的世界里，我们常用降维这个词，但在图像处理的世界里，这是压缩。


[click]

<run></run>

这张图可以用load_sample_image('china')来加载


[click]

这里我们定义了一个pipeline，因为进行PCA之前，需要对特征进行标准化。所以,pipeline共有两步。第二步是定义一个PCA实例。

[click]

这里的关键是指定保留的主成分个数。它可以是一个整数，表明我们希望保留的特征数；也可以是一个小数，在这种情况下，PCA将根据这个数字去计算应该保留多少个特征，以便最终保留的方差满足原数据方差的百分比。这里我们指定了0.95，也就是说，不管最终要保留多少维，我们都希望保留方差达到95%。

[click]

这一步就是提取特征的过程


[click]

从这一行我们可以知道，经过主成份提取之后，保留了多少个特征。

[click]

接下来的代码，就是计算压缩比，以及一些跟图像处理有关的代码，我们就不仔细介绍了。我们这里最关心的是，如果我要进行PCA降维，如何找到一个性价比最高的参数，即，压缩比最大，同时又不牺牲图像的品质。

我现现在来看一下结果。这是在保留95%方差下的结果。此时，压缩比达到了21%。这是以图像而言，由于我们还要还原图像，所以还保留了一些其它信息，否则，大压缩比实际上达到了10%左右。

如果我们保留99%的方差呢？

大家可以自己在课件环境中尝试一下。你会发现压缩比达到66%，图像质量比较好。

在后面，我们还会看到，对证券价格数据进行降维时，我们可以就使用99%的方差，此时，降维效果就已经令人满意了。

为什么会这样呢？大家可以思考一下。可以结合我前面的一句话来思考，就是这个降维也是可以用重采样来进行的。所以，这是价格数据的特点，噪声和冗余都很大。

我们降维的目的是为了加快处理速度，或者说，在机器内存不够时，通过降维，使得数据大小变小。但是，如果降维本身比较吃资源，这件事就会得不尝失。

不过，好在PCA降维只涉及矩阵运算，这些运算在现代CPU上都是以硬件指令运行，所以，速度非常快。
-->
