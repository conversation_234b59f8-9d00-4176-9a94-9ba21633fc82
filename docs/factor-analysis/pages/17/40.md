---
clicks: 8
layout: two-cols
right: 60%
---

<div class='abs mt-15' v-motion
     :enter='{scale: 1}'
     :click-1='{scale: 0}'>

## 构建特征数据集
</div>

<div class='abs mt-15' v-motion
     :enter='{scale: 0}'
     :click-1-2='{scale: 1}'>

## 构建特征数据集
## 降维
</div>

<div class='abs mt-15' v-motion
     :enter='{scale: 0}'
     :click-2-3='{scale: 1}'>

## 构建特征数据集
## 降维
## 聚类
</div>

<div class='abs mt-15' v-motion
     :enter='{scale: 0,y:0}'
     :click-3-4='{scale: 1}'
     >

## 构建特征数据集
## 降维
## 聚类
## 寻找配对
</div>

<div class='abs mt-15' v-motion
     :enter='{scale: 0,y:-50}'
     :click-4-5='{scale: 1}'
     >

## 构建特征数据集
## 降维
## 聚类
## 寻找配对
### half-life-time
</div>

<div class='abs mt-15' v-motion
     :enter='{scale: 0,y:-50}'
     :click-5-8='{scale: 1}'
     >

## 构建特征数据集
## 降维
## 聚类
## 寻找配对
### half-life-time
### hurst
</div>

<div class='abs mt-15' v-motion
     :enter='{scale: 0}'
     :click-8-9='{scale: 1}'>

## 构建特征数据集
## 降维
## 聚类
## 寻找配对
## 交易执行
</div>

::right::

<NoteCell class='abs mt-0 w-full'
          :enter='{ scale: 0}'
          :click-1-2='{ scale: 1}'>

```python
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline

def reduce_dimension(features: pd.DataFrame, variance: float = 0.999)->pd.DataFrame:
    """对特征数据进行降维

    Args:
        features (pd.DataFrame): 特征数据，要求索引为样本id（比如symbol）,特征为列
        variance: 降维时保留的方差百分比。
    returns:
        pd.DataFrame: 降维后的特征数据，索引为样本id，列为降维后的主成分
    """
    scaler = StandardScaler()
    pca = PCA(n_components=variance)
    pipeline = Pipeline([("scaler", scaler), ("pca", pca)])
    reduced = pipeline.fit_transform(features)
    reduced = pd.DataFrame(data=reduced, index=features.index)

    logger.info(f"降维效果：{pca.n_components_}/{features.shape[1]}")
    return reduced

start = datetime.date(2022, 1, 1)
end = datetime.date(2023,12,31)

barss = load_bars(start, end, 2000)

closes = barss["close"].unstack().ffill().dropna(axis=1, how='any')
reduce_dimension(closes.T, 0.999)
```
</NoteCell>

<NoteCell class='abs mt-0 w-full'
          :enter='{ scale: 0}'
          :click-2-3='{ scale: 1}'>

```python
import hdbscan
def hdbscan_clustering(features: pd.DataFrame, 
               min_clusters: int=3, 
               min_samples: int = 2)-> List[List[Any]]:
    """按输入的特征，运用hdbscan进行聚类
        
    Args:
        features: 特征数据，要求索引为样本id（比如symbol）,特征为列
        min_clusters: 最小分类数
        min_samples: 分类中的最小样本数
    Returns:
        返回聚类结果，各个聚类的样本id列表，按元素多少从小到大排序
    """
    clusterer = hdbscan.HDBSCAN(min_cluster_size=min_clusters, 
                                min_samples=min_samples)
    labels = clusterer.fit_predict(features)
    df = pd.DataFrame(labels, columns=["label"], index = features.index)
    valid = df.query("label != -1")
    result = valid.groupby("label").apply(lambda x: x.index.tolist())
    return sorted(result, key=lambda item: len(item))

start = datetime.date(2022, 1, 1)
end = datetime.date(2023,12,31)

barss = load_bars(start, end, 2000)

closes = barss["close"].unstack().ffill().dropna(axis=1, how='any')
hdbscan_clustering(closes.T)
```
</NoteCell>

<div v-motion class='abs mt-0 w-full'
          :enter='{ scale: 0}'
          :click-3-4='{ scale: 1}'>

```python{all}{maxHeight: '450px'}
def hedge_ratio(price1: NDArray|pd.Series, price2: NDArray|pd.Series, use_log=False) -> float:
    """计算两个序列的对冲比率

    Args:
        price1 (NDArray): 序列一
        price2 (NDArray): 序列二
        use_log (bool, optional):当为True时，计算对数价格的对冲比率. Defaults to False.

    Returns:
        float: 对冲比率
    """
    if use_log:
        price1 = np.log(price1)
        price2 = np.log(price2)

    if isinstance(price1, pd.Series):
        price1 = price1.values

    if isinstance(price2, pd.Series):
        price2 = price2.values
        
    X = sm.add_constant(price1)
    model = sm.OLS(price2, X).fit()
    if isinstance(model.params, pd.Series):
        return model.params.iloc[1]
    else:
        return model.params[1]
```
</div>

<div class='abs mt-30 ml-20 w-full' v-motion
     :enter='{scale: 0}'
     :click-5-6='{scale: 1}'>

$hurst < 0.5$ 平稳序列，具有回归特性

$hurst = 0.5$ 随机游走

$hurst > 0.5$ 趋势行情中
</div>

<NoteCell class='abs mt-0 w-full'
          :enter='{ scale: 0}'
          :click-6-7='{ scale: 1}'>

```python{all}{maxHeight: '450px'}
def halflife_and_hurst(spreads: NDArray|pd.Series, max_lag: int=20):
    """计算spreads序列的hurst exponent指数和半衰期
        传入的spreads必须能通过adfuller检验。
        本算法来自https://github.com/bartchr808/Quantopian_Pairs_Trader/tree/master及
        https://www.quantstart.com/articles/Basics-of-Statistical-Mean-Reversion-Testing/

    Args:
        spreads: hedge之后的残差序列
        max_lag: 最大自相关延时跨度

    """
    if isinstance(spreads, pd.Series):
        spreads = spreads.values

    # hurst
    lags = range(2, max_lag)
    tau = [np.sqrt(np.std(np.subtract(spreads[lag:], spreads[:-lag]))) for lag in lags]

    hurst, *_ = np.polyfit(np.log10(lags), np.log10(tau), 1)
    hurst *= 2

    # 半衰期
    lag = np.roll(spreads, 1)
    lag[0] = 0
    ret = spreads - lag
    ret[0] = 0

    lag2 = sm.add_constant(lag)
    model = sm.OLS(ret, lag2)
    res = model.fit()
    phi = res.params[1]

    if phi >= 0:
        raise ValueError(f"phi（{phi}）应该小于零。请确保spreads为平稳序列")
    half_life = -np.log(2) / phi

    return half_life, hurst
```
</NoteCell>

<NoteCell class='abs mt-0 w-full'
          :enter='{ scale: 0}'
          :click-7-8='{ scale: 1}'>

```python{all}{maxHeight: '450px'}
def find_pairs(barss: pd.DataFrame, clusters: List[List[str]]) -> pd.DataFrame:
    pairs = []
    for stocks in clusters:
        if len(stocks) < 2:
            continue  # 至少需要两只股票进行协整性检验

        for pair in itertools.combinations(stocks, 2):
            stock1, stock2 = pair

            # 获取两只股票的价格序列
            price1 = barss.xs(stock1, level=1).close.ffill().dropna()
            price2 = barss.xs(stock2, level=1).close.ffill().dropna()

            min_common_len = min(len(price1), len(price2))
            price1 = price1[-min_common_len:].values
            price2 = price2[-min_common_len:].values
                
            _, coint_p_value, *_ = ts.coint(price1, price2)

            if coint_p_value >= 0.05: # 非协整对
                continue

            # 计算spreads，进一步检验协整性
            hr = hedge_ratio(price1, price2)
            
            spreads = price2 - hr * price1
            _, p_value, *_ = ts.adfuller(spreads)

            if p_value >= 0.05: # 残差序列不平稳
                continue

            try:
                half_life, hurst = halflife_and_hurst(spreads)
            except ValueError:
                continue
    
            pairs.append(
                (stock1, stock2, hr, hurst, half_life, coint_p_value)
            )

    return pd.DataFrame(pairs, columns=["pair1", "pair2", "hedge_ratio", "hurst", "half_life", "coint_p_value"])
```
</NoteCell>

<!--
我们先分步骤讲一下各个阶段的关键代码，最后再合起来讲整个框架。

这一部分要讲的不多，一般直接使用价格数据就可以了。如果你还希望添加其它数据，请参考因子部分。

这里要注意的就是数据的格式，在我们课程中，统一使用date和symbol的双重索引格式。如果是价格数据，那么每一列对应一个时间点。


[click]

<run></run>

我们把前面讲过的降维代码封装了一下。核心代码我们已经讲过了。


[click]

<run></run>

这是聚类的代码。这里我们抛弃了噪声，即标签为-1的数据，最终把聚类结果，以数组的数组的方式返回，并且按大小由由到大排序。


[click]

在寻找配对时，我们对代码进行了适当重构。增加了hedge_ratio函数，用来计算两个序列的对冲比。

这里我们还增加了一个use_log的参数。在前面我们的示例中，都是直接使用的价格，而没有使用对数价格。对数价格相当于收益。考虑到对数价格本身就接近平稳序列，所以，一般情况下，似乎没有理由通过对数价格来构造一个新的平稳序列。

但为了拓展性，我们这里还是加上了这个参数

另一个变化是，兼容输入格式。因为有时候我们会传入numpy数组，有时候会是pandas的Series。


[click]

这里我们还要介绍两个新概念，半衰期和hurst指数。

当我们找到一个协整对，构造出来了平稳序列，并且发现当前价差偏离了均值。这个时候，我们就想知道，它何时会回归呢？显然，一旦我们开仓，价差越快回归，资金利用率就越高，风险也可能会小一些。

这就要用到半衰期的概念。半衰期是指时间序列的某个统计特性，衰减到初始值一半所需要的时间。


[click]

另一个概念是hurst指数。这个指数是判断一个时间序列是平稳序列、随机游走还是趋势保持的一个指数。

如果一个时间序列通过了adf检验，那它的hurst指数也是小于0.5的。


[click]

这段代码我不讲了，它主要不是编程上的技巧。主要涉及到自相关序列的一些理论，感兴趣的同学，可以看一下本章的拓展阅读和脚注。

[click]

这段是在聚类完成之后，寻找配对的代码。它接收聚类的结果用为输入，最终输出一个dataframe，这个dataframe的前两行是配对的资产的代码，然后是对冲比，hurst指数和半衰期。

在代码中，主要加入了错误处理。比如这里对nan数据的处理。

[click]

最后是执行交易。这部分我们结合backtrader框架来看。

-->
