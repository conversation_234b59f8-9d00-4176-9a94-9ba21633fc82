---
clicks: 26
---

<div class='abs mt-40 ml-100' v-motion
     :enter='{opacity: 1}'
     :click-4='{opacity: 0}'>

$$\frac{N\times(N-1)}{2}$$

</div>

<FlashText v-click="[1,4]"
           class='abs mt-70 text-center w-full text-3xl'>

当N=100时，计算次数为4950次
</FlashText>

<FlashText v-click="[2,4]"
           class='abs mt-85 text-center w-full text-3xl'>

当N=10_000时，计算次数为4950万次
</FlashText>

<FlashText v-click="[3,4]"
           class='abs mt-100 text-center w-full text-3xl'>
32万次协整计算约需20分钟
</FlashText>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{opacity: 1}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/12/clustering.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, x: 200, y: 150, scale: 1, color: "black"}'
     :click-5='{opacity: 1}'
     :click-6="{x: 0, y: 50, scale:0.8, color:'gray'}"
     :click-7="{scale:0}">

假设我们可以将样本聚类成$K$个簇，

且每簇内样本数量分别为$( N_1, N_2, \ldots, N_K )$， $( \sum_{i=1}^{K} N_i = N )$

</div>

<div class='abs mt-50 ml-80' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{opacity: 1}'>

$$\sum_{i=1}^{K} \binom{N_i}{2} = \sum_{i=1}^{K} \frac{N_i (N_i - 1)}{2}$$

</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-7-10='{opacity: 1}'>

## K-means
### 分类大于聚类
### 需要明确知道有多少类
### 初始参数敏感
### 高性能，超大数据集首选
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, x: 200, y: 80, scale: 1}'
     :click-8='{opacity: 1}'
     :click-9='{y: -50, scale: 0.6, x: 50}'
     :click-10='{scale:0.55}'
     :click-14='{scale:0}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/data-to-be-clustered.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>数据集</span>
</div>
</div>


<div class='abs' v-motion
     :enter='{scale: 0, x: 200, y: 150}'
     :click-9='{scale: 0.6, x:250, y:200}'
     :click-10='{scale: 0.55, x:50}'
     :click-14='{scale:0}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/cluster-result-by-kmeans.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>k-means聚类</span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-10-11='{opacity: 1}'>

## DBSCAN
### 密度聚类，容许噪声点
### 需要指定超参数$\epsilon$和$minPts$
### 只允许均一密度
### 可以处理大数据集

</div>

<div class='abs' v-motion
     :enter='{scale: 0}'
     :click-10-14='{scale: 0.55, x: 340, y:-50}'>

<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/cluster-result-by-dbscan.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>DBSCAN</span>
</div>
</div>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-11-14='{opacity: 1}'>

## HDBSCAN
### 继承 DBSCAN 优点
### 参数更直观
### 结果稳定
### 高性能
</div>
<div class='abs' v-motion
     :enter='{scale:0}'
     :click-11='{scale: 0.55, x: 340, y: 200}'
     :click-14='{scale:0}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/cluster-by-hdbscan.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>HDBSCAN</span>
</div>
</div>

<FlashText v-click="[12,14]"
           class='abs mt-80 ml-20 w-full text-3xl'>

min_cluster_size
</FlashText>

<FlashText v-click="[13,14]"
           class='abs mt-100 ml-20 w-full text-3xl'>
min_sample_size
</FlashText>

<FlashText v-click="[14,15]"
           class='abs mt-50 text-center w-full text-3xl'>

pip install hdbscan
</FlashText>


<div v-motion
     class="abs mt-10"
     :enter='{scale: 0, width:"100%"}'
     :click-15='{scale: 1}'
     :click-22='{width: "50%"}'
     :click-23="{scale:0}">

```python{all|6-13|15,16|17|19-25|27-29|31-50}{maxHeight: '450px',at:16}
import hdbscan
import pandas as pd
from sklearn.manifold import TSNE
import plotly.express as px

start = datetime.date(2022, 1, 1)
end = datetime.date(2023,12,31)

barss = load_bars(start, end, 2000)

closes = (barss["close"].unstack().
                        ffill().
                        dropna(axis=1, how='any'))

# 使用 HDBSCAN 进行聚类
clusterer = hdbscan.HDBSCAN(min_cluster_size=3, min_samples=2)
cluster_labels = clusterer.fit_predict(closes.T)

# 将聚类结果添加到 DataFrame 中
clustered = closes.T.copy()
clustered['cluster'] = cluster_labels

# 剔除类别为-1的点，这些是噪声，而不是一个类别
clustered = clustered[clustered['cluster'] != -1]
clustered_close = clustered.drop("cluster", axis=1)

# 使用t-SNE进行降维
tsne = TSNE(n_components=3, random_state=42)
tsne_results = tsne.fit_transform(clustered_close)

# 将t-SNE结果添加到DataFrame中
reduced_tsne = pd.DataFrame(data=tsne_results, 
                            columns=['tsne_1', 'tsne_2', 'tsne_3'],
                            index=clustered_close.index)

reduced_tsne['cluster'] = clustered['cluster']

fig_tsne = px.scatter_3d(
    reduced_tsne, 
    x='tsne_1', y='tsne_2', z='tsne_3',
    color='cluster', 
    title='t-SNE Clustering of Stock Returns',
    labels={'tsne_1': 't-SNE Component 1', 
            'tsne_2': 't-SNE Component 2'}
)

fig_tsne.layout.width = 1200
fig_tsne.layout.height = 1100

fig_tsne.show()
```
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, x:300, y:40}'
     :click-22-23='{opacity: 1}'>
<div style='width:50%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2025/01/clustered-by-hdbscan.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<NoteCell layout='horizontal' class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-23='{ scale: 1}'
          :click-26='{scale:0}'>

```python{all|0-22|23-27}{at:24}
import hdbscan
import pandas as pd
from sklearn.manifold import TSNE
import plotly.express as px

start = datetime.date(2022, 1, 1)
end = datetime.date(2023,12,31)

barss = load_bars(start, end, 2000)

closes = barss["close"].unstack().ffill().dropna(axis=1, how='any')

# 使用 HDBSCAN 进行聚类
clusterer = hdbscan.HDBSCAN(min_cluster_size=3, min_samples=2)
cluster_labels = clusterer.fit_predict(closes.T)
clustered = closes.T.copy()
clustered['cluster'] = cluster_labels

# 剔除类别为-1的点，这些是噪声，而不是一个类别
clustered = clustered[clustered['cluster'] != -1]
clustered_close = clustered.drop("cluster", axis=1)

plt.figure(figsize=(12,10))
cluster_12 = clustered.query("cluster == 12").index.tolist()
for code in cluster_12:
    bars = barss.xs(code, level=1)["close"]
    plt.plot(bars)
```
</NoteCell>


<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-26='{ scale: 1}'>

```python
from statsmodels.tsa.stattools import coint

pairs = []

for i in range(len(cluster_12)):
    for j in range(i + 1, len(cluster_12)):
        pair1 = cluster_12[i]
        pair2 = cluster_12[j]
        price1 = barss.xs(pair1, level=1)["close"].ffill().dropna()
        price2 = barss.xs(pair2, level=1)["close"].ffill().dropna()
        minlen = min(len(price1), len(price2))
        t, p, *_ = coint(price1[-minlen:], price2[-minlen:])
        if p < 0.05:
            pairs.append((pair1, pair2))

row = max(1, len(pairs) // 3)
col = len(pairs) // row

if row * col < len(pairs):
    row += 1

cells = row * col

fig, axes = plt.subplots(row, col, figsize=(col * 3,row * 3))
axes = np.array(axes).flatten()
fig.suptitle('Cointegrated Pairs')

plot_index = 0
for pair1, pair2 in pairs:
    ax = axes[plot_index]
    
    price1 = barss.xs(pair1, level=1)["close"]
    price2 = barss.xs(pair2, level=1)["close"]
    
    ax.plot(price1, label=pair1)
    ax.plot(price2, label=pair2)
    ax.set_title(f'{pair1[:-5]} & {pair2[:-5]}')
    ax.set_xticks([])
    plot_index += 1


plt.tight_layout()
plt.show()
```
</NoteCell>

<!--
理论上，有了前面的知识，我们就可以设计出配对交易策略了。那这和今天课程的主题之一，聚类算法有什么关系呢？

谜题的答案是性能。


[click]

当N为100时，计算次数为4950次。对于计算机来说这仍然只是一眨眼的时间。但是，仅以中国资产而论，A股有5000支，配对除了可能发生在股票内部，也可以发生在股票与基金、股票与商品期货之间。所以，资产品种的数量大概在1万左右。

[click]

当资产数量达到1万时，这个计算次数就会达到惊人的4950万次。进行4950万次协整检验要花多久呢？


[click]


在我们课程环境中，计算32万次就要10到20分钟。大家可以自己推导下总的计算量。

[click]

如果我们能先对这些资产进行粗略的分类，然后只在分类中，进行两两协整检验呢？这样我们可能会丢失掉一些机会，因为聚类算法与协整检验不完全是一回事。具有协整关系的两支资产，在聚类算法中可能被分为不同的类别。但无论如何，只要能大大加速计算过程，这样做可能仍然是值得的。

[click]

假设我们可以将样本聚类成K个簇，假设每簇内样本量又是最理想的均分的话，

[click]

那么，计算量由这个公式计算。

现在，假设仍然是100个样本，能够均分为10个簇的话，那么，两两协整计算就会下降到只有450次，减少了90%多的计算量。


[click]

那么说到聚类，我们有哪些聚类算法呢？首先是k-means算法。

严格地说，K-means更像分类，而不是聚类。换句话说，即使是数据中有不属于任何簇的噪声点，K-means算法也会将这些点分配到最近的簇中。之所以说它是聚类算法，是因为它是非监督学习算法。

它的其它缺点还包括，需要明确知道有多少类--这在很多时候是不容易知道的；同时，它对初始化参数敏感。具体我们就不讲了，因为它的优势是高性能，是超大数据集中的首选。但是，在量化场景下，我们似乎很难有超大数据集。


[click]

假设我们有这样一组数据。


[click]

通过kmeans聚类的结果，大致如下图所示。我们看到，每一个点都是彩色的，这意味着它不允许有噪声点。这里大概分出了6类。但如果我们指定为10类的话，它也会照做。


[click]

相对于kmeans，DBSCAN就有不小的优势。它的性能会比kmeans弱一点，但仍然 能打。它最棒的特性是，它会自动识别出噪声，抛弃这些噪声。

在图中，我们看到一些灰色的点，这些都是噪声。不过，超参数epsilon和minpts相当于指定了簇的密度，这样一来，更稀的簇就可能被遗漏。


[click]

hdbscan算法继承了DBSCAN的优点，并作出了两点重要改进，第一，它的参数更直观。


[click]


它有两个重要的参数，第一个，min_cluster_size，它是一个簇最小要包含多少个样本，才能算成一个簇。这一点，应用程序很容易知道。


[click]

第二个参数，min_samples，它表示一个样本点周围至少要有多少个点才能被看成密度核心。尽管没有min_cluster_size那么直观，但这仍然是到目前为止，对超参数要求最少、含义最明确的参数了。


最后我们讨论下hdbscan的结果。我们没有告诉算法总共有多少类。但是hdbscan自己找出了最佳答案是6类，剩余的点是噪声。在dbscan的结果中，它只分出了4类。


[click]

hdbscan这么好用，那么，究竟应该如何实现呢？sklearn并没有提供这个算法实现。它是以第三方开源库存在的。


[click]

我们通过这段代码来演示如何使用Hdbscan

这段代码分两部分，第一部分是进行聚类。第二部分是将聚类结果进行可视化。这一部分我们使用了s-sne降维。先看第一部分


[click]

这一段是获取数据。这里我们只简单地使用了一段时间内的收盘价作为特征。按照要求，最后必须转换成为每个样本一行的二维向量。也就是，最终我们要传给hdbscan的X，如果是dataframe的话，它的index应该是各个股票的代码。每一列应该是时间点上的收盘价。

hdbscan不接收nan值，所以我们要进行简单的预处理。预处理怎么做？这是我们之前讲过的。对收盘价可以用前向填充，如果是基本面数据，可以使用行业中值，等等。

[click]

hdbscan用法类似于sklearn中的api,也是先实例化对象。这里我们要指定超参数，min_cluster_size和min_samples。

[click]

然后调用fit_predict方法，得到每个样本的聚类标签。这里要注意，我们前面得到的close数据，是以股票代码为列，时间为索引的dataframe，所以，在传递给fit_predict之前，需要转置一下。

[click]

得到的标签中，有一类是-1，表示是噪声点。所以，在可视化之前，我们选过滤掉这一部分。


[click]

数据集本身是多维的，我们能可视化的最高维度是三维，所以，在显示时，先要通过tsne把数据降维成三维

[click]

这部分代码是可视化。这个API我们不具体讲了，关于绘图，如果大家感兴趣，可以去学量化24课。在本课程中也有相关的例子。

[click]

最后，我们得到的可视化数据是这样的。在我们的课件中，还不能直接显示3D图形。请大家在课件中自己运行查看。


[click]

当然，我们最关心的，是这样分类出来的簇，它们之间是否有一致的走势。所以，我们将通过这段代码检验一下。

[click]

<run></run>

这部分代码跟之前一样

[click]

这段代码是从标签为12的簇中，找出所有的样本，绘制它们的价格走势图。

从绘制出来的价格走势可以看出，hdbscan干得不错。而实现这一节，我们只用了两行代码。


[click]

<run></run>

当然，最终我们还是要运行协整检验。在第12簇中，共有4支股票，协整检验的结果表明，其中存在三个协整对。有50%的组合被淘汰了。
-->
