---
clicks: 10
---

<div class="mt-10 ml-5">

## 平稳时间序列
### 定义和ADF检验
### 构造平稳时间序列
### 协整检验
## 聚类算法
### 从KMeans到HDBSCAN
## 数据降维
## 带聚类算法的配对交易框架
</div>

<!--
今天的主要课程内容就到这里。现在， 请大家提出问题。在大家准备问题的同时，我为大家总结今天的课程。

今天这节课，是我们课程正式交付的第一个有一定实战价值的模型。在随机测试中，取得过年化14%到24%的收益率，这是扣除了手续费和滑点的，有一定参考价值。

这个模型主要利用了平稳时间序列的一些特性，比如均值恒定、方差恒定和自协相关等等。正是因为有了这些特性，我们就可以假设，两支股票如果是协整的，那么它们之间的价差一旦偏离均值，总有一天会回归，这就给了我们套利的机会。

这个策略在理论上是可行的，数学基础很扎实。问题在于如何寻找套利机会，也就是协整对。我们介绍了暴力搜索所需要的巨大计算量，从而引出来聚类算法。

在所有的聚类算法中，个人认为hdbscan是最有效、最适用于量化交易的。所以，课程中我们就只介绍了hdbscan如何使用。

最后，我们把所有这些知识结合起来，构建了一个配对交易策略。
-->
