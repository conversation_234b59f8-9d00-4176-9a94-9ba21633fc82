---
clicks: 20
---

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-20='{ scale: 0}'>

```python{all|24-34|62-72|71|74-86|88-94|96-113|259-265|267-316|268-269|271-273|275-277|279-281|283-287|292-294|295-299|304-309|311-316|318-354|360-366|369-378}{at:0}
import datetime
import itertools
import logging
from typing import Any, List, Tuple

import backtrader as bt
import hdbscan
import numpy as np
import pandas as pd
import pyfolio as pf
import statsmodels.api as sm
import statsmodels.tsa.stattools as ts
from backtrader.feeds import PandasData
from freezegun import freeze_time
from numpy.typing import NDArray
from sklearn.decomposition import PCA
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler

logging.basicConfig(level=logging.INFO, format='%(asctime)s %(message)s', datefmt="%Y/%m/%d")
logger = logging.getLogger("pairtrade")


def is_notebook():
    try:
        shell = get_ipython().__class__.__name__
        if shell == 'ZMQInteractiveShell':
            return True  # Jupyter notebook or qtconsole
        elif shell == 'TerminalInteractiveShell':
            return False  # Terminal running IPython
        else:
            return False  # Other type (?)
    except NameError:
        return False  # Probably standard Python interpreter

class PairTradingStrategy(bt.Strategy):
    params = dict(
        half_life_thresh=10,
        periods=250,
        delta=2
    )

    def __init__(self):
        # status: 0 未持仓 -1 做多pair1, 做空pair2中；1 做空pair1, 做多pair2中
        self.status = 0

        self.pair1 = self.datas[0]
        self.pair2 = self.datas[1]

        log_price1 = np.log(list(self.pair1.close)[: self.p.periods])
        log_price2 = np.log(list(self.pair2.close)[: self.p.periods])

        # 计算对数价格的hedge_ratio
        hr_log = self.hedge_ratio(log_price1, log_price2)

        # 计算spread
        self.spreads = (log_price2 - hr_log * log_price1).tolist()

        self.mean_spread = np.mean(self.spreads)
        self.std_spread = np.std(self.spreads)

    def log(self, msg, *args, **kwargs):
        """日志记录工具，支持自动记录交易时间，而不是执行回测时的系统时间"""
        dt = kwargs.get("dt")
        if dt is None:
            dt = self.datas[0].datetime.datetime()
        else:
            dt = bt.num2date(dt)
            del kwargs["dt"]

        with freeze_time(dt):
            logger.info(msg, *args, **kwargs)

    def notify_order(self, order):
        if order.status in [bt.Order.Submitted, bt.Order.Accepted]:
            return  # Await further notifications

        if order.status == order.Completed:
            name = order.data._name
            price = order.executed.price
            size = order.executed.size
            
            if order.isbuy():
                self.log("买入 %s(%s, %s)", name, price, size, dt = order.executed.dt)
            else:
                self.log("卖出 %s(%s, %s)", name, price, size, dt = order.executed.dt)

    def notify_trade(self, trade):
        if trade.isclosed:
            name = trade.data._name
            price = trade.price
            size = trade.size
            pnl = trade.pnlcomm
            self.log("交易结束 %s(%s, %s, %s)", name, price, size, pnl, dt = trade.dtclose)

    @classmethod
    def reduce_dimension(cls, features: pd.DataFrame, variance: float = 0.999)->pd.DataFrame:
        """对特征数据进行降维

        Args:
            features (pd.DataFrame): 特征数据，要求索引为样本id（比如symbol）,特征为列
            variance: 降维时保留的方差百分比。
        returns:
            pd.DataFrame: 降维后的特征数据，索引为样本id，列为降维后的主成分
        """
        scaler = StandardScaler()
        pca = PCA(n_components=variance)
        pipeline = Pipeline([("scaler", scaler), ("pca", pca)])
        reduced = pipeline.fit_transform(features)
        reduced = pd.DataFrame(data=reduced, index=features.index)

        logger.info("降维效果：%s/%s", pca.n_components_, features.shape[1])
        return reduced
    
    @classmethod
    def hdbscan_clustering(cls, features: pd.DataFrame,
                min_clusters: int=3,
                min_samples: int = 2,
                reduce_before_clustering: bool=False,
                **kwargs)->List[Any]:
        """按输入的特征，运用hdbscan进行聚类
            
        Args:
            features: 特征数据，要求索引为样本id（比如symbol）,特征为列
            min_clusters: 最小分类数
            min_samples: 分类中的最小样本数
            reduce_before_clustering: 是否要在聚类前进行降维
            kwargs: 如果reduce_before_clustering为真，则接受variance参数，表示降维保留的方差百分比
        Returns:
            返回聚类结果，key为聚类编号，value为该聚类中的样本id列表（即feature.index）
        """
        if reduce_before_clustering:
            variance = kwargs.get("variance", 0.999)
            features = cls.reduce_dimension(features, variance)

        clusterer = hdbscan.HDBSCAN(min_cluster_size=min_clusters, 
                                    min_samples=min_samples)
        labels = clusterer.fit_predict(features)
        df = pd.DataFrame(labels, columns=["label"], index = features.index)
        valid = df.query("label != -1")
        result = valid.groupby("label").apply(lambda x: x.index.tolist())

        return sorted(result, key=lambda item: len(item))

    @classmethod
    def hedge_ratio(cls, price1: NDArray|pd.Series, price2: NDArray|pd.Series, use_log=False) -> float:
        """计算两个序列的对冲比率

        Args:
            price1 (NDArray): 序列一
            price2 (NDArray): 序列二
            use_log (bool, optional):当为True时，计算对数价格的对冲比率. Defaults to False.

        Returns:
            float: 对冲比率
        """
        if use_log:
            price1 = np.log(price1)
            price2 = np.log(price2)

        if isinstance(price1, pd.Series):
            price1 = price1.values # type: ignore

        if isinstance(price2, pd.Series):
            price2 = price2.values # type: ignore
            
        X = sm.add_constant(price1)
        model = sm.OLS(price2, X).fit()
        if isinstance(model.params, pd.Series):
            return model.params.iloc[1]
        else:
            return model.params[1]

    @classmethod
    def halflife_and_hurst(cls, spreads: NDArray|pd.Series, max_lag: int=20):
        """计算spreads序列的hurst exponent指数和半衰期
            传入的spreads必须能通过adfuller检验。
            本算法来自https://github.com/bartchr808/Quantopian_Pairs_Trader/tree/master及
            https://www.quantstart.com/articles/Basics-of-Statistical-Mean-Reversion-Testing/

        Args:
            spreads: hedge之后的残差序列
            max_lag: 最大自相关延时跨度

        """
        if isinstance(spreads, pd.Series):
            spreads = spreads.values # type: ignore

        # hurst
        lags = range(2, max_lag)
        tau = [np.sqrt(np.std(np.subtract(spreads[lag:], spreads[:-lag]))) for lag in lags]

        hurst, *_ = np.polyfit(np.log10(lags), np.log10(tau), 1)
        hurst *= 2

        # 半衰期
        lag = np.roll(spreads, 1)
        lag[0] = 0
        ret = spreads - lag
        ret[0] = 0

        lag2 = sm.add_constant(lag)
        model = sm.OLS(ret, lag2)
        res = model.fit()
        phi = res.params[1]

        if phi >= 0:
            raise ValueError(f"phi（{phi}）应该小于零。请确保spreads为平稳序列")
        half_life = -np.log(2) / phi

        return half_life, hurst

    @classmethod
    def find_pairs(cls, barss: pd.DataFrame, clusters: List[List[str]]) -> pd.DataFrame:
        pairs = []
        for stocks in clusters:
            if len(stocks) < 2:
                continue  # 至少需要两只股票进行协整性检验

            for pair in itertools.combinations(stocks, 2):
                stock1, stock2 = pair

                # 获取两只股票的价格序列
                price1 = barss.xs(stock1, level=1).close.ffill().dropna()
                price2 = barss.xs(stock2, level=1).close.ffill().dropna()

                min_common_len = min(len(price1), len(price2))
                price1 = price1[-min_common_len:].values
                price2 = price2[-min_common_len:].values
                    
                _, coint_p_value, *_ = ts.coint(price1, price2)

                if coint_p_value >= 0.05: # 非协整对
                    continue

                # 计算spreads，进一步检验协整性
                hr = cls.hedge_ratio(price1, price2)
                
                spreads = price2 - hr * price1
                _, p_value, *_ = ts.adfuller(spreads)

                if p_value >= 0.05: # 残差序列不平稳
                    continue

                try:
                    half_life, hurst = cls.halflife_and_hurst(spreads)
                except ValueError:
                    continue
        
                pairs.append(
                    (stock1, stock2, hr, hurst, half_life, coint_p_value)
                )

                if len(pairs) >= 10:
                    break

        return pd.DataFrame(pairs, columns=["pair1", "pair2", "hedge_ratio", "hurst", "half_life", "coint_p_value"])

    def close_position(self, reason: str):
        if self.status != 0:
            self.order_target_percent(self.pair1, target=0)
            self.order_target_percent(self.pair2, target=0)
            self.log(reason)

        self.status = 0

    def next(self):
        if len(self.pair1) < self.p.periods:
            return
        
        # 获取最新数据
        price1 = np.array(self.pair1.close.get(size=self.p.periods))
        price2 = np.array(self.pair2.close.get(size=self.p.periods))

        hr = self.hedge_ratio(price1, price2)
        spreads = price2 - hr * price1
        halflife, hurst = self.halflife_and_hurst(spreads)
        
        # 不满足协整条件时，不开仓
        if self.status == 0 and (hurst >= 0.5 or halflife >= self.p.half_life_thresh):
                return
            
        mean_spread = np.mean(spreads)
        std_spread = np.std(spreads)

        spread = spreads[-1]
        self.spreads.append(spread)

        # self.log("spread %s, mean %s, std %s, upper %s, lower %s", spread, mean_spread, std_spread, mean_spread + self.p.delta * std_spread, mean_spread - self.p.delta * std_spread)
        
        # 出场条件：spread从上方回归
        if self.status == 1 and spread < mean_spread:
            self.close_position("spread从上方回归，退出")
            return
        
        # 出场条件：spread从下方回归
        if self.status == -1 and spread > mean_spread:
            self.close_position("spread从上方回归，退出")
            return

        if self.status != 0:
            return
        
        # 入场条件：如果spread大于均值 delta 倍标准差，则做多pair1，做空pair2
        if self.status == 0 and (spread > mean_spread + self.p.delta * std_spread):
            self.log("入场条件1")
            self.order_target_percent(self.pair1, target=0.5)
            self.order_target_percent(self.pair2, target=-0.5)
            self.status = 1
        
        # 入场条件：如果spread小于均值 delta 倍标准差，则做空pair1，做多pair2
        if self.status == 0 and (spread < mean_spread - self.p.delta * std_spread):
            self.log("入场条件2")
            self.order_target_percent(self.pair1, target=-0.5)
            self.order_target_percent(self.pair2, target=0.5)
            self.status = -1

    @classmethod
    def backtest(
        cls,
        universe: List[str]|int,
        start: datetime.datetime,
        end: datetime.datetime,
        cash: float = 1_000_000,
        commission: float = 1e-4,
        periods: int = 250,
        delta: float = 1,
        half_life_thresh = 10
    ):
        cerebro = bt.Cerebro()

        barss = load_bars(start, end, universe)
        closes = barss["close"].unstack().ffill().dropna(axis=1, how='any')

        clusters = cls.hdbscan_clustering(closes.T)
        cls.pairs = cls.find_pairs(barss, clusters)

        if len(cls.pairs) == 0:
            raise ValueError("No cointegrated pairs found")

        pair1, pair2 = cls.pairs.iloc[0][["pair1", "pair2"]]
        data = PandasData(dataname=barss.xs(pair1, level=1))
        cerebro.adddata(data, name=pair1)

        data = PandasData(dataname=barss.xs(pair2, level=1))
        cerebro.adddata(data, name=pair2)

        cerebro.addstrategy(PairTradingStrategy, periods=periods, delta=delta, half_life_thresh= half_life_thresh)
        cerebro.broker.set_cash(cash)
        cerebro.broker.setcommission(commission)

        if is_notebook():
            cerebro.addanalyzer(bt.analyzers.PyFolio)
        return cerebro.run(), cerebro

if not is_notebook():
    def load_bars(start: datetime.date, end: datetime.date, universe: List[str]) -> pd.DataFrame:
        raise NotImplementedError("load_bars函数需要自行实现")

strategies, cerebro = PairTradingStrategy.backtest(
    500,
    datetime.date(2021, 1, 1),
    datetime.date(2023, 12, 31),
    delta=0.5,
    half_life_thresh = 20
)


if is_notebook():
    pyfoliozer = strategies[0].analyzers.getbyname('pyfolio')
    returns, positions, transactions, gross_lev = pyfoliozer.get_pf_items()
    pf.tears.create_full_tear_sheet(
        returns,
        positions=positions,
        transactions=transactions,
    )
else:
    cerebro.plot()
```
</NoteCell>

<div class='abs mt-20' v-motion
     :enter='{opacity: 0}'
     :click-20='{opacity: 1}'>

### 资金利用率
### 开仓阈值
### 滑点
</div>

<!--

<run></run>

这是一段辅助性代码。backtrader在notebook中使用时，存在无法绘图的情况，所以，我们在notebook下面，会使用pyfolio来输出报表。但在控制台下运行是，pyfolio的报表又无法显示。所以需要这段代码来区分运行环境。

[click]

这段也是辅助性代码。它的作用时，在回测中输出日志时，我们总是希望使用回测时遍历到的时间，而不是运行回测时的系统时间。

[click]

这里有一个技巧，使用了一个名为freezegun的库，它会在输出日志时，替换系统时间，这样，logger打印出来的时间就不会是运行时的系统时间，而是回测时bar的时间。

[click]

这个是backtrader的辅助函数，我们一般都要重写它，来输出一些诊断信息

[click]

这是另一个辅助函数。它发生成一笔交易完成时。也就是，比如我们对平安银行开仓后，这会触发notify_order，再平仓时，就会触发notify_trade

[click]

这是降维函数，跟前面看到的一样，只不过改写成了类方法。其它类似的方法，后面我们就不专门讲了。

[click]

好，我们跳过已经见过的方法，介绍下close_position。这是我们写的一个辅助函数，用在发生均值回归或者其它需要平仓的场合，一键平仓。

这里用到了order_target_percent方法，它是把参数1指定的股票，调仓到target指定的百分比。这里指定为0，所以就是完全清仓。

[click]

这是backtrader 的方法，所有的策略都一定要改写这个方法，是最主要的功能。

[click]

第一段是跳过冷启动期。在示例中判断两个序列是否协整时，我们要求最少有250个bar。所以，对前250个bar，我们就跳过。

[click]

然后，更新数据，这里涉及到backtrader的语法，不熟悉的同学可以在量化24课中学习，内容太多，没办法在这里全讲。

[click]

拿到最新数据之后，我们就计算对冲比，计算hurst和half_life_time。

[click]

这里有一个status变量，它是用来表明当前持仓的方向的。总共有三个方向，0表明无持仓，1表明做多pair1，做多pair2，-1则表示相反。

如果 status 为0，说明当前无持仓，同时hurst >= 0.5，说明不满足平稳条件，那么我们就不开仓。或者，半衰期太长，我们也不开仓。

[click]

然后计算价差，判断是否存在开仓或者平仓的条件。这里我们把计算出的最新的spread保留下来了。在backtrader中，我们可以在绘图时，显示这些指标，便于分析。

[click]

如果当前有持仓，并且是做空价差的方向，也就是对pair2开的空，对pair1开得多，但是价差低于平均值了，这时候我们就要退出了。

[click]

这是持仓的另一种情况，做多价差的方向。

[click]

这是其中的一种入场情况。价差大于我们指定的阈值

[click]

这是另外一种入场情况，价差低于指定阈值。

[click]

这段代码是构造backtrader的运行实例，进行初始化并启动回测的代码。很多示例是把它放在模块的顶层部分，这里我们把它封装为类的方法，这样代码更容易管理一些。

从功能上看，主要是加载数据，进行初始化的聚类和寻找配对操作。这部分代码，在backtrader中，也有放在构造函数中的做法。

[click]

这里启动了回测。回测时我们可以指定一些参数，通过修改这些参数，就可以改变回测的结果。因此，这也是参数优化的入口。

backtrader自己带了参数搜索的方法。这在量化24课中有介绍。

[click]

最后是绘图。现在我们来看下运行结果。回测中我们是算了手续费的，最终年化是24%，应该是一个比较理想的结果。当然，这个过程还是比较随机的，因为试验中，我们是随机选了500支，时间上也只回测了一年多一点，象这种策略，应该回测5到10年。因为它的理论基础比较坚实，如果失效，很有可能是实现上有错误。大家可以多试下，看看总体上如何。


[click]

我们简单讨论下策略优化的问题。当然，可以主要依靠backtrader的参数搜索方法来进行优化。这里我们主要讨论优化的原理。

首先，我们要思考如何提高资金利用率。在策略示例中，我们只使用了寻找到的所有协整对中的第一对。但实际上，我们应该定期搜索（比如半年）一次，选择半衰期最短的、hurst指数最小的进行交易

其次，开仓时，我们设置了一个阈值，这个阈值用多少为好？这里实际上有一个数学计算问题。如果价差序列是平稳序列，它的分布有可能是正态分布，那么，每个分位数的概率就是知道的，这样我们就可以求出来最佳的阈值。在该点上，概率乘以价差的值最大。

回测中也设置了滑点。如果你管理的资金量比较大的话，即使是对冲策略，滑点对收益的影响可能就比较大。如果管理的资金比较小，盈利的概率应该是比较大的。核心是你能不能做空来对冲，融券的手续费、时效性等因素。

-->

