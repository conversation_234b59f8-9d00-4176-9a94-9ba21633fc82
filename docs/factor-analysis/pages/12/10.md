---
clicks: 10
---

<div class='abs mt-10 flex justify-content h-full w-full' v-motion
     :enter='{scale: 0.8, x:0,y:0}'
     :click-1='{ scale:0.5,x:200,y:-50}'
     :click-9='{ scale:0,x:0,y:50}'>



| 指标名称               | 公式                                                        | 提出时间     | 提出者         |
| ---------------------- | ----------------------------------------------------------- | ------------ | -------------- |
| **BIAS**               | $Bias = \frac{(Close - MA(Close, N))}{MA(Close, N)}$        | 早于 1960 年 | N/A            |
| **APO**                | ${短期 EMA} - {长期 EMA}$                                   | 1970 年代    | <PERSON>   |
| **PPO**                | $\frac{\text{短期 EMA} - \text{长期 EMA}}{\text{长期 EMA}}$ | 1970 年代    | <PERSON>   |
| **MACD**               | EMA(Close, 12) - EMA(Close, 26)                             | 1970 年代    | Gerald Appel   |
| **CCI**                | $\frac{Typical Price - MA}{.015 * Mean Deviation}$          | 1980 年代    | Donald Lambert |
| **Awesome Oscillator** | $SMA(5) - SMA(34)$                                          | 1995 年      | Bill Williams  |

</div>

<div class='abs' v-motion
     :enter='{opacity: 0, y:150}'
     :click-1-6='{ opacity: 1}'>

## 使用不同的价格序列
### 收盘价
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, y:150}'
     :click-2-6='{ opacity: 1}'>

## 使用不同的价格序列
### 收盘价
### Typical Price
</div>


<div class='abs' v-motion
     :enter='{opacity: 0, y:150}'
     :click-3-6='{ opacity: 1}'>

## 使用不同的价格序列
### 收盘价
### Typical Price
### 中间价
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, y:150}'
     :click-4-6='{ opacity: 1}'>

## 使用不同的价格序列
### 收盘价
### Typical Price
### 中间价
### 成交量加权平均
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, y:150}'
     :click-5-6='{ opacity: 1}'>

## 使用不同的价格序列
### 收盘价
### Typical Price
### 中间价
### 成交量加权平均
### 移动平均价
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, y:170}'
     :click-6-9='{ opacity: 1}'>

## 使用不同的价格序列
## 使用不同的平滑方式

</div>

<div class='abs' v-motion
     :enter='{opacity: 0, y:170}'
     :click-7-9='{ opacity: 1}'>

## 使用不同的价格序列
## 使用不同的平滑方式
## 归一化
</div>

<FlashText v-click="[8,9]"
           class='abs mt-110 text-center w-full text-3xl'>

Awesome Oscillator
</FlashText>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-9='{ scale: 1}'>

```python{all|6|all}{at:10}
def calc_pao(df):
    mp = (df.high + df.low) / 2
    mp5 = mp.rolling(5).mean()
    mp34 = mp.rolling(34).mean()

    pao = (mp5 - mp34)/(mp5+mp34) * -1
    return pao

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

np.random.seed(78)
alphatest(2000, start, end, calc_pao)
```
</NoteCell>

<!--

这里是一些振荡器指标，多数是我们已经学习过的。最后一个新一点，直到1995年才发表。

也就是说，从1960年代起，历时近40年，技术大师们还在推陈出新。在此之后有没有？当然也有可能还有更新的，因为我的阅读量有限，所以这里没有包含进来。

这些指标在构造上，有何不同，是如何迭代的呢？

[click]

乖离率中使用了收盘价。


[click]

CCI使用了Typical Price，也就是 High + Low + Close / 3。


[click]

到了Awesome Oscillator这里，它使用的是中间价，即 (High + Low) / 2。注意在表格中我们没有明确地标出来它使用的价格序列，只写了是SMA。这个SMA是作用在中间价上的。我们这样写，是为了书写简单、美观。

[click]

成交量加权平均，在这些技术指标中没有体现出来，但我们在alpha101中看到的比较多了。

[click]

最后，还可以使用平滑过后的价格序列。这在除了CCI之外的所有公式中都体现出来了

[click]

我们看到最多的平滑方式是SMA和EMA。我们在之前也讲过，如果我们是做底层开发，最好只使用SMA，以免扭曲信号；如果已经是在生成因子了，而不是作为中间结果，那么可以考虑一些更高级的平滑方式。

[click]

其三，是否使用归一化，以及使用哪一种归一化方式。如果不进行归一化，我们没办法将因子在不同的资产标的之间进行比较；如果进行归一化的话，那么，每一种归一化方式，它提取和丢失的信号都是不一样的，并且它的响应灵敏度和响应区间也不一样。这是我们量化24课中的内容。


[click]

最后我们讲一下这个新的指标， awesome ascillator，1995年，由bill williams提出。这个williams跟larry williams没有关系。我们这里也检验一下这个因子。


[click]

<run></run>

awesome oscillator本身没有做归一化，所以，在这里我们只好越俎代庖，帮他做一下归一化。


[click]

我们使用的归一化方法是这里的第6行。它的理论区间是[-1,1]。如果因子检验的结果不太理想，我们也可以考虑是不是归一化方法不对。当然，这里使用的归一化方法没有太大的问题。

这个因子创新的有两点，一个是价格序列，另一个，它使用了一个特殊的时间窗口，即34周期来做平滑。我还不是太明白34这个数字的含义。大家有知道的我们可以交流下。

好，现在我们看一下结果。在简单的测试中，它的年化alpha是11%，似乎不错，但从分层收益来看，主要靠做空。

从结果上来看，似乎并没有cci等这些指标优秀，但是我们可以尝试调优看看。特别是34周期这个数值，不同的市场可能会有所不同。留给大家自己尝试。

-->
