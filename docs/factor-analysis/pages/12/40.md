---
clicks: 7
---

<div class='abs mt-15' v-motion
     :enter='{opacity: 1,x: 400, y:150}'
     :click-1='{ opacity: 0, x:0, y:0}'
     :click-5="{opacity: 0}">

## 节假效应
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-1-5='{ opacity: 1}'>

## 节假效应
### 春节效应
</div>


<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-2-5='{ opacity: 1}'>

## 节假效应
### 春节效应
### 周四/周五效应
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-3-5='{ opacity: 1}'>

## 节假效应
### 春节效应
### 周四/周五效应
## 交割日效应
</div>

<div class='abs w-full ml-30 mt-20' v-motion
     :enter='{opacity: 0, scale: 0.9}'
     :click-3-4='{ opacity: 1}'>

<Table>
head: 月份,股指期货,ETF期权,A50
body:
  - 7月  , 7月19日  , 7月24日  , 无      
  - 8月  , 8月16日  , 8月21日  , 8月29日 
  - 9月  , 9月20日  , 9月25日  , 9月27日 
  - 10月 , 10月18日 , 10月23日 , 无      
  - 11月 , 11月15日 , 11月20日 , 11月28日
  - 12月 , 12月20日 , 12月25日 , 12月30日
</Table>
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0}'
     :click-4-5='{ opacity: 1}'>

## 节假效应
### 春节效应
### 周四/周五效应
## 交割日效应
## 财报批露效应
</div>

<div class='abs mt-15' v-motion
     :enter='{opacity: 0,x: 400, y:150}'
     :click-5='{ opacity: 1, x:400, y:150}'
     :click-6='{ opacity: 0}'>

## Connor's RSI
</div>

<div class='abs mt-50 ml-40' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{ opacity: 1}'>

$$
CRSI = [RSI(3) + RSI(Streak, 2) + PercentRank(100)] / 3
$$
</div>

<div class='abs mt-10' v-motion
     :enter='{opacity: 0, x:350, scale:0.85}'
     :click-7='{ opacity: 1}'>

<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th>date</th>
      <th>close</th>
      <th>ret</th>
      <th>streak</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>2024-11-07</th>
      <td>3470.661</td>
      <td>0.025668</td>
      <td>1</td>
    </tr>
    <tr>
      <th>2024-11-08</th>
      <td>3452.297</td>
      <td>-0.005291</td>
      <td>-1</td>
    </tr>
    <tr>
      <th>2024-11-11</th>
      <td>3470.066</td>
      <td>0.005147</td>
      <td>1</td>
    </tr>
    <tr>
      <th>2024-11-12</th>
      <td>3421.970</td>
      <td>-0.013860</td>
      <td>-1</td>
    </tr>
    <tr>
      <th>2024-11-13</th>
      <td>3439.278</td>
      <td>0.005058</td>
      <td>1</td>
    </tr>
    <tr>
      <th>2024-11-14</th>
      <td>3379.839</td>
      <td>-0.017282</td>
      <td>-1</td>
    </tr>
    <tr>
      <th>2024-11-15</th>
      <td>3330.726</td>
      <td>-0.014531</td>
      <td>-2</td>
    </tr>
  </tbody>
</table>

</div>


<!--

在时间维度上，如果我们不把目光局限于量价数据上，那么可构建的因子会相当丰富。首先是节假日效应。


[click]

在A股，从大的周期上来讲，存在比较明显的春节效应。据统计春节效应的概率80%以上。它的成因主要是

1. 4季度末由于银行存准金考核等制度性因素，导致市面上银根较紧，出现下跌。下跌是为后面的上涨打开了空间
2. 一季度季节性流动性宽松。每年的新增贷款，为了便于安排生产，因此在一季度放款力度最大。散户因为年终奖等原因，资金面也更宽裕。
3. 节后迎来两会，是政策、利好集中释放期，有利于出货。

当然，从量化的角度，我并不建议过多去研究这个因子。因为它的周期太长，并且由于春节不固定，总体上讲这个效应存在，但没那么规律。我们完全可以用其它因子来代替。

[click]

如果我们以一周为周期进行统计，那么会发现，周四容易下跌，有法定砸盘日一说，而周五更容易上涨，尤其是市场处于低迷状态时。


[click]

交割日效应 是指在 A 股中，每逢 ETF 期权交割、A50 指数交割和股指期货交割时，A 股的波动会加大，并且一般表现为下跌的现象。

所以以上一周的情况来看，周四是法定砸盘日，而刚刚过去的周五，正好就是一个交割日。这两天刚好出现下跌。


[click]

如果一家公司治理得很差，就有可能出现财报拖得很晚、甚至延时发布的情况。A股历来有419魔咒一说。是指从1994年4月19日前后，沪指连续暴跌。此后每一年419前后的行情都不太好。

其中一个重要因素，就是年报比较好的公司，年报往往都在此之前报告了。此后往往是财报暴雷的时候。为了避免踩雷，大家就开始减仓，最终形成踩踏。

关于财报批露效应，国外学者也有很多研究。这部分在教材中有提到，大家可以自己查阅。

我们讲的这些效应，都是由人类经济活动的周期性、制度性因素引起的。制度总在变革，因此，在这方面，总是会有新的因子出现。


[click]

最后，我们介绍在只有量价数据的情况下，时间维度上是否存在因子可以构建？

我们在前面提到过一次，就是连续涨跌次数效应。我们当时回测的效果没有特别理想。但是，我们关于涨跌次数也应该成为因子的想法，并非凭空得来。实际上，它是著名的connor's RSI指标的一个构成部分。这个指标在 Nirvana 网站上，售价是近接1000美金。

下面，我们就介绍一下这个$1000美金的指标。


[click]

connor's RSI是由三个指标合成的。首先就是经典的RSI指标，不过在这里一般取3周期，而不是传统的6周期。第二个指标是streak的两周期RSI。第三个指标是我们熟知的排序指标，是当天的涨跌幅在过去20天以来的百分比排序。

我们重点来看看第二个指标, streak是什么意思，如何计算。


[click]

在这个表中，我们只要关注return和streak两列就可以了。这个结果似曾相识，对吧？实际上，它统计的就是连续涨跌次数。
好，具体的实现代码大家参考教材就可以了。

另外，如果大家使用backtrader来做回测的话，这个指标也是在backtrader中提供的。

-->
