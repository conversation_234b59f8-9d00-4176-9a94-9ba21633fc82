---
clicks: 4
---

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 1}'
          :click-1='{ scale: 0}'>

```python
def calc_rsi(df, n=6):
    return ta.RSI(df.close, n)

def calc_aroon(df, n=14):
    return ta.AROONOSC(high=df.high, low=df.low, timeperiod=n)

def calc_ppo(df):
    return ta.PPO(df.close, 12, 16, 0)

def calc_cci(df):
     return ta.CCI(df.high, df.low, df.close, 14)

def calc_stoch(df):
    k, d = ta.STOCH(df.high, df.low, df.close, fastk_period=14, slowk_period=3)
    return d/k
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-1-2='{ scale: 1}'>

```python
def calc_pao(df):
        mp = (df.high + df.low) / 2
        mp5 = mp.rolling(5).mean()
        mp34 = mp.rolling(34).mean()

        pao = (mp5 - mp34)/(mp5+mp34) * -1
        return pao
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-2-3='{ scale: 1}'>

```python
np.random.seed(78)

start = datetime.date(2023,1,1)
end = datetime.date(2023,12,31)
barss = load_bars(start, end, 10)

data = {}
for func in (calc_rsi, calc_aroon, calc_ppo, calc_cci, calc_stoch, calc_pao):
    factor = (
        barss.groupby(level="asset")
        .apply(lambda x: func(x))
        .droplevel(level=0)
    )
    data[func.__name__.split("_")[1]] = factor

factors = pd.DataFrame(data)
factors.tail()
```
</NoteCell>

<NoteCell class='abs mt-10 w-full'
          :enter='{ scale: 0}'
          :click-3-4='{ scale: 1}'>

```python
corr = factors.corr(method="spearman")
corr
```
</NoteCell>

<NoteCell class='abs mt-10 ml-10 w-90%'
          :enter='{ scale: 0}'
          :click-4-5='{ scale: 1}'>

```python
mask = np.triu(np.ones_like(corr, dtype=np.bool8))
fig, ax = plt.subplots(figsize=(6,5))
cmap = sns.diverging_palette(10, 220, as_cmap=True)

sns.heatmap(corr, mask=mask, cmap=cmap, center=0,
            square=True, linewidths=.5, cbar_kws={"shrink": .5})
sns.clustermap(corr, cmap=cmap,figsize=(5,5), annot=True)
fig.tight_layout()
```
</NoteCell>

 <!--

<run></run>

如果我们有多个因子，很自然我们会好奇其中有多少是独立的，又有多少是相似的。回答这个问题的最佳方法是使用协方差矩阵。

假设我们已经有了这样4个因子


[click]

<run></run>

现在，我们在考察awesome oscillator因子，正在考虑要不要把它纳入策略中。


[click]

<run></run>

此时，我们就可以像这样，把所有的因子都集合在同一个dataframe中，然后进行协相关系数验证。

为了加快演示速度，这里我们只使用了10个资产作为样本。

这里我们依次调用了前面定义的因子计算函数，生成的因子保存在一个字典中。最后将它转换成为因子dataframe

这样生成的dataframe是以日期和资产代码为索引的。这一点很关键。


[click]

<run></run>

现在，我们直接调用dataframe的correlation方法。这个方法很强大。它能正确计算的关键是，每一行都是由日期+资产索引的，这样该方法就可以正确地对齐数据。


[click]

<run></run>

我们还可以通过绘图，使得因子之间的关系更加清晰。

从图中可以看出，新因子PAO与rsi很相似，cci和aroon比较接近。当然，这只是我们用10个小样本做出来的结果。如果我们放大到更多的样本，情况很可能有所不同。

下面这个图以聚类的方式，显示了因子之间的聚类关系

从图中可以看出，pao和stoch更接近，cci与rsi更接近，aroon和ppo更接近。所以，如果我们要缩减因子数量，就可以从这些相似的因子中进行剪枝。

-->
