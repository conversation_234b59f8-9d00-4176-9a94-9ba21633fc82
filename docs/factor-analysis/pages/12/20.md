---
clicks: 8
---

<div class='abs flex justify-content w-full items-center h-full' v-motion
     :enter='{opacity: 1, scale: 0.5}'
     :click-1='{ opacity: 0}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/xshg-2024-11-15.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-1-6='{ opacity: 1}'>

## 换手率
## 成交量的变化
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-2-6='{ opacity: 1}'>

## 换手率
## 成交量的变化
## 柴金线
## OBV
</div>

<div class='abs mt-30' v-motion
     :enter='{opacity: 0}'
     :click-2-6='{ opacity: 1}'>

## 换手率
## 成交量的变化
## 柴金线
## OBV
## 主力因子
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, x:250, scale: 0.8}'
     :click-4-7='{ opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/zlyz-tqly.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<FlashText v-click="[5,6]"
           class='abs mt-5 ml-120 w-full text-3xl'>

max_volume_direction
</FlashText>

<div class='abs mt-30 ml-5' v-motion
     :enter='{opacity: 0}'
     :click-6-7='{ opacity: 1}'>

<div>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>vr</th>
      <th>move_balance</th>
      <th>span</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>145</th>
      <td>-0.244453</td>
      <td>-0.910319</td>
      <td>27.0</td>
    </tr>
    <tr>
      <th>146</th>
      <td>0.206077</td>
      <td>-0.874031</td>
      <td>28.0</td>
    </tr>
    <tr>
      <th>147</th>
      <td>-0.286062</td>
      <td>-0.924238</td>
      <td>29.0</td>
    </tr>
    <tr>
      <th>148</th>
      <td>-0.537171</td>
      <td>-1.018631</td>
      <td>30.0</td>
    </tr>
    <tr>
      <th>149</th>
      <td>-0.773724</td>
      <td>-1.155341</td>
      <td>31.0</td>
    </tr>
  </tbody>
</table>
</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, x:100, scale: 0.9}'
     :click-7-8='{ opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/000800-2024-11.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0, x:100, scale: 0.9}'
     :click-8-9='{ opacity: 1}'>

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/sbs-2023-11.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>



<!--

我在第9课中有提到过，技术指标有四大维度，量、价、时、空。在今天的课程中，我们就稍微深入一点展开一下。
我们先看量的维度。

[click]

在量的维度上，我们已经提到过，换手率和成交量的变化可以做为因子


[click]

也介绍了柴金线、OBV等技术指标


[click]

在这一节，我们介绍一个主力因子。它的原理是，价格的波动是由主力资金决定的，而不是散户决定的。在博弈中，主力的资金量大，目标明确且有计划；散户资金量也大，甚至更大，但它们的方向不明确，没有计划，所以散户资金的矢量和可以看作为零，从而价格的运动方向由主力决定。

这就是羊群效应。关于羊群效应，我认为它是交易中的几个第一性原理之一。其它几个第一性原理，在教材中有，请大家去看教材。

下面，我们就介绍这个因子。这个因子的使用周期最好是日内，比如30分钟或者5分钟。

[click]

这是天齐锂业在2024年11月的一个走势。我们看到图中有三个向上的三角箭头，代表主力的进攻或者试盘。在每次试盘之后，主力会停止攻击，静观其变。发现没有对手盘，后面就会再次拉升。

[click]

这个因子的代码在教材里。因子构造函数是 max_volume_direction，最大成交量方向。它会返回一个dataframe，其中有3列是我们要关注的


[click]

vr列是当前的bar的成交量，与之前n个bar的平均成交量的量比。它的取值可能为正，也可能为负。如果为正，表明当前bar是阳线，如果为负，表明为阴线

move_balance借鉴了OBV，但与之不同的是，它是从前一个最大成交量之后开始算起的。

假设我们当前的时间点在这里，前一个最大成交量在这里，那么它计算的就是这两个时间点之间的on balance volume，并且进行了归一化。如果时间往后延一期，那么起点仍然不变，把新的这一期的成交量也加进来算obv。

最终得到的结果如果与vr同向，表明主力停止攻击之后，跟风资金响应了主力的方向，这是一种最好的情况；否则，表明主力的意愿没有得到响应。这时候就要看move_balance与vr的比值。这是因子分析或者机器学习可以发力的地方。

span则表明当前bar距离上一次主力攻击的时长。这个时间越久，主力的意愿就衰减得越厉害。


[click]

这是另外一支。


[click]

但是，这个因子只能在日内使用，建议使用30分钟的周期。这个图显示了我们在日线级别上计算因子的结果。在高位的信号都可以忽略，但我们看到有一个在底部的信号，它箭头朝上，但随后却是下跌。这是怎么回事呢？

实际上，如果我们使用30分钟的数据，就能看出来，这里最大成交量方向，或者随后的move_balance是给出了做空的信号的。


-->
