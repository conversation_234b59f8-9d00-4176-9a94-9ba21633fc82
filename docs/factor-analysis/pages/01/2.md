---
title: 这门课要解决什么问题？
layout: default
clicks: 4
---

<style scoped>

circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 1px 2px 5px rgba(0,0,0,0.2);
    font-size: 30px;
    flex-flow: column;
}
</style>

<div class="abs w-full h-80% top-20%"
    v-motion
    :enter="{x:0}"
    :click-2="{x:1280}"
>
    <img src='https://images.jieyu.ai/images/2024/02/量化交易策略的基本结构.jpg' style="height:80%;width:60%" >
</div>

<!-- click-1-->
<div v-motion
    :enter="{scale:0}"
    :click-1="{scale:1}"
    :click-2="{scale:0}">
<v-drag-arrow color="red" pos="96,359,309,-85" />
<v-drag-arrow color="red" pos="95,359,136,-199" />
</div>

<div class="abs top-160px left-0 h-200px w-full z-index--1 grid grid-rows-1 place-items-center">

<!-- click-2 -->
<div v-motion class="w-200px h-full"
    :click-2="{scale:1,x:0}"
    :click-3="{x:-140}"
    :click-4="{x:-240}"
    :click-5="{scale:0}"
    :enter="{scale:0,x:0}"
>

<circle style="border: 1px solid green;">挖因子</circle>
<p>如何找到新的因子</p>
</div>

<!-- click-3 -->
<div v-motion class="w-200px h-full" 
    :click-3="{scale:1, x:140}"
    :click-4="{x: 240}"
    :click-5="{scale:0}"
    :enter="{scale:0}"
>

<circle style="border: 1px solid red;">检验因子</circle>
<p>新的因子有效吗？</p>

</div>

<!-- click-4 -->
<div v-motion class="w-200px h-full" 
    :click-4="{scale:1,y:-210}"
    :click-5="{scale:0}"
    :enter="{scale:0}"
>

<circle style="border: 1px solid blue">组装因子</circle>
<p class="ml-2">如何构建策略？</p>

</div>

</div>

<!--
我们先来看，这门课要解决什么问题？

这是打开量化投资的黑箱里的一张图。它表明了一个量化系统的各个组成部分。

[click]

我们这门课程主要研究阿尔法模型和投资组合构建模型。更通俗一点说，

[click]

就是一、如何挖因子。这涉及到哪些技术、算法？有哪些方法论？有哪些现成的资源？

[click]

二、假设我们有一个关于因子的想法，我们如何在把它加到投资系统之前，就能对它快速进行检验？这一部分就涉及到因子检验，原理和框架

[click]

三、如何组合这些因子，构建一个投资策略？这一部分中，我们将跳过传统的均值-方差模型，直接讲解如何构建基于机器学习的模型。

-->
