---
clicks: 1
left: 80%
---

<NoteCell class="abs w-full h-full ml-30">

```python
np.random.seed(78)

def calc_rsi(df, n):
    return 100 - ta.RSI(df.close, n)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

bins = np.linspace(0, 100, 10)
alphatest(2000, start, end, calc_factor=calc_rsi, args=(11,), top = 5, bins=bins, long_short=False)
```

</NoteCell>

<!--

在我们的实验中，因子的年化alpha收益最高达到了35%，累积收益也很不错。样本外检测也表明参数是稳健的，没有过拟合。

显然大家会问，因子表现这么好，是不是我们离成为首富只差一个开户的距离了？

如果说我们的课程已经很深入了，但要探索投资的秘密，我们还得挖得更深一些。在前面的测试中，影响收益的因素还有很多我们没考虑进去。

首先，alphalens并不是标准的做多因子top分层，做空因子bottom分层。如果这样做，收益可能更高一点。这是正向的修正。

其次，我们也要看到，对收益还存在负向修正的因素。比如，在之前的测试中，我们一直使用的是多空组合。

但如果你看得更深入一点，收益主要是做空得来的。但是，如果我们所处的市场是A股，那么，你所在的机构够不够资格去做空？特别是，能不能针对个股融券做空？

这段代码是纯多的策略，其它参数不变。我们运行一下这段代码。

从结果上看，年化Alpha只有3.7%。

第三，Alphalens看到的都是整体。我们在预处理中，也没有去掉极值。这当中我们有没有做空高位股？如果有的话，大家可以想像一下，这些股是否会贡献巨大的收益？

所以，我们的课程主要是帮助大家建立一个系统，能够精通相关的方法和技巧。从我们的示例来看，量化确实存在很大的机会，但需要你在精通课程讲述的方法和技巧之后，还要更深入地去研究和挖掘。
-->
