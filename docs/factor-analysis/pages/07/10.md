---
clicks: 19
right: 60%
---

<div class="abs" v-motion :enter="{opacity:1}" :click-1="{opacity:0}">

## 拉取数据
## 生成因子
## 提取价格
## 预处理
## 生成分析报告
</div>

<div class="abs" v-motion :enter="{opacity:0}" :click-6-7="{opacity:1}">

## plot_mode: quantiles

![](https://images.jieyu.ai/images/2024/08/slope-factor-quantile.jpg)
</div>

<div class="abs" v-motion :enter="{opacity:0}" :click-7-8="{opacity:1}">

## plot_mode: returns

![](https://images.jieyu.ai/images/2024/07/returns-analysis.jpg)
![](https://images.jieyu.ai/images/2024/08/mwpr-good.jpg)
</div>

<div class="abs" v-motion :enter="{opacity:0}" :click-8-9="{opacity:1}">

## plot_mode: full
</div>

<div class="abs" v-motion :enter="{opacity:0}" :click-9-10="{opacity:1}">

## plot_mode: None

</div>

<NoteCell :enter="{x: '-110%', y:50}"
          :click-13-14="{x: '50%'}">

```python
df = pd.DataFrame({
    "A": ["aaron", "jason", "aaron"],
    "B": [60, 100, 70],
})

if getattr(df, "name", None) is None:
    print("df has no attribute name")
else:
    print("df has attribute name:", df.name)

df.groupby("A").apply(lambda x: print(type(x), x.name))
```
</NoteCell>

::right::

<div class="abs" v-motion :enter="{scale: 1}" :click-1="{scale: 0}">

```python

# 0. 定义因子计算函数
def calc_factor(...):
    pass

# 1. 获取行情数据
start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 12, 29)
barss = load_bars(start, end, universe=("000001.XSHE",))

# 2. 计算因子
factors = barss.groupby(level='asset').apply(wrapper)
factors = factors.swaplevel(0,1)

# 3. 提取价格数据
prices = barss['price'].unstack(level=1)

# 4. 预处理
merged = get_clean_factor_and_forward_returns(factors, prices, ...)

# 4. 生成报告
create_full_tear_sheet(merged)
```
</div>

<div class="abs ml--85 w-900px" v-motion
    :enter="{scale: 0,x: 0}"
    :click-1="{scale: 1}"
    :click-6="{x: 500}"
    :click-10="{x: 0}"
    :click-13-14="{x: 1000}">

```python {all|2-4|5-6|7-9|10|10|10|10|10|11|32|34-38|34-38|34-38|40-41|45-53|55-58|60|62-70}{maxHeight: '450px',at:2}
def alphatest(
                universe: int|Tuple[str], 
                start: datetime.date, 
                end: datetime.date, 
                calc_factor: Callable[[NDArray, Tuple[Any, ...]], Any], 
                args=(), 
                bins=None, 
                top=-1, 
                bottom=-1, 
                plot_mode='returns', 
                **kwargs):
    """因子检验

    Args:
        universe: 样本数量，或者指定的资产池
        start: 开始日期
        end: 结束日期
        calc_factor: 计算因子的函数
        args: 计算因子的函数的参数
        bins: number of cut by bins, or cut bin edges
        top: top 分层
        bottom: bottom 分层
        plot_mode: 绘图模型。 
            'full' -> create_full_tear_sheet, 
            'returns' -> create_returns_tear_sheet, 
            'quantiles' -> plot_quantile_statistics_table,
            'none' -> 不绘图，只返回回测结果。
    kwargs:
        传递给 get_clean_factor_and_forward_returns 的参数
    """
    # 加载数据
    barss = load_bars(start, end, universe)

    # 计算因子
    factors = (barss.groupby(level='asset')
                    .apply(lambda x: calc_factor(x, *args))
                    .droplevel(level=0)
                )

    # 提取价格数据
    prices = barss['price'].unstack(level=1)

    long_short = kwargs.pop('long_short', True)

    # 预处理
    if bins is not None:
        factor_data = get_clean_factor_and_forward_returns(factors, prices, quantiles=None, bins=bins, **kwargs)
    else:
        if 'quantiles' in kwargs:
            quantiles = kwargs.pop('quantiles')
        else:
            quantiles = 10
        factor_data = get_clean_factor_and_forward_returns(factors, prices, quantiles=quantiles, **kwargs)

    if top != -1:
        factor_data = factor_data[factor_data.factor_quantile <= top]
    if bottom != -1:
        factor_data = factor_data[factor_data.factor_quantile >= bottom]

    alpha = factor_alpha_beta(factor_data, demeaned = long_short)

    if plot_mode == 'quantiles'
        plot_quantile_statistics_table(factor_data)
    elif plot_mode == 'returns':
        create_returns_tear_sheet(factor_data, long_short=long_short)
    elif plot_mode == 'full':
        create_full_tear_sheet(factor_data, long_short=long_short)
    else:
        pass
    return alpha, factor_data
```
</div>

<!--
因子检验主要有这样几步，提取数据、生成因子、因子预处理和生成分析报告。

如果一切顺利，这样我们就得到了一个基础的报告。但是，它可能并没有真正揭示因子的潜力。

在揭示因子真正的潜力的过程中，我们可能要微调因子计算的参数，改变分层方式，对比多空组合和纯多组合的结果等等。

这样就构成了一个复杂的参数搜索过程。显然，我们并不想在参数搜索的过程中，完全重写右边的代码。

因此，我们在讲解参数优化方法之前，先对上述过程进一步封装。我们要达到的目的是，只通过这一个函数的调用，结合不同的参数，就可以完成多次分析。

[click]

这里定义的alphatest就是我们要实现的函数。

要注意它只适合在本课程环境中使用，因为它绑定了行情数据加载函数。在大家自己的研究中，可以把load_bars稍作修改即可

[click]

这三个参数是用来决定如何加载行情数据。

[click]

这两个参数用来传入自定义的因子计算函数。args是因子计算参数，通过列表方式传入。

[click]

这一组参数用来决定因子的分层，以及哪些分层参与性能分析。

[click]

在调优过程中，我们一开始可能只希望生成部分报告，以免生成的报告过多，占用时间过长。只有当我们确定了几组比较好的参数时，才需要生成全部报告。这里的模式有 quantiles，returns, full和None四种

[click]

在quantiles模式下，将会生成因子分层视图。这将帮助我们了解一开始的因子分层是否合理，以便调整分层方法和参数。

[click]

在returns模式下，只显示收益分析，这将包括收益概览、因子分层收益图和累积分层收益等。只有在这些结果比较好的时候，我们才需要进一步分析验证它的可靠性。

[click]

如果某组参数得到较好的结果，我们就启动这个绘图模式，全面阅读分析报告。

[click]

我们也可以不绘制任何图表，而是仅仅收集最基础的数据，然后转其它工具进行比较分析

[click]

如果还需要对模型分析的过程进一步微调，我们可以通过kwargs来指定。这些参数将传递给get_clean_factor_and_forward_returns函数。

[click]

下面我们就进入到实现代码中。这一行获取行情数据

[click]

这里我们计算因子。注意我们给calc_factor函数传递的第一个参数是一个dataframe。

这里有一个冷知识，就是这个dataframe有一个特殊的属性 -- name 属性。

通常情况下，我们创建的DataFrame是没有name属性的。

[click]

我们通过这个小例子快速检查一下。

我们先是生成了一个普通的DataFrame, 通过第6~9行代码，验证它没有name属性。

然后我们将这个dataframe进行groupby.apply操作，再打印出apply参数的类型和name属性值。

现在我们运行一下。发现普通的DataFrame没有name属性，但是apply中的参数是DataFrame类型，并且有name属性。name属性的值就是分组的key。

好，这是一个小知识点。

[click]

calc_factor是一个回调函数。我们刚刚讨论了它的输入参数。

那么在输出数据格式上，有什么要求呢？

alphatest对calc_factor的返回值是有点小要求的。它必须是Series，以便groupby能将它组装起来，并且保持正确的index信息。

这是我们在为alphatest写计算因子的回调函数时要注意的。

[click]

这一段是提取价格数据

[click]

这一段代码是对因子进行预处理，主要是分层和远期收入计算。

Alphalens的get_clean_factor_and_forward_returns在bins和quantiles参数传递上，有一个不太合理的地方，就是当你指定了bins时，还需要显式地指定quantiles为None。

在这里，我们对它进行了简化。如果传入了bins参数，那么就不再判断quantiles参数。

[click]

这一段是用于优化的代码。它是在分层之后，过滤掉不符合要求的因子。我们马上就会深入讲解它的作用。

[click]

无论plot_mode是哪种模式，我们都会计算因子收益。

[click]

最后，根据指定的参数，决定哪些图要绘制。
-->
