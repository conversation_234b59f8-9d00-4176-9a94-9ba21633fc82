---
clicks: 2
---

<div class="abs h-full w-full" v-motion :enter="{x: 200, y: 80}" :click-1="{y: -300}">

![](https://images.jieyu.ai/images/2024/10/non-linear-factor.jpg)
</div>

<NoteCell class="abs w-95%" :enter="{y:500,x:250}" :click-1="{y:0}" :click-2="{x: 0}">

```python {all}{lines: false}
def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

alphatest(2000, start, end, 
          calc_factor=calc_rsi, 
          args=(6,))
```
</NoteCell>

::right::

<NoteCell :enter="{opacity:0}" :click-2="{opacity: 1}">

```python {all}{lines: false}
def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

alphatest(2000, start, end, 
        calc_factor=calc_rsi, 
        args=(6,), 
        top=5 )
```
</NoteCell>

<!--

因子与收益之间的关系还可能是局部线性、整体非线性。这是原始的RSI因子，在2018到2023年间的分层收益均值图。

从整体上看，因子与收益并没有明显的线性关系。但是，它比较明显地呈现两端低，中间高的特征。如果我们只用其中一部分，会怎么样呢？

[click]

这是使用全部分层时的代码

[click]

右边是只使用1到5层因子的代码。注意两段代码的不同之处，右边的代码多了最下面的top = 5

现在，我们让两段代码都运行起来，对比一下分析结果。

左边的代码将全部分层都纳入因子分析，得到的年化alpha是8.3%；右边只使用了1到5层的因子，得到的年化alpha是22%。6年的累积收益到到2.9倍。

我们使用了6年的时间进行回测，样本数占了A股总数的一半（在2018年总共只有1800支左右，所以实际上是占了一半多）。从累积收益图上看，收益曲线增长平稳、一致性好，所以如果这个因子可以交易的话，会是很不错的因子。

-->
