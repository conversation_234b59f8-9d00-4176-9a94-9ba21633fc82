---
clicks: 2
---

<div class="abs" v-motion :enter="{x: 400, y: 100}" :click-1="{y: -200}">

<ion-ios-lightbulb-outline class="text-8xl text-yellow-400 animate-bounce"/>

</div>

<NoteCell class="abs w-98%" :enter="{y:500,x:250}" :click-1="{y:0}" :click-2="{x: 0}">

```python {all}{lines: false}
def low_volatility(df, win:int, *args):
    return df.close.pct_change().rolling(window=win).std()


np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

alphatest(500, start, end, calc_factor=low_volatility, args=(10,))
```
</NoteCell>

::right::

<NoteCell class="abs w-full" :enter="{opacity:0}" :click-2="{opacity: 1}">

```python {all}{lines: false}
def low_volatility(df, win:int, *args):
    vol = df.close.pct_change().rolling(window=win).std()
    return 1 / (1e-7 + vol)

np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

alphatest(500, start, end, calc_factor=low_volatility, args=(10,))
```
</NoteCell>

<!--

因子往往来源于一个经验、一个想法。这种想法暗含着某种因素与收益的正向关联性。

但当我们把这种因素转化为因子时，其方向性可能与本来的想法不同。

比如，像市盈率因子，它背后的理念是，市盈率越低，公司的盈利能力越强，因此资产价格在未来就越可能上涨。

但是，如果我们直接以市盈率为因子，就不满足因子值越大、未来收益越高的假设。因此，直接使用市盈率本身，接下来的收益分析就可能让我们放弃这个因子。

在这种情况下，我们需要对因子的方向进行修正，以满足正相关性的假设。这部分在前面的课程中也讲过了。

在这里为了内容的完整性，我们略微提一下。

好，现在我们就以低波动率因子为例，对比一下修正前后的情况，也介绍下alphatest函数如何使用。

[click]

<run></run>
 
由于前面定义了alphatest函数，现在我们进行因子检验就更加简单。在定义了因子计算函数之后，只需要一行代码就可以完成因子检验。

这是一个十日低波动因子。我们看到，因子值越大，未来收益越差。实际上，它反映了我们寻找因子的思路是正确的，也就是波动率越大，未来收益率越低；越是低波动的资产，未来收益越好。

现在，我们调整一下因子的方向。

[click]

<run></run>

调整方法有很多种，比如乘以-1，或者取倒数。这里我们采用取倒数的方法。

我们运行一下调整后的因子。大家对比看一下结果。调整后的因子，取得了18.5%的年化alpha，从2018年到2023年底，累计收益是2.3倍左右。如果我们不进行调整，那么得到的将是左边的结果。

显然，左边的结果会导致我们放弃这一因子。

-->
