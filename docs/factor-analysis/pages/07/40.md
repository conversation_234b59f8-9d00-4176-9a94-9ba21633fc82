---
clicks: 1
---

<NoteCell :enter="{scale: 1}" :click-1="{scale: 0}">

```python {all}{lines: false}
def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

alphatest(2000, start, end, 
          calc_factor=calc_rsi,
          args=(6,),
          quantiles=10)
```
</NoteCell>

<NoteCell :enter="{scale: 0}" :click-1="{scale: 1}">

```python {all}{lines: false}
def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

alphatest(2000, start, end, 
          calc_factor=calc_rsi,
          args=(6,),
          quantiles=10,
          top=5)
```
</NoteCell>

::right::

<NoteCell :enter="{scale: 1}" :click-1="{scale: 0}">

```python {all}{lines: false}
def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

alphatest(2000, start, end,
        calc_factor=calc_rsi,
        args=(6,),
        bins=np.linspace(0,100,10))
```
</NoteCell>

<NoteCell :enter="{scale: 0}" :click-1="{scale: 1}">

```python {all}{lines: false}
def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)

alphatest(2000, start, end,
        calc_factor=calc_rsi,
        args=(6,),
        bins=np.linspace(0,100,10),
        top=5)
```
</NoteCell>

<!--

在上一讲中，我们就提到过像RSI这样有明确交易含义的因子，我们应该使用by bins的方法来分层。但我们只讲了原理，并没有给出收益对比。

现在，我们就对比一下两种分层方式对收益的影响。

[click]

<run></run>

首先是将全部分层都纳入分析，在这种情况下，对比两种分层方式的影响。

我们看到使用bins分层的年化要比by quantiles高1.6%左右。两者都是中间高，两头低的模式，

接下来我们再对比只使用部分分层的情况

[click]

<run></run>

采用部分分层之后，两者对比，仍然是by bins更好，年化收益大约高了4%左右。不过，我们也看到，by bins分层时，收益更多地是由做空低因子值的交易贡献，也就是要做空高RSI。这对交易条件施加了一定的限制。
-->
