---
clicks: 3
left: 90%
---

<NoteCell hideOutput>

```python {all|11|12|all}
def calc_rsi(df, n):
    return 100 - ta.RSI(df.close.astype(np.float64), n)

np.random.seed(78)

start = datetime.date(2018, 1, 1)
end = datetime.date(2023, 12, 31)
bins = np.linspace(0, 100, 10)

alphas = []
for win in range(3, 15):
    alpha, _ = alphatest(500, start, end, calc_factor=calc_rsi, args=(win,), top=5,bins=bins,plot_mode='None' )
    alphas.append((win, alpha.iloc[0,0]))

clear_output()
pd.DataFrame(alphas, columns=['win', 'alpha']).set_index('win').plot(title='Alpha', figsize=(6,2))
```
</NoteCell>

<!--

<run></run>

因为我们将因子检验代码重构成了一个函数，所以可以很方便地实现参数搜索。

我们在之前的试验中，计算RSI时都是使用的6天的窗口。这个窗口不一定是最佳的，所以，我们希望通过尝试，找到最佳的窗口。

这段代码将测试rsi窗口大小对alpha的影响。

[click]

我们将搜索3~15的窗口。

[click]

设置绘图模式为None，这样我们将只收集alpha值，而不会绘制任何报表

[click]

现在看看结果运行的结果。在演示中，为了加快速度，我们只抽样了500个标的，大家在实际使用中，可以适当调整。

从运行的结果来看，似乎RSI为9是最优的。接下来，我们就可以针对这一参数，查看详细报告。

-->
