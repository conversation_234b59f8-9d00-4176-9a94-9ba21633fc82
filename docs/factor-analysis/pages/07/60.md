---
clicks: 10
right: 70%
---

<div class="abs ml-50 w-full" v-motion :enter="{opacity: 1}" :click-1="{opacity: 0}">

![](https://images.jieyu.ai/images/2024/10/parameter-plateau-single-param.jpg)
</div>

<div class="abs" v-motion :enter="{opacity: 0}" :click-3-4="{opacity: 1}">

$$
\begin{gathered}
\begin{bmatrix} 1 \\ 2 \\ 3 \end{bmatrix}
\quad
\begin{pmatrix} 4 & 5 \end{pmatrix}
\quad
\end{gathered}
$$

$$
\begin{gathered}
\begin{bmatrix}
(1,4)
\\
(1,5)
\\
(2,4)
\\
(2,5)
\\
(3,4)
\\
(3,5)
\\
\end{bmatrix}
\end{gathered}
$$

</div>

<div class="abs bottom-0 z-10 w-90%"  v-motion :enter="{scale:0}" :click-5-9="{scale: 1}">

![](https://images.jieyu.ai/images/2024/10/mesh-grid.jpg)
</div>

<NoteCell class="abs w-90%" :enter="{scale: 0}" :click-5-9="{scale: 1}" hideOutput>

```python {all|3,4|7|9-16}{at:6}
import numpy as np

win = np.array([1,2,3,4])
top = np.array([5,6,7])

# 使用 np.meshgrid 生成网格数据
X, Y = np.meshgrid(win, top)

print("win:", win)
print("top:", top)

print("\nwin * len(top) -> X:")
print(X)

print("\ntop * len(win) -> Y:")
print(Y)
```
</NoteCell>

::right::

<NoteCell :enter="{scale: 0, x:0}" :click-1="{scale: 1}"
    :click-5-9="{x: 200}" hideOutput>

```python {all|18|18|29-33|30|30|30|30|37,38|all}{maxHeight: '450px',at:2}
from itertools import product
import plotly.graph_objects as go
 
np.random.seed(78)

def calc_rsi(df, n):
    return 100 - ta.RSI(df.close, n)

start = datetime.date(2017, 1, 1)
end = datetime.date(2022, 12, 31)
bins = np.linspace(0, 100, 10)

alphas = []

win = np.arange(6,10)
top = [4,5,6]

for win_, top_ in product(win, top):
    alpha, _ = alphatest(100, start, end, 
                      calc_factor=calc_rsi, args=(win_,), 
                      top=top_, 
                      bins=bins,
                      plot_mode=None)
    alphas.append(alpha.iloc[0,0])

fig = plt.figure()

# 创建 3D 表面图
ax = fig.add_subplot(111, projection='3d')
z = np.array(alphas).reshape((len(win), len(top)))

X, Y = np.meshgrid(top, win)
surf = ax.plot_surface(X, Y, z, cmap='viridis')

fig.colorbar(surf, shrink=0.5, aspect=5)

# 设置俯仰角为30度，方位角为60度
ax.view_init(elev=30, azim=60)
ax.set_title('RSI 参数高原图')
ax.set_xlabel('top')
ax.set_ylabel('win')
ax.set_zlabel('Alpha')

plt.show()
```
</NoteCell>

<!-- 

<run></run>

我们首先介绍参数高原理论

参数高原理论来自于机器学习，它是指在参数空间中存在的一片相对平坦的区域。在这片区域内，目标函数的取值的变化不大。

这意味着在高原上的不同参数设置，引起模型的表现差异很小。高原的存在，一般意味着模型在参数空间中的表现是稳定的。

我们在之前已经绘制过单参数的参数高原。它是一个二维图。

假设我们有两个参数是可调整的，那么就应该绘制3D图，来看两个参数联合时对收益的影响。

[click]

我们通过这段代码来生成一个三维参数高原图。它的想法是对前一个图的简单扩展。

[click]

我们通过 itertools中的product函数，把参数的各种可能性组合出来。

[click]

它将这样生成参数组合。实际上是两个数组的笛卡尔积。注意它的展开顺序。第一个参数是外循环，可以看成行，第二个参数是内循环。可以看成列

[click]

这几行是绘制三维参数高原图的核心代码。

首先我们要通过projection='3d'定义绘制的是三维表面图。

30行是准备x,y的数据。

这里涉及到如何确定哪个轴是x，哪个轴是y的问题，这是绘制图形的关键。如果弄错了，仍然有可能绘制一个图形出来，但它会是错的。

在我们生成alphas数组的过程中，win是外层循环，top是内层循环，是变动最快的变量，一般把它看成列；win就对应着行。


所以，在第30行，准备z轴的数据时，我们把len(win)放在前，len(top)放在后。

然后调用plot_surface来生成3D图。

[click]

这里我们对meshgrid深入介绍一下。代码很简单，win和top是转换前的数组，通过meshgrid转换成X,Y

然后我们比较转换前后的数据。

[click]

win和top是输入参数，分别对应x和y

[click]

这是进行转换

[click]

这是输出转换前后的对比。我们运行一下代码。

所以，实际上它就是将win和top进行了repeat操作。在别的地方，我们也可能看到不用meshgrid，直接将坐标轴数据进行repeat的做法，本质上是一样的。

[click]

这一行的作用，是旋转视图的角度，使得图形以最方便观察的角度呈现出来。这个角度需要根据具体的图形来调整。

[click]

现在，我们来看看生成的图形。

在这里，我们使用的是matplotlib，所以它只会显示一个静态的图片。在教材中，这段代码是用plotly写的，所以它会呈现一个交互式的3D图。你可以通过拖动来来旋转图形，从而可以以不同的角度来观察它。

由于时间限制，这里只用了少量参数。大家可以在课程环境中，修改参数运行，体验一下参数高原的观察和评估。

-->
