<NoteCell class="abs w-full left-40% h-full">

```python
np.random.seed(78)

def calc_rsi(df, n):
    return 100 - ta.RSI(df.close, n)

start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
bins = np.linspace(0, 100, 10)

win = 11
top = 4
alpha, _ = alphatest(2000, start, end, 
                    calc_factor=calc_rsi, args=(win,), 
                    top=top, 
                    bins=bins)
```
</NoteCell>

<!--

样本外检测指把整个数据集划分为互不重叠的训练集和测试集，在训练集上训练模型，在测试集上进行验证。如果模型在测试集上也表现良好，就认为该模型没有过拟合。

这个概念是机器学习中的基础概念之一，但也对因子分析适用。

通过前面的参数搜索和参数高原，我们确定top=4,win=11时，因子收益最高，同时也比较稳健。现在，我们就要用样本外检测来验证这个模型。

在这段代码中，我们使用的参数是win=11，即使用11日窗口来计算RSI，top=4，即只把因子分层在1~4层之间的纳入策略。

运行的结果表明，使用参数top=4, win=11，在样本外检测中，尽管没有回测时表现那么好，但模型表现依然不错。
-->
