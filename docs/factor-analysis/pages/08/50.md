---
clicks: 4
layout: default
---

<NoteCell class="abs w-full mt-10" >

```python {all|24-27|30|all}{maxHeight: '450px'}
def rank(df):
    return df.rank(axis=1, method="min", pct=True)

def ts_argmax(df, window=10):
    return df.rolling(window).apply(np.argmax) + 1

def stddev(df, window=10):
    return df.rolling(window).std()

def alpha001(close, returns):
    inner = close.copy()
    inner[returns < 0] = stddev(returns, 20)
    return rank(ts_argmax(inner**2, 5)) - 0.5

start = datetime.date(2022, 1, 1)
end = datetime.date(2022, 12, 31)

bars = load_bars(start, end, 500)
close = bars.close.unstack("asset")
returns = close.pct_change()

factor = alpha001(close, returns)

factor = (factor.reset_index()
          .melt(id_vars='date', var_name="asset", value_name="factor")
          .set_index(["date", "asset"])
         )
prices = bars.price.unstack(level=1)

merged_factor = get_clean_factor_and_forward_returns(factor, prices, quantiles=None, bins=5)
create_returns_tear_sheet(merged_factor)
```
</NoteCell>

<!--

<run></run>

最后我们把1号因子用Alphalens跑一下，看看结果。完成这一步后，我们就实现了Alpha101从公式到因子分析的完整流程。

。这个因子的计算量比较大，我们只取一部分数据进行演示。我们将把它作为练习题，请大家参考这里的代码，自己进行实现和练习。

[click]

注意它的因子计算中，同时用了横截面和时序计算，前面几章中，我们因子计算都是在时序上的，只是在最后分析时，才借由alphalens实现了截面上的分析。

因为这个原因，这里我们不能复用alphatest函数。在alpha001计算完成之后，它的数据仍然是一张宽表，不符合Alphalens的输入要求，我们要通过melt方法，将其转换为由日期和资产索引的长表。

[click]

从前面的分析可以看出，这个因子的取值相当于离散值，并且只有5个，所以，我们只能对它分层时，只能分5层。并且我们要按bins进行分层。大家可以尝试下，如果按quantiles进行分层，会出现什么样的错误。

[click]

最后我们看一下结果。

-->
