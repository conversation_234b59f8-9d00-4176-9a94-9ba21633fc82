---
clicks: 17
layout: default
---

<div class="abs mt-20" v-motion :enter="{opacity: 1}" :click-3="{opacity: 0}">

# alpha001

```python
(rank(Ts_ArgMax(SignedPower(((returns < 0) ? stddev(returns, 20) : close), 2.), 5)) -0.5)
```

<v-drag class="abs w-full z-10" pos="244,40,337,60"><Box /></v-drag>
</div>

<div class="abs mt-50" v-motion :enter="{opacity: 0}" :click-1-3="{opacity: 1}">

```python
(rank(Ts_ArgMax(SignedPower(ternary_op((returns < 0), stddev(returns, 20), close), 2.), 5)) -0.5)
```

<v-drag class="abs w-full z-10" pos="239,-11,400,57"><Box /></v-drag>
</div>

<div class="abs mt-80 w-full" v-motion :enter="{opacity: 0, y:0}" 
    :click-2="{opacity: 1}"
    :click-3="{y: -280}"
    :click-4="{opacity: 0}"
    >

```python
(
    rank(
        Ts_ArgMax(
            SignedPower(ternary_op((returns < 0), stddev(returns, 20), close), 2.0), 5
        )
    )
    - 0.5
)
```
</div>

<NoteCell class="abs mt-60 w-full" v-motion :enter="{scale: 0}" 
    :click-3-4="{scale: 1}"
    >

```python {all}{maxHeight: '400px'}
def alpha001(close, returns):
    return rank(
        Ts_ArgMax(
            SignedPower(ternary_op((returns < 0), stddev(returns, 20), close), 2.0), 5
        )
    ) - 0.5

for name in ("rank", "Ts_ArgMax", "SignedPower", "ternary_op", "stddev"):
    globals()[name] = lambda name, *args: print(f"called {name} with {args}")

close = np.array([1, 2, 3, 4, 5])
returns = np.arange(5)
alpha001(close, returns)
```
</NoteCell>

<NoteCell class="abs w-full mt-10" v-motion :enter="{scale: 0}"
    :click-4-20="{scale: 1}">

```python {all|1-8|10-13|15-21|10-13|10-13|4-5|13|1-2|10-13|all|all|13}{at:5}
def rank(df):
    return df.rank(axis=1, method="min", pct=True)

def ts_argmax(df, window=10):
    return df.rolling(window).apply(np.argmax) + 1

def stddev(df, window=10):
    return df.rolling(window).std()

def alpha001(close, returns):
    inner = close.copy()
    inner[returns < 0] = stddev(returns, 20)
    return rank(ts_argmax(inner**2, 5)) - 0.5

start = datetime.date(2022, 1, 1)
end = datetime.date(2022, 12, 31)
bars = load_bars(start, end, universe=20)

close = bars.close.unstack("asset")
returns = close.pct_change()
alpha001(close, returns).tail(20)
```
</NoteCell>

<Table class="abs w-50% left-40%" v-motion
    :enter="{opacity: 0}"
    :click-9-10="{opacity: 1, scale: 0.6}">

```yaml
    head: date,000619.XSHE,000789.XSHE,002574.XSHE,002807.XSHE,002992.XSHE
    body: 
        - 2022-12-26,6.62,8.59,6.26,3.84,56.6
        - 12022-12-27,6.59,8.69,6.89,3.92,58.6
        - 2022-12-28,6.4,8.63,6.5,3.95,55.6
        - 2022-12-29,6.29,8.47,5.97,3.95,56.5
        - 2022-12-30,6.34,8.5399,6.08,3.97,55.5
```
</Table>


<!--
接下来我们介绍如何理解和实现这101个因子。首先我们看第1号因子，阿尔法001。 我们通过这个例子，来介绍如何把Alpha 101的公式转换成为Python的代码

我们的做法是先从论文里把它的公式复制下来。一个公式无非就是由算子和数据组成一段伪码，非常像Python代码

如果伪码中存在三目运算符，要先转换为python函数。三目运算符在Python中是不合法的，我们需要进行这个转换。

在1号因子中，就存在三目运算符。所以，我们要先进行转换。转换后就得到下面的代码。

[click]

现在这一段就是完全合法的python代码了。但我们还需要完成一件事，就是把它函数化。

但是，经过转换后的代码包含了很多层嵌套，非常难以理解。所以呢，我们先对他进行格式化，把他的嵌套关系给找出来。这个时候我们就可以借助编辑器的代码格式化功能，下面显示的就是格式化之后的代码。


[click]

到了这一步之后，我们再检查其中有哪些算子，这些算子是要先实现成python函数的，然后找出来代码中使用了哪些数据，这些数据是输入参数，我们把它们放到函数的形参中，转换就完成了。

我们通过对比看一下转换前后的代码。

[click]

在转换时，我们一般使用公式的名字作为函数的名字。在公式中使用了close和returns这两种数据，于是我们就把它当成函数的形参。 这样我们的转换就基本完成了。

这个方法也基本上是我们看论文时，把论文公式转换为代码的一般做法。

出于演示的目的，这里的rank, ts_argmax等等算子，我们先把它定义成为一个空的函数。这些空函数的功能就是打印它被调用时的名字和参数。 一旦我们验证了公式的转换是正确的，我们就再来逐一实现这些算子。


现在我们运行一下这段代码。我们看到算子被依次调用，并且正常运行。

直到最后一步，在用rank的结果-0.5的时候才发生了异常，我们现在不用管这个异常。能运行到这里，证明我们的转换是成功的，也说明从公式到Python代码的转换是比较容易的

转换完成之后，我们还可以对代码进行优化。这里我们还留下一个问题，就是我们前面提到，alpha101中，算子有的是按截面计算的，有的是按时序计算的。大家还记得吗？

那么，我们这里转换后的代码，从哪里方向实现了截面计算和时序计算呢？


[click]


要回答这个问题，我们先把这个因子完整地实现出来，结合运算结果来讲解它。

[click][click][click]

这段代码共分三部分，首先是实现了三个算子。然后是实现了1号因子。从第15行起，我们提供数据进行测试。

[click]

这是对公式的实现。 我们看到这里的代码比之前转换后的代码，其实要更简单。主要是三目运算符的功能，在这里我们只通过一行代码就实现了。大家看出来没有？对，就是第12行。


其次，signed power算子实际上就是乘方运算符。所以，实际实现会更简单。

在这里close和returns是输入参数，在阿法101中，这些数据一般都是所谓的宽表格式，以便实现在横截面上的运算

[click]

现在右边显示的是合法的close的格式。returns也应该是这样的格式。

[click]

ts_argmax是第一个被调用的函数。结合输入数据格式，我们来介绍它的实现，并回答问题：截面和时序是怎么体现出来的。

它的实现是以10行为单位对输入按窗口进行滑动，再求窗口内的最大值的行标。两个动作都是在行的方向上完成的，这是时序上的操作。这些操作同时发生在多个列上，这是向量化操作。

[click]

ts_argmax运算完成之后，再调用 rank 算子

[click]

在这里，我们注意到df.rank多了一个axis参数，其值为1，因此这是在横截面上的操作。这里我们还要注意，它有一个pct=True的参数，也就是最后的排序结果是百分比，是在0到1区间的一个数字。如果没有这个参数，它的返回结果会是什么？

如果没有这个参数，它的返回结果会是0到列数之间的数字。

[click]

现在我们运行一下，看看结果。你们可以发现哪些数字上的规律？

[click]

首先，它是零中心化的。所以，我们在代码中看到的减去0.5，就是为了实现这个零中心化。

[click]

其次，这些数字看上去是浮点数，实际上是离散值，在每一行上，实际上只有5个值。所以，我们要进一步用Alphalens来进行因子分析的话，需要注意这一点：quantiles数量只能设置为5.

现在，结合这个结果，我们再来看看第1号因子的实际含义。

[click]

我们把目光聚焦到第13行来。在这一行之前，inner是这样组成的，在同期收益为正的部分，它的值是收盘价，在收益为负的部分，它的值是20天收益的标准差。所以，这是一组非负数，并且在量纲上非常悬殊。

然后它对inner进行平方，再取最近5天inner最高那天的索引。由于是在5天内寻找索引，所以它的输出必然是在0-4之间的一个整数。这就是为什么我们刚刚在结果中看到，每一天的因子值似乎只能取5个值的原因。

[click]

其次，由于returns与收盘价之间的量纲悬殊，所以，在排序中，returs实际上不会参与排序。所以如果我们把这个因子拿到A股上使用，它实际上就是在5天内寻找最收盘价的位置。

所以，最后我们排序的，就是今日股价离最高点的天数。

另外，如果你读得仔细一点，你会发现这里对inner进行平方其实毫无意义，因为对一个非负数序列，平方不会改变相对排序。

那么问题来了，为什么第一个因子，就这么难懂，甚至有些地方似乎毫无意义？这是理解Alpha101的一个难点。我的理解是，我们所面临的市场不一样。如果资产价格在1以下，那么这里的所有操作都是有意义的。

但如果你是在A股操作，这个因子是可以化简的。所以，你会看到有一些说法，就是alpha101因子似乎没有用。这个看大家怎么看待它，或者说有没有能力把握它。其实，我们对任何一个框架、算法都是这样的态度。有这样一个笑话，说有一个厨师推荐大家炒菜得用铁锅。有一个人马上反驳说，你这瞎说吧，我在家里用的就是铁锅，根本烧不出来好菜。

-->
