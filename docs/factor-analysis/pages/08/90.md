<div class="abs top-25% scale-80%">

## Alpha101简介
## Alpha101的数据和算子
## 从公式到代码
## 从Alpha101到Alphalens
## Supplements
</div>

<!--
这一章我们首先介绍了Alpha101的缘起，补充了一些职场知识。

然后我们介绍了Alpha101中使用的数据和算子。它们是构成Alpha101因子的基础。在算子中，我们要注意有一些是时序的，有一些是截面的，并要掌握这些算子对输入数据格式上的要求。

我们还介绍了如何将公式翻译成代码。

然后我们以第1号因子为例，介绍了如何在alphalens中检验这些因子。第是第7节的内容。学完这一节，我们基本上完全掌握了alpha101的实现，也回答了如何把数据传递给公式和算子的问题。

最后，我们介绍了第三方对Alpha101及Alpha191的实现。这两个都是付费因子库，但我们都找到了开源实现。大家可以在supplements中，自己尝试运行一下，自己检验一下这些因子的效果如何。

-->
