---
clicks: 20
---

<style>
.page-30-cell {
    background-color: aliceblue;
    /* width: 40px;
    margin: 0 5px;
    height: 40px;
    display: inline-block; */
}
</style>

## 三目运算符

<v-switch>
<template #2-25>

## Rank和算子方向
</template>

<template #8-25>

## delay
</template>
<template #9-25>

## covariance
</template>

<template #14-25>

## correlation
</template>


<template #15-25>

## scale
</template>

<template #16-25>

## decay_linear
</template>

<template #20>

## delta, adv, signedpower
</template>
</v-switch>


::right::

<div class="abs w-90%" v-motion :enter="{opacity: 1, scale: 2, x:200}" :click-1="{opacity: 0}">

x ? y : z
</div>

<div class="abs w-90%" v-motion :enter="{scale: 0}" :click-1-2="{scale: 1}">

```python

def ternary_op(x, y, z):
    if x:
        return y
    else:
        return z
```
</div>

<div class="abs w-90%" v-motion :enter="{scale: 0}" :click-2-3="{scale: 1}">

<FlashText class="abs mt-30 left-130px">rank</FlashText>

<Array data="3,5,2,1,6" class="abs mt-10 ml-10 w-50px h-200px" labelPos="left" cell="page-30-cell" flag="vertical"/>


<Array data="3,4,2,1,5" class="abs mt-10 ml-50 w-50px h-200px" labelPos="left" cell="page-30-cell" flag="vertical"/>
</div>

<div class="abs left--10" v-motion
    :enter="{scale: 0}"
    :click-3-5="{scale: 1}">

```python
import pandas as pd

data = {
    'asset': ["000001", "000002", "000004", "000005", "000006"],
    'factor': [85, 92, 78, 92, 88],
    'date': [0] * 5
}
df = pd.DataFrame(data).set_index('date').pivot(index=None, columns="asset", values="factor")

def rank(df):
    return df.rank(axis=1, pct=True, method='min')
```

</div>

<div class="abs mt-70 left--10" v-motion
    :enter="{scale: 0}"
    :click-4-5="{scale: 1}">

```python
from bottleneck import move_rank

def ts_rank(df, window=10, min_count=1):
    return move_rank(df, window, axis=0, min_count=min_count)
```

</div>

<Table class="abs" v-motion :enter="{opacity: 0}"
        :click-5-6="{opacity: 1}" sep='|'>

```yaml
head: 横截面|时序|横截面|时序
body:
  - rank|ts_rank|max|ts_max
  - argmax|ts_argmax|argmin|ts_argmin
  - min|ts_min|min(x,d)|max(x,d)
  - sum(x,d)|product(x,d)|stddev(x,d)
```
</Table>

<div class="abs mt-20 left--30%" v-motion
    :enter="{opacity: 0}"
    :click-6-7="{opacity: 1}">

<FlashText class="abs w-100 mt--10">Alpha#29</FlashText>

```python {all}{lines:false}
(min(product(rank(rank(scale(log(sum(ts_min(rank(rank((-1 * rank(delta((close - 1),
5))))), 2), 1))))), 1), 5) + ts_rank(delay((-1 * returns), 6), 5))
```
</div>

<div class="abs ml--40" v-motion
    :enter="{opacity: 0}"
    :click-7-8="{opacity: 1}">

```python
(
    min(
        product(
            rank(
                rank(
                    scale(
                        log(
                            sum(
                                ts_min(
                                    rank(rank((-1 * rank(delta((close - 1), 5))))), 2
                                ),
                                1,
                            )
                        )
                    )
                )
            ),
            1,
        ),
        5,
    )
    + ts_rank(delay((-1 * returns), 6), 5)
)
```

<v-drag-arrow class="z10" color="red" pos="15,160,88,199"/>
<v-drag-arrow class="z10" color="red" pos="14,161,70,-113"/>
</div>

<NoteCell class="abs left--10% w-530px" v-motion
    :enter="{scale: 0}"
    :click-8-9="{scale: 1}">

```python
def delay(df, n):
    return df.shift(n)

data = {
    'date': pd.date_range(start='2023-01-01', periods=10),
    'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109]
}
df = pd.DataFrame(data)

delay(df, 5)
```
</NoteCell>

<div class="abs left--10 w-full" v-motion
    :enter="{opacity: 0}"
    :click-9-10="{opacity: 1}">

$$
\text{Cov}(X, Y) = \frac{1}{n-1} \sum_{i=1}^{n} (X_i - \bar{X})(Y_i - \bar{Y})
$$

```python
def covariance(x, y, window=10):
    return x.rolling(window).cov(y)

```
</div>


<NoteCell class="abs left--10 w-full" v-motion
    :enter="{scale: 0}"
    :click-10-11="{scale: 1}">

```python
def covariance(x, y, window=10):
    return x.rolling(window).cov(y)

x = pd.DataFrame({
    "A": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "B": [1, 1, 1, 2, 2, 2, 3, 3, 3, 0]
})

y = pd.DataFrame({
    "A": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "B": [1, -1, 1, -1, 1, -1, 1, -1, 1, 0]
})

covariance(x, y, 3)
```
</NoteCell>

<NoteCell class="abs left--10 w-full" v-motion
    :enter="{scale: 0}"
    :click-11-12="{scale: 1}">

```python
x = pd.DataFrame({
    "A": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "B": [1, 1, 1, 2, 2, 2, 3, 3, 3, 0]
})

y = pd.DataFrame({
    "C": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    "B": [1, -1, 1, -1, 1, -1, 1, -1, 1, 0]
})

covariance(x, y, 3)
```
</NoteCell>

<div class="abs left--10 w-full" v-motion
    :enter="{scale: 0}"
    :click-12-14="{scale: 1}">

```python
def covariance(x, y, window=10):
    return x.rolling(window).cov(y)
```
</div>

<div class="abs left--10 mt-20 w-full" v-motion
    :enter="{scale: 0}"
    :click-13-14="{scale: 1}">

<FlashText class='left-0 mt-0' >agg</FlashText>
<FlashText class='left-40 mt-0' >aggregate</FlashText>
<FlashText class='left-80 mt-0' >center</FlashText>
<FlashText class='left-120 mt-0' >corr</FlashText>
<FlashText class='left-160 mt-0' >count</FlashText>
<FlashText class='left-0 mt-10' >cov</FlashText>
<FlashText class='left-40 mt-10' >kurt</FlashText>
<FlashText class='left-80 mt-10' >max</FlashText>
<FlashText class='left-120 mt-10' >mean</FlashText>
<FlashText class='left-160 mt-10' >median</FlashText>
<FlashText class='left-0 mt-20' >min</FlashText>
<FlashText class='left-40 mt-20' >min_periods</FlashText>
<FlashText class='left-80 mt-20' >quantile</FlashText>
<FlashText class='left-120 mt-20' >rank</FlashText>
<FlashText class='left-160 mt-20' >sem</FlashText>
<FlashText class='left-0 mt-30' >skew</FlashText>
<FlashText class='left-40 mt-30' >std</FlashText>
<FlashText class='left-80 mt-30' >sum</FlashText>
<FlashText class='left-120 mt-30' >var</FlashText>
</div>

<div class="abs left--10 w-full z-100" v-motion
    :enter="{scale: 0}"
    :click-14-15="{scale: 1}">

$$
\rho(X, Y) = \frac{\text{Cov}(X, Y)}{\sigma_X \sigma_Y} 
$$

```python
def correlation(x, y, window=10):
    return x.rolling(window)
             .corr(y)
             .fillna(0)
             .replace([np.inf, -np.inf], 0)
```
</div>

<div class="abs w-full" v-motion
    :enter="{opacity: 0}"
    :click-15-16="{opacity: 1}">

```python
def scale(df, k=1):
    return df.mul(k).div(np.abs(df).sum())
```
</div>

<div class="abs w-full" v-motion
    :enter="{opacity: 0}"
    :click-16-20="{opacity: 1}">

```python {all|1}{at:19}
def decay_linear(df, period=10):
    # weighted moving average over the past d days with 
    # linearly decaying weights d, d – 1, …, 1 
    # (rescaled to sum up to 1)
    weights = np.array(range(1, period+1))
    sum_weights = np.sum(weights)

    return df.rolling(period).apply(
            lambda x: np.sum(weights * x) / sum_weights
        )
```
</div>

<div class="abs w-full mt-60" v-motion
    :enter="{opacity: 0}"
    :click-17-18="{opacity: 1}">

```python
import talib as ta

ta.WMA(df, 10)
```
</div>

<v-drag class="abs w-full z-10" v-motion pos="50,45,420,60"
    :enter="{scale: 0}"
    :click-18-19="{scale: 1}">

<Box class="text-red bg-gray-100">
linearly decaying weights d, d – 1, …, 1 <br>
(rescaled to sum up to 1)
</Box>
</v-drag>

<div class="abs w-full" v-motion
    :enter="{opacity: 0}"
    :click-20="{opacity: 1}">

```bash
delta -> dataframe.diff

adv -> df["volume"].rolling(n).mean()

signedpower(x, a) -> x^a
```
</div>

<!--
三目运算符是 Python 中没有，但存在于 C，java这些编程语言中的一个算子。

[click]

它相当于这段python代码。所以，当我们遇到公式中使用了三元运算符时，可以使用这个函数来翻译。

[click]

rank基本上就是大家所熟知的rank，也就是排序的意思

[click]

但这里要强调一下，存在两个方向上的排序。一种是横截面上的排序，另一种是时序方向上的排序。

之前我们在讲分层错误时，使用过一个rank rsi，大家还有印象吗？在Alphalens中，默认排序都是在横截面上的排序，但那一次，我们是先在时序方向上进行排序，提取出来的因子，最终在横截面上再进行排序。

这段代码实现的是横截面上的排序。因为我们传入了axis=1的参数。

[click]

下面这段代码则是在时序方向上的排序。

所以，在实际的因子探索中，这两种排序都有其用途。更一般地说，很多算子都需要两个方向上的表达。因此，alpha 101给时序方向上的算子专门安排了一个前缀

[click]

这个前缀就是ts。这里我们把几个常见的带时序的算子列成表格，供大家对照。

这里要注意的是像min, max, sum等几个统计函数，不知道出于什么样的考虑，在没有ts_前缀的情况下，如果它们在公式里带有d参数，那么也一定要当成时序算子来对待。

[click]

大家注意看，在第29个因子里，就同时出现了min和ts_min，这里的min究竟是横截面上的排序，还是时序方向上的排序呢？

遇到这样的问题，我们需要先将公式转换为代码，再进行格式化。

[click]

经过展开后，我们发现第一个min，实际上是 min(x,d),这里的d=5,所以，这个min是时序方向上的排序。

好，这是阅读Alpha101论文时，这部分需要注意的地方，我们就介绍到这里。

[click]

delay算子用来获取n天前的数据。我们可以用shift方法来实现它。

[click]

Alpha101中的协方差就是我们通常数学意义上的协方差。下面是它的代码实现。

这里要注意一个技巧，就是它是如何实现两个序列在滑动窗口下的协方差的。我们把这段代码讲解一下。

[click]

首先，这里的covariance的两个参数，x, y，应该是什么样的数据？它们必须是pd.Series，或者pd.DataFrame

 在这段代码中，X跟Y都是dataframe，他们拥有相同的列，现在我们就运行一下，看看结果如何。

从结果上看，是X的A列跟Y的A列进行协方差运算，X的B列中Y的B列进行了协方差运算。

我们猜测这个对应关系是通过列名字来进行的，现在我们就把列名字改得有所不同，看看情况会怎么样

[click]

现在我们把y的其中一列名字改成了C，然后我们再计算一次X Y的协方差，看看结果怎么样？

这次我们就清楚了。两个传入的参数，X Y中相同的列会参与协方参运算，其它的列不参与运算，结果都是Nan

[click]

在输入参数中参与运算的两个时间序列找准之后，他们的滑动窗口又要怎么对齐呢？ 这里pandas的rolling function，提供了强大的功能，通过rolling产生的对象，在调用cov方法时，它会把两个时间序列的窗口按索引自动进行cxf齐。


[click]

除了cov之外，rolling对象还有很多统计类的方法都有这个特性。我们把这些方法总结在这里，大家在因子分析的时候要善于使用这些方法，这样可以大大的提高性能，并且减少代码量。

[click]

协相关系数算子。我们来看看它的实现。

在前面计算协方差的时候，当时这个函数到第3行就结束了。这个算子本来到这里也可以结束，这里演示了如何对数据进行修剪。

所以在这里，我们又给它增加了一些新的功能。在第3行结束之后，我们得到了一个什么对象？大家思考一下。

在第3行结束之后，我们得到了一个dataframe对象，所以在后面可以跟一些dataframe具有的方法，以拓展功能。

[click]

scale 算子。按Alpha101的解释，这个算子的作用是对数组的元素进行缩放，使得缩放后的数组元素加起来之和等于K。在默认情况下，k等于1

[click]

线性加权衰退算子。

关于这个算子，我要做三点说明。

[click]

首先大家注意看这里的实现，我们不展开讲，但他实际上是一个 weighted moving average,即 WMA函数，这个函数在talib当中有实现。

[click]

第2点，注意看这个函数的注释，这个注释来自于Alpha101论文 但是我们在这里实现的权重序列刚好与他的说明是相反的。为什么要这么做？因为这么做才是正确的实现，也是是多数人采用的实现。

[click]

第3点，在这个实现当中,第二个参数，即period参数应该是整数，但在论文当中 某些阿法因子使用decay_linear算子的时候，这个参数也有传入浮点数的做法。但如果传入的是浮点数，实际上是无法实现的。这是他论文自己的矛盾之处。

这也是做量化复杂的地方。我们得到的信息常常会是不严谨的、甚至是不正确的，无论它的作者看起来多么权威。所以，最重要的一点是必须学会独立思考。

[click]

这个是三个比较简单的因子，他们的实现就放在右边的代码框当中，我们就不具体讲了，大家在应用的时候，如果不知道怎么实现，再回过来看这一节就可以了
-->
