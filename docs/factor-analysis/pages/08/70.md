---
clicks: 5
---

## <PERSON>'s
<v-clicks>

## DolphinDB's
## QuantAxis's
## <PERSON><PERSON>'s
</v-clicks>

::right::

<div class="abs" v-motion :enter="{opacity:1}" :click-1="{opacity: 0}">

https://github.com/yli188/WorldQuant_alpha101_code

![](https://images.jieyu.ai/images/2024/10/aaron-li-alpha101.png)
</div>

<v-switch at="1">

<template #1>

![](https://images.jieyu.ai/images/2024/10/dolphin-db.png)
</template>

<template #2>

https://github.com/yutiansut/AlphaForge

![](https://images.jieyu.ai/images/2024/10/quantaxis-yutian.png)
</template>

<template #3>

https://github.com/popbo/alphas
![](https://images.jieyu.ai/images/2024/10/popbo-alpha101.png)
</template>

<template #4>

![](https://images.jieyu.ai/images/2024/10/alpha101-supplements.png)
</template>
</v-switch>

<!--

这个库是Aaron Li的一个实现。在Dolphindb仓库中，有引用到这个库，并作为benchmark。所以，大家可以参考一下。

[click]

Dolphine DB自己也有Alpha101的实现，但它并不是一个Python的实现，只能在dolphindb中使用。

在机构的同学可能有人用过Dolphindb。从他们的新闻稿来看，dolphindb能提供海量和高性能的证券数据存储。

[click]

余天是QuantAxis的开发者。他研究生阶段开发了QuantAxis（这个项目获得了8.2k赞）,凭此项目，毕业后即成为波粒二象的CTO。他的这个版本对Quantaxis有一些依赖。

[click]

在本课程中，我们推荐popbo的这个项目。在因子计算上，基本上是使用了Aaron Li的实现，但他加上了Alphalens因子分析和backtrader回测，所以功能上很完整。

[click]

这个项目我们已经clone到了课程环境，包括101和191两大因子库。这个文件夹是只读的，数据我们都已经下载了，大家可以直接从research.ipynb这个文件开始进行研究。
-->
