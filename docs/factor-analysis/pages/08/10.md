---
clicks: 6
layout: two-cols
preload: false
---

<style scoped>

.poem {
    /* background-image: url('https://linguafennica.wordpress.com/wp-content/uploads/2015/10/ships_sail_ship_sea_desktop_1500x967_hd-wallpaper-1170237.jpg'); */
    background-size: cover;
    /* box-shadow: 0 0 2px #ccc, inset 0 0 70px #ece7dd; */
    /* border: 1px solid #e4e2e2; */
    border-top: 0;
    margin: 0 2rem;
    /* border-radius: 8px; */
    /* border-bottom: 2px solid #efeeeb; */
    padding: 1rem;
    opacity: 0.8;
    perspective:400px;

    .the-end {
        width:400px;
        height: 400px;
        margin-left:25%;
    }
}

</style>

<div class="abs w-full h-full" v-motion :enter="{opacity: 1}" :click-1="{opacity: 0}">

![](https://images.jieyu.ai/images/2024/10/101-formulaic-alphas.png)

</div>

<div class="abs w-full h-full mt-10" v-motion 
    :enter="{opacity: 0, x: 230, scale:1, y:0}" 
    :click-1="{opacity: 1}"
    :click-2="{x: 0}"
    :click-3="{x: 250, y: 100, scale: 1.5}"
    :click-4="{scale: 0}">

![](https://images.jieyu.ai/images/2024/10/zura-linked-in.png)
</div>


<div class="abs w-full h-full mt-30" v-motion 
    :enter="{opacity: 0}" 
    :click-4="{opacity: 1}">

## WorldQuant

<br>

<ion-ios-location class="text-blue" />康涅狄格州

<mdi-gold class="text-yellow-400 ml-5"/> 90亿
<mdi-family-tree class="text-green ml-5"/>850人

</div>

<FlashText class="top-100 w-600px left-50" v-motion
        :enter="{opacity: 0}"
        :click-5="{opacity: 1}">

Finding Alphas: A Quantitative Approach to Building Trading Strategies
</FlashText>

::right::

<div class="abs top-30% text-center ml-10%" v-motion :enter="{opacity: 1}" :click-1="{opacity: 0}">

<div style="font-size: 2em">Zura Kahushadze</div>
<div style="font-size: 1em">PhD, WorldQuant, Quantigic, Professor</div>

<div style="color: #808080">https://arxiv.org/pdf/1601.00991</div>
</div>

<div class="poem abs top--250px" v-motion :enter="{opacity: 0}" :click-2-3="{opacity: 1}">

<div v-motion style="overflow-y:hidden" :enter="{rotateX: '0deg', skew: '0deg,0', height:'800px'}" :click-2-3="{rotateX: '45deg', skew: '-5deg,0'}">
<div class="the-end" v-motion :enter="{y:600}" :click-2-3="{y:-100, transition: {ease:'linear', forward: false, duration: 16000}}" ><!--text-box-->

<h2 class="w-full pl-5">The Sail</h2>

<div style="color: #808080;font-size: 0.6em;padding-left: 4em">米哈伊尔.茉蒙托夫</div>


A lonely sail seeming white

In misty haze mid blue sea,

Be foreign gale seeking might?

Why home bays did it flee?


The sail's bending mast is creaking,

The wind and waves blast ahead,

It isn't happiness it's seeking,

Nor is it happiness it's fled!


Beneath are running azure streams,

Above are shining golden beams,

But wishing storms the sail seems,

As if in storms is peace it deems.


<br>

翻译：Zura Kahushadze

《101 Formulaic Alphas》 Page 8.

</div>
</div>
</div>

<div class="abs w-full h-full mt-15 ml--15 scale-150" v-motion 
    :enter="{opacity: 0}" 
    :click-4-5="{opacity: 1}">

![](https://images.jieyu.ai/images/2024/10/worldquant.gif)
</div>

<div class="abs w-full h-full mt-15 ml--15" v-motion
    :enter="{opacity: 0}"
    :click-5="{opacity: 1}">

![](https://www.worldquant.com/wp-content/uploads/2024/03/IQC-Logo-2024-768x380.png)
</div>

<!--
Alpha101因子是由祖拉.卡库沙泽2015年底，通过开放学术预印网站arXiv发布的。

[click]

祖拉.卡库沙泽是康纳尔大学的物理学博士。做过世坤的Direct Manager。现在是自己创立了Quantigic公司。他还是第比利斯自由大学的全职教授。他在物理学、金融等领域发表了130篇以上论文。

[click]

祖拉.卡库沙泽应该是格鲁吉亚人。他这个姓是高加索地区很典型的姓氏。所以，他的俄语也不错。

右边的这首诗，就是他翻译的俄国诗人莱蒙托夫的一首诗。这首诗出现在Alpha 101这篇文章中的第8页。大家在读这篇文章时可以留意一下。

[click]

101个Alpha这篇文章和151个交易策略这本书，引用超过了3400次，在SSRN上被下载超16万次。

151个交易策略这本书，就是现在大家看到的这幅图的背景。这本书旁征博引，引用了2000多篇论文，是一本很好的因子挖掘方法指南，这里也跟大家推荐一下。

他在linkedin上拥有超25万粉丝。所以，他还有一个身份，就是网红。

Alpha 101之所以很有名气，是因为它不是实验室产品，而是在世坤真实使用的因子。祖拉.卡库沙泽发表这篇论文时，他人还在世坤，发表的这些因子，80%以上是世坤在用的因子。他发表这篇文章，也得到了世坤的允许和支持。

[click]

这里我们也简单介绍一下世坤。因为这家公司不仅跟卡库沙泽有关，也跟国内很多私募机构的黄埔军校

你现在看到的，就是世坤网站的首页。海外基金公司在网站设计上，一般都比较注重科技感。

World Quant是一家成立于2007年的对冲基金，由千禧年分拆成立，目前管理着大约90亿美元资产。他的创始人是白俄罗斯人 Igor Tulchinsky（伊戈尔.图尔金斯基）。

World Quant有着相对开放的文化。Alpha 101因子正是文章发表时，他们正在使用的因子的一部分。这篇文章的发表得到了World Quant的许可。

[click]

此外，创始人Igor等人还撰写了《Finding Alphas: A Quantitative Approach to Building Trading Strategies》这本书，阐述他们对寻找Alpha的想法。

这本书有300多页，也是我们课程的推荐读物。链接已经放在教材里了。

[click]

WorldQuant还运营了量化金融领域的重要赛事 -- WorldQuant Challenge。这项赛事已经成为了量化求职入门的敲门砖之一。这也是大家可以留意的地方。


2015年，他们还推出了WorldQuant大学。
-->
