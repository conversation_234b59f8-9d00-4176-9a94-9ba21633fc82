---
clicks: 5
preload: false
---

## OHLC,Volume, Amount

<v-clicks>

## turnover
## returns
## vwap
</v-clicks> 

<FlashText class="top-49 left-25%" v-motion
        :enter="{opacity: 0}"
        :click-4-5="{opacity: 1}">遗憾规避理论</FlashText>

::right::

<div v-motion :enter="{opacity: 0}"
     :click-1-2="{opacity: 1}">

$$

\text{换手率} = \left( \frac{volume}{capital} \right) \times 100% = 10%
$$

</div>

<div class="abs" v-motion :enter="{opacity: 0}"
     :click-2-3="{opacity: 1}">

```python

# the numpy way
bars["close"][1:] / bars["close"][:-1] - 1

# the pandas way
bars.close.pct_change()
```
</div>

<div class="abs" v-motion :enter="{opacity: 0}"
     :click-3-5="{opacity: 1}">

```python

# the numpy way
vwap = bars["amount"] / bars["volume"] / 100

# the pandas way
vwap = df.amount / df.volume / 100
```
</div>

<div class="abs" v-motion :enter="{opacity: 0}"
     :click-5="{opacity: 1}">

```python {lines:false}

# 计算某日的vwap价格。bars为分钟线数据
vwap = np.sum(bars["volume"] * bars["close"]) / np.sum(bars["volume"])

```
</div>

<!--

Alpha101因子主要是基于价格和成交量构建，但也有少部分因子使用了基本面数据，包括市值数据和行业分类数据。

这一组数据是几乎所有的数据源都会提供的基本数据。所以我们就略过了。

[click]

换手率数据不是基本数据。它是跟财报相关的数据。具体来说，它是通过当天的成交量，除以个股的流通股数计算出来的。而后者是基本面数据之一，以季度为单位进行披露。

因为换手率是基本面数据的衍生数据，所以，大家在使用时，要注意数据引用的时间，防止出现使用未来数据的情况。

换手率本身也可以当成因子，也是常用因子。高换手率意味着成交活跃，市场关注度高。换手率在 10% 以上通常被认为是高换手率，可能意味着该资产或有大资金在操作。相反，低换手率表示股票交易清淡，市场关注度低。

讲一下换手率与成交量的关系。成交量的导数有交易信号含义，个别成交量本身没有交易信号含义。但换手率即使是单个数据，也可能有交易信号含义。比如，如果资产正在上升趋势，5%到20%的换手率可能都算良好或者健康。但超过20%的换手率，很可能意味着行情见顶，因为在击鼓传花的游戏中，就意味着后面可能没有新的资金进来了。

[click]

returns比较简单，就是t1期收盘价除以t0期收盘价再减1.

[click]

vwap是非常重要的数据。它是加权平均成交价格。在行情软件中，分时均线就是一种vwap。

vwap是价格的一种，与OHLC应该具有某种可比性。实际上我们在量化中，也常常拿OHLC数据与vwap进行比较。

[click]

比如，遗憾规避理论的一个应用，就是拿收盘价与当天vwap价格进行比较。收盘价低于vwap，说明当天买入的人被套，他们为了避免承认之前的决策失误，从而更倾向于继续持有。

考虑到vwap经常要与OHLC价格进行比较，所以，它必须有正确的量纲。这就是为什么这里的代码除以100的原因。因为成交量的单位一般是手，而一手是100股。

[click]

注意这里的算法求出来的vwap并不精确。要得到最精确的vwap数据，需要有逐笔成交的记录。这在回测中不太容易拿到，因为数据量实在是太大了。

我们可以退而求其次，使用分钟线数据来计算某一日的vwap。

-->

