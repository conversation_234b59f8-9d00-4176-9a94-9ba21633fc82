---
clicks: 1
---

<div class="abs w-full">

```python {all}{maxHeight: '450px', lines:false}
def rank(df):
    return df.rank(axis = 1, method="min", pct=True)

def alpha042(vwap, close):
    return rank(vwap - close) / rank(vwap + close)

start = datetime.date(2008, 1, 1)
end = datetime.date(2022, 12, 31)

bars = load_bars(start, end, 2000)
close = bars.close.unstack("asset")
vwap = (bars.amount/bars.volume/100).unstack("asset")

factor = alpha042(vwap, close)

factor = (factor.reset_index()
          .melt(id_vars='date', var_name="asset", value_name="factor")
          .set_index(["date", "asset"])
         )
prices = bars.price.unstack(level=1)

merged_factor = get_clean_factor_and_forward_returns(factor, 
                                                     prices, 
                                                     quantiles=None, 
                                                     bins=10, 
                                                     max_loss=0.5)

create_returns_tear_sheet(merged_factor)
```
</div>

::right::

![](https://images.jieyu.ai/images/2024/10/alpha042-alpha-beta.png)

![](https://images.jieyu.ai/images/2024/10/alpha042-cumulative-return.png)

<!--
这是第42号因子。这个因子有着明确的交易含义，符合行为金融学的遗憾规避理论。这里vwap使用的是当天的成交额除以当天的成交量得到的，不如分钟线精确，所以，如果你有分钟线数据、甚至是tick数据，理论上有可能得到更好的结果。

但即便如此，这个因子仍然表现出来非常好的收益和稳定性。这里使用的是2008年到2022年15年间的跨度进行的回测，样本数达到了2000支。要知道在2008年，A股数量还不足2000支，所以，我们的测试是广泛覆盖的。正因为使用了2000支样本，在因子测试中，出现了max_loss超过35%的情况。大家可以想一下是为什么。

我们把这个问题作为一道思考题，放在练习中。

另外，大家可以思考一下，在alpha042函数中，是否一定要求rank?当然公式是要求这样实现的。我们也把这个问题作为思考题放到练习中。答中有惊喜
-->
