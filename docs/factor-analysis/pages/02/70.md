---
clicks: 10
---

<div class="abs w-50% h-full top-15%">

## 中性化的作用
<v-clicks>

## 行业中性化
## 市值中性化
## 示例
</v-clicks>
</div>

<div class="abs w-50% h-full top-15% left-40%"
    v-motion
    :enter="{opacity: 0}"
    :click-1-2="{opacity: 1}">

$$
Y = \beta*Industry + \alpha  + \epsilon 
$$
</div>

<div class="abs w-50% h-full top-15% left-40%"
    v-motion
    :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}">

$$
Y = \beta * \log(MarketValue) + \alpha + \epsilon

$$
</div>

<NoteCell class="abs w-60% h-full top-15% left-40%" 
    v-motion
    :enter="{opacity: 0}"
    :click-3="{opacity: 1}">

```python {all|4-5|8-11|14|17|all}{lines: true}
from sklearn.linear_model import LinearRegression
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

F = np.array([10, 15, 20, 25, 30])
I = np.array([[1, 0], [0, 1], [1, 0], [0, 1], [1, 0]])  # 假设有两个行业，每个行业一个虚拟变量

# 初始化线性回归模型
reg = LinearRegression(fit_intercept=True)

# 拟合模型
reg.fit(I, F)

# 预测值
F_pred = reg.predict(I)

# 计算残差
epsilon = F - F_pred

print("行业中性化后的因子值（残差）:", epsilon)

# 绘制回归模型的拟合情况
sns.scatterplot(x=np.arange(5), y=F, label='Actual')
sns.lineplot(x=np.arange(5), y=F_pred, label='Predicted', color='red')
plt.title('Regression Fit')
plt.xlabel('Index')
plt.ylabel('Factor Value')
plt.legend()

# 显示图表
plt.tight_layout()
plt.show()
```
</NoteCell>



<!--
我们选因子，是为了通过因子，找出好的公司 -- 它们不随市场涨涨落落，而是有自己的增长和节奏。但是，不是所有我们当成因子的特征，都能找到好的公司。有时候，它们只反映了行业的差异和经济周期。

比如，市盈率因子就很容易集中在银行业选股。为了提升因子对公司的选择能力，我们就有必要在因子分析过程中，“中和”掉行业/部门对因子暴露的影响。除了行业中性化之外，常做的中性化还有市值中性化。

[click]

基本上可以认为，这个方法是夏普开创的，被他用来分离市场因素与资产的alpha收益。同样的方法，也可以被用来分离行业因素，与资产本身的因子特征。

在具体实现上，要注意行业是分类数据。在数学上一般处理成哑变量

[click]

市值中性化方法跟前面完全一样。要注意的是，我们这里先取了对数。我们做中性化是通过线性回归，线性回归的条件是LINE(即自变量、因变量满足线性、残差满足正态性、独立性和方差齐性<Equal Variance>)

换句话说，自变量、因变量不服从正态分布，你对这两个变量进行回归分析，是没有意义的。

[click]

我们通过这个简单的示例来讲解中性化的原理

[click]

这里F是因子。它的长度表明我们有多少个资产。

你可以把它想像成为因子值在某一天的截面。

I是行业哑变量。为简单起见，我们假设总共只有两个行业。

[click]
接下来，我们创建回归模型，并进行训练。

[click]
我们用训练好的模型，来预测每一个行业的行业因子

[click]

显然，用资产的因子值，减去行业因子值，得到的就是每个资产中性化之后的因子。

[click]

最后，我们来看看绘图的结果。

市值中性化的方法与此完全类似，这里就不再介绍。在我们的教材中，我们是将两个中性化一次性做完，也就是做了一个多重线性回归。代码跟这里的示例大同小异，就不再复述。其中用到的一些pandas的方法，在本课开头也讲过了。

好，这一节课就到这里。

-->
