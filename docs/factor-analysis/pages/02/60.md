---
clicks: 6
---

<div class="abs h-full top-15%">

## 分布推断
<v-clicks>

## 分布调整方法
### 对数化
<span></span>
<p/>

### Box-Cox变换
</v-clicks>
</div>

<NoteCell class="abs w-50% left-50% top-10% h-full"
    v-motion
    :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}">

```python
import akshare as ak

df = ak.stock_zh_a_spot_em()
df = df[["代码", "流通市值"]].rename(columns = {
    "代码": "code",
    "流通市值": "market_value"
})

df.hist()
```
</NoteCell>

<NoteCell class="abs w-50% left-50% top-10% h-full"
    v-motion
    :enter="{opacity: 0}"
    :click-3-4="{opacity: 1}">

```python
df["log_market_value"] = np.log(df["market_value"])
df.hist(column="log_market_value")
```
</NoteCell>

<NoteCell class="abs w-50% left-50% top-10% h-full"
    v-motion
    :enter="{opacity: 0}"
    :click-4-5="{opacity: 1}">

```python
from scipy import stats
stats.probplot(df["log_market_value"], dist="norm", plot=plt)
```
</NoteCell>


<NoteCell class="abs w-50% left-50% top-10% h-full"
    v-motion
    :enter="{opacity: 0}"
    :click-5-6="{opacity: 1}">

```python
from numpy.random import randn
from scipy.stats import boxcox
import matplotlib.pyplot as plt

np.random.seed(78)

data = 5 * randn(100) + 100

data = 1/data

plt.hist(data)
plt.show()

# 进行变换后
data = boxcox(data, -1)
plt.hist(data)
plt.show()
```
</NoteCell>

<!--
分布调整一般是指将分布调整为正态分布。在进行分布调整之前，我们先要对现有数据集进行分布推断，然后才能有分布调整的依据。

一般来说，我们要了解数据的一阶到四阶矩特征，通过绘制直方图等方法来猜测数据的分布，或者运行K-S检验，来确认数据的分布。在已知数据分布的情况下，再看该分布是否有到正态分布的转换方法。

具体内容不在这里介绍了，这部分知识在量化24课，大概是12、13章左右有比较详细的介绍。

[click]

常用的变换方法有对数化和开根号等。我们先介绍对数化方法，再介绍box-cox变换。

[click]

大家可以留意一下这个直方图，看到这种可以先用对数化进行尝试。

[click]

我们对market_value这一列进行对数化。

现在，我们可以看到，新的直方图比较接近正态分布了。

[click]

我们再来一个QQ图检验一下。

[click]

如果数据集可以通过倒数、开平方、取对数等方法变换成正态分布，我们就可以使用boxcox方法。
-->
