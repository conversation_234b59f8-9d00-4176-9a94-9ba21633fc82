---
clicks: 6
---

<div class="abs top-15% h-full">

## 缺失值产生的原因
<v-clicks>

## 缺失值处理方法
### 删除
<span> </span>

### 延用上一期数据
### 插值
### 行业均值（中位数）替代

</v-clicks>
</div>

<NoteCell class="abs w-60% left-40% top-10% h-full"
    v-motion
    :enter="{opacity: 0}"
    :click-2-3="{opacity: 1}">

```md
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
barss = load_bars(start, end)
barss.tail().isna()
```
</NoteCell>

<!-- <Thebe class="abs w-50% left-50% top-10% h-full"
    v-motion
    :enter="{opacity: 0}"
    :click-3-4="{opacity: 1}">

```python
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
barss = load_bars(start, end)
barss.tail().isna()
barss.tail().dropna(how='any')
```
</Thebe> -->

<div class="abs w-50% left-50% top-10% h-full"
    v-motion
    :enter="{opacity: 0}"
    :click-4-5="{opacity: 1}">

```python
# 在可以使用ffill和bfill的场合下
# 不建议使用fillna

df.fillna()
df.ffill()
df.bfill()
```
</div>

<!--
缺失值产生的原因有这些
1. 技术指标有冷启动期。比如，5日移动均线，前4期因为数据不足，是无法计算的
2. 停牌
3. 有一些数据天然缺失。比如分析师评级数据。有一些比较冷门的标的，一连好几个季度、甚至好几年无人问津也很正常。

[click]

处理方法有：

[click]

删除数据

[click]

延用上一期数据，可以用pandas中的fillna或者ffill方法

[click]

也可以使用插值法。这些方法的交易含义不明确，可以探索性地使用

[click]
此外，还可以使用行业均值、中位数来代替。在我们的练习中，就有这样一道题，请大家自己练习。

这道题需要获取行业分类数据、进行关联操作，可以帮助我们熟悉环境中的数据操作以及相关的pandas技巧
-->
