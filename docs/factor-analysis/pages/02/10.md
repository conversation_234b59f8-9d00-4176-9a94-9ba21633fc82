---
clicks: 4
title: hello
time: 3.5 min(快速)
---

<style scoped>
    h2,h3 {
        margin-left: 0
    }
</style>
<!-- <Thebe class="abs w-60% h-full top-10% left-40%">

```python
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
barss = load_bars(start, end)
barss.tail()
```
</Thebe> -->




<div class="abs w-50% h-full left-0 flex flex-col justify-center items-center">

## Rolf W. <PERSON>

### 小市值效应之父

</div>

<div class="abs w-50% left-50%" v-motion
    :enter="{opacity:1}"
    :duration="500"
    :click-1-10="{opacity:0}">

![](https://images.jieyu.ai/images/2024/09/rolf-banz.jpg)
</div>

<div class="abs w-60% left-40% top-15%" v-motion
    :enter="{opacity:0}"
    :duration="500"
    :click-1-2="{opacity:1}"
    >

![](https://images.jieyu.ai/images/2023/04/20230426161715.png)
</div>

<div class="abs w-50% left-50%" v-motion
    :enter="{opacity:0}"
    :click-2-5="{opacity:1}">

![](https://images.jieyu.ai/images/2024/09/low-beta-factor.png)
</div>

<v-drag-arrow color="red" pos="704,111,-76,38" 
    v-motion
    :enter="{opacity:0}"
    :duration="200"
    :click-3-4="{opacity:1}"/>

<div v-motion
    :enter="{opacity:0}"
    :click-4-5="{opacity:1}"
    >

<v-drag-arrow color="rgb(30,160,140)" pos="608,116,45,56"/>
<v-drag-arrow color="rgb(30,160,140)" pos="667,187,39,28"/>
<v-drag-arrow color="rgb(30,160,140)" pos="723,216,49,-13"/>
<v-drag-arrow color="rgb(30,160,140)" pos="790,206,130,44"/>
</div>

<!--
我们先来看第一节，因子数据从哪里来。

最常见的数据源有akshare, tushare, jqdatasdk等，这些在量化24课中有介绍。每个数据源又提供了好些个数据种类，这些就是我们因子数据的来源。

我们将在第12章更详细地介绍这些资源，所以在本课中，我们就一笔带过。大家对着教程，简单浏览一下就好。教程在这里提及这部分内容，主要是从内容完整性上考虑。

这一节提到了Rolf Banz。我们介绍一下他的故事。

Rolf W. Banz，他是瑞士人，70年代在芝加哥大学读书，并在该校任教过。如果说夏普发现了第一个因子的话，那么第二个因子，就是他发现的。他也有一个非正式的title，小市值因子之父。

根据Banz的研究，在1931到1975年间，小市值资产要比大市值资产月平均收益高0.4%。

[click]

在国内，小市值也有很好的表现。A 股市场在 2016 年以前，规模因子的显著性甚至超过了欧美等发达国家市场。该因子在 2017-2018 年期间，弱于大市值资产，但从 2021 年起，又跑赢了沪深 300。这张图，就是聚宽对这个因子最近两年的回测。

从这张图我们可以看出什么？我们可以看出，该因子的年化是15.17%，超额收益达到了51.98%。因此这个因子不仅仍然有效，还非常有效。


[click]

这是Banz当初做出来的统计结果。在他的研究中，Banz把所有的资产按市值大小，分成了5组，这可能是最早的分层回溯思想。在每个组中，他又把资产按beta分成了5个组。所以在他的研究中，实际上是建立了25个投资组合。

[click]

这是第一组，也就是小市值资产的情况，月均0.4%。从图上可以看出，小市值数据点在图中属于异常值。正是因为这个异常值，让 Banz 发现了小市值因子。

[click]

在每一组的资产中，Banz还按beta进行了划分，第1组是波动率最大的一组，第5组则是波动率最小的一组。因此，这里还出现了一个规律，就是低波动性的资产，收益率较高。可惜Banz当时没能抓住这个机会，不然就能一举收获两个因子。后来，他回忆道，这可能是论文疲劳的原因吧。

直到10年之后，Haugen和Baker才发现和命名了低波动因子。

讲这个故事，主要是说明在因子与策略研究上，即使到了现在，仍然会有大量的机会。我们将在第12章，还会继续展开这个故事。

-->
