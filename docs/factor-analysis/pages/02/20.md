---
clicks: 6
---

<style scoped>
/* h2, h3, h4{
    margin-left: 0;
} */
</style>

<div class="abs w-50% left-0 top-15% h-full"
    v-motion
    :enter="{opacity: 1}"
    :click-1="{opacity: 0}"
    >
<h4 style="margin-left:2rem"><span v-mark="{color:'red', at: 0}">supplements/data.ipynb</span></h4>
<h4 style="margin-left:2rem"><span v-mark="{color: 'red', at: 0}">2005~2023</span></h4>
</div>

<div class="abs w-50% left-0 top-15% h-full">

<v-clicks at="1" depth="3" every="2">

## load_bars
### get_level_values
### xs
## load_sector
## fetch_daily_basic
## fetch_valuation
</v-clicks>

</div> <!-- end left -->
<!-- right -->

<NoteCell class="abs w-60% left-40% top-10% h-full"
    v-motion
    :enter="{scale: 0}"
    :click-1-2="{scale: 1}">

```python
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 12, 31)
barss = load_bars(start, end)
barss.tail()
```
</NoteCell>

<NoteCell class="abs w-60% left-40% top-10% h-full"
        v-motion
        :enter="{scale: 0}"
        :click-2-3="{scale: 1}">

```python
barss.index.get_level_values(1)
```
</NoteCell>

<NoteCell class="abs w-60% left-40% top-10% h-full"
    v-motion
    :enter="{scale: 0}"
    :click-3-4="{scale: 1}" >

```python
barss.xs(code, level="asset")
```
</NoteCell>

<NoteCell class="abs w-60% left-40% top-10% h-full"
    v-motion
    :enter="{scale: 0}"
    :click-4-5="{scale: 1}" >

```python
load_sectors()
```
</NoteCell>

<NoteCell class="abs w-60% left-40% top-10% h-full"
    v-motion
    :enter="{scale: 0}"
    :click-5-6="{scale: 1}"
>

```python
date = datetime.date(2023, 12, 29)
fetch_daily_basic(date)
```
</NoteCell>

<!--
在本课程中，最常用的数据不需要借助第三方数据源来获取。

在课程中，我们使用的最多的数据是量价数据、然后是行业分类和基本面数据。

关于如何使用这些数据，在data.ipynb中有比较详细的介绍，并且我们还通过练习题对它进行了扩展。请大家把这个文件看完，把练习做了。

这里我们只简单地介绍一下。


首先，在环境中，只能获取2005年到2023年底的日线量价数据。这一点是跟量化24课的环境不同的。

[click]

然后我们看一下load_bars返回的数据的格式。

它是一个dataframe, data和asset双重索引，列有OHLC，volume和amount，此外还有一个price字段，它是open往前移一天得到的，也就是如果你在t0天得到一个因子，那么可以以t0的price作为买入价，这个价格，就是第二天的开盘价。这样可帮大家防止未来数据。

[click]

get_level_values用于在多层索引的场合下，取某一层索引的值。它返回的是一个Index对象。对该对象，常用的操作可能有去重、转换为Series、切片或者取单个元素等操作

[click]
这个方法叫 extract select, 选择并提取的意思。也可以念作 excess。考虑到pandas的统计背景，它也可能是指cross section, 即截面数据。从这个函数的用法来看，它也多少有提取截面数据的含义。

[click]
这个方法用来获取资产的行业属性。这个方法没有时间参数，因为我们使用的数据源是tushare，它的API没有提供此功能。

换句话说，它给出的分类与时间无关，也许是过去某一年的一个分类 -- 这样会导致数据 out-of-date；或者是现今的一个分类，这样会导致回测时引入未来数据。

因此，这个数据只能在我们课程环境中使用。

[click]

这个方法用来获取基本面数据。注意区分一下，这个方法的前缀是fetch，而不是load。我们想借这个前缀表达这样的含义，如果用的是fetch，那么这个数据有可能需要从远程服务器去取，但也可能读的是本地缓存如果用的是load的话，那么这个数据一定是在本地的，速度会快一些。

[click]
这个方法是在fetch_daily_basic之上，进行了简单的包装，只返回市值数据。
-->
