---
clicks: 7
---

<div class="abs top-15% left-0 h-full w-50%"
    v-motion
    :enter="{opacity: 1}"
    :click-6="{opacity: 0}">
<v-clicks>

## 异常值 (outlier) 的来源
## 异常值修正的必要性
## 修正算法
### 3$\sigma$法
### 分位数修正法
</v-clicks>
</div>

<!-- 6 -->

<div class="abs w-full h-full left-0 top-10%"
    v-motion
    :enter="{scale: 0}"
    :click-6-7="{scale: 1}">

<Carousel n="3" top="-150px"
:slides ="[{
        img: 'https://images.jieyu.ai/images/2024/09/galton.jpg',
        text: '弗朗西斯·高尔顿|人类学家、遗传学家、统计学家|相关性和回归概念提出者'
    },{
        img: 'https://images.jieyu.ai/images/2024/09/karl-pearson.png',
        text:'卡尔.皮尔逊|数学家（生物统计第一人）|概率密度函数、卡方检验、皮尔逊相关性'
    },{
        img: 'https://images.jieyu.ai/images/2024/09/ronald-fisher.png',
        text: '罗纳德.费雪|统计学家、生物学家|费雪精确检验\nFisher\'s Iris Flower dataset\n一已之力，建立了统计学'
    }]"
/>

</div>

<!-- 7 continue -->

<div class="abs top-15% left-0 h-full w-50%"
    v-motion
    :enter="{opacity: 0}"
    :click-7="{opacity: 1}">

## 异常值 (outlier) 的来源
## 异常值修正的必要性
## 修正算法
### 3$\sigma$法
### 分位数修正法

<v-clicks at="2">

### 中位数极值法

</v-clicks>
</div>

<NoteCell class="abs w-50% left-50% top-40% h-full"
    v-motion
    :enter="{opacity: 0}"
    :click-5-6="{opacity: 1}">

```python
from scipy.stats.mstats import winsorize

arr = np.append(np.random.randint(1, 3, 20), [15, -10])
print(arr)
print(winsorize(arr, 0.05).data)
```
</NoteCell>

<!--
这里的异常值，更准确的说法是outlier，离群值

[click]
交易数据中一般异常值比较少。如果有的话，就是出错了。

但在财务数据中出现的比较多。比如，上一年公司是亏损，今年扭亏为盈，计算yoy的利润增长，这个结果怎么算？

或者去年的利润接近于零，今年有了一定的利润，这个利润增长率会很大。A股中不时有年报净利增长十倍的标的，往往都是这种情况。正常的公司都不会有这种增长。

英伟达是现在增长最好的公司，它2024年全财年净利增长是580%，这种增长也是多年罕见。它2025财年Q2的盈利增长也就是122%，已经跌下来了。

所以，无论是从统计上看，还是从实际的投资价值上来看，这种利润增长的异常值都应该去除掉。
[click] 

因子检验中两类重要算法，无论是回归，还是IC法，都会严重受离群值影响。

但有一种IC，受离群值影响小一点。知道是哪一种吗？

对，就是rank ic。但它还是会有影响。这个影响就是说，本来我们应该完全排除这个outlier的，但是，你还是把它放进来参与排序了。所以，它还是会有一点点影响。

因子检验的算法是不太好做异常值修正的。这样相当于一个盲盒，导致不可预测的偏差。所以在Alphalens中是没有做的，我们要自己进行处理。

[click]

异常值修正我们一般使用拉回法，也就是如果数据超出了指定的范围，我们就将其拉回到指定范围的上下限。

但是，我们也可以考虑异常值修正背后的交易含义。比如，如果我是做价值型投资的，那么，在我前面举过的例子中，这种利润增长上十倍的，你要不要拉回？还是说直接drop掉？这个是大家可以自行考虑的点。不一定要拉回，也可以考虑drop掉。

[click]

修正的第一个方法是3 sigma法。在进行此类修正时，我们要先将数据形态调整为正态分布，然后再进行这种修正才会有意义。修正完了，再变换回去。

[click]
分位数修正法也称winsorizing，是根据生物统计学家charles winsor的名字来命名的。这个方法在scipy中有实现。

winsorize这个方法，就是通过分位数来指定缩尾范围，然后把超过范围的数值拉回。

[click]

生物学家推动了统计学。弗朗西斯·高尔顿与学生皮尔逊一起，发展了相关与回归分析。高尔顿还发明了演示正态分布的教学道具，高尔顿盒子。

皮尔逊还发明了皮尔逊相关系数和卡方检验。

罗纳德·费雪（Ronald Fisher）发明了Fisher精确检验（Fisher's exact test）。

Paul Meier（保罗.迈耶，医学方法的统计学家)和 Edward Lynn Kaplan（爱德华.林恩.卡普兰，数学家）发明了卡普兰-迈耶估计器。

金融专业也常常借助生物学。比如，遗传算法就被用于组合优化、趋势预测等问题。著名的华人金融学家，罗闻全教授，结合生物学和金融学，开创了生物金融学。在网易公开课上，可以看到他讲授金融理论的课程。

[click]
中位数极值法是将偏离中位数n倍的数拉回。它与3 sigma极相似，又不同。

不同的是，它使用的中心位置是中位数，在3 sigma 中使用的是均值；范围取值是数组减去中位数的中位数再乘以系数。这个系数一般取5；这个5也是有来由的。具体地大家可以看一下教程中的推导。
-->
