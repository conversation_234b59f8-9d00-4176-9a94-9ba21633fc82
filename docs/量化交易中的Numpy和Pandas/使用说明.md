## 1. 欢迎学习《量化人的Numpy和Pandas》课程！

<div style='width:150px;float:right;padding: 0.5rem 0 0 1rem;text-align:center'>
<img src='https://images.jieyu.ai/images/hot/logo/quantide-alpha-yellow.jpg'>
</div>

本课程是匡醍QuanTide（公众号）量化系列课程的基础课程，将带领您学习量化人的母语 -- Numpy 和 Pandas。如果你去研究一些非常重要的量化库，比如 alphalens, empyrical, backtrader, tushare, akshare, jqdatasdk 等，或者一些非常优秀的量化框架比如 quantaxis, zillionare, vnpy 等等，你就会发现它们都依赖于 numpy 和 pandas。实际上，一个库只要依赖于 pandas，它也必将传递依赖到 numpy。

本课程的与其它 Numpy & Pandas教程不同之处在于，我们提取了量化场景下最高频使用的数据结构、运算和关键函数，既讲基本语法和原理，更回答你在工作中遇到的，要实现某个功能，却不知道如何用Numpy和Pandas来高效解决这个问题，不得不自已造一堆低效轮子的问题。

<hr>

## 2. 课程组织

在根目录下，有一个目录，即：

* **courseware** 课程正文。每一个文件都是notebook文件，按数字排序。

courseware文件夹是只读的。您可以修改和运行其中的notebook，但无法保存，因此，您可以任何时候通过刷新它来还原到我们发布的最初版本。

此外，您可以在根目录下新建自己的Notebook。如果您需要修改courseware下的文件，并保存话，请**事前**将它们拷贝到根目录下来。这是您的工作区。

## 3. 金融平民化运动

我们非常认同 Fawcett (Quantopian CEO)关于金融平民化的理念。我们认为，在国内，金融平民化运动应该包含两方面的内容，一方面要致力于传播正规的量化交易知识与理念，减少普通人被割韭菜的机会；另一方面，要提供低价甚至免费的工具、书籍和课程，让更多的人有机会学习到正规的量化课程。

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/06/20250702093056.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>Fawcett - Quantopian创始人</span>
</div>

为了践行金融平民化理念，我们已经提供了以下免费产品和服务：

### 3.1. 3.1 开源量化库

从2019年以来，我们就开始Zillionare量化框架的开发，前后投入约300万元。这个项目已经发布了2.0，采用先进的时序数据库，在运行中，已经管理了超过40亿条行情数据。

除了Zillionare之外，我们还开发了许多其它开源项目。

* [Python Project Wizard](https://pypi.org/project/ppw/)，已经成为许多人开启新Python项目的起手式。
* [Moonshot](https://github.com/zillionare/moonshot)，Alphalens在月度因子分析上有一些缺陷。这个库补齐了这块短板。
* [Quantstats-Reloaded](https://github.com/zillionare/quantstats-reloaded)，Quantstats是一个非常常用的量化策略评估及可视化工具。它已经在Python 3.12以上版本中不能使用了。我们接手维护了这个产品。

### 3.2. 3.2 免费书籍

我们出版了《Python高效编程实战指南》（机械工业出版社），根据我们与版社达成的协议，这本书的电子版可以在我们的[网站上](https://www.jieyu.ai/articles/python/best-practice-python/chap01/)免费阅读。

### 3.3. 3.3 免费课程

#### 3.3.1. 3.3.1 量化交易场景下的Numpy和Pandas

这是量化人必须掌握的量化母语。这门课程我们免费提供网页版（在有些平台，因为无法放链接，可能会以一元课的形式发布），您可以在[匡醍官网](https://www.jieyu.ai/)免费阅读。

#### 3.3.2. 3.3.2 因子分析与机器学习策略

我们免费开放了这门课程的部分视频（在有些平台，因为无法放链接，可能会以一元课的形式发布）您可以在[QuanTide@B站](https://space.bilibili.com/1229001873)观看。

但是我们也必须生产付费产品。我们的付费产品主要是基于提供的计算资源、数据、实用模型和辅导来进行定价。**感谢我们的付费学员！正是你们的赞助，我们才能有时间做一些免费的项目。**

## 4. Follow us

<div style="width: 100%;margin-bottom: 20px;">
<h3>Quantide@小红书 （5万粉）</h3>

<div style='width:120px;float:left;padding: 0.5rem 1rem 0 0;text-align:center'>
<img src='https://images.jieyu.ai/images/hot/xhs-logo.jpg'>
</div>


我们在[小红书](https://www.xiaohongshu.com/user/profile/5ba12feef7e8b9437f3aca0c)上有接近5万人关注。如果您按量化关键词进行搜索，再按用户排序，一眼就能看到我们。

欢迎点击[Follow](https://www.xiaohongshu.com/user/profile/5ba12feef7e8b9437f3aca0c)

</div>


<div style="width: 100%;margin: 60px 0;text-align: left;">
<h3>Quantide@公众号（万粉）</h3>

<div style='width:120px;float:left;padding: 0.5rem 1rem 0 0;text-align:center'>
<img src='https://images.jieyu.ai/images/hot/logo/gzh.jpg'>
</div>

图文为主，也包含『量化好声音』播客。

内容涵盖 Python 编程和量化策略研究、量化框架开发。

通过公众号后台消息，可以联系到课程助理。欢迎扫码关注。
</div>

<div style="width: 100%;margin-bottom: 20px;">

<h3>Quantide@知乎 （万粉）</h3>

<div style='width:120px;float:left;padding: 0.5rem 1rem 0 0;text-align:center'>
<img src='https://images.jieyu.ai/images/hot/logo/zhihu.png'>
</div>

我们在[知乎](https://www.zhihu.com/people/hbaaron)上有1.2万粉丝。开设有『大富翁之路』、『Daily Dose of Python』和『遐思录』等专栏。

欢迎点击[Follow](https://www.zhihu.com/people/hbaaron)
</div>

