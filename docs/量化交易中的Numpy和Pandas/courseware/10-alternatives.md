# Numpy和Pandas的替代方案

<!--![](https://images.jieyu.ai/images/2024/04/pandas-vs-x.jpg)-->

尽管Numpy与Pandas极其优秀，但它们也只在特定的领域俯视群臣。如果要处理的数据规模过大，或者需要利用CPU多核进行并行计算、甚至需要利用多台机器进行分布式运算，此时就需要使用替代方案。

!!! info
    Numpy在矩阵和线代方面的许多运算都是多线程的，能同时利用多个CPU核，但并不是所有的函数都支持多线程。Pandas则在多数运算上都不是多线程的。

## CuPy


<div style="position:relative;float:left">
<img src="https://images.jieyu.ai/images/2024/04/cupy.jpg" align="left" style="width:150px;margin:10px">
<p style="font-size:10px;text-align:center">来源：CuPy官网</p>
</div>

Numpy虽然很快，但它只能利用多个CPU以及高级指令如SIMD来进行加速。但现在是GPU的时代。当你处理超大规模的数据，同时又需要极快的处理速度时，可以考虑[CuPy](https://cupy.dev/)。CuPy是一个利用GPU来进行加速的数值计算包，其接口与Numpy高度兼容。通常，CuPy的运行速度是Numpy的100倍以上。

下面的例子演示了CuPy最简单的用法。在你学习完本课程之后，就会知道，这些API跟Numpy几乎完全一样。

```python
>>> import cupy as cp
>>> x = cp.arange(6).reshape(2, 3).astype('f')
>>> x
array([[ 0.,  1.,  2.],
       [ 3.,  4.,  5.]], dtype=float32)
>>> x.sum(axis=1)
array([  3.,  12.], dtype=float32)

```


## Modin


<div style="position:relative;float:left">
<img src="https://images.jieyu.ai/images/2024/04/modin.jpg" align="left" style="width:150px;margin:10px">
<p style="font-size:10px;text-align:center">来源：modin项目</p>
</div>

Modin是Pandas的平替，是一个轻量的并行化计算的DataFrame。当我们使用Pandas进行计算时，它一般只能利用单个CPU核。当使用Modin之后，Modin将能利用所有的CPU核来进行计算。

除了并行化计算之外，Modin还试图填补小到1MB数据，大到1TB数据之间的鸿沟。Pandas在处理数据时，必须将所有的数据装入内存，因此是难以处理超过几G的数据集的。

!!! tip
    在磁盘文件上存储的数据集，一般进行了压缩。在它们加载进内存时，往往大小会大许多倍。因此，在磁盘上几G的数据，可能就无法加载进内存。

Modin使用起来非常简单，只需要在导入时将modin.pandas命名为别名pd，就可以像使用pandas一样来使用modin:

```python
# 之前，我们这样导入pandas
# import pandas as pd
# 要使用modin，我们需要这样做：
import modin.pandas as pd

import numpy as np

frame_data = np.random.randint(0, 100, size=(2**10, 2**8))
df = pd.DataFrame(frame_data)
```

从第8行以后，我们就完全像使用pandas一样来使用modin了。

!!! tip
    在底层，Modin使用了一个名为Ray的分布式计算框架。该框架也被用来构建chatGPT后台。也正因为如此，如果我们使用Modin来完成简单任务，它实际上会比直接使用Pandas略慢一点。<br>有时候我们还会听说Ponder，它是Modin的商业版。

## Polars

[Polars](https://pola.rs/)是使用 Apache Arrow Columnar Format 作为内存模型，使用 Rust 语言开发的的速度极快的 DataFrames 库。它使用多核CPU和SIMD指令来优化密集型运算。在数据大小方面，它能处理超过物理内存大小的数据集。

Polars有着与Pandas极其相似的API，但不完全是平替。与Modin相比，Polars被认为有着更快的速度。

```python
import polars as pl
from datetime import datetime

df = pl.DataFrame(
    {
        "integer": [1, 2, 3],
        "date": [
            datetime(2025, 1, 1),
            datetime(2025, 1, 2),
            datetime(2025, 1, 3),
        ],
        "float": [4.0, 5.0, 6.0],
        "string": ["a", "b", "c"],
    }
)

df.head()
```

## DuckDB

[DuckDB](https://duckdb.org/)尽管也常常在这一领域被提起，并且它的性能甚至比Polars更加优异（参考Polars自己发布的[性能评测报告](https://pola.rs/posts/benchmarks/))，但DuckDB主要是作为sqlite的平替存在。因此，似乎在量化框架中，DuckDB是必须安装的Python之一，毕竟，在很多场合，我们需要关系型数据库和基于SQL的查询。

## Dask

尽管Polars很优秀，也能处理较大规模的数据，但它主要是作为单机工作系统设计的。如果我们要处理的数据规模特别大，对算力要求很高，此时我们就很可能需要使用到Dask。Dask可以运行在数千个计算结点构建成的集群上。

Dask提供的主要数据结构是dask.array和dask.dataframe。通过这些数据结构，它可以把数据以分布式方式存储在数千个结点上。当需要对这些数据进行运算是，Dask有一个特别独特的设计：它不是把数据搬运到计算结点上再进行计算，而是把你的数据处理程序复制到数据结点上，执行就进计算。考虑到数据处理程序的大小远远小于数据本身，这种设计就使得Dask可以处理超大规模的数据。

当然，也正是因为这样的设计，使得几乎所有的计算都涉及到网络，因此，如果数据量不是特别大，一般使用Modin或者Polars就够了。

