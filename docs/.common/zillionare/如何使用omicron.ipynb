{"cells": [{"cell_type": "code", "execution_count": 69, "id": "86117da4", "metadata": {}, "outputs": [], "source": ["# 本段代码的作用是允许多行输出， http://www.jieyu.ai/blog/2024/03/04/how-to-use-jupyter-as-quant-researcher/\n", "from IPython.core.interactiveshell import InteractiveShell\n", "InteractiveShell.ast_node_interactivity = \"all\"\n", "\n", "# 本教程中使用的辅助函数\n", "import inspect\n", "def quick_look(obj):\n", "    for item in dir(obj):\n", "        if item.startswith(\"_\"): continue\n", "        usage = inspect.getdoc(getattr(obj, item))\n", "        if usage is None: continue\n", "        title = usage.split(\"\\n\")[0]\n", "        print(f\"{item}: {title}\")"]}, {"cell_type": "markdown", "id": "76f04e58-ed6d-491e-960e-87be12146af4", "metadata": {}, "source": ["## 概览\n", "\n", "zillionare通过omicron提供了行情数据、板块、时间运算、基础算法、策略编写、回测等功能。\n", "\n", "``` {tip}\n", "在课程环境中，我们使用了来自聚宽的数据（正版付费数据）。从2024年1月起，聚宽在盘中不再提供1分钟数据，因此，在课程环境中，暂时无法在盘中提供分钟线数据，但是个股的最新实时价格仍然可以获取。\n", "```\n", "\n", "本教程将简要介绍如何使用omicron的重要功能。如果有本教程没有覆盖的部分，请查询[omicron的在线文档](https://zillionare.github.io/omicron)\n", "\n", "## 初始化\n", "\n", "在课程环境中，我们通过以下方法来完成初始化。"]}, {"cell_type": "code", "execution_count": 11, "id": "b6cc73b2", "metadata": {}, "outputs": [], "source": ["from coursea import *\n", "await init()\n", "clear_output()"]}, {"cell_type": "markdown", "id": "cc9192d8", "metadata": {}, "source": ["初始化完成后，我们将获得以下可用的方法（部分为属性）："]}, {"cell_type": "code", "execution_count": 70, "id": "7cab1560", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BoardType: An enumeration.\n", "Dict: The central part of internal API.\n", "Frame: The central part of internal API.\n", "FrameType: 对证券交易中K线周期的封装。提供了以下对应周期:\n", "List: The central part of internal API.\n", "Optional: Internal indicator of special typing constructs.\n", "Stock: Stock对象用于归集某支证券（股票和指数，不包括其它投资品种）的相关信息，比如行情数据（OHLC等）、市值数据、所属概念分类等。\n", "TraderClient: 大富翁实盘和回测的客户端。\n", "Union: Internal indicator of special typing constructs.\n", "array_math_round: 将一维数组arr的数据进行四舍五入\n", "array_price_equal: 判断两个价格数组是否相等\n", "bars_since: Return the number of bars since `condition` sequence was last `True`,\n", "cfg4py: Top-level package for Cfg4Py.\n", "clear_output: Clear the output of the current cell receiving output.\n", "count_between: 计算数组中，`start`元素与`end`元素之间共有多少个元素\n", "datetime: Fast implementation of the datetime type.\n", "fill_nan: 将ts中的NaN替换为其前值\n", "find_runs: Find runs of consecutive items in an array.\n", "logging: Logging package for Python. Based on PEP 282 and comments thereto in\n", "math_round: 由于浮点数的表示问题，很多语言的round函数与数学上的round函数不一致。下面的函数结果与数学上的一致。\n", "moving_average: 生成ts序列的移动平均值\n", "np: <PERSON>um<PERSON><PERSON>\n", "omicron: Omicron提供数据持久化、时间（日历、triggers)、行情数据model、基础运算和基础量化因子\n", "pd: pandas - a powerful data analysis and manipulation library for Python\n", "price_equal: 判断股价是否相等\n", "smallest_n_argpos: get smallest n (min->max) elements and return argpos which its value ordered in ascent\n", "sys: This module provides access to some objects used or maintained by the\n", "top_n_argpos: get top n (max->min) elements and return argpos which its value ordered in descent\n"]}], "source": ["import coursea\n", "quick_look(coursea)\n"]}, {"cell_type": "markdown", "id": "e8109b69", "metadata": {}, "source": ["要更进一步了解这些方法（属性）的作用，可以在单元格里，按以下语法进行查询（以查询top_n_argpos为例）："]}, {"cell_type": "code", "execution_count": 24, "id": "3186c81a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[0;31mSignature:\u001b[0m \u001b[0mtop_n_argpos\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mts\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;34m'np.array'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mn\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;34m'int'\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0;34m'np.array'\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mDocstring:\u001b[0m\n", "get top n (max->min) elements and return argpos which its value ordered in descent\n", "\n", "Example:\n", "    >>> top_n_argpos([np.nan, 4, 3, 9, 8, 5, 2, 1, 0, 6, 7], 2)\n", "    array([3, 4])\n", "\n", "Args:\n", "    ts (np.array): [description]\n", "    n (int): [description]\n", "\n", "Returns:\n", "    np.array: [description]\n", "\u001b[0;31mFile:\u001b[0m      ~/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/extensions/np.py\n", "\u001b[0;31mType:\u001b[0m      function"]}], "source": ["top_n_argpos?"]}, {"cell_type": "markdown", "id": "483fb3ae", "metadata": {}, "source": ["部分方法在文档中是带有示例的，可以仿示例运行下："]}, {"cell_type": "code", "execution_count": null, "id": "d3df2d4b", "metadata": {}, "outputs": [], "source": ["top_n_argpos([np.nan, 4, 3, 9, 8, 5, 2, 1, 0, 6, 7], 2)"]}, {"cell_type": "markdown", "id": "13b4a0a2", "metadata": {}, "source": ["## 证券列表\n", "\n", "我们通过Security提供的查询方法来进行证券列表的操作。如果我们要查询证券列表，首先要通过Security.select来获得一个查询对象(Query)，然后基于这个对象，我们提供了链式调用的方法。最后，我们都要通过`eval`方法来最终获得查询结果。"]}, {"cell_type": "code", "execution_count": 91, "id": "fdf4b0d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["('Query对象: ', <omicron.models.security.Query at 0x7f46ca8c3280>)"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["('给Query对象加上过滤条件: ', <omicron.models.security.Query at 0x7f46ca8c3280>)"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["('Query对象求值:', ['000001.XSHE', '000002.XSHE', '000004.XSHE'])"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["('加上名字模糊匹配后:', ['000301.XSHE', '000682.XSHE', '000725.XSHE'])"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["('由代码查名字', '上证指数')"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["import datetime\n", "query = Security.select(datetime.date(2024, 3, 8))\n", "\n", "\"Query对象: \", query\n", "\n", "\"给Query对象加上过滤条件: \", query.types([\"stock\"])\n", "\n", "\"Query对象求值:\", (await query.types([\"stock\"]).eval())[:3]\n", "\n", "\"加上名字模糊匹配后:\", (await query.types([\"stock\"]).alias_like(\"东方\").eval())[:3]\n", "\n", "\"由代码查名字\", await Security.alias(\"000001.XSHG\")"]}, {"cell_type": "markdown", "id": "52ff5619", "metadata": {}, "source": ["我们可以通过辅助函数`quick_look`来查看Query对象提供的其它过滤方法："]}, {"cell_type": "code", "execution_count": 71, "id": "8b018532", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["alias_like: 查找股票/证券显示名中出现`display_name的品种\n", "eval: 对查询结果进行求值，返回code列表\n", "exclude_cyb: 从返回结果中排除创业板类型的股票\n", "exclude_kcb: 从返回结果中排除科创板类型的股票\n", "exclude_st: 从返回结果中排除ST类型的股票\n", "include_exit: 从返回结果中包含已退市的证券\n", "name_like: 查找股票/证券名称中出现`name`的品种\n", "only_cyb: 返回结果中只包含创业板股票\n", "only_kcb: 返回结果中只包含科创板股票\n", "only_st: 返回结果中只包含ST类型的证券\n", "target_date: date(year, month, day) --> date object\n", "types: 选择类型在`types`中的证券品种\n"]}], "source": ["quick_look(query)"]}, {"cell_type": "markdown", "id": "07e4ed84-e84a-413f-8026-21c3b1143c76", "metadata": {}, "source": ["## 行情数据\n", "### 个股行情\n", "\n", "我们主要通过get_bars来获取个股或指数的行情数据。"]}, {"cell_type": "code", "execution_count": 82, "id": "28fb38b3-fd3f-4809-a204-db6075ea3815", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([('2024-03-06T00:00:00', 10.4 , 10.45, 10.33, 10.33, 1.34564016e+08, 1.39694006e+09, 126.92925),\n", "       ('2024-03-07T00:00:00', 10.33, 10.64, 10.33, 10.38, 2.01616589e+08, 2.10958924e+09, 126.92925),\n", "       ('2024-03-08T00:00:00', 10.38, 10.38, 10.38, 10.38, 1.64850000e+06, 1.71114300e+07, 126.92925)],\n", "      dtype=[('frame', '<M8[s]'), ('open', '<f4'), ('high', '<f4'), ('low', '<f4'), ('close', '<f4'), ('volume', '<f8'), ('amount', '<f8'), ('factor', '<f4')])"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[0;31mSignature:\u001b[0m\n", "\u001b[0mStock\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_bars\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mcode\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mstr\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mn\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mint\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mframe_type\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mcoretypes\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtypes\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mFrameType\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mend\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mUnion\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mdatetime\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdatetime\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdatetime\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mfq\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0munclosed\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0mnumpy\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mndarray\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mtyping\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mAny\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnumpy\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdtype\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mdtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'frame'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<M8[s]'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'open'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'high'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'low'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'close'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'volume'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f8'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'amount'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f8'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'factor'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mDocstring:\u001b[0m\n", "获取到`end`为止的`n`个行情数据。\n", "\n", "返回的数据是按照时间顺序递增排序的。在遇到停牌的情况时，该时段数据将被跳过，因此返回的记录可能不是交易日连续的，并且可能不足`n`个。\n", "\n", "如果系统当前没有到指定时间`end`的数据，将尽最大努力返回数据。调用者可以通过判断最后一条数据的时间是否等于`end`来判断是否获取到了全部数据。\n", "\n", "Args:\n", "    code: 证券代码\n", "    n: 记录数\n", "    frame_type: 帧类型\n", "    end: 截止时间,如果未指明，则取当前时间\n", "    fq: 是否对返回记录进行复权。如果为`True`的话，则进行前复权。Defaults to True.\n", "    unclosed: 是否包含最新未收盘的数据？ Defaults to <PERSON>.\n", "\n", "Returns:\n", "    返回dtype为`coretypes.bars_dtype`的一维numpy数组。\n", "\u001b[0;31mFile:\u001b[0m      ~/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/models/stock.py\n", "\u001b[0;31mType:\u001b[0m      method"]}], "source": ["bars = await Stock.get_bars(\"000001.XSHE\", 250, FrameType.DAY)\n", "bars[-3:]"]}, {"cell_type": "markdown", "id": "d896e841-913f-4b7d-85a3-2d1556265e3b", "metadata": {}, "source": ["这样获取的数据是Numpy structured array数据结构。如果你更熟悉DataFrame,可以通过以下方法将其转换为pd.DataFrame："]}, {"cell_type": "code", "execution_count": 83, "id": "007038c7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>factor</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-02-28</td>\n", "      <td>13.422896</td>\n", "      <td>13.520518</td>\n", "      <td>13.286226</td>\n", "      <td>13.452183</td>\n", "      <td>5.934735e+07</td>\n", "      <td>8.333160e+08</td>\n", "      <td>123.909691</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-03-01</td>\n", "      <td>13.471707</td>\n", "      <td>13.852428</td>\n", "      <td>13.413135</td>\n", "      <td>13.832905</td>\n", "      <td>1.194347e+08</td>\n", "      <td>1.719711e+09</td>\n", "      <td>123.909691</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-03-02</td>\n", "      <td>13.793857</td>\n", "      <td>14.096481</td>\n", "      <td>13.725522</td>\n", "      <td>13.901239</td>\n", "      <td>9.917098e+07</td>\n", "      <td>1.447566e+09</td>\n", "      <td>123.909691</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-03-03</td>\n", "      <td>14.008623</td>\n", "      <td>14.028147</td>\n", "      <td>13.803619</td>\n", "      <td>13.950050</td>\n", "      <td>6.745170e+07</td>\n", "      <td>9.855521e+08</td>\n", "      <td>123.909691</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-03-06</td>\n", "      <td>13.959812</td>\n", "      <td>13.959812</td>\n", "      <td>13.393610</td>\n", "      <td>13.520518</td>\n", "      <td>1.421191e+08</td>\n", "      <td>2.023955e+09</td>\n", "      <td>123.909691</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>245</th>\n", "      <td>2024-03-04</td>\n", "      <td>10.450000</td>\n", "      <td>10.500000</td>\n", "      <td>10.320000</td>\n", "      <td>10.330000</td>\n", "      <td>1.655930e+08</td>\n", "      <td>1.719563e+09</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>246</th>\n", "      <td>2024-03-05</td>\n", "      <td>10.300000</td>\n", "      <td>10.470000</td>\n", "      <td>10.260000</td>\n", "      <td>10.430000</td>\n", "      <td>1.817319e+08</td>\n", "      <td>1.889144e+09</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>247</th>\n", "      <td>2024-03-06</td>\n", "      <td>10.400000</td>\n", "      <td>10.450000</td>\n", "      <td>10.330000</td>\n", "      <td>10.330000</td>\n", "      <td>1.345640e+08</td>\n", "      <td>1.396940e+09</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>2024-03-07</td>\n", "      <td>10.330000</td>\n", "      <td>10.640000</td>\n", "      <td>10.330000</td>\n", "      <td>10.380000</td>\n", "      <td>2.016166e+08</td>\n", "      <td>2.109589e+09</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>2024-03-08</td>\n", "      <td>10.380000</td>\n", "      <td>10.380000</td>\n", "      <td>10.380000</td>\n", "      <td>10.380000</td>\n", "      <td>1.648500e+06</td>\n", "      <td>1.711143e+07</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>250 rows × 8 columns</p>\n", "</div>"], "text/plain": ["         frame       open       high        low      close        volume  \\\n", "0   2023-02-28  13.422896  13.520518  13.286226  13.452183  5.934735e+07   \n", "1   2023-03-01  13.471707  13.852428  13.413135  13.832905  1.194347e+08   \n", "2   2023-03-02  13.793857  14.096481  13.725522  13.901239  9.917098e+07   \n", "3   2023-03-03  14.008623  14.028147  13.803619  13.950050  6.745170e+07   \n", "4   2023-03-06  13.959812  13.959812  13.393610  13.520518  1.421191e+08   \n", "..         ...        ...        ...        ...        ...           ...   \n", "245 2024-03-04  10.450000  10.500000  10.320000  10.330000  1.655930e+08   \n", "246 2024-03-05  10.300000  10.470000  10.260000  10.430000  1.817319e+08   \n", "247 2024-03-06  10.400000  10.450000  10.330000  10.330000  1.345640e+08   \n", "248 2024-03-07  10.330000  10.640000  10.330000  10.380000  2.016166e+08   \n", "249 2024-03-08  10.380000  10.380000  10.380000  10.380000  1.648500e+06   \n", "\n", "           amount      factor  \n", "0    8.333160e+08  123.909691  \n", "1    1.719711e+09  123.909691  \n", "2    1.447566e+09  123.909691  \n", "3    9.855521e+08  123.909691  \n", "4    2.023955e+09  123.909691  \n", "..            ...         ...  \n", "245  1.719563e+09  126.929253  \n", "246  1.889144e+09  126.929253  \n", "247  1.396940e+09  126.929253  \n", "248  2.109589e+09  126.929253  \n", "249  1.711143e+07  126.929253  \n", "\n", "[250 rows x 8 columns]"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(bars, columns=bars.dtype.names)"]}, {"cell_type": "markdown", "id": "cd2203e3", "metadata": {}, "source": ["同样的，我们可以通过notebook的的`?`语法来查阅`get_bars`的帮助："]}, {"cell_type": "code", "execution_count": 84, "id": "0db3d458", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[0;31mSignature:\u001b[0m\n", "\u001b[0mStock\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_bars\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mcode\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mstr\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mn\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mint\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mframe_type\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mcoretypes\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtypes\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mFrameType\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mend\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mUnion\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mdatetime\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdatetime\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdatetime\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0mfq\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0munclosed\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0mnumpy\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mndarray\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mtyping\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mAny\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnumpy\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdtype\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mdtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'frame'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<M8[s]'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'open'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'high'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'low'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'close'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'volume'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f8'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'amount'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f8'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m'factor'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'<f4'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mDocstring:\u001b[0m\n", "获取到`end`为止的`n`个行情数据。\n", "\n", "返回的数据是按照时间顺序递增排序的。在遇到停牌的情况时，该时段数据将被跳过，因此返回的记录可能不是交易日连续的，并且可能不足`n`个。\n", "\n", "如果系统当前没有到指定时间`end`的数据，将尽最大努力返回数据。调用者可以通过判断最后一条数据的时间是否等于`end`来判断是否获取到了全部数据。\n", "\n", "Args:\n", "    code: 证券代码\n", "    n: 记录数\n", "    frame_type: 帧类型\n", "    end: 截止时间,如果未指明，则取当前时间\n", "    fq: 是否对返回记录进行复权。如果为`True`的话，则进行前复权。Defaults to True.\n", "    unclosed: 是否包含最新未收盘的数据？ Defaults to <PERSON>.\n", "\n", "Returns:\n", "    返回dtype为`coretypes.bars_dtype`的一维numpy数组。\n", "\u001b[0;31mFile:\u001b[0m      ~/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/models/stock.py\n", "\u001b[0;31mType:\u001b[0m      method"]}], "source": ["Stock.get_bars?"]}, {"cell_type": "markdown", "id": "28caed41", "metadata": {}, "source": ["Stock还提供了其它方法："]}, {"cell_type": "code", "execution_count": 85, "id": "2cde2e52", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["batch_cache_bars: 缓存已收盘的分钟线和日线\n", "batch_cache_unclosed_bars: 缓存未收盘的5、15、30、60分钟线及日线、周线、月线\n", "batch_get_day_level_bars_in_range: 获取多支股票（指数）在[start, end)时间段内的行情数据\n", "batch_get_min_level_bars_in_range: 获取多支股票（指数）在[start, end)时间段内的行情数据\n", "cache_bars: 将当期已收盘的行情数据缓存\n", "cache_unclosed_bars: 将未结束的行情数据缓存\n", "days_since_ipo: 获取上市以来经过了多少个交易日\n", "format_code: 新三板和北交所的股票, 暂不支持, 默认返回None\n", "fuzzy_match: 对股票/指数进行模糊匹配查找\n", "get_bars: 获取到`end`为止的`n`个行情数据。\n", "get_bars_in_range: 获取指定证券（`code`）在[`start`, `end`]期间帧类型为`frame_type`的行情数据。\n", "get_buy_limit_secs: 查询在[start, end]区间涨停的个股代码\n", "get_latest_price: 获取多支股票的最新价格（交易日当天），暂不包括指数\n", "get_sell_limit_secs: 查询在[start, end]区间跌停的个股代码\n", "get_stock: 根据`code`来查找对应的股票（含指数）对象信息。\n", "get_trade_price_limits: 从influxdb和cache中获取个股在[begin, end]之间的涨跌停价。\n", "init: 初始化Security.\n", "load_securities: 加载所有证券的信息，并缓存到内存中\n", "persist_bars: 将行情数据持久化\n", "qfq: 对行情数据执行前复权操作\n", "resample: 将原来为`from_frame`的行情数据转换为`to_frame`的行情数据\n", "reset_cache: 清除缓存的行情数据\n", "save_securities: 保存指定的证券信息到缓存中，并且存入influxdb，定时job调用本接口\n", "save_trade_price_limits: 保存涨跌停价\n", "save_xrxd_reports: 保存1年内的分红送股信息，并且存入influxdb，定时job调用本接口\n", "security_type: 返回证券类型\n", "trade_price_limit_flags: 获取个股在[start, end]之间的涨跌停标志\n", "trade_price_limit_flags_ex: 获取股票`code`在`[start, end]`区间的涨跌停标志\n", "update_secs_cache: 更新证券列表到缓存数据库中\n"]}], "source": ["quick_look(Stock)"]}, {"cell_type": "markdown", "id": "540fea05", "metadata": {}, "source": ["我们关心的函数主要是get_*。"]}, {"cell_type": "markdown", "id": "a56cbab0-4d6c-4259-838e-64bd48dcea11", "metadata": {}, "source": ["### 板块信息\n", "\n", "在课程环境中，板块信息来自同花顺分类，通过Board类来进行操作。一些函数要求指明在操作在`BoardType.CONCEPT`类型上，还是`BoardType.INDUSTRY`。\n", "\n", "!!! warning\n", "    同花顺只使用简码。因此，我们在与omicron的其它部分交互时，要注意进行代码转换。"]}, {"cell_type": "code", "execution_count": 102, "id": "868b3e3e-122a-40c3-9c8e-1b852987339f", "metadata": {}, "outputs": [{"data": {"text/plain": ["('概念板块有:',\n", " [['309119', '人形机器人', 23],\n", "  ['309118', '<PERSON><PERSON>概念(文生视频)', 19],\n", "  ['309115', '低空经济', 27]])"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["('行业板块有:',\n", " [['881101', '种植业与林业', 25], ['881102', '养殖业', 27], ['881103', '农产品加工', 42]])"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["['309119 人形机器人']"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["dict_keys(['code', 'name', 'stocks'])"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["[['001306', '夏厦精密'], ['002009', '天奇股份'], ['832491', '奥迪威']]"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["from omicron.models.board import Board, BoardType\n", "\n", "# 如果不传入任何参数，默认取概念板块列表\n", "concepts = await Board.board_list()\n", "\"概念板块有:\", concepts[:3]\n", "\n", "industries = await Board.board_list(BoardType.INDUSTRY)\n", "\"行业板块有:\", industries[:3]\n", "\n", "# 通过板块名查找板块id\n", "await Board.fuzzy_match_board_name(\"人形\")\n", "\n", "# 通过板块id来获取板块信息：id(code), name和成份股\n", "board_info = await Board.board_info_by_id(\"309119\", full_mode=True)  \n", "board_info.keys()\n", "board_info[\"stocks\"][:3]\n"]}, {"cell_type": "markdown", "id": "c7ff84a2-14a2-4703-9ea8-1ea0ec7fc727", "metadata": {}, "source": ["### 获取板块行情\n", "\n", "在知道板块id的情况下，就可以通过`Board.get_bars_in_range`方法来获取板块的行情数据。"]}, {"cell_type": "code", "execution_count": 103, "id": "93d831e9-c91a-4976-831c-5748fb1c8d92", "metadata": {}, "outputs": [{"data": {"text/plain": ["rec.array([('2023-02-27T00:00:00', 1117.748, 1124.364, 1108.741, 1109.525, 1.77208600e+08, 1.13933095e+09, 1.),\n", "           ('2023-02-28T00:00:00', 1112.246, 1119.568, 1109.827, 1113.43 , 1.32828124e+08, 6.65160380e+08, 1.),\n", "           ('2023-03-01T00:00:00', 1122.233, 1123.493, 1116.62 , 1123.274, 7.21718910e+07, 3.71172850e+08, 1.)],\n", "          dtype=[('frame', '<M8[s]'), ('open', '<f4'), ('high', '<f4'), ('low', '<f4'), ('close', '<f4'), ('volume', '<f8'), ('amount', '<f8'), ('factor', '<f4')])"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["start = datetime.date(2022, 9, 1)  # 起始时间， 可修改\n", "end = datetime.date(2023, 3, 1)  # 截止时间， 可修改\n", "board_code = '881128' # 汽车服务， 可修改\n", "bars = await Board.get_bars_in_range(board_code, start, end)\n", "bars[-3:] # 打印后3条数据"]}, {"cell_type": "markdown", "id": "ebeadb91-4dd4-4fee-abbf-13d3ae6e1ffe", "metadata": {}, "source": ["## 绘制K线图\n", "\n", "我们可以通过 omicron.plotting.candlestick中的类 Candlestick 来绘制k线图："]}, {"cell_type": "code", "execution_count": 109, "id": "2c9c7c7e-855f-47e1-8f38-ae87335e7ce8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.7, 0.15, 0.15]\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [1067.23, 1082.51, 1090.92, 1111.16, 1109.46, 1099.8, 1100.6, 1108.18, 1088.3, 1065.5, 1041.49, 1040.36, 1074.7, 1079.79, 1060.06, 1040.61, 1009.8, 1022.42, 1000.52, 993.13, 981.51, 959.32, 969.28, 988.31, 1000.19, 1019.45, 1031.11, 1035.06, 1031.08, 1022.39, 1009.58, 987.51, 996.38, 1008.32, 1020.94, 976.77, 997.94, 1028.22, 1047.52, 1051.75, 1072.04, 1073.76, 1064.36, 1072.13, 1075.19, 1079.5, 1067.79, 1082.17, 1081.12, 1091.05, 1068.59, 1080.15, 1079.4, 1099.12, 1081.22, 1077.6, 1071.7, 1088.92, 1117.63, 1102.26, 1094.21, 1120.37, 1119.34, 1110.06, 1102.04, 1089.96, 1076.37, 1078.8, 1074.42, 1083.37, 1070.57, 1048.57, 1051.34, 1042.4, 1031.56, 1020.98, 1037.09, 1038.65, 1022.85, 1013.3, 1023.96, 1041.4, 1044.65, 1047.42, 1054.55, 1051.12, 1063.06, 1057.07, 1047.49, 1059.57, 1060.87, 1066.37, 1061.58, 1061.36, 1083.83, 1103.04, 1092.67, 1123.75, 1116.51, 1119.52, 1117.39, 1118.66, 1116.63, 1122.66, 1128.27, 1131.03, 1130.92, 1132.24, 1128.97, 1106.6, 1123.51, 1129.05, 1128.3, 1126.63, 1121.17, 1109.53, 1113.43, 1123.27], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [1080.427001953125, 1083.81103515625, 1091.135986328125, 1112.3380126953125, 1114.14697265625, 1117.490966796875, 1103.2850341796875, 1109.81201171875, 1094.9639892578125, 1092.85205078125, 1065.7509765625, 1050.6190185546875, 1081.22900390625, 1085.4639892578125, 1078.821044921875, 1065.72900390625, 1032.56201171875, 1023.5900268554688, 1023.0070190429688, 1012.8980102539062, 994.489990234375, 984.833984375, 970.5170288085938, 988.676025390625, 1007.0499877929688, 1026.4610595703125, 1031.6209716796875, 1044.0240478515625, 1041.9549560546875, 1034.623046875, 1026.8900146484375, 1021.4299926757812, 1000.4910278320312, 1016.4929809570312, 1029.383056640625, 1014.4110107421875, 1004.3309936523438, 1028.958984375, 1052.9010009765625, 1063.240966796875, 1073.72802734375, 1081.7509765625, 1073.220947265625, 1082.1920166015625, 1083.2259521484375, 1096.9840087890625, 1081.166015625, 1082.16796875, 1092.4119873046875, 1092.6219482421875, 1089.4840087890625, 1083.4029541015625, 1090.5450439453125, 1104.1529541015625, 1094.7110595703125, 1082.906982421875, 1075.7679443359375, 1090.8609619140625, 1125.0849609375, 1128.779052734375, 1102.6280517578125, 1120.3719482421875, 1120.97802734375, 1123.885986328125, 1110.447998046875, 1103.64697265625, 1087.885009765625, 1090.9560546875, 1080.758056640625, 1087.8929443359375, 1083.550048828125, 1075.22802734375, 1055.385986328125, 1052.373046875, 1050.2490234375, 1031.803955078125, 1039.1600341796875, 1039.383056640625, 1033.2740478515625, 1022.135009765625, 1025.197998046875, 1041.509033203125, 1046.614013671875, 1047.5069580078125, 1057.0159912109375, 1055.4599609375, 1066.510009765625, 1062.7449951171875, 1050.053955078125, 1062.5040283203125, 1064.426025390625, 1069.47998046875, 1064.8280029296875, 1062.6009521484375, 1085.9429931640625, 1104.697021484375, 1093.844970703125, 1124.08203125, 1122.9560546875, 1122.8929443359375, 1121.60498046875, 1122.4539794921875, 1123.1910400390625, 1123.9639892578125, 1129.7320556640625, 1132.5970458984375, 1140.6199951171875, 1133.718017578125, 1134.3709716796875, 1116.6409912109375, 1124.18505859375, 1136.0970458984375, 1131.56396484375, 1137.2030029296875, 1127.072021484375, 1124.364013671875, 1119.5679931640625, 1123.4930419921875], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [1066.4739990234375, 1069.3709716796875, 1077.68798828125, 1092.4630126953125, 1100.3380126953125, 1098.93701171875, 1087.9830322265625, 1100.3199462890625, 1080.5849609375, 1059.657958984375, 1041.488037109375, 1032.3280029296875, 1046.29296875, 1051.4449462890625, 1058.4219970703125, 1036.0799560546875, 1008.625, 1003.739990234375, 1000.5230102539062, 988.3820190429688, 981.2639770507812, 955.7659912109375, 952.6589965820312, 952.7230224609375, 981.8690185546875, 1011.1719970703125, 1011.2059936523438, 1031.72802734375, 1026.9560546875, 1011.5490112304688, 1008.7680053710938, 983.8280029296875, 972.875, 996.5070190429688, 1008.77197265625, 975.2219848632812, 977.7730102539062, 1000.5759887695312, 1034.2969970703125, 1043.583984375, 1050.7979736328125, 1063.7449951171875, 1059.23095703125, 1064.5419921875, 1064.85595703125, 1078.791015625, 1061.85400390625, 1063.593994140625, 1075.791015625, 1075.010009765625, 1067.199951171875, 1063.6400146484375, 1071.4090576171875, 1079.0579833984375, 1076.16796875, 1069.718017578125, 1055.5489501953125, 1072.9169921875, 1084.262939453125, 1100.717041015625, 1090.4150390625, 1097.5009765625, 1109.1309814453125, 1106.31005859375, 1099.623046875, 1085.14794921875, 1072.7650146484375, 1075.58203125, 1069.697998046875, 1071.0269775390625, 1067.06201171875, 1045.39697265625, 1041.1839599609375, 1037.6529541015625, 1029.1199951171875, 1013.2249755859375, 1020.302001953125, 1025.4949951171875, 1017.5280151367188, 1012.4849853515625, 1015.1179809570312, 1019.4910278320312, 1035.5870361328125, 1041.032958984375, 1042.239990234375, 1047.2330322265625, 1040.5140380859375, 1053.3470458984375, 1044.385986328125, 1052.8599853515625, 1056.0789794921875, 1059.9520263671875, 1060.3990478515625, 1056.3060302734375, 1070.489990234375, 1094.9110107421875, 1088.2449951171875, 1105.06494140625, 1113.2110595703125, 1107.3809814453125, 1113.364013671875, 1114.697021484375, 1115.0159912109375, 1112.1199951171875, 1119.032958984375, 1124.970947265625, 1126.739013671875, 1130.240966796875, 1126.751953125, 1102.6949462890625, 1102.637939453125, 1122.35302734375, 1125.782958984375, 1120.991943359375, 1114.623046875, 1108.740966796875, 1109.8270263671875, 1116.6199951171875], "name": "K线", "open": [1077.7030029296875, 1069.3709716796875, 1080.0140380859375, 1093.052978515625, 1108.7110595703125, 1107.3489990234375, 1096.1700439453125, 1101.5150146484375, 1092.93798828125, 1091.406982421875, 1065.7509765625, 1032.7249755859375, 1046.3310546875, 1066.3189697265625, 1070.116943359375, 1060.9560546875, 1032.56201171875, 1010.0189819335938, 1020.677978515625, 1005.791015625, 991.6690063476562, 982.2739868164062, 961.2899780273438, 971.5250244140625, 983.5650024414062, 1011.1719970703125, 1011.2059936523438, 1033.1719970703125, 1029.9739990234375, 1025.2320556640625, 1021.8270263671875, 1012.0800170898438, 989.739013671875, 997.2899780273438, 1009.5960083007812, 1014.4110107421875, 977.7730102539062, 1000.5759887695312, 1034.509033203125, 1043.583984375, 1050.7979736328125, 1067.56298828125, 1072.06103515625, 1066.02099609375, 1071.323974609375, 1091.31201171875, 1078.5899658203125, 1067.6529541015625, 1081.373046875, 1077.0050048828125, 1089.4840087890625, 1072.3389892578125, 1080.7490234375, 1079.0579833984375, 1091.0689697265625, 1077.2220458984375, 1065.958984375, 1072.9169921875, 1087.3280029296875, 1124.0870361328125, 1101.77294921875, 1098.2960205078125, 1115.737060546875, 1110.552001953125, 1107.677978515625, 1100.6009521484375, 1087.885009765625, 1075.58203125, 1077.35400390625, 1073.7349853515625, 1078.5870361328125, 1069.6080322265625, 1045.3299560546875, 1049.4530029296875, 1047.5489501953125, 1024.426025390625, 1020.302001953125, 1037.2530517578125, 1033.1400146484375, 1019.239013671875, 1017.8579711914062, 1020.9299926757812, 1040.762939453125, 1045.31005859375, 1046.237060546875, 1049.5040283203125, 1052.251953125, 1058.6710205078125, 1044.385986328125, 1052.8599853515625, 1056.0789794921875, 1069.47998046875, 1062.156982421875, 1058.740966796875, 1073.26904296875, 1096.7669677734375, 1092.25, 1105.06494140625, 1122.85302734375, 1115.614990234375, 1115.031982421875, 1121.1719970703125, 1120.864013671875, 1115.31396484375, 1121.06005859375, 1130.4639892578125, 1139.0040283203125, 1132.386962890625, 1128.9520263671875, 1107.802001953125, 1108.60498046875, 1123.4019775390625, 1129.31494140625, 1130.56005859375, 1127.072021484375, 1117.748046875, 1112.2459716796875, 1122.2330322265625], "type": "candlestick", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "yaxis": "y"}, {"line": {"color": "#1432F5", "width": 1}, "name": "ma5", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, 1092.2559999999999, 1098.77, 1102.388, 1105.84, 1101.268, 1092.476, 1080.814, 1068.766, 1062.0700000000002, 1060.3680000000002, 1059.28, 1059.104, 1052.992, 1042.5359999999998, 1026.682, 1013.2959999999999, 1001.4759999999999, 991.38, 980.752, 978.31, 979.722, 987.31, 1001.6679999999999, 1014.8239999999998, 1023.3779999999999, 1027.818, 1025.8439999999998, 1017.1239999999998, 1009.3879999999998, 1004.8359999999998, 1004.5459999999998, 997.9839999999997, 1000.0699999999997, 1006.4379999999998, 1014.2779999999998, 1020.4399999999998, 1039.494, 1054.658, 1061.8859999999997, 1066.8079999999998, 1071.4959999999999, 1072.988, 1071.7939999999999, 1075.356, 1077.154, 1080.3259999999998, 1078.144, 1080.616, 1080.062, 1083.662, 1081.696, 1083.498, 1081.808, 1083.712, 1087.414, 1091.622, 1094.944, 1104.6779999999999, 1110.762, 1109.248, 1109.204, 1108.354, 1099.5539999999999, 1091.446, 1084.318, 1080.584, 1076.706, 1071.146, 1065.654, 1059.25, 1048.8880000000001, 1038.97, 1036.6740000000002, 1034.1360000000002, 1030.2260000000003, 1026.5740000000003, 1027.17, 1028.0320000000002, 1029.2320000000002, 1034.1460000000002, 1042.3960000000002, 1047.8280000000002, 1052.16, 1054.644, 1054.6580000000001, 1055.662, 1057.6119999999999, 1058.274, 1059.176, 1061.9499999999998, 1066.802, 1075.2359999999999, 1080.4959999999999, 1092.93, 1103.9599999999998, 1111.098, 1113.968, 1119.166, 1117.742, 1118.9720000000002, 1120.7220000000002, 1123.45, 1125.902, 1129.0240000000001, 1130.286, 1125.952, 1124.448, 1124.074, 1123.286, 1122.818, 1125.732, 1122.9360000000001, 1119.8120000000001, 1118.8060000000003], "yaxis": "y"}, {"line": {"color": "#EB52F7", "width": 1}, "name": "ma10", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, 1092.366, 1089.7920000000001, 1085.577, 1083.9550000000002, 1080.818, 1075.8780000000002, 1069.959, 1060.8790000000001, 1052.303, 1043.525, 1036.288, 1030.29, 1022.1860000000001, 1011.6440000000001, 1002.4960000000001, 996.509, 994.393, 996.524, 997.788, 1000.8439999999999, 1003.77, 1006.5769999999999, 1009.396, 1012.106, 1014.107, 1016.182, 1011.914, 1008.597, 1007.913, 1009.557, 1012.493, 1018.739, 1027.364, 1034.162, 1040.543, 1045.9679999999998, 1056.2409999999998, 1063.2259999999999, 1068.6209999999999, 1071.981, 1075.9109999999998, 1075.5659999999998, 1076.2049999999997, 1077.7089999999998, 1080.408, 1081.011, 1080.821, 1081.212, 1081.887, 1085.538, 1086.659, 1089.2210000000002, 1093.2430000000002, 1097.237, 1098.3310000000001, 1100.4130000000002, 1101.6490000000001, 1102.1160000000002, 1101.104, 1096.7830000000001, 1094.8940000000002, 1092.5300000000004, 1085.3500000000004, 1078.5500000000004, 1071.7840000000003, 1064.7360000000006, 1057.8380000000004, 1053.9100000000005, 1049.8950000000004, 1044.7380000000005, 1037.7310000000004, 1033.0700000000004, 1032.3530000000005, 1031.6840000000004, 1032.1860000000004, 1034.4850000000004, 1037.4990000000005, 1040.0960000000002, 1041.9380000000003, 1044.4020000000003, 1049.0290000000002, 1052.7200000000003, 1055.2170000000003, 1056.9100000000003, 1058.3040000000003, 1061.2320000000004, 1066.4240000000004, 1069.3850000000004, 1076.0530000000006, 1082.9550000000006, 1088.9500000000005, 1094.6020000000005, 1099.8310000000008, 1105.336000000001, 1111.4660000000008, 1115.9100000000008, 1118.7090000000007, 1122.5340000000008, 1123.3830000000007, 1124.6290000000008, 1123.337000000001, 1123.949000000001, 1124.988000000001, 1126.1550000000009, 1126.5520000000008, 1125.8420000000008, 1123.6920000000007, 1121.943000000001, 1121.046000000001], "yaxis": "y"}, {"line": {"color": "#C0C0C0", "width": 1}, "name": "ma20", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1064.3269999999998, 1060.041, 1053.8815, 1047.7994999999999, 1041.657, 1036.1935, 1032.1760000000002, 1028.7015, 1025.0455, 1022.1845, 1020.029, 1018.4335, 1015.791, 1011.875, 1008.3015, 1006.3455, 1003.1535, 1002.5605, 1002.8505, 1005.2004999999999, 1008.1315, 1012.6579999999999, 1018.3799999999998, 1023.1339999999997, 1027.3249999999996, 1031.0749999999996, 1034.0774999999996, 1035.9114999999997, 1038.2669999999996, 1040.7689999999998, 1044.2019999999998, 1047.1524999999997, 1051.7844999999995, 1055.9354999999996, 1060.4754999999996, 1063.4894999999995, 1068.5309999999995, 1072.2189999999994, 1075.2539999999995, 1078.7594999999994, 1081.2849999999994, 1082.3934999999994, 1084.7239999999995, 1087.4729999999995, 1089.3694999999996, 1090.7119999999993, 1091.2349999999994, 1091.6639999999993, 1091.4954999999993, 1091.1604999999993, 1090.7764999999993, 1090.8754999999992, 1089.2964999999992, 1087.893499999999, 1085.057499999999, 1082.574499999999, 1079.7434999999991, 1078.012999999999, 1075.499499999999, 1070.7604999999992, 1066.312499999999, 1062.799999999999, 1058.851499999999, 1055.116999999999, 1051.9849999999992, 1049.610499999999, 1047.668499999999, 1047.002999999999, 1045.9164999999991, 1044.569999999999, 1043.379999999999, 1042.894999999999, 1043.784999999999, 1044.2969999999991, 1045.244999999999, 1047.8584999999991, 1051.9614999999992, 1054.7404999999992, 1058.995499999999, 1063.678499999999, 1068.9894999999992, 1073.6609999999991, 1077.5239999999992, 1081.1229999999991, 1084.884999999999, 1088.5709999999992, 1092.5664999999992, 1095.9594999999993, 1099.7179999999994, 1103.7919999999992, 1106.1434999999992, 1109.2754999999993, 1112.4094999999993, 1115.7454999999993, 1119.0089999999993, 1120.8759999999993, 1121.2004999999995, 1122.2384999999992, 1122.2144999999994], "yaxis": "y"}, {"line": {"color": "#882111", "width": 1}, "name": "ma30", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1044.1413333333333, 1042.2196666666666, 1039.0529999999999, 1035.9016666666664, 1032.4736666666665, 1029.523, 1025.4219999999998, 1021.9999999999999, 1019.3346666666665, 1017.9753333333333, 1017.5169999999999, 1018.5353333333333, 1019.6486666666666, 1019.304, 1019.0486666666666, 1019.553, 1020.8493333333333, 1022.7823333333333, 1024.7740000000001, 1027.4606666666666, 1030.7246666666665, 1033.6273333333334, 1037.655, 1041.3256666666666, 1045.0193333333334, 1047.7203333333334, 1049.6586666666667, 1051.0116666666668, 1052.807, 1055.692, 1058.3543333333334, 1061.1753333333334, 1065.604, 1069.7026666666668, 1073.094, 1075.7973333333334, 1079.5703333333333, 1082.1846666666668, 1083.8706666666667, 1084.7673333333335, 1085.8213333333333, 1085.7723333333333, 1084.9326666666668, 1084.4986666666668, 1083.5076666666669, 1082.0533333333333, 1080.1026666666667, 1079.0793333333334, 1077.6286666666667, 1075.6863333333333, 1073.0946666666666, 1071.607, 1070.3153333333332, 1069.157, 1067.4336666666666, 1066.5446666666664, 1065.6619999999998, 1065.3739999999998, 1064.3123333333333, 1061.9743333333333, 1060.5513333333333, 1059.44, 1057.64, 1055.7146666666667, 1054.0913333333333, 1053.4843333333333, 1053.9203333333332, 1054.4636666666665, 1055.962, 1057.365, 1058.5700000000002, 1060.1306666666667, 1062.467, 1064.6433333333334, 1067.3186666666668, 1070.5423333333333, 1074.2106666666666, 1077.3383333333334, 1080.458, 1083.9953333333333, 1087.1053333333334, 1090.4236666666666, 1093.3453333333334, 1096.1336666666666, 1098.7740000000001, 1100.9946666666667, 1102.9416666666668, 1104.620666666667, 1106.8273333333336], "yaxis": "y"}, {"line": {"color": "#5E8E28", "width": 1}, "name": "ma60", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1051.2478333333333, 1051.6975, 1052.3285, 1052.8021666666668, 1052.7838333333334, 1052.6601666666668, 1052.4961666666668, 1052.0923333333335, 1051.602666666667, 1051.3713333333335, 1051.669166666667, 1052.1538333333335, 1052.290666666667, 1051.9013333333337, 1051.278166666667, 1050.803166666667, 1050.4760000000003, 1050.9308333333336, 1051.2013333333337, 1051.5735000000004, 1051.9096666666671, 1052.617166666667, 1053.985166666667, 1055.2413333333338, 1056.2265000000004, 1057.1325000000004, 1057.6603333333337, 1058.1928333333337, 1058.559666666667, 1058.833166666667, 1059.4528333333337, 1060.307666666667, 1061.6220000000005, 1062.708666666667, 1063.5926666666671, 1064.6408333333338, 1066.7453333333337, 1068.324166666667, 1069.9163333333338, 1071.066166666667, 1072.195666666667, 1072.9515000000004, 1073.6998333333336, 1074.5710000000004, 1075.413166666667, 1076.2978333333335, 1077.156666666667, 1078.2088333333336, 1079.0433333333335, 1079.8408333333334, 1080.1000000000001, 1081.0153333333335, 1081.8303333333336, 1082.6453333333336, 1083.1038333333336, 1083.7696666666668, 1084.3018333333337, 1084.9973333333337, 1085.5698333333337], "yaxis": "y"}, {"marker": {"color": ["#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136"]}, "showlegend": false, "type": "bar", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x2", "y": [226989220, 236729820, 237900350, 260565040, 201213880, 293380040, 202762210, 179904920, 191233000, 251875980, 256138710, 220909490, 217569700, 244210070, 167554260, 185700070, 218860010, 169503530, 178618370, 177557300, 149769520, 144396460, 138404680, 277828320, 208517190, 252159660, 225319460, 219485390, 181487540, 203298360, 168883830, 209963370, 255026110, 203196670, 250435530, 259622650, 207113380, 244164790, 283163650, 242695650, 330301590, 362218980, 240337660, 224186950, 350872790, 359563370, 266056050, 282066390, 247835770, 566705760, 365896890, 257560460, 298991630, 351255700, 233074010, 288032320, 242412630, 325431890, 523338470, 406875300, 278347760, 407131960, 366643860, 373338390, 327076390, 316952590, 245023320, 254669990, 179312620, 207137230, 195319830, 230006180, 196950780, 155263440, 172545450, 170384160, 144893910, 150435810, 163611590, 178334330, 181519070, 227687640, 186274290, 124547468, 102397242, 84239877, 236017620, 119786571, 22342508, 49775643, 57737987, 91940863, 18334682, 44698124, 253389540, 105484665, 85231478, 527081000, 533311190, 516175760, 116662517, 120588487, 237631200, 231850930, 228114460, 120600309, 188561260, 46104810, 193388150, 125722564, 253587950, 167138400, 91600823, 153580070, 121896192, 177208600, 132828124, 72171891], "yaxis": "y3"}, {"showlegend": false, "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x3", "y": [null, null, null, null, null, null, 79.7468354430378, 82.57294511091328, 57.37572111387301, 40.40639103501499, 29.413317914939086, 28.968217139478327, 54.227539139877976, 56.95046712094351, 44.60726060705147, 35.50425391669486, 25.581064853257367, 34.56978983460072, 27.62213938681536, 25.543393583178876, 22.36725337224261, 17.407235014330837, 26.219644190896208, 40.72115036198661, 48.32924753531781, 58.65307215446654, 63.89390034222551, 65.66318021274594, 61.99029875410149, 54.06653550279513, 44.09601736355228, 31.924438785637793, 39.92212947166057, 49.50477897815361, 58.00133350046944, 33.98427321807704, 46.682105004459544, 59.91558280187094, 66.31103616040382, 67.66777320529536, 73.75224769210874, 74.24527616522707, 66.1024011280651, 69.42825789420615, 70.7829858417031, 72.81879776861163, 59.33850575341311, 68.05342452085603, 66.79895153338327, 72.54286342308738, 49.36342154694091, 57.70946735100332, 56.978310332805016, 69.26477381020977, 52.83047114431639, 49.954088339977, 45.14666306023328, 58.974583822691685, 72.72887418086248, 59.8403976584911, 53.84344254709582, 66.81304875220465, 65.9376467121676, 57.75608832143123, 51.17136903482729, 42.42821605776269, 34.47594776921893, 37.0092168507978, 34.153198218309925, 44.630549917756646, 35.05722926323446, 24.304632087449555, 27.657139883822758, 23.60771424880288, 19.461627954744483, 16.141429631418145, 36.0701587739092, 37.78812932435741, 28.484790254163308, 24.16892125463229, 36.962432456597206, 52.64667421478675, 55.14251636684293, 57.436957639751085, 63.24407268272798, 58.626560809908504, 68.29587063054437, 59.87217562640539, 48.41221609201302, 59.997937882605925, 61.125394607154085, 65.99173575268924, 58.357162065261136, 57.987421204058286, 76.35148509613595, 83.67300506934635, 69.69536338832941, 81.06910213859213, 73.37142127558604, 74.57579453334463, 71.81751764537785, 72.54409376196416, 69.12575331014635, 73.56575106388539, 77.22267476151029, 78.94249590536681, 78.65844852653044, 79.70975586834345, 69.5280851956904, 33.939429563708906, 54.886283003815215, 59.88676190365304, 58.82746939847875, 56.17258315092477, 47.72274178386983, 34.4611532098827, 41.04778196544984, 54.80101520662085], "yaxis": "y5"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "汽车服务", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "volume", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.33999999999999997, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "rsi", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.12, "yanchor": "bottom", "yref": "paper"}], "height": 400, "hovermode": "x unified", "plot_bgcolor": "rgba(0,0,0,0)", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 0.94], "matches": "x3", "range": [-2, 117], "rangeslider": {"visible": false}, "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis2": {"anchor": "y3", "domain": [0, 0.94], "matches": "x3", "range": [-2, 117], "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis3": {"anchor": "y5", "domain": [0, 0.94], "minor": {"nticks": 5, "ticklen": 5, "ticks": "outside"}, "nticks": 11, "range": [-2, 117], "showgrid": false, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "ticklen": 10, "ticks": "outside", "type": "category"}, "yaxis": {"anchor": "x", "domain": [0.44, 1], "gridcolor": "rgba(0, 0, 0, 0.1)", "range": [905.0260467529297, 1197.650994873047], "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis2": {"anchor": "x", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis3": {"anchor": "x2", "domain": [0.22, 0.33999999999999997], "gridcolor": "rgba(0, 0, 0, 0.1)", "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis4": {"anchor": "x2", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y3", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis5": {"anchor": "x3", "domain": [0, 0.12], "gridcolor": "rgba(0, 0, 0, 0.1)", "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis6": {"anchor": "x3", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y5", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}}}, "text/html": ["<div>                            <div id=\"dedfe623-1a74-4aa8-8e6b-d2a3912edd52\" class=\"plotly-graph-div\" style=\"height:400px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"dedfe623-1a74-4aa8-8e6b-d2a3912edd52\")) {                    Plotly.newPlot(                        \"dedfe623-1a74-4aa8-8e6b-d2a3912edd52\",                        [{\"close\":[1067.23,1082.51,1090.92,1111.16,1109.46,1099.8,1100.6,1108.18,1088.3,1065.5,1041.49,1040.36,1074.7,1079.79,1060.06,1040.61,1009.8,1022.42,1000.52,993.13,981.51,959.32,969.28,988.31,1000.19,1019.45,1031.11,1035.06,1031.08,1022.39,1009.58,987.51,996.38,1008.32,1020.94,976.77,997.94,1028.22,1047.52,1051.75,1072.04,1073.76,1064.36,1072.13,1075.19,1079.5,1067.79,1082.17,1081.12,1091.05,1068.59,1080.15,1079.4,1099.12,1081.22,1077.6,1071.7,1088.92,1117.63,1102.26,1094.21,1120.37,1119.34,1110.06,1102.04,1089.96,1076.37,1078.8,1074.42,1083.37,1070.57,1048.57,1051.34,1042.4,1031.56,1020.98,1037.09,1038.65,1022.85,1013.3,1023.96,1041.4,1044.65,1047.42,1054.55,1051.12,1063.06,1057.07,1047.49,1059.57,1060.87,1066.37,1061.58,1061.36,1083.83,1103.04,1092.67,1123.75,1116.51,1119.52,1117.39,1118.66,1116.63,1122.66,1128.27,1131.03,1130.92,1132.24,1128.97,1106.6,1123.51,1129.05,1128.3,1126.63,1121.17,1109.53,1113.43,1123.27],\"decreasing\":{\"fillcolor\":\"#3DAA70\",\"line\":{\"color\":\"#3DAA70\"}},\"high\":[1080.427001953125,1083.81103515625,1091.135986328125,1112.3380126953125,1114.14697265625,1117.490966796875,1103.2850341796875,1109.81201171875,1094.9639892578125,1092.85205078125,1065.7509765625,1050.6190185546875,1081.22900390625,1085.4639892578125,1078.821044921875,1065.72900390625,1032.56201171875,1023.5900268554688,1023.0070190429688,1012.8980102539062,994.489990234375,984.833984375,970.5170288085938,988.676025390625,1007.0499877929688,1026.4610595703125,1031.6209716796875,1044.0240478515625,1041.9549560546875,1034.623046875,1026.8900146484375,1021.4299926757812,1000.4910278320312,1016.4929809570312,1029.383056640625,1014.4110107421875,1004.3309936523438,1028.958984375,1052.9010009765625,1063.240966796875,1073.72802734375,1081.7509765625,1073.220947265625,1082.1920166015625,1083.2259521484375,1096.9840087890625,1081.166015625,1082.16796875,1092.4119873046875,1092.6219482421875,1089.4840087890625,1083.4029541015625,1090.5450439453125,1104.1529541015625,1094.7110595703125,1082.906982421875,1075.7679443359375,1090.8609619140625,1125.0849609375,1128.779052734375,1102.6280517578125,1120.3719482421875,1120.97802734375,1123.885986328125,1110.447998046875,1103.64697265625,1087.885009765625,1090.9560546875,1080.758056640625,1087.8929443359375,1083.550048828125,1075.22802734375,1055.385986328125,1052.373046875,1050.2490234375,1031.803955078125,1039.1600341796875,1039.383056640625,1033.2740478515625,1022.135009765625,1025.197998046875,1041.509033203125,1046.614013671875,1047.5069580078125,1057.0159912109375,1055.4599609375,1066.510009765625,1062.7449951171875,1050.053955078125,1062.5040283203125,1064.426025390625,1069.47998046875,1064.8280029296875,1062.6009521484375,1085.9429931640625,1104.697021484375,1093.844970703125,1124.08203125,1122.9560546875,1122.8929443359375,1121.60498046875,1122.4539794921875,1123.1910400390625,1123.9639892578125,1129.7320556640625,1132.5970458984375,1140.6199951171875,1133.718017578125,1134.3709716796875,1116.6409912109375,1124.18505859375,1136.0970458984375,1131.56396484375,1137.2030029296875,1127.072021484375,1124.364013671875,1119.5679931640625,1123.4930419921875],\"increasing\":{\"fillcolor\":\"rgba(255,255,255,0.9)\",\"line\":{\"color\":\"#FF4136\"}},\"line\":{\"width\":1},\"low\":[1066.4739990234375,1069.3709716796875,1077.68798828125,1092.4630126953125,1100.3380126953125,1098.93701171875,1087.9830322265625,1100.3199462890625,1080.5849609375,1059.657958984375,1041.488037109375,1032.3280029296875,1046.29296875,1051.4449462890625,1058.4219970703125,1036.0799560546875,1008.625,1003.739990234375,1000.5230102539062,988.3820190429688,981.2639770507812,955.7659912109375,952.6589965820312,952.7230224609375,981.8690185546875,1011.1719970703125,1011.2059936523438,1031.72802734375,1026.9560546875,1011.5490112304688,1008.7680053710938,983.8280029296875,972.875,996.5070190429688,1008.77197265625,975.2219848632812,977.7730102539062,1000.5759887695312,1034.2969970703125,1043.583984375,1050.7979736328125,1063.7449951171875,1059.23095703125,1064.5419921875,1064.85595703125,1078.791015625,1061.85400390625,1063.593994140625,1075.791015625,1075.010009765625,1067.199951171875,1063.6400146484375,1071.4090576171875,1079.0579833984375,1076.16796875,1069.718017578125,1055.5489501953125,1072.9169921875,1084.262939453125,1100.717041015625,1090.4150390625,1097.5009765625,1109.1309814453125,1106.31005859375,1099.623046875,1085.14794921875,1072.7650146484375,1075.58203125,1069.697998046875,1071.0269775390625,1067.06201171875,1045.39697265625,1041.1839599609375,1037.6529541015625,1029.1199951171875,1013.2249755859375,1020.302001953125,1025.4949951171875,1017.5280151367188,1012.4849853515625,1015.1179809570312,1019.4910278320312,1035.5870361328125,1041.032958984375,1042.239990234375,1047.2330322265625,1040.5140380859375,1053.3470458984375,1044.385986328125,1052.8599853515625,1056.0789794921875,1059.9520263671875,1060.3990478515625,1056.3060302734375,1070.489990234375,1094.9110107421875,1088.2449951171875,1105.06494140625,1113.2110595703125,1107.3809814453125,1113.364013671875,1114.697021484375,1115.0159912109375,1112.1199951171875,1119.032958984375,1124.970947265625,1126.739013671875,1130.240966796875,1126.751953125,1102.6949462890625,1102.637939453125,1122.35302734375,1125.782958984375,1120.991943359375,1114.623046875,1108.740966796875,1109.8270263671875,1116.6199951171875],\"name\":\"K\\u7ebf\",\"open\":[1077.7030029296875,1069.3709716796875,1080.0140380859375,1093.052978515625,1108.7110595703125,1107.3489990234375,1096.1700439453125,1101.5150146484375,1092.93798828125,1091.406982421875,1065.7509765625,1032.7249755859375,1046.3310546875,1066.3189697265625,1070.116943359375,1060.9560546875,1032.56201171875,1010.0189819335938,1020.677978515625,1005.791015625,991.6690063476562,982.2739868164062,961.2899780273438,971.5250244140625,983.5650024414062,1011.1719970703125,1011.2059936523438,1033.1719970703125,1029.9739990234375,1025.2320556640625,1021.8270263671875,1012.0800170898438,989.739013671875,997.2899780273438,1009.5960083007812,1014.4110107421875,977.7730102539062,1000.5759887695312,1034.509033203125,1043.583984375,1050.7979736328125,1067.56298828125,1072.06103515625,1066.02099609375,1071.323974609375,1091.31201171875,1078.5899658203125,1067.6529541015625,1081.373046875,1077.0050048828125,1089.4840087890625,1072.3389892578125,1080.7490234375,1079.0579833984375,1091.0689697265625,1077.2220458984375,1065.958984375,1072.9169921875,1087.3280029296875,1124.0870361328125,1101.77294921875,1098.2960205078125,1115.737060546875,1110.552001953125,1107.677978515625,1100.6009521484375,1087.885009765625,1075.58203125,1077.35400390625,1073.7349853515625,1078.5870361328125,1069.6080322265625,1045.3299560546875,1049.4530029296875,1047.5489501953125,1024.426025390625,1020.302001953125,1037.2530517578125,1033.1400146484375,1019.239013671875,1017.8579711914062,1020.9299926757812,1040.762939453125,1045.31005859375,1046.237060546875,1049.5040283203125,1052.251953125,1058.6710205078125,1044.385986328125,1052.8599853515625,1056.0789794921875,1069.47998046875,1062.156982421875,1058.740966796875,1073.26904296875,1096.7669677734375,1092.25,1105.06494140625,1122.85302734375,1115.614990234375,1115.031982421875,1121.1719970703125,1120.864013671875,1115.31396484375,1121.06005859375,1130.4639892578125,1139.0040283203125,1132.386962890625,1128.9520263671875,1107.802001953125,1108.60498046875,1123.4019775390625,1129.31494140625,1130.56005859375,1127.072021484375,1117.748046875,1112.2459716796875,1122.2330322265625],\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"type\":\"candlestick\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#1432F5\",\"width\":1},\"name\":\"ma5\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,1092.2559999999999,1098.77,1102.388,1105.84,1101.268,1092.476,1080.814,1068.766,1062.0700000000002,1060.3680000000002,1059.28,1059.104,1052.992,1042.5359999999998,1026.682,1013.2959999999999,1001.4759999999999,991.38,980.752,978.31,979.722,987.31,1001.6679999999999,1014.8239999999998,1023.3779999999999,1027.818,1025.8439999999998,1017.1239999999998,1009.3879999999998,1004.8359999999998,1004.5459999999998,997.9839999999997,1000.0699999999997,1006.4379999999998,1014.2779999999998,1020.4399999999998,1039.494,1054.658,1061.8859999999997,1066.8079999999998,1071.4959999999999,1072.988,1071.7939999999999,1075.356,1077.154,1080.3259999999998,1078.144,1080.616,1080.062,1083.662,1081.696,1083.498,1081.808,1083.712,1087.414,1091.622,1094.944,1104.6779999999999,1110.762,1109.248,1109.204,1108.354,1099.5539999999999,1091.446,1084.318,1080.584,1076.706,1071.146,1065.654,1059.25,1048.8880000000001,1038.97,1036.6740000000002,1034.1360000000002,1030.2260000000003,1026.5740000000003,1027.17,1028.0320000000002,1029.2320000000002,1034.1460000000002,1042.3960000000002,1047.8280000000002,1052.16,1054.644,1054.6580000000001,1055.662,1057.6119999999999,1058.274,1059.176,1061.9499999999998,1066.802,1075.2359999999999,1080.4959999999999,1092.93,1103.9599999999998,1111.098,1113.968,1119.166,1117.742,1118.9720000000002,1120.7220000000002,1123.45,1125.902,1129.0240000000001,1130.286,1125.952,1124.448,1124.074,1123.286,1122.818,1125.732,1122.9360000000001,1119.8120000000001,1118.8060000000003],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#EB52F7\",\"width\":1},\"name\":\"ma10\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,1092.366,1089.7920000000001,1085.577,1083.9550000000002,1080.818,1075.8780000000002,1069.959,1060.8790000000001,1052.303,1043.525,1036.288,1030.29,1022.1860000000001,1011.6440000000001,1002.4960000000001,996.509,994.393,996.524,997.788,1000.8439999999999,1003.77,1006.5769999999999,1009.396,1012.106,1014.107,1016.182,1011.914,1008.597,1007.913,1009.557,1012.493,1018.739,1027.364,1034.162,1040.543,1045.9679999999998,1056.2409999999998,1063.2259999999999,1068.6209999999999,1071.981,1075.9109999999998,1075.5659999999998,1076.2049999999997,1077.7089999999998,1080.408,1081.011,1080.821,1081.212,1081.887,1085.538,1086.659,1089.2210000000002,1093.2430000000002,1097.237,1098.3310000000001,1100.4130000000002,1101.6490000000001,1102.1160000000002,1101.104,1096.7830000000001,1094.8940000000002,1092.5300000000004,1085.3500000000004,1078.5500000000004,1071.7840000000003,1064.7360000000006,1057.8380000000004,1053.9100000000005,1049.8950000000004,1044.7380000000005,1037.7310000000004,1033.0700000000004,1032.3530000000005,1031.6840000000004,1032.1860000000004,1034.4850000000004,1037.4990000000005,1040.0960000000002,1041.9380000000003,1044.4020000000003,1049.0290000000002,1052.7200000000003,1055.2170000000003,1056.9100000000003,1058.3040000000003,1061.2320000000004,1066.4240000000004,1069.3850000000004,1076.0530000000006,1082.9550000000006,1088.9500000000005,1094.6020000000005,1099.8310000000008,1105.336000000001,1111.4660000000008,1115.9100000000008,1118.7090000000007,1122.5340000000008,1123.3830000000007,1124.6290000000008,1123.337000000001,1123.949000000001,1124.988000000001,1126.1550000000009,1126.5520000000008,1125.8420000000008,1123.6920000000007,1121.943000000001,1121.046000000001],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#C0C0C0\",\"width\":1},\"name\":\"ma20\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1064.3269999999998,1060.041,1053.8815,1047.7994999999999,1041.657,1036.1935,1032.1760000000002,1028.7015,1025.0455,1022.1845,1020.029,1018.4335,1015.791,1011.875,1008.3015,1006.3455,1003.1535,1002.5605,1002.8505,1005.2004999999999,1008.1315,1012.6579999999999,1018.3799999999998,1023.1339999999997,1027.3249999999996,1031.0749999999996,1034.0774999999996,1035.9114999999997,1038.2669999999996,1040.7689999999998,1044.2019999999998,1047.1524999999997,1051.7844999999995,1055.9354999999996,1060.4754999999996,1063.4894999999995,1068.5309999999995,1072.2189999999994,1075.2539999999995,1078.7594999999994,1081.2849999999994,1082.3934999999994,1084.7239999999995,1087.4729999999995,1089.3694999999996,1090.7119999999993,1091.2349999999994,1091.6639999999993,1091.4954999999993,1091.1604999999993,1090.7764999999993,1090.8754999999992,1089.2964999999992,1087.893499999999,1085.057499999999,1082.574499999999,1079.7434999999991,1078.012999999999,1075.499499999999,1070.7604999999992,1066.312499999999,1062.799999999999,1058.851499999999,1055.116999999999,1051.9849999999992,1049.610499999999,1047.668499999999,1047.002999999999,1045.9164999999991,1044.569999999999,1043.379999999999,1042.894999999999,1043.784999999999,1044.2969999999991,1045.244999999999,1047.8584999999991,1051.9614999999992,1054.7404999999992,1058.995499999999,1063.678499999999,1068.9894999999992,1073.6609999999991,1077.5239999999992,1081.1229999999991,1084.884999999999,1088.5709999999992,1092.5664999999992,1095.9594999999993,1099.7179999999994,1103.7919999999992,1106.1434999999992,1109.2754999999993,1112.4094999999993,1115.7454999999993,1119.0089999999993,1120.8759999999993,1121.2004999999995,1122.2384999999992,1122.2144999999994],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#882111\",\"width\":1},\"name\":\"ma30\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1044.1413333333333,1042.2196666666666,1039.0529999999999,1035.9016666666664,1032.4736666666665,1029.523,1025.4219999999998,1021.9999999999999,1019.3346666666665,1017.9753333333333,1017.5169999999999,1018.5353333333333,1019.6486666666666,1019.304,1019.0486666666666,1019.553,1020.8493333333333,1022.7823333333333,1024.7740000000001,1027.4606666666666,1030.7246666666665,1033.6273333333334,1037.655,1041.3256666666666,1045.0193333333334,1047.7203333333334,1049.6586666666667,1051.0116666666668,1052.807,1055.692,1058.3543333333334,1061.1753333333334,1065.604,1069.7026666666668,1073.094,1075.7973333333334,1079.5703333333333,1082.1846666666668,1083.8706666666667,1084.7673333333335,1085.8213333333333,1085.7723333333333,1084.9326666666668,1084.4986666666668,1083.5076666666669,1082.0533333333333,1080.1026666666667,1079.0793333333334,1077.6286666666667,1075.6863333333333,1073.0946666666666,1071.607,1070.3153333333332,1069.157,1067.4336666666666,1066.5446666666664,1065.6619999999998,1065.3739999999998,1064.3123333333333,1061.9743333333333,1060.5513333333333,1059.44,1057.64,1055.7146666666667,1054.0913333333333,1053.4843333333333,1053.9203333333332,1054.4636666666665,1055.962,1057.365,1058.5700000000002,1060.1306666666667,1062.467,1064.6433333333334,1067.3186666666668,1070.5423333333333,1074.2106666666666,1077.3383333333334,1080.458,1083.9953333333333,1087.1053333333334,1090.4236666666666,1093.3453333333334,1096.1336666666666,1098.7740000000001,1100.9946666666667,1102.9416666666668,1104.620666666667,1106.8273333333336],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#5E8E28\",\"width\":1},\"name\":\"ma60\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1051.2478333333333,1051.6975,1052.3285,1052.8021666666668,1052.7838333333334,1052.6601666666668,1052.4961666666668,1052.0923333333335,1051.602666666667,1051.3713333333335,1051.669166666667,1052.1538333333335,1052.290666666667,1051.9013333333337,1051.278166666667,1050.803166666667,1050.4760000000003,1050.9308333333336,1051.2013333333337,1051.5735000000004,1051.9096666666671,1052.617166666667,1053.985166666667,1055.2413333333338,1056.2265000000004,1057.1325000000004,1057.6603333333337,1058.1928333333337,1058.559666666667,1058.833166666667,1059.4528333333337,1060.307666666667,1061.6220000000005,1062.708666666667,1063.5926666666671,1064.6408333333338,1066.7453333333337,1068.324166666667,1069.9163333333338,1071.066166666667,1072.195666666667,1072.9515000000004,1073.6998333333336,1074.5710000000004,1075.413166666667,1076.2978333333335,1077.156666666667,1078.2088333333336,1079.0433333333335,1079.8408333333334,1080.1000000000001,1081.0153333333335,1081.8303333333336,1082.6453333333336,1083.1038333333336,1083.7696666666668,1084.3018333333337,1084.9973333333337,1085.5698333333337],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"color\":[\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\"]},\"showlegend\":false,\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[226989220.0,236729820.0,237900350.0,260565040.0,201213880.0,293380040.0,202762210.0,179904920.0,191233000.0,251875980.0,256138710.0,220909490.0,217569700.0,244210070.0,167554260.0,185700070.0,218860010.0,169503530.0,178618370.0,177557300.0,149769520.0,144396460.0,138404680.0,277828320.0,208517190.0,252159660.0,225319460.0,219485390.0,181487540.0,203298360.0,168883830.0,209963370.0,255026110.0,203196670.0,250435530.0,259622650.0,207113380.0,244164790.0,283163650.0,242695650.0,330301590.0,362218980.0,240337660.0,224186950.0,350872790.0,359563370.0,266056050.0,282066390.0,247835770.0,566705760.0,365896890.0,257560460.0,298991630.0,351255700.0,233074010.0,288032320.0,242412630.0,325431890.0,523338470.0,406875300.0,278347760.0,407131960.0,366643860.0,373338390.0,327076390.0,316952590.0,245023320.0,254669990.0,179312620.0,207137230.0,195319830.0,230006180.0,196950780.0,155263440.0,172545450.0,170384160.0,144893910.0,150435810.0,163611590.0,178334330.0,181519070.0,227687640.0,186274290.0,124547468.0,102397242.0,84239877.0,236017620.0,119786571.0,22342508.0,49775643.0,57737987.0,91940863.0,18334682.0,44698124.0,253389540.0,105484665.0,85231478.0,527081000.0,533311190.0,516175760.0,116662517.0,120588487.0,237631200.0,231850930.0,228114460.0,120600309.0,188561260.0,46104810.0,193388150.0,125722564.0,253587950.0,167138400.0,91600823.0,153580070.0,121896192.0,177208600.0,132828124.0,72171891.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y3\"},{\"showlegend\":false,\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,79.7468354430378,82.57294511091328,57.37572111387301,40.40639103501499,29.413317914939086,28.968217139478327,54.227539139877976,56.95046712094351,44.60726060705147,35.50425391669486,25.581064853257367,34.56978983460072,27.62213938681536,25.543393583178876,22.36725337224261,17.407235014330837,26.219644190896208,40.72115036198661,48.32924753531781,58.65307215446654,63.89390034222551,65.66318021274594,61.99029875410149,54.06653550279513,44.09601736355228,31.924438785637793,39.92212947166057,49.50477897815361,58.00133350046944,33.98427321807704,46.682105004459544,59.91558280187094,66.31103616040382,67.66777320529536,73.75224769210874,74.24527616522707,66.1024011280651,69.42825789420615,70.7829858417031,72.81879776861163,59.33850575341311,68.05342452085603,66.79895153338327,72.54286342308738,49.36342154694091,57.70946735100332,56.978310332805016,69.26477381020977,52.83047114431639,49.954088339977,45.14666306023328,58.974583822691685,72.72887418086248,59.8403976584911,53.84344254709582,66.81304875220465,65.9376467121676,57.75608832143123,51.17136903482729,42.42821605776269,34.47594776921893,37.0092168507978,34.153198218309925,44.630549917756646,35.05722926323446,24.304632087449555,27.657139883822758,23.60771424880288,19.461627954744483,16.141429631418145,36.0701587739092,37.78812932435741,28.484790254163308,24.16892125463229,36.962432456597206,52.64667421478675,55.14251636684293,57.436957639751085,63.24407268272798,58.626560809908504,68.29587063054437,59.87217562640539,48.41221609201302,59.997937882605925,61.125394607154085,65.99173575268924,58.357162065261136,57.987421204058286,76.35148509613595,83.67300506934635,69.69536338832941,81.06910213859213,73.37142127558604,74.57579453334463,71.81751764537785,72.54409376196416,69.12575331014635,73.56575106388539,77.22267476151029,78.94249590536681,78.65844852653044,79.70975586834345,69.5280851956904,33.939429563708906,54.886283003815215,59.88676190365304,58.82746939847875,56.17258315092477,47.72274178386983,34.4611532098827,41.04778196544984,54.80101520662085],\"type\":\"scatter\",\"xaxis\":\"x3\",\"yaxis\":\"y5\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-2,117],\"rangeslider\":{\"visible\":false}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.44,1.0],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\",\"range\":[905.0260467529297,1197.650994873047]},\"yaxis2\":{\"anchor\":\"x\",\"overlaying\":\"y\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis2\":{\"anchor\":\"y3\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-2,117]},\"yaxis3\":{\"anchor\":\"x2\",\"domain\":[0.22,0.33999999999999997],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis4\":{\"anchor\":\"x2\",\"overlaying\":\"y3\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis3\":{\"anchor\":\"y5\",\"domain\":[0.0,0.94],\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"minor\":{\"nticks\":5,\"ticklen\":5,\"ticks\":\"outside\"},\"nticks\":11,\"ticklen\":10,\"ticks\":\"outside\",\"type\":\"category\",\"range\":[-2,117]},\"yaxis5\":{\"anchor\":\"x3\",\"domain\":[0.0,0.12],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis6\":{\"anchor\":\"x3\",\"overlaying\":\"y5\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"annotations\":[{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"\\u6c7d\\u8f66\\u670d\\u52a1\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"volume\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.33999999999999997,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"rsi\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.12,\"yanchor\":\"bottom\",\"yref\":\"paper\"}],\"hovermode\":\"x unified\",\"plot_bgcolor\":\"rgba(0,0,0,0)\",\"height\":400},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('dedfe623-1a74-4aa8-8e6b-d2a3912edd52');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from omicron.plotting.candlestick import Candlestick\n", "\n", "cs = Candlestick(bars, height=400, title=f\"汽车服务\")\n", "cs.plot()"]}, {"cell_type": "markdown", "id": "e9cda207", "metadata": {}, "source": ["Candlestick有许多标注方法。比如："]}, {"cell_type": "code", "execution_count": 110, "id": "12544e62", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.7, 0.15, 0.15]\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [1067.23, 1082.51, 1090.92, 1111.16, 1109.46, 1099.8, 1100.6, 1108.18, 1088.3, 1065.5, 1041.49, 1040.36, 1074.7, 1079.79, 1060.06, 1040.61, 1009.8, 1022.42, 1000.52, 993.13, 981.51, 959.32, 969.28, 988.31, 1000.19, 1019.45, 1031.11, 1035.06, 1031.08, 1022.39, 1009.58, 987.51, 996.38, 1008.32, 1020.94, 976.77, 997.94, 1028.22, 1047.52, 1051.75, 1072.04, 1073.76, 1064.36, 1072.13, 1075.19, 1079.5, 1067.79, 1082.17, 1081.12, 1091.05, 1068.59, 1080.15, 1079.4, 1099.12, 1081.22, 1077.6, 1071.7, 1088.92, 1117.63, 1102.26, 1094.21, 1120.37, 1119.34, 1110.06, 1102.04, 1089.96, 1076.37, 1078.8, 1074.42, 1083.37, 1070.57, 1048.57, 1051.34, 1042.4, 1031.56, 1020.98, 1037.09, 1038.65, 1022.85, 1013.3, 1023.96, 1041.4, 1044.65, 1047.42, 1054.55, 1051.12, 1063.06, 1057.07, 1047.49, 1059.57, 1060.87, 1066.37, 1061.58, 1061.36, 1083.83, 1103.04, 1092.67, 1123.75, 1116.51, 1119.52, 1117.39, 1118.66, 1116.63, 1122.66, 1128.27, 1131.03, 1130.92, 1132.24, 1128.97, 1106.6, 1123.51, 1129.05, 1128.3, 1126.63, 1121.17, 1109.53, 1113.43, 1123.27], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [1080.427001953125, 1083.81103515625, 1091.135986328125, 1112.3380126953125, 1114.14697265625, 1117.490966796875, 1103.2850341796875, 1109.81201171875, 1094.9639892578125, 1092.85205078125, 1065.7509765625, 1050.6190185546875, 1081.22900390625, 1085.4639892578125, 1078.821044921875, 1065.72900390625, 1032.56201171875, 1023.5900268554688, 1023.0070190429688, 1012.8980102539062, 994.489990234375, 984.833984375, 970.5170288085938, 988.676025390625, 1007.0499877929688, 1026.4610595703125, 1031.6209716796875, 1044.0240478515625, 1041.9549560546875, 1034.623046875, 1026.8900146484375, 1021.4299926757812, 1000.4910278320312, 1016.4929809570312, 1029.383056640625, 1014.4110107421875, 1004.3309936523438, 1028.958984375, 1052.9010009765625, 1063.240966796875, 1073.72802734375, 1081.7509765625, 1073.220947265625, 1082.1920166015625, 1083.2259521484375, 1096.9840087890625, 1081.166015625, 1082.16796875, 1092.4119873046875, 1092.6219482421875, 1089.4840087890625, 1083.4029541015625, 1090.5450439453125, 1104.1529541015625, 1094.7110595703125, 1082.906982421875, 1075.7679443359375, 1090.8609619140625, 1125.0849609375, 1128.779052734375, 1102.6280517578125, 1120.3719482421875, 1120.97802734375, 1123.885986328125, 1110.447998046875, 1103.64697265625, 1087.885009765625, 1090.9560546875, 1080.758056640625, 1087.8929443359375, 1083.550048828125, 1075.22802734375, 1055.385986328125, 1052.373046875, 1050.2490234375, 1031.803955078125, 1039.1600341796875, 1039.383056640625, 1033.2740478515625, 1022.135009765625, 1025.197998046875, 1041.509033203125, 1046.614013671875, 1047.5069580078125, 1057.0159912109375, 1055.4599609375, 1066.510009765625, 1062.7449951171875, 1050.053955078125, 1062.5040283203125, 1064.426025390625, 1069.47998046875, 1064.8280029296875, 1062.6009521484375, 1085.9429931640625, 1104.697021484375, 1093.844970703125, 1124.08203125, 1122.9560546875, 1122.8929443359375, 1121.60498046875, 1122.4539794921875, 1123.1910400390625, 1123.9639892578125, 1129.7320556640625, 1132.5970458984375, 1140.6199951171875, 1133.718017578125, 1134.3709716796875, 1116.6409912109375, 1124.18505859375, 1136.0970458984375, 1131.56396484375, 1137.2030029296875, 1127.072021484375, 1124.364013671875, 1119.5679931640625, 1123.4930419921875], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [1066.4739990234375, 1069.3709716796875, 1077.68798828125, 1092.4630126953125, 1100.3380126953125, 1098.93701171875, 1087.9830322265625, 1100.3199462890625, 1080.5849609375, 1059.657958984375, 1041.488037109375, 1032.3280029296875, 1046.29296875, 1051.4449462890625, 1058.4219970703125, 1036.0799560546875, 1008.625, 1003.739990234375, 1000.5230102539062, 988.3820190429688, 981.2639770507812, 955.7659912109375, 952.6589965820312, 952.7230224609375, 981.8690185546875, 1011.1719970703125, 1011.2059936523438, 1031.72802734375, 1026.9560546875, 1011.5490112304688, 1008.7680053710938, 983.8280029296875, 972.875, 996.5070190429688, 1008.77197265625, 975.2219848632812, 977.7730102539062, 1000.5759887695312, 1034.2969970703125, 1043.583984375, 1050.7979736328125, 1063.7449951171875, 1059.23095703125, 1064.5419921875, 1064.85595703125, 1078.791015625, 1061.85400390625, 1063.593994140625, 1075.791015625, 1075.010009765625, 1067.199951171875, 1063.6400146484375, 1071.4090576171875, 1079.0579833984375, 1076.16796875, 1069.718017578125, 1055.5489501953125, 1072.9169921875, 1084.262939453125, 1100.717041015625, 1090.4150390625, 1097.5009765625, 1109.1309814453125, 1106.31005859375, 1099.623046875, 1085.14794921875, 1072.7650146484375, 1075.58203125, 1069.697998046875, 1071.0269775390625, 1067.06201171875, 1045.39697265625, 1041.1839599609375, 1037.6529541015625, 1029.1199951171875, 1013.2249755859375, 1020.302001953125, 1025.4949951171875, 1017.5280151367188, 1012.4849853515625, 1015.1179809570312, 1019.4910278320312, 1035.5870361328125, 1041.032958984375, 1042.239990234375, 1047.2330322265625, 1040.5140380859375, 1053.3470458984375, 1044.385986328125, 1052.8599853515625, 1056.0789794921875, 1059.9520263671875, 1060.3990478515625, 1056.3060302734375, 1070.489990234375, 1094.9110107421875, 1088.2449951171875, 1105.06494140625, 1113.2110595703125, 1107.3809814453125, 1113.364013671875, 1114.697021484375, 1115.0159912109375, 1112.1199951171875, 1119.032958984375, 1124.970947265625, 1126.739013671875, 1130.240966796875, 1126.751953125, 1102.6949462890625, 1102.637939453125, 1122.35302734375, 1125.782958984375, 1120.991943359375, 1114.623046875, 1108.740966796875, 1109.8270263671875, 1116.6199951171875], "name": "K线", "open": [1077.7030029296875, 1069.3709716796875, 1080.0140380859375, 1093.052978515625, 1108.7110595703125, 1107.3489990234375, 1096.1700439453125, 1101.5150146484375, 1092.93798828125, 1091.406982421875, 1065.7509765625, 1032.7249755859375, 1046.3310546875, 1066.3189697265625, 1070.116943359375, 1060.9560546875, 1032.56201171875, 1010.0189819335938, 1020.677978515625, 1005.791015625, 991.6690063476562, 982.2739868164062, 961.2899780273438, 971.5250244140625, 983.5650024414062, 1011.1719970703125, 1011.2059936523438, 1033.1719970703125, 1029.9739990234375, 1025.2320556640625, 1021.8270263671875, 1012.0800170898438, 989.739013671875, 997.2899780273438, 1009.5960083007812, 1014.4110107421875, 977.7730102539062, 1000.5759887695312, 1034.509033203125, 1043.583984375, 1050.7979736328125, 1067.56298828125, 1072.06103515625, 1066.02099609375, 1071.323974609375, 1091.31201171875, 1078.5899658203125, 1067.6529541015625, 1081.373046875, 1077.0050048828125, 1089.4840087890625, 1072.3389892578125, 1080.7490234375, 1079.0579833984375, 1091.0689697265625, 1077.2220458984375, 1065.958984375, 1072.9169921875, 1087.3280029296875, 1124.0870361328125, 1101.77294921875, 1098.2960205078125, 1115.737060546875, 1110.552001953125, 1107.677978515625, 1100.6009521484375, 1087.885009765625, 1075.58203125, 1077.35400390625, 1073.7349853515625, 1078.5870361328125, 1069.6080322265625, 1045.3299560546875, 1049.4530029296875, 1047.5489501953125, 1024.426025390625, 1020.302001953125, 1037.2530517578125, 1033.1400146484375, 1019.239013671875, 1017.8579711914062, 1020.9299926757812, 1040.762939453125, 1045.31005859375, 1046.237060546875, 1049.5040283203125, 1052.251953125, 1058.6710205078125, 1044.385986328125, 1052.8599853515625, 1056.0789794921875, 1069.47998046875, 1062.156982421875, 1058.740966796875, 1073.26904296875, 1096.7669677734375, 1092.25, 1105.06494140625, 1122.85302734375, 1115.614990234375, 1115.031982421875, 1121.1719970703125, 1120.864013671875, 1115.31396484375, 1121.06005859375, 1130.4639892578125, 1139.0040283203125, 1132.386962890625, 1128.9520263671875, 1107.802001953125, 1108.60498046875, 1123.4019775390625, 1129.31494140625, 1130.56005859375, 1127.072021484375, 1117.748046875, 1112.2459716796875, 1122.2330322265625], "type": "candlestick", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "yaxis": "y"}, {"line": {"color": "#1432F5", "width": 1}, "name": "ma5", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, 1092.2559999999999, 1098.77, 1102.388, 1105.84, 1101.268, 1092.476, 1080.814, 1068.766, 1062.0700000000002, 1060.3680000000002, 1059.28, 1059.104, 1052.992, 1042.5359999999998, 1026.682, 1013.2959999999999, 1001.4759999999999, 991.38, 980.752, 978.31, 979.722, 987.31, 1001.6679999999999, 1014.8239999999998, 1023.3779999999999, 1027.818, 1025.8439999999998, 1017.1239999999998, 1009.3879999999998, 1004.8359999999998, 1004.5459999999998, 997.9839999999997, 1000.0699999999997, 1006.4379999999998, 1014.2779999999998, 1020.4399999999998, 1039.494, 1054.658, 1061.8859999999997, 1066.8079999999998, 1071.4959999999999, 1072.988, 1071.7939999999999, 1075.356, 1077.154, 1080.3259999999998, 1078.144, 1080.616, 1080.062, 1083.662, 1081.696, 1083.498, 1081.808, 1083.712, 1087.414, 1091.622, 1094.944, 1104.6779999999999, 1110.762, 1109.248, 1109.204, 1108.354, 1099.5539999999999, 1091.446, 1084.318, 1080.584, 1076.706, 1071.146, 1065.654, 1059.25, 1048.8880000000001, 1038.97, 1036.6740000000002, 1034.1360000000002, 1030.2260000000003, 1026.5740000000003, 1027.17, 1028.0320000000002, 1029.2320000000002, 1034.1460000000002, 1042.3960000000002, 1047.8280000000002, 1052.16, 1054.644, 1054.6580000000001, 1055.662, 1057.6119999999999, 1058.274, 1059.176, 1061.9499999999998, 1066.802, 1075.2359999999999, 1080.4959999999999, 1092.93, 1103.9599999999998, 1111.098, 1113.968, 1119.166, 1117.742, 1118.9720000000002, 1120.7220000000002, 1123.45, 1125.902, 1129.0240000000001, 1130.286, 1125.952, 1124.448, 1124.074, 1123.286, 1122.818, 1125.732, 1122.9360000000001, 1119.8120000000001, 1118.8060000000003], "yaxis": "y"}, {"line": {"color": "#EB52F7", "width": 1}, "name": "ma10", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, 1092.366, 1089.7920000000001, 1085.577, 1083.9550000000002, 1080.818, 1075.8780000000002, 1069.959, 1060.8790000000001, 1052.303, 1043.525, 1036.288, 1030.29, 1022.1860000000001, 1011.6440000000001, 1002.4960000000001, 996.509, 994.393, 996.524, 997.788, 1000.8439999999999, 1003.77, 1006.5769999999999, 1009.396, 1012.106, 1014.107, 1016.182, 1011.914, 1008.597, 1007.913, 1009.557, 1012.493, 1018.739, 1027.364, 1034.162, 1040.543, 1045.9679999999998, 1056.2409999999998, 1063.2259999999999, 1068.6209999999999, 1071.981, 1075.9109999999998, 1075.5659999999998, 1076.2049999999997, 1077.7089999999998, 1080.408, 1081.011, 1080.821, 1081.212, 1081.887, 1085.538, 1086.659, 1089.2210000000002, 1093.2430000000002, 1097.237, 1098.3310000000001, 1100.4130000000002, 1101.6490000000001, 1102.1160000000002, 1101.104, 1096.7830000000001, 1094.8940000000002, 1092.5300000000004, 1085.3500000000004, 1078.5500000000004, 1071.7840000000003, 1064.7360000000006, 1057.8380000000004, 1053.9100000000005, 1049.8950000000004, 1044.7380000000005, 1037.7310000000004, 1033.0700000000004, 1032.3530000000005, 1031.6840000000004, 1032.1860000000004, 1034.4850000000004, 1037.4990000000005, 1040.0960000000002, 1041.9380000000003, 1044.4020000000003, 1049.0290000000002, 1052.7200000000003, 1055.2170000000003, 1056.9100000000003, 1058.3040000000003, 1061.2320000000004, 1066.4240000000004, 1069.3850000000004, 1076.0530000000006, 1082.9550000000006, 1088.9500000000005, 1094.6020000000005, 1099.8310000000008, 1105.336000000001, 1111.4660000000008, 1115.9100000000008, 1118.7090000000007, 1122.5340000000008, 1123.3830000000007, 1124.6290000000008, 1123.337000000001, 1123.949000000001, 1124.988000000001, 1126.1550000000009, 1126.5520000000008, 1125.8420000000008, 1123.6920000000007, 1121.943000000001, 1121.046000000001], "yaxis": "y"}, {"line": {"color": "#C0C0C0", "width": 1}, "name": "ma20", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1064.3269999999998, 1060.041, 1053.8815, 1047.7994999999999, 1041.657, 1036.1935, 1032.1760000000002, 1028.7015, 1025.0455, 1022.1845, 1020.029, 1018.4335, 1015.791, 1011.875, 1008.3015, 1006.3455, 1003.1535, 1002.5605, 1002.8505, 1005.2004999999999, 1008.1315, 1012.6579999999999, 1018.3799999999998, 1023.1339999999997, 1027.3249999999996, 1031.0749999999996, 1034.0774999999996, 1035.9114999999997, 1038.2669999999996, 1040.7689999999998, 1044.2019999999998, 1047.1524999999997, 1051.7844999999995, 1055.9354999999996, 1060.4754999999996, 1063.4894999999995, 1068.5309999999995, 1072.2189999999994, 1075.2539999999995, 1078.7594999999994, 1081.2849999999994, 1082.3934999999994, 1084.7239999999995, 1087.4729999999995, 1089.3694999999996, 1090.7119999999993, 1091.2349999999994, 1091.6639999999993, 1091.4954999999993, 1091.1604999999993, 1090.7764999999993, 1090.8754999999992, 1089.2964999999992, 1087.893499999999, 1085.057499999999, 1082.574499999999, 1079.7434999999991, 1078.012999999999, 1075.499499999999, 1070.7604999999992, 1066.312499999999, 1062.799999999999, 1058.851499999999, 1055.116999999999, 1051.9849999999992, 1049.610499999999, 1047.668499999999, 1047.002999999999, 1045.9164999999991, 1044.569999999999, 1043.379999999999, 1042.894999999999, 1043.784999999999, 1044.2969999999991, 1045.244999999999, 1047.8584999999991, 1051.9614999999992, 1054.7404999999992, 1058.995499999999, 1063.678499999999, 1068.9894999999992, 1073.6609999999991, 1077.5239999999992, 1081.1229999999991, 1084.884999999999, 1088.5709999999992, 1092.5664999999992, 1095.9594999999993, 1099.7179999999994, 1103.7919999999992, 1106.1434999999992, 1109.2754999999993, 1112.4094999999993, 1115.7454999999993, 1119.0089999999993, 1120.8759999999993, 1121.2004999999995, 1122.2384999999992, 1122.2144999999994], "yaxis": "y"}, {"line": {"color": "#882111", "width": 1}, "name": "ma30", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1044.1413333333333, 1042.2196666666666, 1039.0529999999999, 1035.9016666666664, 1032.4736666666665, 1029.523, 1025.4219999999998, 1021.9999999999999, 1019.3346666666665, 1017.9753333333333, 1017.5169999999999, 1018.5353333333333, 1019.6486666666666, 1019.304, 1019.0486666666666, 1019.553, 1020.8493333333333, 1022.7823333333333, 1024.7740000000001, 1027.4606666666666, 1030.7246666666665, 1033.6273333333334, 1037.655, 1041.3256666666666, 1045.0193333333334, 1047.7203333333334, 1049.6586666666667, 1051.0116666666668, 1052.807, 1055.692, 1058.3543333333334, 1061.1753333333334, 1065.604, 1069.7026666666668, 1073.094, 1075.7973333333334, 1079.5703333333333, 1082.1846666666668, 1083.8706666666667, 1084.7673333333335, 1085.8213333333333, 1085.7723333333333, 1084.9326666666668, 1084.4986666666668, 1083.5076666666669, 1082.0533333333333, 1080.1026666666667, 1079.0793333333334, 1077.6286666666667, 1075.6863333333333, 1073.0946666666666, 1071.607, 1070.3153333333332, 1069.157, 1067.4336666666666, 1066.5446666666664, 1065.6619999999998, 1065.3739999999998, 1064.3123333333333, 1061.9743333333333, 1060.5513333333333, 1059.44, 1057.64, 1055.7146666666667, 1054.0913333333333, 1053.4843333333333, 1053.9203333333332, 1054.4636666666665, 1055.962, 1057.365, 1058.5700000000002, 1060.1306666666667, 1062.467, 1064.6433333333334, 1067.3186666666668, 1070.5423333333333, 1074.2106666666666, 1077.3383333333334, 1080.458, 1083.9953333333333, 1087.1053333333334, 1090.4236666666666, 1093.3453333333334, 1096.1336666666666, 1098.7740000000001, 1100.9946666666667, 1102.9416666666668, 1104.620666666667, 1106.8273333333336], "yaxis": "y"}, {"line": {"color": "#5E8E28", "width": 1}, "name": "ma60", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1051.2478333333333, 1051.6975, 1052.3285, 1052.8021666666668, 1052.7838333333334, 1052.6601666666668, 1052.4961666666668, 1052.0923333333335, 1051.602666666667, 1051.3713333333335, 1051.669166666667, 1052.1538333333335, 1052.290666666667, 1051.9013333333337, 1051.278166666667, 1050.803166666667, 1050.4760000000003, 1050.9308333333336, 1051.2013333333337, 1051.5735000000004, 1051.9096666666671, 1052.617166666667, 1053.985166666667, 1055.2413333333338, 1056.2265000000004, 1057.1325000000004, 1057.6603333333337, 1058.1928333333337, 1058.559666666667, 1058.833166666667, 1059.4528333333337, 1060.307666666667, 1061.6220000000005, 1062.708666666667, 1063.5926666666671, 1064.6408333333338, 1066.7453333333337, 1068.324166666667, 1069.9163333333338, 1071.066166666667, 1072.195666666667, 1072.9515000000004, 1073.6998333333336, 1074.5710000000004, 1075.413166666667, 1076.2978333333335, 1077.156666666667, 1078.2088333333336, 1079.0433333333335, 1079.8408333333334, 1080.1000000000001, 1081.0153333333335, 1081.8303333333336, 1082.6453333333336, 1083.1038333333336, 1083.7696666666668, 1084.3018333333337, 1084.9973333333337, 1085.5698333333337], "yaxis": "y"}, {"marker": {"symbol": "triangle-down"}, "mode": "markers", "name": "峰", "type": "scatter", "x": ["2022-09-06", "2022-09-21", "2022-10-18", "2022-10-27", "2022-12-05"], "xaxis": "x", "y": [1121.736083984375, 1094.862060546875, 1053.422119140625, 1038.7811279296875, 1129.77001953125], "yaxis": "y"}, {"marker": {"symbol": "triangle-up"}, "mode": "markers", "name": "谷", "type": "scatter", "x": ["2022-09-19", "2022-10-10", "2022-10-24", "2022-10-28", "2022-12-29"], "xaxis": "x", "y": [1022.929931640625, 946.367919921875, 974.429931640625, 965.8239135742188, 1003.0869140625], "yaxis": "y"}, {"marker": {"color": ["#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136"]}, "showlegend": false, "type": "bar", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x2", "y": [226989220, 236729820, 237900350, 260565040, 201213880, 293380040, 202762210, 179904920, 191233000, 251875980, 256138710, 220909490, 217569700, 244210070, 167554260, 185700070, 218860010, 169503530, 178618370, 177557300, 149769520, 144396460, 138404680, 277828320, 208517190, 252159660, 225319460, 219485390, 181487540, 203298360, 168883830, 209963370, 255026110, 203196670, 250435530, 259622650, 207113380, 244164790, 283163650, 242695650, 330301590, 362218980, 240337660, 224186950, 350872790, 359563370, 266056050, 282066390, 247835770, 566705760, 365896890, 257560460, 298991630, 351255700, 233074010, 288032320, 242412630, 325431890, 523338470, 406875300, 278347760, 407131960, 366643860, 373338390, 327076390, 316952590, 245023320, 254669990, 179312620, 207137230, 195319830, 230006180, 196950780, 155263440, 172545450, 170384160, 144893910, 150435810, 163611590, 178334330, 181519070, 227687640, 186274290, 124547468, 102397242, 84239877, 236017620, 119786571, 22342508, 49775643, 57737987, 91940863, 18334682, 44698124, 253389540, 105484665, 85231478, 527081000, 533311190, 516175760, 116662517, 120588487, 237631200, 231850930, 228114460, 120600309, 188561260, 46104810, 193388150, 125722564, 253587950, 167138400, 91600823, 153580070, 121896192, 177208600, 132828124, 72171891], "yaxis": "y3"}, {"showlegend": false, "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x3", "y": [null, null, null, null, null, null, 79.7468354430378, 82.57294511091328, 57.37572111387301, 40.40639103501499, 29.413317914939086, 28.968217139478327, 54.227539139877976, 56.95046712094351, 44.60726060705147, 35.50425391669486, 25.581064853257367, 34.56978983460072, 27.62213938681536, 25.543393583178876, 22.36725337224261, 17.407235014330837, 26.219644190896208, 40.72115036198661, 48.32924753531781, 58.65307215446654, 63.89390034222551, 65.66318021274594, 61.99029875410149, 54.06653550279513, 44.09601736355228, 31.924438785637793, 39.92212947166057, 49.50477897815361, 58.00133350046944, 33.98427321807704, 46.682105004459544, 59.91558280187094, 66.31103616040382, 67.66777320529536, 73.75224769210874, 74.24527616522707, 66.1024011280651, 69.42825789420615, 70.7829858417031, 72.81879776861163, 59.33850575341311, 68.05342452085603, 66.79895153338327, 72.54286342308738, 49.36342154694091, 57.70946735100332, 56.978310332805016, 69.26477381020977, 52.83047114431639, 49.954088339977, 45.14666306023328, 58.974583822691685, 72.72887418086248, 59.8403976584911, 53.84344254709582, 66.81304875220465, 65.9376467121676, 57.75608832143123, 51.17136903482729, 42.42821605776269, 34.47594776921893, 37.0092168507978, 34.153198218309925, 44.630549917756646, 35.05722926323446, 24.304632087449555, 27.657139883822758, 23.60771424880288, 19.461627954744483, 16.141429631418145, 36.0701587739092, 37.78812932435741, 28.484790254163308, 24.16892125463229, 36.962432456597206, 52.64667421478675, 55.14251636684293, 57.436957639751085, 63.24407268272798, 58.626560809908504, 68.29587063054437, 59.87217562640539, 48.41221609201302, 59.997937882605925, 61.125394607154085, 65.99173575268924, 58.357162065261136, 57.987421204058286, 76.35148509613595, 83.67300506934635, 69.69536338832941, 81.06910213859213, 73.37142127558604, 74.57579453334463, 71.81751764537785, 72.54409376196416, 69.12575331014635, 73.56575106388539, 77.22267476151029, 78.94249590536681, 78.65844852653044, 79.70975586834345, 69.5280851956904, 33.939429563708906, 54.886283003815215, 59.88676190365304, 58.82746939847875, 56.17258315092477, 47.72274178386983, 34.4611532098827, 41.04778196544984, 54.80101520662085], "yaxis": "y5"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "汽车服务", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "volume", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.33999999999999997, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "rsi", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.12, "yanchor": "bottom", "yref": "paper"}], "height": 400, "hovermode": "x unified", "plot_bgcolor": "rgba(0,0,0,0)", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 0.94], "matches": "x3", "range": [-2, 117], "rangeslider": {"visible": false}, "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis2": {"anchor": "y3", "domain": [0, 0.94], "matches": "x3", "range": [-2, 117], "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis3": {"anchor": "y5", "domain": [0, 0.94], "minor": {"nticks": 5, "ticklen": 5, "ticks": "outside"}, "nticks": 11, "range": [-2, 117], "showgrid": false, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "ticklen": 10, "ticks": "outside", "type": "category"}, "yaxis": {"anchor": "x", "domain": [0.44, 1], "gridcolor": "rgba(0, 0, 0, 0.1)", "range": [905.0260467529297, 1197.650994873047], "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis2": {"anchor": "x", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis3": {"anchor": "x2", "domain": [0.22, 0.33999999999999997], "gridcolor": "rgba(0, 0, 0, 0.1)", "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis4": {"anchor": "x2", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y3", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis5": {"anchor": "x3", "domain": [0, 0.12], "gridcolor": "rgba(0, 0, 0, 0.1)", "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis6": {"anchor": "x3", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y5", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}}}, "text/html": ["<div>                            <div id=\"efa43400-a299-4399-934c-4c0767362e43\" class=\"plotly-graph-div\" style=\"height:400px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"efa43400-a299-4399-934c-4c0767362e43\")) {                    Plotly.newPlot(                        \"efa43400-a299-4399-934c-4c0767362e43\",                        [{\"close\":[1067.23,1082.51,1090.92,1111.16,1109.46,1099.8,1100.6,1108.18,1088.3,1065.5,1041.49,1040.36,1074.7,1079.79,1060.06,1040.61,1009.8,1022.42,1000.52,993.13,981.51,959.32,969.28,988.31,1000.19,1019.45,1031.11,1035.06,1031.08,1022.39,1009.58,987.51,996.38,1008.32,1020.94,976.77,997.94,1028.22,1047.52,1051.75,1072.04,1073.76,1064.36,1072.13,1075.19,1079.5,1067.79,1082.17,1081.12,1091.05,1068.59,1080.15,1079.4,1099.12,1081.22,1077.6,1071.7,1088.92,1117.63,1102.26,1094.21,1120.37,1119.34,1110.06,1102.04,1089.96,1076.37,1078.8,1074.42,1083.37,1070.57,1048.57,1051.34,1042.4,1031.56,1020.98,1037.09,1038.65,1022.85,1013.3,1023.96,1041.4,1044.65,1047.42,1054.55,1051.12,1063.06,1057.07,1047.49,1059.57,1060.87,1066.37,1061.58,1061.36,1083.83,1103.04,1092.67,1123.75,1116.51,1119.52,1117.39,1118.66,1116.63,1122.66,1128.27,1131.03,1130.92,1132.24,1128.97,1106.6,1123.51,1129.05,1128.3,1126.63,1121.17,1109.53,1113.43,1123.27],\"decreasing\":{\"fillcolor\":\"#3DAA70\",\"line\":{\"color\":\"#3DAA70\"}},\"high\":[1080.427001953125,1083.81103515625,1091.135986328125,1112.3380126953125,1114.14697265625,1117.490966796875,1103.2850341796875,1109.81201171875,1094.9639892578125,1092.85205078125,1065.7509765625,1050.6190185546875,1081.22900390625,1085.4639892578125,1078.821044921875,1065.72900390625,1032.56201171875,1023.5900268554688,1023.0070190429688,1012.8980102539062,994.489990234375,984.833984375,970.5170288085938,988.676025390625,1007.0499877929688,1026.4610595703125,1031.6209716796875,1044.0240478515625,1041.9549560546875,1034.623046875,1026.8900146484375,1021.4299926757812,1000.4910278320312,1016.4929809570312,1029.383056640625,1014.4110107421875,1004.3309936523438,1028.958984375,1052.9010009765625,1063.240966796875,1073.72802734375,1081.7509765625,1073.220947265625,1082.1920166015625,1083.2259521484375,1096.9840087890625,1081.166015625,1082.16796875,1092.4119873046875,1092.6219482421875,1089.4840087890625,1083.4029541015625,1090.5450439453125,1104.1529541015625,1094.7110595703125,1082.906982421875,1075.7679443359375,1090.8609619140625,1125.0849609375,1128.779052734375,1102.6280517578125,1120.3719482421875,1120.97802734375,1123.885986328125,1110.447998046875,1103.64697265625,1087.885009765625,1090.9560546875,1080.758056640625,1087.8929443359375,1083.550048828125,1075.22802734375,1055.385986328125,1052.373046875,1050.2490234375,1031.803955078125,1039.1600341796875,1039.383056640625,1033.2740478515625,1022.135009765625,1025.197998046875,1041.509033203125,1046.614013671875,1047.5069580078125,1057.0159912109375,1055.4599609375,1066.510009765625,1062.7449951171875,1050.053955078125,1062.5040283203125,1064.426025390625,1069.47998046875,1064.8280029296875,1062.6009521484375,1085.9429931640625,1104.697021484375,1093.844970703125,1124.08203125,1122.9560546875,1122.8929443359375,1121.60498046875,1122.4539794921875,1123.1910400390625,1123.9639892578125,1129.7320556640625,1132.5970458984375,1140.6199951171875,1133.718017578125,1134.3709716796875,1116.6409912109375,1124.18505859375,1136.0970458984375,1131.56396484375,1137.2030029296875,1127.072021484375,1124.364013671875,1119.5679931640625,1123.4930419921875],\"increasing\":{\"fillcolor\":\"rgba(255,255,255,0.9)\",\"line\":{\"color\":\"#FF4136\"}},\"line\":{\"width\":1},\"low\":[1066.4739990234375,1069.3709716796875,1077.68798828125,1092.4630126953125,1100.3380126953125,1098.93701171875,1087.9830322265625,1100.3199462890625,1080.5849609375,1059.657958984375,1041.488037109375,1032.3280029296875,1046.29296875,1051.4449462890625,1058.4219970703125,1036.0799560546875,1008.625,1003.739990234375,1000.5230102539062,988.3820190429688,981.2639770507812,955.7659912109375,952.6589965820312,952.7230224609375,981.8690185546875,1011.1719970703125,1011.2059936523438,1031.72802734375,1026.9560546875,1011.5490112304688,1008.7680053710938,983.8280029296875,972.875,996.5070190429688,1008.77197265625,975.2219848632812,977.7730102539062,1000.5759887695312,1034.2969970703125,1043.583984375,1050.7979736328125,1063.7449951171875,1059.23095703125,1064.5419921875,1064.85595703125,1078.791015625,1061.85400390625,1063.593994140625,1075.791015625,1075.010009765625,1067.199951171875,1063.6400146484375,1071.4090576171875,1079.0579833984375,1076.16796875,1069.718017578125,1055.5489501953125,1072.9169921875,1084.262939453125,1100.717041015625,1090.4150390625,1097.5009765625,1109.1309814453125,1106.31005859375,1099.623046875,1085.14794921875,1072.7650146484375,1075.58203125,1069.697998046875,1071.0269775390625,1067.06201171875,1045.39697265625,1041.1839599609375,1037.6529541015625,1029.1199951171875,1013.2249755859375,1020.302001953125,1025.4949951171875,1017.5280151367188,1012.4849853515625,1015.1179809570312,1019.4910278320312,1035.5870361328125,1041.032958984375,1042.239990234375,1047.2330322265625,1040.5140380859375,1053.3470458984375,1044.385986328125,1052.8599853515625,1056.0789794921875,1059.9520263671875,1060.3990478515625,1056.3060302734375,1070.489990234375,1094.9110107421875,1088.2449951171875,1105.06494140625,1113.2110595703125,1107.3809814453125,1113.364013671875,1114.697021484375,1115.0159912109375,1112.1199951171875,1119.032958984375,1124.970947265625,1126.739013671875,1130.240966796875,1126.751953125,1102.6949462890625,1102.637939453125,1122.35302734375,1125.782958984375,1120.991943359375,1114.623046875,1108.740966796875,1109.8270263671875,1116.6199951171875],\"name\":\"K\\u7ebf\",\"open\":[1077.7030029296875,1069.3709716796875,1080.0140380859375,1093.052978515625,1108.7110595703125,1107.3489990234375,1096.1700439453125,1101.5150146484375,1092.93798828125,1091.406982421875,1065.7509765625,1032.7249755859375,1046.3310546875,1066.3189697265625,1070.116943359375,1060.9560546875,1032.56201171875,1010.0189819335938,1020.677978515625,1005.791015625,991.6690063476562,982.2739868164062,961.2899780273438,971.5250244140625,983.5650024414062,1011.1719970703125,1011.2059936523438,1033.1719970703125,1029.9739990234375,1025.2320556640625,1021.8270263671875,1012.0800170898438,989.739013671875,997.2899780273438,1009.5960083007812,1014.4110107421875,977.7730102539062,1000.5759887695312,1034.509033203125,1043.583984375,1050.7979736328125,1067.56298828125,1072.06103515625,1066.02099609375,1071.323974609375,1091.31201171875,1078.5899658203125,1067.6529541015625,1081.373046875,1077.0050048828125,1089.4840087890625,1072.3389892578125,1080.7490234375,1079.0579833984375,1091.0689697265625,1077.2220458984375,1065.958984375,1072.9169921875,1087.3280029296875,1124.0870361328125,1101.77294921875,1098.2960205078125,1115.737060546875,1110.552001953125,1107.677978515625,1100.6009521484375,1087.885009765625,1075.58203125,1077.35400390625,1073.7349853515625,1078.5870361328125,1069.6080322265625,1045.3299560546875,1049.4530029296875,1047.5489501953125,1024.426025390625,1020.302001953125,1037.2530517578125,1033.1400146484375,1019.239013671875,1017.8579711914062,1020.9299926757812,1040.762939453125,1045.31005859375,1046.237060546875,1049.5040283203125,1052.251953125,1058.6710205078125,1044.385986328125,1052.8599853515625,1056.0789794921875,1069.47998046875,1062.156982421875,1058.740966796875,1073.26904296875,1096.7669677734375,1092.25,1105.06494140625,1122.85302734375,1115.614990234375,1115.031982421875,1121.1719970703125,1120.864013671875,1115.31396484375,1121.06005859375,1130.4639892578125,1139.0040283203125,1132.386962890625,1128.9520263671875,1107.802001953125,1108.60498046875,1123.4019775390625,1129.31494140625,1130.56005859375,1127.072021484375,1117.748046875,1112.2459716796875,1122.2330322265625],\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"type\":\"candlestick\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#1432F5\",\"width\":1},\"name\":\"ma5\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,1092.2559999999999,1098.77,1102.388,1105.84,1101.268,1092.476,1080.814,1068.766,1062.0700000000002,1060.3680000000002,1059.28,1059.104,1052.992,1042.5359999999998,1026.682,1013.2959999999999,1001.4759999999999,991.38,980.752,978.31,979.722,987.31,1001.6679999999999,1014.8239999999998,1023.3779999999999,1027.818,1025.8439999999998,1017.1239999999998,1009.3879999999998,1004.8359999999998,1004.5459999999998,997.9839999999997,1000.0699999999997,1006.4379999999998,1014.2779999999998,1020.4399999999998,1039.494,1054.658,1061.8859999999997,1066.8079999999998,1071.4959999999999,1072.988,1071.7939999999999,1075.356,1077.154,1080.3259999999998,1078.144,1080.616,1080.062,1083.662,1081.696,1083.498,1081.808,1083.712,1087.414,1091.622,1094.944,1104.6779999999999,1110.762,1109.248,1109.204,1108.354,1099.5539999999999,1091.446,1084.318,1080.584,1076.706,1071.146,1065.654,1059.25,1048.8880000000001,1038.97,1036.6740000000002,1034.1360000000002,1030.2260000000003,1026.5740000000003,1027.17,1028.0320000000002,1029.2320000000002,1034.1460000000002,1042.3960000000002,1047.8280000000002,1052.16,1054.644,1054.6580000000001,1055.662,1057.6119999999999,1058.274,1059.176,1061.9499999999998,1066.802,1075.2359999999999,1080.4959999999999,1092.93,1103.9599999999998,1111.098,1113.968,1119.166,1117.742,1118.9720000000002,1120.7220000000002,1123.45,1125.902,1129.0240000000001,1130.286,1125.952,1124.448,1124.074,1123.286,1122.818,1125.732,1122.9360000000001,1119.8120000000001,1118.8060000000003],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#EB52F7\",\"width\":1},\"name\":\"ma10\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,1092.366,1089.7920000000001,1085.577,1083.9550000000002,1080.818,1075.8780000000002,1069.959,1060.8790000000001,1052.303,1043.525,1036.288,1030.29,1022.1860000000001,1011.6440000000001,1002.4960000000001,996.509,994.393,996.524,997.788,1000.8439999999999,1003.77,1006.5769999999999,1009.396,1012.106,1014.107,1016.182,1011.914,1008.597,1007.913,1009.557,1012.493,1018.739,1027.364,1034.162,1040.543,1045.9679999999998,1056.2409999999998,1063.2259999999999,1068.6209999999999,1071.981,1075.9109999999998,1075.5659999999998,1076.2049999999997,1077.7089999999998,1080.408,1081.011,1080.821,1081.212,1081.887,1085.538,1086.659,1089.2210000000002,1093.2430000000002,1097.237,1098.3310000000001,1100.4130000000002,1101.6490000000001,1102.1160000000002,1101.104,1096.7830000000001,1094.8940000000002,1092.5300000000004,1085.3500000000004,1078.5500000000004,1071.7840000000003,1064.7360000000006,1057.8380000000004,1053.9100000000005,1049.8950000000004,1044.7380000000005,1037.7310000000004,1033.0700000000004,1032.3530000000005,1031.6840000000004,1032.1860000000004,1034.4850000000004,1037.4990000000005,1040.0960000000002,1041.9380000000003,1044.4020000000003,1049.0290000000002,1052.7200000000003,1055.2170000000003,1056.9100000000003,1058.3040000000003,1061.2320000000004,1066.4240000000004,1069.3850000000004,1076.0530000000006,1082.9550000000006,1088.9500000000005,1094.6020000000005,1099.8310000000008,1105.336000000001,1111.4660000000008,1115.9100000000008,1118.7090000000007,1122.5340000000008,1123.3830000000007,1124.6290000000008,1123.337000000001,1123.949000000001,1124.988000000001,1126.1550000000009,1126.5520000000008,1125.8420000000008,1123.6920000000007,1121.943000000001,1121.046000000001],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#C0C0C0\",\"width\":1},\"name\":\"ma20\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1064.3269999999998,1060.041,1053.8815,1047.7994999999999,1041.657,1036.1935,1032.1760000000002,1028.7015,1025.0455,1022.1845,1020.029,1018.4335,1015.791,1011.875,1008.3015,1006.3455,1003.1535,1002.5605,1002.8505,1005.2004999999999,1008.1315,1012.6579999999999,1018.3799999999998,1023.1339999999997,1027.3249999999996,1031.0749999999996,1034.0774999999996,1035.9114999999997,1038.2669999999996,1040.7689999999998,1044.2019999999998,1047.1524999999997,1051.7844999999995,1055.9354999999996,1060.4754999999996,1063.4894999999995,1068.5309999999995,1072.2189999999994,1075.2539999999995,1078.7594999999994,1081.2849999999994,1082.3934999999994,1084.7239999999995,1087.4729999999995,1089.3694999999996,1090.7119999999993,1091.2349999999994,1091.6639999999993,1091.4954999999993,1091.1604999999993,1090.7764999999993,1090.8754999999992,1089.2964999999992,1087.893499999999,1085.057499999999,1082.574499999999,1079.7434999999991,1078.012999999999,1075.499499999999,1070.7604999999992,1066.312499999999,1062.799999999999,1058.851499999999,1055.116999999999,1051.9849999999992,1049.610499999999,1047.668499999999,1047.002999999999,1045.9164999999991,1044.569999999999,1043.379999999999,1042.894999999999,1043.784999999999,1044.2969999999991,1045.244999999999,1047.8584999999991,1051.9614999999992,1054.7404999999992,1058.995499999999,1063.678499999999,1068.9894999999992,1073.6609999999991,1077.5239999999992,1081.1229999999991,1084.884999999999,1088.5709999999992,1092.5664999999992,1095.9594999999993,1099.7179999999994,1103.7919999999992,1106.1434999999992,1109.2754999999993,1112.4094999999993,1115.7454999999993,1119.0089999999993,1120.8759999999993,1121.2004999999995,1122.2384999999992,1122.2144999999994],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#882111\",\"width\":1},\"name\":\"ma30\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1044.1413333333333,1042.2196666666666,1039.0529999999999,1035.9016666666664,1032.4736666666665,1029.523,1025.4219999999998,1021.9999999999999,1019.3346666666665,1017.9753333333333,1017.5169999999999,1018.5353333333333,1019.6486666666666,1019.304,1019.0486666666666,1019.553,1020.8493333333333,1022.7823333333333,1024.7740000000001,1027.4606666666666,1030.7246666666665,1033.6273333333334,1037.655,1041.3256666666666,1045.0193333333334,1047.7203333333334,1049.6586666666667,1051.0116666666668,1052.807,1055.692,1058.3543333333334,1061.1753333333334,1065.604,1069.7026666666668,1073.094,1075.7973333333334,1079.5703333333333,1082.1846666666668,1083.8706666666667,1084.7673333333335,1085.8213333333333,1085.7723333333333,1084.9326666666668,1084.4986666666668,1083.5076666666669,1082.0533333333333,1080.1026666666667,1079.0793333333334,1077.6286666666667,1075.6863333333333,1073.0946666666666,1071.607,1070.3153333333332,1069.157,1067.4336666666666,1066.5446666666664,1065.6619999999998,1065.3739999999998,1064.3123333333333,1061.9743333333333,1060.5513333333333,1059.44,1057.64,1055.7146666666667,1054.0913333333333,1053.4843333333333,1053.9203333333332,1054.4636666666665,1055.962,1057.365,1058.5700000000002,1060.1306666666667,1062.467,1064.6433333333334,1067.3186666666668,1070.5423333333333,1074.2106666666666,1077.3383333333334,1080.458,1083.9953333333333,1087.1053333333334,1090.4236666666666,1093.3453333333334,1096.1336666666666,1098.7740000000001,1100.9946666666667,1102.9416666666668,1104.620666666667,1106.8273333333336],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#5E8E28\",\"width\":1},\"name\":\"ma60\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1051.2478333333333,1051.6975,1052.3285,1052.8021666666668,1052.7838333333334,1052.6601666666668,1052.4961666666668,1052.0923333333335,1051.602666666667,1051.3713333333335,1051.669166666667,1052.1538333333335,1052.290666666667,1051.9013333333337,1051.278166666667,1050.803166666667,1050.4760000000003,1050.9308333333336,1051.2013333333337,1051.5735000000004,1051.9096666666671,1052.617166666667,1053.985166666667,1055.2413333333338,1056.2265000000004,1057.1325000000004,1057.6603333333337,1058.1928333333337,1058.559666666667,1058.833166666667,1059.4528333333337,1060.307666666667,1061.6220000000005,1062.708666666667,1063.5926666666671,1064.6408333333338,1066.7453333333337,1068.324166666667,1069.9163333333338,1071.066166666667,1072.195666666667,1072.9515000000004,1073.6998333333336,1074.5710000000004,1075.413166666667,1076.2978333333335,1077.156666666667,1078.2088333333336,1079.0433333333335,1079.8408333333334,1080.1000000000001,1081.0153333333335,1081.8303333333336,1082.6453333333336,1083.1038333333336,1083.7696666666668,1084.3018333333337,1084.9973333333337,1085.5698333333337],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"symbol\":\"triangle-down\"},\"mode\":\"markers\",\"name\":\"\\u5cf0\",\"x\":[\"2022-09-06\",\"2022-09-21\",\"2022-10-18\",\"2022-10-27\",\"2022-12-05\"],\"y\":[1121.736083984375,1094.862060546875,1053.422119140625,1038.7811279296875,1129.77001953125],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"symbol\":\"triangle-up\"},\"mode\":\"markers\",\"name\":\"\\u8c37\",\"x\":[\"2022-09-19\",\"2022-10-10\",\"2022-10-24\",\"2022-10-28\",\"2022-12-29\"],\"y\":[1022.929931640625,946.367919921875,974.429931640625,965.8239135742188,1003.0869140625],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"color\":[\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\"]},\"showlegend\":false,\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[226989220.0,236729820.0,237900350.0,260565040.0,201213880.0,293380040.0,202762210.0,179904920.0,191233000.0,251875980.0,256138710.0,220909490.0,217569700.0,244210070.0,167554260.0,185700070.0,218860010.0,169503530.0,178618370.0,177557300.0,149769520.0,144396460.0,138404680.0,277828320.0,208517190.0,252159660.0,225319460.0,219485390.0,181487540.0,203298360.0,168883830.0,209963370.0,255026110.0,203196670.0,250435530.0,259622650.0,207113380.0,244164790.0,283163650.0,242695650.0,330301590.0,362218980.0,240337660.0,224186950.0,350872790.0,359563370.0,266056050.0,282066390.0,247835770.0,566705760.0,365896890.0,257560460.0,298991630.0,351255700.0,233074010.0,288032320.0,242412630.0,325431890.0,523338470.0,406875300.0,278347760.0,407131960.0,366643860.0,373338390.0,327076390.0,316952590.0,245023320.0,254669990.0,179312620.0,207137230.0,195319830.0,230006180.0,196950780.0,155263440.0,172545450.0,170384160.0,144893910.0,150435810.0,163611590.0,178334330.0,181519070.0,227687640.0,186274290.0,124547468.0,102397242.0,84239877.0,236017620.0,119786571.0,22342508.0,49775643.0,57737987.0,91940863.0,18334682.0,44698124.0,253389540.0,105484665.0,85231478.0,527081000.0,533311190.0,516175760.0,116662517.0,120588487.0,237631200.0,231850930.0,228114460.0,120600309.0,188561260.0,46104810.0,193388150.0,125722564.0,253587950.0,167138400.0,91600823.0,153580070.0,121896192.0,177208600.0,132828124.0,72171891.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y3\"},{\"showlegend\":false,\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,79.7468354430378,82.57294511091328,57.37572111387301,40.40639103501499,29.413317914939086,28.968217139478327,54.227539139877976,56.95046712094351,44.60726060705147,35.50425391669486,25.581064853257367,34.56978983460072,27.62213938681536,25.543393583178876,22.36725337224261,17.407235014330837,26.219644190896208,40.72115036198661,48.32924753531781,58.65307215446654,63.89390034222551,65.66318021274594,61.99029875410149,54.06653550279513,44.09601736355228,31.924438785637793,39.92212947166057,49.50477897815361,58.00133350046944,33.98427321807704,46.682105004459544,59.91558280187094,66.31103616040382,67.66777320529536,73.75224769210874,74.24527616522707,66.1024011280651,69.42825789420615,70.7829858417031,72.81879776861163,59.33850575341311,68.05342452085603,66.79895153338327,72.54286342308738,49.36342154694091,57.70946735100332,56.978310332805016,69.26477381020977,52.83047114431639,49.954088339977,45.14666306023328,58.974583822691685,72.72887418086248,59.8403976584911,53.84344254709582,66.81304875220465,65.9376467121676,57.75608832143123,51.17136903482729,42.42821605776269,34.47594776921893,37.0092168507978,34.153198218309925,44.630549917756646,35.05722926323446,24.304632087449555,27.657139883822758,23.60771424880288,19.461627954744483,16.141429631418145,36.0701587739092,37.78812932435741,28.484790254163308,24.16892125463229,36.962432456597206,52.64667421478675,55.14251636684293,57.436957639751085,63.24407268272798,58.626560809908504,68.29587063054437,59.87217562640539,48.41221609201302,59.997937882605925,61.125394607154085,65.99173575268924,58.357162065261136,57.987421204058286,76.35148509613595,83.67300506934635,69.69536338832941,81.06910213859213,73.37142127558604,74.57579453334463,71.81751764537785,72.54409376196416,69.12575331014635,73.56575106388539,77.22267476151029,78.94249590536681,78.65844852653044,79.70975586834345,69.5280851956904,33.939429563708906,54.886283003815215,59.88676190365304,58.82746939847875,56.17258315092477,47.72274178386983,34.4611532098827,41.04778196544984,54.80101520662085],\"type\":\"scatter\",\"xaxis\":\"x3\",\"yaxis\":\"y5\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-2,117],\"rangeslider\":{\"visible\":false}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.44,1.0],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\",\"range\":[905.0260467529297,1197.650994873047]},\"yaxis2\":{\"anchor\":\"x\",\"overlaying\":\"y\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis2\":{\"anchor\":\"y3\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-2,117]},\"yaxis3\":{\"anchor\":\"x2\",\"domain\":[0.22,0.33999999999999997],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis4\":{\"anchor\":\"x2\",\"overlaying\":\"y3\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis3\":{\"anchor\":\"y5\",\"domain\":[0.0,0.94],\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"minor\":{\"nticks\":5,\"ticklen\":5,\"ticks\":\"outside\"},\"nticks\":11,\"ticklen\":10,\"ticks\":\"outside\",\"type\":\"category\",\"range\":[-2,117]},\"yaxis5\":{\"anchor\":\"x3\",\"domain\":[0.0,0.12],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis6\":{\"anchor\":\"x3\",\"overlaying\":\"y5\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"annotations\":[{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"\\u6c7d\\u8f66\\u670d\\u52a1\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"volume\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.33999999999999997,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"rsi\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.12,\"yanchor\":\"bottom\",\"yref\":\"paper\"}],\"hovermode\":\"x unified\",\"plot_bgcolor\":\"rgba(0,0,0,0)\",\"height\":400},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('efa43400-a299-4399-934c-4c0767362e43');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cs.mark_peaks_and_valleys()\n", "cs.plot()"]}, {"cell_type": "markdown", "id": "1bdf1834-1b5f-4b71-9c03-0f5e0fa886a9", "metadata": {}, "source": ["## 回测\n", "\n", "您可以像omicron.strategy.sma.SMAStrategy策略一样，编写回测并执行。"]}, {"cell_type": "code", "execution_count": 112, "id": "7bc621e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[0;32mclass\u001b[0m \u001b[0mSMAStrategy\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mBaseStrategy\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0;32mdef\u001b[0m \u001b[0m__init__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msec\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mstr\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mn_short\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mint\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m5\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mn_long\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mint\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m10\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_sec\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msec\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_n_short\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mn_short\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_n_long\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mn_long\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mindicators\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0msuper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__init__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0;32masync\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mbefore_start\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mdate\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mbs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mend\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mbs\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m \u001b[0;32melse\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mlogger\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minfo\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"before_start, cash is %s\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcash\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdate\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0;32masync\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mbefore_trade\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdate\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mdatetime\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mlogger\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minfo\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0;34m\"before_trade, cash is %s, portfolio is %s\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcash\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpositions\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mdate\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0;32masync\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mafter_trade\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdate\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mdatetime\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mlogger\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minfo\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0;34m\"after_trade, cash is %s, portfolio is %s\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcash\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpositions\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mdate\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0;32masync\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mafter_stop\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mdate\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mbs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mend\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mbs\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m \u001b[0;32melse\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mlogger\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minfo\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0;34m\"after_stop, cash is %s, portfolio is %s\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcash\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpositions\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mdate\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0;32masync\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mpredict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mframe\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mFrame\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mframe_type\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mFrameType\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mi\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mint\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mbarss\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m    \u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0;32mif\u001b[0m \u001b[0mbarss\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0;32mraise\u001b[0m \u001b[0mValueError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"please specify `prefetch_stocks`\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mbars\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mUnion\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mBarsArray\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mbarss\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_sec\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0;32mif\u001b[0m \u001b[0mbars\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0;32mraise\u001b[0m \u001b[0mValueError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"{self._sec} not found in `prefetch_stocks`\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mma_short\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmean\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mbars\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"close\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_n_short\u001b[0m \u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0mma_long\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmean\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mbars\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"close\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_n_long\u001b[0m \u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0;32mif\u001b[0m \u001b[0mma_short\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0mma_long\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mind<PERSON>tors\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mframe\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcash\u001b[0m \u001b[0;34m>=\u001b[0m \u001b[0;36m100\u001b[0m \u001b[0;34m*\u001b[0m \u001b[0mbars\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"close\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m                \u001b[0;32mawait\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mbuy\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m                    \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_sec\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m                    \u001b[0mmoney\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcash\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m                    \u001b[0morder_time\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcombine_time\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mframe\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m14\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m55\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m                \u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m        \u001b[0;32melif\u001b[0m \u001b[0mma_short\u001b[0m \u001b[0;34m<\u001b[0m \u001b[0mma_long\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mind<PERSON>tors\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mframe\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m            \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mavailable_shares\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_sec\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mframe\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m                \u001b[0;32mawait\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msell\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m                    \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_sec\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpercent\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m1.0\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0morder_time\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcombine_time\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mframe\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m14\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m55\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\n", "\u001b[0;34m\u001b[0m                \u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n"]}], "source": ["from omicron.strategy.sma import SMAStrategy\n", "%psource SMAStrategy"]}, {"cell_type": "markdown", "id": "28909364", "metadata": {}, "source": ["要全面了解zillionare的策略框架，请进入《量化24课》系统学习。"]}, {"cell_type": "markdown", "id": "fb3b8869", "metadata": {}, "source": ["## 日历运算\n", "`omicron.tf`模块提供了非常实用的日历计算功能。这些代码多数都有示例。你可以用我们本教程介绍的方法（比如quick_look）来查看。"]}, {"cell_type": "code", "execution_count": 115, "id": "bb91d973", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ceiling: 求`moment`所在类型为`frame_type`周期的上界\n", "combine_time: 用`date`指定的日期与`hour`, `minute`, `second`等参数一起合成新的时间\n", "count_day_frames: calc trade days between start and end in close-to-close way.\n", "count_frames: 计算start与end之间有多少个周期为frame_type的frames\n", "count_month_frames: calc trade months between start and end date in close-to-close way\n", "count_quarter_frames: calc trade quarters between start and end date in close-to-close way\n", "count_week_frames: calc trade weeks between start and end in close-to-close way. Both start and\n", "count_year_frames: calc trade years between start and end date in close-to-close way\n", "date2int: 将日期转换为整数表示\n", "day_frames: ndarray(shape, dtype=float, buffer=None, offset=0,\n", "day_level_frames: Built-in mutable sequence.\n", "day_shift: 对指定日期进行前后移位操作\n", "first_min_frame: 获取指定日期类型为`frame_type`的`frame`。\n", "floor: 求`moment`在指定的`frame_type`中的下界\n", "frame_len: 返回以分钟为单位的frame长度。\n", "get_frame_scope: 对于给定的时间，取所在周的第一天和最后一天，所在月的第一天和最后一天\n", "get_frames: 取[start, end]间所有类型为frame_type的frames\n", "get_frames_by_count: 取以end为结束点,周期为frame_type的n个frame\n", "get_previous_trade_day: 获取上一个交易日\n", "get_ticks: 取月线、周线、日线及各分钟线对应的frame\n", "init: 初始化日历\n", "int2date: 将数字表示的日期转换成为日期格式\n", "int2time: 将整数表示的时间转换为`datetime`类型表示\n", "is_bar_closed: 判断`frame`所代表的bar是否已经收盘（结束）\n", "is_closing_call_auction_time: 判断`tm`指定的时间是否为收盘集合竞价时间\n", "is_open_time: 判断`tm`指定的时间是否处在交易时间段。\n", "is_opening_call_auction_time: 判断`tm`指定的时间是否为开盘集合竞价时间\n", "is_trade_day: 判断`dt`是否为交易日\n", "last_min_frame: 获取`day`日周期为`frame_type`的结束frame。\n", "minute_frames_floor: 对于分钟级的frame,返回它们与frame刻度向下对齐后的frame及日期进位。如果需要对齐到上一个交易\n", "minute_level_frames: Built-in mutable sequence.\n", "month_frames: ndarray(shape, dtype=float, buffer=None, offset=0,\n", "month_shift: 求`start`所在的月移位后的frame\n", "quarter_frames: ndarray(shape, dtype=float, buffer=None, offset=0,\n", "replace_date: 将`dtm`变量的日期更换为`dt`指定的日期\n", "resample_frames: 将从行情服务器获取的交易日历重采样，生成周帧和月线帧\n", "service_degrade: 当cache中不存在日历时，启用随omicron版本一起发行时自带的日历。\n", "shift: 将指定的moment移动N个`frame_type`位置。\n", "ticks: dict() -> new empty dictionary\n", "time2int: 将时间类型转换为整数类型\n", "week_frames: ndarray(shape, dtype=float, buffer=None, offset=0,\n", "week_shift: 对指定日期按周线帧进行前后移位操作\n", "year_frames: ndarray(shape, dtype=float, buffer=None, offset=0,\n"]}], "source": ["quick_look(omicron.tf)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 5}