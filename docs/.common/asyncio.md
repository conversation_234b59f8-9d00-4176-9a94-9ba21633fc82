---
title: 大富翁量化编程答疑课（二） - Asyncio
mainfont: WenQuanYi Micro Hei
puppeteer:
  format: "A4"
  landscape: false
  displayHeaderFooter: true
  headerTemplate: '
  <div style="height:50px; width: 100%;">
    <div style="margin:0 30px 10px 0;text-align:center;font-size:12px">
        大富翁量化金融实战课
    </div>
    <div style="border-top: 1px solid lightgrey;width:100%"/>
  </div/'
  footerTemplate: '<div style="position: relative; width: 100%; border-top: 1px solid black; margin: 0px 30px 30px; padding: 1px, 0px, 0px; font-size: 9px; font-family: Meiryo, Arial, sans-serif;">
  <div style="position: absolute; top: 15px; left: 0px; text-align: left;">
  <span class="title"></span></div>
  <div style="position: absolute; top: 15px; width: 100%; text-align: center;">
  <span class="pageNumber"></span> / <span class="totalPages"></span></div>
  <div style="position: absolute; top: 15px; right: 75px; text-align: right;">by 量化风云</div>
  <div style="position: absolute; top: 0px; right: 0px">
    <img style="opacity:0.8;width:48px" src="data:image/jpg;base64,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">
  </div>
  </div>'
  margin: {
    top: "65px",
    bottom: "70px",
    right: "30px",
    left: "30px"
  }
---


异步协程是编写高并发软件的基础之一。Python从3.3起，开始逐步引入构建异步协程的关键基础设施，直到3.8，asyncio库基本稳定，随后社区也跟进更新了第三方库，以便与asyncio兼容。

Asyncio是python 3以来，在性能上的重大更新。通过asyncio + 多进程，理论上python也可以达到非常高的I/O吞吐量。但是，python的异步协程与其它语言的异步协程仍然有本质区别：

1. python的异步协程只有在涉及到I/O时，才是并发的。而其它语言由于支持多线程，因此它们的异步协程，无论有没有涉及到I/O，都是真正并发的。
2. 在不涉及到I/O时，尽管我们可以把函数写成异步的形式，但这没有任何意义，反而增加了调度开销。

## 基本概念和基础设施

Python asyncio是构建在IO多路复用和coroutines之上的单线程并发。

我们先从一个最简单的例子看起：

```python
# 示例1
import asyncio
import time

async def say_after(delay, what):
    await asyncio.sleep(delay)
    print(what)

async def main():
    print(f"started at {time.strftime('%X')}")

    await say_after(1, 'hello')
    await say_after(2, 'world')

    print(f"finished at {time.strftime('%X')}")

asyncio.run(main())
```

这个简单的例子当中，隐藏了这样一些asyncio的关键设施：

1. async/await 关键字
2. 第5行，asyncio.sleep出让了cpu。
3. say_after是一个coroutine function，但asyncio.sleep却是一个Future
4. asyncio.run这一行创建了一个task，一个eventloop并立即执行了这个task。

!!! note
    如果我们查看asyncio.sleep的源码，会发现它本质上是一个Future:

    ```python
    future = loop.create_future()
    h = loop.call_later(delay,
                        futures._set_result_unless_cancelled,
                        future, result)
    try:
        return await future
    finally:
        h.cancel()
    ```

### Coroutine function/object

通过async，我们把一个function声明成为一个coroutine function。当它被调用时，就成为一个coroutine object。coroutine function是一种在运行中可以被中断和恢复的函数（但这种中断不是操作系统级的，不是thread调度）。这种中断和恢复是通过yield from来实现的，但从3.5以后，python使用了新的关键字await。

asyncio.run生成了一个eventloop，并且创建了一个Task。Task是Future的子类，在Task实例中，通常封装了一个coroutine object和一个future。然后这个task被提交到event loop中去执行。

### Future and Task

Task能实现异步，是依靠Future对象来实现的。一个Future对象具有PENDING, CANCELLED, FINISHED状态。一个future有set_result, add_done_callback和__await__等方法。

task读取封装的coroutine object中的future对象 -- 它是在最里层的coroutine中产生的，通过await被一路传递到最外层的coroutine对象。然后task通过add_done_callback，把自己绑定到这个future上。现在，这个task被提交给event loop执行，进行到等待状态。

一个I/O事件完成后 -- 这是由操作系统控制的，因此不占python进程的CPU，也不管python进程当前是否有空 -- 数据被拷贝到buffer，future.set_result被调用，从而task被唤醒。 task调用coroutine的send方法，这样就回到了最内层的coroutine的断点上。

所以，Future对象尽管一般我们看不见，但却是asyncio中的关键。但在3.8之前，asyncio库并没有很好地封装它，并且常常把future与task混在一起（比如之前调度coroutine执行的方法是ensure_future)，这些文档、教程还在流行，造成了开发者认知上的困难。

我们还要补充一点coroutine的知识，这样才能完全看懂上面的介绍。coroutine是一种特别的generator，所以它具有generator的属性。

### yield
从python 3.3起，出现了yield关键字。它与return一样能从函数返回值，但不一样的时，它并没有退出函数，函数的现场还被保留，下次执行时，代码将从yield的下一行起顺序执行。

```python
# 示例2
>>> def test():
...     val = yield 1
...     print(val)
...     yield 2
...     yield 3
...
>>> gen = test()
>>> next(gen)
1
>>> gen.send("abc")
2
>>> gen.throw(Exception())
```

当一个函数中出现yield时，该函数就转化成为一个generator。generator有send和throw这两个方法。当这两个方法被调用时，代码返回到最初被挂起的地方之后执行。当send被调用时，yield语句还被赋予了返回值。

### yield from
从Python 3.4起，增加了yield from表示法，为后面的asyncio库的实现铺平了道路：

```python
# 示例3
def bar():
    return 'bar'

def foo():
    yield bar()

def inner():
    inner_result = yield from foo()
    print('inner', inner_result)
    return 3

def outer():
    yield 1

    # 如果不加from，则会返回一个generator
    val = yield from inner()
    print('outer', val)
    yield 4

gen = outer()
print("first call: ", next(gen))
print("second call:", next(gen))
print("resume inner by 'send'")
gen.send('abc')
```

这将产生以下输出：

```
first call:  1
second call: bar
resume inner by 'send'
inner None
outer 3
```
上面的代码中，通过yield from串联起来的generator，就能够在收到send消息时，层层穿透址到最里层的generator，返回该函数的结果。

随后，从Python 3.5起，引入了await语法，它实际上就是yield from。如此一来，asyncio库就能把所有的事情组装起来。我们以一个异步的http请求为例：

1. 用户在coroutine function中发出一个异步请求
2. asyncio.run创建task, 该task含有一个corouting object （step 1)
3. asyncio.run将task提交给event loop
4. 下一个时间片，task被选中执行
5. coroutine function运行到网络io部分，此时会阻塞，返回一个future
6. future返回给task, task通过callback将自己绑定到这个future
7. socket就绪，在操作系统层面，数据已经获得。
8. future.set_result被调用，task被唤醒
9. task调用send，从而最里层的coroutine(step 5)恢复执行。接下来它的工作应该是从buffer中读取数据、构建python对象并返回

### Eventloop
我们可以把 Eventloop想像成一个white循环，它不停地从一个FIFO队列中检索任务并执行。因为它是一个死循环，所以，一个线程只能有一个loop。

## 容易犯错的概念
初学者比较容易犯错的是，认为只要函数被声明为async，就一定能并发执行。

实际上，async/await的主要作用，是让并行的代码能够串行执行，从而减轻我们编程时的认知负担（callback是一个噩梦）。要提升程序并发度，必须：

1. asyncio只能在有I/O的地方进行并发。在没有asyncio功能时，当程序执行到I/O时，CPU进入到内核执行，但python中的用户态代码仍处在等待中，因此浪费了它分配到的用户态时间。但它的本质与多线程是不一样的。多线程相当于Python进程多了一个向操作系统竞争CPU资源的单位，因此总体来看，进程分配的CPU时间是增加的。而使用asyncio时，并不会增加进程的CPU占时间。
2. 仅仅是把函数定义为async函数，并不会提升对cpu的利用。只有在函数调用了异步I/O函数，才会省下这部分时间。
3. 即使是调用了I/O函数，但该函数并不是异步的，也不会省时间。一个典型的例子是，在通过async def定义的函数中使用requests库来请求网络资源。
4. 即使满足上述条件,async/await也并不必然导致并发。并发的单位是task。如果所有代码通过async/await串联成一个task的话，仍然不会发生并发。请看下面的例子：

```python
# 示例4
import asyncio
import time

async def bar(i):
    await asyncio.sleep(i)

async def foo():
    t0 = time.time()
    
    for i in range(1, 4):
        await bar(i)

    print(f"time elapsed: {time.time() - t0:.0f}")

#asyncio.run(foo())

await foo()
```
三个任务最终用去了6秒，因此，它们仍然是顺序执行的。要使得它们能够并发执行，我们必须这样写：

```python
# 示例5
import asyncio
import time

async def bar(i):
    await asyncio.sleep(i)

async def foo():
    t0 = time.time()

    tasks = [bar(i) for i in range(1, 4)]

    await asyncio.gather(*tasks)

    print(f"time elapsed: {time.time() - t0:.0f}")

await foo()
```    
这一次，三个任务只花了三秒，这是最长的一个任务所占用的时间。这里我们使用了asyncio.gather，另一个与此类似的API是asyncio.wait。asyncio.wait更底层一些。

我们也可以使用create_task来创建并发任务：

```python
# 示例6
import asyncio
import time

async def bar(i):
    await asyncio.sleep(i)

async def foo():
    t0 = time.time()

    tasks = [asyncio.create_task(bar(i)) for i in range(1, 4)]

    await tasks[2]
    await tasks[1]
    await tasks[0]

    print(f"time elapsed: {time.time() - t0:.0f}")

await foo()
```
如果我们在示例5中的第13行处，用示例6的13~15来代替，则我们得到的仍然是串行执行的结果。这也说明了coroutine与task的不同。

从python 3.11起，我们还可以使用TaskGroup:

```python
# 示例7
import asyncio
import time

async def foo():
    t0 = time.time()

    async with asyncio.TaskGroup() as tg:
        for i in range(1, 4):
            tg.create_task(bar(i))

    print(f"time elapsed: {time.time() - t0:.0f}")

await foo()
```

## How to
### 在异步和传统代码之间进行切换？
django通过asgiref提供了sync-to-async/async-to-sync的转换。
### 单元测试
```python
# 示例8
import unittest

class MyTest(unittest.IsolatedAsyncioTestCase):
    async def asyncSetUp(self):
        pass

    async def test_001(self):
        mock = AsyncMock()
```

### 如何响应操作系统的退出信号？
在传统代码中，我们可以通过atexit来注册退出事件，这样在应用程序退出之前，我们得以进行一些清理工作，比如释放数据库连接。

如果我们的程序主要是一个event_loop，此时， atexit就完全失去了用处，因为，atexit是同步代码，如果我们的数据库连接是异步的，需要通过await close来调用，它是无法关闭的。

具体请参考[这篇文章](https://medium.com/@cziegler_99189/gracefully-shutting-down-async-multiprocesses-in-python-2223be384510)

## History

### Python 3.3
1. 增加了yield表达式
### Python 3.4
1. 增加了yield from 表达式
2. asyncio进入标准库
3. 通过coroutine装饰器和yield来实现
### Python 3.5
1. 引入async/await替换yield from语法，但仍不是关键字
### Python 3.6
1. 引入了async generator
2. ensure_future/run_coroutine_threadsafe

async generator的例子：

```python
# 示例9
async def bar(u: int = 10):
    """Yield powers of 2."""
    i = 0
    while i < u:
        yield 2 ** i
        i += 1
        await asyncio.sleep(0.1)

async def foo():
    # This does *not* introduce concurrent execution
    # It is meant to show syntax only
    g = [i async for i in foo()]
    return g
```

### Python 3.7
1. async/await成为关键字
2. asyncio库进行了较大的修改
3. asyncio.run/create_task

### Python 3.8
一批在3.7引入的语法稳定下来。

### Python 3.9
1. loop.run_in_executer被替换为asyncio.to_thread

### Python 3.11
增加了task_group

## Reference

要完全掌握asyncio，可以参照这个Outline:

![](https://images.jieyu.ai/images/2023/09/asyncio-outline.png)


<p>
[How asyncio actually works?](https://stackoverflow.com/a/51116910/13395693)
</p>
<p>
[Type Check](https://superfastpython.com/asyncio-coroutine-types/)
</p>
