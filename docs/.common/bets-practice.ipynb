{"cells": [{"cell_type": "markdown", "id": "4e9d0b98", "metadata": {}, "source": ["# 如何写出好的代码？\n", "\n", "即使是Quant Research， 写一手高质量的代码也是非常重要的。再好的思路，如果不能正确地实现，都是没有意义的。只有正确实现了、通过回测检验过了，才能算是真正做出来了策略。\n", "\n", "写一手高质量的代码的意义，对Quant developer来讲就更是自不待言了。这篇notebook笔记就介绍一些python best practice。\n", "\n", "## 依赖地狱和解决之道\n", "\n", "<img src=\"https://pic1.zhimg.com/80/v2-bac758cebcafd42a5cb9d8837b8072f4_720w.webp\" width=\"300px\" align=\"left\" style=\"margin:0 15px 0 0\">\n", "\n", "在量化研究中，我们很多功能会借助第三方包。第三方包也必然会依赖其它第三方包。如果有两个以上的包都依赖于某一个包，但是要求的版本不同，这就发生了依赖地狱。\n", "\n", "有一些量化研究员常常会把自己的研究环境搞坏，就是因为不断尝试新的技术、新的python库，而这些新的库，依赖的第三方版本和原来的发生冲突，强行覆盖之后，导致之前的python库无法使用造成的。\n", "\n", "解决依赖地狱问题，需要在不同的层面上进行解决。\n", "\n", "首先，我们一般会为将在进行的新的研究项目，创建一个新的虚拟环境。在这个虚拟环境中，由于只安装了必须的软件库，于是发生冲突的可能性就会小一些。\n", "\n", "其次，我们是要通过正确的依赖管理，尽可能地解决依赖冲突。这主要是通过poetry来实现的。\n", "\n", "### 虚拟环境\n", "\n", "Python中构建虚拟环境的方案有很多，作为量化研究员，我们只需要掌握conda就可以了。其它的方案还有virtualenv, venv和pipenv等。作为QD，需要掌握venv，这是python的一个标准库，用来创建新的虚拟环境，具有轻量、快速的特点。\n", "\n", "### 版本语义\n", "\n", "在软件开发领域中，我们常常对同一软件进行不断的修补和更新，每次更新，我们都保留大部分原有的代码和功能，修复一些漏洞，引入一些新的构件。\n", "\n", "有一个古老的思想实验，被称之为忒修斯船（The Ship of Theseus）问题，它描述的正是同样的场景：\n", "\n", "<img src=\"https://images.jieyu.ai/images/2024/01/thesus.jpg\" width=\"300px\" align=\"left\" align=\"left\" style=\"margin:0 15px 0 0\">\n", "\n", "忒修斯船问题最早出自公元一世纪普鲁塔克的记载。它描述的是一艘可以在海上航行几百年的船，只要一块木板腐烂了，它就会被替换掉，以此类推，直到所有的功能部件都不是最开始的那些了。现在的问题是，最后的这艘船是原来的那艘忒修斯之船呢，还是一艘完全不同的船？如果不是原来的船，那么从什么时候起它就不再是原来的船了？\n", "\n", "忒修斯船之问，发生在很多领域。比如像IBM这样的百年老店，CEO换了一任又一任，那它还是最初创建时的IBM吗？在软件开发领域中，我们更是常常遇到同样的问题。每遇到一个漏洞（bug），我们就更换一块\"木板\"。随着这种修补和替换越来越多，软件也必然出现忒修斯船之问：现在的软件还是不是当初的软件，如果不是，那它是在什么时候不再是原来的软件了呢？\n", "\n", "解决这个问题的核心是要实现版本的语义化。即，版本号分段为major.minor.patch三个段，如果是破坏性更新，则必须变更主版本号；如果是增加新的功能，但对之前保持兼容，则更新小的版本号（minor）。如果没有功能变更，只是修复了bug、安全性更新，则更新patch版本号。\n", "\n", "我们在使用第三方库时，就可以指明自动更新patch，或者允许自动更新到minor，而拒绝major级别的自动更新。\n", "\n", "<img src=\"https://images.jieyu.ai/images/2024/01/poetry-band.jpg\" width=\"300px\" align=\"right\" align=\"left\" style=\"margin:0 0 0 15px\">\n", "\n", "在第三方库遵循这个约定，进行了版本声明之后，我们就可以通过poetry来管理我们项目的依赖了：\n", "\n", "poetry是一个版本依赖管理工具。在使用poetry之前，你可能通过requirements.txt来管理过项目的依赖。它的问题是，并不会检查你加入的依赖，是否能与其它软件和睦相处，但Poetry可以。\n", "\n", "所以，今后我们开启新的策略研究时，我们应该先通过conda创建一个新的项目环境，然后通过poetry来增加项目依赖："]}, {"cell_type": "markdown", "id": "35932a9e", "metadata": {}, "source": ["```bash\n", "conda create -n new_project python=3.11\n", "poetry init\n", "poetry add pandas\n", "```\n"]}, {"cell_type": "markdown", "id": "63c7f876", "metadata": {}, "source": ["如此一来，在我们每次增加新的依赖时，poetry就会自动帮我们检查匹配的版本。如果找不到合适的版本，它就会报错，这样我们也有机会思考，是否有别的方案。\n", "\n", "## 书写漂亮的代码\n", "\n", "在写代码时，我们会有自己的风格。比如变量名如何使用大小写，单词之间如何分隔，如何使用空格和缩进等等。为了统一风格，2001年由Guido等人拟定了一份关于python代码风格的提案，被称为PEP8.\n", "\n", "<img src=\"https://images.jieyu.ai/images/2024/01/black-logo.png\" width=\"300px\" align=\"right\">\n", "\n", "PEP8的目的是为了提高python代码的可读性，使得python代码在不同的开发者之间保持一致的风格。PEP8的内容包括代码布局、命名规范。比如类要以大写字母开头、函数名以小写开头、单词之间用下划线分隔，等等。\n", "\n", "PEP8的内容非常多，在实践中，我们不需要专门去记忆它的规则，只要用对正确的代码格式化工具，最终呈现的代码就一定是符合PEP8标准的。或者lint工具会提示我们相关的错误，照着修改就够了。\n", "\n", "一般情况下，我们配置black作为代码格式化工具，就能保证风格符合PEP8的要求。\n", "\n", "推荐black的原因是,它基本上不接受定制。实际上，代码风格的定制几乎没有意义。一个人即使长得丑点，你强迫自己多看他几眼，就会发现其实也是能看的。所以black的成功就在这地方，它的motto是不妥协的格式化工具。很多事情就是这样，坚持自己的风格，宁可站着死，不愿跪着生，向死而生，反倒是机会。\n", "\n", "## 语法检查工具\n", "\n", "在运行代码之前，我们也有一些方法来检查编码中的错误。这类工具被称为lint工具。一般我们配置flake8, isort(用来给导入排序),mypy等工具。mypy是用来做类型检查的。\n", "\n", "## 类型提示\n", "\n", "类型提示(type hint)可以帮助IDE实现代码自动完成，也可以帮助我们尽早发现错误。这是从python 3.4起开始导入，到python 3.8框架完成的一个功能。\n", "\n", "我们知道，python是一门动态语言。它是有类型的，但这个类型检查只在运行时才能执行："]}, {"cell_type": "code", "execution_count": null, "id": "1e3467dd", "metadata": {}, "outputs": [], "source": [">>> one = 1\n", "... if False:\n", "...     one + \"two\" # 这一行不会执行，所以不会抛出TypeError\n", "... else:\n", "...     one + 2\n", "...\n", "3\n", "\n", ">>> one + \"two\"     # 运行到此处时，将进行类型检查，抛出TypeError\n", "TypeError: unsupported operand type(s) for +: 'int' and 'str'\n", "... one = \"one \"    # 变量可以通过赋值改变类型\n", "... one + \"two\"     # 现在类型检查没有问题\n", "one two"]}, {"cell_type": "markdown", "id": "3499ddf3", "metadata": {}, "source": ["上面的代码演示了在编码阶段，python和IDE不会提示任何类型错误。所以，第一段代码永远不会抛出错误。但如果我们有机会执行 `1 + \"two\"`的话，就会得到一个TypeError错误，提示我们不能把int和str相加。这就是运行时检查。\n", "\n", "如果我们按照type hint的要求来书写代码，就可以在早期发现一些错误，比如下面的例子："]}, {"cell_type": "code", "execution_count": 1, "id": "eb413838", "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["def foo(name: str) -> int:\n", "    score = 20\n", "    return score\n", "\n", "foo(10)"]}, {"cell_type": "markdown", "id": "1d7d048a", "metadata": {}, "source": ["在这段代码中，如果我们在IDE（比如vscode）中，把光标移动到foo(10)的位置，就会出现如下的错误提示：\n", "\n", "![50%](https://images.jieyu.ai/images/2023/01/20230114102202.png)\n", "\n", "这样我们就能在运行前，发现调用foo方法时，传入了错误的参数。\n", "\n", "下面的代码演示了多数常见的type hint用法："]}, {"cell_type": "code", "execution_count": 2, "id": "9fc92905", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'Any' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 9\u001b[0m\n\u001b[1;32m      5\u001b[0m child: \u001b[38;5;28mbool\u001b[39m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;66;03m# 如果一个变量可以是任何类型，也最好声明它为Any。\u001b[39;00m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# zen of python: explicit is better than implicit\u001b[39;00m\n\u001b[0;32m----> 9\u001b[0m dummy: \u001b[43mAny\u001b[49m \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m     10\u001b[0m dummy \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>o\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;66;03m# 如果一个变量可以是多种类型，可以使用Union\u001b[39;00m\n", "\u001b[0;31mNameError\u001b[0m: name 'Any' is not defined"]}], "source": ["# 声明变量的类型\n", "age: int = 1\n", "\n", "# 声明变量类型时，并非一定要初始化它\n", "child: bool\n", "\n", "# 如果一个变量可以是任何类型，也最好声明它为Any。\n", "# zen of python: explicit is better than implicit\n", "dummy: Any = 1\n", "dummy = \"hello\"\n", "\n", "# 如果一个变量可以是多种类型，可以使用Union\n", "dx: Union[int, str]\n", "# 从python 3.10起，也可以使用下面的语法\n", "dx: int | str\n", "\n", "# 如果一个变量可以为None,可以使用Optional\n", "dy: Optional[int]\n", "\n", "# 对python builtin类型，可以直接使用类型的名字，比如int, float, bool, str, bytes等。\n", "x: int = 1\n", "y: float = 1.0\n", "z: bytes = b\"test\"\n", "\n", "# 对collections类型，如果是python 3.9以上类型，仍然直接使用其名字：\n", "h: list[int] = [1]\n", "i: dict[str, int] = {\"a\": 1}\n", "j: tuple[int, str] = (1, \"a\")\n", "k: set[int] = {1}\n", "\n", "# 注意上面的list[], dict[]这样的表达方式。如果我们使用list()，则这将变成一个函数调用，而不是类型声明。\n", "\n", "# 但如果是python 3.8及以下版本，需要使用typing模块中的类型：\n", "from typing import List, Set, Dict, Tuple\n", "h: List[int] = [1]\n", "i: Dict[str, int] = {\"a\": 1}\n", "j: Tuple[int, str] = (1, \"a\")\n", "k: Set[int] = {1}\n", "\n", "# 如果你要写一些decorator，或者是公共库的作者，则可能会常用到下面这些类型\n", "from typing import Callable, Generator, Coroutine, Awaitable, AsyncIterable, AsyncIterator\n", "\n", "def foo(x:int)->str:\n", "    return str(x)\n", "\n", "# Callable语法中，第一个参数为函数的参数类型，因此它是一个列表，第二个参数为函数的返回值类型\n", "f: Callable[[int], str] = foo\n", "\n", "def bar() -> Generator[int, None, str]:\n", "    res = yield\n", "    while res:\n", "        res = yield round(res)\n", "    return 'OK'\n", "    \n", "g: Generator[int, None, str] = bar\n", "\n", "# 我们也可以将上述函数返回值仅仅声明为Iterator:\n", "def bar() -> Iterator[str]:\n", "    res = yield\n", "    while res:\n", "        res = yield round(res)\n", "    return 'OK'\n", "\n", "def op() -> Awaitable[str]:\n", "    if cond:\n", "        return spam(42)\n", "    else:\n", "        return asyncio.Future(...)\n", "\n", "h: Awaitable[str] = op()\n", "\n", "# 上述针对变量的类型定义，也一样可以用在函数的参数及返回值类型声明上，比如：\n", "def stringify(num: int) -> str:\n", "    return str(num)\n", "\n", "# 如果函数没有返回值，请声明为返回None\n", "def show(value: str) -> None:\n", "    print(value)\n", "\n", "# 你可以给原有类型起一个别名\n", "Url = str\n", "def retry(url: Url, retry_count: int) ->None:\n", "    pass\n"]}, {"cell_type": "markdown", "id": "94f688ad", "metadata": {}, "source": ["如果我们是在vscode中写代码，它自带一个pylance工具，将根据我们提供的type hint，来推断哪些代码在调用上，类型不对，这样可以尽早排除错误。\n", "\n", "如果我们习惯使用notebook进行策略研究，也可以在vscode中创建notebook，此时也可以得到pylance的帮助。\n", "\n", "!!! tip\n", "    vscode提供的Jupyter notebook除了在排版上可能不如原生的jupyter notebook之外，在很多方面都是胜出的。比如，除了语法检查，还有记住上一个编辑位置并实现跳转、支持单元格调试等等。\n", "\n", "\n", "## 单元测试: mock it till you make it!\n", "\n", "在策略研发时，我们要多用单元测试。单元测试有几个用处，第一，我们可以用它来学习第三方库的一些用法。第二，确保我们自己写的可复用的功能模块得到充分测试。\n", "\n", "单元测试并不复杂，主要难点在于如何将待测试的代码与系统中的其它部分隔离开来。这里我们一般使用mock对象。"]}, {"cell_type": "code", "execution_count": null, "id": "bb089c74", "metadata": {}, "outputs": [], "source": ["\n", "# 通过mock.patch，我们把cfg4py.core.dispatch对象替换成mock对象\n", "@mock.patch(\"cfg4py.core.dispatch\")\n", "def test_013_watch(self, mocked_handler):\n", "    # mock对象被调用后，我们可以通过call_count来检查它被调用多少次\n", "    self.assertTrue(mocked_handler.call_count > 3)\n", "\n", "# 我们还可以通过mock来模拟调用时发生异常的情形\n", "with mock.patch(\n", "    \"sys.exit\", lambda *args: early_jump(\"no files in folder\")\n", "):\n", "\n", "# 将对象的某个方法（这里是qfq）替换掉：\n", "with mock.patch.object(Stock, \"qfq\") as mocked_qfq:\n", "    mocked_qfq.assert_called()\n", "\n", "# 如果我们要修改系统时间，请用freezegun的freeze_time方法\n", "# 下面的语句执行后，再调用 datetime.datetime.now()就会\n", "# 得到 2022-02-09 10:33:00，而不是真实的系统时间\n", "@freeze_time(\"2022-02-09 10:33:00\")\n", "async def test_get_cached_bars_n(self):\n", "    pass\n", "\n", "# 如果我们的程序要接收用户输入，那么测试就无法自动化\n", "# 这种情况下，我们需要将builtins.input mock住，并且通过side_effect\n", "# 返回一个假的用户输入。\n", "with mock.path('builtins.input', side_effect=..):\n", "    pass"]}, {"cell_type": "markdown", "id": "b535331c", "metadata": {}, "source": ["## 更多\n", "更多关于Python编程最佳实践，请阅读[《Python能做大项目》](http://www.jieyu.ai/articles/python/best-practice-python/chap01/)。\n", "\n", "这本书除涵盖上述内容（当然讲得更详细）之外，还介绍了如何进行代码版本管理（即使用git），如何进行持续集成（CI/CD）和如何撰写和生成技术文档。"]}], "metadata": {"kernelspec": {"display_name": "course", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}