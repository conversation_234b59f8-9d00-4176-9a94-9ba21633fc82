{"cells": [{"cell_type": "markdown", "id": "76b224b8", "metadata": {}, "source": ["#  Asyncio\n", "\n", "异步协程是编写高并发软件的基础之一。Python从3.3起，开始逐步引入构建异步协程的关键基础设施，直到3.8，asyncio库基本稳定，随后社区也跟进更新了第三方库，以便与asyncio兼容。\n", "\n", "Asyncio是python 3以来，在性能上的重大更新。通过asyncio + 多进程，理论上python也可以达到非常高的I/O吞吐量。但是，python的异步协程与其它语言的异步协程仍然有本质区别：\n", "\n", "1. python的异步协程只有在涉及到I/O时，才是并发的。而其它语言由于支持多线程，因此它们的异步协程，无论有没有涉及到I/O，都是真正并发的。\n", "2. 在不涉及到I/O时，尽管我们可以把函数写成异步的形式，但这没有任何意义，反而增加了调度开销。\n", "\n", "## 基本概念和基础设施\n", "\n", "Python asyncio是构建在IO多路复用和coroutines之上的单线程并发。\n", "\n", "我们先从一个最简单的例子看起："]}, {"cell_type": "code", "execution_count": null, "id": "9ea07e28", "metadata": {}, "outputs": [], "source": ["# 示例1\n", "import asyncio\n", "import time\n", "\n", "async def say_after(delay, what):\n", "    await asyncio.sleep(delay)\n", "    print(what)\n", "\n", "async def main():\n", "    print(f\"started at {time.strftime('%X')}\")\n", "\n", "    await say_after(1, 'hello')\n", "    await say_after(2, 'world')\n", "\n", "    print(f\"finished at {time.strftime('%X')}\")\n", "\n", "asyncio.run(main())"]}, {"cell_type": "markdown", "id": "a22129ca", "metadata": {}, "source": ["这个简单的例子当中，隐藏了这样一些asyncio的关键设施：\n", "\n", "1. async/await 关键字\n", "2. 第5行，asyncio.sleep出让了cpu。\n", "3. say_after是一个coroutine function，但asyncio.sleep却是一个Future\n", "4. asyncio.run这一行创建了一个task，一个eventloop并立即执行了这个task。"]}, {"cell_type": "markdown", "id": "ce9b9c3b", "metadata": {}, "source": ["``` {note}\n", "    如果我们查看asyncio.sleep的源码，会发现它本质上是一个Future:\n", "    \n", "    ```python\n", "    future = loop.create_future()\n", "    h = loop.call_later(delay,\n", "                        futures._set_result_unless_cancelled,\n", "                        future, result)\n", "    try:\n", "        return await future\n", "    finally:\n", "        h.cancel()\n", "    ```\n", "\n", "```\n"]}, {"cell_type": "markdown", "id": "d79a2b62", "metadata": {}, "source": ["### Coroutine function/object\n", "\n", "通过async，我们把一个function声明成为一个coroutine function。当它被调用时，就成为一个coroutine object。coroutine function是一种在运行中可以被中断和恢复的函数（但这种中断不是操作系统级的，不是thread调度）。这种中断和恢复是通过yield from来实现的，但从3.5以后，python使用了新的关键字await。\n", "\n", "asyncio.run生成了一个eventloop，并且创建了一个Task。Task是Future的子类，在Task实例中，通常封装了一个coroutine object和一个future。然后这个task被提交到event loop中去执行。\n", "\n", "### Future and Task\n", "\n", "Task能实现异步，是依靠Future对象来实现的。一个Future对象具有PENDING, CANCELLED, FINISHED状态。一个future有set_result, add_done_callback和__await__等方法。\n", "\n", "task读取封装的coroutine object中的future对象 -- 它是在最里层的coroutine中产生的，通过await被一路传递到最外层的coroutine对象。然后task通过add_done_callback，把自己绑定到这个future上。现在，这个task被提交给event loop执行，进行到等待状态。\n", "\n", "一个I/O事件完成后 -- 这是由操作系统控制的，因此不占python进程的CPU，也不管python进程当前是否有空 -- 数据被拷贝到buffer，future.set_result被调用，从而task被唤醒。 task调用coroutine的send方法，这样就回到了最内层的coroutine的断点上。\n", "\n", "所以，Future对象尽管一般我们看不见，但却是asyncio中的关键。但在3.8之前，asyncio库并没有很好地封装它，并且常常把future与task混在一起（比如之前调度coroutine执行的方法是ensure_future)，这些文档、教程还在流行，造成了开发者认知上的困难。\n", "\n", "我们还要补充一点coroutine的知识，这样才能完全看懂上面的介绍。coroutine是一种特别的generator，所以它具有generator的属性。\n", "\n", "### yield\n", "从python 3.3起，出现了yield关键字。它与return一样能从函数返回值，但不一样的时，它并没有退出函数，函数的现场还被保留，下次执行时，代码将从yield的下一行起顺序执行。"]}, {"cell_type": "code", "execution_count": null, "id": "eb0aaa49", "metadata": {}, "outputs": [], "source": ["# 示例2\n", ">>> def test():\n", "...     val = yield 1\n", "...     print(val)\n", "...     yield 2\n", "...     yield 3\n", "...\n", ">>> gen = test()\n", ">>> next(gen)\n", "1\n", ">>> gen.send(\"abc\")\n", "2\n", ">>> gen.throw(Exception())"]}, {"cell_type": "markdown", "id": "ed4ebdbd", "metadata": {}, "source": ["当一个函数中出现yield时，该函数就转化成为一个generator。generator有send和throw这两个方法。当这两个方法被调用时，代码返回到最初被挂起的地方之后执行。当send被调用时，yield语句还被赋予了返回值。\n", "\n", "### yield from\n", "从Python 3.4起，增加了yield from表示法，为后面的asyncio库的实现铺平了道路："]}, {"cell_type": "code", "execution_count": null, "id": "9184a071", "metadata": {}, "outputs": [], "source": ["# 示例3\n", "def bar():\n", "    return 'bar'\n", "\n", "def foo():\n", "    yield bar()\n", "\n", "def inner():\n", "    inner_result = yield from foo()\n", "    print('inner', inner_result)\n", "    return 3\n", "\n", "def outer():\n", "    yield 1\n", "\n", "    # 如果不加from，则会返回一个generator\n", "    val = yield from inner()\n", "    print('outer', val)\n", "    yield 4\n", "\n", "gen = outer()\n", "print(\"first call: \", next(gen))\n", "print(\"second call:\", next(gen))\n", "print(\"resume inner by 'send'\")\n", "gen.send('abc')"]}, {"cell_type": "markdown", "id": "0c82fea4", "metadata": {}, "source": ["这将产生以下输出："]}, {"cell_type": "markdown", "id": "b8fba48d", "metadata": {}, "source": ["```\n", "first call:  1\n", "second call: bar\n", "resume inner by 'send'\n", "inner None\n", "outer 3\n", "```\n"]}, {"cell_type": "markdown", "id": "91360bb5", "metadata": {}, "source": ["上面的代码中，通过yield from串联起来的generator，就能够在收到send消息时，层层穿透址到最里层的generator，返回该函数的结果。\n", "\n", "随后，从Python 3.5起，引入了await语法，它实际上就是yield from。如此一来，asyncio库就能把所有的事情组装起来。我们以一个异步的http请求为例：\n", "\n", "1. 用户在coroutine function中发出一个异步请求\n", "2. asyncio.run创建task, 该task含有一个corouting object （step 1)\n", "3. asyncio.run将task提交给event loop\n", "4. 下一个时间片，task被选中执行\n", "5. coroutine function运行到网络io部分，此时会阻塞，返回一个future\n", "6. future返回给task, task通过callback将自己绑定到这个future\n", "7. socket就绪，在操作系统层面，数据已经获得。\n", "8. future.set_result被调用，task被唤醒\n", "9. task调用send，从而最里层的coroutine(step 5)恢复执行。接下来它的工作应该是从buffer中读取数据、构建python对象并返回\n", "\n", "### Eventloop\n", "我们可以把 Eventloop想像成一个white循环，它不停地从一个FIFO队列中检索任务并执行。因为它是一个死循环，所以，一个线程只能有一个loop。\n", "\n", "## 容易犯错的概念\n", "初学者比较容易犯错的是，认为只要函数被声明为async，就一定能并发执行。\n", "\n", "实际上，async/await的主要作用，是让并行的代码能够串行执行，从而减轻我们编程时的认知负担（callback是一个噩梦）。要提升程序并发度，必须：\n", "\n", "1. asyncio只能在有I/O的地方进行并发。在没有asyncio功能时，当程序执行到I/O时，CPU进入到内核执行，但python中的用户态代码仍处在等待中，因此浪费了它分配到的用户态时间。但它的本质与多线程是不一样的。多线程相当于Python进程多了一个向操作系统竞争CPU资源的单位，因此总体来看，进程分配的CPU时间是增加的。而使用asyncio时，并不会增加进程的CPU占时间。\n", "2. 仅仅是把函数定义为async函数，并不会提升对cpu的利用。只有在函数调用了异步I/O函数，才会省下这部分时间。\n", "3. 即使是调用了I/O函数，但该函数并不是异步的，也不会省时间。一个典型的例子是，在通过async def定义的函数中使用requests库来请求网络资源。\n", "4. 即使满足上述条件,async/await也并不必然导致并发。并发的单位是task。如果所有代码通过async/await串联成一个task的话，仍然不会发生并发。请看下面的例子："]}, {"cell_type": "code", "execution_count": null, "id": "53f979ef", "metadata": {}, "outputs": [], "source": ["# 示例4\n", "import asyncio\n", "import time\n", "\n", "async def bar(i):\n", "    await asyncio.sleep(i)\n", "\n", "async def foo():\n", "    t0 = time.time()\n", "    \n", "    for i in range(1, 4):\n", "        await bar(i)\n", "\n", "    print(f\"time elapsed: {time.time() - t0:.0f}\")\n", "\n", "#asyncio.run(foo())\n", "\n", "await foo()"]}, {"cell_type": "markdown", "id": "1ad9545a", "metadata": {}, "source": ["三个任务最终用去了6秒，因此，它们仍然是顺序执行的。要使得它们能够并发执行，我们必须这样写："]}, {"cell_type": "code", "execution_count": null, "id": "5025ed8f", "metadata": {}, "outputs": [], "source": ["# 示例5\n", "import asyncio\n", "import time\n", "\n", "async def bar(i):\n", "    await asyncio.sleep(i)\n", "\n", "async def foo():\n", "    t0 = time.time()\n", "\n", "    tasks = [bar(i) for i in range(1, 4)]\n", "\n", "    await asyncio.gather(*tasks)\n", "\n", "    print(f\"time elapsed: {time.time() - t0:.0f}\")\n", "\n", "await foo()\n", "```    \n", "这一次，三个任务只花了三秒，这是最长的一个任务所占用的时间。这里我们使用了asyncio.gather，另一个与此类似的API是asyncio.wait。asyncio.wait更底层一些。\n", "\n", "我们也可以使用create_task来创建并发任务：\n", "\n", "```python\n", "# 示例6\n", "import asyncio\n", "import time\n", "\n", "async def bar(i):\n", "    await asyncio.sleep(i)\n", "\n", "async def foo():\n", "    t0 = time.time()\n", "\n", "    tasks = [asyncio.create_task(bar(i)) for i in range(1, 4)]\n", "\n", "    await tasks[2]\n", "    await tasks[1]\n", "    await tasks[0]\n", "\n", "    print(f\"time elapsed: {time.time() - t0:.0f}\")\n", "\n", "await foo()"]}, {"cell_type": "markdown", "id": "f94709e9", "metadata": {}, "source": ["如果我们在示例5中的第13行处，用示例6的13~15来代替，则我们得到的仍然是串行执行的结果。这也说明了coroutine与task的不同。\n", "\n", "从python 3.11起，我们还可以使用TaskGroup:"]}, {"cell_type": "code", "execution_count": null, "id": "b06c7bbc", "metadata": {}, "outputs": [], "source": ["# 示例7\n", "import asyncio\n", "import time\n", "\n", "async def foo():\n", "    t0 = time.time()\n", "\n", "    async with asyncio.TaskGroup() as tg:\n", "        for i in range(1, 4):\n", "            tg.create_task(bar(i))\n", "\n", "    print(f\"time elapsed: {time.time() - t0:.0f}\")\n", "\n", "await foo()"]}, {"cell_type": "markdown", "id": "94926407", "metadata": {}, "source": ["## How to\n", "### 在异步和传统代码之间进行切换？\n", "django通过asgiref提供了sync-to-async/async-to-sync的转换。\n", "### 单元测试"]}, {"cell_type": "code", "execution_count": null, "id": "731ca4a8", "metadata": {}, "outputs": [], "source": ["# 示例8\n", "import unittest\n", "\n", "class MyTest(unittest.IsolatedAsyncioTestCase):\n", "    async def asyncSetUp(self):\n", "        pass\n", "\n", "    async def test_001(self):\n", "        mock = AsyncMock()"]}, {"cell_type": "markdown", "id": "2c3dff99", "metadata": {}, "source": ["### 如何响应操作系统的退出信号？\n", "在传统代码中，我们可以通过atexit来注册退出事件，这样在应用程序退出之前，我们得以进行一些清理工作，比如释放数据库连接。\n", "\n", "如果我们的程序主要是一个event_loop，此时， atexit就完全失去了用处，因为，atexit是同步代码，如果我们的数据库连接是异步的，需要通过await close来调用，它是无法关闭的。\n", "\n", "具体请参考[这篇文章](https://medium.com/@cziegler_99189/gracefully-shutting-down-async-multiprocesses-in-python-2223be384510)\n", "\n", "## History\n", "\n", "### Python 3.3\n", "1. 增加了yield表达式\n", "### Python 3.4\n", "1. 增加了yield from 表达式\n", "2. async<PERSON>进入标准库\n", "3. 通过coroutine装饰器和yield来实现\n", "### Python 3.5\n", "1. 引入async/await替换yield from语法，但仍不是关键字\n", "### Python 3.6\n", "1. 引入了async generator\n", "2. ensure_future/run_coroutine_threadsafe\n", "\n", "async generator的例子："]}, {"cell_type": "code", "execution_count": null, "id": "b3e12943", "metadata": {}, "outputs": [], "source": ["# 示例9\n", "async def bar(u: int = 10):\n", "    \"\"\"Yield powers of 2.\"\"\"\n", "    i = 0\n", "    while i < u:\n", "        yield 2 ** i\n", "        i += 1\n", "        await asyncio.sleep(0.1)\n", "\n", "async def foo():\n", "    # This does *not* introduce concurrent execution\n", "    # It is meant to show syntax only\n", "    g = [i async for i in foo()]\n", "    return g"]}, {"cell_type": "markdown", "id": "280892a8", "metadata": {}, "source": ["### Python 3.7\n", "1. async/await成为关键字\n", "2. asyncio库进行了较大的修改\n", "3. asyncio.run/create_task\n", "\n", "### Python 3.8\n", "一批在3.7引入的语法稳定下来。\n", "\n", "### Python 3.9\n", "1. loop.run_in_executer被替换为asyncio.to_thread\n", "\n", "### Python 3.11\n", "增加了task_group\n", "\n", "## Reference\n", "\n", "要完全掌握asyncio，可以参照这个Outline:"]}, {"cell_type": "markdown", "id": "6db3e7c5", "metadata": {}, "source": ["```{figure} https://images.jieyu.ai/images/2023/09/asyncio-outline.png\n", ":width: 100%\n", ":align: center\n", "```\n"]}, {"cell_type": "markdown", "id": "a48d828b", "metadata": {}, "source": ["[How as<PERSON><PERSON> actually works?](https://stackoverflow.com/a/51116910/13395693)\n", "[Type Check](https://superfastpython.com/asyncio-coroutine-types/)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}