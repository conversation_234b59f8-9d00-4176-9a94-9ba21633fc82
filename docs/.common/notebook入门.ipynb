{"cells": [{"cell_type": "markdown", "id": "9fba2b7a", "metadata": {}, "source": ["# NOTEBOOK 入门\n", "\n", "Jupyter Notebook 是一种**交互式编程环境**，或称**探索式编程环境**。它由网页和服务器组成。\n", "\n", "网页由一个个单元格组成，在单元格里编写代码，或者描述性文字。当我们执行代码单元格时，单元格的代码会被发送到后台服务器运行，输出结果再传送回浏览器，显示在对应的代码单元格下方。\n", "\n", "这种方式特别适合于数据科学家。作为从事数据分析的人，他们往往需要先加载一部分数据，绘图看看数据分布特性，再运行一个模型，得到结果并可视化，再决定下一步怎么做。\n", "\n", "这种风格就被称为探索式编程。它区别于工程化编程的地方是，工程化编程的方向和目标都十分明确，基本上不存在推倒重来的情况。而在探索式编程中，一个模型不能用，推倒重来是很正常的事，并不意味着失败。\n", "\n", "我们的环境是基于 Jupyter Lab 构建的。它前身是 jupyter notebook server，不过现在官方已决定弃用 notebook 了。注意，notebook 即可能指代 notebook server，也可能指代一个个具体的，后缀为.ipynb 的文件。当它指代 notebook server 时，它是被官方弃用的；但 jupyter lab 使用的文件格式仍然是 notebook，即后缀为.ipynb 的这些文件。\n", "\n", "我们使用 Jupyter lab 来作为实验环境，是因为一方面我们需要给示例代码配上大量的说明性文字，方便大家理解；其次，我们对策略的探索也非常符合探索式编程的模型。\n", "\n", "## 1. Notebook 语法\n", "\n", "Notebook 由一个个单元格组成。一共有三种格式的单元格，Markdown, Python 和 Raw 格式的单元格。您正在读到的这段文字，就处在一个 Markdown 单元格下。在 Markdown 单元格中，我们可以按照 Markdown 语法来对文字进行简单的格式化。比如，这个单元格的标题，是由两个“#”引起的，在 Markdown 语法中，这意味着二级标题。"]}, {"cell_type": "markdown", "id": "2785a709", "metadata": {}, "source": ["``` {note}\n", "在我们的系统里，我们还安装了 myst 扩展，以更加生动、灵活地传递信息。您正在读到的这段话，它的渲染格式被称之为 admonition，就是通过 myst 来渲染的。\n", "\n", "在课程中，我们还使用 myst 来实现精美的图文混排。少数地方也可能直接使用 html 语法片段。\n", "\n", "如果您在我们课程中，觉得某些排版效果很好，可以双击该单元格，了解它的语法。\n", "\n", "```\n"]}, {"cell_type": "markdown", "id": "bcee141c", "metadata": {}, "source": ["Python 单元格是我们书写代码的地方。Notebook 的一大优势是可以分段执行，即在一个 notebook 中，如果存在多个 Python 单元格，这些单元格都是可以分别运行（即使它们之间存在逻辑上的依赖，必须顺序运行，也仍然是分别运行，即我们可以一次只运行一个单元格并查看输出结果）。这种方式，给我们学习和探索带来很大的方便。\n", "\n", "Raw 格式的单元格一般我们不会用到，这里就不介绍了。\n", "\n", "## 2. Notebook 界面\n", "\n", "<div id='fig-None-1' style='width:80%;margin:1em auto;text-align:center'><img src='https://images.jieyu.ai/images/2024/01/notebook-toolbar.jpg' ><span style='color: #808080;font-size:0.8em'>图None-1 75%</span></div>\n", "\n", "在页面上方我们会看到这样一个工具条。\n", "\n", "序号 1 指向的是存盘按钮。点击后，我们对 notebook 作的修改就会保存。"]}, {"cell_type": "markdown", "id": "4598b9cd", "metadata": {}, "source": ["``` {warning}\n", "在本课程中，我们提供的 notebook 是只读的，因此您看到的这个按钮将是灰色的，也就是无法保存。课程学员一般可以在自己的工作区新建 notebook 并保存。如果您只有预览权限，也可以修改、运行这些 notebook，但无法保存。\n", "\n", "```\n"]}, {"cell_type": "markdown", "id": "c95d921e", "metadata": {}, "source": ["当我们选中一个代码单元格时，就可以点击序号 2 指示的按钮以运行它。当单元格正在运行时，它左侧的状态将显示为 [*]，同时序号 4 所指标的 notebook 状态将显示为忙。\n", "\n", "<img src=\"https://images.jieyu.ai/images/2024/01/notebook-status-idle.jpg\" width=\"200px\" align=\"left\" style=\"margin:10px\">\n", "\n", "如果当前没有单元格正在执行，则会显示为左图所示的状态。\n", "\n", "如果一个单元格执行时间过长，我们也可以点击序号 3 指向的按钮，以中断执行。\n", "\n", "<img src=\"https://images.jieyu.ai/images/2024/01/notebook-status-disconnected.jpg\" width=\"200px\" align=\"left\" style=\"margin:10px\"> \n", "\n", "序号 4 显示了当前 notebook 中的状态。左图的状态表明当前没有连接到后台服务器。您需要选择一个 kernle 并连接。\n", "\n", "## 3. 代码提示"]}, {"cell_type": "code", "execution_count": null, "id": "75238d16", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "np.random.\n"]}, {"cell_type": "markdown", "id": "48456e02", "metadata": {}, "source": ["在上述单元格中，将光标移动到. 之后，再按一次 tab 键，这将给您一些代码提示：\n", "\n", "<div style=\"width:100%;text-align:center\">\n", "<img src=\"https://images.jieyu.ai/images/2024/01/notebook-tab-hint.jpg\" width=\"350px\" align=\"center\">\n", "</div>\n", "\n", "## 4. 文档帮助\n", "\n", "<div style=\"position:relative;float:left\">\n", "<img src=\"https://images.jieyu.ai/images/2024/01/notebook-help-doc.jpg\" align=\"left\" style=\"width: 250px;margin:0 20px 0 0\">\n", "</div>\n", "\n", "有两种方式可以得到文档帮助。一种是在我们要查询的函数之后，输入'?'并运行。另一种方式类似于代码提示，不过要按两次 tab（有可能要加入 shift 键，请查询 notebook 快捷键帮助）。最终我们将得到左图所示的文档。\n", "\n", "## 5. 安装和卸载 Python 库\n", "\n", "在 Notebook 环境下，您也可以自行安装和卸载 Python 库。方法是，新建一个单元格，再输入安装命令，记得要以！开头。\n", "\n", "如果是要卸载某个库，则一定要注意，要使用免提示模式，即在命令中传入`-y`参数。我们以卸载 cfg4py 为例"]}, {"cell_type": "code", "execution_count": null, "id": "24ed70e3", "metadata": {}, "outputs": [], "source": ["! pip uninstall -y cfg4py"]}, {"cell_type": "markdown", "id": "6a5e20c2", "metadata": {}, "source": ["如果缺少`-y`参数，这条命令会挂起。如果出现以下警告："]}, {"cell_type": "markdown", "id": "3d8cd362", "metadata": {}, "source": ["``` {warning}\n", "WARNING: Skipping cfg4py as it is not installed.\n", "\n", "```\n"]}, {"cell_type": "markdown", "id": "51ad4da8", "metadata": {}, "source": ["说明该库没有安装。我们可以使用如下命令安装："]}, {"cell_type": "code", "execution_count": null, "id": "ae3f5ef1", "metadata": {}, "outputs": [], "source": ["! pip install cfg4py"]}, {"cell_type": "markdown", "id": "b3fe1715", "metadata": {}, "source": ["``` {warning}\n", "在执行安装和卸载命令后，需要重启 notebook，才能使当前的 notebook 完全得到更新。\n", "\n", "```\n"]}, {"cell_type": "markdown", "id": "f164bef9", "metadata": {}, "source": ["## 6. 重启 notebook\n", "\n", "有时候您可能需要重启 notebook。通过菜单栏 Kernel，再选择需要的操作：\n", "\n", "<div style=\"width:100%;text-align:center\">\n", "<img src=\"https://images.jieyu.ai/images/2024/01/notebook-restart.jpg\" style=\"width:300px\">\n", "</div>\n", "\n", "## 7. 导航和跳转\n", "\n", "有时候我们会遇到这样的情况，在底部的某个单元格工作时，要跳到头部的某个单元格进行修改并运行，然后再回到底部单元格继续工作。这在 IDE 中非常简单，往往会有快捷键帮助我们跳转。但 Jupyter Notebook 基于浏览器工作，就无法提供这样的功能了。\n", "\n", "有一个补救方案。如果文档很长，您应该在其中插入 markdown 单元格进行注释和说明，并且使用多级标题。这样，我们就可以利用它的导航功能来进行跳转：\n", "\n", "<div id='fig-None-2' style='width:80%;margin:1em auto;text-align:center'><img src='https://images.jieyu.ai/images/2024/01/notebook-toc-nav.jpg' ><span style='color: #808080;font-size:0.8em'>图None-2 75%</span></div>\n", "\n", "我们可以点击左侧面板中的任意一个标题，快速跳转到对应的节中。\n", "\n", "\n", "---\n", "\n", "***版权声明<br>本课程全部文字、图片、代码、习题等所有材料，除声明引用外，版权归公众号QuanTide所有。所有草稿版本均通过第三方git服务进行管理，作为拥有版权的证明。未经作者书面授权，请勿引用和传播。***"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}