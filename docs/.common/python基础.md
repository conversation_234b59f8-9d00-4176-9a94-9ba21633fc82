# Python基础知识

在学习量化交易课程之前，您需要预先掌握以下Python语法。

## 注释

注释是程序员在程序源代码中所做的注释。为了帮助自己、或者其它人读懂程序代码，我们往往要在关键之处，加上注释。

!!! note
    注释并不会参与执行。它纯粹是为人阅读使用的。

在Python中，注释由字符#引起，比如：

```python
# 这是一行注释
print("hello stranger!") # 注释也可以出现在行末
```

除此之外，Python还有一种特殊的注释，称之docstring。它常常出现于Python模块的头部、类声明的头部或者函数的头部。

```python
# foo.py
"""这是foo模块的说明文档(docstring)。 Foo模块的主要作用是..."""

class Foo：
    """Foo这个类是非常特别，他能做任何事！
    
        好了，不开玩笑。这一段，是Foo的帮助文档。
    """
    def say_hello(self, name: str)->None:
        """向某人打招呼！
        
        Args:
            name: 跟谁打招呼？
        Returns:
            本函数无返回值
        """
        print(f"Hello {name}!")
```

## 变量

变量为编程中的值提供名称。如果您想保存某个值以供以后或重复使用，您可以为该值指定一个名称，并将内容存储在变量中。编程中的变量与代数中的变量的工作方式基本相似，但在 Python 中它们可以采用各种不同的数据类型。

在编程语言中，变量都有所谓的类型。类型暗含了允许哪些操作的意义。常见的Python类型有字符串(str)， 布尔型 (bool)，浮点数（float），整数(int)，类（class），时间日期(datetime)等。

编程中的 int 与数学中的相同，是一个整数，小数点后没有任何值。我们这样声明一个整数变量：

```python
my_integer = 50
print(my_integer, type(my_integer))
```

这里的等号(=)并不是表示判断是否相等的意思，而是让my_integer这个变量取值为50的意思，我们一般称其为赋值。

接下来的type(my_integer)显示了变量的类别。这是我们进行调试查错的方法之一。

!!! warning
    在Python中，变量名是区分大小写的。因此，下面的语句在执行时会出错:

    ```python
        one = 1
        print(One)
    ```
    在print语句中，我们使用了大写的One，此变量是没有定义的。

在python中，实数（或者带小数的数）被称为浮点数(float)。下面的语句都是正确的声明语句：

```python
score = 1.0
price = 1.
factor = .1 # 即0.1
```

出现其它浮点数的表示方法是为了书写简便。此外，我们对于数字，为了便于辨识，还有下面的分隔符法：

```python
principal = 1_0000_0000 # 这是一个小目标！
```

它的关键是在数字之间，使用下划线来连接。

浮点数有所谓的精度问题。这个概念对于初学者很难理解。我们只要知道由于精度问题，在python中：

```
# 3个0.3相加，结果并不等于0.9
0.3 + 0.3 + 0.3 != 0.9
```
并且如果我们用Python的方法来对小数进行四舍五入的话，0.3/2舍入到一位小数，并不是我们想像中的0.2,而是0.1！

!!! tip
    显然，在量化交易中，我们因为要跟钱打交道，所以，不可避免地涉及到小数，并且也要涉及到四舍五入。如何正确使用小数，在我们的量化课程中有介绍。

字符串类型我们在前面已见过了一些示例，我们这里进行一些补充和总结：

```python
name = "Aaron" # 包含在一对双引号内的字面量就是字符串
score = "5.0"  # 此时5.0是字符串，而不是浮点数
title = 'CEO'  # 字符串也可以使用单引号来声明
car = """Porsche Cayenne, A verty beautiful one""" #也可以使用强大的三引号来声明。它的作用是允许跨行字符
mixed = "The name of the car is 'Porsche'" # 通过嵌套来声明带引号的字符串
```

我们以上见过的数据类型，都可以相互间进行类型转换。

```python
int(5.0)

# 但我们不能将字符串的5.0转换为整数5，下面的语句是错误的
int("5.0")

# 但我们可以将它转换为浮点数
float("5.0")

# 我们也可以通过str方法，将数字转换成为字符串

str(5.0)
str(5)
```

计算机的威力仅靠数值运算是不够的。它必须有强大的逻辑运算能力，才能进行编程。布尔型(bool)变量是逻辑运算的基础。比如，如果我们希望计算机在温度大于26度时说热，在小于20度时说冷，我们需要这样写语句：

```python

temp = 23

if temp > 26:
    print("It's hot")
elif temp < 20:
    print("It's cold")
else:
    print("Cool.")

```

这里temp与具体数字比较的结果，就是bool量。它的取值只有两种可能，一种是True, 另一个是 False。这里True和False是关键字，首字母大写。我们也不能将其挪作它用。

```python

truth = True
lie = False

print(truth, type(truth))
```

## 基础数学

量化交易不可避免地要使用数学。我们先看看Python中如何实现基础数学吧！

```python
print('加法:', 2 + 2)
print('减法:', 7 - 4)
print('乘法:', 2 * 5)
print('除法:', 10 / 2)
print('乘方:', 3**2)
print('整除:', 15 // 4)   # 结果为3
print('模运算:', 15 % 4)  # 结果为3
```

整除相当于对除法的结果只取整数。$15//4$ 就相当于 $int(15/4)$。模运算相当于求余数。 $15%4$即相当于 $15 - (15 // 4) * 4$。


