{"cells": [{"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["1. 只需要替换股票名称，代码，时间，数量等直接运行即可其他不需要更改！需要存到本地，则自行存入\n", "2. 有问题扫二维码咨询，写的匆忙，也比较简单，有任何问题和需求都可以提出\n", "#### ![image.png](attachment:image.png)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 导入所需安装包"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import talib as tb\n", "import omicron\n", "from omicron import tf\n", "from omicron.models.security import Security\n", "from omicron.talib import peaks_and_valleys\n", "from coretypes import FrameType\n", "from numpy.typing import NDArray\n", "import cfg4py\n", "import datetime\n", "import numpy as np\n", "import pandas as pd\n", "from omicron.models.stock import Stock\n", "\n", "cfg4py.init('~/cheese/config')\n", "await omicron.init()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 定义仙人指路函数\n", "仙人指路：一般是指股票在拉升之前主力对上方价格空间进行的试探，\n", "导致会出现长上引线；\n", "一般长上引线出现后续股价很可能会上升"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def high_upper_lead(bars: NDArray, upper_thresh: float = 0.015) :\n", "    '''主力在拉升股票之前，会先进行试探\n", "    就会形成有长上引线的K线\n", "    称为仙人指路\n", "    检测八个时间单位内是否出现过长上引线 '''\n", "\n", "    assert len(bars)>=30, '传入数据长度必须大于30'\n", "    close = bars[\"close\"]\n", "    close = close.astype(np.float64)\n", "    rsi = tb.RSI(close, 6)\n", "    \n", "    # 检测8周期内是否出现RSI高位，并且已经触发回调\n", "    high = bars[\"high\"]\n", "    open_ = bars[\"open\"]\n", "    upper_line = high / np.maximum(close, open_) - 1\n", "    iupper = np.where(upper_line >= upper_thresh)[0]\n", "    condlist = [upper_line >= upper_thresh, upper_line < upper_thresh]\n", "    choicelist = [high, close]\n", "    new_price = np.select(condlist, choicelist)\n", "\n", "    new_price = new_price.astype(np.float64)\n", "    rsi = tb.RSI(new_price, 6)\n", "    pivots = peaks_and_valleys(new_price)\n", "    ipivots = np.where(pivots == 1)[0]\n", "    ihigh_upper = np.intersect1d(iupper, ipivots)\n", " \n", "\n", "    if len(ihigh_upper) > 0:\n", "        dist = len(close) - 1 - ihigh_upper[-1]\n", "        if dist <= 8:\n", "            upper_rsi = rsi[ihigh_upper]\n", "            upper_rsi = upper_rsi[~np.isnan(upper_rsi)]\n", "            if (len(upper_rsi) == 1) and (upper_rsi[-1] >= 70):\n", "                return dist\n", "            elif (len(upper_rsi) == 2) and (upper_rsi[1] >= upper_rsi[0]):\n", "                return dist\n", "            elif len(upper_rsi) > 2:\n", "                prev_mean_rsi = np.mean([upper_rsi[-2], upper_rsi[-3]])\n", "                if upper_rsi[-1] >= prev_mean_rsi:\n", "                    return dist\n", "    \n", "    return np.nan\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 对指定股票进行验证"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 股票代码\n", "code = \"000608.XSHE\"  # 阳光股份 可自行修改股票代码，6开头的股票，字母后缀：XSHG，其他为：XSHE\n", "# 250个日线bar就是差多过去一年行情数据\n", "bars = await Stock.get_bars(code, 250, end=datetime.date(2023, 3, 1), frame_type=FrameType.DAY) # 时间也可改\n", "\n", "index = np.arange(len(bars))\n", "upper_index = []\n", "for i in index[29:]+1:\n", "    dist = high_upper_lead(bars[:i])\n", "    if dist >=0:\n", "        upper_index.append(i-1-dist)\n", "    \n", "upper_index = np.unique(upper_index)\n", "upper_index"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 绘制出交互式K线图"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go \n", "from plotly.subplots import make_subplots\n", "from omicron.models.security import Security\n", "\n", "name = await Security.alias(code)\n", "frame = bars['frame']\n", "close = bars['close'].astype(np.float64)\n", "index = np.arange(len(close))\n", "rise_fall = close-bars['open']\n", "\n", "\n", "fig = make_subplots(rows=2, cols=1, specs=[[{}], [{}]], shared_xaxes=True, shared_yaxes=False)\n", "# K线\n", "fig.add_trace(go.Candlestick(\n", "    x = index, \n", "    close = close, \n", "    open = bars['open'], \n", "    high = bars['high'], \n", "    low = bars['low'],\n", "    increasing=dict(line=dict(color='red')), \n", "    decreasing=dict(line=dict(color='green')), \n", "    name = 'K线', \n", "    text=frame,\n", "), row = 1, col = 1)\n", "\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall>=0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close>=open成交量',\n", "    marker = dict(color = 'red'),\n", "    text = bars['frame'][rise_fall>=0]\n", "), row = 2, col = 1)\n", "\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall<0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close<open成交量',\n", "    marker = dict(color = 'green'),\n", "    text = bars['frame'][rise_fall<0]\n", "), row = 2, col = 1)\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x = upper_index, \n", "    y = close[upper_index]*1.1, \n", "    mode = 'markers', \n", "    marker_symbol = 'triangle-down', \n", "    marker_line_color = 'midnightblue', marker_color = 'pink', \n", "    marker_line_width = 1, marker_size = 9,   name = '长上引线', \n", "    text = frame[upper_index]\n", "), row = 1, col = 1)\n", "\n", "fig.update_layout(\n", "    title = (f'{code}: {name} -仙人指路标记点'), width = 1000, height = 600\n", ")\n", "\n", "\n", "fig.update_yaxes(dict(domain=[0.3, 1]), row = 1, col = 1)\n", "fig.update_yaxes(dict(domain=[0., 0.3]), row = 2, col = 1)\n", "\n", "fig.update_xaxes(rangeslider_visible = False, row = 1, col = 1)\n", "fig.update_xaxes(showspikes = True, spikethickness = 2, \n", "                # rangebreaks = [dict(bounds=[6, 1], pattern='day of week', enabled = True),\n", "                                    # dict(bounds=[11.5001, 13], pattern='hour', enabled = True),\n", "                                    # dict(bounds=[15.001, 9.5], pattern='hour', enabled = True)]\n", "                )\n", "fig.update_yaxes(showspikes = True, spikethickness = 2)\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "cheese", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}