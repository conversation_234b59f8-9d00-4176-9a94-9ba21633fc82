{"cells": [{"attachments": {"image.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAANQAAAELCAYAAAChwahWAAAgAElEQVR4nOxdd3wU1fb/zs723Wx6b/ROkA4CgiCK+BCQpg95dp+CHX2gPKXZEEVRkKLg86G+n1JsiHQpQUAQBaSE0BJCQhLSNtun3N8fccad2ZlkN1kU39vv57MfmJtz7zlz596Ze8499xyKEEIQQQQRhAWaP1qACCL4b0JkQkUQQRgRmVARRBBGRCZUBBGEEZEJFUEEYURkQkUQQRjxB00o/o9hG0EEVxh/0ISi/hi2EURwhRGZUBFEEEZoQ69C0NgJQUBAhWEyqTl3UFRkokbwx6IRE4rCsV/y8P2uH+FiXNDrAatJA6veiJh4PTp3tsJkjgXloMFfcgFtYqDX2sEyDtDmVFRdJrht1CNw2GtRY68CTwhYzgue94LjCAAe4Cnw0IFnGSSnJCEj04aPPvk/pKa2BSGRiRPB1YuQl3w/Hz2J95ZvhsOpBY0YEN4AjujAEB61rkpwFdU4XnQQGnspTqz4BtYyAlLtg9ZZAwO5jORkO9q37ITKUgcYF+B18PA5CXxuHp5aBq4aDm6XGz53DXw+F2orynD5TCEsvlL4aosiq8UIrmqEPKEO/uyEKT4DnJ4GSxPQOgO0WhP0Oh1ojRnFl4xIi20OlmZhSdFi57dr8eIrL2HfocOwO8rgcVVh0p2D4fM6AXhBNG7QOgZaLQ9aS6DVUdBQWgBaxNni4fI48NSzT8CWYgPPlUXm0xUBkf0iaCxCnlA+Xo+BQ3sgKSkKsTEW0LQR4EygKSMIz8MBO6w+D1iagEnWoPPAHAztdz26tekHmqljOXBIcwwYOAAe1g2eZUARDhQBQCgQwgMUA5rSQEPx6NmjFe56eALY6mJw3oth74BgQQhR1d2UyuujD6Z+qLKF1hYBITwICHj5BPp11U0IRJoIgkfIEyouJgp6fRT05hR4eA1qaqvhcFWDUBwMlBYcq8Gpi07UlGsRreuBih+BmFIDzh0+D4veDJrnQHFlePOtJ5GWnAmni4GX8YDjeRDCg+cJNFotTCYNbBZg/Rcfg6stBOd1gfGxQcv56aef4t///jdWrlwp+f373//GTz/9JKEVBuGsWbMAAAsXLkRlZWVAm/6623PPPScpnzdvHux2uyo9APh8PlRWVuLQoUP45JNP8Pjjj4Pn+QC6FStW4NChQ+L13LlzUVJSonqvFEXB7XZjypQpQeqXFEBRAKHAgwchFQCcANi6cs2vJISAj+wZhgQq1PNQby3LQ1xaMmodtbhQdATNkyhkZxjhrq5CvMEK2ugFFc0jQ2dD/uJTyE62wNmlGJ375YA3esHTNAgoGKJTcPznYtw16Tn8+MsxGGgKGooGBQrRsRZkpJqxf99XAOdEbe1l6NwUHNAgocXI4G6MorBo0SJ4PB5xwhgMBmzYsAGpqalYuXKlhH779u0YMmQICCGgaRpHjhxBx44dAQA8z0Oj0eDEiRMYMGAAkpKScObMGaSnp4OmaWg0GhQWFiIxMRE8z2PMmDFYuHAhAOCmm27Cli1bYLVaodfrUVFRgcGDB6Nly5bo1KkT7r//fpjNZjidTjz44INISEjA119/jaysLOTk5ICmaXz44Yfo168fkpOT0aJFC8lkFlBVVYW4uLgQvoo8KIrg7MbhiKo8CNYUB15vA6FN0HhYGFt2QVznheBggCYsttn/DYRs5TttN4G7WIv2zfRomdYNp059h9hEG3joccnjRayOQ6yXh8NYg/azcsC7XTDzNtg5FiYY4XW7QYGDni5Dh84mbNr8OsaNexZ79v4MhmNh0QP9rm2Jz9asBO8phbvWA8IAHKsB24ilEU3Tv92sVguKoqDRBH6Yb7jhBnzxxRcAAJvNBoPBAAC48847kZ+fj/3796NFixY4evQoUlNT0bx5c3z99dcwm81ITU3F9ddfj5dffhnXXHMNXC4XAODnn3/GrFmzMH/+fOh0Opw9exZPPvkk/vWvf6G0tBQ+nw87d+7E0KFDodVqkZOTg/j4eGzcuBHJycno1KkTDAYDPvroIzRv3hytWrVCQkJCg/epCnHHg4CiNPBUHoexchdMtixQGgrgq6HxlYNoWOg0NHye89AY2/1Wn0fEWa0BhPyFmrPoKBKSM8AY3Mg/VoOeLXWwJZWg+lwhLLE2xBt8IF4fLDFapGXoQelpUAwFs44CRXGAxgvCM6ChhVarg8FAQ2eIx9ZdP2Lt6t148O+3o2vvjuDsxfC47eBZHpyPgsbnhZ0xIKPTKFXZCCHikufNN98Ez9ctV/yXQQzDoF+/fujfv79YNmTIEJSVleHo0aMAgPj4eBw7dgwTJ07E4cOHcfnyZZF27NixKCsrg81mQ3l5Ofbv348HH3wQBQUFIISgoqICubm5MJlMmDFjBjZv3oyEhATU1NRg7969SExMRHl5OUaOHImqqiqUlZVh7969iImJEXls3rwZ1157La655hrs2bMH27Ztw9ixY6HX6+t9NhRFqet5AMATEA0DDfTw1hxC5cG5sLm+B6cxoaq4GsmJiSAuH/Q5zUCbCTzR90IffzcoiPMwYmVtACFPqCqOhZ7W4ttdVTh6yIW2WTyMtnOw+HicPHkCn/7fB4g2GzHsxiHo3rUN2rZLAQMHKMJDq9WA5zyoqa5CtNUGitKAJxwsRi2iktIx/am3ccvoochpnwbCeUBrePi8DHhOB8rnhZMzIrvLbfXKt337dkyePBkdOnQAx3EA6t7ePM+L+orb7Ybdbse+ffswfvx4rFmzBkOHDsWmTZsAAG3atEF+fj5Gjx6NdevWSdo/c+YMGIYBRVHweDzo0qULCgsL4XK5oNFo4Ha70aVLF0md3bt34/bbb8fSpUvx0ksv4eabb8bu3buxdetWCd2RI0dw//33IyUlBRRF4fDhw2AYBgMHDgTLsmjbti3mzp0LABg0aBB27twpqZ+cnIzS0lJJ2ezZs/HCCy8AooGBBiF2nP2/joi2ctCDAuPywWSl4S5wovaiA9kTB8FJcdBbroE2+21BpYogCIS85Pt4XR5M5ubIL/CAJ2447DU4d/4XfLZ0BYrKLiM22oYSXykO/bQIzbIzce89o/HXu25AbfVl8ASgtQYY9GYwDAHhOTAsA47VweUuw8wZT4Djy0B4HzS0BjxLwLM8WI6DhmWDeqqDBw/Grl27MH78eHTr1g1A3SRITk6G1WqFw+GAx+PB5s2bQQjBiRMnsHPnTkyfPh3l5eUYOnQo8vPzsWrVKtx5552StvPy8tCuXTtkZGRAp9OhtLQUCxYswBNPPIGYmBiYzWaUlpbi3nvvxdtvv405c+bgxRdfRHJyMrZu3YrKykoUFBRg5syZWLRoESiKQk5ODj7//HO0aNECqampePrpp2EwGEBRFLRaLUwmE5xOJ0aMGIHPPvtMlGXHjh0S2ZxOJ6xWaz06VN1ajVBAxbaHEY9a6Ngo1BSVwc7o0L51CqoZJ7yg4Lvsg1bLgDZ7QSHyYQoFIU8oDbHCXu1Fq+YJuFxEobTwJDZ9txVmfRQyswy4eDofbVumIy25FXbu/QkPPfUCLl48gWn/uB+1bhdAATqtDoTTgONZ8CyBh/OB1rBgPFUwmDloaR4EFDTQQKvVgWE5EJ4FoYP7mBYVFeHIkSMYPnw4KIrCmjVrMGzYMKSkpKCkpARbtmwBULdEOnr0KDZv3gyO41BcXIxx48ahpKREXBL6LyPbtGkDQghWrVqFTz/9FEePHoXFYsEdd9yBHTt2YP78+di7dy/i4+MBAN27d8f+/fvRp08f/P3vfwfP8+jSpQv69OkDu92O2267DePHj0dGRgYAIDExEePHj5fci8vlQlxcHF599VWMGzdOnDBya57wNZajzvhAgacAChTY2n0g1T/DYNCA5b2wZkQjSmOAnWGRfnNvwGjE6S93IS0lFWwzDnpEJlMoCFnFbJcTjawWGnTvpsXl0l+Qf+oICMNBQ1NgPNXIaZaKv948EFPG9seq16bg+Ttvw/w3/oNNG/dCp9fB66PAMBQ0lKbOOEBrQIgGGo0BlEYDn4+Bx+cDDwKOcGA5AsJTYBgeHBe8CZfneRiNRhgMBtA0DZ1OB51OB5qmwbJS8zshBD6fD126dMGMGTPg8Xjg8/kA1A3cI0eOiP/PysoCTdNIS0tDu3Z1CnvLli1B0zRsNhtGjRol6mK33HILunbtCp/PhxdffBEvvPACnnrqKbzyyisYP348amtrMWHCBFXdaObMmbBYLPjoo48wbdo0UQYl07j/l0lY3hLCo66YggYUHMWf4tKqvrj8/XFcLvYAThqn9pTDVeaFLiMZMBrhKqtC/DVtYOqYDspn/01/iiAohPyF+mJDPpJiWsFeacfRo3twKe9ngDjhqqhAtAm47YYhyE6LAa9zwqLh8Jfr2uDsxR5YvPATDL2pD5wsA4bXQKPVgQcPmqcBjQaURgNKw4JQGjA+/tfvEwVwAE04gNaDVrDOKUGv12PGjBlwu92wWCxiOcdxSExMxNSpU+utz/M8TCaTeN2/f3/89NNPyMjIAMdxyM7OhtvtxqhRo7B48WLExMSgdevWsNvtePvttzF16lRs27ZN0uaePXvESarT6ZCfnw+ttq77/b86s2fPxunTp7F27VqMGDEiaDN4dHS0+H9/KyZF1S3ZfI6DqN72N+h9gDHRgMTEGDhq7UhvaYY1yQhTWhSKt+2CRWOCdWAfePh4aC29I5MpRIQ8oXIsWXBVFyLRloTLxw/BWV0KDTg4ah3o0qwl4uKs8LFeMDUMYvQceIMWfXp1wtwF/8LZvEvokJMJh5OCz8eANpihMRvAcyzcXh90OhpGUzQ43gmD0QhaQ9fpUS4PvKwGRGdpUD5CCCorKxEVFQWdTgez2QyWZWG1WpGQkACGYeD1elFRUSEuzQghokUQqBvwH374IQYMGACv14va2lq0bNkSH3/8MSZNmoQ9e/agvLwcq1atwiOPPIInnngC27dvh9vtRo8ePQI2eIG6ZajH4wFQZyQpLS0VB77wxZk6dSo2b96MTp06QafTYdSoUdi0aRM4jhNlrKiowN13343z589j4sSJsNlsoChKXPKNGjXq1703Hk6nE7m5e0ABqDq9FRprM7B0JWI1PrgpN+hYA1iqFqZmseCcGtDt7wYf3wG61IGg9e3BA2CJD1qqfutiBL8h5Amlq72M9knNUF5xAlWlRaANPvAeBmYNDaNGh+ryyzh/6Cyc7hJ07tIOcVFROHviJOLiYnDzzXeiwvWbt5hOB9A0YNACm778FHkn9mPqP15GcnIm9DoTONYHo0GPZs0yEZ+UgC3fbcfxgrJ65cvNzcW0adPETU5CCHr37o0TJ06ISzGO4/DVV19h8+bNAOomlLDvBACrV6/GCy+8gPXr1wMA3njjDQDAxIkT4Xa70a1bN7zzzjsAgEWLFoHjOLRv3x6vvvoqAODAgQPiZjDHcTCbzRg1ahR8Ph8IIdDr9di9e7e4lBRw5MgR/PTTT9Bqtdi7dy8++OADFBcXi9ZJnudhsVhw9913IyYmBvfee6/4JaUoCg899BDcbrfY3m//55ByzXTgmukAquFxnoe38ijYqqOg+TKwza6FzjIUybrmAITz1BxoQkAikykkhGw2/2DpKcQkMojiWdx+9xhYYikQFwcd0aB9y+bolB2LC8UFyMiMQmZ2MpJiYrFj50GcuVyFn0/lw8VoQWgeLNHAoDfAarXC53bj3+8vweDr+uLJJ57E9p3HYYmywemyg+cJrDYLdEYtSssv43zBOVXZ/A0IwSAUemFy+i+nhEnjD39XIjl9fW0DkNSrT65Q5aYo/0f8mzws6vQjza9/JhQHCvSvyzz+179GFn2hIGSjxJHTX+Oa7oCz+mdoDV64XF4wPhYUKGh0gEbHIjUjEdlZqWiVmIYW6SkYP/Y63DFmCDgtgdPHABygBQEFDuAZaHU6vLF4KfILivH0P6YhOTsKRZfPoqS6EGX2YhQWn0PhhfOgNPXP/VDPSYVCL/ew8F8i+kOj0YiGg2Amk9C2vywNyRWq3KJzHjQAyK/+ewRacKDJr9OGItAQGhThUbeGiOw+NQYhTygrSpChYdCpTQribBYwXh8Ytu6AYJIeGNKjC/4yoBvaZ6eChgZGrQlW6NElNQM3X9sV3XPawOnj4GVZMBwHp8sDh6MW27bvwqatO2CxmHHnxDEoL68EYX3gGBdYzgG3x4Hi4mKZNH+cJ7Sate2qBqk7nlHnm0eBgAKheNS5+lMA+F/nkP99RbzNQ0HIE6p/j46g6FpkN49G906t4GMYEBDU2CtgBAXOToN1uVBZ7sSpvDKs37QDpZUVOHr0Fzx6919x4KeNaJudBpYFKB7wer1gWRoUpUdWVnuwLINRf7kFvXp1hMPJg2EpsF4ObrcHPh9zJfqgUfjzTahfPcxFV3LBmO73JaI0CBwSf6Z7/OMR8oQaen1H1FafB/hq3HfncFCEAasB3GBxrqIcp4ou4PCRPJQUX4bNGoN4WywuXSxDRa0DlR4Xys4eBTg3bHo9nG4fWmSmwmokIMQHs5FGde0lVFVfxhOPPlB33I0AHgK0SUrA9uVLZNJEHnYEVxdCmFB1n/7aywVwV7rgqK5E/2uaoWPLDLDg4QOFInsNqn0cGKKHw81h546d8JZVo3XLtjiaX4iTBeXYsGED/u+TlajxlOLIns9x7NRWlNccw/jRPWA0MnC4ylFWUozWzdPrBNQAnFaDKJ0Wv+zecyX6IIIIwoYQJlTd10BjMCMuMQ2W2DhoCYOHJ40B8bpgooHTFy5g7y8nYLAlIKNlawy79WZoNTRo2oTkjBTcMWYsctp1QtuWmTi+dwv0tBu/7NuK3VvXIiEhGmXlF5AQlQazKQoJ8ano070jeEKg0+gRG29FTLz1CnVDBBGEByHvQ3kq3HD7HEhqnw2AoGuXbJg0DHjo4IMP+/J+QWFpAbp3aoucNi1hiU/C+s3b8ciUe6BzVKJTp47Q20z4+chRpGek4NSpI3hl/jKs//ZzRMWk4cLpQjD2aqTGx6Nvz07Y9+MxROmAIYP7o6o08BRtBBFcTQhZh+K4uoADjMcNj8+FzevW484RN4HjfdBACy2tQaxNh5pLp7H94G48s2Q5zlwqhc5dA2fJGVTl5+Fk7lYM69EOWTFa9OzUHP9Z9goSNR6g+jxaJBvQsXUmNLwbfXvVeYv73B4wLAeOV3YAjSCCqwUhTyh9nAXR8TboQOHn3Ydwy43X48WH78eYa7uDgAHDuGEx6NG6bQscyzuLWgAJqRkgFAcN54az4DyoixdQlXcCpKYSSdFmRJn0OHL4FzA+Ly5VnMfFsmNgfJfRpk06/vnPR5AQF4vNm3fC7qz9VYqIKTeCqxMhL/m0tBEsWwnvJRdM+hi0bNkGRq0bk/8+Bofzz+BIaQWKyy8hxmYE96tZOT4hDsmZOeAclfCYa+FjGVTZnbhQVAkjA7A+Fjzh4fPUIiUpFpfKLqK2phygbBg9fCBaZ8ahU3YcaqsEs3nEuhfB1YmQJ9SxvCIUHNqFntfkoEvP7gCvw/QXFqK8qgZDru+Hmq27cLyiGsa4KpijbEB1ObweL3bu2AGHoxo+zg2j2Qhaa0TrLp3g5lk4OAdaNMsCobywmc3g46NAvAbY3Xqs37AZ9ho77GUmnDlXgoG3P30l+iGCCMKCkJd8ZbUVWPLuKrRsmwNAg0E3jsCQ4UOwYuVczH92PPZ/NBfd2jXD8TMXUHO5GiYAR345iYtVBPnFXuQXuXDoWAEu2e0ovFgGt5NGlCkOly6Uw+tmUVRYjMLzl+Dx+KChPHjmhUdx47DB2L/jAOznisLfAxFEEEaEPKHibfGY99ILQJQVj/7jedx8y60YOuRGsCUVuFxShpLaGjx9+1/Ac4CHZxBl0IGjdTDbomHQ6WG2GmGwmZCanIScDq2QmGSEieFQWlKKxPh4OJwubNywHefOFQJgUXjyNM4WVCArPRn9BmbLpInoUhFcXQh5yed1u9H/2m4o+qUEX3+3F7/snofqs6dRWVaEansV3ByPZqnxaJ2RhILyalyT0xWXigqh07NIjNGj2uWE1WRB0YlTMJdcwrWZKdiycw/47Gz4iBNGaNGpSw80a9MGDjeP+S+9ju92nsADE29FUY1bJk1El4rg6kLIE6pH38Egzov458vPY9KE8fBWVaAorwg1tV54eB8oPQ+DnsWNfTvhzf9sx+WKYhDKja+3fYUn/3on8s+fRkxUFA6t34x1O/ahtH0mqqodMLgIyos7IN/hRHzrrnh50afgPJV4642piE1Or/NBMnaORAyJ4KpGiBOKR3R6a3hrUnCqtBJT7rsPl4pLUM0zcOlpMB4TeK8LGrcXOR3bg8V2nDp3AceP7sfrs+eC4zUw2WIQl5SCWq8Pdi+w9/gFGCx68IVnsOixN7G/QBpyePnKLLA+Dj42CUa9FZrIZIrgKkZIBwwFyj/KybqOf2iHCCOI4PdEyCd2I4ggAnVEIlVHEEEYEZlQEUQQRkQmVAQRhBGRCRVBBGFEZEJFEEEYEZlQEUQQRkQmVAQRhBGRCRVBBGFEZEJFEEEYEZlQEUQQRkQmVAQRhBGRCRVBBGFEZEJFEEEYEZlQEUQQRkQmVAQRhBGRCRVBBGFEZEJFEEEYEZlQEUQQRkQm1FUAISH21dre1cLrz4BGxZTgeR4PPfQQ9uzZA51Op0onJG3273SKosQs5sL/hb9pNBoxi7rwN6G+RqMBIQQsyyIzMxMrVqxAamqqIt9Qs8Ff7ZBniRfKQssE//v3R6gyAoEJuYuLi/H3v/8d58+fB03T4hhRgzBO/NsU6tQ3HpXAMAwGDBiAxYsXg6bpoO4DJES43W6i1Wrrsh//wb/Dhw+ryrlnzx7Sq1cvMmTIENKvXz9y6dIlUlpaSvr3708GDRpEevToQXbt2kUIIWTgwIFk4MCBpHfv3uSDDz4IqT/eeecd0rdvXzJgwADyl7/8hRBCyK5du0jPnj3JkCFDSN++fUl1dTW5dOkS6d69u8j7p59+IjzPk4EDB5IhQ4aQ3r17k7Vr1xJCCLn11lvJoEGDyLXXXktee+01Vd7z5s0jffr0IYMGDSIjRowghBCydetW0rNnT7F+VVWVYt3a2lrSr18/MmjQINKzZ0+yceNGQgghw4cPJ4MGDSJ9+/YlixYtUuX98ssvi/VvueUWVbrDhw+T7t27k4EDB5KuXbuSkpISUlNTQ/r06UOuv/560rNnT/Ldd98p1j1y5MgfPsYAEK1WS7xer+o9+iPkCTV48OCgBdFoNESj0RAAhKKooP9GUZTq3/x/JpMpaDlfffVVMmfOHEnZrbfeSnbu3CkpS0hICKk/aJqW1P/xxx9J//79JWVLly4lM2bMkJTdeeedZNOmTZKynJwcUl1dHXCfhBDC83wAbzndmTNnSNu2bSVly5YtU5R7yZIlErrrrruOnDp1SpG3EuR0J0+eVKSbNGmShG727Nnk3XfflZT17dtXsa7ZbG5wDAhjKRxjrj4egwYNamgoEEIICVmH2r59e9C0xO/TSmSf1fr+5l+m9DcBbrcbVVVVin+zWqXpQ00mE8xms6TMYrHAYDBIyqKiolT5KUFOr9frG83barVCq5XGHpXTKEFYJul0ugB5jEYjgMB+NJlMAbz1en29NPVBbekvv2+z2Sy2K8gt7y8AqKqqgsvlapAvRVEBS8DGjLn6xhkA7Nixo0FZgEaEYg4F9QnZ2L/JobZG93g8kmuGYcAwTACNvL7T6QyaNwDU1tZKrgkh8Hq9kjKfzxfw0H0+X0Bbbrc7QB6hrfr0J6G/eJ4PuG+iopvI+9jtdos6hn9ZsFB7ZvL79Hq9YFlWUkcuMwCJzhLKWKlPvwplXNXXRn16Ydgm1Pvvvw+LxVLvDTUGFEVBp9Ph4YcfxuXLl4Ou9+2332LlypXiG37SpEkAgI4dO0Kr1cLj8WDEiBHQarVYvXo1PB4PGIbBzTffHJJ8JSUlyM3NhU6ng81mQ+fOnbF9+3asXbsWVqsVLMvilltuAQBkZGTAYDDA6/Vi0qRJoGkaH3/8MSiKgtfrxYgRI2A2m7Fx40ZcunQJHMdh0KBBin0CAOfPn8fOnTtB0zRiYmKQnZ2No0ePYt26dTAajWAYBiNHjlQcBHfffbeo5Hs8HkyYMAFWqxU7duyAw+EAwzAYOHCg6n2fO3cOO3fuhFarRXR0NFq2bKlIt2LFCvTv3x8GgwFOpxMPPvggACA+Ph4GgwEejwe33norgIYHKwAkJCRg8eLFYBgm7MYWjUYDh8MhyqiEhniGbOVTauz7779H3759Q2kmZPA8r2hpqa6uRnR0dFBtuN1uPPvsszCZTHC73XjkkUfQqlUrRdpXXnkFFy5cAMuyGDx4MG6//XasXLkS+/fvh0ajQZs2bfDkk09i+/bt+Oabb6DT6WC1WvHPf/4ThYWFeP3112GxWODz+TBv3ryApVyoWLJkCQ4fPgyKotCpUydMmTJFke706dN45513YDab4fP58MYbb4TEZ9asWfB4PPB6vRg9ejSuu+66oOseOnQIS5cuhcFgAMMwWLp0qSIdIQSPPfYYbDYbamtr8cQTT6BFixYSGrvdrvhcvV5vwNI03Pj+++/Rr1+/gHJCiMRaqIigNC0/QEFhKykpIYTUKc5X4lcf7+rq6qBlf+qppyR1hwwZIsrtj5qaGkXlXKlMbvE8ePAgGThwoKTs5ZdfDrWbAyDnbbfbFekyMjIkdEuWLAmax/fffx+0UUIJKSkpkrqffPIJIYQEPMtXXnlFQte7d++AtpSegb88SkaacKGkpESVN8dx9dYNy8Yux9VlZxf2j8L9CxcsFovkWlCG5TyC3nMAYLPZJNcGgyHAMCBXzMMBtS9eTEwMgN/uKRTeTTFKAIEGGn/jg/+zlLcr78NgcCX31YTx3BiE1VOC/GpFIWFS/sINuQGhurpakU5QmoNBZWWl5Nrj8QS063A4gm4vWMgNLAIEPVPov70JzqMAACAASURBVFCMLHLjQChGCQABFlc1K528P9Qstb8HwjVeBYTVyid/ayxduhTl5eVBv/EJIdBqtfjHP/7RpDcQUVEc33zzTcTExECr1Yo6lBKio6OxYsUKXLx4ERzHicr5F198gV27doGmabRt2xYAcODAAXzzzTfQarWwWq3o0aMH1q5di5deeknUoWbMmNFk2T/66CMcPnwYAJCTk6P6Vj9y5IioQ7Esi4cffjho3v369cNLL72EmpoasCyLCRMmBF0XAH744QesWLFCNMb89a9/VaSbMWMGXC4XbDYbnE4nHnvsMQDBGSXU8Oqrr4JhmKDr8zyP+Ph4VV20sbhiZvPp06dj3rx5jaq7evVqHDx4sNG8KYqCz+fDkiVLYDQawfO8OLBat24NmqbhdrsRHx8PAPjXv/4FhmHg8/lw6623IjMzE61bt4bFYgHHccjIyAAAjBw5EiNHjpTwSklJEdsUBnlSUhIWLlwYstzCgFq7di3Ky8vBcRwGDx6M9u3bY+LEiZg4caKEPi8vD9u2bRMtbRMmTEBsbCxatWoFo9Eo+dK+8847MJlMcLlc4gBWwnPPPReUrCdPnsS2bdtA0zRiY2MxYcIExMfHo3379jAYDKK1l+M4LFy4EFFRUXC5XHj88ccBAO3atYPRaITL5UJsbGyoXQXgt/7q0aMHfvzxx0a1UVBQgNdee61RddWECglQUNaKiooC6G688cagdqCVftHR0UHzVjNKCPyF32uvvUZeeuklSdnIkSPJnj17JGVpaWmKvAhRVoT9d+kBkC1btoTapRKcOXMmaN5yuoKCAtK+fXtJ2bp168izzz4rKXvooYeaJKMSb7vdTlq0aCEp2759O3nooYckZbNnzyYrVqyQlPXr1y+g/YaMEv6IiooKeYwJv6FDhwa0V1RU9McaJZTQFDNxvWbJICF4CAjQ6/UBXgdmszlgOepvuAhm+SBfeoVi0FCCnGcoRgWKogK8EGiaDrhved80Bf585O3SNB1ggDAajQH3GKrxQ476HLSvZF0lXFFPicYiHBYcuTLudrsDFHmHwxGgiPsrzCQIZbWmpkZyrbTrHwrkngXBuN8IYBgGdrsdwG+ye73egDZC9QapDwIflmUDjA0+ny+Al8vlCpBHbiz6M+OqnFDhwPz58/HII4/AarXC6/Vi8uTJAIDc3FyYzWbY7XbMnDkT3bt3x/Dhw8HzPNxuNx566CEAwMMPP4zCwkJwHIcbb7xRlc8HH3yA//u//4PBYIDVag3Z00KOtm3b4q677kJlZSVYlsVtt90GQPkls3jxYqxevRp6vR5RUVFo0aIFPvzwQ7zwwguwWCzwer247bbbMGjQIPzwww+Ijo5GbW0t/vnPfzZJRqBOJ9uwYYPou2iz2bBs2TLMmTNHNMbccMMNyMrKwpEjRxAdHQ273Y4nn3wSRqMRX3zxBaxWKxwOB1566aUmy3O1ICyeEkVFRUhPT5eU3XLLLdiwYUO9503UEBcXh4qKiqB4K3lKkHqsRceOHQMhBD6fD926dVOVobS0FOXl5eB5Hi1btgzYwxLAcRyOHj0KrVaLqKgoZGdnAwA2btwoLoGU3IeUIMhdVFSEiooKcByHDh06wGg04vz58zh79iwIIWjTpg0yMzNV2zl8+DD0ej0IIejQoQOAOi8Gg8EAt9uNHj16qNY9dOgQ7HY7WJZF7969ERUVhXPnzuH8+fNieykpKeB5HkePHhWNMVlZWUHdo7+MWq0WDMPgmmuuCfi7mqeE0liKj48P2L4IFsOHD8c333wjKbt48aJoiJLz/l08JZSMEsOHD2+0USIuLi5o3vV5SnAcJ/4IIeTVV1+V1B03bpwindfrDUoZJoSQ2NhYCd2ZM2fI6NGjJWVLly4Nun9LS0slda1Wq+K9+3w+xfp9+vSR0G3dupW89tprkrLp06cr1pUf34iPj1fkrVR28eLFgL7071N/rFy5UlJXOMvlj1CMEnFxcY02SgwfPjygvaYYJf5rl3xAoHFDvlYXPLnldIIeE8zXVb6r7nA4RB1BqB+KziLXQ9R27X0+n6JCLd9UdjqdAfzV5BHqCnIrecTLIb/HYAxKSjL+t+B/KqaE/FiF/FqAMIkamkxKbXAcJxomhPrBDEz/+sHIqObVLzeKsCwbwF/Ny0LgLcgdjIFFoA3llIGcv9o9/hnxPzWhbr/9dgC/mY3Hjx+vSBcVFYXmzZvDarXCaDRixIgRqm1OnDgRFEXBaDQiOjoaXbt2xX333QfgN3OwcDwhGLRu3Vo82kBRFO644w4AQLdu3cSDil27dlU9CCl4fwi8+/fvL8ov6CRjxoxRrNujRw9ER0fDarVCp9Ph7rvvBgB06NABBoMBer0e1157LQBg1KhRoCgKBoMBNptN9BwJBkJ/CL6UwtGa/wrUuyBUAELUoRrzC5cO5Y/6vJNrampIaWkpKS4uDq4TGoFgvKND9aCW019JD+xgUVxcTMrLy0lFRQUhJHgZg/H4VxuuV5MO9T/zhVKz+p04cQLR0dFITk5GWlqa6Lfn7yHdWNeYYPgHS6PkgS+nV6u/ePFiSd0333xTka60tFRCF8pXBwBuvPFGpKWlITExEfHx8fjpp5+ClvG/JUrV/8yEUsOpU6ck10VFRQE0al7pfyRCUeTz8/MB/DZoz5w5o0h3/vx5yfW5c+eCap/8qkedPn1aUi6/vlIgIW7LhKuuEq7YhGqKotlUb4NQIO/QcB/hv5og3Kua5VB+78H2hTBR5e2Ge7CqoSnjJRSDUTC4YhOqKR688+fPD6Mk9aN79+6S665duwbQNG/e/PcSRxX+vpE0TatuNCtBMCQI6NOnjyJdx44dJde9evUKQcI6A4g/lPoynBAm7Ouvv97oNppSVwlXxFOChCF4hlIbwXpK/K+gwV37q4CHvH6wYyNYT4lw9IG8jaZ4SlyRpxEOBbOpbbzzzjsSBVstrtqlS5ckdPfccw+AOi9voSzUADTz5s2TtHn48GHs3LlTUvbee+8p1mVZFnq9XqQbNmwYACA9PV0sE9yOrvRkqo9Hr169AowkTz31lKTs3LlzAfUpisKBAwckdMK5ucYsEYUwy8TvtHiwP57nw77ED8sT+b3WyqFg/fr1kustW7Yo0u3du1dy/d133wGQHv/et29fSLy//PJLyfX+/fvFdgVs27ZNse65c+ckG59CYNHi4mKxrKioSPQq/6Nw4MAByXVNTU1An6sd+tu5c6fkWvCla+xLtCn1lOKWNGU8h8X1qDFBNq405OeS1M4pyTszHG99Jd7yMjU+8vJg6f5o+N+j4I4UbJ83NcRauE3u8uA9oUywsDyV+o5UhwtqMd7U0KxZM8m1Wvw9eXlaWloAjZoBQK2j5W1mZGQExJ0Tlm3yNpKTkyXXSUlJijyUDh4KS5lg4U/bUD3/ZRKgHMpZ8DgX2pKfQBAgN/IIHvrBYNmyZUHTNhZPPvkkgND7EwiTUQKoe2P6O2v60zXmCIe8rhA2Wd5OKMc3QjWWeL1eEELCdsLV4XBAq9WC5/l6T+KSX51NKYqq15onv5+mGoNCrS+Ejq6vf4KVUV4uN0r4P3uDwQCapsMeOZaiKLjdbjGlknysBWOUCIvr0R/5q6ysVJTzs88+I8nJyaRZs2YkJSWFHD16VJGupqaGZGRkkGbNmpHk5GQyZ84c1Xu/6aabSHJyMsnIyFDNGEEIIZ988glJSEgg2dnZJCUlheTl5SnSVVZWkpSUFJKZmUliY2PJggULVNscPHgwSU9PJ5mZmeT6669XpVuyZAmJjY0lmZmZJCUlhZw7d06R7uLFiyQhIYFkZmaSqKgo8t5779XLOy0tjSQnJyu66giYP38+SU9PJy1atCApKSnk8uXLinSnTp0i8fHxpFWrViQpKYl89NFHhBCp+1FVVZXkOVMU1aijQOH8EdKw69GffkKpnQuSp7OZNm2aIt26deskdEKQlmDuXS16a69evSR0r776qiLdZ599JqFr165dk3k3a9ZMQrd8+XJFumXLlknoevXqpUjncrkCeKv556Wnp0voPvvsM8U2582bJ6EbMGBAAA3DMFflhGrIXzJkHeqdd94JtcoVw+jRo1WDbMiXIWrLErniHEr6GDVlWr5MU4vF3dRIrUqQ36eajHI6Nd5Kyxu1/UF536k9G/l9K/W5VqvFqFGjxGsS5oCUoWLRokVB0YU8oR555JGgY7ddSQwePBjr1q1T/bv8YarpIo3ZFBYerNqAkQ9iQV+SD4iG9Kj6BpDaJJVPFDUe8gnkny4zGD7+tGr9ocY72En/+eefY+jQoar8fy9Mnz4dU6ZMCWrPqlE5dgXs3r0bWq1WVcm8EuA4Du3atUNCQkKDtLt27RIfXn1uNMePH4fD4QDLsujVq5fqA+Z5HmfOnAFFUUhPT6/3i7Jr1y7xzdu7d29VukOHDsHj8YBlWfTr10/V1FxaWorTp0+DEIJWrVqJcR2UviC7d+8WJ0J9vAXnV5/PJ3qWK7Xp9XpRVFQEjuPQunXreg0Bu3btgl6vh0ajUexz8qshITc3F1qtFj6fr8EMHxUVFTh58mSTzeuhwufzYcCAAQB+e8E1tF3RpAn1R0IQOxRrXlFREW6//XYYjUY4nU689tprYofJ8de//lUMxXzTTTfh+eefx3PPPYdDhw6BoihkZWWFZMItKirCuHHjEBUVBYfDgbfffls1WMq4ceNQXV0NhmEwYsQITJ06NYCG4zhQFNWgW49aXzgcDowZMwY6nQ5OpxPTpk3DsGHDFCfUlClTcObMGRBC0K1bN7zyyiuYP38+1q9fD41Gg6ioKHz11VcBPIQ3ulxGQDkB3NV6hMN/ijQoY70a1n8ZnnnmGYmSKaSzkSPYdDZqFkYB/kFKHn30UUndsWPHKtY5ceKEqjIs/BqyNAWD5cuXS3h0795dlNkfbrc7qL4QcuwK9yzIGQ5Z/0z4rw7SIodSnlslBBv9taHEX/5vM7neoGb8kC9rwhnltb52hSP18q9TsH3h7yUh4Gr94lxJ/E9NKPl5HbWzME0NpyzAf0DJFVq1QCnyCXWlzobJ5VE7vxbspBDk/l+cRP4IeUKRq2ytqyaPUvncuXPRtm1b0DQNl8ulGhzEaDRi3bp1KCwsBM/zYnrI3NxcHDx4UDweHsqZpNdff12s4/F4xAAocpmbNWuGb775BtXV1eA4TjzLJNwPqUflDeXZ3HXXXaAoSsxEIgSDkUOr1WLr1q04ceIECCHiWaqCggJs375dzO8rd/W6UlC6x6aOyWDrCx4U9dGGPKEoisKGDRvEvLLx8fF4/vnnFWnLysrw/PPPw2q1wuVyYdGiRU1++69duxY7duwATdPIzMzE1KlTsW/fPvz73/+G0WiEXq/Hq6++qnjTHMfhl19+ESOolpWVISMjA9OmTYPP54PH48E999yDXr16YfTo0ZK6hBD069cvIPfq9u3bsWbNGuh0OsTGxmLWrFkoKSnB888/D5vNBoZhxL27vLw8sS8qKysDLJWCzKdPn8alS5fAsiyysrLQsmVLcTLVN6mEIxPz5s0Tc+wuWrQIXq8Xjz76qBj6eMGCBbBarThx4oRooCkrK1M9SDlkyBAMGTJEUpafn4+TJ09Cr9fDYrGoRoaqrKzEjBkzYDQaUVtbi+XLl0Oj0eDhhx+G2WyG0+nEjBkz6o2EK7/HL7/8Elu2bIFWq0ViYmJI+be8Xi8ef/xxWCwWOBwOzJkzJ8B/UsDbb7+Ns2fPijnCxo4d2zCDxihekCmkBQUFinQTJkyQ0KlFLA0Gwg61nDchhCQmJkrKvv76a8U2pk2bJqFTSmeTmpoaklzydDZKOXaXLl1KZs6cKSmbMGGCYnunT59WNUoEo+DLPSVWr14dkM5m6tSpAR4iap4SAuQeAnIZ1dyrJk2aJKGbO3dugEFE8JQINmqTnPelS5eCqkcICeiLgQMHKtLJI/gGO1Ua5W0uV2jVbPNqm4eNAalnqROsZ4LShqtcplC9FYSctv685cYPvV4fsmeC8LUKVR45vVarVdzsVdt8VkNDSyK1ZysYX4S/m0wm1fQ6jV22hTKu5P2jtmxvtCyNqSRXlNWCfsjDCoeSmiUUyPmoKfJy/na7PUAZD/XgnjxIPcMwiql05GVqfARjhfACCTXPrbxdhmECQlDX1tYG9FFTU8qo5SUW7lswgrjdbsXnADTeGSCUU7dy3mpjUs1o1JCMjbLyrVmzRkyjkpSUpHqeZdGiRbDZbLBYLHA6nY1OEQr89hZatWqVqL8Ja/41a9bg448/hk6ng06nU43U+tprr6G0tBQGgwG1tbWYN28eWrRogQceeEDUodRy0hIVxfWbb77BqlWrYDQaERUVhW7duuHjjz/GzJkzxbQukydPBsdxyMvLg8VigcvlwltvvaXIo02bNnj++edht9vh8XhEf7ZgB1tubi5efPFFWK1WeDwejBs3DsOHD8fZs2fFlDKzZ8+GzWbDQw89BL1eD6fTiWeeeSao9gWsX78eX331FXQ6HaKjo9GuXTtFujfffBMsyyImJgaVlZV49tlnQdM09u/fL+bYFdLrBPtV+Pzzz7Fx40ZRh1LSgdSe15w5c3D58mWYTCY4nU68/PLLijzS0tKwYMECnDlzBhzHiTpkQzKG7Cmh5u5SWVkp5knS6XT429/+BgBimc/nCzkJ8p49e3D8+HFwHIc+ffoopj0B6jKfCxYnq9WKm266CYQQLF68WFxO3H///SHx3rRpEwoKCsDzPPr3749OnTrhwIEDOHToEIC68MRqXhYA8Mknn8BkMoHnedXQx2oPPTc3FyUlJWBZFtdddx3S09MVXV/U6gu8OY7D2LFjVenWrl0LiqLg8XgwduzYBvfV/HHx4kXs2bMHWq0WcXFxGDRoUEjWttWrV0Or1cLhcGDixIkhLdvOnz+P/fv3g6ZppKamol+/figtLcXatWthMBhgNBoxceJEMAyD5cuXw2g0SnKECR8Dp9MpJtb+7LPPUFNTA5/Ph7FjxyI5ORkHDx7E+fPnwfM8evfuHdxByKC1uV+h5rrfqVMniQK3b98+MnnyZEnZzJkzQ+IFBaXQXzkXeKekpEjojh49SsaNGycpk59zqk8BDtY7wOPxEEIC0+Fcd911ErqFCxc2yFPAhQsXJHVpmg64bwFKRgp5jt1Vq1ZJeAv/rlq1KijlXA3yvigsLFSl9ffyIISQhQsXSuoOHjxYIluovAkJNMacOnWK3HHHHZKyxYsXB/AePXo0yc3NlZSlp6er8gn78Q011335G0bwNfOH2jq7sSAqfmE+ny/APC9fZ9f3JlVbP8vrqt2P2n2TIBYDct4NfTXk9yXnIfAWZBL+FfReeXljUV/ASHkwFLnOreRlESrknu4MwwSMAZ/PF8CDpumQ+7w+hM1TQq7c+Xy+AIU6VAW7IQidI1ewfT5fgDyheBw0pOQKg1YYrPKXiVJfBAv5wxX6jCgsp5SWSXLjh5oHhCCTcC9NNRipGaaUIB8H4TBWydtgGEaRj9y66XK5Ap6P3MgVCsI2oWbMmIG33npL3FC87rrroNfrkZubC6vVitraWlWFXw0TJkxAQUEBOI4TI7z6v+mEf2fNmoUPPvhA1Fn69OmDKVOm4OTJk2KO3QceeCBovtHR0Rg2bJiox9xwww0A6vSwI0eOAACysrJUz1K98MILeOqppxAVFQW3242JEycCCJwA8q8JRVFo06YNbrjhBjidTng8Htxyyy2qdZXe6AsXLsTs2bPFTVMhR68ct912G959912YzWY4HA784x//UKRT4/PMM8+IhgGj0Yg2bdoo1lfCPffcg48//hgxMTGoqanB9OnTQ+a9fft2aLVacdviueeew6JFi0TdsUuXLpg8eTJOnToFm80Gu92O++67D06nEytWrBDH5NNPP43+/fujZ8+eYFkWHo9HzLM8evRoFBcXi+lR65NJQKOOb7jdbhw/fjzA7eTChQvQ6XTQarX1nlf6+eefAdS9JYUzM2fPnhXdba655hrodDowDIOysjIQQsRInna7HXl5edBoNEhKShJ32M+fPw+dTgeLxRKwN+SPw4cPg6Io+Hw+8fjEiRMn4PV6wbIsunbtWq83R0lJCSiKQnx8vOoBQzUcPHhQzCvbs2dPxUOEGo0GTqcTdrsdPM+LkYOqqqrE80vNmjVDXFyc6sMtLCyETqcDIUQxipOA0tJSAHVvc6F/z5w5g5qaGslzCCcEmQUZvV4vmjVrpnovpaWluHjxomgBFZx4i4qKoNFoEBMTU+8e2oULF8SALmpRmNTAcRxKS0vB87zYPw1NqEZ5Slit1gBlbcCAAZKyvXv3KtZdsWJFgFJ49uxZSVnz5s1VlUKlss6dO0vKDhw4QAgJVIbfeustCd39999P8vLyJGWdOnUS68oh5x3K0YQ33nhDUnfy5MmKMpaXl0vo1PLcMgyjyEfupfHll18q0n399dcSujFjxgQERklKSgr6/kKB3CAyZswYQkhgnwdrHHK5XGJ9/7689957A4wSwcLpdCrybgiN2thV0hHka/fLly8r1pVnd3c4HKiqqpKUhbrJWFNTo8hbrgyXlZUF8JZvzAq8SRAf7lB0QuFrIEB+zwLk96LGQ003Eu5RuGc1PkrPQZ62Ry5LuCB/DoKM8jd/sLqVoPPIn7f83uXPoD40Vq9r1ISSK+0cxwUodmpKqlzp9vl8AbQNWdkaajNY3gzDqOZ7Ddb7uLEyqhkqgj1W0VCOXSIznMihdJQl2OMtTYVcJrXnHWz/qtEpPe9gEcwLVa1iyLjtttuIVqslZrNZXJK88MILBACxWq1Eq9WqOiweOnSIaDQaYjKZCADy5ptvEo/HQ6Kjo4nZbCYajYbcfffdhBBCWrVqRYxGI9Hr9eTaa68lhBAydOhQotfridFoJBkZGYSQutOwNE2TqKgoYjQaVXkL+w0Wi4UAIO+//z7xeDzEaDQSi8VCNBoNeeyxxwghyns8PXv2JFqtluj1enFZGiwE3sJ9f/LJJyIf/z0slmVJQkICMZvNRK/Xi060OTk5xGQyEZPJRDp37qzKZ/78+QQAMZvNBADJz89XpBOccIW+EPbKbDYbMZvNhKZpcuedd4Z0j8HiyJEjBACJiooiAMi7776rStuqVStiMBiITqcjPXv2JITUxUfU6/XEbDbXG/Zt5cqV4pgEQH744YeQ5MzOziZ6vZ7odDrFUGdK+NPGlGgIVyLNyZUGucrOmjUVf5b7kcupJncw99Oo0SJEvvFfr06YMEFSdvHiRbzxxhuSsk8//TQkPv5167M2DRo0SEJ7+vTpoCdCfn6+pO7gwYNDklEJjz32mKTNb7/9Nqh69Zpj/doT6LKzsyVlXq8X99xzj6QsNzdXsb09e/ZI6IRthXC+XymKwuuvvy7h88UXX2D37t2SsqeffhoVFRWSMsH6qyRPUlJSQF8MGzZMUlZdXY1Zs2ZJyjZv3oyvvvpKUjZt2jRVZwWl+2kIjZpQ8hytLpcrIFftuXPnAsoEc3ljUJ+XhZxPXl5e0O0eO3YMgPRwX1Mh+PsJ+OWXXwBANJOHa9AWFhYC+E32S5cu4eDBgxIaed8IkPfRyZMnJW2FC3I+x48fVyyT5zb27zM5ysvLA8rk91lYWCg+S+Gejhw5grNnz0roDh8+HMxtBI1GbewGMyCUaH6v1WUofARa+b/h5K/UdjgHrn/7jZX/Sj0bpb4ItgxoWu4npWerxidcaNQXKicnR3JtNpvRpUsXSVnbtm0DyuS5XkNBfZu13bp1k1x36tQp6HblMqp5tIcCuRe6sIEsX6aEAv8lr/B/uezp6ekBR9Xlz0qA/D7luYabCmGgymMP9ujRI4B37969A9L9CLErlPpKTgsEyt+sWTNFPp07d5aUNWVMKuGqMEpcCeU1WEWzPvzeRon/NvxZjBLB5gG+YkaJoUOHBrxt5flV/VNY+kOuDAuKY7Do3r27WLc+lxMlRXPbtm0S3qFmm2/VqpVYV/DjGzlypKRNtc3DrVu3SuhWrVqF8vJySZmQ6Kup8NfVQtkrczgcEnmEg5oJCQlimdLXQQ3yF5rw7hYiRwm/N954Q8w1JfzGjx8PAIiPjxfLWrduHdQ9C3wWL14safPHH39Ebm6upOytt95SzAMM1MUqFOg6d+585YwSW7dulVy73W4xF6yA/fv3K9aVJ4/esGFDSLz9FX632x3g6VAfNm/eLLmW572VQ96B/sYY4di2/L7VlFy5pe+7777D0aNHJWXyfg0HQnlZCYYAAZs2bQIg9ao4d+5co/UOQRZ5v2/ZskU0iggQxoX/8w3VYLRx40bJ9XfffReQL1lt/JWWlkq8zuV9o4ZGGSWEzNsCKIoKOqdtYyOT1idLsPAPxkiIeg5YAcEMRvlxALU2lejC3RcCGrvMksujtlXRmPb968jvU6vVKpY1tn01Pkr9q8an0X3YmErx8fGSa6PRiMTEREmZmmevPP5asPHYBMgPf4USbFI4wiy8YVNTU0PirQR5X6jFeJMHgkxNTUVKSoqkTH79e0Puma52L02F4Lntz1c+furzkg8W8jGYlZUV8MzVjrU3NhF7o40SLMtCo9FI3mqFhYWgaRparbbehyH4jLEsG1SCM394PB6UlpaKrvuCO3+w4DgOhBBwHBcyb+A3/zb/iV1YWAiNRgOLxYLY2FjVuoWFhdBqtWIASwDieRuWZUPST64UKisrxTyz/i87hmFAUVRIX476lHiPxyPGehCeQ0VFBdxuNziOkwx0lmXB83yjTtIKp7cJIaLsBQUF4kldpeCegtxOp1PUc9PS0oI7yhKUg5IMU6ZMIZmZmaRly5b1+pUp4eTJkyQtLY20atWKpKamkvfff78xIkgwc+ZMkpiYSLKyskT/vi+++ILExcWJOXbz8/PJiRMnSFpampgDds2aNYQQQjIzM0lmZiZJSEggc+fOJYQQ0q1bN5KWlkaSkpLIUtts/wAAIABJREFU3/72N0IIISNHjiRZWVkkOzub9O/fv8ly/1kwePBg0rx5c5KdnU1Gjx4dUt3t27cTm81GsrKySFRUFMnLyyMXL14kSUlJpEWLFiQxMVHMsauE6667jjRr1oxkZWWJvCdNmiTmOq5v/K1Zs4akpqaSVq1akeTkZNVgnOFEWCLHqiVGVsKcOXMkdbt169YYESRISEiQtPn999+TUaNGScrmzZsXwHvkyJFk586dkrL6AnTIy2pqapos+9UOj8fTqHNBAv72t79J6s6ZMycgcqzwcpKfh5KfDVN7DqWlpYq8R4wYIaFrSuTiYNEoo4SwbBEQimFAvsxqKGJpMFDKpyv/POv1enH5IRglTCZTwDIilPQxoSiu5I/f7pMgWNmbuo+kFCXWPzouIUQ1cmywvNXGXzC5fMONsMSUCGVSyGnDkeZRbr2xWCwBnRkVFSUeGhMGN03TAUaNUCxtoazp/wwbnEpoSgQgpfpC3Dzgt+egNtCDnQBq409ev7GGhlDQKKOE3W7HoUOHQNM04uLi0LFjx5Dq7969GxqNBl6vNyze3W63G3v37oVWq4XFYhHdULZs2QKj0Qie5zFw4ECRN03T8Hg8Iu/NmzfD7XbD6/ViyJAhiI+PR35+PkpKSsDzPDp37oz4+Hjk5eWJ+xGtWrUKcP2R48CBA3jvvfdQWloq8S37o8FxHNLT03HffffVm3tYQGFhIc6fPw9CCDp27BhUfmN/bNmyBSaTCR6PRwx4s23bNphMJni9Xlx//fWqdQsKClBYWAhCCDp06ICEhAQUFBTg0KFD0Gg0SExMrNd9aMeOHTAajWAYpt7ApOFCoz4PX375JdatWwedToe4uDgsXboUO3bsEKPtAHUhis+cOYP77rtPDLn77bffgqZpvPHGGzAajXA4HEhISEBOTg4mTJggRvq57777VPMVLVy4UGynefPmWLRoEXbs2IFly5bBYrFAq9Xiww8/RH5+PhYuXAiLxQKGYdC7d2/wPI8FCxaIqVViY2PRtWtX3HjjjQF8li1bhoKCArAsi+HDh+OBBx5A27ZtxeTODaFPnz6qm9tXC5YvX45+/fqJRzyIilXugw8+wLFjx8BxHAYMGIAnnngCK1aswKeffiqm8fnoo4+wY8cOvPjii2Kfb9iwAcXFxXj77bdhs9lQU1ODfv36wWg0YsGCBYiJiUFtbS2Sk5PRoUMH3HHHHeIYePTRRzFixAgsW7YM+fn54kvxscceQ3Z2dlBRXE+ePIk333wTZrMZLpcL/fv3h9PpxKhRo2CxWGC32/Hee++hVatWuOOOO+BwOOB2u/HUU09h+PDhePzxx3HmzBnwPI+bb74Zjz76aMOd2hjFCwpBMlq3bi0p27ZtG7nrrrskZbNnzw4IlNK3b1+yb98+SZlWqw2aNyGB6Wxyc3PJ8OHDJWUvvfQSmT17tqRMLcdudXV1kxTxXr16BdS/mn89evRQvRe1YCXysurqapKdnS0p27RpE3nggQckZbNmzSLvvvuupGzQoEHkwIEDkjKNRqP6vIPFzTffLKn74osvkldeeUVSNmzYMPLtt99KyuLi4hrNu1Ebu/J1sRAw3h9GozFgbWs2mwPSidhstgADQjBrXf83qXwvymKxBJQJSQv8YbVaFdtuil63a9cu/PDDD42u/0fg4MGD2L17t+Lfgu0Lg8EQ0L8GgyGgj00mk+IYkBuDwqHvyPkojT+r1RowTtXiLQaDRk0oefAOQkhAdB6e5xUDt8jLXC5XgANnMBFniJ8+IqdnWTZAHp/PF8D7SuSvfe+998Le5u+BJUuWNKk+z/MB/UkICShjGCbgsKjX6w0IEBOOKMPBjD8l3k2JZNuoV/H333+PzZs3Q6vVigEfN2zYgNWrV8NoNIIQgv79+yMnJwepqanijvi0adMA1O2I63Q6uN1uTJ48GampqXjrrbdQW1sLn88XkI7TH5s2bcL3338PnU4nurDs2rUL69evh16vh8FgQI8ePfCvf/0LCxYsENfzTz31FIDf3pC1tbW45557FHlYLBasWbMGxcXF4HkePXv2DLpvhFO0fzZcvHhRsVyv1+PTTz9FXl4eCCFiBNU9e/Zg8+bN4urEbDYjNzcX//nPf2AymcCyLAYNGoQePXogLi4OZrMZtbW1YvrOgoICmEwmuFwuPPnkk4iPj8eKFSvgcrng9XpFT/f//Oc/OHXqFAj5Lb9vsPjoo4+wfPlyWCwWuN1uTJ06FTzPw263i+lspk+fjqioKLz//vtiWOaRI0eK9c+ePQtCSNAGjUYlre7bty/69u0rKRdcOfydPm02G+bOnRvQxsyZMwPKHn/88aD433jjjQFGBCGegv+XLjY2VpG38PViWbZBVxKGYcBxnLjs2bhxo3j8pGPHjorpecLl4Pp7Q01uQoh4lMIf1157bYB1jaIoMSC/8Na3Wq148cUXA+orPRug7ivC87z4bG6//fYAml27dmHLli2gaRppaWl48MEHcfr0aaxYsQLG/2/vy8OjKLb239knM9kghCSsIYEIyFWD+BMQEBAELiCigqggIpt8ihBZRBAuykUWAReED1FBRa4CEdlEICEgqywBRHYIWyJLWCTLZPap3x+h+nZXd890z4Le7/I+Tx5IpapPrd11Tp06r9kMk8mEcePGcZFpDQYDVx+tVispm4612+3mVBoaQpvfFwGPP1RpeX5Qv359gQK3cuXKcD1aFvRkPSEhQSD7xx9/lMw/duxYRUaJ0tJSRYp4eXm5qGy7du3+dCNDMD/t2rULeTxq1qwpeOayZcsUl920aZOgLKXxoSHW+F4UUmOTkpIiSMvPzxdRGsl5Snz33XeCfPHx8UH3QdiuoyrluQ0n6NuClS2nSLMn8nKe6kq/Mndv8wrB9qeauOhynL987xZ/oMYPms9kMokMEHIHxVKOAcEibDNCKY1KJMDyyspFPGU9Jdg6UyilZlFzG/a/AWwIbTWGBXbM+CGxlby42BDaUuHB5YwNofIs8xE2Opt58+Zh7ty5sFgs8Pl8sjQqSkEk9qtSaQCwdOlSLF26FBaLBYQQdOvWTfKZEyZMwMmTJxEdHY3S0lLJvT1QaYYfN24cR2dDvSymT5+OX375BVqtFg0aNAjpTSaH+Ph4tGzZEnFxcfD5fPB4PNi3b99fytghNw7fffcdPv30U84IJXc4L4VOnTph2LBhcDgccDqdnN4mtZgWLFiADRs2wGAwcF4bX3/9NRYtWoSoqCgQQpCZmYl3330XbrcbZrMZdrud4/Jl8cILL2Djxo1wOp2w2+2qqI9YhCVIi1wHA5UEwyaTCU6n06/1TgrHjx/nrEuZmZlITU3F77//zsUkqFWrFpo2bQq3242dO3fCaDQiKioKmZmZACo9OoxGI3w+H8ezpBTnz5/nXI+aNGmi+Gyiffv2Aa/WszAYDJg1axaGDx/ud2uTm5uLrKwsxdex1aBdu3ai6/wUJ06cwKlTp+Dz+ZCZmYm6devi+vXrnIEmMTGRM1L9/PPPnCNy8+bNFSnyNM/Ro0dRWlrKuQlpNBocP34cBQUF3BxgLydSeL1e7Ny5EwaDAWazmZsDcrKU4LfffuOsfA899JAyOpygtS8FeOmllwTK3qRJk1SVh4TyKZXGKqRHjhwhvXv3FqRNnTpV8Gx/XKkVFRVBn9CrNUpQDlx+vaT4dPnYt2+fiFIo1B85o4TH41E0DhUVFaRx48aCtHXr1inut23btgnKxsbGSsqRA+ulwadTokYNNfRDN2/elJQdiGM3bFs+KZAIBxWkYJVNKS5VVi/y95ZSQ28ZLKKiolBcXMwp02zfyNWPEIJmzZqhrKwMzZo1Q35+fkTrqbQv6A1uPtTo0SwzhtrjB3+ylRo25OqjxrE5omaqO+GZAIiVTboXDlZ2pI0NRqMRFRUVArccfmgrf8Ew+en79+8XBfkMN9RQyrBGADUk5eyCUuut4E92MAFG+S8S/mIK9IyIfqGGDBmCffv2wWq1ory8XLWy16NHDy5sFZ04Q4cO5a5g0LgVY8eOxeeff86ZSZs3b47XXnsNp0+fRmxsLOx2O1588UXFcmNjY9GhQwdcvXoVXq9XFI01VNBwZGyAxb1794quU5Dbe/6zZ8/CarUiKSlJEKY4Pz8/onetzGYzevbsyV2hoFdWsrKyuDiHFosFcXFxePvtt/HBBx8gKioKdrsdnTp1UiynTZs2aNmyJdxuNxwOB/r16wcA6N69Oy5cuMDpUHKYMGEC5s+fzxklWrVqFVK7U1JS0L17d85bhnVkkIXiTaVKBNprBgOPx6Mqf0lJCbHZbMRms0WsTiwC6VBdu3YlhBABJxQhhFStWpUAIK+99pro76dPn+bKX7p0SSRz7ty5EdOhKMrKykhZWVnA9t+6dYvY7XbidDoJIcr6nJ+noqLCrxyp54VjXK9evUrOnj1Lzpw5I0i32WyCA/xAsiJqlGARasNZDlV/YA0i/ki9wolAC+ro0aOS5ejfO3XqJPrbrl27uL8fOnTIb/lILCg2rxweeughQb5t27YF6K1/48CBA4KyderUIYRIE9+FG3l5eQLZNPCL0nbzEdEtH4tQtyZqyrMcv3Khoe8kDAYDGjduLPm3M2fOYPXq1ZLhmFu0aIHvvvsOJpNJcEu4qKgIhw8fxrVr17ijiXBDie5Jbm9LWU5bKdoZObCcv+zvkQQ7V/hcwzqdTpWR6v+s74xSzt87CeqpLaXop6enc/HhpfDss8/iySefhN1ux/PPPw+NRoPu3btj3bp1KCsrw4IFCzBw4MCwsIfwQVRYZtkFrcYocaf4fdXKZv8WsD/Ufh6VbtvU7HWV5lVT/rPPPiNAJYesRqMhv/zyi+KyoaB9+/ay2yrKExzsNqZnz54kNjaWbNq0KWDejz/+OGxbvsaNG5OoqChiMpk4nlspjB8/nmi1Wo63V0rfk8P169dJbGwsiYqKIhqNhgwaNIgQEnxfqZkrFy9eJNHR0cRsNhMAZPjw4YQQQurVq0csFgsxm83k0UcfVST3jupQkQLbSXfC+CAHfwsqNTU16PoZjUYyceJE1eUeeOCBsBgl/gyoPYyNNJSMW1BbvnvuuUdk2+/Tp48g7fLly5gzZ44gbfny5ZLPO3PmjCAfn2yL/lBP8eTkZEG6z+eTjOdGJD7N2dnZgrLjx4/H9evXBWkdO3YMpksU4fz58ygrK1OtS0ZFRWHt2rV49913/W45+H+j/z948CB69eoVXIVvg98/Uh7kVFbPnj0FeS9cuCCilFmxYoVkebZdhHc0oASdOnUSyGEpRimWL18uyPfWW29J5qOhp+lPbGysogPeoBYUy1PqcDhEHKcFBQU4ffq0IE3uVJ/Nx/4O/Ht/zvIvyXkGSw3EwYMHRXJYvlc1/LzBYM6cOYry0YEbNmwYevfujccff1zgh7ZhwwaRwUCj0eDQoUM4deqUoP3Lly9XFcCTDyV6EZXFcigXFBTg2LFjgjR2DGh5qZeimsNYdtxYHmgKyqlMwXISU1CjCJVPvdkD1SeoBSWlVLMrV2o1+3On8fd7uMDWW+3peTgwefJk7v/+2knrtWDBAnz11VeCv02ZMgVdunQRhdK6cOECMjMzJUOdsc9QilC8RvjuQ7Q9kRpb1nggV+9QjVMR+UI1bNhQ8LvZbEajRo0EaQ0aNBCZiOV4XNl8UhOCXpVg6Uj8ce+yYL0QGjZsKKq32qCdwaBLly4AAr/tVqxYwV3D5n+daDvo9XA6yJTRQ8oPTuoauxKwl/T8bfnY2BsNGjTg+I5pnmBcpZQsQta6yc5RCtbbglpeWVB6HSqbpS2SRRh1tojAbrcTu90uSKuoqBClsZAzVLhcLuJyuTjviXDDn1GC//PMM88EfFb//v3JvHnzJP/mcrlU161ly5Z/ilGioqKC2Gy2iJMr2Gw24nA4AvZNRUUFcTgckiEMWEjNP38I6gvVpUsXbrtEfdHGjRsn2PfabDZ8++23gjQaoZQFaxjgK9Fms1kUCzsqKkqgE/Tv319QnnL6SLw8YDAYYDAYuCvWrOxXXnkFQOUXkTWShBPZ2dncF5vwlHLCexsXFhbKel0bDAbV26dgt28pKSlcX8gdTAPAqFGjBH1JD9OjoqJgsViCirXH5/eVk037z2KxwGQy+e0bcpskgh9H0F8/Ss0/fwhqQfG5S8nt2Gssn2l+fr7oot26deskn8eGLKbcroBwErCLhP6N5UmVCjQppy+xAR4pFy7f2zlSIZUpXzC/bvw6Pvzww34PONXqf8EsKKfTiStXrnC/Hz9+XDZvdna24Pddu3aplsfHjRs3BB4TcrLljBpyeZWkKS3LIqgFRffRVIBWq5XkkGX323LBU5Ryu7Kg8tmAMGqCp7BfgDsRXIaCf1X/iSeeQEZGBsaNG4fc3Fzs2rULVqtVRLStFvy3ajARbdUsWnZehBpS7U4bjMKBoHz5jEYj3G43N1hGo1EUzjYxMVEUDpnlUZVLV0qPQzucDferhquWNXJEIk6EHHr06IEPPviAC8IJADNmzMCMGTPCLmv27NlBlVPzgqF0qHReqGXpYCEXKvuvjKBiStBzJ8pzS+/5FxcXw2AwQKvVcjEYDh48CIPBAJ/Ph/vuuw+A+B4QUGnyLS8vh9frRePGjSW/ZkQmHoDH48GxY8eg0WhgtVqRlpYmyitXFqg8w6DnLffeey90Oh2Ki4tRXFwMQgjq1auneHAfe+wx2dgMwcJfvAelCPS2DyTj+vXrIIRIvhT5fXvo0CHodDoYjUbFTCXsM1hcu3YNPp8vYiTaausTqGBYsHjxYlKrVi1yzz33kAYNGsi6abBBC/2ha9eupEaNGiQ5OZkMGDCAEELI8OHDSY0aNUitWrVI27ZtVdWxqKiIJCUlkdTUVJKQkKDIJ46Pl156idSsWZPUrFmTu9fEQqmVT+3P6NGjVdWVj5iYmJBcj3r37k3S0tJI3bp1ydChQ1XJzs/PJ4mJiSQ9PZ3ExcWRy5cvk9LSUpKQkEDS09NJQkICycnJkS3fo0cPkp6eTurVq0deeeUVQggho0ePJsnJyaROnTqcf6RSXLx4kSQmJpJ69eqRqlWrcrEnGjduTOrWrUuqVasma1lVgrAtqPT0dMEAUUJoPtQsJkKUBWm5du2a4ueNGjVKUFYucqzS+khdhIvUggJAOnbsKJClpC+VPltuQdntdslxUIq+ffsKyk6ePJnMmzdPkCa3KJRy7F6+fFlxfcaNGyco26VLF7Jx40ZBWlxcnKo28hG26xus3kMDpxBC4PP5OJ87tZ/RQPn9xeRmIUVlEgrudBzznJwcaDQavPPOO4p8AsNxZSXU6Lhsn/PpbGj9A0XwDdRONfRDbH2sVis3VwPVRwnCtqDYIBv8gVDrl8WH1MKQkxMIbF62zmrxZ4Vinjx5Mues+fjjj6N9+/Z4/PHHcf/993P9PGHCBOh0OjRo0CAkWaG2kR1zwtNN6NjKLXy6oMI5B1h4vV7RGWAoFzVVGyWIjLJWUlKCn376CUajEXq9nqMjCeWZ+fn5XJCWJk2aoFGjRjh16hR+/fVXaLVaJCcn45FHHlH1zM2bN0Ov18Nut6Nz586K6whUUr4cP34cGo0GNWvWlHRviYRRIlhUrVoVhBDRTVop+DNKFBQU4Ny5cwAqXXrkgk3KYcOGDTAajbDb7VzA0U2bNsFkMsHhcPgN5nLixAkUFRWBkEp+3xo1aqCgoAD79u2DXq9HYmIiF9lXKZYtWwaj0QiHw8FFt929ezccDgfcbjdatWqlioidD9Vmc41Gg5UrV2LNmjVcKNxp06YhLi5ORD1y8eJFjB8/nuM4Xbx4MQgheOWVV2AwGFBeXo4xY8bI3jJ98MEHBf5/Pp8PGRkZyMjIEOTbvHkzlixZAr1eD6PRiPnz50suJrfbjZUrV3JhvNLT01W9wXNycnDgwAFoNBqkpaXJ+ov9VXDz5s2wPIfyJXu9Xjz00EPo378/vv/+e6xbtw46nQ5VqlTB+++/L1n26tWryM7ORnR0NGw2Gzp27Aij0YiVK1ciOjoa5eXlyMjIQL169TBy5EjYbDY4nU4MGjQIbdq0wYYNG3D27Fn4fD5cvXoVffv2RXp6OtLT04NujxQN0apVq+ByuWC322G1WmVf1AERjOIFRimkkWKo0YEqyyydyKRJk8gHH3wgSKMBMQLBX4CWuLg4wTN/+OEHyWeMHDlSsWVLSbtv3bolyhNJo0Qkf9QaJdg0NvgMHacXXnhBkO8f//hHWDh21QTsCYQlS5YIZFgslqCfFRaOXfq7VqsV6Eqs0k+Z7tg0JZDSw8jt3SrrIyb3uWbrEyqPayhcvP8pCNRGOh5s9F46NlK8xmxabGysqHzVqlUDyg3X9RvWUEEPqINBUDNCaQAUlt6krKxMpECWlJQEUwUBWB1BTqlko5GGQlsC/DUCv0QagdpIF46cUaq8vFyQbrPZRFFeKRUsH+HarioBe1GTH/VILYJaUNu2bcNPP/3EceympqZKGgG+/PJL3HfffbBarXA6ndx142vXrsFgMKCiogKvvfZa0JWn8vLz8zmWD6Ay2qgUZs+eDYvFAq1WC7vdrpiGlGLt2rX45ZdfoNFokJGRIfmF+09dZHL1NplM+Pbbb3H06FGODhYAduzYgQ0bNkCv1yM2NhYNGzaUnANffPEFGjZsyOlLEydOBFDpGUNDn2VlZSEpKQmzZs1CWVmZgApn6dKlOHbsmEB2uPHCCy+gqKgIdrsdTqdTRAWqBmGhs5GD2+3meE/tdjuGDRsmm3fRokUoKSmB2+1Gt27d0LhxY/z44484deoUvF4vmjdvjlatWmHPnj3YunUrDAYD6tSpg2eeeQZFRUX48ccfYTQaYTQa8cILL8DpdOKDDz7gSLSl4t1RzJs3D263m+MlqlevHlasWIHCwkJ4vV60adNG9iIai5deeino27F/Jl588cWI1JsQwhFHl5WV+Z0D4UZ5eTk++eQTmM1muN1ujBkzBgDw/vvvcy/0N954QzY8wOrVq3H69Gl4vV60bdsWDz/8cGCXpBD1Ob946qmnBMoedZ9hI9msWbNGkM9oNJLbCz2gMkwIIdWqVROk7d69m3Tt2lWQNnbsWEKIWJldtWqVIF9ycrKsHCXgR3n9T/rZtWtXCCMtj379+gnkvPHGGxGRI4XOnTsLZE+fPp1MmzZNkPbII49Ilr169arkHAhkBInoySS7JWKVPwpWIVVqqKBgvdoNBoPIAEEVYVaZZa+K8OusVuElt7clkdqaRAqRrDNrILqT3vzsHDCbzaI5KDfX5A6LIxKkRSmkFFIpsNQzao0FbH673S4yiMjJZhVS/rOIyt0w7exdu3ahXbt2qsr+WWjbtm3IFwH9gZ0D7O+RBDsHKioqRPNAziimJuotHxG1+7777rsoLy/nDvXGjx8vma9Hjx4YPHgw7HY7XC4XdwV+9OjRHB0kvYb+0UcfIS8vD3q9HjVq1AAALFy4EIsXL+a8NFq1aoVZs2bBbDbDarXC5XLJxl976qmn0KdPH3i9XjgcDgwaNAgA8Nprr3Ecu8HQ2eTl5eHw4cP4/PPPceXKFWi12uCvBIQZXq8XKSkpGDRoEHelJlKYMmUKd1haUlIiy3MbCcyePRvR0dGIjo5GRUUFsrKyYLfbsXPnTo5nedasWZJlk5OTMXLkSJw/fx6EELRt2xYAAo5h2IwS9Bq8wWCAyWRChw4dJPOR2/f/2U/qsWPHUFZWBrfbjWbNmkkqimon5Pbt2zkSbRqRZ/369dDr9bDZbKo5fy9cuIDDhw9Do9GgXr16shGS/ioLJ1w4efIkTp8+DZ/Ph6ZNm6JWrVq4fv06du3aBa1Wi2rVqvnl092+fTvHGdW6dWtV/XPs2DEBx27t2rVx5coV7NmzBzqdDikpKXjwwQdhs9mQk5PDXXalC2D37t3chdhQY4MoqnfImt9t3H///QIFLjc3l/sb5Tri//Dx888/C8rGxMQQQkILqdyzZ0/BM+fPn0/mzJkjSOvevbuqZ4JRUh0OR9D1+0+B1+tVZBwqKiqSLD9s2DBBvnfeeUex7LKyMsWGqYyMDEFaQUEB6d+/vyBt5syZIfZGYIRty0f3nDTAJf8QNZA3MHvgSg0FJIQ3PXvQ6Ha7VfHsKsF/6pmTGihtI6sHy5VXQ80aite33W4XBaWR06PDibAtKNqhRObk3B/YU3L+4AS7qNiBczgcogEKlfM30ly8fwUoXVBy+dg+VkNTo6Z/2QXt8XgkuZcjjnB96hYuXEhq1KhBGjRoQOrVq0cqKioUl7Xb7aRRo0akbt26JDk5mWOZCIV94aeffiKJiYkkLS2NJCUlkYsXL5Jz586ROnXqkIyMDJKSkkJWrVql6pnPP/88SUtLI+np6aLbs/+X0alTJ1KrVi1So0YN0rt3b0JIZSiCunXrkvr165P7779ftuyOHTtIjRo1SMOGDUlSUhI5deqUKtmPPfYYqVWrFqlZsyZ57rnnCCGV20gqu0WLFoSQSvqeOnXqcCEYCCFk48aNJCEhgdSrV49Uq1aNFBQUBNN8VQjaKHHhwgUuGAs9u3G73dDr9YIvyoULF2AwGOD1elG7dm0AlfeKgMq3CBufm7fQuf+zX6jCwkJotVpUqVIFFotFVUAWn88HQgjcbrffAPoOh4MLTELrDVS+5TQaTcBoQOfOnePO16g18uLFi9DpdHC73UhNTZVsMw0SevPmTRBCuPDKcm26ePEigMrzFHqmcv78eej1emi1WoFsg8EAj8cjGAeNRgOPx8PJKSkpQVlZGXw+H5cmB8pQQR1o5cbB6XRCr9cL+jxQXwSCx+OBVqsVqBMOhwN6vd6vQ69cP/7xxx9ckKBg6sMXoBoJCQkipfCxxx4TKYUTJkwQpC1ZskTkmdCzZ09VsrVaraC8mpDEn376qaDsyy+/LJuXn0/Oc0POaPL8888L8q1du5YsWrRIkcfArVtCoNuBAAAgAElEQVS3BPmSkpJk68gGXyktLSVPPvmkIG3Hjh2iGA5z5swhu3fvFqQ9//zzxGazCdIoz60UdDqdIK/UVRZCCHn77bcF+RYtWkTWr18vkq0Ger1eNP9atGghSDt58qTi5+3YsUNQ9r777lNVHz6COtiV8u5m0y5duiTyGC4sLMTly5cFaSw9TSCEomheuHBB8HtxcbFkPvZAUG7fLye7sLBQJIdNY/tBrk7+btuy9bxx4wZ3s5bi2rVroj4uLi4Wpd28eVMkmx8xlgWrM8n1JcttXFxcLHqu2jnAN4DJPUOOH0oKdMdEwXLuqkHY6GzYk2VCiCjN5/OJ0oI9kebLUQrWUBKqbDmwz/V6vaI0pUYbNXX0+Xyi50rJ9nq9ogXh8XhE46rGsCRnQJAa72D7wh9CGctwzklVC4pOXvYOv8lkEjH/NW3aFO3btxekdezYkTtwo1ATe4I+lw81l8F69Ogh+F0upkRMTIzg7UfpY/ixFCwWi2zUJJYxsFWrVoKwywC42Aos2Cv5bH/x0aZNG8HvaWlpojAEzZs3F41Nx44dRYecbdu2RVpamiDN39iw5eWCWrJ93LFjR9G8YMclEOghPf9lSimC2DxKwPaj2lgjfIQtSEsoCPWZkajTnQBbb7l20CHi/00q+u6dqKOavGrq+J86hixUj4hcowcNGiTw5D5//jwAcK5GdFLk5eUJ8mVlZfllSqA//uJkS5V/7733BOW3bNnCxbWjP++99x5KS0sFaU8//bRINiVlq1KlishbvV27doK08vJyvPPOO4K0bdu2Kap3oH7gQ26iDh8+XCD79OnTWLVqlSBtxYoVKCgoEKTR+Ors+1WuThkZGYLyXq9XcR137NghKDthwgTOIUAJpDien332WUGaw+HAhx9+KEjbuXMntmzZIpoDcuDn41tb/SFsr7jdu3cLfmf5VilYBgilns58WhMlYGlq9u/fj3379gnSDh48iN9++02UxuLEiRMApK9Gs7zBp06dErUpGNaLYEHpeCiOHj0qaveePXtE40PrqHRSszzIcpy2UmD7Y+fOnQCUe66wfM4ARG08deqUaE7u2rWLoxCiYCmXKFjDCWtUkkPYFpS/ty1/kJS+xcJdH51OJ4r0KiVb7bZDqt1KvzyRgJRstt2U0IGPUAPOqGkju2jDMQfYZ2i1WsnxZvPJRf/1t1vwW49AFWXB38Lxt3L0AJGCfzjG/5zXq1dPkE/uYDeQfLk0+m/9+vVFclilu2bNmoJDW5rGwh+PL8sIUbNmTZEc2ka5vlMCtn1y+Nvf/iaqD3tQWadOHVGwSv7vSurIXhRUw4zBxtRj5wRbD3oYT8FeKAXE45acnCw6mK5fv76o3XJxGdkxVxp2+y9hlPAHegYUSSI0alomhPj1npCrj8PhgEajEdw8rqio4LxG5Ajkwt2Xd9pg5HQ6odVqVTkzyxkqwlF3m80Gg8EguRvhw+FwQKfTwev1So63P2NKoHoGZZR47rnnOJcTaiyYPn06NJpKfiaDwQCXy4X169dzE0qj0eDIkSMoKCiATqfjOGznz5/vVx4NvELRrVs3bltFvy4jR47k6mM0GkEIQXZ2NjQaDaKiojjZR48ehU6ng8lkgkajweLFiwGAS+N3br169WA2m6HT6QT3pvj1oe8is9ksIEcAKs3qRqORm2z5+fmcy5JGo+EMBUqRmZkJnU4HvV4vy6Su0Wgwd+5caDQaWCwWaDSVfMN79uyBRqPh4qHv3LkTf/zxBwwGA9c/CxcuBFD59tfpdNBoNBg2bJjfOlI+W9puJe3RarU4ffq0oI6LFy+WNUo0btyYqw81bz/xxBNcX/IJ86xWK4xGI7eYli5dyr3oNBoNjh07BqByvAwGg+zLky6me+65h3seDRcdsI3BuFeAccGx2+3kvvvuE6Rt2bKFDB48WHQXhnWDadasWUiyCSEkJSVFkLZz507So0cPQdq0adPI1KlTBWmPP/64pIySkhJJOaHczxoxYoTgeb169Qqp3aWlpZL5UlNTBfmys7PJm2++KUgbPXo0yc7OFqS1bduWnD59WrLd4cbMmTMFMlq3bi2Zr7i4WLI+bNrVq1cly3fr1k2Q76233lJcx8uXLwfVFyFx7FJQxjo2D0sTQt8MfLABWoIB+wz+V4Qvm62jnOxI0NSwwUGCDUZPIbclYdsk9SamLOh80PABbL5IgJUtNw6Bvgb073J9QfuYPweUItg5EJRph3UzoVs6PqKjo7mGEl5YXqnFKAfC2wL461z2GfHx8aItWFRUlMjFhS1Hbm9b/OlrbJ0Ib6tD/Gx72MmplJhbDnJ1ZCNNSb1IdDqdqD4mk0mUFsykkuoDNk2uL9i+DbQACG/LLQX2udSYQZitpdSYqVl8gmcR9ukK4HQ6OX0kISGBs5zs378fRqMRJpOJc0W5dOkSdDodCCEcmfSRI0c4vzM+u4ZS2ZRPNyUlhbMuXbhwgQvKQl2CioqKuMVevXp1AJVOlNSnkLXw8XHt2jVcvnwZhBBkZGSoelvTLmUH6uDBg9Dr9fB4PMjMzFTVbuDfZyOBSLmLioo4XY1y4ubn58NoNMLlcnF9fuTIEXg8Hrjdbs5Vp7CwEH/88Qe8Xi8eeOCBiBmgioqKYDKZ4Ha7RRZiFnQc+PmKioqg1WpRtWpVv5OfzgH+/GMh9yK8ceMGioqKON5nJYssLFY+uQrl5+cjKysLMTExKCsrw7/+9S9JbiG58oMGDcLZs2fh9XrRrFkzWSbzL7/8EkuXLuXeSOvXr0d+fj5GjBiBuLg4OBwOLF26FFqtFn369EFUVBRKS0sxbdo0tGrVCh06dIDP54PT6cTAgQPx8ssvS8oZNWoUd4CYnp6OxYsXY+7cuRzfkNVqxapVq6DT6USWIjX9JpXn9ddfx/HjxwFUeinMmzcPM2fOxA8//ACj0YiYmBisW7cOOTk5ePfdd2G1WuFwOLB69WrJ2HNK66Omjt9//z0Xbcrj8WD79u04evQoBgwYgNjYWNy6dQvr1q1DTEwMunTpAovFApvNxo1D586d4XQ64XQ6MXjwYAwYMABDhgzBuXPnuEA706dPxz//+U9s2bIFer0e8fHxWLZsGZYuXYq5c+dyQXm2bt2K3bt346233kJUVBTKy8uxZs0aOJ1OPPnkk7BYLCgrK8OcOXPQunVrv+3jtxMIsBVVrKUpAEtn8/TTTwuUulGjRql6Hr+sv6pWqVJFkG/btm2ke/fugrSpU6eSf/7zn4K07t27k61btwrSaORYpfUxmUyCtB9//JHri3BCSjabdubMGdKgQQNB2oIFC8JaDynQ8WaNQxs3biRDhgwRpE2ePFl0L61Dhw5k7969gjSDwaC43R6Ph9SsWVOQduDAAVHk4qlTp4oix8oZRIJFWN0U2JXL7ucjFTWU9fq2WCyS3K6sISA6Olqky6hVxNk2hcPIEixMJpPo0DNU44cS0HFnx4F+tdn6sFsnq9Uq6nc1VEP0GIYPg8EgSrNarZL0OuFEWANdsguKvZhHgrttHxBSVCpS94Kk6sdattQGbmEDgdxpImsWUu2ONMjtLR97j0ij0Yjq4/P5JO9isZY6uShKcmDz6/V60VgSQkRzMhx3sQRy1RYgKvbZ33zzDZ5++mmYTCa4XC48+eSTqmT9+uuvnNe6P/rN48eP4+eff4ZOp4PZbEZmZiays7M55g9CCF588UUAQGJiIrRaLSoqKvDiiy/CbDYjJycHLpcLLpfL7/2jM2fO4MiRI9BoNJw70fHjx5GXlweDwYD4+Hi0bds2pBeH3Cn9nj17cOLECfh8Ps77/dy5c9i6dSv0ej3i4uJQq1Yt7N27F0uWLIHZbIbP50P//v2DrgugbLzp/3/99VcsW7aM+9o8+uijePTRR/G3v/2NY2AZOnQogEpLLOXdfeaZZwAAW7duhc1mg8vl4u5wHTlyhNOhaJTbwsJCHDhwgKMjBSqDcVKOZ5PJhIYNG2L58uWcjunxeLh7V2lpaTCZTKioqOBkhwth49jds2cP5s2bx03gzz77DFeuXMHmzZsRFRWFiooKdO7cWdZSMmbMGJSWlsLlcuGZZ55B165dsW3bNi5q6K1bt5CRkYFvvvkGW7duhU6nQ40aNfCPf/wDv/32G0dGrdfr0aFDB2i1WgwePFggg7+w+Ni0aRM8Hg8cDgcSEhLQunVrTJkyhaOzadeuHfr27Yvdu3dj//790GgqOXbvv/9+pKamCowYRMb/zWazYdiwYbBaraioqMDUqVNRq1YtDBkyhOOrGjp0KFq2bCkqSwjB//t//4+76AhULrzU1FS89NJLgnxms1nUbjXgj0OfPn3QqVMnzJkzB8ePHwchlSGxabhqFjExMYK/0YU3ZMgQQT6v14vc3FxYLBaUl5ejadOmSEtLQ05ODhwOB1wuF6pWrYo2bdpgy5YtKCgogM/nQ1lZGVJTU7Fv3z5s2bIFOp0OiYmJaNmyJQ4fPoy8vDyYzWZoNBp07NgR586dQ25uLqKiouB0OtGjRw9UVFRgy5YtMJlMsNlsePjhh/1ae1UjGMULjFLocDhEkTvz8vLIgAEDRCf0UmDpbEwmk6QcubSqVasK0tauXau4LStXrhSUrVGjhmLZct4KUnj99dcFZfv27UtycnIEaQ0bNlT8PIpwcs2ygVv89XkoYA0D7dq1I/v37xek+ePYZdO8Xi+pVauWyCjBRg+eOnUqee+99wRprVq1Crk9fARllGAVb5/Px5lm+afSrMInp7DTrxYtq5bjlHoG0/JqDk3ZL6aUJ7MciIqtnRS9Du0PusVTI5uCvWgXCtjxCZWDWA5SBghWl1VDaaTVakV9ZzQaJY1V4eZZZhGUUYKNwOlyuThaEDrJ7Ha7iCpEjsqEKo+0rL9IP1JgZauJEBoKv6qayKZS1CpUNn1OOPiGQ0GotEJKwUaLKi0tFY2ZWp5b9gKq3W4X1b+srEz0sg13nwe1oHJycvD999/DYDAgOTkZcXFxyM7OxieffMJ5L7dv3x73338/x4ZQUVGBDz/8EIBYqe3evTvGjx+P8vJyuFwuPP/88wAqqTqPHTsGn8/HeRqvWLECeXl50Ol03N73xx9/xOLFi2EymWAymWSDi7ByAaBnz56YPHkyfD4fHA4Hx+06f/58FBUVwePxcEFF/vWvf2Hbtm3Q6XRo2LCh33tSLD7++GM4HA5Oh5o+fTqqVKnCXf+mh8pSdQbEFlSptkilqUHLli0xfvx42Gw2AdfskiVLOB2qWbNmQT+fYvz48bh58yZnqHjzzTdRvXp1TJgwAeXl5XA6nZxu+Mknn+DEiRMghHAHsGvXruVYVKj3RG5uLmeM0ev1aNasGRYsWICkpCSuzydOnAiv18t51ZSXl2PatGmSdZQyDinp34h6SoQCqWeqaaTD4cBHH32EqKgoEEIwYsQI2bzLli3jTui7deuGlJQUbN68GUVFRfD5fGjevDlnWVMiWw2WLl0KQgjsdjt69uwpGzsjLy8PBw8ehFarxQMPPIB27drh6NGjHHF01apV0a9fP5SXl2P+/PmIioqC1+vFyJEjAQDTpk2DxWKB3W7HuHHjAABz5syBXq9HRUUFhg4dKrvV3rVrF0dn89BDD6FJkyYhtRkAPvvsM25SDxw4UNI3UWq85aygxcXFWLt2LXflho3+BPgfr0WLFqG8vBxutxu9evVCnTp1sGbNGpw9e5Z7oSt6mYRDEZNSikNRlOXKyqVJ5Wf5VSdPniwpa926dYJ8tWrVIoQoU8TVto/ml+P3bdy4sWxZqfqwaYWFhaRJkyaCtB9++IFMnDhRkDZmzBiRIYgq52x/ut3usBslZs+eLXjeo48+KugjtT+EEJGnxI4dO0R9L1d2+fLlgrLVq1eX7fNAYx6Wg12pVR/Km1vNfX65vFIe1lKg+gv1HOcrzHKX3gLJDpSfyLwp1XpZ8L3daT3ZZ2g0GtEbnX87gJblGwUIb5sZCYYRqfrw6xsM2HZLHTIrhT/vErmxo4goJeifCaXcrqxBhJ/P32IKBXRAWIOIWv4iWj/6r9PpFCniTqdT5M1hs9m4NFqWX44/YSKxoFjjRzh4d1njghrDFJuXNSCpQdALyufzIS8vL6KxHqTg8XjQqFEjwdVnKcyaNQuvvvoqYmJi4HA4MHr0aMl8vXr1wiOPPAKg0vI2atQoAJVx3n7//Xd4PB78/e9/D2sb6ITt3bs3Pv30UxgMBlRUVGDMmDGyZbKysvDrr79Co9FwgViopzv14UtPT8eiRYswZswYREdHw+l04umnn0abNm2wfv16VKlSBWVlZRg5ciTq1KmDpk2bcoerM2bMENSNwmw2Y8iQITh8+DB8Pp9fTxIWcm/z119/HWvWrEFMTAzKy8u5WwSB3v7+sGDBAsyZM4cLrfD4448rLvvcc8/h22+/hcvlgsPhwKuvvgoAGDx4MAoKCuD1ejnPjbAbJYDKawxz5sxRWyyseOSRR7Bjxw6/eQ4dOsQt+MaNGwMA52XhcrmCIqNWitzcXC5ICw1dnZuby91JohzEp0+f5u4kUdeaw4cP48aNG/B6vXjwwQe5hUCNJHXr1kV0dDQIITh8+DDngkMj/xw+fJjb1vlz2ZLCwYMHuftQLVq0kHUeLSkpwf79+6HT6VC1alXcd9998Pl82Lx5MxemrE2bNqJFQn8/fPgwzGYzHA4H126pBXXu3DmOAKFJkybcvTYp/PbbbzAYDDAYDKLISoFw4cIFVFRUwOPxcC+s69evc/fnMjIyYDKZAkfDVatQsjy1f+YP5ciVUhRfeOEFQd7FixeTzz//XJBGCbwo7284vA0IIeSJJ54QyFm2bBn55JNPBGmjRo0ihw4dEqS1bduWECJUhuPj40VpdNiMRqMg7caNG6R9+/Yio4RSnD17VlA2ISFBNq9UfZo3by5I27p1q2RZltqnR48ekvnsdrtig0jjxo0F+Q4dOqS43XIeK0pl86F6y0dD9v4VsHbtWrjdbknPCPZgsKSkRORZTPfu4Q62ycouLS0V7fFLS0tF+RwOh0if8KcLUM9palgoLy/nKIRompoD0mvXromepxQ2m02UXy7aLz24p3LkdEc1OiWbV0272byszqkGkY82H2HINZ6diE6nU+S6r4bvVQ1Y2W63W1I2u8CdTqfICKBEuSa3d+3UwZdNUwoqm/CMHErh9XpFRhYqmzBaBe0Lms7Wma2PErB1VdNudhz8XeFh6yiVQRUgs/3SarUkNjaWREdHh/UnJiaGREdHy8qVY8779ttvCQASExNDDAYDOXnyJDlx4gQxm82kSpUqRK/Xk6+//lpt8xXhm2++IQaDgVStWpWYTCZy/vx5cuTIEQKAxMbGEqDSgdfpdBKDwcAxEc6YMYMQUnmmYrVaiU6nI/379yeEVDL0xcfHk/j4ePLII48QQgjp168fsVgsJD4+nqSkpBBC/s3SGBMTQ7RaLbl48aLievt8PlK9enVSpUoVYjabyauvviqbt0+fPsRgMBCr1cqd28ycOZNotVpSpUoVYjQayc2bNyXLnjlzhuh0OhIfH0+0Wi1ZsmQJJ59FkyZNiNlsJkajkdsSS2HSpEnEaDSS+Ph4EhMTo4rjubi4mFStWpVr94QJEwghhDRs2JBYLBZiNBpJt27dZOvIh2qjhJSVY/DgwVygxEjhq6++ElxVoLh165YqR0opuFwu7utAn0Vub3kIIQEdKMvKyqDVamEymUKOER4p3Lp1C3q9Hl6vN6j+on0RjAMvNcl7vV5Zbww6DSMVFOaOQfEyvg1IfCXk3kThAP+NICVb7gslha+++kpkGLh06VJAw4A/5ZxVhouLi8n//M//CNLWrFkTfAfItJ0QIrqyUFRUxOXnx7RYsGCBIN8nn3wiKePKlSuCfNRzQ0o2H3SMWO+UgoIC8vHHHwvSsrOzyfbt2wVpw4YNC7l/WrduLXjmmTNnFJdl41m0bNky6HqERYdSeyCpBuF8Y9FQvPSZFy9eFHHSUlZ1PvxR6bDlL1++LKJ6kaJfCQdYHlnKFcvqHrTdFDR6EguWg/js2bOK6kH78+TJk6LnSfUFm0+uPmrAjgO96R1MWTXUPCz+kkYJInPrNRzPZf9l5aiVK1U+1GcGC75RgQ1hpqQ+weZj5fPzST1TKl+oCOWZ4azPX3JBRWof3apVK8HvmZmZ3IEvhVQgfn+HhCyXa1pamijOm1xwfzXg+6rRw2rW65vS6Gi1WsGkaNGihSAfy49LwfLk0nL8xRkXFyc7Pmy7GzVqJGr7Qw89JAryqTQunj9QbxcKNR7xDzzwgOB3fqgBtQiLUaKwsFAUwDI/Pz/ouzMffvghRowYoUh2OIwSdwJExmMg3OA7+7Khou9COYLuN7VKFyQMA4WFhaJ8HTt2JACIRqORNXnL/VitVsWy1RglpMCyLFAztcVi4dIefPBBQgghtWvX5tJiY2Nln8kG1Ny+fbtkvnPnzgnyvf7666J2dujQgRBCSFJSEpdGzdRSoF4ffLDXRFasWCFbVsosTE39AEjdunUJIYQ0a9ZMZKhgjTHXr18n3333nSBt27Zt3BEC/ZkxYwYpLy8XpD399NOEEMIdKQSSPXDgQJFxSClY1pFnn31WcVkWEdvyUfMxCWI/eieDRe7Zs0fwe15eHgDhgTHl0eXzrEp5P1Bs2LBB8DvL9UrB8sJu375d9MytW7cCqIzJTlFcXCzrES0VXyI3N1fw++bNmxWXZT3YqeFi//79gnwlJSXIyckRpB08eFDEdbx9+3YRB/HWrVtFhptNmzYBEHp+y8mWatOvv/4qyiMHdhzk+kcJIragQnHnuZNbFFaWmnMkuXpK8b0qyUedaZXUx9+dMfZv9C4YTZerT6h32Ni6SvHc6nQ6kXyNRswDrCRgKL++wZTn15OPUM4SI7aggvky/Rlg+XClmCDkPK7l0pVyyLL8wikpKaJD5ISEBFWy/dWHjgnbZn+Q2y1IRQ9i9egaNWqIrtnUrl1bxH1bt25dkRe5FNcxBQ2nzJ9j7LhJkVLIga1PKHH6ImaU6Nq1K9avXx9UpRISEnD9+nVFssNllHC73fD5fIIJRHl3+c63brdb8m3MgvLP0rxEpZLrcrlACPG7/VXzTJfLxbGChMpNFQ7QfvR4PEHxGrtcLmg08vzFwSAQn7OS/v5Lms0jhUOHDnH3huLi4rBkyRLub3zGRQq9Xi8aMIPBIFhMw4cPR2JiIlJSUpCWlsbpG5R/luaVGojr168jJSUFqampqFatmoCuh4YUpmjfvj1q166NGjVqcPe41CxQyhUbzARs3bo197Whly0HDhyIpKQk1KxZk/sCfvjhh0hKSkJqaioSExNRUVGBvLw8VKlSBXXr1kVsbCynj9K+oYupvLwc6enpqF27NhITEzF+/HhB3dlJzucvBoApU6agWrVqqFWrFlJSUoLyGGflNG3aFMnJyahevTp69uwZMCQCgMhZ+f7+97+rtu7RHzlXH6m8aqx8o0ePFpTt2LGj2uaLYDAYBM/ctGmT4rLLli0TlL333ntl87LtLisrC7nuSuBwOESypepz69YtkpaWJuqLYcOGCdIopRFrTVy/fr0gH40cq/SOWnJysqB8bm5uSO2+evWqZLsD4b/qC8W+nYOlffT3DDUKLZtXTTiBcN/hClWOFJev1BeepWrl5wX+/dWlwXKUfoWldhd/Bv6rFhSra4WDO4mdMGpC+7L1UbOg1B4tsBNYKZRuEaOiokQhluPi4hTzPtGgobSeatvH9l2oenWwRzf/dUaJ3377DUClUhwOlyCg8rxFp9MhPj5eZDGSA7mt4J48eRJutxtut1uSd5fmKysr45w2MzIyVL0MfD5fSPHPy8rKuICPjRs35mIrHDp0iKPlpO3+6aefuGCTNNpvTk4OjEYjnE6n3+ApFy9eRGlpqWxfBMLBgwdhNBoRFRWlypIph5s3b6KwsBA+nw/33nsvjEbjf28YsV27diErKwuxsbFwOp1YuXIlfD4fsrKyOG7XSZMmcQFUWPTp0wdXrlyBz+dD+/btMXnyZGRlZXF0NpRjd+HChVixYgU3kNnZ2di1axdGjRrF8dz+8MMPHHk0BSUjy8rKgl6vh81mw6uvvoqnnnpKlA8AJk2ahJMnT8Lr9aJhw4b46KOPMGvWLKxevZqTvW7dOlE7Ql1MADBu3DiOo6lp06Z47733MGfOHC4QTVRUFFasWAEA6NKli6g8jRhEcePGDXTr1g1RUVGw2WyYPXs2WrVqhaysLO5u2nPPPYcBAwZI1mfKlCnYtGkT9Ho9qlWrhhUrVmD58uVYuHAh92VZt26dZJsvXryI3r17w2q1oqysDAsWLEBmZia6dOkCt9sNu92OV155BS+++CLefPNNXLp0CV6vF61atcLbb78duLPUKmv4DzFKsMFKpk2bRqZMmSJIo249LEpKShQp4oSIOXb3799P2rZtK5ItBXqrmP74o7NRUp+dO3cKyoSD5kYuUAqbdvz4cUKIMm7hGTNmCMq2a9eO5OfnC9I0Go1sean6sHfDcnJyJMu+9dZbgnydO3cmGzduFKQlJibKygmEiOlQocRrUBMPQA7swWNUVJRIaZbbNql5m7OcrZQcgZUtBXafHirfK6tHkDA4xio1SlAjgJL8bLtjYmJEdVfbF2yfy+lAbLrVapXk/A0WEVtQobjAZ2RkhCyfXdBS3K5y/Kpq3FbYgB5arVZSthI5aoKiSCESLltKF5SaPiOM2i7VbrV9weaXqzcrW+rlrZZnmY+I6VBTp05FgwYNUFxcrHhQyO0Y21lZWSHLX7lyJWbOnAmLxcLpThSUcWL48OGSZS0WC7799ltOb6B0Nnv27MHBgwc5HQqoDCq5ceNG6PV6VKlSBffeey9Wr16NWbNmwWq1imTz8cQTT+CLL76Az+fjorzKYf369Th06BCAf9/fOXToENatW8fJfvDBB9V3VADo9Xps2bIFJ59M2n8AAAcRSURBVE+eBCGEk3HgwAH89NNPnB4j514lhZEjR6K0tJSjmRkxYgRiY2OxYMEClJSUCKh0pLB7927k5uYKKI12796NlStXwmQywWg0iu5HUUyePJnT+2w2G8aOHQuz2Yz//d//RVlZGUeFCgDLly/n5oDSiLkRsfKFY6sh9Yw7dR9Krv7Lli3D3r17uQU1bNgw7NixA9nZ2TAajYiJicHEiRMln+nxeDBw4EBYrVbYbDbMmDEDycnJGDNmDPR6Pex2OwYPHox7771XcX0CIWCUUwmMHTuWI0AbMGAA2rRpgy+++ALHjx+Hz+dDixYt0KtXL8myhw8fxpw5cxAdHQ2Px4MFCxbg1q1beOONN2AymVBWVoavvvpK8mumtI1y+Q4cOIAvv/yS4yebPn26qnYrkaOojoo0LT8KIWSMEpGAlOxQ70OFIp8QcfTWDRs2EELEynlWVpYgX9++fUlubq4grVGjRmGtrxIDAR9ssBJ/PLd8UMMH/74YUOkp8eqrrwrSxo4dG3rDJFC9enWBHLmotZFGWHSov2rorDsBNqyWXF9IKb5s3nAcNIcC9suhVjmnhgX6FpfyjwyHd4oUQjEkhBNhWVCRiurDh5wBQSkIE5iF/xMKWPoYuXqykaFsNpvIeBEKjQofwbaJrbva+tBQzFS+0+kUtTsYp1Ul7WHrGup8CRZh+bQ8+uijmDdvHqKjo+Hz+cJ6F4pef6DUlqE8h/8vhVRd5XSPL774Anv37oVWq+WU8BUrVuC7776D0WiE1WpF586dAYitTNOmTcOlS5cQHR2NiooKvPfee6hduzb69esHrVYLu92O119/XXF7/NGj8i8SEhX618MPP4xhw4Zxi50aBmbPno0zZ85wB7tyWLt2LRYsWMDRkXbq1AmZmZlwuVyIjo5GSUkJ3nnnHcVtZNvDT2OxfPlyfPrpp5yXBmU3USMnHAiLUeLPhD+jxOLFi7ltVb9+/QBUctpqNBrY7Xb0798fer0eq1ev5iZR165dkZiYiO3bt+Ps2bMghKBVq1aoX78+zp8/j6NHj0Kj0SA1NVUUMYmPL774AiaTCRqNxq/FSgq5ubm4fPky3G43OnToIOvO9PvvvyMnJwcajQZJSUno3LkzfD4fFi9ezG2tqOyFCxfCYrHA4XBg0KBBqupz5swZnD59GoQQPPDAA5KXMIHKibl27VoYDAbodDrOzWjVqlUwmUyw2+2cJ8jXX3/NvUhefvllWbP73r17ceLECY7ruGHDhjhz5gy2b98OrVaLunXrquKsAiqjEFNDkL++2LlzJ+dy1aJFC2XHOWqVLkgYBv7Mnz/++EOyniylzNy5c8msWbMEab169RKd0NNAIKwcqTS73S4pu02bNoJ8H330ESFE+ioCazgoLCyUNAxIlWXr8/vvv5PMzExB2po1a8ikSZMEaSNHjgw4zlSex+NR7DFQv359Qb4dO3aQESNGCNJmzJghiuBLPVbYNt66dUvROJSUlARsDwXrLdO5c2fJfDdu3FDcbj5U61CRJClTC4vFwnkpB4LUVlSn04kOBNVcwJPz6GC/4vzQXoHy0r0/TWcVfX+gfntSsvlgD7ilQJ8TCiWox+OR3Jayz5TbjiupJ6DOK4eVLXeswI6DUqheUBs3bryjUYn8gd7+ZBcKIDYCOBwOEber3W4XLQo1p+Ryk01KthzkFhRtk5r6OBwOkWyn0ylS0Nl+8Ac1C4qV7XK5JPucfYnJ0dkoXVBK8/FlUciFEWepfZRCtVFCp9PB4XBgxIgR2L59O+fSfqfg9XpRp04dLFy4ENWqVZOVPX78eFy6dInj2O3bty+ASiOC2WxGeXk5hg0bhtatW6NZs2acXkX5Vbt27YorV67A4/FwusDQoUM5o0RaWprs3Z6pU6ciKysL0dHRcDgcnP6mBBkZGXjssce4awxPPvmkbN6JEydy3uZWqxX169fH+++/jzfffJOT3a1bN2RmZmLlypWIj49HSUmJrIeIFEwmE/r06YPTp0/D5/OJIuXyMXPmTEyfPh0WiwVOpxMdOnRAQkICtm7diri4OPzxxx8YPHgwTCYTPvroI8TGxqKkpARvvfUWAPHLJTExEb1798aZM2dACMHDDz8MABgzZgznnZKYmIikpCTF7Rk0aBDWrl0Lq9WK0tJSTJo0STJfSkoKevbsiYsXL8Lj8cjeSmARFMfuXwVExjqjxkNA6hlSacF4HSipqxJIyQ61PqEglLaE+kyv1ysyYJDbxx/B9odS2UquwvxHL6hAoE2TMrvyO1GqC/gBOeQ6MNBABCr/3wx+nwcKGR1oiv6V+vf/9IK6i7u40/iviilxF3cRadxdUHdxF2HE3QV1F3cRRtxdUHdxF2HE3QV1F3cRRtxdUHdxF2HE3QV1F3cRRtxdUHdxF2HE3QV1F3cRRvx/iwB99TsD0RsAAAAASUVORK5CYII="}}, "cell_type": "markdown", "metadata": {}, "source": ["1. 只需要替换股票名称，代码，时间，数量等直接运行即可其他不需要更改！需要存到本地，则自行存入\n", "2. 有问题扫二维码咨询，写的匆忙，也比较简单，有任何问题和需求都可以提出\n", "####  ![image.png](attachment:image.png)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 导入所需安装包"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import talib as tb\n", "import omicron\n", "from omicron import tf\n", "from omicron.models.security import Security\n", "from coretypes import FrameType\n", "import cfg4py\n", "import datetime\n", "import numpy as np\n", "import pandas as pd\n", "from zigzag import peak_valley_pivots\n", "from omicron.models.stock import Stock\n", "\n", "cfg4py.init('~/cheese/config')\n", "await omicron.init()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 定义RSI与收盘价背离\n", "一般是指RSI到达低位时收盘价再持续向下走，而RSI低点却在向上，出现两者背离，则后续股价有可能会触底反弹，但是背离有些股票可能出现，有些不一定会出现，所以在换股票代码验证的时候，有可能会没有背离点"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def dev_rsi_detection(bars, thresh=None, rsi_limit=30):  \n", "    assert len(bars) > 60, \"must provide an array with at least 61 length!\"\n", "\n", "    close = bars['close'].astype(np.float64)\n", "    if thresh is None: \n", "        std = np.std((close[-60:]-close[-61:-1])/close[-61:-1])\n", "        thresh = (2*std, -2*std)\n", "  \n", "    rsi = tb.RSI(close, 6)\n", "    \n", "    pivots = peak_valley_pivots(close, thresh[0], thresh[1])\n", "    pivots[0], pivots[-1]=0, 0   # 掐头去尾\n", "    valley_pivots = -1*(((pivots==-1)&(rsi<=rsi_limit)).astype('int'))    # 更改了RSI最低点限制\n", "    #-------------------------------------------------------------\n", "    bottom_dev=0\n", "    valley_index=np.where(valley_pivots==-1)[0]\n", "\n", "    if len(valley_index)>=2:   # 相邻底背离\n", "        if (((rsi[valley_index[-1]]-rsi[valley_index[-2]])>0)\n", "            &((close[valley_index[-1]]-close[valley_index[-2]])<0)):\n", "            bottom_dev=1\n", "\n", "   \n", "        elif le<PERSON>(valley_index)>=3: # 间隔背离点\n", "            if ((rsi[valley_index[-1]]-rsi[valley_index[-3]])>0)&((close[valley_index[-1]]-close[valley_index[-3]])<0):\n", "                bottom_dev=2\n", "        else:\n", "            pass\n", "   \n", "\n", "    return bottom_dev, valley_index "]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 选取指定股票进行验证，寻找RSI背离"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# 股票代码\n", "code = \"000608.XSHE\"  # 阳光股份 可自行修改股票代码，6开头的股票，字母后缀：XSHG，其他为：XSHE\n", "# 250个日线bar就是差多过去一年行情数据\n", "bars = await Stock.get_bars(code, 250, end=datetime.date(2023, 3, 1), frame_type=FrameType.DAY)  # 时间可改\n", "\n", "bottom_dev, valley_index  = dev_rsi_detection(bars)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 不论最后的低价是否出现了背离，都绘制出交互式K线图"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [3.569999933242798, 3.440000057220459, 3.359999895095825, 3.2699999809265137, 3.3299999237060547, 3.309999942779541, 3.369999885559082, 3.4000000953674316, 3.4700000286102295, 3.3499999046325684, 3.3299999237060547, 3.1500000953674316, 3.059999942779541, 3.109999895095825, 3.130000114440918, 3.069999933242798, 2.859999895095825, 2.990000009536743, 3.0799999237060547, 3.200000047683716, 3.319999933242798, 3.4800000190734863, 3.8299999237060547, 4.210000038146973, 3.9100000858306885, 4.150000095367432, 3.740000009536743, 3.7699999809265137, 4.150000095367432, 4.570000171661377, 4.190000057220459, 3.7699999809265137, 3.880000114440918, 3.619999885559082, 3.5999999046325684, 3.4700000286102295, 3.5899999141693115, 3.549999952316284, 3.450000047683716, 3.5199999809265137, 3.430000066757202, 3.240000009536743, 3.1700000762939453, 2.9800000190734863, 3.0399999618530273, 3, 2.990000009536743, 3.009999990463257, 3.069999933242798, 2.9800000190734863, 3.0799999237060547, 3.109999895095825, 3.0399999618530273, 3.0299999713897705, 3.119999885559082, 3.200000047683716, 3.190000057220459, 3.130000114440918, 3.180000066757202, 3.1500000953674316, 3.1500000953674316, 3.0299999713897705, 3.069999933242798, 3.059999942779541, 3.069999933242798, 3.0299999713897705, 3.0299999713897705, 3.009999990463257, 2.950000047683716, 2.9700000286102295, 3.009999990463257, 2.9600000381469727, 2.9200000762939453, 2.9200000762939453, 2.869999885559082, 2.950000047683716, 2.990000009536743, 2.9600000381469727, 2.930000066757202, 2.9600000381469727, 2.990000009536743, 2.930000066757202, 2.950000047683716, 2.940000057220459, 2.950000047683716, 2.9600000381469727, 2.990000009536743, 3.009999990463257, 2.990000009536743, 2.950000047683716, 2.9100000858306885, 2.869999885559082, 2.880000114440918, 2.890000104904175, 2.869999885559082, 2.880000114440918, 2.930000066757202, 2.859999895095825, 2.6700000762939453, 2.809999942779541, 2.819999933242798, 2.819999933242798, 2.799999952316284, 2.799999952316284, 2.8399999141693115, 2.9700000286102295, 3, 3.059999942779541, 3, 2.930000066757202, 2.7799999713897705, 2.7300000190734863, 2.7699999809265137, 2.799999952316284, 2.8399999141693115, 2.809999942779541, 2.7699999809265137, 2.799999952316284, 2.8299999237060547, 2.819999933242798, 2.9800000190734863, 3.009999990463257, 2.9600000381469727, 2.930000066757202, 2.930000066757202, 2.9200000762939453, 2.8299999237060547, 2.859999895095825, 2.809999942779541, 2.8399999141693115, 2.8399999141693115, 2.809999942779541, 2.8399999141693115, 2.869999885559082, 2.940000057220459, 3.069999933242798, 3.0899999141693115, 3.059999942779541, 3.1500000953674316, 3.069999933242798, 3.049999952316284, 3.0899999141693115, 2.9800000190734863, 2.940000057220459, 2.940000057220459, 3.059999942779541, 2.9800000190734863, 2.940000057220459, 2.8399999141693115, 2.880000114440918, 2.7799999713897705, 2.7100000381469727, 2.75, 2.700000047683716, 2.680000066757202, 2.7200000286102295, 2.700000047683716, 2.740000009536743, 2.7699999809265137, 2.759999990463257, 2.7200000286102295, 2.7200000286102295, 2.7100000381469727, 2.630000114440918, 2.5799999237060547, 2.6600000858306885, 2.75, 2.6700000762939453, 2.6500000953674316, 2.680000066757202, 2.700000047683716, 2.6700000762939453, 2.7200000286102295, 2.7599000930786133, 2.7799999713897705, 2.819999933242798, 2.8399999141693115, 2.940000057220459, 3.0299999713897705, 3.0399999618530273, 3.069999933242798, 3.0799999237060547, 3.009999990463257, 2.940000057220459, 2.9100000858306885, 2.869999885559082, 2.9200000762939453, 3.0399999618530273, 3.009999990463257, 3.309999942779541, 3.319999933242798, 3.430000066757202, 3.7699999809265137, 4.150000095367432, 3.7799999713897705, 3.4000000953674316, 3.369999885559082, 3.509999990463257, 3.4000000953674316, 3.490000009536743, 3.4100000858306885, 3.4100000858306885, 3.450000047683716, 3.549999952316284, 3.359999895095825, 3.309999942779541, 3.380000114440918, 3.5, 3.4000000953674316, 3.4600000381469727, 3.4000000953674316, 3.3399999141693115, 3.369999885559082, 3.4200000762939453, 3.5199999809265137, 3.4200000762939453, 3.4100000858306885, 3.3499999046325684, 3.2899999618530273, 3.2300000190734863, 3.180000066757202, 3.190000057220459, 3.1600000858306885, 3.130000114440918, 3.119999885559082, 3.109999895095825, 3.1600000858306885, 3.1700000762939453, 3.190000057220459, 3.190000057220459, 3.1600000858306885, 3.140000104904175, 3.109999895095825, 3.1500000953674316, 3.1700000762939453, 3.1500000953674316, 3.1700000762939453, 3.2100000381469727, 3.200000047683716, 3.1700000762939453, 3.0999999046325684, 3.119999885559082, 3.1600000858306885, 3.200000047683716, 3.190000057220459, 3.2100000381469727, 3.180000066757202, 3.140000104904175, 3.190000057220459, 3.2200000286102295], "decreasing": {"line": {"color": "green"}}, "high": [3.640000104904175, 3.5899999141693115, 3.4800000190734863, 3.390000104904175, 3.369999885559082, 3.3399999141693115, 3.4000000953674316, 3.430000066757202, 3.4700000286102295, 3.4700000286102295, 3.430000066757202, 3.3499999046325684, 3.200000047683716, 3.1600000858306885, 3.140000104904175, 3.240000009536743, 3.0899999141693115, 3.009999990463257, 3.1600000858306885, 3.2100000381469727, 3.390000104904175, 3.5999999046325684, 3.8299999237060547, 4.210000038146973, 4.360000133514404, 4.300000190734863, 4, 3.930000066757202, 4.150000095367432, 4.570000171661377, 4.550000190734863, 4.010000228881836, 3.9200000762939453, 3.8399999141693115, 3.700000047683716, 3.5999999046325684, 3.6700000762939453, 3.6700000762939453, 3.5999999046325684, 3.549999952316284, 3.5299999713897705, 3.4200000762939453, 3.2799999713897705, 3.180000066757202, 3.1500000953674316, 3.069999933242798, 3.0399999618530273, 3.049999952316284, 3.0799999237060547, 3.0199999809265137, 3.140000104904175, 3.109999895095825, 3.130000114440918, 3.069999933242798, 3.130000114440918, 3.2899999618530273, 3.240000009536743, 3.190000057220459, 3.25, 3.240000009536743, 3.180000066757202, 3.1500000953674316, 3.0899999141693115, 3.140000104904175, 3.0999999046325684, 3.0799999237060547, 3.0399999618530273, 3.059999942779541, 3.009999990463257, 2.9700000286102295, 3.0199999809265137, 3.0199999809265137, 2.9800000190734863, 2.940000057220459, 2.9100000858306885, 2.9700000286102295, 3.0299999713897705, 3.0199999809265137, 2.9600000381469727, 2.9700000286102295, 3.0399999618530273, 2.990000009536743, 2.9600000381469727, 2.9600000381469727, 2.9700000286102295, 2.9800000190734863, 3.0199999809265137, 3.0199999809265137, 3.0299999713897705, 2.990000009536743, 2.9700000286102295, 2.9100000858306885, 2.9100000858306885, 2.9100000858306885, 2.890000104904175, 2.9000000953674316, 2.9600000381469727, 2.9200000762939453, 2.8399999141693115, 2.8499999046325684, 2.8299999237060547, 2.8399999141693115, 2.8299999237060547, 2.8299999237060547, 2.890000104904175, 2.990000009536743, 3.059999942779541, 3.0899999141693115, 3.0899999141693115, 3.009999990463257, 2.930000066757202, 2.8299999237060547, 2.7799999713897705, 2.809999942779541, 2.9000000953674316, 2.8499999046325684, 2.799999952316284, 2.8299999237060547, 2.8299999237060547, 2.8399999141693115, 3.0999999046325684, 3.059999942779541, 3.0799999237060547, 3, 2.9600000381469727, 2.940000057220459, 2.940000057220459, 2.880000114440918, 2.869999885559082, 2.8499999046325684, 2.890000104904175, 2.859999895095825, 2.9000000953674316, 2.890000104904175, 2.9600000381469727, 3.130000114440918, 3.119999885559082, 3.109999895095825, 3.180000066757202, 3.1600000858306885, 3.119999885559082, 3.130000114440918, 3.119999885559082, 3, 3.059999942779541, 3.0799999237060547, 3.0899999141693115, 3.059999942779541, 2.9700000286102295, 2.9100000858306885, 2.890000104904175, 2.809999942779541, 2.7899999618530273, 2.799999952316284, 2.7200000286102295, 2.7200000286102295, 2.7300000190734863, 2.759999990463257, 2.7799999713897705, 2.7899999618530273, 2.759999990463257, 2.75, 2.759999990463257, 2.7200000286102295, 2.640000104904175, 2.7100000381469727, 2.7799999713897705, 2.7699999809265137, 2.680000066757202, 2.700000047683716, 2.7200000286102295, 2.700000047683716, 2.7300000190734863, 2.7699999809265137, 2.799999952316284, 2.859999895095825, 2.859999895095825, 3, 3.0899999141693115, 3.119999885559082, 3.1700000762939453, 3.140000104904175, 3.130000114440918, 3.009999990463257, 3.0299999713897705, 2.9200000762939453, 2.9800000190734863, 3.049999952316284, 3.059999942779541, 3.309999942779541, 3.549999952316284, 3.430000066757202, 3.7699999809265137, 4.150000095367432, 4.150000095367432, 3.5999999046325684, 3.450000047683716, 3.569999933242798, 3.5, 3.5399999618530273, 3.5299999713897705, 3.450000047683716, 3.5399999618530273, 3.559999942779541, 3.4600000381469727, 3.4000000953674316, 3.549999952316284, 3.700000047683716, 3.4800000190734863, 3.5199999809265137, 3.4600000381469727, 3.430000066757202, 3.4100000858306885, 3.440000057220459, 3.549999952316284, 3.549999952316284, 3.4800000190734863, 3.4100000858306885, 3.369999885559082, 3.319999933242798, 3.259999990463257, 3.2200000286102295, 3.200000047683716, 3.1700000762939453, 3.1500000953674316, 3.119999885559082, 3.1700000762939453, 3.259999990463257, 3.200000047683716, 3.2200000286102295, 3.190000057220459, 3.1600000858306885, 3.1500000953674316, 3.180000066757202, 3.200000047683716, 3.1700000762939453, 3.2100000381469727, 3.2200000286102295, 3.240000009536743, 3.2100000381469727, 3.200000047683716, 3.1700000762939453, 3.1600000858306885, 3.259999990463257, 3.200000047683716, 3.240000009536743, 3.2300000190734863, 3.190000057220459, 3.200000047683716, 3.240000009536743], "increasing": {"line": {"color": "red"}}, "low": [3.5, 3.4200000762939453, 3.3399999141693115, 3.049999952316284, 3.25, 3.25, 3.309999942779541, 3.3499999046325684, 3.390000104904175, 3.3399999141693115, 3.309999942779541, 3.130000114440918, 2.930000066757202, 3.059999942779541, 3.009999990463257, 3.059999942779541, 2.859999895095825, 2.8399999141693115, 3.049999952316284, 3.0399999618530273, 3.1700000762939453, 3.2200000286102295, 3.369999885559082, 4.059999942779541, 3.7899999618530273, 3.7100000381469727, 3.740000009536743, 3.6600000858306885, 3.7899999618530273, 3.940000057220459, 4.110000133514404, 3.7699999809265137, 3.6700000762939453, 3.549999952316284, 3.4800000190734863, 3.4200000762939453, 3.4800000190734863, 3.5199999809265137, 3.4000000953674316, 3.390000104904175, 3.369999885559082, 3.200000047683716, 3.1500000953674316, 2.950000047683716, 2.950000047683716, 2.8299999237060547, 2.9000000953674316, 2.9700000286102295, 2.9700000286102295, 2.859999895095825, 2.9800000190734863, 2.990000009536743, 3.0199999809265137, 2.950000047683716, 2.9800000190734863, 3.119999885559082, 3.0799999237060547, 3.109999895095825, 3.049999952316284, 3.130000114440918, 3.119999885559082, 3.009999990463257, 3, 3.049999952316284, 3.0299999713897705, 3.009999990463257, 2.9800000190734863, 2.990000009536743, 2.950000047683716, 2.9100000858306885, 2.940000057220459, 2.9200000762939453, 2.9200000762939453, 2.890000104904175, 2.859999895095825, 2.8299999237060547, 2.950000047683716, 2.950000047683716, 2.890000104904175, 2.9100000858306885, 2.950000047683716, 2.9200000762939453, 2.9200000762939453, 2.930000066757202, 2.930000066757202, 2.930000066757202, 2.950000047683716, 2.950000047683716, 2.9800000190734863, 2.940000057220459, 2.890000104904175, 2.859999895095825, 2.859999895095825, 2.859999895095825, 2.859999895095825, 2.8499999046325684, 2.869999885559082, 2.8499999046325684, 2.6600000858306885, 2.6700000762939453, 2.7899999618530273, 2.7899999618530273, 2.7899999618530273, 2.7699999809265137, 2.799999952316284, 2.809999942779541, 2.950000047683716, 3, 2.990000009536743, 2.9200000762939453, 2.75, 2.7200000286102295, 2.700000047683716, 2.75, 2.799999952316284, 2.7799999713897705, 2.75, 2.7699999809265137, 2.7799999713897705, 2.799999952316284, 2.809999942779541, 2.950000047683716, 2.930000066757202, 2.9200000762939453, 2.9000000953674316, 2.9000000953674316, 2.8299999237060547, 2.809999942779541, 2.799999952316284, 2.759999990463257, 2.819999933242798, 2.799999952316284, 2.799999952316284, 2.8299999237060547, 2.869999885559082, 2.930000066757202, 3, 3.0299999713897705, 3.069999933242798, 3.0399999618530273, 3.0299999713897705, 3.009999990463257, 2.9800000190734863, 2.9000000953674316, 2.9200000762939453, 2.9000000953674316, 2.9600000381469727, 2.930000066757202, 2.8399999141693115, 2.809999942779541, 2.7799999713897705, 2.680000066757202, 2.7100000381469727, 2.680000066757202, 2.619999885559082, 2.630000114440918, 2.6500000953674316, 2.690000057220459, 2.680000066757202, 2.740000009536743, 2.7100000381469727, 2.690000057220459, 2.700000047683716, 2.630000114440918, 2.549999952316284, 2.609999895095825, 2.680000066757202, 2.6600000858306885, 2.5899999141693115, 2.630000114440918, 2.680000066757202, 2.6600000858306885, 2.6700000762939453, 2.700000047683716, 2.7300000190734863, 2.759999990463257, 2.7799999713897705, 2.859999895095825, 2.950000047683716, 3, 3.049999952316284, 3.0399999618530273, 3, 2.9200000762939453, 2.880000114440918, 2.859999895095825, 2.880000114440918, 2.9200000762939453, 2.940000057220459, 3.299999952316284, 3.25, 3.240000009536743, 3.299999952316284, 4.150000095367432, 3.740000009536743, 3.4000000953674316, 3.3299999237060547, 3.380000114440918, 3.3299999237060547, 3.369999885559082, 3.4000000953674316, 3.3399999141693115, 3.390000104904175, 3.4000000953674316, 3.200000047683716, 3.309999942779541, 3.359999895095825, 3.369999885559082, 3.3499999046325684, 3.380000114440918, 3.3499999046325684, 3.3399999141693115, 3.319999933242798, 3.359999895095825, 3.4100000858306885, 3.4000000953674316, 3.319999933242798, 3.3399999141693115, 3.2699999809265137, 3.2200000286102295, 3.1700000762939453, 3.1500000953674316, 3.130000114440918, 3.0999999046325684, 3.0999999046325684, 3.0799999237060547, 3.0899999141693115, 3.140000104904175, 3.0899999141693115, 3.1500000953674316, 3.140000104904175, 3.0999999046325684, 3.0999999046325684, 3.0899999141693115, 3.140000104904175, 3.130000114440918, 3.140000104904175, 3.1700000762939453, 3.180000066757202, 3.1500000953674316, 3.069999933242798, 3.0899999141693115, 3.109999895095825, 3.180000066757202, 3.1600000858306885, 3.180000066757202, 3.140000104904175, 3.130000114440918, 3.119999885559082, 3.190000057220459], "name": "K线", "open": [3.5299999713897705, 3.5199999809265137, 3.4600000381469727, 3.3499999046325684, 3.259999990463257, 3.309999942779541, 3.309999942779541, 3.359999895095825, 3.4100000858306885, 3.4600000381469727, 3.3499999046325684, 3.3499999046325684, 3.180000066757202, 3.0899999141693115, 3.059999942779541, 3.0999999046325684, 3.0899999141693115, 2.9100000858306885, 3.059999942779541, 3.059999942779541, 3.200000047683716, 3.309999942779541, 3.4000000953674316, 4.210000038146973, 4.199999809265137, 3.799999952316284, 4, 3.7300000190734863, 3.7899999618530273, 4.320000171661377, 4.389999866485596, 3.930000066757202, 3.7699999809265137, 3.8299999237060547, 3.5999999046325684, 3.549999952316284, 3.4800000190734863, 3.5899999141693115, 3.509999990463257, 3.4800000190734863, 3.5, 3.4200000762939453, 3.2200000286102295, 3.130000114440918, 3, 2.990000009536743, 3, 3.009999990463257, 3.0299999713897705, 3, 3.009999990463257, 3.0299999713897705, 3.119999885559082, 3.0199999809265137, 3.0199999809265137, 3.2300000190734863, 3.240000009536743, 3.1700000762939453, 3.069999933242798, 3.1700000762939453, 3.1600000858306885, 3.1500000953674316, 3.009999990463257, 3.0799999237060547, 3.069999933242798, 3.0799999237060547, 3.0299999713897705, 3.0399999618530273, 3.009999990463257, 2.950000047683716, 2.9700000286102295, 3.009999990463257, 2.950000047683716, 2.9000000953674316, 2.9000000953674316, 2.869999885559082, 2.9600000381469727, 2.9800000190734863, 2.930000066757202, 2.930000066757202, 2.9700000286102295, 2.9800000190734863, 2.930000066757202, 2.940000057220459, 2.940000057220459, 2.940000057220459, 2.9600000381469727, 2.990000009536743, 3.009999990463257, 2.9700000286102295, 2.9600000381469727, 2.9100000858306885, 2.869999885559082, 2.890000104904175, 2.890000104904175, 2.880000114440918, 2.890000104904175, 2.9200000762939453, 2.8399999141693115, 2.690000057220459, 2.799999952316284, 2.799999952316284, 2.819999933242798, 2.799999952316284, 2.819999933242798, 2.8299999237060547, 2.9800000190734863, 3, 3.0899999141693115, 2.990000009536743, 2.9100000858306885, 2.7799999713897705, 2.740000009536743, 2.7699999809265137, 2.819999933242798, 2.8399999141693115, 2.799999952316284, 2.7799999713897705, 2.809999942779541, 2.8399999141693115, 2.819999933242798, 2.9600000381469727, 3.009999990463257, 2.940000057220459, 2.930000066757202, 2.930000066757202, 2.930000066757202, 2.8399999141693115, 2.859999895095825, 2.7799999713897705, 2.8399999141693115, 2.819999933242798, 2.799999952316284, 2.8399999141693115, 2.880000114440918, 2.940000057220459, 3.109999895095825, 3.0899999141693115, 3.0799999237060547, 3.140000104904175, 3.049999952316284, 3.0799999237060547, 3.0799999237060547, 3, 2.9600000381469727, 2.940000057220459, 3.0299999713897705, 3, 2.930000066757202, 2.869999885559082, 2.890000104904175, 2.7899999618530273, 2.7200000286102295, 2.759999990463257, 2.7200000286102295, 2.680000066757202, 2.7200000286102295, 2.700000047683716, 2.7200000286102295, 2.7699999809265137, 2.759999990463257, 2.7100000381469727, 2.7300000190734863, 2.7200000286102295, 2.619999885559082, 2.609999895095825, 2.690000057220459, 2.759999990463257, 2.6600000858306885, 2.6500000953674316, 2.690000057220459, 2.680000066757202, 2.6700000762939453, 2.7100000381469727, 2.759999990463257, 2.7699999809265137, 2.799999952316284, 2.869999885559082, 3.049999952316284, 3, 3.059999942779541, 3.059999942779541, 3.0999999046325684, 3, 2.9600000381469727, 2.890000104904175, 2.9100000858306885, 2.930000066757202, 3.009999990463257, 3.309999942779541, 3.549999952316284, 3.299999952316284, 3.380000114440918, 4.150000095367432, 4.150000095367432, 3.5999999046325684, 3.369999885559082, 3.390000104904175, 3.390000104904175, 3.4000000953674316, 3.4600000381469727, 3.380000114440918, 3.4200000762939453, 3.430000066757202, 3.440000057220459, 3.359999895095825, 3.4600000381469727, 3.369999885559082, 3.450000047683716, 3.4200000762939453, 3.430000066757202, 3.4000000953674316, 3.3399999141693115, 3.369999885559082, 3.430000066757202, 3.549999952316284, 3.450000047683716, 3.359999895095825, 3.3399999141693115, 3.299999952316284, 3.259999990463257, 3.180000066757202, 3.200000047683716, 3.1600000858306885, 3.140000104904175, 3.0999999046325684, 3.119999885559082, 3.200000047683716, 3.119999885559082, 3.2100000381469727, 3.190000057220459, 3.1600000858306885, 3.140000104904175, 3.0999999046325684, 3.1500000953674316, 3.1600000858306885, 3.1600000858306885, 3.1700000762939453, 3.2200000286102295, 3.200000047683716, 3.1700000762939453, 3.0999999046325684, 3.140000104904175, 3.190000057220459, 3.200000047683716, 3.200000047683716, 3.2200000286102295, 3.180000066757202, 3.140000104904175, 3.200000047683716], "text": ["2022-02-21T00:00:00", "2022-02-22T00:00:00", "2022-02-23T00:00:00", "2022-02-24T00:00:00", "2022-02-25T00:00:00", "2022-02-28T00:00:00", "2022-03-01T00:00:00", "2022-03-02T00:00:00", "2022-03-03T00:00:00", "2022-03-04T00:00:00", "2022-03-07T00:00:00", "2022-03-08T00:00:00", "2022-03-09T00:00:00", "2022-03-10T00:00:00", "2022-03-11T00:00:00", "2022-03-14T00:00:00", "2022-03-15T00:00:00", "2022-03-16T00:00:00", "2022-03-17T00:00:00", "2022-03-18T00:00:00", "2022-03-21T00:00:00", "2022-03-22T00:00:00", "2022-03-23T00:00:00", "2022-03-24T00:00:00", "2022-03-25T00:00:00", "2022-03-28T00:00:00", "2022-03-29T00:00:00", "2022-03-30T00:00:00", "2022-03-31T00:00:00", "2022-04-01T00:00:00", "2022-04-06T00:00:00", "2022-04-07T00:00:00", "2022-04-08T00:00:00", "2022-04-11T00:00:00", "2022-04-12T00:00:00", "2022-04-13T00:00:00", "2022-04-14T00:00:00", "2022-04-15T00:00:00", "2022-04-18T00:00:00", "2022-04-19T00:00:00", "2022-04-20T00:00:00", "2022-04-21T00:00:00", "2022-04-22T00:00:00", "2022-04-25T00:00:00", "2022-04-26T00:00:00", "2022-04-27T00:00:00", "2022-04-28T00:00:00", "2022-04-29T00:00:00", "2022-05-05T00:00:00", "2022-05-06T00:00:00", "2022-05-09T00:00:00", "2022-05-10T00:00:00", "2022-05-11T00:00:00", "2022-05-12T00:00:00", "2022-05-13T00:00:00", "2022-05-16T00:00:00", "2022-05-17T00:00:00", "2022-05-18T00:00:00", "2022-05-19T00:00:00", "2022-05-20T00:00:00", "2022-05-23T00:00:00", "2022-05-24T00:00:00", "2022-05-25T00:00:00", "2022-05-26T00:00:00", "2022-05-27T00:00:00", "2022-05-30T00:00:00", "2022-05-31T00:00:00", "2022-06-01T00:00:00", "2022-06-02T00:00:00", "2022-06-06T00:00:00", "2022-06-07T00:00:00", "2022-06-08T00:00:00", "2022-06-09T00:00:00", "2022-06-10T00:00:00", "2022-06-13T00:00:00", "2022-06-14T00:00:00", "2022-06-15T00:00:00", "2022-06-16T00:00:00", "2022-06-17T00:00:00", "2022-06-20T00:00:00", "2022-06-21T00:00:00", "2022-06-22T00:00:00", "2022-06-23T00:00:00", "2022-06-24T00:00:00", "2022-06-27T00:00:00", "2022-06-28T00:00:00", "2022-06-29T00:00:00", "2022-06-30T00:00:00", "2022-07-01T00:00:00", "2022-07-04T00:00:00", "2022-07-05T00:00:00", "2022-07-06T00:00:00", "2022-07-07T00:00:00", "2022-07-08T00:00:00", "2022-07-11T00:00:00", "2022-07-12T00:00:00", "2022-07-13T00:00:00", "2022-07-14T00:00:00", "2022-07-15T00:00:00", "2022-07-18T00:00:00", "2022-07-19T00:00:00", "2022-07-20T00:00:00", "2022-07-21T00:00:00", "2022-07-22T00:00:00", "2022-07-25T00:00:00", "2022-07-26T00:00:00", "2022-07-27T00:00:00", "2022-07-28T00:00:00", "2022-07-29T00:00:00", "2022-08-01T00:00:00", "2022-08-02T00:00:00", "2022-08-03T00:00:00", "2022-08-04T00:00:00", "2022-08-05T00:00:00", "2022-08-08T00:00:00", "2022-08-09T00:00:00", "2022-08-10T00:00:00", "2022-08-11T00:00:00", "2022-08-12T00:00:00", "2022-08-15T00:00:00", "2022-08-16T00:00:00", "2022-08-17T00:00:00", "2022-08-18T00:00:00", "2022-08-19T00:00:00", "2022-08-22T00:00:00", "2022-08-23T00:00:00", "2022-08-24T00:00:00", "2022-08-25T00:00:00", "2022-08-26T00:00:00", "2022-08-29T00:00:00", "2022-08-30T00:00:00", "2022-08-31T00:00:00", "2022-09-01T00:00:00", "2022-09-02T00:00:00", "2022-09-05T00:00:00", "2022-09-06T00:00:00", "2022-09-07T00:00:00", "2022-09-08T00:00:00", "2022-09-09T00:00:00", "2022-09-13T00:00:00", "2022-09-14T00:00:00", "2022-09-15T00:00:00", "2022-09-16T00:00:00", "2022-09-19T00:00:00", "2022-09-20T00:00:00", "2022-09-21T00:00:00", "2022-09-22T00:00:00", "2022-09-23T00:00:00", "2022-09-26T00:00:00", "2022-09-27T00:00:00", "2022-09-28T00:00:00", "2022-09-29T00:00:00", "2022-09-30T00:00:00", "2022-10-10T00:00:00", "2022-10-11T00:00:00", "2022-10-12T00:00:00", "2022-10-13T00:00:00", "2022-10-14T00:00:00", "2022-10-17T00:00:00", "2022-10-18T00:00:00", "2022-10-19T00:00:00", "2022-10-20T00:00:00", "2022-10-21T00:00:00", "2022-10-24T00:00:00", "2022-10-25T00:00:00", "2022-10-26T00:00:00", "2022-10-27T00:00:00", "2022-10-28T00:00:00", "2022-10-31T00:00:00", "2022-11-01T00:00:00", "2022-11-02T00:00:00", "2022-11-03T00:00:00", "2022-11-04T00:00:00", "2022-11-07T00:00:00", "2022-11-08T00:00:00", "2022-11-09T00:00:00", "2022-11-10T00:00:00", "2022-11-11T00:00:00", "2022-11-14T00:00:00", "2022-11-15T00:00:00", "2022-11-16T00:00:00", "2022-11-17T00:00:00", "2022-11-18T00:00:00", "2022-11-21T00:00:00", "2022-11-22T00:00:00", "2022-11-23T00:00:00", "2022-11-24T00:00:00", "2022-11-25T00:00:00", "2022-11-28T00:00:00", "2022-11-29T00:00:00", "2022-11-30T00:00:00", "2022-12-01T00:00:00", "2022-12-02T00:00:00", "2022-12-05T00:00:00", "2022-12-06T00:00:00", "2022-12-07T00:00:00", "2022-12-08T00:00:00", "2022-12-09T00:00:00", "2022-12-12T00:00:00", "2022-12-13T00:00:00", "2022-12-14T00:00:00", "2022-12-15T00:00:00", "2022-12-16T00:00:00", "2022-12-19T00:00:00", "2022-12-20T00:00:00", "2022-12-21T00:00:00", "2022-12-22T00:00:00", "2022-12-23T00:00:00", "2022-12-26T00:00:00", "2022-12-27T00:00:00", "2022-12-28T00:00:00", "2022-12-29T00:00:00", "2022-12-30T00:00:00", "2023-01-03T00:00:00", "2023-01-04T00:00:00", "2023-01-05T00:00:00", "2023-01-06T00:00:00", "2023-01-09T00:00:00", "2023-01-10T00:00:00", "2023-01-11T00:00:00", "2023-01-12T00:00:00", "2023-01-13T00:00:00", "2023-01-16T00:00:00", "2023-01-17T00:00:00", "2023-01-18T00:00:00", "2023-01-19T00:00:00", "2023-01-20T00:00:00", "2023-01-30T00:00:00", "2023-01-31T00:00:00", "2023-02-01T00:00:00", "2023-02-02T00:00:00", "2023-02-03T00:00:00", "2023-02-06T00:00:00", "2023-02-07T00:00:00", "2023-02-08T00:00:00", "2023-02-09T00:00:00", "2023-02-10T00:00:00", "2023-02-13T00:00:00", "2023-02-14T00:00:00", "2023-02-15T00:00:00", "2023-02-16T00:00:00", "2023-02-17T00:00:00", "2023-02-20T00:00:00", "2023-02-21T00:00:00", "2023-02-22T00:00:00", "2023-02-23T00:00:00", "2023-02-24T00:00:00", "2023-02-27T00:00:00", "2023-02-28T00:00:00", "2023-03-01T00:00:00"], "type": "candlestick", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249], "xaxis": "x", "yaxis": "y"}, {"marker": {"color": "red"}, "name": "close>=open成交量", "text": ["2022-02-21T00:00:00", "2022-02-25T00:00:00", "2022-02-28T00:00:00", "2022-03-01T00:00:00", "2022-03-02T00:00:00", "2022-03-03T00:00:00", "2022-03-10T00:00:00", "2022-03-11T00:00:00", "2022-03-16T00:00:00", "2022-03-17T00:00:00", "2022-03-18T00:00:00", "2022-03-21T00:00:00", "2022-03-22T00:00:00", "2022-03-23T00:00:00", "2022-03-24T00:00:00", "2022-03-28T00:00:00", "2022-03-30T00:00:00", "2022-03-31T00:00:00", "2022-04-01T00:00:00", "2022-04-08T00:00:00", "2022-04-12T00:00:00", "2022-04-14T00:00:00", "2022-04-19T00:00:00", "2022-04-26T00:00:00", "2022-04-27T00:00:00", "2022-04-29T00:00:00", "2022-05-05T00:00:00", "2022-05-09T00:00:00", "2022-05-10T00:00:00", "2022-05-12T00:00:00", "2022-05-13T00:00:00", "2022-05-19T00:00:00", "2022-05-25T00:00:00", "2022-05-27T00:00:00", "2022-05-31T00:00:00", "2022-06-06T00:00:00", "2022-06-07T00:00:00", "2022-06-10T00:00:00", "2022-06-14T00:00:00", "2022-06-15T00:00:00", "2022-06-17T00:00:00", "2022-06-20T00:00:00", "2022-06-21T00:00:00", "2022-06-23T00:00:00", "2022-06-24T00:00:00", "2022-06-27T00:00:00", "2022-06-28T00:00:00", "2022-06-29T00:00:00", "2022-06-30T00:00:00", "2022-07-07T00:00:00", "2022-07-08T00:00:00", "2022-07-12T00:00:00", "2022-07-13T00:00:00", "2022-07-18T00:00:00", "2022-07-19T00:00:00", "2022-07-20T00:00:00", "2022-07-22T00:00:00", "2022-07-25T00:00:00", "2022-07-26T00:00:00", "2022-07-27T00:00:00", "2022-07-28T00:00:00", "2022-08-04T00:00:00", "2022-08-05T00:00:00", "2022-08-08T00:00:00", "2022-08-11T00:00:00", "2022-08-12T00:00:00", "2022-08-16T00:00:00", "2022-08-17T00:00:00", "2022-08-22T00:00:00", "2022-08-25T00:00:00", "2022-08-29T00:00:00", "2022-08-30T00:00:00", "2022-09-01T00:00:00", "2022-09-02T00:00:00", "2022-09-05T00:00:00", "2022-09-06T00:00:00", "2022-09-09T00:00:00", "2022-09-14T00:00:00", "2022-09-15T00:00:00", "2022-09-21T00:00:00", "2022-09-27T00:00:00", "2022-09-30T00:00:00", "2022-10-12T00:00:00", "2022-10-14T00:00:00", "2022-10-17T00:00:00", "2022-10-20T00:00:00", "2022-10-26T00:00:00", "2022-10-27T00:00:00", "2022-11-01T00:00:00", "2022-11-02T00:00:00", "2022-11-04T00:00:00", "2022-11-07T00:00:00", "2022-11-08T00:00:00", "2022-11-09T00:00:00", "2022-11-10T00:00:00", "2022-11-11T00:00:00", "2022-11-15T00:00:00", "2022-11-16T00:00:00", "2022-11-17T00:00:00", "2022-11-24T00:00:00", "2022-11-25T00:00:00", "2022-11-28T00:00:00", "2022-11-29T00:00:00", "2022-12-01T00:00:00", "2022-12-02T00:00:00", "2022-12-05T00:00:00", "2022-12-08T00:00:00", "2022-12-09T00:00:00", "2022-12-12T00:00:00", "2022-12-13T00:00:00", "2022-12-15T00:00:00", "2022-12-16T00:00:00", "2022-12-19T00:00:00", "2022-12-23T00:00:00", "2022-12-27T00:00:00", "2022-12-30T00:00:00", "2023-01-03T00:00:00", "2023-01-04T00:00:00", "2023-01-13T00:00:00", "2023-01-19T00:00:00", "2023-01-20T00:00:00", "2023-01-31T00:00:00", "2023-02-07T00:00:00", "2023-02-08T00:00:00", "2023-02-10T00:00:00", "2023-02-13T00:00:00", "2023-02-17T00:00:00", "2023-02-20T00:00:00", "2023-02-21T00:00:00", "2023-02-23T00:00:00", "2023-02-28T00:00:00", "2023-03-01T00:00:00"], "type": "bar", "x": [0, 4, 5, 6, 7, 8, 13, 14, 17, 18, 19, 20, 21, 22, 23, 25, 27, 28, 29, 32, 34, 36, 39, 44, 45, 47, 48, 50, 51, 53, 54, 58, 62, 64, 66, 69, 70, 73, 75, 76, 78, 79, 80, 82, 83, 84, 85, 86, 87, 92, 93, 95, 96, 99, 100, 101, 103, 104, 105, 106, 107, 112, 113, 114, 117, 118, 120, 121, 124, 127, 129, 130, 132, 133, 134, 135, 138, 140, 141, 145, 149, 152, 155, 157, 158, 161, 165, 166, 169, 170, 172, 173, 174, 175, 176, 177, 179, 180, 181, 186, 187, 188, 189, 191, 192, 193, 196, 197, 198, 199, 201, 202, 203, 207, 209, 212, 213, 214, 221, 225, 226, 228, 233, 234, 236, 237, 241, 242, 243, 245, 248, 249], "xaxis": "x2", "y": [24430600, 11819300, 7297300, 10093501, 7916005, 13176647, 9558200, 10150040, 16002800, 18880810, 20136900, 28508708, 59610802, 66744872, 53677751, 127465874, 100160308, 123693450, 186127406, 78486766, 53500244, 41477310, 21338203, 26548560, 35091700, 57223700, 47096431, 34460500, 29152380, 27671180, 40540477, 35354667, 19955491, 16101200, 12271729, 15755233, 17850858, 10349350, 19204601, 26744810, 12029809, 11128298, 17017300, 8361611, 8797080, 7630657, 10215076, 17142400, 14850034, 5705984, 5590133, 6743300, 11958810, 17968420, 10064379, 7579900, 6494300, 11154680, 23718605, 21383664, 21351740, 7524600, 6377200, 10855941, 7121400, 6100689, 44761764, 37027461, 11097720, 9872001, 6174200, 7162234, 11453500, 7816801, 11057201, 34306996, 26424771, 12879600, 16429680, 15349680, 7799300, 8095444, 8213500, 11299558, 10331034, 7477900, 16498923, 21724001, 9268538, 7650398, 8184500, 8504900, 9490948, 12492000, 10575799, 22743940, 19579900, 22409700, 20088840, 14848130, 22939170, 23228380, 23256610, 77139992, 57515164, 17944532, 60141845, 74662480, 39342200, 45270640, 27710873, 29036180, 34773485, 41501600, 24701500, 15418501, 15595803, 26172916, 8013800, 8154300, 9955602, 15804220, 11622022, 12003891, 11895920, 12164620, 9347802, 6251560, 13015400, 9096241, 9825258, 9673946], "yaxis": "y2"}, {"marker": {"color": "green"}, "name": "close<open成交量", "text": ["2022-02-22T00:00:00", "2022-02-23T00:00:00", "2022-02-24T00:00:00", "2022-03-04T00:00:00", "2022-03-07T00:00:00", "2022-03-08T00:00:00", "2022-03-09T00:00:00", "2022-03-14T00:00:00", "2022-03-15T00:00:00", "2022-03-25T00:00:00", "2022-03-29T00:00:00", "2022-04-06T00:00:00", "2022-04-07T00:00:00", "2022-04-11T00:00:00", "2022-04-13T00:00:00", "2022-04-15T00:00:00", "2022-04-18T00:00:00", "2022-04-20T00:00:00", "2022-04-21T00:00:00", "2022-04-22T00:00:00", "2022-04-25T00:00:00", "2022-04-28T00:00:00", "2022-05-06T00:00:00", "2022-05-11T00:00:00", "2022-05-16T00:00:00", "2022-05-17T00:00:00", "2022-05-18T00:00:00", "2022-05-20T00:00:00", "2022-05-23T00:00:00", "2022-05-24T00:00:00", "2022-05-26T00:00:00", "2022-05-30T00:00:00", "2022-06-01T00:00:00", "2022-06-02T00:00:00", "2022-06-08T00:00:00", "2022-06-09T00:00:00", "2022-06-13T00:00:00", "2022-06-16T00:00:00", "2022-06-22T00:00:00", "2022-07-01T00:00:00", "2022-07-04T00:00:00", "2022-07-05T00:00:00", "2022-07-06T00:00:00", "2022-07-11T00:00:00", "2022-07-14T00:00:00", "2022-07-15T00:00:00", "2022-07-21T00:00:00", "2022-07-29T00:00:00", "2022-08-01T00:00:00", "2022-08-02T00:00:00", "2022-08-03T00:00:00", "2022-08-09T00:00:00", "2022-08-10T00:00:00", "2022-08-15T00:00:00", "2022-08-18T00:00:00", "2022-08-19T00:00:00", "2022-08-23T00:00:00", "2022-08-24T00:00:00", "2022-08-26T00:00:00", "2022-08-31T00:00:00", "2022-09-07T00:00:00", "2022-09-08T00:00:00", "2022-09-13T00:00:00", "2022-09-16T00:00:00", "2022-09-19T00:00:00", "2022-09-20T00:00:00", "2022-09-22T00:00:00", "2022-09-23T00:00:00", "2022-09-26T00:00:00", "2022-09-28T00:00:00", "2022-09-29T00:00:00", "2022-10-10T00:00:00", "2022-10-11T00:00:00", "2022-10-13T00:00:00", "2022-10-18T00:00:00", "2022-10-19T00:00:00", "2022-10-21T00:00:00", "2022-10-24T00:00:00", "2022-10-25T00:00:00", "2022-10-28T00:00:00", "2022-10-31T00:00:00", "2022-11-03T00:00:00", "2022-11-14T00:00:00", "2022-11-18T00:00:00", "2022-11-21T00:00:00", "2022-11-22T00:00:00", "2022-11-23T00:00:00", "2022-11-30T00:00:00", "2022-12-06T00:00:00", "2022-12-07T00:00:00", "2022-12-14T00:00:00", "2022-12-20T00:00:00", "2022-12-21T00:00:00", "2022-12-22T00:00:00", "2022-12-26T00:00:00", "2022-12-28T00:00:00", "2022-12-29T00:00:00", "2023-01-05T00:00:00", "2023-01-06T00:00:00", "2023-01-09T00:00:00", "2023-01-10T00:00:00", "2023-01-11T00:00:00", "2023-01-12T00:00:00", "2023-01-16T00:00:00", "2023-01-17T00:00:00", "2023-01-18T00:00:00", "2023-01-30T00:00:00", "2023-02-01T00:00:00", "2023-02-02T00:00:00", "2023-02-03T00:00:00", "2023-02-06T00:00:00", "2023-02-09T00:00:00", "2023-02-14T00:00:00", "2023-02-15T00:00:00", "2023-02-16T00:00:00", "2023-02-22T00:00:00", "2023-02-24T00:00:00", "2023-02-27T00:00:00"], "type": "bar", "x": [1, 2, 3, 9, 10, 11, 12, 15, 16, 24, 26, 30, 31, 33, 35, 37, 38, 40, 41, 42, 43, 46, 49, 52, 55, 56, 57, 59, 60, 61, 63, 65, 67, 68, 71, 72, 74, 77, 81, 88, 89, 90, 91, 94, 97, 98, 102, 108, 109, 110, 111, 115, 116, 119, 122, 123, 125, 126, 128, 131, 136, 137, 139, 142, 143, 144, 146, 147, 148, 150, 151, 153, 154, 156, 159, 160, 162, 163, 164, 167, 168, 171, 178, 182, 183, 184, 185, 190, 194, 195, 200, 204, 205, 206, 208, 210, 211, 215, 216, 217, 218, 219, 220, 222, 223, 224, 227, 229, 230, 231, 232, 235, 238, 239, 240, 244, 246, 247], "xaxis": "x2", "y": [24430600, 11819300, 7297300, 10093501, 7916005, 13176647, 9558200, 10150040, 16002800, 18880810, 20136900, 28508708, 59610802, 66744872, 53677751, 127465874, 100160308, 123693450, 186127406, 78486766, 53500244, 41477310, 21338203, 26548560, 35091700, 57223700, 47096431, 34460500, 29152380, 27671180, 40540477, 35354667, 19955491, 16101200, 12271729, 15755233, 17850858, 10349350, 19204601, 26744810, 12029809, 11128298, 17017300, 8361611, 8797080, 7630657, 10215076, 17142400, 14850034, 5705984, 5590133, 6743300, 11958810, 17968420, 10064379, 7579900, 6494300, 11154680, 23718605, 21383664, 21351740, 7524600, 6377200, 10855941, 7121400, 6100689, 44761764, 37027461, 11097720, 9872001, 6174200, 7162234, 11453500, 7816801, 11057201, 34306996, 26424771, 12879600, 16429680, 15349680, 7799300, 8095444, 8213500, 11299558, 10331034, 7477900, 16498923, 21724001, 9268538, 7650398, 8184500, 8504900, 9490948, 12492000, 10575799, 22743940, 19579900, 22409700, 20088840, 14848130, 22939170, 23228380, 23256610, 77139992, 57515164, 17944532, 60141845, 74662480, 39342200, 45270640, 27710873, 29036180, 34773485, 41501600, 24701500, 15418501, 15595803, 26172916, 8013800, 8154300, 9955602, 15804220, 11622022, 12003891, 11895920, 12164620, 9347802, 6251560, 13015400, 9096241, 9825258, 9673946], "yaxis": "y2"}, {"mode": "lines", "name": "RSI_6", "text": ["2022-03-15T00:00:00", "2022-04-25T00:00:00", "2022-07-15T00:00:00", "2022-08-03T00:00:00", "2022-10-25T00:00:00"], "type": "scatter", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249], "xaxis": "x3", "y": [null, null, null, null, null, null, 27.27271249471009, 32.77313323539474, 44.52150703497698, 32.74841606441038, 31.10344497499902, 20.16443883681768, 16.650801610712694, 25.325433955736482, 28.87845084733357, 24.655322324161542, 15.274037569081726, 33.944873735606414, 44.16653420406348, 55.24699806441231, 63.854833689608945, 72.36071362982356, 82.91476095112661, 88.59079194968686, 67.38310059705341, 73.47818097055402, 53.1264452252582, 54.23933843355548, 66.37435468035638, 75.1237772567786, 58.57586886169896, 45.33193892275709, 48.95890276712368, 41.204986669216474, 40.61127829025267, 36.50821171949407, 42.89860731820484, 41.238364504665704, 36.94847426960559, 42.01531588317275, 37.380689288421095, 29.216277813793067, 26.643544773567427, 20.704974602741284, 26.880861108951613, 25.304208177204522, 24.86665429924571, 27.860458575597207, 36.910494443887124, 30.111025749811127, 43.89222212435729, 47.61116491175728, 40.15820548246314, 39.10871924159219, 52.51197524529941, 61.54171334174462, 59.83509274921367, 49.876540281781466, 57.02843931075, 51.715514381030836, 51.715514381030836, 33.65540542987141, 41.78704780311372, 40.30503063026862, 42.74188641443437, 35.7390182574262, 35.7390182574262, 31.96790996497092, 23.16742431300613, 30.788727222121647, 44.09721021257399, 34.225493287527954, 28.17126078358606, 28.17126078358606, 21.367635080037157, 46.278261153587565, 54.85866385464411, 47.96396646374353, 41.67817556474896, 49.603648410746075, 56.669563177930925, 42.40158023824732, 47.671577067528105, 45.19072783530894, 48.41230279171943, 51.81122582630972, 61.04984180298604, 66.22936983584236, 57.11525804281029, 42.93494346046366, 33.0795396173456, 25.935524052889814, 30.442214249238837, 35.17545016643215, 30.237226320391752, 35.65708851454042, 56.11372157695973, 36.5769823343556, 17.13999344675998, 43.6276250074381, 45.131037390569, 45.131037390569, 41.91188543854103, 41.91188543854103, 51.81119310309339, 71.05142056922222, 73.93349495639839, 78.96064261487209, 64.12108526894937, 50.76442744381474, 33.05752946582533, 29.009999971388527, 36.47662628979582, 41.969805596188834, 49.02298034141266, 44.18917636478671, 38.16767232759305, 44.922382649575546, 51.305752888997446, 49.032644232446955, 72.4599029304743, 75.04118359419981, 63.19490774448534, 56.74514989252154, 56.74514989252154, 54.09506253486964, 35.95841844208171, 43.531373334323035, 35.20528240582742, 43.04821714697123, 43.04821714697123, 36.65856829707283, 46.2349813284404, 54.49136793799354, 68.17536917587059, 80.94456011958322, 82.25873307722642, 73.17491776400973, 80.80561386821843, 61.994811947887406, 57.94790275556553, 63.64375115432222, 43.983955966611965, 38.7594060208661, 38.75940602086611, 59.527554855978806, 46.82419271929372, 41.5092697084774, 30.964935743432715, 38.46767995909671, 29.00945435360506, 24.043624242856218, 32.02281209048543, 27.663717829046586, 25.967035992205684, 35.46625641744342, 32.93101568255196, 42.75239372841913, 49.41867196130332, 47.21926634149936, 38.907571000572005, 38.907571000572005, 36.588984826360964, 23.274181165435824, 18.28397616115127, 42.11374705723897, 58.46522402833342, 44.92805407119606, 42.00993174127666, 48.07999373306896, 52.09179484251424, 45.73130250158141, 56.38277084853763, 63.28365555403161, 66.48873364277237, 72.26948742520246, 74.87042552979594, 83.91976389180678, 88.4224455618978, 88.83913770289587, 90.11936174283021, 90.55282298625575, 66.16910940313487, 50.009478804411245, 44.42927767342438, 37.698777198030335, 49.23434209687702, 66.89021181225141, 60.57044418593577, 81.52106886791, 81.90564111356856, 85.8051278616861, 92.11106363358233, 95.05645375239787, 66.18432041948327, 48.15735204273242, 46.94595136952923, 53.49674448694724, 47.918198804305355, 52.755225563229594, 48.000086342696584, 48.00008634269659, 51.16910593970114, 58.71683853276788, 43.41621367521533, 40.115163157730635, 46.89814532612688, 56.93300582015707, 47.88415414341474, 53.235667264879346, 47.39550151654633, 41.88193990459571, 45.67381351912371, 51.944536057154764, 62.369179759584995, 49.48696554875989, 48.290061089962755, 41.12796555622295, 34.91408938326327, 29.555558020605666, 25.623000822666086, 27.924623079477996, 25.125554425070927, 22.42784581073333, 21.504266156414857, 20.491672741596503, 38.00662864850433, 41.11960837606402, 47.45239956297536, 47.45239956297536, 38.50669628386936, 33.46035274722814, 27.07380925322586, 44.13454689266541, 51.01095870458211, 44.445993658349806, 51.877818793740005, 63.573158810951604, 59.253005239674195, 47.606531843337244, 30.70684231148165, 38.225389353858276, 50.98847340045545, 60.725654117188064, 57.30990515715153, 62.387491156108375, 51.38608488195951, 40.07825088863812, 54.94891613128624, 61.77839187975859], "yaxis": "y3"}, {"marker": {"color": "pink", "line": {"color": "midnightblue", "width": 1}, "size": 9, "symbol": "triangle-up"}, "mode": "markers", "name": "RSI_6低处买点", "text": ["2022-03-15T00:00:00", "2022-04-25T00:00:00", "2022-07-15T00:00:00", "2022-08-03T00:00:00", "2022-10-25T00:00:00"], "type": "scatter", "x": [16, 43, 98, 111, 164], "xaxis": "x", "y": [2.5739999055862426, 2.682000017166138, 2.403000068664551, 2.4570000171661377, 2.321999931335449], "yaxis": "y"}, {"marker": {"color": "pink", "line": {"color": "midnightblue", "width": 1}, "size": 9, "symbol": "triangle-up"}, "mode": "markers", "name": "RSI_6低处买点", "text": ["2022-03-15T00:00:00", "2022-04-25T00:00:00", "2022-07-15T00:00:00", "2022-08-03T00:00:00", "2022-10-25T00:00:00"], "type": "scatter", "x": [16, 43, 98, 111, 164], "xaxis": "x3", "y": [10.274037569081726, 15.704974602741284, 12.13999344675998, 24.009999971388527, 13.28397616115127], "yaxis": "y3"}, {"line": {"color": "black"}, "mode": "lines", "name": "间隔背离收盘价标记点", "opacity": 0.6, "text": ["2022-07-15T00:00:00", "2022-10-25T00:00:00"], "type": "scatter", "x": [98, 164], "xaxis": "x", "y": [2.6700000762939453, 2.5799999237060547], "yaxis": "y"}, {"line": {"color": "black"}, "mode": "lines", "name": "间隔背离RSI标记点", "opacity": 0.6, "text": ["2022-07-15T00:00:00", "2022-10-25T00:00:00"], "type": "scatter", "x": [98, 164], "xaxis": "x3", "y": [17.13999344675998, 18.28397616115127], "yaxis": "y3"}], "layout": {"height": 600, "shapes": [{"line": {"color": "grey", "width": 1}, "type": "line", "x0": 0, "x1": 249, "xref": "x2", "y0": 30, "y1": 30, "yref": "y2"}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "000608.XSHE: 阳光股份最后close低点与RSI6出现间隔背离"}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 1], "matches": "x3", "rangeslider": {"visible": false}, "showspikes": true, "showticklabels": false, "spikethickness": 2}, "xaxis2": {"anchor": "y2", "domain": [0, 1], "matches": "x3", "showspikes": true, "showticklabels": false, "spikethickness": 2}, "xaxis3": {"anchor": "y3", "domain": [0, 1], "showspikes": true, "spikethickness": 2}, "yaxis": {"anchor": "x", "domain": [0.5, 1], "showspikes": true, "spikethickness": 2}, "yaxis2": {"anchor": "x2", "domain": [0.3, 0.5], "showspikes": true, "spikethickness": 2}, "yaxis3": {"anchor": "x3", "domain": [0, 0.3], "showspikes": true, "spikethickness": 2}}}, "text/html": ["<div>                            <div id=\"73da5d89-875c-488c-b78b-c12d6918560c\" class=\"plotly-graph-div\" style=\"height:600px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"73da5d89-875c-488c-b78b-c12d6918560c\")) {                    Plotly.newPlot(                        \"73da5d89-875c-488c-b78b-c12d6918560c\",                        [{\"close\":[3.569999933242798,3.440000057220459,3.359999895095825,3.2699999809265137,3.3299999237060547,3.309999942779541,3.369999885559082,3.4000000953674316,3.4700000286102295,3.3499999046325684,3.3299999237060547,3.1500000953674316,3.059999942779541,3.109999895095825,3.130000114440918,3.069999933242798,2.859999895095825,2.990000009536743,3.0799999237060547,3.200000047683716,3.319999933242798,3.4800000190734863,3.8299999237060547,4.210000038146973,3.9100000858306885,4.150000095367432,3.740000009536743,3.7699999809265137,4.150000095367432,4.570000171661377,4.190000057220459,3.7699999809265137,3.880000114440918,3.619999885559082,3.5999999046325684,3.4700000286102295,3.5899999141693115,3.549999952316284,3.450000047683716,3.5199999809265137,3.430000066757202,3.240000009536743,3.1700000762939453,2.9800000190734863,3.0399999618530273,3.0,2.990000009536743,3.009999990463257,3.069999933242798,2.9800000190734863,3.0799999237060547,3.109999895095825,3.0399999618530273,3.0299999713897705,3.119999885559082,3.200000047683716,3.190000057220459,3.130000114440918,3.180000066757202,3.1500000953674316,3.1500000953674316,3.0299999713897705,3.069999933242798,3.059999942779541,3.069999933242798,3.0299999713897705,3.0299999713897705,3.009999990463257,2.950000047683716,2.9700000286102295,3.009999990463257,2.9600000381469727,2.9200000762939453,2.9200000762939453,2.869999885559082,2.950000047683716,2.990000009536743,2.9600000381469727,2.930000066757202,2.9600000381469727,2.990000009536743,2.930000066757202,2.950000047683716,2.940000057220459,2.950000047683716,2.9600000381469727,2.990000009536743,3.009999990463257,2.990000009536743,2.950000047683716,2.9100000858306885,2.869999885559082,2.880000114440918,2.890000104904175,2.869999885559082,2.880000114440918,2.930000066757202,2.859999895095825,2.6700000762939453,2.809999942779541,2.819999933242798,2.819999933242798,2.799999952316284,2.799999952316284,2.8399999141693115,2.9700000286102295,3.0,3.059999942779541,3.0,2.930000066757202,2.7799999713897705,2.7300000190734863,2.7699999809265137,2.799999952316284,2.8399999141693115,2.809999942779541,2.7699999809265137,2.799999952316284,2.8299999237060547,2.819999933242798,2.9800000190734863,3.009999990463257,2.9600000381469727,2.930000066757202,2.930000066757202,2.9200000762939453,2.8299999237060547,2.859999895095825,2.809999942779541,2.8399999141693115,2.8399999141693115,2.809999942779541,2.8399999141693115,2.869999885559082,2.940000057220459,3.069999933242798,3.0899999141693115,3.059999942779541,3.1500000953674316,3.069999933242798,3.049999952316284,3.0899999141693115,2.9800000190734863,2.940000057220459,2.940000057220459,3.059999942779541,2.9800000190734863,2.940000057220459,2.8399999141693115,2.880000114440918,2.7799999713897705,2.7100000381469727,2.75,2.700000047683716,2.680000066757202,2.7200000286102295,2.700000047683716,2.740000009536743,2.7699999809265137,2.759999990463257,2.7200000286102295,2.7200000286102295,2.7100000381469727,2.630000114440918,2.5799999237060547,2.6600000858306885,2.75,2.6700000762939453,2.6500000953674316,2.680000066757202,2.700000047683716,2.6700000762939453,2.7200000286102295,2.7599000930786133,2.7799999713897705,2.819999933242798,2.8399999141693115,2.940000057220459,3.0299999713897705,3.0399999618530273,3.069999933242798,3.0799999237060547,3.009999990463257,2.940000057220459,2.9100000858306885,2.869999885559082,2.9200000762939453,3.0399999618530273,3.009999990463257,3.309999942779541,3.319999933242798,3.430000066757202,3.7699999809265137,4.150000095367432,3.7799999713897705,3.4000000953674316,3.369999885559082,3.509999990463257,3.4000000953674316,3.490000009536743,3.4100000858306885,3.4100000858306885,3.450000047683716,3.549999952316284,3.359999895095825,3.309999942779541,3.380000114440918,3.5,3.4000000953674316,3.4600000381469727,3.4000000953674316,3.3399999141693115,3.369999885559082,3.4200000762939453,3.5199999809265137,3.4200000762939453,3.4100000858306885,3.3499999046325684,3.2899999618530273,3.2300000190734863,3.180000066757202,3.190000057220459,3.1600000858306885,3.130000114440918,3.119999885559082,3.109999895095825,3.1600000858306885,3.1700000762939453,3.190000057220459,3.190000057220459,3.1600000858306885,3.140000104904175,3.109999895095825,3.1500000953674316,3.1700000762939453,3.1500000953674316,3.1700000762939453,3.2100000381469727,3.200000047683716,3.1700000762939453,3.0999999046325684,3.119999885559082,3.1600000858306885,3.200000047683716,3.190000057220459,3.2100000381469727,3.180000066757202,3.140000104904175,3.190000057220459,3.2200000286102295],\"decreasing\":{\"line\":{\"color\":\"green\"}},\"high\":[3.640000104904175,3.5899999141693115,3.4800000190734863,3.390000104904175,3.369999885559082,3.3399999141693115,3.4000000953674316,3.430000066757202,3.4700000286102295,3.4700000286102295,3.430000066757202,3.3499999046325684,3.200000047683716,3.1600000858306885,3.140000104904175,3.240000009536743,3.0899999141693115,3.009999990463257,3.1600000858306885,3.2100000381469727,3.390000104904175,3.5999999046325684,3.8299999237060547,4.210000038146973,4.360000133514404,4.300000190734863,4.0,3.930000066757202,4.150000095367432,4.570000171661377,4.550000190734863,4.010000228881836,3.9200000762939453,3.8399999141693115,3.700000047683716,3.5999999046325684,3.6700000762939453,3.6700000762939453,3.5999999046325684,3.549999952316284,3.5299999713897705,3.4200000762939453,3.2799999713897705,3.180000066757202,3.1500000953674316,3.069999933242798,3.0399999618530273,3.049999952316284,3.0799999237060547,3.0199999809265137,3.140000104904175,3.109999895095825,3.130000114440918,3.069999933242798,3.130000114440918,3.2899999618530273,3.240000009536743,3.190000057220459,3.25,3.240000009536743,3.180000066757202,3.1500000953674316,3.0899999141693115,3.140000104904175,3.0999999046325684,3.0799999237060547,3.0399999618530273,3.059999942779541,3.009999990463257,2.9700000286102295,3.0199999809265137,3.0199999809265137,2.9800000190734863,2.940000057220459,2.9100000858306885,2.9700000286102295,3.0299999713897705,3.0199999809265137,2.9600000381469727,2.9700000286102295,3.0399999618530273,2.990000009536743,2.9600000381469727,2.9600000381469727,2.9700000286102295,2.9800000190734863,3.0199999809265137,3.0199999809265137,3.0299999713897705,2.990000009536743,2.9700000286102295,2.9100000858306885,2.9100000858306885,2.9100000858306885,2.890000104904175,2.9000000953674316,2.9600000381469727,2.9200000762939453,2.8399999141693115,2.8499999046325684,2.8299999237060547,2.8399999141693115,2.8299999237060547,2.8299999237060547,2.890000104904175,2.990000009536743,3.059999942779541,3.0899999141693115,3.0899999141693115,3.009999990463257,2.930000066757202,2.8299999237060547,2.7799999713897705,2.809999942779541,2.9000000953674316,2.8499999046325684,2.799999952316284,2.8299999237060547,2.8299999237060547,2.8399999141693115,3.0999999046325684,3.059999942779541,3.0799999237060547,3.0,2.9600000381469727,2.940000057220459,2.940000057220459,2.880000114440918,2.869999885559082,2.8499999046325684,2.890000104904175,2.859999895095825,2.9000000953674316,2.890000104904175,2.9600000381469727,3.130000114440918,3.119999885559082,3.109999895095825,3.180000066757202,3.1600000858306885,3.119999885559082,3.130000114440918,3.119999885559082,3.0,3.059999942779541,3.0799999237060547,3.0899999141693115,3.059999942779541,2.9700000286102295,2.9100000858306885,2.890000104904175,2.809999942779541,2.7899999618530273,2.799999952316284,2.7200000286102295,2.7200000286102295,2.7300000190734863,2.759999990463257,2.7799999713897705,2.7899999618530273,2.759999990463257,2.75,2.759999990463257,2.7200000286102295,2.640000104904175,2.7100000381469727,2.7799999713897705,2.7699999809265137,2.680000066757202,2.700000047683716,2.7200000286102295,2.700000047683716,2.7300000190734863,2.7699999809265137,2.799999952316284,2.859999895095825,2.859999895095825,3.0,3.0899999141693115,3.119999885559082,3.1700000762939453,3.140000104904175,3.130000114440918,3.009999990463257,3.0299999713897705,2.9200000762939453,2.9800000190734863,3.049999952316284,3.059999942779541,3.309999942779541,3.549999952316284,3.430000066757202,3.7699999809265137,4.150000095367432,4.150000095367432,3.5999999046325684,3.450000047683716,3.569999933242798,3.5,3.5399999618530273,3.5299999713897705,3.450000047683716,3.5399999618530273,3.559999942779541,3.4600000381469727,3.4000000953674316,3.549999952316284,3.700000047683716,3.4800000190734863,3.5199999809265137,3.4600000381469727,3.430000066757202,3.4100000858306885,3.440000057220459,3.549999952316284,3.549999952316284,3.4800000190734863,3.4100000858306885,3.369999885559082,3.319999933242798,3.259999990463257,3.2200000286102295,3.200000047683716,3.1700000762939453,3.1500000953674316,3.119999885559082,3.1700000762939453,3.259999990463257,3.200000047683716,3.2200000286102295,3.190000057220459,3.1600000858306885,3.1500000953674316,3.180000066757202,3.200000047683716,3.1700000762939453,3.2100000381469727,3.2200000286102295,3.240000009536743,3.2100000381469727,3.200000047683716,3.1700000762939453,3.1600000858306885,3.259999990463257,3.200000047683716,3.240000009536743,3.2300000190734863,3.190000057220459,3.200000047683716,3.240000009536743],\"increasing\":{\"line\":{\"color\":\"red\"}},\"low\":[3.5,3.4200000762939453,3.3399999141693115,3.049999952316284,3.25,3.25,3.309999942779541,3.3499999046325684,3.390000104904175,3.3399999141693115,3.309999942779541,3.130000114440918,2.930000066757202,3.059999942779541,3.009999990463257,3.059999942779541,2.859999895095825,2.8399999141693115,3.049999952316284,3.0399999618530273,3.1700000762939453,3.2200000286102295,3.369999885559082,4.059999942779541,3.7899999618530273,3.7100000381469727,3.740000009536743,3.6600000858306885,3.7899999618530273,3.940000057220459,4.110000133514404,3.7699999809265137,3.6700000762939453,3.549999952316284,3.4800000190734863,3.4200000762939453,3.4800000190734863,3.5199999809265137,3.4000000953674316,3.390000104904175,3.369999885559082,3.200000047683716,3.1500000953674316,2.950000047683716,2.950000047683716,2.8299999237060547,2.9000000953674316,2.9700000286102295,2.9700000286102295,2.859999895095825,2.9800000190734863,2.990000009536743,3.0199999809265137,2.950000047683716,2.9800000190734863,3.119999885559082,3.0799999237060547,3.109999895095825,3.049999952316284,3.130000114440918,3.119999885559082,3.009999990463257,3.0,3.049999952316284,3.0299999713897705,3.009999990463257,2.9800000190734863,2.990000009536743,2.950000047683716,2.9100000858306885,2.940000057220459,2.9200000762939453,2.9200000762939453,2.890000104904175,2.859999895095825,2.8299999237060547,2.950000047683716,2.950000047683716,2.890000104904175,2.9100000858306885,2.950000047683716,2.9200000762939453,2.9200000762939453,2.930000066757202,2.930000066757202,2.930000066757202,2.950000047683716,2.950000047683716,2.9800000190734863,2.940000057220459,2.890000104904175,2.859999895095825,2.859999895095825,2.859999895095825,2.859999895095825,2.8499999046325684,2.869999885559082,2.8499999046325684,2.6600000858306885,2.6700000762939453,2.7899999618530273,2.7899999618530273,2.7899999618530273,2.7699999809265137,2.799999952316284,2.809999942779541,2.950000047683716,3.0,2.990000009536743,2.9200000762939453,2.75,2.7200000286102295,2.700000047683716,2.75,2.799999952316284,2.7799999713897705,2.75,2.7699999809265137,2.7799999713897705,2.799999952316284,2.809999942779541,2.950000047683716,2.930000066757202,2.9200000762939453,2.9000000953674316,2.9000000953674316,2.8299999237060547,2.809999942779541,2.799999952316284,2.759999990463257,2.819999933242798,2.799999952316284,2.799999952316284,2.8299999237060547,2.869999885559082,2.930000066757202,3.0,3.0299999713897705,3.069999933242798,3.0399999618530273,3.0299999713897705,3.009999990463257,2.9800000190734863,2.9000000953674316,2.9200000762939453,2.9000000953674316,2.9600000381469727,2.930000066757202,2.8399999141693115,2.809999942779541,2.7799999713897705,2.680000066757202,2.7100000381469727,2.680000066757202,2.619999885559082,2.630000114440918,2.6500000953674316,2.690000057220459,2.680000066757202,2.740000009536743,2.7100000381469727,2.690000057220459,2.700000047683716,2.630000114440918,2.549999952316284,2.609999895095825,2.680000066757202,2.6600000858306885,2.5899999141693115,2.630000114440918,2.680000066757202,2.6600000858306885,2.6700000762939453,2.700000047683716,2.7300000190734863,2.759999990463257,2.7799999713897705,2.859999895095825,2.950000047683716,3.0,3.049999952316284,3.0399999618530273,3.0,2.9200000762939453,2.880000114440918,2.859999895095825,2.880000114440918,2.9200000762939453,2.940000057220459,3.299999952316284,3.25,3.240000009536743,3.299999952316284,4.150000095367432,3.740000009536743,3.4000000953674316,3.3299999237060547,3.380000114440918,3.3299999237060547,3.369999885559082,3.4000000953674316,3.3399999141693115,3.390000104904175,3.4000000953674316,3.200000047683716,3.309999942779541,3.359999895095825,3.369999885559082,3.3499999046325684,3.380000114440918,3.3499999046325684,3.3399999141693115,3.319999933242798,3.359999895095825,3.4100000858306885,3.4000000953674316,3.319999933242798,3.3399999141693115,3.2699999809265137,3.2200000286102295,3.1700000762939453,3.1500000953674316,3.130000114440918,3.0999999046325684,3.0999999046325684,3.0799999237060547,3.0899999141693115,3.140000104904175,3.0899999141693115,3.1500000953674316,3.140000104904175,3.0999999046325684,3.0999999046325684,3.0899999141693115,3.140000104904175,3.130000114440918,3.140000104904175,3.1700000762939453,3.180000066757202,3.1500000953674316,3.069999933242798,3.0899999141693115,3.109999895095825,3.180000066757202,3.1600000858306885,3.180000066757202,3.140000104904175,3.130000114440918,3.119999885559082,3.190000057220459],\"name\":\"K\\u7ebf\",\"open\":[3.5299999713897705,3.5199999809265137,3.4600000381469727,3.3499999046325684,3.259999990463257,3.309999942779541,3.309999942779541,3.359999895095825,3.4100000858306885,3.4600000381469727,3.3499999046325684,3.3499999046325684,3.180000066757202,3.0899999141693115,3.059999942779541,3.0999999046325684,3.0899999141693115,2.9100000858306885,3.059999942779541,3.059999942779541,3.200000047683716,3.309999942779541,3.4000000953674316,4.210000038146973,4.199999809265137,3.799999952316284,4.0,3.7300000190734863,3.7899999618530273,4.320000171661377,4.389999866485596,3.930000066757202,3.7699999809265137,3.8299999237060547,3.5999999046325684,3.549999952316284,3.4800000190734863,3.5899999141693115,3.509999990463257,3.4800000190734863,3.5,3.4200000762939453,3.2200000286102295,3.130000114440918,3.0,2.990000009536743,3.0,3.009999990463257,3.0299999713897705,3.0,3.009999990463257,3.0299999713897705,3.119999885559082,3.0199999809265137,3.0199999809265137,3.2300000190734863,3.240000009536743,3.1700000762939453,3.069999933242798,3.1700000762939453,3.1600000858306885,3.1500000953674316,3.009999990463257,3.0799999237060547,3.069999933242798,3.0799999237060547,3.0299999713897705,3.0399999618530273,3.009999990463257,2.950000047683716,2.9700000286102295,3.009999990463257,2.950000047683716,2.9000000953674316,2.9000000953674316,2.869999885559082,2.9600000381469727,2.9800000190734863,2.930000066757202,2.930000066757202,2.9700000286102295,2.9800000190734863,2.930000066757202,2.940000057220459,2.940000057220459,2.940000057220459,2.9600000381469727,2.990000009536743,3.009999990463257,2.9700000286102295,2.9600000381469727,2.9100000858306885,2.869999885559082,2.890000104904175,2.890000104904175,2.880000114440918,2.890000104904175,2.9200000762939453,2.8399999141693115,2.690000057220459,2.799999952316284,2.799999952316284,2.819999933242798,2.799999952316284,2.819999933242798,2.8299999237060547,2.9800000190734863,3.0,3.0899999141693115,2.990000009536743,2.9100000858306885,2.7799999713897705,2.740000009536743,2.7699999809265137,2.819999933242798,2.8399999141693115,2.799999952316284,2.7799999713897705,2.809999942779541,2.8399999141693115,2.819999933242798,2.9600000381469727,3.009999990463257,2.940000057220459,2.930000066757202,2.930000066757202,2.930000066757202,2.8399999141693115,2.859999895095825,2.7799999713897705,2.8399999141693115,2.819999933242798,2.799999952316284,2.8399999141693115,2.880000114440918,2.940000057220459,3.109999895095825,3.0899999141693115,3.0799999237060547,3.140000104904175,3.049999952316284,3.0799999237060547,3.0799999237060547,3.0,2.9600000381469727,2.940000057220459,3.0299999713897705,3.0,2.930000066757202,2.869999885559082,2.890000104904175,2.7899999618530273,2.7200000286102295,2.759999990463257,2.7200000286102295,2.680000066757202,2.7200000286102295,2.700000047683716,2.7200000286102295,2.7699999809265137,2.759999990463257,2.7100000381469727,2.7300000190734863,2.7200000286102295,2.619999885559082,2.609999895095825,2.690000057220459,2.759999990463257,2.6600000858306885,2.6500000953674316,2.690000057220459,2.680000066757202,2.6700000762939453,2.7100000381469727,2.759999990463257,2.7699999809265137,2.799999952316284,2.869999885559082,3.049999952316284,3.0,3.059999942779541,3.059999942779541,3.0999999046325684,3.0,2.9600000381469727,2.890000104904175,2.9100000858306885,2.930000066757202,3.009999990463257,3.309999942779541,3.549999952316284,3.299999952316284,3.380000114440918,4.150000095367432,4.150000095367432,3.5999999046325684,3.369999885559082,3.390000104904175,3.390000104904175,3.4000000953674316,3.4600000381469727,3.380000114440918,3.4200000762939453,3.430000066757202,3.440000057220459,3.359999895095825,3.4600000381469727,3.369999885559082,3.450000047683716,3.4200000762939453,3.430000066757202,3.4000000953674316,3.3399999141693115,3.369999885559082,3.430000066757202,3.549999952316284,3.450000047683716,3.359999895095825,3.3399999141693115,3.299999952316284,3.259999990463257,3.180000066757202,3.200000047683716,3.1600000858306885,3.140000104904175,3.0999999046325684,3.119999885559082,3.200000047683716,3.119999885559082,3.2100000381469727,3.190000057220459,3.1600000858306885,3.140000104904175,3.0999999046325684,3.1500000953674316,3.1600000858306885,3.1600000858306885,3.1700000762939453,3.2200000286102295,3.200000047683716,3.1700000762939453,3.0999999046325684,3.140000104904175,3.190000057220459,3.200000047683716,3.200000047683716,3.2200000286102295,3.180000066757202,3.140000104904175,3.200000047683716],\"text\":[\"2022-02-21T00:00:00\",\"2022-02-22T00:00:00\",\"2022-02-23T00:00:00\",\"2022-02-24T00:00:00\",\"2022-02-25T00:00:00\",\"2022-02-28T00:00:00\",\"2022-03-01T00:00:00\",\"2022-03-02T00:00:00\",\"2022-03-03T00:00:00\",\"2022-03-04T00:00:00\",\"2022-03-07T00:00:00\",\"2022-03-08T00:00:00\",\"2022-03-09T00:00:00\",\"2022-03-10T00:00:00\",\"2022-03-11T00:00:00\",\"2022-03-14T00:00:00\",\"2022-03-15T00:00:00\",\"2022-03-16T00:00:00\",\"2022-03-17T00:00:00\",\"2022-03-18T00:00:00\",\"2022-03-21T00:00:00\",\"2022-03-22T00:00:00\",\"2022-03-23T00:00:00\",\"2022-03-24T00:00:00\",\"2022-03-25T00:00:00\",\"2022-03-28T00:00:00\",\"2022-03-29T00:00:00\",\"2022-03-30T00:00:00\",\"2022-03-31T00:00:00\",\"2022-04-01T00:00:00\",\"2022-04-06T00:00:00\",\"2022-04-07T00:00:00\",\"2022-04-08T00:00:00\",\"2022-04-11T00:00:00\",\"2022-04-12T00:00:00\",\"2022-04-13T00:00:00\",\"2022-04-14T00:00:00\",\"2022-04-15T00:00:00\",\"2022-04-18T00:00:00\",\"2022-04-19T00:00:00\",\"2022-04-20T00:00:00\",\"2022-04-21T00:00:00\",\"2022-04-22T00:00:00\",\"2022-04-25T00:00:00\",\"2022-04-26T00:00:00\",\"2022-04-27T00:00:00\",\"2022-04-28T00:00:00\",\"2022-04-29T00:00:00\",\"2022-05-05T00:00:00\",\"2022-05-06T00:00:00\",\"2022-05-09T00:00:00\",\"2022-05-10T00:00:00\",\"2022-05-11T00:00:00\",\"2022-05-12T00:00:00\",\"2022-05-13T00:00:00\",\"2022-05-16T00:00:00\",\"2022-05-17T00:00:00\",\"2022-05-18T00:00:00\",\"2022-05-19T00:00:00\",\"2022-05-20T00:00:00\",\"2022-05-23T00:00:00\",\"2022-05-24T00:00:00\",\"2022-05-25T00:00:00\",\"2022-05-26T00:00:00\",\"2022-05-27T00:00:00\",\"2022-05-30T00:00:00\",\"2022-05-31T00:00:00\",\"2022-06-01T00:00:00\",\"2022-06-02T00:00:00\",\"2022-06-06T00:00:00\",\"2022-06-07T00:00:00\",\"2022-06-08T00:00:00\",\"2022-06-09T00:00:00\",\"2022-06-10T00:00:00\",\"2022-06-13T00:00:00\",\"2022-06-14T00:00:00\",\"2022-06-15T00:00:00\",\"2022-06-16T00:00:00\",\"2022-06-17T00:00:00\",\"2022-06-20T00:00:00\",\"2022-06-21T00:00:00\",\"2022-06-22T00:00:00\",\"2022-06-23T00:00:00\",\"2022-06-24T00:00:00\",\"2022-06-27T00:00:00\",\"2022-06-28T00:00:00\",\"2022-06-29T00:00:00\",\"2022-06-30T00:00:00\",\"2022-07-01T00:00:00\",\"2022-07-04T00:00:00\",\"2022-07-05T00:00:00\",\"2022-07-06T00:00:00\",\"2022-07-07T00:00:00\",\"2022-07-08T00:00:00\",\"2022-07-11T00:00:00\",\"2022-07-12T00:00:00\",\"2022-07-13T00:00:00\",\"2022-07-14T00:00:00\",\"2022-07-15T00:00:00\",\"2022-07-18T00:00:00\",\"2022-07-19T00:00:00\",\"2022-07-20T00:00:00\",\"2022-07-21T00:00:00\",\"2022-07-22T00:00:00\",\"2022-07-25T00:00:00\",\"2022-07-26T00:00:00\",\"2022-07-27T00:00:00\",\"2022-07-28T00:00:00\",\"2022-07-29T00:00:00\",\"2022-08-01T00:00:00\",\"2022-08-02T00:00:00\",\"2022-08-03T00:00:00\",\"2022-08-04T00:00:00\",\"2022-08-05T00:00:00\",\"2022-08-08T00:00:00\",\"2022-08-09T00:00:00\",\"2022-08-10T00:00:00\",\"2022-08-11T00:00:00\",\"2022-08-12T00:00:00\",\"2022-08-15T00:00:00\",\"2022-08-16T00:00:00\",\"2022-08-17T00:00:00\",\"2022-08-18T00:00:00\",\"2022-08-19T00:00:00\",\"2022-08-22T00:00:00\",\"2022-08-23T00:00:00\",\"2022-08-24T00:00:00\",\"2022-08-25T00:00:00\",\"2022-08-26T00:00:00\",\"2022-08-29T00:00:00\",\"2022-08-30T00:00:00\",\"2022-08-31T00:00:00\",\"2022-09-01T00:00:00\",\"2022-09-02T00:00:00\",\"2022-09-05T00:00:00\",\"2022-09-06T00:00:00\",\"2022-09-07T00:00:00\",\"2022-09-08T00:00:00\",\"2022-09-09T00:00:00\",\"2022-09-13T00:00:00\",\"2022-09-14T00:00:00\",\"2022-09-15T00:00:00\",\"2022-09-16T00:00:00\",\"2022-09-19T00:00:00\",\"2022-09-20T00:00:00\",\"2022-09-21T00:00:00\",\"2022-09-22T00:00:00\",\"2022-09-23T00:00:00\",\"2022-09-26T00:00:00\",\"2022-09-27T00:00:00\",\"2022-09-28T00:00:00\",\"2022-09-29T00:00:00\",\"2022-09-30T00:00:00\",\"2022-10-10T00:00:00\",\"2022-10-11T00:00:00\",\"2022-10-12T00:00:00\",\"2022-10-13T00:00:00\",\"2022-10-14T00:00:00\",\"2022-10-17T00:00:00\",\"2022-10-18T00:00:00\",\"2022-10-19T00:00:00\",\"2022-10-20T00:00:00\",\"2022-10-21T00:00:00\",\"2022-10-24T00:00:00\",\"2022-10-25T00:00:00\",\"2022-10-26T00:00:00\",\"2022-10-27T00:00:00\",\"2022-10-28T00:00:00\",\"2022-10-31T00:00:00\",\"2022-11-01T00:00:00\",\"2022-11-02T00:00:00\",\"2022-11-03T00:00:00\",\"2022-11-04T00:00:00\",\"2022-11-07T00:00:00\",\"2022-11-08T00:00:00\",\"2022-11-09T00:00:00\",\"2022-11-10T00:00:00\",\"2022-11-11T00:00:00\",\"2022-11-14T00:00:00\",\"2022-11-15T00:00:00\",\"2022-11-16T00:00:00\",\"2022-11-17T00:00:00\",\"2022-11-18T00:00:00\",\"2022-11-21T00:00:00\",\"2022-11-22T00:00:00\",\"2022-11-23T00:00:00\",\"2022-11-24T00:00:00\",\"2022-11-25T00:00:00\",\"2022-11-28T00:00:00\",\"2022-11-29T00:00:00\",\"2022-11-30T00:00:00\",\"2022-12-01T00:00:00\",\"2022-12-02T00:00:00\",\"2022-12-05T00:00:00\",\"2022-12-06T00:00:00\",\"2022-12-07T00:00:00\",\"2022-12-08T00:00:00\",\"2022-12-09T00:00:00\",\"2022-12-12T00:00:00\",\"2022-12-13T00:00:00\",\"2022-12-14T00:00:00\",\"2022-12-15T00:00:00\",\"2022-12-16T00:00:00\",\"2022-12-19T00:00:00\",\"2022-12-20T00:00:00\",\"2022-12-21T00:00:00\",\"2022-12-22T00:00:00\",\"2022-12-23T00:00:00\",\"2022-12-26T00:00:00\",\"2022-12-27T00:00:00\",\"2022-12-28T00:00:00\",\"2022-12-29T00:00:00\",\"2022-12-30T00:00:00\",\"2023-01-03T00:00:00\",\"2023-01-04T00:00:00\",\"2023-01-05T00:00:00\",\"2023-01-06T00:00:00\",\"2023-01-09T00:00:00\",\"2023-01-10T00:00:00\",\"2023-01-11T00:00:00\",\"2023-01-12T00:00:00\",\"2023-01-13T00:00:00\",\"2023-01-16T00:00:00\",\"2023-01-17T00:00:00\",\"2023-01-18T00:00:00\",\"2023-01-19T00:00:00\",\"2023-01-20T00:00:00\",\"2023-01-30T00:00:00\",\"2023-01-31T00:00:00\",\"2023-02-01T00:00:00\",\"2023-02-02T00:00:00\",\"2023-02-03T00:00:00\",\"2023-02-06T00:00:00\",\"2023-02-07T00:00:00\",\"2023-02-08T00:00:00\",\"2023-02-09T00:00:00\",\"2023-02-10T00:00:00\",\"2023-02-13T00:00:00\",\"2023-02-14T00:00:00\",\"2023-02-15T00:00:00\",\"2023-02-16T00:00:00\",\"2023-02-17T00:00:00\",\"2023-02-20T00:00:00\",\"2023-02-21T00:00:00\",\"2023-02-22T00:00:00\",\"2023-02-23T00:00:00\",\"2023-02-24T00:00:00\",\"2023-02-27T00:00:00\",\"2023-02-28T00:00:00\",\"2023-03-01T00:00:00\"],\"x\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249],\"type\":\"candlestick\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"color\":\"red\"},\"name\":\"close>=open\\u6210\\u4ea4\\u91cf\",\"text\":[\"2022-02-21T00:00:00\",\"2022-02-25T00:00:00\",\"2022-02-28T00:00:00\",\"2022-03-01T00:00:00\",\"2022-03-02T00:00:00\",\"2022-03-03T00:00:00\",\"2022-03-10T00:00:00\",\"2022-03-11T00:00:00\",\"2022-03-16T00:00:00\",\"2022-03-17T00:00:00\",\"2022-03-18T00:00:00\",\"2022-03-21T00:00:00\",\"2022-03-22T00:00:00\",\"2022-03-23T00:00:00\",\"2022-03-24T00:00:00\",\"2022-03-28T00:00:00\",\"2022-03-30T00:00:00\",\"2022-03-31T00:00:00\",\"2022-04-01T00:00:00\",\"2022-04-08T00:00:00\",\"2022-04-12T00:00:00\",\"2022-04-14T00:00:00\",\"2022-04-19T00:00:00\",\"2022-04-26T00:00:00\",\"2022-04-27T00:00:00\",\"2022-04-29T00:00:00\",\"2022-05-05T00:00:00\",\"2022-05-09T00:00:00\",\"2022-05-10T00:00:00\",\"2022-05-12T00:00:00\",\"2022-05-13T00:00:00\",\"2022-05-19T00:00:00\",\"2022-05-25T00:00:00\",\"2022-05-27T00:00:00\",\"2022-05-31T00:00:00\",\"2022-06-06T00:00:00\",\"2022-06-07T00:00:00\",\"2022-06-10T00:00:00\",\"2022-06-14T00:00:00\",\"2022-06-15T00:00:00\",\"2022-06-17T00:00:00\",\"2022-06-20T00:00:00\",\"2022-06-21T00:00:00\",\"2022-06-23T00:00:00\",\"2022-06-24T00:00:00\",\"2022-06-27T00:00:00\",\"2022-06-28T00:00:00\",\"2022-06-29T00:00:00\",\"2022-06-30T00:00:00\",\"2022-07-07T00:00:00\",\"2022-07-08T00:00:00\",\"2022-07-12T00:00:00\",\"2022-07-13T00:00:00\",\"2022-07-18T00:00:00\",\"2022-07-19T00:00:00\",\"2022-07-20T00:00:00\",\"2022-07-22T00:00:00\",\"2022-07-25T00:00:00\",\"2022-07-26T00:00:00\",\"2022-07-27T00:00:00\",\"2022-07-28T00:00:00\",\"2022-08-04T00:00:00\",\"2022-08-05T00:00:00\",\"2022-08-08T00:00:00\",\"2022-08-11T00:00:00\",\"2022-08-12T00:00:00\",\"2022-08-16T00:00:00\",\"2022-08-17T00:00:00\",\"2022-08-22T00:00:00\",\"2022-08-25T00:00:00\",\"2022-08-29T00:00:00\",\"2022-08-30T00:00:00\",\"2022-09-01T00:00:00\",\"2022-09-02T00:00:00\",\"2022-09-05T00:00:00\",\"2022-09-06T00:00:00\",\"2022-09-09T00:00:00\",\"2022-09-14T00:00:00\",\"2022-09-15T00:00:00\",\"2022-09-21T00:00:00\",\"2022-09-27T00:00:00\",\"2022-09-30T00:00:00\",\"2022-10-12T00:00:00\",\"2022-10-14T00:00:00\",\"2022-10-17T00:00:00\",\"2022-10-20T00:00:00\",\"2022-10-26T00:00:00\",\"2022-10-27T00:00:00\",\"2022-11-01T00:00:00\",\"2022-11-02T00:00:00\",\"2022-11-04T00:00:00\",\"2022-11-07T00:00:00\",\"2022-11-08T00:00:00\",\"2022-11-09T00:00:00\",\"2022-11-10T00:00:00\",\"2022-11-11T00:00:00\",\"2022-11-15T00:00:00\",\"2022-11-16T00:00:00\",\"2022-11-17T00:00:00\",\"2022-11-24T00:00:00\",\"2022-11-25T00:00:00\",\"2022-11-28T00:00:00\",\"2022-11-29T00:00:00\",\"2022-12-01T00:00:00\",\"2022-12-02T00:00:00\",\"2022-12-05T00:00:00\",\"2022-12-08T00:00:00\",\"2022-12-09T00:00:00\",\"2022-12-12T00:00:00\",\"2022-12-13T00:00:00\",\"2022-12-15T00:00:00\",\"2022-12-16T00:00:00\",\"2022-12-19T00:00:00\",\"2022-12-23T00:00:00\",\"2022-12-27T00:00:00\",\"2022-12-30T00:00:00\",\"2023-01-03T00:00:00\",\"2023-01-04T00:00:00\",\"2023-01-13T00:00:00\",\"2023-01-19T00:00:00\",\"2023-01-20T00:00:00\",\"2023-01-31T00:00:00\",\"2023-02-07T00:00:00\",\"2023-02-08T00:00:00\",\"2023-02-10T00:00:00\",\"2023-02-13T00:00:00\",\"2023-02-17T00:00:00\",\"2023-02-20T00:00:00\",\"2023-02-21T00:00:00\",\"2023-02-23T00:00:00\",\"2023-02-28T00:00:00\",\"2023-03-01T00:00:00\"],\"x\":[0,4,5,6,7,8,13,14,17,18,19,20,21,22,23,25,27,28,29,32,34,36,39,44,45,47,48,50,51,53,54,58,62,64,66,69,70,73,75,76,78,79,80,82,83,84,85,86,87,92,93,95,96,99,100,101,103,104,105,106,107,112,113,114,117,118,120,121,124,127,129,130,132,133,134,135,138,140,141,145,149,152,155,157,158,161,165,166,169,170,172,173,174,175,176,177,179,180,181,186,187,188,189,191,192,193,196,197,198,199,201,202,203,207,209,212,213,214,221,225,226,228,233,234,236,237,241,242,243,245,248,249],\"y\":[24430600.0,11819300.0,7297300.0,10093501.0,7916005.0,13176647.0,9558200.0,10150040.0,16002800.0,18880810.0,20136900.0,28508708.0,59610802.0,66744872.0,53677751.0,127465874.0,100160308.0,123693450.0,186127406.0,78486766.0,53500244.0,41477310.0,21338203.0,26548560.0,35091700.0,57223700.0,47096431.0,34460500.0,29152380.0,27671180.0,40540477.0,35354667.0,19955491.0,16101200.0,12271729.0,15755233.0,17850858.0,10349350.0,19204601.0,26744810.0,12029809.0,11128298.0,17017300.0,8361611.0,8797080.0,7630657.0,10215076.0,17142400.0,14850034.0,5705984.0,5590133.0,6743300.0,11958810.0,17968420.0,10064379.0,7579900.0,6494300.0,11154680.0,23718605.0,21383664.0,21351740.0,7524600.0,6377200.0,10855941.0,7121400.0,6100689.0,44761764.0,37027461.0,11097720.0,9872001.0,6174200.0,7162234.0,11453500.0,7816801.0,11057201.0,34306996.0,26424771.0,12879600.0,16429680.0,15349680.0,7799300.0,8095444.0,8213500.0,11299558.0,10331034.0,7477900.0,16498923.0,21724001.0,9268538.0,7650398.0,8184500.0,8504900.0,9490948.0,12492000.0,10575799.0,22743940.0,19579900.0,22409700.0,20088840.0,14848130.0,22939170.0,23228380.0,23256610.0,77139992.0,57515164.0,17944532.0,60141845.0,74662480.0,39342200.0,45270640.0,27710873.0,29036180.0,34773485.0,41501600.0,24701500.0,15418501.0,15595803.0,26172916.0,8013800.0,8154300.0,9955602.0,15804220.0,11622022.0,12003891.0,11895920.0,12164620.0,9347802.0,6251560.0,13015400.0,9096241.0,9825258.0,9673946.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y2\"},{\"marker\":{\"color\":\"green\"},\"name\":\"close<open\\u6210\\u4ea4\\u91cf\",\"text\":[\"2022-02-22T00:00:00\",\"2022-02-23T00:00:00\",\"2022-02-24T00:00:00\",\"2022-03-04T00:00:00\",\"2022-03-07T00:00:00\",\"2022-03-08T00:00:00\",\"2022-03-09T00:00:00\",\"2022-03-14T00:00:00\",\"2022-03-15T00:00:00\",\"2022-03-25T00:00:00\",\"2022-03-29T00:00:00\",\"2022-04-06T00:00:00\",\"2022-04-07T00:00:00\",\"2022-04-11T00:00:00\",\"2022-04-13T00:00:00\",\"2022-04-15T00:00:00\",\"2022-04-18T00:00:00\",\"2022-04-20T00:00:00\",\"2022-04-21T00:00:00\",\"2022-04-22T00:00:00\",\"2022-04-25T00:00:00\",\"2022-04-28T00:00:00\",\"2022-05-06T00:00:00\",\"2022-05-11T00:00:00\",\"2022-05-16T00:00:00\",\"2022-05-17T00:00:00\",\"2022-05-18T00:00:00\",\"2022-05-20T00:00:00\",\"2022-05-23T00:00:00\",\"2022-05-24T00:00:00\",\"2022-05-26T00:00:00\",\"2022-05-30T00:00:00\",\"2022-06-01T00:00:00\",\"2022-06-02T00:00:00\",\"2022-06-08T00:00:00\",\"2022-06-09T00:00:00\",\"2022-06-13T00:00:00\",\"2022-06-16T00:00:00\",\"2022-06-22T00:00:00\",\"2022-07-01T00:00:00\",\"2022-07-04T00:00:00\",\"2022-07-05T00:00:00\",\"2022-07-06T00:00:00\",\"2022-07-11T00:00:00\",\"2022-07-14T00:00:00\",\"2022-07-15T00:00:00\",\"2022-07-21T00:00:00\",\"2022-07-29T00:00:00\",\"2022-08-01T00:00:00\",\"2022-08-02T00:00:00\",\"2022-08-03T00:00:00\",\"2022-08-09T00:00:00\",\"2022-08-10T00:00:00\",\"2022-08-15T00:00:00\",\"2022-08-18T00:00:00\",\"2022-08-19T00:00:00\",\"2022-08-23T00:00:00\",\"2022-08-24T00:00:00\",\"2022-08-26T00:00:00\",\"2022-08-31T00:00:00\",\"2022-09-07T00:00:00\",\"2022-09-08T00:00:00\",\"2022-09-13T00:00:00\",\"2022-09-16T00:00:00\",\"2022-09-19T00:00:00\",\"2022-09-20T00:00:00\",\"2022-09-22T00:00:00\",\"2022-09-23T00:00:00\",\"2022-09-26T00:00:00\",\"2022-09-28T00:00:00\",\"2022-09-29T00:00:00\",\"2022-10-10T00:00:00\",\"2022-10-11T00:00:00\",\"2022-10-13T00:00:00\",\"2022-10-18T00:00:00\",\"2022-10-19T00:00:00\",\"2022-10-21T00:00:00\",\"2022-10-24T00:00:00\",\"2022-10-25T00:00:00\",\"2022-10-28T00:00:00\",\"2022-10-31T00:00:00\",\"2022-11-03T00:00:00\",\"2022-11-14T00:00:00\",\"2022-11-18T00:00:00\",\"2022-11-21T00:00:00\",\"2022-11-22T00:00:00\",\"2022-11-23T00:00:00\",\"2022-11-30T00:00:00\",\"2022-12-06T00:00:00\",\"2022-12-07T00:00:00\",\"2022-12-14T00:00:00\",\"2022-12-20T00:00:00\",\"2022-12-21T00:00:00\",\"2022-12-22T00:00:00\",\"2022-12-26T00:00:00\",\"2022-12-28T00:00:00\",\"2022-12-29T00:00:00\",\"2023-01-05T00:00:00\",\"2023-01-06T00:00:00\",\"2023-01-09T00:00:00\",\"2023-01-10T00:00:00\",\"2023-01-11T00:00:00\",\"2023-01-12T00:00:00\",\"2023-01-16T00:00:00\",\"2023-01-17T00:00:00\",\"2023-01-18T00:00:00\",\"2023-01-30T00:00:00\",\"2023-02-01T00:00:00\",\"2023-02-02T00:00:00\",\"2023-02-03T00:00:00\",\"2023-02-06T00:00:00\",\"2023-02-09T00:00:00\",\"2023-02-14T00:00:00\",\"2023-02-15T00:00:00\",\"2023-02-16T00:00:00\",\"2023-02-22T00:00:00\",\"2023-02-24T00:00:00\",\"2023-02-27T00:00:00\"],\"x\":[1,2,3,9,10,11,12,15,16,24,26,30,31,33,35,37,38,40,41,42,43,46,49,52,55,56,57,59,60,61,63,65,67,68,71,72,74,77,81,88,89,90,91,94,97,98,102,108,109,110,111,115,116,119,122,123,125,126,128,131,136,137,139,142,143,144,146,147,148,150,151,153,154,156,159,160,162,163,164,167,168,171,178,182,183,184,185,190,194,195,200,204,205,206,208,210,211,215,216,217,218,219,220,222,223,224,227,229,230,231,232,235,238,239,240,244,246,247],\"y\":[24430600.0,11819300.0,7297300.0,10093501.0,7916005.0,13176647.0,9558200.0,10150040.0,16002800.0,18880810.0,20136900.0,28508708.0,59610802.0,66744872.0,53677751.0,127465874.0,100160308.0,123693450.0,186127406.0,78486766.0,53500244.0,41477310.0,21338203.0,26548560.0,35091700.0,57223700.0,47096431.0,34460500.0,29152380.0,27671180.0,40540477.0,35354667.0,19955491.0,16101200.0,12271729.0,15755233.0,17850858.0,10349350.0,19204601.0,26744810.0,12029809.0,11128298.0,17017300.0,8361611.0,8797080.0,7630657.0,10215076.0,17142400.0,14850034.0,5705984.0,5590133.0,6743300.0,11958810.0,17968420.0,10064379.0,7579900.0,6494300.0,11154680.0,23718605.0,21383664.0,21351740.0,7524600.0,6377200.0,10855941.0,7121400.0,6100689.0,44761764.0,37027461.0,11097720.0,9872001.0,6174200.0,7162234.0,11453500.0,7816801.0,11057201.0,34306996.0,26424771.0,12879600.0,16429680.0,15349680.0,7799300.0,8095444.0,8213500.0,11299558.0,10331034.0,7477900.0,16498923.0,21724001.0,9268538.0,7650398.0,8184500.0,8504900.0,9490948.0,12492000.0,10575799.0,22743940.0,19579900.0,22409700.0,20088840.0,14848130.0,22939170.0,23228380.0,23256610.0,77139992.0,57515164.0,17944532.0,60141845.0,74662480.0,39342200.0,45270640.0,27710873.0,29036180.0,34773485.0,41501600.0,24701500.0,15418501.0,15595803.0,26172916.0,8013800.0,8154300.0,9955602.0,15804220.0,11622022.0,12003891.0,11895920.0,12164620.0,9347802.0,6251560.0,13015400.0,9096241.0,9825258.0,9673946.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y2\"},{\"mode\":\"lines\",\"name\":\"RSI_6\",\"text\":[\"2022-03-15T00:00:00\",\"2022-04-25T00:00:00\",\"2022-07-15T00:00:00\",\"2022-08-03T00:00:00\",\"2022-10-25T00:00:00\"],\"x\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249],\"y\":[null,null,null,null,null,null,27.27271249471009,32.77313323539474,44.52150703497698,32.74841606441038,31.10344497499902,20.16443883681768,16.650801610712694,25.325433955736482,28.87845084733357,24.655322324161542,15.274037569081726,33.944873735606414,44.16653420406348,55.24699806441231,63.854833689608945,72.36071362982356,82.91476095112661,88.59079194968686,67.38310059705341,73.47818097055402,53.1264452252582,54.23933843355548,66.37435468035638,75.1237772567786,58.57586886169896,45.33193892275709,48.95890276712368,41.204986669216474,40.61127829025267,36.50821171949407,42.89860731820484,41.238364504665704,36.94847426960559,42.01531588317275,37.380689288421095,29.216277813793067,26.643544773567427,20.704974602741284,26.880861108951613,25.304208177204522,24.86665429924571,27.860458575597207,36.910494443887124,30.111025749811127,43.89222212435729,47.61116491175728,40.15820548246314,39.10871924159219,52.51197524529941,61.54171334174462,59.83509274921367,49.876540281781466,57.02843931075,51.715514381030836,51.715514381030836,33.65540542987141,41.78704780311372,40.30503063026862,42.74188641443437,35.7390182574262,35.7390182574262,31.96790996497092,23.16742431300613,30.788727222121647,44.09721021257399,34.225493287527954,28.17126078358606,28.17126078358606,21.367635080037157,46.278261153587565,54.85866385464411,47.96396646374353,41.67817556474896,49.603648410746075,56.669563177930925,42.40158023824732,47.671577067528105,45.19072783530894,48.41230279171943,51.81122582630972,61.04984180298604,66.22936983584236,57.11525804281029,42.93494346046366,33.0795396173456,25.935524052889814,30.442214249238837,35.17545016643215,30.237226320391752,35.65708851454042,56.11372157695973,36.5769823343556,17.13999344675998,43.6276250074381,45.131037390569,45.131037390569,41.91188543854103,41.91188543854103,51.81119310309339,71.05142056922222,73.93349495639839,78.96064261487209,64.12108526894937,50.76442744381474,33.05752946582533,29.009999971388527,36.47662628979582,41.969805596188834,49.02298034141266,44.18917636478671,38.16767232759305,44.922382649575546,51.305752888997446,49.032644232446955,72.4599029304743,75.04118359419981,63.19490774448534,56.74514989252154,56.74514989252154,54.09506253486964,35.95841844208171,43.531373334323035,35.20528240582742,43.04821714697123,43.04821714697123,36.65856829707283,46.2349813284404,54.49136793799354,68.17536917587059,80.94456011958322,82.25873307722642,73.17491776400973,80.80561386821843,61.994811947887406,57.94790275556553,63.64375115432222,43.983955966611965,38.7594060208661,38.75940602086611,59.527554855978806,46.82419271929372,41.5092697084774,30.964935743432715,38.46767995909671,29.00945435360506,24.043624242856218,32.02281209048543,27.663717829046586,25.967035992205684,35.46625641744342,32.93101568255196,42.75239372841913,49.41867196130332,47.21926634149936,38.907571000572005,38.907571000572005,36.588984826360964,23.274181165435824,18.28397616115127,42.11374705723897,58.46522402833342,44.92805407119606,42.00993174127666,48.07999373306896,52.09179484251424,45.73130250158141,56.38277084853763,63.28365555403161,66.48873364277237,72.26948742520246,74.87042552979594,83.91976389180678,88.4224455618978,88.83913770289587,90.11936174283021,90.55282298625575,66.16910940313487,50.009478804411245,44.42927767342438,37.698777198030335,49.23434209687702,66.89021181225141,60.57044418593577,81.52106886791,81.90564111356856,85.8051278616861,92.11106363358233,95.05645375239787,66.18432041948327,48.15735204273242,46.94595136952923,53.49674448694724,47.918198804305355,52.755225563229594,48.000086342696584,48.00008634269659,51.16910593970114,58.71683853276788,43.41621367521533,40.115163157730635,46.89814532612688,56.93300582015707,47.88415414341474,53.235667264879346,47.39550151654633,41.88193990459571,45.67381351912371,51.944536057154764,62.369179759584995,49.48696554875989,48.290061089962755,41.12796555622295,34.91408938326327,29.555558020605666,25.623000822666086,27.924623079477996,25.125554425070927,22.42784581073333,21.504266156414857,20.491672741596503,38.00662864850433,41.11960837606402,47.45239956297536,47.45239956297536,38.50669628386936,33.46035274722814,27.07380925322586,44.13454689266541,51.01095870458211,44.445993658349806,51.877818793740005,63.573158810951604,59.253005239674195,47.606531843337244,30.70684231148165,38.225389353858276,50.98847340045545,60.725654117188064,57.30990515715153,62.387491156108375,51.38608488195951,40.07825088863812,54.94891613128624,61.77839187975859],\"type\":\"scatter\",\"xaxis\":\"x3\",\"yaxis\":\"y3\"},{\"marker\":{\"color\":\"pink\",\"line\":{\"color\":\"midnightblue\",\"width\":1},\"size\":9,\"symbol\":\"triangle-up\"},\"mode\":\"markers\",\"name\":\"RSI_6\\u4f4e\\u5904\\u4e70\\u70b9\",\"text\":[\"2022-03-15T00:00:00\",\"2022-04-25T00:00:00\",\"2022-07-15T00:00:00\",\"2022-08-03T00:00:00\",\"2022-10-25T00:00:00\"],\"x\":[16,43,98,111,164],\"y\":[2.5739999055862426,2.682000017166138,2.403000068664551,2.4570000171661377,2.321999931335449],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"color\":\"pink\",\"line\":{\"color\":\"midnightblue\",\"width\":1},\"size\":9,\"symbol\":\"triangle-up\"},\"mode\":\"markers\",\"name\":\"RSI_6\\u4f4e\\u5904\\u4e70\\u70b9\",\"text\":[\"2022-03-15T00:00:00\",\"2022-04-25T00:00:00\",\"2022-07-15T00:00:00\",\"2022-08-03T00:00:00\",\"2022-10-25T00:00:00\"],\"x\":[16,43,98,111,164],\"y\":[10.274037569081726,15.704974602741284,12.13999344675998,24.009999971388527,13.28397616115127],\"type\":\"scatter\",\"xaxis\":\"x3\",\"yaxis\":\"y3\"},{\"line\":{\"color\":\"black\"},\"mode\":\"lines\",\"name\":\"\\u95f4\\u9694\\u80cc\\u79bb\\u6536\\u76d8\\u4ef7\\u6807\\u8bb0\\u70b9\",\"opacity\":0.6,\"text\":[\"2022-07-15T00:00:00\",\"2022-10-25T00:00:00\"],\"x\":[98,164],\"y\":[2.6700000762939453,2.5799999237060547],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"black\"},\"mode\":\"lines\",\"name\":\"\\u95f4\\u9694\\u80cc\\u79bbRSI\\u6807\\u8bb0\\u70b9\",\"opacity\":0.6,\"text\":[\"2022-07-15T00:00:00\",\"2022-10-25T00:00:00\"],\"x\":[98,164],\"y\":[17.13999344675998,18.28397616115127],\"type\":\"scatter\",\"xaxis\":\"x3\",\"yaxis\":\"y3\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"matches\":\"x3\",\"showticklabels\":false,\"rangeslider\":{\"visible\":false},\"showspikes\":true,\"spikethickness\":2},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.5,1],\"showspikes\":true,\"spikethickness\":2},\"xaxis2\":{\"anchor\":\"y2\",\"domain\":[0.0,1.0],\"matches\":\"x3\",\"showticklabels\":false,\"showspikes\":true,\"spikethickness\":2},\"yaxis2\":{\"anchor\":\"x2\",\"domain\":[0.3,0.5],\"showspikes\":true,\"spikethickness\":2},\"xaxis3\":{\"anchor\":\"y3\",\"domain\":[0.0,1.0],\"showspikes\":true,\"spikethickness\":2},\"yaxis3\":{\"anchor\":\"x3\",\"domain\":[0,0.3],\"showspikes\":true,\"spikethickness\":2},\"shapes\":[{\"line\":{\"color\":\"grey\",\"width\":1},\"type\":\"line\",\"x0\":0,\"x1\":249,\"xref\":\"x2\",\"y0\":30,\"y1\":30,\"yref\":\"y2\"}],\"title\":{\"text\":\"000608.XSHE: \\u9633\\u5149\\u80a1\\u4efd\\u6700\\u540eclose\\u4f4e\\u70b9\\u4e0eRSI6\\u51fa\\u73b0\\u95f4\\u9694\\u80cc\\u79bb\"},\"width\":1000,\"height\":600},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('73da5d89-875c-488c-b78b-c12d6918560c');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go \n", "from plotly.subplots import make_subplots\n", "from omicron.models.security import Security\n", "\n", "name = await Security.alias(code)\n", "frame = bars['frame']\n", "close = bars['close'].astype(np.float64)\n", "RSI6 = tb.RSI(close, 6)\n", "index = np.arange(len(close))\n", "rise_fall = close-bars['open']\n", "\n", "\n", "fig = make_subplots(rows=3, cols=1, specs=[[{}], [{}], [{}]], shared_xaxes=True, shared_yaxes=False)\n", "fig.add_trace(go.Candlestick(\n", "    x = index, \n", "    close = close, \n", "    open = bars['open'], \n", "    high = bars['high'], \n", "    low = bars['low'],\n", "    increasing=dict(line=dict(color='red')), \n", "    decreasing=dict(line=dict(color='green')), \n", "    name = 'K线', \n", "    text=frame,\n", "), row = 1, col = 1)\n", "\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall>=0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close>=open成交量',\n", "    marker = dict(color = 'red'),\n", "    text = bars['frame'][rise_fall>=0]\n", "), row = 2, col = 1)\n", "\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall<0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close<open成交量',\n", "    marker = dict(color = 'green'),\n", "    text = bars['frame'][rise_fall<0]\n", "), row = 2, col = 1)\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x = index, \n", "    y = RSI6,\n", "    mode = 'lines', \n", "    name = 'RSI_6', \n", "    text = frame[valley_index], \n", "), row = 3, col = 1)\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x = valley_index, \n", "    y = close[valley_index]*0.9, \n", "    mode = 'markers', \n", "    marker_symbol = 'triangle-up', \n", "    marker_line_color = 'midnightblue', marker_color = 'pink', \n", "    marker_line_width = 1, marker_size = 9,   name = 'RSI_6低处买点', \n", "    text = frame[valley_index]\n", "), row = 1, col = 1)\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x = valley_index, \n", "    y = RSI6[valley_index]-5, \n", "    mode = 'markers', \n", "    marker_symbol = 'triangle-up', \n", "    marker_line_color = 'midnightblue', marker_color = 'pink', \n", "    marker_line_width = 1, marker_size = 9,   name = 'RSI_6低处买点', \n", "    text = frame[valley_index]\n", "), row = 3, col = 1)\n", "\n", "\n", "fig.add_shape(type = 'line', \n", "    xref = 'paper', yref = 'y', \n", "    x0 = index[0], y0 = 30, x1 = index[-1], y1 =30, \n", "    line = dict(\n", "        color = 'grey', width = 1\n", "    ), row = 2, col = 1)\n", "\n", "if bottom_dev==1:\n", "    fig.add_trace(go.<PERSON>(\n", "        x = valley_index[-2:], \n", "        y = close[valley_index[-2:]], \n", "        mode = 'lines', \n", "        line = dict(color = 'black'), \n", "        opacity = 0.6,\n", "        name = '相邻背离收盘价标记点', \n", "        text =  frame[valley_index[-2:]]\n", "    ), row = 1, col = 1)\n", "\n", "    fig.add_trace(go.<PERSON>(\n", "        x = valley_index[-2:], \n", "        y = RSI6[valley_index[-2:]], \n", "        mode = 'lines', \n", "        line = dict(color = 'black'), \n", "        opacity = 0.6,\n", "        name = '相邻背离RSI标记点', \n", "        text =  frame[valley_index[-2:]]\n", "    ), row = 3, col = 1)\n", "\n", "    fig.update_layout(\n", "        title = (f'{code}: {name}最后close低点与RSI6出现背离'), width = 1000, height = 600\n", "    )\n", "\n", "elif bottom_dev==2:\n", "    fig.add_trace(go.<PERSON>(\n", "        x = [valley_index[-3], valley_index[-1]], \n", "        y = close[[valley_index[-3], valley_index[-1]]], \n", "        mode = 'lines', \n", "        line = dict(color = 'black'), \n", "        opacity = 0.6,\n", "        name = '间隔背离收盘价标记点', \n", "        text =  frame[[valley_index[-3], valley_index[-1]]]\n", "    ), row = 1, col = 1)\n", "\n", "    fig.add_trace(go.<PERSON>(\n", "        x = [valley_index[-3], valley_index[-1]], \n", "        y = RSI6[[valley_index[-3], valley_index[-1]]], \n", "        mode = 'lines', \n", "        line = dict(color = 'black'), \n", "        opacity = 0.6,\n", "        name = '间隔背离RSI标记点', \n", "        text =  frame[[valley_index[-3], valley_index[-1]]]\n", "    ), row = 3, col = 1)\n", "\n", "    fig.update_layout(\n", "        title = (f'{code}: {name}最后close低点与RSI6出现间隔背离'), width = 1000, height = 600\n", "    )\n", "\n", "\n", "else: \n", "    fig.update_layout(\n", "        title = (f'{code}: {name}最后close低点与RSI6没有背离'), width = 1000, height = 600\n", "    )\n", "\n", "\n", "fig.update_yaxes(dict(domain=[0.5, 1]), row = 1, col = 1)\n", "fig.update_yaxes(dict(domain=[0.3, 0.5]), row = 2, col = 1)\n", "fig.update_yaxes(dict(domain=[0, 0.3]), row = 3, col = 1)\n", "\n", "fig.update_xaxes(rangeslider_visible = False, row = 1, col = 1)\n", "fig.update_xaxes(showspikes = True, spikethickness = 2, \n", "                # rangebreaks = [dict(bounds=[6, 1], pattern='day of week', enabled = True),\n", "                                    # dict(bounds=[11.5001, 13], pattern='hour', enabled = True),\n", "                                    # dict(bounds=[15.001, 9.5], pattern='hour', enabled = True)]\n", "                )\n", "fig.update_yaxes(showspikes = True, spikethickness = 2)\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "cheese", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}