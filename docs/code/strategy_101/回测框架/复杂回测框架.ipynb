{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def account_init(start: datetime = start, end: datetime = end):\n", "    # 初始化回测账户\n", "    log.info('初始函数开始运行，且全局只运行一次。')\n", "    url = 'http://192.168.100.114:7080/backtest/api/trade/v0.4'\n", "\n", "    token = uuid.uuid4().hex\n", "    account = f\"{token[-4:]}\"\n", "    print(f'账户名:{account}: {token} ')\n", "    client: TraderClient = TraderClient(url, account, token, is_backtest=True, start=start, end=end)\n", "    \n", "    \n", "    try:\n", "        result = client.info()\n", "        result_df = pd.DataFrame.from_dict(result, orient=\"index\").T\n", "        print('账户初始化信息: ')\n", "        display(result_df)\n", "       \n", "        if result is None:\n", "            log.error(\"failed to get information\")\n", "            return None\n", "        return client\n", "    except Exception as e:\n", "        print(e)\n", "        return False\n", "\n", "start  = datetime.date(2022, 3, 1)\n", "end = datetime.date(2023, 3, 1)\n", "client = account_init(start, end)\n", "print(client)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def stock_buy(client, code: str, buy_time: datetime, price: float):\n", "    print(\"函数(market_buy)运行\")\n", "    available_money = client.available_money\n", "    available_holds = available_money/price//100  # 100股等于一手\n", "\n", "    # 满足布林带条件时, 本金全买\n", "    if available_holds > 0:\n", "        try: \n", "            buy = client.market_buy(code, volume=available_holds * 100,  order_time=buy_time)\n", "            buy_info = pd.DataFrame.from_dict(buy, orient=\"index\").T\n", "            print('买入股票详情：')\n", "            display(buy_info)\n", "\n", "        except Exception as e:\n", "                print(e)\n", "\n", "    else:\n", "        print(\"可用本金无法买入全部满足条件股票\")\n", "    return\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def stock_sell(client, code: str, sell_time: datetime):\n", "    print(\"函数(market_buy)运行\")\n", "    available_shares = client.available_shares(code)\n", "    \n", "    # 满足布林带条件时, 本金全买\n", "    if available_shares >= 100:\n", "        try: \n", "            sell = client.market_sell(code, volume=available_shares,  order_time=sell_time)\n", "            sell_info = pd.DataFrame.from_dict(sell, orient=\"index\").T\n", "            print('卖出股票详情：')\n", "            display(sell_info)\n", "\n", "        except Exception as e:\n", "                print(e)\n", "\n", "    else:\n", "       print(f\"{sell_time}无法卖出股票{code}\")\n", "    return"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def scan_dts(start, end):\n", "    '''遍历[start: end]时间中，所有股票可能出现的买卖点进行回测'''\n", "\n", "    client = account_init(start, end)\n", "    frames = tf.get_frames(start, end, FrameType.DAY)\n", "\n", "    for i in range(len(frames)):\n", "        frame = tf.int2date(frames[i])\n", "        frame_time = tf.combine_time(frame, 15, 0)\n", "\n", "        await stock_buyl(frame, client)\n", "        await stock_buy(frame, client)\n", "\n", "    return \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = account_init(start, end)\n", "\n", "frames = tf.get_frames(start, end, FrameType.DAY)\n", "for i in range(len(frames)):\n", "    frame = tf.int2date(frames[i])\n", "    print(frame)\n", "    frame_time = tf.combine_time(frame, 15, 0)\n", "    "]}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}