{"cells": [{"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["1. 只需要替换股票名称，代码，时间，数量等直接运行即可其他不需要更改！需要存到本地，则自行存入\n", "2. 有问题扫二维码咨询，写的匆忙，也比较简单，有任何问题和需求都可以提出\n", "#### ![image.png](attachment:image.png)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 导入所需安装包"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import omicron\n", "from coretypes import BarsArray, FrameType\n", "import pandas as pd\n", "from omicron.models.stock import Stock\n", "from omicron.models.security import Security\n", "from omicron import tf\n", "from omicron.extensions.np import find_runs\n", "import datetime\n", "import numpy as np\n", "from IPython.display import display\n", "import cfg4py\n", "import talib as tb\n", "\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "# 配置不可删\n", "cfg = cfg4py.init('~/cheese/config')\n", "await omicron.init()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["####  布林带原理\n", "  \n", "    '''\n", "    形态简介：\n", "    BOLL指标又叫布林线指标（Bolinger Bands），是由约翰·布林格(<PERSON>)根据统计学中的标准差原理设计出来的一种非常简单实用的技术分析指标。\n", "    布林线是研判股价运动趋势的一种中长期技术分析工具。\n", "\n", "    一般而言，股价的运动总是围绕某一价值中枢（如均线、成本线等）在一定的范围内变动，\n", "    布林线指标指标正是在上述条件的基础上，引进了“股价通道”的概念，其认为股价通道的宽窄随着股价波动幅度的大小而变化，\n", "    而且股价通道又具有变异性，它会随着股价的变化而自动调整。\n", "\n", "    操作要点：\n", "    一、在常态范围内，布林线使用的技术和方法\n", "    常态范围通常是股价运行在一定宽度的带状范围内，它的特征是股价没有极度大涨 大跌，处在一种相对平衡的状态之中，此时使用布林线的方法非常简单。\n", "    1，当股价穿越上限压力线（动态上限压力线，静态最上压力线BOLB1）时，卖点信号；\n", "    2，当股价穿越下限支撑丝（动态下限支撑线，静态最下支撑线BOLB4）时，买点信号；\n", "    3，当股价由下向上穿越中界限（静态从BOLB4穿越BOLB3）时，为加码信号；\n", "    4，当股价由上向下穿越中界线（静态由BOLB1穿越BOLB2）时，为卖出信号。\n", "    '''\n", "\n", "    布林带函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def STD(S,N):           #求序列的N日标准差，返回序列    \n", "    return  pd.Series(S).rolling(N).std(ddof=0).values  \n", "\n", "\n", "def bollingerbands(close: np.array, N: int=20, P: int=2):  # N和P的值可改\n", "    '''此处函数仅限于：\n", "    1，当股价穿越上限压力线，卖点信号；\n", "    2，当股价穿越下限支撑丝，买点信号；'''\n", "    assert len(close)>=20, '传入数据长度不得低于20'\n", "    close = close.astype(np.float64)\n", "    mid = tb.MA(close, N)\n", "    upper = mid + P*STD(close, N) #上线\n", "    lower = mid - P*STD(close, N)  # 下线\n", "    upper_diff =close - upper\n", "    condlist = [upper_diff>=0 , upper_diff<0]\n", "    choicelist = [1, -1]\n", "    sell_points = np.select(condlist, choicelist, 1)\n", "\n", "    lower_diff = lower - close\n", "    condlist = [lower_diff>=0 , lower_diff<0]\n", "    choicelist = [1, -1]\n", "    buy_points = np.select(condlist, choicelist,1)\n", "\n", "    return sell_points, buy_points, mid, upper, lower"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 选取指定股票，拉取行情数据 "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 股票代码\n", "code = \"000619.XSHE\"  #  可自行修改股票代码，6开头的股票，字母后缀：XSHG，其他为：XSHE\n", "# 过去一年行情数据\n", "start  = datetime.date(2022, 3, 1)\n", "end = datetime.date(2023, 3, 1)\n", "bars = await Stock.get_bars_in_range(code, start=start, end=end, frame_type=FrameType.DAY)\n", "close = bars['close']\n", "frame = bars['frame']"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 找到买卖点的索引"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "sell_points, buy_points, mid, upper, lower = bollingerbands(close)\n", "print(sell_points, buy_points)  # 从1到-1的改变为交易点，即上穿下穿的交叉点\n", "\n", "unique_values, start_indices, _ = find_runs(sell_points)   # omicron框架中的数列处理函数\n", "sell_index = start_indices[unique_values==-1]\n", "\n", "unique_values, start_indices, _ = find_runs(buy_points)   # omicron框架中的数列处理函数\n", "buy_index = start_indices[unique_values==-1]\n", "\n", "print(sell_index, buy_index)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 找到买点及对应卖点，即买卖点必须成对出现，才合理\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buy_sell = {}\n", "for b in buy_index:\n", "    real_sell_index = sell_index[sell_index>b]\n", "    if len(real_sell_index)>0:\n", "        real_sell_i = real_sell_index[0]\n", "        buy_sell[b] = real_sell_i\n", "print(buy_sell)  #keys为买点index(19, 41, 149)， values为卖点index(65, 65, 184)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 计算收益每次单笔单股收益，每笔交易资产暴露时间"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算收益\n", "sell_price = close[list(buy_sell.values())]\n", "buy_price = close[list(buy_sell.keys())]\n", "\n", "# 每次单笔收益\n", "profit = (sell_price)-buy_price\n", "\n", "# 每笔资产暴露时间\n", "hold_days = np.array(list(buy_sell.values())) - np.array(list(buy_sell.keys()))\n", "\n", "info = pd.DataFrame({\n", "    '买入价': close[list(buy_sell.keys())],\n", "    '每笔单股收益': profit, \n", "    '收益率%': np.around(profit/close[list(buy_sell.keys())]*100,  2),\n", "    '资产暴露时间': hold_days, \n", "    '买入时间': frame[list(buy_sell.keys())], \n", "    '卖出时间': frame[list(buy_sell.values())], \n", "    })\n", "\n", "display(info)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 绘制出交互式K线图，灰色框为真实发生交易"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["name = await Security.alias(code)\n", "\n", "index = np.arange(len(close))\n", "rise_fall = close-bars['open']\n", "\n", "\n", "fig = make_subplots(rows=2, cols=1, specs=[[{}], [{}]], shared_xaxes=True, shared_yaxes=False)\n", "fig.add_trace(go.Candlestick(\n", "    x = index, \n", "    close = close, \n", "    open = bars['open'], \n", "    high = bars['high'], \n", "    low = bars['low'],\n", "    increasing=dict(line=dict(color='red')), \n", "    decreasing=dict(line=dict(color='green')), \n", "    name = 'K线', \n", "    text=frame,\n", "), row = 1, col = 1)\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x = index, \n", "    y = mid, \n", "    mode = 'lines', \n", "    name = '均值中界线', \n", "    text = frame\n", "), row = 1, col = 1)\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x = index, \n", "    y = upper, \n", "    mode = 'lines', \n", "    name = '压力线',  \n", "    text = frame\n", "), row = 1, col = 1)\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x = index, \n", "    y = lower, \n", "    mode = 'lines', \n", "    name = '支撑线',\n", "    text = frame\n", "), row = 1, col = 1)\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x = index[buy_index], \n", "    y = bars['close'][buy_index]*0.9, \n", "    mode = 'markers', \n", "    marker_symbol = 'triangle-up', \n", "    marker_line_color = 'midnightblue', marker_color = 'pink', \n", "    marker_line_width = 1, marker_size = 9,   name = '计算出的买点', \n", "    text = frame[buy_index]\n", "), row = 1, col = 1)\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x = index[sell_index], \n", "    y = bars['close'][sell_index]*1.1, \n", "    mode = 'markers', \n", "    marker_symbol = 'triangle-down', \n", "    marker_line_color = 'midnightblue', marker_color = 'pink', \n", "    marker_line_width = 1, marker_size = 9,   name = '计算出的卖点', \n", "    text = frame[sell_index]\n", "), row = 1, col = 1)\n", "\n", "# 标注出实际发生的买卖点\n", "for b, s in zip(buy_sell.keys(), buy_sell.values()):\n", "    fig.add_shape(type='rect', fillcolor = 'grey', \n", "                  layer='above', xref='x', yref='y', \n", "                  x0 = index[b], x1=index[s], \n", "                  y0 = close[b], y1 = close[s], \n", "                  line_width=0, \n", "                  opacity=0.3, row = 1, col = 1)\n", "    \n", "# 交易量\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall>=0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close>=open成交量',\n", "    marker = dict(color = 'red'),\n", "    text = bars['frame'][rise_fall>=0]\n", "), row = 2, col = 1)\n", "\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall<0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close<open成交量',\n", "    marker = dict(color = 'green'),\n", "    text = bars['frame'][rise_fall<0]\n", "), row = 2, col = 1)\n", "    \n", "\n", "fig.update_layout(\n", "    title = (f'{code}: {name}布林带'), width = 1000, height = 600\n", ")\n", "fig.update_yaxes(dict(domain=[0.3, 1]), row = 1, col = 1)\n", "fig.update_yaxes(dict(domain=[0, 0.3]), row = 2, col = 1)\n", "\n", "fig.update_xaxes(rangeslider_visible = False, row = 1, col = 1)\n", "fig.update_xaxes(showspikes = True, spikethickness = 2, \n", "                # rangebreaks = [dict(bounds=[6, 1], pattern='day of week', enabled = True),\n", "                                    # dict(bounds=[11.5001, 13], pattern='hour', enabled = True),\n", "                                    # dict(bounds=[15.001, 9.5], pattern='hour', enabled = True)]\n", "                                    )\n", "fig.update_yaxes(showspikes = True, spikethickness = 2)\n", "\n", "fig.show()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 利用quantstats生成html格式回测报告，在该文件目录下，点进去查看"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import quantstats as qs\n", "\n", "# 初始资金设为10000\n", "init_bill = 10000\n", "assets = init_bill*(info['收益率%']*0.01+1).cumprod()\n", "init_bill = pd.Series([init_bill])\n", "assets = pd.concat([init_bill, assets])\n", "display(pd.DataFrame(assets, columns = ['资产变化']))\n", "\n", "assets.index = frame[[0]+list(buy_sell.values())]   # 卖出时间\n", "qs.reports.html(assets, output='report.html', title='回测绩效报告', rf=0.00,\n", "                download_filename='布林带回测报告.html')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "cheese", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}