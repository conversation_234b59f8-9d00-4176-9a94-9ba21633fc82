{"cells": [{"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "8ed81128", "metadata": {}, "source": ["1. 只需要替换股票名称，代码，时间，数量等直接运行即可其他不需要更改！需要存到本地，则自行存入\n", "2. 有问题扫二维码咨询，写的匆忙，也比较简单，有任何问题和需求都可以提出\n", "### ![image.png](attachment:image.png)"]}, {"cell_type": "markdown", "id": "0b7ff36d-cd63-4bb6-afd0-69500ad9fb94", "metadata": {}, "source": ["### 导入所需安装包"]}, {"cell_type": "code", "execution_count": null, "id": "00003df9-9f7f-4c67-9f49-62fc27a807ee", "metadata": {"tags": []}, "outputs": [], "source": ["# 导入所需包\n", "import omicron\n", "from coretypes import BarsArray, FrameType  \n", "import pandas as pd\n", "import numpy as np\n", "from omicron.models.security import Security\n", "from omicron.models.board import Board, BoardType\n", "import datetime\n", "import cfg4py\n", "\n", "# 初始化配置，不可删！\n", "cfg = cfg4py.init('~/cheese/config')\n", "await omicron.init()\n", "Board.init('192.168.100.101')"]}, {"cell_type": "markdown", "id": "13ddc08a-3618-49ed-a323-edfd1a6e96f7", "metadata": {}, "source": ["### 通过同花顺板块代码查询板块信息（名字，成员数目或清单）,行业板块，概念板块均可"]}, {"cell_type": "code", "execution_count": null, "id": "706eef85-35cf-4a7c-9a23-6060356b09df", "metadata": {"tags": []}, "outputs": [], "source": ["# 返回值：\n", "# {'code': '301505', 'name': '医疗器械概念', 'stocks': 242}\n", "# or\n", "# {'code': '301505', 'name': '医疗器械概念', 'stocks': [['300916', '朗特智能'], ['300760', '迈瑞医疗']]}\n", "\n", "board_code = '881128' # 汽车服务 可自行修改\n", "board_info = await Board.board_info_by_id(board_code)   \n", "print(board_info) # 字典形式"]}, {"cell_type": "markdown", "id": "39dc62bc-9958-46a1-812c-68dcc82a7fa0", "metadata": {}, "source": ["### 获取股票所在同花顺概念板块信息：名称，代码"]}, {"cell_type": "code", "execution_count": null, "id": "d65ff679-1944-4884-9329-e11e639ca830", "metadata": {"tags": []}, "outputs": [], "source": ["stock_code = '002236'  # 大华股份，股票代码不带字母后缀\n", "stock_in_board = await Board.board_info_by_security(stock_code, _btype=BoardType.CONCEPT)\n", "print(stock_in_board)"]}, {"cell_type": "markdown", "id": "a5f1f2f4-4ae1-4500-831b-19044665ff25", "metadata": {}, "source": ["### 获取指定同花顺概念板块日线数据\n", "1. 从持久化数据库中获取介于[`start`, `end`]间的行情记录\n", "2. code: 板块代码（概念、行业）\n", "    start: 起始时间\n", "    end: 结束时间，如果未指明，则取当前时间\n", "    返回dtype为`coretypes.bars_dtype`的一维numpy数组。\n", "\n", " "]}, {"cell_type": "code", "execution_count": null, "id": "9ad6c65b-5a37-4c23-87a8-3c5b41d94a74", "metadata": {"tags": []}, "outputs": [], "source": ["start = datetime.date(2022, 9, 1)  # 起始时间， 可修改\n", "end = datetime.date(2023, 3, 1)  # 截止时间， 可修改\n", "board_code = '881128' # 汽车服务， 可修改\n", "bars = await Board.get_bars_in_range(board_code, start, end) \n", "bars[:10] # 只返回前十条数据"]}, {"cell_type": "markdown", "id": "c8e7cf10-fd52-407f-8311-2c580ca424fd", "metadata": {}, "source": ["#### 行情数据转为dataframe形式，如不需要，则略过"]}, {"cell_type": "code", "execution_count": null, "id": "11a22da4-81b5-4006-824a-891cd13bb32b", "metadata": {"tags": []}, "outputs": [], "source": ["bars_df = pd.DataFrame(bars)\n", "bars_df"]}, {"attachments": {}, "cell_type": "markdown", "id": "7c6b26cb-0471-4d0c-b7bc-da121ec98395", "metadata": {}, "source": ["### 绘制出K线图，横坐标为数字索引，交互式绘图"]}, {"cell_type": "code", "execution_count": null, "id": "365b8977-cde2-4ecc-8d7d-d72c880b0b9e", "metadata": {"tags": []}, "outputs": [], "source": ["import plotly.graph_objects as go \n", "from plotly.subplots import make_subplots\n", "import numpy as np \n", "\n", "name = board_info['name']\n", "frame = bars['frame']\n", "close = bars['close']\n", "rise_fall = close-bars['open']\n", "index = np.arange(len(bars))\n", "\n", "fig = make_subplots(rows=2, cols=1, specs=[[{}], [{}]], shared_xaxes=True, shared_yaxes=False)\n", "# K线图\n", "fig.add_trace(go.Candlestick(\n", "    x = index, \n", "    close = bars['close'], \n", "    open = bars['open'], \n", "    high = bars['high'], \n", "    low = bars['low'],\n", "    increasing=dict(line=dict(color='red')), \n", "    decreasing=dict(line=dict(color='green')), \n", "    name = (f'{name}概念板块K线'), \n", "    text=frame,\n", "), row = 1, col = 1)\n", "\n", "# 交易量\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall>=0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close>=open成交量',\n", "    marker = dict(color = 'red'),\n", "    text = bars['frame'][rise_fall>=0]\n", "), row = 2, col = 1)\n", "\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall<0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close<open成交量',\n", "    marker = dict(color = 'green'),\n", "    text = bars['frame'][rise_fall<0]\n", "), row = 2, col = 1)\n", "\n", "fig.update_layout(\n", "    title = (f'{board_code}: {name}概念板块'), width = 1000, height = 600\n", ")\n", "fig.update_yaxes(dict(domain=[0.3, 1]), row = 1, col = 1)\n", "fig.update_yaxes(dict(domain=[0, 0.3]), row = 2, col = 1)\n", "\n", "fig.update_xaxes(rangeslider_visible = False, row = 1, col = 1)\n", "fig.update_xaxes(showspikes = True, spikethickness = 2, \n", "                # rangebreaks = [dict(bounds=[6, 1], pattern='day of week', enabled = True),\n", "                                    # dict(bounds=[11.5001, 13], pattern='hour', enabled = True),\n", "                                    # dict(bounds=[15.001, 9.5], pattern='hour', enabled = True)]\n", "                )\n", "fig.update_yaxes(showspikes = True, spikethickness = 2)\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "f76ea55e-ca23-471b-bdd4-61998423ed52", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "toc-autonumbering": false}, "nbformat": 4, "nbformat_minor": 5}