{"cells": [{"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "0c19349e-95fa-4329-ae84-3198b630db30", "metadata": {"tags": []}, "source": ["1. 只需要替换股票名称，代码，时间，数量等直接运行即可其他不需要更改！需要存到本地，则自行存入\n", "2. 有问题扫二维码咨询，写的匆忙，也比较简单，有任何问题和需求都可以提出\n", "### ![image.png](attachment:image.png)"]}, {"cell_type": "markdown", "id": "0b7ff36d-cd63-4bb6-afd0-69500ad9fb94", "metadata": {}, "source": ["### 导入所需安装包"]}, {"cell_type": "code", "execution_count": 1, "id": "00003df9-9f7f-4c67-9f49-62fc27a807ee", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2023-05-24 09:45:07,600 I 1182346 cfg4py.core:update_config:280 | configuration is\n", "alpha: {data_home: ~/zillionare/alpha/data, tts_server: 'http://127.0.0.1:5002/api/tts?'}\n", "backtest: {url: 'http://192.168.100.114:7080/backtest/api/trade/v0.4/'}\n", "influxdb: {bucket_name: zillionare, enable_compress: true, max_query_size: 5000, org: zillionare,\n", "  token: hwxHycJfp_t6bCOYe2MhEDW4QBOO4FDtgeBWnPR6bGZJGEZ_41m_OHtTJFZKyD2HsbVqkZM8rJNkMvjyoXCG6Q==,\n", "  url: 'http://192.168.100.101:58086'}\n", "notify: {dingtalk_access_token: 58df072143b52368086736cb38236753073ccde6537650cad1d5567747803563,\n", "  keyword: trader}\n", "redis: {dsn: 'redis://192.168.100.101:56379'}\n", "tasks: {pooling: false, wr: false}\n", "\n", "2023-05-24 09:45:07,610 I 1182346 /home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/dal/cache.py:init:94 | init redis cache...\n", "2023-05-24 09:45:07,622 I 1182346 /home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/dal/cache.py:init:124 | redis cache is inited\n", "2023-05-24 09:45:07,720 I 1182346 omicron.models.security:load_securities:319 | 6834 securities loaded, types: {'mmf', 'stock', 'index', 'lof', 'reits', 'etf', 'fjm'}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["init securities done\n"]}], "source": ["# 导入所需包\n", "from coursea import *\n", "await init()"]}, {"cell_type": "markdown", "id": "13ddc08a-3618-49ed-a323-edfd1a6e96f7", "metadata": {}, "source": ["### 通过板块代码查询同花顺板块信息（名字，成员数目或清单）,行业板块，概念板块均可"]}, {"cell_type": "code", "execution_count": 2, "id": "706eef85-35cf-4a7c-9a23-6060356b09df", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'code': '881130', 'name': '计算机设备', 'stocks': 80}\n"]}], "source": ["# 返回值：\n", "# {'code': '301505', 'name': '医疗器械概念', 'stocks': 242}\n", "# or\n", "# {'code': '301505', 'name': '医疗器械概念', 'stocks': [['300916', '朗特智能'], ['300760', '迈瑞医疗']]}\n", "\n", "board_code = '881130' # 计算机设备， 可改\n", "board_info = await Board.board_info_by_id(board_code)   \n", "print(board_info) # 字典形式"]}, {"cell_type": "markdown", "id": "39dc62bc-9958-46a1-812c-68dcc82a7fa0", "metadata": {}, "source": ["### 获取股票所在同花顺行业板块信息：名称，代码"]}, {"cell_type": "code", "execution_count": 3, "id": "d65ff679-1944-4884-9329-e11e639ca830", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'code': '881130', 'name': '计算机设备', 'stocks': 80}]\n"]}], "source": ["stock_code = '002236'  # 大华股份，股票代码不带字母后缀， 可改\n", "stock_in_board = await Board.board_info_by_security(stock_code, _btype=BoardType.INDUSTRY)\n", "print(stock_in_board)"]}, {"cell_type": "markdown", "id": "a5f1f2f4-4ae1-4500-831b-19044665ff25", "metadata": {}, "source": ["### 获取指定同花顺行业板块日线数据\n", "1. 从持久化数据库中获取介于[`start`, `end`]间的行情记录\n", "2. code: 板块代码（概念、行业）\n", "    start: 起始时间\n", "    end: 结束时间，如果未指明，则取当前时间\n", "    返回dtype为`coretypes.bars_dtype`的一维numpy数组。\n", "\n", " "]}, {"cell_type": "code", "execution_count": 4, "id": "9ad6c65b-5a37-4c23-87a8-3c5b41d94a74", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["rec.array([('2022-09-01T00:00:00', 2017.631, 2049.67 , 2006.446, 2010.588, 5.86179140e+08, 8.47062180e+09, 1.),\n", "           ('2022-09-02T00:00:00', 2011.682, 2064.932, 2011.682, 2064.174, 7.49831890e+08, 8.52596990e+09, 1.),\n", "           ('2022-09-05T00:00:00', 2063.435, 2069.552, 2041.759, 2054.819, 6.64215330e+08, 9.17705230e+09, 1.),\n", "           ('2022-09-06T00:00:00', 2057.9  , 2059.847, 2038.632, 2059.847, 6.03301520e+08, 9.33921210e+09, 1.),\n", "           ('2022-09-07T00:00:00', 2054.249, 2065.291, 2044.536, 2060.678, 5.84048170e+08, 8.46979630e+09, 1.),\n", "           ('2022-09-08T00:00:00', 2056.337, 2056.337, 2033.065, 2033.415, 5.79092210e+08, 8.00887230e+09, 1.),\n", "           ('2022-09-09T00:00:00', 2035.571, 2038.241, 2017.372, 2029.761, 5.57832790e+08, 7.66872760e+09, 1.),\n", "           ('2022-09-13T00:00:00', 2034.778, 2058.166, 2034.778, 2042.109, 5.80075330e+08, 8.27695320e+09, 1.),\n", "           ('2022-09-14T00:00:00', 2010.921, 2035.147, 2006.41 , 2034.746, 5.82183500e+08, 7.84765350e+09, 1.),\n", "           ('2022-09-15T00:00:00', 2037.146, 2037.624, 1972.281, 1991.901, 6.75455560e+08, 8.99872480e+09, 1.),\n", "           ('2022-09-16T00:00:00', 1988.77 , 2025.156, 1974.527, 1974.639, 8.84237760e+08, 1.34932080e+10, 1.),\n", "           ('2022-09-19T00:00:00', 1968.544, 1969.356, 1909.105, 1914.105, 7.08704630e+08, 9.50916080e+09, 1.),\n", "           ('2022-09-20T00:00:00', 1921.349, 1941.711, 1921.223, 1936.407, 5.11101880e+08, 6.98103860e+09, 1.),\n", "           ('2022-09-21T00:00:00', 1930.257, 1969.996, 1891.775, 1963.541, 5.96341890e+08, 8.17238410e+09, 1.),\n", "           ('2022-09-22T00:00:00', 1949.016, 1986.046, 1947.529, 1963.235, 5.72595870e+08, 7.76536840e+09, 1.),\n", "           ('2022-09-23T00:00:00', 1964.854, 1964.854, 1895.217, 1902.752, 5.62060000e+08, 7.64438960e+09, 1.),\n", "           ('2022-09-26T00:00:00', 1890.473, 1891.037, 1851.802, 1855.696, 4.98740370e+08, 6.34373460e+09, 1.),\n", "           ('2022-09-27T00:00:00', 1858.265, 1901.67 , 1858.068, 1901.67 , 4.59532480e+08, 6.10874940e+09, 1.),\n", "           ('2022-09-28T00:00:00', 1896.236, 1897.423, 1843.783, 1843.783, 4.86856400e+08, 7.25819870e+09, 1.),\n", "           ('2022-09-29T00:00:00', 1864.513, 1869.34 , 1819.606, 1829.531, 5.56609310e+08, 7.18462460e+09, 1.),\n", "           ('2022-09-30T00:00:00', 1828.661, 1839.364, 1806.977, 1807.126, 5.23856110e+08, 6.72517590e+09, 1.),\n", "           ('2022-10-10T00:00:00', 1811.754, 1815.399, 1761.701, 1765.985, 6.37544290e+08, 8.68489680e+09, 1.),\n", "           ('2022-10-11T00:00:00', 1764.971, 1782.036, 1750.567, 1770.534, 5.04277060e+08, 6.77378270e+09, 1.),\n", "           ('2022-10-12T00:00:00', 1767.628, 1853.509, 1762.384, 1853.509, 7.58965850e+08, 9.40331420e+09, 1.),\n", "           ('2022-10-13T00:00:00', 1856.658, 1933.375, 1854.881, 1912.018, 1.03490602e+09, 1.32994204e+10, 1.),\n", "           ('2022-10-14T00:00:00', 1918.16 , 1952.24 , 1918.145, 1949.653, 1.01990332e+09, 1.26331033e+10, 1.),\n", "           ('2022-10-17T00:00:00', 1944.124, 2009.332, 1942.695, 2009.332, 9.71563940e+08, 1.35295150e+10, 1.),\n", "           ('2022-10-18T00:00:00', 2013.373, 2015.263, 1986.902, 2003.832, 9.16281570e+08, 1.09167523e+10, 1.),\n", "           ('2022-10-19T00:00:00', 2000.405, 2020.404, 1982.891, 2005.929, 7.86884860e+08, 1.03037568e+10, 1.),\n", "           ('2022-10-20T00:00:00', 2000.928, 2051.734, 1995.62 , 2030.331, 1.02287962e+09, 1.34126978e+10, 1.),\n", "           ('2022-10-21T00:00:00', 2034.708, 2050.399, 2001.336, 2034.886, 1.09643092e+09, 1.26340908e+10, 1.),\n", "           ('2022-10-24T00:00:00', 2035.922, 2073.967, 1998.08 , 2005.547, 1.01234163e+09, 1.29421921e+10, 1.),\n", "           ('2022-10-25T00:00:00', 1989.817, 2002.775, 1945.328, 1968.651, 8.64182980e+08, 1.04017991e+10, 1.),\n", "           ('2022-10-26T00:00:00', 1975.832, 2062.333, 1975.832, 2054.908, 1.12423901e+09, 1.56588670e+10, 1.),\n", "           ('2022-10-27T00:00:00', 2055.139, 2088.197, 2053.058, 2061.519, 1.05815551e+09, 1.51559800e+10, 1.),\n", "           ('2022-10-28T00:00:00', 2048.135, 2072.71 , 1995.28 , 2002.018, 1.13247463e+09, 1.63220810e+10, 1.),\n", "           ('2022-10-31T00:00:00', 2004.791, 2111.766, 2004.791, 2096.599, 1.35493960e+09, 1.97284780e+10, 1.),\n", "           ('2022-11-01T00:00:00', 2107.202, 2115.527, 2079.926, 2115.16 , 1.27133941e+09, 1.88216850e+10, 1.),\n", "           ('2022-11-02T00:00:00', 2120.162, 2138.333, 2114.292, 2137.797, 1.18451963e+09, 1.74964940e+10, 1.),\n", "           ('2022-11-03T00:00:00', 2120.494, 2130.974, 2092.36 , 2118.063, 9.45254540e+08, 1.39140970e+10, 1.),\n", "           ('2022-11-04T00:00:00', 2118.35 , 2139.438, 2103.126, 2137.253, 9.05956920e+08, 1.31631464e+10, 1.),\n", "           ('2022-11-07T00:00:00', 2138.973, 2168.941, 2130.865, 2148.93 , 9.24091620e+08, 1.34770710e+10, 1.),\n", "           ('2022-11-08T00:00:00', 2143.255, 2184.652, 2110.367, 2184.652, 1.30905560e+09, 1.71383690e+10, 1.),\n", "           ('2022-11-09T00:00:00', 2182.129, 2182.129, 2146.913, 2158.569, 1.10889757e+09, 1.39555670e+10, 1.),\n", "           ('2022-11-10T00:00:00', 2141.792, 2169.153, 2139.22 , 2151.439, 1.02788597e+09, 1.27980404e+10, 1.),\n", "           ('2022-11-11T00:00:00', 2186.103, 2196.833, 2130.398, 2130.561, 1.26335930e+09, 1.60542390e+10, 1.),\n", "           ('2022-11-14T00:00:00', 2122.814, 2149.907, 2105.642, 2149.756, 1.03392507e+09, 1.34236560e+10, 1.),\n", "           ('2022-11-15T00:00:00', 2144.784, 2197.567, 2143.874, 2195.03 , 1.16923052e+09, 1.65207330e+10, 1.),\n", "           ('2022-11-16T00:00:00', 2192.128, 2213.302, 2181.384, 2192.329, 1.13070532e+09, 1.35904440e+10, 1.),\n", "           ('2022-11-17T00:00:00', 2201.896, 2252.403, 2191.603, 2252.403, 1.34225510e+09, 1.82778270e+10, 1.),\n", "           ('2022-11-18T00:00:00', 2256.472, 2274.968, 2219.422, 2220.339, 1.51018590e+09, 2.25138330e+10, 1.),\n", "           ('2022-11-21T00:00:00', 2220.393, 2241.389, 2197.653, 2241.389, 1.18522601e+09, 1.69362770e+10, 1.),\n", "           ('2022-11-22T00:00:00', 2229.712, 2249.755, 2202.042, 2208.899, 1.18162632e+09, 1.72209390e+10, 1.),\n", "           ('2022-11-23T00:00:00', 2195.708, 2195.855, 2127.144, 2156.796, 1.08210254e+09, 1.52557090e+10, 1.),\n", "           ('2022-11-24T00:00:00', 2151.822, 2166.5  , 2129.783, 2134.98 , 8.64833900e+08, 1.19099026e+10, 1.),\n", "           ('2022-11-25T00:00:00', 2130.261, 2137.084, 2101.668, 2101.863, 7.27234600e+08, 9.48014490e+09, 1.),\n", "           ('2022-11-28T00:00:00', 2073.559, 2093.224, 2062.916, 2075.646, 7.49210290e+08, 1.01302553e+10, 1.),\n", "           ('2022-11-29T00:00:00', 2074.619, 2104.893, 2072.552, 2102.736, 6.94735880e+08, 9.58123750e+09, 1.),\n", "           ('2022-11-30T00:00:00', 2101.358, 2102.329, 2078.278, 2096.299, 7.11654160e+08, 9.22073440e+09, 1.),\n", "           ('2022-12-01T00:00:00', 2108.043, 2151.37 , 2107.845, 2151.37 , 9.35795270e+08, 1.33905394e+10, 1.),\n", "           ('2022-12-02T00:00:00', 2145.082, 2185.835, 2142.327, 2175.981, 8.98405210e+08, 1.39984810e+10, 1.),\n", "           ('2022-12-05T00:00:00', 2185.89 , 2199.587, 2173.394, 2188.208, 9.27903930e+08, 1.41908260e+10, 1.),\n", "           ('2022-12-06T00:00:00', 2182.451, 2191.056, 2171.285, 2179.393, 9.83483290e+08, 1.36313390e+10, 1.),\n", "           ('2022-12-07T00:00:00', 2176.322, 2183.645, 2160.888, 2165.883, 7.91151440e+08, 1.07288438e+10, 1.),\n", "           ('2022-12-08T00:00:00', 2160.701, 2162.355, 2128.147, 2130.663, 7.45712580e+08, 9.89414970e+09, 1.),\n", "           ('2022-12-09T00:00:00', 2129.095, 2132.67 , 2108.416, 2113.309, 7.86792950e+08, 1.16401221e+10, 1.),\n", "           ('2022-12-12T00:00:00', 2114.31 , 2140.659, 2111.085, 2133.074, 7.15489590e+08, 1.05814845e+10, 1.),\n", "           ('2022-12-13T00:00:00', 2134.02 , 2135.742, 2090.146, 2092.341, 6.74122990e+08, 9.13534310e+09, 1.),\n", "           ('2022-12-14T00:00:00', 2097.232, 2114.247, 2085.306, 2087.597, 6.11357390e+08, 8.46400540e+09, 1.),\n", "           ('2022-12-15T00:00:00', 2088.22 , 2123.289, 2077.156, 2111.862, 7.13785670e+08, 8.79665850e+09, 1.),\n", "           ('2022-12-16T00:00:00', 2100.666, 2102.43 , 2058.397, 2060.833, 6.69716530e+08, 8.10641290e+09, 1.),\n", "           ('2022-12-19T00:00:00', 2058.797, 2065.062, 2019.623, 2024.728, 6.70516760e+08, 7.73021870e+09, 1.),\n", "           ('2022-12-20T00:00:00', 2036.62 , 2051.121, 2017.625, 2022.133, 7.47179780e+08, 8.86446460e+09, 1.),\n", "           ('2022-12-21T00:00:00', 2010.433, 2014.085, 1983.191, 1991.646, 5.75426840e+08, 6.53631530e+09, 1.),\n", "           ('2022-12-22T00:00:00', 1996.551, 2000.843, 1954.752, 1958.543, 5.94357150e+08, 6.81686310e+09, 1.),\n", "           ('2022-12-23T00:00:00', 1949.413, 1995.664, 1949.413, 1984.263, 5.76439840e+08, 7.19849220e+09, 1.),\n", "           ('2022-12-26T00:00:00', 1983.923, 2011.187, 1983.828, 2007.469, 4.68530500e+08, 6.31767810e+09, 1.),\n", "           ('2022-12-27T00:00:00', 2016.498, 2017.543, 1986.551, 2003.411, 6.62170360e+08, 8.21140830e+09, 1.),\n", "           ('2022-12-28T00:00:00', 2000.707, 2000.707, 1961.47 , 1966.509, 7.16242530e+08, 9.74115530e+09, 1.),\n", "           ('2022-12-29T00:00:00', 1959.394, 1994.225, 1958.865, 1969.176, 6.93146310e+08, 9.54058570e+09, 1.),\n", "           ('2022-12-30T00:00:00', 1975.788, 1998.137, 1969.736, 1988.994, 5.69114830e+08, 8.09613410e+09, 1.),\n", "           ('2023-01-03T00:00:00', 1991.405, 2006.914, 1990.706, 2006.914, 1.01427657e+08, 1.32933268e+09, 1.),\n", "           ('2023-01-04T00:00:00', 2092.374, 2109.606, 2086.051, 2109.43 , 9.95729600e+08, 1.39405990e+10, 1.),\n", "           ('2023-01-05T00:00:00', 2113.511, 2118.466, 2094.447, 2113.273, 7.29712180e+08, 1.12915737e+10, 1.),\n", "           ('2023-01-06T00:00:00', 2109.826, 2110.711, 2101.291, 2107.078, 2.14429710e+08, 2.94373630e+09, 1.),\n", "           ('2023-01-09T00:00:00', 2103.222, 2122.235, 2097.346, 2109.335, 7.36849890e+08, 1.13240715e+10, 1.),\n", "           ('2023-01-10T00:00:00', 2112.548, 2124.001, 2094.94 , 2114.079, 6.88777740e+08, 1.09078943e+10, 1.),\n", "           ('2023-01-11T00:00:00', 2113.924, 2122.601, 2100.59 , 2101.788, 4.03109310e+08, 6.05842550e+09, 1.),\n", "           ('2023-01-12T00:00:00', 2085.504, 2098.408, 2077.225, 2089.845, 2.83112910e+08, 3.98555020e+09, 1.),\n", "           ('2023-01-13T00:00:00', 2093.355, 2093.355, 2073.946, 2078.707, 2.65086730e+08, 3.66940000e+09, 1.),\n", "           ('2023-01-16T00:00:00', 2090.365, 2124.264, 2090.365, 2124.264, 1.26672976e+08, 1.96001400e+09, 1.),\n", "           ('2023-01-17T00:00:00', 2127.82 , 2133.847, 2121.21 , 2131.327, 2.27205550e+08, 3.55001630e+09, 1.),\n", "           ('2023-01-18T00:00:00', 2118.033, 2118.847, 2103.617, 2111.251, 9.20155360e+07, 1.25025292e+09, 1.),\n", "           ('2023-01-19T00:00:00', 2147.122, 2163.071, 2146.995, 2162.633, 8.05885790e+07, 1.15813845e+09, 1.),\n", "           ('2023-01-20T00:00:00', 2206.518, 2231.688, 2203.97 , 2214.539, 9.51718750e+08, 1.55179280e+10, 1.),\n", "           ('2023-01-30T00:00:00', 2241.429, 2257.942, 2237.111, 2250.621, 1.16694324e+09, 1.80239610e+10, 1.),\n", "           ('2023-01-31T00:00:00', 2244.816, 2255.47 , 2236.072, 2255.47 , 9.80042260e+08, 1.45494250e+10, 1.),\n", "           ('2023-02-01T00:00:00', 2259.143, 2270.64 , 2257.08 , 2270.64 , 1.49463990e+08, 1.86363600e+09, 1.),\n", "           ('2023-02-02T00:00:00', 2325.178, 2353.195, 2324.601, 2334.14 , 1.26346869e+09, 1.81937580e+10, 1.),\n", "           ('2023-02-03T00:00:00', 2335.352, 2382.337, 2334.839, 2382.337, 1.38497120e+09, 2.02964660e+10, 1.),\n", "           ('2023-02-06T00:00:00', 2385.399, 2413.58 , 2378.145, 2393.156, 1.73553810e+09, 2.43670920e+10, 1.),\n", "           ('2023-02-07T00:00:00', 2382.488, 2403.643, 2366.893, 2403.643, 1.03070834e+09, 1.46427830e+10, 1.),\n", "           ('2023-02-08T00:00:00', 2404.72 , 2405.203, 2367.274, 2387.735, 8.01135290e+08, 1.17281141e+10, 1.),\n", "           ('2023-02-09T00:00:00', 2362.798, 2438.039, 2353.837, 2437.945, 1.57796900e+09, 2.46546220e+10, 1.),\n", "           ('2023-02-10T00:00:00', 2435.167, 2458.013, 2418.696, 2427.951, 1.52423910e+09, 2.43508170e+10, 1.),\n", "           ('2023-02-13T00:00:00', 2420.564, 2451.432, 2418.954, 2447.427, 1.40212580e+09, 2.33083720e+10, 1.),\n", "           ('2023-02-14T00:00:00', 2451.067, 2455.354, 2428.215, 2432.62 , 1.41173530e+09, 2.44090790e+10, 1.),\n", "           ('2023-02-15T00:00:00', 2434.649, 2451.607, 2430.449, 2449.158, 2.65925020e+08, 5.04419750e+09, 1.),\n", "           ('2023-02-16T00:00:00', 2502.449, 2513.039, 2420.858, 2440.503, 2.22671440e+09, 4.22878880e+10, 1.),\n", "           ('2023-02-17T00:00:00', 2439.087, 2451.629, 2389.853, 2393.025, 1.28017409e+09, 2.23890990e+10, 1.),\n", "           ('2023-02-20T00:00:00', 2393.974, 2407.348, 2369.234, 2403.51 , 1.12793239e+09, 2.13373010e+10, 1.),\n", "           ('2023-02-21T00:00:00', 2416.196, 2450.853, 2400.223, 2420.802, 1.46112120e+09, 2.69334520e+10, 1.),\n", "           ('2023-02-22T00:00:00', 2406.338, 2414.42 , 2389.139, 2399.814, 5.01206440e+08, 8.55448280e+09, 1.),\n", "           ('2023-02-23T00:00:00', 2425.25 , 2427.197, 2385.595, 2396.473, 1.26274537e+09, 2.14463870e+10, 1.),\n", "           ('2023-02-24T00:00:00', 2399.428, 2421.035, 2393.639, 2412.531, 1.43394720e+09, 2.55900600e+10, 1.),\n", "           ('2023-02-27T00:00:00', 2403.777, 2417.384, 2377.713, 2380.314, 1.37477380e+09, 2.32693150e+10, 1.),\n", "           ('2023-02-28T00:00:00', 2416.303, 2444.768, 2390.409, 2424.611, 1.50494230e+09, 2.67149840e+10, 1.),\n", "           ('2023-03-01T00:00:00', 2421.333, 2501.797, 2421.333, 2501.757, 1.47525560e+09, 2.74053940e+10, 1.)],\n", "          dtype=[('frame', '<M8[s]'), ('open', '<f4'), ('high', '<f4'), ('low', '<f4'), ('close', '<f4'), ('volume', '<f8'), ('amount', '<f8'), ('factor', '<f4')])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["start = datetime.date(2022, 9, 1)  # 起始时间\n", "end = datetime.date(2023, 3, 1)  # 截止时间\n", "board_code = '881130' # 计算机设备， 可改\n", "bars = await Board.get_bars_in_range(board_code, start, end) \n", "bars"]}, {"cell_type": "markdown", "id": "c8e7cf10-fd52-407f-8311-2c580ca424fd", "metadata": {}, "source": ["#### 行情数据转为dataframe形式，如不需要，则略过"]}, {"cell_type": "code", "execution_count": 5, "id": "11a22da4-81b5-4006-824a-891cd13bb32b", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>factor</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-09-01</td>\n", "      <td>2017.630981</td>\n", "      <td>2049.669922</td>\n", "      <td>2006.446045</td>\n", "      <td>2010.588013</td>\n", "      <td>5.861791e+08</td>\n", "      <td>8.470622e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-09-02</td>\n", "      <td>2011.682007</td>\n", "      <td>2064.931885</td>\n", "      <td>2011.682007</td>\n", "      <td>2064.174072</td>\n", "      <td>7.498319e+08</td>\n", "      <td>8.525970e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-09-05</td>\n", "      <td>2063.435059</td>\n", "      <td>2069.552002</td>\n", "      <td>2041.759033</td>\n", "      <td>2054.819092</td>\n", "      <td>6.642153e+08</td>\n", "      <td>9.177052e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-09-06</td>\n", "      <td>2057.899902</td>\n", "      <td>2059.846924</td>\n", "      <td>2038.631958</td>\n", "      <td>2059.846924</td>\n", "      <td>6.033015e+08</td>\n", "      <td>9.339212e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-09-07</td>\n", "      <td>2054.249023</td>\n", "      <td>2065.291016</td>\n", "      <td>2044.536011</td>\n", "      <td>2060.677979</td>\n", "      <td>5.840482e+08</td>\n", "      <td>8.469796e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>113</th>\n", "      <td>2023-02-23</td>\n", "      <td>2425.250000</td>\n", "      <td>2427.197021</td>\n", "      <td>2385.594971</td>\n", "      <td>2396.472900</td>\n", "      <td>1.262745e+09</td>\n", "      <td>2.144639e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>2023-02-24</td>\n", "      <td>2399.427979</td>\n", "      <td>2421.034912</td>\n", "      <td>2393.638916</td>\n", "      <td>2412.531006</td>\n", "      <td>1.433947e+09</td>\n", "      <td>2.559006e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>2023-02-27</td>\n", "      <td>2403.777100</td>\n", "      <td>2417.384033</td>\n", "      <td>2377.712891</td>\n", "      <td>2380.313965</td>\n", "      <td>1.374774e+09</td>\n", "      <td>2.326932e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116</th>\n", "      <td>2023-02-28</td>\n", "      <td>2416.302979</td>\n", "      <td>2444.768066</td>\n", "      <td>2390.408936</td>\n", "      <td>2424.611084</td>\n", "      <td>1.504942e+09</td>\n", "      <td>2.671498e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>2023-03-01</td>\n", "      <td>2421.333008</td>\n", "      <td>2501.797119</td>\n", "      <td>2421.333008</td>\n", "      <td>2501.757080</td>\n", "      <td>1.475256e+09</td>\n", "      <td>2.740539e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>118 rows × 8 columns</p>\n", "</div>"], "text/plain": ["         frame         open         high          low        close  \\\n", "0   2022-09-01  2017.630981  2049.669922  2006.446045  2010.588013   \n", "1   2022-09-02  2011.682007  2064.931885  2011.682007  2064.174072   \n", "2   2022-09-05  2063.435059  2069.552002  2041.759033  2054.819092   \n", "3   2022-09-06  2057.899902  2059.846924  2038.631958  2059.846924   \n", "4   2022-09-07  2054.249023  2065.291016  2044.536011  2060.677979   \n", "..         ...          ...          ...          ...          ...   \n", "113 2023-02-23  2425.250000  2427.197021  2385.594971  2396.472900   \n", "114 2023-02-24  2399.427979  2421.034912  2393.638916  2412.531006   \n", "115 2023-02-27  2403.777100  2417.384033  2377.712891  2380.313965   \n", "116 2023-02-28  2416.302979  2444.768066  2390.408936  2424.611084   \n", "117 2023-03-01  2421.333008  2501.797119  2421.333008  2501.757080   \n", "\n", "           volume        amount  factor  \n", "0    5.861791e+08  8.470622e+09     1.0  \n", "1    7.498319e+08  8.525970e+09     1.0  \n", "2    6.642153e+08  9.177052e+09     1.0  \n", "3    6.033015e+08  9.339212e+09     1.0  \n", "4    5.840482e+08  8.469796e+09     1.0  \n", "..            ...           ...     ...  \n", "113  1.262745e+09  2.144639e+10     1.0  \n", "114  1.433947e+09  2.559006e+10     1.0  \n", "115  1.374774e+09  2.326932e+10     1.0  \n", "116  1.504942e+09  2.671498e+10     1.0  \n", "117  1.475256e+09  2.740539e+10     1.0  \n", "\n", "[118 rows x 8 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["bars_df = pd.DataFrame(bars)\n", "bars_df"]}, {"cell_type": "markdown", "id": "7c6b26cb-0471-4d0c-b7bc-da121ec98395", "metadata": {}, "source": ["### 绘制出K线图"]}, {"cell_type": "code", "execution_count": 7, "id": "365b8977-cde2-4ecc-8d7d-d72c880b0b9e", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [2010.5880126953125, 2064.174072265625, 2054.819091796875, 2059.846923828125, 2060.677978515625, 2033.4150390625, 2029.760986328125, 2042.1090087890625, 2034.7459716796875, 1991.9010009765625, 1974.6390380859375, 1914.10498046875, 1936.406982421875, 1963.541015625, 1963.2349853515625, 1902.751953125, 1855.696044921875, 1901.6700439453125, 1843.782958984375, 1829.531005859375, 1807.1259765625, 1765.9849853515625, 1770.5340576171875, 1853.509033203125, 1912.0179443359375, 1949.6529541015625, 2009.33203125, 2003.83203125, 2005.928955078125, 2030.3310546875, 2034.885986328125, 2005.5469970703125, 1968.6510009765625, 2054.907958984375, 2061.51904296875, 2002.0179443359375, 2096.59912109375, 2115.159912109375, 2137.797119140625, 2118.06298828125, 2137.2529296875, 2148.929931640625, 2184.652099609375, 2158.569091796875, 2151.43896484375, 2130.56103515625, 2149.756103515625, 2195.030029296875, 2192.3291015625, 2252.403076171875, 2220.339111328125, 2241.388916015625, 2208.89892578125, 2156.7958984375, 2134.97998046875, 2101.863037109375, 2075.64599609375, 2102.736083984375, 2096.299072265625, 2151.3701171875, 2175.98095703125, 2188.2080078125, 2179.39306640625, 2165.883056640625, 2130.6630859375, 2113.30908203125, 2133.073974609375, 2092.341064453125, 2087.596923828125, 2111.862060546875, 2060.8330078125, 2024.72802734375, 2022.133056640625, 1991.64599609375, 1958.54296875, 1984.262939453125, 2007.468994140625, 2003.4110107421875, 1966.509033203125, 1969.176025390625, 1988.9940185546875, 2006.9139404296875, 2109.429931640625, 2113.27294921875, 2107.077880859375, 2109.3349609375, 2114.0791015625, 2101.7880859375, 2089.844970703125, 2078.70703125, 2124.263916015625, 2131.326904296875, 2111.2509765625, 2162.633056640625, 2214.5390625, 2250.62109375, 2255.469970703125, 2270.639892578125, 2334.139892578125, 2382.3369140625, 2393.156005859375, 2403.64306640625, 2387.735107421875, 2437.945068359375, 2427.950927734375, 2447.427001953125, 2432.6201171875, 2449.157958984375, 2440.5029296875, 2393.02490234375, 2403.510009765625, 2420.802001953125, 2399.81396484375, 2396.472900390625, 2412.531005859375, 2380.31396484375, 2424.611083984375, 2501.757080078125], "decreasing": {"line": {"color": "green"}}, "high": [2049.669921875, 2064.931884765625, 2069.552001953125, 2059.846923828125, 2065.291015625, 2056.3369140625, 2038.240966796875, 2058.166015625, 2035.14697265625, 2037.6240234375, 2025.156005859375, 1969.35595703125, 1941.7110595703125, 1969.9959716796875, 1986.0460205078125, 1964.85400390625, 1891.0369873046875, 1901.6700439453125, 1897.4229736328125, 1869.3399658203125, 1839.364013671875, 1815.3990478515625, 1782.0360107421875, 1853.509033203125, 1933.375, 1952.239990234375, 2009.33203125, 2015.262939453125, 2020.404052734375, 2051.73388671875, 2050.39892578125, 2073.967041015625, 2002.7750244140625, 2062.3330078125, 2088.197021484375, 2072.7099609375, 2111.76611328125, 2115.527099609375, 2138.3330078125, 2130.97412109375, 2139.43798828125, 2168.94091796875, 2184.652099609375, 2182.12890625, 2169.153076171875, 2196.8330078125, 2149.906982421875, 2197.56689453125, 2213.302001953125, 2252.403076171875, 2274.968017578125, 2241.388916015625, 2249.7548828125, 2195.85498046875, 2166.5, 2137.083984375, 2093.22412109375, 2104.89306640625, 2102.3291015625, 2151.3701171875, 2185.8349609375, 2199.5869140625, 2191.055908203125, 2183.64501953125, 2162.35498046875, 2132.669921875, 2140.658935546875, 2135.741943359375, 2114.2470703125, 2123.2890625, 2102.429931640625, 2065.06201171875, 2051.12109375, 2014.0849609375, 2000.843017578125, 1995.6639404296875, 2011.18701171875, 2017.54296875, 2000.70703125, 1994.2249755859375, 1998.136962890625, 2006.9139404296875, 2109.60595703125, 2118.466064453125, 2110.7109375, 2122.235107421875, 2124.0009765625, 2122.60107421875, 2098.407958984375, 2093.35498046875, 2124.263916015625, 2133.846923828125, 2118.846923828125, 2163.071044921875, 2231.68798828125, 2257.94189453125, 2255.469970703125, 2270.639892578125, 2353.195068359375, 2382.3369140625, 2413.580078125, 2403.64306640625, 2405.202880859375, 2438.0390625, 2458.012939453125, 2451.431884765625, 2455.35400390625, 2451.60693359375, 2513.0390625, 2451.62890625, 2407.347900390625, 2450.85302734375, 2414.419921875, 2427.197021484375, 2421.034912109375, 2417.384033203125, 2444.76806640625, 2501.797119140625], "increasing": {"line": {"color": "red"}}, "low": [2006.446044921875, 2011.6820068359375, 2041.759033203125, 2038.6319580078125, 2044.5360107421875, 2033.06494140625, 2017.3719482421875, 2034.7779541015625, 2006.4100341796875, 1972.281005859375, 1974.5269775390625, 1909.10498046875, 1921.2230224609375, 1891.7750244140625, 1947.529052734375, 1895.217041015625, 1851.802001953125, 1858.0679931640625, 1843.782958984375, 1819.60595703125, 1806.97705078125, 1761.7010498046875, 1750.5670166015625, 1762.384033203125, 1854.8809814453125, 1918.14501953125, 1942.6949462890625, 1986.9019775390625, 1982.8909912109375, 1995.6199951171875, 2001.3360595703125, 1998.0799560546875, 1945.3280029296875, 1975.83203125, 2053.05810546875, 1995.280029296875, 2004.791015625, 2079.926025390625, 2114.2919921875, 2092.360107421875, 2103.1259765625, 2130.864990234375, 2110.366943359375, 2146.9130859375, 2139.219970703125, 2130.39794921875, 2105.64208984375, 2143.8740234375, 2181.384033203125, 2191.60302734375, 2219.422119140625, 2197.653076171875, 2202.0419921875, 2127.14404296875, 2129.782958984375, 2101.66796875, 2062.916015625, 2072.552001953125, 2078.278076171875, 2107.844970703125, 2142.326904296875, 2173.39404296875, 2171.284912109375, 2160.887939453125, 2128.14697265625, 2108.416015625, 2111.0849609375, 2090.14599609375, 2085.305908203125, 2077.156005859375, 2058.39697265625, 2019.623046875, 2017.625, 1983.1910400390625, 1954.751953125, 1949.4129638671875, 1983.8280029296875, 1986.551025390625, 1961.469970703125, 1958.864990234375, 1969.7359619140625, 1990.7060546875, 2086.051025390625, 2094.447021484375, 2101.291015625, 2097.345947265625, 2094.93994140625, 2100.590087890625, 2077.22509765625, 2073.946044921875, 2090.364990234375, 2121.2099609375, 2103.616943359375, 2146.9951171875, 2203.969970703125, 2237.111083984375, 2236.072021484375, 2257.080078125, 2324.60107421875, 2334.839111328125, 2378.14501953125, 2366.89306640625, 2367.27392578125, 2353.8369140625, 2418.696044921875, 2418.9541015625, 2428.215087890625, 2430.448974609375, 2420.85791015625, 2389.85302734375, 2369.23388671875, 2400.222900390625, 2389.138916015625, 2385.594970703125, 2393.638916015625, 2377.712890625, 2390.408935546875, 2421.3330078125], "name": "计算机设备行业板块K线", "open": [2017.6309814453125, 2011.6820068359375, 2063.43505859375, 2057.89990234375, 2054.2490234375, 2056.3369140625, 2035.571044921875, 2034.7779541015625, 2010.9210205078125, 2037.14599609375, 1988.77001953125, 1968.5439453125, 1921.3489990234375, 1930.2569580078125, 1949.0159912109375, 1964.85400390625, 1890.4730224609375, 1858.2650146484375, 1896.2359619140625, 1864.512939453125, 1828.6610107421875, 1811.7540283203125, 1764.970947265625, 1767.6280517578125, 1856.657958984375, 1918.1600341796875, 1944.1240234375, 2013.373046875, 2000.405029296875, 2000.927978515625, 2034.7080078125, 2035.9219970703125, 1989.8170166015625, 1975.83203125, 2055.138916015625, 2048.135009765625, 2004.791015625, 2107.201904296875, 2120.162109375, 2120.493896484375, 2118.35009765625, 2138.972900390625, 2143.2548828125, 2182.12890625, 2141.7919921875, 2186.10302734375, 2122.81396484375, 2144.783935546875, 2192.1279296875, 2201.89599609375, 2256.471923828125, 2220.39306640625, 2229.7119140625, 2195.7080078125, 2151.822021484375, 2130.260986328125, 2073.55908203125, 2074.618896484375, 2101.35791015625, 2108.04296875, 2145.08203125, 2185.889892578125, 2182.450927734375, 2176.322021484375, 2160.700927734375, 2129.094970703125, 2114.31005859375, 2134.02001953125, 2097.23193359375, 2088.219970703125, 2100.666015625, 2058.797119140625, 2036.6199951171875, 2010.4329833984375, 1996.551025390625, 1949.4129638671875, 1983.9229736328125, 2016.498046875, 2000.70703125, 1959.39404296875, 1975.7879638671875, 1991.405029296875, 2092.3740234375, 2113.510986328125, 2109.825927734375, 2103.221923828125, 2112.548095703125, 2113.924072265625, 2085.50390625, 2093.35498046875, 2090.364990234375, 2127.820068359375, 2118.032958984375, 2147.1220703125, 2206.51806640625, 2241.428955078125, 2244.81591796875, 2259.14306640625, 2325.177978515625, 2335.35205078125, 2385.39892578125, 2382.488037109375, 2404.719970703125, 2362.798095703125, 2435.1669921875, 2420.56396484375, 2451.06689453125, 2434.64892578125, 2502.448974609375, 2439.0869140625, 2393.97412109375, 2416.196044921875, 2406.337890625, 2425.25, 2399.427978515625, 2403.777099609375, 2416.302978515625, 2421.3330078125], "text": ["2022-09-01T00:00:00", "2022-09-02T00:00:00", "2022-09-05T00:00:00", "2022-09-06T00:00:00", "2022-09-07T00:00:00", "2022-09-08T00:00:00", "2022-09-09T00:00:00", "2022-09-13T00:00:00", "2022-09-14T00:00:00", "2022-09-15T00:00:00", "2022-09-16T00:00:00", "2022-09-19T00:00:00", "2022-09-20T00:00:00", "2022-09-21T00:00:00", "2022-09-22T00:00:00", "2022-09-23T00:00:00", "2022-09-26T00:00:00", "2022-09-27T00:00:00", "2022-09-28T00:00:00", "2022-09-29T00:00:00", "2022-09-30T00:00:00", "2022-10-10T00:00:00", "2022-10-11T00:00:00", "2022-10-12T00:00:00", "2022-10-13T00:00:00", "2022-10-14T00:00:00", "2022-10-17T00:00:00", "2022-10-18T00:00:00", "2022-10-19T00:00:00", "2022-10-20T00:00:00", "2022-10-21T00:00:00", "2022-10-24T00:00:00", "2022-10-25T00:00:00", "2022-10-26T00:00:00", "2022-10-27T00:00:00", "2022-10-28T00:00:00", "2022-10-31T00:00:00", "2022-11-01T00:00:00", "2022-11-02T00:00:00", "2022-11-03T00:00:00", "2022-11-04T00:00:00", "2022-11-07T00:00:00", "2022-11-08T00:00:00", "2022-11-09T00:00:00", "2022-11-10T00:00:00", "2022-11-11T00:00:00", "2022-11-14T00:00:00", "2022-11-15T00:00:00", "2022-11-16T00:00:00", "2022-11-17T00:00:00", "2022-11-18T00:00:00", "2022-11-21T00:00:00", "2022-11-22T00:00:00", "2022-11-23T00:00:00", "2022-11-24T00:00:00", "2022-11-25T00:00:00", "2022-11-28T00:00:00", "2022-11-29T00:00:00", "2022-11-30T00:00:00", "2022-12-01T00:00:00", "2022-12-02T00:00:00", "2022-12-05T00:00:00", "2022-12-06T00:00:00", "2022-12-07T00:00:00", "2022-12-08T00:00:00", "2022-12-09T00:00:00", "2022-12-12T00:00:00", "2022-12-13T00:00:00", "2022-12-14T00:00:00", "2022-12-15T00:00:00", "2022-12-16T00:00:00", "2022-12-19T00:00:00", "2022-12-20T00:00:00", "2022-12-21T00:00:00", "2022-12-22T00:00:00", "2022-12-23T00:00:00", "2022-12-26T00:00:00", "2022-12-27T00:00:00", "2022-12-28T00:00:00", "2022-12-29T00:00:00", "2022-12-30T00:00:00", "2023-01-03T00:00:00", "2023-01-04T00:00:00", "2023-01-05T00:00:00", "2023-01-06T00:00:00", "2023-01-09T00:00:00", "2023-01-10T00:00:00", "2023-01-11T00:00:00", "2023-01-12T00:00:00", "2023-01-13T00:00:00", "2023-01-16T00:00:00", "2023-01-17T00:00:00", "2023-01-18T00:00:00", "2023-01-19T00:00:00", "2023-01-20T00:00:00", "2023-01-30T00:00:00", "2023-01-31T00:00:00", "2023-02-01T00:00:00", "2023-02-02T00:00:00", "2023-02-03T00:00:00", "2023-02-06T00:00:00", "2023-02-07T00:00:00", "2023-02-08T00:00:00", "2023-02-09T00:00:00", "2023-02-10T00:00:00", "2023-02-13T00:00:00", "2023-02-14T00:00:00", "2023-02-15T00:00:00", "2023-02-16T00:00:00", "2023-02-17T00:00:00", "2023-02-20T00:00:00", "2023-02-21T00:00:00", "2023-02-22T00:00:00", "2023-02-23T00:00:00", "2023-02-24T00:00:00", "2023-02-27T00:00:00", "2023-02-28T00:00:00", "2023-03-01T00:00:00"], "type": "candlestick", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117], "xaxis": "x", "yaxis": "y"}, {"marker": {"color": "red"}, "name": "close>=open成交量", "text": ["2022-09-02T00:00:00", "2022-09-06T00:00:00", "2022-09-07T00:00:00", "2022-09-13T00:00:00", "2022-09-14T00:00:00", "2022-09-20T00:00:00", "2022-09-21T00:00:00", "2022-09-22T00:00:00", "2022-09-27T00:00:00", "2022-10-11T00:00:00", "2022-10-12T00:00:00", "2022-10-13T00:00:00", "2022-10-14T00:00:00", "2022-10-17T00:00:00", "2022-10-19T00:00:00", "2022-10-20T00:00:00", "2022-10-21T00:00:00", "2022-10-26T00:00:00", "2022-10-27T00:00:00", "2022-10-31T00:00:00", "2022-11-01T00:00:00", "2022-11-02T00:00:00", "2022-11-04T00:00:00", "2022-11-07T00:00:00", "2022-11-08T00:00:00", "2022-11-10T00:00:00", "2022-11-14T00:00:00", "2022-11-15T00:00:00", "2022-11-16T00:00:00", "2022-11-17T00:00:00", "2022-11-21T00:00:00", "2022-11-28T00:00:00", "2022-11-29T00:00:00", "2022-12-01T00:00:00", "2022-12-02T00:00:00", "2022-12-05T00:00:00", "2022-12-12T00:00:00", "2022-12-15T00:00:00", "2022-12-23T00:00:00", "2022-12-26T00:00:00", "2022-12-29T00:00:00", "2022-12-30T00:00:00", "2023-01-03T00:00:00", "2023-01-04T00:00:00", "2023-01-09T00:00:00", "2023-01-10T00:00:00", "2023-01-12T00:00:00", "2023-01-16T00:00:00", "2023-01-17T00:00:00", "2023-01-19T00:00:00", "2023-01-20T00:00:00", "2023-01-30T00:00:00", "2023-01-31T00:00:00", "2023-02-01T00:00:00", "2023-02-02T00:00:00", "2023-02-03T00:00:00", "2023-02-06T00:00:00", "2023-02-07T00:00:00", "2023-02-09T00:00:00", "2023-02-13T00:00:00", "2023-02-15T00:00:00", "2023-02-20T00:00:00", "2023-02-21T00:00:00", "2023-02-24T00:00:00", "2023-02-28T00:00:00", "2023-03-01T00:00:00"], "type": "bar", "x": [1, 3, 4, 7, 8, 12, 13, 14, 17, 22, 23, 24, 25, 26, 28, 29, 30, 33, 34, 36, 37, 38, 40, 41, 42, 44, 46, 47, 48, 49, 51, 56, 57, 59, 60, 61, 66, 69, 75, 76, 79, 80, 81, 82, 85, 86, 88, 90, 91, 93, 94, 95, 96, 97, 98, 99, 100, 101, 103, 105, 107, 110, 111, 114, 116, 117], "xaxis": "x2", "y": [749831890, 603301520, 584048170, 580075330, 582183500, 511101880, 596341890, 572595870, 459532480, 504277060, 758965850, 1034906020, 1019903320, 971563940, 786884860, 1022879620, 1096430920, 1124239010, 1058155510, 1354939600, 1271339410, 1184519630, 905956920, 924091620, 1309055600, 1027885970, 1033925070, 1169230520, 1130705320, 1342255100, 1185226010, 749210290, 694735880, 935795270, 898405210, 927903930, 715489590, 713785670, 576439840, 468530500, 693146310, 569114830, 101427657, 995729600, 736849890, 688777740, 283112910, 126672976, 227205550, 80588579, 951718750, 1166943240, 980042260, 149463990, 1263468690, 1384971200, 1735538100, 1030708340, 1577969000, 1402125800, 265925020, 1127932390, 1461121200, 1433947200, 1504942300, 1475255600], "yaxis": "y2"}, {"marker": {"color": "green"}, "name": "close<open成交量", "text": ["2022-09-01T00:00:00", "2022-09-05T00:00:00", "2022-09-08T00:00:00", "2022-09-09T00:00:00", "2022-09-15T00:00:00", "2022-09-16T00:00:00", "2022-09-19T00:00:00", "2022-09-23T00:00:00", "2022-09-26T00:00:00", "2022-09-28T00:00:00", "2022-09-29T00:00:00", "2022-09-30T00:00:00", "2022-10-10T00:00:00", "2022-10-18T00:00:00", "2022-10-24T00:00:00", "2022-10-25T00:00:00", "2022-10-28T00:00:00", "2022-11-03T00:00:00", "2022-11-09T00:00:00", "2022-11-11T00:00:00", "2022-11-18T00:00:00", "2022-11-22T00:00:00", "2022-11-23T00:00:00", "2022-11-24T00:00:00", "2022-11-25T00:00:00", "2022-11-30T00:00:00", "2022-12-06T00:00:00", "2022-12-07T00:00:00", "2022-12-08T00:00:00", "2022-12-09T00:00:00", "2022-12-13T00:00:00", "2022-12-14T00:00:00", "2022-12-16T00:00:00", "2022-12-19T00:00:00", "2022-12-20T00:00:00", "2022-12-21T00:00:00", "2022-12-22T00:00:00", "2022-12-27T00:00:00", "2022-12-28T00:00:00", "2023-01-05T00:00:00", "2023-01-06T00:00:00", "2023-01-11T00:00:00", "2023-01-13T00:00:00", "2023-01-18T00:00:00", "2023-02-08T00:00:00", "2023-02-10T00:00:00", "2023-02-14T00:00:00", "2023-02-16T00:00:00", "2023-02-17T00:00:00", "2023-02-22T00:00:00", "2023-02-23T00:00:00", "2023-02-27T00:00:00"], "type": "bar", "x": [0, 2, 5, 6, 9, 10, 11, 15, 16, 18, 19, 20, 21, 27, 31, 32, 35, 39, 43, 45, 50, 52, 53, 54, 55, 58, 62, 63, 64, 65, 67, 68, 70, 71, 72, 73, 74, 77, 78, 83, 84, 87, 89, 92, 102, 104, 106, 108, 109, 112, 113, 115], "xaxis": "x2", "y": [749831890, 603301520, 584048170, 580075330, 582183500, 511101880, 596341890, 572595870, 459532480, 504277060, 758965850, 1034906020, 1019903320, 971563940, 786884860, 1022879620, 1096430920, 1124239010, 1058155510, 1354939600, 1271339410, 1184519630, 905956920, 924091620, 1309055600, 1027885970, 1033925070, 1169230520, 1130705320, 1342255100, 1185226010, 749210290, 694735880, 935795270, 898405210, 927903930, 715489590, 713785670, 576439840, 468530500, 693146310, 569114830, 101427657, 995729600, 736849890, 688777740, 283112910, 126672976, 227205550, 80588579, 951718750, 1166943240, 980042260, 149463990, 1263468690, 1384971200, 1735538100, 1030708340, 1577969000, 1402125800, 265925020, 1127932390, 1461121200, 1433947200, 1504942300, 1475255600], "yaxis": "y2"}], "layout": {"height": 600, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "881130: 计算机设备行业板块"}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 1], "matches": "x2", "rangeslider": {"visible": false}, "showspikes": true, "showticklabels": false, "spikethickness": 2}, "xaxis2": {"anchor": "y2", "domain": [0, 1], "showspikes": true, "spikethickness": 2}, "yaxis": {"anchor": "x", "domain": [0.3, 1], "showspikes": true, "spikethickness": 2}, "yaxis2": {"anchor": "x2", "domain": [0, 0.3], "showspikes": true, "spikethickness": 2}}}, "text/html": ["<div>                            <div id=\"9b7d4833-ea1e-46c7-b797-f2faeaa82862\" class=\"plotly-graph-div\" style=\"height:600px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"9b7d4833-ea1e-46c7-b797-f2faeaa82862\")) {                    Plotly.newPlot(                        \"9b7d4833-ea1e-46c7-b797-f2faeaa82862\",                        [{\"close\":[2010.5880126953125,2064.174072265625,2054.819091796875,2059.846923828125,2060.677978515625,2033.4150390625,2029.760986328125,2042.1090087890625,2034.7459716796875,1991.9010009765625,1974.6390380859375,1914.10498046875,1936.406982421875,1963.541015625,1963.2349853515625,1902.751953125,1855.696044921875,1901.6700439453125,1843.782958984375,1829.531005859375,1807.1259765625,1765.9849853515625,1770.5340576171875,1853.509033203125,1912.0179443359375,1949.6529541015625,2009.33203125,2003.83203125,2005.928955078125,2030.3310546875,2034.885986328125,2005.5469970703125,1968.6510009765625,2054.907958984375,2061.51904296875,2002.0179443359375,2096.59912109375,2115.159912109375,2137.797119140625,2118.06298828125,2137.2529296875,2148.929931640625,2184.652099609375,2158.569091796875,2151.43896484375,2130.56103515625,2149.756103515625,2195.030029296875,2192.3291015625,2252.403076171875,2220.339111328125,2241.388916015625,2208.89892578125,2156.7958984375,2134.97998046875,2101.863037109375,2075.64599609375,2102.736083984375,2096.299072265625,2151.3701171875,2175.98095703125,2188.2080078125,2179.39306640625,2165.883056640625,2130.6630859375,2113.30908203125,2133.073974609375,2092.341064453125,2087.596923828125,2111.862060546875,2060.8330078125,2024.72802734375,2022.133056640625,1991.64599609375,1958.54296875,1984.262939453125,2007.468994140625,2003.4110107421875,1966.509033203125,1969.176025390625,1988.9940185546875,2006.9139404296875,2109.429931640625,2113.27294921875,2107.077880859375,2109.3349609375,2114.0791015625,2101.7880859375,2089.844970703125,2078.70703125,2124.263916015625,2131.326904296875,2111.2509765625,2162.633056640625,2214.5390625,2250.62109375,2255.469970703125,2270.639892578125,2334.139892578125,2382.3369140625,2393.156005859375,2403.64306640625,2387.735107421875,2437.945068359375,2427.950927734375,2447.427001953125,2432.6201171875,2449.157958984375,2440.5029296875,2393.02490234375,2403.510009765625,2420.802001953125,2399.81396484375,2396.472900390625,2412.531005859375,2380.31396484375,2424.611083984375,2501.757080078125],\"decreasing\":{\"line\":{\"color\":\"green\"}},\"high\":[2049.669921875,2064.931884765625,2069.552001953125,2059.846923828125,2065.291015625,2056.3369140625,2038.240966796875,2058.166015625,2035.14697265625,2037.6240234375,2025.156005859375,1969.35595703125,1941.7110595703125,1969.9959716796875,1986.0460205078125,1964.85400390625,1891.0369873046875,1901.6700439453125,1897.4229736328125,1869.3399658203125,1839.364013671875,1815.3990478515625,1782.0360107421875,1853.509033203125,1933.375,1952.239990234375,2009.33203125,2015.262939453125,2020.404052734375,2051.73388671875,2050.39892578125,2073.967041015625,2002.7750244140625,2062.3330078125,2088.197021484375,2072.7099609375,2111.76611328125,2115.527099609375,2138.3330078125,2130.97412109375,2139.43798828125,2168.94091796875,2184.652099609375,2182.12890625,2169.153076171875,2196.8330078125,2149.906982421875,2197.56689453125,2213.302001953125,2252.403076171875,2274.968017578125,2241.388916015625,2249.7548828125,2195.85498046875,2166.5,2137.083984375,2093.22412109375,2104.89306640625,2102.3291015625,2151.3701171875,2185.8349609375,2199.5869140625,2191.055908203125,2183.64501953125,2162.35498046875,2132.669921875,2140.658935546875,2135.741943359375,2114.2470703125,2123.2890625,2102.429931640625,2065.06201171875,2051.12109375,2014.0849609375,2000.843017578125,1995.6639404296875,2011.18701171875,2017.54296875,2000.70703125,1994.2249755859375,1998.136962890625,2006.9139404296875,2109.60595703125,2118.466064453125,2110.7109375,2122.235107421875,2124.0009765625,2122.60107421875,2098.407958984375,2093.35498046875,2124.263916015625,2133.846923828125,2118.846923828125,2163.071044921875,2231.68798828125,2257.94189453125,2255.469970703125,2270.639892578125,2353.195068359375,2382.3369140625,2413.580078125,2403.64306640625,2405.202880859375,2438.0390625,2458.012939453125,2451.431884765625,2455.35400390625,2451.60693359375,2513.0390625,2451.62890625,2407.347900390625,2450.85302734375,2414.419921875,2427.197021484375,2421.034912109375,2417.384033203125,2444.76806640625,2501.797119140625],\"increasing\":{\"line\":{\"color\":\"red\"}},\"low\":[2006.446044921875,2011.6820068359375,2041.759033203125,2038.6319580078125,2044.5360107421875,2033.06494140625,2017.3719482421875,2034.7779541015625,2006.4100341796875,1972.281005859375,1974.5269775390625,1909.10498046875,1921.2230224609375,1891.7750244140625,1947.529052734375,1895.217041015625,1851.802001953125,1858.0679931640625,1843.782958984375,1819.60595703125,1806.97705078125,1761.7010498046875,1750.5670166015625,1762.384033203125,1854.8809814453125,1918.14501953125,1942.6949462890625,1986.9019775390625,1982.8909912109375,1995.6199951171875,2001.3360595703125,1998.0799560546875,1945.3280029296875,1975.83203125,2053.05810546875,1995.280029296875,2004.791015625,2079.926025390625,2114.2919921875,2092.360107421875,2103.1259765625,2130.864990234375,2110.366943359375,2146.9130859375,2139.219970703125,2130.39794921875,2105.64208984375,2143.8740234375,2181.384033203125,2191.60302734375,2219.422119140625,2197.653076171875,2202.0419921875,2127.14404296875,2129.782958984375,2101.66796875,2062.916015625,2072.552001953125,2078.278076171875,2107.844970703125,2142.326904296875,2173.39404296875,2171.284912109375,2160.887939453125,2128.14697265625,2108.416015625,2111.0849609375,2090.14599609375,2085.305908203125,2077.156005859375,2058.39697265625,2019.623046875,2017.625,1983.1910400390625,1954.751953125,1949.4129638671875,1983.8280029296875,1986.551025390625,1961.469970703125,1958.864990234375,1969.7359619140625,1990.7060546875,2086.051025390625,2094.447021484375,2101.291015625,2097.345947265625,2094.93994140625,2100.590087890625,2077.22509765625,2073.946044921875,2090.364990234375,2121.2099609375,2103.616943359375,2146.9951171875,2203.969970703125,2237.111083984375,2236.072021484375,2257.080078125,2324.60107421875,2334.839111328125,2378.14501953125,2366.89306640625,2367.27392578125,2353.8369140625,2418.696044921875,2418.9541015625,2428.215087890625,2430.448974609375,2420.85791015625,2389.85302734375,2369.23388671875,2400.222900390625,2389.138916015625,2385.594970703125,2393.638916015625,2377.712890625,2390.408935546875,2421.3330078125],\"name\":\"\\u8ba1\\u7b97\\u673a\\u8bbe\\u5907\\u884c\\u4e1a\\u677f\\u5757K\\u7ebf\",\"open\":[2017.6309814453125,2011.6820068359375,2063.43505859375,2057.89990234375,2054.2490234375,2056.3369140625,2035.571044921875,2034.7779541015625,2010.9210205078125,2037.14599609375,1988.77001953125,1968.5439453125,1921.3489990234375,1930.2569580078125,1949.0159912109375,1964.85400390625,1890.4730224609375,1858.2650146484375,1896.2359619140625,1864.512939453125,1828.6610107421875,1811.7540283203125,1764.970947265625,1767.6280517578125,1856.657958984375,1918.1600341796875,1944.1240234375,2013.373046875,2000.405029296875,2000.927978515625,2034.7080078125,2035.9219970703125,1989.8170166015625,1975.83203125,2055.138916015625,2048.135009765625,2004.791015625,2107.201904296875,2120.162109375,2120.493896484375,2118.35009765625,2138.972900390625,2143.2548828125,2182.12890625,2141.7919921875,2186.10302734375,2122.81396484375,2144.783935546875,2192.1279296875,2201.89599609375,2256.471923828125,2220.39306640625,2229.7119140625,2195.7080078125,2151.822021484375,2130.260986328125,2073.55908203125,2074.618896484375,2101.35791015625,2108.04296875,2145.08203125,2185.889892578125,2182.450927734375,2176.322021484375,2160.700927734375,2129.094970703125,2114.31005859375,2134.02001953125,2097.23193359375,2088.219970703125,2100.666015625,2058.797119140625,2036.6199951171875,2010.4329833984375,1996.551025390625,1949.4129638671875,1983.9229736328125,2016.498046875,2000.70703125,1959.39404296875,1975.7879638671875,1991.405029296875,2092.3740234375,2113.510986328125,2109.825927734375,2103.221923828125,2112.548095703125,2113.924072265625,2085.50390625,2093.35498046875,2090.364990234375,2127.820068359375,2118.032958984375,2147.1220703125,2206.51806640625,2241.428955078125,2244.81591796875,2259.14306640625,2325.177978515625,2335.35205078125,2385.39892578125,2382.488037109375,2404.719970703125,2362.798095703125,2435.1669921875,2420.56396484375,2451.06689453125,2434.64892578125,2502.448974609375,2439.0869140625,2393.97412109375,2416.196044921875,2406.337890625,2425.25,2399.427978515625,2403.777099609375,2416.302978515625,2421.3330078125],\"text\":[\"2022-09-01T00:00:00\",\"2022-09-02T00:00:00\",\"2022-09-05T00:00:00\",\"2022-09-06T00:00:00\",\"2022-09-07T00:00:00\",\"2022-09-08T00:00:00\",\"2022-09-09T00:00:00\",\"2022-09-13T00:00:00\",\"2022-09-14T00:00:00\",\"2022-09-15T00:00:00\",\"2022-09-16T00:00:00\",\"2022-09-19T00:00:00\",\"2022-09-20T00:00:00\",\"2022-09-21T00:00:00\",\"2022-09-22T00:00:00\",\"2022-09-23T00:00:00\",\"2022-09-26T00:00:00\",\"2022-09-27T00:00:00\",\"2022-09-28T00:00:00\",\"2022-09-29T00:00:00\",\"2022-09-30T00:00:00\",\"2022-10-10T00:00:00\",\"2022-10-11T00:00:00\",\"2022-10-12T00:00:00\",\"2022-10-13T00:00:00\",\"2022-10-14T00:00:00\",\"2022-10-17T00:00:00\",\"2022-10-18T00:00:00\",\"2022-10-19T00:00:00\",\"2022-10-20T00:00:00\",\"2022-10-21T00:00:00\",\"2022-10-24T00:00:00\",\"2022-10-25T00:00:00\",\"2022-10-26T00:00:00\",\"2022-10-27T00:00:00\",\"2022-10-28T00:00:00\",\"2022-10-31T00:00:00\",\"2022-11-01T00:00:00\",\"2022-11-02T00:00:00\",\"2022-11-03T00:00:00\",\"2022-11-04T00:00:00\",\"2022-11-07T00:00:00\",\"2022-11-08T00:00:00\",\"2022-11-09T00:00:00\",\"2022-11-10T00:00:00\",\"2022-11-11T00:00:00\",\"2022-11-14T00:00:00\",\"2022-11-15T00:00:00\",\"2022-11-16T00:00:00\",\"2022-11-17T00:00:00\",\"2022-11-18T00:00:00\",\"2022-11-21T00:00:00\",\"2022-11-22T00:00:00\",\"2022-11-23T00:00:00\",\"2022-11-24T00:00:00\",\"2022-11-25T00:00:00\",\"2022-11-28T00:00:00\",\"2022-11-29T00:00:00\",\"2022-11-30T00:00:00\",\"2022-12-01T00:00:00\",\"2022-12-02T00:00:00\",\"2022-12-05T00:00:00\",\"2022-12-06T00:00:00\",\"2022-12-07T00:00:00\",\"2022-12-08T00:00:00\",\"2022-12-09T00:00:00\",\"2022-12-12T00:00:00\",\"2022-12-13T00:00:00\",\"2022-12-14T00:00:00\",\"2022-12-15T00:00:00\",\"2022-12-16T00:00:00\",\"2022-12-19T00:00:00\",\"2022-12-20T00:00:00\",\"2022-12-21T00:00:00\",\"2022-12-22T00:00:00\",\"2022-12-23T00:00:00\",\"2022-12-26T00:00:00\",\"2022-12-27T00:00:00\",\"2022-12-28T00:00:00\",\"2022-12-29T00:00:00\",\"2022-12-30T00:00:00\",\"2023-01-03T00:00:00\",\"2023-01-04T00:00:00\",\"2023-01-05T00:00:00\",\"2023-01-06T00:00:00\",\"2023-01-09T00:00:00\",\"2023-01-10T00:00:00\",\"2023-01-11T00:00:00\",\"2023-01-12T00:00:00\",\"2023-01-13T00:00:00\",\"2023-01-16T00:00:00\",\"2023-01-17T00:00:00\",\"2023-01-18T00:00:00\",\"2023-01-19T00:00:00\",\"2023-01-20T00:00:00\",\"2023-01-30T00:00:00\",\"2023-01-31T00:00:00\",\"2023-02-01T00:00:00\",\"2023-02-02T00:00:00\",\"2023-02-03T00:00:00\",\"2023-02-06T00:00:00\",\"2023-02-07T00:00:00\",\"2023-02-08T00:00:00\",\"2023-02-09T00:00:00\",\"2023-02-10T00:00:00\",\"2023-02-13T00:00:00\",\"2023-02-14T00:00:00\",\"2023-02-15T00:00:00\",\"2023-02-16T00:00:00\",\"2023-02-17T00:00:00\",\"2023-02-20T00:00:00\",\"2023-02-21T00:00:00\",\"2023-02-22T00:00:00\",\"2023-02-23T00:00:00\",\"2023-02-24T00:00:00\",\"2023-02-27T00:00:00\",\"2023-02-28T00:00:00\",\"2023-03-01T00:00:00\"],\"x\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117],\"type\":\"candlestick\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"color\":\"red\"},\"name\":\"close>=open\\u6210\\u4ea4\\u91cf\",\"text\":[\"2022-09-02T00:00:00\",\"2022-09-06T00:00:00\",\"2022-09-07T00:00:00\",\"2022-09-13T00:00:00\",\"2022-09-14T00:00:00\",\"2022-09-20T00:00:00\",\"2022-09-21T00:00:00\",\"2022-09-22T00:00:00\",\"2022-09-27T00:00:00\",\"2022-10-11T00:00:00\",\"2022-10-12T00:00:00\",\"2022-10-13T00:00:00\",\"2022-10-14T00:00:00\",\"2022-10-17T00:00:00\",\"2022-10-19T00:00:00\",\"2022-10-20T00:00:00\",\"2022-10-21T00:00:00\",\"2022-10-26T00:00:00\",\"2022-10-27T00:00:00\",\"2022-10-31T00:00:00\",\"2022-11-01T00:00:00\",\"2022-11-02T00:00:00\",\"2022-11-04T00:00:00\",\"2022-11-07T00:00:00\",\"2022-11-08T00:00:00\",\"2022-11-10T00:00:00\",\"2022-11-14T00:00:00\",\"2022-11-15T00:00:00\",\"2022-11-16T00:00:00\",\"2022-11-17T00:00:00\",\"2022-11-21T00:00:00\",\"2022-11-28T00:00:00\",\"2022-11-29T00:00:00\",\"2022-12-01T00:00:00\",\"2022-12-02T00:00:00\",\"2022-12-05T00:00:00\",\"2022-12-12T00:00:00\",\"2022-12-15T00:00:00\",\"2022-12-23T00:00:00\",\"2022-12-26T00:00:00\",\"2022-12-29T00:00:00\",\"2022-12-30T00:00:00\",\"2023-01-03T00:00:00\",\"2023-01-04T00:00:00\",\"2023-01-09T00:00:00\",\"2023-01-10T00:00:00\",\"2023-01-12T00:00:00\",\"2023-01-16T00:00:00\",\"2023-01-17T00:00:00\",\"2023-01-19T00:00:00\",\"2023-01-20T00:00:00\",\"2023-01-30T00:00:00\",\"2023-01-31T00:00:00\",\"2023-02-01T00:00:00\",\"2023-02-02T00:00:00\",\"2023-02-03T00:00:00\",\"2023-02-06T00:00:00\",\"2023-02-07T00:00:00\",\"2023-02-09T00:00:00\",\"2023-02-13T00:00:00\",\"2023-02-15T00:00:00\",\"2023-02-20T00:00:00\",\"2023-02-21T00:00:00\",\"2023-02-24T00:00:00\",\"2023-02-28T00:00:00\",\"2023-03-01T00:00:00\"],\"x\":[1,3,4,7,8,12,13,14,17,22,23,24,25,26,28,29,30,33,34,36,37,38,40,41,42,44,46,47,48,49,51,56,57,59,60,61,66,69,75,76,79,80,81,82,85,86,88,90,91,93,94,95,96,97,98,99,100,101,103,105,107,110,111,114,116,117],\"y\":[749831890.0,603301520.0,584048170.0,580075330.0,582183500.0,511101880.0,596341890.0,572595870.0,459532480.0,504277060.0,758965850.0,1034906020.0,1019903320.0,971563940.0,786884860.0,1022879620.0,1096430920.0,1124239010.0,1058155510.0,1354939600.0,1271339410.0,1184519630.0,905956920.0,924091620.0,1309055600.0,1027885970.0,1033925070.0,1169230520.0,1130705320.0,1342255100.0,1185226010.0,749210290.0,694735880.0,935795270.0,898405210.0,927903930.0,715489590.0,713785670.0,576439840.0,468530500.0,693146310.0,569114830.0,101427657.0,995729600.0,736849890.0,688777740.0,283112910.0,126672976.0,227205550.0,80588579.0,951718750.0,1166943240.0,980042260.0,149463990.0,1263468690.0,1384971200.0,1735538100.0,1030708340.0,1577969000.0,1402125800.0,265925020.0,1127932390.0,1461121200.0,1433947200.0,1504942300.0,1475255600.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y2\"},{\"marker\":{\"color\":\"green\"},\"name\":\"close<open\\u6210\\u4ea4\\u91cf\",\"text\":[\"2022-09-01T00:00:00\",\"2022-09-05T00:00:00\",\"2022-09-08T00:00:00\",\"2022-09-09T00:00:00\",\"2022-09-15T00:00:00\",\"2022-09-16T00:00:00\",\"2022-09-19T00:00:00\",\"2022-09-23T00:00:00\",\"2022-09-26T00:00:00\",\"2022-09-28T00:00:00\",\"2022-09-29T00:00:00\",\"2022-09-30T00:00:00\",\"2022-10-10T00:00:00\",\"2022-10-18T00:00:00\",\"2022-10-24T00:00:00\",\"2022-10-25T00:00:00\",\"2022-10-28T00:00:00\",\"2022-11-03T00:00:00\",\"2022-11-09T00:00:00\",\"2022-11-11T00:00:00\",\"2022-11-18T00:00:00\",\"2022-11-22T00:00:00\",\"2022-11-23T00:00:00\",\"2022-11-24T00:00:00\",\"2022-11-25T00:00:00\",\"2022-11-30T00:00:00\",\"2022-12-06T00:00:00\",\"2022-12-07T00:00:00\",\"2022-12-08T00:00:00\",\"2022-12-09T00:00:00\",\"2022-12-13T00:00:00\",\"2022-12-14T00:00:00\",\"2022-12-16T00:00:00\",\"2022-12-19T00:00:00\",\"2022-12-20T00:00:00\",\"2022-12-21T00:00:00\",\"2022-12-22T00:00:00\",\"2022-12-27T00:00:00\",\"2022-12-28T00:00:00\",\"2023-01-05T00:00:00\",\"2023-01-06T00:00:00\",\"2023-01-11T00:00:00\",\"2023-01-13T00:00:00\",\"2023-01-18T00:00:00\",\"2023-02-08T00:00:00\",\"2023-02-10T00:00:00\",\"2023-02-14T00:00:00\",\"2023-02-16T00:00:00\",\"2023-02-17T00:00:00\",\"2023-02-22T00:00:00\",\"2023-02-23T00:00:00\",\"2023-02-27T00:00:00\"],\"x\":[0,2,5,6,9,10,11,15,16,18,19,20,21,27,31,32,35,39,43,45,50,52,53,54,55,58,62,63,64,65,67,68,70,71,72,73,74,77,78,83,84,87,89,92,102,104,106,108,109,112,113,115],\"y\":[749831890.0,603301520.0,584048170.0,580075330.0,582183500.0,511101880.0,596341890.0,572595870.0,459532480.0,504277060.0,758965850.0,1034906020.0,1019903320.0,971563940.0,786884860.0,1022879620.0,1096430920.0,1124239010.0,1058155510.0,1354939600.0,1271339410.0,1184519630.0,905956920.0,924091620.0,1309055600.0,1027885970.0,1033925070.0,1169230520.0,1130705320.0,1342255100.0,1185226010.0,749210290.0,694735880.0,935795270.0,898405210.0,927903930.0,715489590.0,713785670.0,576439840.0,468530500.0,693146310.0,569114830.0,101427657.0,995729600.0,736849890.0,688777740.0,283112910.0,126672976.0,227205550.0,80588579.0,951718750.0,1166943240.0,980042260.0,149463990.0,1263468690.0,1384971200.0,1735538100.0,1030708340.0,1577969000.0,1402125800.0,265925020.0,1127932390.0,1461121200.0,1433947200.0,1504942300.0,1475255600.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y2\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"matches\":\"x2\",\"showticklabels\":false,\"rangeslider\":{\"visible\":false},\"showspikes\":true,\"spikethickness\":2},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.3,1],\"showspikes\":true,\"spikethickness\":2},\"xaxis2\":{\"anchor\":\"y2\",\"domain\":[0.0,1.0],\"showspikes\":true,\"spikethickness\":2},\"yaxis2\":{\"anchor\":\"x2\",\"domain\":[0,0.3],\"showspikes\":true,\"spikethickness\":2},\"title\":{\"text\":\"881130: \\u8ba1\\u7b97\\u673a\\u8bbe\\u5907\\u884c\\u4e1a\\u677f\\u5757\"},\"width\":1000,\"height\":600},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('9b7d4833-ea1e-46c7-b797-f2faeaa82862');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go \n", "from plotly.subplots import make_subplots\n", "import numpy as np \n", "\n", "name = board_info['name']\n", "frame = bars['frame']\n", "close = bars['close']\n", "rise_fall = close-bars['open']\n", "index = np.arange(len(bars))\n", "\n", "fig = make_subplots(rows=2, cols=1, specs=[[{}], [{}]], shared_xaxes=True, shared_yaxes=False)\n", "# K线图\n", "fig.add_trace(go.Candlestick(\n", "    x = index, \n", "    close = bars['close'], \n", "    open = bars['open'], \n", "    high = bars['high'], \n", "    low = bars['low'],\n", "    increasing=dict(line=dict(color='red')), \n", "    decreasing=dict(line=dict(color='green')), \n", "    name = (f'{name}行业板块K线'), \n", "    text=frame,\n", "), row = 1, col = 1)\n", "\n", "# 交易量\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall>=0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close>=open成交量',\n", "    marker = dict(color = 'red'),\n", "    text = bars['frame'][rise_fall>=0]\n", "), row = 2, col = 1)\n", "\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall<0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close<open成交量',\n", "    marker = dict(color = 'green'),\n", "    text = bars['frame'][rise_fall<0]\n", "), row = 2, col = 1)\n", "\n", "fig.update_layout(\n", "    title = (f'{board_code}: {name}行业板块'), width = 1000, height = 600\n", ")\n", "fig.update_yaxes(dict(domain=[0.3, 1]), row = 1, col = 1)\n", "fig.update_yaxes(dict(domain=[0, 0.3]), row = 2, col = 1)\n", "\n", "fig.update_xaxes(rangeslider_visible = False, row = 1, col = 1)\n", "fig.update_xaxes(showspikes = True, spikethickness = 2, \n", "                # rangebreaks = [dict(bounds=[6, 1], pattern='day of week', enabled = True),\n", "                                    # dict(bounds=[11.5001, 13], pattern='hour', enabled = True),\n", "                                    # dict(bounds=[15.001, 9.5], pattern='hour', enabled = True)]\n", "                )\n", "fig.update_yaxes(showspikes = True, spikethickness = 2)\n", "fig.show()\n"]}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "toc-autonumbering": false, "vscode": {"interpreter": {"hash": "046c8af816d98eb55bb2070cfc0dea3510f631af20975c6dcbe41ce56bdf6058"}}}, "nbformat": 4, "nbformat_minor": 5}