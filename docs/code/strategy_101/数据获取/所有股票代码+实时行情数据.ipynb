{"cells": [{"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "0c19349e-95fa-4329-ae84-3198b630db30", "metadata": {"tags": []}, "source": ["1. 只需要替换股票名称，代码，时间，数量等直接运行即可其他不需要更改！需要存到本地，则自行存入\n", "2. 有问题扫二维码咨询，写的匆忙，也比较简单，有任何问题和需求都可以提出\n", "### ![image.png](attachment:image.png)\n"]}, {"cell_type": "markdown", "id": "0b7ff36d-cd63-4bb6-afd0-69500ad9fb94", "metadata": {}, "source": ["### 导入所需安装包"]}, {"cell_type": "code", "execution_count": 1, "id": "00003df9-9f7f-4c67-9f49-62fc27a807ee", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init securities done\n"]}], "source": ["# 导入所需包\n", "import omicron\n", "import pandas as pd\n", "import numpy as np\n", "from omicron.models.stock import Stock\n", "from omicron.models.security import Security\n", "import datetime\n", "import cfg4py\n", "from IPython.display import display\n", "\n", "# 初始化配置，不可删！\n", "cfg = cfg4py.init('~/cheese/config')\n", "await omicron.init()"]}, {"cell_type": "markdown", "id": "86ce72ed-0f54-4e5c-9146-9ffb4cb2eabf", "metadata": {}, "source": ["### 获取所有股票代码"]}, {"cell_type": "code", "execution_count": 3, "id": "beab0b24-2def-4d80-be34-bb6d87243ecc", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['000001.XSHE', '000002.XSHE', '000004.XSHE', '000005.XSHE', '000006.XSHE', '000007.XSHE', '000008.XSHE', '000009.XSHE', '000010.XSHE', '000011.XSHE']\n"]}], "source": ["# 获取所有股票代码\n", "stocks = await Security.select().types(['stock']).eval()  #所有股票\n", "# stocks = await Security.select().types(['stock']).excluse_st().exclude_cyb().exclude_kcb().eval()  #排除ST,创业板，科创板\n", "\n", "print(stocks[:10]) # 打印前10个"]}, {"cell_type": "markdown", "id": "5a1efa2d-d30d-4853-ad9e-534b96ff9a8c", "metadata": {}, "source": ["### 获取指定股票实时行情数据\n", "获取多支股票的最新价格（交易日当天），暂不包括指数\n", "\n", "价格数据每5秒更新一次，接受多只股票查询，返回最后缓存的价格"]}, {"cell_type": "code", "execution_count": 5, "id": "d65ff679-1944-4884-9329-e11e639ca830", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["[12.87, 15.75, 9.76, 1.78, 5.35, 7.66, 2.44, 11.74, 3.15, 10.7]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 选择前10只股票，获取实时行情数据\n", "selected_stocks = stocks[:10]  \n", "latest_prices = await Stock.get_latest_price(selected_stocks)\n", "latest_prices"]}, {"cell_type": "markdown", "id": "a5f1f2f4-4ae1-4500-831b-19044665ff25", "metadata": {}, "source": ["### 数据整理\n", "列出股票代码，名称，最新价格"]}, {"cell_type": "code", "execution_count": 6, "id": "8301b2eb-f91e-40c7-8aff-c2b269e69e6a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>代码</th>\n", "      <th>名称</th>\n", "      <th>最新价格</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>000001.XSHE</td>\n", "      <td>平安银行</td>\n", "      <td>12.87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000002.XSHE</td>\n", "      <td>万科A</td>\n", "      <td>15.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000004.XSHE</td>\n", "      <td>ST国华</td>\n", "      <td>9.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>000005.XSHE</td>\n", "      <td>ST星源</td>\n", "      <td>1.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000006.XSHE</td>\n", "      <td>深振业A</td>\n", "      <td>5.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>000007.XSHE</td>\n", "      <td>全新好</td>\n", "      <td>7.66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>000008.XSHE</td>\n", "      <td>神州高铁</td>\n", "      <td>2.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>000009.XSHE</td>\n", "      <td>中国宝安</td>\n", "      <td>11.74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>000010.XSHE</td>\n", "      <td>美丽生态</td>\n", "      <td>3.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>000011.XSHE</td>\n", "      <td>深物业A</td>\n", "      <td>10.70</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            代码    名称   最新价格\n", "0  000001.XSHE  平安银行  12.87\n", "1  000002.XSHE   万科A  15.75\n", "2  000004.XSHE  ST国华   9.76\n", "3  000005.XSHE  ST星源   1.78\n", "4  000006.XSHE  深振业A   5.35\n", "5  000007.XSHE   全新好   7.66\n", "6  000008.XSHE  神州高铁   2.44\n", "7  000009.XSHE  中国宝安  11.74\n", "8  000010.XSHE  美丽生态   3.15\n", "9  000011.XSHE  深物业A  10.70"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data_df = pd.DataFrame()\n", "for (code, price) in zip(selected_stocks, latest_prices):\n", "    name = await Security.alias(code)\n", "    info = pd.DataFrame([[code, name, price]], columns = ['代码','名称','最新价格'])\n", "    data_df = pd.concat([data_df, info])\n", "    \n", "data_df = data_df.reset_index(drop=True)\n", "display(data_df)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "toc-autonumbering": false}, "nbformat": 4, "nbformat_minor": 5}