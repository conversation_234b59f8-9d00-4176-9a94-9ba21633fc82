{"cells": [{"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "0c19349e-95fa-4329-ae84-3198b630db30", "metadata": {"tags": []}, "source": ["1. 只需要替换股票名称，代码，时间，数量等直接运行即可其他不需要更改！需要存到本地，则自行存入\n", "2. 有问题扫二维码咨询，写的匆忙，也比较简单，有任何问题和需求都可以提出\n", "### ![image.png](attachment:image.png)\n"]}, {"cell_type": "markdown", "id": "0b7ff36d-cd63-4bb6-afd0-69500ad9fb94", "metadata": {}, "source": ["### 导入所需安装包，获取指定股票代码"]}, {"cell_type": "code", "execution_count": null, "id": "00003df9-9f7f-4c67-9f49-62fc27a807ee", "metadata": {"tags": []}, "outputs": [], "source": ["# 导入所需包\n", "import omicron\n", "from coretypes import BarsArray, FrameType  \n", "import pandas as pd\n", "from omicron.models.stock import Stock\n", "from omicron.models.security import Security\n", "import datetime\n", "import cfg4py\n", "\n", "# 初始化配置，不可删！\n", "cfg = cfg4py.init('/etc/zillionare')\n", "await omicron.init()\n", "\n", "# modified by be<PERSON><PERSON>\n", "\n", "# 如果不知道股票具体代码，对股票/指数进行模糊匹配查找\n", "# 可以是股票/指数代码，也可以是字母（按name查找），也可以是汉字（按显示名查找）\n", "stock_info = Stock.fuzzy_match('上证指数')   # '平安银行', 板块个股均可， 可改\n", "print(stock_info) # 字典形式，key为代码，value为股票具体信息，时间是股票上市时间和截止时间（截止时间不一定是真正截止时间）\n"]}, {"cell_type": "code", "execution_count": null, "id": "d65ff679-1944-4884-9329-e11e639ca830", "metadata": {"tags": []}, "outputs": [], "source": ["# 获取代码\n", "code = list(stock_info.keys())[0]\n", "print(code)"]}, {"cell_type": "markdown", "id": "a5f1f2f4-4ae1-4500-831b-19044665ff25", "metadata": {}, "source": ["### 获取指定条数股票/指数的日线数据\n", "1. 获取到end为止的n个行情数据,返回的数据是按照时间顺序递增排序的。在遇到停牌的情况时，该时段数据将被跳过，因此返回的记录可能不是交易日连续的，并且可能不足n个。如果系统当前没有到指定时间end的数据，将尽最大努力返回数据。调用者可以通过判断最后一条数据的时间是否等于end来判断是否获取到了全部数据。"]}, {"cell_type": "code", "execution_count": null, "id": "8301b2eb-f91e-40c7-8aff-c2b269e69e6a", "metadata": {"tags": []}, "outputs": [], "source": ["#时间单位 FrameType.MIN30是30分钟线，FrameType.DAY是日线，以下是可供选择的时间单位\n", "# 日线级别：\n", "# FrameType.DAY,\n", "# FrameType.WEEK,\n", "# FrameType.MONTH,\n", "# FrameType.QUARTER,\n", "# FrameType.YEAR,\n", "\n", "n = 120   \n", "end = datetime.date(2023, 3, 1)   # 时间可改\n", "bars = await Stock.get_bars(code, n, FrameType.DAY, end, fq=True, unclosed=True)  # 如果当天未收盘，最后一行数据是当前股票实时价格 \n", "# fq是否对返回记录进行复权。如果为True的话，则进行前复权。默认True\n", "# unclosed 是否包含最新未收盘的数据？ 默认True.\n", "\n", "print(bars[:10]) # 看一下前十个，返回数据为ndarray形式，没有返回dataframe形式，为了提高后续计算速度\n"]}, {"cell_type": "markdown", "id": "8c76f6fa-b456-456e-a031-cd01fe720fb4", "metadata": {}, "source": ["#### 行情数据转为dataframe形式，如不需要，则略过"]}, {"cell_type": "code", "execution_count": null, "id": "cdac4adf-ef4d-4475-b154-8195688dc6dc", "metadata": {"tags": []}, "outputs": [], "source": ["bars_df = pd.DataFrame(bars)\n", "bars_df[:10]"]}, {"cell_type": "markdown", "id": "442a61f6-e027-4c95-99f8-b713d20a4950", "metadata": {}, "source": ["### 获取指定时间范围的股票/指数的日线数据"]}, {"cell_type": "code", "execution_count": null, "id": "9ad6c65b-5a37-4c23-87a8-3c5b41d94a74", "metadata": {"tags": []}, "outputs": [], "source": ["start = datetime.date(2022, 3, 1)  # 起始时间， 可改\n", "end = datetime.date(2023, 3, 1)  # 截止时间， 可改\n", "bars = await Stock.get_bars_in_range(code, FrameType.DAY, start, end, fq=True, unclosed=True) # 如果当天未收盘，最后一行数据是当前股票实时价格\n", "bars[:10]"]}, {"cell_type": "markdown", "id": "c8e7cf10-fd52-407f-8311-2c580ca424fd", "metadata": {}, "source": ["#### 行情数据转为dataframe形式，如不需要，则略过"]}, {"cell_type": "code", "execution_count": null, "id": "11a22da4-81b5-4006-824a-891cd13bb32b", "metadata": {"tags": []}, "outputs": [], "source": ["bars_df = pd.DataFrame(bars)\n", "bars_df[:10]"]}, {"attachments": {}, "cell_type": "markdown", "id": "90e3ebe8-d968-4bcb-9ea8-acd419cddd3a", "metadata": {}, "source": ["### 绘制出交互式K线图"]}, {"cell_type": "code", "execution_count": null, "id": "78068daf-2477-45cc-bf3b-90b55e36b7ed", "metadata": {"tags": []}, "outputs": [], "source": ["import plotly.graph_objects as go \n", "from plotly.subplots import make_subplots\n", "import numpy as np \n", "\n", "name = await Security.alias(code)\n", "frame = bars['frame']\n", "close = bars['close']\n", "index = np.arange(len(bars))\n", "rise_fall = close-bars['open']\n", "\n", "fig = make_subplots(rows=2, cols=1, specs=[[{}], [{}]], shared_xaxes=True, shared_yaxes=False)\n", "# K线\n", "fig.add_trace(go.Candlestick(\n", "    x = index, \n", "    close = bars['close'], \n", "    open = bars['open'], \n", "    high = bars['high'], \n", "    low = bars['low'],\n", "    increasing=dict(line=dict(color='red')), \n", "    decreasing=dict(line=dict(color='green')), \n", "    name = 'K线', \n", "    text=frame,\n", "), row = 1, col = 1)\n", "\n", "# 交易量\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall>=0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close>=open成交量',\n", "    marker = dict(color = 'red'),\n", "    text = bars['frame'][rise_fall>=0]\n", "), row = 2, col = 1)\n", "\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall<0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close<open成交量',\n", "    marker = dict(color = 'green'),\n", "    text = bars['frame'][rise_fall<0]\n", "), row = 2, col = 1)\n", "\n", "fig.update_layout(\n", "    title = (f'{code}: {name}'), width = 1000, height = 600\n", ")\n", "fig.update_yaxes(dict(domain=[0.3, 1]), row = 1, col = 1)\n", "fig.update_yaxes(dict(domain=[0, 0.3]), row = 2, col = 1)\n", "\n", "fig.update_xaxes(rangeslider_visible = False, row = 1, col = 1)\n", "fig.update_xaxes(showspikes = True, spikethickness = 2, \n", "                # rangebreaks = [dict(bounds=[6, 1], pattern='day of week', enabled = True),\n", "                                    # dict(bounds=[11.5001, 13], pattern='hour', enabled = True),\n", "                                    # dict(bounds=[15.001, 9.5], pattern='hour', enabled = True)]\n", "                )\n", "fig.update_yaxes(showspikes = True, spikethickness = 2)\n", "fig.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "toc-autonumbering": false}, "nbformat": 4, "nbformat_minor": 5}