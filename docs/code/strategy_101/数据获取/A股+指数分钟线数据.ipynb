{"cells": [{"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "0c19349e-95fa-4329-ae84-3198b630db30", "metadata": {"tags": []}, "source": ["1. 只需要替换股票名称，代码，时间，数量等直接运行即可其他不需要更改！需要存到本地，则自行存入\n", "2. 有问题扫二维码咨询，写的匆忙，也比较简单，有任何问题和需求都可以提出\n", "### ![image.png](attachment:image.png)"]}, {"cell_type": "markdown", "id": "0b7ff36d-cd63-4bb6-afd0-69500ad9fb94", "metadata": {}, "source": ["### 导入所需安装包，获取指定股票代码"]}, {"cell_type": "code", "execution_count": 9, "id": "00003df9-9f7f-4c67-9f49-62fc27a807ee", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'000001.XSHG': ('000001.XSHG', '上证指数', 'SZZS', datetime.datetime(1991, 7, 15, 0, 0), datetime.datetime(2200, 1, 1, 0, 0), 'index')}\n"]}], "source": ["# 导入所需包\n", "import omicron\n", "from coretypes import BarsArray, FrameType  \n", "import pandas as pd\n", "from omicron.models.stock import Stock\n", "from omicron.models.security import Security\n", "import datetime\n", "import cfg4py\n", "\n", "# 初始化配置，不可删！\n", "cfg = cfg4py.init('/etc/zillionare')\n", "await omicron.init()\n", "\n", "# 如果不知道股票具体代码，对股票/指数进行模糊匹配查找\n", "# 可以是股票/指数代码，也可以是字母（按name查找），也可以是汉字（按显示名查找）\n", "stock_info = Stock.fuzzy_match('上证指数')   # '平安银行', 板块个股均可，可改\n", "print(stock_info) # 字典形式，key为代码，value为股票具体信息，时间是股票上市时间和截止时间（截止时间不一定是真正截止时间）\n"]}, {"cell_type": "code", "execution_count": 10, "id": "d65ff679-1944-4884-9329-e11e639ca830", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["000001.XSHG\n"]}], "source": ["# 获取代码\n", "code = list(stock_info.keys())[0]\n", "print(code)"]}, {"cell_type": "markdown", "id": "a5f1f2f4-4ae1-4500-831b-19044665ff25", "metadata": {}, "source": ["### 获取指定条数股票/指数的分钟数据\n", "1. 获取到end为止的n个行情数据,返回的数据是按照时间顺序递增排序的。在遇到停牌的情况时，该时段数据将被跳过，因此返回的记录可能不是交易日连续的，并且可能不足n个。如果系统当前没有到指定时间end的数据，将尽最大努力返回数据。调用者可以通过判断最后一条数据的时间是否等于end来判断是否获取到了全部数据。"]}, {"cell_type": "code", "execution_count": 11, "id": "8301b2eb-f91e-40c7-8aff-c2b269e69e6a", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('2023-02-08T10:30:00', 3248.72, 3249.02, 3241.84, 3245.39, 3.27004680e+09, 4.11648267e+10, 1.)\n", " ('2023-02-08T11:00:00', 3245.56, 3249.72, 3244.36, 3247.68, 2.33303530e+09, 2.81525641e+10, 1.)\n", " ('2023-02-08T11:30:00', 3247.2 , 3249.8 , 3243.17, 3246.53, 1.79123500e+09, 2.32506131e+10, 1.)\n", " ('2023-02-08T13:30:00', 3246.53, 3247.68, 3237.92, 3239.64, 2.15465890e+09, 2.78285803e+10, 1.)\n", " ('2023-02-08T14:00:00', 3239.76, 3239.76, 3233.66, 3238.02, 2.00949690e+09, 2.48373813e+10, 1.)\n", " ('2023-02-08T14:30:00', 3237.8 , 3241.18, 3235.18, 3235.53, 1.99104000e+09, 2.52314931e+10, 1.)\n", " ('2023-02-08T15:00:00', 3236.09, 3237.11, 3230.44, 3232.11, 3.66864220e+09, 4.20302881e+10, 1.)\n", " ('2023-02-09T10:00:00', 3227.73, 3252.26, 3225.77, 3251.13, 6.31179960e+09, 8.54289404e+10, 1.)\n", " ('2023-02-09T10:30:00', 3251.45, 3260.32, 3251.39, 3259.9 , 3.56092120e+09, 4.94338481e+10, 1.)\n", " ('2023-02-09T11:00:00', 3259.82, 3259.82, 3252.42, 3254.66, 2.37763140e+09, 3.60254776e+10, 1.)\n", " ('2023-02-09T11:30:00', 3254.13, 3256.5 , 3250.5 , 3252.02, 1.85998100e+09, 2.61519289e+10, 1.)\n", " ('2023-02-09T13:30:00', 3252.02, 3264.65, 3252.02, 3264.35, 2.69676400e+09, 3.63542055e+10, 1.)\n", " ('2023-02-09T14:00:00', 3264.52, 3265.98, 3262.17, 3263.62, 2.42185720e+09, 3.32454065e+10, 1.)\n", " ('2023-02-09T14:30:00', 3263.62, 3268.89, 3263.2 , 3268.05, 2.53818990e+09, 3.61381010e+10, 1.)\n", " ('2023-02-09T15:00:00', 3268.48, 3270.38, 3267.43, 3270.38, 3.70830860e+09, 4.89829237e+10, 1.)\n", " ('2023-02-10T10:00:00', 3266.44, 3269.04, 3261.41, 3267.38, 6.81296080e+09, 9.44673988e+10, 1.)\n", " ('2023-02-10T10:30:00', 3267.84, 3269.99, 3264.05, 3266.47, 3.36426790e+09, 4.60135574e+10, 1.)\n", " ('2023-02-10T11:00:00', 3266.35, 3266.9 , 3260.79, 3264.83, 2.92619800e+09, 3.92147269e+10, 1.)\n", " ('2023-02-10T11:30:00', 3264.75, 3264.9 , 3249.73, 3250.85, 2.76898930e+09, 3.55548357e+10, 1.)\n", " ('2023-02-10T13:30:00', 3250.85, 3255.52, 3248.15, 3249.29, 2.52178870e+09, 3.24850686e+10, 1.)\n", " ('2023-02-10T14:00:00', 3249.29, 3257.37, 3247.23, 3257.37, 2.24894890e+09, 2.70350350e+10, 1.)\n", " ('2023-02-10T14:30:00', 3257.56, 3263.69, 3256.89, 3260.86, 2.19450240e+09, 2.75944839e+10, 1.)\n", " ('2023-02-10T15:00:00', 3261.25, 3262.71, 3258.01, 3260.67, 3.22503660e+09, 3.93847657e+10, 1.)\n", " ('2023-02-13T10:00:00', 3256.99, 3272.77, 3252.63, 3272.54, 7.85337230e+09, 1.04109249e+11, 1.)\n", " ('2023-02-13T10:30:00', 3272.98, 3273.2 , 3263.2 , 3268.79, 4.42602600e+09, 6.11609705e+10, 1.)\n", " ('2023-02-13T11:00:00', 3268.9 , 3280.21, 3268.78, 3280.21, 3.46903720e+09, 4.33447668e+10, 1.)\n", " ('2023-02-13T11:30:00', 3280.16, 3283.09, 3276.23, 3278.07, 2.81116030e+09, 3.68317393e+10, 1.)\n", " ('2023-02-13T13:30:00', 3278.07, 3282.98, 3276.08, 3281.09, 2.53543920e+09, 3.53099620e+10, 1.)\n", " ('2023-02-13T14:00:00', 3280.56, 3284.99, 3278.42, 3279.73, 2.49932190e+09, 3.42171578e+10, 1.)\n", " ('2023-02-13T14:30:00', 3280.1 , 3283.16, 3278.82, 3282.17, 2.37619120e+09, 3.37414313e+10, 1.)\n", " ('2023-02-13T15:00:00', 3282.24, 3285.09, 3281.72, 3284.16, 3.75077070e+09, 4.82961364e+10, 1.)\n", " ('2023-02-14T10:00:00', 3288.97, 3292.16, 3284.3 , 3289.74, 7.93402710e+09, 1.05618265e+11, 1.)\n", " ('2023-02-14T10:30:00', 3289.37, 3293.73, 3287.08, 3287.66, 4.03715550e+09, 5.27588529e+10, 1.)\n", " ('2023-02-14T11:00:00', 3288.05, 3288.63, 3280.64, 3283.73, 2.69473240e+09, 3.44773804e+10, 1.)\n", " ('2023-02-14T11:30:00', 3283.57, 3284.58, 3282.21, 3283.21, 1.96440760e+09, 2.56804034e+10, 1.)\n", " ('2023-02-14T13:30:00', 3283.21, 3286.41, 3279.29, 3280.43, 2.58147340e+09, 3.28971650e+10, 1.)\n", " ('2023-02-14T14:00:00', 3280.27, 3287.22, 3279.19, 3286.82, 2.12521670e+09, 2.88782249e+10, 1.)\n", " ('2023-02-14T14:30:00', 3287.05, 3289.44, 3284.34, 3288.15, 2.23406670e+09, 3.01938967e+10, 1.)\n", " ('2023-02-14T15:00:00', 3288.48, 3293.28, 3287.63, 3293.28, 3.52633740e+09, 4.50538640e+10, 1.)\n", " ('2023-02-15T10:00:00', 3294.02, 3296.2 , 3284.67, 3285.5 , 6.57907730e+09, 8.99731862e+10, 1.)\n", " ('2023-02-15T10:30:00', 3285.26, 3287.16, 3278.66, 3279.01, 3.92595200e+09, 5.15742478e+10, 1.)\n", " ('2023-02-15T11:00:00', 3278.73, 3281.85, 3274.77, 3281.39, 3.06826080e+09, 3.90181730e+10, 1.)\n", " ('2023-02-15T11:30:00', 3281.31, 3284.43, 3280.36, 3282.61, 2.14626320e+09, 2.91858983e+10, 1.)\n", " ('2023-02-15T13:30:00', 3282.61, 3285.75, 3275.55, 3278.45, 3.06912730e+09, 3.96916382e+10, 1.)\n", " ('2023-02-15T14:00:00', 3278.45, 3282.55, 3277.92, 3279.98, 2.34166590e+09, 3.26285869e+10, 1.)\n", " ('2023-02-15T14:30:00', 3279.89, 3281.05, 3274.55, 3278.47, 2.45448670e+09, 3.20447579e+10, 1.)\n", " ('2023-02-15T15:00:00', 3278.26, 3281.22, 3277.58, 3280.49, 3.40907910e+09, 4.35053278e+10, 1.)\n", " ('2023-02-16T10:00:00', 3281.74, 3295.03, 3278.64, 3293.96, 7.77710760e+09, 9.82311739e+10, 1.)\n", " ('2023-02-16T10:30:00', 3294.7 , 3305.88, 3290.96, 3305.66, 4.60896020e+09, 5.83290276e+10, 1.)\n", " ('2023-02-16T11:00:00', 3305.57, 3308.52, 3302.17, 3305.  , 3.20699590e+09, 4.22978711e+10, 1.)\n", " ('2023-02-16T11:30:00', 3304.95, 3308.3 , 3303.22, 3305.85, 2.26218650e+09, 3.05790563e+10, 1.)\n", " ('2023-02-16T13:30:00', 3305.85, 3308.83, 3305.33, 3305.91, 2.72392660e+09, 3.50875573e+10, 1.)\n", " ('2023-02-16T14:00:00', 3305.81, 3306.09, 3293.22, 3293.65, 3.05306410e+09, 3.86930174e+10, 1.)\n", " ('2023-02-16T14:30:00', 3293.75, 3293.96, 3252.55, 3252.64, 7.04114210e+09, 7.91666976e+10, 1.)\n", " ('2023-02-16T15:00:00', 3252.35, 3255.72, 3231.34, 3249.03, 7.41297150e+09, 8.59981110e+10, 1.)\n", " ('2023-02-17T10:00:00', 3244.73, 3262.47, 3244.73, 3260.94, 8.04946570e+09, 1.00476945e+11, 1.)\n", " ('2023-02-17T10:30:00', 3261.2 , 3261.2 , 3243.98, 3256.37, 3.92012000e+09, 5.14674154e+10, 1.)\n", " ('2023-02-17T11:00:00', 3256.46, 3258.93, 3252.11, 3255.15, 2.32117870e+09, 3.05409171e+10, 1.)\n", " ('2023-02-17T11:30:00', 3255.19, 3256.75, 3243.54, 3243.82, 2.03624620e+09, 2.61093366e+10, 1.)\n", " ('2023-02-17T13:30:00', 3243.82, 3246.92, 3235.35, 3243.48, 2.82312440e+09, 3.60350678e+10, 1.)\n", " ('2023-02-17T14:00:00', 3243.13, 3244.81, 3237.87, 3243.39, 2.18274930e+09, 2.76993584e+10, 1.)\n", " ('2023-02-17T14:30:00', 3243.41, 3248.21, 3230.1 , 3231.18, 2.68831260e+09, 3.50119144e+10, 1.)\n", " ('2023-02-17T15:00:00', 3231.1 , 3235.21, 3223.26, 3224.02, 3.82392680e+09, 4.82848874e+10, 1.)\n", " ('2023-02-20T10:00:00', 3230.47, 3244.93, 3225.84, 3243.06, 8.88780980e+09, 1.05285110e+11, 1.)\n", " ('2023-02-20T10:30:00', 3243.05, 3250.56, 3237.57, 3249.37, 4.26388020e+09, 5.09016594e+10, 1.)\n", " ('2023-02-20T11:00:00', 3248.67, 3252.2 , 3242.95, 3251.6 , 3.05709460e+09, 3.76517901e+10, 1.)\n", " ('2023-02-20T11:30:00', 3251.46, 3256.07, 3249.76, 3255.8 , 2.39881940e+09, 3.03834565e+10, 1.)\n", " ('2023-02-20T13:30:00', 3255.8 , 3266.16, 3254.47, 3265.82, 2.99169610e+09, 3.67091244e+10, 1.)\n", " ('2023-02-20T14:00:00', 3265.86, 3277.  , 3265.21, 3274.78, 3.26948750e+09, 4.13336175e+10, 1.)\n", " ('2023-02-20T14:30:00', 3274.87, 3289.46, 3274.02, 3287.48, 3.50294930e+09, 4.46860348e+10, 1.)\n", " ('2023-02-20T15:00:00', 3287.21, 3291.04, 3286.47, 3290.34, 4.12903640e+09, 5.07695226e+10, 1.)\n", " ('2023-02-21T10:00:00', 3291.63, 3304.48, 3287.68, 3299.42, 1.01282845e+10, 1.15189211e+11, 1.)\n", " ('2023-02-21T10:30:00', 3299.49, 3307.24, 3297.64, 3305.58, 4.77814000e+09, 5.80313260e+10, 1.)\n", " ('2023-02-21T11:00:00', 3305.4 , 3308.79, 3301.2 , 3306.14, 3.30349170e+09, 4.39707481e+10, 1.)\n", " ('2023-02-21T11:30:00', 3305.99, 3306.66, 3282.44, 3293.71, 3.86248020e+09, 4.52075873e+10, 1.)\n", " ('2023-02-21T13:30:00', 3293.71, 3298.86, 3291.88, 3293.61, 2.14428460e+09, 2.77869512e+10, 1.)\n", " ('2023-02-21T14:00:00', 3294.01, 3297.18, 3292.1 , 3293.69, 2.06357420e+09, 2.64133561e+10, 1.)\n", " ('2023-02-21T14:30:00', 3294.04, 3296.19, 3286.7 , 3295.67, 2.25744190e+09, 2.95193171e+10, 1.)\n", " ('2023-02-21T15:00:00', 3295.58, 3306.52, 3294.71, 3306.52, 3.90324780e+09, 4.45727751e+10, 1.)\n", " ('2023-02-22T10:00:00', 3292.05, 3298.79, 3288.59, 3291.92, 7.59342760e+09, 9.02824106e+10, 1.)\n", " ('2023-02-22T10:30:00', 3292.19, 3298.79, 3291.87, 3296.9 , 3.47887850e+09, 4.41016389e+10, 1.)\n", " ('2023-02-22T11:00:00', 3296.81, 3307.03, 3296.72, 3301.48, 2.61686090e+09, 3.32254406e+10, 1.)\n", " ('2023-02-22T11:30:00', 3301.59, 3301.59, 3297.61, 3298.13, 1.98231130e+09, 2.59930993e+10, 1.)\n", " ('2023-02-22T13:30:00', 3298.13, 3300.29, 3291.78, 3296.71, 2.43511450e+09, 3.37948210e+10, 1.)\n", " ('2023-02-22T14:00:00', 3296.68, 3296.78, 3289.2 , 3291.04, 1.98860780e+09, 2.56339036e+10, 1.)\n", " ('2023-02-22T14:30:00', 3291.05, 3293.85, 3286.72, 3290.9 , 2.17079860e+09, 2.76622428e+10, 1.)\n", " ('2023-02-22T15:00:00', 3291.26, 3292.53, 3287.44, 3291.15, 3.27439930e+09, 3.98220632e+10, 1.)\n", " ('2023-02-23T10:00:00', 3293.52, 3307.44, 3293.52, 3299.12, 7.60737870e+09, 9.35422768e+10, 1.)\n", " ('2023-02-23T10:30:00', 3299.33, 3301.02, 3286.44, 3290.27, 3.68602770e+09, 4.75850683e+10, 1.)\n", " ('2023-02-23T11:00:00', 3290.14, 3298.28, 3288.92, 3296.44, 2.40556880e+09, 3.06086422e+10, 1.)\n", " ('2023-02-23T11:30:00', 3296.41, 3302.56, 3292.63, 3293.37, 2.17867460e+09, 3.04795231e+10, 1.)\n", " ('2023-02-23T13:30:00', 3293.37, 3294.27, 3285.88, 3286.09, 2.39963240e+09, 3.23663610e+10, 1.)\n", " ('2023-02-23T14:00:00', 3286.1 , 3286.9 , 3275.36, 3278.75, 2.80272720e+09, 3.48504991e+10, 1.)\n", " ('2023-02-23T14:30:00', 3278.58, 3284.14, 3278.36, 3280.16, 2.11424590e+09, 2.68821870e+10, 1.)\n", " ('2023-02-23T15:00:00', 3280.52, 3287.86, 3280.3 , 3287.48, 3.03817510e+09, 3.80499769e+10, 1.)\n", " ('2023-02-24T10:00:00', 3287.26, 3288.39, 3276.57, 3280.81, 5.94276970e+09, 8.34144122e+10, 1.)\n", " ('2023-02-24T10:30:00', 3280.53, 3283.97, 3270.05, 3270.33, 3.19113420e+09, 4.40718345e+10, 1.)\n", " ('2023-02-24T11:00:00', 3270.07, 3270.2 , 3258.96, 3259.58, 3.08980300e+09, 3.75488486e+10, 1.)\n", " ('2023-02-24T11:30:00', 3259.67, 3266.12, 3253.97, 3264.58, 2.31387880e+09, 2.67955691e+10, 1.)\n", " ('2023-02-24T13:30:00', 3264.58, 3271.17, 3260.67, 3268.71, 2.08012780e+09, 2.82787546e+10, 1.)\n", " ('2023-02-24T14:00:00', 3268.88, 3269.88, 3264.24, 3266.84, 1.77859440e+09, 2.37751795e+10, 1.)\n", " ('2023-02-24T14:30:00', 3267.18, 3269.55, 3261.47, 3269.55, 1.91741190e+09, 2.54190570e+10, 1.)\n", " ('2023-02-24T15:00:00', 3269.27, 3273.88, 3267.16, 3267.16, 3.10691080e+09, 3.87006922e+10, 1.)\n", " ('2023-02-27T10:00:00', 3257.  , 3273.34, 3253.2 , 3267.48, 7.06533330e+09, 9.56004804e+10, 1.)\n", " ('2023-02-27T10:30:00', 3267.74, 3270.63, 3264.01, 3266.99, 3.46086370e+09, 4.67492847e+10, 1.)\n", " ('2023-02-27T11:00:00', 3267.08, 3276.17, 3267.01, 3275.99, 2.23108010e+09, 3.22757905e+10, 1.)\n", " ('2023-02-27T11:30:00', 3275.99, 3276.58, 3260.88, 3263.38, 2.24094820e+09, 2.85420814e+10, 1.)\n", " ('2023-02-27T13:30:00', 3263.38, 3270.77, 3257.72, 3257.72, 2.25290340e+09, 2.96448870e+10, 1.)\n", " ('2023-02-27T14:00:00', 3257.71, 3257.89, 3251.72, 3256.12, 2.18135630e+09, 2.89001947e+10, 1.)\n", " ('2023-02-27T14:30:00', 3256.3 , 3260.1 , 3256.3 , 3257.28, 1.73477100e+09, 2.38763683e+10, 1.)\n", " ('2023-02-27T15:00:00', 3257.28, 3258.77, 3254.5 , 3258.03, 3.11827840e+09, 3.90160039e+10, 1.)\n", " ('2023-02-28T10:00:00', 3265.74, 3272.68, 3265.74, 3266.6 , 6.45915390e+09, 8.58695173e+10, 1.)\n", " ('2023-02-28T10:30:00', 3267.2 , 3267.2 , 3260.48, 3262.77, 3.28431770e+09, 4.31610149e+10, 1.)\n", " ('2023-02-28T11:00:00', 3262.39, 3268.32, 3260.3 , 3264.61, 2.25688930e+09, 2.90817416e+10, 1.)\n", " ('2023-02-28T11:30:00', 3264.4 , 3264.64, 3259.24, 3260.4 , 1.74872120e+09, 2.20912846e+10, 1.)\n", " ('2023-02-28T13:30:00', 3260.35, 3261.91, 3249.56, 3251.  , 2.25692440e+09, 2.86329531e+10, 1.)\n", " ('2023-02-28T14:00:00', 3250.82, 3258.36, 3246.14, 3255.31, 2.18440490e+09, 2.64594716e+10, 1.)\n", " ('2023-02-28T14:30:00', 3255.64, 3268.23, 3254.79, 3268.04, 2.44241650e+09, 2.85697834e+10, 1.)\n", " ('2023-02-28T15:00:00', 3269.14, 3280.42, 3266.32, 3279.61, 4.52967530e+09, 5.41643708e+10, 1.)\n", " ('2023-03-01T10:00:00', 3279.14, 3292.6 , 3272.04, 3292.6 , 8.12714200e+09, 9.16288056e+10, 1.)]\n"]}], "source": ["#时间单位 FrameType.MIN30是30分钟线，FrameType.DAY是日线，以下是可供选择的时间单位\n", "#分钟级别：\n", "# FrameType.MIN1,\n", "# FrameType.MIN5,\n", "# FrameType.MIN15,\n", "# FrameType.MIN30,\n", "# FrameType.MIN60,\n", "\n", "n = 120   \n", "end = datetime.datetime(2023, 3, 1, 10, 0)  # 不要取交易时间之外的时间， 可改其他时间\n", "bars = await Stock.get_bars(code, n, FrameType.MIN30, end, fq=True, unclosed=True)  # 如果当天未收盘，最后一行数据是当前股票实时价格 \n", "# fq是否对返回记录进行复权。如果为True的话，则进行前复权。默认True\n", "# unclosed 是否包含最新未收盘的数据？ 默认True.\n", "\n", "print(bars) # 返回数据为ndarray形式，没有返回dataframe形式，为了提高后续计算速度\n"]}, {"cell_type": "markdown", "id": "8c76f6fa-b456-456e-a031-cd01fe720fb4", "metadata": {}, "source": ["#### 行情数据转为dataframe形式，如不需要，则略过"]}, {"cell_type": "code", "execution_count": 12, "id": "cdac4adf-ef4d-4475-b154-8195688dc6dc", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>factor</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-02-08 10:30:00</td>\n", "      <td>3248.719971</td>\n", "      <td>3249.020020</td>\n", "      <td>3241.840088</td>\n", "      <td>3245.389893</td>\n", "      <td>3.270047e+09</td>\n", "      <td>4.116483e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-02-08 11:00:00</td>\n", "      <td>3245.560059</td>\n", "      <td>3249.719971</td>\n", "      <td>3244.360107</td>\n", "      <td>3247.679932</td>\n", "      <td>2.333035e+09</td>\n", "      <td>2.815256e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-02-08 11:30:00</td>\n", "      <td>3247.199951</td>\n", "      <td>3249.800049</td>\n", "      <td>3243.169922</td>\n", "      <td>3246.530029</td>\n", "      <td>1.791235e+09</td>\n", "      <td>2.325061e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-02-08 13:30:00</td>\n", "      <td>3246.530029</td>\n", "      <td>3247.679932</td>\n", "      <td>3237.919922</td>\n", "      <td>3239.639893</td>\n", "      <td>2.154659e+09</td>\n", "      <td>2.782858e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-02-08 14:00:00</td>\n", "      <td>3239.760010</td>\n", "      <td>3239.760010</td>\n", "      <td>3233.659912</td>\n", "      <td>3238.020020</td>\n", "      <td>2.009497e+09</td>\n", "      <td>2.483738e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>2023-02-28 13:30:00</td>\n", "      <td>3260.350098</td>\n", "      <td>3261.909912</td>\n", "      <td>3249.560059</td>\n", "      <td>3251.000000</td>\n", "      <td>2.256924e+09</td>\n", "      <td>2.863295e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116</th>\n", "      <td>2023-02-28 14:00:00</td>\n", "      <td>3250.820068</td>\n", "      <td>3258.360107</td>\n", "      <td>3246.139893</td>\n", "      <td>3255.310059</td>\n", "      <td>2.184405e+09</td>\n", "      <td>2.645947e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>2023-02-28 14:30:00</td>\n", "      <td>3255.639893</td>\n", "      <td>3268.229980</td>\n", "      <td>3254.790039</td>\n", "      <td>3268.040039</td>\n", "      <td>2.442416e+09</td>\n", "      <td>2.856978e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>2023-02-28 15:00:00</td>\n", "      <td>3269.139893</td>\n", "      <td>3280.419922</td>\n", "      <td>3266.320068</td>\n", "      <td>3279.610107</td>\n", "      <td>4.529675e+09</td>\n", "      <td>5.416437e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>119</th>\n", "      <td>2023-03-01 10:00:00</td>\n", "      <td>3279.139893</td>\n", "      <td>3292.600098</td>\n", "      <td>3272.040039</td>\n", "      <td>3292.600098</td>\n", "      <td>8.127142e+09</td>\n", "      <td>9.162881e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>120 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                  frame         open         high          low        close  \\\n", "0   2023-02-08 10:30:00  3248.719971  3249.020020  3241.840088  3245.389893   \n", "1   2023-02-08 11:00:00  3245.560059  3249.719971  3244.360107  3247.679932   \n", "2   2023-02-08 11:30:00  3247.199951  3249.800049  3243.169922  3246.530029   \n", "3   2023-02-08 13:30:00  3246.530029  3247.679932  3237.919922  3239.639893   \n", "4   2023-02-08 14:00:00  3239.760010  3239.760010  3233.659912  3238.020020   \n", "..                  ...          ...          ...          ...          ...   \n", "115 2023-02-28 13:30:00  3260.350098  3261.909912  3249.560059  3251.000000   \n", "116 2023-02-28 14:00:00  3250.820068  3258.360107  3246.139893  3255.310059   \n", "117 2023-02-28 14:30:00  3255.639893  3268.229980  3254.790039  3268.040039   \n", "118 2023-02-28 15:00:00  3269.139893  3280.419922  3266.320068  3279.610107   \n", "119 2023-03-01 10:00:00  3279.139893  3292.600098  3272.040039  3292.600098   \n", "\n", "           volume        amount  factor  \n", "0    3.270047e+09  4.116483e+10     1.0  \n", "1    2.333035e+09  2.815256e+10     1.0  \n", "2    1.791235e+09  2.325061e+10     1.0  \n", "3    2.154659e+09  2.782858e+10     1.0  \n", "4    2.009497e+09  2.483738e+10     1.0  \n", "..            ...           ...     ...  \n", "115  2.256924e+09  2.863295e+10     1.0  \n", "116  2.184405e+09  2.645947e+10     1.0  \n", "117  2.442416e+09  2.856978e+10     1.0  \n", "118  4.529675e+09  5.416437e+10     1.0  \n", "119  8.127142e+09  9.162881e+10     1.0  \n", "\n", "[120 rows x 8 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["bars_df = pd.DataFrame(bars)\n", "bars_df"]}, {"cell_type": "markdown", "id": "442a61f6-e027-4c95-99f8-b713d20a4950", "metadata": {}, "source": ["### 获取指定时间范围的股票/指数的分钟线数据"]}, {"cell_type": "code", "execution_count": 13, "id": "9ad6c65b-5a37-4c23-87a8-3c5b41d94a74", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["array([('2023-02-09T10:00:00', 3227.73, 3252.26, 3225.77, 3251.13, 6.31179960e+09, 8.54289404e+10, 1.),\n", "       ('2023-02-09T10:30:00', 3251.45, 3260.32, 3251.39, 3259.9 , 3.56092120e+09, 4.94338481e+10, 1.),\n", "       ('2023-02-09T11:00:00', 3259.82, 3259.82, 3252.42, 3254.66, 2.37763140e+09, 3.60254776e+10, 1.),\n", "       ('2023-02-09T11:30:00', 3254.13, 3256.5 , 3250.5 , 3252.02, 1.85998100e+09, 2.61519289e+10, 1.),\n", "       ('2023-02-09T13:30:00', 3252.02, 3264.65, 3252.02, 3264.35, 2.69676400e+09, 3.63542055e+10, 1.),\n", "       ('2023-02-09T14:00:00', 3264.52, 3265.98, 3262.17, 3263.62, 2.42185720e+09, 3.32454065e+10, 1.),\n", "       ('2023-02-09T14:30:00', 3263.62, 3268.89, 3263.2 , 3268.05, 2.53818990e+09, 3.61381010e+10, 1.),\n", "       ('2023-02-09T15:00:00', 3268.48, 3270.38, 3267.43, 3270.38, 3.70830860e+09, 4.89829237e+10, 1.),\n", "       ('2023-02-10T10:00:00', 3266.44, 3269.04, 3261.41, 3267.38, 6.81296080e+09, 9.44673988e+10, 1.),\n", "       ('2023-02-10T10:30:00', 3267.84, 3269.99, 3264.05, 3266.47, 3.36426790e+09, 4.60135574e+10, 1.),\n", "       ('2023-02-10T11:00:00', 3266.35, 3266.9 , 3260.79, 3264.83, 2.92619800e+09, 3.92147269e+10, 1.),\n", "       ('2023-02-10T11:30:00', 3264.75, 3264.9 , 3249.73, 3250.85, 2.76898930e+09, 3.55548357e+10, 1.),\n", "       ('2023-02-10T13:30:00', 3250.85, 3255.52, 3248.15, 3249.29, 2.52178870e+09, 3.24850686e+10, 1.),\n", "       ('2023-02-10T14:00:00', 3249.29, 3257.37, 3247.23, 3257.37, 2.24894890e+09, 2.70350350e+10, 1.),\n", "       ('2023-02-10T14:30:00', 3257.56, 3263.69, 3256.89, 3260.86, 2.19450240e+09, 2.75944839e+10, 1.),\n", "       ('2023-02-10T15:00:00', 3261.25, 3262.71, 3258.01, 3260.67, 3.22503660e+09, 3.93847657e+10, 1.),\n", "       ('2023-02-13T10:00:00', 3256.99, 3272.77, 3252.63, 3272.54, 7.85337230e+09, 1.04109249e+11, 1.),\n", "       ('2023-02-13T10:30:00', 3272.98, 3273.2 , 3263.2 , 3268.79, 4.42602600e+09, 6.11609705e+10, 1.),\n", "       ('2023-02-13T11:00:00', 3268.9 , 3280.21, 3268.78, 3280.21, 3.46903720e+09, 4.33447668e+10, 1.),\n", "       ('2023-02-13T11:30:00', 3280.16, 3283.09, 3276.23, 3278.07, 2.81116030e+09, 3.68317393e+10, 1.),\n", "       ('2023-02-13T13:30:00', 3278.07, 3282.98, 3276.08, 3281.09, 2.53543920e+09, 3.53099620e+10, 1.),\n", "       ('2023-02-13T14:00:00', 3280.56, 3284.99, 3278.42, 3279.73, 2.49932190e+09, 3.42171578e+10, 1.),\n", "       ('2023-02-13T14:30:00', 3280.1 , 3283.16, 3278.82, 3282.17, 2.37619120e+09, 3.37414313e+10, 1.),\n", "       ('2023-02-13T15:00:00', 3282.24, 3285.09, 3281.72, 3284.16, 3.75077070e+09, 4.82961364e+10, 1.),\n", "       ('2023-02-14T10:00:00', 3288.97, 3292.16, 3284.3 , 3289.74, 7.93402710e+09, 1.05618265e+11, 1.),\n", "       ('2023-02-14T10:30:00', 3289.37, 3293.73, 3287.08, 3287.66, 4.03715550e+09, 5.27588529e+10, 1.),\n", "       ('2023-02-14T11:00:00', 3288.05, 3288.63, 3280.64, 3283.73, 2.69473240e+09, 3.44773804e+10, 1.),\n", "       ('2023-02-14T11:30:00', 3283.57, 3284.58, 3282.21, 3283.21, 1.96440760e+09, 2.56804034e+10, 1.),\n", "       ('2023-02-14T13:30:00', 3283.21, 3286.41, 3279.29, 3280.43, 2.58147340e+09, 3.28971650e+10, 1.),\n", "       ('2023-02-14T14:00:00', 3280.27, 3287.22, 3279.19, 3286.82, 2.12521670e+09, 2.88782249e+10, 1.),\n", "       ('2023-02-14T14:30:00', 3287.05, 3289.44, 3284.34, 3288.15, 2.23406670e+09, 3.01938967e+10, 1.),\n", "       ('2023-02-14T15:00:00', 3288.48, 3293.28, 3287.63, 3293.28, 3.52633740e+09, 4.50538640e+10, 1.),\n", "       ('2023-02-15T10:00:00', 3294.02, 3296.2 , 3284.67, 3285.5 , 6.57907730e+09, 8.99731862e+10, 1.),\n", "       ('2023-02-15T10:30:00', 3285.26, 3287.16, 3278.66, 3279.01, 3.92595200e+09, 5.15742478e+10, 1.),\n", "       ('2023-02-15T11:00:00', 3278.73, 3281.85, 3274.77, 3281.39, 3.06826080e+09, 3.90181730e+10, 1.),\n", "       ('2023-02-15T11:30:00', 3281.31, 3284.43, 3280.36, 3282.61, 2.14626320e+09, 2.91858983e+10, 1.),\n", "       ('2023-02-15T13:30:00', 3282.61, 3285.75, 3275.55, 3278.45, 3.06912730e+09, 3.96916382e+10, 1.),\n", "       ('2023-02-15T14:00:00', 3278.45, 3282.55, 3277.92, 3279.98, 2.34166590e+09, 3.26285869e+10, 1.),\n", "       ('2023-02-15T14:30:00', 3279.89, 3281.05, 3274.55, 3278.47, 2.45448670e+09, 3.20447579e+10, 1.),\n", "       ('2023-02-15T15:00:00', 3278.26, 3281.22, 3277.58, 3280.49, 3.40907910e+09, 4.35053278e+10, 1.),\n", "       ('2023-02-16T10:00:00', 3281.74, 3295.03, 3278.64, 3293.96, 7.77710760e+09, 9.82311739e+10, 1.),\n", "       ('2023-02-16T10:30:00', 3294.7 , 3305.88, 3290.96, 3305.66, 4.60896020e+09, 5.83290276e+10, 1.),\n", "       ('2023-02-16T11:00:00', 3305.57, 3308.52, 3302.17, 3305.  , 3.20699590e+09, 4.22978711e+10, 1.),\n", "       ('2023-02-16T11:30:00', 3304.95, 3308.3 , 3303.22, 3305.85, 2.26218650e+09, 3.05790563e+10, 1.),\n", "       ('2023-02-16T13:30:00', 3305.85, 3308.83, 3305.33, 3305.91, 2.72392660e+09, 3.50875573e+10, 1.),\n", "       ('2023-02-16T14:00:00', 3305.81, 3306.09, 3293.22, 3293.65, 3.05306410e+09, 3.86930174e+10, 1.),\n", "       ('2023-02-16T14:30:00', 3293.75, 3293.96, 3252.55, 3252.64, 7.04114210e+09, 7.91666976e+10, 1.),\n", "       ('2023-02-16T15:00:00', 3252.35, 3255.72, 3231.34, 3249.03, 7.41297150e+09, 8.59981110e+10, 1.),\n", "       ('2023-02-17T10:00:00', 3244.73, 3262.47, 3244.73, 3260.94, 8.04946570e+09, 1.00476945e+11, 1.),\n", "       ('2023-02-17T10:30:00', 3261.2 , 3261.2 , 3243.98, 3256.37, 3.92012000e+09, 5.14674154e+10, 1.),\n", "       ('2023-02-17T11:00:00', 3256.46, 3258.93, 3252.11, 3255.15, 2.32117870e+09, 3.05409171e+10, 1.),\n", "       ('2023-02-17T11:30:00', 3255.19, 3256.75, 3243.54, 3243.82, 2.03624620e+09, 2.61093366e+10, 1.),\n", "       ('2023-02-17T13:30:00', 3243.82, 3246.92, 3235.35, 3243.48, 2.82312440e+09, 3.60350678e+10, 1.),\n", "       ('2023-02-17T14:00:00', 3243.13, 3244.81, 3237.87, 3243.39, 2.18274930e+09, 2.76993584e+10, 1.),\n", "       ('2023-02-17T14:30:00', 3243.41, 3248.21, 3230.1 , 3231.18, 2.68831260e+09, 3.50119144e+10, 1.),\n", "       ('2023-02-17T15:00:00', 3231.1 , 3235.21, 3223.26, 3224.02, 3.82392680e+09, 4.82848874e+10, 1.),\n", "       ('2023-02-20T10:00:00', 3230.47, 3244.93, 3225.84, 3243.06, 8.88780980e+09, 1.05285110e+11, 1.),\n", "       ('2023-02-20T10:30:00', 3243.05, 3250.56, 3237.57, 3249.37, 4.26388020e+09, 5.09016594e+10, 1.),\n", "       ('2023-02-20T11:00:00', 3248.67, 3252.2 , 3242.95, 3251.6 , 3.05709460e+09, 3.76517901e+10, 1.),\n", "       ('2023-02-20T11:30:00', 3251.46, 3256.07, 3249.76, 3255.8 , 2.39881940e+09, 3.03834565e+10, 1.),\n", "       ('2023-02-20T13:30:00', 3255.8 , 3266.16, 3254.47, 3265.82, 2.99169610e+09, 3.67091244e+10, 1.),\n", "       ('2023-02-20T14:00:00', 3265.86, 3277.  , 3265.21, 3274.78, 3.26948750e+09, 4.13336175e+10, 1.),\n", "       ('2023-02-20T14:30:00', 3274.87, 3289.46, 3274.02, 3287.48, 3.50294930e+09, 4.46860348e+10, 1.),\n", "       ('2023-02-20T15:00:00', 3287.21, 3291.04, 3286.47, 3290.34, 4.12903640e+09, 5.07695226e+10, 1.),\n", "       ('2023-02-21T10:00:00', 3291.63, 3304.48, 3287.68, 3299.42, 1.01282845e+10, 1.15189211e+11, 1.),\n", "       ('2023-02-21T10:30:00', 3299.49, 3307.24, 3297.64, 3305.58, 4.77814000e+09, 5.80313260e+10, 1.),\n", "       ('2023-02-21T11:00:00', 3305.4 , 3308.79, 3301.2 , 3306.14, 3.30349170e+09, 4.39707481e+10, 1.),\n", "       ('2023-02-21T11:30:00', 3305.99, 3306.66, 3282.44, 3293.71, 3.86248020e+09, 4.52075873e+10, 1.),\n", "       ('2023-02-21T13:30:00', 3293.71, 3298.86, 3291.88, 3293.61, 2.14428460e+09, 2.77869512e+10, 1.),\n", "       ('2023-02-21T14:00:00', 3294.01, 3297.18, 3292.1 , 3293.69, 2.06357420e+09, 2.64133561e+10, 1.),\n", "       ('2023-02-21T14:30:00', 3294.04, 3296.19, 3286.7 , 3295.67, 2.25744190e+09, 2.95193171e+10, 1.),\n", "       ('2023-02-21T15:00:00', 3295.58, 3306.52, 3294.71, 3306.52, 3.90324780e+09, 4.45727751e+10, 1.),\n", "       ('2023-02-22T10:00:00', 3292.05, 3298.79, 3288.59, 3291.92, 7.59342760e+09, 9.02824106e+10, 1.),\n", "       ('2023-02-22T10:30:00', 3292.19, 3298.79, 3291.87, 3296.9 , 3.47887850e+09, 4.41016389e+10, 1.),\n", "       ('2023-02-22T11:00:00', 3296.81, 3307.03, 3296.72, 3301.48, 2.61686090e+09, 3.32254406e+10, 1.),\n", "       ('2023-02-22T11:30:00', 3301.59, 3301.59, 3297.61, 3298.13, 1.98231130e+09, 2.59930993e+10, 1.),\n", "       ('2023-02-22T13:30:00', 3298.13, 3300.29, 3291.78, 3296.71, 2.43511450e+09, 3.37948210e+10, 1.),\n", "       ('2023-02-22T14:00:00', 3296.68, 3296.78, 3289.2 , 3291.04, 1.98860780e+09, 2.56339036e+10, 1.),\n", "       ('2023-02-22T14:30:00', 3291.05, 3293.85, 3286.72, 3290.9 , 2.17079860e+09, 2.76622428e+10, 1.),\n", "       ('2023-02-22T15:00:00', 3291.26, 3292.53, 3287.44, 3291.15, 3.27439930e+09, 3.98220632e+10, 1.),\n", "       ('2023-02-23T10:00:00', 3293.52, 3307.44, 3293.52, 3299.12, 7.60737870e+09, 9.35422768e+10, 1.),\n", "       ('2023-02-23T10:30:00', 3299.33, 3301.02, 3286.44, 3290.27, 3.68602770e+09, 4.75850683e+10, 1.),\n", "       ('2023-02-23T11:00:00', 3290.14, 3298.28, 3288.92, 3296.44, 2.40556880e+09, 3.06086422e+10, 1.),\n", "       ('2023-02-23T11:30:00', 3296.41, 3302.56, 3292.63, 3293.37, 2.17867460e+09, 3.04795231e+10, 1.),\n", "       ('2023-02-23T13:30:00', 3293.37, 3294.27, 3285.88, 3286.09, 2.39963240e+09, 3.23663610e+10, 1.),\n", "       ('2023-02-23T14:00:00', 3286.1 , 3286.9 , 3275.36, 3278.75, 2.80272720e+09, 3.48504991e+10, 1.),\n", "       ('2023-02-23T14:30:00', 3278.58, 3284.14, 3278.36, 3280.16, 2.11424590e+09, 2.68821870e+10, 1.),\n", "       ('2023-02-23T15:00:00', 3280.52, 3287.86, 3280.3 , 3287.48, 3.03817510e+09, 3.80499769e+10, 1.),\n", "       ('2023-02-24T10:00:00', 3287.26, 3288.39, 3276.57, 3280.81, 5.94276970e+09, 8.34144122e+10, 1.),\n", "       ('2023-02-24T10:30:00', 3280.53, 3283.97, 3270.05, 3270.33, 3.19113420e+09, 4.40718345e+10, 1.),\n", "       ('2023-02-24T11:00:00', 3270.07, 3270.2 , 3258.96, 3259.58, 3.08980300e+09, 3.75488486e+10, 1.),\n", "       ('2023-02-24T11:30:00', 3259.67, 3266.12, 3253.97, 3264.58, 2.31387880e+09, 2.67955691e+10, 1.),\n", "       ('2023-02-24T13:30:00', 3264.58, 3271.17, 3260.67, 3268.71, 2.08012780e+09, 2.82787546e+10, 1.),\n", "       ('2023-02-24T14:00:00', 3268.88, 3269.88, 3264.24, 3266.84, 1.77859440e+09, 2.37751795e+10, 1.),\n", "       ('2023-02-24T14:30:00', 3267.18, 3269.55, 3261.47, 3269.55, 1.91741190e+09, 2.54190570e+10, 1.),\n", "       ('2023-02-24T15:00:00', 3269.27, 3273.88, 3267.16, 3267.16, 3.10691080e+09, 3.87006922e+10, 1.),\n", "       ('2023-02-27T10:00:00', 3257.  , 3273.34, 3253.2 , 3267.48, 7.06533330e+09, 9.56004804e+10, 1.),\n", "       ('2023-02-27T10:30:00', 3267.74, 3270.63, 3264.01, 3266.99, 3.46086370e+09, 4.67492847e+10, 1.),\n", "       ('2023-02-27T11:00:00', 3267.08, 3276.17, 3267.01, 3275.99, 2.23108010e+09, 3.22757905e+10, 1.),\n", "       ('2023-02-27T11:30:00', 3275.99, 3276.58, 3260.88, 3263.38, 2.24094820e+09, 2.85420814e+10, 1.),\n", "       ('2023-02-27T13:30:00', 3263.38, 3270.77, 3257.72, 3257.72, 2.25290340e+09, 2.96448870e+10, 1.),\n", "       ('2023-02-27T14:00:00', 3257.71, 3257.89, 3251.72, 3256.12, 2.18135630e+09, 2.89001947e+10, 1.),\n", "       ('2023-02-27T14:30:00', 3256.3 , 3260.1 , 3256.3 , 3257.28, 1.73477100e+09, 2.38763683e+10, 1.),\n", "       ('2023-02-27T15:00:00', 3257.28, 3258.77, 3254.5 , 3258.03, 3.11827840e+09, 3.90160039e+10, 1.),\n", "       ('2023-02-28T10:00:00', 3265.74, 3272.68, 3265.74, 3266.6 , 6.45915390e+09, 8.58695173e+10, 1.),\n", "       ('2023-02-28T10:30:00', 3267.2 , 3267.2 , 3260.48, 3262.77, 3.28431770e+09, 4.31610149e+10, 1.),\n", "       ('2023-02-28T11:00:00', 3262.39, 3268.32, 3260.3 , 3264.61, 2.25688930e+09, 2.90817416e+10, 1.),\n", "       ('2023-02-28T11:30:00', 3264.4 , 3264.64, 3259.24, 3260.4 , 1.74872120e+09, 2.20912846e+10, 1.),\n", "       ('2023-02-28T13:30:00', 3260.35, 3261.91, 3249.56, 3251.  , 2.25692440e+09, 2.86329531e+10, 1.),\n", "       ('2023-02-28T14:00:00', 3250.82, 3258.36, 3246.14, 3255.31, 2.18440490e+09, 2.64594716e+10, 1.),\n", "       ('2023-02-28T14:30:00', 3255.64, 3268.23, 3254.79, 3268.04, 2.44241650e+09, 2.85697834e+10, 1.),\n", "       ('2023-02-28T15:00:00', 3269.14, 3280.42, 3266.32, 3279.61, 4.52967530e+09, 5.41643708e+10, 1.),\n", "       ('2023-03-01T10:00:00', 3279.14, 3292.6 , 3272.04, 3292.6 , 8.12714200e+09, 9.16288056e+10, 1.)],\n", "      dtype=[('frame', '<M8[s]'), ('open', '<f4'), ('high', '<f4'), ('low', '<f4'), ('close', '<f4'), ('volume', '<f8'), ('amount', '<f8'), ('factor', '<f4')])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["start = datetime.datetime(2023, 2, 9, 10, 0)  # 起始时间， 可改\n", "end = datetime.datetime(2023, 3, 1, 10, 0)  # 截止时间， 可改\n", "bars = await Stock.get_bars_in_range(code, FrameType.MIN30, start, end, fq=True, unclosed=True) # 如果当天未收盘，最后一行数据是当前股票实时价格\n", "bars"]}, {"cell_type": "markdown", "id": "c8e7cf10-fd52-407f-8311-2c580ca424fd", "metadata": {}, "source": ["#### 行情数据转为dataframe形式，如不需要，则略过"]}, {"cell_type": "code", "execution_count": 14, "id": "11a22da4-81b5-4006-824a-891cd13bb32b", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>factor</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-02-09 10:00:00</td>\n", "      <td>3227.729980</td>\n", "      <td>3252.260010</td>\n", "      <td>3225.770020</td>\n", "      <td>3251.129883</td>\n", "      <td>6.311800e+09</td>\n", "      <td>8.542894e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-02-09 10:30:00</td>\n", "      <td>3251.449951</td>\n", "      <td>3260.320068</td>\n", "      <td>3251.389893</td>\n", "      <td>3259.899902</td>\n", "      <td>3.560921e+09</td>\n", "      <td>4.943385e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-02-09 11:00:00</td>\n", "      <td>3259.820068</td>\n", "      <td>3259.820068</td>\n", "      <td>3252.419922</td>\n", "      <td>3254.659912</td>\n", "      <td>2.377631e+09</td>\n", "      <td>3.602548e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-02-09 11:30:00</td>\n", "      <td>3254.129883</td>\n", "      <td>3256.500000</td>\n", "      <td>3250.500000</td>\n", "      <td>3252.020020</td>\n", "      <td>1.859981e+09</td>\n", "      <td>2.615193e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-02-09 13:30:00</td>\n", "      <td>3252.020020</td>\n", "      <td>3264.649902</td>\n", "      <td>3252.020020</td>\n", "      <td>3264.350098</td>\n", "      <td>2.696764e+09</td>\n", "      <td>3.635421e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>2023-02-28 13:30:00</td>\n", "      <td>3260.350098</td>\n", "      <td>3261.909912</td>\n", "      <td>3249.560059</td>\n", "      <td>3251.000000</td>\n", "      <td>2.256924e+09</td>\n", "      <td>2.863295e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109</th>\n", "      <td>2023-02-28 14:00:00</td>\n", "      <td>3250.820068</td>\n", "      <td>3258.360107</td>\n", "      <td>3246.139893</td>\n", "      <td>3255.310059</td>\n", "      <td>2.184405e+09</td>\n", "      <td>2.645947e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>2023-02-28 14:30:00</td>\n", "      <td>3255.639893</td>\n", "      <td>3268.229980</td>\n", "      <td>3254.790039</td>\n", "      <td>3268.040039</td>\n", "      <td>2.442416e+09</td>\n", "      <td>2.856978e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>2023-02-28 15:00:00</td>\n", "      <td>3269.139893</td>\n", "      <td>3280.419922</td>\n", "      <td>3266.320068</td>\n", "      <td>3279.610107</td>\n", "      <td>4.529675e+09</td>\n", "      <td>5.416437e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>2023-03-01 10:00:00</td>\n", "      <td>3279.139893</td>\n", "      <td>3292.600098</td>\n", "      <td>3272.040039</td>\n", "      <td>3292.600098</td>\n", "      <td>8.127142e+09</td>\n", "      <td>9.162881e+10</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>113 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                  frame         open         high          low        close  \\\n", "0   2023-02-09 10:00:00  3227.729980  3252.260010  3225.770020  3251.129883   \n", "1   2023-02-09 10:30:00  3251.449951  3260.320068  3251.389893  3259.899902   \n", "2   2023-02-09 11:00:00  3259.820068  3259.820068  3252.419922  3254.659912   \n", "3   2023-02-09 11:30:00  3254.129883  3256.500000  3250.500000  3252.020020   \n", "4   2023-02-09 13:30:00  3252.020020  3264.649902  3252.020020  3264.350098   \n", "..                  ...          ...          ...          ...          ...   \n", "108 2023-02-28 13:30:00  3260.350098  3261.909912  3249.560059  3251.000000   \n", "109 2023-02-28 14:00:00  3250.820068  3258.360107  3246.139893  3255.310059   \n", "110 2023-02-28 14:30:00  3255.639893  3268.229980  3254.790039  3268.040039   \n", "111 2023-02-28 15:00:00  3269.139893  3280.419922  3266.320068  3279.610107   \n", "112 2023-03-01 10:00:00  3279.139893  3292.600098  3272.040039  3292.600098   \n", "\n", "           volume        amount  factor  \n", "0    6.311800e+09  8.542894e+10     1.0  \n", "1    3.560921e+09  4.943385e+10     1.0  \n", "2    2.377631e+09  3.602548e+10     1.0  \n", "3    1.859981e+09  2.615193e+10     1.0  \n", "4    2.696764e+09  3.635421e+10     1.0  \n", "..            ...           ...     ...  \n", "108  2.256924e+09  2.863295e+10     1.0  \n", "109  2.184405e+09  2.645947e+10     1.0  \n", "110  2.442416e+09  2.856978e+10     1.0  \n", "111  4.529675e+09  5.416437e+10     1.0  \n", "112  8.127142e+09  9.162881e+10     1.0  \n", "\n", "[113 rows x 8 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["bars_df = pd.DataFrame(bars)\n", "bars_df"]}, {"attachments": {}, "cell_type": "markdown", "id": "e10419dc-a6ef-42c1-af4a-7ad0b724728d", "metadata": {}, "source": ["### 绘制出K线图，交互式图"]}, {"cell_type": "code", "execution_count": 15, "id": "03b6781e-7541-4b6c-97ac-1d5fc88703e1", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [3251.1298828125, 3259.89990234375, 3254.659912109375, 3252.02001953125, 3264.35009765625, 3263.6201171875, 3268.050048828125, 3270.3798828125, 3267.3798828125, 3266.469970703125, 3264.830078125, 3250.85009765625, 3249.2900390625, 3257.3701171875, 3260.860107421875, 3260.669921875, 3272.5400390625, 3268.7900390625, 3280.2099609375, 3278.070068359375, 3281.090087890625, 3279.72998046875, 3282.169921875, 3284.159912109375, 3289.739990234375, 3287.659912109375, 3283.72998046875, 3283.2099609375, 3280.429931640625, 3286.820068359375, 3288.14990234375, 3293.280029296875, 3285.5, 3279.010009765625, 3281.389892578125, 3282.610107421875, 3278.449951171875, 3279.97998046875, 3278.469970703125, 3280.489990234375, 3293.9599609375, 3305.659912109375, 3305, 3305.85009765625, 3305.909912109375, 3293.64990234375, 3252.639892578125, 3249.030029296875, 3260.93994140625, 3256.3701171875, 3255.14990234375, 3243.820068359375, 3243.47998046875, 3243.389892578125, 3231.179931640625, 3224.02001953125, 3243.06005859375, 3249.3701171875, 3251.60009765625, 3255.800048828125, 3265.820068359375, 3274.780029296875, 3287.47998046875, 3290.340087890625, 3299.419921875, 3305.580078125, 3306.139892578125, 3293.7099609375, 3293.610107421875, 3293.68994140625, 3295.669921875, 3306.52001953125, 3291.919921875, 3296.89990234375, 3301.47998046875, 3298.1298828125, 3296.7099609375, 3291.0400390625, 3290.89990234375, 3291.14990234375, 3299.1201171875, 3290.27001953125, 3296.43994140625, 3293.3701171875, 3286.090087890625, 3278.75, 3280.159912109375, 3287.47998046875, 3280.81005859375, 3270.330078125, 3259.580078125, 3264.580078125, 3268.7099609375, 3266.840087890625, 3269.550048828125, 3267.159912109375, 3267.47998046875, 3266.989990234375, 3275.989990234375, 3263.3798828125, 3257.719970703125, 3256.1201171875, 3257.280029296875, 3258.030029296875, 3266.60009765625, 3262.77001953125, 3264.610107421875, 3260.39990234375, 3251, 3255.31005859375, 3268.0400390625, 3279.610107421875, 3292.60009765625], "decreasing": {"line": {"color": "green"}}, "high": [3252.260009765625, 3260.320068359375, 3259.820068359375, 3256.5, 3264.64990234375, 3265.97998046875, 3268.889892578125, 3270.3798828125, 3269.0400390625, 3269.989990234375, 3266.89990234375, 3264.89990234375, 3255.52001953125, 3257.3701171875, 3263.68994140625, 3262.7099609375, 3272.77001953125, 3273.199951171875, 3280.2099609375, 3283.090087890625, 3282.97998046875, 3284.989990234375, 3283.159912109375, 3285.090087890625, 3292.159912109375, 3293.72998046875, 3288.6298828125, 3284.580078125, 3286.409912109375, 3287.219970703125, 3289.43994140625, 3293.280029296875, 3296.199951171875, 3287.159912109375, 3281.85009765625, 3284.429931640625, 3285.75, 3282.550048828125, 3281.050048828125, 3281.219970703125, 3295.030029296875, 3305.8798828125, 3308.52001953125, 3308.300048828125, 3308.830078125, 3306.090087890625, 3293.9599609375, 3255.719970703125, 3262.469970703125, 3261.199951171875, 3258.929931640625, 3256.75, 3246.919921875, 3244.81005859375, 3248.2099609375, 3235.2099609375, 3244.929931640625, 3250.56005859375, 3252.199951171875, 3256.070068359375, 3266.159912109375, 3277, 3289.4599609375, 3291.0400390625, 3304.47998046875, 3307.239990234375, 3308.7900390625, 3306.659912109375, 3298.860107421875, 3297.179931640625, 3296.18994140625, 3306.52001953125, 3298.7900390625, 3298.7900390625, 3307.030029296875, 3301.590087890625, 3300.2900390625, 3296.780029296875, 3293.85009765625, 3292.530029296875, 3307.43994140625, 3301.02001953125, 3298.280029296875, 3302.56005859375, 3294.27001953125, 3286.89990234375, 3284.139892578125, 3287.860107421875, 3288.389892578125, 3283.969970703125, 3270.199951171875, 3266.1201171875, 3271.169921875, 3269.8798828125, 3269.550048828125, 3273.8798828125, 3273.340087890625, 3270.6298828125, 3276.169921875, 3276.580078125, 3270.77001953125, 3257.889892578125, 3260.10009765625, 3258.77001953125, 3272.679931640625, 3267.199951171875, 3268.320068359375, 3264.639892578125, 3261.909912109375, 3258.360107421875, 3268.22998046875, 3280.419921875, 3292.60009765625], "increasing": {"line": {"color": "red"}}, "low": [3225.77001953125, 3251.389892578125, 3252.419921875, 3250.5, 3252.02001953125, 3262.169921875, 3263.199951171875, 3267.429931640625, 3261.409912109375, 3264.050048828125, 3260.7900390625, 3249.72998046875, 3248.14990234375, 3247.22998046875, 3256.889892578125, 3258.010009765625, 3252.6298828125, 3263.199951171875, 3268.780029296875, 3276.22998046875, 3276.080078125, 3278.419921875, 3278.820068359375, 3281.719970703125, 3284.300048828125, 3287.080078125, 3280.639892578125, 3282.2099609375, 3279.2900390625, 3279.18994140625, 3284.340087890625, 3287.6298828125, 3284.669921875, 3278.659912109375, 3274.77001953125, 3280.360107421875, 3275.550048828125, 3277.919921875, 3274.550048828125, 3277.580078125, 3278.639892578125, 3290.9599609375, 3302.169921875, 3303.219970703125, 3305.330078125, 3293.219970703125, 3252.550048828125, 3231.340087890625, 3244.72998046875, 3243.97998046875, 3252.110107421875, 3243.5400390625, 3235.35009765625, 3237.8701171875, 3230.10009765625, 3223.260009765625, 3225.840087890625, 3237.570068359375, 3242.949951171875, 3249.760009765625, 3254.469970703125, 3265.2099609375, 3274.02001953125, 3286.469970703125, 3287.679931640625, 3297.639892578125, 3301.199951171875, 3282.43994140625, 3291.8798828125, 3292.10009765625, 3286.699951171875, 3294.7099609375, 3288.590087890625, 3291.8701171875, 3296.719970703125, 3297.610107421875, 3291.780029296875, 3289.199951171875, 3286.719970703125, 3287.43994140625, 3293.52001953125, 3286.43994140625, 3288.919921875, 3292.6298828125, 3285.8798828125, 3275.360107421875, 3278.360107421875, 3280.300048828125, 3276.570068359375, 3270.050048828125, 3258.9599609375, 3253.969970703125, 3260.669921875, 3264.239990234375, 3261.469970703125, 3267.159912109375, 3253.199951171875, 3264.010009765625, 3267.010009765625, 3260.8798828125, 3257.719970703125, 3251.719970703125, 3256.300048828125, 3254.5, 3265.739990234375, 3260.47998046875, 3260.300048828125, 3259.239990234375, 3249.56005859375, 3246.139892578125, 3254.7900390625, 3266.320068359375, 3272.0400390625], "name": "K线", "open": [3227.72998046875, 3251.449951171875, 3259.820068359375, 3254.1298828125, 3252.02001953125, 3264.52001953125, 3263.6201171875, 3268.47998046875, 3266.43994140625, 3267.840087890625, 3266.35009765625, 3264.75, 3250.85009765625, 3249.2900390625, 3257.56005859375, 3261.25, 3256.989990234375, 3272.97998046875, 3268.89990234375, 3280.159912109375, 3278.070068359375, 3280.56005859375, 3280.10009765625, 3282.239990234375, 3288.969970703125, 3289.3701171875, 3288.050048828125, 3283.570068359375, 3283.2099609375, 3280.27001953125, 3287.050048828125, 3288.47998046875, 3294.02001953125, 3285.260009765625, 3278.72998046875, 3281.31005859375, 3282.610107421875, 3278.449951171875, 3279.889892578125, 3278.260009765625, 3281.739990234375, 3294.699951171875, 3305.570068359375, 3304.949951171875, 3305.85009765625, 3305.81005859375, 3293.75, 3252.35009765625, 3244.72998046875, 3261.199951171875, 3256.4599609375, 3255.18994140625, 3243.820068359375, 3243.1298828125, 3243.409912109375, 3231.10009765625, 3230.469970703125, 3243.050048828125, 3248.669921875, 3251.4599609375, 3255.800048828125, 3265.860107421875, 3274.8701171875, 3287.2099609375, 3291.6298828125, 3299.489990234375, 3305.39990234375, 3305.989990234375, 3293.7099609375, 3294.010009765625, 3294.0400390625, 3295.580078125, 3292.050048828125, 3292.18994140625, 3296.81005859375, 3301.590087890625, 3298.1298828125, 3296.679931640625, 3291.050048828125, 3291.260009765625, 3293.52001953125, 3299.330078125, 3290.139892578125, 3296.409912109375, 3293.3701171875, 3286.10009765625, 3278.580078125, 3280.52001953125, 3287.260009765625, 3280.530029296875, 3270.070068359375, 3259.669921875, 3264.580078125, 3268.8798828125, 3267.179931640625, 3269.27001953125, 3257, 3267.739990234375, 3267.080078125, 3275.989990234375, 3263.3798828125, 3257.7099609375, 3256.300048828125, 3257.280029296875, 3265.739990234375, 3267.199951171875, 3262.389892578125, 3264.39990234375, 3260.35009765625, 3250.820068359375, 3255.639892578125, 3269.139892578125, 3279.139892578125], "text": ["2023-02-09T10:00:00", "2023-02-09T10:30:00", "2023-02-09T11:00:00", "2023-02-09T11:30:00", "2023-02-09T13:30:00", "2023-02-09T14:00:00", "2023-02-09T14:30:00", "2023-02-09T15:00:00", "2023-02-10T10:00:00", "2023-02-10T10:30:00", "2023-02-10T11:00:00", "2023-02-10T11:30:00", "2023-02-10T13:30:00", "2023-02-10T14:00:00", "2023-02-10T14:30:00", "2023-02-10T15:00:00", "2023-02-13T10:00:00", "2023-02-13T10:30:00", "2023-02-13T11:00:00", "2023-02-13T11:30:00", "2023-02-13T13:30:00", "2023-02-13T14:00:00", "2023-02-13T14:30:00", "2023-02-13T15:00:00", "2023-02-14T10:00:00", "2023-02-14T10:30:00", "2023-02-14T11:00:00", "2023-02-14T11:30:00", "2023-02-14T13:30:00", "2023-02-14T14:00:00", "2023-02-14T14:30:00", "2023-02-14T15:00:00", "2023-02-15T10:00:00", "2023-02-15T10:30:00", "2023-02-15T11:00:00", "2023-02-15T11:30:00", "2023-02-15T13:30:00", "2023-02-15T14:00:00", "2023-02-15T14:30:00", "2023-02-15T15:00:00", "2023-02-16T10:00:00", "2023-02-16T10:30:00", "2023-02-16T11:00:00", "2023-02-16T11:30:00", "2023-02-16T13:30:00", "2023-02-16T14:00:00", "2023-02-16T14:30:00", "2023-02-16T15:00:00", "2023-02-17T10:00:00", "2023-02-17T10:30:00", "2023-02-17T11:00:00", "2023-02-17T11:30:00", "2023-02-17T13:30:00", "2023-02-17T14:00:00", "2023-02-17T14:30:00", "2023-02-17T15:00:00", "2023-02-20T10:00:00", "2023-02-20T10:30:00", "2023-02-20T11:00:00", "2023-02-20T11:30:00", "2023-02-20T13:30:00", "2023-02-20T14:00:00", "2023-02-20T14:30:00", "2023-02-20T15:00:00", "2023-02-21T10:00:00", "2023-02-21T10:30:00", "2023-02-21T11:00:00", "2023-02-21T11:30:00", "2023-02-21T13:30:00", "2023-02-21T14:00:00", "2023-02-21T14:30:00", "2023-02-21T15:00:00", "2023-02-22T10:00:00", "2023-02-22T10:30:00", "2023-02-22T11:00:00", "2023-02-22T11:30:00", "2023-02-22T13:30:00", "2023-02-22T14:00:00", "2023-02-22T14:30:00", "2023-02-22T15:00:00", "2023-02-23T10:00:00", "2023-02-23T10:30:00", "2023-02-23T11:00:00", "2023-02-23T11:30:00", "2023-02-23T13:30:00", "2023-02-23T14:00:00", "2023-02-23T14:30:00", "2023-02-23T15:00:00", "2023-02-24T10:00:00", "2023-02-24T10:30:00", "2023-02-24T11:00:00", "2023-02-24T11:30:00", "2023-02-24T13:30:00", "2023-02-24T14:00:00", "2023-02-24T14:30:00", "2023-02-24T15:00:00", "2023-02-27T10:00:00", "2023-02-27T10:30:00", "2023-02-27T11:00:00", "2023-02-27T11:30:00", "2023-02-27T13:30:00", "2023-02-27T14:00:00", "2023-02-27T14:30:00", "2023-02-27T15:00:00", "2023-02-28T10:00:00", "2023-02-28T10:30:00", "2023-02-28T11:00:00", "2023-02-28T11:30:00", "2023-02-28T13:30:00", "2023-02-28T14:00:00", "2023-02-28T14:30:00", "2023-02-28T15:00:00", "2023-03-01T10:00:00"], "type": "candlestick", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112], "xaxis": "x", "yaxis": "y"}, {"marker": {"color": "red"}, "name": "close>=open成交量", "text": ["2023-02-09T10:00:00", "2023-02-09T10:30:00", "2023-02-09T13:30:00", "2023-02-09T14:30:00", "2023-02-09T15:00:00", "2023-02-10T10:00:00", "2023-02-10T14:00:00", "2023-02-10T14:30:00", "2023-02-13T10:00:00", "2023-02-13T11:00:00", "2023-02-13T13:30:00", "2023-02-13T14:30:00", "2023-02-13T15:00:00", "2023-02-14T10:00:00", "2023-02-14T14:00:00", "2023-02-14T14:30:00", "2023-02-14T15:00:00", "2023-02-15T11:00:00", "2023-02-15T11:30:00", "2023-02-15T14:00:00", "2023-02-15T15:00:00", "2023-02-16T10:00:00", "2023-02-16T10:30:00", "2023-02-16T11:30:00", "2023-02-16T13:30:00", "2023-02-17T10:00:00", "2023-02-17T14:00:00", "2023-02-20T10:00:00", "2023-02-20T10:30:00", "2023-02-20T11:00:00", "2023-02-20T11:30:00", "2023-02-20T13:30:00", "2023-02-20T14:00:00", "2023-02-20T14:30:00", "2023-02-20T15:00:00", "2023-02-21T10:00:00", "2023-02-21T10:30:00", "2023-02-21T11:00:00", "2023-02-21T14:30:00", "2023-02-21T15:00:00", "2023-02-22T10:30:00", "2023-02-22T11:00:00", "2023-02-23T10:00:00", "2023-02-23T11:00:00", "2023-02-23T14:30:00", "2023-02-23T15:00:00", "2023-02-24T11:30:00", "2023-02-24T13:30:00", "2023-02-24T14:30:00", "2023-02-27T10:00:00", "2023-02-27T11:00:00", "2023-02-27T14:30:00", "2023-02-27T15:00:00", "2023-02-28T10:00:00", "2023-02-28T11:00:00", "2023-02-28T14:00:00", "2023-02-28T14:30:00", "2023-02-28T15:00:00", "2023-03-01T10:00:00"], "type": "bar", "x": [0, 1, 4, 6, 7, 8, 13, 14, 16, 18, 20, 22, 23, 24, 29, 30, 31, 34, 35, 37, 39, 40, 41, 43, 44, 48, 53, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 70, 71, 73, 74, 80, 82, 86, 87, 91, 92, 94, 96, 98, 102, 103, 104, 106, 109, 110, 111, 112], "xaxis": "x2", "y": [6311799600, 3560921200, 2696764000, 2538189900, 3708308600, 6812960800, 2248948900, 2194502400, 7853372300, 3469037200, 2535439200, 2376191200, 3750770700, 7934027100, 2125216700, 2234066700, 3526337400, 3068260800, 2146263200, 2341665900, 3409079100, 7777107600, 4608960200, 2262186500, 2723926600, 8049465700, 2182749300, 8887809800, 4263880200, 3057094600, 2398819400, 2991696100, 3269487500, 3502949300, 4129036400, 10128284500, 4778140000, 3303491700, 2257441900, 3903247800, 3478878500, 2616860900, 7607378700, 2405568800, 2114245900, 3038175100, 2313878800, 2080127800, 1917411900, 7065333300, 2231080100, 1734771000, 3118278400, 6459153900, 2256889300, 2184404900, 2442416500, 4529675300, 8127142000], "yaxis": "y2"}, {"marker": {"color": "green"}, "name": "close<open成交量", "text": ["2023-02-09T11:00:00", "2023-02-09T11:30:00", "2023-02-09T14:00:00", "2023-02-10T10:30:00", "2023-02-10T11:00:00", "2023-02-10T11:30:00", "2023-02-10T13:30:00", "2023-02-10T15:00:00", "2023-02-13T10:30:00", "2023-02-13T11:30:00", "2023-02-13T14:00:00", "2023-02-14T10:30:00", "2023-02-14T11:00:00", "2023-02-14T11:30:00", "2023-02-14T13:30:00", "2023-02-15T10:00:00", "2023-02-15T10:30:00", "2023-02-15T13:30:00", "2023-02-15T14:30:00", "2023-02-16T11:00:00", "2023-02-16T14:00:00", "2023-02-16T14:30:00", "2023-02-16T15:00:00", "2023-02-17T10:30:00", "2023-02-17T11:00:00", "2023-02-17T11:30:00", "2023-02-17T13:30:00", "2023-02-17T14:30:00", "2023-02-17T15:00:00", "2023-02-21T11:30:00", "2023-02-21T13:30:00", "2023-02-21T14:00:00", "2023-02-22T10:00:00", "2023-02-22T11:30:00", "2023-02-22T13:30:00", "2023-02-22T14:00:00", "2023-02-22T14:30:00", "2023-02-22T15:00:00", "2023-02-23T10:30:00", "2023-02-23T11:30:00", "2023-02-23T13:30:00", "2023-02-23T14:00:00", "2023-02-24T10:00:00", "2023-02-24T10:30:00", "2023-02-24T11:00:00", "2023-02-24T14:00:00", "2023-02-24T15:00:00", "2023-02-27T10:30:00", "2023-02-27T11:30:00", "2023-02-27T13:30:00", "2023-02-27T14:00:00", "2023-02-28T10:30:00", "2023-02-28T11:30:00", "2023-02-28T13:30:00"], "type": "bar", "x": [2, 3, 5, 9, 10, 11, 12, 15, 17, 19, 21, 25, 26, 27, 28, 32, 33, 36, 38, 42, 45, 46, 47, 49, 50, 51, 52, 54, 55, 67, 68, 69, 72, 75, 76, 77, 78, 79, 81, 83, 84, 85, 88, 89, 90, 93, 95, 97, 99, 100, 101, 105, 107, 108], "xaxis": "x2", "y": [6311799600, 3560921200, 2696764000, 2538189900, 3708308600, 6812960800, 2248948900, 2194502400, 7853372300, 3469037200, 2535439200, 2376191200, 3750770700, 7934027100, 2125216700, 2234066700, 3526337400, 3068260800, 2146263200, 2341665900, 3409079100, 7777107600, 4608960200, 2262186500, 2723926600, 8049465700, 2182749300, 8887809800, 4263880200, 3057094600, 2398819400, 2991696100, 3269487500, 3502949300, 4129036400, 10128284500, 4778140000, 3303491700, 2257441900, 3903247800, 3478878500, 2616860900, 7607378700, 2405568800, 2114245900, 3038175100, 2313878800, 2080127800, 1917411900, 7065333300, 2231080100, 1734771000, 3118278400, 6459153900, 2256889300, 2184404900, 2442416500, 4529675300, 8127142000], "yaxis": "y2"}], "layout": {"height": 600, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "000001.XSHG: 上证指数"}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 1], "matches": "x2", "rangeslider": {"visible": false}, "showspikes": true, "showticklabels": false, "spikethickness": 2}, "xaxis2": {"anchor": "y2", "domain": [0, 1], "showspikes": true, "spikethickness": 2}, "yaxis": {"anchor": "x", "domain": [0.3, 1], "showspikes": true, "spikethickness": 2}, "yaxis2": {"anchor": "x2", "domain": [0, 0.3], "showspikes": true, "spikethickness": 2}}}, "text/html": ["<div>                            <div id=\"63686587-0f5a-4c1a-9084-0e30d48c3c83\" class=\"plotly-graph-div\" style=\"height:600px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"63686587-0f5a-4c1a-9084-0e30d48c3c83\")) {                    Plotly.newPlot(                        \"63686587-0f5a-4c1a-9084-0e30d48c3c83\",                        [{\"close\":[3251.1298828125,3259.89990234375,3254.659912109375,3252.02001953125,3264.35009765625,3263.6201171875,3268.050048828125,3270.3798828125,3267.3798828125,3266.469970703125,3264.830078125,3250.85009765625,3249.2900390625,3257.3701171875,3260.860107421875,3260.669921875,3272.5400390625,3268.7900390625,3280.2099609375,3278.070068359375,3281.090087890625,3279.72998046875,3282.169921875,3284.159912109375,3289.739990234375,3287.659912109375,3283.72998046875,3283.2099609375,3280.429931640625,3286.820068359375,3288.14990234375,3293.280029296875,3285.5,3279.010009765625,3281.389892578125,3282.610107421875,3278.449951171875,3279.97998046875,3278.469970703125,3280.489990234375,3293.9599609375,3305.659912109375,3305.0,3305.85009765625,3305.909912109375,3293.64990234375,3252.639892578125,3249.030029296875,3260.93994140625,3256.3701171875,3255.14990234375,3243.820068359375,3243.47998046875,3243.389892578125,3231.179931640625,3224.02001953125,3243.06005859375,3249.3701171875,3251.60009765625,3255.800048828125,3265.820068359375,3274.780029296875,3287.47998046875,3290.340087890625,3299.419921875,3305.580078125,3306.139892578125,3293.7099609375,3293.610107421875,3293.68994140625,3295.669921875,3306.52001953125,3291.919921875,3296.89990234375,3301.47998046875,3298.1298828125,3296.7099609375,3291.0400390625,3290.89990234375,3291.14990234375,3299.1201171875,3290.27001953125,3296.43994140625,3293.3701171875,3286.090087890625,3278.75,3280.159912109375,3287.47998046875,3280.81005859375,3270.330078125,3259.580078125,3264.580078125,3268.7099609375,3266.840087890625,3269.550048828125,3267.159912109375,3267.47998046875,3266.989990234375,3275.989990234375,3263.3798828125,3257.719970703125,3256.1201171875,3257.280029296875,3258.030029296875,3266.60009765625,3262.77001953125,3264.610107421875,3260.39990234375,3251.0,3255.31005859375,3268.0400390625,3279.610107421875,3292.60009765625],\"decreasing\":{\"line\":{\"color\":\"green\"}},\"high\":[3252.260009765625,3260.320068359375,3259.820068359375,3256.5,3264.64990234375,3265.97998046875,3268.889892578125,3270.3798828125,3269.0400390625,3269.989990234375,3266.89990234375,3264.89990234375,3255.52001953125,3257.3701171875,3263.68994140625,3262.7099609375,3272.77001953125,3273.199951171875,3280.2099609375,3283.090087890625,3282.97998046875,3284.989990234375,3283.159912109375,3285.090087890625,3292.159912109375,3293.72998046875,3288.6298828125,3284.580078125,3286.409912109375,3287.219970703125,3289.43994140625,3293.280029296875,3296.199951171875,3287.159912109375,3281.85009765625,3284.429931640625,3285.75,3282.550048828125,3281.050048828125,3281.219970703125,3295.030029296875,3305.8798828125,3308.52001953125,3308.300048828125,3308.830078125,3306.090087890625,3293.9599609375,3255.719970703125,3262.469970703125,3261.199951171875,3258.929931640625,3256.75,3246.919921875,3244.81005859375,3248.2099609375,3235.2099609375,3244.929931640625,3250.56005859375,3252.199951171875,3256.070068359375,3266.159912109375,3277.0,3289.4599609375,3291.0400390625,3304.47998046875,3307.239990234375,3308.7900390625,3306.659912109375,3298.860107421875,3297.179931640625,3296.18994140625,3306.52001953125,3298.7900390625,3298.7900390625,3307.030029296875,3301.590087890625,3300.2900390625,3296.780029296875,3293.85009765625,3292.530029296875,3307.43994140625,3301.02001953125,3298.280029296875,3302.56005859375,3294.27001953125,3286.89990234375,3284.139892578125,3287.860107421875,3288.389892578125,3283.969970703125,3270.199951171875,3266.1201171875,3271.169921875,3269.8798828125,3269.550048828125,3273.8798828125,3273.340087890625,3270.6298828125,3276.169921875,3276.580078125,3270.77001953125,3257.889892578125,3260.10009765625,3258.77001953125,3272.679931640625,3267.199951171875,3268.320068359375,3264.639892578125,3261.909912109375,3258.360107421875,3268.22998046875,3280.419921875,3292.60009765625],\"increasing\":{\"line\":{\"color\":\"red\"}},\"low\":[3225.77001953125,3251.389892578125,3252.419921875,3250.5,3252.02001953125,3262.169921875,3263.199951171875,3267.429931640625,3261.409912109375,3264.050048828125,3260.7900390625,3249.72998046875,3248.14990234375,3247.22998046875,3256.889892578125,3258.010009765625,3252.6298828125,3263.199951171875,3268.780029296875,3276.22998046875,3276.080078125,3278.419921875,3278.820068359375,3281.719970703125,3284.300048828125,3287.080078125,3280.639892578125,3282.2099609375,3279.2900390625,3279.18994140625,3284.340087890625,3287.6298828125,3284.669921875,3278.659912109375,3274.77001953125,3280.360107421875,3275.550048828125,3277.919921875,3274.550048828125,3277.580078125,3278.639892578125,3290.9599609375,3302.169921875,3303.219970703125,3305.330078125,3293.219970703125,3252.550048828125,3231.340087890625,3244.72998046875,3243.97998046875,3252.110107421875,3243.5400390625,3235.35009765625,3237.8701171875,3230.10009765625,3223.260009765625,3225.840087890625,3237.570068359375,3242.949951171875,3249.760009765625,3254.469970703125,3265.2099609375,3274.02001953125,3286.469970703125,3287.679931640625,3297.639892578125,3301.199951171875,3282.43994140625,3291.8798828125,3292.10009765625,3286.699951171875,3294.7099609375,3288.590087890625,3291.8701171875,3296.719970703125,3297.610107421875,3291.780029296875,3289.199951171875,3286.719970703125,3287.43994140625,3293.52001953125,3286.43994140625,3288.919921875,3292.6298828125,3285.8798828125,3275.360107421875,3278.360107421875,3280.300048828125,3276.570068359375,3270.050048828125,3258.9599609375,3253.969970703125,3260.669921875,3264.239990234375,3261.469970703125,3267.159912109375,3253.199951171875,3264.010009765625,3267.010009765625,3260.8798828125,3257.719970703125,3251.719970703125,3256.300048828125,3254.5,3265.739990234375,3260.47998046875,3260.300048828125,3259.239990234375,3249.56005859375,3246.139892578125,3254.7900390625,3266.320068359375,3272.0400390625],\"name\":\"K\\u7ebf\",\"open\":[3227.72998046875,3251.449951171875,3259.820068359375,3254.1298828125,3252.02001953125,3264.52001953125,3263.6201171875,3268.47998046875,3266.43994140625,3267.840087890625,3266.35009765625,3264.75,3250.85009765625,3249.2900390625,3257.56005859375,3261.25,3256.989990234375,3272.97998046875,3268.89990234375,3280.159912109375,3278.070068359375,3280.56005859375,3280.10009765625,3282.239990234375,3288.969970703125,3289.3701171875,3288.050048828125,3283.570068359375,3283.2099609375,3280.27001953125,3287.050048828125,3288.47998046875,3294.02001953125,3285.260009765625,3278.72998046875,3281.31005859375,3282.610107421875,3278.449951171875,3279.889892578125,3278.260009765625,3281.739990234375,3294.699951171875,3305.570068359375,3304.949951171875,3305.85009765625,3305.81005859375,3293.75,3252.35009765625,3244.72998046875,3261.199951171875,3256.4599609375,3255.18994140625,3243.820068359375,3243.1298828125,3243.409912109375,3231.10009765625,3230.469970703125,3243.050048828125,3248.669921875,3251.4599609375,3255.800048828125,3265.860107421875,3274.8701171875,3287.2099609375,3291.6298828125,3299.489990234375,3305.39990234375,3305.989990234375,3293.7099609375,3294.010009765625,3294.0400390625,3295.580078125,3292.050048828125,3292.18994140625,3296.81005859375,3301.590087890625,3298.1298828125,3296.679931640625,3291.050048828125,3291.260009765625,3293.52001953125,3299.330078125,3290.139892578125,3296.409912109375,3293.3701171875,3286.10009765625,3278.580078125,3280.52001953125,3287.260009765625,3280.530029296875,3270.070068359375,3259.669921875,3264.580078125,3268.8798828125,3267.179931640625,3269.27001953125,3257.0,3267.739990234375,3267.080078125,3275.989990234375,3263.3798828125,3257.7099609375,3256.300048828125,3257.280029296875,3265.739990234375,3267.199951171875,3262.389892578125,3264.39990234375,3260.35009765625,3250.820068359375,3255.639892578125,3269.139892578125,3279.139892578125],\"text\":[\"2023-02-09T10:00:00\",\"2023-02-09T10:30:00\",\"2023-02-09T11:00:00\",\"2023-02-09T11:30:00\",\"2023-02-09T13:30:00\",\"2023-02-09T14:00:00\",\"2023-02-09T14:30:00\",\"2023-02-09T15:00:00\",\"2023-02-10T10:00:00\",\"2023-02-10T10:30:00\",\"2023-02-10T11:00:00\",\"2023-02-10T11:30:00\",\"2023-02-10T13:30:00\",\"2023-02-10T14:00:00\",\"2023-02-10T14:30:00\",\"2023-02-10T15:00:00\",\"2023-02-13T10:00:00\",\"2023-02-13T10:30:00\",\"2023-02-13T11:00:00\",\"2023-02-13T11:30:00\",\"2023-02-13T13:30:00\",\"2023-02-13T14:00:00\",\"2023-02-13T14:30:00\",\"2023-02-13T15:00:00\",\"2023-02-14T10:00:00\",\"2023-02-14T10:30:00\",\"2023-02-14T11:00:00\",\"2023-02-14T11:30:00\",\"2023-02-14T13:30:00\",\"2023-02-14T14:00:00\",\"2023-02-14T14:30:00\",\"2023-02-14T15:00:00\",\"2023-02-15T10:00:00\",\"2023-02-15T10:30:00\",\"2023-02-15T11:00:00\",\"2023-02-15T11:30:00\",\"2023-02-15T13:30:00\",\"2023-02-15T14:00:00\",\"2023-02-15T14:30:00\",\"2023-02-15T15:00:00\",\"2023-02-16T10:00:00\",\"2023-02-16T10:30:00\",\"2023-02-16T11:00:00\",\"2023-02-16T11:30:00\",\"2023-02-16T13:30:00\",\"2023-02-16T14:00:00\",\"2023-02-16T14:30:00\",\"2023-02-16T15:00:00\",\"2023-02-17T10:00:00\",\"2023-02-17T10:30:00\",\"2023-02-17T11:00:00\",\"2023-02-17T11:30:00\",\"2023-02-17T13:30:00\",\"2023-02-17T14:00:00\",\"2023-02-17T14:30:00\",\"2023-02-17T15:00:00\",\"2023-02-20T10:00:00\",\"2023-02-20T10:30:00\",\"2023-02-20T11:00:00\",\"2023-02-20T11:30:00\",\"2023-02-20T13:30:00\",\"2023-02-20T14:00:00\",\"2023-02-20T14:30:00\",\"2023-02-20T15:00:00\",\"2023-02-21T10:00:00\",\"2023-02-21T10:30:00\",\"2023-02-21T11:00:00\",\"2023-02-21T11:30:00\",\"2023-02-21T13:30:00\",\"2023-02-21T14:00:00\",\"2023-02-21T14:30:00\",\"2023-02-21T15:00:00\",\"2023-02-22T10:00:00\",\"2023-02-22T10:30:00\",\"2023-02-22T11:00:00\",\"2023-02-22T11:30:00\",\"2023-02-22T13:30:00\",\"2023-02-22T14:00:00\",\"2023-02-22T14:30:00\",\"2023-02-22T15:00:00\",\"2023-02-23T10:00:00\",\"2023-02-23T10:30:00\",\"2023-02-23T11:00:00\",\"2023-02-23T11:30:00\",\"2023-02-23T13:30:00\",\"2023-02-23T14:00:00\",\"2023-02-23T14:30:00\",\"2023-02-23T15:00:00\",\"2023-02-24T10:00:00\",\"2023-02-24T10:30:00\",\"2023-02-24T11:00:00\",\"2023-02-24T11:30:00\",\"2023-02-24T13:30:00\",\"2023-02-24T14:00:00\",\"2023-02-24T14:30:00\",\"2023-02-24T15:00:00\",\"2023-02-27T10:00:00\",\"2023-02-27T10:30:00\",\"2023-02-27T11:00:00\",\"2023-02-27T11:30:00\",\"2023-02-27T13:30:00\",\"2023-02-27T14:00:00\",\"2023-02-27T14:30:00\",\"2023-02-27T15:00:00\",\"2023-02-28T10:00:00\",\"2023-02-28T10:30:00\",\"2023-02-28T11:00:00\",\"2023-02-28T11:30:00\",\"2023-02-28T13:30:00\",\"2023-02-28T14:00:00\",\"2023-02-28T14:30:00\",\"2023-02-28T15:00:00\",\"2023-03-01T10:00:00\"],\"x\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112],\"type\":\"candlestick\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"color\":\"red\"},\"name\":\"close>=open\\u6210\\u4ea4\\u91cf\",\"text\":[\"2023-02-09T10:00:00\",\"2023-02-09T10:30:00\",\"2023-02-09T13:30:00\",\"2023-02-09T14:30:00\",\"2023-02-09T15:00:00\",\"2023-02-10T10:00:00\",\"2023-02-10T14:00:00\",\"2023-02-10T14:30:00\",\"2023-02-13T10:00:00\",\"2023-02-13T11:00:00\",\"2023-02-13T13:30:00\",\"2023-02-13T14:30:00\",\"2023-02-13T15:00:00\",\"2023-02-14T10:00:00\",\"2023-02-14T14:00:00\",\"2023-02-14T14:30:00\",\"2023-02-14T15:00:00\",\"2023-02-15T11:00:00\",\"2023-02-15T11:30:00\",\"2023-02-15T14:00:00\",\"2023-02-15T15:00:00\",\"2023-02-16T10:00:00\",\"2023-02-16T10:30:00\",\"2023-02-16T11:30:00\",\"2023-02-16T13:30:00\",\"2023-02-17T10:00:00\",\"2023-02-17T14:00:00\",\"2023-02-20T10:00:00\",\"2023-02-20T10:30:00\",\"2023-02-20T11:00:00\",\"2023-02-20T11:30:00\",\"2023-02-20T13:30:00\",\"2023-02-20T14:00:00\",\"2023-02-20T14:30:00\",\"2023-02-20T15:00:00\",\"2023-02-21T10:00:00\",\"2023-02-21T10:30:00\",\"2023-02-21T11:00:00\",\"2023-02-21T14:30:00\",\"2023-02-21T15:00:00\",\"2023-02-22T10:30:00\",\"2023-02-22T11:00:00\",\"2023-02-23T10:00:00\",\"2023-02-23T11:00:00\",\"2023-02-23T14:30:00\",\"2023-02-23T15:00:00\",\"2023-02-24T11:30:00\",\"2023-02-24T13:30:00\",\"2023-02-24T14:30:00\",\"2023-02-27T10:00:00\",\"2023-02-27T11:00:00\",\"2023-02-27T14:30:00\",\"2023-02-27T15:00:00\",\"2023-02-28T10:00:00\",\"2023-02-28T11:00:00\",\"2023-02-28T14:00:00\",\"2023-02-28T14:30:00\",\"2023-02-28T15:00:00\",\"2023-03-01T10:00:00\"],\"x\":[0,1,4,6,7,8,13,14,16,18,20,22,23,24,29,30,31,34,35,37,39,40,41,43,44,48,53,56,57,58,59,60,61,62,63,64,65,66,70,71,73,74,80,82,86,87,91,92,94,96,98,102,103,104,106,109,110,111,112],\"y\":[6311799600.0,3560921200.0,2696764000.0,2538189900.0,3708308600.0,6812960800.0,2248948900.0,2194502400.0,7853372300.0,3469037200.0,2535439200.0,2376191200.0,3750770700.0,7934027100.0,2125216700.0,2234066700.0,3526337400.0,3068260800.0,2146263200.0,2341665900.0,3409079100.0,7777107600.0,4608960200.0,2262186500.0,2723926600.0,8049465700.0,2182749300.0,8887809800.0,4263880200.0,3057094600.0,2398819400.0,2991696100.0,3269487500.0,3502949300.0,4129036400.0,10128284500.0,4778140000.0,3303491700.0,2257441900.0,3903247800.0,3478878500.0,2616860900.0,7607378700.0,2405568800.0,2114245900.0,3038175100.0,2313878800.0,2080127800.0,1917411900.0,7065333300.0,2231080100.0,1734771000.0,3118278400.0,6459153900.0,2256889300.0,2184404900.0,2442416500.0,4529675300.0,8127142000.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y2\"},{\"marker\":{\"color\":\"green\"},\"name\":\"close<open\\u6210\\u4ea4\\u91cf\",\"text\":[\"2023-02-09T11:00:00\",\"2023-02-09T11:30:00\",\"2023-02-09T14:00:00\",\"2023-02-10T10:30:00\",\"2023-02-10T11:00:00\",\"2023-02-10T11:30:00\",\"2023-02-10T13:30:00\",\"2023-02-10T15:00:00\",\"2023-02-13T10:30:00\",\"2023-02-13T11:30:00\",\"2023-02-13T14:00:00\",\"2023-02-14T10:30:00\",\"2023-02-14T11:00:00\",\"2023-02-14T11:30:00\",\"2023-02-14T13:30:00\",\"2023-02-15T10:00:00\",\"2023-02-15T10:30:00\",\"2023-02-15T13:30:00\",\"2023-02-15T14:30:00\",\"2023-02-16T11:00:00\",\"2023-02-16T14:00:00\",\"2023-02-16T14:30:00\",\"2023-02-16T15:00:00\",\"2023-02-17T10:30:00\",\"2023-02-17T11:00:00\",\"2023-02-17T11:30:00\",\"2023-02-17T13:30:00\",\"2023-02-17T14:30:00\",\"2023-02-17T15:00:00\",\"2023-02-21T11:30:00\",\"2023-02-21T13:30:00\",\"2023-02-21T14:00:00\",\"2023-02-22T10:00:00\",\"2023-02-22T11:30:00\",\"2023-02-22T13:30:00\",\"2023-02-22T14:00:00\",\"2023-02-22T14:30:00\",\"2023-02-22T15:00:00\",\"2023-02-23T10:30:00\",\"2023-02-23T11:30:00\",\"2023-02-23T13:30:00\",\"2023-02-23T14:00:00\",\"2023-02-24T10:00:00\",\"2023-02-24T10:30:00\",\"2023-02-24T11:00:00\",\"2023-02-24T14:00:00\",\"2023-02-24T15:00:00\",\"2023-02-27T10:30:00\",\"2023-02-27T11:30:00\",\"2023-02-27T13:30:00\",\"2023-02-27T14:00:00\",\"2023-02-28T10:30:00\",\"2023-02-28T11:30:00\",\"2023-02-28T13:30:00\"],\"x\":[2,3,5,9,10,11,12,15,17,19,21,25,26,27,28,32,33,36,38,42,45,46,47,49,50,51,52,54,55,67,68,69,72,75,76,77,78,79,81,83,84,85,88,89,90,93,95,97,99,100,101,105,107,108],\"y\":[6311799600.0,3560921200.0,2696764000.0,2538189900.0,3708308600.0,6812960800.0,2248948900.0,2194502400.0,7853372300.0,3469037200.0,2535439200.0,2376191200.0,3750770700.0,7934027100.0,2125216700.0,2234066700.0,3526337400.0,3068260800.0,2146263200.0,2341665900.0,3409079100.0,7777107600.0,4608960200.0,2262186500.0,2723926600.0,8049465700.0,2182749300.0,8887809800.0,4263880200.0,3057094600.0,2398819400.0,2991696100.0,3269487500.0,3502949300.0,4129036400.0,10128284500.0,4778140000.0,3303491700.0,2257441900.0,3903247800.0,3478878500.0,2616860900.0,7607378700.0,2405568800.0,2114245900.0,3038175100.0,2313878800.0,2080127800.0,1917411900.0,7065333300.0,2231080100.0,1734771000.0,3118278400.0,6459153900.0,2256889300.0,2184404900.0,2442416500.0,4529675300.0,8127142000.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y2\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"matches\":\"x2\",\"showticklabels\":false,\"rangeslider\":{\"visible\":false},\"showspikes\":true,\"spikethickness\":2},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.3,1],\"showspikes\":true,\"spikethickness\":2},\"xaxis2\":{\"anchor\":\"y2\",\"domain\":[0.0,1.0],\"showspikes\":true,\"spikethickness\":2},\"yaxis2\":{\"anchor\":\"x2\",\"domain\":[0,0.3],\"showspikes\":true,\"spikethickness\":2},\"title\":{\"text\":\"000001.XSHG: \\u4e0a\\u8bc1\\u6307\\u6570\"},\"width\":1000,\"height\":600},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('63686587-0f5a-4c1a-9084-0e30d48c3c83');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go \n", "from plotly.subplots import make_subplots\n", "import numpy as np \n", "\n", "name = await Security.alias(code)\n", "frame = bars['frame']\n", "close = bars['close']\n", "index = np.arange(len(bars))\n", "rise_fall = close-bars['open']\n", "\n", "fig = make_subplots(rows=2, cols=1, specs=[[{}], [{}]], shared_xaxes=True, shared_yaxes=False)\n", "# K线图\n", "fig.add_trace(go.Candlestick(\n", "    x = index, \n", "    close = bars['close'], \n", "    open = bars['open'], \n", "    high = bars['high'], \n", "    low = bars['low'],\n", "    increasing=dict(line=dict(color='red')), \n", "    decreasing=dict(line=dict(color='green')), \n", "    name = 'K线', \n", "    text=frame,\n", "), row = 1, col = 1)\n", "\n", "# 交易量\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall>=0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close>=open成交量',\n", "    marker = dict(color = 'red'),\n", "    text = bars['frame'][rise_fall>=0]\n", "), row = 2, col = 1)\n", "\n", "fig.add_trace(go.Bar(\n", "    x = index[rise_fall<0], \n", "    y = bars['volume'][rise_fall>=0], \n", "    name = 'close<open成交量',\n", "    marker = dict(color = 'green'),\n", "    text = bars['frame'][rise_fall<0]\n", "), row = 2, col = 1)\n", "\n", "fig.update_layout(\n", "    title = (f'{code}: {name}'), width = 1000, height = 600\n", ")\n", "fig.update_yaxes(dict(domain=[0.3, 1]), row = 1, col = 1)\n", "fig.update_yaxes(dict(domain=[0, 0.3]), row = 2, col = 1)\n", "\n", "fig.update_xaxes(rangeslider_visible = False, row = 1, col = 1)\n", "fig.update_xaxes(showspikes = True, spikethickness = 2, \n", "                # rangebreaks = [dict(bounds=[6, 1], pattern='day of week', enabled = True),\n", "                                    # dict(bounds=[11.5001, 13], pattern='hour', enabled = True),\n", "                                    # dict(bounds=[15.001, 9.5], pattern='hour', enabled = True)]\n", "                )\n", "fig.update_yaxes(showspikes = True, spikethickness = 2)\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "df301622", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "toc-autonumbering": false}, "nbformat": 4, "nbformat_minor": 5}