{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from coursea import *\n", "\n", "await init()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import h5py\n", "import numpy as np\n", "from coretypes import FrameType\n", "from pandas import DataFrame\n", "import os\n", "\n", "class H5Store(object):\n", "    def __init__(self, store: str):\n", "        with h5py.File(store, \"a\") as f:\n", "            \n", "        with h5py.File(store, \"w\") as f:\n", "            f.create_group(\"/bars/1d\")\n", "            f.create_group(\"/bars/1m\")\n", "            f.create_group(\"/factors\")\n", "            f.create_group(\"/backtests\")\n", "\n", "            self._store = f\n", "\n", "    @classmethod\n", "    def load(cls, store_path: str)->'H5Store':\n", "        store = H5Store(store_path)\n", "        h5py.File(store, \"a\") as f:\n", "\n", "    def append_bars(self, store: str, frame_type: FrameType, df: DataFrame):\n", "        \"\"\"保存行情数据到store中\n", "\n", "        Args:\n", "            frame_type: k线周期类型\n", "            df: 包含了symbol, frame和OHLC等列的行情数据\n", "        \"\"\"\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 2}