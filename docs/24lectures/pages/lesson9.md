---
theme: ../theme
class: text-center
lineNumbers: false
aspectRatio: 16/9
transition: slide-left
title: 第9课 Numpy和Pandas
layout: cover
---

<!--
今天我们介绍Numpy和Pandas
这两个库是我们做量化量核心的库
它们提供了作为基础的数据结构
和最常用的算法

量化中我们需要使用的数据
多是与时间相关的多维数组
因此，使用numpy或者pandas的DataFrame
来组织它们
就成为最自然的选择
几乎所有的量化库
都选择了numpy或者pandas的DataFrame
来作为数据传输、存储和用以计算的中间结果

除了在量化中广泛应用之外
numpy和pandas也是Python中最核心的库
可以说Python能应用这么广泛
早期全是这两个库带起来的
当然现在主要是人工智能了
人工智能两大框架之一的pytorch
它的底层数据结构称之为tensor
早期其实就是numpy
直到现在，tensor的大多数API
仍然保存着跟numpy一样的名字和几乎一样的用法
比如，我们把一个tensor转为标量
使用的是item()这个方法
我们在numpy中提取标量
用的也是同样的方法

这两个库包含的内容非常之多
我们会讲解跟量化最相关的核心功能
以及在量化中的一些基础功能
比如，计算连续涨停板、
连续多少天阳线
最大回撤
多个标的的行情数据合并等等
总之
这一课的内容
将会几乎是我们在量化中
每天都要用到的知识与技巧。
-->

---
src: lesson9/2.md
title: outline
---

---
src: lesson9/3.md
title: section 1
---

---
src: lesson9/4.md
---
---
src: lesson9/5.md
---
---
src: lesson9/6.md
---
---
src: lesson9/7.md
---

---
src: lesson9/8.md
---
---
src: lesson9/9.md
---
---
src: lesson9/10.md
---
---
src: lesson9/11.md
---
---
src: lesson9/12.md
---
---
src: lesson9/13.md
---
---
src: lesson9/14.md
---
---
src: lesson9/15.md
---
---
src: lesson9/16.md
---
---
src: lesson9/17.md
---
---
src: lesson9/18.md
---
---
src: lesson9/19.md
---
---
src: lesson9/20.md
---
---
src: lesson9/21.md
---
---
src: lesson9/22.md
---
---
src: lesson9/23.md
---
---
src: lesson9/24.md
---
