---
theme: ../theme
lineNumbers: true
aspectRatio: 16/9
transition: slide-left
layout: cover
title: 第21课 策略评估方法
sync: true
drawings:
  presenterOnly: false
---

<!--
sections:

1. 策略评估
2. 指标可视化 - quantstats
-->

<!--
我们在上一节课里，介绍了如何通过backtrader来分析策略的绩效。我们是通过向cerebro添加analyzer或者observer来实现的。但在backtrader那一课中，我们并没有着重介绍这些方法。

原因有二个，第一，backtrader的方法略显复杂。评估一个策略的好坏，往往需要从好多个维度来入手，在backtrader中，我们需要一个个地加入分析器或者观测器，但主流的方法，则是一次输入，同时得到多个指标；并且，受backtrader的绘图框架限制，backtrader对绩效指标的绘制也不是很理想。

尽管backtrader已经给出了通过pyfolio框架来进行绩效分析的方案。但是，pyfolio本身存在着后继无人的问题。

第二，并非只有回测需要进行绩效评估。实际上，绩效评估方法，不仅适用于回测，也适用于实盘；不仅适用于量化策略，也适用于主观策略。因此，绩效评估方法应该独立于回测框架，而是对任何策略、任何阶段都生效。

今天的课程，我们就来补齐这一部分内容。对于回测，我们将向cerebro添加一个指标，即value（每日资产），得到这个数据之后，我们将自己进行各类指标(metrics)的计算，并进行可视化。同样的方法，在得到每日资产数据之后，也一样可适用于主观投资和量化实盘。
-->

---
src: lesson21/2.md
title: outline
---

---
src: lesson21/3.md
title: cerebro
---

---
src: lesson21/4.md
---
---
src: lesson21/5.md
---
---
src: lesson21/6.md
title: Order
---
---
src: lesson21/7.md
---

---
src: lesson21/8.md
---
---
src: lesson21/9.md
---
---
src: lesson21/10.md
---
---
src: lesson21/11.md
---
---
src: lesson21/12.md
---
---
src: lesson21/13.md
---
---
src: lesson21/14.md
---
---
src: lesson21/15.md
---
---
src: lesson21/16.md
---
---
src: lesson21/17.md
---
---
src: lesson21/18.md
---
---
src: lesson21/19.md
---
---
src: lesson21/20.md
title: 可视化
---
---
src: lesson21/21.md
---
---
src: lesson21/22.md
---
---
src: lesson21/23.md
---
---
src: lesson21/24.md
---
---
src: lesson21/25.md
---
---
src: lesson21/26.md
---
---
src: lesson21/27.md
---
---
src: lesson21/28.md
---
---
src: lesson21/29.md
---
---
src: lesson21/30.md
---
