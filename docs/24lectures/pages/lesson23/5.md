---
layout: two-cols
clicks: 10
title: 基础交易指令
---

# 基础交易指令
<hr>

::left::

<show at="0">

## 导入
## 创建和连接客户端 
## 调用交易API实现交易
## 退出客户端
</show>

<show at="1-10">

```python {all|1|4-7|9|10|11|12|13|14|15|17-18}
import easytrader
import os

user = easytrader.use('xq')
cookies = os.environ.get('xq_cookie')
pc = 'ZH3285850'
xq_user.prepare(cookies=cookies, portfolio_code=pc)

print(user.balance)
print(user.position)
print(user.buy('SH601398', volume=100))
print(user.sell('SH601398', volume=100))
print(user.adjust_weight('SH601398', 100))
print(user.adjust_weight('SH601398', 0))
print(user.entrust)

# 如果user是雪球，则不要调用
# user.exit()
```
</show>
::right::
<show at="3">

```
[{'asset_balance': 1000000.0,
  'current_balance': 990000.0,
  'enable_balance': 990000.0,
  'market_value': 10000.0,
  'money_type': '人民币',
  'pre_interest': 0.25}]
```
</show>
<show at="4">

```
[{
    'cost_price': 1308.3069999999998,
    'current_amount': 100,
    'enable_amount': 100,
    'income_balance': 0,
    'keep_cost_price': 1308.3069999999998,
    'last_price': 1308.3069999999998,
    'market_value': 130830.69999999998,
    'position_str': 'random',
    'stock_code': 'SH601398',
    'stock_name': '工商银行'
}]
```
</show>

<show at="5">

```
xqtrader.py 490: weight:0.010000, cash:84.590000
xqtrader.py 507: 调仓 buy工商银行: 200

[{
    'entrust_no': 151311604,
    'init_date': '2023-09-06 22:46:39',
    'batch_no': '委托批号',
    'report_no': '申报号',
    'seat_no': '席位编号',
    'entrust_time': '2023-09-06 22:46:39',
    'entrust_price': 0,
    'entrust_amount': 0,
    'stock_code': 'SH601398',
    'entrust_bs': '买入',
    'entrust_type': '雪球虚拟委托',
    'entrust_status': '-'
}]
```
</show>
<show at="6">

```
xqtrader.py 490: weight:0.010000, cash:84.610000
xqtrader.py 507: 调仓 sell工商银行: 200
```
</show>
<show at="7">

```
xqtrader.py 376: 调仓比例:20.000000, 剩余持仓:77.680000
xqtrader.py 391: 调仓 工商银行: 持仓比例20
xqtrader.py 401: 调仓成功 工商银行: 持仓比例20
```

![](https://images.jieyu.ai/images/2023/09/lesson24-xq-rebalance.png)

</show>

<show at="9">

```
[{
    'entrust_no': 251722622,
    'entrust_bs': '卖出',
    'report_time': '2023-09-05 09:30:21',
    'entrust_status': '已成',
    'stock_code': 'SH601398',
    'stock_name': '工商银行',
    'business_amount': 100,
    'business_price': 4.58,
    'entrust_amount': 100,
    'entrust_price': 4.58
}]
```
</show>



<!--
现在我们就开始介绍如何使用easytrader
# CLK1
我们一般象第一行这样
直接导入easytrader

# CLK2
这几行代码
我们先是通过use方法
决定使用哪一个客户端
在easytrader中

它的客户端既有同花顺通用版(universe_client)

也有国金（gj_client)
华泰（ht_client)等
所以
我们要先通过use方法
选择一种客户端来创建


创建客户端后
我们需要进行登录
这里的代码显示的是雪球的登录方式
它不是通过账号和口令
而是在网页登录后
拷贝cookie过来进行登录的
如果是其它客户端
处理方式都不太一样
请大家自己查看文档


为了这段代码能够运行
这里还涉及到如何获取组合名称和cookie的问题
我们将在下一个slide中讲解

# CLK3

我们通过 balance来获取资产表
这包括总资产、当前现金current_balance
当前市值 mark_value
enable_balance应该是可用（可取现）现金


# CLK4
我们通过 position来获取当前持仓
返回结果是一个List[dict]
包含了当前持仓数(current_amount)
持仓股的证券代码(stock_code)
持仓市值(market_value)
 其它字段
比如价格
与雪球的调仓算法有关
与一般理解的不一样
可以不用理会


# CLK5
我们通过buy方法来下一个买单
右边的输出中
上部分是日志
后面的List是返回值
这里有用的信息只有entrust_no
 init_date
 stock_code等
entrust_no用以撤销请求


# CLK6
这是卖出指令

# CLK7
我们也可以用adjust_weight方法来进行交易
它类似于backtrader中的order_target_percent方法


# CLK8

通过将权重设置为0
我们就实现了对个股的平仓


# CLK9
查询委托我们使用user.entrust
也可以使用user.get_entrust()
查询只返回最后20条委托

在委托结果中
我们看到成交价是真实的


# CLK10
当业务流程完成时
我们调用user.exit退出登录
不过
在使用雪球客户端的情况下
这样做是不必要的



-->
