---
layout: two-cols
clicks: 5
---
# 客户端与服务器交互
<hr>

::left::

<v-clicks>

## POST方法
## 证券代码格式
## Authorization
## CID
## 服务器响应


</v-clicks>

::right::

<show at="3">

```python
import httpx

headers = {
    # token under server_info > access_token
    "Authorization": token
    # acct_id under gm_info > accounts > acct_id
    "Account-ID": acct_id
}

_url_prefix = "http://192.168.100.100:9000/"

```
</show>

<show at="4">

```python {7}
import uuid

r = httpx.post(_url_prefix + "buy", headers=headers, json={
    "security": "000001.XSHE",
    "price": 13,
    "volume": 100,
    "cid": str(uuid.uuid4()),
    "timeout": 1
})

print(r.json())
```
</show>

<show at="5">

```json
{
    "status": 0,
    "msg": "OK",
    "data": {
        "code": "000001.XSHE",
        "price": 0.0,
        "volume": 100,
        "order_side": 1,
        "bid_type": 2,
        "time": "2023-04-04 15:27:38.921555",
        "entrust_no": "0d23bb4e-d81e-4ef2-ab21-c58e0fe6814f",
        "status": -1,
        "average_price": 0.0,
        "filled": 0,
        "filled_amount": 0,
        "eid": "",
        "trade_fees": 0,
        "reason": "[Counter] [EMC_PC]不支持该下单类型",
        "recv_at": "2023-04-04 15:27:38.924565"
    }
}
```

</show>


<!--
现在我们就来介绍gmadaptor的设计理念
以及它暴露出来的接口
了解这部分概念之后
大家可以自己开发gmadaptor的客户端
也可以甚至自行设计emc的交易接口的封装

# CLK1
客户端向服务器请求都是POST方法
这里没有一定之规
有的设计会对restful风格遵守得更好一些
我们为了简单起见
没有完全依照restful风格
大家如果自己设计这样的服务器的话
也建议只使用POST
如果使用了GET请求的话
一些查询请求有可能被缓存
从而不能反映账户最新的状态

# CLK2
EMC接受的股票代码是前缀式
我们来打开一个csv文件看一下
策略下单时
使用的代码有可能不是前缀式
并且交易所代码可能有
可能没有
即使有的话
也可能不同
这些都需要adaptor来进行转换
对gmadaptor来说
我们要求客户端传入格式
是以.XSHE或者.xshg结尾的后缀式
如果策略不是用的这种格式
需要先进行一个简单的转换


# CLK3
客户端需要通过header传入鉴权信息
我们可以在一开始就把它封装好
放在一个dict中
在调用时直接传入进来就好
其中token来自于 server_info > access_token
Authorization是http header的标准字段
一般用来传递鉴权信息
account-id 来自于gm_info > accounts > acct_id
起到区分账户和进行校验的作用

# CLK4
客户端需要给每一个交易带上CID
这是一个惟一的序号
如果命令是会改变状态的
比如买和卖
是一定生成这个cid
这样服务器可以区分并拒绝重复的请求
在这一点上大家要注意
在网络实现上
是有可能出现我们意识不到的重复请求的
那如果是同一笔买入交易
能被重复多次
这可能导致我们的账户损失
所以 服务器会对这些CID进行检验的
以保证这些操作的惟一性
另外，cid也是后面查询命令状态
要必须要提供的字段
要生成CID
可以用第7行所示的代码

# CLK5
注意这里有一个timeout参数
它是指我们从客户端发起请求
到gmadaptor收到这个请求
以及gmadaptor把请求传递给EMC
EMC上报到交易所
并且一直到order执行并最终返回给gmadaptor
不能超过这个timeout
也就是gmadaptor如果在这个超时范围内
拿到执行结果就会立即返回
如果等不到执行结果
比如我们下一个买单
但给的价格太低了
可能等一天都不会有成交结果
那么这种情况下
gmadaptor也会在1秒钟结束时
返回一个结果给客户端
此后客户端可以用CID来继续查询该委托的状态
这样设计是为了避免策略端陷入死等的状态
从而错过其它机会
# CLK6
这就是gmadaptor发回的响应
首先是对状态进行封装
这里不能完全使用HTTP的status
因为有一些比较细致的响应状态
不能跟HTTP status一一对应
所以我们单独给出了服务器状态
和消息描述
这样前端可以根据这样一个状态
更加有针对性地进行设计前端需要响应的各种异常
由于增加了这样两个字段
所以我们真正的成交响应信息
就封装在data这个字段中
这里包含了每笔成交需要的各种信息
如果交易失败
emc也会给一个reason字段
这样我们可以用这个字段
通过查询EMC的文档
知道错误的原因

-->
