---
layout: two-cols
---
# 冒烟测试
<hr>

::left::

## 启动 server

```python
python -m gmadaptor.server
```

## 测试

```python
python -m gmtest %account %token %server %port
```
::right::

![](https://images.jieyu.ai/images/2023/04/started.jpg)

<!--
gmadaptor 自带了多个测试用例
我们可以在安装配置完成后
可以执行这些测试用例
以验证我们配置正确
以及各项权限都开通了
这被称为冒烟测试

要执行冒烟测试
首先我们要打开一个
安装了 gmadaptor 的虚拟环境
通过这个命令来启动一个 server

然后打开另一个命令行窗口
也是进入到安装了 gmadaptor 的虚拟环境
然后运行 python -m gmtest

这里需要我们传入 account 和 token
这个 account
就是我们刚刚这里序号 2 指示的 id
另外我们要传入一个 token
就是这里配置的 token

在测试过程中
将会输出初始账号资金
如果是仿真呢 应该是 100 万
当然我们不应该拿实盘来做测试
如果我们仿真测试通过了
到实盘只需要执行买卖各几手
如果成功基本上也就验证了

这个测试还会打印当前持仓
并执行买、卖各一笔交易
以及输出所有的 entrust 等等信息

-->
