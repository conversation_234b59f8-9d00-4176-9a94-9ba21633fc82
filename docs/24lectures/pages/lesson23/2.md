---
layout: toc
image: https://images.jieyu.ai/images/2023/09/lesson23-outline.png
clicks: 1
---

<show at="0">
<TopLayer at="0" top="12vh" fc="white">

## 券商托管
## 专用客户端代理
### 文件扫单 （EMC Trader)
### API接入 (QMT XtTrade)
</TopLayer>
</show>

<show at="1">

<Ellipse left="60%" top="35%" s="150" />
<Ellipse left="72%" top="20%" s="120" />
<Ellipse left="72%" top="30%" s="120" />
<Ellipse left="72%" top="40%" s="120" />
<Ellipse left="72%" top="50%" s="120" />

<Ellipse left="55%" top="70%" s="150" />
<Ellipse left="68%" top="58%" s="120" />
<Ellipse left="68%" top="64%" s="120" />
<Ellipse left="68%" top="70%" s="120" />
<Ellipse left="68%" top="76%" s="120" />
<Ellipse left="68%" top="82%" s="120" />


<Ellipse left="25%" top="60%" s="150" />
<Ellipse left="10%" top="50%" s="120" />
<Ellipse left="10%" top="56%" s="120" />
<Ellipse left="10%" top="62%" s="120" />
<Ellipse left="10%" top="68%" s="120" />
</show>

<!--
在国内市场上
做商品期货的程序化交易
投资者门槛是比较低的
但A股的程序化交易
从2005年之后
只允许一些合格投资者参与
目前参与的途径大概有这么几种
一是券商机房托管
技术方案有Ptrade
之前还有一创聚宽
不过它刚刚宣布了中止服务的决定
二是通过专有客户端作为代理
实现本地接入
技术方案有掘金的文件扫单
QMT的xttrade API等

考虑到开户的门槛
为了便于大家在正式开户前做一些测试
或者说仿真交易
我们还将介绍一种非正式的接入方案
就是Easy trader
不过，请大家仅用来做仿真交易或者测试

这里也顺便提一下

有一些券商只为windows开发了交易软件
为了照顾只使用mac或者Linux的客户
他们也提供了网页版交易接口
比如东方财富就有
所以在没有开通量化实盘接口之前
我们如果要做仿真交易的话
也可以通过网页API的方式来接入
现在我们一般使用微软的playwright这个python库
来实现对网页的互操作
它使用起来会比seleniunm更容易安装
同时API上设计更合理一些
当然，基于网页版互操作性天生也有速度慢
不稳定的缺陷
不可用于正式投资
只能用于模拟盘
最多是策略正式上线前的
小资金量的仿真 

# CLK1
我们将分两个课时来介绍实盘接入方案
今天我们将介绍Easytrader
它是一个基于键盘鼠标事件模拟的方案
我们的重点将放在如何通过它
来实现雪球的模拟交易

然后我们将介绍东方财富的EMC智能交易终端
它源自于掘金量化平台
实现了基于文件单的扫单交易
我们将介绍这个客户端的基本使用
以及如何进行维护
使得它可以成为一个稳定的
供我们生产使用的交易代理

EMC是基于文件扫单的方式来实现程序化交易的
这样会要求我们把量化策略
生成的交易信号写入到EMC的监控目录中
并且要监视它的输出目录
以得到输出反馈
使用不太方便
我们一般会对它进行一层薄的封装
将其转换成一个网络服务
这样我们的策略
就可以使用我们喜欢的操作系统
比如linux来开发和运行
而不需要绑定在windows上

大富翁对此进行了封装
发布在pypi上
这个包就叫gmadaptor
我们也将介绍这个包的使用
-->
