---
layout: two-cols
title: 服务器模式
---

# 服务器模式
<hr>

::left::

<v-clicks>

## 服务器
## 客户端
</v-clicks>
::right::

<show at="1">

```python
# server.py
from easytrader import server
server.run(port=1430)
```
</show>

<show at='2'>

```python
from easytrader import remoteclient

# 使用客户端类型，可选 yh_client, ht_client, ths, xq等'
client_type = 'ths'
host = '服务器ip'
port = '服务器端口，默认为1430'

ser = remoteclient.use(client_type, host=host, port=port)
ser.prepare(...)

#user.buy(......)
#user.sell(......)
```
</show>


<!--
如果我们要使用easytrader来进行交易
比如同花顺的模拟交易
由于它是基于键鼠模式的
比较容易受到人的操作和其它进程的影响
所以我们需要找一台独立的机器
它上面只允许运行交易客户端
一个带图形界面的进程


此时
我们就要以服务器模式来部署easytrader
这样我们开发和部署策略时
都不会干扰到正在进行的交易


# CLK1
我们应该在这台机器上
通过conda创建一个专属虚拟环境
再在这个环境中
运行上述脚本


# CLK2
在量化策略端
我们不再象之前那样
使用xq.use来创建客户端
现在我们要使用
remoteclient来进行连接
然后通过client type来指定客户端类型
注意
远程服务器上的防火墙需要事先打开


这里可能遇到的问题是
服务器上的easytrader找不到交易客户端
如果出现这种情况
需要看文档
传入客户端路径
本课程只讲雪球的使用
所以不会涉及到这一问题
因为雪球没有自己的客户端
它的操作是基于网络的
实际上
如果我们只用需要来进行模拟交易的话
是没有必须安装交易客户端
也没必要在windows上安装服务器的
这种情况下
策略可以直接调用并连接

-->
