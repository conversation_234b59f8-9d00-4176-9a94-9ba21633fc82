---
layout: default
---
# 测试与撮合配置规则
<hr>

```
    [全部成交]
    000572	full
    000725	full

    [分笔成交] 
    分成两笔：
    000002	lot      	2

    [部分成交] 成交一半
    000001	part
    000004	part
    ...
    [挂单]	只有响应，没有成交
    018014	pending
    020417	pending
    ...

    [拒单]	
    000010	reject
    010609	reject

    [拒绝撤单] 部分成交，不可撤单
    000008	cancel_reject
    000151
```


<!--
最后我们讲一些比较重要的
就是从回测到实盘
我们还是会遇到很多回测遇不到的情况
那么怎么保证我们的策略
能够正确处理这些情况呢
实际上
在我们开发gmadaptor的时候
就首先遇到了这样的问题
比如 如果我们开一个买单
交易所只能撮合成交一半
那么gmadaptor能不能正确处理这种情况
资产表、position及返回的order
是否能正确更新
策略遇到这种情况
它能不能正确处理？
所有这些都需要测试来进行验证

EMC为我们进行这些测试
提供了一个比较好的环境
但这部分好象在它的文档中没有体现出来
这里特别介绍一下
在我们登录仿真模式时
你会注意到
每天登录后
你的持仓表并不是空的
而是已经有了好多支股票
这是EMC每天都会重置这个账户
放一些持仓进去
因此我们可以随时测试卖出交易
而不必为了卖出
先执行一笔买入
因为如果这样的话
我们还必须等到第二天才能执行卖出

除了这些之外
在仿真模式下
EMC还提供了各种配置
它还针对不同的品种
设置了不同的撮合规则


比如象000572这个品种
如果我们提交买入委托
它的响应是全部成交
无论当天的真实成交量是多少
也无论你下单的委买量是多少

而如果是000002这个品种
则仍然是全部成交
但是会分两笔返回结果
这就会考验到我们的策略
以及adaptor能否正确处理这种情况

有的只会成交一半
比如000001这个品种
如果你委买200手
它就只会成交100手

有的则不会成交
只有挂单
比如018014

还有的会直接拒绝

一般来说
我们在量化回测时
不会考虑这么多
因此
即使是代码对这些情况进行了处理
这部分代码能否正确工作
很可能是没有测试过的
我们可以借EMC的仿真来进行测试
确保策略能正确处理这些情况


当然
如果你是像gmadaptor一样
自己对交易功能进行封装的话
就更需要进行这些测试了

所以这是EMC比较好的地方
作为一个生产级的产品
必须要考虑这些


要注意这些仿真撮合规则
EMC是可能随时改变的
所以我们要及时更新这个规则
一般这个规则会在他们的客服群里发布
当我们在回测中发现不对劲
特别是EMC有了版本升级
我们可能都需要找客服要一下这个规则

-->
