---
layout: section
---
# /03 Adaptor
<hr>

<!--

通过前面的配置
EMC智能交易终端已经可以监听文件单了
现在，我们要做的事情
就是将量化策略中的交易指令
翻译成EMC智能交易终端可以理解的格式
并存为CSV文件
放到文件单输入目录下
并从输出目录读取交易指令的结果


翻译工作本身没有太大难度
在它的帮助文档里
有输入输出格式的比较详细的说明和代码示例
我们就不专门介绍

但是 我们无法直接在策略中使用这种文件单
毕竟这些翻译工作还是比较繁琐
不可能每次写策略
都由策略来重复这些工作
因此比较好的方式是将其封装成一个库
供策略来调用

这里，我们介绍一个
我们对EMC智能交易终端文件扫单功能的封装
即trader-gm-adaptor

如果大家要自己去实现
类似的封装库的话
也可以借鉴我们这里
已经走过的路
或者在我们的基础上进一步进行修改
-->
