---
layout: default
clicks: 4
title: Trader-gm-adaptor
---

# Trader-gm-adaptor
<hr>

<show at="0">

## trader-client / trader-gm-adaptor
</show>
<show at="1">

## trader-client / trader-gm-adaptor
## 安装

<Loc at="1" w="100%" left="0" top="45%">

pip install git+https://github.com/zillionare/trader-gm-adaptor --user
</Loc>
</show>
<show at="2-4">

## trader-client / trader-gm-adaptor
## 安装
## 配置

<Loc at="2" left="35%" fc="white">

```yaml
# defaults.yaml
log_level: INFO

server_info:
    port: 9000
    # client 使用这一token来访问 gmadaptor 提供的服务
    access_token : "84ae0899-7a8d-44ff-9983-4fa7cbbc424b"

gm_info:
    fake: false
    # 文件单输出目录
    gm_output: "~/gmadaptor/FileOrders/out"
    trade_fees:
        commission: 2.5
        stamp_duty: 10.0
        transfer_fee: 0.1
        minimum_cost: 5.0
    accounts:
        # 账号名
        - name: fileorder_s01
          acct_id: 1a66e81c-ae5d-11ec-aef5-00163e0a4100
          # 文件单输入目录。东财量化终端将从这里读取文件单
          acct_input: "~/gmadaptor/FileOrders/inputs/fileorder_s01"
```
</Loc>

<Loc at="3" left="50%" w="50%" top="12vh">

![](https://images.jieyu.ai/images/2023/04/**************.png)
</Loc>
<Loc at="4" left="50%" w="50%" top="12vh">

![](https://images.jieyu.ai/images/2023/04/**************.png)
</Loc>
</show>

<!--
这个模块我们把它设计成为一个
基于网络API的交易服务器
它应该跟 EMC 部署在同一台机器上
我们一般推荐使用traderclient
来与它进行交互
当然大家也可以用 HTTP 的方式来跟它进行交互
我们今天也会讲它的HTTP接口
traderclient我们在6~8 那几课中
有简单的介绍
而且文档也在网络上面有
所以我们今天就不讲这个


trader-gm-adaptor一直是内部在使用
还没有发布为pip包
但是
它是开源库
大家可以通过

#CLK1
这个命令来进行安装


#CLK2
在启动trader-gm-adaptor服务之前
我们需要进行一些配置
我们需要在用户目录下
创建gmadaptor/config的目录
将这里的配置文件存进去
并命名为defaults.yaml

这里我们是使用了一个
名为cfg4py的配置管理工具
它可以读取yaml文件
并将其转换成一个python对象
于是我们便可以用属性语法
比如用 cfg.server_info.port来
访问这里的port属性
这样会自动具有代码自动完成
语法查错等功能
因此比其它配置管理模块
功能要丰富一些

配置文件中
port和access_token是用来供客户端
即trader-client访问时使用的


gm_output是文件单输出目录
在下面的accounts配置中
name来自于这张图中

# CLK3

中的序号2所指示的位置 

#CLK4
acct_id则来自于序号3指示的位置 

acct_input是文件单输入目录
也就是
当trader-gm-adaptor接收到来自客户端的指令时
它将把它翻译成为csv文件
存到这个目录里
并且它监视gm_output目录的文件变更
一旦发生变更
就读取这里的文件内容
返回给客户端
-->
