---
layout: two-cols
---
# Bug
<hr>

::left::

### easytrader/xqtrader.py 
#### _search_stock_info
::right::

```python {all|36-39}{maxHeight: '400px'}
def _search_stock_info(self, code):
    """
    通过雪球的接口获取股票详细信息
    :param code: 股票代码 000001
    :return: 查询到的股票 {
            u'stock_id': 1000279, 
            u'code': u'SH600325',
            u'name': u'华发股份', 
            u'ind_color': u'#d9633b', 
            u'chg': -1.09,
            u'ind_id': 100014, 
            u'percent': -9.31, 
            u'current': 10.62,
            u'hasexist': None, 
            u'flag': 1, 
            u'ind_name': u'房地产', 
            u'type': e,
            u'enName': None
        }
    flag : 未上市(0)、正常(1)、停牌(2)、涨跌停(3)、退市(4)
    """
    data = {
        "code": str(code),
        "size": "300",
        "key": "47bce5c74f",
        "market": self.account_config["portfolio_market"],
    }
    r = self.s.get(self.config["search_stock_url"], 
                   params=data)
    stocks = json.loads(r.text)
    stocks = stocks["stocks"]
    stock = None
    if len(stocks) > 0:
        stock = stocks[0]

    if 'enName' not in stock:
        stock['enName'] = ''
    if 'type' not in stock:
        stock['type'] = ''

    logger.info("stock info of %s is %s", code, stock,)
    return stock
```



<!--
在执行一些操作时
我们可能会遇到一个由_search_stock_info
函数引起的错误
0.2.3是两年前发布的
可能由于在其发布之后
雪球更改了接口
导致_search_stock_info返回的数据
少了type和enName两项
这将导致交易函数
如buy sell和adjustweight调用失败


这个错误可能只针对部分股票出现
要修正这个错误
我们需要增加第36~39行

也就是说
雪球在返回的字典中
部分接口中缺少enName和type字段
我们给它补充一个空值就可以了

-->
