---
layout: two-cols
title: 组合名称和cookies
---

# 组合名称和cookies
<hr>

::left::

<v-clicks>

## 组合名称
## cookies
</v-clicks>

::right::

<show at="1">

![](https://images.jieyu.ai/images/2023/09/lesson24-xq-portfolio.png)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/09/lesson24-xq-cookie.png)
</show>


<!--
上一张slide我们讲到
雪球登录时
需要提供组合账号和cookies


在雪球上
一个人可以管理多个组合
实际上是多个资金账户
因此
我们在操作前需要指定其中一个账户进行操作


# CLK1

在使用easytrader操作雪球账户之前
雪球账户必须创建至少一个组合
如果还没有的话
可以通过这里的”创建组合“按钮进行创建


已经存在的组合都排列在这里

这里还显示了这些组合的id
注意它是以中文名的拼音首字母开头的
我们需要将这个组合ID传递到

【back to 5/lin36】
在第5张slide中
第7行
prepare的portfolio_code参数中


# CLK2
我再看cookie怎么获取
当我们登录雪球之后
雪球就会为这次会话生成一个cookie
这个cookie在客户端可以查到
我们在浏览器中
按F12键
打开开发者窗口
在网络 > HTML 下
重刷网页
点击一个项目
此时就能看到 cookie条目
然后找到”请求 cookie“
注意不是”响应cookie“
然后拷贝 xq_a_token即可

最终
我们得到的cookie字符串的格式应该是

【type】

xq_a_token=xxxx
即我们要把截图中的冒号
替换成为等号
再传给prepare方法

-->
