---
layout: two-cols
clicks: 4
title: 自动跟单
---

# 自动跟单
<hr>

<Loc at="4" left="0" top="0" h="100%">

![](https://images.jieyu.ai/images/2023/09/lesson24-jq-simulator.png)

https://www.joinquant.com/algorithm/trade/list?process=1
</Loc>

::left::

```python{all|5,11|8,16|22}
import os
import easytrader

# 初始化雪球
xq_user = easytrader.use('xq')
cookies = os.environ.get('xq_cookie')
pc = 'ZH3285850'
xq_user.prepare(cookies=cookies, portfolio_code=pc)

# 初始化跟踪joinquant
follower = easytrader.follower('jq')

jq_account = os.environ.get("jq_account")
jq_password = os.environ.get("jq_password")

follower.login(user=jq_account, password=jq_password)

backtest = '7dde84fea86cd80dc36e5e7e90ee0691'
url = 'https://www.joinquant.com/algorithm/live/' + \
      f'index?backtestId={backtest}'
    
follower.follow(xq_user, url)
```

::right::

```
follower.py 58: 登录成功
joinquant_follower.py 78: 开始跟踪策略: easytrader

follower.py 202: 策略 [easytrader] 发送指令到交易队列,
    股票: sz000001 动作: buy 数量: 100 价格: 11.42 
    信号产生时间: 2023-09-05 13:44:00

follower.py 262: execute trade cmd for ...
xqtrader.py 490: weight:0.110000, cash:99.880000
xqtrader.py 507: 调仓 buy平安银行: 200

follower.py 335: 策略 [easytrader] 指令(股票: sz000001 
    动作: buy 数量: 100 价格(考虑滑点): 11.42 
    指令产生时间: 2023-09-05 13:44:00) 执行成功, 
    返回: [{
        'entrust_no': *********, 
        'init_date': '2023-09-05 19:32:29', 
        'batch_no': '委托批号', 
        'report_no': '申报号', 
        ...
    }]
```


<!--
easytrader提供了一种跟单功能
它可以让我们跟踪聚宽或者米筐上运行的策略
通过easytrader同步到证券账户上
跟单有什么作用呢？


一些网站上
常常会有收益令人不可思议的组合出现
它背后的原理
可能是某个游资在发出信号
我们当然不能盲目跟随
但作为量化研究员
不妨拿他们作为短期的另类因子
进一步挖掘同概念板块的标的
我想这个原理是比较容易理解的

这个例子
是将我们关注的某个聚宽的策略
同步到雪球组合中
当然你也可以把雪球上
其它人的组合的交易
同步到自己的组合中来


# CLK1

分别生成雪球客户端和聚宽客户端


# CLK2
分别登录两个客户端

# CLK3
我们调用API follow来进行同步
这里的URL来自哪里？


# CLK4
它来自于这里的网页
在这个网页上
有一些正在运行的策略列表
点击进去
这里地址栏里的地址


【back】
就是这段代码（第19行）需要的URL
大家需要登录聚宽后
打开这个网址
就会出现如截图所示的
模拟策略列表
如果没有策略的话
你需要自己创建
或者克隆他人的策略
当然有一些策略需要先付费
再才能克隆


当上述脚本开始运行时
我们只会得到前两行的日志
但一旦被跟踪的策略开始交易
就会输出下面的这些信息
为了调试需要
follow函数还接受一个时间参数
trade_cmd_expire_seconds
允许你把已经发生的操作也立即进行同步
比如
被跟踪的策略是今天早上11时执行过一笔操作
但你启动跟踪是在今天下午的16时
中间隔了5个小时
正常情况下启动跟踪后
我们从日志中看不到任何信息
也不知道操作会不会成功
此时我们可以设置
trade_cmd_expire_seconds
参数为300
即5个小时
这样11时那一笔交易就会马上被同步
这是一种加快调试的手段

另外在非交易时间
我们也是可以对雪球组合进行交易的
实现半夜加仓的效果
不过雪球会等到交易时间再执行这些动作

-->
