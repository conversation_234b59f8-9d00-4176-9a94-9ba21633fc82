---
layout: default
clicks: 4
title: API示例
---

# API示例
<hr>

<show at="0">

## 请求资产表

```python
# 请求资金信息
import httpx
headers = {
    "Authorization": "84ae0899-7a8d-44ff-9983-4fa7cbbc424b",
    "Account-ID": "780dc4fda3d0af8a2d3ab0279bfa48c9"
}

_url_prefix = "http://192.168.100.100:9000/"

def get_balance():
    r = httpx.post(_url_prefix + "balance", headers=headers)
    resp = r.json()
    if r.status_code == 200 and resp['status'] == 0:
        print("\n------ 账户资金信息 ------")
        print(resp["data"])
```
</show>

<show at="1">

## 请求持仓表

```python
def get_positions():
    r = httpx.post(_url_prefix + "positions", headers=headers)
    
    resp = r.json()
    if r.status_code == 200 and resp['status'] == 0:
        print("\n----- 持仓信息 ------")
        print(resp["data"])
```
</show>

<show at="2">

## 限价买入

```python
    r = httpx.post(_url_prefix + "buy", headers=headers, json={
        "security": "000001.XSHE",
        "price": 13,
        "volume": 100,
        "cid": str(uuid.uuid4()),
        "timeout": 1
    })

    print(r.json())
```
</show>

<show at="3">

## 市价买入

```python
def market_buy():
    global buy_entrust_no
    r = httpx.post(_url_prefix + "market_buy", headers=headers, json={
        "security": "000001.XSHE",
        "volume": 100,
        "cid": cid,
        "timeout": 1
    })

    resp = r.json()
    if r.status_code == 200 and resp["status"] == 0:
        print("\n ------ 委买成功 ------")
        print(resp["status"], resp["msg"], resp["data"])
        buy_entrust_no = resp["data"]["entrust_no"]
    else:
        print("委买失败:", r.status_code, resp)
```
</show>

<show at="4">

## 限价卖出

```python
def sell():
    global sell_entrust_no

    r = httpx.post(_url_prefix + "sell", headers=headers, json={
        "security": "000001.XSHE",
        "price": 10,
        "volume": 100,
        "cid": cid,
        "timeout": 1
    })

    resp = r.json()
    if r.status_code == 200 and resp["status"] == 0:
        print("\n ------ 限价委卖成功 ------")
        data = resp["data"]
        print(data)
        sell_entrust_no = data["entrust_no"]
    else:
        print("卖出失败:", r.status_code, resp)
```

</show>

<!--
这里就是gmadaptor的一些API示例
gmadaptor提供了比较全面的文档和示例
这些示例也放到了课件的notebook中
这是从我们的文档生成的pdf文档
我们简单浏览下
这些示例基本上都是
先构建header
定义服务器url
然后通过post命令发出请求
然后再处理服务器响应
如果服务器正常返回
那么http状态码一般为200
gmadaptor给出的状态码一般为0

我们的示例中使用了httpx这个库
它可能是python未来作为http客户端
最有前途的一个库
同时支持了同步和异步语法
一些同学可能比较熟悉requests这个库
它是只支持同步方法的

# CLK1
刚刚是请求资产表
这次是请求持仓
主要是命令变了
变成了positions

#CLK2

这是限价买入
我们通过json字段
把交易用到的参数传递过去

# CLK3
这是市价买入
跟刚刚的方法基本一样
我们增加了出错时打印信息的逻辑

# CLK4
-->
