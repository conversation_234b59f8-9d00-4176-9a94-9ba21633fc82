---
layout: two-cols
clicks: 1
title: 运行和维护
---

# 运行和维护
<hr>

<show at="0">

## windows下的启动脚本

```
    @echo off
    call C:\ProgramData\Anaconda3\Scripts\activate.bat C:\ProgramData\anaconda3
    call conda activate gmclient
    python -m gmadaptor.server
    pause
```
</show>

<show at="1">

## 每日维护

```
    REM kill process
    TASKKILL /F /IM EMCTrade.exe

    REM sleep 5 seconds
    TIMEOUT  /T 5

    REM remove all file orders after process killed

    DEL /Q C:\zillionare\FileOrders\real_input\*.csv
```
</show>

<!--
我们先来看看一个跟维护相关的功能
因为我们一旦进入实盘
就需要很严肃地对待程序的运行
要确保在无人值守的情况下
程序也能正常运行
比如在断电或者机器重启之后
gm-adaptor也要能自动启动起来
这些任务可以通过计划任务
来执行windows的batch脚本来实现

这里我们讲一个
在windows下
要切换到某个conda环境
再运行python脚本
需要的特殊技巧

即使大家不用gm-adaptor
也可能有一些同学需要在windows下
长期运行一些无人值守的任务
也会面临类似的情况
所以有必要介绍下

这个脚本首先是调用
activate.bat这个脚本
并且传入annaconda的安装路径
这实际上是我们常见的从快捷方式
进入annaconda环境所做的事情
这里假设annaconda安装在
c:\programdata\annaconda下
如果conda被安装在别处
我们需要相应地变更路径
第三行是激活安装了gmadaptor的环境
类似于我们在conda环境下
运行conda activate命令
现在我们就可以启动服务器了
最后要注意
这里的pause命令是必须的
如果没有这行命令
那么这个脚本会立即退出
从而我们的gmadaptor服务器也会跟着退出


# CLK1
EMC量化终端有时候不稳定
我们的经验
大概半年可能遇到2~3次问题
当然随着版本的迭代
会变得越来越稳定
不过我们现在通过脚本来维护后
每天自动重启EMC
所以很少遇到问题
也不太清楚这方面的情况了

这段代码是通过定时重启来提高起稳定性
它将退出EMC trader
清除掉输入目录下的所有文件
所以它是和gmadaptor相配合的
gmadaptor会在此之前进行文件归档

我们需要将这段代码存为batch命令
再通过任务计划
定时执行
同时 还需要另一个计划任务
在早上8:30，或者8:15左右启动EMC
-->
