---
layout: section
---
# /01 Easytrader


<!--
easytrader是一个开源软件


它的原理通过模拟键鼠事件来操作券商客户端
从而实现交易功能的交易代理
这种模式有比较大的灵活性
我们很多自动化任务都可以通过类似的方式来完成
因此
学习easytrader
还能给我们带来这方面好处
此外
它还使用了tersseract库来进行OCR识别
这对我们打码
或者将pdf文档转换为文字和表格提供了一个示例
此外
它还演示了如何与聚宽、雪球、米筐等网站的交互


easytrader支持的券商客户端比较多
比如有海通、华泰、国金和同花顺
由于同花顺支持很多券商
因此 easytrader也就支持了大多数券商账户交易
除实盘支持外
它还可以支持同花顺模拟盘及雪球组合


很多人使用easytrader来进行实盘交易
这里要指出
这种做法可能不太合规
在我们这门课里
将只会介绍如何通过它来实现模拟盘交易
以及实现智能跟单
也请大家合法合规地使用这款工具
-->
