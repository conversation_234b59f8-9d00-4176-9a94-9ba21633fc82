---
layout: default
clicks: 5
title: 安装和配置
---

# 安装和配置
<hr>

<show at="0">

![](https://images.jieyu.ai/images/2023/03/20230403154605.png)

<Loc at="0" fc="white" top="55vh" left="35%">

https://emt.eastmoneysec.com/down
</Loc>
</show>

<show at="1">

![75%](https://images.jieyu.ai/images/2023/04/仿真.jpg)

<Loc at="1" w="100%" left="0" top="55vh">

https://emt.eastmoneysec.com/business/business-handing
</Loc>
</show>

<show at="2,3">

![75%](https://images.jieyu.ai/images/2023/04/login.jpg)
<Loc at="3" left="0" padding="20px" top="40%" w="100%" h="150px">

## QQ群 971584613
</Loc>
</show>

<show at="4">

![75%](https://images.jieyu.ai/images/2023/04/20230403200024.png)
</show>

<show at="5">

![50%](https://images.jieyu.ai/images/2023/04/output.jpg?1)
<br>
![50%](https://images.jieyu.ai/images/2023/04/input.jpg?1)
</show>

<!--
EMC只有windows版
下载地址在这里
下载后
可以马上申请一个仿真账号

#CLK1

申请后
记录普通资金账号和密码

#CLK2

然后就可以用仿真账号登录试用
开通实盘需要额外申请
主要门槛是100万初始资金要求


申请可以加入

#CLK3
这个QQ群
按照群文件要求申请

#CLK4

这是登录后的界面
它在顶部有4个tab页
下面带大家看一下真实界面

#CLK5

开通文件扫单之前
需要先对文件单进行一些配置


上面的图是配置文件单输出的
委托的结果会以csv文件格式
输出到这里
进入路径是 量化 > 文件单 > 文件单输出
在4
输出路径处
选择我们存放输出结果的文件目录
在5处
将输出格式选择为csv
它还支持一种数据库格式
在6处
选择自动启动
在7处
把所有的功能都选上

下面的图是配置文件单输入的
配置也差不多
-->
