---
layout: two-cols
title: 安装
clicks: 3
---

# 安装
<hr>

::left::

<v-clicks>

## 券商交易客户端
## easytrader库
## FAQ
</v-clicks>

::right::


<Loc at="1" fc='white' left="0" top="0vh">
注意版本兼容性,可能需要使用老的版本
</Loc>

<Loc at="2" fc='white' left="0" top="0vh">

### 0.2.3/Python 3.5
### conda 专属虚拟环境
### pip install easytrader
### tesseract
</Loc>

<Loc at="3" fc='white' left="0" top="0vh">

### 使用性能较好的物理机
</Loc>

<!--
我们先来介绍安装问题
这个安装分两步
首先是安装交易客户端
然后是安装easytrader这个python库

# CLK1

这里要注意交易客户端的版本选择问题
券商的交易客户端总是在不断更新的
这种更新可能导致界面发生变更
而easytrader有两年没有更新了
这可能导致与easytrader不兼容

因此为了两者能协同工作
有时候我们可能要把券商的客户端
版本锁定在较旧的版本上


交易客户端安装完成后
我们需要找到它的可执行程序文件的路径
并记下来
后面easytrader配置可能需要


# CLK2
交易客户端的安装是一个普通的windows安装
这里我们就不介绍了
下面我们介绍easytrader的安装
当前easytrader最新的版本是0.2.3
建议通过conda构建专属虚拟环境
然后再安装easytrader
从安装文件看
它依赖的python版本是3.6
因此我们可以构建
一个Python 3.6的环境来安装easytrader
此外，它还依赖requests
 flask
 easyutils
 pywinauto
 pillow
 pandas
tersseract
等库
由于python版本比较老
所以建议一定要构建专属虚拟环境
以避免与其它库发生版本冲突

tesseract是一个基于深度学习的自动OCR识别的库
用来打码用
它没有包含在easytrader的依赖声明中
但是会被easytrader调用

# CLK3
easytrader是基于
键鼠模拟原理工作的
它的工作原理是把模拟键盘鼠标事件
发送到某个GUI程序的指定控件（比如按钮）
当它发送事件到某个控件时
该控件会发生响应
往往会跳转到新的状态
这会导致图形窗口重绘
在绘制完成之前
新的状态将无法与easytrader交互
这是导致easytrader速度慢和不可靠的重要因素


要缓解这个问题
需要找一台性能较好的物理机（普通独显更好）
配上较快的网络（有一些等待是在等待网络响应）


此外
尽量找一台机器专门用于easytrader
避免交易客户端被隐藏、失活、遮盖
这些都可能导致easytrader失效
另外
该机器上任何可能弹窗的定时程序
比如windows自动更新
都应该关掉
-->
