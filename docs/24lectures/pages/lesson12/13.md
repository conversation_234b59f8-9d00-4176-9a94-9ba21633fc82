---
layout: two-cols
clicks: 2
class: fade
title: 
---

# 协方差和相关系数
<hr>

::left::

<show at="0">

<Loc color="black">

$$ cov(X, Y) = \frac{1}{n-1}\sum_{i=1}^n(X_i - \mu_X)(Y_i - \mu_Y)
$$
</Loc>

</show>
<show at="1">
<Loc left="15px" top="0">

$$ cov(X, Y) = \frac{1}{n-1}\sum_{i=1}^n(X_i - \mu_X)(Y_i - \mu_Y)
$$
</Loc>

<Loc color="black" left="100%">

$$\rho_{XY} = \frac{cov(X, Y)}{\sigma_X\sigma_Y}$$
</Loc>

</show>

<show at="2">

<Loc left="15px" top="0">

$$ cov(X, Y) = \frac{1}{n-1}\sum_{i=1}^n(X_i - \mu_X)(Y_i - \mu_Y)
$$
</Loc>

<Loc left="15px" top = "10vh">

$$\rho_{XY} = \frac{cov(X, Y)}{\sigma_X\sigma_Y}$$
</Loc>

<Loc color="black" left="100%">

## np.cov
## df.cov
##
## np.corrcoef
## df.corr
</Loc>
</show>
::right::


<!--
协方差描述的是随机变量联合变化程度
通俗一点讲
假设股票B是板块A中的一支股票
那么当板块A上涨时
B也可能上涨
当板块A下跌时
B也可能下跌
这就是联合变化
协方差就是以量化的方式
来定量分析这种联合变化的程度


假设板块A每日涨跌幅记为X
股票B的每日涨跌记为Y
则两者的n日协方差就可以用这个公式来计算
它的求法是
先计算x和y的平均值
然后对每一个取值xi，yi
分别计算它们与对应均值的差值
再将两者相乘
并且求和
最后，除以(n-1)

# CLK1

这个公式计算出来的cov的大小
跟X，Y的大小相关
为了无量纲化
要对其进行标准化
就有了相关系数的概念

相关系数是在协方差的基础上
除以X与Y的标准差的乘积
这样相关系数刚好落在[-1,1]之间

# CLK2

我们可以使用 np.cov来计算协方差
使用`np.corrcoef`来计算相关系数
在pandas中
对应的分别是`df.cov`和`df.corr`

下面
我们就选取一个板块和其中的几支股票
求一下它们的相关系数
并且通过热力图来进行展示

-->
