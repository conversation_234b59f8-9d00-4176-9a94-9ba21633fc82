---
layout: default
class: fade
title: 
---

# 距离和相似性
<hr>

## 距离的定义

<v-clicks>

### 1. 到自己的距离为0:     $d(x,x) = 0$
### 2. 距离非负:            $d(x,y) >= 0$
### 3. 对称性:            $d(x, y) = d(y, x)$
### 4. 三角形法则: $d(x, k)+ d(k, y) >= d(x, y)$
</v-clicks>


<!--
判断两组数据是否相似
需要先定义度量方法


相似性度量方法在多元统计中的聚类分析、机器学习等方面都有应用
随着机器学习在证券分析中的应用越来越广泛
我们也有必要介绍一些入门知识


相似性是通过距离来度量的
如果两组数据之间的距离越大
那么相似性越小
反之
相似性越大
那么距离越小


最常见的距离定义
是欧氏距离
但是
在不同的场合下
相似性有着不同的含义
因此距离也就有着不同的定义


但无论怎么定义距离
都必须满足以下条件

1. 到自己的距离为0
2. 距离非负
3. 对称性
4. 三角形法则

-->
