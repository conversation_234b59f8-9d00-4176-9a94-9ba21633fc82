---
layout: two-cols
clicks: 1
class: fade
title: 
---

# 计算距离
<hr>


::left::

<show at="0">

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

x=np.arange(0,np. pi*2, 0.1)
y1=np.sin(x)
y2=np.cos(x)-2
y3=y1-2

plt.plot(y1)
plt.plot(y2)
plt.plot(y3)
plt.legend(['y1','y2','y3'])
```
</show>

<show at="1">

```python {all}{maxHeight: '400px'}
from sklearn.metrics.pairwise import 
    euclidean_distances,
    cosine_distances, 
    cosine_similarity

from scipy.spatial.distance import euclidean, cosine
import pandas as pd

def corr(x1, x2):
    return round(np.corrcoef(x1, x2).flatten()[1], 2)

def calc_dist(label, x1, x2):
    return [
        label,
        round(euclidean_distances(
            [x1], [x2]).flatten()[0], 2),
        round(cosine_distances(
            [x1], [x2]).flatten()[0], 2),
        round(cosine_similarity(
            [x1], [x2]).flatten()[0], 2),
        corr(x1, x2)]

data = []
data.append(calc_dist("y1->y2", y1, y2))
data.append(calc_dist("y2->y3", y2, y3))
data.append(calc_dist("y1->y3", y1, y3))

columns = "label,eucl,cos_dis,cos_sim,corr".split(",")
df = pd.DataFrame(data, columns=columns)
df
```
</show>

::right::

<show at="0">

![350px](https://images.jieyu.ai/images/2023/06/three_sim_lines.png)
</show>

<show at="1">

![](https://images.jieyu.ai/images/2023/06/dist_results.png)
</show>


<!--
如果我们有这样三条曲线
从视觉上看
我们一般认为`y1`
 `y2`和`y3`都是相似的曲线
y3相当于y1的缩小版本
y2则是在y1的基础上进行了平移
如果我们还有曲线`y4`
假设它是`y1`在平面上进行了90度旋转
我们一般也认为`y4`和`y1`相似
这一点在图像处理领域尤其如此
比如
一张泰迪熊的照片
无论是横着放、还是竖着放
都不会改变它是一张泰迪熊的事实


也就是说

如果一幅图经过平移、放大、旋转
都不改变其语义
这被称为平移不变性
尺缩不变性
和旋转不变性

但是
距离算法并不一定能适应这些操作
即经过这些操作后
距离算法不一定能检测前后两个图的相似性

比如
我们在前面已经验证过了
皮尔逊相关系数作为距离使用时
它是不满足平移不变性的

# CLK1

这段代码分别两两计算了`y1
y2
y3`之间的欧氏距离、
余弦距离、
余弦相似度和皮尔逊距离

由于距离是成对计算的
所以我们是从sklearn.metrics.pairwise中
分别导入了这三种距离的计算函数

计算皮尔逊距离
也就是皮尔逊相关系数
我们在前面的部分已经接触过了

余弦距离和余弦相似度几乎完全相同
1减去余弦距离
就得到余弦相似度

从欧氏距离和余弦距离（相似度）的角度看
都是y2和y3最接近
但从相关性上看
y1和y3相关性很强
但其它两对关系则不相关
总的来说
我们使用的这些相似性检测算法
没有一个能够完全反映出三条曲线实际是都是相似的这一结论


这个结果反映了我们要找出一组证券中
具有相同特点的证券的难度
如果仅仅是要检测股价走势的相似性
可以考虑`fastdtw`[^fastdtw]方法



-->
