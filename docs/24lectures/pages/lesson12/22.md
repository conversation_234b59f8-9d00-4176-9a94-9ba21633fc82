---
layout: two-cols
clicks: 6
class: fade
title: 归一化
---

# 归一化
<hr>

::left::

<show at="1">
<Loc >

$$
    S = \frac{1}{1 + e^{-x}}
$$
</Loc>
</show>

<show at="3">

```python
import matplotlib.pyplot as plt

def sigmoid(x):
    return 1/(1 + np.exp(-x))

x = np.linspace(10, 30, 20)
plt.plot(x, [sigmoid(i) for i in x])
```
</show>

<show at="4">

```python
def sigmoid(x):
    return 1/(1 + np.exp(-x))

print("sigmoid(12) == sigmoid(13)?", np.isclose(sigmoid(12), sigmoid(13)))
```
</show>

<show at="6">

```python {all}{maxHeight: '400px'}
import matplotlib.pyplot as plt

def scaled_sigmoid(x, start, end):
    """当`x`落在`[start,end]`区间时，
    函数值为[0,1]且在该区间有较好的响应灵敏度
    """
    n = np.abs(start - end)

    score = 2/(1 + np.exp(-np.log(40_000)*
            (x - start - n)/n + np.log(5e-3))
            )
    return score/2


fig, (ax1, ax2, ax3,ax4) = plt.subplots(nrows = 1, 
                        ncols = 4, figsize=(12,3))

x = np.linspace(0, 1)
y = [scaled_sigmoid(i, x[0], x[-1]) for i in x]
ax1.plot(x, y)
ax1.set_title("fit (0,1)")

x = np.linspace(0, 100)
y = [scaled_sigmoid(i, x[0], x[-1]) for i in x]
ax2.plot(x, y)
ax2.set_title("fit (0, 100)")

x = np.linspace(18, 38)
y =  [scaled_sigmoid(i, x[0], x[-1]) for i in x]
ax3.plot(x, y)
ax3.set_title("fit (18, 38)")

x = np.linspace(0, 100)
ax4.plot(x, [sigmoid(i) for i in x])
ax4.set_title("fit (0,100) with original")
```
</show>

::right::

<show at = "2">
<Loc w="400px" left="-15vh">

![](https://images.jieyu.ai/images/2023/06/comarison_tanh_sigmoid.png)
</Loc>
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/06/sigmoid_no_tuned.png)
</show>

<show at="5">

<Loc w="100%" left="-20vh">

$$
y = \frac{2}{1 + e^{-\frac{ln(40000)}{b}.(x-b)+ln(0.005)}} - 1
$$
</Loc>
</show>

<show at="6">

![](https://images.jieyu.ai/images/2023/06/scaled_sigmoid.png)

</show>

<!--
现在我们就来介绍
如何解决刚刚的问题
我们要介绍两个函数
sigmoid和tanh

# CLK1

这个函数被称为sigmoid
有时候也叫logistic函数
它的特征是
当x趋向负无穷时
函数取值趋向0
当x取值趋向正无穷时
函数取值趋向1
当x为零时
取值为0.5
在0.5附近
S对X的变化更为敏感


另外一个常用的函数是tanh
我们将两者的变换图对比如下

# CLK2

两者的主要区别在函数值域上
sigmoid取值为(0 1)
而tanh的取值为(-1 1)
另外一个不同之处是
在中心区
似乎tanh上升得更快
不过这些都可以通过变换来消除


另一个比较重要的区别就是在机器学习中
出于性能的考虑
更多时候我们使用sigmoid函数而不是tanh
这是因为对sigmoid函数求导的计算存在简便算法
因此会快很多


直接使用sigmoid函数仍会有很多不便之处
比如
我们以刚刚讲的地量因子为例
我们可能关注的区间在[10,200]之间
超过200的会很罕见

但是
如果我们直接使用sigmoid函数
结果会是这样

# CLK3

这个结果很难说理想
事实上
从x = 12开始
它们的sigmoid值就不再有区分度


从下面的例子可以看出
尽管从数学的角度
sigmoid(12)和sigmoid(13)是不相等的
但在实际的运用中
由于浮点误差的存在
计算机会认为它们是相等的

# CLK4

这个结果大家可以自行验证一下

所以
如果我们使用原始的sigmoid来进行归一化
12个周期以来的地量与200个周期以来的地量将没有任何区别
但实际上
后者出现的频率很低
一旦出现
它的信号意义将很强
但如果我们使用sigmoid来进行归一化的话
我们将丢失这个信号

# CLK5
我们给出这个公式
以对sigmoid函数进行调整
使之能在我们期望的区间内
有较好的响应灵敏度
这里的x是自变量
b是我们要调整的区间的大小
这个公式给出的仍然是一个
零中心的sigmoid
我们在实现中
有时候还要对它进行平移
这里的4万和0.005没有特别的意义
只是用来表示一个较大和一个较小的数值而已

# CLK5
这里我们分别对x取值在
[0,1]
[0,100]
[18,38]等三个区间
进行了测试
并与未经调整的sigmoid进行了对照
从图中可以看出
无论`x`的值域在哪一个范围
调用后的sigmoid函数都能在该区间给出非常好的区分度
-->
