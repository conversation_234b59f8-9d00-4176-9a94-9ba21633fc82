---
layout: two-cols
clicks: 1
class: fade
title: 
---

# 残差及其度量 - mean_absolute_error
<hr>

::left::

<show at="0|1">

```python
from sklearn.metrics import mean_absolute_error
import numpy as np

np.random.seed(78)
y = np.arange(10)
y_hat = y + np.random.randint(-10, 10, size=10)
mae = mean_absolute_error(y_hat, y)

print(y)
print(y_hat)
print(mae)
```
</show>

<show at="1">

<Loc left="2vw" top="100%">
  [0 1 2 3 4 5 6 7 8 9]<br>
  [-5 -1  1 12  8 -2  8  8  2  5]<br>
  4.1
</Loc>
</show>
::right::

<!--
mean_absolute_error是
原序列（真值）与预测值之间的平均绝对值差


要计算这个差值我们从
sklearn.metrics中导入mean_absolute_error
然后我们这里生成了一个真值序列
这边模拟了一个拟合序列
它是在真值序列的基础上
加上了一个负10到正10之间的随机数
然后我们通过mean_absolute_error来计算
两个序列之间的平均绝对值差
最后我们得到的这个结果
他就是两两相减
然后取绝对值最后再平均
这样一个结果大家可以自己验证一下
MAE 不便于在不同的序列之间进行比较
比方说我们对均线进行拟合
我们希望找到当下所有品种当中
如果按向上的直线进行拟合的话
需要找到拟合的最好的那个品种
那这个时候我们就
不能用
MAE来进行比较和筛选
因为不同的标的
它的价格不一样
所以它们的误差值
价格越高的
它的绝对误差值是肯定是越高的
那么为了在高价股跟低价股之间方便进行比较
我们就需要知道
残差对于真值的相对系数
这样就有了MAPE

-->
