---
layout: default
clicks: 10
class: fade
title: 相关系数计算
---

# 相关系数计算
<hr>


```python {all|3,4,9|11|13,14|16-21|25|27,28|29,30|31}{maxHeight:'400px'}
import matplotlib.pyplot as plt

codes = ["300607.XSHE", "300165.XSHE", "300535.XSHE", 
         "603392.XSHG", "603718.XSHG", "002030.XSHE"]

start = datetime.date(2022, 9, 1)  # 起始时间， 可修改
end = datetime.date(2023, 3, 1)  # 截止时间， 可修改

board = "300941" #抗原检测

bbars = await Board.get_bars_in_range(board, start, end)

data = [ bbars["close"][1:]/bbars["close"][-1:] - 1]
names = [board]

for code in codes:
    bars = await Stock.get_bars_in_range(code, FrameType.DAY, start, end)
    
    pc = bars["close"][1:] / bars["close"][:-1] - 1
    data.append(pc)
    names.append(code.split(".")[0])


f = plt.figure(figsize=(19, 15))
plt.matshow(np.corrcoef(data), fignum = f.number)

plt.xticks(range(len(names)), names, fontsize=14, rotation=45)
plt.yticks(range(len(names)), names, fontsize=14)
cb = plt.colorbar()
cb.ax.tick_params(labelsize=14)
plt.title('Correlation Matrix', fontsize=16)
```

<show at="9">

<Loc >

![](https://images.jieyu.ai/images/2023/06/corr_heatmap.png)
</Loc>
</show>

<show at="10">
<Loc left="-20vw" top="10vh" w="150%" padding="50px">

![100%](https://images.jieyu.ai/images/2023/06/stock_trend_compare.png?1)

</Loc>
</show>

<!--
第3、4和第9行，这里分别给出了
个股的代码和板块指数的代码

第11行，获取板块的行情数据
这个方法是omicron的方法

第13行，先将板块的涨跌幅计息出来
存入data数组中
第14行，将板块代码存入names中
后面绘制坐标轴标签时需要

第16到21行， 计算每支个股的涨跌幅
添加到data数组中

第25行，利用matplotlib中的matshow方法
来绘制热力图
第一个参数就是根据data计算出来的相关性数组
算上板块指数，我们总共有7个标的
data数组的size是7
这样求出来的相关性系数会是一个
7*7的矩阵
第二个参数用来指定热力图绘制的figure
fignum这个参数我们现在不用管
在学完matplotlib绘图后就能理解了
现在只要知道通过指定fignum
热力图将是绘制在第24行生成的fig对象上就可以了

第27，28行，我们给热力图坐标轴打上标签
第29，30行，我们给热力图增加一个色条
通过这个色条，我们可以知道
每个色块代表的相关性系数是多少
最后，我们给热力图增加了一个title

# CLK1
在这个热力图中
从色条可以看出
黄色代表相关性最高
系数为1
深蓝色代表负相关
接近-1

每组数据与它自己的相关性都是1
这是显然易见的
因此，300535与板块有较强的正相关性
603392与板块则是强负相关

# CLK2
这是两支个股与板块指数走势对比图
可以看出
相关性确实能刻画股价走势之间的关联度
所以从现在起
我们可以利用这一工具
来给市场中的股票进行归类
这种归类
对于市场涨跌的归因
是重要的一步
-->
