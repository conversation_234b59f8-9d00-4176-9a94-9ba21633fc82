---
layout: two-cols
clicks: 3
class: fade
title: 分位图
---

# 分位图
<hr>

::left::

<show at="0">

```python {all}
import matplotlib.pyplot as plt

fig, axes = plt.subplots(nrows=1,
                        ncols=3, 
                        figsize = (9, 3)
                        )
ax1, ax2, ax3 = axes

n = 1000
X = sorted(stats.norm.rvs(size=n))
Y = sorted(stats.norm.rvs(size=n))

ax1.scatter(np.arange(n), X, s=1)
ax1.text(100, 3, "X")
ax3.scatter(np.arange(n), Y, s=1)
ax3.text(100, 2.8, "Y")

ax2.scatter(X, Y, s=1)
ax2.plot(X,X, color='orange')
ax2.text(-2, 3, "y = x")
```

</show>

<show at = "1">

```python
fig, axes = plt.subplots(nrows=2, 
                        ncols=4, 
                        figsize=(16,8))
axes = axes.flatten()

N = (10, 20, 40, 80, 160, 320, 640, 1280)
for i, n in enumerate(N):
    X = stats.norm.rvs(size=n)
    Y = stats.norm.rvs(size=n)
    
    ax = axes[i]
    ax.scatter(sorted(stats.zscore(X)), sorted(Y))
    ax.plot(X, X, '--', color='grey')
```
</show>

<show at="2">

<br>
<br>

## 非标准的正态分布
</show>
<show at="3">

```python {all}{maxHeight: '400px'}
import matplotlib.pyplot as plt
from scipy import stats
import numpy as np

np.random.seed(318)

fig=plt.figure(figsize=(12,3))

ls = stats.skewnorm(-5).rvs(size=1000)
zs = stats.norm.rvs(size=1000)
rs = stats.skewnorm(5).rvs(size=1000)

x = np.arange(-5, 5)
dist = ['left-skew', 'zero-skew', 'right-skew']
for i, (name, data) in enumerate(
                    zip(dist, [ls, zs, rs])):

    ax = fig.add_subplot(1,3,i+1) 
    mu = np.mean(data)
    sigma = np.std(data)
    y = sigma * x + mu 
    plt.plot(x,y) #绘制截距为mu,斜率为sigma的直线
 
    xs = stats.norm.rvs(loc=0,scale=1,size=len(data))
    xs.sort()
    data.sort()
    
    plt.scatter(xs, data, color='r')
    plt.title(name)
    
plt.show()
```

<Box d top="38%" left="5%" h="70px" w="400px"/>
</show>

::right::

<show at="0">

</show>

<show at="0">

![](https://images.jieyu.ai/images/2023/07/lesson12-qq-plot-0.png)
</show>

<show at="1">

![](https://images.jieyu.ai/images/2023/07/lesson12-qq-plot-4.png)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson12-qq-plot-3.png)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/06/qq-three-skew.png)
</show>

<!--
我们先来看如何通过图形进行统计推断
常用的方法是Q-Q图
Q-Q图 quantile-quantile plot
又称分位图
在统计学中
是通过比较两个概率分布的分位数
来比较这两个概率分布的一种方法


Q-Q图是基于这样一个原理：

假设X是一个实数集
那么以[X,X]自己为坐标的所有点
都会落在 y = x
即一条过零点
45度角向上的直线上
这条直线
就是中间这个图的黄线部分

如果X是随机变量
Y是它的理论分布
那么对X Y的n次抽样结果
进行排序后得到的 X，Y
分别以这两个数组为纵横坐标的点
也应该落在 y = x 这条直线附近
因为有随机性
所以不可能完全相等

这里我们先以标准正态分布为例
绘图进行说明

我们对标准正态分布
分别作两次size = 1000的取样
第一次记为 X
假定它来自随机样本
第二次记为Y
是对理论分布的一次采样
然后我们对这两个数组进行排序
分别作图
于是我们就得到了右边这张大图

左图和右图都是索引为横坐标
就是0到1000
纵坐标则是抽样数据
这样绘制的散点图
中间的图则是以y=x绘制的一直线
以及以两个抽样数据集的
纵横坐标绘制的散点图

从视觉上看
左图与右图几乎一致

除了开始入和结束处

我们知道
在这些地方分布已经很稀少了
个别离群值可以忽略
中图反映了以[x, y]为坐标的点
基本上都会落在直线两侧


这里我们需要解释一下
为什么对属于同一分布的 X 和 Y
进行排序后
对应位置的元素应该大致相等
这就需要引入分位数的概念

假设我们有随机变量 X
服从标准正态分布
那么在 50%分位处
X 的采样点xi应该非常接近于 0
因为标准正态分布
在 50%分位处的取值就是 0
由于X是随机变量
所以在50%分位处它也很难完全等于0

推广到 25%分位处
75%分位处
X 在这些分位数上的取值
也应该与标准正态分布取值非常接近
如果我们推广到任意分位数处
两者的采样值都应该非常接近


因此
按照分位数分别对 X 
和标准正态分布进行采样
再进行绘图
这样的图形就是一个
分布在 45 度直线两侧的散点图


在实际绘图中
我们会简化上述算法
不进行分位数计算
而是以排序进替代
将 X 与 Y 进行排序后
如果样本总数是 N
那么排序后的第 i 个元素
就被认为是第 i/N 分位数处的随机变量取值
如果第 i 个元素处的 Xi 与 Yi 非常接近
那么点 （Xi, Yi) 将落在直线  Y = X 附近
也就是 45 度角的直线上


这种简化
在 n 值较大时
是完全有效的
但在 n 值较小时
则有可能出现较大偏差

[CLK1]
这段代码中抽样数从n=10
增加到n=1280
演示了n较小时
可能会出现较大偏差

[MARK]
但在n=320以后
基本上大多数点就都分布在对角线两侧了

[CLK2]
我们刚刚讨论的是
X属于标准正态分布的情况
如果X是任意正态分布的情况
也就是均值mu不为零
方差不为1的情况
此时对[X Y]进行绘图
也仍然会得到一个沿直线两侧分布的散点图
只不过这条直线的方程是 y = (x - mean) / std
我们也可以事先对X进行zscore化
这样点就会仍然分布在 y=x 直线两侧


这就是利用QQ图进行正态性检验的原理
并且
如果样本曲线与直线不重合
我们还有可能得出它是左偏、还是右偏的结论


下面
我们就分别生成左偏、无偏和右偏数据集各一份
绘制它们的QQ图


[CLK3]
注意这段代码中使用了skewnorm和norm来分别生成有偏正态分布和标准正态分布

可以看出
如果样本分布是正态分布的话
它会呈现为一条直线
如果是左偏的话
它可能是一条向下拐头的曲线
如果是右偏
则可能是一条微笑曲线
-->
