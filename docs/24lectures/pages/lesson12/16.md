---
layout: default
clicks: 5
class: fade
title: 
---

# 斯皮尔曼相关性计算
<hr>

```python {all|3}
from scipy import stats

corr, pvalue = stats.spearmanr(data, axis=1)

f = plt.figure(figsize=(9, 7))
plt.matshow(corr, fignum = f.number)

plt.xticks(range(len(names)), names, fontsize=14, rotation=45)
plt.yticks(range(len(names)), names, fontsize=14)
cb = plt.colorbar()
cb.ax.tick_params(labelsize=14)
plt.title('Correlation Matrix', fontsize=16)
```

<show at="2">
<Loc w="100%" left="-5vw" padding="100px" top="15vh">

## df.corr(..., method = 'pearson | spearman | kendall')
</Loc>
</show>

<show at="3">

<Loc w="100%" left="-5vw" top="15vh" padding="100px">

```python
stats.spearmanr(
    a,
    b = None,
    axis = 0,
    nan_policy = 'propagate',
    alternative = 'two-sided'
)
```
</Loc>
</show>



<!--
在numpy中没有计算皮尔逊相关性的函数


# CLK1
这次我们将使用`scipy.stats.spearmanr`来计算相关性


# CLK2

如果数据已经是DataFrame格式
也可以使用`df.corr方法来计算
通过method参数来指定计算方法
这里`method`可以是空
此时选择 pearson
或者`spearman`和`kendall`

# CLK3

这是`stats.spearmanr`方法的签名
a和b是待检验相关性的数据
如果`a`和`b`是二维的数组
则`axis`有意义
在示例中
由于我们的观察数据是按列存放的（即每一行是一组数据）
所以这里指定`axis=1`
在`b`没有指定的情况下
相关性将在`a`和`a.T`之间进行计算

`spearmanr`方法返回相关性系数矩阵和`pvalue`
后者即置信度


`spearmanr`的原假设（null hypothesis）是
两组数据是**非线性相关** 
因此
当p值小于5%时
我们拒绝原假设
即可以认为两组数据是线性相关的


`spearmanr`要求观察数据至少在50个以上
我们的示例中
观察值是118个
满足条件
如果观察数据不足50
scipy的建议是改用`stats.permutation_test`
-->
