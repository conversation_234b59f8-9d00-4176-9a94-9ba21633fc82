---
layout: default
clicks: 6
title: 
---

# 常见距离定义
<hr>

<v-clicks>

## 1. 欧氏距离
## 2. 余弦相似度
## 3. 马氏距离
## 4. 编辑距离
## 5. 杰卡德距离
## 6. 皮尔逊相关系数

</v-clicks>


<!--
欧氏距离
这是最直观的一种距离
即两点之间的直线距离
余弦相似度
通过求两个向量之间的夹角来定义其相似度
如果两个向量完全相似
则夹角为零
早期在计算文本相似度上有较多应用

马氏距离是基于样本分布的一种距离

编辑距离
是指两个字串之间
由一个转换成另一个所需的最少编辑操作次数
在软件中常用此方法来查找文章中的拼写错误
如果一个词不在词库里
但又与词库中的某个词在编辑距离上很接近
则可能判断这里出现了拼写错误

杰卡德距离 用来判断两个集合之间的距离
可用以推荐算法中

皮尔逊相关系数
形式上类似于余弦相似性


-->
