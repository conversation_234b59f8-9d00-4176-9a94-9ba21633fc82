---
layout: two-cols
clicks: 9
class: fade
title: 
---

# 回归分析
<hr>

::left::

<show at="0|1">

## 一元一次

$$ y = ax + b $$

</show>
<show at = "1">
<Loc top="0vh" left="75%" w="100%">

![](https://images.jieyu.ai/images/2023/08/lesson12-lr-1.png)
</Loc>
</show>

<show at="2">

## 多元一次

$$
y = a_0 + a_1x_1 + a_2x_2 + a_3x_3 + ...
$$
</show>
<show at = "3">

## 多元多次

$$
y = a_0 + a_1x_1 + a_2x_2 + a_3x_3 + ... \tag 1
$$

<Arrow x1="250" y1="120" x2="250" y2="80" />

<br>
<Arrow x1="250" y1="200" x2="250" y2="160" />

$$x_n = f_n(x) \tag 2$$ 

<br>

$$
y = a_0 + a_1x +a_2x^2 + a_3x^3 + ... \tag 3
$$

</show>

<show at="4">

![](https://images.jieyu.ai/images/2023/08/lesson12-lr-2.png)
</show>

<show at="5">

```python
import numpy as np
import plotly.express as px
np.random.seed(78)

y = np.array([ x**2 -5 for x in range(-10, 10)]) + np.random.random(20) * 10

coff_1 = np.polyfit(x = np.arange(-10, 10), y = y, deg=1)
coff_2 = np.polyfit(x = np.arange(-10, 10), y = y, deg=2)

f1 = np.poly1d(coff_1)
f2 = np.poly1d(coff_2)

px.line(
    {"y_fit_deg2": f2(np.arange(-10, 10)),
     "y_fit_deg1": f1(np.arange(-10, 10)),
     "y": y
    })
```
</show>

<show at="6">

```python
def np.polyfit(x, y, deg, rcond=None, full=False, w=None, cov=False):
    pass

 >>> np.polyfit(np.arange(10), 2 * np.arange(10) + 3, deg = 1, full=True)

(array([2., 3.]),
 array([3.0460508e-29]),
 2,
 array([1.35754456, 0.39632407]),
    2.220446049250313e-15)

>>> np.polyfit(np.arange(10), 2 * np.arange(10) + 3, deg = 1, full=False, cov=True)

(array([2., 3.]),
 array([[ 4.61522848e-32, -2.07685282e-31],
        [-2.07685282e-31,  1.31534012e-30]]))
```

</show>

<show at="7">

```python {all}{maxHeight: '400px'}
arr = np.array([95.48, 82.81, 66.99, 52., 
                40.65, 24.44, 19.31,  
                6.47, -0.5 ,0.87,  
                1.5 ,  5.21,  6.39,  
                6.47, 16.18, 26.64, 
                33.93, 52.74, 67.83, 81.6 ])

coff_1 = np.polyfit(x = np.arange(-10, 10), 
                    y = y, deg=1)
coff_2 = np.polyfit(x = np.arange(-10, 10), 
                    y = y, deg=2)

f1 = np.poly1d(coff_1)
f2 = np.poly1d(coff_2)

y_hat1 = f1(np.arange(-10, 10))
y_hat2 = f2(np.arange(-10, 10))

px.line({"y_fit_deg2": y_hat2),
     "y_fit_deg1": y_hat1),
     "y": y
    })
```
</show>

<show at="8">

```python
from sklearn.metrics import max_error, mean_absolute_percentage_error, mean_squared_error

y = np.array([ x**2 -5 for x in range(-10, 10)]) + np.random.random(20) * 10

coff_1 = np.polyfit(x = np.arange(-10, 10), y = y, deg=1)
coff_2 = np.polyfit(x = np.arange(-10, 10), y = y, deg=2)

f1 = np.poly1d(coff_1)
f2 = np.poly1d(coff_2)

yhat2 = f2(np.arange(-10, 10))
yhat1 = f1(np.arange(-10, 10))

pos2 = np.argmax(abs(y - yhat2))
print(f"max error between yhat2 and y: {pos2}, {max_error(y, yhat2):.2f}")

pos1 = np.argmax(abs(y -yhat1))
print(f"max error between yhat1 and y: {pos1}, {max_error(y, yhat1):.2f}")

print(f"mape between yhat2 and y:{mean_absolute_percentage_error(y, yhat2):.2%}")
print(f"rmse between yhat2 and y:{mean_squared_error(y, yhat2, squared=False):.2f}")
```
</show>

<show at="9">

## 1. 寻找支撑线
## 2. 寻找压力线
## 3. 通过均线判断股价的长期趋势
## 4. 通过均线判断股价的动态特征
</show>
::right::

<show at="7">

![](https://images.jieyu.ai/images/2023/06/fit_and_residual.png)
</show>

<show at="9">

![](https://images.jieyu.ai/images/2023/08/lesson12-resist-line.png)
</show>


<!--
回归分析很多时候专指线性回归
但从sklearn来看
至少有十几种已知的回归模型


在讲线性回归时
我们要知道
在不同的领域、文章和软件实现中
线性回归的概念可能不尽相同
狭义地来讲
它是指一元一次回归

# CLK1

这是惟一一种无须变换
即可在视觉上呈现出线性相关性的回归
或者多元一次线性回归

# CLK2

它的”回归线“将是一个超平面
这里我们就不展示了

# CLK3

广义来讲
一元多次或者多元多次也是线性回归的一种
尽管它们从图形上看
并不呈现出线性的特征
但我们可以通过式子2
将其变换成式子1
从而从形式上
它也是一种线性回归
这个变换
在sklearn中可以通过pipeline来实现


甚至更广义地来讲
我们有时候也会看到这样的定义
只要系数不出现在自变量函数体内
都可以看成线性回归


在本节中
我们主要以技术形态分析为例
讨论一元一次和一元多次线性回归的实现
及其运用


比如有以下股价序列

# CLK4

我们想知道这个序列未来会如何演绎
我们可以分别对它进行一次曲线和二次曲线拟合
然后根据拟合后的模型
假设趋势不变
这样我们就可以推测未来的价格


# CLK5 

在这段代码中
我们通过`np.polyfit`来进行多项式拟合
具体是进行线性拟合
还是二次项拟合
取决于`deg`这个参数
我们来看看`np.polyfit`这个方法的签名：

# CLK6

x,y对应于图上的横纵坐标
或者说，自变量、因变量
`deg`给出待拟合多项式的幂次
注意
幂次越高
拟合残差就越小
但这样越容易引起过拟合
趋势就越不稳定
对未来的预测没有任何意义
一般我们用到2次就足够了


`full`这个参数默认为False
此时只返回拟合后的系数项
当它为True时
还将返回残差等数据


第7到第10行就给出了当full为True时
返回结果的第2到第5项

当`cov`为True时
还将返回样本与拟合数据间的协方差矩阵
这就是第15行第16行的输出
关于协方差
我们在今天后面的课程里
还将进一步说明

怎么使用拟合的结果来进行预测呢？

# CLK7
在得到系数项之后
我们将它传给`np.poly1d`
就生成了一个新的多项式函数
当我们传入自变量`x`后
就生成了对应的y_hat
我们将原序列与两种拟合生成的结果对照绘图

直观上看
二次曲线拟合的较好
不过我们还需要得到数据的验证



直观上看
上述数据更贴近某个二次曲线
现在
我们通过残差的概念
来检查一下
两次拟合的具体情况
现在
我们通过残差的概念
来检查一下
两次拟合的具体情况


# CLK 8

这里我们分别计算了
max_error
mean_absolute_percentage_error
mean_squared_error

二次曲线拟合与原序列的 `max_error` 为5.24
发生在第0个元素上
一次曲线拟合与原序列的 `max_error` 为56.31
发生在第19个元素上
这个数据上看
二次曲线的拟合结果更好
但能否让人满意？


但是
拟合值与原曲线之间的相对误差达到了71.57%
标准差达到了2.76
这说明拟合曲线与原曲线实际上也不能较好地拟合

这些输出的具体内容
大家可以在notebook中运行
看一下结果

这里提一下这两种拟合的作用

# CLK9

在k线图中
我们常常把一些重要的顶点连线起来
将它的延伸线作为压力线
反之
一些重要的底部的连线
我们将它的延伸线当成支撑线

这个图，显示2023年8月3日
沪指的5日均线图
我们通过程序自动捕捉它的顶点
进行拟合连线
可以看出
有时候确实存在这样的压力线

一些长周期的均线往往能进行较好的一次曲线拟合
这对我们判断它的整体趋势是有益的
一般情况下
我们应该避开整体向下的标的
短周期的均线则有可能拟合成二次曲线
这对我们判断它的动量特征
发现短期是否可能反转有一定帮助


-->
