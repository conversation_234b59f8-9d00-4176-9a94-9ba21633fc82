---
layout: two-cols
class: fade
title: 
---

# 皮尔逊相关和斯皮尔曼相关
<hr>

::left::

<Loc >

$$
 r_s =
 \rho_{\operatorname{R}(X),\operatorname{R}(Y)} =
 \frac{\operatorname{cov}(\operatorname{R}(X), \operatorname{R}(Y))}
      {\sigma_{\operatorname{R}(X)} \sigma_{\operatorname{R}(Y)}}
$$
</Loc>

::right::


<!--
上一张slide中
我们计算出来的的相关系数
称作皮尔逊相关系数

皮尔逊相关性的适用条件比较严格
它要求，
两个随机变量都要服从正态分布
数据至少在逻辑范围内是等距的
由于金融数据分布往往只是近似于正态分布
所以在出现尾部风险时
这种相关性计算方法就不能用了


另外
有一些场合
数据取值并不是等距的
比如
近期收益回报前3的股票
第1名与第2名的收益差
与第2名和第3名的收益差
往往是差距比较大
甚至这个差别是对数级的
这种情况下
也不满足皮尔逊相关性条件
这就需要使用斯皮尔曼相关性


Spearman是一种秩相关
是用来度量
两个连续型变量之间单调关系强弱
的相关系数
取值范围也是 [−1，1]
它的定义是
在没有重复数据的情况下
如果一个变量是另外一个变量的严格单调函数
则 Spearman 秩相关系数就是 1 或 −1
称变量完全 Spearman 秩相关


在因子分析中
Spearman相关更常用
基于spearman相关的因子IC
称作Rank-IC
现在越来越倾向于认为RANK-IC的鲁棒性更好

在实现上，秩相关是将两个变量的样本值
按大小顺序排列位次
以各要素样本值的位次代替实际数值
而求得的一种统计量
排序不论从大到小还是从小到大排都无所谓
只要保证大家排序的标准一致即可


下面
我们仍以上面的板块和股票为例
来计算Spearman相关系数

-->
