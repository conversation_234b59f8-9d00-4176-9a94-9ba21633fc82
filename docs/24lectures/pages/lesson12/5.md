---
layout: default
clicks: 8
class: fade
title: 假设检验
---

# 假设检验
<hr>

<show at="0">

## 正态分布的快速检验

```python
>>> from scipy.stats import normaltest
>>> import numpy as np

>>> np.random.seed(78)

>>> data = np.random.normal(size=1000)

>>> s, p = normaltest(data)
>>> print(p)

0.97114
```
</show>

<show at="1">

## K-S假设检验

```python
kstest(rvs, cdf, args=(), N=20, alternative='two-sided', method='auto')
```

<Box top="40%" left="13%" w="10%"/>
<Box top="40%" left="20%" w="10%"/>
<Box top="40%" left="38%" w="10%"/>

</show>

<show at="2|3">

```python {all}{maxHeight: '350px'}
from coursea import *
await init()
import scipy
import matplotlib.pyplot as plt
import pandas as pd

import warnings
warnings.filterwarnings('ignore')

bars = await Stock.get_bars("000001.XSHG", 1001, FrameType.DAY)
close = bars["close"]

pct = close[:-1]/close[1:] - 1

dist_names = ['burr12', 'dgamma', 'dweibull', 'fisk', 'genhyperbolic', 
              'genlogistic', 'gennorm', 'hypsecant', 'johnsonsu', 
              'laplace', 'laplace_asymmetric', 'logistic', 'loglaplace',
              'nct', 'norminvgauss']
xmin, xmax = min(pct), max(pct)


# for name in dir(scipy.stats):
#     dist = getattr(scipy.stats, name)
#     if not isinstance(dist, scipy.stats.rv_continuous):
#         continue
        
dist_pvalue = []
for name in dist_names:
    dist = getattr(scipy.stats, name)
    if getattr(dist, 'fit'):
        params = dist.fit(pct)

        ks = scipy.stats.kstest(pct, name, args=params)
        dist_pvalue.append(round(ks.pvalue, 2))
        
df = pd.DataFrame({
    "name": dist_names,
    "pvalue": dist_pvalue
})

df.sort_values("pvalue", ascending=False).transpose()
```
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/06/sh_kstest_result.png)
</show>

<show at="4">

```python
from scipy.stats import genhyperbolic

params = genhyperbolic.fit(pct)
rv = genhyperbolic(*params)

fig, ax = plt.subplots(1,1)
x = np.linspace(rv.ppf(0.01), rv.ppf(0.99), 100)
ax.plot(x, rv.pdf(x), label = 'genhyperbolic pdf', color="#EABFC7")

ax2 = ax.twinx()
_ = ax2.hist(pct, bins=50)
```
</show>

<show at="5">

![50%](https://images.jieyu.ai/images/2023/06/sh_histo_pdf.png)
</show>

<show at="6">

<Loc left="0">

![](https://images.jieyu.ai/images/2023/06/sh_histo_pdf.png)
</Loc>

<Loc left="55%" top="15vh">

```python
>>> rv.cdf(-0.04)

0.0016
```
</Loc>

</show>

<show at="7">

```python
import scipy.stats as st
import numpy as np
import matplotlib.pyplot as plt

bars = await Stock.get_bars("000001.XSHG", 1000, FrameType.DAY)
close = bars["close"]

pct = close[1:]/close[:-1] - 1
hist = np.histogram(pct, bins = 100, density=False)
hist_dist = st.rv_histogram(hist, density=False)

x = np.linspace(hist_dist.ppf(0.001), hist_dist.ppf(0.999), 100)

fig, ax1 = plt.subplots()

ax1.plot(x, hist_dist.cdf(x), color='#4A73A2')

ax2 = ax1.twinx()
ax2.plot(x, hist_dist.pdf(x), color="#B297D5")

print(f"当上证下跌4%以后，继续下跌的概率是: {hist_dist.cdf(-0.04):.2%}")
```
</show>

<show at="8">

![50%](https://images.jieyu.ai/images/2023/06/下跌0.04后.png)

<Loc top = "90%" left="30%">
当上证下跌4%以后，继续下跌的概率是: 0.4%
</Loc>
</show>


<!--
前一节讲的方法
可以帮我们快速建立起一个直觉
但要更精确地描述
两个变量间分布的相似度
我们需要借助假设检验方法

我们先来看
如何快速检验
一个分布是否为正态分布
我们需要使用scipy.stats中的
normaltest方法

我们先是通过random.normal
生成了一组随机数
然后对它进行normaltest
输出结果中的p值接近1
远大于显著性水平0.05
表明我们生成的样本`data`服从正态分布
当然这个结果是显然易见的

这里的normaltest是假设检验的一种
用来判定一组样本是否服从正态分布

# CLK1

统计检验的方法很多
这里介绍下比较常用的K-S检验
K-S是一种非参数检验
用来检验一组样本
是否来自某个概率分布
这被称作 one-sample K-S test
或者用来比较两组样本的分布是否相同
这被称作 two-sample K-S test

K-S检验的名字
来源于它的两个提出者
是两名俄国的统计学家

我们可以通过`scipy.stats.kstest`来执行k-S检验
该方法的签名如下：

这里`rvs`参数的类型可以是`str`
 `array_like`或者`callable`
当它的类型为`str`时
必须是`scipy.stats`支持的某种分布的名字
如果类型是`callable`
则要求它能生成随机变量
如果类型为`array_like`
则必须为随机变量观测值


`cdf`具有与`rvs`一样的参数类型要求和解释
如果`cdf`是观测值的一维数组
则`kstest`将执行 two-sample test
此时`rvs`也必须为同样类型


如果`rvs`或者`cdf`需要参数
通过`args`参数传入


`N`是用来生成`rvs`的数组大小
缺省为20


返回结果为一个`KstestResult`类
它包括`statistic`
 `pvalue`等重要属性

# CLK2

现在
我们就通过`kstest`
对上证指数
运用`scipy.stats`中已实现的分布模型
逐一进行 One-Sample test
看看会不会有匹配上的

第10到第13行
我们先计算出上证指数的每日涨跌
这里总共是取了1000个抽样

第15行
我们打算对这些分布进行测试
这里的分布只是stats包中的一部分
我们也可以象第22-25行一样
对其中的每一种都进行遍历
但是有一些分布

在这项测试上耗时太长
所以我们就只取了部分

第29行
我们通过名字获取分布模型
第30行
我们检查该分布中是否存在'fit‘这个方法
如果存在
我们以选定的模型和给出的抽样数据
进行一个拟合
这将得到一些分布参数
第33行
我们运行kstest
我们传入抽样数据
分布名称和已经得到的参数
最终我们把每个测试得到的p值保存下来
对生成的dataframe进行排序

这样我们得到的结果是

# CLK3
看起来上证指数
与`genhyperbolic`（广义双曲分布）
匹配度最高
这边越往右
p值是越小的
我们通过绘图来检验一下这个结论

# CLK4

在这段代码中
我们先是通过第3行
对沪指抽样进行一个拟合
得到一组特征参数
然后构建了广义双曲分布对象 rv
然后通过第7行
我们取它ppf 0.01
和ppf 0.99的之间的线性空间
构成x轴
这是我们绘制这一类图形常用的方法
第8行
我们以pdf(x)为y值
绘制pdf图
然后在第二个y轴上
绘制它的直方图
这里直方图来自数据本身
pdf则是按广义双曲分布拟合出来的分布
如果沪指确实服从广义双曲
那么我们应该看到
pdf曲线与直方图外边缘相重叠

接下来我们就看看绘制结果是不是这样
# CLK5

结果表明
沪指确实与广义双曲分布吻合得较好
现在
我们能回答这个问题了：
当上证下跌4%之后
还会继续下跌的概率为多大？
我们可以通过`rv`对象的`cdf`方法来获得

# CLK6

我们算出来的结果是
继续下跌的概率仅为0.16%
所以
下次遇到这样的大跌

是不是应该勇敢地抄底？


如果我们不能幸运地为上证找到任何一种已知的分布
我们还能回答上述问题吗？
答案是肯定的
因为我们实际上需要的是cdf函数
所以只要能够找到这个CDF函数
就能回答这个问题


我们可以使用上次课中介绍的`statsmodels.ECDF`


不过
出于增加知识面的目的
我们这次使用`scipy.stats.rv_histogram`模型来求上证的经验分布

# CLK7


使用该模型
首先我们要通过`np.histogram`来生成`hist`对象
这将包括`hist`和`edges`两个数组
然后将其传递给`scipy.stats.rv_histogram`
以构建一个`rv_frozen`对象
这里的hist_dist就是一个rv_frozen对象
此后我们便可以象其它随机变量一样
调用它的`pdf`
 `ppf`和`cdf`函数


# CLK8

这段代码将绘制沪指的cdf和pdf图
输出继续下跌的预测概率值

经验分布表明
**当上证下跌4%以后
继续下跌的概率为0.4%**
高出`genhyperbolic`的预测不少

在实践中我们具体使用哪一个
就是见仁见智了


注意这里的`pdf`曲线
它的数值非常大
但它在`pct`作用域上的积分为1
这并不矛盾
因为这里给出的是`pdf`
而不是`pmf`
`pdf`是`cdf`的导数
当它的数值比较大时
只说明在该处上升较快


这里的方法
我们不只是可以运用在沪指涨跌上
我们也可以运用在其它技术指标上
回答一些实际问题
沪指已经6连阳了
接下来继续收阳的概率是多大？


或者
某支股票的RSI当前值为x
 次日rsi继续上升的概率是多少？
等等
-->
