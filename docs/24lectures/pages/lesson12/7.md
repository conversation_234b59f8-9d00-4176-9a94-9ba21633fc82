---
layout: two-cols
clicks: 1
class: fade
title: 
---

# 残差及其度量 - max_error
<hr>

::left::

<show at="0|1">

```python
from sklearn.metrics import max_error
import numpy as np

np.random.seed(78)
y = np.arange(10)
y_hat = np.arange(10) + np.random.randint(1, 10, size=10)

print(y)
print(y_hat)
print(max_error(y_hat, y))
```
</show>
<show at="1">

<Loc left = "15px" top="98%">
[0 1 2 3 4 5 6 7 8 9]<br>
[ 6  8 10 12 11 14 11 12 12 13]<br>
9
</Loc>
</show>

::right::



<!--
在数据分布一节
我们接触到了误差的概念
并且学习了方差和标准差
它们是关于测量值与真实值之间差异的度量标准


残差（residual）是拟合值与观测值之间的差异
在 `sklearn.metrics` 中包含了许多跟残差相关的度量标准
我们就来一一认识下

首先是max_error
它是拟合值与真值之间的最大残差

我们可以用`sklearn.metrics.max_error`来计算

在这段代码当中
我们首先是导入max_error
然后生成了两个序列
一个是np.arange(10)
另外一个 我们称作y_hat
是一个想象中的拟合值
它是在y的基础上
加上了一个随机的整数变量
然后我们把这个打印出来
我们来看一下最终的结果
最终max_error它求的最大的残差
是发生在3和12这一对数据上
最大残差是9


-->
