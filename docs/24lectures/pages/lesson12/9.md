---
layout: two-cols
clicks: 1
class: fade
title: 
---

# 残差及其度量 - MAPE
<hr>

::left::

<show at="0|1">

```python
from sklearn.metrics import mean_absolute_percentage_error
import numpy as np

np.random.seed(78)
y = np.arange(10)
y_hat = np.arange(10) + np.random.randint(1, 10, size=10)
pmae = mean_absolute_percentage_error(y_hat, y)

print(y)
print(y_hat)
print(pmae)
```

</show>
<show at="1">

<Loc left="15px" top="100%">

[0 1 2 3 4 5 6 7 8 9]<br>
[ 6  8 10 12 11 14 11 12 12 13]<br>
0.6216458541458542


</Loc>
</show>
::right::

<!--
mean_absolute_percentage_error
是残差绝对值相对于真值的比值
它取值在[0,1]之间
便于我们在不同的场景下进行比较

这个方法同样来自于sklean.metric
使用上与刚刚介绍的其它方法没有区别
这里我们就不详细介绍了
-->
