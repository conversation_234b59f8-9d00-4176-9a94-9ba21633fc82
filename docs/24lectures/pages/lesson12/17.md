---
layout: two-cols
clicks: 1
class: fade
title: 
---

# 相关性分析示例
<hr>

::left::

```python
code = '603392.XSHG'
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 3, 1)
bars = await Stock.get_bars_in_range(code, FrameType.DAY,
                                     start, end)

close = bars["close"].astype(np.float64)
rsi = ta.RSI(close, 6)

pct1 = (close[1:]/close[:-1] - 1)

data = {}
for n in (5, 10):
    data[f"pct{n}"] = (close[n:]/close[:-n] - 1)[-21:-1] * 10

data["pre_rsi"] = rsi[-21:-1]
data["rsi"] = rsi[-20:]

drsi = np.diff(rsi)
data["pre_drsi"] = drsi[-21:-1]
data["drsi"] = drsi[-20:]

df = pd.DataFrame(data)
s = pd.Series(pct1)

df.corrwith(s)
```

::right::

```
pct5        0.028
pct10       0.215
pre_rsi     0.36
rsi        -0.029
pre_drsi    0.149
drsi       -0.34
```

<show at="1">

<Loc w="600px" left="-30vw" padding="50px" top="0vh">

![](https://images.jieyu.ai/images/2023/06/pct_drsi.png)
</Loc>
</show>


<!--
我们来做这样一个实验
取一支股票
获取它的每日收盘价
分别计算以下指标：

1. 每日涨跌
2. 移动5日收益率
3. 移动10日收益率
4. RSI
分别得到两个序列

   一个是前一日rsi

   一个是当日rsi
5. RSI的导数
即 $rsi[1:] - rsi[:-1]

然后我们来看这些指标
是否与次日的涨跌呈现某种相关性
实际上这是一个最简单的因子分析过程
引入因子分析的概念
这是我们这个实验的第一个目标

数据表明
当日涨跌与前一日的`rsi`的相关系数是0.36
和前一日`drsi`的相关系数是-0.34
有观点认为0.3以上的系数也可以认为存在弱相关
但与近5日收益率、10日收益率的相关性比较弱
当然，这只能说明这支股票
在这一段时间内的情况


如果我们将当日涨跌与当日`drsi`对照绘图
会发现它们的走势几乎完全一致
这不难理解
毕竟涨跌影响着RSI的取值


但如果用前一天的drsi
即pre_drsi与pct1对照绘图
它们的走势也极为相似
但有一天的相位差
也就是前一日的rsi的涨跌
会影响到次日股价涨跌


也就是说
pct1与drsi之间
pct1与pre_drsi之间都存在某种关联
不管这种关联有没有实际利用价值
但是我们在这里发现了一个现象
即存在关联的两个序列
并不一定能通过相关性分析给找出来


那么这种情况下
我们又应该通过什么方法来找出它们之间的联系呢？


这就引入了相似性的概念

-->
