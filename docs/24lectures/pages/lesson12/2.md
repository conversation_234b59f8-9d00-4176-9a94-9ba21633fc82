---
layout: toc
image: https://images.jieyu.ai/images/2023/07/lesson12-outline.png
---

<Ellipse top="25%" left="53%" s="150" />
<Ellipse top="25%" left="18%" s="150" />
<Ellipse top="41%" left="13%" s="150" />
<Ellipse top="58%" left="55%" s="150" />
<Ellipse top="60%" left="16%" s="150" />


<!--
假如今天沪指盘中大跌4%
这时候我们应该如何应对？
有没有加仓的机会？
如果此时加仓
沪指继续下跌的概率是多大？


要回答这个问题
我们就需要先知道沪指的概率分布模型
这就是统计推断要回答的问题


我们在这一节
将介绍分位图、数值验证和假设检验
等三类统计推断方法

证券价格的变化是一种时间序列
我们对时间序列的研究
不仅仅限于它的分布和统计特征
也许还希望预测它的走势
这可能就要用到回归分析
甚至神经网络等技术
但是
无论我们怎么进行拟合
都会需要定义一种
度量拟合值与观测值之间差异的方式


这就是第二节的内容
我们介绍拟合、回归和残差
主要回答什么是残差
如何度量残差
我们还要对多项式回归进行研究


经济活动往往具有关联性
比如
上游原材料涨价后
下游企业的利润就可能会受影响
这是我们从经济活动的原理推导出来的结论
如果从数据分析的角度
是否存在某种方法
可以揭示两组数据之间的关联？


这是第三节
相关性分析要解决的问题
我们将介绍协方差和相关系数、
皮尔逊和斯皮尔曼相关性等概念
并且通过板块与个股的行情数据为例
进行一次相关性分析的实操

第四节
我们讨论距离和相似性
这些概念在机器学习中非常重要
除了机器学习
我们在技术分析中也需要使用

第五节
我们补充一点归一化知识
归一化在机器学习和因子预处理中都有用到
同时
在技术分析中
要进行多个标的之间技术指标的比较
这些指标也必须是归一化的
-->
