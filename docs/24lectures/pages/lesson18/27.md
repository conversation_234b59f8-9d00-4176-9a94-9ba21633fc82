---
layout: two-cols
clicks: 0
class: fade
title: 
---
# 使用选项
<hr>

::left::

```python


opts = opts.TitleOpts(title="主标题",
                     subtitle="副标题")

bar.set_global_opts( title_opts=opts )
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson18-pyecharts-options.png)


<!--
在 pyecharts 中
title colormap sliderranger legend axis 等对象都是通过选项来进行设置


比如，标题对应的属性由 TitleOpts 进行管理
如果我们要设置坐标轴线
可以用 AxisLineOpts 设置
设置坐标轴刻度可以用 AxisTickOpts
等等



-->
