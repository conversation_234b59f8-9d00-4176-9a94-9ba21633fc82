---
layout: two-cols
clicks: 2
class: fade
title: 调色板
---

# 调色板
<hr>


::left::

<show at="0">

## 定性调色板（qualitative palettes）
## 顺序调色板（sequential palettes）
## 发散的调色板（diverging palettes）
</show>

<show at="1">

```python {all}
p1 = sns.color_palette("husl", 3)
sns.palplot(p1)

p2 = sns.color_palette("husl", 5)
sns.palplot(p2)

p3 = sns.color_palette("husl", 8)
sns.palplot(p3)
```
</show>

<show at="2">

```python {all}
# 使用循环色绘制sin

p4 = sns.color_palette("RdPu", 10)
sns.palplot(p4)

def sinplot(n=10, flip=1):
    x = np.linspace(0, 14, 100)
    for i in range(1, n + 1):
        plt.plot(x, np.sin(x + i * .5)
                 * (n + 2 - i) * flip)
        
sns.set_palette(p4)
sinplot()
```
</show>

::right::

<show at='1'>

![](https://images.jieyu.ai/images/2023/07/lesson18-circular-palette.png)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson18-sin-with-circular.png)
</show>

<!--
seaborn 中的调色板操作很简单
最重要的函数是 color_palette() 
这将生成一个调色板
可供任何具有 palette 参数的函数内部使用


color_palette() 的主要参数通常是一个字符串
它是要指定的预置调色板的名称
比如deep, muted等
或者是matplotlib的colormap名称
或者是”husl"/"hsl"等。

如果没有另外指定颜色
不带参数调用 color_palette() 将返回当前默认调色板
可以通过 set_palette() 函数设置默认调色板


一般来说
调色板分为以下三类之一：

* 定性调色板（qualitative palettes）
适合表示分类数据
* 顺序调色板（sequential palettes）
适合表示数值数据
* 发散的调色板（diverging palettes）
适合具有分类边界的数值数据

# clk1
这段代码演示了
如何生成一个有着不同采样次数的**循环色彩系统**下的调色板
这个绘制方法在我们挑选色彩时非常有用
这里我们分别使用了3， 5， 8等不同次数的采样


# clk2
这段代码我们再次使用sinplot来绘制曲线
但使用了单一色调
主要演示如何生成调色板和设置调色板
第3行 我们生成了一个单一色调的颜色系列
第12行
设置它为调色板
第13行
绘制sin曲线

关于定性调色板
我们就介绍这么多
这一节要注意我们通过示例
实际上演示了color_palette的色彩名称参数的各种取值
-->
