---
layout: two-cols
class: fade
title: 
---
# 调用习惯
<hr>

::left::

```python {all}
from pyecharts.charts import Bar

bar = (
    Bar()
    .add_xaxis(["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"])
    .add_yaxis("商家 A", [5, 20, 36, 10, 75, 90])
)
bar.render_notebook()
```

::right::


<!--
pyecharts 在构建图形时
支持了链式调用
这是它与其它框架明显不同的特点之一

这一次
我们不需要调用 bar.load_javascript()
因为之前已经为 bar 对象加载过 javascript 了


当然
我们也可以仍按传统方式来调用


-->
