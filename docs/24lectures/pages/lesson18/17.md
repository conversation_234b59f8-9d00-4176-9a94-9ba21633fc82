---
layout: two-cols
clicks: 2
class: fade
title: 
---
# Pairgrid
<hr>

::left::

```python {all|1-3|5-8}
iris = sns.load_dataset("iris")
g = sns.PairGrid(iris)
g.map(sns.scatterplot)

g = sns.PairGrid(iris, hue="species")
g.map_diag(sns.histplot)
g.map_offdiag(sns.scatterplot)
g.add_legend()
```
::right::

<show at="0,1">

![75%](https://images.jieyu.ai/images/2023/07/lesson18-pair-grid.png)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson18-pairgrid-diag.png)
</show>


<!--
前面我们介绍了许多高级绘图函数
但是
它们还是缺少一种可以立即
全面展示数据集中两两关系的图
PairGrid 则弥补了这一缺陷
使用 PairGrid 可以对数据集当中
那些有趣关系
为我们提供一个快速和高级的摘要

[CLK1]
现在显示的是右图对应的代码

[CLK2]
显然
在这个图中的对角线位置上的绘图意义并不大
为了充分利用空间
我们也可以单独为它指定一些有意义的单变量绘图
比如直方图

这里我们还顺便演示了在seaborn中如何增加legend
大家可以跟matplotlib比较一下

-->
