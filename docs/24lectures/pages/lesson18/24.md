---
layout: two-cols
clicks: 0
class: fade
title: 
---
# PyEcharts
<hr>

::left::
![75%](https://images.jieyu.ai/images/2023/07/lesson18-pyecharts-intro.jpg)

::right::

![75%](https://images.jieyu.ai/images/2023/07/lesson18-pyecharts.jpg)


<!--
Echarts 是一个开源的数据可视化 JS 库
pyecharts 是一个生成 Echarts 图表的 python 类库
这个关系有点类似plotly.py与plotly.js之间的关系
Echarts 最初由百度开发
后来开源
贡献给 Apache 基金会
现在是其顶级成员之一
Echarts 使用了很多优化技术
据称可以实现千万级数据的流畅交互


ECharts 的特点之一是
开箱即用的图形比较多
不仅提供了常规的折线图、柱状图、散点图、饼图、K 线图
还有用于统计的盒形图
用于地理数据可视化的地图、热力图、线图
用于关系数据可视化的关系图、treemap、旭日图
多维数据可视化的平行坐标
还有用于 BI 的漏斗图
仪表盘
并且支持图与图之间的混搭

通过 pyecharts 来绘图
原理跟 plotly 类似
也是在 python 域内完成图形定义
然后转换成 json 对象
再交给 echarts 来渲染
但在这个转换中
与 plotly 中不同的是
我们需要自行保证 json 序列化能够成功
而通过 Plotly 定义的图形
哪怕数据使用了 dataframe、numpy这样一些数据结构
plotly 一般情况下都是能将其 json 序列化的
pyecharts 没有这么做
是为了避免打包这两个库
导致包体积变大
-->
