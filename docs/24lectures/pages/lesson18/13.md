---
layout: two-cols
class: fade
title: 
---
# ECDF
<hr>

::left::

```python
sns.displot(penguins, x="flipper_length_mm", 
            hue="species", kind="ecdf")
```
::right::

![](https://images.jieyu.ai/images/2023/07/lesson18-ecdf.png)


<!--
如果对ECDF的概念不太熟悉
可以回过头看看第11章

ECDF 图有两个主要优点
与直方图或 KDE 不同
它直接表示每个数据点
这意味着无需考虑箱大小或平滑参数
此外
由于曲线是单调递增的
因此它非常适合比较多个分布
就是它跟直方图和KDE不同
数据上的每一个点它都会画出来
它也没有那些估算、推断等等
所以呢它不会丢失数据不不失真
这样我们也就不用去考虑
箱的大小和平滑参数对吧
那些参数我们刚刚说了
是一个调参的过程
如果你参数调的不对
可能本来有特征的地方
被你弄丢了
或者本来不是特征的地方
凭空的给他增加了一个特征
ECDF在这一方面相对来讲稳定一些



ECDF 图的主要缺点是
它表示分布形状的方式不如直方图或密度曲线直观
考虑一下鳍状肢长度的双峰性如何在直方图中立即显现出来
但要在 ECDF 图中看到它
您必须寻找变化的斜率
尽管如此
通过练习
您可以通过检查 ECDF 来学会回答有关分布的所有重要问题
这样做可能是一种强大的方法


-->
