---
layout: two-cols
clicks: 5
class: fade
title: 
---
# 可视化变量关系
<hr>

::left::

```python {all|5,6,7|9-11|13-14|16-19}
dowjones = sns.load_dataset("dowjones")
sns.relplot(data=dowjones, x="Date", 
            y="Price", kind="line")

# -- 或者，使用 LINEPLOT --
sns.lineplot(downjones, x = "Date", 
            y = "Price")

fmri = sns.load_dataset("fmri")
sns.relplot(data=fmri, x="timepoint",
            y="signal", kind="line")

# 查看fmri数据集
fmri.groupby('timepoint').aggregate('count')

# 禁止聚合计算
sns.relplot(data=fmri, x="timepoint",
            y="signal", kind="line", 
            estimator=None)
```
::right::

<show at="0,1">

![](https://images.jieyu.ai/images/2023/07/lesson18-lineplot.png)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson18-confidence.png)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/07/lesson18-fmri-dataset.png)
</show>

<show at="4">

![](https://images.jieyu.ai/images/2023/07/lesson18-fmri-no-agg.png)
</show>


<!--
我们刚刚介绍的是散点图
对某些数据集
比如时间序列
画线图是一个不错的选择
像我们右边的这个图
这是道琼斯的1920年到1970年的指数走势图
如果我们把它绘制成散点图的话
就不会有这里这样明显的时间序列的含义
比如，如果我们在绘图时
给出的数据出现错误
那么折线图就会出现数据折返的现象
我们很容易发现其中的错误
那如果是绘制成散点图
我们是不太容易发现这种数据的错位的
所以
人类的直觉更容易从折线图中发现趋势

# CLK4
这个图就是同一时间数据多重采样后
折线图上很容易发现这个问题

# BACK TO 1

在 seaborn 中
这可以通过 relplot() 函数
设置参数kind = line来实现

[CLK1]

也可以直接调用lineplot来实现
两种方法
从最终的结果上看起来
似乎是一样的
但实际上
它们的返回值是不一样的
前面一段返回了一个fig
这一段返回的是一个ax

[CLK2]

散点图和线图绘制并不是 seaborn 的专利
其它框架也一样能绘制
如果seaborn的功能就到此为止的话
是没法体现seaborn的优势
以及它更更强调语义关系这一特性的
对吧

因此，我们这里介绍它的另一个功能
就是能通过绘制平均值
和围绕平均值的 95% 置信区间
来聚合每个 x 值的多个测量值
形成的一个包络图
这个有点像布林带
但是它并不是布林带
它们的算法不同

注意看
到跟上一图代码相比
我们调用的方法和参数是完全一样的
但在 dowjonse 数据集上
并没有显示出右图这样的包络图
这是为什么
原因在于
只有当同一 x 值
存在多个测量值时
seaborn 才会绘制置信区间包络图
我们来看一下 fmri 这个数据集

[CLK3]
对按时间索引的dataframe
我们可以把每列看成一个变量
而每一行
看成是对这些变量的一次采样
对时间序列来讲
我们以行情数据为例
最小的数据点是tick级的
那么分钟级的行情
就可能包含了多个tick级的采样

回到我们这里的fmri数据集
我们从输出结果发现
它的每一个时间点（即同一个'x'值）
都对应着 56 次采样
而在dowjones数据集中
每个时间点上只有一次采样
所以，在dowjones数据集示例中
seaborn只是绘制了一个简单的折线图
seaborn 会在采样值达到什么条件下
启用置信区间包络图
文档没有介绍


不过 seaborn 是开源的
如果对这一问题特别感兴趣
可以阅读他们的源码来了解

在这里，seaborn自动进行了聚合推断
seaborn的这些推断往往都是有效的和有帮助的
[CLK4]
对于frmi数据集
或者其它任何时间序列
如果在同一时间点有多个数据采样的话
如果我们不是对聚合后的数据进行绘图
而是直接绘制折线图
那么将显示出这样的结果
注意这仍然是进行修正过的绘图
如果seaborn直接按x-y坐标来进行绘图的话
情况会更加糟糕
但尽管是进行了偏移修正
我们仍然从中得不到任何有用的信息
所以这种情况下
唯一有效的绘图
就是绘制聚合统计后的图

当然
绘制统计推断后的图
会有一定的性能损失
所以
如果我们确实不需要绘制这种类型的图的话
也可以通过设置estimator = None来关掉它
-->
