---
layout: toc
image: https://images.jieyu.ai/images/2023/07/lesson18-outline.png
---


<!--
这是今天的课程的大纲
我们主要是分两块
一个是seaborn
一个是pyechart

今天的课跟之前的课不太一样
我们之前讲原理的东西多一些
那么今天
我们主要是讲一些应用的东西多一些
很多都是案例过一下
也有少部分的代码会精讲一下
但是多数主要是告诉你
它有哪些功能大概怎么用
我想有了之前的基础
这一块大家也不希望我们再重复那些基础知识了

在seaborn这一节
我们一是要讲
它的各种图的作用是什么
如何绘制
如何来探索我们数据的一些语义关系
其次，除了这些绘图之外，
很重要的一些基本的模块
像调色板、主题等等这些概念仍然是要讲的

在Pyecharts部分
我们也是首先讲怎么把它用起来
其次，它的调用习惯跟其他别的
它不是一个派系
所以调用习惯
包括它的整个的架构
会有些不一样
所以我们重点是放在这部分
它的子图布局这块非常有特色
我们也重点讲一下这部分
-->
