---
layout: two-cols
clicks: 3
class: fade
title: 
---
# 在Notebook中运行
<hr>

::left::

```python {1-2|4-18|19|21}
from pyecharts.globals import CurrentConfig, NotebookType
CurrentConfig.NOTEBOOK_TYPE = NotebookType.JUPYTER_LAB

from pyecharts.charts import Bar
from pyecharts import options as opts
from pyecharts.globals import ThemeType

import pyecharts.options as opts
from pyecharts.charts import Bar, Line

bar = (
    Bar()
    .add_xaxis(["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"])
    .add_yaxis("商家 A", [5, 20, 36, 10, 75, 90])
    .add_yaxis("商家 B", [15, 6, 45, 20, 35, 66])
    .set_global_opts(title_opts=opts.TitleOpts(title="主标题", subtitle="副标题"))
)

bar.load_javascript()

bar.render_notebook()
```

::right::


<!--
与 plotly 相比
pyecharts 在环境自适应上略逊一筹
要在 Notebook/jupyterlab 中使用PyEcharts
我们需要声明具体的 notebook 环境
即是 notebook 还是 jupyterlab
pyecharts 默认环境为 notebook
所以
在这种情况下
也可以不用声明


[CLK1]
这段代码绘制柱状图
比较简单

[CLK2]
这里是加载基本的 js 文件
及绘制 bar 所需要的 js 文件到 notebook 当中
pyecharts 可能是为了速度上的考虑
在加载 js 的策略上比较保守
不会一次性把所有的 js 都加载到 notebook
这里我们是通过 bar 对象调用的 load_javascript
如果我们后面绘制其它图形对象
也可能要通过该对象
再次调用 load_javascript 方法


[CLK3]

上述代码运行后
并不会有输出

Pyechart在使用上
与我们前面介绍的框架有一个比较显著的不同
也是容易让新手困惑的地方
其它框架中
当图形定义完成后
最终我们一般会调用 show 或者类似的方法将图形呈现出来
show 方法会根据预定义的渲染 backend
自行决定如何处理
如果是在 notebook 中
单元格中的最后一个语句的返回值如果是某个图形对象
它还会自动显示。


在 pyecharts 中没有类似的行为
pyecharts 被设计成为能与 python web 服务器集成
也能在 notebook 中运行
但是
在不同的环境下
显示的函数不一样
如果是在 notebook 中运行
需要调用 render_notebook 方法；如果是在服务器中运行
则要调用 render 方法

所以当我们在 notebook 中把代码调通后
如果要移植到服务器项目中
还需要将这些方法全部进行修改


在本课的示例中
我们将只使用 jupyterlab 作为渲染后台
因此
我们显示图形时
将使用 render_notebook 方法

-->
