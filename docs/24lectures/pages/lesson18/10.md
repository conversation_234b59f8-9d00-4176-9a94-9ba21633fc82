---
layout: two-cols
clicks: 8
class: fade
title: 
---
# 分面图与hue子集划分
<hr>

::left::

```python {all|3|5-9|11-14|17-18|20-21|23|25|27-28} {maxHeight: '400px'}
import matplotlib.pyplot as plt

fig, axes = plt.subplots(nrows=1, ncols=3)

sns.lineplot(
    data=fmri, 
    x="timepoint", y="signal", hue="event",
    ax=axes[0]
)

grid = sns.relplot(
    data=fmri,kind='line',
    x="timepoint", y="signal", col="event"
)

def move_axes(ax, fig, pos):
    ax.remove()
    ax.figure = fig

    fig.axes.append(ax)
    fig.add_axes(ax)

    ax.set_position(axes[pos-1].get_position())

    axes[pos-1].remove()

move_axes(grid.axes[0][0], fig, 2)
move_axes(grid.axes[0][1], fig, 3)

fig.show()
```
::right::

![](https://images.jieyu.ai/images/2023/07/lesson18-facet-vs-hue.png)


<!--
我们已经学习了在 relplot 中
通过指定 col 参数
将同一数据集的不同的子集
按分面图进行显示
我们也可以按色调语义
将数据集分别显示
并显示误差包络带


右边是两种方法效果对比图

facet 绘图将绘制一个 1*2（一行两列）的图
加上通过 hue 来进行子集划分的子图
我们一共要绘制 3 个子图


[CLK1]
所以
 第 3 行中
我们先生成了一个 1*3（一行三列）的图
然后尝试将 sns 的绘图分别填充到这三个 Axes 中


[CLK2]
我们立即使用了第0个 Axes
在其中绘制了线图
这个绘制使用的是 sns.lineplot
它是一个轴级函数
因此允许我们指定一个 Axes 对象
这个线图中我们将数据集
按 event 划分成不同的子集
并用不同的色调来显示


[CLK3]
接下来
我们使用 sns.relplot 来绘制分面图
由于 relplot 是 figure-level 的绘图
所以
它不接受 Axes 对象
而是会自己生成一个新的图（figure)
因此
我们必须将 relplot 中生成的 Axes
移动到第三行生成的 fig 中
这样最终才能融合成一张图

这里sns.replot 返回的对象中
包含了 axes 对象数组
我们要移动的 Axes 就在这个数组中

[CLK4]
这里我们定义了一个辅助函数
它有三个参数
ax, fig, pos
表时我们将一个源 ax移动到
目标fig中位置为pos的ax处

第 17~18行
将 ax 从它之前所在的 figure 中移除
加入到新的 figure 中
注意每一个 axes 对象都保留了对 figure 的引用
我们可以通过 ax.remove() 来移除掉这个引用
并通过 ax.figure 来设置新的引用
如果我们不执行第 17 行
则 grid 所绘制的图形将仍然显示出来


[CLK5]
第 20~21行将 ax 加入到新的 figure 中
这里的语句看上去有所重复
是因为它们发生在不同的层面
有的是为了维护数据结构的完整
有的是为了触发图形绘制


[CLK6]

第 23行
我们将新加入的 axes 定位到 pos 索引指定的位置
我们先是通过 get_position 获得索引处原 axes 的位置
再通过 set_position 将新加入的 axes 定位到该处


[CLK7]
第 25 行
我们将被替换的 axes
即 axes[pos-1] 从 figure 中移除


[CLK8]
我们对relplot生成的grid中的两个Axes对象
分别调用移动操作
最终将这两个Axes
移动到了目标figure中
的第二、第三个子图的位置

这个示例演示了一些绘图的高级技巧
这里移动 Axes 及重新定位的技巧是必须掌握的

有时候我们想利用 seaborn 提供的 facet 绘图
又需要额外添加一些别的绘图
这样的场景还是很常见的
就需要参考这里的
在不同的figure中移动axes的操作
注意在这里
我们使用的都是matplotlib的方法和对象
因此
在一个纯的matplotlib工程中
我们也可以这样使用


-->
