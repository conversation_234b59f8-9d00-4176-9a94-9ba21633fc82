---
layout: two-cols
clicks: 6
class: fade
title: 
---
# 可视化变量关系
<hr>


::left::

```python {all|2,3|5-7|9-14|16-19|22-24|26-30} {maxHeight: '400px'}
tips = sns.load_dataset("tips")
sns.relplot(data=tips, x="total_bill",
             y="tip")

# 加上色调语义
sns.relplot(data=tips, x="total_bill",
             y="tip", hue="smoker")

# 强调类的差异
sns.relplot(
    data=tips,
    x="total_bill", y="tip", 
    hue="smoker", style="smoker"
)

# 颜色的语义随数据类型变化
sns.relplot(
    data=tips, x="total_bill", 
    y="tip", hue="size",
)

# 将数值关联到点的大小
sns.relplot(data=tips, x="total_bill", 
            y="tip", size="size")

# 标准化点的大小范围
sns.relplot(
    data=tips, x="total_bill", y="tip",
    size="size", sizes=(15, 200)
)
```

::right::

<show at="0,1">

![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-1.png)

</show>
<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-2.png)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-3.png)
</show>

<show at="4">

![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-4.png)
</show>

<show at="5">

![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-5.png)
</show>

<show at="6">

![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-6.png)
</show>


<!--
这一节中将讨论如何探索变量之间的关系
比如我们在因子分析中
生成了一个dataframe
有因子数据
这可能有好多列
也有衡量因子表现的指标
比如IC、5日、10日收益等等等
作为一个有经验的投资者
我们希望先将这些数据可视化
通过我们强大的直觉来发现一些pattern
然后决定下一步如何展开

在探索变量之间的关系时
我们最常用的方法是 relplot
这是一个 figure-level 级的函数
可用以绘制散点图和线图
这是前面已经提过的
当我们使用relplot来绘制线图时
需要加上kind = line这个参数

如果我们不提供这一参数
它将绘制一个散点图
正如这段代码所做的那样
[CLK1]
这张图就揭示了两个变量
即账单与小费之间的关系
[CLK2]
如果我们想揭示三个变量之间的关系
我们可以通过对点进行着色
来将另一个维度添加到图中
在 seaborn 中
这被称为使用“色调语义”
因为点的颜色获得了意义

[CLK3]
为了强调类之间的差异
并提高可读性（accessibility - 方便色弱视力者）
您可以为每个类使用不同的标记样式

[BACK]
注意看
变化发生在marker上
我们现在有了两种marker

[CLK4]
颜色的语义还会根据数据的类型发生变化
当数据是类别型的
seaborn就会采用离散型的调色板
如果是连续型的
就会使用连续型的调色板

关于什么是离散型的调色板
什么是连续型的调色板
本节课后面还会专门说明

[CLK5]
我们也可以将某列数值
与点的大小进行语义关联
不过要注意
这里点的大小并不是与数值直接对应
而是进行了某种变换
为什么要进行这种变换呢
因为首先这种变换是天然存在的
我们从dataframe中看到的size数据
它的量纲可能是美元
也可能是人数
还可能是身高、体重
但在图上的点
它的大小的量纲可能是像素
所以，这种转换是必然存在的

其次，size可能少到几微米
比如，我们正在研究是某种细菌
也可能大到数光年
比如，如果我们正在研究银河系里那些眨着眼睛的星星

因此，我们也需要有一个办法
让seaborn既能处理如此宏大
又能处理如此微少的尺度
如果在别的绘图框架中
我们不得不自己挽起袖子来干这脏活
但在seaborn中


[CLK6]
我们只要指定sizes这个参数
就能自动完成这一映射
在这里
sizes[0]即15 是最小的点的面积
sizes[1] 即200
是最大的点的面积
如果两者接近
则所有的点看起来大小差不多
如果 sizes[1] 较小
则所有的点都会较小
如果 sizes[0] 较大
则所有的点都会较大
可以看出
这张图比前一张
点的面积大了不少
-->
