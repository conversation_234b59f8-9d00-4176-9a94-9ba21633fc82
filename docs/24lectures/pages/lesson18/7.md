---
layout: default
class: fade
title: Seaborn绘图功能分类概览
---
# Seaborn绘图功能归类
<hr>

<show at="0">

![75%](https://images.jieyu.ai/images/2023/07/lesson18-sns-arch.png)
</show>


<!--
我们刚刚看到的例子
体现了seaborn基于语义绘图的特点
为了更深入地理解这一点
我们拿plotly来做一个对照
我们在上一节课已经讲到
plotly已经有半语义化的特点了
但是在plotly当中
我们还是要显式地告诉绘图框架
我们要绘制散点图
要通过facet_col告诉它
我们要绘制分面图
如果我们不这么做
它是不会主动地绘制出一个分面图的

不过
尽管Seaborn 可以自动帮我们做一些决定
但我们也可以进行定制化
比如，
在 relplot 关系图绘制时
有时候可能绘制成折线图更好

[CLK1]

在这种情况下
我们可以给 relplot 传一个kind参数
指定类型为line
以切换到折线图
如果不传入的话
它默认使用散点图

注意到转换成折线图之后
size 和 style 参数依然在使用
但它们对可视化结果的影响不一样
在散点图中
size 代表点的大小
style 则用来选择不同的符号（marker）
在折线图中
size代表的线的宽度
而style则代表了线的样式
比如
是虚线还是实线，等
这也正好反映了 Seaborn 基于语义绘制图形的含义
这些本来应该由人来完成的语义映射
Seaborn 都能为我们考虑到
从而让我们可以专注于绘制的整体结构和希望它传达的信息
Seaborn当中有很多种图形
我们今天会摘要介绍几种
在这之前
我们先看一下它的一个分类图
通过这个分类
大家在后面可以反过来去查找
如果我想要画某一种什么样的图
那么我应该到哪个包里面去查找它

在seaborn中
它的命名空间是扁平的
所有的功能都是在顶层可以访问的
也就是说
当你在代码中敲一个sns.的话
后面就会提示比如说real plot
lineplot
scatterplot等等
所谓命名空间扁平
简单地说
就是这些方法都可以sns.这样的方式来引用

但是实际上它是有归类的
了解这个归类
在我们查文档的时候
是需要用到的

最常用的归类
是这样三类
我们来做数据探索的时候

[MARK]

最主要的归类为"relational" 统计关系
 "distributional" 变量是如何分布的
 以及"categorical" ，变量的分类
 分布和分类是我们在第11课比较仔细的讨论过的
此外
还有其它一些无法归类的情况

就像multiple views
regression跟matrix plot等等

这个层次，
我们看到这里有一个real plot方法
同时
它又被归类成一个节点
这个节点比后面这些节点
的层次要高

所以呢
在每一个包的顶层位置
它都会有个这样的函数
我们通过给它传入不同的参数
可以分别的对应到下一层节点
上面的这些函数
那这些函数有什么样不同呢？
除了在调用的方法上不同之外
我们还要传入一个kind参数
如果不传怎么办
不传他会有使用默认的参数
这是在前面讲过的
所以，在real plot当中
如果不传kind参数
它就自动的转化为scatter_plot
那更重要的不同就在于说
这些节点它是所谓的figure level
Figure level下一级的函数
它们被称为ax level

figure-level的图形
它绘制出来时
它是既有fig对象也有ax对象
因为seaborn的许多概念都是从matplotlib延伸过来的
我们在第16课讲的概念
对这边绝大多数是适用的
所以呢在这个级别上的绘图
它会带上一个fig对象
然后在ax这个级别
它是只有ax
这些不同会体现在
特别是你要对图形进行组合的时候
在fig级别的这种图形
如果返回给你的是fig
那么你把几个fig组合到一起
这个是需要有一定的技巧的
我们之前有示例
一般是通过转化成image
对image进行拼接
然后通过imshow类似的方法来显示的
对吧？
因为我们说过
就是图真正是画在ax level对吧
然后fig它是一个画布
那么这些ax在fig之间
是没有那么容易去做迁移的
我们今天也会讲这个问题
好这就是我们 seaborn
比较高层次的一个概览

-->
