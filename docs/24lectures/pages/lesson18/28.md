---
layout: two-cols
clicks: 5
class: fade
title: 
---
# 子图和布局 - Grid
<hr>

::left::


```python{all}{maxHeight: '400px'}
from pyecharts import options as opts
from pyecharts.charts import Bar, Grid, Line
from pyecharts.faker import Faker

bar = (
    Bar()
    .add_xaxis(Faker.choose())
    .add_yaxis("商家 A", Faker.values())
    .add_yaxis("商家 B", Faker.values())
    .set_global_opts(title_opts=
                    opts.TitleOpts(title="Grid-Bar"))
)
line = (
    Line()
    .add_xaxis(Faker.choose())
    .add_yaxis("商家 A", Faker.values())
    .add_yaxis("商家 B", Faker.values())
    .set_global_opts(
        title_opts=opts.TitleOpts(
                    title="Grid-Line", 
                    pos_top="48%"),
        legend_opts=opts.LegendOpts(pos_top="48%"),
    )
)

grid = (
    Grid()
    .add(bar, grid_opts=opts.GridOpts(
                        pos_bottom="60%"))

    .add(line, grid_opts=opts.GridOpts(
                        pos_top="60%"))
)

grid.load_javascript()

grid.render_notebook()
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson18-pyecharts-grid.png)


<!--
接下来我们介绍子图和布局在PyEcharts中如何实现
首先
我们分别构建了一个柱状图和一个折线图
然后构建了一个 Grid 对象用来管理布局
第 25~26 行
我们将这两个子图加到 grid 当中
并且指定第一个子图占据高度的 40% (1 - pos_bottom)
-->
