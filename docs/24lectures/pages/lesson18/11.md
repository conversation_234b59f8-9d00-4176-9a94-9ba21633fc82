---
layout: two-cols
clicks: 10
class: fade
title: 
---
# 单变量直方图
<hr>

::left::

```python {all|4-6|8-10|12-14|16-18|20-22|24-26|28-30|32-34} {maxHeight: '400px'}
penguins = sns.load_dataset("penguins")
sns.displot(penguins, x="flipper_length_mm")

# 修改bin大小
sns.displot(penguins, x="flipper_length_mm", 
            binwidth=3)

# 也可以修改bins的个数
sns.displot(penguins, x="flipper_length_mm", 
            bins=5)

# 也可以传入数组
sns.displot(tips, x="size", 
            bins=[1, 2, 3, 4, 5, 6, 7])

# 还可以当成类别
sns.displot(tips, x="size", discrete=True, 
            shrink=0.8)

# 增加hue语义
sns.displot(penguins, x="flipper_length_mm",
             hue="species")

# 使用阶梯图
sns.displot(penguins, x="flipper_length_mm", 
            hue="species", element="step")

# 使用堆叠直方图
sns.displot(penguins, x="flipper_length_mm",
            hue="species", multiple="stack")

# 使用闪避图
sns.displot(penguins, x="flipper_length_mm", 
            hue="sex", multiple="dodge")
```

::right::

<show at="0">

![75%](https://images.jieyu.ai/images/2023/07/lesson18-hist.png)
</show>

<show at="1">

![75%](https://images.jieyu.ai/images/2023/09/lesson18-hist-bin-width.png)

</show>

<show at="2">

![75%](https://images.jieyu.ai/images/2023/09/lesson18-hist-bins.png)

</show>

<show at="3">

![75%](https://images.jieyu.ai/images/2023/09/lesson18-hist-bin-edges.png)
</show>

<show at="4">

![75%](https://images.jieyu.ai/images/2023/07/lesson18-hist-category.png)
</show>

<show at="5">

![](https://images.jieyu.ai/images/2023/07/lesson18-hist-conditional.png)
</show>
<show at="6">

![](https://images.jieyu.ai/images/2023/07/lesson18-hist-step.png)
</show>


<show at="7">

![](https://images.jieyu.ai/images/2023/07/lesson18-stacked-hist.png)
</show>

<show at="8">

![](https://images.jieyu.ai/images/2023/07/lesson18-dodge-hist.png)
</show>



<!--
直方图我们已经比较熟悉了
那么在seaborn中
还隐藏着哪些技巧呢？

在这个例子中
我们加载了一个企鹅的数据集
其中有一列就是企鹅鳍的长度
当我们对它以默认值进行绘图时
我们将得到右图
这个图已经提供了一些数据的情况
比如最常见的鳍状肢是195毫米
但分布呈双峰
而不是接近正态分布
这是比较让人奇怪的地方

在直方图绘制中
箱 (bins) 的大小是一个重要参数
使用错误的箱大小可能
会因为模糊数据的重要特征
凭空创造出特征而产生误导
默认情况下
 displot() / histplot() 
 根据数据方差和观测值数量选择默认箱大小

所以接下来
我们试着对这个图进行一些调试
以期暴露真正的规律
[CLK1]

这里我们是按bin_width进行了分箱
此时每个分箱应该落入3个样本

[CLK2]
我们还可以设置共分5个bin
结果如右图所示

# CLK3
我们可以让bins传入的是分箱的边缘值数组
不过这种情况相当于我们手动划分bin
注意这里我们使用了另外一个数据集
tips数据集

# CLK4
我们还可以当列当成类别来看
注意这里我们使用的是tips数据集

# CLK5
经过上面几次调试
似乎我们从来都没有解决
分布不满足正态的问题
这究竟是我们观察的错误
还是数据处理的问题
还是说我们有了一个真正的发现
自然界出现了某种物种
它的鳍长不符合正态分布？

我们猜想是数据处理的问题
现在，让我们给图增加一个维度试试
在这一次的绘图中
我们增加了hue语义
让它与物种关联起来
现在我们明白了
这种双峰
背后其实是多个物种叠加造成的


[CLK6]
如果觉得上图还不够直观
我们还可以将样式改为阶梯图

[CLK4]
我们还可以使用使用堆叠直方图

[CLK5]
闪避条形图
它是把不同的类型分别展示出来

当然我们也可以使用分面图来显示
大家可以自己试一下

这一节的示例
充分演示了
seaborn让我们可以直接深入到数据的语义联系
而不会陷入到图形绘制细节中

熟悉了 seaborn 的语法之后
我们就可以在数据探索阶段
快速而大量地进行尝试
从而迅速发现数据之间的规律

-->
