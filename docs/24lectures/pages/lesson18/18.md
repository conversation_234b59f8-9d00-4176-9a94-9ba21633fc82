---
layout: two-cols
clicks: 3
class: fade
title: 主题
---

# 主题
<hr>

::left::

```python {1-14|16-35|38-39|41-43} {maxHeight: '400px'}
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

def sinplot(n=10, flip=1):
    x = np.linspace(0, 14, 100)
    for i in range(1, n + 1):
        plt.plot(x, np.sin(x + i * .5) 
                * (n + 2 - i) * flip)
        
sns.set_style('white')
sinplot()
sns.despine()
_ = plt.title("white theme without spine")

# 上下文管理
f = plt.figure(figsize=(6, 6))
gs = f.add_gridspec(2, 2)

with sns.axes_style("darkgrid"):
    ax = f.add_subplot(gs[0, 0])
    sinplot(6)

with sns.axes_style("white"):
    ax = f.add_subplot(gs[0, 1])
    sinplot(6)

with sns.axes_style("ticks"):
    ax = f.add_subplot(gs[1, 0])
    sinplot(6)

with sns.axes_style("whitegrid"):
    ax = f.add_subplot(gs[1, 1])
    sinplot(6)

f.tight_layout()

# 列出样式细节
sns.axes_style()

# 更改样式
sns.set_style("darkgrid", {"axes.facecolor": ".9"})
sinplot()
```

::right::

<show at="0">

![](https://images.jieyu.ai/images/2023/07/lesson18-sinplot.png)
</show>
<show at="1">

![75%](https://images.jieyu.ai/images/2023/07/lesson18-temp-style.png)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/07/lesson18-theme-dark-grid)
</show>

<!--
画出有吸引力的图形很重要
当我们为自己制作图表
或者探索数据集时
一张赏心悦目的图能提升我们的工作乐趣
对于向观众传达见解
可视化也是至关重要的
此时就更需要有吸引力的图形
而有吸引力的图形
离不开色彩和展现样式的搭配
这就是主题要解决的问题

Seaborn 附带了5个预设主题
预设主题列表没有API可以查询，我们需要记忆。
这5个主题是
"darkgrid"
 "whitegrid"
 "dark"
 "white"和"ticks"
默认的主题是 darkgrid
各个主题的风格正如它们的名字所暗示的那样
如果主题带 grid
则将显示网格线；反之则不带
ticks主题将在轴线上加上刻度
我们稍后将来一一展示

设置主题可以通过set_style方法来实现
我们来解读一下这段代码
我们先是定义了一个绘制sine曲线的函数
sinplot
然后我们调用set_style将主题更改为white

在绘图时
我们常常希望隐藏上方和右边的spine
这样图形会美观
这也是最常见的一种样式
这个任务在 seaborn 中将非常简单
我们调用方法despine就可以了
对照一下，在 matplotlib 那一章
我们有个例子也隐藏了 spine
不过代码量会大一些
也正是有这样的普遍需求
seaborn才提供了这个方法


注间在sinplot中绘图时
我们并没有使用 seaborn 的函数
我们只是使用了 seaborn 的方法
来进行主题设置
以及移除 spine
这说明
seaborn 与 matplotlib 之前有着紧密的集成
我们完全可以混用两者的API


# CLK1

接下来的这段代码
我们绘制了一个两行两列
由4个子图组成的一个图
来演示4种主题之间的差异
以及seaborn的上下文管理语法

我们通过上下文管理语法（即with语句）
通过axes_style方法来在绘制sinplot之前
临时设置了接下来要使用的样式

注意我们在调用sinplot时
并没有传入一个ax对象进去
但sinplot却能找到正确的ax进行绘图
为什么？
如果不了解这一点的
可以去回顾一下matplotlib那一章
关于gcf和gca的内容

注意这里第三个图是ticks主题
与其它子图相比
它在spines上增加了刻度线

# CLK2

我们通过axes_style来设置一个样式
也可以通过它来显示当前使用的样式的细节特征

## browser

如果要对seaborn的样式进行微调
我们可以通过set_style来实现
这里的参数
就是axes_style的输出结果

# CLK3

然后
我们调用 set_style 方法
传入 theme 名字和要更改的样式
这里我们看看darkgrid的效果

# 结束语

有时候
我们希望把seaborn的绘图打印出来
大家知道
印刷场景和显示器场景下
存在很多差异
比如颜色、缩放等等
所以很多配置上也应该是不一样的
seaborn提供了场景式管理
场景分别有paper, notebook, poster等
由于不是高频应用
我们就不讲了

总的来说
seaborn的配色都比较柔和
在plotly中我们也使用过dark模式的主题
不知道大家还有没有印象
对比要强烈许多
-->
