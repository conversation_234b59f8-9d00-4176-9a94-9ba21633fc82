---
layout: two-cols
clicks: 1
class: fade
title: 
---
# 与plotly对照
<hr>

::left::

<show at="0">

```python
import plotly.express as px
import seaborn as sns

tips = sns.load_dataset("tips")

fig = px.scatter(
    tips,  # 数据集
    x="total_bill",
    y="tip",
    color="smoker",  # 按国家所在大陆进行颜色区分
    size="size",  # 按人口数决定大小
    facet_col="time",  # 列切面字段
    title="relationship plotting using plotly",
)
fig.show()
```
</show>

<show at="1">

```python
dots = sns.load_dataset("dots")
sns.relplot(
    data=dots, kind="line",
    x="time", y="firing_rate", col="align",
    hue="choice", size="coherence", style="choice",
    facet_kws=dict(sharex=False),
)
```
</show>

::right::

<show at="0">

![](https://images.jieyu.ai/images/2023/07/lesson17-seabron-demo-1.png)
</show>

<show at="1">

![75%](https://images.jieyu.ai/images/2023/07/lesson18-sns-demo-2.png)
</show>



<!--
我们刚刚看到的例子
体现了seaborn基于语义绘图的特点
为了更深入地理解这一点
我们拿plotly来做一个对照
我们在上一节课已经讲到
plotly已经有半语义化的特点了
但是在plotly当中
我们还是要显式地告诉绘图框架
我们要绘制散点图
要通过facet_col告诉它
我们要绘制分面图
如果我们不这么做
它是不会主动地绘制出一个分面图的

不过
尽管Seaborn 可以自动帮我们做一些决定
但我们也可以进行定制化
比如，
在 relplot 关系图绘制时
有时候可能绘制成折线图更好

[CLK1]

在这种情况下
我们可以给 relplot 传一个kind参数
指定类型为line
以切换到折线图
如果不传入的话
它默认使用散点图

注意到转换成折线图之后
size 和 style 参数依然在使用
但它们对可视化结果的影响不一样
在散点图中
size 代表点的大小
style 则用来选择不同的符号（marker）
在折线图中
size代表的线的宽度
而style则代表了线的样式
比如
是虚线还是实线，等
这也正好反映了 Seaborn 基于语义绘制图形的含义
这些本来应该由人来完成的语义映射
Seaborn 都能为我们考虑到
从而让我们可以专注于绘制的整体结构和希望它传达的信息


-->
