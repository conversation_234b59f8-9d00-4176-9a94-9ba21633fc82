---
layout: two-cols
clicks: 4
class: fade
title: 快速入门
---

# 快速入门
<hr>

::left::

```python {all|2|5|8|11-15}
# IMPORT SEABORN
import seaborn as sns

# APPLY THE DEFAULT THEME
sns.set_theme()

# LOAD AN EXAMPLE DATASET
tips = sns.load_dataset("tips")

# CREATE A VISUALIZATION
sns.relplot(
    data=tips,
    x="total_bill", y="tip", col="time",
    hue="smoker", style="smoker", size="size",
)
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson17-seabron-demo-1.png)

<!--
这一节我们通过一个简单地例子
快速入门seaborn绘图

Seaborn 是一个制作统计图形的 Python 库
它建立在 matplotlib 之上
并与 Pandas 数据结构紧密集成

与 matplotlib 不同
Seaborn 站在更高的层面帮助我们探索和理解数据
我们可以告诉 Seaborn
要探索数据之间的什么样的关系
Seaborn 就以恰当的形式将其绘制出来
而不需要我们去关心绘图细节
当然
在任何时候
我们仍然可以深入到最底层的细节
进行强大的定制


这段代码演示了 
Seaborn 是如何向我们屏蔽绘图的细节
而直接进入到数据关系的探索的

这会展示一个 facet 图
通过一次简单地调用
显示了 Tips 数据集中 5 个变量之间的关系
这些关系包括
账单额
就餐人数（size）
小费金额 (tip)
是否吸烟
时间（中午或者晚餐）


与直接使用 matplotlib 不同
我们只需要给 Seaborn 提供变量的名称及其在图中的角色
在幕后
seaborn 把数据帧中的值转换成 matplotlib 可以理解的参数
这种方法可以让我们专注于
想要回答的问题
而不是关注如何控制 matplotlib 的细节

现在
我们就来解读一下代码：

[CLK1]

第 2 行
我们导入 Seaborn 库
Seaborn 库是这个简单示例
中我们需要导入的唯一库
按照惯例
我们导入后
重命名为 sns

[CLK2]

第 5 行
 我们设置 Seaborn 使用默认主题
这使用了 matplotlib rcParam 系统
并且会影响所有 matplotlib 绘图的外观
这里要注意
即使是直接用 matplotlib 的API 来绘制
也会受到seaborn主题设置的影响
所以seaborn与matplotlib的集成是很紧密的
除了默认主题之外
还有其他几个选项
我们将在样式部分专门介绍


[CLK3]

接下来
第 8 行
我们通过 load_dataset 加载了一个演示用的数据集
这些数据集都是 csv 数据集
我们也可以通过 pandas.read_csv() 来读入


[CLK4]

第 11 到第 15 行
我们调用 relplot
一次显示了 Tips 数据集中五个变量之间的关系
如果你对上一章的内容还有印象
就能理解这里 relplot 进行的操作
就是绘制了一个基于散点图的 facet 图
-->
