---
layout: two-cols
clicks: 4
class: fade
title: 
---
# 二元分布的可视化
<hr>


::left::

```python {all|1-2|5-12|5-12|14-16} {maxHeight: '400px'}
sns.displot(penguins, x="bill_length_mm", 
            y="bill_depth_mm")


sns.kdeplot(
    penguins,
    x="bill_length_mm",
    y="bill_depth_mm",
    cmap="Greens",
    cbar=True,
    fill=True,
)

sns.displot(penguins, x="bill_length_mm", 
            y="bill_depth_mm", 
            hue="species", kind="kde")
```
::right::

<show at="0,1">

![](https://images.jieyu.ai/images/2023/07/lesson18-hist-bivariant.png)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-bivariant.png?2)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-bivar-3d.png)
</show>

<show at="4">

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-hue-bivar.png)
</show>



<!--
到目前为止
关于变量分布的示例
除了有几次通过分配给 hue 的第二个变量
有条件地展示多个变量之间的关系外
我们都只考虑了单变量
但是
我们也可将第二个变量
分配给 y 以绘制更直接的二元分布


# CLK1
直方图的二元分布如下绘制
这将产生一个类似热力图的绘图

双变量直方图绘制时
它是将数据同时在两个维度上进行分箱操作(bin cut)
这样将形成一组网格
然后用落在每个网格里的观测值计数
来作为填充颜色进行填充


# CLK2
我们也可以绘制双变量的KDE图
它使用 2D 高斯平滑的 (x,y) 观测值
生成的图类似等高线图


这里我们通过 cbar 设置了色条
从中可以看出
越居于中心位置
数值越大
这与我们的直觉相符
我们通过 cmaps 给轮廓线指定了颜色
这里的cmaps指定的一组颜色为绿色
fill是指在等值线之间
像这个点与这个点之间
是否进行颜色填充

[CLK2]
这个图实际上是
三维图在xy平面上的一个投影
其中z值是联合分布密度
尽管 3D 绘图在视觉上很吸引人
但它们很难阅读
因为绘图的某些部分被其他部分遮挡
并且并非所有应用程序都支持 3D 绘图的旋转
所以我们通过将z值转换成色彩
从而实现了降维

[CLK3]
上述两个类型的绘图中
我们仍然可以添加颜色语义
这样使得不同的物种
有自己的等值线图
不过，这种情况下
hue参数与cmaps是互斥的
所以，即使我们传入了cmaps
在指定了hue的情况下
它也会被忽略


-->
