---
layout: default
clicks: 0
class: fade
title: 
---
# Page布局
<hr>

<video controls>
  <source src="https://images.jieyu.ai/images/2023/10/pyecharts_page_layout.mp4" type="video/mp4">
</video>

<!--
PyEcharts还支持一种Page布局
这个功能相当cool
它给一些对前端知识掌握不多的人
提供了一种所见即所得的布局编辑方式
在** Page 布局**中内置了 
SimplePageLayout 和 DraggablePageLayout
后者允许你在生成图表后
手动拖拽图形进行所见即所得的布局
这会生成一个 json 格式的选项
将这个选项与生成的 html 文件一起部署
这样最终用户看到的页面
就是之前你手动拖拽生成的那一个
-->
