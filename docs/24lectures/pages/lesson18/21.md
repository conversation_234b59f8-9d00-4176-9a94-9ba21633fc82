---
layout: two-cols
clicks: null
class: fade
title: 连续调色板
---

# 连续调色板
<hr>

::left::


```python
palettes = ["rocket", "mako", "flare", "crest"]

fig, axes = plt.subplots(nrows=8, ncols=1, 
                    layout="tight", figsize=(12, 4))

i = 0
while i < 4:
    p = sns.color_palette(palettes[i], as_cmap=True)
    ax = axes[i * 2]
    ax.set(xticklabels=[], yticklabels=[])
    ax.imshow(np.arange(100).reshape((1, 100)), cmap=p, 
              aspect="auto")

    name = palettes[i] + "_r"
    p = sns.color_palette(name, as_cmap=True)
    ax = axes[i * 2 + 1]
    ax.set(xticklabels=[], yticklabels=[])
    ax.imshow(np.arange(100).reshape((1, 100)), cmap=p, 
                aspect="auto")

    i += 1

fig.subplots_adjust(hspace=0.01)
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson18-palette-reverse.png)

<!--
第二类调色板称为“连续调色板”（Sequential color palettes）
当数据取值为连续域时
使用连续调色板是最合适的
正如我们在前一张slide中所看到的
连续调色板中变化的主要维度是亮度
如果我们映射的数据是数值类型
所谓的数值类型
可以认为是实数作用域
它是与分类数据相对照的
在这种情况下
seaborn函数一般将默认使用连续调色板


在连续调色板中
又有一种所谓视觉统一的调色板
所谓视觉统一
是指两种颜色的可辨别性
与相应数据之间的差异成正比
我们在上面绘制 sin 曲线的例子中已经接触到过这个概念了
在那个图中
颜色的基调都是红色
只不过亮度不一样


在 seaborn 中
连续调色板中预定义的有 
rocket、mako、flare 和 crest
此外
matplotlib 中也有一些
比如 magma、viridis 等
每个连续调色板都有一个反转版本
其后缀为"_r"

这段代码就绘制了这样几种调色板
以及它们的反转调色板
这段代码中
有这样几点值得注意：

一、绘制调色板
在seaborn中有专门的方法
叫palplot
我们在上一个slide中有过示例

这里的rocket, mako等调色板
是同时具有离散和连续两种定义的
本来也是可以通过palplot绘制的
但我们的示例中
我们设置了 as_cmap = True
此时返回的palette就不再具有离散色定义列表
如果我们仍然使用palplot来绘制
就会抛出 ListedColorMaps has no Len 错误
因此我们就不能使用 sns.palplot 来绘制
这是这里我们使用imshow来显示调色板的原因

二、在绘制调色板时
我们通过 ax.set(xticklabels=[], yticklabels=[]) 
隐藏了数轴上的刻度及文本标签
这样会美观一些

第三，我们调用 imshow 时
指定 aspect="auto"
这对绘图有何影响
请大家自行通过修改代码
运行并体会
在下面一个示例中
我们将不加这一参数进行调用
大家也可以比较一下
-->
