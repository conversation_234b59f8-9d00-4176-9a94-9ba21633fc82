---
layout: two-cols
clicks: 5
class: fade
title: 联合分布和边缘分布
---

# 联合分布和边缘分布
<hr>

::left::

```python {all|6-16|24-38|40|42-45} {maxHeight: '400px'}
from PIL import Image
from io import BytesIO
import numpy as np
import matplotlib.pyplot as plt

def to_image(fig):
    fig.canvas.draw()

    d = np.frombuffer(fig.canvas.tostring_rgb(), 
                        dtype=np.uint8)

    d = d.reshape(
            fig.canvas.get_width_height()[::-1] + (3,)
        )

    return d

params = dict(data=penguins, 
             x="bill_length_mm", y="bill_depth_mm")

allow_hue = (True, True, False, 
            False, False, False)

kinds = ('scatter', 'kde', 'hist', 
        'reg', 'hex', 'resid')

figures = []

for i, kind in enumerate(kinds):
    if allow_hue[i]:
        fig = sns.jointplot(**params, 
                            kind=kind, 
                            hue='species')
    else:
        fig = sns.jointplot(**params, kind=kind)
        
    plt.close(fig.figure)
    _ = figures.append(to_image(fig.figure))
    
fig, axes = plt.subplots(nrows=2, ncols=3)

for i, ax in enumerate(axes.flatten()):
    _ = ax.imshow(figures[i])
    _ = ax.annotate(kinds[i], (0.5,0.5), 
                    xycoords='axes fraction')
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson18-jointplot-kinds.png)

<!--
jointplot 是一种对双变量关系图
或者双变量分布图
进行边际分布增强的图形
默认情况下
jointplot() 函数使用 scatterplot() 来表示双变量分布
并使用 histplot() 来表示边缘分布
我们可以通过指定 kind 的类型
来绘制各种联合分布

我们把各种情况都绘制出来展示一下
这段代码将 jointplot 支持的 6 种模式全部绘制出来
并输出到一个由 2 行三列的子图构成的 figure 中


这段代码中
我们遍历 kinds 元组
依次调用 sns.jointplot 方法
绘制每一类分布图
它们的参数基本相同
但只有 scatter 和 kde 这两种类型
支持 hue 这个参数
因此我们定义了一个allow_hue数组
在循环中通过条件判断来决定是否传入hue参数
注意这里我们在传递参数时
是如何对参数进行展开的


jointplot 是 figure-level 函数
我们对其每调用一次
就会生成一个 Figure 对象
为了将这些对象排列成 2*3 的图
我们可以获取这些对象的 Axes 对象
再重新生成一个 2*3 的图
并将原图中的 Axes 对象绑定到这个新生成对象中
对应的 Axes 对象上
这是我们在本次课中已经使用过的技巧


[MARK]
# 任意axes

但是
这次情况有所不同
jointplot 返回的结果中
包含了 Figure 属性
Figure 对象中又包含了 Axes 对象
但 Axes 对象有三个
我们要将这三个对象
重新在新生成的 Figure 中
对应的某个 Axes 上绑定
代码量将会比较大


# CLK1

于是
在这个示例中我们将采用另一种方法
将 jointplot 生成的 figure 转换成 Image
再通过 ax.imshow 来在恰当的位置显示它
这里的 ax
就是我们新创建的 2*3 子图中的某一个

这是将figure转换为image的代码

在前一课讲plotly的时候
我们也介绍了类似的功能
我们是通过 plotly.io 模块中的 to_image 方法
将 plotly 的绘图转换成以 RGB 值表示的 numpy array

在 matplotlib 中
没有统一的方法

这里是调用fig.canvas.tostring_rgb()这个方法
然后通过frombuffer来生成一个numpy数组
这是一个一维数组
所以我们要通过reshape方法将它重新排列
这样得到的就是一个由rgb值组成的numpy array
可以被imshow接受

为了绘制复杂的图的组合
特别是在像seaborn这种高级的图形库中
这里的技巧可能是我们必须要掌握的

# CLK2

这里我们应用不同的参数
绘制6种类型的联合分布
每次生成的图形figure
我们都将其转换成为rgb值的数组
注意第37行
这里我们关闭了figure对象
它只是作为绘图对象不存在
但仍然作为内存对象
或者说程序对象存在
如果在37行之后

我们通过plt.get_fignums来显示figure的数量
应该是多少？
这个大家可以自己练习一下
结果应该是空
因为我们将到此为止的figure对象都释放了
如果没有这一句
这些图形对象就得不到释放
将会造成资源泄漏


# CLK3
这里我们生成6个子图和一个新的fig
这些axes都是空白的子图
它们不包含任何artist对象
仅仅是占据了画布的空间的作用

# CLK4

现在
我们遍历新的空白的Axes
在它们的位置上
通过imshow将图形画出来

此外
在 jointplot 方法中
是没有办法指定图的标题的
我们重新绘制image的同时
也给每一个图叠加上了标题
在 annotate 中
需要传入要绘制的文本
xy 坐标
以及这个坐标是以何种方式对齐到坐标系的
这里我们通过 xycoords = 'axes fraction'
将其对齐到 ax 子图上
fraction表明前面的0.5应该解释为
对齐到坐标轴的中间位置
此外还可以按 figure 进行对齐


这里还有一个技巧需要交待一下
许多代码行中出现了 _ 变量
在 notebook 中使用 seaborn
我们常常需要此技巧
即将函数返回值赋值给某个哑变量
以压制某些我们不需要的绘图显示
在这里
如果不这么做
那么我们将在最后作为一个整体的图之前
还将得到 jointplot 绘制的各个子图
大家可以把这里的赋值取消掉
自行尝试一下
-->
