---
layout: two-cols
clicks: 3
class: fade
title: 
---
# 回归拟合
<hr>

::left::

```python {all|5-8|10-24} {maxHeight:'400px'}
tips = sns.load_dataset("tips")
sns.regplot(x="total_bill", 
            y="tip", data=tips)

# 多项式的阶
sns.lmplot(x="x", y="y", 
            data=anscombe.query("dataset == 'II'"),
           order=2, ci=None, scatter_kws={"s": 80})

# 使用RLM而不是OLS
fig, (ax1, ax2) = plt.subplots(1, 2)

anscombe = sns.load_dataset("anscombe")
sns.regplot(x="x", y="y", 
            data=anscombe.query("dataset == 'III'"),
           ci=None, scatter_kws={"s": 80}, ax=ax1)

sns.regplot(x="x", y="y", 
            data=anscombe.query("dataset == 'III'"),
           robust=True, ci=None, 
           scatter_kws={"s": 80}, ax=ax2)

_ = ax1.set_title("ols")
_ = ax2.set_title('rlm')
```
::right::

<show at="0">

![](https://images.jieyu.ai/images/2023/09/lesson18-regplot.png)
</show>

<show at="1">

![](https://images.jieyu.ai/images/2023/09/lesson18-lm-order-2.png)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson18-reg-ols-vs-rlm.png)
</show>

<!--
如果我们拿到一些数据
在显示这些数据之外
还要加一条回归线
通常我们需要自己来计算并绘制
在 seaborn 中
我们可以通过 regplot 来轻松绘制这种类型的图

regplot 是一个 axes-level 的绘图函数
对应的 figure-level 的函数是 lmplot
最终
它们都绘制出一样的图

[CLK1]
我们还可以在进行线性回归时
指定拟合多项式的阶数
比如

[CLK2]
在进行回归时
seaborn 也能处理离群值的影响
我们可以传入 robust 参数来消除离群值的影响


[MARK]
在右图中
我们通过传入 robust=true
来让 seaborn 使用 rlm 模型来排除离群值的影响


这次由于我们可以直接使用 axes-level 的绘图函数
所以代码要简单不少
很轻松就实现了两个图并排对照显示

当然这里也要提一下
关于离群值的处理
这里的举例是纯粹从统计学的观点
但在时间序列分析中
我们一定要看离群值出现的位置
如果离群值在时间序列的末端
那么对当下市场的冲击会比较大
特别是可能多数量化基金都能捕捉到
这种短期的冲击
并做出相应的响应
所以单个离群值的效应会被放大
因此我们还不能轻易地排除掉它
这是我们在实战中要注意的地方
-->
