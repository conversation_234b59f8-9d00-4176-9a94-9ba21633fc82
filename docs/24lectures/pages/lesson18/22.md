---
layout: two-cols
clicks: 5
class: fade
title: 
---
# 发散调色板
<hr>

::left::

```python {all}
fig, axes = plt.subplots(nrows=2, ncols=1, 
                        layout="tight", 
                        figsize=(12,4))

for i, kind in enumerate(("vlag", "icefire")):
    p = sns.color_palette(kind, as_cmap=True)
    ax = axes[i]
    ax.set(xticklabels=[], yticklabels=[])
    ax.imshow(np.arange(100).reshape((1, 100)), 
                        cmap=p, aspect='auto')
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson18-divergeing.png)

<!--

发散调色板（diverging color palettes) 适用于这样一些数据集
数据集中有着比较大的最低值和最高值
而且我们只对这些绝对值比较大的数据感兴趣
对中间部分则不那么在意
因此，数据在跨越中位数（通常为零）时
应该被弱化
这一类调色板也有所谓感知统一的说法
主要体现在 vlag 和 icefire 两个调色板上
它们的极点都使用蓝色和红色
许多人直观地将其称之为“冷”和“热”
这里的代码和语法在之前都见过了
我们这里的举例
主要是演示一下发散调色板它的视觉特征
-->
