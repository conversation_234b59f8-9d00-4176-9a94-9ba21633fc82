---
layout: two-cols
clicks: 5
class: fade
title: 
---
# 核密度估计
<hr>

::left::

```python {all|4-5|7-8|10-12|14-17|19-22} {maxHeight: '400px'}
sns.displot(penguins, x="flipper_length_mm", 
            kind="kde")

sns.displot(penguins, x="flipper_length_mm", 
            kind="kde", bw_adjust=.25)

sns.displot(penguins, x="flipper_length_mm", 
            kind="kde", bw_adjust=2)

# 增加色调语义
sns.displot(penguins, x="flipper_length_mm", 
            hue="species", kind="kde")

# 应用stack样式
sns.displot(penguins, x="flipper_length_mm", 
            hue="species", kind="kde", 
            multiple="stack")

# 使用透明度填充
sns.displot(penguins, x="flipper_length_mm", 
            hue="species", kind="kde", 
            fill=True)

```
::right::

<show at="0">

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-default.png)
</show>

<show at="1">

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-bw-025.png)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-bw-2.png)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-hue.png)
</show>

<show at="4">

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-stack.png)
</show>

<show at="5">

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-fill.png)
</show>

<!--
这一节我们介绍核密度估计图
我们前面画出来的是直方图
我们在之前讲过
如果直方图的bin的宽度能够无限趋近于零
那么这个图，理论上就变成了一个连续的线
这就是概率密度函数图
怎么让他就是无限的趋近于零
是需要通过一些统计估计的一些方法
我们在讲统计那一章时
我们讲的是核密度估计和ECDF经验分布
不过没有深入介绍核密度估计

在seaborn中直接提供了绘制kde线的方法
我们今天就来学习一下
它是使用了高斯核平滑观测值
从而产生连续密度估计

我们绘制核密度估计图
也是用displot方法
只不过传入的kind=‘kde'

[CLK1]
KDE有一个重要的参数
bw_adjust
这个参数呢是
实际上是我们在应用当中
是需要去做调参的一个参数

所以当我们把bw_adjust调到一个很小的数值时
你会发现说这个曲线就失去了平滑
检测到的波峰跟谷底就会变多对吧
那如果说我们把bw_adjust调很大
我们就会发现说他又会检测不到一些波谷
如这一段代码所示

那我们来对照一下
一个是默认值
一个是.25
结果发现它的默认值表现相当好对吧
所以你可能会奇怪
它的默认值到底是怎么算的
其实它是通过标准差
方差等这样一些方法来计算的
这种方法也是我们常用的方法之一
比如在做技术形态分析那一章
我们讲怎么探测这些峰跟谷
我们在取阈值时
也是用的标准差的倍数
只有跟标准差进行比较
才能找到真正的outlier

[CLK2]
请注意
窄带宽如何使双峰性更加明显
但曲线却不太平滑
相反
较大的带宽几乎完全掩盖了双峰性
[BACK]

[CLK3]
我们仍然可以增加色调语义

[CLK4]

也可以应用stack样式
这是我们前面在会直方图都有对吧

[CLK5]
我们也可以使用透明度的填充啊
这个就有点类似直方图的那个step的模式



-->
