---
layout: two-cols
clicks:
---
# 美化应用程序
<hr>

::left::

```python {all|11-12|18,23,34,37|20|28}{maxHeight:'400px'}
# Import packages
import pandas as pd
import plotly.express as px
from dash import Dash, Input, Output, callback, dash_table, dcc, html
from plotly.data import gapminder

# Incorporate data
df = gapminder()

# Initialize the app - incorporate css
external_stylesheets = ["https://codepen.io/chriddyp/pen/bWLwgP.css"]
app = Dash(__name__, external_stylesheets=external_stylesheets)

# App layout
app.layout = html.Div(
    [
        html.Div(
            className="row",
            children="My First App with Data, Graph, and Controls",
            style={"textAlign": "center", "color": "blue", "fontSize": 30},
        ),
        html.Div(
            className="row",
            children=[
                dcc.RadioItems(
                    options=["pop", "lifeExp", "gdpPercap"],
                    value="lifeExp",
                    inline=True,
                    id="my-radio-buttons-final",
                )
            ],
        ),
        html.Div(
            className="row",
            children=[
                html.Div(
                    className="six columns",
                    children=[
                        dash_table.DataTable(
                            data=df.to_dict("records"),
                            page_size=11,
                            style_table={"overflowX": "auto"},
                        )
                    ],
                ),
                html.Div(
                    className="six columns",
                    children=[dcc.Graph(figure={}, id="histo-chart-final")],
                ),
            ],
        ),
    ]
)

# Add controls to build the interaction
@callback(
    Output(component_id="histo-chart-final", component_property="figure"),
    Input(component_id="my-radio-buttons-final", component_property="value"),
)
def update_graph(col_chosen):
    fig = px.histogram(df, x="continent", y=col_chosen, histfunc="avg")
    return fig


# Run the app
if __name__ == "__main__":
    app.run(debug=True)
```
::right::


<!--
Dash中的每一个html控件
即在模块html之下的组件
都可以设置style属性
这些属性将在最终生成控件时
被绑定到html元素的style属性上
此外
html元素的class属性
可以通过className指定
Python中class是关键字，所以必须换名字
html元素的其它属性
只要不与Python关键字
Dash定义的组件的某些保留字冲突
都可以使用


在上面的例子中

# CLK1

我们通过第11-12行
定义并加载了外部css样式表（这可以经由设计师设计）

# clk2

在第18行、23、34、37、47等行中
使用了样式表中定义的row和six columns class

# CLK3
我们还在第20行
直接给元素定义了内联样式
指定了文本左对齐、字体使用30px及蓝色等

# CLK4
html的radio button有inline属性
我们在第28行也进行了指定
现在
三个radio选项将呈一行三列排列
而不是象之前那样
成三行排列

通过className, inline style和属性
来控制组件的外观
这些都是我们在dash下
美化应用程序的常用方法

好，今天的课我们就讲到这里
-->
