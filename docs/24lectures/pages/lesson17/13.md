---
layout: two-cols
clicks:
---
# 子图 - facet
<hr>

::left::

```python {all|1|11-12|14}
gdp = px.data.gapminder()

fig = px.scatter(
    gdp,  # 数据集
    x="gdpPercap",
    y="lifeExp",
    # 按国家所在大陆进行颜色区分
    color="continent", 
    # 按人口数决定大小 
    size="pop",  
    # 列切面字段
    facet_col="year",  
    # 每行最多 3 个图形
    facet_col_wrap=3,  
    width=800,
    height=600,
    title="faceting example",
)
fig.show()
```
::right::

![](https://images.jieyu.ai/images/2023/07/lesson17-faceting-example.png)


<!--
接下来
我们尝试给分析图增加成交量图
成交量图一般绘制成柱状图
通过这一节
我们学习如何生成多个子图
首先我们介绍一种特殊的子图
分面图(facet)


facet 图（分面图）也称为 trellis（网格图）
是由具有相同轴集的多个子图组成的图形
其中每个子图显示数据的子集
左边这段代码将国家 GDP 与人均寿命进行关联绘图
并按 year 字段进行划分
最终生成了 3 行 4 列的 facet 图

[CLK1]
这里第1行
我们通过px.data中的gapminder接口
来获取各个国家的GDP数据
返回值是一个dataframe

[CLK2]
这里告诉plotly
我们将使用year字段来进行子集的划分

[CLK3]
这里告诉plotly
每一行最多放3个子图

facet 图用来显示同一数据集的多个子集
适用范围比较窄
下面
我们就介绍更为通用的子图创建方式


-->
