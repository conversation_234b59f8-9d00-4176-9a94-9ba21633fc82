---
layout: two-cols
clicks: 
---
# K线图绘制
<hr>

::left::

```python {all|10-15}
from coursea import *
import plotly.graph_objects as go

await init()
code = "000001.XSHG"
ft = FrameType.DAY

bars = await Stock.get_bars(code, 20, ft)

fig = go.Figure(data=[
                go.Candlestick(x=bars['frame'],
                open=bars['open'],
                high=bars['high'],
                low=bars['low'],
                close=bars['close'])])

fig.show()
```
::right::

![](https://images.jieyu.ai/images/2023/07/lesson17-cs-1.png)


<!--
蜡烛图的绘制比较简单
plotly本身就提供了这个功能
注意matplotlib是不提供这个功能的

[CLK1]
蜡烛图绘制我们要使用Candlestick这个trace

第10到15行
我们除了给x轴赋值外
还对ohlc字段分别赋值
这样就可以生成一个k线图

[CLK2]
这个图形与我们常见的A股的K线图有所不同：

第一、它使用了红色表示下跌
绿色表示上涨
此外边框线的宽度与实体相比也要宽一些

第二、它使用连续坐标轴
所以在遇到节假日时
会出现断档

第三、坐标刻度上它使用了英文
我们希望看到使用纯数字或者中文

下面，我们就来介绍如何针对这些问题
进行定制
-->
