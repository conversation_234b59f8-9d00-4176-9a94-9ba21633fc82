---
layout: two-cols
clicks: 2
---
# 使用离散色彩
<hr>

::left::

<show at="0">

```python
import plotly.express as px

fig = px.colors.qualitative.swatches()
print(px.colors.qualitative.Plotly)
fig.show()
```

</show>

<show at="1">

```python
import plotly.express as px
df = px.data.gapminder()
fig = px.line(df, y="lifeExp", x="year", color="continent", line_group="country",
              line_shape="spline", render_mode="svg",
             color_discrete_sequence=px.colors.qualitative.G10,
             title="Built-in G10 color sequence")

fig.show()
```
</show>

<show at="2">

```python
import numpy as np

# EXTRACT PALETTES FROM COOLORS.CO

ysl = [
    "#4D0609","#8F2B37","#7F1C2E","#AC5754",
    "#790D1D","#FE7082","#A20D44","#B61629",
    "#CE171B","#ED322B","#F776AE","#CE242D",
    "#C91D37","#A63139","#FE52AE","#C91715",
    "#DE2025","#BE161D","#530C1E","#FE82AE",
]

x = np.arange(20)
y = [str(i) for i in np.random.randint(5, 20, 20)]
# Y = NP.RANDOM.RANDINT(5, 20, 20)

fig = px.bar(x=x, y=y, color = y,
 color_discrete_sequence = ysl)
fig.update_layout(title = "Plotly 自定义离散色彩方案")
```
</show>

::right::

<show at="0">

![](https://images.jieyu.ai/images/2023/07/lesson17-qualitative-colors.png)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson17-discrete-color-ysl.png)
</show>


<!--
这段代码将打印出 Plotly 
内置色彩方案所定义的各种颜色
以及输出一个色阶图

[CLK1]

这里我们使用了 G10 这种色彩方案来显示一条直线


在 plotly 中
定义和使用自己的离散色彩方案非常容易
这次
我们将使用上次课给出的 YSL 色彩
再实现一次柱状图配色

[CLK2]
我们只需要将 color 属性与 y 关联（如果使用 dataframe
这里要指定列名字）
并且通过 color_discrete_sequence 参数把我们定义的调色板传入就好


不过
我们也注意到第 14 行
我们将 y 序列转换成了字符串
如果 y 序列是数值类型
生成的图将不会采用离散色
而是使用连续色阶


-->
