---
layout: two-cols
clicks:
---
# 主题与模板
<hr>

::left::

<show at="0">

```python
import plotly.io as pio
pio.templates
```
</show>

<show at="1">

```python
import plotly.express as px

df = px.data.gapminder()
df_2007 = df.query("year==2007")

fig = px.scatter(df_2007,
                 x="gdpPercap", y="lifeExp", size="pop", color="continent",
                 log_x=True, size_max=60,
                 template='plotly_dark', title="Gapminder 2007: 'plotly dark' theme")
fig.show()
```
</show>

::right::

<show at="1">

![](https://images.jieyu.ai/images/2023/07/lesson17-plotly-dark.png)
</show>


<!--
在 plotly 中
模板归类在 plotly.io.templates 下
我们通过这一行代码
就可以列出来plotly当中的所有模板
我们转到notebook中看一下输出结果

我们来看看其中一个主题的效果
我们选择一个深色背景
看上去还很酷

[CLK1]

-->
