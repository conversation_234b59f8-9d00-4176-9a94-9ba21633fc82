---
layout: two-cols
clicks:
---
# 子图 - 网格属性
<hr>

::left::

```python
fig = make_subplots(rows=2, cols=2,
                    column_widths=[0.3, 0.7], 
                    row_heights = [0.8, 0.2],
                    specs=[[{}, {}],
                    [{'colspan': 2}, None]],
                    horizontal_spacing = 0.05
                    )
                    
fig.add_trace(go.Scatter(x=[1,2,3], 
                y=[2,1,2]), row=1, col=1) 
fig.add_trace(go.Scatter(x=[1,2,3], 
                y=[2,1,2]), row=1, col=2) 

fig.add_trace(go.Scatter(x=[1,2,3], 
                y=[2,1,2]), row=2, col=1) 
fig.show()
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson17-make-subplots-example.png)


<!--
在 Plotly 中
创建子图的方式相对于 matplotlib 要少一些
只能通过 plotly.subplots.make_subplots 方法来创建子图
我们一般通过 rows cols 参数
来指定要创建的子图的行数和列数
子图的大小比例 column_widths 
和 row_heights 来指定
如果需要创建异形的子图
我们需要通过 specs 参数
通过 colspan 或者 rowspan 来做单元格的合并


示例中
我们先是生成了一个 2*2 的网格
然后通过 specs 进行单元格合并
将第 2 行的第一个单元格
跨了一列
这样的结果导致
后面一个单元格的 spec 变为 None
注意
当我们使用 span 功能时
子网格的索引顺序就很重要了
在这段代码中
通过 start_cell 来指定网格索引顺序
默认是从左上角开始
即左上角的第一个单元格被记作（1
1）


单元格的的尺寸大小按比率指定
如果未指定
则它们将均分图的行宽和列高
在示例中
我们指定了第一列占图的 30%宽
第一行占图的 80%高


最后
我们通过 horizontal_spacing 
指定每个子图之间的横向间距是 5%的行宽
我们这里没有指定纵向间距
如果要指定纵向间距
则应该使用 vertical_spacing


这里对 specs 属性再进行一点讨论
specs 是一个 List[Dict]
它的元素除了我们这里看到的 colspan 之外
还有 rowspan
我们用 l r t b来分别
指定单元格的左、右、上、下内边距
此外
尽管这里没有显示
但还有一个重要参数
即 type
它用来为每个子图指定坐标轴类型
每种 trace 都有唯一的坐标轴类型
一种坐标轴类型可以容纳多种 trace
区分 trace 的坐标轴类型是必要的
比如
2D trace 和 3D trace 不可能绘制在同一个子图中
我们必须进行区分
分别绘制


-->
