---
layout: two-cols
clicks: 3
---
# 连接到数据
<hr>

::left::

<show at="0-2">

```python {all|2|13-17}
# Import packages
from dash import Dash, html, dash_table
from plotly.data import gapminder
import pandas as pd

# Incorporate data
df = gapminder()

# Initialize the app
app = Dash(__name__)

# App layout
app.layout = html.Div([
    html.Div(children='My First App with Data'),
    dash_table.DataTable(
        data=df.to_dict('records'), page_size=10)
])

# Run the app
if __name__ == '__main__':
    app.run(debug=True)
```
</show>

<show at="3">

```python

# Import packages
from dash import Dash, html, dash_table, dcc
from plotly.data import gapminder
import pandas as pd
import plotly.express as px

# Incorporate data
df = gapminder()

# Initialize the app
app = Dash(__name__)

# App layout
app.layout = html.Div([
    html.Div(children='My First App with Data and a Graph'),
    dash_table.DataTable(data=df.to_dict('records'), page_size=10),
    dcc.Graph(figure=px.histogram(df, x='continent', y='lifeExp', histfunc='avg'))
])

# Run the app
if __name__ == '__main__':
    app.run(debug=True)
```
</show>

::right::

<show at="0-2">

![](https://images.jieyu.ai/images/2023/07/lesson17-dash-1.png)
</show>

<Loc at="3" w="200%" left="-150%" top="-2vh">

![](https://images.jieyu.ai/images/2023/09/lesson17-dash-visualeffect.png)
</Loc>

<!--
接下来
我们介绍如何给刚才的应用添加最基础的逻辑和功能
向应用添加数据的方法有很多
API、外部数据库、本地 .txt 文件、JSON 文件等等
在示例中
为简单起见
我们使用plotly.data模块提供的数据

通过短短几行代码
我们就得到了一个分页显示的表格
不能不说dash的强大
尽管在notebook中
我们也能把dataframe显示成为表格
但它不能提供分页功能
而且一旦行数和列数超过预设值
就有一些行或者列不会被显示


这些强大的功能
是由dash_table带来的


[CLK1]

它是在第2行代码中被导入的


[CLK2]
第13~14行
这次我们在顶层的html.Div构造时
把dash_table.DataTable加了进来
并且设置了每页显示10条记录
div是网页中最常见的标签
或者说一种容器
它的特点是本身没有任何语义和格式
所以常常被用来选作容器
使用dash来编写网页
可能还是要对这些非常基础的网页元素等概念有所了解
可能10分钟就够了

现在
我们给这个应用增加可视化效果

# CLK3
我们通过plotly.express生成了一个直方图
这部分跟我们之前学过的并无二致
但在顶层的html.Div对象构造时
我们增加了第三项，即一个dcc.Graph
它自动封装了直方图对象
注意，直方图对象是通过figure参数来传递的

-->
