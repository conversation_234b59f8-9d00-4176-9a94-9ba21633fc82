---
layout: two-cols
clicks:
---
# px vs go
<hr>

::left::

```python {all|4-10|15-24|14} {maxHeight: '400px'}
# -- by px --
import plotly.express as px

df = px.data.iris()
fig = px.scatter(df, 
                x="sepal_width", 
                y="sepal_length", 
                color="species", 
                title="A Plotly Express Figure"
                )

# -- by go --

colors = df.species.astype('category').cat.codes
fig = go.Figure(
    data = [go.Scatter(x=df["sepal_width"], 
            y = df["sepal_length"], 
            mode="markers",
            marker = dict(
                color = colors)
            )],
    layout = dict(title=dict(text="Grpah Object"))
)
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson17-px-vs-figure.png)

<!--
这一节我们比较一下plotly express与graph objects的异同

我们同样创建一个鸢尾花数据集的散点图
看看代码量和风格上的不同之处
帮大家建立一个快速的印象
由于plotly express比较简单
我们在完成这部分介绍之后
将以graph objects为主
介绍如何通过plotly来绘制复杂的图形
后面大家自己绘制简单图形时
可以通过类比方法倒推px中的概念

[CLK1]

通过px来创建二维绘图
一般我们只需要传入dataframe作为数据集
指定x轴与y轴要关联的数据
然后指定一些定制特性
比如color, size等
这样一张图就生成了

[CLK2]
但在go的版本中
我们需要创建Scatter trace
也需要指定layout
在创建Scatter trace时
需要专门指定x, y轴关联的数据
指定生成模式为marker
并且要为marker指定颜色

[CLK3]
这一次，为marker指定颜色
要比px版本复杂不少
我们必须将species这一列的数据
转换成相应的整数
这样才能当成colormap中的一个索引来使用
为了完成这个转换
我们还要先将其转换成category
再转换成数值

所以通过这个比较我们可以看出
graph_objects对细节的把控要强很多
但操作上也会繁复不少
-->
