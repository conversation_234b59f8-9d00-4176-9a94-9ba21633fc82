---
layout: two-cols
---
# 增加交互式控件
<hr>

::left::


```python {all|18-24|29|35-47} {maxHeight: '400px'}
# Import packages
import pandas as pd
import plotly.express as px
from dash import Dash, Input, 
from dash import Output, callback
from dash import dash_table, dcc, html
from plotly.data import gapminder

# Incorporate data
df = gapminder()

# Initialize the app
app = Dash(__name__)

# App layout
app.layout = html.Div(
    [
        html.Div(children=
        "First App with Data, Graph, Controls"),
        html.Hr(),
        dcc.RadioItems(
            options=["pop", "lifeExp", "gdpPercap"],
            value="lifeExp",
            id="controls-and-radio-item",
        ),
        dash_table.DataTable(
            data=df.to_dict("records"), 
            page_size=6),
        dcc.Graph(figure={}, 
        id="controls-and-graph"),
    ]
)

# Add controls to build the interaction
@callback(
    Output(component_id=
    "controls-and-graph", 
    component_property="figure"),
    Input(component_id=
    "controls-and-radio-item", 
    component_property="value"),
)
def update_graph(col_chosen):
    fig = px.histogram(df,
     x="continent", 
    y=col_chosen, histfunc="avg")
    return fig


# Run the app
if __name__ == "__main__":
    app.run(debug=True)
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson17-dash-control.png)


<!--
这一次的代码中
我们依然导入了dcc
我们上一次导入它
是为了使用Graph组件
这次除了使用Graph之外
我们还使用了单选按钮组件dcc.RadioItems

# CLK1
在第18到22行中
我们生成RadioItems控件
为该控件提供了三个选项（options）
当前取值为"lifeExp"
因此一开始时
这个按钮将被选中
最后
我们给这个控件一个id
每一个网页元素都可以有一个ID
这个ID在一个html网页中必须唯一


# CLK2
我们在第29行
构建了一个空的图对象
它的内容
将通过radio button的选择来确定


当用户点击Radio button之后
需要服务器作出响应
从而更新页面显示

用户的输入是怎么传递给服务端的代码的？
服务端生成的结果又如何传递给客户端呢？

在这里，
Dash提供了一个通过callback注解
它有两个重要的参数
Input和Output
通过它们来接收前端的输入
并把后台程序的输出传递给前端


第35到47行
我们定义了一个名为update_graph的函数
它的作用正如名字暗示的那样
更新直方图
更新直方图的依据是
当前用户选择的新的radio button的属性值
实际上是对应的dataframe的列名字
在这里被记为col_chosen
它的输出是一个Figure对象


现在唯一的问题
就是如何将radio button的输入值传递给col_chosen参数
以及如何将update_graph函数返回的Figure对象
传递给gcc.Graph
这个机制比较复杂
我们需要知道的是
通过callback机制和Input/Output组件
Dash就将这一切联系起来


这个机制可以大致描述为
Dash维护了一个由浏览器到服务器的websocket通信
并且根据callback的定义
在前端监听注册的Input/Output组件的值的变化
当Input组件中
某个被注册的属性（比如这里radio button的value属性）发生改变时
就把这个控件的id、属性名和属性值通过websocket传递给服务器
最终进入到第29行的callback中
callback对输入值进行解码
然后调用我们的update_graph函数
并且把它生成的Figure对象串行化后
将输出对象的ID、属性名及属性值
即这里的controls-and-graph
'figure'和Figure.to_json
通过websocket返回给前端
最终完成对图对象的更新


得益于plotly.js之前打下的基础
Dash实现这些工作
工作量相对比较少


在Input/Output组件定义中
最重要的就是compoent_id
component_property
Dash通过它来定义要监听（和修改）的控件属性


-->
