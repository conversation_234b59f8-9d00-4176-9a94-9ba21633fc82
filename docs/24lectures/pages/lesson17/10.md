---
layout: two-cols
clicks: 3
---
# 修改颜色
<hr>

::left::

```python {all|15-21|15-21|13}
code = '000001.XSHG'
bars = await Stock.get_bars(code, 20, FrameType.DAY)

RED = "#FF4136"
GREEN = "#3DAA70"

cs = go.Candlestick(
    x=bars["frame"],
    open=bars["open"],
    high=bars["high"],
    low=bars["low"],
    close = bars["close"],
    line=dict({"width": 1}),
    name="K 线",
    increasing = {
        "fillcolor": "rgba(255,255,255,0.9)",
        "line": dict({"color": RED})
    },
    decreasing = dict(fillcolor =GREEN, 
            line = dict(color =  GREEN)
    )
)

fig = go.Figure(data=cs)
fig.show()
```

::right::

<show at="0,1">

![](https://images.jieyu.ai/images/2023/07/lesson17-cs-2.png)
</show>

<show at="2">

```python
RED = "rgba(255,255,255,0.9)"
cs.increasing.fillcolor = RED
cs.increasing.line.color = RED
```
</show>



<!--
修改颜色比较简单
plotly已经提供了方案
就是通过修改increasing
 decreasing等参数来实现

[CLK1]
对 increasing 和 decreasing 
我们分别使用了两种格式来赋值
两种格式都比较繁琐易错
建议使用属性访问法来赋值
这样可以利用 IDE 的属性检查和自动代码提示

[CLK2]
使用属性访问法
第 15 行我们可以改造成为
右边的代码

[CLK3]
在这里我们指定的k线图的tip线和边框线的宽度


-->
