---
layout: two-cols
clicks: 6
title: Plotly中的基本概念
---

# Plotly中的基本概念
<hr>

::left::
<v-clicks>

## px vs go
## Figure
### data (traces)
### layout
### frames
## json与Python对象的映射
</v-clicks>

::right::
<show at="1">
<br>
<br>

## Plotly Express
## ploty.graph_objects
</show>
<show at="2-5">

![](https://images.jieyu.ai/images/2023/07/lesson16-fig-json.png)
</show>

<show at="6">
<Loc color="red" left="-20%" width="100%" top="2vh">

```
fig.layout.title.font.color= "red" 

fig.update_layout(dict(title=dict(font=dict(color="red"))))

fig.update_layout({
    "title": {
        "font": {
            "color": "blue"
        }
    }
})
```
</Loc>
</show>

<!--
我们先从plotly的一些基本概念入手

[CLK1]

与matplotlib类似
在 API 组织上
Plotly 同样提供了两个层次的API集合
一个是简便易用的高级接口
称作Plotly Express

另一个是功能强大的低级别 API
称作Graph Object

按照plotly的推荐
我们应该优先使用plotly express
只有在遇到难以实现的功能时
才使用graph object

[CLK2]
与matplotlib类似，
在Plotly中
Figure同样是顶级的图形对象
它主要有三个属性

[CLK3]
[MARK]

首先是data属性

一个figure中可能包含多个图形对象（比如 line bar等）
类似于matplotlib中的artist
这些对象被称为trace
而data就是这些trace的集合

[CLK4]
[MARK]
其次是 layout属性
layout定义了图形中的尺寸、
边距、图例、模板、字体、颜色、坐标轴、标记、子图类型
和操作控件
在图中我们可以看到一部分
还有些属性没有打印出来
意味着它们将取默认值
在这个截图当中是没有frames属性的
意味着说这个图形
并不包含动画

[CLK5]
最后我们来介绍frames属性
只有Figure对象中包含动画时
才会有这个属性

在 plotly 中也有样式的概念
不过换了个名字
被称为主题和模板
另外它使用 color scale/sequence 
来代替 color map 的概念

我们在上一课中学习到的其它概念
比如坐标轴、图例等仍然适用
我们一样可以创建双轴图
轴可以设置对数坐标或者时间坐标等

[CLK6]
在plotly中
所谓图形(trace)
归根结底都是json对象
python对象实际上也是通过某种工具(swig)
由json转换生成的
所以就存在着对象属性与json字符串之间的映射
这样一来
实现同一功能
就存在多种表示方法
比如
右边这三种表示法
最终都实现了更改title颜色为红色的目标
我们可以根据自己的代码习惯来选用
当然使用属性表示法
可以得到IDE的提示
不容易出错
-->
