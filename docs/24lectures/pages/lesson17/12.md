---
layout: two-cols
clicks:
---
# 叠加技术指标
<hr>

::left::

```python {all|26-31|34} {maxHeight:'400px'}
MA_COLORS = {
    5: "#1432F5",
    10: "#EB52F7",
    20: "#C0C0C0",
    30: "#882111",
    60: "#5E8E28",
    120: "#4294F7",
    250: "#F09937",
}

ma_groups = [5, 10, 20, 30, 60, 120, 250]

bars = await Stock.get_bars("000001.XSHE",
                     130, FrameType.DAY)

# DRAW THE CANDLESTICK AND GET THE FIG
# ...

# ADD MA LINE
for win in ma_groups:
    name = f"ma{win}"
    if win > len(bars):
        continue

    ma = moving_average(bars["close"], win)
    line = go.Scatter(
        y=ma,
        x=_format_ticks(bars["frame"]),
        name=name,
        line=dict(width=1, 
             color=MA_COLORS.get(win)),
    )

    fig.add_trace(line)

fig.update_layout(height=600)
fig.show()
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson17-cs-with-ma.png)



<!--
我们现在来讨论
如何向主图中叠加技术指标
我们以叠加均线系统为例
说明如何在一个（子）图中
叠加多个 trace


首先
我们需要为均线指定颜色
尽管如果我们不指定颜色
plotly会自动运用循环色
也能够保证各条均线在颜色上能够区分开来
但这样生成的图会难以阅读
如果我们需要产生和阅读大量 k 线图
我们希望均线的颜色最好固定下来
无论阅读器是人
还是机器学习模型

[CLK1]
绘制均线的地方在第26到31行
这里我们要使用与主图相同的x轴数据
这里还指定了trace的名称
最后我们指定的线的宽度和颜色


[CLK2]
最后
我们在循环中
依次将生成的trace
通过add_trace方法
加入到figure中

主图叠加技术指标是如何实现的？
在这里的关键是
我们复用了绘制 k 线图时产生的那个 Figure 对象
该对象只包含一个子图
这个子图是默认产生的
因此
当我们给 fig 对象增一个 trace 时
并且 trace 的坐标轴也一致
这个 trace 就自然叠加在图上

接下来
我们尝试给分析图增加成交量图
成交量图一般绘制成柱状图
通过这一节
我们学习如何生成多个子图
首先我们介绍一种特殊的子图
分面图(facet)


facet 图（分面图）也称为 trellis（网格图）
是由具有相同轴集的多个子图组成的图形
其中每个子图显示数据的子集
左边这段代码将国家 GDP 与人均寿命进行关联绘图
并按 year 字段进行划分
最终生成了 3 行 4 列的 facet 图

[CLK1]
这里第1行
我们通过px.data中的gapminder接口
来获取各个国家的GDP数据
返回值是一个dataframe

[CLK2]
这里告诉plotly
我们将使用year字段来进行子集的划分

[CLK3]
这里告诉plotly
每一行最多放3个子图

facet 图用来显示同一数据集的多个子集
适用范围比较窄
下面
我们就介绍更为通用的子图创建方式
-->
