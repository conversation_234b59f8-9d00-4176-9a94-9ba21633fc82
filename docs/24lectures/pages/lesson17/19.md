---
layout: default
clicks:
---
# 色彩
<hr>

```python {all|12,13|14|15|16|17|18|21|22-24}{maxHeight:'400px'}
from plotly.subplots import make_subplots
from plotly.io import to_image
import plotly.express as px
import plotly.graph_objects as go
from PIL import Image
from io import BytesIO

fig = make_subplots(3, 2, horizontal_spacing = 0.01, vertical_spacing=0.01)

traces = []

for i, pkg in enumerate(('sequential', 'diverging', 'cyclical')):
    for j, method in enumerate(('swatches', 'swatches_continuous')):
        f = getattr(getattr(px.colors, pkg), method)
        img = to_image(f(), width=400, height=600)
        rgb = Image.open(BytesIO(img))
        trace = go.Image(z = rgb)
        fig.add_trace(trace, row = i + 1, col = j + 1)

fig.update_layout(width = 800, height = 1800)
fig.update_layout(margin=dict(l=0,r=0,b=0,t=0))
fig.update_layout(showlegend=False)
fig.update_xaxes(visible=False)
fig.update_yaxes(visible=False)
fig.show()
```



<!--
在 plotly 中
色彩主要通过两种方法来定义
其一是离散色彩序列color sequences
它映射到离散数据值上的一个色彩列表
在使用时
不会采用色彩插值
另一个可称为色阶 color scale
它是一种连续色彩表示方法
它将 [0 1] 之间的所有值映射到某个颜色域上
比如 我们可以定义 [(0 "blue")
 (1 "red")] 这样一个简单的色阶
通过在蓝色与红色之间插值
就可以提供无数种色彩方案


在 plotly 中
当前活动模板 (template) 的 layout.colorway 
属性指定了当前使用的离散色彩序列
而 layout.colorscales 则指定了当前使用的连续色彩序列
 

在 plotly 中
许多函数接受 color 参数
如果数据是数值类型
该参数会自动将数据值分配给连续颜色
如果数据是字符串类型
颜色将被视为离散的（也称为分类或定性）


plotly 中提供了许多
内置的离散色彩序列和连续色阶
这段代码将显示内置色阶

代码将遍历'sequential'
 'diverging'
 'cyclical'这三个包中
定义的所有色阶
并分别以离散和连续两种方式显示它们


这段代码使用了较多技巧
比如使用 get_attr
将 fig 转换成静态图
以及将静态图读作 RGB 数值（去掉格式串）
生成 Image trace
移除掉子图之间的间距（通过设置 lrbt 边距）等


-->
