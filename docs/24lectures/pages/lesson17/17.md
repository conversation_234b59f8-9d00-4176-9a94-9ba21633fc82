---
layout: two-cols
clicks: 1
---
# 交互式提示
<hr>

::left::

<show at="0">

![](https://images.jieyu.ai/images/2023/07/lesson17-spike-lines.png)
</show>

<show at="1">
```python
fig.update_xaxes(
    showgrid=False,
    showspikes=True,
    spikemode="across",
    spikesnap="cursor",
    spikecolor="grey",
    spikedash="solid",
    spikethickness=1,
)
fig.update_yaxes(
    showspikes=True,
    spikemode="across",
    spikesnap="cursor",
    spikedash="solid",
    spikecolor="grey",
    spikethickness=1,
)

fig.update_layout(hovermode="x unified")
```
</show>

::right::

<show at="0">
<Loc left=20%>

## Spike
</Loc>
</show>

<show at="1">

![](https://images.jieyu.ai/images/2023/07/lesson17-crosshair-x-unified.png)
</show>


<!--
在 k 线图中
我们还需要显示十字光标
并且
当光标移动时
显示个股的高开低收等信息
前者需要通过 spike 功能来实现
后者可以通过 hovertemplate 来实现
不过
在这里我们实现了一个简单的方法
自动添加了所有信息

我们先看解释什么是spike
spike 是指当前数据点到 x 轴或者 y 轴的线
正常情况下
它显示为下图中的 1 和 2 标识处的线

-->
