---
layout: two-cols
clicks: 
---
# Hellow World!
<hr>

::left::

```python {all|1|2|5-8|9-10}
from dash import Dash, html

app = Dash(__name__)

app.layout = html.Div([
    html.Div(children='Hello World')
])

if __name__ == '__main__':
    app.run(debug=True)
```
::right::


<!--
首先我们要安装dash
安装命令是 pip install dash

将这段代码存为 app.py 文件

现在
让我们安装 dash
并运行这个应用：
python app.py

首先
我们从dash中导入Dash和html
创建 Dash 应用程序时
我们几乎总是会使用上面的 import 语句


第3行被称为 Dash 构造函数
负责初始化我们的应用程序
对于我们创建的任何 Dash 应用程序来说
它几乎总是相同的


第5~7行
app.layout代表了将在 Web 浏览器中显示的应用程序组件
通常包含在 html.Div 中
在此示例中
添加了一个组件：另一个 html.Div 
 Div 有一些属性
例如 children 
我们用它来向页面添加文本内容：“Hello World”


第9~10行
用于运行我们的应用程序
对于我们创建的任何 Dash 应用程序来说
它们几乎总是相同的

程序的功能非常简单
就是一个空白的网页，
显示了“hello world”字符串
这里我们就不展示截图了

dash默认的端口是8050
我们可以在浏览器中
输出localhost:8050来打开这个网页
-->
