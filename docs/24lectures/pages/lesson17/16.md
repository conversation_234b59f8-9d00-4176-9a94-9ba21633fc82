---
layout: two-cols
clicks:
---
# 显示区域
<hr>

::left::

```python
fig.update_layout(height = 600, width=800)
fig.update_xaxes(range=[len(bars) - 120, len(bars)-1])
```

::right::


<!--
默认地
plotly 会在 Figure 中
加载全部数据
如果数据过多
它将根据指定的图形窗口大小
自动缩放 trace 元素的大小
因此
如果我们一次性提供过多的 bars
有可能导致这些 bars 会缩小到几乎看不清
我们需要通过设置显示区域来解决这个问题


这是象 plotly 这样
具有交互式绘图能力的工具才能提供的功能
具体地说
是通过设置 xaxis 的 range 来实现的
range 对象是一个二元组
分别定义了显示区域的起点和终点
根据坐标轴类型不同
指定方法也不一样
对于对数轴
如果我们要设置显示 x 从 1 到 100
则要指定 (1，2)；对于日期
可以接受字符串（但要能转换成日期）
date 对象或者 unix epoch 时间
对于我们这里的 categorical 坐标轴
则是传入下标数字


这段代码将最初的显示区域
固定在最后的 120 根 bar 下
当然
这个数值应该根据图形的尺寸来设置

[NOTEBOOK]

-->
