---
layout: two-cols
class: fade
clicks: 6
title: Plotly子模块
---

# Plotly子模块
<hr>

::left::

<show at="0-99">

## Plotly Express
</show>

<show at="2-99">

## Graph Objects
</show>

<show at="4-99">

## 其它
### I/O
</show>

<show at="6-99">

### subplots colors data Figure Factories
</show>


::right::

<show at="0">

![](https://images.jieyu.ai/images/2023/07/lesson17-px-outline.png)
</show>

<show at="1">
<Loc fc="white" left=0 width=100% color='red' top="2vh">

### import plotly.express as px
</Loc>
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson17-go-outline.png)
</show>

<show at="3">
<Loc left=0 width=100% color='red'>

### import plotly.graph_objects as go
</Loc>
</show>

<show at="4">

```python
fig = dict({
    "data": [{"type": "bar",
              "x": [1, 2, 3],
              "y": [1, 3, 2]
            }],
    "layout": {
        "title": {
            "text": "A Figure By Dictionary"
        }
    }
})

import plotly.io as pio

pio.show(fig)
```
</show>


<show at="5">

![](https://images.jieyu.ai/images/2023/07/lesson17-plot-by-dict.png)
</show>


<!--
了解基本概念之后
我们来看plotly的模块构成
Plotly主要有5个顶层模块
首先我们来看plotly express

Plotly Express 是 plotly 库的高级绘图接口
Plotly Express 提供了各种常见图形的绘制方案
包括散点图、折线图、面积图、柱状图等约 32 种图形
如果我们需要简单快速地绘制图形
可以在这个包里查找
右边这个图
分类显示了这30多种图
部分图如果不能见名知义的话
还附上了它的示例图

[CLK1]
我们习惯上这样导入Plotly.express

[CLK2]

在 Plotly 中
真正的图形类
即Trace
都定义在 plotly.graph_objects 模块中
这个模块中还包含了Figure和Layout

这些 Trace 类中
除了 plotly express 中有的都有之外
还包括了一些领域相关的
比如 Candlestick 这样仅属于金融领域的Trace类

[CLK3]
我们一般这样导入graph_objects

[CLK4]
其它接口包括I/O
I/O 模块提供了读写和渲染接口
比如
我们可以直接从一个字典来渲染成图
右边的代码中
我们仅仅定义了一个类似于json的数据结构
然后通过plotly.io就生成了一条直线

[CLK5]

其它如subplots colors data
这些后面会专门讲到
Figure Factories一般只在专门的领域中使用
本节课将不会讨论它
-->
