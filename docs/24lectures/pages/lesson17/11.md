---
layout: two-cols
clicks: 3
title: 隐藏缺失的日期
---

# 隐藏缺失的日期
<hr>

::left::

<show at="0">

## 坐标轴类型
### Linear
### log
### date
### category
### multicategory
</show>

<show at="1">

```python
import plotly.express as px
from plotly.subplots import make_subplots

fig = make_subplots(rows=1, cols=2)
fig.add_trace(go.<PERSON>atter(x=[1, 3, 9], 
                y=[1, 1, 1]), row=1, col=1)
fig.add_trace(go.Scatter(x=[1, 3, 9], 
                y=[1, 1, 1]), row=1, col=2)

fig.update_xaxes(type="category", row=1, col=2)
```
</show>

<show at="2">

```python
nticks = len(bars) // 10
fig.update_xaxes(type="category", tickformat="%y/%m/%d", nticks=nticks)

# as same as line 2
fig.update_layout(dict(xaxis=dict(type="category")))
```
</show>

<show at="3">

```python {all} {maxHeight: '400px'}
from datetime import datetime

import pandas as pd
import plotly.graph_objects as go

code = "000001.XSHG"
bars = await Stock.get_bars(code, 120, FrameType.DAY)

fmt = f"{x.item().year:02}-{...}"
def _format_ticks(tm):
    return np.array(
        [fmt for x in tm]
    )

cs = go.Candlestick(
    x=_format_ticks(bars["frame"]),
    open=bars["open"],
    high=bars["high"],
    low=bars["low"],
    close=bars["close"],
    line=dict({"width": 1}),
    name="K 线",
)

fig = go.Figure(cs)

fig.update_xaxes(
    type="category",
    nticks=10,
    ticklen=10, 
    ticks="outside",
    minor=dict(nticks=5, ticklen=5, 
            tickcolor="blue", ticks="inside"),
)

fig.show()
```
</show>
::right::

<show at="0,1">

![](https://images.jieyu.ai/images/2023/07/lesson17-ax-type.png)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/07/lesson17-candlestick-by-category.png)
</show>


<!--
在第9张的slide中
我们绘制的k线图中间是有日期空档的
为什么会出现这种情况？
这是好多人都问过的一个问题
明明我传进去的x轴的数据
在空档部分对应的数据是没有传入的
为什么还会在坐标轴上显示出来
最终导致k线图出现空档？

我们正好借这个问题
来讨论下坐标轴的一些特性

最常见的坐标都是线性的
意味着如果有三个点
 x 的取值为 [1 3 9]

各元素之间的横向距离为 [2 6]
这样 3 和 9 之间的距离就是 1 和 3 之间的 3 倍
当绘制在图上时
无论坐标使用什么样的比例尺
这些距离之间的比例都保持不变

并不是所有的坐标
当点描在图上时
这些点的横向距离都跟它们x轴的坐标成比例
比如对数坐标
点之间的图上距离
就是跟坐标的对数成正比例

如果我们使用的坐标轴是线性的
当它看到x坐标3和9时
就会默认它们之间的距离为6
而不论在实际中
这两个坐标是否相邻
当坐标轴为日期时
本质上它是按自然日历线性连续的
所以
当中间间隔了节假日时
两点之间的距离无法去掉这些节假日的影响

也就是
对线性坐标、对数坐标和日历坐标
任意两点间的距离有它们内在的计算法则

但如果我们把坐标轴指定为类别型的
则各相邻元素之间的距离均为 1
即 1 和 3 之间
3 和 9 之间
距离都为 1
我们就利用这个原理
来过滤掉节假日的影响

[CLK1]

这段代码中
我们生成了两个子图以相互对照
左图是默认的线性坐标
右图则是category坐标
可以看出
在category类型的坐标中
相邻数据点的距离就是1
无论它们在“数值”上相差多少

[CLK2]
我们通过这段代码将x轴指定为category类型

这里的第2行和第5行
在设置坐标轴类型上是等价的
我们列举在这里
主要是帮助理解在plotly中
所谓的属性树与对象层次之间的对应关系

我们把第3个问题
如何格式化坐标轴上的刻度
也一并在这里讨论了
在第2行代码里
我们设置了格式为'%y/%m/%d"
这个格式串来自于d3.js
如果需要了解更多d3.js关于时间的格式化串的话
大家可以在notebook中
找到对应的链接

为什么它要使用d3.js的格式？
这是因为
plotly最初只有js的版本
即plotly.js
在plotly.js获得成功之后
才移植到python中
而d3.js又是一个很有名的绘图库
所以plotlyjs借鉴它并不奇怪

有时候我们会想
这里的格式化串能否使用lambda表达式
或者使用一个函数呢
这种方法在其它场合是很常见的
比如在pandas中
我们对column进行格式化
既可以传入格式字符串
也可以传入lambda表达式
或者函数

但 Plotly 不允许我们这样做
而且这一决定几乎是永久性的
原因是
最终的图形渲染引擎是 plotly.js
所有不能串行化的属性都不能允许
如果 plotly.py 在这里
允许我们指定一个函数来进行格式化
而这个方法将在 plotly.js 中无法还原
因此 plotly 禁止了所有类似的需求
所以，不同的技术架构
决定了API设计方面的一些细节
如果我们精通了设计背后的原理
要理解这些API
就会觉得是跟自己开发出来的一样

[CLK3]
最终我们的代码如图所示

这里的关键是
第9行到13行
以及第16行
我们把格式化为字符串后的日期传递给x
再在第27到34行
将x轴设置为category类型

这里我们还演示了对刻度设置样式的方法
我们通过minor来设置次级刻度
通过tickcolor
 ticklen
 nticks
来分别设置tick的颜色、长度和区间内的tick个数

最后
我们还通过ticks来设置了刻度是向内
还是向外显示
这里注意蓝色的线是次级刻度
它是向内显示的

而黑色的则是主刻度
它是向外显示的

-->
