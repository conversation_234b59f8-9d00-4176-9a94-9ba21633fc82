---
layout: section
---
# /04 Dash

<!--
我们知道
如果想通过网页与用户进行交互
使用javascript是必不可少的
但很多策略研究员
以及做人工智能的研究者
需要构建一些简单的网页交互
但又没时间学习另一种开发语言
怎么办呢？

Dash 为 Python 程序员
提供了一种只用 Python 
就可以端到端完整构建网页的能力


除了 Dash
wave 和 streamlit 
也是常见的 Python 全栈构建 web 应用的框架
Wave 的服务器通过 Go 语言开发
在性能上比 Dash 会强不少
但 Dash 功能更全
社区更活跃一点
Wave 还处于比较早期的阶段
但不排除未来成为一种普通web程序
也可以用它来进行构建的框架

streamlit似乎比dash更简单
目前百度的人工智能社区
就有许多用streamlit构建的交互式应用

接下来我们就以一个例子
为大家初步介绍dash构建网页应用的一般步骤

dash也是一个服务
运行时需要开放一些端口

但在我们的实验环境中
只开放了jupyter的端口
所以这些例子
大家需要自己在本地运行
-->
