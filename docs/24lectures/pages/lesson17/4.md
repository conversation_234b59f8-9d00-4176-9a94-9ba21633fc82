---
layout: statement
clicks: 5
class: fade
title: Plotly简介
---

# Plotly简介
<br>
<v-clicks>

## 交互式
## 多种编程语言支持
## 云端协作(plotly dash)
## 响应式设计
## 语义化图表
</v-clicks>

<Loc top="25%" left="45%">

<show at="0,1">
<video controls autoplay loop height="400px">
  <source src="https://images.jieyu.ai/images/2023/07/plotly-animation.mp4?1" type="video/mp4">
</video>
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/plotly.png)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/07/dash.png)
</show>

<show at="4">

<video controls autoplay loop height="400px">
  <source src="https://images.jieyu.ai/images/2023/07/plotly-responsive.mp4?1" type="video/mp4">
</video>
</show>

<show at="5">

![](https://images.jieyu.ai/images/2023/07/lesson16-plotly-semantic-color.png)
</show>
</Loc>

<!--
Plotly是一个交互式的数据可视化库
支持多种编程语言
包括Python、R、JavaScript和MATLAB等
它可以用于生成静态图表
也可以创建交互式图表
让用户能够与数据进行探索和交互

与其它绘图框架相比
plotly主要有以下有优势

[CLK1]
1. 交互式支持

正如右图所示
随着光标移动
光标位置所在处的
数据点的信息也跟随更新显示
它还提供了一些操作工具条
我们可以借由这些工具
来拖动和缩放图形
在今天的课程中
我们还会通过编程
来显示它这一能力的一个应用
另外，我们看到的这个动画
除了光标移动之外
其它在动的部分是Plotly绘制出来的
也就是plotly支持制作动画
这是matplotlib不可能支持的

随光标显示数据点信息
这一能力称为hoverinfo
在很多场景下都非常有用
比如我们创建了一个k线分析图
在研究中我们可能会
查看每一个bar的相关信息

如果是静态图像
我们只能通过添加注释
来呈现这些信息
但这会使得图表变得混乱
如果使用 Plotly
我们则可以在开始的时候
只绘制少量信息
当用户想要获取某个数据点的更多信息
他可以通过把光标悬停在该数据点上
通过交互式绘图获得该点的详细信息

[CLK2]
2. Plotly还有多种语言支持
Plotly最初是一个基于JavaScript的图表库
在浏览器中使用
名为plotly.js
随着plotlyjs的成功
又被移植到python, R, matlab, julia等语言上

[CLK3]
3. Plotly Dash
Plotly出身于前端
即使后来移植到Python等语言上
也只是进行了翻译
最终都转化成了plotly.js绘图
这也使得它天然具有较好的云端协作能力

2017年，plotly dash诞生
从此python程序员也可以不借助html/js
也能完整地生成一个网站应用。

也正是基于这样的特性
plotly开发出来plotly dash企业版
为用户提供了SaaS服务

[CLK4]
4. 响应式设计
正是由于plotly是基于js的
所以能够实现响应式设计
也就是它够根据不同的设备
和屏幕尺寸自动调整布局和样式
无论是在桌面、平板还是手机上
Plotly图都能够以最佳的方式呈现
在右边的视频当中
我们可以看出
当窗口大小在变化时
我们生成的图不仅随之改变大小
连刻度也能根据需要进行旋转
这正是响应式的含义

[CLK5]
5. 语义化图表
我们绘图的根本目标
是为了揭示数据之间的关系
或者说数据本身存在某种语义
plotlty在这方面已经有所尝试
比如，在scatter图中
指定size为DataFrame的某一列
它就会自动调整各个点的大小
而不需要我们手动建立
绘制的点与size列的关系
我们还可以把color/hue指定为某一列
这样各个点的颜色也会按数值的大小
自动生成渐变色
关于语义化图表
在后面介绍seaborn时
我们还会看到更多的使用场景
-->
