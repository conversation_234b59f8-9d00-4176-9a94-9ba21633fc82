---
layout: toc
image: https://images.jieyu.ai/images/2023/07/lesson17-outline.png
---

<Ellipse top="20%" left="55%" s="150"/>

<Ellipse top="40%" left="55%" s="150"/>
<Ellipse top="35%" left="70%" s="120"/>

<Ellipse top="70%" left="55%"/>
<Ellipse left="75%" top="60%" s="120"/>
<Ellipse left="75%" top="65%"  s="120"/>
<Ellipse left="75%" top="70%"  s="120"/>
<Ellipse left="75%" top="78%"  s="120"/>
<Ellipse left="75%" top="85%"  s="120"/>

<Ellipse left="15%" top="25%"  s="120"/>
<Ellipse left="15%" top="30%"  s="120"/>

<Ellipse left="22%" top="40%"  s="150"/>
<Ellipse left="22%" top="63%"  s="150"/>


<!--
这一节课我们将从plotly的一些基础概念开始
然后介绍它的两个重要模块 -- plotly express和go

在介绍完这两个模块之后
我们将分步学习如何绘制一张k线图
在这里我们将接触一些重要的技巧
比如如何绘制日期坐标轴
如何叠加指标
如何增加子图
如何只显示一张很大的图中的部分区域
以及如何绘制交互式提示
在学完这一部分之后
基本上你就搞定了量化交易中所有最有挑战性的绘图任务


接下来我们会介绍如何美化plotly绘图
我们将介绍离散色彩序列和连续色阶
以及主题和模板


最后
作为量化研究员
我们也可能需要制作简单的交互式网页应用
我们将介绍如何通过plotly的一个组件
 plotly dash来实现这一目标




-->
