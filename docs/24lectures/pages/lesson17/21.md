---
layout: default
clicks:
---
# 连续色阶
<hr>

<show at="0">

```python
import plotly.express as px
df = px.data.iris()
fig = px.scatter(df, x="sepal_width", y="sepal_length",
                 color="sepal_length", color_continuous_scale=px.colors.sequential.Viridis)

fig.show()
```
</show>

<show at="1">

```python
import plotly.express as px
from textwrap import wrap

named_colorscales = px.colors.named_colorscales()
print("\n".join(wrap("".join('{:<12}'.format(c) for c in named_colorscales), 96)))
```
</show>

<show at="2">

```python
import plotly.express as px
df = px.data.iris()
fig = px.scatter(df, x="sepal_width", y="sepal_length",
                 color="sepal_length", color_continuous_scale="viridis")

fig.show()
```
</show>

<show at="3">

```python
import numpy as np

# EXTRACT PALETTES FROM COOLORS.CO

ysl = [
    "#4D0609","#8F2B37","#7F1C2E","#AC5754",
    "#790D1D","#FE7082","#A20D44","#B61629",
    "#CE171B","#ED322B","#F776AE","#CE242D",
    "#C91D37","#A63139","#FE52AE","#C91715",
    "#DE2025","#BE161D","#530C1E","#FE82AE",
]

x = np.arange(20)
y = np.random.randint(5, 20, 20)

fig = px.bar(x=x, y=y, color = y, color_continuous_scale = ysl)
fig.update_layout(title = "Plotly 自定义连续色阶方案")
```
</show>




<!--
plotly 中
前面提到的三个包：'sequential'
 'diverging'
 'cyclical'中的色阶都可以当成连续色阶
 [NOTEBOOK]

在这里，
我们通过color和color_continuous_scale
两个参数
来为每一个点指定颜色
由于color指定的sepal_length列
是数值类型
显然，我们不可能使用一个离散色阶
因此离散色阶中的颜色种类是有限的
不可能跟花萼的长度(sepal_length)一一对应
所以，我们在这里通过color_continuous_scale
来要求scatter使用一个连续色阶
colors.sequential.Viridis是plotly中
预置的一个由翠绿向嫩黄过渡的sequential色阶
它既可以用作离散色，也可以使用连续色阶

注意
在这里
如果我们没有通过color_continuous_scale来指定色阶
而是通过color_discrete_sequence
或者color_discrete_map来指定了色阶
plotly将使用默认的连续色阶
这将最终绘制出以下图形

此外
它还提供了命名色阶方案
这段代码输出了所有的命名色阶
我们可以直接使用这些名字命名的色阶
我们来使用viridis这个命名色阶
重新绘制上述散点图看看

输出结果与上一段代码完全一致

我们是否可以使用自定义色阶
用作连续色阶呢
答案是肯定的
在 YSL 的示例代码中
我们只要将 y 值修改为数值型
并将 color_discrete_sequence 参数
改为 color_continuous_scale 即可
但是这次我们看到
color bar的输出不是很理想
似乎出现了某种颜色的循环
原因是什么？

这里的原因是
我们给定的颜色并没有严格排序
或者说YSL的调色板没有办法排序
因此 colorbar 经插值后
出现了明暗相间的现象
而不是常见的单调递增
或者两端分布
所以这也是我们在前面讲YSL调色板时
强调要进行HSL转换
再进行排序的原因
-->
