---
layout: two-cols
clicks:
---
# 子图 - 成交量图

::left::


```python {all|4-10|30,47|53-60|62|63-65} {maxHeight: '400px'}

from plotly.subplots import make_subplots

fig = make_subplots(
    rows=2,
    cols=1,
    shared_xaxes=True,
    vertical_spacing=0.1,
    subplot_titles=("K 线图", "Volume"),
    row_heights=[0.8, 0.2],
)

bars = await Stock.get_bars('000001.XSHG', ...)

def _format_ticks(tm):
    return np.array(
        [f"{x.item().year:...}" for x in tm]
    )

# CANDLESTICK
cs = go.Candlestick(
    x=_format_ticks(bars["frame"]),
    open=bars["open"],
    high=bars["high"],
    low=bars["low"],
    close=bars["close"],
    line=dict({"width": 1}),
    name="K 线",
)
fig.add_trace(cs, row=1, col=1)

# MOVING AVERAGE
ma_groups = [5, 10, 20, 30, 60, 120, 250]
for win in ma_groups:
    name = f"ma{win}"
    if win > len(bars):
        continue

    ma = moving_average(bars["close"], win)
    line = go.Scatter(
        y=ma,
        x=_format_ticks(bars["frame"]),
        name=name,
        line=dict(width=1, color= MA_COLORS.get(win)),
    )

    fig.add_trace(line, row=1, col=1)
    
# VOLUME WILL BE ADD TO SECONDARY PLOT
colors = np.repeat(RED, len(bars))
colors[bars["close"] <= bars["open"]] = GREEN

vol = go.Bar(
    x=ticks,
    y=bars["volume"],
    showlegend=False,
    marker={"color": colors},
)

fig.add_trace(vol, row=2, col=1)

fig.update_layout(xaxis_rangeslider_visible=False)
fig.update_xaxes(type="category", 
                tickangle=45, 
                nticks=len(bars) // 5)

fig.show()
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson17-candlestick-with-vol.png)


<!--
有了这些知识之后
我们就可以着手绘制成交量子图了

# CLK1
第 4-10行
在 make_subplots 调用中
我们指定了 shared_xaxes = True
如果缺少这一指定
则每个子图都会显示一个 x 轴
显然不美观
其它参数我们已经讨论过了
比如
通过 row_heights 的指定
我们让 k 线图占据 80%的高度
成交量图则只占 20%的高度
通过 vertical_spacing
使得两图之间产生 10%的间距

# CLK2
第 30, 47
我们将 k 线图和均线加入到第一个子图中
这是我们之前已经讲过的代码

# CLK3
第 53~60
我们构建了成交量 trace
加入到第二个子图中
这里的row = 2 col=1
表明我们在使用第二个子图
因为我们生成的子图就是二行一列的

这里我们为柱状图的每一个 bar 指定了颜色
这是通过 marker={"color": colors}来指定的
而 colors 则是一个等长的数组
取值要么为 RED
要么为 GREEN

# CLK4
第 62 行
我们隐藏了 x 轴上的滑动区域控制条
也是出于美观的需要
在此之前的绘图中
都是存在这一控制条的

# CLK5
第 63~65 行
将 x 轴切换为 category 类型
这在之前就已经见过了
同时
我们还设置了刻度标签旋转 45 度
并且每 5 个交易日显示一次刻度
-->
