---
theme: ../theme
lineNumbers: true
aspectRatio: 16/9
transition: slide-left
layout: cover
title: 第19课 backtrader
drawings:
  presenterOnly: false
---



<!--
今天我们开始讲backtrader
这是当下最热的
免费的本地回测框架
其它的回测框架还有
zipline
backtesting.py
等等
国内的话像聚宽
Quantaxis
Vnpy
或者券商的一些平台
东方财富的MATIC
恒生的ptrade等等

backtrader是一个本地回测框架
有一些平台是在线使用的
在人多的情况下
有时候资源就很难得到保证
长期使用的话也会有一定的费用

另外有的人可能会担心
使用在线平台的话
自己的策略的安全性问题
所以这是本地回测框架的一个优势
首先是资源可以自由分配
第二就是对策略的保护
会更好一些

那么从这一课起
我们讲backtrader回测框架
整个框架的内容比较多
我们一共分两次课
整个回测模块
除了backtrader之外
我们还会讲策略评估指标
以及回测陷阱等等
-->

---
src: lesson19/2.md
title: outline
---

---
src: lesson19/3.md
title: section 1
---

---
src: lesson19/4.md
---
---
src: lesson19/5.md
---
---
src: lesson19/6.md
---
---
src: lesson19/7.md
---

---
src: lesson19/8.md
---
---
src: lesson19/9.md
---
---
src: lesson19/10.md
---
---
src: lesson19/11.md
---
---
src: lesson19/12.md
---
---
src: lesson19/13.md
---
---
src: lesson19/14.md
---
---
src: lesson19/15.md
---
---
src: lesson19/16.md
---
---
src: lesson19/17.md
---
---
src: lesson19/18.md
---
---
src: lesson19/19.md
---
---
src: lesson19/20.md
---
---
src: lesson19/21.md
---
---
src: lesson19/22.md
---
---
src: lesson19/23.md
---
---
src: lesson19/24.md
---
---
src: lesson19/25.md
---
---
src: lesson19/26.md
---
---
src: lesson19/27.md
---
---
src: lesson19/28.md
---
---
src: lesson19/29.md
---
---
src: lesson19/30.md
---
