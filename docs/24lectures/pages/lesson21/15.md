---
layout: two-cols
---
# information_ratio
<hr>

::left::

<Loc at="0,1" fc="white" left="20px">

$$
IR = \frac{E[R_p - R_b]}{\sigma} = \frac{E[R_p - R_b]}{\sqrt{var[R_p-Rb]}}
$$
</Loc>

::right::


```python {all|17-27}{maxHeight: '400px'}
from coursea import *
await init()

from empyrical import sharpe_ratio

rp = np.array([-0.01, -0.02, -0.015, 0.05, 
               -0.01, -0.01, -0.005, 0.05, 
               -0.01, -0.01, -0.02, -0.01, 
               0.07, -0.01, -0.01, -0.005,
               -0.005, 0.01, -0.005, 0.04,
               -0.005, -0.01])

bars = await Stock.get_bars("399300.XSHE", 23, FrameType.DAY)
close = bars["close"]
rb = close[1:]/close[:-1] - 1

def information_ratio(returns, benchmark_returns):
    if len(returns) < 2:
        return np.nan

    active_return = returns - benchmark_returns
    tracking_error = np.std(active_return, ddof=1)
    if np.isnan(tracking_error):
        return 0.0
    if tracking_error == 0:
        return np.nan
    return np.mean(active_return) / tracking_error

information_ratio(rp, rb)
```


<!--
信息比率通过两组数据进行计算
其一是投资组合收益
其二是基准收率
两者之差被称为超额收益（或者主动收益）
该超额收益的期望与波动性之比
即为信息比率


在本课程中
我们使用的是empyrical reloaded这个库
该库中移除了原本在empyrical包中存在的information_ratio方法
所以
在演示代码中
我们还提供了information_ratio方法的实现


我们在这里提供的示例代码
来自quantpian开发的empyrical
与本课其它示例代码不同
它进行了大量的错误处理
因此是可以作为生产级代码直接使用的


# CLK1

-->
