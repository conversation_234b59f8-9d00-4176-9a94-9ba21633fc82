---
layout: two-cols
---
# QuantStats
<hr>

::left::

<v-clicks>

## Stats
## Plots
## Reports
</v-clicks>

::right::

<show at="1">

```python {all}{maxHeight:'400px'}
adjusted_sortino                      autocorr_penalty
avg_loss                              avg_return
avg_win                               best
cagr                                  calmar
common_sense_ratio                    comp
compare                               compsum
conditional_value_at_risk             consecutive_losses
consecutive_wins                      cpc_index
cvar                                  distribution
drawdown_details                      expected_return
expected_shortfall                    exposure
gain_to_pain_ratio                    geometric_mean
ghpr                                  greeks
implied_volatility                    information_ratio
kelly_criterion                       kurtosis
max_drawdown                          monthly_returns
omega                                 outlier_loss_ratio
outlier_win_ratio                     outliers
payoff_ratio                          pct_rank
profit_factor                         profit_ratio
r2                                    r_squared
rar                                   recovery_factor
remove_outliers                       risk_of_ruin
risk_return_ratio                     rolling_greeks
rolling_sharpe                        rolling_sortino
rolling_volatility                    ror
serenity_index                        sharpe
skew                                  smart_sharpe
smart_sortino                         sortino
tail_ratio                            to_drawdown_series
treynor_ratio                         ulcer_index
ulcer_performance_index               upi
value_at_risk                         var
volatility                            warn
win_loss_ratio                        win_rate
worst
```
</show>

<show at="2">

```md
daily_returns       distribution
drawdown            drawdowns_periods
earnings            histogram
log_returns         monthly_heatmap
monthly_returns     plotly
returns             rolling_beta
rolling_sharpe      rolling_sortino
rolling_volatility  snapshot
to_plotly           warnings
yearly_returns
```
</show>

<show at="3">

```md

basic  full
html   iDisplay
iHTML  metrics
plots  relativedelta
```
</show>


<!--
quantstats主要有以下功能：

1. 计算多种性能指标
这是由stats模块实现的
2. 指标可视化
这是通过plots模块实现的
3. 生成metrics报告
这是通过reports模块来实现的


下面我们就演示quantstats的用法
在演示之前
我们先要生成一些数据
在这里也介绍下corner's RSI策略

-->
