---
layout: two-cols
clicks: 10
---
# Returns
<hr>

<Loc at="1-8" left="5%" top="30%" fc="white">

$$ 
R(t_0,t_1) = \frac{P_{t_1}-P_{t_0}}{P_{t_0}}
$$

<Box at="1" d left="-10px" w="200px" top="15%" h="60px"/>
</Loc>

::left::

<show at="0-8">

## Simple Returns
</show>

<show at="9-12">

## Simple Returns
## Log Returns
</show>

::right::

<show at="2-8">

```python {all|all|all|39|9|15-21|42,43|44-45|47,48}{maxHeight: '400px'}
%matplotlib inline

from coursea import *
await init()
    
from datetime import datetime
import backtrader as bt

from backtrader.observers import Value

class Day1Strategy(bt.Strategy):
    def __init__(self):
        self.values = []
        
    def next(self):
        if len(self) % 2 == 0:
            self.buy(size = self.broker.get_cash()/self.data.close)
        else:
            self.close()
            
        self.values.append((self.datetime.date(-1), 
                          self.stats.value[0]))
        
    def stop(self): 
        self.values.append((self.datetime.date(0),
                          self.stats.value[0]))

    
cerebro = bt.Cerebro()
cerebro.broker.set_cash(1_000_000)

code = "399300.XSHE"
bars = await Stock.get_bars(code, 1000, FrameType.DAY)
df = pd.DataFrame(bars)

data = bt.feeds.PandasData(dataname=df, datetime='frame')

cerebro.adddata(data)
cerebro.addobserver(Value)
cerebro.addstrategy(Day1Strategy)

results = cerebro.run()
values = results[0].values
values = np.array(values, dtype=[('frame', 'O'), ('values', 'O')])
values = values['values']

# 计算simple returns
returns = values[1:]/values[:-1] - 1
```
</show>

<show at="9-10">

| 资产       | 1   | 1.5   | 0.75   |
| ---------- | --- | ----- | ------ |
| 简单回报率 |     | 50%   | -50%   |
| 对数回报率 |     | 40.5% | -69.3% |

40.5% + (-69.3%) = -28.77%

$1 - e^{-28.77\%}= -25\%$

<Box at="10" left="-10px" w="250px" top="45%"/>
<Box at="10" left="-10px" w="250px" top="60%"/>
</show>


<!--
几乎所有的评估指标
都是以回报率为基础进行计算的
所以
我们这节课从回报率开始
首先
我们介绍Simple Returns


# CLK1

Simple Returns在金融领域更多地被称为Rate of Returns
即回报率
回报率代表了一个投资在一段时间内相对于初始成本的净收益或亏损的百分比
在量化策略中
一般是以日为单位
计算每一日相对于前一日的资产变化比


有时候我们也看到持有期回报（Holding Period Return）的说法
在上式中
如果t0
 t1之间的间隔是一天
则相当于一天持有回报
如果是一秒
则是一秒持有期回报


【POINTER】

这是回报率的计算公式
非常简单
它可以化简成为Pt1 /Pt0 - 1
我们在计算时
常常是以向量化的方式、成批计算每期的回报率
那时候我们会看到这个化简式的运用


# CLK2

这段代码的主要部分我们在上一节课中已经介绍过了
我们还是再讲解一下
作为对上节课的内容的一个回顾


这段代码中
我们定义了一个当日买入、次日卖出的交易策略
标的使用的是沪深300
在这段代码中
我们先向cerebro中增加了一个名为Value的observer


# CLK3

Value这个类来自于backtrader.observers包

# CLK4

然后在strategy的next方法中
通过self.stats.value[0]来获得上一期总资产并保存到strategy的成员变量values中


# CLK5

Cerebro负责实例化Strategy对象
并在回测结束后
通过results数组来返回这些实例对象
在这次回测中
只生成了惟一的一个实例化对象
因此我们可以通过results[0]来取得被cerebro实例化的strategy对象


# CLK6

进而获取每天的values数据
这里的values就是在第13行处定义的values

# CLK7
values数据是一个List[Tuple]对象
所以很适合转换为numpy structured array
每日资产就保存在'values'列中


# CLK8

最终
我们通过第49行
计算出每日的收益率
这里用的就是我们刚刚介绍的化简公式



# CLK9

与Simple Returns对应的概念是对数收益率
即log returns
在金融领域
我们常常因为以下两个原因
使用对数收益率
其一
对数收益率具有时序可加性
比如
某个标的去年涨了50%
今年跌了50%
这里使用的是简单回报率(simple returns)
如果我们将两个简单回报率直接相加
会得到平均收益率为零的结果
但实际上这笔投资怎么样？


它实际上是亏损的


如果我们使用对数收益率来计量
则这两年的对数收益率分别为40.5%和-69.3%


其和为-28.77%
再转换为简单收益率为亏损25%
这与我们的直观印象是一致的



-->
