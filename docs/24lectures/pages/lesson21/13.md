---
layout: two-cols
---
# Calmar Ratio
<hr>

::left::

$$
calmar = annual\_return(r)/mdd(r)
$$

::right::
```python
import numpy as np
from empyrical import calmar_ratio

returns = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, -0.01])

calmar_ratio(returns)
```


<!--
我们已经介绍了sharpe ratio和sortino ratio
它们都被称为 Risk-Adjust Returns
在这个类别里
还存在另一个指标
即Calmar ratio
Calmar Ratio又称为Drawdown Ratio


Calmar Ratio是由Terry W. Young创建的


其中r是过去36个月的月收益率
由于calmar基于最大回撤
因此该比率往往比夏普或者索提诺更稳定
多数时候
改变的是因子
当然
如果我们使用的收益率周期小于36个月
则显然另当别论


-->
