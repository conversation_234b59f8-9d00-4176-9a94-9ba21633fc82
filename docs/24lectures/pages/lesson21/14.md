---
layout: two-cols
clicks: 3
---
# <PERSON> Ratio
<hr>

::left::

<show at="0-2">

$$
\Omega(r)\triangleq\frac{\int_{r}^{\infty}(1-F(x))dx}{\int_{-\infty}^rF(x)dx}
$$
</show>

<show at="3">

```python
import numpy as np
from empyrical import omega_ratio

returns = np.array([-0.01, -0.02, -0.015, 0.05,
                    -0.01, -0.01, -0.005, 0.05,
                    -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,
                    -0.005, 0.01, -0.005, 0.04,
                    -0.005, -0.01])

for rf in (0, 0.03, 0.2, 0.4, 0.6):
    print(f"{omega_ratio(returns, rf/252):.2f}")
```
</show>

::right::

<show at="0">

![](https://images.jieyu.ai/images/2023/08/lesson21-omega-ratio.png)
</show>

<show at="1,2">

```python {all|all|2,12}
import numpy as np
from empyrical import omega_ratio

returns = np.array([-0.01, -0.02, -0.015, 0.05,
                    -0.01, -0.01, -0.005, 0.05,
                    -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,
                    -0.005, 0.01, -0.005, 0.04,
                    -0.005, -0.01])

for rf in (0, 0.03, 0.2, 0.4, 0.6):
    print(f"{omega_ratio(returns, rf/252):.2f}")
```
</show>

<Loc at="3" fc="white">

```
1.29
1.28
1.18
1.08
0.99
```
</Loc>


<!--
Omega Ratio发表于2002年
由Keating和Shadwick在一篇名为《A Universal Performance Measure》的文章中提出
它使用希腊字母的最后一个字母来命名
以显示它是一个终极性的指标


左式是omega ratio的定义
这个公式的分子部分是上偏矩
分母部分是下偏矩
通常我们用定积分进行计算
而阈值r是我们设定的临界收益率
临界收益率用来区分收益或损失
高于临界收益率的
视为收益
低于临界收益率的
视为损失
也可以称为最低要求收益率
Omega比率利用了收益率分布的所有信息
考虑了所有的高阶矩
刻画了收益率风险的所有特征


我们可以使用右图来理解omega ratio
Omega 比率就等于上方绿色面积除以下方红色面积
随着r向右移动
代表目标收益在增长
但实现的概率也在变小
因此
该指标也可以适合不同风险偏好的投资者
对于风险容忍度较低的投资者
可以选择较低的临界收益率
反之
可选择较高的临界收益率


# CLK1
这段代码演示了如何计算omega ratio


# CLK2
使用的方法是empyrical中的omega_ratio

# CLK3
右边是演示代码的输出结果
一般认为
比率大于 1 表明风险调整后的业绩良好
该投资组合实现高于目标水平的回报的可能性较高
而产生重大损失的可能性较低
比率等于 1 意味着阈值与投资的平均回报相匹配
该投资组合有 50% 的概率实现高于目标水平的回报
有 50% 的概率发生重大损失
 比率小于1表明该投资组合实现高于目标水平的回报的可能性较低
而遭受重大损失的可能性较高
表明该投资组合的风险调整后表现较差


因此
在这个示例中
该策略要实现大于40%年化的可能性高于50%
但要实现大于60%的年化收益的可能性则低于50%


从这个演示来看
omega能够根据投资者不同的风险偏好
给出收益值
确实在某种程度上
可算作是终极指标


Omega指标的主要缺陷之一
就是它本身是目标收益水平的函数
因此不便于在不同的投资策略间进行比较



-->
