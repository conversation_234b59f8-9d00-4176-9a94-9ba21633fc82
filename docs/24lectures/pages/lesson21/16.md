---
layout: two-cols
---
# Alpha/Beta
<hr>

::left::

<Loc at="0,1" fc="white" top="0" left="25%">

$$
y = \alpha + \beta x + \epsilon
$$

<Box left="-20px" top="3vh"/>

$$
\beta = \frac{cov(x, y)}{\sigma_x^2}
$$

<Box left="-20px" top="12vh" h="4rem"/>

$$
\alpha = \frac{y-\epsilon}{\beta}
$$

<Box left="-20px" top="23vh" h="4rem"/>

</Loc>

::right::

```python {all|1,15}
from empyrical import alpha_beta
import datetime

rp = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
               -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
               0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
               -0.005, 0.04, -0.005, -0.01])

end = datetime.date(2023, 8, 23)
bars = await Stock.get_bars("399300.XSHE", 23, 
                            FrameType.DAY, end=end)
close = bars["close"]
rb = close[1:]/close[:-1] - 1

alpha_beta(rp, rb, 0.03/252)
```


<!--
威廉.夏普把金融资产的收益拆成两部分：跟随市场一起波动的部分叫贝塔收益
不随市场一起波动、与市场无关的部分就叫阿尔法收益
Alpha 和 Beta 是用于评估股票、基金或投资组合绩效的两个关键衡量指标
 Alpha 衡量的是与市场指数或其他广泛基准相比的投资回报金额
 Beta 衡量投资的相对波动性
它表明了其相对风险


Alpha总是越高越好
高Beta可能受到成长型投票投资者的亲睐
但寻求稳定回报和较低风险的投资者却会回避


【MARK】

在这个公式中
 y是策略收益
x是基准收益戓市场收益
 𝛼就是不随市场波动的收益部分
𝛽是随市场一起波动的部分
是风险因子
𝜖 则是无法归因的随机收益残差
但在计算中
我们一般用无风险利率来代替


【MARK]
𝛽的计算类似于协相关性计算
只不过它把分母中的Var[X]Var[Y]都换成了Var[X]
这样最终变成除以基准的方差
即基准标准差的平方


【MARK】
计算中
我们一般先求𝛽
再求𝛼


# CLK1
在实际运用中
我们可以通过empyrical中的alpha_beta方法来求解
并且用无风险利率来代替残差


在这里的例子中
我们最终得到投资rp的alpha值为61.8%
而beta值为-0.2
沪深300取的是2023年8月21日
这段时间以下跌为主
因此
两者的相关性为负
注意
这里的alpha值是年化的收益


-->
