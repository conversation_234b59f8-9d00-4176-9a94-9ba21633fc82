---
layout: two-cols
clicks: 2
---
# 年化波动率
<hr>

::left::
<Loc at="0-2" fc="white" left="15%">

$$
vol = \sigma_r\sqrt{t}
$$

<Loc at="1" fc="white" left="0%" top="70%">

![](https://images.jieyu.ai/images/2023/08/lesson21-annual-volatility.png)
</Loc>
</Loc>



::right::

```python {all|all|2,13}
import numpy as np
from empyrical import annual_volatility

returns = np.array([-0.01, -0.02, -0.015, 
                    0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01,
                    -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, 
                    -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, 
                    -0.01])

av = annual_volatility(returns)
# av is 0.403
```


<!--
波动率表明资产价格围绕均值的摆动幅度
是衡量回报分散性的统计指标
通常认为
波动性高的资产会比波动性小的资产风险更高
因为价格的可预测性更低


从数学上看
波动率就是收益的标准差
但波动率与标准差有一个关键区别
就是波动率是与时间区间绑定的
因此
我们常常讨论的波动率
是指周波动率、月波动率或者年化波动率等等
相应地
计算公式也需要在标准差的基础上
进行时间因子调整


在这个公式中
如果r（returns）是按天计算的收益率
vol是年化波动率
则t为252
如果vol是周波动率
则t为5（或者7
视交易品种而定）


一般我们是对这个t进行开方运算
但也可能是

# CLK1

这里的 𝛼 是levy稳定系数
一般默认取2


# CLK2

我们通过empyrical的annual_volatility来计算年化波动率

-->
