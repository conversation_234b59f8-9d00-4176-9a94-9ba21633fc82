---
layout: default
---
# Corner's RSI
<hr>

```python {all}{maxHeight: '400px'}
class Streak(bt.ind.PeriodN):
    '''
    Keeps a counter of the current upwards/downwards/neutral streak
    '''
    lines = ('streak',)
    params = dict(period=2)  # need prev/cur days (2) for comparisons

    curstreak = 0

    def next(self):
        d0, d1 = self.data[0], self.data[-1]

        if d0 > d1:
            self.l.streak[0] = self.curstreak = max(1, self.curstreak + 1)
        elif d0 < d1:
            self.l.streak[0] = self.curstreak = min(-1, self.curstreak - 1)
        else:
            self.l.streak[0] = self.curstreak = 0

class ConnorsRSI(bt.Indicator):
    '''
    Calculates the ConnorsRSI as:
        - (RSI(per_rsi) + RSI(Streak, per_streak) + PctRank(per_rank)) / 3
    '''
    lines = ('crsi',)
    params = dict(prsi=3, pstreak=2, prank=100)
    params = dict(prsi = 3, pstreak = 2, prank = 20)

    def __init__(self):
        # Calculate the components
        rsi = bt.ind.RSI(self.data, period=self.p.prsi)
        streak = Streak(self.data)
        rsi_streak = bt.ind.RSI(streak.data, period=self.p.pstreak)
        prank = bt.ind.PercentRank(self.data, period=self.p.prank)

        # Apply the formula
        self.l.crsi = (rsi + rsi_streak + prank) / 3.0

class ConnerRSI(bt.Strategy):
    params = (('low', 12), ('high', 58))
    
    def __init__(self):
        self.myind = ConnorsRSI()
        self.assets = []

    def notify_order(self, order):
        if order.status == order.Margin:
            print(order)
        
    def next(self):
        if self.myind.crsi[0] <= self.p.low:
            size = self.broker.get_cash()/self.data.close[0]
            if size > 100:
                self.buy(size=size * 0.95)
        elif self.myind.crsi[0] >= self.p.high:
            self.close()
            
        self.assets.append((self.datetime.date(-1), self.stats.value[0]))
        
    def stop(self): 
        self.assets.append((self.datetime.date(0),
                          self.stats.value[0]))
```

<!--
Corner's RSI是对经典RSI的改良
经典RSI只在空间的维度上预测趋势的反转
Corner's RSI给它加上了时间维度和概率的维度
从行为金融学的角度来看
更有利于捕捉促使趋势反转的那些市场情绪


比如
从趋势的角度看
当一段上涨的幅度足够时
多数人可能开始计划兑现利润
从时间的维度看
刚开始下跌那一段时间
交易者可能还沉浸在市场之前的火热气氛中
在下跌过程中仍然愿意去接飞速下跌的刀
此时市场还没有跌透
从概率的维度看
如果出现近期不常见的大的涨幅
则可能意味着行情已经加速
显然上涨也快到了尽头
等等


详细介绍在我们公众号里
代码在notebook的examples目录下
我们这里就不仔细讲解了


我们选择了过去1000天
相当于4年左右的时间
对沪指进行了回测
得到了21.6%左右的收益
同期沪指上涨4.9%
超额比较明显


下面
我们就以这个回测数据为例
来展示quantstats的报告生成能力


-->
