---
layout: two-cols
clicks: 6
---
# <PERSON>
<hr>

<Loc at="1-5" fc="white" left="0" top="11vh">

```python {|all|3|6|9-10|12|all}
import numpy as np
n = 20
rf = 0.03

np.random.seed(78)
returns = np.random.normal(size=(n))/100

def sharpe_ratio(returns, annual_rf, annual_factor):
    # calc risk-free adjust returns
    ra = returns - annual_rf/annual_factor

    return np.mean(ra)/np.std(ra, ddof=1) * np.sqrt(annual_factor)

sharpe_ratio(returns, rf, 252)
```
</Loc>

<Loc at="6" fc="white" left="0" top="11vh">

```python {6}
from empyrical import sharpe_ratio

np.random.seed(78)
returns = np.random.normal(size=(n))/100

sharpe_ratio(returns, annual_rf/252)
```
</Loc>

::left::
<show at="0">

![](https://images.jieyu.ai/images/2023/08/lesson21-william-sharp.png)
</show>
::right::

<Loc at="0" fc="white" left="20%" top="0">

$$
Sharpe\ Ratio = \frac{E_{R_p} - R_f}{\sigma_p}
$$

$E_{R_p} = 组合的期望收益率$<br>
$R_f = 无风险利率$<br>
$\sigma_p = 资产超额利润的标准差$

</Loc>


<!--
夏普率由著名经济学家威廉.夏普于1966年发表
威廉.夏普也是著名的资产定价模型(Capital assets pricing model
 CAPM)理论创始人之一
他于1990年获得了诺贝尔经济学奖


注意sharpe率的单词
是以e结尾
因为sharpe率是以夏普的名字命名的


这个公式来自于维基百科
是对夏普原公式的一个化简
这里的sigma(p)
资产超额利润的标准差
就是指收益率减去无风险收益率


这里也特别提示一下
左边的图来自于investopedia
这是一个重要的百科类金融网站
一些很重要的金融术语都能在上面找到解释
但是
关于夏普率的公式
它是错误的
它使用的是Rp - Rf
而不是Rp的数学期望


我们演示一下sharpe ratio的计算
这会对深入理解上述公式有帮助.

# CLK1

这段代码定义了一个名为sharpe_ratio的方法
通过它来计算夏普率
同时
我们模拟了一个收益率序列
通过它和一个指定的无风险收益率来演示夏普率的计算


# CLK2
第3行
我们先定义无风险收益率为3%
这大致相当于国债的收益率
注意这个收益率是年化的


# CLK3
第6行
我们首先是生成了一个20天的收益率


# CLK4
我们先是计算了通过无风险收益率调整后的收益率
注意我们给的rf是年化的
而returns是日收益
所以我们需要先将annual_rf除以annual_factor
如果我们忘记这样做
那么计算出来的夏普率将是负数


# CLK5
这里就是根据公式
用调整后的收益率 -ra的期望
除以ra的标准差


代码中不太容易懂的地方是为什么要乘以np.sqrt(annual_factor)
这跟我们作为输入的returns的周期有关
本质上
为了便于比较
sharpe ratio是一个年化指标
而在示例中
我们把returns当成日回报收益率
rf当成是年化无风险收益率
这种情况下
np.mean(ra)需要乘以年化因子252
才能得到年化收益率
对ra的标准差部分
我们也需要进行同样的调整
最终结果就是需要乘以np.sqrt(annual_factor)


当returns数组是日收益率时
annual_factor是252
当returns数组是月收益率时
则annual_factor为12
以此类推


# CLK6

在调用empyrical的sharpe_ratio时
我们要注意一点
关于无风险收益率
我们需要先将它调整为与returns对应周期的收益率


-->
