---
layout: default
clicks: 5
---
# Max Drawdown
<hr>

```python {all|2,9|11-13|15-18}
import numpy as np
from empyrical import max_drawdown

returns = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, -0.01])

mdd = max_drawdown(returns)

xs = returns.cumsum()
i = np.argmax(np.maximum.accumulate(xs) - xs) # end of the period
j = np.argmax(xs[:i]) # start of period

ev = equity_value(returns)
plt.plot(ev, label=f"mdd: {mdd:.2%}")
plt.plot([i, j], [ev[i], ev[j]], 'o', color='Red')
plt.legend()
```

<TopLayer at="4" top="11vh">

![50%](https://images.jieyu.ai/images/2023/08/lesson21-mdd-marked.png)
</TopLayer>


<!--
Max DrawDown是指账户净值从最高点的滑落程度
它可以帮我们了解从任意一点进场
策略可能承担的最大损失


# CLK1

我们一般使用empyrical的max_drawdown来进行计算

# CLK2
这里的代码也演示了如何手动计算mdd
我们在这里手动计算
目的是为了绘图


# CLK3
这里我们将mdd的区间绘制出来


# CLK4
这里代码运行的结果


-->
