---
layout: two-cols
clicks: 9
---
# Returns
<hr>

<Loc at="0" top="50%" left="15%" fc="white">

![](https://images.jieyu.ai/images/2023/08/lesson21-sr-vs-log-r.png)
</Loc>

<Loc at="5" fc="white" top="20vh">

### pip install empyrical-reloaded

<Box d top="0%" left="10%" w="350px"/>
</Loc>

::left::

<show at="0">

## Simple Returns
## Log Returns
</show>

<show at="1-5">

## Simple Returns
## Log Returns
## Cumulative Returns
</show>

<show at="6-9">

## Simple Returns
## Log Returns
## Cumulative Returns
## Aggregate Returns
</show>

::right::

<show at="0">

| 资产       | 1   | 1.5   | 0.75   |
| ---------- | --- | ----- | ------ |
| 简单回报率 |     | 50%   | -50%   |
| 对数回报率 |     | 40.5% | -69.3% |

</show>

<show at="1">

| 2015-07-16 | -0.012143 |
| ---------- | --------- |
| 2015-07-17 | 0.045350  |
| 2015-07-20 | 0.030957  |
| 2015-07-21 | 0.004902. |
</show>

<show at="2-4">

```python {all|all|5-6|8-9|11-13}
>>> import numpy as np

>>> returns = np.array([-0.012143, 0.045350, 0.030957, 0.004902])

>>> np.cumprod(1+returns) - 1
array([-0.012143  ,  0.03265631,  0.06462426,  0.06984304])

>>> np.exp(np.cumsum(np.log(1 + returns))) - 1
array([-0.012143  ,  0.03265631,  0.06462426,  0.06984304])

>>> from empyrical import cum_returns
>>> cum_returns(returns)
array([-0.012143  ,  0.03265631,  0.06462426,  0.06984304])
```
</show>

<show at="6">

| 2015-07-16 | -0.012143 |
| ---------- | --------- |
| 2015-07-17 | 0.045350  |
| 2015-07-20 | 0.030957  |
| 2015-07-21 | 0.004902. |
</show>

<show at="7">

```python
from empyrical import aggregate_returns
import datetime

returns = pd.Series(returns, 
                    index=[datetime.date(2015,7,16), 
                           datetime.date(2015,7,17), 
                           datetime.date(2015,7,20),
                           datetime.date(2015,7,21)]
                   )
for period in ('weekly', 'monthly', 'yearly'):
    print(aggregate_returns(returns, period))

# --- output ----
aggregated weekly
2015  29    0.032656
      30    0.036011

aggregated monthly
2015  7    0.069843

aggregated yearly
2015    0.069843
```
</show>

<show at="8">

```python {11}
from empyrical import aggregate_returns
import datetime

returns = pd.Series(returns, 
                    index=[datetime.date(2015,7,16), 
                           datetime.date(2015,7,17), 
                           datetime.date(2015,7,20),
                           datetime.date(2015,7,21)]
                   )
for period in ('weekly', 'monthly', 'yearly'):
    print(aggregate_returns(returns, period))

# --- output ----
aggregated weekly
2015  29    0.032656
      30    0.036011

aggregated monthly
2015  7    0.069843

aggregated yearly
2015    0.069843
```
</show>

<show at="9">

```python {13-22}
from empyrical import aggregate_returns
import datetime

returns = pd.Series(returns, 
                    index=[datetime.date(2015,7,16), 
                           datetime.date(2015,7,17), 
                           datetime.date(2015,7,20),
                           datetime.date(2015,7,21)]
                   )
for period in ('weekly', 'monthly', 'yearly'):
    print(aggregate_returns(returns, period))

# --- output ----
aggregated weekly
2015  29    0.032656
      30    0.036011

aggregated monthly
2015  7    0.069843

aggregated yearly
2015    0.069843
```
</show>


<!--
另一个原因则是建模相关的
收益是复利
是指数相关的
如果收益波动较小的话
它的对数将会拟合成一条直线
这样比较方便看出来收益是否稳健
类似的原理我们在QQ图里介绍过


有一种观点认为
通过对数据取对数
可以实现降维
这个观点在一定条件下是正确的
从上面的图形可以看出
如果数据取对数后能够较好地拟合成直线
那么确实可以降维


这里的两个图都是日收益为10%的资产曲线图
从左图我们看不出来它的规律
但从右图我们可以很清楚地知道
该投资是以非常稳健的收益在增长


# CLK1

第三个我们要掌握的概念
是cumulative returns（累积回报）
与其说它是某种回报类型
不如说它是一种计算方法：它是由一组时间上连续的简单回报以复利的方式计算累积回报的一种方法
在右边的表格中
每一行都是一个简单回报
那么截止到2015年7月21日
我们在这笔资产上的累积收益应该如何表示？
这就是cumulative returns的含义


Cumlative Returns本质上仍然是Rate of Returns


这里我们分别使用了三种方法来计算累积回报


# CLK2
首先我们使用了np.cumprod方法
它是将数组从第0个元素起
一直累乘到第n个元素止
并将其结果作为结果数组的第n个元素的值的计算方法
要通过cumprod和简单回报来计算累积回报
我们需要先给每日回报加上1
然后进行累乘
最后减去基数1


# CLK3
第二种方法是对收益取对数
然后进行累加
这里我们先对每日收益取对数
然后进行累加
最后进行指数还原
减去1
就得到每日的累积收益



# CLK4
第三种方法是使用empyrical的API
cum_returns来进行计算


empyrical是quantpian开发并开源的进行风险评估的核心库之一
最初在pyfolio还可以使用时
我们一般不直接使用它 -- 在我们使用pyfolio时
就已经间接使用了empyrical
但在quantpian不再维护pyfolio之后
有开发者将其剥离出来进行维护
并将期发行为empyrical-reload
在本课程中使用的empyrical
实际上是empyrical-reload这个库：

# CLK5

pip install empyrical-reloaded

# CLK6

这个概念可能与cumulative returns一样
更多地存在于量化交易中
它是指让我们把按日计算的收益率
聚合成按周、月、季或者年为单位的收益率


在这里给出的日收益示例（表格一）中
我们分别按周、月、年进行统计
就得到了每周、每月、每年的聚合收益


# CLK7

我们通过这段代码来进行聚合统计
这里的关键是第11行

# CLK8
使用了empyrical的aggregate_returns方法
从输出可以看到
按周聚合
产生了两行结果
按月和年聚合
都只有一行结果
并且结果相同
这是因为
我们的数据只跨了两周
这个数据也说明
聚合收益只是按指定的时间周期
计算周期内的累积收益
并未执行其它操作



-->
