---
layout: default
clicks: 7
---
# <PERSON>
<hr>

<show at="0-6">

```python {all|19|20|22|23-27|33-37|5-6}{maxHeight:'400px'}
import numpy as np
from empyrical import sharpe_ratio
import matplotlib.pyplot as plt

def equity_value(returns):
    return np.cumprod(1 + returns)

n = 252
rf = 0.03/252

np.random.seed(78)
returns = np.random.normal(size=(n))/100

results = {}
target = np.array([1, 1.5, 2, 2.5, 3])
found = {}

for i in range(10000):
    returns = np.random.normal(size=(n))/100
    sharpe = sharpe_ratio(returns, rf)
    
    if np.any(abs(sharpe - target) < 0.1):
        pos = np.argmin(abs(sharpe-target))
        key = target[pos]
        if key not in found or abs(found[key] - key) > abs(sharpe - key):
            results[sharpe] = returns
            found[key] = sharpe
            
    if found == set((1, 1.5, 2, 2.5, 3)):
        break

sharpes = sorted(set(found.values()))
for sharpe in sharpes:
    returns = results[sharpe]
    ev = equity_value(returns)
    plt.plot(ev, label=f"夏普率:{sharpe:.1f}")
    plt.legend()
```
</show>

<show at="7">

![50%](https://images.jieyu.ai/images/2023/08/lesson21-sharpe-equity.png)
</show>


<!--
这段代码用来演示sharpe比率与资产曲线之间的关系
通过它我们可以直观地看到sharpe比率为何能成为策略的评估指标
在这段代码中
我们希望绘制当sharpe比率为1
 1.5
直到3时的资产曲线


sharpe比率是收益率的函数
反过来则不是
一个sharpe比率
可以对应着无穷多的收益序列
因此
我们要绘制某个sharpe比率对应的资产曲线
只能反向暴力搜索


这一次我们使用了长达一年的日线收益率
无风险收益还是3%
我们在一个无限循环中
随机生成一组收益

# CLK1

然后计算它的sharpe比率

# CLK2

再看这个sharpe比率是否贴近了目标中的sharpe比率
即1到3之间的任意一个

# CLK3

# CLK4
如果是
我们找出来具体是哪一个数值比较接近
 并且将这个sharpe率、及returns保存到results中
并且如果新生成的returns数组
它的sharpe比率比之前保存的更接近目标
我们还会进行替换


# CLK5
最后
我们按sharpe比率
从小到大
绘制资产图
这里我们调用了一个名为equity_value的方法

# CLK6
它实际上就是一个简单的np.cumprod调用

最终
我们将得到这样一张图：

# CLK7
一般认为
夏普率大于1.5
才能产生稳定的正收益
大于2
则策略很可能值得推荐


夏普率是基民选择基金时常用的比较指标之一
实际上它也可以成为我们量化策略的一个因子
当我们使用夏普比率时
需要了解它的局限性
夏普比率在计算时引入了正态分布假设
但我们已经知道
几乎没有任何一种投资品的收益是完全符合正态分布的
这是它的局限之一
另外一方面
考虑到碾路机前捡钢磞的比喻：你可以很长一段时间都能捡到钢蹦
但最终碾路机会到来
导致巨大的损失
sharpe ratio就是这样
它可能因一系列很小的、稳定的收益产生较高的数值
但一旦发生几笔大额亏损
夏普率也将迅速下降
它是一种事后指标
不具备预测能力


另外
有一些策略
平时亏损很小
但正收益都会比较高
因此
它的标准差也不会低
计算下来
尽管策略的总体收益不错
风险也很小
但夏普率可能并不高
这也是夏普率的局限


下面
我们就来看如何改进夏普率指标


-->
