---
layout: two-cols
clicks: 4
---
# Returns
<hr>

::left::

<show at="0-5">

## Simple Returns
## Log Returns
## Cumulative Returns
## Aggregate Returns
## Annual Return
</show>
::right::

```python {all|10|3}
from empyrical import annual_return

returns = np.array([-0.012143, 0.045350, 0.030957, 0.004902])
returns = pd.Series(returns, 
                    index=[datetime.date(2015,7,16), 
                           datetime.date(2015,7,17), 
                           datetime.date(2015,7,20),
                           datetime.date(2015,7,21)]
                   )
annual_return(returns)
```

<Loc at="3" fc="white" top="60%" left="0">

$$
CAGR = (1 + P_n)^{(252/n)} - 1
$$
</Loc>

<Loc at="4" fc="white" top="60%" left="0">

$$
(1 + 0.069843)^{(252/4)} - 1 = 69.33
$$
</Loc>


<!--
Annual Return
即年化收益率
年化收益率是我们比较投资品的一个重要指标
有时候我们也使用CAGR （Compounded Annual Growth Rate)这个说法
都是一样的含义
它是指按年为单位
通过其它数据计算出来的一个年化均值
比如
在Aggregate Returns的例子中
我们得到的按年聚合收益是7%左右
但实际上这个收益是在4个交易日内取得的
如果我们假设这四个交易日的收益均值能够保持一年
那么这样一年下来的收益
就是Annual Return


# CLK1

我们可以通过empyrical包的中annual_return方法来计算年化收益率
在这个示例中
我们将得到69倍左右的年化收益


# CLK2
这四天的累积收益率是6.9843%
相当于我们之前计算出的聚合月收益率、或者聚合年收益率
我们也可以直接通过下面的公式来计算年化

# CLK3
在公式中
Pn是n日累积收益率
 252是一年的交易天数
需要根据具体情况进行调整.

# CLK4
最终按公式
我们将算出与empyrical.annual_return一样的结果


-->
