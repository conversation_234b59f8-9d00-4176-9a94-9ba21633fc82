---
layout: two-cols
---
# Metrics文本输出
<hr>

::left::
```python
qs.reports.metrics(assets, mode='basic')
```
::right::
```
                    Strategy
..................  ..........
Start Period        2019-08-09
End Period          2023-08-24
Risk-Free Rate      0.0%
Time in Market      63.0%

Cumulative Return   21.58%
CAGR﹪              4.95%

Sharpe              0.48
Prob. Sharpe Ratio  82.41%
Sortino             0.66
Sortino/√2          0.47
Omega               1.11

Max Drawdown        -19.98%
Longest DD Days     699

Gain/Pain Ratio     0.11
Gain/Pain (1M)      0.41

Payoff Ratio        1.02
Profit Factor       1.11
...
```


<!--
我们通过qs.reports.metrics方法来生成简单的、基于文本的报告
该方法主要有两个参数
其一就是每日资产
它是一个pd.Series
索引为日期
另一个参数是mode
可选值有'basic'和'full'两种


-->
