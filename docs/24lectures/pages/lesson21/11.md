---
layout: default
clicks: 1
---
# 夏普率与最大回撤的关系
<hr>

<show at="0">

![](https://images.jieyu.ai/images/2023/08/lesson21-draws-as-function-of-sharpe.png)
</show>

<show at="1">

![](https://images.jieyu.ai/images/2023/08/lesson21-sharpe-mdd-relation.png)
</show>


<!--
如果你通过夏普率排序
购买了一支基金
或者自己的策略在历史回测中
得到了一个不错的夏普率
于是你将它打入实盘


在实际交易的进程中
sharpe比率肯定会随时波动
单纯从sharpe比率来看
你无法确定是否应该终止该投资
你可能会想
如果当前最大回撤超过一定范围
是否意味着该策略失效
应该中止策略？


一名叫Francesco Landolfi的交易员
对此进行了研究
他生成了4000万组收益数据
统计了这些收益数据的sharpe比率和最大回撤
然后将它们进行切片
再在每个切片里
研究该组sharpe比率与最大回撤之间的分布关系


左图是sharpe率与期望回撤、最坏回撤的函数关系图
右图则各种sharpe值下
取最坏的回撤情况下的资产走势图


最终
作者得出以下结论：

如果在实时投资中
以sharpe等于1为例
如果最大回撤达到年化波动率的2.35倍时
可以认为夏普比率的假设将失效
此时应该终止该策略
如果预期sharpe为1.5
则最大回撤达到年化波动率的2.18倍时
可以认为夏普比率的假设失效
此时也应该终止该策略


关于sharpe比率与最大回撤的关系的研究
给出了一个基于统计的策略中止条件


这里我们既介绍他的结论
也介绍他的研究方法
包括前面我们对sharpe率与年化收益的研究
掌握这些方法与思路
对我们独立展开策略研究是非常有帮助的


在他的研究中
出现了一个指标
叫做年化波动率
下面
我们就介绍这个指标


-->
