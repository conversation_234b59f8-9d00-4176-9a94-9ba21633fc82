---
layout: toc
image: https://images.jieyu.ai/images/2023/08/lesson21-outline.png?1
---
# 
<hr>

<Ellipse top="30%" left="60%"/>
<Ellipse top="55%" left="60%"/>
<Ellipse top="75%" left="60%"/>
<Ellipse top="40%" left="25%"/>
<Ellipse top="70%" left="25%"/>


<!--
今天的课程我们将介绍一些常见和重要的指标


首先是从一些普通的回报率开始
我们将讨论简单回报、对数回报、聚合回报、年化回报、累积回报等指标


在我们的课程中
有时候会使用回报这个词
有时候会使用收益这个词
它们都对应英文的return或者returns

然后我们讨论一些被称为风险调整收益的指标
这一类中有sharpe
 sortino
 calmar
 omega等等

此外
我们还将讨论信息比率、alpha/beta
它们的特征是与基准参照的


此外
还有一类风险指标
比如最大回撤、波动率等
我们也将进行介绍


这些内容构成了今天的第一部分
第二节则是关于如何进行可视化
我们将先介绍一个名为Conner's RSI的策略
生成一些数据
然后基于这些数据
看看quantstats能给我们什么样的报告


在我之前招聘量化研究员时
我会问他们这样一个问题
如果一个策略在回测时
sharpe比率很好
于是我们将它投入到实盘中
并且一直监控相关指标
如果有一天
我们发现这个策略开始回撤了
我们如何知道
当最大回撤到多少时
就可以认为该策略依赖的适用条件不存在了
必须中止策略？


今天我们也将回答这个问题


-->
