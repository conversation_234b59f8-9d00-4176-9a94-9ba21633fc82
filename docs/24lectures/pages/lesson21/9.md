---
layout: two-cols
clicks: 5
---
# So<PERSON><PERSON>
<hr>

<TopLayer at="5" fc="white" w="100%" left="0" top="11vh">

![50%](https://images.jieyu.ai/images/2023/08/lesson21-sortino-vs-sharpe.png)
</TopLayer>

::left::

```python {all|12|13|14|16-18}
import numpy as np
from empyrical import sortino_ratio, sharpe_ratio, annual_return

n = 20
rf = 0.03/252

returns = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, -0.01])

sortino = sortino_ratio(returns, rf)
sharpe = sharpe_ratio(returns, rf)
cagr = annual_return(returns)

label = f"sortino: {sortino:.1f}\nsharpe: {sharpe:.1f}\ncagr: {cagr:.2%}"
plt.plot(equity_value(returns), label=label)
plt.legend()
```
::right::

$$
Sortino\ Ratio = \frac{r_p - r_f}{\sigma_{d}}
$$

<Loc at="0" fc="white" left="30%">

* $r_p$是指组合收益
* $r_f$是指无风险收益
* $\sigma_{d}$是下行标准差
</Loc>


<!--
上一节我们讲到
如果有一种策略
平时亏损很小
但正收益都会比较高
因此
它的标准差也不会低
即使最终年化收益比较高
回撤也不大
但夏普指标可能并不高
通常情况下
夏普指标是对的：很可能这几笔较高的正收益有它的偶然性
所以给予较低的夏普值似乎并没有错


但夏普率的适用条件是
资产收益率要满足正态分布
而这一条件并不存在
所以
如果真的出现这样的收益分布
它并不一定就是偶然的、不可靠的
无论理论上最终如何决定
我们都应该先有一个恰当的指标来表述策略的这一收益特征
这个指标
就是sortino指标


这段代码以给定的日收益率
分别计算了sharpe率
sortino比率和年化收益


# CLK1
这里计算sortino指标

# CLK2
这里计算sharpe指标

# CLK3
这里计算年化收益率

# CLK4
这里绘制资产收益图

# CLK5
可以看出
它的sharpe比率只有1.3
这一指标并不高
但年化收益率达到了64.4%
相当于前一个例子中sharpe比率为2.5的策略的资产收益率


所以
如果我们仅以sharpe指标来区分的话
我们将失去这一从结果上看比较优秀的策略
无论如何它的年化收益相当优秀


当然
仅以年化收益来进行比较
可能让我们心里有一丝不安
如果一个策略的sharpe比率不算优秀
但sortino比较好
年化也不错
是否还有更让我们心安的指标来佐证？



-->
