---
theme: ../theme
lineNumbers: true
aspectRatio: 16/9
transition: slide-left
layout: cover
title: '第18课 seaborn和pyecharts'
drawings:
    presenterOnly: false
---


<!--
绘图不仅仅是创造美丽的可视化效果；这是为了释放数据的全部潜力并揭示原本隐藏的见解。它是数字语言和故事语言之间的桥梁，使个人和组织能够做出明智的决策并创造有意义的变革。 -- 可用以 seaborn
-->
<!--
大家好！
今天我们上
大富翁量化金融实战的第18课
今天的内容是 seaborn和pyecharts
-->
---
src: lesson18/2.md
title: outline
---

---
src: lesson18/3.md
title: section(Plotly简介)
---

---
src: lesson18/4.md
title: plotly简介
---
---
src: lesson18/5.md
title: Plotly中的基本概念
---
---
src: lesson18/6.md
title: Plotly子模块
---
---
src: lesson18/7.md
title: px vs go
---

---
src: lesson18/8.md
title: 股票分析图绘制
---
---
src: lesson18/9.md
---
---
src: lesson18/10.md
---
---
src: lesson18/11.md
---
---
src: lesson18/12.md
---
---
src: lesson18/13.md
---
---
src: lesson18/14.md
---
---
src: lesson18/15.md
---
---
src: lesson18/16.md
---
---
src: lesson18/17.md
---
---
src: lesson18/18.md
---
---
src: lesson18/19.md
---
---
src: lesson18/20.md
---
---
src: lesson18/21.md
---
---
src: lesson18/22.md
---
---
src: lesson18/23.md
---
---
src: lesson18/24.md
---

---
src: lesson18/25.md
---
---
src: lesson18/26.md
---
---
src: lesson18/27.md
---
---
src: lesson18/28.md
---
---
src: lesson18/29.md
---
---
src: lesson18/30.md
---
---
src: lesson18/31.md
---
