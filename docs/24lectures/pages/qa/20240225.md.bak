---
title: 量化二十四课答疑
---

---
clicks: 
---

## 1. 因子暴露和截面是什么？
## 2. Tick级股票数据库，字段设计的最佳实践？
## 3. 对vol字段计算rolling zscore，为什么zscore会大于3？
## 4. 计算机要掌握到什么程度？量化策略是不是在量化平台上直接调用封装好的函数？



<!--
* 是否需要同时保存前复权数据和后复权数据？


-->

---
clicks: 6
---

# 因子暴露和截面是什么？


## 因子是什么

<show at="1">

## 因子收益/组合收益

$$
R_p = \sum{w_iR_i}
$$

</show>
<show at="2">

## 因子收益/组合收益

<Loc> 

$$

E[R^e_i] = \beta^{\rq}_i\lambda
$$

<p>factor expected return</p>
<p>又称因子溢价 factor risk premium</p>

</Loc>
</show>



<show at="3">

## 因子收益/组合收益
## 因子暴露

<Loc left="10%" top="45%">
<p>Barra的定义： 股票在因子特征上的取值</p>
<p>factor exposure又称: 因子载荷 factor loading</p>

</Loc>
</show>

<show at="4">

## 因子收益/组合收益
## 因子暴露
## 截面(cross-section)

![](https://images.jieyu.ai/images/2024/02/section-vs-time-series.jpg)
</show>


<!--
所谓因子
就是一个可以描述股票某方面特征的因素
比如行业因子描述了股票是否属于这个行业
P/E 因子描述股票 Price-to-Earnings ratio


因子收益就是我们有一个因子
和一个股票池
比如对市盈率因子
我们可以把市盈率为正的股票拿出来
在这个因子基础上构建一个组合
这个组合的收益率就是这个因子的收益率

那么这个组合要如何构建呢？

我们可以把这些资产的EP进行排序
做多EP高我
做空EP低的
这样我们在构建组合时
就只从股票池(univers)中
选择了其中的一部分股票
显然
我们构建组合时
不同的股票的持仓比还会不一样
持仓多的
对组合收益的影响可能就大一些
反之
可能就小一些
这个持仓比
或者说权重
就是风险暴露的来源

我们一般所说的因子暴露
可能有多种含义
需要区分提到它的场景
刚刚讲到的是一个场景
如果我们再往前推一点
用Barra的说法
因子暴露就是股票在因子所代表的特征上的取值
比如一个股票的 P/E 为 15.9
那么它对 P/E 因子的因子暴露就是 15.9
有时候也叫factor loading（因子载荷）
由bara的因子暴露
到最终的资产组合中的资产权重
中间是一个线性变换关系
因此
把股票在因子所代表的特征上的取值
称为因子暴露
也没有什么不可

## CLK4

投资有两种基本的方法
一种是基于橫截面的
一种是基于时序的
基于横截面的
就是构建资产组合
构建资产组合时
我们根据因子来选择最‘强’的股票
不能保证一直赚钱
但是
只要我们持有的是最强的股票
那么它的表现也应该是最好的
这种方法理论基础比较好
而且在股票市场上
因为股票品种太多
在构建资产组合时
就需要有方法论来指导


基于时序的方法
称为择时
通俗地说就是低买高卖


两种方法应该相互结合


-->
