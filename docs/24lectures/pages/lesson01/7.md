---
layout: two-cols
clicks: 2
---
# 课程编排与内容介绍
<hr>


::left::

!!! tip Module 1 如何获取数据

    💡 课时安排： 5 课时

    💡 课程目录:<br>

           ✅ 第1课 证券基础知识与Akshare

           ✅ 第2课 Tushare
           
           ✅ 第3课 JqDataSdk
           
           ✅ 第4课 Zillionare

           ✅ 第5课 习题讲解
    <br>

::right::

<Loc at="1" left="5%" top="2.5vh">

### 1. 如何正确复权？
</Loc>


<Loc at="2" left="5%" top="2.5vh" textAlign="left">

### 1. 如何正确复权？
### 2. Python能正确处理四舍五入吗？

   ```
         round(0.03 / 2, 1) == ?
   ```
</Loc>


<!--
我们的课程是由浅入深安排的
前面的内容相对少一些
给大家一些熟悉和适应的时间


第1课给大家补充必要的证券常识
主要是证券如何编码、复权等等


这部分我们就会提一些很基础
但是又非常重要的问题


# CLK1
首先是如何正确地处理复权？
我们在这里打下一些基础
会在讲回测时
再来讲正确复权对策略的重要影响
关于这一点
至今网上还流传着许多错误的认识
当然
情况正在好转
因为我们看到有一些量化软件
开始使用正确的复权方式了


# CLK2
另一个非常基础的问题
就是Python的四舍五入问题
四舍五入在很多时候是必要的
比如我们计算涨跌停价格
最后必须精确到分
这就需要进行四舍五入
如果0.03/2
四舍五入后
不是0.02
而是0.01
大家想
这会引入多大的误差？
这引入了100%的误差
这种误差如果发生在信号生成阶段
又该造成多大损失？


这可能是很多人注意不到的问题
我们也是因为有大量的实践
才会注意到这样的问题
这也是我们课程的特点之一

量化交易中的数据处理一环扣一环
如果我们不能处理好这些细节的话
错误就会不断累积


这本质上是一个浮点数精度问题
我们在第13课
讲数据归一化时
我们还会介绍到浮点数
可能给我们带来的其它坑


-->
