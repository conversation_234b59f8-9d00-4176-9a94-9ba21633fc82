---
layout: two-cols
---
# 课程编排与内容介绍
<hr>


::left::

!!! tip Module 4 高级数据可视化

    💡 课时安排： 3 课时

    💡 课程目录:<br><br>

           ✅ 第16课 Matplotlib与图的构成原理
           ✅ 第17课 交互式绘图Plotly及Plotly Dash
           ✅ 第18课 语义关系图Seaborn及PyEcharts
           
    <br>

::right::

<Loc at="0" left="10%" w="80%" top="1vh">

![](https://images.jieyu.ai/images/2023/07/dash.png)
</Loc>


<!--
在量化中
尽管人工参与比较少
但可视化技能仍然非常关键
特别是当策略回测完成
我们一般要通过可视化来检查策略运行是否符合预期
如何进行调优等等


这一部分我们将介绍如何绘制k线图
这是量化中可能最复杂的一种图
我们必须掌握如何绘制它
并且还要了解如何往k线图上叠加其它信息


-->
