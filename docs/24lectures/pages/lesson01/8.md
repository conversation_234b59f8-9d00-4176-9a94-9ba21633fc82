---
layout: two-cols
clicks: 3
---
# 课程编排与内容介绍
<hr>


::left::

!!! tip Module 2 初识策略

    💡 课时安排： 3 课时

    💡 课程目录:<br><br>

           ✅ 第6课 小市值策略

           ✅ 第7课 布林带策略
           
           ✅ 第8课 网格交易法
           
    <br>

::right::

<Loc at="1"  w="80%" left="5%" top="2vh">

![](https://images.jieyu.ai/images/2024/01/华泰小市值因子表现-2023-11.jpg)
</Loc>

<Loc at="2" w="35%" left="40%" top="0vh">

![](https://images.jieyu.ai/images/2023/12/galton_box.png)

Galton盒子，它演示了正态分布的形成过程
</Loc>

<Loc at="3" left="30%" top="0vh" w="50%">

![](https://images.jieyu.ai/images/2023/06/grid-atr-auto-grids.png)
</Loc>


<!--
这一个模块
我们从基本面、技术面和
交易面这样三个维度
精选了三个策略


# CLK1
首先是小市值策略
小市值策略放眼全球
多少年都是非常成功的
在A股2017年之前一直有效
17到19年
出现白马股虹吸效应
期间收益较差
此后基本上跑赢大市
更详细的数据在我们课程里有介绍


这是我们拿到的23年11月
券商的研报
小市值因子仍然是最有效的因子


这个因子如此有效
实现起来又比较简单
所以我们就让它打头阵
逐步帮大家深入到其它策略


# CLK2 高顿合子
布林带策略是技术分析的代表
上世纪80年代
它所向披靡
但现在必须进行改进
尤其在A股
因为它用到了统计分析中
最基础的概念 -- 正态分布
后面我们在很多具体的技术分析中
都要使用正态分布的一些特性
即使一些数据、因子只是近似正态
我们也可能使用它的一些特性
所以我们就介绍了这个策略


# CLK3 优化
网格交易法是信息论之父发明的
它是从交易维度入手的一种策略
在这一节课
我们把前面写的代码重新抽象
形成了一个框架
完成了这一课
我们就为后面
理解更加复杂的策略框架打下了基础


另外
这一课我们也演示了如何对策略进行一步步优化
去掉经验参数
改用算法来实现自适应的参数
也就是市场在变
我们策略的参数也在跟着变
这样也意味着
我们的策略是没有过拟合的

-->

