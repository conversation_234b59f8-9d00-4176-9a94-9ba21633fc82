---
layout: two-cols
clicks: 1
---
# 课程编排与内容介绍
<hr>


::left::

!!! tip Module 5 回测和回测框架

    💡 课时安排： 4 课时

    💡 课程目录:<br><br>

           ✅ 第19课 Bactrader(一)
           ✅ 第20课 Backtrader(二)
           ✅ 第21课 策略评估与可视化
           ✅ 第22课 回测陷阱与大富翁回测框架
           
    <br>

::right::

<Loc at="0" left="5%" top="2vh" w="80%">

![](https://images.jieyu.ai/images/2024/01/about-zillonare-backtest.png)

</Loc>

<Loc at= "1" left="5%" top="2vh" w="80%">

![](https://images.jieyu.ai/images/2023/08/lesson21-draws-as-function-of-sharpe.png)

<br>问题： 当回撤发生时，何时认为策略已不适用？
</Loc>


<!--
回测是量化的核心
我们会介绍backtrader
这是当下最流行的本地化回测框架之一
我们的学员也有部署大富翁量化框架的
主要是backtrader的本土化还没有人做
还没法投入实盘


所以这是我们这门课的优势之一
如果我们只教backtrader
那么你学完之后
会面临无法实战的问题

# CLK1
这是我在面试时会问的一个问题
如果你开发的策略在回测中
夏普等各项指标都不错
我们也决定把你的策略投入实盘
但很不幸
投入实盘后就开始亏损
现在的问题是
这个回撤是正常范围内的、合理的
还是表明策略赖以生存的环境已经不存在了
我们必须停止策略？

在这一课里
我们将介绍一种蒙特卡罗方法
来回答这个问题
这个问题应该只有概率解
没有确定解
即最终的答案是
如果回测的sharpe是2.0
那么在实盘中
如果回撤达到20%
表明策略失效的可能性达到百分之几这样


-->
