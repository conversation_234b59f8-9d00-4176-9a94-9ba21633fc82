---
layout: two-cols
clicks: 3
---
# 课程编排与内容介绍
<hr>


::left::

!!! tip Module 3 量化分析方法和技术

    💡 课时安排： 7 课时

    💡 课程目录:<br><br>

           ✅ 第9课 Numpy和Pandas
           ✅ 第10课 经典技术指标库 Ta-lib
           ✅ 第11课 数据分析与Python实现（1）
           ✅ 第12课 数据分析与Python实现（2）
           ✅ 第13课 技术分析实战
           ✅ 第14课 因子分析
           ✅ 第15课 Alphalens及其它
           
    <br>

::right::

<Loc at="0" left="0" top="2vh">

![](https://images.jieyu.ai/images/2024/01/numpy-logo.png)
</Loc>

<Loc at="1" left="10%" w="80%" top="2vh">

![](https://images.jieyu.ai/images/2023/08/lesson12-resist-line.png)
</Loc>

<Loc at="2" left="10%" w="80%" top="2vh">

![](https://images.jieyu.ai/images/2023/06/cluster_bounding_box.png)
</Loc>


<Loc at="3" left="10%" w="80%" top="2vh">

![](https://images.jieyu.ai/images/2023/06/double_top_detected.png)
</Loc>



<!--
这一部分是整个课程的核心
也是我们开始加量、提升难度的地方


numpy和pandas是量化的核心库
行情数据、因子数据
在我们的程序中都会以numpy数组、
或者pandas的dataframe来保存

我们使用的其它量化库
比如scipy
 alphalens
 empyrical等
也都要依赖这些数据结构
所以
玩转numpy和pandas
是我们能快速开发策略、
提高策略性能的关键


talib是经典的技术指标库
这里我们重点讲几个指标


在数据分析课程里
我们主要介绍一些
统计的基本概念和它们的应用
我们自己在构造因子时
必须掌握这些概念
这样构建的因子才有一定的科学性
构建因子时
我们要从现象入手
找到背后的行为学、博弈学和金融基础
也要用统计的方法来进行验证



第13课中
我们还会介绍一些机器学习的基础
这些概念和方法
即使不用机器学习
也是需要的
比如归一化方法
我们构造了一个因子
要在不同的标的、
同一标的不同时间进行对比的话
就必须先对因子进行归一化
不然是没法比较的


技术分析实战
是综合运用上述概念、理论和方法
进行实战的一课
这里我们介绍了
如何寻找k线的顶和底
这个寻找虽然是事后才能找出来
但是它仍然有意义
比如机器学习需要标注数据
比如要实现波浪理论的程序化
这些都是必不可少的
我们还介绍了一维聚类算法
如果你要寻找长期横盘整理的
就可以用这样的方法


# CLK1
这是自动检测顶和底的例子
可以看出
画线派确实在影响市场

# CLK2
这是自动聚类找到横盘整理的例子
它自动找到了两个区间
这两个区间长度多少
振幅多大
都自动识别出来

# CLK3
这是寻找M头的例子
当价格越过M头中间的低点时
M头就成立了

第14课我们还介绍了因子分析
我们只讲了单因子分析
多因子我们就不介绍
因为那个没有意义了
有了机器学习
线性规划往往是无用功
你掌握了单因子分析方法后
因子组合是应该用机器学习来做组合了
我们看有的私募路演时的策略报告
也有使用多层神经网络来做组合的了

最后是alphalens
前面讲的是因子分析的原理
明白了原理之后
实操就要尽可能借助他人的框架来做
出错可能少一些
这部分也是进机构必问的内容


这部分选题也好
示例也好
我们对财务因子不是很看重
原因比较简单
我们分析了很多私募的路演策略报告
包括也通过向券商量化部门的人咨询
在A股
有效的策略主要还是CTA
也就是趋势和反转
高频当然有效
另类也有效
但适合我们课程讲的只有趋势和反转
其它的我们没办法带大家做实验
但毕竟CTA就是主流
先把这块吃透就够了


-->
