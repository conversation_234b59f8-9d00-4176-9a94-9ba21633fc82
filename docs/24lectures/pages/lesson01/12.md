---
layout: two-cols
---
# 课程编排与内容介绍
<hr>


::left::

!!! tip Module 6 实盘接口

    💡 课时安排： 2 课时

    💡 课程目录:<br><br>

           ✅ 第23课 Easytrader、东财EMC和Trader-gm-adaptor
           ✅ 第24课 PTrade和QMT
           
    <br>

::right::


<!--
最后一站就是把策略接入实盘
如果使用大富翁框架
这会很简单
安装好券商提供的
量化交易终端和我们的适配器
把策略中的服务器地址
由回测服务器指向
这个实盘服务器就可以了
现在我们只有东财的EMC适配器
今年6月之前会出QMT的适配器


也可以不使用大富翁框架
自己进行适配
另外还可以开通PTrade
直接以券商托管的方式来运行量化
当然
这会在技术和数据上受到限制



-->


import numpy as np

class Foo(object):
    n: int = 20
    def __init__(self, name:str):
        self.name = name

    def bar(self, word: str):
        if not isinstance(word, str):
            raise TypeError("wrod should be str type")
        
        print(f"{self.n} {self.name} {self.word}")

$$
Z = \frac{X-\mu}{\sigma}
$$
