---
layout: default
clicks: 3
title: 什么是量化交易
---

# 什么是量化交易

<hr>

<show at="0">

## 定义
## 优势
<Loc w="50%" top="12vh" left="50%" z=0>

```
数学模型 + 计算机程序

分析、预测金融市场走势
并进行程序化交易的方法

量化交易就是一段代码

Talk is cheap, show me the code
```
</Loc>

</show>

<show at="1">

## 定义
## 优势
### 夫未战而庙算胜者，得算多也

<Loc w="45%" top="12vh" left="50%">

![](https://images.jieyu.ai/images/2024/01/backtest-result-hnpc.jpg)
</Loc>
</show>

<show at="2">

## 定义
## 优势
### 夫未战而庙算胜者，得算多也
### 战胜情绪：别人贪婪时我恐惧

<Loc w="45%" top="12vh" left="50%">

![](https://images.jieyu.ai/images/2024/01/Warren-<PERSON>-Buffett.jpg)
</Loc>
</show>

<show at="3">

## 定义
## 优势
### 夫未战而庙算胜者，得算多也
### 战胜情绪：别人贪婪时我恐惧
### 全面、高效地掌握市场讯息

<Loc w="45%" top="12vh" lef="50%">

![](https://images.jieyu.ai/images/2024/01/6screens-trading.jpg)
</Loc>
</show>

<!--
我们首先介绍
什么是量化交易

简单来说
量化交易就是数学模型 + 计算机程序
它是指通过建立数学模型
和利用计算机程序
来分析、预测金融市场走势
并进行程序化交易的方法

归根结底
量化交易就是一段代码

# CLK1

量化交易有哪些优势呢？
量化交易相对于主观交易
有很多优势
最主要的有以下几点：

第一、在执行交易前
对该笔交易
可能产生的风险和收益
都有一个明确的数学期望
能赚才做
右图是我们的某一个模型
基于历史数据进行回测的结果
回测结果好
我们就投入实盘
回测结果不好
我们就分析原因
进行改进
这就是兵法上的夫未战而庙算胜者
得算多也
算多胜
算少不胜
而况于无算乎？
散户基本上就处于无算的状态
同时
在运行过程中
我们还可以随时监测这些指标
及时发现市场走偏的情况


# CLK2 巴菲特
第二
避免情绪化操作
巴菲特是投资界第一人
但他的公司远离华尔街
自己的办公室也没有联网
为什么？
因为他秉持买好公司的理念
不在意股价短时间的波动
为了避免受到市场情绪的干扰
就采取了这样极端的阻隔方式
股神尚且难以抗拒情绪的影响
一般人就更不用说了
巴菲特讲过
要在别人贪婪时恐惧
在别人恐惧时贪婪
但实际上
要做到这一点非常难


比如
市场快速下跌后
股市往往出现遍地是黄金的状态
但是交易者此时心理上往往陷入恐慌状态
不敢入市
又比如
小幅亏损后
尽管明知市场已经没有机会
但容易产生一种心理
就是希望反弹一点
不亏了再走
结果反而越套越深
这种心理在行为金融学里
被称为遗憾规避心理
如果我们采取了程序化的交易
那么就不会受这些
错误认知和执行上的影响
就能够坚持按规律办事

#CLK3 计算机

第三
量化策略可以更加快速和全面地
了解市场变化、趋势和节奏
毕竟
计算机可能没有人聪明
但它的计算能力很强大
也不知疲倦
人脑的信号传递是个电化学过程
速度是100m/s左右
而电脑信号是以70%的光速传递的
比人快1000万倍
并且能记住的信息更是没有上限
只取决于你的硬件
我们的交易员一般桌上都摆6个显示器
每个显示器还好几个窗口
但计算能同时处理的信息
比这要多太多了
-->
