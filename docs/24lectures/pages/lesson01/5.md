---
layout: 
clicks: 3
---
# 为什么选择大富翁课程？
<hr>

<show at="0">

<Loc left="3%" top="20vh" w="25%">

![](https://images.jieyu.ai/images/2024/01/course-takeaway.jpg)

</Loc>

<Loc left="32%" top="12vh" w="35%">

![](https://images.jieyu.ai/images/2024/01/course-compose.jpg)
</Loc>

<Loc left="72%" top="20vh" w="25%">

![](https://images.jieyu.ai/images/2024/01/course-env.jpg)

</Loc>


</show>


<show at="1">

<Loc left="3%" top="20vh" w="25%">

![](https://images.jieyu.ai/images/2024/01/course-compose.jpg)

</Loc>

<Loc left="32%" top="12vh" w="35%">

![](https://images.jieyu.ai/images/2024/01/course-env.jpg)
</Loc>

<Loc left="72%" top="20vh" w="25%">

![](https://images.jieyu.ai/images/2024/01/course-brands.jpg)

</Loc>

</show>

<show at="2">

<Loc left="3%" top="20vh" w="25%">

![](https://images.jieyu.ai/images/2024/01/course-env.jpg)

</Loc>

<Loc left="32%" top="12vh" w="35%">

![](https://images.jieyu.ai/images/2024/01/course-brands.jpg)
</Loc>

<Loc left="72%" top="20vh" w="25%">

![](https://images.jieyu.ai/images/2024/01/course-takeaway.jpg)

</Loc>

</show>

<show at="3">

<Loc left="3%" top="20vh" w="25%">

![](https://images.jieyu.ai/images/2024/01/course-brands.jpg)

</Loc>

<Loc left="32%" top="12vh" w="35%">

![](https://images.jieyu.ai/images/2024/01/course-takeaway.jpg)
</Loc>

<Loc left="72%" top="20vh" w="25%">

![](https://images.jieyu.ai/images/2024/01/course-compose.jpg)

</Loc>

</show>


<!--
这么多量化课程
为什么要选择大富翁量化课程？


首先
大家能找到我们这个课
也是因为看了我们发表的文章
知道我们所做的研究
比较有见地
又不脱离A股实际
不是一味地追求理论上的深度和难度
不去管这些理论是否脱离了实际

另外我们研发的量化软件
大家也知道我们在技术上
在代码质量上是非常领先的


正因为我们开发了量化框架
也有自己的投资实践
所以我们能理论联系实际
有能力编写好一门课程

这门课的特点之一
是有一个非常完整的课程体系
我们是先梳理了量化的整个框架
然后选定一个场景来进行编排
编排目标就是要涵盖量化交易80%的常规工作
如果你进入到机构
那么是可以立即上手的
如果你是机构的老总
那么学完这门课
你就知道
如何引领公司转型量化交易

我们编写课程的方法是
把金融理论梳理一遍
把市面上最常见的开源量化软件梳理一遍
看看它们都在引用哪些库
这些库里用得最多的
是什么功能
是哪些函数
就像大家学英语一样
我们把最常用的
核心单词、
核心语法、
核心句法提炼出来了

除了这些之外
我们还介绍了一些比较有效的策略
并且也告诉大家
如何对这些策略进行优化
以及如何寻找它们适用的时间和标的

# CLK1

第二点就是学习环境
我们为学员构建了一个服务器集群
大家无须安装任何软件
用浏览器登录就可以开始学习
这个环境里
有超大规模真实的商用数据
而且支持回测
这一点很多课是不具备的
凡是要你自己搭环境的
要你自己拷贝数据文件的
这样的课是学不到东西的
为什么

第一他没有回测环境
我们的课会讲backtrader回测
但我们不只有backtrader
我们还有自己的框架
其他人的课只会讲backtrader回测
但问题是
backtrader都不适应国内的交易制度
它没有T+1的限制
也不限制涨跌停板上交易
这样回测结果失真非常严重
他还不能接入实盘


第二
我们的回测是基于分钟线数据的
这样可以真实地反映当天交易情况
分钟线数据非常大
是不可能拷贝下来的
策略投入实盘前
得用跨越几个牛熊周期的数据来回测
这么长周期的数据
数据量太大
是无法拷贝的

# CLK3
关于我们的品牌情况
我们是小红书量化赛道第1
跟我们跟的比较紧的
其实算不上做量化的
大家看看笔记内容就知道了
我们还是迅投官方论坛特邀的大V
大家现在接入实盘
很多人用qmt
qmt就是他们开发的

我还是Python技术作家
Python能做大项目这本书
今年将由机械工业出版社出版
这本书在我们网站上已经全文刊出
大家有时间可以看看
也间接了解下我们的技术实力与写作能力
这本书我可以跟大家推荐下
作为一本技术书籍
文字还算比较引人入胜

# CLK4
然后是我们这门课的一些福利

我们这门课有一些内容还在更新中
预计到今年6月全部更新完
所以后面价格还会上调
我们会更新哪些内容？

一是我们会增加到10个左右的策略
现在是4个
二是我们会增加机器学习的策略

第二点
我们会送大富翁2.0版
因为2.1版将会是一个企业版
我们会限量赠送大富翁2.1版
2.1版将集成QMT
数据库改用clickhouse
因此性能上还会有提升
应该能支持tick级的数据储存

这一点非常重要
学其它别的课
学完之后你可能仍然没办法开始量化交易
因为他们没有自己的系统
比如你学了backtrader
但是你数据怎么办？

实盘怎么办？

所以
学完了之后
你还得考虑怎么用
如果你考完驾照
很久没有车开
那是不是算白学了？


第三个福利
就是我们目前这个24课的学员
将来可以免费学一门我们的后续课程
我们也会分拆这门课程
把内容做得更细一点
这些拆分课程都会免费送给大家
-->
