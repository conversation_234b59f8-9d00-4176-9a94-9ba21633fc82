---
layout: default
class: fade
---
# 自定义Feed
<hr>


```python
# 示例12 从数据源获取OHLC各项数据，注意它们都是标量
def _load:
    o, h, l, c, v, oi = ... 

    # 将ohlc等分别赋值给当下的数据点
    self.lines.open[0] = o
    self.lines.high[0] = h
    ...

    # 如果一切正常且还有数据，返回True
    # 如果数据已经读完，则返回False
    return True
```

<Loc w="50%" fc="white" top="15vh">

## start
## stop
## _load
</Loc>

<!--
这一张slide我们来讲如何自定义feed

自定义feed时
我们需要从AbstractDataBase派生出自己的子类
并且实现start, stop和_load 方法

start 该方法会被cerebro调用
我们一般在这里实现打开文件
或者数据库连接

stop  当回测结束时被调用
这是我们关闭数据库连接、关闭打开的文件的地方

_load 我们需要自定义_load方法
它读取一个数据记录
并且附加到self.lines的各个时间线上
比如:


-->
