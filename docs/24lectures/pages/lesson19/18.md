---
layout: two-cols
class: fade
---
# Line Coupling (2)
<hr>

::left::

```python
# 示例17
self.sma_day = btind.SMA(self.data0, 
                         peroid = self.p.day)
self.sma_week = btind.SMA(self.data1, 
                         peroid = self.p.week)

# 在next方法中，我们增加这样一行
print(self.buysig.buflen(), 
      self.sma_week.buflen(), 
      f"{self.sma_week[0]:.1f}", 
      self.buysig[0])
```

::right::

```
25 5 3268.7 1.0
26 5 3268.7 1.0
27 5 3268.7 1.0
28 5 3268.7 1.0
29 5 3268.7 1.0
30 6 3262.1 1.0
31 6 3262.1 1.0
32 6 3262.1 1.0
33 6 3262.1 1.0
34 6 3262.1 1.0
35 7 3260.0 1.0
```


<!--
让我们将第13、14行的sma_day
sma_week的局部变量声明为成员变量
以便将它们带到next方法中

在next方法中，我们增加这样一行

然后来检查输出结果

这个结果跟之前的展开大同小异
但在这里我们通过buflen方法

显示了大小周期指标在不同的时钟周期下的取值
因为短周期是20日均线
长周期是5周线
所以冷启动期是25
我们得到buysig.buflen最开始的值就是25

这也我们也注意到
buflen的值也是一点点增加的
这是因为这次我们启动cerebro
指定了runonce = False
从而失去了加速计算的优化
因此指标的计算也是在next方法中一点点计算出来的
-->
