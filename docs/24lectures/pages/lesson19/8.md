---
layout: two-cols
class: fade
---
# 时间线 - 获取一段数据
<hr>

::left::

```python {all}{maxHeight:'400px'}
from coursea import *
await init()

class TestStrategy(bt.Strategy):
    def __init__(self):
        self.sma1 = btind.SimpleMovingAverage(period=5)
        self.sma2 = btind.SMA(self.data, period=5)
        self.sma3 = btind.SMA(self.data.close, 
                              period=5)

        self.sma4 = btind.SMA(self.datas[0].
                              lines[0], 
                              period=5)
        # self.sma4 = btind.SMA(self.datas[0].
        #                       lines.close, 
        #                       period=5)
        
        
    def next(self):
        # 提取当前时间点
        print('datetime', self.datas[0].datetime.date(0))
        # 打印当日、昨日、前日的均线
        print('sma1',self.sma1.get(ago=0, size=3))
        print('sma2',self.sma2.get(ago=0, size=3))
        print('sma3',self.sma3.get(ago=0, size=3))
        print('sma4',self.sma4.get(ago=0, size=3))

        # 通过feed名字，而不是下标来访问
        data = self.getdatabyname(code_1)
        print('close', data.close.get(ago=0, size=3))
        

cerebro = bt.Cerebro()
start = datetime.datetime(2023,1,1)
end = datetime.datetime(2023,8, 3)

code_1 = "000001.XSHG"
code_2 = "000300.XSHG"

bars_1 = await Stock.get_bars_in_range(code_1, 
                                       FrameType.DAY, 
                                       start = start, 
                                       end=end)
bars_2 = await Stock.get_bars_in_range(code_2, 
                                       FrameType.DAY, 
                                       start = start, 
                                       end=end)

data_1 = pd.DataFrame(bars_1)
data_1.set_index("frame", inplace = True)

data_2 = pd.DataFrame(bars_2)
data_2.set_index("frame", inplace = True)

datafeed1 = bt.feeds.PandasData(dataname=data_1)
cerebro.adddata(datafeed1, name=code_1)

datafeed2 = bt.feeds.PandasData(dataname=data_2)
cerebro.adddata(datafeed2, name=code_2)

cerebro.addstrategy(TestStrategy)
result = cerebro.run()
```


::right::

![](https://images.jieyu.ai/images/2023/08/lesson19-slicing.png)


<!--
我们刚刚介绍的是获取单个数据
这一部分我们来看如何获取一段数据

我们在__init__方法中，定义了4组均线

在next方法中
我们先打印了4组sma
然后打印了收盘价

我们先来看一下get这个方法的语法
它是代替数组切片的函数
在bt重写了下标语法之后

我们熟悉的切片语法就不再适用了
必须改用get方法来获取一段数据

ago是指数据的起点
它从0开始
可以向前取-1 -2 等

size是要返回的元素个数
注意这些地方如果参数不对
有可能返回Index errror错误

另外
不要使用大于0的ago
backtrader并不禁止我们这样做
但这是在偷看未来数据

每组输出
我们都通过get方法来获取三个数据
我们从对比中看到了技术指标冷启动的过程

第一次调用next时候
我们看到三个技术指标中
只有一个有效
第二次调用next时
有两个有效
直到第三个时
才全部都有了有效值

注意！这里11-13和第14-16
代码是等价的

-->
