---
layout: two-cols
---
# 使用内置指标
<hr>

::left::
<v-clicks>

## Alias
## Formula
## Lines
## Params
## PlotInfo
</v-clicks>

::right::

<show at="1">
Alias 函数别名
</show>

<show at="2">

* 函数别名，在我们前面的例子中，计算移动平均线指标，我们分别使用过MovingAverageSimple和SMA，这里的SMA就是别名。
* Formula 技术指标的算法说明
</show>

<show at="3">

* 函数别名，在我们前面的例子中，计算移动平均线指标，我们分别使用过MovingAverageSimple和SMA，这里的SMA就是别名。
* Formula 技术指标的算法说明
* Lines 说明函数返回的指标对象中包含哪些 lines
</show>

<show at="4">

* 函数别名，在我们前面的例子中，计算移动平均线指标，我们分别使用过MovingAverageSimple和SMA，这里的SMA就是别名。
* Formula 技术指标的算法说明
* Lines 说明函数返回的指标对象中包含哪些 lines
* Params 指标函数可以设置的参数
</show>

<show at="5">

* 函数别名，在我们前面的例子中，计算移动平均线指标，我们分别使用过MovingAverageSimple和SMA，这里的SMA就是别名。
* Formula 技术指标的算法说明
* Lines 说明函数返回的指标对象中包含哪些 lines
* Params 指标函数可以设置的参数
* PlotInfo 这里指定绘图的各项参数
</show>



<!--
backtrader的指标模块
提供了 140 多个技术分析指标计算函数
大部分指标与 TA-Lib 库里的指标是一致的
各函数的用途、算法、参数、返回的结果
等信息可以查阅官网
这里我们也给出一个使用说明
它的帮助文档一般由以下几部分构成

* Alias 指标别名
在我们前面的例子中
计算移动平均线指标
我们分别使用过MovingAverageSimple和SMA
这里的SMA就是别名

* Formula 技术指标的算法说明
它一般给出一个简要说明
以及一些参考网址
这对我们完全掌握这些指标帮助较大

* Lines 说明函数返回的指标对象中包含哪些 lines
如 MACD 函数返回的指标对象就包含 2 条线：macd 线和 signal 线
可通过 xxxx.lines.macd/xxxx.macd 的形式调用具体的线
请参见我们在示例10中
对`self.sma.lines.getlinealiases()`

* Params 指标函数可以设置的参数
如移动均线 MovingAverageSimple 包含一个参数：period (30)
括号里是该参数的默认值
默认情况下是计算 30 日均值

* PlotInfo backtrader指标的
* 一个重要功能就是实现了指标的自动绘图
这里指定绘图的各项参数
-->
