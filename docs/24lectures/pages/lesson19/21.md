---
layout: default
class: fade
---
# 自定义指标
<hr>

```python {27-40|28|30|31|39}{maxHeight: '400px'}
from . import Indicator, <PERSON>, <PERSON>, MovAv

class TrueHigh(Indicator):
    lines = ('truehigh',)

    def __init__(self):
        self.lines.truehigh = Max(self.data.high, self.data.close(-1))
        super(TrueHigh, self).__init__()


class TrueLow(Indicator):
    lines = ('truelow',)

    def __init__(self):
        self.lines.truelow = Min(self.data.low, self.data.close(-1))
        super(TrueLow, self).__init__()

class TrueRange(Indicator):
    alias = ('TR',)

    lines = ('tr',)

    def __init__(self):
        self.lines.tr = TrueHigh(self.data) - TrueLow(self.data)
        super(TrueRange, self).__init__()

class AverageTrueRange(Indicator):
    alias = ('ATR',)

    lines = ('atr',)
    params = (('period', 14), ('movav', MovAv.Smoothed))

    def _plotlabel(self):
        plabels = [self.p.period]
        plabels += [self.p.movav] * self.p.notdefault('movav')
        return plabels

    def __init__(self):
        self.lines.atr = self.p.movav(TR(self.data), period=self.p.period)
        super().__init__()
```


<!--
这一部分
我们介绍如何自定义指标

在自定义新的指标时
我们需要继承bt.Indicator类
并且设置以下属性

# CLK1

alias 设置策略的别名

# CLK2

lines 告诉backtrader我们要暴露什么数据出来

# CLK3

多数策略都会有自己的参数

在方法上
主要是改写 __init__和 next/once中的一个

__init__ 同策略 Strategy 里的 __init__() 类似
对整条 line 进行运算
运算结果也以整条 line 的形式返回

next 同策略 Strategy 里的 next() 类似
每个 bar 都会运行一次
在 next() 中是对数据点进行运算

once 这个方法只运行一次
但是需要从头到尾循环计算指标

数据从哪里来？
我们并没有看到输入数据的地方
这是backtrader利用了隐藏的机制
我们只要知道

# CLK4
可以象第39行这样
使用self.data就可以了



-->
