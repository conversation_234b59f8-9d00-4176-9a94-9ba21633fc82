---
layout: default
class: fade
---
# 增加新的数据列
<hr>

```python {all|2|3|4}{maxHeight: '400px'}
# 示例13
class MyPandasData(PandasData):
    lines = ('money', )
    params = (('money', 'amount'),)
    
data = MyPandasData(dataname=df, datetime = 'frame',                      
                        fromdate=datetime.date(2023, 7,28),
                        todate=datetime.date(2023, 8, 3))
    
class DummyStrategry(bt.Strategy):
    def next(self):
        rec = self.datas[0]
        dt = bt.num2date(rec.datetime[0])
        
        # 这里实际上会转换成datetime.datetime, 而不是datetime.date
        pre_dt = bt.num2date(rec.datetime[-1])
        pre_close = rec.close[-1]
        
        money = rec.money[0]
        
        print(f"前日：{pre_dt:%Y/%m/%d} {pre_close}"
              f"现在：{dt:%Y/%m/%d} {rec.close[0]}")
        print(f"money is: {money:.2f}")
        
cerebro = Cerebro()
cerebro.addstrategy(DummyStrategry)
cerebro.adddata(data)

cerebro.run()
```

<Loc w="40%" fc="white">

```
前日：2023/08/03 3280.4599609375 
现在：2023/07/28 3275.929931640625
money is: 436923617400.00

前日：2023/07/28 3275.929931640625 
现在：2023/07/31 3291.0400390625
money is: 511505348400.00

前日：2023/07/31 3291.0400390625 
现在：2023/08/01 3290.949951171875
money is: 441023318600.00

前日：2023/08/01 3290.949951171875 
现在：2023/08/02 3261.68994140625
money is: 388952704000.00

前日：2023/08/02 3261.68994140625 
现在：2023/08/03 3280.4599609375
money is: 389530672000.00
```
</Loc>

<!--
前面讲的例子
都只使用了OHLC和volume数据
如果我们的数据包含其它列
比如PE值
PB等
要增加这样的数据
怎么办？


方案之一是提供一个自定义feed

但backtrader给出了更简单的方案
下面

我们以PandasData为例
给feed增加一个money时间线

可以看出
要增加一个时间线很容易
我们只需要从PandasData（或者GenericCSVData）派生一个类

# CLK1
这里我们从PandasData派生了一个类

然后声明lines属性
增加了一个名为money的line

# CLK2

新增的money这个line
它的数据从何而来？

它是通过params参数来指定的
如果这里我们继承自GenericCSVData
那么我们将csv中属于money那一列的序号
通过元组设置给params
这里的类是继承自pandasdata
所以我们可使用列名字来进行映射

这里我们还观察到两件事
第一
在子类中我们只声明要增加的line
这并不会导致原来丢失
考虑到lines这个属性父类本来就有
所以
backtrader
在幕后使用了一些技巧
进行了父子类属性的合并
正常情况下
这里应该是覆盖了父类的lines属性的

第二
注意我们给lines和params赋值时
要遵循tuple的语法
在只有一个元素的情况下
不要忘了后面的逗号

其它部分之前已经都介绍过了
我们通过next方法来检查
新增的属性是否可以访问
它的访问方法如同第19行所示
与其它line没有任何区别

右边就是我们打印的新的money属性的结果

-->
