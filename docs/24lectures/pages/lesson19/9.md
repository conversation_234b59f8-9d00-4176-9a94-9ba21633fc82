---
layout: two-cols
class: fade
clicks: 10
---
# 时间线 - 运算符重载
<hr>

::left::

<show at="0">

```python
# 示例4
>>> import numpy as np

>>> a = [3] * 5
>>> b = np.array(a) * 5

>>> print(a, b)
[3, 3, 3, 3, 3] [15 15 15 15 15]
```
</show>

<show at="1">

```python
# 示例5
# 这行代码需要在Strategy.next方法中运行
def next(self):
    print(type(self.data0), 
          self.data0[0], 
          self.datas[0].lines.close[0])
```
</show>

<show at="2">

```python
>>> class MyClass:
>>>     pass

>>> obj = MyClass()
>>> obj[0]
TypeError: 'MyClass' object is not subscriptable
```
</show>

<show at="3">

```python
>>> class MyClass:
>>>     def __getitem__(self, i):
>>>          return i

>>> obj = MyClass()
>>> obj[10]
10
```
</show>

<show at="4">

```python
>>> class MyClass:
>>>     data = [1, 2, 3, 4]
>>>     def __getitem__(self, i):
>>>          return self.data[i]

>>> obj = MyClass()
>>> obj[3]
4
```
</show>


<show at="5">

```python
# 示例5
# 这行代码需要在Strategy.next方法中运行
def next(self):
    print(type(self.data0), 
          self.data0[0], 
          self.datas[0].lines.close[0])
```
</show>

<show at="6">

```python
# 示例6
class SmaCross(bt.Strategy):
    params = dict(
        pfast=10,  
        pslow=30  
    )

    def __init__(self):
        sma1 = bt.ind.SMA(period=self.p.pfast)
        sma2 = bt.ind.SMA(period=self.p.pslow)
        self.crossover = bt.ind.CrossOver(sma1, sma2)

    def next(self):
        if not self.position:  # not in the market
            # 为什么crossover能与整数进行比较？
            if self.crossover > 0:
                self.buy()
```
</show>

<show at="7">

```python {all}{maxHeight:'400px'}
# 示例8
class TestStrategy(bt.Strategy):
    def __init__(self):
        self.sma5 = btind.SMA(period=5) # 5日均线
        self.sma10 = btind.SMA(period=10) # 10日均线
        
        # bt.And 中所有条件都满足时返回 1；有一个条件不满足就返回 0
        self.And = bt.And(self.data > self.sma5, 
                          self.data > self.sma10, 
                          self.sma5 > self.sma10)
        
        # bt.Or 中有一个条件满足时就返回 1；所有条件都不满足时返回 0
        self.Or = bt.Or(self.data>self.sma5, 
                        self.data>self.sma10,
                        self.sma5>self.sma10)
        
        # bt.If(a, b, c) 如果满足条件 a，就返回 b，否则返回 c
        self.If = bt.If(self.data>self.sma5, -1, 1)
        
        # bt.All,同 bt.And
        self.All = bt.All(self.data>self.sma5, 
                          self.data>self.sma10, 
                          self.sma5>self.sma10)
        
        # bt.Any，同 bt.Or
        self.Any = bt.Any(self.data>self.sma5, 
                          self.data>self.sma10, 
                          self.sma5>self.sma10)
        
        # bt.Max，返回同一时刻所有指标中的最大值
        self.Max = bt.Max(self.data, self.sma10, self.sma5)
        
        # bt.Min，返回同一时刻所有指标中的最小值
        self.Min = bt.Min(self.data, self.sma10, self.sma5)
        
        # bt.Sum，对同一时刻所有指标进行求和
        self.Sum = bt.Sum(self.data, self.sma10, self.sma5)
        
        # bt.Cmp(a,b), 如果 a>b ，返回 1；否则返回 -1
        self.Cmp = bt.Cmp(self.data, self.sma5)
        
    def next(self):
        # print
        # ...
        
start = datetime.datetime(2023,1,1)
end = datetime.datetime(2023,8, 3)

code_1 = "000001.XSHG"
bars_1 = await Stock.get_bars_in_range(code_1, 
                                       FrameType.DAY, 
                                       start = start, 
                                       end=end)

data_1 = pd.DataFrame(bars_1)
data_1.set_index("frame", inplace = True)

datafeed1 = bt.feeds.PandasData(dataname=data_1)

cerebro = bt.Cerebro()
cerebro.adddata(datafeed1, name=code_1)

cerebro.addstrategy(TestStrategy)
result = cerebro.run()
```

</show>
::right::

<show at="1">

\<class 'backtrader.feeds.pandafeed.PandasData'\> 
<br>3328.39 3328.39
</show>

<show at="6">

```
print(type(self.crossover))

<class 'backtrader.indicators
.crossover.CrossOver'>
```
</show>


<!--
现在我们来介绍语法糖的另外一个内容
运算符重载

在Python的基本语法中
象"+" "-" "*" "/"
这样的运算符
是只能作用于Number及其子类型之间的
我们在前面的课程中看到
Numpy允许我们在一个数组和数字之间进行操作
结果是通过广播(broadcasting)
让数字作用于数组的每一个元素身上
这里实际上发生了运算符重载
从而使得本身不能参加运算的两个变量之间
可以进行数学运算
当然
这里的数学运算含义已经发生了变化


比如
这里的两个表过式中
发生了两种不同的重载
第3行通过对python list进行"\*"运算符的重载
实现的是repeat的语义
最终我们将数组的元素重复了5次
第4行对numpy array进行"\*"的重载
实现的是按元素分别进行乘法运算的语义


在backtrader中
也大量使用了运算符重载
这些重载
使得代码变得简单易读
更符合人类的直觉
在backtrader中
最常见的重载有下标
比较运算符（> == <)和代数运算符(+ - * /)等


运算符重载有点琐碎
为什么我们这里要特别介绍呢
一是如果看不明白某种运算符的作用
在网上寻找帮助是比较困难的
它不象函数
你按单词搜索就一定能有结果
按运算符进行搜索
搜索引擎一般是忽略的
它也不知道该如何查找结果
所以这是我们的课应该去讲的地方
我们接触一门新语言时

我觉得必须买书看的一个原因
就在这里
这些知识必须系统地学
搜索不到

第二 这是backtrader的特点
它不仅在Line对象上重写了运算符
还提供了一些关键词、内置函数的重写
形成了自己的特色
所以有必要介绍下

在前面我们接触过
我们可以这样访问当日的收盘价

# CLK1

这里self.data0是self.datas[0]的简写
它是一个PandasData对象
这行代码要求对一个PandasData对象进行下标运算


在Python中
如果要对一个对象进行下标运算
那么我们是必须对它的__getitem__进行重写
否则会报语法错误


比如

# CLK2

这段代码我们自定义了一个Python类
没有重写任何方法
然后对它进行下标运算
就会报一个异常

提示我们
不能对MyClass对象进行下标运算
要改正这个错误
我们需要重载__getitem__运算符

# CLK3
现在我们改写代码
重写了__getitem__的实现
让它直接返回下标值
现在对obj进行下标运算能得到结果了
当然
我们一般是让MyClass持有一个集合类的成员
然后让下标运算最终作用到这个集合类成员上

比如

# CLK4

这里把对对象的下标运算
转移到对象的成员data上
这也差不多是PandasData对象所做的事情
通过下标运算符重载
让它返回收盘价数据


# CLK5

我们对self.datas[0].lines.close也进行了下标运算
实际上
这里的self.datas[0].lines.close仍然不是一个python的List
或者其它任何天然支持下标运算的python对象
它是一个backtrader.linebuffer.LineBuffer
backtrader对它也进行了下标运算重载


# CLK6
我们在快速开始一节中
问了这样一个问题
在这段代码中
为什么指标Crossover可以与一个数字进行比较？
现在你应该有了答案
这是因为指标类也重载了比较运算符


所以这个类重载了">"运算符
让它实际上与Crossover中最新一期的数据进行比较
返回的结果是一个bool型的标量
但也有返回另一个对象
比如LinesOperation的时候
比如
如果我们将第10行的sma1除以10
再检查这个新变量的类型
它将是一个`<class 'backtrader.linebuffer.LinesOperation'>`对象

# CLK8
但有些操作符、关键词和函数
是Python不允许重载的
为了增强条件判断的表达能力
在backtrader中
对这些符号/关键字
使用大写开头的符号来进行改写
比如将if改写为If
and改写为And

看上去所有的这些操作
都存在python
或者numpy的对应物
那么backtrader为什么要发明这样的语法
主要原因是为了进行数据对齐
方便在next当中进行引用
如果不进行这些重载
而直接使用python或者numpy的原生对象
我们必须自己维护当前访问数据点的指针
并且自己来处理不同的数据、指标之间size不一致
并且周期不同的问题

-->
