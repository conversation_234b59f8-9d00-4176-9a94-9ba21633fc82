---
layout: default
class: fade
title: 快速开始
---

# 快速开始
<hr>

```python {all|1|7|8|10|12-15|17-20|22-29|32|34-39|41|43|44|46}{maxHeight: '400px'}
%matplotlib inline
# without this line, we'l get Javascript Error: IPython is not defined

from coursea import *
await init()

import backtrader as bt
from backtrader import feeds

class SmaCross(bt.Strategy):
    # list of parameters which are configurable for the strategy
    params = dict(
        pfast=10,  # period for the fast moving average
        pslow=30   # period for the slow moving average
    )

    def __init__(self):
        sma1 = bt.ind.SMA(period=self.p.pfast)  # fast moving average
        sma2 = bt.ind.SMA(period=self.p.pslow)  # slow moving average
        self.crossover = bt.ind.CrossOver(sma1, sma2)  # crossover signal

    def next(self):
        if not self.position:  # not in the market
            # if fast crosses slow to the upside
            if self.crossover > 0:
                self.buy()  # enter long

        elif self.crossover < 0:  # in the market & cross to the downside
            self.close()  # close long position


cerebro = bt.Cerebro()  # create a "Cerebro" engine instance

start = datetime.date(2023, 1, 4)
end = datetime.date(2023, 8, 3)
bars = await Stock.get_bars_in_range("000001.XSHG", FrameType.DAY, start, end)

df = pd.DataFrame(bars)
data = feeds.PandasData(dataname=df, datetime = 'frame')

cerebro.adddata(data)  # Add the data feed

cerebro.addstrategy(SmaCross)  # Add the trading strategy
cerebro.run()  # run it all

cerebro.plot(iplot=False)  # and plot it with a single command
```

<show at="2">
<Loc w="50%" h="80%">

```python
    # 示例2
    import backtrader as bt
    import backtrader.indicators as btind
    import backtrader.feeds as btfeeds
```
</Loc>
</show>

<!--
在这个例子当中
我们将演示如何导入backtrader
如何定义策略
实例化回测引擎
加载数据和策略
运行回测
以及最后绘制回测结果
第1行 我们声明启用matplotlib绘图
如果没有此声明
backtrader会提示Javascript Error: IPython is not defined
这个错误涉及到我们在之前讲解绘图的时候
接触过的一个概念
每种绘图库
它的图形定义和真正渲染到物理表面都是分开的
在真正需要渲染成图时
都是利用一个所谓的backend来进行渲染
有的库在notebook环境下能自动识别出正确的backend
比如plotly
有的则不行
比如PyEcharts
较新版的matplotlib多数情况下是能正常识别的
但在这里当我们通过backtrader来调用绘图时
它使用的matplotlib会将backend确定为tkinter
这是一种图形窗口界面
不适用于notebook/jupyterlab
所以
会产生错误


第7行 我们一般将backtrader导入为bt
这是一种惯例
除此之外
关于backtrader
我们还有右边的这些导入惯例


第8行
我们导入feeds模块
这个模块是用来提供数据的
接下来我们就会使用这个模块中的PandasData类


第10行
这里我们定义了一个策略类
它是我们定义自己的交易逻辑的地方
它需要继承自bt.Strategy类


几乎所有的策略都有调整参数的需要
在第12~15行
我们定义了这个策略需要的参数
双均线策略需要定义两条均线
这里的参数就是两条均线的窗口大小
快线是10天
慢线是30天


第17行到第20行
这里有一个初始化函数
在这里
我们生成了两个技术指标
即慢均线和快均线
另外还计算出了均线相互穿越的信号
这里我们没有使用talib或者其它计算均线的方法
而是使用了backtrader自己指标库里的方法

从这里的语法来看
sma1, sma2和crossover都是对象
所以我们还不清楚指标运算是否真的已经执行了


从这里也可以看出
backtrader的功能之一
就是提供了常用的指标计算
它们归类在backtrader.indicators包下


# CLK7
next函数是我们真正实现策略的地方
它会被Cerebro引擎调用
当每次被调用时
Cerebro会将数据准备好供我们使用
这里的数据就是我们在__init__方法中
计算出来的self.crossover
从代码上看
这里的逻辑似乎是
当我们有持仓、且当前发生快线上穿慢线时买入
否则
就卖出


不过
next方法给我们留下一些疑问：

1. 我们并没有定义self.position
从名字上看，它似乎是当前持仓数据
应该是基类定义的
它为什么可以与not进行运算
而不是通过self.position > 0之类的方法
来判断当前是否有持仓？
   
2. self.crossover是CrossOver对象的实例
它是在第20行代码
通过构造函数实例化的
它为什么能与一个整数进行比较？
这里补充一下
如果我们定义一个类
一般情况下
它的实例是不能与数字进行比较的
因为python不知道应该如何进行比较
比如我定义一个Foo类
生成实例
然后比较foo > 0
我们会得到一个类型错误
提示不能在Foo与int之间进行比较


1. 买入和卖出为什么没有参数？

2. 手续费是如何处理的？

3. 整个next方法中
并没有出现跟时间相关联的任何迹象
尽管我们猜测框架会一个bar一个bar地往前推进
但这一切究竟是如何发生的
特别是
如果不与时间关联
我们是如何知道当前处理的数据也在同步更新的？


我们先跳过这些问题
等把这个例子看完之后
再回过头来深入这些细节


第32行
我们生成了回测引擎
它将替我们保管数据、
处理时间前进并驱动我们的策略


第34到39行
我们通过第三方数据源获取数据
把它喂给feeds.PandasData


# CLK10
然后将PandasData对象
通过adddata方法
加入到cerebo引擎的管理之下


# CLK11
除了要给cerebo增加数据之外
我们还要给它增加策略
（注意是类而不是实例）
这是第43行所做的事


第44行
我们调用crebor.run
让齿轮开始旋转


最后
当一切结束
我们调用 cerebro.plot来绘图
这样我们就完成了一轮回测
接下来要做的就是
对着图和回测结果
进行策略的查错和优化

关于第46行绘图的结果
我们也介绍一下

# CLK
-->
