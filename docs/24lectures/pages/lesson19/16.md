---
layout: default
class: fade
clicks: 6
---
# 多周期数据
<hr>

```python {all|5-12|19-20|15-17|24|33-49} {maxHeight: '400px'}
# 示例15
import backtrader.indicators as btind

class MultiFrameDataStrategy(bt.Strategy):
    params = (
        # period for the fast Moving Average
        ('fast', 10),
        # period for the slow moving average
        ('slow', 30),
        # moving average to use
        ('_movav', btind.MovAv.SMA)
    )

    def __init__(self, params=None):
        if params != None:
            for name, val in params.items():
                setattr(self.params, name, val)

        sma_fast = self.p._movav(period=self.p.fast)
        sma_slow = self.p._movav(period=self.p.slow)

        self.inds = dict()
        self.inds['crossup'] = indicators.CrossUp(sma_slow, sma_fast)
        self.inds['week_rsi'] = indicators.RSI_Safe(self.data1)    


    def next(self):
        print(f"Week: {bt.num2date(self.data1.datetime[0]).date()}"
              f"\tDay: {bt.num2date(self.data0.datetime[0]).date()}"
              f"\tWeek-Rsi: {self.inds['week_rsi'][0]:.1f}"
              f"\tDay-Sma-cross: {self.inds['crossup'][0]:.1f}")

code = "000001.XSHG"
daily =  await Stock.get_bars_in_range(code, FrameType.DAY, start, end)
daily = pd.DataFrame(daily)
daily = PandasData(dataname = daily, datetime = 'frame',                      
                   fromdate=datetime.date(2023, 4, 22),
                   todate=datetime.date(2023, 8, 3))

weekly = await Stock.get_bars_in_range(code, FrameType.WEEK, start, end)
weekly = pd.DataFrame(weekly)
weekly = PandasData(dataname = weekly, datetime = 'frame',                      
                   fromdate=datetime.date(2023, 1, 22),
                   todate=datetime.date(2023, 8,3))

cerebro = Cerebro()

cerebro.adddata(daily)
cerebro.adddata(weekly)

params = {'fast': 9, 'slow': 20}
cerebro.addstrategy(MultiFrameDataStrategy, params)
cerebro.run()
```

<show at="6">

<Loc w="100%" left="0" fc="white" padding="50px">

```
Week: 2023-07-14	Day: 2023-07-18	Week-Rsi: 49.0	Day-Sma-cross: 0.0
Week: 2023-07-14	Day: 2023-07-19	Week-Rsi: 49.0	Day-Sma-cross: 0.0
Week: 2023-07-14	Day: 2023-07-20	Week-Rsi: 49.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-21	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-24	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-25	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-26	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-27	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-07-28	Week-Rsi: 53.2	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-07-31	Week-Rsi: 53.2	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-08-01	Week-Rsi: 53.2	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-08-02	Week-Rsi: 53.2	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-08-03	Week-Rsi: 53.2	Day-Sma-cross: 0.0
```
</Loc>
</show>


<!--
有些策略会使用到多周期的数据
典型的应用为：

使用周线（大周期）数据判断趋势
使用日线（小周期）数据判断买卖点

这就需要同时读入多周期数据进行回测
backtrader内置了对多周期策略回测的支持
它以两种方式支持多周期
其一是读入多周期的数据
其二是对数据进行重采样(resample)


在backtrader中
第一个被添加的数据将被作为时钟数据
因此需要将小周期数据首先添加到系统中
以使得小粒度的时间都能被遍历到
backtrader在回测过程中
不会对时间进行重新排序
只能按照整理好的数据顺序依次处理
因此需要将不同周期的数据都做好对齐

这是一个使用多周期的例子
它利用大周期上的RSI指标和
小周期上的均线上穿来发出信号
当然这里并没有真正实现上述逻辑
我们主要用它来演示多周期数据
如何添加和使用

这段代码演示了以下几个方面的技巧

# CLK1

第一、如何向strategy中添加参数?

在第5-12行中
我们添加了三个参数

# CLK2
在后面我们就可以通过self.p
注意这是self.params的简写
来使用这个参数
第19~20行给出了示例

# CLK3
第15~第17行
注意这里与官方文档似乎不一致
根据官方文档
strategy类的参数不需要在__init__中进行声明
就能自动合并到self.params中
但可能是因为版本原因
根据我们的测试
还必须
1. 通过__init__声明params参数
2. 通过16-17的语句
将参数合并进self.params
   

# CLK4
第二
如何在构造函数和next方法中引用不同周期的数据和指标?

我们还是通过数据流的索引
或者它的名字来引用
在第24行
这里是通过索引

# CLK5
第三
如何添加多周期数据
第33到49演示了
这里实际上跟添加其它数据是没有任何区别的

那么大小周期之间是如何对齐的？

我们先看next函数的输出
我们知道next函数是按最小的时钟周期步进的
我们在每一个时钟周期上
同时输出两个数据流的时间和各自的指标

# CLK6

我们可以看到
在每个步进的周期内
backtrader向我们正确地传递了当前周期的行情数据
比如
在2023-08-03这一天
它传给我们的周线数据是2023-7-28这一天的
这是最近的一个已经结束的周期
2023年7月28日这一天是周五
当天正好是周线结束日
所以这一天的周线也是7月28日
这些地方都是正确的


比较有意思的是6月22那一周
这里因为空间原因
没有展示
大家可以自己运行下

6月22日是端午
这周的周五是6月23日
但A股当周收盘于6月21日
我们看到backtrader也正确处理了


第33行到第45行
这是通过omicron获取数据
生成PandasData对象

第48到第49行
我们先添加小周期的数据
这样backtrader才能得到正确的时钟概念
然后添加大周期的数据
这里再强调一次
-->
