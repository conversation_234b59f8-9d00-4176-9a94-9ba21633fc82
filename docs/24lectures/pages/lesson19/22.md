---
layout: default
class: fade
---
# 最小周期
<hr>

```python
class SimpleMovingAverage1(Indicator):   
    lines = ('sma',)   
    params = (('period', 20),)   

    def __init__(self):   
        self.addminperiod(self.params.period)   

    def next(self):   
        datasum = math.fsum(self.data.get(size=self.p.period))   
        self.lines.sma[0] = datasum / self.p.period
```


<!--
backtrader的各个line本身并没有日期概念
datetime都是专门的line

因此
不同的line如何对齐就很费心思
这里有不同周期指标带来的问题
也有同一周期
不同指标的冷启动期不同带来的对齐问题

在第16张及后面slide中
我们已经看到
不同周期的指标
是如何自动对齐和展开的
但有时候
backtrader可能无法检测出来最小周期
这种情况下
就需要我们手动指定我们可以在__init__方法中
调用addminperoid来进行声明

-->
