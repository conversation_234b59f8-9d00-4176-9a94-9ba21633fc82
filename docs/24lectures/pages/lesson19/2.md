---
layout: toc
image: https://images.jieyu.ai/images/2023/08/lesson19-outline-1.png?2
---



<!--
一次完整的回测一般包括以下几个步骤：

1. 准备回测数据
2. 编写策略
3. 运行回测
4. 获得回测结果
5. 分析回测结果

这个流程我们在第6到第8课已经接触过了
在我们学习backtrader的过程中，
需要与之一一对应
看看在backtrader中是如何完成这些功能的
掌握了原理
未来遇到其它框架
也会用得上

这个脑图是我们今天课程的大纲
我们会先介绍backtrader中的一个基础概念
lines 时间线
然后介绍 datafeeds - 这是在backtrader中提供数据的模块
strategy - 这是策略模块
indicator - 这是指标模块
我们会简单介绍它的内置指标库
同时介绍如何自定义指标
这会大致对应到上述步骤中的
数据获取和编写策略两部分

-->
