---
layout: default
class: fade
---
# 快速开始
<hr>


<Loc left="15%" top="10vh" w="600px">

![](https://images.jieyu.ai/images/2023/08/lesson19-bt-plot.png)
</Loc>


<!--
这是刚刚最后的一行代码生成的图
这个图虽然不够美观
但却揭示了一些非常有用的信息
这张图共由4行组成
第一行显示了每个时间点上的现金数和总资产
红色的线是现金变化
蓝色的线是资产变化
当发生买入时
现金下降较多
同时资产也开始波动

第二行显示了每笔交易的收益
它绘制在一笔交易完成之后


第三行显示了每日收盘曲线、成交量、两条移动平均线
更重要的是标识出了买卖点

第四行是信号线的一个绘图
把它与买卖标识和现金流对照起来看
就可以还原信号发出、执行交易的逻辑
比如这里发出了一个卖出信号
但从现金流线可以看出当时没有持仓
所以最终没有对应的卖出操作


仅仅只用了一行代码就显示出这么多有用的信息
backtrader干得不错
特别是
我们可能关注的三个指标
两条均线、信号线都自动绘制出来了
还能还原交易逻辑
这对初学者确实比较友好


这张图还能读出更多的信息
比如
当Cerebo创建时
它自动分配了现金给我们
每次交易
似乎是以全仓的式


现在
我们再回到前一段代码中的next方法

【goback】

来回答刚刚的几个问题
比如
为什么crossover能够与一个整数进行比较

要回答这个问题
就要先介绍backtrader的一些封装
我们称之为语法糖
-->
