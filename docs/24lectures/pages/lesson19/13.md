---
layout: two-cols
class: fade
---
# Pandas Feed
<hr>

::left::

```python
# 示例11
start = datetime.date(2023, 1, 4)
end = datetime.date(2023, 8, 3)
bars = await Stock.get_bars_in_range("000001.XSHG", FrameType.DAY, start, end)

df = pd.DataFrame(bars)
data = feeds.PandasData(dataname=df, 
                        datetime = 'frame',
                        fromdate=datetime.date(2023, 7,28),
                        todate=datetime.date(2023, 8, 3))
cerebro = Cerebro()
cerebro.addstrategy(bt.Strategy)
cerebro.adddata(data)

cerebro.run()
cerebro.plot(style='bar')
```

::right::


<!--
pandas feed给出了
通过DataFrame来生成feed的方案

这个方案比较重要
因为如果我们通过akshare
 tushare等数据源
来获取数据的话
它们返回的结果就是Dataframe的格式
如果我们使用的数据源
backtrader feed没有提供相应的封装
我们也可以先将它转换为dataframe
再通过pandas feed来为回测提供数据
也就是通过dataframe来提供一个桥梁

我甚至建议
不用csvfeed
而是直接使用pandas feed
我们先把csv通过pandas读取为dataframe
再用这里的方法生成pandas feed
为什么这么建议呢
因为csv的解析实际上比较复杂
使用pandas来解析
正确性和性能都有更好的保障
我没有做过测试
来比较两者读取csv的性能
但从阅读代码的角度
我的感觉backtrader花的功夫
还是比不上pandas的。

这里的例子给出了在大富翁量化环境下
如何将omicron获取的数据
转化成为Pandas feed

omicron返回的bars是numpy structured array
每一列都有自己的名字
可以直接转换成为DataFrame
我们通过dataname把新生成的DataFrame
传递给PandasData
这里要注意PandasData
是如何完成列名字之间的映射的
通过猜测
刚好omicron返回的数据
各列名字就是open
 high
 low等
所以就直接对应上了
但是
omicron返回的数据中
时间列的名字是frame
而PandasData期望的名字是datetime
所以
我们在第8行
指定了datetime列的名字是frame



-->
