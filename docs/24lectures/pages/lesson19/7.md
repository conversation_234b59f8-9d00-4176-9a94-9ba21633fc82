---
layout: two-cols
clicks: 4
class: fade
---
# Line (时间线)
<hr>

::left::

<show at="0">

```python
class TestStrategy(bt.Strategy):
    def __init__(self):
        self.highhigh = bt.MAX(self.data.high, peroid=5)
        self.sma2 = btind.SMA(self.data.close, period=5)

datafeed2 = bt.feeds.PandasData(dataname=data_2)
cerebro.adddata(datafeed2, name=code_2)

cerebro.addstrategy(TestStrategy)
result = cerebro.run()
```
</show>

<show at="1">

```python
def next(self):
    print("rec.datetime[0] > rec.datetime[-1]: ", 
            rec.datetime[0] > rec.datetime[-1])

    print("rec.datetime[-1] > rec.datetime[-2]: ", 
            rec.datetime[-1] > rec.datetime[-2])
```
</show>

<show at="2">

```python {all}{maxHeight: '400px'}
from coursea import *
await init()

class TestStrategy(bt.Strategy):
    def __init__(self):
        self.sma1 = btind.SMA(self.datas[0].close, period=5)
        self.sma2 = btind.SMA(self.data[1].close, period=5)
        
cerebro = bt.Cerebro()
start = datetime.datetime(2023,1,1)
end = datetime.datetime(2023,8, 3)

code_1 = "000001.XSHG"
code_2 = "000300.XSHG"

bars_1 = await Stock.get_bars_in_range(code_1, FrameType.DAY, start = start, end=end)
bars_2 = await Stock.get_bars_in_range(code_2, FrameType.DAY, start = start, end=end)

data_1 = pd.DataFrame(bars_1)
data_1.set_index("frame", inplace = True)

data_2 = pd.DataFrame(bars_2)
data_2.set_index("frame", inplace = True)

datafeed1 = bt.feeds.PandasData(dataname=data_1)
cerebro.adddata(datafeed1, name=code_1)

datafeed2 = bt.feeds.PandasData(dataname=data_2)
cerebro.adddata(datafeed2, name=code_2)

cerebro.addstrategy(TestStrategy)
result = cerebro.run()
```
</show>

<show at="3|4">

![](https://images.jieyu.ai/images/2023/08/lesson19-lines-shortcut.png)
</show>

::right::

<show at="0|1">

![75%](https://images.jieyu.ai/images/2023/08/lesson19.lines.png)
</show>

<show at="4">

<Loc w="50%" fc="white" left="25%">

## len
## buflen
</Loc>
</show>


<!--
量化交易中
我们打交道的数据
很多是时间序列数据

在回测中访问时间序列有它的特点
比如
不允许偷看未来数据
时间戳一直向前
等等
这些约束虽然可以要求策略研发人员自己注意
但是
如果框架能够提供
则会大大降低策略开发门槛
提高策略的可靠性


时间线就是backtrader
对时间序列进行的一种封装
注意
考虑到我们这门课程不是纯粹的编程课
接下来的介绍中
我们不会严格区分作为对象的Line
和作为数据组的line
除非在可能产生混淆的情况下


在backtrader中
Strategy、Indicator和
DataFeed是最主要的类
它们的基类都指向DataAccessor
而DataAccessor的基类又最终指向LineRoot
LineRoot类是一个接口
定义了运算符重载、迭代等等方法
所以
你会在后面看到
这些对象都有相似的接口和用法

在本课程中
不需要深入到这些细节
但是
了解这些关系
对于我们理解backtrader中的
一些特殊语法很有帮助


数据流(data feeds)、指标和策略
都有所谓的“时间线”（line）属性
时间线是由一系列的点组成
比如
数据流可能有open
 high
 low
 close
 volume等时间线
由open
 high
 ...等时间线构成一个时间线群--lines


# CLK1

对时间线的索引
backtrader有自己独特的方式
一般来说
对于类似于list这样的对象
我们一般以0为起始点的坐标
用-1来指代最后一个数据
在backtrader的时间线语法中
0恰好代表当前可见的最新的数据
（或者说最后一个数据点）
-1 -2 ...-n
则依次代表前一个前两个...前n个数据

主要的不同之处在于
它使用0来指示最后一个数据
而在python list中
这原本是用-1来指示的

这段代码中
两次输出都为True
验证了0指向最后的位置
而不是-1

# CLK2
这段代码中

我们使用cerebro.adddata
把一个个data feed加入进来
然后就可以在Strategy类中
通过self.datas来访问该数据流
注意
尽管data本身就是一个复数
在backtrader中
它仍然使用了datas来强调
self.datas是关于数据流的一个list
因此
我们可以使用list的下标语法来访问每一个数据流
其顺序是由加入的顺序来确定的

关于时间线
backtrader提供了一些快捷方法
了解这些行为有助于我们读懂他人的代码
现在我们就来介绍一下

# CLK3

很多对象都有lines集合
比如在一个strategy对象里
我们通过self.sma来引用简单移动平均指标
通过self.sma.l来引用sma的所有时间线集合
用self.sma.lines_name
来引用它的时间线集合下面的某个时间线
在strategy中
我们可以self.data_name
来引用self.data.lines.name
等等
其它的请大家自己对照来理解
这里注意下划线和大写的用法
bt通过它们来表示引用进入了更深一个的层次
但省掉了点符号或者下标运算符

# CLK4

时间线(line)具有len和buflen两个相似但有区别的函数
buflen给出的是Data Feed已经预加载的bar的总数
而len给出的是当前已经处理的bar的个数
当策略运行完毕后
两者应该具有相同的取值


-->
