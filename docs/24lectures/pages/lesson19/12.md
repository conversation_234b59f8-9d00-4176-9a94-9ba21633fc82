---
layout: default
class: fade
---
# csv data feed
<hr>


```python {all}{maxHeight:'400px'}

data = GenericCSVData(dataname='/tmp/test_generic_data_feeds.csv', 
                      datetime=0, 
                      openinterest = -1,
                      fromdate=datetime.date(2023, 7, 25),
                      todate=datetime.date(2023, 8, 3),
                      dtformat = "%Y-%m-%d"
                     )

class DummyStrategy(bt.Strategy):
    def __init__(self):
        print("策略的 lines: ", self.lines.getlinealiases())
        
        print("self.datas[0] lines: ", self.datas[0].lines.getlinealiases())
        
        self.sma = bt.indicators.SMA(self.data.close, period=5)
        print("indicators 对象的 lines: ", self.sma.lines.getlinealiases())
        print("\n")

        
    def next(self):
        rec = self.datas[0]
        dt = bt.num2date(rec.datetime[0])
        
        # 这里实际上会转换成datetime.datetime, 而不是datetime.date
        pre_dt = bt.num2date(rec.datetime[-1])
        pre_close = rec.close[-1]
        
        print(f"前日：{pre_dt:%Y/%m/%d} {pre_close}"
              f" 现在：{dt:%Y/%m/%d} {rec.close[0]}")
    
cerebro = Cerebro()
cerebro.adddata(data, name="000001")
```

<show at="1">


```
策略的 lines:  ('datetime',)
self.datas[0] lines:  ('close', 'low', 'high', 'open', 'volume', 'openinterest', 'datetime')
indicators 对象的 lines:  ('sma',)


前日：2023/07/28 3275.93 现在：2023/07/31 3291.04
前日：2023/07/31 3291.04 现在：2023/08/01 3290.95
前日：2023/08/01 3290.95 现在：2023/08/02 3261.69
前日：2023/08/02 3261.69 现在：2023/08/03 3280.46
```
</show>

<!--
这段代码演示了我们刚刚介绍的data feed的一些属性
也演示了 csv data feed的用法 

在backtrader中
如果要检查feed的数据加载过程是否正确
一般是定义一个很简单的策略
在该策略的next方法中查看

比如dataname
在csv feed中
它是文件名
name属性我们是通过第33行
adddata传入的
这是我们在本课中第一次这样使用
这里的fromdate和todate限制了csv feed的数据范围
其它几个属性
则是csv feed独有
比如openinterest = -1
datetime = 0
这是在告诉csv feed如何读取datetime
 OHLC等各列的值
对一个csv文件来说
它可能有表头
也可能没有表头
这都是允许的情况
所以
对csv列的引用

使用索引是两种情况下都能得到兼容的一种方式

这里datetime=0
告诉feed第0列是日期时间
openinterest = -1
告诉feed我们的数据中不包含这一项
csv feed对数据列有一个默认的顺序
即datetime
 ohlc
 volume
 openinterest
如果我们的数据不是按这样的顺序排列
就一定要通过这里给的方法重新进行映射


我们在__init__函数中
演示了一些查看lines属性的方法
比如 getlinealiases
我们演示了Strategy
 feed和指标都有lines属性

从输出中我们可以看出
策略本身只有一个datetime的line
feed则有OHLC等多个line
sma指标有一个line
名为sma

注意！第7行

我们提供了一个dtformat给feed构造函数
这是因为backtrader的csv feed在解析时间时

默认的格式既包括了日期
也包括了时间

第23行中
我们执行了一个由数字到日期的转换
在内部
GenericCSVData将日期转换成为epoch时间来保存了
因此
为了显示正确的时间
我们又把它转换回来
这里使用了num2date这个方法


第22行
这里是读取第一个数据流
在我们的例子中
也只存在这样一个数据流
rec.close[0]是取当前的收盘价
这里我们也还使用了rec.close[-1]来获取前一日收盘价

-->
