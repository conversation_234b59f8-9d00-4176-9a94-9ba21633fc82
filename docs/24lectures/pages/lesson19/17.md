---
layout: default
class: fade
---
# Line coupling - 空括号调用
<hr>

```python {all|16-17|47}{maxHeight:'400px'}
# 示例16
class MyStrategy(bt.Strategy):
    params = (
        ('day', 10),
        ('week', 5)
    )

    def __init__(self, params=None):
        if params != None:
            for name, val in params.items():
                setattr(self.params, name, val)

        sma_day = btind.SMA(self.data0, period=self.p.day)
        sma_week = btind.SMA(self.data1, period=self.p.week)

        # 这里是施展魔法的地方
        self.buysig = sma_day > sma_week()

    def next(self):
        print(f"Week: {bt.num2date(self.data1.datetime[0]).date()}"
              f"\tDay: {bt.num2date(self.data0.datetime[0]).date()}"
              f"\tDay-cross-week: {self.buysig[0]:.1f}")
        
            
code = "000001.XSHG"
end = tf.day_shift(datetime.date(2023, 8, 3), 0)
start = tf.day_shift(end, -250)

daily =  await Stock.get_bars_in_range(code, FrameType.DAY, start, end)
daily = pd.DataFrame(daily)
daily = PandasData(dataname = daily, datetime = 'frame',                      
                   fromdate=datetime.date(2023, 1, 22),
                   todate=datetime.date(2023, 8, 3))

weekly = await Stock.get_bars_in_range(code, FrameType.WEEK, start, end)
weekly = pd.DataFrame(weekly)
weekly = PandasData(dataname = weekly, datetime = 'frame',                      
                   fromdate=datetime.date(2023, 1, 22),
                   todate=datetime.date(2023, 8, 3))

cerebro = Cerebro()
cerebro.addstrategy(MyStrategy)

cerebro.adddata(daily)
cerebro.adddata(weekly)

cerebro.run(runonce=False)
```


<!--
上一张slide
我们看到了如何使用多周期的数据
以及大周期的数据
是如何在小周期的时钟步进时进行展开的

关于这一点
我们还需要进一步讨论
这次我们来看另外一种情况
如果我们将大小两个周期的技术指标相互比较
这会发生什么情况？


显然
这种比较无法直接进行
因为两个序列的长度不一致
实际上每个元素对应的时间点也不一致


backtrader 实现了一种称作line coupling的机制
使得大周期上的数据
可以扩展(spread)并对齐到小周期


# CLK1

对大周期进行拓展
是通过()（空括号调用）来实现的
这里的sma_week是大周期的指标
它是一个比sma_day短的指标序列
所以我们对它进行扩展

其它的代码跟之前没有差别
我们就不解释了

# CLK2

这里要提示下
这次我们调用cerebro.run的方法不一样了
我们额外传入了一个runonce = False的方法
这会牺牲一点性能
但在这种情况下
如果我们不这样做
会得到IndexError错误

line coupling的使用很容易
我们也无须关心它是如何实现的
但我们需要知道它具体是如何工作的

-->
