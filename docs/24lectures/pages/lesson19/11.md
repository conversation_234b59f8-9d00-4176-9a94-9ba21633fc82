---
layout: two-cols
---
# DataFeeds
<hr>

::left::

<v-clicks>

## dataname
## name
## fromdate
## todate
## timeframe
## compression
## session strat
## session end
</v-clicks>

::right::

![](https://images.jieyu.ai/images/2023/08/lesson19-xmind-feeds.png)

<!--
datafeeds这个包
提供了许多内置的feed
比如加载csv数据的csv data feed
加载dataframe的PandasData
PandasData我们已经在前面接触过了
还有加载其它数据源的
比如从Influxdb中加载数据的feed
等等

这里也插句题外话
我们看到这里有InfluxDB feed
大家也知道大富翁框架从一开始起
就是使用的influxdb作为行情数据存储
那么在本课程中
能否使用backtrader Influxdb feed
来为回测提供数据呢

答案是否定的
原因有二
一是backtrader在这个feed上做得比较简单
不支持使用dsn作为连接串
也不支持组织架构
这样无法连接到课程环境中的influxdb数据库
二是它在数据处理上，
与我们构建行情数据库时不一样
我们构建行情数据库时
进行了数据点的优化
每个数据点有惟一取值
直接存取就行了
不需要重采样
而influxdb feed进行了重采样
它会取数据点的均值
这可能会引起一些误差

所以，如果你们在其它地方
要使用backtrader的这些feed
可能需要读一下源码
思考backtrader对数据的处理方式
是否与你们的数据源构建方式是一致的
否则可能引入难以排查的误差

下面，我们先来介绍data feed中的
一些基本属性

* dataname 这个属性的具体含义
* 由各个类自行定义
比如
如果我们使用PandasData
那么我们应该通过dataname
传入一个包含行情和指标数据的DataFrame
如果是csv类的data feed
那么很可能应该传入文件位置
等等
这是一个必须传入的参数

* name 这个属性将在绘图时使用

* fromdate 用来指定数据流的起始日期
在该日期（时间）之前
即使数据流能够提供数据
也被忽略

* todate 用来指定数据流的结束日期
在该日期（时间）之后
即使数据流还能提供数据
也被忽略

* timeframe 数据流的周期类别
可能是Ticks Seconds ... Years对象中的一个
这些对象由TimeFrame来定义
这个地方大家可以把它对应到
大富翁的 FrameType 类型来理解
即FrameType.MIN1， FrameType.MIN5, ...
FrameType.DAY等等

* compression
 session start
 session end 这几个属性只用于重采样

对于派生类
则可能还会定义一些其它别的属性
比如
如果数据源来自于csv文件
我们还需要指定csv文件是否包含表头（header）
分隔符是什么等等
这分别用headers和separator参数来指定

-->
