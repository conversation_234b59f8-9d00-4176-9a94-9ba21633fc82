---
layout: two-cols
title: 概览
---

# 概览
<hr>

::left::

<v-clicks>

## 特点
### 券商定制：国金 国盛 东莞 湘财
### 托管方式
### 策略运行环境
### 官方视频教程
## 功能
### 自带算法交易
### 研究界面
### 策略回测
</v-clicks>

::right::
<Loc at="0-4" w="100%" left="-50px" top="0" fc="white">

![75%](https://images.jieyu.ai/images/2023/09/lesson24-ptrade-login.png)
</Loc>

<Loc at="5" w="100%" left="-60px" top="0" fc="white">

![](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-tutorial.png?1)

## [视频课程链接](https://www.hs.net/cloud/open/faq/productTemplateDetail.html?id=1114)
</Loc>
<Loc at="7" w="120%" left="-150px" top="0" fc="white">

![](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-tools.png)
</Loc>

<Loc at="8" w="120%" left="-150px" top="0" fc="white">

![](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-quant.png)
</Loc>
<Loc at="9" w="120%" left="-100px" top="0" fc="white">

![](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-sma.png)
<Ellipse top="15%" left="40%" s="100"/>
<Ellipse top="3%" left="3%" s="100"/>
</Loc>

<!--
# SEC1

ptrade 没有办法在网上直接下载到
它是所谓的券商采购软件
由券商采购后提供给用户使用
因此需要先找到与 ptrade 
有合作的券商进行开户
然后申请量化权限

申请量化权限一般有资金门槛
目前一般为 30 万
申请通过后
就可以下载 Ptrade 软件

# SEC2

到目前（2023 年）为止
据我知道的有国金、国盛、东莞、湘财等
可能除了头部的券商会有自己的量化平台
比如华泰有MATIC
东财有EMC
他们可能就不太会推其它平台之外
可能其它券商都会有推ptrade或者qmt
大家可以找自己的客服多打听一下
但不同的券商
他当前的工作重心不一样
所以当前力推量化的
可能在开户上优惠就多一些
在资源支持力度上就大一些
反之就少一些
这也是我们寻找开户券商的一个考虑
在这一块
我们通常要考虑资金门槛
手续费
行情速度、资源带宽、技术支持等等

# SEC3 托管方式

Ptrade 是以托管模式运行的
即我们的策略会上传到券商的机房里
在那里运行
Ptrade 提供了一个 Windows 下的客户端
我们可以在该客户端中开发策略
进行回测
成功后
再上传到券商的机房里运行
Ptrade 支持的交易品种：股票
基金 ETF
可转债（T+0）
债券
行情级别为 tick 级
最小时间粒度是 3s
委托档位默认可以获取到十档

托管模式的优点是
行情速度更快
运行更稳定
不需要额外购买云服务器
也不需要进行维护

# SEC4

托管模式有一个固有的特点
就是运行环境只能有一种或者几种固定的选择
用户无法进行定制
在其运行环境里
尽管预安装了比较丰富的库
但是因为一般无法连接互联网
所以我们无法更新但这些库
Python 的版本我看到的是 3.5 版
在这个版本下
提供了几十个第三方库
甚至有AI 的
mysql的
不过不清楚像mysql要怎么连接数据库

另外
除了 ptrade 提供的数据之外
临时计算出来的指标、模型等数据如何存储
这些也尚不清楚

托管模式中
我们需要使用到券商提供的数据源和 API
策略锁定效应比较强
一旦因某些原因
需要变更券商
除非新券商也能提供完全一致的 PTrade 版本和权限
否则会有一定的策略移植开销

# SEC5 视频
我们这个课对ptrade的介绍是概览性的
会帮助大家打下一个框架
要深入学习的话
可以看它的视频
恒生电子提供了 [视频课程](https://www.hs.net/cloud/open/faq/productTemplateDetail.html?
id=1114)
可以免费注册后听课


# SEC6-7
Ptrade 还提供了一些现成的算法交易工具
包括网格、篮子交易、抢单交易、追涨停、拐点交易等等
这些功能基本上各个量化平台
或者券商的一些特殊版本的软件
其实都是有的

# SEC8
作为量化研究员
我们可能更关注它的研究界面
和回测、交易接口这一块
这是研究界面
我们可以在这里创建一些 notebook
并且可以学习 ptrade 中
与交易无关的函数的用法
比如获取行情函数
但在这个界面下
我们不能执行交易
也不能调用跟交易相关的函数

# SEC9
所以
要写一个真正的策略
我们需要通过这个tab进入回测界面
然后创建一个新的策略
当策略创建完成之后
这里的工具条有保存、运行
当策略调试通过之后
我们点击这里的回测按钮进行回测
之后策略的运行情况
及各种指标就会同步显示在右边的面板上
-->
