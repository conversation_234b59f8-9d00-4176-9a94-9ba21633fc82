---
layout: two-cols
---
# 小结
<hr>

## 优点
### 合规性好
### 券商托管运行，免维护
### 行情和下单更快
## 缺点
### 软件环境难定制
### Python版本低
### 数据品种不够丰富
### 因子库？

<Loc at="0" w="50%" left="48%" fc="white" top="12vh">

![](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-tutorial.png?1)

#### https://www.hs.net/cloud/open/faq/productTemplateDetail.html?id=1114
</Loc>


<!--
关于Ptrade我们就介绍到这里
我们前面介绍的它的官方教程
大约有4~5小时视频时长
需要进一步深入学习的同学可以对着视频多练习

最后我们再总结一下ptrade的特点
方便大家在不同的接入方式上进行选择
首先ptrade是一个可以实现实盘的方式
但它并不是一个接入接口
我们要用ptrade
需要全盘使用并且券商托管
不能只使用它来进行交易
这是不可能的

第二，在券商托管环境下
技术栈选择上比较受限制
比如ptrade现在用的还是python 3.5
现在python 3.7都已经停止维护了
最新的版本是python 3.12了
这样大量的新语法、新的第三方库
或者说新的技术不能运用起来
这可能也是不利之处

第二，ptrade在多数券商机房里不能连网
这意味着我们只能使用它提供的数据
那么数据的品种是否足够
价格能否承受这也是我们要考虑的一个点

第三，如何拓展因子库
这也是要考虑的一个问题
可能我们有自己的因子计算
但计算出来的因子如何保存
如何每天更新
如果需要使用数据库怎么办
这一块还没有看到明确的说明
因此 在我们进行选择前
需要跟券商就这些技术细节进行沟通
-->
