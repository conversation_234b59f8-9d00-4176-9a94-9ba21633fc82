---
layout: default
clicks: 6
title: XtTrader
---

# XtTrader
<hr>

<Loc at="0" fc="white" left="0" w="50%" top="12vh">

![75%](https://images.jieyu.ai/images/2023/09/lesson25-xttrade-lifecycle.png)
</Loc>

<Loc at="0" fc="white" top="12vh">

* 实例化trader = XtQuantTrader()
* 注册callback xtrader.register_callback()
* trader.start()
* trader.connect()
* 订阅账号通知 trader.subscribe()
* trader.xxx()
* trader.stop()
</Loc>

<show at="1-6">

```python {all|0-74|76-80|82-84|86-87|88-90|92-95}{maxHeight:'400px'}
from xtquant.xttrader import XtQuantTrader
from xtquant.xttrader import XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant

class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def on_disconnected(self):
        """
        连接断开
        :return:
        """
        print("connection lost")

    def on_stock_order(self, order):
        """
        委托回报推送
        :param order: XtOrder对象
        :return:
        """
        print("on order callback:")
        print(order)
 
    def on_stock_trade(self, trade):
        """
        成交变动推送
        :param trade: XtTrade对象
        :return:
        """
        print("on trade callback")
        print(trade)

    def on_stock_position(self, position):
        """
        持仓变动推送  注意，该回调函数目前不生效
        :param position: XtPosition对象
        :return:
        """
        print("on position callback")
        print(position.stock_code, position.volume)

    def on_order_error(self, order_error):
        """
        委托失败推送
        :param order_error:XtOrderError 对象
        :return:
        """
        print("on order_error callback")
        print(order_error)

    def on_cancel_error(self, cancel_error):
        """
        撤单失败推送
        :param cancel_error: XtCancelError 对象
        :return:
        """
        print("on cancel_error callback")
        print(cancel_error)

    def on_order_stock_async_response(self, response):
        """
        异步下单回报推送
        :param response: XtOrderResponse 对象
        :return:
        """
        print("on_order_stock_async_response")
        print(response.account_id)

    def on_account_status(self, status):
        """
        :param response: XtAccountStatus 对象
        :return:
        """
        print("on_account_status")
        print(status.account_id)

# path为mini qmt客户端安装目录下userdata_mini路径
path = 'D:\\国金QMT交易端模拟\\userdata_mini'

session_id = np.random.randint(20000, 60000)
xt_trader = XtQuantTrader(path, session_id)

# 创建交易回调类对象，并声明接收回调
callback = MyXtQuantTraderCallback()
xt_trader.register_callback(callback)

# 启动交易线程
xt_trader.start()
# 建立交易连接，返回0表示连接成功
connect_result = xt_trader.connect()
assert connect_result == 0

acc = StockAccount('********', 'STOCK')
# 对交易回调进行订阅，订阅后可以收到交易主推，返回0表示订阅成功
subscribe_result = xt_trader.subscribe(acc)
assert == 0
```
</show>

<!--
xttrader是基于qmt-mini的交易接口
对外以Python库的形式
提供策略交易所需要的交易相关的API接口
该接口需开通A股实盘版权限方可登录使用

这里再提一下
qmt-mini也提供了文件扫单交易接口
只不过在有sdk可用的情况下
似乎我们没有必要再去看这个文件单接口了


XtTrader支持多线程操作
以便接受回调消息
在实盘交易中
一个委托往往要等待一段时间才能成交
拥有回调机制是很重要的


我们将通过一个demo
来讲解XtTrader的生命周期及主要交易函数的用法
此图简要地显示了它的主要生命期

首先我们是对XtQuantTrader进行实例化
然后在这个实例基础上
注册一些callback
注册callback并不是必须的
但是当我们注册了callback
并且在后面订阅账号通知之后
那么我们发出的委托
在后面有了状态更新之后
我们就能在callback方法中得到通知

注册完之后
我们就调用start启动一个单独的线程
因为要实现callback
至少需要两个线程
不然如果主线程阻塞在这边
它是没办法接收消息的
然后我们就用connect
连接到qmt-mini
这是一个socket连接
qmt-mini会监听在某个网络端口上
连接成功之后
我们就可以订阅消息
如果只注册callback
但是不订阅消息
那么后面也不会有消息推送过来
也就是说消息订阅分两步
一个是提供消息处理的消费者
一个是保证消息能传过来
把渠道打通
然后我们就可以进行一些交易上的操作
最后我们调用stop方法关闭与qmt-mini的连接

好，这就是xtrader的基本框架和原理

下面我们来看看代码实现
# SEC1

这段代码实现了到订阅账号通知时为止的主要过程


首先我们定义了XtQuantTraderCallback的一个子类
这个子类的定义和使用并不是必须的
我们这里定义这样一个子类
是为了重载以接收各种消息通知
当然我们重载的方法实现上也很简单
就是打印接收到的消息

好，现在准备工作就完成了

# SEC2

实例化XtQuantTrader时
需要传入两个参数
其中之一是极简客户端安装目录下userdata_mini的路径
第二个参数则是会话ID
它是一个整数
每一次调用connect
理论上都应该使用一个新的、惟一的id

# SEC3
这里我们注册了处理callback事件的自定义类
但是
我们还没有发起消息订阅
只有当消息订阅成功
这个自定义类才会接收到消息

# SEC4
这一行是启动交易线程

# SEC5
我们通过connect方法连接到qmt mini
如果连接成功
返回值应该为0
否则就是连接失败了


# SEC6
现在我们生成StockAccount实例
这里需要把账号传入进来
然后我们订阅这个账号上的相关消息
订阅后就可以收到交易主推
返回0表示订阅成功

现在我们就有了一个可以进行交易的客户端
-->
