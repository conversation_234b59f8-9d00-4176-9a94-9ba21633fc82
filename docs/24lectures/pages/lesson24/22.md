---
layout: two-cols
clicks: 4
title: 交易
---

# 交易
<hr>

<show at="0">

```python
stock_code = '600000.SH'
print("order using the fix price:")
order_id = tr.order_stock(acc, 
                        stock_code, 
                        xtconstant.STOCK_BUY, 
                        200, 
                        xtconstant.FIX_PRICE, 
                        10.5, 
                        'strategy_name', 
                        'remark'
                        )
print(order_id)
```

<Loc at="0" fc="white" top="11vh">

```
account: 证券账号
stock_code: 证券代码, 例如"600000.SH"
order_type: 委托类型, 23:买, 24:卖
order_volume: 委托数量, 股票以'股'为单位
price_type: 报价类型, 详见帮助手册
price: 报价价格, 0或指定价格
strategy_name: 策略名称
order_remark: 委托备注
```
</Loc>
</show>
<show at="1">

```python
# 使用订单编号撤单
print("cancel order:")
cancel_order_result = xt_trader.cancel_order_stock(acc, fix_result_order_id)
print(cancel_order_result)
```
</show>

<show at="2">

```python
# 异步下单
print("order using async api:")
async_seq = xt_trader.order_stock_async(acc, 
                                        stock_code, 
                                        xtconstant.STOCK_BUY, 
                                        200, 
                                        xtconstant.FIX_PRICE, 
                                        10.5, 
                                        'strategy_name', 
                                        'remark'
                                        )
print(async_seq)
```
</show>

<show at="3">

```python
# 查询证券资产
print("query asset:")
asset = xt_trader.query_stock_asset(acc)
if asset:
    print("asset:")
    print("cash {0}".format(asset.cash))
```
</show>

<show at="4">

```python
# 根据订单编号查询委托
print("query order:")
order = xt_trader.query_stock_order(acc, fix_result_order_id)
if order:
    print("order:")
    print("order {0}".format(order.order_id))
```
</show>

<!--
现在我们就通过这样一个trader进行交易了

这段代码我们用来下一个委买单
当我们调用 order_stock时
它会返回一个order_id
如果是-1话
意味着创建order失败
如果是大于零的整数
则表明order创建成功
并且这个order_id可以在后面用来进行查询
或者取消订单

order成功之后
由于之前我们已经注册接收通知
所以后面在order状态变更
比如成交了
我们就会得到通知
这里我们下的是一个fix_price单
即限价单
所以我们需要传入一个价格
这里我们传入的是10.5
如果是别的类型
这里传入0就可以了
order类型这里是买入
取值在手册里有
另外它允许我们传入策略名称和一个备注
方便后面进行跟踪
这是比较贴心的一个地方
如果我们是一家机构的话
是有可能一个账号上运行好几个策略的
这样可以把策略的委托区分开

# SEC1
这里是取消订单
这个比较简单
就不详细介绍了

# SEC2
xttrader在交易上
一般有两种API
一种是同步的
我们刚刚看到的是同步的API
另一种是异步的API
它的特征是有一个_asyn后缀
这里就是一个异步订单的例子
异步订单也能马上得到订单号
文档没有说明两种接口的主要区别是什么

# SEC3
这是查询证券资产
也比较简单
就不多介绍了

# SEC4
这是通过订单编号来查询委托的例子
-->
