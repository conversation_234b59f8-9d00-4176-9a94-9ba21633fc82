---
layout: two-cols
title: 量化策略研究功能
---

# 量化策略研究功能
<hr>

::left::
<v-clicks>

## 新建策略
##
## 启动和停止
## 基本信息
## 回测参数
## 参数设置
## 补充数据
</v-clicks>

::right::

<show at="0-1">

![](https://images.jieyu.ai/images/2023/09/lesson24-qmt-new-strategy-1.png)

</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-download-python.png)
</show>

<Loc at="3-6" w="130%" left="-150px" top="0">

![](https://images.jieyu.ai/images/2023/09/lesson24-qmt-editor-1.png)
</Loc>

<Loc at="4" w="50%" left="0" top="-50px">

![](https://images.jieyu.ai/images/2023/09/lesson24-qmt-editor-2.png?1)

<Arrow x1="350" y1="400" x2="200" y2="200" color='red'/>

</Loc>

<Loc at="5" w="50%" left="0" fc="white" top="50px">

![75%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-editor-3.png?1)

<Arrow x1="440" y1="310" x2="200" y2="200" color='red'/>
</Loc>

<Loc at="6" w="100%" left="-100px" top="0" fc="white">

![](https://images.jieyu.ai/images/2023/09/lesson24-qmt-editor-4.png?1)

<Arrow x1="510" y1="310" x2="200" y2="200" color='red'/>
</Loc>

<show  w="100%" left="-20%" at="7" fc="white">

![](https://images.jieyu.ai/images/2023/09/lesson24-qmt-download-data.png)
</show>

<!--
这一节介绍策略研究功能
我们有两种方式可以进入策略研究
一种是在系统提供的策略的基础上
通过编辑修改这些策略形成自己的策略
另一种是点击新建策略按钮
建立自己全新的策略
系统提供了10多种python策略
不过都比较简单
大家可以看看它的指增策略
是在指数股票池内的一个动量策略
机器学习策略用的是svm(支持向量机)模型
除了这张图列出的之外
还有ARIMA
使用的是statmodels中的
时间序列分析模块tsa
关于statsmodels及它的时间序列分析
我们之前的课程简单地提过一两句
实际上它在电商这些可解释性强的场景下
有一定的作用
在量化领域可以作为思路借鉴
简单使用很难有太好的效果

# SEC1

我们在模型研究tab页
找到新建策略
即可创建新的python量化策略

# SEC2
注意第一次运行时
需要点击“下载Python库”这个按钮
来初始化python运行环境
这个看券商
有的券商给QMT版本给我们的时候
可能是不带python库的
当前（2023年Q3）我们下载到的
python的版本是3.6.4
这个版本还是比较旧

我们刚刚下载的python
自带了一些常用库
qmt也允许我们安装新的库
这里我们就讲一下怎么操作
注意我们这里讲的python
要与后面的qmt-mini的sdk相区别
这是两个不同的python系统

通常情况下
我们安装python库
都是在某个python环境下
运行pip来进行安装的
qmt并没有暴露出来一个python的命令行接口
所以我们要进行三方库安装
就需要用一些技巧

首先我们要创建一个跟qmt使用的python
版本一致的python环境
然后在这个环境下
把三方库安装到QMT中python库的路径下
这个路径就是我们这里的图中所指示的位置
一般是qmt安装路径下的
x64_bin/Lib/site-packages

这里我们先讲一下为什么版本要一致
一个规范的python包
它是会声明自己兼容的python的版本的
比如像omicron
我们声明的python版本就是3.8
不支持3.9
因为Omicron依赖的个别库
在去年Q2时都还没能支持3.9
因此omicron就只能支持python 3.8
那么如果我们的虚拟环境中
运行的python大于3.9
或者小于3.8
都不能安装omicron

所以，qmt用的python是3.6
我们创建的虚拟环境也必须是3.6
这样安装三方库时
pip就不会选择不兼容3.6版本的三方库
避免安装后无法正常运行的问题

所以 我们首先创建一个
python版本为3.6的虚拟环境
然后在这个环境下
运行这个命令

pip install xxx -t E:\qmt\bin.x64\Lib\site-packages

就可以把库安装到QMT中了
这里的xxx是我们���安装的三方库
而-t是用来指定安装路径的
后面的E:\qmt。。等则是这个图里的python库的路径

# SEC3

当策略编写好后
点击编译以保存策略
这里的按钮名字不是保存
而是编译
是因为QMT还支持其它开发语言
但因为python不存在编译的概念
所以实际上执行的是保存的功能
保存之后
可以点击运行
它会用少量的bar快速把这个策略过一遍
这个时候
这些语句基本上就会被执行
那如果说我们的策略有错误
在这个时候就会爆出来
我们可以用它的一些调试的方法
进行去调试
调试通过
我们就可以点回测
进行一个完整的回测

# SEC4 基本信息

进行回测时
往往有一些参数要指定
这些参数大致可以分为两类
一类是策略本身的参数
比如双均线策略中的长短周期
另一类则是回测相关的参数
比如佣金、滑点、起止时间等


在backtrader这样的框架中
所有的参数都通过代码来指定
在一些集成回测框架
比如Ptrade和聚宽中
回测相关的参数可以在界面上指定
而在QMT中
无论是哪一类参数
一般都通过界面来指定
这可能是因为qmt兼容了一些老的软件的功能
比如通达信中
用户可以通过公式来决定买卖点
这些公式需要从界面上设置一些参数
QMT通过VBA策略
保留了大多数通达信的这些公式
所以通过界面来指定参数的方式
也被保留、沿用到python这边的回测上来

关于回测参数的指定
QMT一共提供了三个信息卡来进行设置
其一是基本信息
在这里最重要的是默认周期、默认品种和复权方式
QMT中量化策略都有自己的绘图界面
在回测运行时
在我的主页那个界面中
它会自动切换成正在回测的策略相关的k线图
带上我们的交易日志
以及策略的一些收益指标等等

在这个界面中
存在一个主图和若干个副图
主图为k线图
因此需要我们指定k线显示的品种
因为有可能我们的策略
运行的是一个组合
那这种情况下
我们就需要指定其中的某一支股票
或者它的参照基准来绘制k线
同时我们也要指定周期和复权方式
此外我们还可以在策略中
调用QMT的一些绘图函数
比如在k线上叠加一个字符串
或者一个什么记号
它提供了paint、text这样一些方法
这些方法需要我们进行一些设置
以告诉它最终绘图到主图还是副图
以及绘制到哪个位置
这些功能可能对新人来讲还是比较友好的

# SEC5 回测参数
回测参数部分是我们比较熟悉的
在做回测之前
我们需要指定比如
起止时间、基准、本金等信息
在其它框架中
比如ptrade
或者backtrader
大家应该还有印象
我们是通过API来进行设置的


# SEC6
还有一个界面
就是策略的参数
比如双均线策略中
我们可能需要传入长短均线的周期
long 和 short
这些要通过参数选项卡来设置
比如在这个图中
我们设置了win这个参数
它将成为一个全局变量
我们可以在策略中直接使用win这个变量
另外我们注意到它还有
最小、最大和步长的设置
这个是做什么的呢？
它是对应参数优化的
比如我们在双均线策略中
我们可能想看看短周期用5，10
长周期用20，30，40来试
这样我们就可以把long设置为
参数名为long
最新20
最小20
最大40
步长为10试试
当然，现在我们也看出来了
通过界面来设置回测参数有它的弊端
比如这里，我们没法将优化参数设置为
20, 30, 60这样步长不等距的参数值
进行这样的设置之后
我们还可以点击公式测评
在这里选择某个策略评估指标
就可以让QMT来搜索最佳参数

# SEC7
如果我们现在立即进行回测
有可能无论什么样的策略
都会回测失败
原因是没有足够的数据
在qmt中
数据获取共分两步
首先是将数据下载到本地缓存
然后才是调用api来从本地读取数据
这种做法
在性能上要优秀很多
实际上类似于大富翁框架
只不过大富翁框架
会以服务的方式运行Omega
始终保持对全市场数据的实时同步
不需要最终用户来进行管理

因此
在回测之前
我们需要���通过数据管理功能
将必要的数据保存到本地

好 关于QMT的全功能版本
我们就介绍到这里
这里我们省略了也许是QMT最核心的功能
也就是对它的量化投研功能
即策略编辑、回测功能的详细介绍
原因有二
第一，我们这个模块主打的就是
如何接入实盘交易
而不是介绍新的量化平台或者框架
因此，我们会把重点放在接入实盘交易部分
也就是接下来我们要重点介绍的
qmt-mini上
其次
我们更推荐大家基于开源技术
构建自己的本地化量化框架
这样可以用到最新的技术
避免技术锁定在某个平台或者供应商上
另外，使用开源技术
我们就可以在每一个部分上
都使用最好、最专业的做法
比如像刚刚提到过的
参数优化部分
qmt为了兼顾vba做了某些tradeoff
从而导致参数优化的路径不能完全自定义
另外，在绘图上
qmt的做法更适合新手
可以快速把功能撸出来
但在可定制性上要略差一些
而且学习资料也会少一些。

好，接下来，我们就转入mini-qmt的学习
-->
