---
layout: default
---
# 策略框架 - 运行结果
<hr>

```python{all}{maxHeight:'400px'}
2023-09-11 16:31:12 开始运行回测, 策略名称: zillionare
2023-06-05 00:00:00 - INFO - context is type of StrategyContext'>
2023-06-05 00:00:00 - INFO - context contains member: ['blotter', 'capital_base', 'commission', 'enddate', 'initialized', 'on_initialize', 'pboxUsername', 'portfolio', 'previous_date', 'recorded_vars', 'sim_params', 'slippage', 'startdate']
2023-06-05 00:00:00 - INFO - 

2023-06-05 08:30:00 - INFO - >>> before trading: 2023-06-05
2023-06-05 08:30:00 - INFO - data is type: <class 'IQEngine.core.bar.BarDict'>
2023-06-05 08:30:00 - INFO - each item contains member: ['dt', 'is_open', 'mavg', 'money', 'pre_close', 'price', 'returns', 'set_frequency', 'sid', 'stddev', 'vwap']
2023-06-05 08:30:00 - INFO - data is 11.93 11.59 11.93
2023-06-05 08:30:00 - INFO - got 300 stocks in pool
2023-06-05 08:30:00 - INFO - 

2023-06-05 15:00:00 - INFO - >>> handle_data: 2023-06-05
2023-06-05 15:00:00 - INFO - data is 11.91 11.93 11.91
2023-06-05 15:00:00 - INFO - 生成订单，订单号:7c92f2aa623c460280077ee8af7d06d2，股票代码：000001.XSHE，数量：买入100股
2023-06-05 15:00:00 - INFO - <Position {'sid': '000001.SZ', 'cost_basis': 12.060584886999999, 'amount': 100, 'business_type': 'stock', 'enable_amount': 0, 'last_sale_price': 11.91}>
2023-06-05 15:00:00 - INFO - [<Order {'priceGear': 0, 'id': '7c92f2aa623c460280077ee8af7d06d2', 'symbol': '000001.XSHE', 'limit': None, 'entrust_no': None, 'amount': 100, 'dt': datetime.datetime(2023, 6, 5, 15, 0), 'status': '8', 'created': datetime.datetime(2023, 6, 5, 15, 0), 'filled': 100}>]
2023-06-05 15:00:00 - INFO - <Portfolio {'positions': {'000001.SZ': <Position {'sid': '000001.SZ', 'cost_basis': 12.060584886999999, 'amount': 100, 'business_type': 'stock', 'enable_amount': 0, 'last_sale_price': 11.91}>}, 'start_date': datetime.date(2023, 6, 5), 'cash': 98803.941998299997, 'positions_value': 1191.0, 'returns': -0.00015058488700003814, 'pnl': -15.058488700003814, 'portfolio_value': 99984.9415113, 'capital_used': 1206.0584887}>
2023-06-05 15:00:00 - INFO - 

2023-06-05 15:30:00 - INFO - >>> after_trading: 2023-06-06
2023-06-05 15:30:00 - INFO - 

2023-06-06 08:30:00 - INFO - >>> before trading: 2023-06-05
2023-06-06 08:30:00 - INFO - data is type: <class 'IQEngine.core.bar.BarDict'>
2023-06-06 08:30:00 - INFO - each item contains member: ['dt', 'is_open', 'mavg', 'money', 'pre_close', 'price', 'returns', 'set_frequency', 'sid', 'stddev', 'vwap']
2023-06-06 08:30:00 - INFO - data is 11.91 11.93 11.91
2023-06-06 08:30:00 - INFO - got 300 stocks in pool
2023-06-06 08:30:00 - INFO - 

2023-06-06 15:00:00 - INFO - >>> handle_data: 2023-06-05
2023-06-06 15:00:00 - INFO - data is 11.84 11.91 11.84
2023-06-06 15:00:00 - INFO - 生成订单，订单号:a5386969dc87426295f4d9389acd8686，股票代码：000001.XSHE，数量：买入100股
2023-06-06 15:00:00 - INFO - <Position {'sid': '000001.SZ', 'cost_basis': 12.0255831825, 'amount': 200, 'business_type': 'stock', 'enable_amount': 100, 'last_sale_price': 11.84}>
2023-06-06 15:00:00 - INFO - [<Order {'priceGear': 0, 'id': 'a5386969dc87426295f4d9389acd8686', 'symbol': '000001.XSHE', 'limit': None, 'entrust_no': None, 'amount': 100, 'dt': datetime.datetime(2023, 6, 6, 15, 0), 'status': '8', 'created': datetime.datetime(2023, 6, 6, 15, 0), 'filled': 100}>]
2023-06-06 15:00:00 - INFO - <Portfolio {'positions': {'000001.SZ': <Position {'sid': '000001.SZ', 'cost_basis': 12.0255831825, 'amount': 200, 'business_type': 'stock', 'enable_amount': 100, 'last_sale_price': 11.84}>}, 'start_date': datetime.date(2023, 6, 5), 'cash': 97614.8843375, 'positions_value': 2368.0, 'returns': -0.00037116636500000855, 'pnl': -37.116636500000851, 'portfolio_value': 99962.883363500005, 'capital_used': 2405.1166364999999}>
2023-06-06 15:00:00 - INFO - 

2023-06-06 15:30:00 - INFO - >>> after_trading: 2023-06-06
2023-06-06 15:30:00 - INFO -
2023-09-11 16:31:18 策略回测结束
```

<!--
这是我们刚刚的那段代码
在回测运行之后打印出来的日志
这里我们要注意
PTrade有一个功能上的特点
或者说优点
我们来介绍一下
注意看
它这边的时间是不一致的
这里是9月11日
接下来变成了6月5日
这里不是错误
恰恰是一个优点

Ptrader把日志进行了分类
有系统日志和回测事件日志
系统日志是指像回测开始、回测结束
这样一些事件的日志
它由Ptrade系统生成

回测日志则是策略生成的日志
比如我们在handle_data中
发出交易信号等等
实际上我们是希望这些事件
按照回测当前bar的时间来进行记录
这样方便进行调试

通过这段日志
我们也看清楚了策略回测期间
生命期管理
它是有初始化和结束
然后在每一个交易日
由before_trading_start开始
中间是若干个handle_data
然后以after_trading_end结束
-->
