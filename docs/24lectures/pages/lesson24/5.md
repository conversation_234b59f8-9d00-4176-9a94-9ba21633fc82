---
layout: default
clicks: 7
title: 策略框架
---

# 策略框架
<hr>

<Loc at="5" fc="white" top="12vh">

```
capital_base -- 起始资金
previous_date –- 前一个交易日
sim_params -- SimulationParameters对象
    capital_base -- 起始资金
    data_frequency -- 数据频率
portfolio -- 账户信息，可参考Portfolio对象
initialized -- 是否执行初始化
slippage -- 滑点，VolumeShareSlippage对象
    volume_limit -- 成交限量
    price_impact -- 价格影响力
commission -- 佣金费用，Commission对象
    tax—印花税费率
    cost—佣金费率
    min_trade_cost—最小佣金
blotter -- Blotter对象（记录）
recorded_vars -- 收益曲线值
```
</Loc>

```python{all|9-23|25-40|46-60|62-63|9-23|14-21|22-23}{maxHeight:'400px'}
def get_members(obj):
    items = []
    for item in dir(obj):
        if not item.startswith('_'):
            items.append(item)
            
    return items
    
def initialize(context):
    _type = str(type(context)).split(".")[-1]
    print("context type:", context_type) 
    print("members:", get_members(context))

    if is_trade():     
        set_benchmark('000001.SS')
    
        set_commission(commission_ratio=0.0003, 
                       min_commission = 5.0)
        set_fixed_slippage(fixedslippage=0.2)
        set_volume_ratio(volume_ratio=0.2)
    
    g.security = '000001.SZ'
    set_universe(g.security)
            
def before_trading_start(context, data):
    print(">>> before trading:", context.startdate)
    print("data is type:", type(data))
    print("each item contains member:", get_members(data[g.security]))

    item = data[g.security]
    print('data is', item.price, item.pre_close, item.close)
    
    # 去掉ST，停牌、退市
    stocks = get_index_stocks('000300.XBHS')

    st = get_stock_status(stocks, 'ST')
    halt = get_stock_status(stocks, 'HALT')
    delisted = get_stock_status(stocks, 'DELISTING')

    for stock in stocks.copy():
        if st[stock] or halt[stock] or delisted[stock]:
            stocks.remove(stock)

    log.info("{} stocks in pool".format(len(stocks)))

def handle_data(context, data):
    print(">>> handle_data:", context.startdate)
    item = list(data.values())[0]
    print('data is', item.price, item.pre_close, item.close)
    
    _id = order(g.security, 100)
    log.info(get_order(_id))

    order_obj = get_orders()
    log.info(order_obj)

    position = get_position(g.security)
    log.info(position)
    
    log.info(context.portfolio)

def after_trading_end(context, data):
    print(">>> after_trading:", context.enddate)
```

<!--
刚刚我们介绍了ptrade的基本功能
现在我们就来介绍
如何在ptrader中实现一个策略并运行
要实现一个策略
我们必须实现这样一些方法：

# SEC1
initialize 用来做策略初始化
在回测和交易期间
它只在最初被调用一次
这跟bactrader中的init方法是一样的

# SEC2
before_trading_start 
每天执行一次
我们可以用它来做盘前初始化
无论我们的策略是以什么样的周期级别在运行
比如是tick还是分钟
它都只会每天开盘前运行一次
不过我们要注意
在回测中与实盘中
它们被调用的时间是不一样的
这点大家要注意下
可以看看它的文档说明

# SEC3 handle data
然后我们要实现handle_data这个方法
它相当于backtrader中的next方法
这是我们实现策略逻辑
和发出交易指令的地方

# SEC4 after trading end
最后
我们可以通过after_trading_end
这个函数来执行一些收盘工作
比如保存今天的交易结果
或者说跟策略相关的中间状态
特别是在托管模式下
平台进行维护这种情况
不是我们自主可控的
平台会保证在收盘之后才进行维护
在维护之后
会让你的策略再运行起来
这些不用我们去管
但是
如果我们的策略有一些状态是在内存中的
那样两次启停之间
这些中间状态就会丢失掉
我们就没办法回到前一天交易束时的状态
所以我们改写这个函数还是很有必要的

这些函数都是既在回测期间被调用
又可以交易期间被调用的

除这些函数外
还有一些函数只能交易期间被调用
比如
我们可以实现tick_data

这样可以在tick级别实现交易
刚刚介绍的handle_data
它是只会在日线或者分钟线级别被调用的

一个委托发出去之后
常常并不会立刻就成交
比如我们一个买单
挂的价太低了
可能只有当大盘都下跌时
它的价格才会打下来
我们的策略不可能死等它
一般会通过一个回调来收到通知
on_oder_response就是来响应这样的回调的
一旦order成交
我们就可以在这里得到通知
此外还有on_trade_response方法
通过这样两个方法
策略可以在委托和交易状态改变时
得到通知
进行相应的处理

前四个函数构成了我们交易的基础框架
下面我们就进一步详细介绍这四个函数


# SEC5 init
初始化函数只在策略回测
或者交易启动时
被调用一次
一般来说
我们通过它来设置佣金、滑点
回测撮合量比、回测比较基准以及股票池
这些在回测中往往是只需要设置一次的
注意
关于佣金、回测撮合量比
回测比较基准的设置
在交易进行设置并没有意义


这个方法接受一个StrategyContext类型的参数
这个参数被称为上下文对象
在该对象中
包含了佣金、滑点等设置信息
也包括账户信息、收益曲线值等
我们刚刚说的那些设置信息
设置后就会保存到这个对象中

# SEC6
is_trade方法来用判断当前运行环境
是回测还是实盘交易
有一些方法在交易中是无效的
而另一些方法在回测中是无效的
如果在回测中
调用了只在交易状态下支持的函数
或者反过来
就会出现错误
因此我们需要进行判断
只在恰当的场景下调用被支持的函数
这是学员常问的一个问题
就是怎么让自己的策略在回测后
不进行修改就能进入实盘交易
通过is_trader进行场景判断
在不同的场景下调用对应的方法
是解决这个问题其中的一个技巧

set_benchmark用来设置业绩比较基准
如果不设置
默认采用沪深300
这里使用的是上证指数
ptrade中有哪些指数呢？
可以从帮助文档中的get_index_stocks函数
的帮助中的”指数列表“链接进去查看
这部分我们先带过
后面还要专门去讲


set_fixed_slippage用来设置滑点
除了这种固定滑点
它还有按比例设置的
set_volume_ratio用来设置撮合时
按成交量的比例进行委托匹配
这里我们设置了0.2
如果我们看到的成交量是500手
那它就只会匹配100手
它的文档没有说明
这个0.2是按bar还是按天来算
大家如果要用ptrade的话
可能自己需要去研究一下
一般情况下应该是按天
即当你在分钟级别下回测时
比如9：31下了一个买单
当时和9:32都没有成交
这个买单一般会执行到收盘
在收盘后，无论有没有执行完成
这个买单都会关掉
所以一般是按天算


# SEC7 g.security
g是一个全局对象
这个对象初始值为空
我们可以通过属性语法来为它赋值
以便我们在不同的函数中传递数据
对任何python对象
我们都可以通过属性赋值语法来给它增加一个新的属性
但是
我们必须先增加这个属性
然后才能访问它
如果我们颠倒两行代码的顺序的话
就会出错


在这里
我们给g.security赋值了一个字符串
此处也可以是一个List[str]

通过set_universe我们设置了要操作的股票池
它的作用是
如果我们在初始化时调用了该函数
则后面在get_price、get_history时
我们可以不用传入security_list这个参数
另外
主要是在handle_data函数中
它会自动把这些股票的数据传入进来
handle_data有两个参数
context和data
这个data
就是g.security中设置的股票对应的数据

-->
