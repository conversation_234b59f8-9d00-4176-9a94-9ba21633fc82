---
layout: two-cols
---
# 功能概览
<hr>

::left::

<v-clicks>

## 安装和申请量化权限
## 功能概览
### 我的板块
### 模型研究
### 模型交易
## 风控设置

</v-clicks>

::right::

<show at="0-1">

![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-login.png)
</show>
<Loc at="2-3" w="120%" top="0" left="-20%">

<!--我的板块-->
![](https://images.jieyu.ai/images/2023/09/lesson24-qmt-home.png)
</Loc>

<Loc at="4" w="120%" top="0" left="-20%">

<!--模型研究-->
![](https://images.jieyu.ai/images/2023/09/lesson24-qmt-research.png)
</Loc>

<Loc at="5" w="120%" top="0" left="-20%">

<!--交易-->
![](https://images.jieyu.ai/images/2023/09/lesson24-qmt-trade.png)
</Loc>

<Loc at="6" w="120%" top="0" left="-20%">

<!--风控-->
![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-risk-management.png)
</Loc>

<!--
# SEC1 安装
与Ptrade一样
我们无法从网上直接下载到QMT软件
需要在开户后
找券商客服索取软件
安装后
还需要专门申请以开通量化权限


目前（2023年）个别券商还能做到2万元起实现开户
但这应该只是阶段性的
后面很可能会恢复到正常的门槛要求
至少到30万左右


安装时
我们要给QMT找一个大一点的分区
因为QMT需要缓存行情数据
这些数据占用空间比较大
当然
如果你安装时没有选择大的分区
它的数据目录是可以更改的



启动后
登录界面如图所示
这里的极简模式如果被选中
则登录的就是qmt-mini
当我们以sdk方式运行量化策略时
就需要启动qmt-mini
它的sdk之所以能提供数据和交易
其实是经与qmt-mini通讯来实现的
现在我们就来启动全功能的QMT
来介绍一下它的界面和功能
注意我们启动的是行情+交易的界面
并且不勾选“极简模式”
如果只启动交易界面
那么界面上会少一个“我的板块”的功能


# SEC2 SEC3 我的板块

我的板块是一个dashboard性质的界面
主要包括大盘指数、自选股、热门板块、策略列表、
快速交易、用户信息、账户信息和运行中的交易
等 8 个模块

从这个界面可以看出
它的一级菜单除了我的板块之外
还有行情、交易、模型研究和模型交易
等菜单项
作为量化研究者
我们更关注模型研究和模型交易两个模块
其它两个菜单项就不介绍了

# SEC4
这是模型研究界面
我们可以在这里新建模型
也可以编辑系统预先提供的模型


# SEC5
我们在模型交易界面安排策略进入模拟或者实盘
模型运行过程中
会有相应的模型交易信号
输出日志、持仓、委托和成交等等信息

# SEC6 风控
最后我们简单地看一下它的风控设置
如果我们自己来实现这些规则
可能要花比较多的时间
而且风控规则还有复杂多变的特点
所以，这是QMT有优势的地方
-->
