---
layout: null
clicks: null
---

#
<hr>

<Loc at="2" fc="white" top="11vh" w="50%" h="80%">

```
'dt'
'is_open'
'mavg'
'money'
'pre_close'
'price'
'returns'
'set_frequency'
'sid'
'stddev'
'vwap'
```
</Loc>

<Loc at="3" top="11vh" w="50%" fc="white">

<!--order-->
```
'id': '4c348b450b8747a996e3f25d2e430a76', 
'dt': datetime.datetime(2023, 9, 7, 15, 0), 
'priceGear': 0, 
'created': datetime.datetime(2023, 9, 7, 15, 0), 
'amount': 100, 
'status': '8', 
'entrust_no': None, 
'limit': None, 
'symbol': '000001.XSHE', 
'filled': 100
```

</Loc>

<Loc at="4" top="11vh" w="50%" fc="white">
<!--position-->

```
'enable_amount': 0, 
'amount': 100, 
'last_sale_price': 11.33, 
'sid': '000001.SZ', 
'business_type': 'stock', 
'cost_basis': 11.480556641
```
</Loc>

<Loc at="5" top="11vh" w="50" fc="white">

```
    'portfolio_value': 99984.9443359, 
    'positions': {
        '000001.SZ': {'enable_amount': 0, 
                    'amount': 100, 
                    'last_sale_price': 11.33,
                    'sid': '000001.SZ', 
                    'business_type': 'stock', 'cost_basis': 11.480556641
                    }
        }, 
    'positions_value': 1133.0, 
    'start_date': datetime.date(2023, 9, 7), 
    'cash': 98861.944822899997, 
    'capital_used': 1148.0556641000001, 
    'pnl': -15.055664100005473, 
    'returns': -0.00015055664100005473

```
</Loc>

```python{25-44|25-40|46-60|51-52|57-58|60|62-63}{maxHeight:'400px'}
def get_members(obj):
    items = []
    for item in dir(obj):
        if not item.startswith('_'):
            items.append(item)
            
    return items
    
def initialize(context):
    _type = str(type(context)).split(".")[-1]
    print("context type:", context_type) 
    print("members:", get_members(context))

    if is_trade():     
        set_benchmark('000001.SS')
    
        set_commission(commission_ratio=0.0003, 
                       min_commission = 5.0)
        set_fixed_slippage(fixedslippage=0.2)
        set_volume_ratio(volume_ratio=0.2)
    
    g.security = '000001.SZ'
    set_universe(g.security)
            
def before_trading_start(context, data):
    print(">>> before trading:", context.startdate)
    print("data is type:", type(data))
    print("each item contains member:", get_members(data[g.security]))

    item = data[g.security]
    print('data is', item.price, item.pre_close, item.close)
    
    # 去掉ST，停牌、退市
    stocks = get_index_stocks('000300.XBHS')

    st = get_stock_status(stocks, 'ST')
    halt = get_stock_status(stocks, 'HALT')
    delisted = get_stock_status(stocks, 'DELISTING')

    for stock in stocks.copy():
        if st[stock] or halt[stock] or delisted[stock]:
            stocks.remove(stock)

    log.info("{} stocks in pool".format(len(stocks)))

def handle_data(context, data):
    print(">>> handle_data:", context.startdate)
    item = list(data.values())[0]
    print('data is', item.price, item.pre_close, item.close)
    
    _id = order(g.security, 100)
    log.info(get_order(_id))

    order_obj = get_orders()
    log.info(order_obj)

    position = get_position(g.security)
    log.info(position)
    
    log.info(context.portfolio)

def after_trading_end(context, data):
    print(">>> after_trading:", context.enddate)
```

<!--
前面我们在init方法中设置了股票池
这里要指出
这纯粹是出于演示目的
除非我们的策略是针对某个特殊的标的的
否则
我们都不该在initialize中进行股票池的设置
而应该把它放到 before_trading_start 中

无论是在回测还是在交易中
我们都希望过滤掉ST、退市股和当天停牌股
进行这样的设置
before_trading_start是最恰当的地方

如果我们在initialize中进行股票池设定
则在回测中会带来前视偏差
为什么？
因为它只在回测开始前运行一次
所以，它获得的列表是静态的
是当下所见的一个列表
而我们在回测中
则是需要在回测当天刷新这个列表
另外，如果是在实盘交易中
如果我们用 initialize 中设置的股票池
则可能会得到错误的ST和停牌信息
比如 我们在1月4号启动策略
当时把ST和停牌的过滤掉了
现在是10月了 这中间信息不知道变化多少轮了


before_trading_start接收两个参数
其一是context
这个参数与initialize方法中的一样
第二个是data参数
它是一个BarDict对象
它的 键是我们通过set_universe设置的那些股票代码
值则是BarData对象
它包括这些属性

# SEC1 Bardata
没有关于这些字段的文档
不过
经验证
dt就是bar的归属时间
money是成交额
pre_close是前收价
price则是当前实时价格（在回测中为当前bar的收盘价）
close则是当前bar的收盘价
sid是证券代码
比如603986.XSHG
returns是当日涨跌幅


这里有几个函数要介绍一下


第34行
get_index_stocks用来获取指数成份股
这里我们没有传入时间
在回测中
它会自动取回测周期对应的成份股列表


我们通过get_stock_status来获取当前股票的状态
查询类型可以是ST
HALT或者DELISTING中的任何一个


第40行到42行
我们从stocks中移除掉当前属于ST
 HALT和DELISTING的股票
这里使用了一个循环
我们不能在列表迭代过程中删除元素
所以第40行使用了copy方法来获得一个副本


# SEC2
handle_data是我们书写策略逻辑的地方
这个示例中我们没有实现任何策略
只是简单地下单
然后查看仓位、资产和委托


我们通过order函数来买入了100股股票
它接受三个参数
分别是security
 amount和委买限价
如果不提供限价
则会以当前价交易
除了order函数外
还有order_target
 order_value
order_target_value等函数可用于交易


在价格变动激烈时
这些order函数可能无法立即成交
Ptrade还提供了一个order_market函数
供我们按市价成交


ptrade没有专门的buy/sell方法
当amount为正时
意味着买入
为负则意味着卖出


order系列函数的返回值是order_id
尽管它没有在文档中进行说明
如果我们需要撤销这个order
可以调用cancel_order
传入这个id来实现
我们可以通过get_order
传入这个order_id来查询order的状态


order对象有以下属性

# SEC3 order
这里最重要的属性是status字段
无论是在回测还是在实盘
我们都要关注这个字段
如果回测当中
如果这里是没有成交的话
我们要去检查是不是资金不足等等原因
以检查策略是不是有什么问题

这里我们还要关注symbol这个字段
它的输出是000001.XSHE
到目前为止
我们已经看到了在ptrade中
证券代码有000001.SS表示上证指数
000001.SZ表示平安银行
现在我们又看到了000001.XSHE这样的表示方法
现在我们就来讲解一下ptrade中证券代码的格式
这部分没有看到官方文档
所以是我自己通过试验做出来的一个总结
大家也可以关注它的文档
看后面会不会有一个正式的说明
首先 Ptrade使用后缀式
在股票编码上
上交所编码为'SS'
深交所所码为'SZ'
从文档看
目前还不支持北交所
所以这里的000001.SS是上证的指数
因为上证的股票一定是6开头的
这里要注意的是
有个别API似乎允许我们
使用像000001.XSHE这样的证券代码
或者一些函数返回值中
比如在这里的order的返回值中
可能会有证券代码
那这个证券代码就可能是以.XSHE结尾的
这种表示方法上的不一致
可能会带来问题
比如 当我们下一个order时
用的代码是000001.SZ
返回的结果却是000001.XSHE
这会给校验上带来不必要的转换需求

get_orders供我们查询账户当日在柜台的全部委托记录


我们通过get_position来获得当前持仓
它接受一个参数
security
用来查询某个标的的持仓
如果不提供
则返回全部持仓信息


持仓信息示例如下

# SEC4 position
持仓同样是一个dictionary

enable_amount意味着当前可卖数据
sid为证券代码
注意这里使用的后缀又是.SZ
而在get_order中后缀则是.XSHE
Ptrade在这方面再次出现不一致的情况


# SEC5 portofolio
最后
我们通过context.portfolio
来获取当前账户的基础信息
它有哪些内容呢
它包括了市值、现金、当前损益（绝对值）、回报率和持仓
持仓是另一个对象
我们刚刚已经介绍过了

# SEC6
如果我们希望有一些工作安排在收盘后做
则可以在after_trading_end中进行
比如
在交易中
我们可以安排收盘后
对一些重要信息进行持久化
-->
