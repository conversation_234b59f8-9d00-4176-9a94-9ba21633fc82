---
layout: two-cols
clicks: 6
title: 极简模式与XtQuant
---

# 极简模式与XtQuant
<hr>

::left::

<show at="0">

## 进入极简模式
</show>
<show at="1-2">

## 进入极简模式
## 自动重启
</show>
<show at="3-5">

## 进入极简模式
## 自动重启
## XtQuant安装
</show>

<show at="6">

## 进入极简模式
## 自动重启
## XtQuant安装
## XtData/XtTrade
</show>

::right::

<show at="0">

![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-login.png)
</show>

<show at="1">

![](https://images.jieyu.ai/images/2023/10/lesson24-qmt-auto-restart.png)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-mini-download-python.png)
</show>

<show at="4">

![](https://images.jieyu.ai/images/2023/10/lesson24-download-xtquant.png)
</show>

<Loc at="4" w="100%" fc="white" left="0" top="30vh">
http://docs.thinktrader.net/pages/633b48/
</Loc>

<Loc at="5" w="100%" fc="white" left="0" top="11vh">

```python
import sys

path = r'c:\gszqqmt\bin.x64\Lib\site-packages'
sys.path.append(path)
```
</Loc>

<!--
我们再次重温一下
进入极简模式的方式是在登录界面上
勾选极简模式
我们在介绍东财EMC时
介绍过一个简单维护技巧
就是需要每天自动重启EMC
在QMT中
我们可以在这个界面选中自动登录
然后在登录后 

# SEC1
在 设置 > 自动重启 设置中
选中 启用开机自动启动 和 启用自动初始化 选项
再设置一个自动重启时间 
这样QMT就能每天自动重启了
这样可以大大增加它的稳定性

这里强调一下
要使用xtquant sdk
就必须启动qmt-mini
两者必须在同一台机器上
必须协同工作


# SEC2
在第三方量化框架中使用xtquant之前
我们需要将xtquant安装到
第三方量化框架同一个虚拟环境下
xtquant没有提供whl的安装方式
只能通过源代码拷贝的方式安装
xtquant支持的python版本比较新
也比较全
现在是从3.6到3.11都支持

# SEC3 安装
我们在完全模式下
下载安装Python库之后
在图中所示位置的Lib/site-packages下
就可以找到一个名为xtquant的目录

# SEC4 下载
这个sdk也可以在它的官网上下载
下载页面是 http://docs.thinktrader.net/


假设我们为量化策略创建了一个名为xt的conda 虚拟环境
为了在这个虚拟环境下使用xtquant
我们有以下几种方法

一是将xtquant目录
拷贝到虚拟环境xt的lib/site-packages目录下
也可以建立从虚拟环境的lib/site-packages/xtquant
到QMT中上述xtquant目录位置的软链接
这种模式下
一旦QMT中的xtquant进行了更新
我们自己虚拟环境（xt）下的xtquant也将进行更新
从稳定性上来讲的话
可能我们不倾向于这种模式
我们更倾向于经过仔细的测试之后
再做手动更新

另外一种方式就是在量化策略初始化时
加入以下代码

# SEC5

这样也会在当前环境下
自动加入对xtquant库的引用


# SEC6
xtquant包含两个模块
其一是XtData
Xtdata作为行情模块
主要提供行情数据
历史和实时的K线和分笔
财务数据、合约基础信息
板块和行业分类信息等
qmt-mini本身就有数据缓存
所以使用xtquant的话
似乎也可以不需要第三方量化框架
提供的数据本地化服务
只使用它的策略和回测框架就可以了
但是我们也要注意
目前来看
QMT提供的板块信息
行业分类信息
合约基础信息等
它不是PIT类型的数据
所以在回测中使用这些数据
会引入未来数据

另一个是XtTrader
XtTrader作为交易模块
封装了策略交易所需要的Python API接口
可以和MiniQMT客户端交互
进行报单、撤单、查询资产、查询委托、查询成交、查询持仓
以及接���资金、委托、成交和持仓等变动的主推消息


下面
我们就分别介绍这两组API
-->
