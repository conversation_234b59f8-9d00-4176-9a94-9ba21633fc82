---
layout: two-cols
clicks: 5
title: 获取行情数据
---

# 获取行情数据
<hr>

::left::

<show at="0-3">


```python{all|6-7|9-12|13-15}
from xtquant.xtdata import *

stocks = ['000001.SZ', '600000.SH']
end = "20050105"

download_history_data(stocks[0], '1d')
download_history_data(stocks[1], '1d')

bars = get_market_data(stock_list=stocks, 
                        period='1d', 
                        end_time=end, 
                        count=3)
print(bars.keys())
display(bars['time'])
print(type(bars['open']))
```
</show>

<show at="4">

```python{all}{maxHeight:'400px'}
def on_progress(data):
    '''补充历史数据回调函数'''
    print(data) 
    

stock_list = ['603909.SH','300450.SZ','600740.SH']
field_list = ['time','open','close','low','high','volume']

download_history_data2(stock_list, 
                       period='1d', 
                       start_time='20230201', 
                       end_time='20230223', 
                       callback=on_progress
                    )

print('download_history_data2 finished')

# 获取股票close数据
ret = get_market_data(field_list, 
                      stock_list, 
                      period='1d', 
                      start_time='', 
                      end_time='', 
                      count=5, 
                      dividend_type='front', 
                      fill_data=True
                      )

print(ret['close'].T)
```
</show>

<show at="5">

```python

def on_data(datas):
    for stock_code in datas:
        	print(stock_code, datas[stock_code])

subscribe_quote(stock_code, 
                period='1d', 
                start_time='', 
                end_time='', 
                count=0, 
                callback=None)
```
</show>

::right::

<show at="0-3">

```
    ['time', 'open', 'high', 'low', 'close', 
    'volume', 'amount', 'settelementPrice', 
    'openInterest', 'preClose', 'suspendFlag']
```

![](https://images.jieyu.ai/images/2023/09/lesson25-xtquant-dhd.png)

![](https://images.jieyu.ai/images/2023/10/lesson24-qmt-tick-level.png)

</show>

<show at="4">

```
{'finished': 1, ..., 'message': '603909.SH'}
{'finished': 2, ..., 'message': '600740.SH'}
{'finished': 3, ..., 'message': '300450.SZ'}

download_history_data2 finished

          603909.SH  300450.SZ  600740.SH
20230217   9.946154     44.543       5.42
20230220   9.976923     45.023       5.53
20230221  10.100000     44.863       5.64
20230222   9.984615     44.313       5.70
20230223   9.976923     43.913       5.69
```
</show>

<!--
前面介绍过
在xtquant中
获取数据是分两阶段的
首先
我们下载历史数据
使用download_接口
然后通过get_方法来读取本地已缓存的数据
这对行情数据的获取也是一样
不过 行情数据中还存在实时行情数据
这部分数据需要用subscribe来订阅
然后通过callback来接收


我们先看如何下载历史数据并读取


# SEC1
我们使用download_history_data来为单只股票下载历史行情
我们没有指定起止时间
所以xtquant会把全部的数据都给了我们
第一个参数是证券代码
只接收str作为参数
不能传入List[str]
第二个参数是行情的周期
合法的值有'tick' '1m' '5m'和'1d'


# SEC2
然后
我们通过get_market_data
来从本地缓存中读取数据
在qmt中，行情数据的起始点在哪里呢？
这里我们特别选取了20050105作为end_time
结果发现
当读取3个日线bar时
它取回了包括2004年的数据
因此
在xtquant中
数据的起始时间早于2005年
应该就是从1991年起了


get_market_data的第一个参数是field_list
即返回值应该包括哪些字段
第二个参数是stock_list
它是一个List[str]
即可以传入多个证券代码
它还有dividen_type
即除权类型，可使用的值有"none" "front" "back" "front_ratio" "back_ratio"
这里front是前复权
back是后复权
front_ratio是等比前复权
back_ratio是等比后复权
但是 它返回的数据并不包括复权因子
所以如果我们取不复权数据
你可能要考虑该如何使用它
另外 如果我们在三方框架中
如果在next方法
或者omicron.strategy.BaseStrategy中的predict方法中
获取到当前bar为止的前复权
实际上就相当于动态前复权

此外，这个函数还有周期、截止日期等参数


# SEC3
它的返回值类型尽管都是dict
但值类型则因数据周期而异
如果周期是tick级
则key是输入的证券代码
值则是一个numpy数组
除了高开低收之外
还有买卖5档的数据
如果周期是分钟或者日线
则key是输入的field_list的字段
而值类型是一个dataframe

这里的代码获取的是日线
所以得到的bars是以time open, close等为key
以证券代码为索引
以时间为列的一个dataframe

当没有提供field_list时
xtdata会返回一个默认的字段
这里第13行就显示了默认的字段名
第14行中显示了bar['time']的值
正如我们所说
这个图也显示了
它是一个dataframe


# SEC4
如果我们要下载多支股票的历史行情数据
使用download_history_data就没有那么方便了
此时我们应该使用download_history_data2
它允许同时下载多支股票的历史行情
��与download_history_data的不同之处有二
其一
第一个参数是List[str]
而不是单支股票代码
其二
我们需要传入一个callback函数
这段代码演示了它的用法
这里的callback我们只用来显示进度
这边的finish这些信息
就是它在下载过程中输出的进度
我们看到它是以个股为单位进行请求的

最后，我们再看下第29行及其输出
这里我们对dataframe的列进行了转置处理
这样得到的dataframe如右图所示
这样在使用上会方便一些
这是一个小的技巧

其它部分跟前一段示例是一样的


# SEC5

如果我们要在盘中实时获取最新的数据
此时使用download_history_data就显得不够高效
而应该改用subscribe_quote来订阅更新
如何使用订阅数据呢？
一是subscribe_quote方法有一个callback参数
我们可以在这个callback中对订阅数据及时进行处理
比如我们有一个冲高回落的策略
要求在个股冲高到7%以后
如果回落超过1.5%
则立即抛出
这种场景就是使用订阅数据加callback的好地方
这会比我们周期轮询要快很多
这个冲高回落的小策略在我们的实践中也是非常有效的
大家可以关注下

另外就是如果我们的策略是15分钟或者30分钟级的
我们可以周期性地调next方法
在next方法中
仍然通过get_market_data来取数据
来判断是否有交易信号
-->
