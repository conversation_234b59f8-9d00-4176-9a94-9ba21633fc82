---
layout: section
title: /02 QMT
---

# /02 QMT
<hr>

<!--
QMT是北京睿智融科研发的
集行情显示
投资研究
产品交易于一身
并自备完整风控系统的综合性平台
它既有传统的行情研究系统和交易系统
也有投研量化平台
可以进行策略编写和回测
还能进行程序化交易

QMT的特色我觉得有这样几点
第一个是它的风控系统
我们在做量化的时候
自己做风控还是比较难的
我们在书本上学到那些风控
可能都不是我们在现实生活中
真正会遇到的那些风险
我们在现实生活中遇到的那些风险
往往更重要更极端
就像今年（2023年）
我前面课程当中跟大家讲过的
有人因为数据持续发过来是错的
频繁的触发了 ask and kill 的这样一个交易指令
最终导致他因为交易手续费亏了400多万
所以这种风控是至关重要的
但不是每一个中小私募
或者说个人量化交易
大家都有时间、有精力也有能力
来实现风控场景的
有些错误只有在大量的实践中才会遇到
后面我们还会看QMT的风控设置
到时候可能就更清楚了
所以如果有平台帮我们做这件事
这是非常好的一个事情

第二个特点就是
它通过SDK把自己的行情数据和交易接口暴露出来
因此可以跟第三方本地化量化框架集成使用
我们多次也讲过
如果要长期做量化
有一个本地部署的第三方量化框架
特别是开源的框架是很重要的
一方面它们的技术上更新一些
毕竟只有这样
才会有竞争力
第二是
这样就不会把我们锁定在
某个券商
或者某个数据源上
一旦技术框架被锁定
可能就会有人利用垄断优势
获取不合理的高利润

在量化支持方面
它支持vba这种脚本语言
以对接传统的通达信公式
也支持python这种当前量化策略编写的主力语言
在程序化交易方面
它支持文件扫单模式和API模式
可以说是目前对实盘接入支持得最好的一种模式


QMT的玩法主要分两种
一种模式称为极速策略交易系统
它提供了行情、交易、策略编辑和回测等界面
在QMT内部编写策略
执行回测和执行交易
另一种是精简模式
即QMT-mini
在这种模式下
只保留了策略运行和交易界面
允许量化策略使用第三方框架和工具来编写、回测
但qmt-mini通过一个python sdk
即xtquant来提供数据和交易支持
-->
