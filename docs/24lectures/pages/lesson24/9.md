---
layout: default
title: 复权机制
---

# 复权机制
<hr>

```python{all}{maxHeight:'400px'}
def initialize(context):
    # 初始化策略
    g.security = "000541.SZ"
    set_universe(g.security)

def handle_data(context, data):
    log.info("前复权不包括当前价")
    bars = get_history(3, '1d', 'close', g.security, fq='pre', include=False)
    log.info(bars)

    log.info("前复权包括当前价")
    bars = get_history(4, '1d', 'close', g.security, fq='pre', include=True)
    log.info(bars)

    log.info("动态前复权不包括当前价")
    bars = get_history(3, '1d', 'close', g.security, fq='dypre', include=False)
    log.info(bars)

    log.info("动态前复权包括当前价")
    bars = get_history(4, '1d', 'close', g.security, fq='dypre', include=True)
    log.info(bars)   

    log.info("不复权且不包括当前价")
    bars = get_history(3, '1d', 'close', g.security, fq=None, include=False)
    log.info(bars)   

    log.info("不复权且包括当前价")
    bars = get_history(4, '1d', 'close', g.security, fq=None, include=True)
    log.info(bars)
```

<Loc at="0" fc="white">

![](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-dyna-adjust.png)
</Loc>

<!--
在Ptrade中复权机制比较复杂
它有前复权、动态前复权、不复权
和后复权这样好几种处理方式

复权按以下规则进行：

* 前复权是按照回测起始区间的结束日向前进行的修正
* 动态前复权是按照回测当日往前做的前复权
* data取的数据都是不复权数据
* 回测框架撮合成交时使用的是不复权数据

关于各种复权方式的问题
我们之前有过详细地介绍了
如果框架支持动态前复权
就应该只用动态前复权
对于新上手的人
Ptrade的复权机制可能有一些我们不太熟悉的地方
这里我们也提示一下

这段代码演示的是
前复权、动态前复权和不复权这三种复权处理方式
加上是否包括当前价这两种状态
一共6种状态的情况
在有动态前复权的情况下
实际上后复权和前复权就完全不用看了

我们关注的是动态前复权的两种情况
一种是 include = True
一种是 include = False

我们看右边的图片
这里打印出来了两种情况下的动态前复权价格
在handle_data中
当前的时间都是2018年5月11日
这一天发生了复权
在不包括当前价的输出中
输出只截止到5月10日
实际上如果我们把5/11这一天的收盘价打印出来
它会是7.23
与前一日的8.36相去甚远

所以实际上它的动态前复权是这样算的
先看当前要取的数据截止到哪一天起
get_history指定的截止时间是5月11日
我们说这里没有指定日期
实际上相当于是指定截止到回测当前的bar
即5月11日
但在include = False的情况下
数据只取到5月10日
这样就从5月10日起往前算前复权

在include = True的情况下
数据取到5月11日
这样就从5月11日起向前复权
这天刚好又发生了复权
所以数据大幅向前进行了修正

所以理解它的动态前复权的关键
是看数据最终截取到哪一天为止
而不是回测周期的当前bar的时间

这里的问题是
data给的始终是现价
那么在include = False
当天又发生复权的情况下
这样两个数据
即历史数据和今天的数据实际上是拼不起来的
我们再看一眼这里的输出
我们没有打印data
但实际上data中的price价格当前为7.23
这与我们第一种情况是对不起来的
所以，如果我们用include = False去取数据
计算信号
这会跟实际情况发生一点偏差
如果我们要按5月10日的价格
加上某个涨跌幅来挂限价单
那么一定会出错
-->
