---
layout: default
---
# 回测 DEMO - 双均线策略
<hr>

```python {all|1-10|12-25|28-39}{maxHeight:'400px'}
def initialize(context):
    set_benchmark('000001.SS')
    
    set_commission(commission_ratio=0.0003, min_commission = 5.0)
    set_fixed_slippage(fixedslippage=0.2)
    set_volume_ratio(volume_ratio=0.2)
    
    g.security = '000001.SZ'
    set_universe(g.security)

#当五日均线高于十日均线时买入，当五日均线低于十日均线时卖出
def handle_data(context, data):
    security = g.security

    # 得到十日历史价格
    df = get_history(10, '1d', 'close', security, fq='pre', include=False)
    
    # 得到五日均线价格
    ma5 = round(df['close'][-5:].mean(), 3)
    
    # 得到十日均线价格
    ma10 = round(df['close'][-10:].mean(), 3)

    # 得到当前资金余额
    cash = context.portfolio.cash
    
    # 如果当前有余额，并且五日均线大于十日均线
    if ma5 > ma10:
        # 用所有 CASH 买入股票
        order_value(security, cash)
        # 记录这次买入
        log.info("Buying %s" % (security))
        
    # 如果五日均线小于十日均线，并且目前有头寸
    elif ma5 < ma10 and get_position(security).amount > 0:
        # 全部卖出
        order_target(security, 0)
        # 记录这次卖出
        log.info("Selling %s" % (security))
```

<Loc at="0" fc="white" w="50%" top="11vh">

![75%](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-backtest-report.png)
</Loc>


<!--
前面那段代码和日志
仅仅是演示了基本框架
和回测的生命期
没有任何策略
那么我们现在来看一个策略
这个策略是一个双均线策略
这个策略当中最重要的方法
在前面都已经讲过了
但是这次会完成一个真正的策略
就包括如何获取数据
查询资金余额
下单等等
把这些运作组合起来

# SEC1 INITIALIZE

initialize 跟之前一样

# SEC2 HANDLE_DATA
我们通过 get_history 方法来获取行情数据
得到 10 天的日线收盘数据

然后计算 10 日移动平均和 5 日移动平均
这里用了 python round 方法
来进行四舍五入
这个方法本身是有错误的
之前讲过
我们还是应该用 omicron 中的 math_round
使用 python round
有可能导致该发出信号的时候
发不出信号
不该发出信号的时候
又发生了信号
当然这里的评判标准
是以大多数人用的行情软件为准

然后我们得到当前的资金余额
我们刚才讲了
context 有 portofolio 对象
这一行代码我们更进一步
通过portofolio对象来获取了资金余额

# SEC3 交易
这一行是按金额下买入单

这一行是卖出
用的是 order_target 函数
当 order_target 函数的参数中的amount为0时
即经过调仓后余额为0
实际上就是平仓

右边是一个回测报告
跟backtrader差不多
但个人觉得会比backtrader好看一些


-->
