---
layout: toc
image: https://images.jieyu.ai/images/2023/10/lesson24-outline.png
---
#
<hr>

<!--
今天我们将介绍恒生电子的Ptrade和迅投的QMT
这里的qmt-mini也是qmt的一部分
它的功能没有qmt那么全
但个人觉得它能更好地与我们自己的量化框架适配
所以也要重点介绍下

在ptrade方面
我们主要是做一个框架概览
再通过一个示例介绍它的基本操作
最后提醒大家对它的复权机制
在开发量化策略之前
可能要深入了解下
ptrade有官方的教学视频
对如何使用ptrader进行策略开发
有比较深入的介绍
大家如果需要也可以在课后找我们领取链接

在qmt的部分
我们会介绍如何申请和安装
它的功能概览
在这一部分中
我们会将重点放在模型研究

之后我们就转向qmt-mini
它由两部分组成
第一，它是一个只有部分功能的QMT
实际上程序还是一个
只不过启动时选项不同
其次，在这种模式下启动时
它像一个代理一样工作
可以通过xtdata\xttrader
这样两个python库来与之交互
获取行情数据
以及发送交易指令
xtdata与xtrader也是迅投开发的
-->
