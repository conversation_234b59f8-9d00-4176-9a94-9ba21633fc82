---
layout: two-cols
title: 线束语
clicks: 3
---

# 封装XtTrade
<hr>

<show at="0">

![33%](https://images.jieyu.ai/images/2023/09/lesson25-xttrade-lifecycle.png)

</show>
::left::

<show at="1">

## 启动和关闭钩子

</show>
<show at="2">

## 启动和关闭钩子
## 交易相关操作

</show>

<show at="3">

## 启动和关闭钩子
## 交易相关操作
## 启动sanic

</show>

::right::


<show at="1">

```python{all}{maxHeight:'400px'}
from xtquant import xtdata, xttrader, xtconstant
from xtquant.xttrader import XtQuantTrader
from sanic import Sanic, Blueprint, response
 
api = Blueprint('xt', url_prefix='/xt/trade')

trader = None

@api.listener('before_server_start')
async def before_server_start(app, loop):
    """初始化xtrader clients"""
    global trader

    # read config, get qmt_dir
    qmt_dir = "d:\\qmt"
    session_id = random.randint(20000, 60000)
    trader = XtQuantTrader(qmt_dir, session_id)

    # 启动交易线程
    trader.start()
    # 建立交易连接，返回0表示连接成功
    trader.connect()
 
@api.listener('after_server_stop')
async def after_server_stop(app, loop):
    '''关闭session'''
    trader.stop()
```
</show>

<show at="2">

```python{all} {maxHeight:'400px'}
@api.route('/query/assets', methods=['POST'])
async def query_assets(request):
    '''
    查询总资产
    '''
    global trader
    asset = trader.query_stock_asset(trader.account)
    return response.json({"总资产": asset.total_asset, 
                         "现金": asset.cash, 
                         "持仓市值": asset.market_value, 
                         "冻结金额": asset.frozen_cash
                         })

@api.route('/place_order', methods=['POST'])
async def trade_place_order(request):
    '''
    下单
    '''
    global trader
    stock_code = request.args.get('stock_code')
    volume = int(request.args.get('volume'))
    price = float(request.args.get('price'))

    if stock_code is None or volumn is None or price is None
        # error handling
        return

    if request.args.get('direction', 'buy') == 'buy':
        direction = xtconstant.STOCK_BUY 
    else:
        direction = xtconstant.STOCK_SELL

    order_id = trader.order_stock(trader.account, 
                                  stock_code, 
                                  direction, 
                                  volume, 
                                  xtconstant.FIX_PRICE,
                                   price, 
                                   'strategy_name', 
                                   'remark')

    return response.json({'order_id': order_id})
```
</show>

<show at="3">

```python
if __name__ == '__main__':
    app = Sanic(name='xtquant')
    app.config.RESPONSE_TIMEOUT = 600000
    app.config.REQUEST_TIMEOUT = 600000
    app.config.KEEP_ALIVE_TIMEOUT = 600

    # add route
    app.blueprint(api)
    app.run(host='0.0.0.0',
            port=7878, 
            workers=1, 
            auto_reload=True)
```
</show>

<!--

这是前面我们介绍过的生命期图
当时有一个地方没有讲
就是它启动了一个run_forever
它的作用是维护主线程循环
以保持进程不退出
这样订阅的消息才能收到
这个主线程循环是必须的

但是一旦启动run_forever
当前线程就会阻塞住
这个线程也无法用来进行交易
所以通常情况下
我们最简单的用法是
策略框架本身应该是后台程序
在它运行期间
xttrader的实例化对象是一个全局变量
它又通过register/subscribe生成了一个新的线程
但我们不调用run_forever方法
维持进程的生命期是策略的主线程自己应该做的事
因此在需要交易的时候
我们在策略的主线程中
具体地说
可能是在生成交易信号的地方
比如backtrader中的next方法中
我们调用xtrader的order函数来执行交易

当然这样做的话
我们要考虑回测怎么办
所以可能最佳的方案是类似于trader-client那样
一套API，既用于实盘
也用于回测

那么我们首先需要把xtTrader封装成一个web服务
这种封装还有额外的好处
就是使得我们的策略
可以运行在其它机器上
其它操作系统的机器上

我们以sanic为例
介绍一下这个封装大致上怎么做
这里需要大家有一点sanic的知识
后面我们也会把完整的代码
放到课件服务器中
并且通过课程服务群
把消息发给大家

# SEC1
这是利用sanic的钩子
实现trader的实例化、启动和退出
这里我们没有做错误处理
实际上，如果启动和连接不成功
要么进行重试
要么就需要退出

# SEC2
这里增加交易接口
我们可以仿此例增加更多交易接口

# SEC3
这里我们启动了sanic服务器
接下来就可以通过http client
发出交易请求了

我们可以在这个例子基础上进一步修改
增加消息回调的处理


今天的主要内容就是这么多


由于这个模块的主要内容
是讲解如何通过PTrade和QMT来实现实盘接入
所以我们对这两个软件的其它功能
比如量化投研平台介绍并不多

在QMT中
我们主要讲解了XtQuant这个库如何使用
要注意的是
我们通过XtQuant获取的数据
往往不是PIT数据
所以
在回测中使用它需要小心
XtTrader模块
尽管文档没有说明
但应该只能用于仿真交易和实盘
在回测中调用这些函数是没有意义的
因此
如果我们要在QMT之外编写策略
还得借助第三方框架
只将QMT当成实盘接口来用更好
-->
