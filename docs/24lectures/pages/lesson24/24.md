---
layout: statement
---
# 结束语
<hr>

![50%](https://images.jieyu.ai/images/2023/10/cheese-course-roadmap.png)
<!--
-->
<!--
ImportError: DLL load failed while importing IPythonApiClient: 找不到指定的模块。

![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-mini-dll.png)

D:/国金QMT交易端模拟/bin.x64

![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-download-python-1.png)

![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-mini-download-python.png)

D:\国金QMT交易端模拟\bin.x64\Lib\site-packages

也可以这样注册：

sys.path.append(r'c:\gszqqmt\bin.x64\Lib\site-packages')

或者直接copy到lib/site-packages下。每次更新都要copy，不方便

D:\国金QMT交易端模拟\userdata_mini

安装第三方库
pip install openpyxl -t E:\QMT交易端20962\bin.x64\Lib\site-packages

安装需要找一个大一点的分区，以便后面下载行情数据

封装交易接口

https://mp.weixin.qq.com/s?__biz=Mzg3ODcyNzc5MA==&mid=2247483711&idx=1&sn=bdf4df36f4ec73e2017263e3c88312ff&chksm=cf0e063ef8798f282a5b954d63ea870eb5112f4121531a405df040651b4f056928d99ccc47d7&cur_album_id=2459911668907491328&scene=189#wechat_redirect


•xtdata：xtdata提供和MiniQmt的交互接口，本质是和MiniQmt建立连接，由MiniQmt处理行情数据请求，再把结果回传返回到python层。需要注意的是这个模块的使用目前并不需要登录，因此只要安装了QMT,就可以无门槛的使用其提供的数据服务。


•xttrader：xttrader是基于迅投MiniQMT衍生出来的一套完善的Python策略运行框架，对外以Python库的形式提供策略交易所需要的交易相关的API接口。该接口需开通A股实盘版权限方可登录使用。

批量下载数据：

https://blog.csdn.net/popboy29/article/details/129192189

文件单

![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-file-order.png)

FAQ

在QMT内编写策略时，如何安装第三方库？
QMT内置的Python是3.6.8，这个版本是Python 3以来最先稳定的第一个版本。如果我们要安装第三方库，要注意这样两点。一是我们要在Python 3.6.8环境下，运行pip命令。只有这样，pip才会自动选择正确的第三方库版本。为此，我们可以先用conda安装一个python 3.6.8的虚拟环境，切换到这个环境下时，再进行安装。

二是安装位置必须在QMT安装目录下的bin.x64\Lib\site-packages。我们需要使用命令：

```
pip install openpyxl -t E:\QMT交易端20962\bin.x64\Lib\site-packages
```
来进行安装。
-->

<!--
今天的课就上到这里
本课程也到此结束了
感谢大家一路陪伴
课程虽然结束了
但我们的服务还没有结束
对购买了答疑服务的同学
我们将继续在群里或者腾讯会议中进行答疑
我们将持续修订课件
增加策略示例
这些您都可以在
原来获得课件的地方得到
对没有购买答疑服务的同学
也可以在我们的量化社区群里
向老师进行提问

接下来
您可以沿着这个路线图最上方的部分
继续深造
我们也有不同阶段的课程提供
并且对老学员有感恩优惠
详情请联系 quantfans_99 （宽粉）
-->
