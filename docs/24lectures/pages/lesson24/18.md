---
layout: two-cols
clicks: 1
---
# 获取交易日历

<hr>

<show at="0">

```python
import numpy as np

days = get_trading_dates('SH', start_time='', end_time='', count=10)
np.array(days, dtype='datetime64[ms]').astype(datetime.date)
```

```
    -- output --
    array([datetime.datetime(2023, 8, 31, 16, 0),
        datetime.datetime(2023, 9, 3, 16, 0),
        datetime.datetime(2023, 9, 4, 16, 0),
        datetime.datetime(2023, 9, 5, 16, 0),
        datetime.datetime(2023, 9, 6, 16, 0),
        datetime.datetime(2023, 9, 7, 16, 0),
        datetime.datetime(2023, 9, 10, 16, 0),
        datetime.datetime(2023, 9, 11, 16, 0),
        datetime.datetime(2023, 9, 12, 16, 0),
        datetime.datetime(2023, 9, 13, 16, 0)], dtype=object)
```
</show>

<show at="1">

```python
import time
def conv_time(ct):
    '''
    conv_time(1476374400000) --> '20161014000000.000'
    '''
    local_time = time.localtime(ct / 1000)
    data_head = time.strftime('%Y%m%d%H%M%S', local_time)
    data_secs = (ct - int(ct)) * 1000
    time_stamp = '%s.%03d' % (data_head, data_secs)
    return time_stamp

conv_time(1693152000000)
```

```
    --- output ---
    20230828000000.000
```
</show>


<!--
我们通过get_trading_dates来获取交易日历
它的参数依次是
市场代码 
比如获取上交所日历就要使用SH
当然两个市场的日历是一样的
所以我们传入哪一个都可以
start_time 开始时间
它是一个8位字符串
我们可以传入20200101
这样是ok的
但如果传入一个标准的日期格式串
比如2022-01-01反倒会报错
这里要注意一下

end_time 是结束时间
也是字符串
count 返回记录数
-1为全部返回


这个函数的返回值是什么呢
它的返回值是List[Int64]
是毫秒为单位的unix epoch时间
unix epoch时间是指以1970年1月1日为纪元
当前时间减去纪元得到的毫秒数

这个时间没办法直接使用
在多数情况下
这里的时间转换是必须的
这里我们可以像示例代码一样
进行转换
我们注意到输出数组是object类型
即每一个元素都已经是python的datetime.date对象了
这里对numpy有依赖
不过在量化环境下
这应该不是问题
我们不可能不用numpy 对吧?
官方也给出了转换代码

# SEC1
这段代码是官方给出的参考
不过多数情况下
可能使用我们给出的例子更简洁
也更高效
一次性就把整个数组转换过来了


-->
