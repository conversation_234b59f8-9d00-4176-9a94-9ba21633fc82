---
layout: two-cols
clicks: 4
title: XtData - 证券列表
---

# XtData - 证券列表
<hr>

::left::

<show at="0-3">

```python{all|13|15-25|26-28}{maxHeight:'420px'}
from collections import defaultdict

sectors = defaultdict(list)
prefixes = [
    '1000SW', '500SW', '300SW', 'CSRC', 
    '迅投一级', '迅投二级', '迅投三级', 
    'HKSW', 'TGN', 'TDY', 'THY', 'DY1', 
    'DY2', 'SW1', 'SW2', 'SW3', 'ETF',
    'GN', '转债', '国证', '上证', '沪深', 
    '中证','深证'
]

download_sector_data()

for item in get_sector_list():
    for i in range(6, 1, -1):
        key = item[:i]

        if key in prefixes:
            sectors[key].append(item)
            break
    else:
        sectors['NA'].append(item)

print(sectors['GN'][:10])
all_stocks = get_stock_list_in_sector('沪深A股')
print(f"当前共计{len(all_stocks)}股A股")
print(all_stocks[:5])
```
</show>

<show at="4">

```python
# 示例5
get_instrument_detail('000001.SZ')
```
</show>

::right::

<show at="0-2">

```
[
    'GN3D感应', 'GN3d打印', 'GN3D玻璃', 'GN4680电池', 
    'GN4D打印', 'GN5G', 'GN5G主设备', 'GN5G运营商', 
    'GN6G', 'GNAH溢价股'
]

```
</show>

<show at="3">

```
当前共计5059股A股

[
    '601882.SH', '603995.SH', '601128.SH', 
    '600740.SH', '603909.SH'
]
```
</show>

<show at="4">

* InstrumentName: 该证券的显示名，如平安银行
* OpenDate: 该证券的IPO日
* ExpireDate: 该证券的退市日，如果未退市，则为99999999
* UpStopPrice: 当日涨停价
* DownStopPrice: 当日跌停价
</show>

<!--
我们在最早的课程中就讲过
数据源最基础的API就是
证券列表、市场日历和行情数据
我们先来看如何获取证券列表


xtquant中
把证券进行了板块分类
然后通过get_stock_list_in_sector
就可以获取板块的全部成分股

我们进行板块划分时
会有好几种体系
比如按地域划分
这样我们会得到大约30几个板块
按行业划分的话
这其中又会有证监会行业
申万行业等
每一个行业分类体系中
就会有几十到上百个板块
我们在使用板块数据时
需要区分不同的体系
比如我们同时使用地域板块
叠加某个行业板块来选股
这样选股是有意义的
但是如果用证监会行业分类的板块
再叠加申万行业分类的板块
来进行选股的话
似乎意义就不大
所以我们应该首先区分分类体系

但是qmt中并没有特别关注这个区分

我在这里进行了一些整理
大致可以认为
同一个体系的分类
会有相同的前缀
比如
SW代表申万行业分类
CSRC为证监会行业分类
除此之外
我们猜测
GN代表概念板块
DY代表地域板块
TGN可能代表同花顺概念板块
但没有文档明确说明
这些板块的编制者是谁
如何编制
数据发布的时间等等


# SEC1 先下载

在使用上我们要注意
首先
我们要通过 download_sector_data
将板块信息数据缓存到本地
对几乎所有数据
我们都是先调用 download_*
再调用 get_*方法


# SEC2 归类
我们通过这段代码
提取QMT中所有板块名字信息
再通过前缀进行一个归类
这样我们就可以得到每个归类中有哪些板块

这里通过第25行
我们打印出了概念板块中的前10个概念

# SEC3
有了板块的名字之后
我们就可以通过get_stock_list_in_sector
来获取该板块所有的成份股
在QMT中板块没有索引编号
所以这个函数接受一个字符串参数
它是板块的名字
这里我们得到全A市场共有5000多支股票
并且列出了其中的一些


我们注意到
无论是get_sector_list还是get_stock_list_in_sector
都无法传入时间
这意味着QMT中的这些数据
不是PIT数据
因此作为辅助数据比较好
如果用来进行回测
就会导致使用了未来数据


# SEC4

在获取到证券列表后
我们可以通过 get_instrument_detail
来得到某支证券的基本信息


输出很长
这里只选了重要的几项
注意这里有涨跌停价
get_instrument_detail只能获取到当前的数据
而无法获取历史PIT数据
因此
我们在回测中
也不能依赖它返回的信息
-->
