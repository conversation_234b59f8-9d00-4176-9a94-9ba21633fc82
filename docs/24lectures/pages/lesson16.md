---
theme: ../theme
lineNumbers: true
aspectRatio: 16/9
transition: slide-left
layout: cover
title: '第16课 Matplotlib'
drawings:
    presenterOnly: true
---

<!--
大家好！
今天是我们大富翁量化课程的第16课
我们来讲量化中常用的图形绘制库-matplotlib
-->

---
layout: toc
image: https://images.jieyu.ai/images/2023/07/lesson16-outline.png
---



<!--
今天的课程主要由以下几个部分组成
首先，我们对matplotlib的历史进行简短的介绍
然后以matplotlib为例
讲解图的构成
在这一部分里
我们首先会接触到
matplotlib中最顶层的几个概念 <mark>
分别是pyplot, Figure, Axes和Artist 
然后介绍它们之间的相互包含和转化关系
接着我们介绍图的布局
最后以一个图的解剖图为例
把这些概念串起来

在第二部分
我们会接触到一些更具体而微
但又是非常常用的基础部件
即Axis（轴）
样式表和colormap
以及文本和中文的显示问题

今天的课程中
我们侧重于讲概念
讲图的领域知识
一旦我们掌握了这些底层的逻辑
再来学习任何绘图框架
以及具体的应用案例
都会更加容易
并且有能力举一反三
-->

---
layout: section
---
## /01 概览

---
layout: statement
class: fade
---

# Matplotlib简介

<Loc alpha="0.9"
img='https://images.jieyu.ai/images/2023/07/matplotlib_jd_hunter.jpg'>
</Loc>

<br>
<br>
<br>

<v-clicks>

## Matplotlib
## Pylab
## Pyplot
</v-clicks>

<!--
matplotlib 的创始人是 John Hunter
他是一名神经生物学家
他最初于 2003 年左右开始开发 matplotlib
2012 年不幸去世后
现在 matplotlib 由一个社区来进行持续开发和维护


这里我们先澄清几个概念

matplotlib是整个matplotlib的库

pylab 是一个便利贴
它导入了 matplotlib.pyplot 中的大多数对象
以及 numpy 中的许多对象，
我们可以通过
`from pylab import *`来使用它的许多功能 <type>

但这种方法容易引起名字冲突
也不便于版本管理
现在 matplotlib 已经不再推荐使用了
因此
从现在起
当你再遇到有的教程或者书籍中，
有pylab 相关的介绍
可以完全不用看了

pyplot 是 matplotlib 中的一个模块
它是一个“有状态的接口”
我们可以简单地认为
pyplot 是一个高级接口
它自动管理了绘图所需要的状态和对象
所以我们可以调用它的方法
跳过一些繁琐的对象生成
直接进入到绘图概念
这种方式相当于设计模式中的facade模式

它为复杂系统中的最常用部分
提供了简化的接口
增强了易用性
平滑了学习曲线

我们在其他一些绘图系统中
也能看到类似的做法
比如在plotly中
它既提供了功能强大的为
复杂系统中的最常用部分提供简单
接口增强了应用性
平滑了学习曲线
我们在其他一些绘图系统中
也能看到类似的做法
比如在plotting中
它既提供了功能强大的Graph Objects
又提供了简化的plotting express包
来简化一些常见的绘图操作
-->

---
layout: statement
clicks: 2
---

# 图的构成


<div v-if="$slidev.nav.clicks===0">
<h3> 1. Figure</h3>
<h3 style="opacity: 0.3"> 2. Axes</h3>
<h3 style="opacity: 0.3"> 3. Artist</h3>

<h3 style="position: absolute; left: 35%; top: 30%; width: 45%">
Figure是最顶层的对象
</h3>
</div>

<div v-if="$slidev.nav.clicks===1">
<h3 style="opacity: 0.3"> 1. Figure</h3>
<h3> 2. Axes</h3>
<h3 style="opacity: 0.3"> 3. Artist</h3>
<h3 style="position: absolute; left: 35%; top: 30%; width: 45%">
Axes是一个绘图
<br>
Axes 不是 Axis 的复数
</h3>
</div>

<div v-if="$slidev.nav.clicks===2">
<h3 style="opacity: 0.3"> 1. Figure</h3>
<h3 style="opacity: 0.3"> 2. Axes</h3>
<h3> 3. Artist</h3>
<h3 style="position: absolute; left: 35%; top: 30%; width: 45%">
任何可见的部分，都是Artist
</h3>
</div>

<!--
我们这一节课先从一些基本的概念开始

绘图涉及到一系列的领域知识
在不同的绘图框架中
都存在着相同的概念
这些概念很可能都有相同的名字
掌握这些知识
对我们迅速掌握一种新的绘图工具非常有帮助
因此，在这一节里
我们就以matplotlib为例
深入讨论这些概念以及它们之间的关系
为我们学习绘图打下良好的基础

在matplotlib中
存在这样一些概念
pyplot，Figure,Axes, Artist
它们被认为是最顶层的概念。

其中
Figure是最顶层的绘图对象
它是 matplotlib 图形的最外层容器
它可以包含0到多个 Axes 对象、
艺术家（artist）对象

有时候我们提到Figure时
将它等同于canvas（画布）
这并没有错
但实际上
 Figure维护的是一个虚拟的画布
此外
还存在一个真正的画布
即最终图形被绘制的物理表面
比如打印机
比如显示器待
在matplotlib中
有时也被称为backend。

<next>

在matplotlib中
真正的”绘图”（准确地说，是关于图形的定义）
并不是发生成Figure这个层面
而是发生在Axes这个层面
一个Figure对象可以有零到多个Axes对象
但要产生实际绘图
则必须至少拥有一个Axes对象。

Axes 的名字相当引入困惑
它实际上指的是一个绘图（plot），
而不是我们常常认为的 Axis（轴）的复数

不过
Axes 中确实包含了若干个 Axis
在二维图中
包含了X axis, Y axis
在三维图中
还存在一个Z axis

<next>
在Figure当中
任何可见的部分都可以认为是一个Artist对象
是来定义具体图形的对象
比如文本，线条
它也可以是这些artist的集合
Artist是一个庞大的家族
派生出许多子类
多数Artist对象是绑定在Axes对象上的
我们不能在多个Axes中共享它
或者将其从一个Axes移动到另一个Axes上
当Figure被渲染（即真正被画到canvas上时）
实际上是这些Artist被画到canvas
-->

---
layout: two-cols
---

# 图的构成
<hr>

## 一个最简单的图

::left::

```python {all|4|5|8,12|9|14|all}
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D

fig = plt.figure()
fig.show()

# 增加一个绘图，该子图拥有一条直线对象
axes = fig.add_axes([0.25, 0.25, 0.75, 0.75])
axes.add_line(Line2D(np.arange(10),np.arange(10)))

# 增加另一个绘图
fig.add_axes([0, 0, 0.5, 0.5])

fig.show()
```

::right::

<show at="2">

<Loc top="30%" left="15%">

```
<Figure size 640 * 480 with 0 Axes>
```
</Loc>
</show>
<!-- right -->
<show at="5|6">

![100%](https://images.jieyu.ai/images/2023/07/lesson16-screenshot-1.png?1)

</show>

<!--
下面我们来创建一个最简单的图

<next>
我们通过第4行代码创建了一个空的 Figure
它不包含任何 Axes
因此当我们运行第五行代码时

<next>

它只会显示右图这样一句文本
而不会有任何绘图
要进行绘图
我们必须首先给它添加一个Axes对象
<next>

我们通过第8行和第12行
给fig增加了两个Axes对象

<next>
然后通过第9行
在其中的一个Axes上
增加了一条直线。
现在将显示这样的图形 
<next>

两个绘图大小和位置都不一样
这是由我们增加子图时
给add_axes传入的参数决定的 <guide>
-->

---
layout: two-cols
clicks: 4
---

# 图的构成
<hr>

## pyplot, Figure与Axes之间的关系

::left::

```python {1|3,4|6-11|13-19}
fig1, (ax1, ax2) = plt.subplots(2, 1)

print("fig1 is plt.gcf(), ax2 is plt.gca()", 
      fig1 is plt.gcf(), ax2 is plt.gca())

fig2, ax3 = plt.subplots()

print("fig1 is plt.gcf(), ax2 is plt.gca()", 
      fig1 is plt.gcf(), ax2 is plt.gca())
print("fig2 is plt.gcf(), ax3 is plt.gca()", 
      fig2 is plt.gcf(), ax3 is plt.gca())

# matplotlib/pyplot.py
def plot(*args, **kwargs):
    ax = plt.gca()
    return ax.plot(*args, **kwargs)

def gca(**kwargs):
    return plt.gcf().gca(**kwargs)
```
::right::
<div v-if="$slidev.nav.clicks===0">

![50%](https://images.jieyu.ai/images/2023/07/lesson16-screenshot-2.png)
</div>

<div v-if="$slidev.nav.clicks===1">

<br><br><br>
<p style="padding-left: 5vw">fig1 is plt.gcf(), ax2 is plt.gca() True True</p>
</div>

<div v-if="$slidev.nav.clicks===2">

<br><br><br>
<p style="padding-left: 5vw">fig1 is plt.gcf(), ax2 is plt.gca() False False</p>
<p style="padding-left: 5vw">fig2 is plt.gcf(), ax3 is plt.gca() True True</p>
</div>

<!--
我们刚刚创建了两个Axes对象
两个Axes对象之间的位置关系比较任意
这样我们就掌握了一种强大的布局模式
但我们更常用
其实是网格状的布局
对这种布局
上述方式固然能做
但在参数上还需要精心计算
另外，我们先生成Figure
再生成Axes这种方式
在步骤上也显得过于繁复
所以一般情况下
我们是通过这里第一行的代码
同时生成 Figure 和 Axes 对象

这里的subplots似乎引出了子图的概念
实际上
Axes对象就是这里的子图
除此之外
没有别的子图
所以
从现在起
我们就把Axes对象叫作子图
实际上
Axes包括坐标轴、图例、标题等对象
它也确实构成了完整意义上的图

这样
第一行的代码
就一次生成了一个Figure和两个子图  <mark>
这两个子图按两行一列的方式进行排列
与前一张slide的例子相比
它只能按照指定的网格
在固定位置上生成固定大小的子图
少了一些灵活性
不过，对绝大多数作图
简单、网格状的布局
正是我们想要的

现在我们来总结一下
我们已经接触到了pyplot
Figure和Axes三个对象
我们可能已经发现
或者在后面的学习中也会发现
这些对象往往有相似的方法
并且似乎都能用于绘图
那么，它们之间是一种什么样的关系呢？

我们前面讲过
Figure是最顶层的绘图对象
pyplot只是提供了一些便利性的封装
它本身不提供任何实际的功能
但保持了对全局当前活跃的Figure和Axes对象的跟踪
并且会将一些绘图指令分发到这两个对象上

下面
我们就通过代码来探索
这几个对象之间的相互包含和引用关系
当然
由于pyplot是全局对象
我们可以在代码的任意位置处使用它
所以
我们主要看
如何通过pyplot来获取
Figure对象和Axes对象

我们首先来看
为什么可以通过pyplot对象来绘图
实际上
通过pyplot绘图
是绘制在最后一个活动的Figure对象
及活动的子图对象上
我们可以通过plt.gcf和plt.gca分别获取这两个对象

CLK1

结果表明
我们用plt.gcf得到的
就是刚刚创建的Figure对象
而用plt.gca得到的
是最后一个创建的子图对象

我们再创建一次试试

CLK2

这一次
gcf指向了fig2
而不是fig1
gca指向了ax3
而不是ax2

这次完全验证了
plt.gcf跟踪的是最后一次活动的Figure
plt.gca跟踪的是最后一次活动的Axes
这正是我们说pyplot是有状态的接口的意思
它隐式地保存和指向了最后活动的Figure和Axes

CLK3

实际上
当我们调用plt中的一些方法时
比如这里的 plot
它实际上是先检索到
当前活动Figure对象的活动子图对象
再在这个子图对象上进行的操作

这里强调一下plt.gca和plt.gcf这两个方法
如果我们直接使用了plt.plot方法开始了一个绘图
如果我们在中间需要调整绘图的一些属性
而plt又没有提供这个封装
那么有时候会需要通过plt.gcf
或者plt.gca得到相应的对象
再进行操作
-->

---
layout: statement
clicks: 4
---

# 图的构成
<hr>

```python {all|5,6|9-21|23|28-30} {maxHeight: '400px'}
fig1 = plt.figure()
fig2 = plt.figure()

# now fig2 is gcf, but we can retrieve fig 1 by:
for i in plt.get_fignums():
    if plt.figure(i) is fig1:
        print("found fig1")

plt.figure(
    num=None,
    figsize=None,
    dpi=None,
    *,
    facecolor=None,
    edgecolor=None,
    frameon=True,
    FigureClass=<class 'matplotlib.figure.Figure'>,
    clear=False,
    **kwargs,
)
# Create a new figure, or activate an existing figure.

fig, ((ax1, ax2, ax3), (ax4, ax5, ax6)) = plt.subplots(nrows = 2, ncols=3)
# 下面的代码也可以
# fig, axes = plt.subplots(nrows = 2, ncols=3)
# ax1, ax2, ax3, ax4, ax5, ax6 = axes.flatten()

print(type(axes), axes.shape)
print(fig.axes)
print(fig.get_axes())
```

<!--
上一张slide我们看到了如何通过
plt对象来获取全局唯一活动的Figure和Axes

在这张slide中
我们来看
要如何通过plt对象来检索其它对象
以及如何通过Figure对象
来检索子图对象
pyplot将进程中所有的Figure对象保存在一个数组里
并且提供了plt.get_fignums()
和plt.figure()方法来检索所有的Figure对象

CLK1

注意这次我们给plt.figure传入了一个参数
以检索某个Figure对象
而不是创建新的Figure
这个行为在其文档中有说明
第一个参数num
如果我们不传入这个参数
那么plt将为我们创建一个新的Figure
否则，就尝试为我们检索在这个位置上的Figure

CLK2
从Figure获取Axes比较简单
为了演示
我们先来创建若干个子图

这段代码中
我们通过plt.subplots(2, 3)
创建了一个Figure和6个Axes对象
但这一次
我们展开plt.subplots创建的Axes数组的方法
略为复杂一些
因为plt.subplots将它组成了
一个二维的numpy数组
plt.suplots这样组织
是为了方便调用者将各个子图与网格顺序相对应起来
使得我们可以直接
使用 axes[i][j] 这样的方式
定位到某个Axes对象并进行绘制

现在
我们来看看如何从Figure中获取axes
方法是使用Figure.axes
或者Figure.get_axes()

最后，我们对plt, Figure和Axes进行一个小结

pyplot与Axes共有78个左右的同名方法
Figure与Axes之间也有76个左右的同名方法
但与pyplot之间只有20个左右的共同方法

Figure对象没有plot方法
因为Figure对象更多的只是一个容器
因此也没必要实现plot方法

不过
并不是所有的Figure和Axes方法都被封装进了pyplot
所以
很多时候
我们还需要直接操作Figure对象和Axes对象
以获得更强大的定制能力
-->

---
layout: two-cols
clicks: 4
---

# layout

::left::

```python {all|11,16|8|17,19|11,16}
from matplotlib.gridspec import GridSpec

def annotate_axes(fig):
    for i, ax in enumerate(fig.axes):
        ax.text(0.5, 0.5, "ax%d" % (i+1), va="center", ha="center")
        ax.tick_params(labelbottom=False, labelleft=False)

fig = plt.figure(facecolor='0.8')
fig.suptitle("Controlling spacing around and between subplots")

gs1 = GridSpec(3, 3, left=0.3, right=0.48, wspace=0.05)
ax1 = fig.add_subplot(gs1[:-1, :])
ax2 = fig.add_subplot(gs1[-1, :-1])
ax3 = fig.add_subplot(gs1[-1, -1])

gs2 = GridSpec(3, 3, left=0.55, right=0.98, hspace=0.05)
print("gs2(ax4) is:", gs2[:, :-1])

ax4 = fig.add_subplot(gs2[:, :-1])
ax5 = fig.add_subplot(gs2[:-1, -1])
ax6 = fig.add_subplot(gs2[-1, -1])

annotate_axes(fig)
plt.show()
```

::right::

![75%](https://images.jieyu.ai/images/2023/07/using_grid_spec.png?1)

<div v-if="$slidev.nav.clicks===3">
<p style="padding-left:4vw">gs2 is: GridSpec(3, 3)[0:3, 0:2]</p>
</div>

<!--
在前面的slide中
我们已经在一个Figure中
创建了多个子图
这些子图之间的相互位置关系如何
与及与Figure的一些全局对象的位置关系如何
这就是布局的概念

matplotlib中的layout
与大多数图形界面中的布局概念类似
比如通过盒模型（left, top, right, bottom)定位
有外边距(margin)，内边距（padding）等

布局在matplotlib中
主要由GridSpec类和LayoutEngine一起来实现

gridspec 是将图形划分为逻辑上的行和列
这些行和列中轴的相对宽度
由 width_ratios 和 height_ratios 设置
默认生成等宽等高的网格
LayoutEngine 实现网格
并且避免子图的一些装饰性元素
（比如labels, ticks等）相互重叠
在matplotlib中，
layout的主要取值有constrained, compressed, tight等
其中constrained是推荐类型。

我们通过例子来看具体如何使用GridSpec和 layout

CLK1

这段代码中
我们先通过GridSpec创建了两个3*3的网格
每个网格都是等宽等高
一左一右
然后通过add_subplot创建了6个子图
在创建时
将gridspec传入
由于传入gridspec时
使用了切片
因此生成了大小不一的子图

我们还指定了网格的位置
比如，left=0.3, right=0.48
它们的横向间距(wspace=0.05)
和纵向间距（hspace=0.05)等

GUIDE

我们还通过facecolor为绘图设置了背景色

CLK2

第17,19行，这里展示了切片的细节

GUIDE

CLK3

最后
我们再回顾下第11，16行
这里通过left,right来给两个gridspec对象的盒模型设置了不一样的宽度。在没有设置高度的情况下，最终两个gridspec占据了figure的全部高度
-->

---
layout: two-cols
clicks: 4
---

# layout

::left::

```python {all|1-5|17-19|21-23}
widths = [2, 3, 1]
heights = [1, 3, 2]
gs_kw = dict(width_ratios=widths, height_ratios=heights)
plt.subplots(ncols=3, nrows=3, constrained_layout=True,
        gridspec_kw=gs_kw)


def example_plot(ax, fontsize=12, hide_labels=False):
    ax.plot([1, 2])

    ax.locator_params(nbins=3)

    ax.set_xlabel('x-label', fontsize=fontsize)
    ax.set_ylabel('y-label', fontsize=fontsize)
    ax.set_title('Title', fontsize=fontsize)

fig, axs = plt.subplots(2, 2, layout=None)
for ax in axs.flat:
    example_plot(ax)
    
fig, axs = plt.subplots(2, 2, layout="tight")
for ax in axs.flat:
    example_plot(ax)
```

::right::

<div v-if="$slidev.nav.clicks === 1">

![75%](https://images.jieyu.ai/images/2023/07/lesson16-screenshot-3.png?1)
</div>

<div v-if="$slidev.nav.clicks === 2">

![75%](https://images.jieyu.ai/images/2023/07/lesson16-screenshot-4.png)
</div>

<div v-if="$slidev.nav.clicks === 3">

![75%](https://images.jieyu.ai/images/2023/07/lesson16-screenshot-5.png)
</div>

<!--
前一张slide中的创建布局的方法
是由若干个子网格通过合并
生成较复杂的网格布局
方法简单易懂
但语句较多
我们也可以用这段代码来实现布局
CLK1

我们这样解读这两组参数的作用
widths数组对应于列宽
heights数组对应于行高
我们先从height的第一个元素开始
它表明第一行应该占据Figure的1/6高
然后依次对widths数组展开
我们就得到
第一行第一列
占据1/3宽和1/6高
第一行第二列
占据1/2宽和1/6高
第一行第三列
占据1/6宽和1/6高
这里的width_ratios和height_ratios参数
都是相对数值
数值之间的比值才重要
绝对值并不重要

值得注意的是
这里的方法生成的网格
不如上一张slide中的灵活

我们对比下17和21两段代码
演示下layout参数的作用

CLK2
CLK3

可以看出
LayoutEngine的作用之一
就是为label, title, colorbar等装饰性元素安排绘图空间
在有多个子图时
避免这些对象重叠在一起
从两个图的对比可以看出
有没有设置layout
结果大不一样
-->

---
layout: two-cols
clicks: 2
class: fade
---

# Figure
<hr>

::left::

<show at="0">

### 功能 
1. 定义canvas（长宽、像素比，blending模式等）
2. 归集Axes和Artist对象，提供检索能力
3. 创建子图(subplot_*)
4. 管理子图布局（通过GridSpec和LayoutEngine)
5. 设置共同的属性，比如suptitle, supxlabel, subylabel
6. 显示图形窗口，保存图形到io设备上。
</show>

<show at="1">

### 属性

1. .artists, .axes, .canvas, .lines, .legends, 
2. .texts, .subfigures, .images, .patches, .dpi
3. .gca, .bbox

</show>

<show at="2">

### 方法

1. get_*: alpha, axes, clip_box, dpi, visible, ...
2. set_*: ...
<br>
<br>
1. add_*: artist, axes, gridspec, subfigure, subplot
2. align_*: labels, xlabels, ylabels
3. suptitle, supxlabel, subylabel
4. sub*: subplot_mosaic, subplots, subplots_adjust

</show>

::right::

```python
class Figure(
    figsize=None,
    dpi=None,
    *,
    facecolor=None,
    edgecolor=None,
    linewidth=0.0,
    frameon=None,
    subplotpars=None,
    tight_layout=None,
    constrained_layout=None,
    layout=None,
    **kwargs,
):  
# The top level container for all the plot elements.
```

<!--
Figure的主要功能有以下几类
1. 定义canvas（长宽、像素比，blending模式等）
2. 归集Axes和Artist对象，提供检索能力
3. 创建子图(subplot_*)
4. 管理子图布局（通过GridSpec和LayoutEngine)
5. 设置共同的属性，比如suptitle, supxlabel, subylabel
6. 显示图形窗口，保存图形到io设备上。

右边是Figure的构造函数
当我们无论通过pyplot的哪个方法
来创建一个Figure对象时
一些参数最终都被传递给这个构造函数
以创建Figure对象

我们通过figsize和dpi来指定canvas size
figsize是一个表示长和宽的二元组
单位是inch
figsize 乘以 dpi
就得到了以像素单位计的canvas size

Figure对象有这些属性
可以看到它归集了artists, axes, legends等
这些集合对象
也有象.dpi, bbox gca这样的对象

在方法中
几乎所有的属性
都有对应的getter和setter方法

我们可以通过add_*方法
来向Figure中增加artist, axes, gridspec, subplot等

这里我们提一下add_subplot方法
因为并不存在所谓的subplot对象
这个操作实际上也是调用了fig.add_axes
不同之处在于
add_subplot是更高级的接口
它要求传入子图的网格坐标
（即类似111, 241这类的数字）
因此在创建子图时
也就同时完成了子图的定位

我们接下来演示一下add_subplot的用法
[CLK]
-->

---
layout: two-cols
class: fade
clicks: null
---

# add_subplot
<hr>

::left::

```python {all|11-18|21-29|23} {maxHeight:'400px'}
def plot_and_text(ax, text):
    ax.plot([0, 1], [0, 1])
    ax.text(0.02, 0.9, text)


f = plt.figure()
f2 = plt.figure()

_max = 12
for i in range(_max):
    ax = f.add_subplot(
        3, 4, i + 1, 
        fc=(0, 0, 0, i / (_max * 2)), 
        xticks=[], 
        yticks=[]
    )
    text = f"{chr(i + 97)})3,4,{str(i + 1)}"
    plot_and_text(ax, text)

    if i < 9:
        ax = f2.add_subplot(
            341 + i, 
            fc=(0, 0, 0, i / (_max * 2)), 
            xticks=[], 
            yticks=[]
        )
        
        text = f"{chr(i + 97)}) {str(341 + i)}"
        plot_and_text(ax, text)

f.tight_layout()

plt.show()
```

::right::

<show at="0,1">

![75%](https://images.jieyu.ai/images/2023/07/lesson16-subplot-12.png?3)
</show>

<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson16-subplot-9.png)
</show>

<!--
add_subplot方法用来向Figure中增加一个子图
它有好几个函数签名
有不带参数的
有带参数的
我们主要介绍这样两种，分别是
add_subplot(331)
和add_subplot(3,3,1)
这两者完全等价

[CLK1]

这里11~18，绘制出右图
图中的文字反映了add_subplot的调用参数
这里使用的方法是将 行，列和索引分别传入

也可以象第21行~29这样
[CLK2]
以单一整数的方式传入
整数的百分位表明子图的行数
十分位表明子图的列数
个位表明要增加子图的索引

[CLK3]
第23行，这里给子图增加了背景色
这里使用的是RGBA表示法
基色是黑色
alpha通道数值逐次递增
最终形成由浅到深的变化
-->

---
layout: statement
clicks: 13
---

# Figure Anatomy
<hr>


```python {all|20|21|23-26|27|29-30|32,33|36|38,39|40-42|44|45,46|47|49} {maxHeight:'400px'}
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
from matplotlib.patheffects import withStroke
from matplotlib.ticker import AutoMinorLocator, MultipleLocator


royal_blue = [0, 20/256, 82/256]


# make the figure

np.random.seed(19680801)

X = np.linspace(0.5, 3.5, 100)
Y1 = 3+np.cos(X)
Y2 = 1+np.cos(1+X/0.75)/2
Y3 = np.random.uniform(Y1, Y2, len(X))

fig = plt.figure(figsize=(7.5, 7.5))
ax = fig.add_axes([0.2, 0.17, 0.68, 0.7], aspect=1)

ax.xaxis.set_major_locator(MultipleLocator(1.000))
ax.xaxis.set_minor_locator(AutoMinorLocator(4))
ax.yaxis.set_major_locator(MultipleLocator(1.000))
ax.yaxis.set_minor_locator(AutoMinorLocator(4))
ax.xaxis.set_minor_formatter("{x:.2f}")

ax.set_xlim(0, 4)
ax.set_ylim(0, 4)

ax.tick_params(which='major', width=1.0, length=10, labelsize=14)
ax.tick_params(which='minor', width=1.0, length=5, labelsize=10,
               labelcolor='0.25')

ax.grid(linestyle="--", linewidth=0.5, color='.25', zorder=-10)

ax.plot(X, Y1, c='C0', lw=2.5, label="Blue signal", zorder=10)
ax.plot(X, Y2, c='C1', lw=2.5, label="Orange signal")
ax.plot(X[::3], Y3[::3], linewidth=0, markersize=9,
        marker='s', markerfacecolor='none', markeredgecolor='C4',
        markeredgewidth=2.5)

ax.set_title("Anatomy of a figure", fontsize=20, verticalalignment='bottom')
ax.set_xlabel("x Axis label", fontsize=14)
ax.set_ylabel("y Axis label", fontsize=14)
ax.legend(loc="upper right", fontsize=14)

fig.suptitle("this is figure title")

# Annotate the figure

def annotate(x, y, text, code):
    # Circle marker
    c = Circle((x, y), radius=0.15, clip_on=False, zorder=10, linewidth=2.5,
               edgecolor=royal_blue + [0.6], facecolor='none',
               path_effects=[withStroke(linewidth=7, foreground='white')])
    ax.add_artist(c)

    # use path_effects as a background for the texts
    # draw the path_effects and the colored text separately so that the
    # path_effects cannot clip other texts
    for path_effects in [[withStroke(linewidth=7, foreground='white')], []]:
        color = 'white' if path_effects else royal_blue
        ax.text(x, y-0.2, text, zorder=100,
                ha='center', va='top', weight='bold', color=color,
                style='italic', fontfamily='Courier New',
                path_effects=path_effects)

        color = 'white' if path_effects else 'black'
        ax.text(x, y-0.33, code, zorder=100,
                ha='center', va='top', weight='normal', color=color,
                fontfamily='monospace', fontsize='medium',
                path_effects=path_effects)


annotate(3.5, -0.13, "Minor tick label", "ax.xaxis.set_minor_formatter")
annotate(-0.03, 1.0, "Major tick", "ax.yaxis.set_major_locator")
annotate(0.00, 3.75, "Minor tick", "ax.yaxis.set_minor_locator")
annotate(-0.15, 3.00, "Major tick label", "ax.yaxis.set_major_formatter")
annotate(1.68, -0.39, "xlabel", "ax.set_xlabel")
annotate(-0.38, 1.67, "ylabel", "ax.set_ylabel")
annotate(1.52, 4.15, "Title", "ax.set_title")
annotate(1.75, 2.80, "Line", "ax.plot")
annotate(2.25, 1.54, "Markers", "ax.scatter")
annotate(3.00, 3.00, "Grid", "ax.grid")
annotate(3.60, 3.58, "Legend", "ax.legend")
annotate(2.5, 0.55, "Axes", "fig.subplots")
annotate(4, 4.5, "Figure", "plt.figure")
annotate(0.65, 0.01, "x Axis", "ax.xaxis")
annotate(0, 0.36, "y Axis", "ax.yaxis")
annotate(4.0, 0.7, "Spine", "ax.spines")

# frame around figure
fig.patch.set(linewidth=4, edgecolor='0.5')
plt.show()
```

<Loc width="45%" height="50%">

        
           
<show at="2">

![](https://images.jieyu.ai/images/2023/07/lesson16-empty-ax.png)
</show>

<show at="3">

![](https://images.jieyu.ai/images/2023/07/lesson16-add-locator.png)
</show>

<show at="4">

![](https://images.jieyu.ai/images/2023/07/lesson16-formatter.png)
</show>

<show at="5">

![](https://images.jieyu.ai/images/2023/07/lesson16-set-lim.png)
</show>

<show at="6">

![](https://images.jieyu.ai/images/2023/07/lesson6-tick-params.png)
</show>

<show at="7">

<Arrow x1="10" y1="10" x2="130" y2="120"/>
![](https://images.jieyu.ai/images/2023/07/lesson6-add-gridline.png)
</show>

<show at="8|15">

![](https://images.jieyu.ai/images/2023/07/fig_anatomy.png)
</show>

<show at="8">

<Arrow x1="10" y1="10" x2="110" y2="65"/>
<Arrow x1="10" y1="10" x2="110" y2="300"/>
</show>

<show at="9">
<Arrow x1="10" y1="10" x2="130" y2="140"/>
</show>

<show at="10">
<Arrow x1="10" y1="10" x2="145" y2="50"/>
</show>


<show at="11">
<Arrow x1="10" y1="10" x2="205" y2="410"/>
</show>

<show at="12">
<Arrow x1="10" y1="10" x2="260" y2="80"/>
</show>
</Loc>

<!--
下面我们通过一段代码
绘制一张图
并一步步地展示中间结果

[CLK1]
第20行，我们从新建一个Figure对象开始
此时不产生任何绘图
但如果你此时保存图的话
则会仍然会得到一张空白图

[CLK2】
第21行
我们给图加上一个子图（axex)
注意子图与Figure所在的区域
有轻微的背景色差异
注意这里的aspect
aspect可用来缩放两个轴
使得长宽比接近一些

[GUIDE]

[CLK3]
23-26 设置轴的刻度
这里有主刻度与次刻度之分
相当于我们从米尺上看到的
每厘米有一个大刻度
每毫米上有一个小刻度

[CLK4]
第27行 给x轴标签加上格式化
现在小刻度的标签就显示出来了

[CLK5]
第29，30行
我们为x轴、y轴设置象限大小
注意设置前后的差异
设置之前
最大刻度为1
设置之后
最大刻度变为4

[CLK6]
第32行
使用tick_params
更改刻度、刻度标签和线条的外观
在这里
我们注意前后两张图
线的长度发生明显变化

[CLK7]
第36行，增加网格线

[CLK8]
38,39 作图
画两条普通的曲线
给曲线设置标签
这个标签会成为legend的名字
颜色使用调色板
通过名字来获取颜色
这里 lw 是线条宽度，即line_width

[CLK9]
40 添加标记（mark）
通过参数marker来选择标记的形状
这里显示的是小方块
如果marker = 'o'
则会显示成为小圆圈

[CLK10]

44 给子图增加title
当我们调用plt.title时
也是给子图增加标题
给Figure增加标题要用 fig.suptitle

[CLK11]
45,46 增加x轴和y轴的标签

[CLK12]
47 给图增加图例（legend)

[CLK13]
49 给Figure增加title
这里没有截图
最终这个标题会显示在图上方正中间
在ax的标题上方
-->

---
layout: section
---

# /02 常用对象

<!--
下面
我们就来讨论几个matplotlib绘图中
常用的对象
和常见的场景
-->

---
layout: two-cols
clicks: 10
---

# Axis - Spine
<hr>

::left::

```python {all|4-7|9-14|22|23|34} {maxHeight: '400px'}
x = np.linspace(0, 2*np.pi, 100)
y = 2 * np.sin(x)

fig, ax_dict = plt.subplot_mosaic(
    [['center', 'zero'],
     ['axes', 'data']]
)

# axd = plt.figure().subplot_mosaic(
#    """
#    ABD
#    CCD
#    """
#)

fig.suptitle('Spine positions')


ax = ax_dict['center']
ax.set_title("'center'")
ax.plot(x, y)
ax.spines[['left', 'bottom']].set_position('center')
ax.spines[['top', 'right']].set_visible(False)

ax = ax_dict['zero']
ax.set_title("'zero'")
ax.plot(x, y)
ax.spines[['left', 'bottom']].set_position('zero')
ax.spines[['top', 'right']].set_visible(False)

ax = ax_dict['axes']
ax.set_title("'axes' (0.2, 0.2)")
ax.plot(x, y)
ax.spines.left.set_position(('axes', 0.2))
ax.spines.bottom.set_position(('axes', 0.2))
ax.spines[['top', 'right']].set_visible(False)

ax = ax_dict['data']
ax.set_title("'data' (1, 2)")
ax.plot(x, y)
ax.spines.left.set_position(('data', 1))
ax.spines.bottom.set_position(('data', 2))
ax.spines[['top', 'right']].set_visible(False)
```

::right::

<show at="0,3|10">

![75%](https://images.jieyu.ai/images/2023/07/lesson16_spine_position.png?1)
</show>

<show at="1">

![75%](https://images.jieyu.ai/images/2023/07/lesson16-set-xticks.png?1)
</show>

<show at='2'>

![75%](https://images.jieyu.ai/images/2023/07/lesson16-subplot_mosaic.png?1)
</show>

<!--
首先我们讨论Axis对象
Axis是坐标轴对象
它是Axes的一个构成部分
在一个二维图中
存在着x-axis和y-axis
在三维图中
还存在z-axis

[TYPE]

我们可以通过axes.get_xaxis()来获取x轴
通过axes.get_yaxis()来获取y轴

轴有自己的上下界（xmin, xmax, ymin, ymax)
我们在上一张slide中已经看到过
如何通过 axes.set_xlim 来设置轴的上下界

如果数据变动范围太大
我们也可以为它设置scale
[TYPE]
# axes.set_yscale('log')
在绘制行情软件图时
我们常常会遇到这样的场景
比如
象k线图的y轴
就有等分坐标、等比坐标和对数坐标
对数坐标就是通过set_yscale来设置

讨论完Axis与axes的关系
我们再来讨论Axis的构成

一根轴由spine(轴脊线)
tick（刻度）
和label(标签)组成
注意spine并不是轴
一张图可以有4个spine
但只有2个轴

刻度是表示轴上数据点的标记
它有刻度值和刻度标签（tick labels)
两个重要属性
在上一张slide中我们已经看到
如何设置刻度定位
tick标签及tick的样式
如果我们不进行这样的设定

Matplotlib 会根据输入数据
自行选择刻度数和刻度位置
以使得轴上有合理的刻度数
并且会对刻度值进行四舍五入
以确保刻度数看上去更规整
这样做的后果之一是
图的边缘可能没有刻度
在我们的示例中
输出的图形就是这种情况

但我们也可以通过set_xticks
直接设置各个ticks的取值
这样刻度将在x取值的边缘处有值
这张图就显示了我们进行set_xticks后的情况
现在，在轴的边缘也有了刻度
我们再退回到前一张图
对比看一下差异

这张slide我们重点关注spine
讨论如何解决以下几个问题
1. 如何隐藏某个spine
2. 图一般是按第一象限来显示
如何做到按其它象限来显示

[CLK1]
4, plot.subplot_mosaic
这是另一种生成多个子图的方式
实际上是figure的方法
它的特点是我们可以使用带语义的名字
来命名将要生成的轴
并且可以实现直观易懂的布局
这一行代码生成了右边这样四个子图
我们可以直接使用 'center' 'zero'
等名字来引用这些子图
此外，我们还可以以简练的语法
生成较复杂的跨网格布局
比如第9~13行
将生成这样的布局

[CLK2]

这里ABDCCD这样的表示
很直观地描述了子图在网格中的跨越情况

至此
我们已经接触了
subplot
add_subplot
subplot_mosaic
add_axes
等多个生成子图的方法
我们可以在工作中
根据需要来选择使用哪一个
不过
有关于 subplot_mosaic 
值得一提的是
这个API是3.3引入的
目前还是实验性的API
未来有可能发生破坏性的变更

[CLK3]
22 我们将left, bottom spine放到中心位置 
23 隐藏top, right spine
34 还可以直接按数值进行设置坐标原点
注意到目前为止
出现了两种引用spine的方法
一种是字典式
一种是属性式。
-->

---
layout: two-cols
clicks: null
---

# Axis - 共享x轴
<hr>

::left::

```python {all|6,7|10|12|13,14|16-18|24-26|28} {maxHeight: '400px'}
import matplotlib.pyplot as plt

fig, ax = plt.subplots()
fig.subplots_adjust(right=0.75)

twin1 = ax.twinx()
twin2 = ax.twinx()

# Offset the right spine of twin2. 
twin2.spines.right.set_position(("axes", 1.2))

p1, = ax.plot([0, 1, 2], [0, 1, 2], "C0", label="Density")
p2, = twin1.plot([0, 1, 2], [0, 3, 2], "C1", label="Temperature")
p3, = twin2.plot([0, 1, 2], [50, 30, 15], "C2", label="Velocity")

ax.set(xlim=(0, 2), ylim=(0, 2), xlabel="Distance", ylabel="Density")
twin1.set(ylim=(0, 4), ylabel="Temperature")
twin2.set(ylim=(1, 65), ylabel="Velocity")

ax.yaxis.label.set_color(p1.get_color())
twin1.yaxis.label.set_color(p2.get_color())
twin2.yaxis.label.set_color(p3.get_color())

ax.tick_params(axis='y', colors=p1.get_color())
twin1.tick_params(axis='y', colors=p2.get_color())
twin2.tick_params(axis='y', colors=p3.get_color())

ax.legend(handles=[p1, p2, p3])

plt.show()
```

::right::

![75%](https://images.jieyu.ai/images/2023/07/lesson16_twinx.png)

<!--
有时候我们需要多个y轴
特别是一左一右双y轴比较常见
比如，我们在第4课
将投资者人数与上证指数进行关联
但这两组数据的量纲不一致
直接显示的话
量纲小的数据
会被显示成类似一条直线
完全看不清波动
当时为了解决这个问题
我们是将数据本身进行了缩放

我们也可以让绘图软件来自动完成这个工作
这样做的好处是
我们不仅保留了数据的走势
还保留了数据本身
这会在刻度、hotspot数据上体现出来
比如
在交互式图形中
当我们把鼠标移动到图上某个点时
由于我们并没有修改数据本身
此时hotspot就能够提示正确的数值

具体的做法就是使用twinx来生成共享x轴
我们在第12课已经使用过这一技巧
不过当时我们并没有深入讨论
这一次我们再进行一点升级
看看多于两个y轴如何处理

6,7 通过twinx实现共享Y轴
这里我们需要几个Y轴
或者说有多少个数据序列，
我们就增加几个twinx轴

10 将twin2的右轴脊线定位到 axes的 1.2位置处

12 第一个序列仍然使用ax来绘图
13，14 第2，第3个系列使用twinx返回的对象来绘制
16-18 分别设置x轴，y轴的象限和标签
以及y2,y3的象限和标签
24-26 设置轴的颜色
这里都仅对y轴进行设置
因为x轴是共享的

28 设置legend
让ax, twin1, twin2分别自行处理。
在这里
如果我们直接调用 plt.legend()
将会只出现twin2图例项
这是因为plt只会获取当前活动的axes
而当前活动的axes正是twin2
-->

---
layout: two-cols
clicks: null
---

# ticks formatting
<hr>

::left::

```python {all|12-20|21-22|25-29} {maxHeight: '400px'}
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
from matplotlib.dates import (AutoDateLocator, YearLocator, MonthLocator,
                              DayLocator, WeekdayLocator, HourLocator,
                              MinuteLocator, SecondLocator, MicrosecondLocator,
                              RRuleLocator, rrulewrapper, MONTHLY,
                              MO, TU, WE, TH, FR, SA, SU, DateFormatter,
                              AutoDateFormatter, ConciseDateFormatter)

locators = [
    ('AutoDateLocator(maxticks=8)', '2003-02-01', '%Y-%m'),
    ('YearLocator(month=4)', '2003-02-01', '%Y-%m'),
    ('MonthLocator(bymonth=[4,8,12])', '2003-02-01', '%Y-%m'),
    ('DayLocator(interval=180)', '2003-02-01', '%Y-%m-%d'),
    ('WeekdayLocator(byweekday=SU, interval=4)', '2000-07-01', '%a %Y-%m-%d'),
    ('HourLocator(byhour=range(0,24,6))', '2000-02-04', '%H h'),
    ('MinuteLocator(interval=15)', '2000-02-01 02:00', '%H:%M'),
    ('SecondLocator(bysecond=(0,30))', '2000-02-01 00:02', '%H:%M:%S'),
    ('MicrosecondLocator(interval=1000)', '2000-02-01 00:00:00.005', '%S.%f'),
    ('RRuleLocator(rrulewrapper(freq=MONTHLY, \nbyweekday=(MO, TU, WE, TH,' +
     ' FR), bysetpos=-1))', '2000-07-01', '%Y-%m-%d')
]

formatters = [
    ('AutoDateFormatter(ax.xaxis.get_major_locator())'),
    ('ConciseDateFormatter(ax.xaxis.get_major_locator())'),
    ('DateFormatter("%b %Y")')
]


def plot_axis(ax, locator=None, xmax='2002-02-01', fmt=None, formatter=None):
    """Set up common parameters for the Axes in the example."""
    ax.spines.right.set_visible(False)
    ax.spines.left.set_visible(False)
    ax.spines.top.set_visible(False)
    ax.yaxis.set_major_locator(ticker.NullLocator())
    ax.tick_params(which='major', width=1.00, length=5)
    ax.tick_params(which='minor', width=0.75, length=2.5)
    ax.set_xlim(np.datetime64('2000-02-01'), np.datetime64(xmax))
    if locator:
        ax.xaxis.set_major_locator(eval(locator))
        ax.xaxis.set_major_formatter(DateFormatter(fmt))
    else:
        ax.xaxis.set_major_formatter(eval(formatter))
    ax.text(0.0, 0.2, locator or formatter, transform=ax.transAxes,
            fontsize=14, fontname='Monospace', color='tab:blue')


fig, ax = plt.subplots(len(locators), 1, figsize=(8, len(locators) * .8),
                       layout='constrained')
fig.suptitle('Date Locators')
for i, loc in enumerate(locators):
    plot_axis(ax[i], *loc)

fig, ax = plt.subplots(len(formatters), 1, figsize=(8, len(formatters) * .8),
                       layout='constrained')
fig.suptitle('Date Formatters')
for i, fmt in enumerate(formatters):
    plot_axis(ax[i], formatter=fmt)
```

::right::
![](https://images.jieyu.ai/images/2023/07/lesson16-datelocators.png)
![](https://images.jieyu.ai/images/2023/07/lesson16-date-formatter.png)

<!--
关于刻度
我们常常遇到这样的问题
一是刻度太过拥挤
或者文本太长

二是如何处理时间
如何正确地格式化时间
以及按时间划分刻度

在matplotlib中
使用Locator来划分刻度
第12到20行主要演示了各种日期时间刻度划分器的用法
第21行是基于规则的Locator
它也是其它几种locator的基类

第25~29行，演示了三种格式化类的用法
这张图在notebook中有
请大家在notebook中仔细研究
-->

---
layout: two-cols
clicks: null
---

# K线的刻度问题

::left::

```python {all|5|29,30|31} {maxHeight: '400px'}
from coursea import *
await init()
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
from matplotlib.dates import (AutoDateLocator, HourLocator,
                              MinuteLocator, ConciseDateFormatter)

bars = await Stock.get_bars("000001.XSHE", 500, FrameType.MIN30)
frames = bars["frame"]

def plot_axis(frames):
    fig, ax = plt.subplots(figsize=(10, 1), layout='constrained')
    
    ax.spines.right.set_visible(False)
    ax.spines.left.set_visible(False)
    ax.spines.top.set_visible(False)
    ax.yaxis.set_major_locator(ticker.NullLocator())
    ax.tick_params(which='major', width=1.00, length=10)
    ax.tick_params(which='minor', width=0.75, length=2.5)
        
    ax.set_xlim(frames[0], frames[-1])

    major_locator = AutoDateLocator(maxticks=8)
    minor_locator = HourLocator(interval=100)
    
    ax.xaxis.set_major_locator(major_locator)
    ax.xaxis.set_minor_locator(minor_locator)
    
    ax.xaxis.set_major_formatter(ConciseDateFormatter(major_locator))
    ax.xaxis.set_minor_formatter(ConciseDateFormatter(minor_locator))
    ax.tick_params(axis='x', rotation=45)
    
plot_axis(frames)
```
::right::
![](https://images.jieyu.ai/images/2023/07/lesson16-conciseformatter.png)

<!--
在面对一些比较复杂的情况
比如k线绘制
matplotlib
自带的各种Locator都是不够用的
k线主要面临的主要问题是
交易日历不连续
交易时间不连续的问题
所谓交易日历不连续
是指我们存在各种各样的节假日
在这期间是不交易的
所谓交易时间不连续
是指我们的股市不是24小时交易
它只是在中间一小段才进行交易

但是刻度本质上应该是连续的
除非做特别处理
这里我们使用了ConciseDateFormatter
但效果仍然不理想
这是需要自行开发的

第5行，我们引入了ConciseDateFormatter
29,30 构造ConciseDateFormatter
将两个locator传给它
这样ConciseDateFormatter在内部就会知道
如何使用这两个Locator

第31行，这里通过旋转文本45度角
解决了长文本标签显示时
空间不够的问题
-->

---
layout: two-cols
click: null
---

# 文本和中文 
<hr>

<div style="width: 50%; margin: 0 auto">

| method     | description                                          |
| ---------- | ---------------------------------------------------- |
| text       | 用以在任意位置输出文本(Axes、Figure)                 |
| annotate   | 在axes的任意位置，增加一个文本注释，和一个可选的箭头 |
| set_xlabel | 给x轴增加标签                                        |
| set_ylabel | 给y轴增加标签                                        |
| set_title  | 给Axes增加标题                                       |
| suptitle   | 给Figure增加标题                                     |
| figtext    | 给Figure增加文本                                     |

</div>

<!--
在matplotlib中
与文本打交道主要有以下几种方法
一是text方法
在plt, Figure和Axes对象中都存在此方法
用以在任意位置输出文本

二是annotate方法
它是plt和Axes中才有的方法
用来增加一个文本注释
它与text方法的主要区别
就是这个方法带一个参数
通过这个参数
我们可以在注解文本之外
再添加一个箭头

给坐标轴增加标签
我们使用set_xlabel/set_ylable方法

我们使用Axes.set_title方法
或者plt.title方法
来给子图增加标题
Figure对象没有此方法
但Figure对象中存在一个suptitle方法
用来增加标题
我们前面已经提到过
Figure的标题与子图的标题是不一样的

figtext是plt独有的方法
它的作用是给figure增加一段文本
它与text的区别是
它只作用于Figure
而不会绘制到Axes内部

在使用text方法时
我们要了解我们可以
给它加上旋转、边框
可以通过'ha', 'va'来指定如何对齐
有时候我们需要与坐标轴对齐
此时可以通过transform参数来实现

在文本边框上有多种样式可选
比如方框、圆形框、箭头、圆角边框等等
-->

---
layout: two-cols
click: null
---

# 文本和中文 
<hr>

::left::

```python {all|3|4|15|16}
#绘制折线图
import matplotlib.pyplot as plt
plt.rcParams["font.sans-serif"]=["WenQuanYi Micro Hei"] 
plt.rcParams["axes.unicode_minus"]=False 

year = [2017, 2018, 2019, 2020]
people = [20, 40, 60, 70]
#生成图表
plt.plot(year, people)
plt.xlabel('年份')
plt.ylabel('人口')
plt.title('人口增长')

#设置纵坐标刻度
plt.yticks([0, 20, 40, 60, 80])
plt.fill_between(year, people, 20, color = 'green')
plt.show()
```
::right::

![](https://images.jieyu.ai/images/2023/07/lesson16-font-missed.png)

<!--
matplotlib默认不支持中文
在没有对 Matplotlib 进行设置时
直接使用中文
绘制的图像会出现中文乱码

如果系统中没有安装中文字体
在matplotlib中要使用中文
还得先安装中文字体
这一部分
请大家查看我们的notebook
这里就不详细介绍了

3 设置字体
这里的意思是
当matplotlib需要使用衬线字体时
就使用我们指定的文泉驿微米黑字体
这是一种开源、可免费商用的字体
比较接近微软雅黑
4 必须加上这个设置，才能正常显示负号
16行 这里我们学到一个新的方法
如果我们要绘制积分图
可以使用此方法
它的作用是填充两条横向曲线中间的空白
这个区间由(x, y1)和(x,y2)来定义
所以这里的x是共享的
我们可以把横坐标x作为第一个参数传入
纵坐标y作为第二个参数传入
这样就定义了第一条曲线
然后我们传入y2
这里指定为标量20
这样x与y2就构成第二条曲线
接下来就填充这两条曲线之间的空白
就完成了整个区域的填充
-->

---
layout: two-cols
clicks: 2
---

# 样式
<hr>

::left::

<div v-if="$slidev.nav.clicks === 0">

<br>
<br>
```python
print(plt.style.available)

plt.style.use('Solarize_Light2')

for y in range(20, 100, 5):
    plt.plot([i for i in np.arange(50)], np.linspace(0, y, 50))
```
</div>


<div v-if="$slidev.nav.clicks === 1">

<br>
```python
colors = plt.cm.Pastel1.colors
plt.gca().set_prop_cycle('color', colors)
for i, y in enumerate(range(20, 100, 5)):
    plt.plot([i for i in np.arange(50)],
            np.linspace(0, y, 50), 
            label=f'C{i%len(colors)}')
plt.legend()
```
</div>

::right::


<div v-if="$slidev.nav.clicks === 0">

![75%](https://images.jieyu.ai/images/2023/07/lesson16-use-style.png)
</div>

<div v-if="$slidev.nav.clicks === 1">

![75%](https://images.jieyu.ai/images/2023/07/lesson16-color-cycle.png)
</div>

<!--
在批量绘图时
我们常常会遇到重复设置选项的情况
matplotlib允许我们
通过rcParams或者stylesheet来减轻这项繁琐工作
此外
使用stylesheet还将使得绘图结果
可以在不同机器上重现

style用来设置背景、
字体及字体大小、
线的宽度、
标签字体大小
线的颜色等等。

matplotlib提供了许多预置的样式

第一行代码
将列出机器上所有的样式表的名字
第二行，我们要求使用名为solarize的样式
这是一种暖色调的样式

第5~6行
我们连续绘制了20条直线
每条直线都显示出不同的颜色
由于我们并没有在绘制过程中指定颜色
那么，这些色彩一定是样式指定的
具体是怎么回事？

这一次
我们使用另一种样式定义的颜色
这种样式共定义了10种颜色
并且我们通过第2行的语句
将这10种颜色设置为当前的循环色
所谓循环色
就是在我们没有指定颜色时
为了区分被绘制的对象
matplotlib会自动在指定的色彩表中
循环选取颜色来进行绘制
这里我们看到
总共绘制了20条直线
但从第10条起
色彩就开始重复使用了
-->

---
layout: two-cols
---

# colormap
<hr>

::left::
![75%](https://images.jieyu.ai/images/2023/07/ysl_2021_spring.png)

::right::

<!--
在这一节
我们来看一个实际的例子
关于设计
特别是配色
我们常常需要从他人身上学习
一个常见的例子是
我们发现一张非常具有美感的图
或者网站
我们希望借鉴它的调色
于是，我们通过工具把它的主要配色撷取下来
然后转换成color map
在我们自己的绘图中使用。

这个例子演示了
如何转换某款经典口红配色成为colormap。

首先，我们借助工具将这些颜色提取出来
保存为hex方式
然后将其绘制成色板
-->

---
layout: two-cols
clicks: null
---

# colormap
<hr>

::left::

```python {7-12|14-21|23-24|26-27|30-44|28|42-44} {maxHeight: '400px'}
import numpy as np
import matplotlib.pyplot as plt
import mpl_toolkits.axes_grid1 as axes_grid1
import re
import math

ysl = [
        "4D0609","8F2B37","7F1C2E","AC5754",
        "790D1D", "FE7082","A20D44","B61629",
        "CE171B","ED322B","F776AE","CE242D",
        "C91D37","A63139","FE52AE","C91715", 
        "DE2025","BE161D","530C1E", "FE82AE"]

def parse_hex(colors):
    cmap = []
    for c in colors:
        cmap.append((int(c[:2], base=16), 
                    int(c[2:4], base=16), 
                    int(c[4:], base=16)))
            
    return cmap

def lum (r,g,b):
    return .8 * r + .1 * g + .1 * b

colors = sorted(parse_hex(ysl), 
                key = lambda rgb: lum(*rgb), reverse=False)
colors = np.array(colors).reshape(5, 4, 3)

plt.imshow(colors, aspect='auto')
plt.gca().spines.top.set_visible(False)
plt.gca().spines.right.set_visible(False)
plt.gca().spines.left.set_visible(False)
plt.gca().spines.bottom.set_visible(False)
plt.xticks([])
plt.yticks([])

for y in range(colors.shape[0]):
    for x in range(colors.shape[1]):
        txt = f"#{colors[y,x][0]:02X}{colors[y,x][1]:02X}"
                f"{colors[y,x][2]:02X}"
        plt.text(x, y, txt, 
                horizontalalignment='center',
                 verticalalignment='center', color='white')
```
::right::

![](https://images.jieyu.ai/images/2023/07/ysl_palette.png)

<!--
第7-12行就是我们提取的各种颜色
这里使用了hex编码方案保存

第14-21行，我们通用一个函数
来将hex编码的颜色
转换成rgb三通道色
这里我们使用int函数
来将字符串表示的数字转换成整数
但要注意这里的数字是十六进制
所以我们要额外多传入一个base参数

23~24 这个函数用来将rgb通道颜色
转换成为HSL空间
这是为了对色彩排序的需要
因为一般情况下
我们是无法在rgb色彩空间对色彩进行排序的
要说明的是
这里的逻辑并没有严格按RGB->hsl转换的要求来实现
因为我们这里处理的色彩谱系都在红色区域
所以可以这样简单处理
第26~27行，我们实现了对色彩的排序

第30~44行
我们调用imshow绘制色彩块
我们在之前讲统计分析时
已经见过这个方法
在这个方法中
如果colors是一个 [ m * n * 3 ]的数组
且数组元素值要么在 [0,255]之间
要么都在 [0,1]之间
此时imshow就会把这个数组当成
要绘制方块的填充色
否则，会把这个数组当成对调色板的索引

这个准备工作
我们是在第28行完成的
这里我们通过 reshape方法
将它重排成 [5 * 4 * 3] 的数组

最后，我们看一下第42~44行
这里在每个色块中心处绘制了色号
我们通过'ha' /'va'来设置居中对齐

这样
我们就完成了色彩的提取
并且绘制了这些色彩的一个预览图
-->

---
layout: two-cols
clicks: null
---

# colormap
<hr>

::left::

```python {all|1|14-21|24|29} {maxHeight: '400px'}
from matplotlib.colors import ListedColormap
import numpy as np
import matplotlib.pyplot as plt

# extract palettes from coolors.co

ysl = [
        "4D0609","8F2B37","7F1C2E","AC5754",
        "790D1D", "FE7082","A20D44","B61629",
        "CE171B","ED322B","F776AE","CE242D",
        "C91D37","A63139","FE52AE","C91715", 
        "DE2025","BE161D","530C1E", "FE82AE"]

def parse_hex(colors):
    cmap = []
    for c in colors:
        cmap.append((int(c[:2], base=16)/255, 
                    int(c[2:4], base=16)/255, 
                    int(c[4:], base=16)/255, 1))
            
    return cmap


ysl_cmp = ListedColormap(parse_hex(ysl))
x = np.arange(20)
y = np.random.randint(5, 20, 20)

fig, ax = plt.subplots(figsize=(10, 7))
ax.bar(x, y, color=ysl_cmp.colors)
ax.set_title("Different colors for each bar")
```

::right::

![](https://images.jieyu.ai/images/2023/07/lesson16_use_colormap.png)

<!--
现在我们来讨论
如何将上述色彩转换成colormap

CLK1
我们要借助一个名为ListedColormap的类

CLK2
同样地
我们需要先将hex表示的色彩进行转换
这次略有不同的是
我们需要将其转换为 RGBA 的模式
我们在本节课的前面部分已经提到过这种色彩表示方法
RGB是基色
A代表alpha，是指色彩如何与背景相互混合

CLK3
转换成RGBA，是ListedColormap的需要
另外，它只接受[0,1]之间的值
所以我们还要把由hex转过来的值
除以255

接下来的工作比较简单
我们定义了一个Y序列
打算将其绘制成 Bar 图

CLK4
ax.bar这个方法接受一个color参数
这个参数可以是调色板

最终我们就得到了右边这个图
-->
