---
theme: ../theme
class: text-center
lineNumbers: false
aspectRatio: 16/9
transition: slide-left
title: 第10课 Talib
layout: cover
---

# 第10课 Ta-lib

<!--
大家好！
欢迎来到我们的量化金融实战课
今天我们将向大家介绍经典的技术分析库 - Talib
-->

---
layout: toc
image: 'https://images.jieyu.ai/images/2023/07/lesson10-outline.png?2'
---

---
layout: section
image: '/section-right.png'
imageOrder: 2
---
# /01 练习
## 第9课习题
---
layout: statement
---

# TALIB - 经典的技术分析库

## 安装python库
## 安装原生库
<v-clicks>

### Macos
### Linux
### Windows
### conda
### 非官方源pip

</v-clicks>

<!--
我们来看ta-lib
ta-lib是一个经典的技术分析库
当我们说到ta-lib时
我们实际是在说两个库
其中一个是ta-lib的c库
另一个是它的python封装
在python量化研究中
我们直接调用的
是这个封装库
而不是原生库

talib的python封装库本身没有实现任何功能
它必须依赖于原生库才能工作
因此
在安装时
两个库都必须安装

安装其封装库比较简单
我们通过这个命令来安装就好 <type>
pip install TA-Lib

一般来说
安装python库时
它会把依赖的库也一并安装好
但对ta-lib并不是这样
我们还必须手动安装它的原生库

两个库在安装上没有先后顺序之分
先安装哪一个都行
但安装ta-lib的原生库
在不同平台上
安装体验完全不同
我们来具体地看一下

首先是macos <next>
在macos下安装比较容易
我们可以通过以下命令进行安装 <type>
brew install TA-Lib

现在我们来看ubuntu/linux <next>
linux有很多分发版本
这里我们只讲ubuntu
因为ubuntu差不多是目前发行量最大的一种linux
特别是通过windows WSL发布之后

首先要确保机器上已经安装了 <type>
build-essential
然后下载talib的源码
解压，进入目录，执行 <type>
# ./configure <type>
# make <type>
# sudo make install
这是我们以源码方式安装c库的一般步骤
这个过程略有点复杂
你可以通过omicron的帮助文档 <browser>
直接下载这个脚本来运行

最后是windows <next>
在windows下的安装最为复杂
主要原因在于
如果不是windows下的c/c++开发者
那么你的机器上应该不会有编译工具链
因此你需要先安装visual c++的分发包
MSVC BuildTool
设置编译环境变量
可能最难的还是有部分域名无法访问
可能还得用魔法
这使得整个过程更加复杂和耗时
所以我们在omicron的帮助文档里 <browser>
提供了详细的指南

当然也有一些跨平台的方法
比如使用conda <next>
这个方法在最新的TA-Lib安装指南有推荐
但我们没有测试过
大家可以自行试一下 <type>
conda install -c conda-forge ta-lib

另外一个跨平台的方法
就是使用非官方编译的wheel <next>
在ldf.uci.edu网站上
提供了他们预编译的wheel包 <browser>
我们可以在这里直接下载wheel包进行安装

好，整个安装就介绍到这里
-->

---
layout: statement
---

# Talib 概览

## 关于帮助文档
## 两类接口

<v-clicks>

### 函数式 (talib)
### 抽象接口 (talib.abstract)

</v-clicks>

## 功能分类概览

<!--
ta-lib是一个注册商标
其IP属于一家名叫TicTacTec的公司 <type>
这家公司的业务之一就是提供培训和相关的分析工具
可能由于这个原因
ta-lib的c库并没有提供帮助文档

不过
它的Python封装库提供了一些基础性的文档 <browser>
# https://ta-lib.github.io/ta-lib-python/doc_index.html
这部分文档中
除了安装指南
两类接口的说明外
还有一个功能列表
它的API级的说明通过docstring提供
不过这部分文档不是很详细
比如，象布林带指标
需要传入均线计算类型
这部分API文档就没有说明均线类型有哪些取值

talib提供了两类接口
一类是函数式的  <next>
一类是抽象接口  <next>
抽象接口提供了更多的灵活性
更适合二次开发
比如，我们可以继承abstract.Function类
重载`set_input_arrays`方法
来让talib可以直接操作pandas DataFrame
作为普通用户
使用任何一种接口都可以

函数式接口直接通过talib模块暴露
使用方法如下    <run>

抽象接口跟函数式接口是一样的
只不过导入位置不同
我们要从abstract这边导入  <run>

另外
通过抽象接口暴露出来的对象
会多一个info属性    <run>

通过info属性暴露出来的信息
就相当于docstring
不过它是dict
方便二次开发

ta-lib当前版本共包含了158个函数 <run>

ta-lib的函数是按分组进行组织的，
我们可以通过`get_function_groups`方法来查看 <run>

这会输出这些分组 <run>
overlap studies
其中包含了布林带
均线
加权的移动平均
模式识别
一些价格变换类的方法
和统计学的方法
还有一些波动类的指标
还有成交量类的指标

在本课中
我们更关注指标类函数
以及模式识别函数
其它一些方法
在像numpy这样的库中已经存在了
性能上可能numpy还会更快一些
就没有必要再关注了
-->

---
layout: section
---
# /02 常用指标函数

<!--
技术指标可以用来平滑价格、过滤高频噪声
判断价格的运动方向（及强弱）
划出支撑位和压力位和预测波动率

我们将在这一课里
介绍6个最常用的技术指标
-->
---
layout: image-right
image: https://images.jieyu.ai/images/2023/07/welles_wilder.png
clicks: 3
---
# ATR
<hr>

<div v-if="$slidev.nav.clicks >= 0">

## 公式

$$
TR = MAX[(H-L), |H - C_P|, |L - C_P|]
$$

$$ H = Todays'\ High $$

$$ L = Today's\ Low $$

$$ C_p = Yesterday's\ closing\ price $$

$$ Max = Highest\ value\ of\ the\ three\ terms $$

</div>
<div v-if="$slidev.nav.clicks >= 1">

## 模拟代码
</div>
<div v-if="$slidev.nav.clicks >= 2">

## ATR指标的作用

</div>

<!-- 
ATR是技术分析大师
Welles Wilder   <type>
发明的一个衡量市场平均真实波幅的指标
主要用于研判买卖时机
是显示市场变化率的反趋向指标
也是一个比较常用的技术指标

Welles Wilder是机械工程师和技术分析师
除了ATR之外
他还贡献了RSI和抛物线转向等广泛使用的指标 <type>
# RSI
# Parabolic Sar

它的计算方法是
先用以下公式计算TR <next>
这里    <mark>
H是今天的最高价
L是今天的最低价
Cp是昨天的收盘价
max则是求这里三个差中最大值 <mark>
要注意这里后两项是绝对值

然后再计算TR的指数移动平均值
就得到ATR

<next>

下面我们来看这个公式如何用python代码来实现 <code>
这里第20行的代码比较有技巧 <mark>
它用了一个reduce方法
它是从三个数组中
依次取取对应列中最大的值
生成一个结果数组
我们也可以用这个方法来实现同样的效果 <mark>
# np.vstack([A, B, C]).max(axis=0) <mark>

最后
我们对上述TR结果数组求移动平均
就得到了ATR

在talib中
我们使用以下方法进行计算 <code>

<mark>
<run>
我们来运行一下
注意看
这里两次运行的结果有所不同
因为我们使用的是SMA
而talib使用的是EMA
但我们实现的版本与国内券商的版本是一致的

<slide>
<next>
ATR有着广泛的用途
其用途之一就是仓位管理
比如
如果我们计划买入多支标的
应该如何在多个标的之间分配资金呢
这里为方便理解
我们仅以两支标的为例进行说明

假设有A、B两支标的
仓位管理上最简单的方法是平分仓位
但这样一来
如果标的A的波动比标的B大
那么总体收益率将由A来决定
从而我们买入多支标的以分散风险的目标就不能达成
但我们可以通过ATR
来按权重分配资金
达到分散风险的目的

假设我们有100万资金
希望保证每日波动在1%以内
也就是损益在1万元以内
假设出现最坏的情况
次日所有持仓都下跌一个ATR

当然还可能出现都超出一个ATR的更极端的情况
这种情况下如何处理
请大家按这里的原理
自行考虑

那么实际上
每支标的我们允许的亏损就是5千元

我们就用这5000除以个股ATR的值
这样得到每支标的可以持有的仓位

比如
假设A当前的ATR为0.152
相当于收盘价(3.72)的4%
假设B当前的ATR为4.7
相当于收盘价（70.85）的6.7%

那么我们可以持有A大约32800股 <type>
# 5000/0.152
这是用5000除以A的ATR得到的结果
此时我们持有A约12.2万元

我们可以持有B大约1000股 <type>
# 5000/4.7
这是用5000除以B的ATR得到的结果
此时我们持有B约7万元

这样
即使A、B股次日双双下跌一个ATR
即A下跌4%
B下跌6.7%
我们也只亏损1万元左右
如果我们对风险比较敏感
还可以对系数进行调整

我们也可以使用同样的原理
不是以固定的止损比率
而是以标的ATR的一定比例来设置止损
这样一来
就可以避免对股性活跃的标的的止损设置过小
导致过早地被震荡出来
同时又对某些股性不活跃的标的止损设置过大
导致止损太迟
利润被侵蚀过多

此外
正如我们在网格交易法那一课中讲过
ATR还可用来计算网格
它在海龟交易法中也有应用
-->
---
layout: statement
preload: false
clicks: 5
---
# 移动平均线
<hr>

<div v-if="$slidev.nav.clicks === 1" v-motion>

![33%](https://images.jieyu.ai/images/2023/07/1d-convolve.png)

</div>
<div  v-if="$slidev.nav.clicks === 2" v-motion>

![75%](https://images.jieyu.ai/images/2023/06/ma-phase-lag.png)

</div>

<div v-if="$slidev.nav.clicks === 3">

## SMA

<div style="color: black">

$$
\bar{p}_{SM} = \frac{p_M + p_{M-1} + ... + p_{M - (n - 1)}}{n} \\
= \frac{1}{n}\sum_{i=0}^{n-1}p_{M-i}
$$

</div>
</div>

<div v-if="$slidev.nav.clicks === 4">

## EMA

![50%](https://images.jieyu.ai/images/2023/07/ema_formula.png)

</div>

<div v-if="$slidev.nav.clicks === 5">

## WMA

![50%](https://images.jieyu.ai/images/2023/07/wma_formula.png)

</div>


<!-- 

从数学上看
移动平均是卷积的一种
这个图显示了移动平均的计算过程
它是使用一个卷积核
作用在一个一维时间序列上
并对对应的窗口进行卷积的结果
在图中，卷积核是a   <mark>
m是要进行卷积的时间序列
第三行显示了卷积的中间结果
它的算法是这样相乘 <mark>
将窗口内的中间结果加总起来  <mark>
就得到了对应窗口的卷积
通过更换卷积核
就可以生成各种移动平均的算法

这种通过卷积来求移动平均的方法
我们在第9章的习题中
已经讲解过了
因此
移动平均可以看成信号处理的低通滤波器的例子
它过滤了高频噪声
反映出中长期低频趋势
很多人理解移动平均时
都把它当成是噪声过滤
信号平滑的一种方式
就是从这个角度出发的。

在金融领域
由于价格有时序特性
移动平均作为信息平滑工具的主要缺陷是
它是一种滞后指标
会使得波的相位落后于真实的波动几个周期
我们以SMA为例 <next>

在图中
红色的线是移动均线
蓝色的线是原始序列
可以看出，
这里均线的相位与原序列相比
晚了10个周期

移动平均有好几个种类
下面我们主要介绍常见的三种

<next>

SMA是简单移动平均的意思
它是前`n`个数据的未加权平均数。
这里是它的计算公式 <mark>
在talib中，我们这样计算SMA <code>
用到talib.SMA方法
注意talib的函数一般都要求是双精度数
也就是np.float64
这里我们计算的是5日均线

<slide>
<next>
EMA是指数移动平均的意思
指数平均是以指数式递减加权的移动平均
各数值的加权影响力随时间而指数式递减
越近期的数据加权影响力越重
但较旧的数据也给予一定的加权值。
这个图显示的是递归法计算EMA的公式。

加权的程度由常数𝛼决定
介乎0与1之间

我们看一下如何用talib来求EMA  <code>
我们要用到talib.EMA这个方法
这里我们计算了sma和ema
并且通过图形来进行一个对比

这里红色的线是MA
绿色的线是EMA
蓝色的线是收盘价
可以看出
由于近期的数据权重更大
所以ema比sma下降的更快
包含了更多近期的信息
但是
它仍然是滞后指标，

<slide>
<next>
WMA是加权移动平均
（weighted moving average，WMA），
它类似于EMA，只不过计算权重的方法不一样

<code>
这是三种均线计算方法
最终结果的一个对照图

与EMA不同的是
在t0日的权重为0
而在EMA中
t0日的权重仍然大于0
只不过逼近于0。

除了这些算法之外
还有一些比较新颖的
比如分形自适应移动平均（FRAMA）
赫尔移动平均（Hull)等计算方法
不过它们应用的范围比较小众

实际上
在这里高深的数学技巧可能并不一定有用
既要去掉高频噪声
又要刻画出当前的微小变化
这似乎本身就是一个悖论。

在证券投资领域，
我们一定不能忽略移动平均本身的金融含义。
它实际上是`win`个周期以内，
持有该支证券的所有人的平均成本。
从这个角度上看，
我们就可以更好地理解
均线对证券价格的支撑和压制作用，
理解近期波动对持有人心理上的影响，
从而预测未来的方向。

以单一均线指标构建的交易策略有单均线策略和双均线策略。
单均线策略是指在当股价上穿均线时买入，
下穿均线时卖出

双均线策略通过由两根一快一慢的均线构成，
当快均线上穿慢均线时买入，
反之则卖出。

双均线策略是许多回测平台中必备的示例策略。
由于均线有一定的滞后性，
双均线策略多数情况下的表现都比较一般。
-->

---
layout: image-right
clicks: 1
image: https://i.ytimg.com/vi/Oi-6e6rWsUw/maxresdefault.jpg
---
# 布林带
<hr>

```python
import talib as ta
from talib import MA_Type

close = np.arange(20).astype(np.float64)
ub, mb, lb = ta.BBANDS(close, timeperiod = 5, matype=MA_Type.SMA)
ub, mb, lb
```


<!--
布林带属于波动类指标
它度量股价波动的可能程度
而不是预测方向

我们使用ta.BBANDS来计算布林带
它的第一个参数是收盘价
第二个参数 timeperiod 是中轨的周期
第三个参数用以指定均线的算法
函数的返回结果为一个三元组
依次是上轨、中轨和下轨

根据布林带的定义
中轨就是某个长度的均线
所以 timeperiod 参数指明了这个均线的窗口是多大
在代码中，我们使用的是5

上下轨的计算是在中轨的基础上
加上（或者减去）n个标准差
这个系数可以通过nbdevup和nbdevdn来指定
默认是2

matype参数指定均线的计算方式    <mark>
默认值为0
即使用简单移动平均
文档并没有说明
其它几种移动平均算法对应的传入值是多少
不过它提供了一个枚举类型
从中我们可以看出一些端倪
<code>
<run>
这段代码显示了MA_Type中包含的均线算法类型
从输出中我们可以看出
有这几种类型可以使用
其中就包含了我们刚刚使用过的SMA
MA_Type也几乎是talib中
定义的唯一一个有枚举性质的变量
<run>
现在我们运行下计算布林带的代码
从这个结果也可以看出
中轨确实就是收盘价的n日移动平均线
-->

---
layout: image-right
image: https://images.jieyu.ai/images/2023/07/gerald_appel.png
clicks: 2
---
# MACD
<hr>

<div v-if="$slidev.nav.clicks >= 0">

## 计算方法

1. *DIF = EMA<sub>(close,12)</sub> - EMA<sub>(close,26)</sub>*
   <br>
   <br>
</div>

<div v-if="$slidev.nav.clicks >= 1">

2. *DEA = EMA(DIF, 9)*
    <br><br>
</div>
<div v-if="$slidev.nav.clicks >= 2">

3. *MACD = DIF - DEA*<br><br>
</div>


<!--
MACD由 Gerald Appel创立于60年代
Appel是技术分析领域的传奇人物
他提出的MACD曾经是实战中最流行的技术指标之一
在国内也是如此

MACD是Moving Average Convergence Divergence的首字母简写 <type>
MACD的计算方法共有三步
先根据短、长窗口（12日/26日）的指数移动平均
求出差离值，记作DIF
再求出所谓信号线DEA
最后求DIF与DEA的差，也就是所谓的MACD
一般我们在绘图时
会将MACD绘制成柱状图

<code>
在图中，
柱状图是MACD，
当它大于零时，
市场走势较强，
反之则走势较弱。
当柱状图由正转负时，
也正好就是所谓快线（DEA）下穿慢线（DIF）之时
反之亦然

在了解了原理之后
我们可以通过下面的代码来实现macd <mark>
这里的代码刚好对应前面的三个步骤
就不一一解释了

也可以用talib.MACD来实现
在使用talib.MACD时
我们需要传入4个参数
分别是收盘价
快线周期
慢线周期
和信号周期
这些参数的含义
大家可以在前面我们自己的实现中去找
最后返回的结果是一个三元组
第一个元素即为macd

在实际运用中
单个MACD的效果并不好
这可能归因于因子拥挤效应
-->

---
layout: statement
clicks: 2
---
# RSI
<br>

<div v-if = "$slidev.nav.clicks <= 1">

$$
    RSI = \frac{EMA_{(U,n)}}{EMA_{(U,n)} + EMA_{(D,n)}} * 100%
$$

</div>

<div v-if="$slidev.nav.clicks === 1">

```python
                                periods = 14
                                close_delta = np.diff(close)

                                up = close_delta.clip(lower=0)
                                down = -1 * close_delta.clip(upper=0)

                                # Use exponential moving average
                                ma_up = up.ewm(com = periods - 1, adjust=True, min_periods = periods).mean()
                                ma_down = down.ewm(com = periods - 1, adjust=True, min_periods = periods).mean()
                                    
                                rsi = ma_up / (ma_up + ma_down)
```
</div>

<!--
RSI是Welles Wilder于1978年提出的另一个技术指标
发表在《Commodities》（现为《 Futures》）杂志上

RSI是一个振荡指标
它在0到100之间摆动
这个特别适合机器学习
这是它的计算公式

我们通过伪码来看这个公式应该如何实现
首先我们计算出每日涨跌幅
记为close_delta
然后分别求出up和down数组
注意这里我们使用了clip函数
不熟悉的同学可以复习下第9章

然后我们通过ema的方法求出上涨期和下跌期的ema
最后相除就得到了rsi

RSI的本质上反映了标的在区间内的赚钱效应
与sortino指标有一定的相似性
当区间内价格全部为下跌时
RSI与sortino一样
取值全为零
但当区间内价格全部为上涨时
RSI会取值100
但sortino则变为正无穷
这是他们俩的区别
也就是取值范围不一样

在talib中，我们这样计算RSI
<code>

它有两个参数
一个是收盘价序列
另一个是周期长度，缺省是14
国内券商给的短线周期一般是6
长线周期一般是12
如果我们只计算一个RSI指标
最好把周期就定为6
这样好跟多数投资都保持一致
<slide>
-->
---
layout: statement
---
# RSI
<hr>

![R50](https://images.jieyu.ai/images/2023/06/rsi_bottom.png)
<br>
1. 不同的标的，同一标的不同阶段，其投资者对亏损和盈利的忍受程度各异
2. 同样的RSI极大值，可以对应不同的证券价格，对RSI极小值也是如此。
3. 上一个波峰时RSI打到的高水位（极大值），很可能是下一次RSI的高水位
4. 上一个谷底时RSI打到的低水位（极小值），很可能是下一次RSI的低水位。

<!--

一般认为
作为一个震荡指标
应该把RSI大于70当成超买（即此时应该卖出）
小于30当成超卖（即此时应该买入）
这些都是来自于早期
公式刚发表后的形成的刻板之见
在当时还比较有效
但现在大量数据统计表明已经无效
在量化交易中，我们应该使用“智能”RSI
这也是许多行情软件现在推出的收费指标

首先，不同的标的
或者同一标的不同时期
其投资者对亏损和盈利的忍受程度是不一样的
比如
持有茅台的投资者
他们换手周期一般是好几个季度
有的甚至长达数年
在这个期间里
尽管波动巨大
他们也不在意
也就是对亏损的忍耐
对盈利的预期
都要比其它品种的投资者更高一些

其次
同一支标的
在上涨期
rsi很可能一再突破前高
也就是大家对盈利的预期看得更高
而对风险则没那么担心
在下跌阶段
往往RSI还未到前高
就开始下跌
也就是大家对盈利的预期更低
对风险看得更重一些
如果你对这段话不太理解
请回顾RSI的计算公式
特别是我关于rsi本质上是反映投资者赚钱效应的说法

因此在量化中
我们不能刻舟求剑
而是应该给每个品种都计算自己的RSI水位
并且根据不同的阶段
随时更新RSI的水位
能进行大量的计算
这也正是量化的优势

再次，注意rsi的极值与股价极值存在对应关系
这个关系如右图所示
rsi的极值点，往往也是股价的极值点

从右图可以看出
每次发生向上反转时的RSI
波动在16到25之间
这个幅度还比较宽
但与前一次RSI极值比
则相差小很多

注意看，这里形成第一个低点
接近着在这里
当rsi逼近前低时
发生了反转
在这里（第3个）也是
只不过反转失败
或者说这里只有一个小周期的反转
这里（第4个）rsi再次逼近前低
这次反转时间长一点
这里（第5个）反转时长和幅度都够）
这里（第6个）只有一个小周期的反转
但第7个，这里的反转时长和幅度还可以

所以，这里给我们一个提示
如果我们能计算出上一个阶段rsi的极小值
那么当下次RSI向下逼近这个极小值时
有可能股价就会反转了
反之，对于上涨也是一样
当rsi逼近上次的极大值
有可能股价就会下跌
这样一来
我们就不再是锁定一个固定的RSI值
而是可以根据当前行情
随时更新RSI的高低水位
对可能发生的反转作出预判

关于如何计算上一期RSI的极值
这是一个技术形态分析问题
我们会在第13课讲到
-->
---
layout: image-right
image: https://images.jieyu.ai/images/2023/07/joseph_granville.jpg
clicks: 1
---
# OBV (on-balance volume)
<hr>

<div v-if="$slidev.nav.clicks === 0">

```python
    import talib as ta

    ta.OBV(close, volume)
```
</div>

<div v-if="$slidev.nav.clicks === 1">

## 归一化OBV
```python
def onbalance_volume(bars: BarsArray)->np.array:
    close = bars["close"]
    volume = bars["volume"]

    signs = np.sign(np.diff(close))

    # OBV[0]值的计算依赖于前一日的收盘价，按talib，这里也取vol[0]
    obv = np.concatenate(([volume[0]], volume[1:] * signs ))
    return obv / volume.sum()
```
</div>

<!--
前述所有指标都只考虑了价格因素
但是
单纯的价格是容易被操纵的

有时会给出虚假的信号
技术分析师约瑟夫·格朗维尔（Joseph Granville）于1963年
从成交量的角度入手
指出了推动价格变动的另一个维度
就是OBV

他把OBV比作被紧紧缠住的弹簧
如果成交量发生了剧烈的变化
而价格在短期内没有反应这种变化
最终也必然发生相应的调整
就象弹簧一样

OBV是一个提前指标
某种程度上反应了大众投资者的情绪
同时它完全脱离了价格维度
因此如果我们把它用作一个因子
它将是与其它因子正交的
这也是我们推荐它的一个原因

根据定义计算OBV比较简单
这段代码是使用talib求OBV的
需要传入收盘价和成交量序列
它的返回结果没有归一化
不便于在不同的时期、不同的标的之间进行比较
这里我们随机挑一支个股看一下
OBV的计算结果
所以
在这里我们给出一个进行了归一化的OBV

二者结果是一致的
只不过我们的版本进行了缩放
使之被压缩到了[-1,1]这个区间内。

这里就不给出类似传统教科书里那些OBV的指标用法了
我的建议是
把它当成另外一个因子
然后通过机器学习来使用它
单单使用这一个指标
可能效果不算好
但结合其它因子来看
可能会增强策略的稳健性
-->

---
layout: statement
clicks: 3
---

<div v-if="$slidev.nav.clicks===0">

# 模式识别
<hr>

![R33](https://images.jieyu.ai/images/2023/07/top_patterns.png)
## 函数调用惯例
## 模式检测演示
## 如何突破talib限制

</div>

<div v-if="$slidev.nav.clicks===1">

# 模式识别
<hr>

![50%](https://images.jieyu.ai/images/2023/07/the_pattern_site.png)


</div>

<div v-if="$slidev.nav.clicks===2">

# 模式识别
<hr>

![50%](https://images.jieyu.ai/images/2023/07/upward_breakout_rank.png)

</div>

<div v-if="$slidev.nav.clicks===3">

# 模式识别
<hr>

![R33](https://images.jieyu.ai/images/2023/07/top_patterns.png)
<h2 style="opacity:0.3">函数调用惯例</h2>
<h2 style="opacity:0.3">模式检测演示</h2>

## 如何突破talib限制

</div>

<!--
talib的一个重要功能
就是提供了模式识别函数
所谓模式识别
就是从若干根k线组合中
找出能预测未来一段时间趋势的特定形态
就象右边这个图一样
这些连续出现的k线
与前面的k线一起
以及放在大的上涨或者下跌的阶段中
就构成了反转或者中继模式
某种程度上
就能预测未来

关于什么是模式
甚至更基础的K线形态
我们在这里不进行详细说明
大家可以在这个网站里找到 <type>
# www.incrediblecharts.com
现在就带大家看一下  <browser>
这里它详细介绍k线的构成
特别是关于k线的一些英文术语
可以在这里找到
我们后面调用talib的方法时
需要根据这些术语
找到对应的方法

<browser>
这里是它对模式有效性的一个排序
这里列举出来的模式
一般都在talib中能找到对应的方法检测

关于技术形态分析
它属于择时的范畴
在量化领域
可能理论上研究不多
但实际上受众还是挺广泛的
有一些研究者也一直在跟踪这个领域 <next>

如果你感兴趣可以看看
thepatternsite这个网站
他们一直保持着对模式识别的研究
并且对模式的有效性进行了数据分析

这个图就是thepatternsite的一个截图 <mark>
这个网站有一定的引用量和访问量

<next>
这个图就是他们给各种模式有效性给出的一个排名
不过，它这里列出的模式
多数比较难用talib来进行识别
因为talib只能识别较短周期的一些模式
对于更长周期的模式识别
比如W底、圆弧底 <mark>
我们将在第13课
技术形态分析中为大家介绍怎么识别


talib提供了近60种模式的识别
在talib中，所有的模式识别函数
都是以CDL开头       <type>
方便大家记忆
这是英文candlestick的意思
这些函数的返回值一般为-100, 0和100
如果返回-100
则表明talib检测到了一个反模式
如果返回100
则表明talib检测到了指定的模式
比如我们调用红三兵
返回值100
就表明检测到了红三兵
返回0则表明不能确定

下面我们演示一下具体的调用方法
首先我们以一阳穿三线为例
这个函数名为 CDL3LINESSTRIKE <code>
根据宝科斯基（bulkowski）
下跌途中的一阳穿三阴有84%的概率实现反转
不过这一模式出现的概率并不高
这段代码
演示了平安银行在过去2000个交易日中进行搜索的情况
结果表明
上述pattern只出现了3次
但反转模式只成功了一次
所以
当检测到模式之后
还要结合其它情况进行判断

另外一个例子是三个白武士
也就是我们所说的底部红三兵
早期在纸媒出版物上
白色中空框表示上涨
黑色实心框表示下跌
所以三个白武士
与红三兵是一样的

具体代码就不分析了
感兴趣的同学可以自己看一下
基本上跟调用一阳穿三阴是一样的
它的输入参数也是OHLC
只需要把函数名换一下就行了

其它有一定成功率的模式还有
三支乌鸦
黄昏之星
Abandoned Baby
等等
我们就不一一演示了

<slide>
<next>
最后，介绍一点小知识
给大家开拓一下思路
我们知道
talib无法检测需要较长周期才能形成的模式
因为它只能检测3~5个bar以内的模式
因此
象圆弧底这样比较好的模式
talib是无法直接检测出来的
这种情况下
我们可以在更高的周期级别来进行替代检测
比如周线级别的早晨之星
在日线级别上看
可能就是圆弧底
因此
如果我们在周线级别检测到了早晨之星
也就相当于找到了日线上的圆弧底
只不过
如果这个圆弧底没有发生在一周的边界上
那么我们就可能漏掉这个信号
因此
进一步学习更高级的技术仍然是有必要的
-->

