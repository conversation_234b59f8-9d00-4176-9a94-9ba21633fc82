---
theme: ../theme
class: text-center
lineNumbers: false
aspectRatio: 16/9
transition: slide-left
title: 学员答疑课
layout: cover
---

---
layout: two-cols
---
# Asyncio发展历程
<hr>

<!--

from itertools import cycle

def endless():
    """Yields 9, 8, 7, 9, 8, 7, ..."""
    yield from cycle((9, 8, 7))

e = endless()

count = 0
for i in e:
    if count < 9:
        print(i, " ")
        count += 1
    else:
        break

-->
::left::
<v-clicks>

## Python 3.3
### yield表达式
## Python 3.4 
### yield from表达式
### asyncio进入标准库
### 通过coroutine装饰器和yield来实现
## Python 3.5 
### async/await引入，但仍不是保留字
</v-clicks>
::right::

<show at="1-2">

```python
def test():
    yield 1
    yield 2

gen = test()
next(gen) # 1
next(gen) # 2
next(gen) #StopIteration Error
```  
</show>

<show at="4">

```python
def inner():
    inner_result = yield 2
    print('inner', inner_result)
    return 3

def outer():
    yield 1
    val = yield from inner()
    print('outer', val)
    yield 4

gen = outer()
next(gen) #1
next(gen) #2
gen.send("abc")
# inner abc
# outer 3
# 4
```
</show>
<show at="5">

```python
import asyncio

@asyncio.coroutine
def py34_coro():
    """Generator-based coroutine"""
    s = yield from stuff()
    return s
```
</show>

<show at="6">
```python
async def py35_coro():
    """Native coroutine, modern syntax"""
    s = await stuff()
    return s

async def stuff():
    return 1
```
</show>

<show at ="7-8">

```python
async def inner():
    return 1

async def outer():
    await inner()
```
</show>

---
layout: two-cols
---
# Asyncio发展历程
<hr>

::left::

## Python 3.5
```python

import asyncio

async def compute(x, y):
    print("Compute %s + %s ..." % (x, y))
    await asyncio.sleep(1.0)
    return x + y

async def print_sum(x, y):
    result = await compute(x, y)
    print("%s + %s = %s" % (x, y, result))

loop = asyncio.get_event_loop()
loop.run_until_complete(print_sum(1, 2))
loop.close()

```
::right::

![](https://images.jieyu.ai/images/2023/09/asyncio-coro.png)

---
layout: two-cols
---
# Asyncio发展历程
<hr>

::left::

<v-clicks>

## Python 3.6 
### 引入了async generator
### ensure_future/run_coroutine_threadsafe
## Python 3.7
### async/await成为关键字
### 替换asyncio.coroutine()装饰器
### asyncio.run/create_task
## Python 3.8
### asyncio.run稳定
</v-clicks>
::right::

<show at="3">

```python
async def bar(u: int = 10):
    """Yield powers of 2."""
    i = 0
    while i < u:
        yield 2 ** i
        i += 1
        await asyncio.sleep(0.1)

async def foo():
    # This does *not* introduce concurrent execution
    # It is meant to show syntax only
    g = [i async for i in foo()]
    return g
```
</show>

---
layout: two-cols
---
# Asyncio发展历程
<hr>

<v-clicks>

## Python 3.9
### asyncio.to_thread
## Python 3.11
### TaskGroup
### timeout -> wait_for
</v-clicks>
