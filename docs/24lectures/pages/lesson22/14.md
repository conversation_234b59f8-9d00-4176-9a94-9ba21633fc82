---
layout: default
---
# 特点与优势
<hr>

<v-clicks>

## 基于前复权的动态复权机制
## 回测驱动与撮合分开
## 不过度设计，学习曲线简单，可拓展性强
## 已为分布式回测做好准备
## 回测转换实盘无须变更API
## 自动实现的T+1和涨跌停限制
## 更精准的成交量匹配（需要分钟线支持）
</v-clicks>

<!--
在大富翁框架里进行回测时
回测服务器使用的截止Order这一刻的前复权价格
我们在策略中获取数据
通过像get_bars这一类的方法
获取的也是到order这一刻的前复权价格
这与我们实盘的情况是完全一致的

# CLK1
回测驱动与撮合分开
这样无论策略使用什么样的数据源
回测服务器都可以加载最细粒度的数据源
一般来说
撮合占用资源不多
策略因为有大量计算
占资源更多
分离式架构允许我们水平切分股票池
为分布式回测做好准备

# CLK2
我们不做过度设计
能交给现成框架做的
都交给第三方做
API很简单
学习曲线也简单

# CLK3
分离式设计已经为分布式回测做好准备

# CLK4
回测与实盘之间转换，无须变更API
实际上我们回测服务器相当于一个实盘服务器
只不过它只有历史数据而已

# CLK5
自动实现涨跌停限制和T+1限制
无须策略关心
未来可实现出自动停损单功能

# CLK6

可以在分钟级上进行撮合，成交量匹配更精准
-->
