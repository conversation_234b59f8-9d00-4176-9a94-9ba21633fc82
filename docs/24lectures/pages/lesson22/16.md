---
layout: two-cols
clicks: 10
---
# 一个完整的策略示例
<hr>

::left::

<v-clicks>

## 定义策略类
## 初始化
## 实现predict方法
## 实例化策略类
## 通过await backtest来运行回测
## 通过plot_metrics来绘图
## 通过quantstats绘图（可选）

</v-clicks>
::right::
<show at="1-2">

```python
class CornerRSI(BaseStrategy):
    def __init__(self, url, **kwargs):
        super().__init__(url, **kwargs)
        self.is_backtest = kwargs.get("is_backtest", True)
        self.crsis = []
```
</show>

<show at="3">

```python
async def predict(self, frame: Frame, 
                  frame_type: FrameType, 
                  i: int, 
                  **kwargs):
    # 解析参数
    # 获取数据
    # 计算信号 
    # 发生指令

```
</show>
<show at="4">

```python
s = CornerRSI(
    url=cfg.backtest.url,
    is_backtest=True,
    start=datetime.date(2022, 1, 22),
    end=datetime.date(2023, 8, 3),
    frame_type=FrameType.DAY
)
```
</show>
<show at="5">

```python
await s.backtest(code = "000001.XSHE", 
                 hrsi=75,
                 lrsi=15,
                 baseline="000001.XSHE")
```
</show>
<show at="6">

```python
# 通过omicron绘图
await s.plot_metrics()
```
</show>
<show at="7">

```python
# 通过quantstats绘图
import quantstats as qs

df = pd.DataFrame(s.bills["assets"], 
                  columns=("frame", "assets"))
                  
df["frame"] = pd.to_datetime(df["frame"])
assets = df.set_index("frame").assets

qs.reports.html(assets, output="000001.html")
```
</show>

<!--
一些同学反映
希望能看到更多完整的策略示例
心情能够理解
主要是这类策略示例不太容易做
如果一个示例效果太差
显然大家也没心情去学
效果比较好的又不便展示
而且最终所有的alpha都将变成beta
这是我们之前讲过的一句话
所以
即使在写的时候
某个策略还行
也可能过一段时间
策略也就不行了
这点请大家理解
这里我们选择了一个基于conner's RSI
生成的策略
目的是给大家介绍下
在时序因子这一块
如何捕捉反转点
在A股肯定要赚波动的钱
因为我们一直在3000点保卫战
所以不做波动就没钱赚
之前也跟大家分享过
7月份最好的因子
仍然是反转和动量
小市值在中证1000中
也只排到第3
趋势捕捉不算难
截面动量因子和反转因子源码到处都是
大家自己写也很容易
从时序的角度来看
捕捉动量、特别是反转比较难
这里介绍conner's RSI
以及之前我们介绍的许多技术形态分析
统计的方法都用上
也许大家自己可以发现更好的策略

这里我们还是以演示策略的编写方法为主
大家对策略本身期望不要太高

从方法上看，
使用大富翁框架编写策略
主要是这样几步

# CLK1

-->
