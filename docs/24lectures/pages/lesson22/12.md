---
layout: two-cols
clicks: 5
---
# 回测与实盘的差异
<hr>

::left::

<show at="0">

## 1. 信号闪烁
</show>

<show at="2">

## 1. 信号闪烁
## 2. 先止损还是先止盈？
</show>

<show at="3">

## 1. 信号闪烁
## 2. 先止损还是先止盈
## 3. 冲击成本
</show>

<show at="4">

## 1. 信号闪烁
## 2. 先止损还是先止盈
## 3. 冲击成本
## 4. 不可能成交的价格
</show>

<show at="5">

## 1. 信号闪烁
## 2. 先止损还是先止盈
## 3. 冲击成本
## 4. 不可能成交的价格
## 5. 撮合问题
</show>

::right::

<show at="0">

```python{all}{maxHeight:'400px'}
# 示例7
from coursea import *
from omicron.strategy.base import BaseStrategy

class CrossStrategy(BaseStrategy):
    def predict(self, frame: Frame, 
                frame_type: FrameType, 
                i: int, **kwargs):
        bars = await Stock.get_bars(self.sec, 27, 
                                    frame_type, end=frame)
        close = bars["close"]
        ma = moving_average(bars["close"], kwargs.get("win"))

        close = array_math_round(close, 2)
        ma = array_math_round(ma, 2)

        if close[-1] > ma[-1] and close[-2] < ma[-2]:
            self.buy()

    start = datetime.date(2023, 1, 4)
    end = datetime.date(2023, 4, 14)
    gs = GridStrategy(
        cfg.backtest.url,
        "002344.XSHE",
        4.63,
        100_000,
        interval = 0.02,
        start=start,
        end=end,
        frame_type=FrameType.DAY,
        baseline = "002344.XSHE"
    )

await gs.backtest()
await gs.plot_metrics()
```
</show>

<show at="1">

![](https://images.jieyu.ai/images/2023/08/lesson22-flashing-signals.png)
</show>

<show at="2">

```python
# 示例8
class DummyStrategy(bt.Strategy):
    def next(self):
        if True:
            orders = self.buy_bracket(price=13.5, 
                                      limitprice=14., 
                                      stopprice=13.)

def buy(self, data=None,
        size=None, price=None, plimit=None,
        exectype=None, valid=None, tradeid=0, oco=None,
        trailamount=None, trailpercent=None,
        parent=None, transmit=True,
        **kwargs):
```
</show>


<!--
信号闪烁是指算法发出了不稳定的交易信号
这种情况多在实盘中出现
但在回测中一般则不会发生
如果是在T+0交易中
出现这种情况
程序会在极短的时间之内
反复多次进行开平仓操作
这是非常危险的
如果不立即进行应急处理
程序很可能一直进行这种不合理的操作
产生大量的交易手续费成本和滑点成本
造成交易事故
今年就有这样一个教训惨痛的例子
有人因为上游推送的数据错误
导致期货交易在半小时内反复开仓和撤单
最终产生了400多万的高额手续费


信号闪烁主要由以下两个原因造成：第一
所使用的判断条件不稳定
即判断条件时而成立时而不成立
比如
如果我们的策略是收盘价上穿均线时立即买入
下穿时卖出
那么 这很可能引起信号闪烁

我们来讨论一下这段代码
它是使用Omicron的策略框架编写的均线突破策略
如果我们要把它加入实盘
如果采用每分钟检查一次
即每分钟调用一次predict方法的话
则有可能出现在当天某些时间点上
现价穿越均线的情况
导致发出交易信号
但如果最终收盘时股价回落
则股价上破均线的条件不成立
此时我们来比较实盘与回测结果
就会发现信号不一致。

# CLK1

右图模拟了实盘中可能出现的各种情况
如果以股价上穿均线作为买入条件
由于当天成交价格的波动
就造成了信号反复触发的情况
生成这个图的代码在notebook中
请大家自行查看和练习

因此
同样的代码
在回测时可能是稳定的
但在实盘则可能出现信号闪烁

这是因为
代码在回测中的正确性
是回测框架特殊的调用条件和撮合机制决定的
当这段代码转为实盘运行后
调用条件和对数据的解释发生了变化
从而导致代码的运行情况
与我们的预期就发生了很大的不一致

解决这个问题的关键之一
是不要使用实时价格参与信号判断
而一定只使用已收盘的价格来参与信号判断
比如
在上穿均线的策略中
这个策略是没必要使用实时价格的
一天运行一次就好了

至于前面提到的期货交易手续费问题
这个则需要在实盘交易接口处有异常行为拦截手段
有一些券商采购的QMT软件
是实现了这种拦截的

还有一种情况
做期货的同学可能会遇到
就是信号虽然确定
但在某些情况下
开仓和平仓条件同时得到了满足
此时
程序可能先执行开仓交易
又立即把刚开的仓位平掉
接下来推送过来一个新Tick
开仓和平仓条件又同时得到了满足
程序会再次开仓然后平仓
如此反复
直到价格变动到不再满足开仓平仓条件
才会停止下来


# CLK2
如果一天之内
止损和止盈同时出现的情况
会怎么样
这是一个回测中会遇到
而在实盘中不会出现的场景
比如
在一个大幅低开
随后上涨的场景下
在实盘中可能先触发止损
从而不再有止盈的机会
而在回测中
是先止损还是先止盈
如果没有框架的支持
则只取决于我们代码的顺序


在第5行代码中
我们通过buy_bracket下了一个组合订单
这是我们上一课讲过的
主订单13.5
止盈14元 13元止损
如果接下来有一个bar
它的OHLC是这样
bar(13.6 14.5 12.8 13.5)
我们发现
止损单和止盈单都满足了触发条件
究竟谁先执行呢？


backtrader是按订单顺序来执行的
当创建bracket订单时
主订单最先创建
其次是止损订单
然后是止盈订单
因此
当一个bar能同时满足止损、止盈条件时
backtrader会先进行止损
这是一个更保守的策略
用在回测中是恰当的


但不是所有的回测框架都这么做
也可能backtrader会在后面的实现中变更这一行为
比如，trading view是一个热门的回测平台
它就会做一个危险的假设
它认为下一个bar的最高价和最低价中
谁最接近上一个收盘价
则谁先到
然后它根据这个假设来执行止损止盈


实际上
在只有日线数据的情况下
backtrader无论怎么选
都很可能是错误的
要解决这个问题
必须引入至少是细至分钟线粒度
才有可能一定程度上
决定真实止损、止盈的顺序
如果我们使用backtrader
那么
你必须添加一个分钟线粒度的数据
供buy/sell/buy_bracket/sell_bracket方法来执行

这里9-14行

是buy的方法签名
对buy_bracket也基本适用
这里我们要注意
可以通过data参数传入一个撮合数据源
它应该是更细粒度的
比如是分钟级的
在这个级别上
止损和止盈同时发生的概率就很小了
此时backtrader才能做出正确的判断

# CLK3

回测使用的是静态数据
比如
假设我们用backtrader回测并使用了1分钟数据
我们下了一个市价单
如果不考虑成交量匹配
这个市价单几乎一定是在下一分钟内完成匹配
但在实盘中
我们获取数据会有延迟
计算信号会有延迟
下单传输到券商排队也会有延时
这样有可能匹配用的数据
在事后看来是属于T+2的
这样导致价格偏离
为了减轻这一类影响
我们一般需要增加滑点


# CLK4
在套利策略中
回测总是能同时在两个方向上都成交
但在实盘中
会存在很多价差抢不到的情况
或者只抢到了不利于自己的方向先成交
再补另一个方向时
滑点可能比预期的要大


价格真空情况
如果在日线级别上进行回测撮合时
如果我们下限价买入单
即使该限价单在下一个bar中的最低价能够打到
但实际上在最低价上成交量严重不足
此时即使回测框架执行了订单
在实盘中也是无效的
即使是在分钟级别上进行回测撮合
也不能完全避免上述情况（特别是个别乌龙指发生时
会单笔打出一个最高价或者最低价）
但要可靠得多

这些都是不可能成交的价格的几个例子
# CLK5

回测框架必须以有限的知识
来决定某个订单是否能成交
它们一般主要按价格进行匹配
有的会辅以成交量
比如
某一天某支股票共成交1千万
就最多把这1千万都分配给你

我们在backtrader那一章中
讨论过这个方法的问题
如果你使用日线数据来进行回测的话
这1000万成交量是分布在最高价与最低价之间的
至少，超过你的委买价的那部分成交量
就不应该分配给你

backtrader的一个改进版本是
使用你的委买价在最低、最高价之间的
位置百分比
来分配当天的总成交量
这个方法也有问题
因为我们并不知道当天的成交分布

使用tick级和分钟级的数据来进行回测会精准很多
它将从发出order的时间起
只匹配满足你的委托价的那部分成交量

但是
这样仍然有问题
撮合本质上是看order book的
也许在卖一的位置上有天量的卖单
你的策略也发出了买入信号
但当天市场成交量很小
这样就会导致你的策略
并不能在你想要的时间和价格上拿到足够多的筹码
但在实盘中发出的委买很可能都是可以成交的
-->
