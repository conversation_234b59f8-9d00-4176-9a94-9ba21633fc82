---
layout: default
clicks: 1
---
# 交易规则
<hr>

## T+1交易
## 涨跌停限制


<!--
然后我们在回测当中
还可能遇到的一个问题
就是回测框架有没有严格地遵循A股的交易规则的问题
比如
T+1交易制度是A股独有的制度
因此国外生产的回测框架
比如backtrader
一般都不会遵守这些规则限制
因为在其它市场上
一般不存在这些限制
在回测中
如果我们使用的是日线以上级别的交易信号
那么就不会受T+1的影响
但如果我们要做日线及以下级别的交易
那么必须在卖出信号发出之后
自行判断是否满足了T+1的限制
如果你的信号是在日内级别发出的
就更麻烦
比如，如果是30分钟的级别
11：30那个bar买入的
要等4个bar过去之后的那个bar才能卖出
如果是14:30那个bar买入的
则只要等2个bar就可以了
这方面的时间运算很多库都没有提供
大家可以借鉴omicron的timeframe模块来写

# CLK1

涨跌停限制也是A股独有的机制
一些回测框架
比如backtrader
不会限制在涨停板买入
也不会限制在跌停板上卖出
但涨停板上买入、跌停板上卖出
会对策略业绩有比较显著的向上修正效果
比方说
如果我们在涨停板上买入了股票
那么第二天平均会有1.8%的涨幅
这也是动量效应的一种
这个数据大家可以在行情软件里看到
是涨停指数
对跌停也是一样
跌停的股票
多数后面还有连续下跌的可能
所以
我们回测必须避免在涨停上买入
在跌停板上卖出
这样的结果才更贴近实盘


在backtrader中
要实现这一限制
必须同时引入三个数据流
即未复权收盘信息（分钟级）
涨停限价、跌停限价
为什么要有分钟级数据？
其实如何有tick级数据更好
有一些框架只使用日线级别的数据流
来实现涨跌停限制
这是错误的
除非当天是一字涨停
那么回测框架就可以简单地做出决定
当天不允许该股的成交
但如果不是一字涨停
那么根据撮合规则
价高者优先
所以在没涨停之前
如果你打出市价买入单
仍然是会有成交的
这是实盘的情况
因此我们回测框架必须这样实现
框架需要在股价未涨停的期间
允许策略买入
这是有正当性的一个场景
另外就是涨停板打开的情况怎么处理
这个也是框架比较难实现的地方
因为我们在实盘中
是可以撤销指令的
但在回测时比较难这么做
所以
在考虑回测与实盘的差异时
这些地方是我们需要考虑的

有的人会自己计算涨跌停价格
实际上这种做法也是错误的
关于涨跌停价格
必须从数据源获取
而不应该自行计算
因为在A股
一支股票的历史涨跌停限价
是无法计算的
有几个原因
第一，一支股票可能在一段时间内是ST
这样它的涨跌停限价会与正常时的不一样
所以自己计算涨跌停价时
需要先有历史的ST数据
另外，ST的涨跌停限价
之前是5%
现在好象也有到20%的？
基本不碰ST
所以对这个信息不太了解
但是ST的涨跌停限价在历史上是可能变动的
大家要考虑这一点
另外
在除权前后的涨跌停限价
也是无法仅从前一日收盘价来计算的
必须要有复权因子才行
所以 建议我们不要自己去算
即使是我们拿不到这些数据
也不要去算
而是应该先去解决数据源的问题
因为数据源如果连这些信息都不能提供
他们对业务的理解可能是有问题的
他的数据是否准确也值得考虑
毕竟他们的专业性是有问题的

我们再回到backtrader中
我们自己怎么做涨跌停限制
在我们拿到
未复权收盘信息（分钟级）
涨停限价、跌停限价
这样三个数据流之后
通过这三个数据流
在下单前
如果当前bar的最低价等于涨停限价
则不允许买入
当前价的最高价等于跌停价
则不允许卖出
另外在这个地方我们要注意
在判断是否等于涨跌停价时
我们只能使用动态前复权数据
或者未复权数据
并且要对数据进行到分的取整
否则比较是没有意义的

-->
