---
layout: default
clicks: 2
---
# 参数优化
<hr>

<show at="0-1">

```python
# 示例3
from itertools import product

for a, b in product((3, 5), (0.2, 0.5)):
    print(a, b)
```

<Loc at="1" fc="white" w="100%" top="50%" left="-40%">

```
3 0.2
3 0.5
5 0.2
5 0.5
```
</Loc>
</show>

<show at="2">

```python {all}{maxHeight:'400px'}
# 示例4

import asyncio

hrsis = (60, 70)
lrsis = (15, 20, 25)

strategies = []
tasks = []

for hrsi, lrsi in product(hrsis, lrsis):
    s = CornerRSI(
    url=cfg.backtest.url,
    is_backtest=True,
    start=datetime.date(2023, 1, 4),
    end=datetime.date(2023, 8, 3),
    frame_type=FrameType.DAY
)

    strategies.append(s)
    tasks.append(asyncio.create_task(s.backtest(code = "000001.XSHE", stop_on_error=False, hrsi=hrsi, lrsi=lrsi,baseline="000001.XSHE")))

await asyncio.gather(*tasks)
# do the report 
for s in strategies:
    await s.plot_metrics()
```
</show>

<!--
不过，上述代码虽然是异步并发，但它只能使用一个进程，而且每一个predict方法都要从influxdb中读取数据，而backtrader是多进程并发执行的，一次性加载数据，速度会快不少。不过, haste makes waste。与大富翁框架相比，backtrader更适合作为策略探索工具，进行初步研究。
-->
