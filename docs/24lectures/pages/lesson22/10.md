---
layout: default
clicks: 3
---
# 过度拟合
<hr>

<show at="0">

![50%](https://images.jieyu.ai/images/2023/08/lesson22-overfitting.png)
</show>

<show at="1">

![50%](https://images.jieyu.ai/images/2023/08/lesson22-overfit-polyfit.png)
</show>

<show at="2">

## 防止过拟合
### 使用带外数据检验
</show>

<show at="3">

## 防止过拟合
### 使用带外数据检验
### 参数平原
</show>


<!--
另外一个回测偏差就是过拟合
只要系统设计得足够复杂、参数足够多
这个系统就一定能对过去发生的一切都自圆其说
但它却无法预测未来

这个图就是在机器学习中
讲解过拟合时
常常会用到的一张图
一个本来只是一个简单的二分类数据
经过我们不断优化
最终从中找出一只狗出来

这已经完全偏离了数据本身包含的知识
也就完全不能用以预测未来

# CLK1
我们在第12章中
在使用np.polyfit进行曲线拟合时
提到过deg参数（多项式幂次）一般使用1~2次就够了
使用的次数越高
拟合残差就越小
但这样拟合出来的结果
完全无法预测未来的趋势

在这个例子中
我们使用了17次方来进行拟合
几乎解释了过去的每一个数据
但是
这样的曲线其实是不包含任何规律的
如果再来一个新的数据
你会发现它的拟合参数又会发生一些改变
所以我们没办法预测未来
因为这个方程一直在变

但如果我们使用二次项拟合
它也能较好地拟合过去的数据
尽管残差大一点
但只要数据是符合这个规律的
新一期的数据即使并不完美
我们的二次项拟合参数可能仍然是稳定的
所以就可以用来预测未来的趋势
并且相信
即使未来的某些数据点
对预测趋势线出现了较大的偏离
但它们总会向这条趋势线进行回归


在实际回测中
产生过拟合的主要原因可能来自于调优
当我们有了一个初始的模型之后
从收益图、买卖点上看到哪个地方不够优化
然后就去猜想如何纠正它
比如加入一个在该点适用的指标等等
或者让backtrader对已有的参数
在参数空间里进行搜索
这样很可能会得到一个看上去很理想的结果
# CLK2

防止过拟合
并不是要完全取消参数搜索与优化
我们可以通过使用带外数据来防止过拟合
比如
如果我们要回测过去两年的数据
我们可以把最后一个季度视为带外数据
前7个季度作为训练数据
参数搜索和优化只在前7个季度上进行测试
这样得到的回测结果
再在最后一个季度上进行回测
如果指标与之前差异显著
则有可能出现了过拟合


# CLK3
参数平原（parameter surface）是检查过拟合的另一个方案
将策略表现的指标（比如收益率或者最大回撤）画成参数的函数
对于一个好的策略
在微调参数取值时
它的表现应该比较稳定
参数平原应该十分光滑
比如
假设一个策略中使用了6日RSI
则当我们使用5日或者7日RSI指标时
策略的表现应该与6日差不太多
如果策略的参数平原非常不规则
则意味着参数背后没有合理的业务逻辑
而更多的是数据挖掘的结果
这时就要小心


-->
