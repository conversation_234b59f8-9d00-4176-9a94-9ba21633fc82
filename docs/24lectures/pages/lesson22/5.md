---
layout: default
clicks: 2
---
# 前视偏差 - 偷价
<hr>

## 利用过去的价格进行交易
### 开盘价买入
### 以峰谷价卖出（买入）

<Box at="1" left="3%" top="28%"/>

<Box at="2" left="3%" top="36%" w="300px"/>


<!--
偷价行为是指利用过去的价格去交易
这样的场景下可能出现偷价：

# CLK1
如果最高价大于某个固定价位即以开盘价买入
在实盘中
当最高价高于某个价位的条件得到满足时
价格已经高于开盘价了
不可能再返回到过去买入
不过在回测中
如果使用的框架是backtrader中
这样的偷价行为不容易发生（除非使用self.data.high[1])
因为在backtrader中
订单的执行都发生在下一个bar中
cheat-on-close也只能用来以当前bar的收盘价买入
在其它框架下
需要了解框架实现原理
再行判断


# CLK2

对k线进行峰谷检测
再以峰、谷价卖出（买入）
这也是偷价
如果回测框架是backtrader
这样的偷价不容易发生
但其它框架下
需要了解框架实现原理
再行判断
比如我们有研究员会使用dataframe来计算交易信息
手动写的框架
这是有可能的


-->
