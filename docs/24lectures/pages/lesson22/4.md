---
layout: two-cols
clicks: 5
---
# 前视偏差 - 引用（编码）错误
<hr>

::left::

<show at="0">

## 编码错误
</show>

<show at="1">

## 编码错误
## 策略初始化
</show>

<show at="2-3">

## 编码错误
## 策略初始化
### 聚合统计函数
</show>

<show at="4">

## 编码错误
## 策略初始化
### 聚合统计函数
### 波形分析函数
</show>

<show at="5">

## 编码错误
## 策略初始化
### 聚合统计函数
### 波形分析函数
### FFT/Wavelet/Kalman
</show>

::right::

<show at="0">

```python
self.data.close[1]
```
</show>

<show at="1-2">

```python {all|all|19,22}{maxHeight:'400px'}
# 示例1
import backtrader as bt
import backtrader.feeds as btfeeds
from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, 
    ft: FrameType = FrameType.DAY, 
    end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")


class Day1Strategy(bt.Strategy):
    def __init__(self):
        self.max = round(max(self.data.close), 2)
        
    def next(self):
        print(len(self), self.max, round(self.data.close[0], 2))

        
cerebro = bt.Cerebro()

end = datetime.date(2023, 8, 29)
data = await get_sample_feed("000001.XSHE", 5, end=end)
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

results = cerebro.run()
```
</show>

<show at="3">

```
bar max     close[0]
1   11.52   11.25
2   11.52   11.13
3   11.52   11.23
4   11.52   11.52
5   11.52   11.31
```
</show>

<show at="4">

![](https://images.jieyu.ai/images/2023/08/lesson22-zigzag.png)
</show>


<!--
现在我们来看前视偏差
第一类错误
主要是编码错误引起的

我们举一个backtrader中的例子
在Line派生的对象中
比如Strategy中
我们可以通过self.data.close[0]
来读取最新的收盘价
-1则代表上一期的收盘价
也就是说
正常情况下
Line对象的下标应该是小于等于0的整数
但backtrader并未阻止我们
使用self.data.close[1]
这样的方法来获取未来数据
甚至它都不愿意对此发出一个警告
这种机制上的漏洞迟早有一天会带来问题
比如
有可能我们本来是希望使用
self.data.close[-1]的
但误输入成为self.data.close[1]
这样的bug一般会使得策略的指标变得好看
但是，除非指标好看到荒谬
我们都很难有机会发现它
因为我们会倾向于相信对自己有利的结论

在我个人的过去的经验当中
以及其它一些投资者
都会有这样的经验
在我第一次尝试用CNN网络来训练一个量化模型时
就曾经不小心把跟收益相关的一个数据放到了train data中
这样训练出来的结果
理所当然就会是非常好的
当时还感叹
炒股还是得用上最新的技术
不过很快我就发现了问题
不仅用CNN来做端到端的投资不行
就是只用它来进行时间序列模式识别也是不行的
至少我们不能用生成的K线图来做输入
因为k线图不像人脸等这些图
它不满足平移不变性、旋转不变性和尺缩不变性等条件
而这也是CNN对数据集的要求

据《量化炼金术》的作者自述
他在做量化过程中，
也有95%以上的错误是由计算和编码错误引起的

可能有人要问
这样的错误发生的机会应该不多吧？
根据卡内基.梅隆CyLab的一项研究
商业软件一般有每千行20~30个bug
另有机构研究认为
开发人员把75%的时间用在查错上
多数策略研究员接受的软件工程训练
少于他们在数学、统计学、机器学习等方面接受的训练
缺少关于代码质量把控和单元测试的技巧
如果他们把策略回测看成软件测试
也不会让人感到奇怪
所以此类错误实际上是很容易发生的

# CLK1
第二类错误
就是使用了聚合统计函数
比如在策略初始化部分
backtrader允许我们访问全部数据
而不是象在next中那样
逐步揭示数据
我们来验证一下这个结论

# CLK2
在第19行
这里我们通过max函数
取了数据的最大值
然后我们在第22行
看看什么时候能访问到这个最大值
当然这是一个最简单的情况
真实情况要比这复杂很多
我们这里主要演示
backtrader并不能帮助我们防止这种错误

# CLK3
这是前面代码的输出
很显然
这个最大值是在第4个周期才出现的
但我们从第一个周期就能访问它了

除了聚合统计函数
一些算法也会引起未来函数
在第13章
我们介绍技术形态分析时
介绍了一个名为zigzag的库
用来寻找过去出现的k线上形态上的波峰与波谷
这是突破、支撑和压力线、波浪技术分析的基础
但是
如果我们象示例1一样
在backtrader的初始化部分去计算这些波峰与波谷的话
我们也是在使用未来函数
我们来看看为什么

# CLK4

让我们来看序号为2的波峰上
这个波峰对应的时间是2023年4月20
但只有到4月25日
我们才能检测到它
所以，如果我们在初始化函数中
进行波谷检测
并保存检测信息的话
我们会检测出所有这些波谷
但在next函数中
我们会在4月20日对应的那个周期
就得到当天是波峰的提示
这就形成了未来函数

如果大家有主观交易经验
有通达信公式编写经验
就会知道
通达信公式里的zigzag等几个函数
是提示有未来数据的
原理跟这里是一样的

# CLK5
另外，如果你利用了傅立叶变换、小波变换或卡尔曼滤波
也一定要小心
它们也是使用未来数据的源泉
-->
