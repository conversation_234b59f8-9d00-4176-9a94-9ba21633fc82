---
layout: toc
image: https://images.jieyu.ai/images/2023/08/lesson22-outline.png
---
#
<hr>

<Ellipse left="50%" top="15%" s="150"/>
<Ellipse left="50%" top="35%" s="150"/>
<Ellipse left="50%" top="45%" s="150"/>
<Ellipse left="50%" top="65%" s="150"/>
<Ellipse left="60%" top="25%" s="100"/>
<Ellipse left="60%" top="28%" s="100"/>
<Ellipse left="60%" top="31%" s="100"/>
<Ellipse left="60%" top="34%" s="100"/>
<Ellipse left="60%" top="37%" s="100"/>
<Ellipse left="60%" top="40%" s="100"/>
<Ellipse left="60%" top="45%" s="100"/>
<Ellipse left="60%" top="48%" s="100"/>

<Ellipse left="60%" top="55%" s="100"/>
<Ellipse left="60%" top="70%" s="100"/>
<Ellipse left="60%" top="80%" s="100"/>

<Ellipse left="30%" top="25%" s="150"/>
<Ellipse left="30%" top="40%" s="150"/>
<Ellipse left="30%" top="53%" s="150"/>
<Ellipse left="30%" top="65%" s="150"/>


<!--
今天的课程内容由两部分组成
我们先是介绍回测中可能遇到的陷阱
这里有一些你可能已经听说过
比如幸存者偏差、未来函数
不管你对回测陷阱了解多少
今天的课程都可能是你听过的
介绍最全面
分析最深入的课程之一
因为好多问题
是数据的收集、处理的过程中带来的
在系统实现上我们有时候不得不做某些tradeoff
而这些tradeoff很少被以文档的形式发布出来
所以，只有开发过系统的人
才知道这些tradeoff会怎样影响我们的回测

在这一部分
最重要的不是理论和概念
这些理论和概念本身并不难理解
重要的是
这些偏差
在实际工作中
究竟会以什么样的面貌出现？
我们又如何识别以及如何避免？

所以
这一部分的示例
可能看上去不起眼
但却非常重要


我们将介绍这样一些偏差

幸存者偏差
这是一种很难察觉的偏差
它跟我们的信念、视野有关
后面我们会举例说明

在前视偏差中
我们将分别介绍5种场景
即 引用（编码）错误
偷价
前复权
缺少PIT数据
跨周期数据对齐等

在交易规则方面
主要是一些框架未能完全实现A股的交易规则
特别是一些国外的框架

复权引起的问题比较多
我们会谈谈使用复权数据的必要性
前后复权引起的问题
学完这一部分
你可能才算真正理解了复权问题
也能理解我们从本课的第1部分
就强调复权问题的意义所在


我们还会介绍过拟合问题
做过人工智能的同学
对这一部分可能天生有很强的理解
最后我们会介绍回测与实盘的差异

在第二部分我们将介绍大富翁的回测框架
我们在6~8课
已经从零到1完整地实现过一个框架
并引入了大富翁回测框架
不过这一次我们会介绍的更详细一点
并且，在学完backtrader
了解了回测陷阱之后
我们再来看大富翁回测框架
是如何解决其中的一些问题的
那么未来你在选用回测框架
或者自己设计、修改一个回测框架时
就会有一个更清晰的概念
-->
