---
layout: default
---
# 前视偏差 - 前复权
<hr>


![50%](https://images.jieyu.ai/images/2023/08/lesson22-adjust-problem-1.png)


<!--
Look-ahead bias的另一个常见例子是使用了前复权数据
我们在第一章中讲过
前复权和后复权本身存在线性变换关系
所以
如果使用前复权数据会带来未来信息
那么使用后复权数据也同样是
但这个结论
根据回测框架实现机制的不同
有的框架下使用前复权没有问题
有的则会有问题
如果框架采用动态前复权
则在复权那一刻
要使用的数据都已经是过去
就不会引用前视偏差
但如果框架不支持动态前复权
复权的时候利用了全部数据
但在使用时
只使用了从开始起的部分数据
也就是复权的结束点
和使用的结束时间点不一致
这样就引入了前视偏差

假设我们使用的框架是backtrader
我们在启动cerebro之前
已经对数据分别进行了前复权和后复权
然后在策略的next方法中去使用它


为了演示效果突出一点
我们假设一段时间以来
价格没有波动
但是不断发生复权
图中的青绿色直线是收盘价
因此它将是一条直线

蓝色的是复权因子

红色是前复权

如果我们在策略的next方法中
来存取这些数据
就会出现这张图中显示的情况

黄色是后复权
如果我们在next方法中
假设当前已经访问到第6个bar
此时我们只使用到此为止的factors数据
和到此时为止的未复权数据
手动计算复权
得到的结果会是什么？

得到的结果会是图上的+号表示的各个点
它与黄色的后复权完全一致
也就是说
无论事后计算
还是事中计算
结果都一样
因此没有引入未来数据

但前复权不一样
如果我们在同样的时间点上
在next中使用未复权数据
和复权因子来进行计算
我们会得到图上的红色的点
它与红色的线并不重合
而是发生了向上的平移
结果不一样
唯一的解释就是
红色的线包含了未来数据
所以它的位置
也就是价格要比实时计算的低一些
这就是有人说前复权导致未来数据
实际上不是前复权本身的问题
而是我们如何复权和如何使用它的问题

我们还注意到
这两条线是相似图形
也就是如果我们利用红色的线来进行趋势预测
结果与盘中的动态前复权是一样的
但是绝对价格发生了变化
很多时候我们会利用绝对价格
所以这种情况下
前复权是不能使用的


这个示例也给出了动态复权的概念
动态复权就是在回测过程中
只利用到回测点的数据和复权因子
来进行复权
此时无论是前复权
还是后复权
都没有未来数据
在这种情况下
我们更推荐前复权
-->
