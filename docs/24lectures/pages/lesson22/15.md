---
layout: two-cols
clicks: 10
---
# 回顾与比较
<hr>

::left::

<v-clicks>

## 架构与风格
## 策略框架
## 数据和数据格式
## 跨周期数据
##
## 驱动模式与性能
## 回测报告
</v-clicks>

::right::

<show at="4">

```python{all}{maxHeight:'400px'}
# 示例1
from coursea import *
from omicron.strategy.base import BaseStrategy

await init()

class DummyStrategy(BaseStrategy):
    async def predict(self, frame: Frame, 
                      frame_type: FrameType, 
                      i: int, **kwargs):
        code = kwargs.get("code")
        daily = await Stock.get_bars(code, 
                                     5, 
                                     frame_type, 
                                     end=frame)
        
        weekend = tf.floor(frame, FrameType.WEEK)
        weekly = await Stock.get_bars(code, 
                                      1, 
                                      FrameType.WEEK, 
                                      end=weekend)
        
        print(i, frame, daily[-1]["frame"], weekly[-1]["frame"])
        
s = DummyStrategy(
    url=cfg.backtest.url,
    is_backtest=True,
    start=datetime.date(2023, 4, 22),
    end=datetime.date(2023, 8, 3),
    frame_type=FrameType.DAY,
)

await s.backtest(code = "000001.XSHE", stop_on_error=True)
```
</show>

<show at="5">

```
62 2023-07-25 2023-07-25T00:00:00 2023-07-21T00:00:00
63 2023-07-26 2023-07-26T00:00:00 2023-07-21T00:00:00
64 2023-07-27 2023-07-27T00:00:00 2023-07-21T00:00:00
65 2023-07-28 2023-07-28T00:00:00 2023-07-28T00:00:00
66 2023-07-31 2023-07-31T00:00:00 2023-07-28T00:00:00
67 2023-08-01 2023-08-01T00:00:00 2023-07-28T00:00:00
68 2023-08-02 2023-08-02T00:00:00 2023-07-28T00:00:00
69 2023-08-03 2023-08-03T00:00:00 2023-07-28T00:00:00
```
</show>

<show at="7">

![](https://images.jieyu.ai/images/2023/08/lesson23-payh.png)
</show>

<!--

既然都是回测框架
backtrader与大富翁
要解决的问题都是一样的
所以，
这一部分我们
将以对比对照的方式
对大富翁回测框架的特性
进行更深入的说明
这样大家也能更快熟悉大富翁框架
或者说
对任何类似的框架
都能更快上手
发现它们的优势与缺点

# CLK1

backtrader是一个单体式的架构，它支持多进程，每个进程都运行同样的代码，并且运行在同一台机器上。它在设计风格上，功能大而全，包括了对数据的格式封装、策略框架、指标计算和绘图等。

大富翁是一个分布式架构（但还未完全实现分布式回测），它的回测驱动与撮合服务是独立进程，可以部署在不同的机器上。大富翁在设计风格上，除非必要，不重新发明轮子。比如指标计上我们使用了empyrical，也提供了assets数据，用户可以通过quantstats来绘图。但是，大富翁也提供了自己的策略收益报告，该报告是基于交互式的。

大富翁是通过omicron和backtesting server来共同实现策略编写与回测的，安装好omicron之后，就有了策略基类、绘图函数、交易客户端等功能。backtesting server是一个独立的发行包，需要单独安装和管理。

两者架构的最大区别是，大富翁的回测是一个服务器，它可以有自己的数据和运行方式，与策略完全不相关，类似于一个真正的交易代理。策略可以运行在不同的周期上（比如日线），但回测服务器可以运行在更细粒度的周期上（比如tick或者分钟），这样就解决了上一章中讲到的许多问题（成交量匹配，T+1,涨跌停限制、复权分红和止盈止损顺序）。

# CLK2
要实现一个策略，在backtrader中，需要继承自backtrader.Strategy类，并实现初始化和next方法。此外，backtrader的策略基类还提供了生命期管理（比如有stop, start函数可以重载）订单函数和事件通知。

在大富翁中要实现一个策略，也需要继承自Strategy基类（但完全跳过这一部分，从traderclient开始，编码量也并不大），实现初始化方法和predict方法（类似于backtrader中的next)。

大富翁的订单函数没有backtrader丰富，主要是缺少target_order_*函数，以及buy_bracket, sell_bracket函数。*_bracket函数实现的功能将在后续版本中实现。

最新版本的回测服务器已经实现了事件通知，但omicron中的Strategy基类还未实现这一功能。大富翁的事件通知机制基于网络通信，因此要比backtrader强大很多。

# CLK3
backtrader读取来自于csv， dataframe等来源的数据，并将其转换为data feed数据格式。它的特点是实现了步进机制和特殊的切片语法。

大富翁回测框架对数据的要求来自两个方面。backtesting server和策略分别读取独立的数据源。backtesting server通过data feed接口读取数据，当前已实现的feed是zillionare feed，它通过omicron从influxdb中读取数据。用户可以拓展它从文件或者其它网络接口读取数据。

strategy中，用户自己处理数据来源，也对数据格式没有任何要求。一般而言，可以通过omicron的接口读取数据来编写策略，但也可以完全使用其它数据源。

# CLK4
backtrader实现了漂亮的跨周期数据同步。大富翁框架在策略端基本不直接处理数据，所以并没有直接进行跨周期数据同步。但是，omicron的timeframe模块提供了相应的功能

在这段代码中，我们重点关注
主循环是日线级别的
这是在第30行指定的
但是在predict方法中
我们需要访问周线数据
于是我们在第17行
通过tf.floor方法
来获取frame所属的那一周
最后，我们在23行
把当期的i，frame
我们获取的日线数据中
最后一期frame
以及周线数据中
最后一期的frame
都打印出来
最终我们得到以下输出结果

# CLK5
我们可以将这个结果与第19课中，多周期一节中的示例15的输出结果相对照，以验证结果的正确性。所以，在大富翁框架里，实现跨周期数据的正确存取并不困难。

# CLK6
backtrader允许我们在策略初始化时，就读取全部数据，计算出技术指标与信号，这样在next方法中，就只是一个非常简单的步进循环，所以在这种情况下，它的性能比较快。

大富翁完全是事件驱动模式，这种模式下，策略的编写需要的技巧不如向量式复杂，出错的可能性也更小。但是，backtrader在一开始就将全部数据加载到了内存中，因此无论之后是向量式还是事件驱动式，它的运算速度都要显著快于大富翁。在大富翁中，推荐的模式是，在每一个predict方法中，我们都根据frame来获取想要的数据。如果这些数据来自于omicron的话，考虑到omicron将访问网络来获取数据，显然会比backtrader慢不少。

此外，大富翁策略中调用 sell/buy/position等函数（属性）时，都会触发网络访问，这也会相应地比backtrader慢。因此，在性能上，大富翁的回测是比backtrader慢的。

如果大富翁回测的性能不能满足要求，当前一个简单的方法是在predict中，在i==0时，加载全部所需要的数据并保存在内存中。此后，根据i来换算下标，访问与frame对应的数据。

有所得必有所失，这样一来，你会失去动态前复权支持。

# CLK7
大富翁的回测报告要优于backtrader，它充分利用了plotly的交互式绘图能力。它的回测报告不仅提供了买卖点，并且在这些买卖点上通过hoverinfo提供了热点信息，这样查错比较方便。它也没有backtrader那样当回测周期过长时出现的图形拥挤问题。

但是大富翁报告对技术指标的支持不如backtrader那样多。如果有必要，用户需要自己绘制这些指标。在策略指标上，大富翁提供了几乎所有empyrical支持的技术指标。如果这些还不能完全反映策略的特征的话，可以通过assets数据，调用quantstats进行报告分析。

注意这个图它是交互式的
当你把鼠标移动到买卖点上时
就能显示该点的交易详情
它也是左右可拖动的

-->
