---
layout: default
---
# 前视偏差 - PIT数据
<hr>

<v-clicks>

## PIT与数据修正
## 发布时间与归属时间
</v-clicks>


<!--
有一些经济数据
在发布之后
往往还会进行修正
比如宏观数据中的GDP
公司财务报表等等
我们以公司财报为例
如果它在2021年1季末发布了财报1.0
在半年报发布时
对1季度的财报进行了修正
此时1季度的财报的版本应该是2.0
如果数据源在发布时
记录了数据版本和它生成的时间（而不仅仅是归属时间）
这种数据就称为Point-in-Time data (PIT)


以2019年一季度美国的宏观经济数据发布为例
GDP预估值发布于2019年4月25日
5月30日进行第一次修正
最终修正则是在当年的6月27日


举个真实的例子
某海产品养殖公司
2014
2015年连续两年亏损被ST
如果2016年再亏损
按当时规则将进入退市流程
于是
2017年3月
该公司公布了2016年年报
净利润7571万
扭亏为盈
成功保壳
2019年
最终查实他们虚增利润1.3亿
追溯修订2016年财报为亏损-5543万


如果我们现在来进行回测
当运行到2017年3月
财报可获取时
我们取得的数据将是-5543万
避开这个雷
但在2017年5月
实盘中获取到的该公司上年净利润为7571万


如果数据源不提供PIT数据
我们在2023年对该公司的股票进行回测时
当我们回测到2021年1季度时
我们看到的数据是1.0的
还是三个月之后发布的2.0的？
如果我们看到的数据
是三个月之后发布的2.0的数据
这样就引入了未来数据


# CLK1

另一个与此关联的数据偏差是
即使基本面数据没有进行修正
它们的发布时间也往往晚于归属时间
比如
以上述美国宏观数据为例
它的一季度数据最早要在2019年4月25日才能得到
但在回测时
我们可能在回测2019年1月左右时
就能拿到4月25日才发表的这项数据
这取决于数据服务商如何给该项数据索引


在A股
上市公司的财报披露截止时间分别为当年的4月底、8月底、10月底和次年4月底
由于各家上市公司的披露日期不固定
一般我们在构建量化模型时
如果使用了基本面数据
我们需要从次月的第一个交易日起
才能放心地调用


-->
