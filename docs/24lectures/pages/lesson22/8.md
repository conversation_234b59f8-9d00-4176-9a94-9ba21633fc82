---
layout: two-cols
clicks: 5
---
# 复权引起的问题
<hr>

::left::

<show at="0-3">

## 必要性
</show>
<show at="4">

## 必要性
## 后复权问题
### 最小交易单位
### 价差消息
### 整数关口压力、支撑信息消失
### 不能使用固定滑点
</show>
<show at="5">

## 必要性
## 后复权问题
### 最小交易单位
### 价差消息
### 整数关口压力、支撑信息消失
### 不能使用固定滑点
## 其它问题
</show>
::right::

<show at="0-3">

```python {all|19,23|20,36|9-11}{maxHeight:'400px'}
# 示例4
import backtrader as bt
import backtrader.feeds as btfeeds
from coursea import *
await init()

code = "600750.XSHG"
end = datetime.date(2023, 8, 29)
bars = await Stock.get_bars(code, 500, 
                            FrameType.DAY, 
                            end=end)

class BuyAndHold(bt.Strategy):
    def notify_order(self, order):
        if order.status in (order.Margin, 
                            order.Completed):
            print(order)
            
    def start(self):
        size = self.broker.get_value() // self.data.open[1]
        self.buy(size = size)
        
    def stop(self):
        self.close()

        
cerebro = bt.Cerebro()

df = pd.DataFrame(bars)
data = btfeeds.PandasData(dataname=df, datetime="frame")
cerebro.adddata(data)

cerebro.addstrategy(BuyAndHold)

print(cerebro.broker.get_value())
results = cerebro.run(cheat_on_open = True)
print(cerebro.broker.get_value())
```
</show>


<!--
我们用这个例子来演示在回测中使用复权数据的必要性
我们以最简单的买入并持有（buy and hold）策略为例
看看使用不复权数据和复权数据都有多大的差异

# CLK1
我们的buy and hold策略是这样实现的
首先我们是通过start和stop来完成开盘买入和回测结束时的平仓动作
而不是在next方法中


# CLK2

为了最大限度地利用资金
我们设置了cheat_on_open
以便能以开盘价全仓买入
并且在计算size时
我们通过self.data.open[1]来偷看了第二天的开盘价


# CLK3
我们通过omicron来返回数据
它默认地返回前复权数据
运行策略
这笔交易赚了8204元左右
然后我们再以fq=False调用一次
这次将返回不复权数据
运行策略
结果显示这笔交易只赚了6124.7元

我们再把两次运行的结果进行相除
会发现它们刚好等于复权因子首尾相除的结果 -- 0.8858

因此
回测中不使用复权数据是不可行的
它会导致策略收益损失
各项指标没有任何意义

# CLK4

1. 最小交易单位问题
在A股
除了最后一次平仓之外
其它交易中
买入和卖出都必须以100股为最小单位
在使用后复权数据时
越靠近回测末端
价格越高（比如万科A在2018年后复权价格达到过5900元）
这样受1手整数限制
会导致策略无法用完资金
一些研究员会使用无限大本金来缓解这个问题
但这又会引起成交量匹配问题



在使用后复权时
加大本金可以缓解资金利用率问题
比如
在万科A后复权价达到5900元时
购买一手万科也要59万元
如果本金只有10万
则资金利用率为0%
资金100万时
利用率59%
资金1000万时
利用率可达94.4%
资金1个亿时
利用率达99.7%
但是
过大的交易本金很可能会导致成交量匹配不足的问题
从而又降低了资金利用率
<br><br>backtrader本身没有强加最小购买单元100股的限制
它甚至允许成交量不足1股
但使用小数股成交也违反了A股交易规则
也会导致回测与实盘的差异


2. 在配对交易中
后复权时的价格差可能导致无法像实盘那样触发信号
一些依靠盘口数据来产生信号的策略可能也会遇到同样的情况

3. 后复权会使得一些信号消失
比如整数关口的压力位、支撑位等

4. 后复权下
只能使用百分比滑点
而不适合固定滑点


# CLK5
在A股我们只遇到了拆股
但在美股还存在反向拆股
即将若干股合并成一股
2023年8月
法拉迪未来就进行了这种操作
以避免因股价低于要求的最低价而被退市


复权的计算本身十分复杂
既涉及到拆股
也涉及到派息
还会遇到小数点引起的调整问题
为了简化这些计算
一些商用数据源采用复权因子来进行简化
这样的操作也带来另外一个问题：它丢失了部分信息
导致回测框架无法真实复权


比如
某股连续两日收平
复权价格分别为[11
10]
复权因子分别为[1
 1.1]
假设股民在第一日持有100股
市值为1100元
第二天按后复权价格计算
市值为100 * 11
仍为1100元
权益不变
从复权因子我们看不出来究竟是发生了除权
还是除息
这两者实际上会引起权益的不一致：如果是派息引起的除权
那么账户的现金增加
持股数不变
如果持有人在一个月内、一年内卖出
将分别扣除红利税
在像backtrader这样的框架中
我们使用复权数据
实际上是把所有的分红都折算成了拆股
这样可能少扣除红利税
并且加大了账户波动


使用复权因子复权的另一个问题
是在允许做空的情况下
分红的处理问题：比如
做多股票时
我们可以获得分红
如果做空股票
我们需要返还分红
使用复权因子时
由于丢失了分红信息
回测框架无法做到这一点


此外
如果使用前复权
并且回测时间很长的话
还有可能遇到最早期的价格都低于0.01元
从而导致无法区分价格的问题



-->
