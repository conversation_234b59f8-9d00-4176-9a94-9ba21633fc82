---
layout: default
---
# 仓位变化与查询
<hr>

```python {all|33-51|68-70|72-77}{maxHeight: '400px'}
# 示例 6
%matplotlib inline

import datetime
import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind
from tabulate import tabulate
from collections import defaultdict

from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")


class TestStrategy(bt.Strategy):
    params = dict(
        enter1 = [1, 2, 3],
        enter2 = [2],
        exit1 = 3,
        exit2 = 4,
        pentry=0.015,
        plimits=0.03,
        valid=10,
    )

    def notify_order(self, order):
        if order.status == order.Completed:
            dt, dn = self.datetime.date(), order.data._name

            positions = []
            for i, d in enumerate(self.datas):
                pos = self.getposition(d).size
                dn = d._name
                p1 = self.getposition(d).price
                
                if order.data == d:
                    p2 = order.executed.price
                else:
                    p2 = None
                    
                positions.append([dt, dn, pos, p1, p2])
            print(tabulate(positions, ["日期", "股票", "仓位", "均价", "订单"], "simple"))
            print("\n")

    def __init__(self):
        self.o = defaultdict(list)

    def next(self):
        print(f" === {len(self.data)}th day ===")
        for i, d in enumerate(self.datas):
            dt, dn = self.datetime.date(), d._name
            pos = self.getposition(d).size

            if i == 0:
                enter = self.p.enter1
                exit = self.p.exit1
            else:
                enter = self.p.enter2
                exit = self.p.exit2
                
            if len(d) in enter:
                self.o[d].append(self.buy(data=d))
                print('{} {} Buy {}'.format(dt, dn, self.o[d][-1].ref))
            
            if pos:
                for order in self.o[d]:
                    if self.datetime[0] - order.created.dt > exit:
                        o = self.sell(data=d, size=1)
                        self.o[d].append(o)  # manual order to list of orders
                        print('{} {} Manual Close {}'.format(dt, dn, o.ref))


cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("600000.XSHG", 9)
cerebro.adddata(data, name="浦发银行")

data = await get_sample_feed("000002.XSHE", 9)
cerebro.adddata(data, name="万科A")
results = cerebro.run()

cerebro.plot(iplot = False, volume=False)
```

<show>
<TopLayer>

```text
=== 1th day ===
2023-08-04 浦发银行 Buy 127
日期        股票        仓位    均价    订单
----------  --------  ------  ------  ------
2023-08-07  浦发银行       1    7.57    7.57
2023-08-07  万科A          0    0


=== 2th day ===
2023-08-07 浦发银行 Buy 128
2023-08-07 万科A Buy 129
日期        股票        仓位    均价    订单
----------  --------  ------  ------  ------
2023-08-08  浦发银行       2    7.52    7.47
2023-08-08  万科A          1   14.72


日期        股票        仓位    均价    订单
----------  --------  ------  ------  ------
2023-08-08  浦发银行       2    7.52
2023-08-08  万科A          1   14.72   14.72


=== 3th day ===
2023-08-08 浦发银行 Buy 130
2023-08-08 浦发银行 Manual Close 131
日期        股票        仓位      均价    订单
----------  --------  ------  --------  ------
2023-08-09  浦发银行       2   7.51333     7.5
2023-08-09  万科A          1  14.72


日期        股票        仓位      均价    订单
----------  --------  ------  --------  ------
2023-08-09  浦发银行       2   7.51333     7.5
2023-08-09  万科A          1  14.72


=== 4th day ===
2023-08-09 浦发银行 Manual Close 132
日期        股票        仓位      均价    订单
----------  --------  ------  --------  ------
2023-08-10  浦发银行       1   7.51333    7.45
2023-08-10  万科A          1  14.72


=== 5th day ===
2023-08-10 浦发银行 Manual Close 133
日期        股票        仓位    均价    订单
----------  --------  ------  ------  ------
2023-08-11  浦发银行       0    0       7.44
2023-08-11  万科A          1   14.72


=== 6th day ===
=== 7th day ===
2023-08-14 万科A Manual Close 134
日期        股票        仓位    均价    订单
----------  --------  ------  ------  ------
2023-08-15  浦发银行       0       0
2023-08-15  万科A          0       0   14.53


=== 8th day ===
=== 9th day ===
```
</TopLayer>
</show>

<!--
这个示例在示例5的基础上，增加了以下内容：
1. 使用了多标的股票池。由于是多个标的，所以我们通过defaultdict来分别管理每个标的的order
2. 通过参数来决定何时交易，包括买入和卖出
3. 打印持仓
4. 在打印持仓时，我们使用了一个名为tabulate的库，用来以文本格式输出表格


# CLK1
第33到51行，我们在notify_order中，
打印每支标的当前的持仓
notify_order是在next方法中
当buy或者sell方法被执行时
自动触发的
当然，触发notify_order的时机还有其它
这里我们只要知道
当一个买入、卖出动作完成时
持仓显然会发生变化
这也是我们打印持仓的最佳时机之一

上一节讲到order有好几种状态
每一个next中至少触发三种状态通知
所以在这里
我们只在order执行完成时进行打印

这里我们通过对self.datas的遍历
这样才能取出每一个标的的仓位
我们也可以使用self.positions来获取所有仓位
不过它是一个dict数据结构
之后还是需要进行遍历

在查询持仓时，我们使用的方法在上一张slide中已经介绍过了
这里只使用了getposition方法

我们在这里查询了两个价格
一个是当前正在执行的order的价格
另一个是持仓的均价
通过两者的对比
我们可以看出新的成交加入后
对持仓均价的影响

第49行，tabulate方法
它将一个多维数组格式化成为一个字符串
第一个参数是输入数据
第二个参数是表头
第三个参数是表的样式
这里使用simple,此外还有grid, roundgrid等
大家可以在notebook中自行尝试一下

# CLK2

在next方法中
我们通过len(d)来判断当前是第几个交易日
如果这个数字在指定的enter日期集合中
则我们下一手买单
buy方法会返回新建的order
我们把它保存在self.o这个对象中

# CLK3
如果某个标的当前有仓位
且持仓时间大于指定的退出日
我们就通过sell方法卖出一手

我们通过订单的持有时间来判断是否应该退出
注意第74行
self.datatime 与order.created.dt
相减的结果是相差的天数
而不是秒数
这也是对运算符进行重载的结果
不过，似乎它的运算结果始终是以天为单位
而不是根据datafeed中指定的TimeFrame为单位
或者说是以bar为单位
这可能是不太方便的地方
-->
