---
layout: two-cols
clicks: 5
---
# 资金管理
<hr>

::left::

```python {all|17|20|9-11|9-11|9-11}{maxHeight:'400px'}
# 示例 3
import backtrader as bt

cerebro = bt.Cerebro()  

class MyStrategy(bt.Strategy):
  def next(self):
    broker = self.broker  
    print("初始资金:", broker.getcash())
    broker.add_cash(50000)
    print("增加后资金:", broker.getcash())

broker = bt.brokers.BackBroker(cash=100_000)
cerebro.broker = broker

broker.set_cash(5_000_000)
print("初始资金:", broker.get_cash())

cerebro.broker.add_cash(-4_500_000)
print("strategy外修改资金无效:", cerebro.broker.get_cash())

cerebro.addstrategy(MyStrategy)

data = await get_sample_feed("000001.XSHG", 3)
cerebro.adddata(data)

cerebro.run()
```

::right::

<show at="1">

初始资金: 5000000
</show>

<show at="2">
strategy外修改资金无效: 5000000
</show>

<show at="3">
初始资金: 5000000.0
增加后资金: 5000000.0
</show>

<show at="4">
初始资金: 5050000.0
增加后资金: 5050000.0
</show>

<show at="5">
初始资金: 5100000.0
增加后资金: 5100000.0
</show>

<!--

我们通过broker.set_cash来初始化资金。cerebro默认的broker，初始资金是10万单位。
在增加资金时，要注意在strategy类之外修改资金无效，在next方法中修改资金后，也需要等待下一次的next，我们才能看到资金变化

# CLK1
运行到第16行时，输出初始资金5百万

# CLK2
第20行，strategy外修改资金无效，资金仍然为5百万

# CLK3
第9-11行，next方法内，修改资金后，当期不生效

# CLK4
上次修改生效，但本次修改还没体现出来

# CLK5
上次修改生效，但本次修改还没体现出来

-->
