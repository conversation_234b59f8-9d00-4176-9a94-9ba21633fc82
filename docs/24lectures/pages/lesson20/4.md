---
layout: two-cols
---
# Cerebro
<hr>

::left::
<v-clicks>

### 1. 实例化
### 2. 增加数据流
### 3. 增加策略类
### 4. 增加记录器
### 
### 5. 增加指标分析
### 6. 增加观察者
### 7. 设置交易代理参数
### 8. 执行
### 9. 结果分析（绘图和指标）
</v-clicks>
::right::
<show at="1">

```python
cerebro = bt.Cerebro()
```
</show>
<show at="2">

```python {3-7}
cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)
```
</show>
<show at="3">

```python {9-11}
cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)

cerebro.addstrategy(MA_CrossOver)
# 或者：
# cerebro.optstrategy(MyStrategy, myparam1=range(10, 20))
```
</show>
<show at="4">

```python {11}
cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)

cerebro.addstrategy(MA_CrossOver)

cerebro.addwriter(bt.WriterFile, rounding=2, csv=True, out='...')

```
</show>

<show at="5">

<TopLayer>

```python {all}{maxHeight:'500px'}
===============================================================================
Id,,len,datetime,open,high,low,close,volume,openinterest,MA_CrossOver,len,datetime,Broker,len,cash,value,BuySell,len,buy,sell,Trades - Net Profit/Loss,len,pnlplus,pnlminus
1,,1,2023-07-04 00:00:00,3241.22998046875,3246.9599609375,3233.989990234375,3245.35009765625,27924872100.0,,MA_CrossOver,1,738705.0,Broker,1,10000.0,10000.0,BuySell,1,,,Trades - Net Profit/Loss,1,,
2,,2,2023-07-05 00:00:00,3240.050048828125,3241.840087890625,3218.60009765625,3222.949951171875,26401642600.0,,MA_CrossOver,2,738706.0,Broker,2,10000.0,10000.0,BuySell,2,,,Trades - Net Profit/Loss,2,,
3,,3,2023-07-06 00:00:00,3215.860107421875,3229.47998046875,3199.719970703125,3205.570068359375,24611903200.0,,MA_CrossOver,3,738707.0,Broker,3,10000.0,10000.0,BuySell,3,,,Trades - Net Profit/Loss,3,,
4,,4,2023-07-07 00:00:00,3197.469970703125,3212.050048828125,3189.2099609375,3196.610107421875,25568846900.0,,MA_CrossOver,4,738708.0,Broker,4,10000.0,10000.0,BuySell,4,,,Trades - Net Profit/Loss,4,,
5,,5,2023-07-10 00:00:00,3208.72998046875,3216.530029296875,3192.659912109375,3203.699951171875,23656036700.0,,MA_CrossOver,5,738711.0,Broker,5,10000.0,10000.0,BuySell,5,,,Trades - Net Profit/Loss,5,,
6,,6,2023-07-11 00:00:00,3211.1201171875,3221.679931640625,3200.3798828125,3221.3701171875,22760046700.0,,MA_CrossOver,6,738712.0,Broker,6,10000.0,10000.0,BuySell,6,,,Trades - Net Profit/Loss,6,,
7,,7,2023-07-12 00:00:00,3220.3798828125,3224.330078125,3193.43994140625,3196.1298828125,27142338600.0,,MA_CrossOver,7,738713.0,Broker,7,10000.0,10000.0,BuySell,7,,,Trades - Net Profit/Loss,7,,
8,,8,2023-07-13 00:00:00,3202.699951171875,3238.469970703125,3202.699951171875,3236.47998046875,27662050800.0,,MA_CrossOver,8,738714.0,Broker,8,10000.0,10000.0,BuySell,8,,,Trades - Net Profit/Loss,8,,
9,,9,2023-07-14 00:00:00,3240.969970703125,3248.3798828125,3234.0,3237.699951171875,27332749400.0,,MA_CrossOver,9,738715.0,Broker,9,10000.0,10000.0,BuySell,9,,,Trades - Net Profit/Loss,9,,
10,,10,2023-07-17 00:00:00,3219.02001953125,3219.02001953125,3195.889892578125,3209.6298828125,25020837600.0,,MA_CrossOver,10,738718.0,Broker,10,10000.0,10000.0,BuySell,10,,,Trades - Net Profit/Loss,10,,
11,,11,2023-07-18 00:00:00,3206.7900390625,3210.25,3190.0,3197.820068359375,23503025900.0,,MA_CrossOver,11,738719.0,Broker,11,10000.0,10000.0,BuySell,11,,,Trades - Net Profit/Loss,11,,
12,,12,2023-07-19 00:00:00,3195.010009765625,3204.360107421875,3181.56005859375,3198.840087890625,21735586000.0,,MA_CrossOver,12,738720.0,Broker,12,10000.0,10000.0,BuySell,12,,,Trades - Net Profit/Loss,12,,
13,,13,2023-07-20 00:00:00,3201.909912109375,3209.06005859375,3165.669921875,3169.52001953125,23893289100.0,,MA_CrossOver,13,738721.0,Broker,13,10000.0,10000.0,BuySell,13,,,Trades - Net Profit/Loss,13,,
14,,14,2023-07-21 00:00:00,3163.419921875,3185.639892578125,3157.330078125,3167.75,22533136300.0,,MA_CrossOver,14,738722.0,Broker,14,10000.0,10000.0,BuySell,14,,,Trades - Net Profit/Loss,14,,
15,,15,2023-07-24 00:00:00,3157.469970703125,3178.72998046875,3151.1298828125,3164.159912109375,21659521100.0,,MA_CrossOver,15,738725.0,Broker,15,10000.0,10000.0,BuySell,15,,,Trades - Net Profit/Loss,15,,
16,,16,2023-07-25 00:00:00,3201.4599609375,3231.75,3201.4599609375,3231.52001953125,34861771400.0,,MA_CrossOver,16,738726.0,Broker,16,10000.0,10000.0,BuySell,16,,,Trades - Net Profit/Loss,16,,
17,,17,2023-07-26 00:00:00,3228.090087890625,3229.889892578125,3212.8798828125,3223.030029296875,31720605100.0,,MA_CrossOver,17,738727.0,Broker,17,10000.0,10000.0,BuySell,17,,,Trades - Net Profit/Loss,17,,
18,,18,2023-07-27 00:00:00,3225.47998046875,3245.60009765625,3210.469970703125,3216.669921875,32711886700.0,,MA_CrossOver,18,738728.0,Broker,18,10000.0,10000.0,BuySell,18,,,Trades - Net Profit/Loss,18,,
19,,19,2023-07-28 00:00:00,3206.739990234375,3280.280029296875,3200.989990234375,3275.929931640625,41106843400.0,,MA_CrossOver,19,738729.0,Broker,19,10000.0,10000.0,BuySell,19,,,Trades - Net Profit/Loss,19,,
20,,20,2023-07-31 00:00:00,3287.2099609375,3322.1298828125,3281.010009765625,3291.0400390625,45903099400.0,,MA_CrossOver,20,738732.0,Broker,20,10000.0,10000.0,BuySell,20,,,Trades - Net Profit/Loss,20,,
21,,21,2023-08-01 00:00:00,3288.760009765625,3305.340087890625,3279.570068359375,3290.949951171875,45989083300.0,,MA_CrossOver,21,738733.0,Broker,21,10000.0,10000.0,BuySell,21,,,Trades - Net Profit/Loss,21,,
22,,22,2023-08-02 00:00:00,3281.860107421875,3290.820068359375,3252.300048828125,3261.68994140625,40534149900.0,,MA_CrossOver,22,738734.0,Broker,22,10000.0,10000.0,BuySell,22,,,Trades - Net Profit/Loss,22,,
23,,23,2023-08-03 00:00:00,3254.570068359375,3280.860107421875,3247.27001953125,3280.4599609375,38469131700.0,,MA_CrossOver,23,738735.0,Broker,23,10000.0,10000.0,BuySell,23,,,Trades - Net Profit/Loss,23,,
24,,24,2023-08-04 00:00:00,3296.090087890625,3315.050048828125,3281.72998046875,3288.080078125,47092769700.0,,MA_CrossOver,24,738736.0,Broker,24,10000.0,10000.0,BuySell,24,,,Trades - Net Profit/Loss,24,,
25,,25,2023-08-07 00:00:00,3276.780029296875,3276.780029296875,3258.5400390625,3268.830078125,36034148400.0,,MA_CrossOver,25,738739.0,Broker,25,10000.0,10000.0,BuySell,25,,,Trades - Net Profit/Loss,25,,
26,,26,2023-08-08 00:00:00,3260.719970703125,3274.52001953125,3246.669921875,3260.6201171875,31652073300.0,,MA_CrossOver,26,738740.0,Broker,26,10000.0,10000.0,BuySell,26,,,Trades - Net Profit/Loss,26,,
27,,27,2023-08-09 00:00:00,3251.14990234375,3257.080078125,3240.2900390625,3244.489990234375,27121853800.0,,MA_CrossOver,27,738741.0,Broker,27,10000.0,10000.0,BuySell,27,,,Trades - Net Profit/Loss,27,,
28,,28,2023-08-10 00:00:00,3242.2099609375,3257.030029296875,3234.1201171875,3254.56005859375,28741214200.0,,MA_CrossOver,28,738742.0,Broker,28,10000.0,10000.0,BuySell,28,,,Trades - Net Profit/Loss,28,,
29,,29,2023-08-11 00:00:00,3254.47998046875,3254.47998046875,3189.25,3189.25,33013775000.0,,MA_CrossOver,29,738743.0,Broker,29,10000.0,10000.0,BuySell,29,,,Trades - Net Profit/Loss,29,,
30,,30,2023-08-14 00:00:00,3159.7099609375,3180.510009765625,3141.659912109375,3178.429931640625,29984032700.0,,MA_CrossOver,30,738746.0,Broker,30,10000.0,10000.0,BuySell,30,,,Trades - Net Profit/Loss,30,,
31,,31,2023-08-15 00:00:00,3180.56005859375,3186.22998046875,3167.89990234375,3169.18994140625,15639223700.0,,MA_CrossOver,31,738747.0,Broker,31,10000.0,10000.0,BuySell,31,,,Trades - Net Profit/Loss,31,,
===============================================================================
Cerebro:
  -----------------------------------------------------------------------------
  - Datas:
    +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    - Data0:
      - Name: 
      - Timeframe: Days
      - Compression: 1
  -----------------------------------------------------------------------------
  - Strategies:
    +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    - MA_CrossOver:
      *************************************************************************
      - Params:
        - fast: 10
        - slow: 30
        - _movav: SMA
      *************************************************************************
      - Indicators:
        .......................................................................
        - SMA:
          - Lines: sma
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
          - Params:
            - period: 30
        .......................................................................
        - CrossOver:
          - Lines: crossover
          - Params: None
      *************************************************************************
      - Observers:
        .......................................................................
        - Broker:
          - Lines: cash, value
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
          - Params:
            - fund: None
        .......................................................................
        - BuySell:
          - Lines: buy, sell
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
          - Params:
            - barplot: True
            - bardist: 0.01
        .......................................................................
        - Trades:
          - Lines: pnlplus, pnlminus
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
          - Params:
            - pnlcomm: True
      *************************************************************************
      - Analyzers:
        .......................................................................
        - Value:
          - Begin: 10000.0
          - End: 10000.0

```
</TopLayer>
</show>

<show at="6">

```python {13-23}
cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)

cerebro.addstrategy(MA_CrossOver)

cerebro.addwriter(bt.WriterFile, rounding=2, csv=True, out='...')

cerebro.addanalyzer(TimeReturn,
    timeframe=bt.TimeFrame.Years,
    data=benchmark,
    _name="benchmark")

cerebro.addanalyzer(
    bt.analyzers.TimeReturn, 
    timeframe=bt.TimeFrame.Years,
    _name="target", 
    data=target
)
```
</show>

<show at="7">

```python {25,26}{maxHeight: '500px'}
cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)

cerebro.addstrategy(MA_CrossOver)

cerebro.addwriter(bt.WriterFile, rounding=2, csv=True, out='...')

cerebro.addanalyzer(TimeReturn,
    timeframe=bt.TimeFrame.Years,
    data=benchmark,
    _name="benchmark")

cerebro.addanalyzer(
    bt.analyzers.TimeReturn, 
    timeframe=bt.TimeFrame.Years,
    _name="target", 
    data=target
)

cerebro.addobserver(bt.observers.Benchmark,
                    data=benchmark)
```
</show>

<show at="8">

```python {28-31}{maxHeight:'500px'}
cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)

cerebro.addstrategy(MA_CrossOver)

cerebro.addwriter(bt.WriterFile, rounding=2, csv=True, out='...')

cerebro.addanalyzer(TimeReturn,
    timeframe=bt.TimeFrame.Years,
    data=benchmark,
    _name="benchmark")

cerebro.addanalyzer(
    bt.analyzers.TimeReturn, 
    timeframe=bt.TimeFrame.Years,
    _name="target", 
    data=target
)

cerebro.addobserver(bt.observers.Benchmark,
                    data=benchmark)

cerebro.broker.set_cash(1_000_000)
print("the cash is:", cerebro.broker.get_cash())

cerebro.broker.setcommission(1.5e-3)
```
</show>
<show at="9">

```python {35,36}{maxHeight:'500px'}
cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)

cerebro.addstrategy(MA_CrossOver)

cerebro.addwriter(bt.WriterFile, rounding=2, csv=True, out='...')

cerebro.addanalyzer(TimeReturn,
    timeframe=bt.TimeFrame.Years,
    data=benchmark,
    _name="benchmark")

cerebro.addanalyzer(
    bt.analyzers.TimeReturn, 
    timeframe=bt.TimeFrame.Years,
    _name="target", 
    data=target
)

cerebro.addobserver(bt.observers.Benchmark,
                    data=benchmark)

cerebro.broker.set_cash(1_000_000)
print("the cash is:", cerebro.broker.get_cash())

cerebro.broker.setcommission(1.5e-3)

cerebro.broker.set_slippage_perc(0.01)

results = cerebro.run()
```
</show>
<show at="10">

```python {38-41}{maxHeight:'500px'}
# 示例1
cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)

cerebro.addstrategy(MA_CrossOver)

cerebro.addwriter(bt.WriterFile, rounding=2, csv=True, out='...')

cerebro.addanalyzer(TimeReturn,
    timeframe=bt.TimeFrame.Years,
    data=benchmark,
    _name="benchmark")

cerebro.addanalyzer(
    bt.analyzers.TimeReturn, 
    timeframe=bt.TimeFrame.Years,
    _name="target", 
    data=target
)

cerebro.addobserver(bt.observers.Benchmark,
                    data=benchmark)

cerebro.broker.set_cash(1_000_000)
print("the cash is:", cerebro.broker.get_cash())

cerebro.broker.setcommission(1.5e-3)

cerebro.broker.set_slippage_perc(0.01)

results = cerebro.run()

cerebro.plot(iplot = False)

analyzer = results[0].analyzers.getbyname("target")
print(analyzer.get_analysis())
```
</show>

<!--
我们已经见过了好几个示例。但这一次，我们将介绍一个更全面的cerebro的示例，作为对cerebro功能的回顾与拓展。

我们将按次序介绍如何给cerebro增加功能。首先，我们创建一个对象:

# CLK1

接着我们增加数据流

# CLK2

cerebro可以接受多个数据流，不同的周期、不同的标的。通过加入cerebro的顺序，或者使用name来区分它们。如果要使用名字来区分它们，可以在adddata时，增加一个name参数。这是我们前面介绍过的。

这里也可以增加resampledata或者replaydata

# CLK3
接下来我们增加策略 -- Strategy
需要再次提醒，我们往cerebro中增加策略时
我们提供的是类，而不是它的实例
为什么？
因为cerebro在进行参数优化时
需要通过类来生成多个实例

到此为止，都是我们已经见过的玩法了。

接下来，我们增加一个记录器，以便把cerebro的各项运作都记录下来，这需要使用addwriter方法

# CLK4

为简单起见，我们使用了内置的MA_CrossOver策略。
addwriter有一个必选参数，即writer class，这里我们使用它内置的Writer,即bt.WriterFile，这也是backtrader提供的惟一一个Writer。其它参数有：

[MARK]

1. out，可以省略。当它省略时，默认输出到控制台。如果提供该参数，则要求提供文件名，这样信息会写入到该文件中。
2. csv，可以省略，此时默认为False，当它为True时，将输出非常丰富的信息。
3. rouding，用来对某些输出进行小数位限制

我们来检查一下输出结果。

# CLK5
输出内容相当多，这是因为我们开启了csv=True。如果不使用这一选项，我们将只得到

[SCROLL to 35]

第35行以下的内容。这些内容注意是各个对象的构建参数。但会自动打印出分析器的输出结果。

接下来我们就介绍分析器， analyzer。analyzer负责评估策略指标。在backtrader中，每个要查看的指标都必须手动添加，并不是很方便。我对它的一些源码阅读之后，有一种感觉，个别指标的计算上，我可能更愿意使用其它方案。我们在后面还会介绍更好的方案。

# CLK6

在示例中我们添加了一个收益指标， TimeReturn。

接下来我们增加一个观察者。

# CLK7

这里我们使用了一个名为Benchmark的观察者。观察者和分析器都可用作benchmark，两者的区别是，observer具有line的属性，它会记录每一个值，因此更适合绘图和实时查询。分析器则只会在cerebro.run结束后，才能通过get_analysis进行查询。

# CLK8
此外，我们还可以更换交易代理，或者设置本金、佣金等等
分别是通过set_cash, setcommission, set_slippage_perc来实现的

顺便说一句，backtrader在API风格上，在是否使用下划线来分割单词，并不统一，看上去它更倾向于不使用下划线，所以我们记忆时，可以只记少数加了下划线的函数。

# CLK9
接下来就可以执行回测了，仍然是执行run方法。

# CLK10
现在我们通过绘图和指标分析来查看回测结果，以便进一步优化。
-->
