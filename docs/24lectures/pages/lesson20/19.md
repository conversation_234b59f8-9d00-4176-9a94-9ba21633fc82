---
layout: two-cols
---
# 其它交易知识
<hr>

::left::
<v-clicks>

## 滑点
### 百分比滑点
### 固定滑点
## 交易费用
## 自定义交易费用类
## 
</v-clicks>
::right::

<show at="0|2">

```python
# 方式1
cerebro.broker = bt.brokers.BackBroker(slip_perc=0.0001)
# 方式2
cerebro.broker.set_slippage_perc(perc=0.0001)
```
</show>

<show at="3">

```python
# 方式1
cerebro.broker = bt.brokers.BackBroker(slip_fixed=0.001)
# 方式2
cerebro.broker.set_slippage_fixed(fixed=0.001)
```
</show>

<show at="4">

```python {all}{maxHeight:'400px'}
cerebro.broker.setcommission(
    # 交易手续费，根据margin取值情况区分是百分比手续费还是固定手续费
    commission=0.0,
    # 期货保证金，决定着交易费用的类型,只有在stocklike=False时起作用
    margin=None,
    # 乘数，盈亏会按该乘数进行放大
    mult=1.0,
    # 交易费用计算方式，取值有：
    # 1.CommInfoBase.COMM_PERC 百分比费用
    # 2.CommInfoBase.COMM_FIXED 固定费用
    # 3.None 根据 margin 取值来确定类型
    commtype=None,
    # 当交易费用处于百分比模式下时，commission 是否为 % 形式
    # True，表示不以 % 为单位，0.XX 形式；False，表示以 % 为单位，XX% 形式
    percabs=True,
    # 是否为股票模式，该模式通常由margin和commtype参数决定
    # margin=None或COMM_PERC模式时，就会stocklike=True，对应股票手续费；
    # margin设置了取值或COMM_FIXED模式时,就会stocklike=False，对应期货手续费
    stocklike=False,
    # 计算持有的空头头寸的年化利息
    # days * price * abs(size) * (interest / 365)
    interest=0.0,
    # 计算持有的多头头寸的年化利息
    interest_long=False,
    # 杠杆比率，交易时按该杠杆调整所需现金
    leverage=1.0,
    # 自动计算保证金
    # 如果False,则通过margin参数确定保证金
    # 如果automargin<0,通过mult*price确定保证金
    # 如果automargin>0,如果automargin*price确定保证金
    automargin=False,
    # 交易费用设置作用的数据集(也就是作用的标的)
    # 如果取值为None，则默认作用于所有数据集(也就是作用于所有assets)
    name=None)
```
</show>
<show at="5">

```python {all}{maxHeight:'400px'}
# 示例 12
class StockCommission(bt.CommInfoBase):
    params = (
      ('stocklike', True), # 指定为股票模式
      # 使用百分比费用模式
      ('commtype', bt.CommInfoBase.COMM_PERC), 
      # commission 不以 % 为单位
      ('percabs', True), 
      # 印花税，默认为 0.1%
      ('stamp_duty', 0.001),
      ('commission', 1e-4)
    )
    
    # 自定义费用计算公式
    def _getcommission(self, size, price, pseudoexec):
            comm_all = self.p.commission + self.p.stamp_duty

            if size > 0: # 买入时，只考虑佣金
                return abs(size) * price * self.p.commission
            elif size < 0: # 卖出时，同时考虑佣金和印花税
                return abs(size) * price * comm_all
            else:
                return 0
            
comminfo = StockCommission()
cerebro = bt.Cerebro()
cerebro.broker.addcommissioninfo(comminfo)
```
</show>

<!--

backtrader中有两种滑点，即百分比滑点和固定滑点。

# CLK1-2
假设设置了 n% 的滑点，如果指定的买入价为 x，那实际成交时的买入价会提高至 x * (1+ n%) ；同理，若指定的卖出价为 x，那实际成交时的卖出价会降低至 x * (1- n%)。

# CLK3
假设设置了大小为 n 的固定滑点，如果指定的买入价为 x，那实际成交时的买入价会提高至 x + n ；同理，若指定的卖出价为 x，那实际成交时的卖出价会降低至 x - n。

在设置滑点时，需要考虑调整后的价格是否超出次日的最高、最低价的问题，以及在超出时，订单是否执行的问题。backtrader给出了slip_open, slip_match, slip_out, slip_limit等参数来定制相关行为，具体的请参考文档。

# CLK4
在设置交易费用时，允许指定commission(手续费/佣金)， multi乘数，margin(保证金)和是否双边征收等参数。

如果我们对交易费用要求比较精细，那么还需要定制。

# CLK5
这段代码从bt.CommInfoBase派出了一个子类，设置了相关参数，可能需要在实例化时指定的参数有commision和stamp_duty。

要点是重写_getcommission方法，从而返回具体委买size和价格下的手续费。对于A股股票交易，还应该加上最小5元的限制。

最后，我们在第25行，实例化这个类，通过broker的方法addcommissioninfo将其添加到broker中。

关于交易费率的设置，我们还要注意，不同历史时期，相关费用是不相同的。对于高频交易，交易费率对策略的影响又比较大，要评估策略在不同时期的影响，可能需要分段设置交易费率。这样的回测结果才能带入到实盘中。
-->
