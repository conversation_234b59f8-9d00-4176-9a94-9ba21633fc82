---
layout: two-cols
clicks: 5
---
# 收集回测数据
<hr>

::left::

```python {all|5-10|24-30|38-41}{maxHeight: '400px'}
import csv
from backtrader.observers import DrawDown, TimeReturn, Value, Cash, Broker

class Day1Strategy(bt.Strategy):
    def start(self):
        self.mystats = csv.writer(open("/tmp/mystats.csv", "w"))
        self.mystats.writerow(['datetime',
                               'drawdown', 'maxdrawdown',
                               'timereturn',
                               'value', 'cash'])
    def next(self):
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()  
            
        w = self.mystats
        w.writerow([self.data.datetime.date(-1).strftime('%Y-%m-%d'),
                    '%.2f' % self.stats.drawdown.drawdown[0],
                    '%.2f' % self.stats.drawdown.maxdrawdown[0],
                    '%.2f' % self.stats.timereturn.line[0],
                    '%.2f' % self.stats.broker.value[0],
                    '%.2f' % self.stats.broker.cash[0]])
        
    def stop(self): 
        w = self.mystats 
        w. writerow([self.data.datetime.date(0).strftime('%Y-%m-%d'),
                    '%.2f' % self.stats.drawdown.drawdown[0],
                    '%.2f' % self.stats.drawdown.maxdrawdown[0],
                    '%.2f' % self.stats.broker.value[0],
                    '%.2f' % self.stats.broker.cash[0]])
        
cerebro = bt.Cerebro()

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(DrawDown)
cerebro.addobserver(TimeReturn)
cerebro.addobserver(Value)
cerebro.addobserver(Cash)

cerebro.run()
```

::right::

```text
datetime,drawdown,maxdrawdown,timereturn,value,cash
2023-08-17,nan,nan,nan,nan,nan
2022-08-09,0.00,0.00,0.00,10000.00,10000.00
2022-08-10,0.00,0.00,0.00,10000.06,10011.83
2022-08-11,0.00,0.00,-0.00,10000.04,10000.04
2022-08-12,0.00,0.00,-0.00,9999.92,10012.01
2022-08-15,0.00,0.00,0.00,10000.01,10000.01
2022-08-16,0.00,0.00,-0.00,9999.99,10011.83
2022-08-17,0.00,0.00,-0.00,9999.97,9999.97
2022-08-18,0.00,0.00,0.00,10000.20,10012.15
```

<TopLayer at="4" vcenter>

```python {all}{maxHeight: '400px'}
# 示例 ？
from backtrader.observers import Value

class Day1Strategy(bt.Strategy):
    def __init__(self):
        self.values = []
        
    def next(self):
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()  
            
        self.values.append((self.datetime.date(-1), 
                          self.stats.value[0]))
        
    def stop(self): 
        self.values.append((self.datetime.date(0),
                          self.stats.value[0]))
        
cerebro = bt.Cerebro()

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(Value)

results = cerebro.run()
results[0].values
```
</TopLayer>

<!--
上一张slide我们提到，要获得更好的绘图体验，还得自己动手。但是，数据从何而来？

所以，我们又要回到observers上来。observers可以为我们收集全面的数据，用以后续分析和绘图。

我们可以通过这段代码来获取并保存各周期的相关数据。这里使用了csv这个模块，它是python标准库的一部分。

# CLK1-2

注意我们在strategy中，通过start, stop来打开文件，写入表头和最后一期的数据。

# CLK3
当然，要写入这些数据，我们必须先加入对应的观察器

# CLK4
我们也可以选择不通过csv来保存数据，而是直接在内存中存放和处理数据。我们可以像第6行这样，在strategy中定义一个属性self.values，在回测过程中，将收益数据保存到这个values中。注意这里我们使用的时间是date(-1)，只有这样，时间才是对应的。

在回测结束前，第18行，在stop方法中，我们把最后一期数据添加进来。
最后，在第31行，回测结束后，我们通过results[0].values来使用我们保存的returns数据。cerebro.run返回的结果，就是它生成的策略对象。如果我们不是通过cerebro.run方法来运行回测，而是通过cerebro.optstrategy来运行回测时，就有可能生成若干个Strategy对象，每个对象都对应不同的参数，供我们择优选择。这时候，我们就可以遍历这个数组，获取它们的values进行比较。

在这里，我们只保存了每日总资产。实际上，这已足够用来计算绝大多数收益指标，对策略进行评估了。我们在后面的课程中，将介绍如何使用总资产数据来进行策略评估及绘图。
-->
