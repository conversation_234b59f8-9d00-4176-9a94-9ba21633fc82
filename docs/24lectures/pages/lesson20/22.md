---
layout: default
clicks: 2
---
# 定制绘图
<hr>

```python {all}{maxHeight:'400px'}
class PlotScheme(object):
    def __init__(self):
        # 无论只有x轴还是xy轴都适合紧凑的图表排列(参见matplotlib)
        self.ytight = False 

        # 子图表的上下边距。这不会覆盖plotinfo.plotymargin选项
        self.yadjust = 0.0

        # 每条新线都在之前线的Z轴下方绘制。改为False可以使线绘制在之前线的Z轴上方
        self.zdown = True  

        # x轴日期标签的旋转角度 
        self.tickrotation = 15

        # 用来调节主图的高度
        self.rowsmajor = 5

        # 用来调节子图的高度
        self.rowsminor = 1  

        # 子图表之间的距离
        self.plotdist = 0.0

        # 在所有图表的背景上都有网格
        self.grid = True

        # 默认的K线图样式(收盘价线)。其他选项:'bar', 'candle'
        self.style = 'line'

        # 收盘价线的默认颜色 
        self.loc = 'black' 

        # 多头K线/蜡烛的默认颜色
        self.barup = '0.75'

        # 空头K线/蜡烛的默认颜色
        self.bardown = 'red'

        # 蜡烛线的透明度(当前未使用)
        self.bartrans = 1.0  

        # 蜡烛是否填充还是透明
        self.barupfill = True
        self.bardownfill = True

        # 填充蜡烛的不透明度(1.0不透明 - 0.0透明)  
        self.baralpha = 1.0

        # 填充区域(fill_gt和fill_lt之间)的alpha混合    
        self.fillalpha = 0.20

        # 是否绘制成交量。如果数据没有成交量,即使设置为True也不会绘制成交量
        self.volume = True

        # 成交量是叠加在价格线上还是使用独立子图
        self.voloverlay = True

        # 叠加成交量时的缩放比例 
        self.volscaling = 0.33

        # 向上推移叠加成交量以改善可见性。如果价量重叠太多可能需要调整
        self.volpushup = 0.00 

        # 多头日成交量的默认颜色
        self.volup = '#aaaaaa'  

        # 空头日成交量的默认颜色
        self.voldown = '#cc6073'

        # 叠加成交量时使用的透明度
        self.voltrans = 0.50

        # 图表上标签的默认字体大小
        self.subtxtsize = 9

        # 指标子图是否显示图例
        self.legendind = True

        # 指标图例的位置(参见matplotlib)
        self.legendindloc = 'upper left' 

        # 数据源图例的位置(参见matplotlib)  
        self.legenddataloc = 'upper left'

        # 在线条名称后面绘制最后一个值
        self.linevalues = True

        # 在每条线的末端打标签显示最后一个值
        self.valuetags = True

        # 水平线的默认颜色 
        self.hlinescolor = '0.66'  

        # 水平线的默认样式
        self.hlinesstyle = '--'

        # 水平线的默认宽度
        self.hlineswidth = 1.0

        # 默认颜色方案:Tableau 10
        self.lcolors = tableau10

        # x轴刻度的显示格式  
        self.fmt_x_ticks = '%Y-%m-%d %H:%M' 

        # 数据点值的显示格式
        self.fmt_x_data = None
```

<TopLayer at="1" vcenter>

```python {all}{maxHeight: '400px'}
from backtrader.plot import PlotScheme
from backtrader.plot import Plot

class MyStyle(PlotScheme):
    def __init__(self):
        super().__init__()
        self.rowsmajor = 3
        self.barup = 'red'
        self.bardown = 'green'
        self.barupfill = False

        self.volup = 'red'
        self.voldown = 'green'
        self.style = 'candle'
    
class Day1Strategy(bt.Strategy):
    def next(self):
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro()

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

cerebro.run()
plotter = Plot(scheme = MyStyle())
cerebro.plot(plotter = plotter, iplot = False, numfigs = 5)
```
</TopLayer>

<TopLayer at="2" center>

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-mystyle.png)
</TopLayer>

<!--
从前面的示例可以看出，backtrader默认的绘图不是太理想，它在布局、显示大量数据方面存在诸多问题。

不过，backtrader也提供了一些参数，让我们可以控制绘图效果。它提供了一个名为PlotScheme的类来进行绘图控制，我们也可以继承这个类，进一步定制化。

rowsmajor参数和rowsminor用来调节主图与子图的高度。我们可以把backtrader的绘图想像成一个n*1的网格，每个子图都占据其中的一行或者多行。所以，通过rowsmajor来指定它要占据的行数，也就指定了它的高度。当然，最终都是一个计算比例的过程。

style这个参数，如果我们希望主图显示蜡烛图的话，可以将其设置为'candle',不过，如果我们要求绘制k线图的话，接下来还要分别指定barup, bardown, barupfill, bardownfill这些颜色，以符合A股的习惯。

有时候我们觉得volume子图比较占空间，也可以通过volume = False隐藏这个图。另外，volume子图的颜色也是可以定制的。

# CLK1
这段代码显示了如何定制一个PlotScheme类，并使用它。要注意官方文档在这部分讲得不太详细，可能导致误解。

我们先从PlotScheme派出一个类，称为MyStyle，然后修改相关的属性。这里我们是修改了主图高度的比例，将主图设置为k线图，并调整颜色为我们熟悉的A股风格。

然后我们实例化Plot类，生成MyStyle的一个实例对象，作为scheme参数传入，并把这个实例命名为Plotter，再在调用cerebro.plot时，传入plotter = plotter。

由于我们回测的时间跨度较大，bar的数量较多，我们还通过参数numfigs = 5的设置，总共生成了5幅图。

这个图显示了其中的第一个子图。

# CLK2

当然这个图还是很丑。

如果不进行深度定制，该类的一些属性，可以做为参数，在调用cerebro.run时传入。大家可以自行尝试，在调用 cerebro.plot时，将PlotScheme的属性一个个作为参数传入，看看效果。再在这个基础上，按照前面的方法，自己进行定制。

考虑到backtrader设计上的固有风格和体系，要想自由定义绘图并不容易，特别是它依赖于matplotlib，只能生成静态图，所以，backtrader生成的图，可以在初期作为探索，进行快速筛选，如果要作为正式展示，还是需要用到我们前面介绍的知识，自己动手，绘制更丰富和美观的图形。
-->
