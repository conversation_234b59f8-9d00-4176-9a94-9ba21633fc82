---
layout: default
---
# OCO订单
<hr>

```python
# 示例 11
def next(self):
   ...
   o1 = self.buy(...)
   ...
   o2 = self.buy(..., oco=o1)
   ...
   o3 = self.buy(..., oco=o1)

# 案例 2
def next(self):
   ...
   o1 = self.buy(...)
   ...
   o2 = self.buy(..., oco=o1)
   ...
   o3 = self.buy(..., oco=o2)
```

<!--
前一张slide中我们介绍的组合交易，是针对同一个标的，目标是要实现自动止损或者止盈。有时候，我们尝试以不同的延时或者限价来进行委托，但只要其中的一个被执行、或者取消（到期），同一组内的其它订单也随之取消。此时我们就可以使用OCO(One Cancels Other)订单。

在上述案例1中，生成的 o1 与 o2 是一组关联订单，其中 o1 是主订单，它的执行情况将会决定 o2 的生死存亡，如果 o1 被执行、取消或到期，就会自动取消订单 o2； o1 与 o3 也是一组关联订单，情况与o1 - o2 组类似；

案例 2 中，订单 o1 关联着订单 o2，订单 o2 关联着订单 o3，虽然是 2 组关联订单，实质上o1、o2、o3 是一组订单，因为 o1 以 o2 为媒介，影响 o2 的同时，也影响了 o3 。

很难说这种类型的订单有多大用处。毕竟，在实盘中能否得到执行，很可能依赖于broker的实现。所以，这里不作详细介绍。

-->
