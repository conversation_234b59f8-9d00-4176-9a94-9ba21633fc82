---
layout: default
clicks: 5
---
# 交易时机 - Cheat-On-Open
<hr>

```python {all|36-47|58|49-51|30-34}{maxHeight:'400px'}
# 示例 7
%matplotlib inline

import datetime
import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind
from tabulate import tabulate
from collections import defaultdict
from backtrader import Strategy

from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")

class TestStrategy(Strategy):
    def __init__(self):
        self.order = None
        fast = btind.SMA(period = 5)
        slow = btind.SMA(period = 10)
        
        self.crossover = btind.CrossOver(fast, slow)
        
    def notify_order(self, order):
        if order.status == order.Completed:
            price = round(order.executed.price, 2)
            print(order.data._name, order.ordtypename(), order.size, price)
            print("\n")
            
    def next_open(self):
        if self.order:
            self.cancel(self.order)
            
        dt = self.data.datetime.date()
        if not self.position:
            if self.crossover > 0:
                print('{} Send Buy, open {:.2f}'.format(dt, self.data.open[0]))
                self.order = self.buy(size=100)
        elif self.crossover < 0:
            print('{} Send Close, open {:.2f}'.format(dt, self.data.open[0]))
            self.order = self.close()
            
    def next(self):
        dt = self.data.datetime.date()
        print(f"in next: {dt}, {self.crossover[0]}, {self.data.open[1]:.2f}")
            
cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("000001.XSHE", 100)
cerebro.adddata(data, name="平安银行")
results = cerebro.run(cheat_on_open = True)

cerebro.plot(iplot = False, volume=False)
```

<show at="5">

<TopLayer vcenter>

```text
in next: 2023-04-06, 0.0, 12.27
in next: 2023-04-07, 0.0, 12.34
in next: 2023-04-10, 1.0, 12.40
2023-04-11 Send Buy, open 12.40
2023-04-11 平安银行 Buy 123 12.4


in next: 2023-04-11, 0.0, 12.28
in next: 2023-04-12, -1.0, 12.11
2023-04-13 Send Close, open 12.11
2023-04-13 平安银行 Sell -123 12.11
```
</TopLayer>
</show>

<!--
我们已经进行了好几笔交易，但从输出信息来看，backtrader并没有提示我们它是如何进行撮合的。比如，它是使用当天的数据？还是次日数据？这一节我们就来讨论这个问题

对于交易订单生成和执行时间，Backtrader 默认是 “当日收盘后下单，次日以开盘价成交”，这种模式在回测过程中能有效避免使用未来数据。考虑到开盘价是集合竞价形成的，那么只要我们在集合竞价中报一个最高价，在不考虑成交量的情况下，就必然能以开盘价成交，因此以次日开盘价成交，是可以真实复现的。

上述模式也可能出现一些小小的问题，比如，我们是在next函数中计算信号并发出买入指令的。此时计算size，我们会使用当天的收盘价进行计算。但以这个价格计算出的size，如果开盘价比较高，就可能导致第二天因资金不足失败。

为了应对一些特殊交易场景，Backtrader 还提供了一些 cheating 式的交易时机模式：Cheat-On-Open 和 Cheat-On-Close。

Cheat-On-Open是当日下单，当日以开盘价执行的方式。

这段代码展示了如何使用cheat-on-open模式。

# CLK1

使用cheat-on-open方式，需要将策略写在next_open中，而不是常见的next方法中.

# CLK2

此外，我们还需要在cerebro.run中，指定cheat_on_open参数为True

# CLK3

我们在next方法中，只打印当前时间、信号，这里注意，我们通过self.data.open[1]来偷看了次日的开盘价。backtrader允许偷看，部分削弱了它通过封装避免未来数据所做出的努力。

# CLK4

在notify_order方法中，我们打印执行完成的order的信息，价格正是我们在next方法中偷看的价格

# CLK5

从输出中可以看出，next提前一天发出信号，或者说，在next_open中，它仍然使用的是前一天的信号，但交易发生在当天开盘，利用了当天的开盘价信息。

买入和平仓都是在开盘时完成的。


-->
