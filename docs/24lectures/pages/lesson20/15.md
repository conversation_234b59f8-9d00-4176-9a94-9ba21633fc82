---
layout: default
clicks: 4
---
# 交易时机 Cheat-On-Close
<hr>

```python {all|37-48|55|30-35}{maxHeight: '400px'}
# 示例 8
%matplotlib inline

import datetime
import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind
from tabulate import tabulate
from collections import defaultdict
from backtrader import Strategy

from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")

class TestStrategy(Strategy):
    def __init__(self):
        self.order = None
        fast = btind.SMA(period = 5)
        slow = btind.SMA(period = 10)
        
        self.crossover = btind.CrossOver(fast, slow)
        
    def notify_order(self, order):
        if order.status == order.Completed:
            price = round(order.executed.price, 2)
            dt = self.data.datetime.date()
            print(dt, order.data._name, order.ordtypename(), order.size, price)
            print("\n")
            
    def next(self):
        if self.order:
            self.cancel(self.order)
            
        dt = self.data.datetime.date()
        if not self.position:
            if self.crossover > 0:
                print('{} Send Buy, price {:.2f}'.format(dt, self.data.close[0]))
                self.order = self.buy(size=123)
        elif self.crossover < 0:
            print('{} Send Close, price {:.2f}'.format(dt, self.data.close[0]))
            self.order = self.close()
            
cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("000001.XSHE", 100)
cerebro.adddata(data, name="平安银行")
cerebro.broker.set_coc(True)
results = cerebro.run()

cerebro.plot(iplot = False, volume=False)
```

<show at="4">
<TopLayer vcenter>

```text
2023-04-10 Send Buy, price 12.38
2023-04-11 平安银行 Buy 123 12.38


2023-04-12 Send Close, price 12.18
2023-04-13 平安银行 Sell -123 12.18


2023-04-18 Send Buy, price 12.69
2023-04-19 平安银行 Buy 123 12.69
```
</TopLayer>
</show>
<!--

cheat-on-close是指用当天的收盘价来成交，而不是推迟到下一天用开盘价成交。
使用cheat-on-close模式的要点是
1. 策略实现在next中
2. 在回测运行之前，要设置coc模式

# CLK1
这次我们的策略实现在next方法中，这是与cheat-on-open不一样的地方

# CLK2
这里设置cheat-on-close模式，注意这次我们的设置方法不一样。在run方法的参数中，没有cheat_on_close或者coc参数，因此我们只能通过broker的方法来进行设置

# CLK3
notify_order的方法仍然与coo一样

现在，我们来看看输出结果有什么不同。
# CLK4
在next方法中，我们打印的是当天的收盘价信息。通过notify_order，我们确认最终执行的价格，也正是当天收盘价。

-->
