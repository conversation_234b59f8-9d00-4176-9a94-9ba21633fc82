---
layout: two-cols
clicks: 4
---
# 优化
<hr>

::left::

```python {all|5|29|30|32-39}{maxHeight: '400px'}
from backtrader.analyzers import TimeReturn
import backtrader as bt

class TestStrategy(Strategy):
    params = (('fast', 5), ('slow', 10))
    def __init__(self):
        self.order = None
        fast = btind.SMA(period = self.p.fast)
        slow = btind.SMA(period = self.p.slow)
        
        self.crossover = btind.CrossOver(fast, slow)
            
    def next(self):
        if self.order:
            self.cancel(self.order)
            
        if not self.position:
            if self.crossover > 0:
                self.order = self.buy(size = 800)
        elif self.crossover < 0:
            self.order = self.sell(size = 800)
            
cerebro = bt.Cerebro()

data = await get_sample_feed("000001.XSHE", 100)
cerebro.adddata(data, name="平安银行")
cerebro.addanalyzer(TimeReturn, timeframe=bt.TimeFrame.Years)

cerebro.optstrategy(TestStrategy, fast = (5, 10), slow = (20, 30))
results = cerebro.run(maxcpus = 4, optreturn = True)

returns = []
for r in results:
    analyzer = r[0].analyzers
    fast, slow = r[0].params.fast, r[0].params.slow
    item = [(k,v) for k,v in analyzer[0].get_analysis().items()]
    returns.append((fast, slow, f"{item[0][1]:.2%}"))

print(tabulate(returns, ["fast", "slow", "pnl"]))
```

::right::

<show at="4">

```text
  fast    slow  pnl
------  ------  ------
     5      20  1.44%
     5      30  -0.64%
    10      20  1.92%
    10      30  -0.64%

```
</show>

<!--

关于回测的运行方式，我们已经多次接触到了run方法，也在上一张slide中，提到了cerebro.optstrategy方法。实际上，有一些策略，我们知道需要调整参数，如何尝试出最优参数，这件事可以让backtrader来做。

# CLK1
我们通过一个双均线策略来说明优化过程。这个策略有两个参数，快线和慢线的计算参数。

# CLK2
我们通过optstrategy,而不是addstrategy来加入策略。这里每个参数都指定了一个元组，所以组合下来，将生成4个Strategry实例。

# CLK3
我们仍然使用cerebro.run方法来运行回测。考虑到我们一共要运行4次回测，所以我们设置maxcpus=4，这样可以让4个核同时运行。optreturn这里设置为False，这样，cerebro.run的返回值，就会与我们前面的运行结果一致，否则，返回的结果将是一个OptReturn对象，它只只包含params和analyzers，而策略所包含的datas， observers，indicators都被移除掉了。不过，对我们这里的结果并没有影响。

# CLK4
接下来我们处理回测结果，显示了各个参数下的最终收益。

在进行优化回测时，这里使用了多进程。父子进程在传递参数时，需要进行序列化操作。如果你的程序在未进行优化时可以正常运行，但在参数优化时无法正常运行，请考虑是否遇到序列化问题。

-->
