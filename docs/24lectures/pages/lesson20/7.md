---
layout: two-cols
---
# Order (订单)
<hr>

::left::

<v-clicks>

## 创建订单
## 订单通知
## 订单状态
</v-clicks>
::right::

<show at="1">

```python
class Strategy:
    def buy(self, data=None, size=None, 
            price=None, plimit=None, exectype=None, 
            valid=None, tradeid=0, oco=None,
            trailamount=None, trailpercent=None,
            parent=None, transmit=True, **kwargs):
    """
    data: 订单关联的标的。默认为self.data0。
    size: 订单委托数量。默认通过getsizer获取。
    price: 默认为市价；当指定时，将使用该价格进行委托。
    plimit: 仅用于在StopLimit单中指定限价
    exectype: 订单类型，默认为市价单，可选类型有：
        1. Order.Market 下一个bar的开盘价
        2. Order.Close 下一个bar的收盘价
        3. Order.Limit 限价单
        4. Order.Stop 止损单
        5. Order.StopLimit 止损限价单
        6. Order.StopTrail 跟踪止损单
        7. Order.StopTrailLimit 跟踪止损限价单
    valid: 订单有效期
    tradeid：跟踪同一资产的多个交易，用在订单通知中
    **kwargs： 生成特定订单时可能需要
    """
```
</show>

<show at="2">

```python
class Strategy:
    def notify_order(self, order):
        '''
        接收Order变更通知
        '''
        pass
```
</show>

<show at="3">

- Order.Created
- Order.Submitted
- Order.Accepted
- Order.Partial
- Order.Complete
- Order.Rejected
- Order.Margin
- Order.Cancelled
- Order.Expired
</show>

<!--
# CLK1

当我们在Strategy中调用buy, sell和close时，就会创建一个Order。这里给出了Buy方法的签名，
我们来解读一下它的参数。

data: 订单关联的标的。默认为self.data0。注意bt与标的关联时，它并不是使用的证券代码或者名字等更容易记忆，或者从内存占用上更小的对象，而是使用了data feed。可能部分原因是，一是bt并不要求给data feed指定证券代码或者名字，从而在创建Order时，可能获取不到这个名字；二来data feed还包含了当前时间、价格等信息，这些信息最终在创建Order时使用了。感兴趣的同学可以看看order.py文件中的__init__方法。
size: 订单委托数量。默认通过getsizer获取。
price: 默认为市价；当指定时，将使用该价格进行委托。
plimit: 仅用于在StopLimit单中指定限价
exectype: 订单类型，默认为市价单，可选类型有：
    1. Order.Market 下一个bar的开盘价
    2. Order.Close 下一个bar的收盘价。注意在A股，只有日线及以上周期的收盘价，才是可以在交易中使用的（利用集合竞价机制），在其它情况下使用收盘价回测，其结果在实盘中将无法重现。
    3. Order.Limit 限价单
    4. Order.Stop 止损单，需要指定止损价格（Stop Price），一旦股价突破止损价格，将会以市价单的方式成交；
    5. Order.StopLimit 止损限价单，需要指定止损价格（Stop price）和限价（Limit Price），一旦股价达到设置的止损价格，将以限价单的方式下单
    6. Order.StopTrail 跟踪止损单
    7. Order.StopTrailLimit 跟踪止损限价单
关于订单类型，不同的交易系统有自己的规则，这些规则也可能随时改变。在进行A股回测时，一般只使用Market、Limit两种。
valid: 订单有效期，只在实盘中有效，且取决于交易代理能否实现。一般不用。
tradeid: 跟踪同一资产的多个交易，用在订单通知中。它是由用户自己在调用buy等方法时指定的。
**kwargs： 生成特定订单时可能需要

此外，在生成订单时，系统还将为每个订单生成一个惟一的编号，被称为ref。

每个订单还有创建时间，保存在属性created中。如果订单被执行，则属性executed中保存了size和price信息。

# CLK2
当订单状态发生改变时（比如被提交、被执行等），就会发出一个通知。我们可以在Strategy中，通过重写notify_order方法，来接收这个通知。

notify_order会在next方法之前被调用。
同一个order，在同一个next周期中，被调用多次，每次可能具有不同、或者相同的状态，可能至少有三个状态会被通知，Submitted, Accepted, Completed，此外，在实盘中，还可能出现多次Order.Partial通知，即一个委托被多次部分成交。这在回测中不会出现，但在实盘中常常出现。这些概念，在其它系统中也是一样的。

# CLK3
在这些状态中，我们主要关心的有Partial, Complete, Rejected和Cancelled。在我们的Strategy中，可能需要对这些状态进行相应的处理。Margin状态会发生在现金不够的情况下。
-->
