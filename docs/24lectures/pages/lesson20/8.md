---
layout: default
clicks: 5
---
# Order (订单)
<hr>

```python {all|38,39|47,56|50,59|26-35} {maxHeight: '400px'}
# 示例 5 Order Status Transmittion
%matplotlib inline

import datetime
import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind

from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")

class TestStrategy(bt.Strategy):
    params = dict(
        pentry=0.015,
        plimits=0.03,
        valid=10,
    )

    def notify_order(self, order):
        dt, dn = self.datetime.date(), order.data._name
        created = bt.num2date(order.created.dt).date()
        size = order.size
        p1 = round(order.created.price, 2)
        p2 = round(order.executed.price, 2)
        
        print('{} {} Order {}: {}\t{}\t{}\t{}\t{}'.format(
            dt, dn, order.ref, order.getstatusname(), created, size, p1, p2)
        )

    def __init__(self):
        self.o = []
        self.holding = 0 # holding periods per data

    def next(self):
        print(f" === {len(self.data)}th day ===")

        dt, dn, d = self.datetime.date(), self.data._name, self.data
        pos = self.getposition(d).size

        if not pos and not self.o:  # no market / no orders
            if dt.weekday() == 3:
                self.o = [self.buy(data=d)]
                print('{} {} Buy {}'.format(dt, dn, self.o[0].ref))

                self.holding = 0

        elif pos:  # exiting can also happen after a number of days
            self.holding += 1
            if self.holding >= 2:
                o = self.close(data=d)
                self.o.append(o)  # manual order to list of orders
                print('{} {} Manual Close {}'.format(dt, dn, o.ref))


cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("600000.XSHG", 6)
cerebro.adddata(data, name="浦发银行")

results = cerebro.run()

cerebro.plot(iplot = False, volume=False)
```

<show at="5">
<TopLayer center>

```text
=== 1th day ===
=== 2th day ===
2023-08-10 浦发银行 Buy 48
2023-08-11 浦发银行 Order 48: Submitted	2023-08-10	1	7.42	0.0
2023-08-11 浦发银行 Order 48: Accepted	2023-08-10	1	7.42	0.0
2023-08-11 浦发银行 Order 48: Completed	2023-08-10	1	7.42	7.44
=== 3th day ===
=== 4th day ===
2023-08-14 浦发银行 Manual Close 49
2023-08-15 浦发银行 Order 49: Submitted	2023-08-14	-1	7.1	0.0
2023-08-15 浦发银行 Order 49: Accepted	2023-08-14	-1	7.1	0.0
2023-08-15 浦发银行 Order 49: Completed	2023-08-14	-1	7.1	7.12
=== 5th day ===
=== 6th day ===
 ```
</TopLayer>
</show>

<!--
这段代码中实现了一个非常简单的交易逻辑，在周三当天买入，持有2天后卖出。后面我们还将丰富这段代码，以展示更加复杂的交易场景。

我们通过weekday方法来判断今天是否为周三。研究表明，A股存在节假日效应，所以有时候我们在调仓时，会根据weekday来决定是否调仓，比如在小市值策略中。

# CLK1
我们将orders保存在self.o对象中，将当前持仓时间保存在self.holding中

# CLK2
如果当前没有持仓，也没有orders，则执行买入；如果有持仓且超过2天则卖出。

# CLK3
在每个next周期，我们先打印当前周期序号，然后在buy和sell执行后，将这一动作打印出来

最后，我们在notify_order中，将order的一些属性输出出来。

# CLK4

这里我们输出了当前日期，feed名字，order的编号--这是通过order.ref来指定的

order有created和executed两个属性，都是OrderData类型。created保存了创建order时的一些信息，在order生命期中都保持不变；executed则保存了order执行之后的数据。

在示例中，我们输出创建时的价格和执行时的价格，以及创建时间

# CLK5
从输出信息可以看出，每次buy和sell都会触发三次通知，此时order的状态分别从提交、接受转移为完成。只有在完成时，exectued属性中的price数据才更新，它是实际执行的平均价格。
-->
