---
layout: section
---
# Broker (交易代理)

<!--
交易代理是继前面介绍过的概念之后，最重要的一个概念。在Backtrader中,无论是实盘交易还是模拟回测,都需要设置Broker对象来处理交易流程。

首先,策略的buy和sell操作会生成Order(订单)对象。Order对象中包含了订单的关键信息,如数量、类型、价格等。

然后,Order对象会提交到Broker进行处理。Broker根据订单类型、资金情况等来决定订单的成交执行。

一旦订单成交,Broker就会更新内部的账户状态,如持仓、现金等。同时Order的状态也会更新为Completed(完成)。

最后,策略可以通过Broker提供的接口来查询账户的实时状态,如持仓、资金等,以供策略调整使用。

所以简单来说,在Backtrader内部,一个完整的交易流程就是:策略生成Order订单 -> Broker接收并处理订单 -> 反馈账户和Order状态 -> 策略调整。

Broker作为中间层,扮演着执行交易的关键角色。
-->
