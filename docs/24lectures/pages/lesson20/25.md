---
layout: default
---
# 小结
<hr>

<v-clicks>

## 1. 可视化问题
## 2. 交易规则问题
## 3. 复权问题
## 4. 撮合问题
</v-clicks>

<!--
到此为止，关于backtrader的方方面面基本上都介绍到了。我们知道了如何加载数据、定义策略、定义指标、运行策略、掌握了一些下单知识，还了解了如何绘图和优化。

# CLK1

前面已经介绍过，backtrader绘制的图形，在k线图的表示方法上是与A股不一致的。我们已经介绍了如何修改。

# CLK2
如果我们的回测是基于日线的，那么backtrader的机制没有问题。如果回测的主周期是日内的，显然backtrader是无法处理T+1交易规则限制的。此外，backtrader不能处理涨跌停的情况。

# CLK3
在复权问题上，backtrader要求我们导入的数据是已进行了复权处理的。但是，尽管backtrader 不存在偷看数据问题，使用复权数据本身，就已经引入了未来数据。在这方面，大富翁和聚宽的框架都是实时复权，可以避免这样的问题。

# CLK4
我们已经介绍了撮合上存在哪些问题，也给出了减轻这个问题的方法。backtrader这种机制，它的回测数据与撮合数据没有分开，就会导致如果回测粒度太小，速度慢，很多小周期的数据策略并不关注，加载它们是一种浪费；但在撮合时，数据粒度却是越细越精准。这是backtrader及类似的框架解决不了的问题。
-->
