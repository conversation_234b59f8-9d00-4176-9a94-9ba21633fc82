---
layout: two-cols
clicks: 2
---
# 成交量限制
<hr>

::left::

<show at="0">

## FixedSize
</show>

<show at="1">

## FixedSize
## FixedBarPerc
</show>

<show at="2">

## FixedSize
## FixedBarPerc
## BarPointPerc
</show>

::right::

<show at="0">

```python
# 通过 BackBroker() 类直接设置
cerebro = Cerebro()
filler = bt.broker.fillers.FixedSize(size=100)
newbroker = bt.broker.BrokerBack(filler=filler)
cerebro.broker = newbroker

# 通过 set_filler 方法设置
cerebro = Cerebro()
cerebro.broker.set_filler(bt.broker.fillers.FixedSize(size=100))
```
</show>

<show at="1">

```python
# 通过 BackBroker() 类直接设置
cerebro = Cerebro()
filler = bt.broker.fillers.FixedBarPerc(perc=50)
newbroker = bt.broker.BrokerBack(filler=filler)
cerebro.broker = newbroker

# 通过 set_filler 方法设置
cerebro = Cerebro()
cerebro.broker.set_filler(bt.broker.fillers.FixedBarPerc(perc=50))
# perc 以 % 为单位，取值范围为[0.0,100.0]
```
</show>

<!--
当我们发出一个交易委托时，回测框架如何进行撮合，在设计上有很多难点，最难的可能是成交量匹配的策略。如果回测框架只有普通的行情数据，那么它只能看到价格和已经成交的成交量。如果委托价能够匹配上，此时回测框架应该：

1. 允许仅按价格进行成交吗？
2. 允许把所有的成交量都匹配给回测中的策略吗？

如果我们只允许按成交量的一部分（0,1)撮合回测委托，显然，由于A股的涨停机制，这将不合理地阻止我们在涨停板上卖出股票，在跌停板块买入股票。

如果我们不进行任何限制，这也会使得策略占不少便宜，这样的策略在实盘中，效果将大打折扣。

因此，这些问题没有正确答案，两种策略都可能是对的，但都只在特定的条件下正确。实际上，即使有order book，也不一定就能有正确答案，因为存在时间差，可能当你看到order book上有对手盘而进行下单时，等到真正下单时，对手盘又撤销了的情况。

因此，我们的策略一定要考虑这些情况，特别是高频策略。这也是高频策略量化中比较难做的地方。

在backtrader中，提供了四种方式来进行限制。

首先是通过FixedSize。我们通过broker.fillers.FixedSize生成一个Sizer对象
然后将它赋值给broker
也可以直接通过broker.set_filler来完成

设置了FixedSize之后，委托将按委买量、sizer限制和当天实际成交量中的最小者来填单。未被执行的部分，会在第二个周期来临时被取消。

# CLK1
FixedBarPerc执行逻辑与FixedSize类似

# CLK3
BarPointPerc比较复杂，它的本意是将成交量在[low, high]区间里进行均匀划分，然后根据成交价，决定有多少成交量是可以供撮合的。不过从源码来看，它的实现不一定正确。

最后一种方式，当我们不提供fillers对象时，就是不限制成交的模式。实际回测中，可以先用不限成交量的方式进行回测，然后按FixedBarPerc的方式，比如使用5%的成交量，再加上适当滑点运行几次，通过这样的试探来发现真实的成交量限制。
-->
