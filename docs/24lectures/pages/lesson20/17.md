---
layout: default
clicks: 2
---
# 组合(bracket)交易
<hr>

<show at="0">

```python
buy_bracket(# 主订单的参数
            data=None, size=None, price=None,
            plimit=None,exectype=bt.Order.Limit,
            valid=None, tradeid=0,
            trailamount=None, trailpercent=None,
            oargs={},
            # 止损单的参数
            stopprice=None, stopexec=bt.Order.Stop, stopargs={},
            # 止盈单的参数
            limitprice=None, limitexec=bt.Order.Limit, limitargs={},
            **kwargs):......
```
</show>

<show at="1|2">

```python {all}{maxHeight:'400px'}
# 示例 10
import backtrader as bt

class TestStrategy(bt.Strategy):
    params = dict(
        ma=bt.ind.SMA,
        win1=5,
        win2=10,
        stop=0.05,
        limit=0.05
    )

    def notify_order(self, order):
        print('{}: Order({}, {}) {} {:.2f}'.format(
            self.data.datetime.date(0),
            order.ref, order.ordtypename(),
            order.getstatusname(),
            self.broker.getvalue()))

        if order.status == order.Completed:
            self.holdstart = len(self)

        if not order.alive() and order.ref in self.orefs:
            self.orefs.remove(order.ref)

    def __init__(self):
        fast, slow = self.p.ma(period=self.p.win1), self.p.ma(period=self.p.win2)
        self.cross = bt.ind.CrossOver(fast, slow)

        self.orefs = list()

    def next(self):
        if self.orefs:
            return

        if not self.position:
            if self.cross > 0.0:
                close = self.data.close[0]
                p1 = math_round(close, 2)
                p2 = math_round(p1 * (1 - self.p.stop), 2)
                p3 = math_round(p1 * (1 + self.p.limit), 2)

                print("buy_bracket", p1, p2, p3)
                os = self.buy_bracket(size=700, price = p1, stopprice=p2, limitprice=p3)

                self.orefs = [o.ref for o in os]


cerebro = bt.Cerebro()
data = await get_sample_feed("000001.XSHE", n=40, end=datetime.date(2023, 8, 15))
cerebro.adddata(data)
cerebro.addstrategy(TestStrategy)
cerebro.addanalyzer(
    bt.analyzers.TimeReturn, 
    timeframe=bt.TimeFrame.Months
)
results = cerebro.run()
analyzer = results[0].analyzers[0]
print(analyzer.get_analysis())
```
</show>

<show at="2">
<Loc fc="white" top="11vh">

```text
buy_bracket 11.34 10.77 11.91

2023-07-06: Order(885, Buy) Submitted 9958.00
2023-07-06: Order(886, Sell) Submitted 9958.00
2023-07-06: Order(887, Sell) Submitted 9958.00

2023-07-06: Order(885, Buy) Accepted 9958.00
2023-07-06: Order(886, Sell) Accepted 9958.00
2023-07-06: Order(887, Sell) Accepted 9958.00

2023-07-06: Order(885, Buy) Completed 9958.00

2023-07-28: Order(887, Sell) Completed 10427.00
2023-07-28: Order(886, Sell) Canceled 10427.00

# 收益分析
[((2023, 6, 30, 0, 0), 0.0), 
((2023, 7, 31, 0, 0), 0.043),
((2023, 8, 31, 0, 0), 0.0)]

```
</Loc>
</show>

<!--
前面的交易都是独立的交易，每个订单之间彼此没有依赖。但有时候，我们希望针对主定单自动生成止损单(stop order)和止盈单(limit order)。这时候我们就需要使用 buy_bracket和sell_bracket，后者用以可以做空的场合。

buy_bracket() 用于多头交易场景，买入证券后，如果价格下跌，希望通过止损单卖出证券，限制损失；在价格上升时，希望通过限价单卖出证券，及时获利，通过 buy_bracket() 可以同时提交上述 3 个订单，而无须单独提交三次。

在最简单的例子中，我们只需要传入stopprice和limitprice就可以了。

这里我们以平安银行在2023年8月15日前50天的k线为例，使用双均线策略，它将在7月6日出现一个金叉，此时我们下买单，同时设定下跌5%时止损，上涨5%止盈。最后，该订单将触发止盈。

# CLK1
在这段代码中，我们先是通过第44行，在金叉发生时，下了一个组合订单，并将组合订单保存在self.orefs中。

然后在第13行到第18行中，打印出我们接收到的每一个通知。这里我们输出当前日期、订单号、订单类型、状态和当前市值。

//我们在第22行、23行，把状态已不属于alive的订单移除掉。

为了方便进行收益分析，确定是否触发了止盈，我们添加了一个TimeReturn分析器，该分析器将按月汇总收益。这是在代码第53到56行中添加的。

最后，我们通过58，59行的代码，把收益分析打印出来。

# CLK2
在notify_order的输出中，我们先是注意到在7月6号，通过buy_bracket增加了三个订单，一个主订单，是买入订单，两个卖出订单，一个是止损，一个是止盈。

接下来的三行，是这些订单被接受。在买入时，我们指定了size为700，如果我们指定size为1000，这将超出可用现金范围，这些订单就会被拒绝，大家可以自行尝试一下。

第11行，主买订单完成，如果现在查询持仓，我们将看到一个size为700的持仓。

第13行，7月28日，止盈订单触发，卖出操作完成。注意这里没有submitted和accepted两个状态
第14行，同一交易日，由于止盈订单触发，止损订单也因此取消。
-->
