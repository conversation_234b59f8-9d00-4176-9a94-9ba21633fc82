---
layout: two-cols
---
# 交易函数
<hr>

::left::
<v-clicks>

## 1. buy
## 2. sell
## 3. close
## 
## 4. order_taget_size
## 5.order_target_value
## 6. order_target_percent
## 7. cancel
</v-clicks>

::right::
<show at="1|3">

```python
# 示例 9
class TestStrategy(bt.Strategy):
    def notify_order(self, order):
        dt = self.datetime.date()
        if order.status == order.Completed:
            price = round(order.executed.price, 2)
            print(dt, order.ordtypename(), price, order.size)
        
    def next(self):
        print("in next:", len(self.data), self.datetime.date())
        if len(self.data) == 1:
            self.buy(size = 500)
        elif len(self.data) == 2:
            self.sell(size = 200)
        else:
            self.close()
        
cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("000001.XSHE", 4)
cerebro.adddata(data)

cerebro.run()
```
</show>

<show at="4">

```text
in next: 1 2023-08-11
2023-08-14 Buy 11.76 500

in next: 2 2023-08-14
2023-08-15 Sell 11.69 -200

in next: 3 2023-08-15
2023-08-16 Sell 11.67 -300

in next: 4 2023-08-16
```
</show>
<show at="5|7">

```python
class TestStrategy(bt.Strategy):
   def next(self):
      # 按目标数量下单
      self.order = self.order_target_size(target=size)
      # 按目标金额下单
      self.order = self.order_target_value(target=value)
      # 按目标百分比下单
      self.order = self.order_target_percent(target=percent)
```
</show>

<show at="8">

```python
self.cancel(order)
```
</show>

<!--

#CLK1-3
这里buy, sell, close都是常规交易。close是平仓函数，在A股回测中，则于是单向交易，所以它在不带size参数调用时，相当于卖出所有持仓；如果带上size参数，则相当于sell

# CLK4
示例9的输出如右图所示

我们先是在8月11日买入了500股
然后在下一个交易日卖出200股，此时order.size为负数
最后，在第三个交易日，即8月15日，我们下达了平仓指令
但实际执行是在第4个交易日，即8月16日
显然，根据前一张slide所学的内容
我们可以知道，这里的交易时机是backtrader默认的最常用的交易时机
即次日开盘时，以开盘价成交

[BACK AND MARK]
如果我们在第21行，这里只取3个bar，会发生什么情况？
最后的平仓指令会下达
但不会执行，所以我们不会看到8月16日的卖出

# CLK5

order_target系列函数是指，当下单后，持仓会达到target的状态。比如，order_target_size是指，假设当前持仓为n, target指定为m，如果m-n为正数，则买入m-n股，否则，卖出m-n股。比如，当前持仓为-3, target为-7, m-n为-4，则还要卖出4股。

# CLK6
order_target_value与order_target_size一样，不过最终是要按市值来算。市值计算用当前Bar的close计算，但买入卖出操作还是以下一根bar的开盘价算。这里也有空单和多单的考虑，由于我们只在A股上实践，所以就不详细介绍了。

# CLK7
order_target_percent，是按当前账户的总资产的百分比来决定如何补足仓位

# CLK8
上述交易函数都会返回一个订单对象order，我们可以通过cancel方法来取消订单。
-->
