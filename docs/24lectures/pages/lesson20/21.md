---
layout: two-cols
---
# observers
<hr>

::left::
<v-clicks>

## 1. Broker
##
## 2. BuySell
##
## 3. Trades
##
## 4. TimeReturn
##
## 5. DrawDown
##
## 6. Benchmark
##
</v-clicks>
::right::

<show at="1">

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-broker.png)
</show>

<TopLayer at="2" vcenter>

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # print(self.stats.broker.cash[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.Cash)
cerebro.addobserver(bt.observers.Value)
cerebro.addobserver(bt.observers.Broker)
cerebro.run()
cerebro.plot(iplot = False)
```
</TopLayer>

<show at="3">

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-buysell.png)
</show>

<TopLayer at="4" vcenter>

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # print(self.stats.buysell[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.BuySell)
cerebro.run()
cerebro.plot(iplot = False)
```
</TopLayer>

<show at="5">

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-trades.png)
</show>

<TopLayer at="6" vcenter>

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # print(self.stats.trades[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.Trades)
cerebro.run()
cerebro.plot(iplot = False)
```
</TopLayer>

<show at="7">

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-timereturn.png)
</show>

<TopLayer at="8" vcenter>

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # print(self.stats.timereturn[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.TimeReturn)
cerebro.run()
cerebro.plot(iplot = False)
```
</TopLayer>

<show at="9">

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-drawdown.png)
</show>

<TopLayer at="10" vcenter>

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # print(self.stats.drawdown[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.DrawDown)
cerebro.run()
cerebro.plot(iplot = False)
```
</TopLayer>

<show at="11">

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-benchmark.png)
</show>

<TopLayer at="12" vcenter>

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # print(self.stats.benchmark[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.Benchmark, 
                    data=data)
cerebro.run()
cerebro.plot(iplot = False)
```
</TopLayer>

<!--
我们先介绍几种常见的观察器，熟悉下它们绘制的图形。然后我们再演示如何实现这些绘图。
# CLK1
Broker观察器记录了broker 中各时间点的可用资金和总资产；可视化时，会同时展示 cash 和 values 曲线；如果想各自单独展示 cash 和 values，可以分别调用 backtrader.observers.Cash 和 backtrader.observers.Value。这个图显示了分别绘制cash, value和broker的情况，从上到下，依次是cash,value和broker。

# CLK2
这段代码演示了如何加载Broker观察器。我们构建了一个非常简单的策略，如果当前为奇数天，则买入，次日卖出。这一次，我们在实例化cerebro时，增加了一个stdstats = False的参数，这是什么意思呢？

backtrader把观察器的集合称为stats， 我们可以在strategy中访问这个stats属性，比如在被注释的第3行，这里我们就通过stats.broker.cash显示了当前可用资金。

Broker, Trades和BuySell 3个观察器被当成默认观察器，它们被称为stdstats，会自动被cerebro加载并绘图。因此，为了单独演示Broker，我们在第9行，传入了stdstas = False的参数，同时，这也要求我们通过第15到第17行，加入这些观察器。

在后面的代码演示中，我们将保持这个基本框架。

# CLK3
BuySell观察器记录回测过程中的买入和卖出信号；可视化时，会在价格曲线上标注买卖点。与Broker不一样，BuySell信号会自动叠加在主图上。

# CLK4
第3行代码，注意我们一样可以通过stats属性来访问buysell。这将打印每个买卖点上的价格信息。规则就是，将观察器名字全部小写化。
第15行，我们添加了BuySell这个观察器。

# CLK5
Trades：记录了回测过程中每次交易的盈亏（从买入建仓到卖出清仓算一次交易）；可视化时，会绘制盈亏点

# CLK6
第3行代码，这将打印出该时间点上，已结束的交易的盈亏数据

# CLK7
TimeReturn记录回测过程中的收益序列；可视化时，会绘制 TimeReturn 收益曲线。

# CLK8
第13行，这会打印出每天的收益，第一天收益则为0.
第15行，加入TimeReturn观察器。它与analyzer中的TimeReturn一样，可以带一个TimeFrame参数，如果我们不指定参数，就会使用主循环的默认周期

# CLK9
DrawDown记录了回测情况。基于这个数据，我们可以计算最大回撤时间和最大回撤比。

# CLK10

第13行，这会打印当前的回撤数据。

# CLK11

Benchmark用来记录业绩基准。在backtrader的官方示例中，benchmark应该与标的收益是两条曲线，绘制在同一个副图中，但在我们的实验中，只能绘制出benchmark线，标的收益并没有绘制。原因不明。

# CLK12
-->
