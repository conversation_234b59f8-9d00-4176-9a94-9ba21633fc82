---
layout: two-cols
clicks: 9
---
# 资产与持仓查询
<hr>
::left::

```python {all|4-5|7-8|10-11|13|7,10}
# 示例 4
class TestStrategy(bt.Strategy):
    def next(self):
        print('当前可用资金', self.broker.getcash())
        print('当前总资产', self.broker.getvalue())
        
        size = self.broker.getposition(self.data).size
        print('当前持仓量', size)
        
        price = self.getpositionbyname(self.data._name).price
        print('当前持仓成本', price)

        print('当前持仓', self.position.size)
        
cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("000001.XSHG", 1)
cerebro.adddata(data)

cerebro.run()
```
::right::

```text
当前可用资金 10000.0
当前总资产 10000.0
当前持仓量 0
当前持仓成本 0.0
当前持仓 0
```

<show at="6">
<TopLayer vcenter>

```python
class Position(object):
    def __bool__(self):
        return bool(self.size != 0)
```
</TopLayer>
</show>

<show at="7">
<TopLayer vcenter>

```python {8}
class Position(object):
    def __bool__(self):
        return bool(self.size != 0)

class MyStrategy(bt.Strategy):

    def next(self):
        if self.position:
            ...
```
</TopLayer>
</show>

<!--
我们使用只有1个bar的数据流来完成这个测试，这将导致next只被调用一次
因此输出也只有6行。

# CLK1
我们看到，可以通过broker的getcash, getvalue方法来来查询可用现金、总资产
注意backtrader使用value来表示总资产，即持仓市值与现金之和。在其它量化框架中，则可能使用asset或者这个词。

# CLK2

获取持仓数据需要特别提示下
我们既可以通过getposition方法
也可以通过getpositionbyname方法来获取持仓
getposition方法接受一个参数，这个参数是data feed
第7、8行演示了它的用法

# CLK3
我们也可以按名字来查询持仓
不过这个名字是data feed的名字
并不是通常意义上的证券名
除非我们在加入data feed时，就使用证券的名字来命名这个feed
第10、11行演示了getpositionbyname的用法

# CLK4
position本身也是一个属性
当查询datas[0]对应的持仓时
我们也可以只用self.position
第13行演示了它的用法

# CLK5
注意上面的示例中，还演示了一个用法
即我们既可以使用getposition和
getpositionbyname方法
既在self.broker中存在
也在strategy中存在
在第7行中
我们是通过broker来调用的
但在第10行中，我们是直接通过strategy来调用的

我们在上一课还问过一个问题
为什么self.position能作为条件表达式？
我们的猜测是它也进行了重载
现在，我们就透过backtrader的源码
来揭示这里究竟是如何重载的

# CLK6
很少人有会重写过__bool__这个方法。
通过这个方法
我们就可以写下这样的语句：

# CLK7
现在，即使self.position 不为None
但实际持仓数为零
也不会误判为有持仓
因为在self.position对象存在时
它会检查其size是否为零
如果为零
仍然返回False

这个例子过于简单
也不产生任何有效的结果
下面，我们通过一个复杂的例子
来演示仓位的变化
以及仓位查询
-->
