---
layout: two-cols
---
# 只使用信号交易
<hr>

::left::
```python {all|16}
# 示例2
class MySignal(bt.Indicator):
    lines = ('signal',)
    params = (('period', 30),)

    def __init__(self):
        sma = bt.indicators.SMA(period=self.p.period)
        self.lines.signal = self.data - sma               
        
cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

cerebro.add_signal(bt.SIGNAL_LONGSHORT, MySignal)
cerebro.run()
cerebro.plot(iplot = False)
```
::right::

![](https://images.jieyu.ai/images/2023/08/lesson20-siganls-only.png)

<!--
前面介绍的示例中，我们都毫不例外地使用了Strategy。但是，backtrader允许一种更简化的操作，即不使用策略，而只使用信号。这种情况下，实际上是backtrader帮我们生成了一个策略。

这里的signal的语法是，如果数值大于0，认为是做多信号；小于0是做空信号；等于0则不操作。在这些地方尤其要注意浮点数比较问题，建议先对收盘价及计算出来的sma都取整到小数点后两位，再进行比较，以避免两个数实际相等，但因浮点误差导致它们大于零、或者小于零，从而引起开平仓操作的情况。

# CLK1
第16行，我们添加一个信号时，需要指明信号对应的操作。示例中，我们使用的是LONGSHORT,其它可能的值还有LONG, SHORT.
我们先看参数为LONG的情况。当参数为LONG时，如果signal>0，则将开多仓；如果signal < 0，则将平多仓。
如果参数为SHORT，则刚好相反。
如果参数为LONGSHORT，则signal >0时开多仓，signal < 0时开空仓。当然我们这门课讲的是A股，所以一般我们用LONG作为参数就好了。

这里还有一种情况，就是可能会连续发生好几期的 signal > 0，或者signal < 0的情况，此时backtrader会将信号合并从而只处理一次。这也是我们自己写策略时，需要考虑的问题。
-->
