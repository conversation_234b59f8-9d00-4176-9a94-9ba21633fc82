# 欢迎使用大富翁量化环境！

在此环境中，您将能
* **获取A股数据**（实时及历史数据。历史数据从2005年以来到2023年底的全部行情数据，粒度细至分钟线数据）。
* **编写策略**，
* **运行回测**

**如果需要实盘下单接口，可联系我，为您提供低门槛的接入方式。微信号：quantfans_99**

下图显示的是一个布林带策略在环境中运行后的情况：

![布林带策略](https://images.jieyu.ai/images/2025/04/bolling-strategy.jpg)

在策略收益图上，每次买卖都有记录，便于优化：

![策略收益图](https://images.jieyu.ai/images/2025/04/backtest-result.jpg)

## 1. 文件中含有**courseware**

我的学习的课件，放在**courseware**当中。

!!! warning
    **注意courseware目录是只读的**。可以修改并运行，但无法**保存修改！！**。如果确实需要保存，将它们**提前拷贝到根目录**下面来。


根目录是工作区。可以在**根目录**下新建notebook文件。如果需要修改并保存示例文件，请将文件拷贝到此目录下，再进行修改和保存。

!!! tip
    在课程的多数示例中，我们需要使用 Zillionare（大富翁量化框架）提供的数据。您可以在[如何使用omicron](supplements/omicron.ipynb)中找到它们的使用方法。


## 2. Jupyter Lab简介

看到这个页面，可能你已经知道了，这是**jupter lab**。是基于jupter lab，自己改造的一个多用户系统。在这个系统中，提供的一些示例文件是只读的，大家共享的；但你们个人的写的代码，都会运行在一个只属于你们自己的容器docker中，是彼此不可见的。

!!! attention
    根据所报课程套餐的不同，您可能运行在共享服务器上，或者是专享服务器上。如果是运行在共享服务器上，那么，您在所有可读写的文件夹下创建的文件，其它人都是可见的！


可能有的人还不太了解jupyter lab。这是一种**交互式编程环境**，或称**探索式编程环境**。它由网页和服务器组成。网页由一个个单元格组成，在单元格里编写代码，或者描述性文字。这些代码在执行时，会被发送到后台服务器运行，然后将输出结果传送回浏览器，显示在对应的代码单元格下方。这种方式特别适合于数据科学家。作为从事数据分析的人，他们往往需要先加载一部分数据，绘图看看数据分布特性，再运行一个模型，得到结果并可视化，再决定下一步怎么做。这种风格被称为探索式编程。它区别于工程化编程的地方是，工程化编程的方向和目标都十分明确，基本上不存在推倒重来的情况。而在探索式编程中，一个模型不能用，推倒重来是很正常的事，并不意味着失败。

我们使用Jupyterlab来作为实验环境，是因为一方面我们需要给示例代码配上大量的说明性文字，方便大家理解；其次，我们对策略的探索也非常符合探索式编程的模型。

Jupyter lab的前身是jupyter notebook server，不过现在官方已决定弃用notebook了。注意，notebook即可能指代notebook server，也可能指代一个个具体的，后缀为.ipynb的文件。当它指代notebook server时，它是被官方弃用的；但jupyter lab使用的文件格式仍然是notebook，即后缀为.ipynb的这些文件。

如果对深入了解Jupyter Lab感兴趣，建议阅读[Notebook入门](notebook入门.ipynb)，以及[Notebook高级技巧](notebook高级技巧.ipynb)。

## 3. 编辑和运行

单元格有编辑模式和命令模式。当单元格处于命令模式，双击单元格即进入编辑模式。当编辑完成后，按住shift +
enter键即可执行，并切换到命令模式。在命令模式下，我们可以执行对单元格的一些操作，比如运行，在前、后插入新的单元格，删除单元格等等。

单元格可以是Markdown单元格，也可以是Python代码单元格，您需要根据自己的需要来手动进行切换。如果单元格是Markdown单元格，执行它意味着将Markdown文本渲染成为HTML格式。

提供的此环境已经安装好了所需安装包，全部模块直接运行即可，快捷键：shift+enter，或者点击：

![如何运行代码](https://images.jieyu.ai/images/2025/04/how-to-run-cell.jpg)

## 4. 故障排查

### 4.1. 不能保存Notebook
如果不能保存Notebook，请检查是否是以下情况之一：

1. 文件在只读目录下，比如courseware目录，assignments目录，这些都是只读目录。此时『存盘』按钮应该处于禁止状态。
2. kernel已断开这种情况有可能是您（或者共享容器的其它学员）自己安装了特别的Python库引起的。如果出现此类情况，请联系我修复。


### 4.2. 不能执行代码，或者点击运行没有响应
请确保已经连接到kernel。有时候，jupyter server需要较长时间来清除stale的session，此时需要等等。

![](https://images.jieyu.ai/images/2023/03/20230313141606.png)

## 5. Follow us

<div style="width: 100%;margin-bottom: 20px;">
<img src="https://images.jieyu.ai/images/hot/xhs-logo.jpg" width="120px"
align="left" style="margin: 40px 10px 0 0"/>

### 5.1. Quantide@小红书 （5万粉）
我们在[小红书](https://www.xiaohongshu.com/user/profile/5ba12feef7e8b9437f3aca0c)上有接近5万人关注。如果您按量化关键词进行搜索，再按用户排序，一眼就能看到我们。

欢迎点击[Follow](https://www.xiaohongshu.com/user/profile/5ba12feef7e8b9437f3aca0c)
</div>


<div style="width: 100%;margin-bottom: 20px;">
<img src="https://images.jieyu.ai/images/hot/logo/gzh.jpg" width="120px"
align="left" style="margin: 40px 10px 0 0"/>

### 5.2. Quantide@公众号

每日更新，内容涵盖 Python 编程和量化策略研究、量化框架开发。通过公众号，可以及时获得我们的内容更新，联系到课程助理。欢迎扫码关注。
</div>

<div style="width: 100%;margin-bottom: 20px;">
<img src="https://images.jieyu.ai/images/hot/logo/zhihu.png" width="120px"
align="left" style="margin: 40px 10px 0 0"/>

### 5.3. Quantide@知乎 （万粉）
我们在[知乎](https://www.zhihu.com/people/hbaaron)上有1.2万粉丝。欢迎点击[Follow](https://www.zhihu.com/people/hbaaron)
</div>
