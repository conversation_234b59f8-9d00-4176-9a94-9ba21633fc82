---
title: 布林带策略
---

布林带（Bollinger Band）是 20 世纪 80 年代由约翰·布林格（John Bollinger）开发的市场趋势技术指标，用于衡量市场波动性和价格动态。在整个 80 年代，这个指标都非常好用。

布林带由上、中、下三条线组成。位于中间的中轨线主要用于衡量中期趋势，通常是简单移动平均线，是上轨线和下轨线的基础。上下轨一般由均价的两个标准差计算得来。当股份触及上转时卖出，当股价触及下轨时买入。

如今，布林带是 MetaTrader 4 交易平台上的一种标准工具，主要用于衡量波动性和预测当前趋势是否发生反转。

某支股票在 2023 年 4 月期间的布林通道图：

![50%](https://images.jieyu.ai/images/2023/05/bollingerbands.png)

从图上可以粗略地看出，bolling 带似乎能给出较好的买卖点。但实际上还有几处失败的买点是我们没有标记出来的。

下面，我们看看布林带策略如何构建。这一次，我们将使用 coursea 库与 omicron 中的相关模块来简化策略编写。

## 1. 使用coursea的初始化
首先，我们在之前的代码中，都要导入比较多的模块，并且进行了初始化。在我们逐步熟悉这些模块和库之后，就可以省略这些繁复的步骤，改用课程环境中提供的初始化来简化这个过程：

!!! attention
    你应该在之前的课程中运行过 tushare 安装命令。如果没有，请先运行下面的命令：
    ```bash
    !pip install tushare
    ```

```python
from coursea import *
await init()
```

这相当于实现了下面的功能：

```markdown
import datetime
import logging
from typing import Dict, List, Optional, Union
import cfg4py
import numpy as np
import omicron
import pandas as pd
import talib as ta
from coretypes import Frame, FrameType
from omicron import tf
from omicron.extensions import (
    array_math_round,
    array_price_equal,
    bars_since,
    count_between,
    fill_nan,
    find_runs,
    math_round,
    price_equal,
    smallest_n_argpos,
    top_n_argpos
)
from omicron.models.security import Security
from omicron.models.stock import Stock
from omicron.plotting.candlestick import Candlestick
from omicron.plotting.metrics import MetricsGraph
from omicron.strategy.base import BaseStrategy
from traderclient import TraderClient

cfg = cfg4py.get_instance()

async def init():
    cfg4py.init("/etc/zillionare")
    await omicron.init()
```

## 2. 基于基类的布林带策略
其次，`omicron.strategy.base.BaseStrategy`库已经为我们提供了一个策略基类。这个基类封装了`trader-client`，提供了策略回测的基础框架。如果我们的策略子类继承于这个基类，那么，很多情况下，我们只需要实现构造函数和`predict`方法即可完成一个策略。这样策略的实现会变得非常简单，因此出错的机会也会变少。

```python
class BollingerBandsStrategy(BaseStrategy):
    def __init__(self, url: str, win: int = 20, **kwargs):
        """
        Args:
            url: 回测/实盘服务器
            win: 计算移动平均线的周期数
            k: 使用`k`个标准差来构成上下 band
        """
        self.win = win
        super().__init__(url, **kwargs)

    async def predict(self, frame: Frame, frame_type: FrameType, i: int, barss, baseline: str, sec: str):
        bars = await Stock.get_bars(sec, self.win + 1, frame_type, end=frame)
        if len(bars) < self.win + 1:
            return
        
        close = array_math_round(bars["close"], 2).astype(np.float64)

        hb, _, lb = ta.BBANDS(close, self.win) # type: ignore
        hb = array_math_round(hb[-2:], 2)
        lb = array_math_round(lb[-2:], 2)

        # 股价突破上轨卖出。注意‘突破’的实现
        if close[-1] > hb[-1] and close[-2] <= hb[-2]:
            if self.available_shares(sec, frame) > 0:
                await self.sell(
                    sec, percent=1.0, order_time=tf.combine_time(frame, 14, 55)
                )
            return

        # 股份突破下轨就买入。
        if close[-1] < lb[-1] and close[-2] >= lb[-2]:
            if len(self.positions(frame)) == 0:
                await self.buy(
                    sec, money=self.cash, order_time=tf.combine_time(frame, 14, 55)
                )
                print(i, frame, self.positions(frame))

    async def kplot(self, sec: str, start: Frame, end: Frame, frame_type: FrameType):
        bars = await Stock.get_bars_in_range(sec, frame_type, start, end)
        cs = Candlestick(bars)
        cs.add_indicator("bbands", win = self.win)

        close = array_math_round(bars["close"], 2)
        hb, _, lb = ta.BBANDS(close, self.win)

        xpos = np.argwhere(close > hb).flatten()
        cs.add_marks(xpos, hb[xpos] * 1.05, "upbreak", color="#00f")

        xpos = np.argwhere(close < lb).flatten()
        cs.add_marks(xpos, lb[xpos] * 0.95, "downbreak", color="#f00")

        cs.plot()

start = tf.day_shift(datetime.date(2022, 7, 4), 0)
end = tf.day_shift(datetime.datetime.now(), 0)

code = "002344.XSHE"
bbs = BollingerBandsStrategy(
    cfg.backtest.url, start=start, end=end, frame_type=FrameType.DAY
)

await bbs.backtest(sec=code,baseline=code)
await bbs.kplot(code, start, end, FrameType.DAY)
await bbs.plot_metrics()
```

## 3. 策略优化方向讨论

### 3.1. 参数优化
在布林带策略中，中轴均线的窗口大小，上下轴的标准差的倍数是显然易见的参数。不过，当我们使用 talib 来计算布林带时，标准差的个数是无法指定的。

talib.BBANDS 默认指定两个标准差。如果股份波动符合正态分布，那么约有 95%的数值会分布在距离平均值正负 2 个标准差的范围内。

### 3.2. 趋势判断

在上述的策略中，我们采用的方案是，股价突破上轨就卖出；跌破下轨就买入。策略在前面几次能很好地发出信号，但是，在 2030-4-3 日卖出后，如下图 1 和 2 标记点所示，股价一直上涨，但再也没有发出过买入信号。导错失这一段比较丰富的利润。

![75%](https://images.jieyu.ai/images/2023/05/bband-optimal-v2.png)

尽管我们**不应该**要求量化策略吃尽每一段利润，回避每一段风险，但是，我们仍然应该对每一个可疑的地方进行检查和讨论。

上图中，从 3 月 16 日最后一次触及下轨后，后面就一直在中轨（20 日均线）与上轨之间震荡，再也没有机会触及下轨。这是一种市场强势特征，但如果我们拘泥于布林带策略，则必然会失去这一部分利润。此时，应该退出布林带策略，改用其它策略，比如趋势类策略来进行跟踪。

现在的问题是，我们有何办法来检测到布林带策略的退出条件？

注意到布林带三条线之间的关系。中间的均线走势决定了方向。当这条线向上拐头后，尤其是三条线都向上拐头后，我们要注意到市场进入强势特征，此时再去等待股份触及下轨很可能不现实。我们可以把这种情况的出现，当成布林带策略的退出边界条件之一。

相反，如果均线各下拐头，特别是三条线都往下拐头时，此时市场走弱，我们也不应该在股价下破布林带时买入，因为此时还有可能进一步下跌，而且往往跌幅较大。请看下图：

![75%](https://images.jieyu.ai/images/2023/05/bbands-decline.png)

在图中，可以看出，如果在股份触及下轨时，我们判断一下三条线的走势，是可以避免买入的。

如何通过程序来判断某个时间序列的走势，我们将在第13章中详细讲解。
