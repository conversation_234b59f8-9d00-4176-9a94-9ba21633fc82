# Alphalens及其它

## 1. Alphalens
在上一章我们手工打造了一个简单的因子分析体系。我们也指出，这个体系在功能上是有局限的，比如，它不能进行多周期（T+1, T + 5, ...) 的因子分析。

Alphalens 补齐了这一功能缺失，同时提供了丰富的可视化分析功能。

![100%](https://images.jieyu.ai/images/2023/07/alphalens-framework.png?1)

### 1.1. Alphalens 调用流程

使用 alphalens 之前，我们首先要自行提取因子数据，并生成如下格式的 DataFrame:

![50%](https://images.jieyu.ai/images/2023/07/factor_df_format.png)

格式的关键是使用多重索引，并且索引名为 date 和 asset。

此外，我们还要生成 prices 数据，alphalens 会从其中提取计算 T+n 期的收益，从而与因子数据关联。 prices 数据也是一个 dataframe，其格式与 factor 有所不同：

![100%](https://images.jieyu.ai/images/2023/07/prices_df_format.png)

这里提供一段提取因子，并生成上述格式数据的代码片段供参考。

!!! attention
    为简洁起见，本段代码仅为演示。可运行代码请见本课同名附件。

```python

async def calc_rb_factor(codes, start, end):
    rb_factor = []
    prices = []
    start_ = tf.day_shift(start, -50)
    end_ = tf.day_shift(end, 10)
    
    for code in codes:
        bars = await Stock.get_bars_in_range(code, FrameType.DAY, start_, end_)
        ma = moving_average(bars["close"][:-10], 5)
        
        df = pd.DataFrame(bars[["frame", "close"]], columns=["frame","close"])
        df["code"] = [code] * len(bars)
        prices.append(df.iloc[50:-10])
        
        for i in range(50, len(ma)):
            score = round_bottom(ma[i-50:i]) or 0
            rb_factor.append((bars[i]['frame'].item().date(), code, score))
        
    factor = pd.DataFrame(rb_factor, columns = ["date","asset","score"])
    factor["date"] = pd.to_datetime(factor["date"], utc=True)
    factor = factor.set_index(['date','asset'])
    
    prices = pd.concat(prices).pivot(index='frame', columns='code', values='close')
    prices.index = pd.to_datetime(prices.index, utc=True)
    prices = prices.rename_axis('date')
    
    return factor, prices

start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 1, 1)
factor, prices = await calc_rb_factor(secs[:50], start, end)     
```

在上面的代码中，我们在对 codes 数组进行遍历时，同时生成了 prices 和 factor 数据。但两者构建方法不同。

对于 prices 数据，我们首先是在每一次迭代中，都生成了一个 dataframe, 该 dataframe 由 frame、close 和 code 三列组成，其中 code 列各行取值都相同，为当前迭代中的 code。最终在循环退出时，我们通过 pandas.concat 将这些 dataframe 在行生长的方向上联接起来，最后，我们通过 pivot 方法，将其转换成为以 frame 为索引、以 code 列的值为新的列名字、以 close 字段值为单元格值的新 dataframe。

下图显示了 pivot 转换过程：

![100%](https://images.jieyu.ai/images/2023/07/pivot_table.png)

对于 factor 数据，处理技巧上会平淡一些，在每个对 codes 的迭代中，我们通过另一个循环，生成对应标的在因子测试时间范围内的每个因子值，最终在外导循环结束后，我们得到一个 List[Tuple] 数组，Tuple 依次是 date, asset 和 score（因子值），然后我们将其转换成为 dataframe，并将 date 和 asset 设置成为多重索引，这样就满足了 alphalens 的要求。

### 1.2. 数据预处理

在获得正确的 factor 和 prices 之后，我们将调用 alphalens.utils 中的`get_clean_factor_and_forward_returns`方法来进行预处理。

这个方法将完成数据清洗、收益计算、因子数据与收益对齐和分组。

这个函数有 2 个输入变量：股票的因子值 (factor)， 股票的价格 (prices)，以及 7 个参数变量：
1. 股票的行业分组 (groupby)
2. 是否按行业分组 (binning_by_group)
3. 分组个数 (quantiles)
4. 直方图个数 (bins)
5. 因子换手周期 (periods)
6. 异常值阀值设定 (filter_zscore)
7. 行业分组标签 (groupby_labels)。
8. max_loss 经过预处理后，如果 drop 掉的数据占比超过 max_loss，则中止处理，抛出异常。
9. zero_aware 当为 True 时，分别按正值和负值各自进行 bin 切分。如果信号是零中心化的，且 0 为多空信号分界点时有用。
10. cumulative_returns，是否包含累积收益。
    
第一个输入变量是股票的因子值。第二个输入变量是股票的价格数据。这两者的数据格式已经在之前介绍过了。

第一个参数变量是行业分类 groupby，它可以是一个多重索引的序列，也可以是一个字典 (dict)。如果它是一个序列的话，那么其多重索引必须要包含之前的 factor 的多重索引；如果它是一个字典的话，那么其股票代码的个数也必须包含 factor 的股票代码个数。这都是为了保证每个股票都有行业分类。该值默认是空值 (None)， 也就是默认不按照行业分组计算收益。

第二个参数 binning_by_group，布尔型 (bool)。是指分层（quantile) 是在分组 (group) 内部进行，还是跨组进行。

第三个参数 quantiles 用来指示如何进行分层。可以是一个整数值，也可以是一个数组。如果传入整数值，则按该数值将 factor 进行等分，保证各分层的 size（元素个数）近似相同；如果是一个数组，则把数组元素值看成是分位数值，再对 factor 进行划分。

第四个参数 bins 类似 quantiles，也是用来指示如何进行分层。它与 quantiles 同时只能有一个有值。可以是一个整数值，也可以是一个数组。如果传入整数值，则按该数值将 factor 进行等分，保证各分层的宽度（上界值减去下界值）基本相同；如果是一个数组，则把元素值看成是各个 bucket 的边界值。

第五个参数是调仓周期 periods，类型为元组，如 (1,5,10,)。

第六个参数是异常值阈值倍数的设置，filter_zscore 的取值是一个数值，当数值超过均值加上这个倍数的标准差之后，就会被抛弃掉。如果要禁止这种 drop，可以设置为 None。

第七个参数 groupby_labels，它也是一个字典格式，它的意思是把所有的行业贴上标签。

其它参数见前面说明。

经过数据预处理之后，我们就可以调用`alphalens.tears`中的方法来进行因子分析了。

### 1.3. 因子分析

我们可以简单地调用`create_full_tear_sheet`来生成各种可视化分析。它包含了以下类型的图表：

![100%](https://images.jieyu.ai/images/2023/07/return_analysis.png)

![100%](https://images.jieyu.ai/images/2023/07/mean_daily_return_alphalens.png)

![100%](https://images.jieyu.ai/images/2023/07/alphalens_violion.png)

![100%](https://images.jieyu.ai/images/2023/07/alphalens_cum_returns.png)

![100%](https://images.jieyu.ai/images/2023/07/alphalens-1d5d-forward.png)

![100%](https://images.jieyu.ai/images/2023/07/alphalens-10d-return.png)

![100%](https://images.jieyu.ai/images/2023/07/alphalens-5d10-qq.png)

![100%](https://images.jieyu.ai/images/2023/07/alphalens-top-bottom.png)

![100%](https://images.jieyu.ai/images/2023/07/alphalens-monthly-ic.png)

`create_full_tear_sheet`函数具有以下签名：

```python
def create_full_tear_sheet(factor_data,
                           long_short=True,
                           group_neutral=False,
                           by_group=False):
```
这里 factor_data 即为我们前面通过`get_clean_factor_and_forward_returns`清洗后的 dataframe。我们也可以跳过`get_clean_factor_and_forward_returns`，自行生成合格的`factor_data`，这在我们需要进行一些特殊的预处理过程时特别有用。

`long_short`参数含义是是否计算多空组合的收益，对 A 股而言，一般应该设置为 False，因为我们不存在卖空机制，`group_adjust`参数的含义是要不要对收益进行行业中性化调整，`by_group` 参数的含义是指是否按照行业分组展示。

关于如何阅读 alphalens 报表，可以参考这篇 [翻译文章](https://zhuanlan.zhihu.com/p/546143901)。
### 1.4. Alphalens 常见错误和警告
使用 Alphalens 的过程中，最常见的错误主要有两种。首先我们可能遇到时区没有设置，或者时区不一致导致的错误。

#### 1.4.1. 时区问题
在因子提取方法`calc_rb_factor`中的第 19 到 26 行：
```python
import pandas as pd
factor = pd.DataFrame(rb_factor, columns = ["date","asset","score"])
# FACTOR["DATE"] = PD.TO_DATETIME(FACTOR["DATE"], UTC=TRUE)
factor = factor.set_index(['date','asset'])

prices = pd.concat(prices).pivot(index='frame', columns='code', values='close')
# PRICES.INDEX = PD.TO_DATETIME(PRICES.INDEX, UTC=TRUE)
prices = prices.rename_axis('date')
```
如果我们注释掉代码中的第 2 行、第 7 行，这样提取出来的 factor 和 prices 因子，在调用`get_clean_factor_and_forward_returns`时，就会抛出下面的错误：

![100%](https://images.jieyu.ai/images/2023/07/index_has_no_attribute_tz.png)

这是因为，我们获取的行情数据是不存在时区概念的，但 alphalens 拒绝对 naive 时间对象进行比较。所以，尽管我们处理 A 股数据时，由于都在同一时区，时区信息并不重要，但我们在将因子传递给 alphalens 之前，还是得加上时区信息。

!!! tip
    补充一点关于时间的小知识。在 datetime 库中，存在着 date 类型和 datetime 类型，两者是派生与继承的关系。
    
    datetime 对象存在时区概念。当 datetime 对象不包含时区信息时，被称为 naive 时间对象。带时区的 datetime 则被称为 timezone-aware 对象。
    
    date 对象不存在时区概念，也无所谓 naive 与 timezone-aware。

#### 1.4.2. MaxLossExceedError

其次，比较常遇到的错误是 MaxLossExceededError。这个错误产生于 alphalens/utils.py 文件中，`get_clean_factor`在计算前向收益和对因子进行分层后，如果 drop 掉的行过多，超过了`max_loss`（缺省为 35%）时，就会抛出这个错误。

产生这个错误的具体场景至少有两种。其一，我们给出的 prices 记录，在时间上必须与因子时间一一对应。比如，我们要计算$T+10$期的因子收益率，那么 prices 记录覆盖的时长就必须比因子的时长至少多 10 个周期。否则，没有收益对应的因子记录（指 factor dataframe 中的一行）就必须被 drop。当然，我们还要保证 prices 记录与 factor 记录的索引在时间这个 level 上是能够对应的，即使没有时区上的差异，也不能发生一边是 datetime.date，另一边是 datetime.datetime 的情况，这两种数据类型是无法比较的。

另一个原因发生在对因子进行分层时。在上面的例子中，如果我们将
```python
factor_data = get_clean_factor_and_forward_returns(factor, prices, bins=5, quantiles=None)
```
改为
```python
factor_data = get_clean_factor_and_forward_returns(factor, prices)
```
就会抛出 MaxLossExceedError 错误。这个错误如下所示：

![75%](https://images.jieyu.ai/images/2023/07/MaxLossExceedError.png)

我们使用的 round bottom 这个因子，在绝大多数情况下它是稀疏的（指多数时间取值为 0）。错误的根本原因是，`get_clean_factor_and_forward_returns`会最终调用到`pd.qcut`（此方法我们在上一章中介绍过），而此时容易出现传入的数组（round bottom factor）都为零的情况，从而导致`pd.qcut`返回 nan 值。最后，`get_clean_factor`把这些 nan 当成非法值 drop 掉，从而导致数据丢失率过高，抛出异常。

下图显示了全部的调用过程：

![75%](https://images.jieyu.ai/images/2023/07/root_cause_to_max_loss.png)

#### 1.4.3. FutureWarning

此外，在运行过程中，我们会看到如下警告：
```text
utils.py:319: FutureWarning: Unlike other reduction functions (e.g. `skew`, `kurtosis`), the default behavior of `mode` typically preserves the axis it acts along. 

In SciPy 1.11.0, this behavior will change: the default value of `keepdims` will become False, the `axis` over which the statistic is taken will be eliminated, and the value None will no longer be accepted. 

Set `keepdims` to True or False to avoid this warning.
```

这是因为，alphalens 中的 utils.py 的第 319 行调用了 scipy.stats 中的`mode`方法来寻找众数，但 scipy.stats 将从 1.11.0 起，改变行为，因此 scipy 决定从当前的版本（1.10.x）起发出这个警示，以便大家可以提前准备修改。

Alphalens 是 quantpian 社区开源的组件。quantpian 在 2019 年停止运营后，这些组件也就不再有人维护了。在未来将可能遇到更多的兼容性问题。因此，如果要继续使用 alphalens，必须将 scipy（其实也包括 pandas）锁定在当前支持的版本上。

!!! tip
    关于如何锁定版本，可以参考《Python 能做大项目》一书。目前在公众号上有连载。

## 2. JQFactor 和 jqfactor-analyzer
jqfactor 是类似于 alphalens 的一个因子分析框架。但仅限于在聚宽的在线研究环境中使用。

jqfactor_analyzer 是聚宽提供的可以在本地开发环境中使用的一个因子分析框架。目前在 github 上开源，获得了近 400 stars。对缺乏自研能力的读者，alphalens 的老版本又无法使用的情况下（比如用了新的 python, pandas, scipy 等），也可以考虑 jqfactor_analyzer。

它的 API 与 alphalens 比较类似，所以替换和学习的成本比较低。这里不再详细介绍期使用。

安装 jqfactor_analyzer:
```bash
pip install jqfactor_analyzer
```
## 3. sympy

SymPy 是一个功能强大的符号数学库，可用于金融领域的各种任务。 SymPy 在金融领域的一些具体应用方式包括：

1. 金融建模：SymPy 可用于构建和求解金融系统的数学模型，例如资产价格或投资组合绩效模型。
2. 期权定价：SymPy 提供使用 Black-Scholes 模型等数学模型对期权进行定价的工具。
3. 金融工程：SymPy 可用于执行金融工程任务，例如构建自定义金融工具或计算衍生品敏感性。
4. 符号计算：SymPy 可用于执行符号计算，例如金融方程的微分和积分、求解方程组以及简化表达式。

更具体一点说，在我们阅读相关量化的论文时，不可避免地要遇到各种通过数学符号来描述的金融模型。Sympy 可以帮助我们将这些数学公式转化为代码实现。并且，我们可以将 sympy 实现的公式打印出来与原文对照，从而保证我们的实现是正确的，还可以极大地减轻我们在阅读和实现相关论文时的心智负担。

除了以上功能外，我们在量化中运用 sympy 还有一个非常直接的理由，就是尽量减少浮点误差。因为我们在中间过程推演（比如积分和求导）时，可以只对符号进行运算，而不代入具体的数值，因此在中间过程中，不会受到浮点精度的影响。浮点精度对我们计算的影响，可以推迟到最后一步，代入具体数值时才会有所体现，此时对我们结果的影响已大为减少。

在我们的实验环境中，并没有预安装 sympy，请大家自行安装：

```bash
!pip install sympy
```

我们先看一个简单的例子：

```python
import sympy as sy
from sympy.abc import x, y
sy.init_printing()

y = 2 * x ** 2 + 3 * x + 1
y
```
注意我们从`sympy.abc`中导入了 x, y。它们既是传统意义上的变量，更是 sympy 中进行符号运算的载体。在`abc`包中还有大量预定义的符号，比如各种希腊字母。

这里我们定义了一个二次多项式，在 notebook 环境下，这将显示为：
$$
y = 2x^2 + 3x + 1 \tag 1
$$

与代码实现`y = 2 * x ** 2 + 3 * x + `相比，可以说要直观不少。

现在我们可以使用我们熟悉的数学中的代入概念，来这样求解$x=3$时的$y$值：
```python
y.subs(x, 3)
```
输出结果为 28。

我们可以进一步拓展上述表达式，以生成更为复杂的表达式：
```python
expr = sy.sin(x)
y.subs(x, expr)
```
这在 notebook 环境下，将输出如下：

$$
2sin^2(x) + 3sin(x) + 1 \tag 2
$$

显然，通过这样一步步构建数学表达式，要比直接看代码容易得多，出现错误也少，还能与论文直接对照。

我们也可以直接由字符串来生成 sympy 表达式：

```python
str_expr = "x**2 + 3*x - 1/2"
expr = sy.sympify(str_expr)
expr
```
这将在 notebook 环境下，输出如下数学表达式：

$$
x^2 + 3x - 1/2 \tag 3
$$

我们来看一个例子，sympy 如何显示一个矩阵：

```python
m = [
    [1,2,3],
    [4,5,6]
]
sy.Matrix(m)
```
![200](https://images.jieyu.ai/images/2023/07/sympy_show_matrix.png)

在代码中显示矩阵，我们必须处理好对齐，尽管这样，还是与我们平常熟悉的方式风格迥异。而 sympy 显示出来的结果，则与我们熟悉的矩阵一模一样。

此外，sympy 还提供了求解数学模型的能力。这里，我们就以经典的 [Black-Scholes 公式求解](https://gist.github.com/Kevin-Jin/4972722d4a3a842be483) 为例展示其用法：

!!! attention
    Black-Scholes 公式的求解比较复杂，涉及到偏微分方程（Partial Differential Equation) 的求解，已经远远超出了本课程的范围。这里举例仅为说明 sympy 在量化金融中的重要作用。

    此部分参考文献较少，也不排除存在错误。

```python
import sympy as sy
from sympy.stats import Normal as syNormal
from sympy.stats import cdf as syCdf
from sympy.solvers import nsolve
sy.init_printing()

#SPOT, STRIKE, VOL, DAYS TILL EXPIRY, INTEREST RATE, CALL OR PUT (1,-1)
S, K, vol, dte, r,cp = sy.symbols('S,K,vol,dte,r,cp')

T = dte / 260.
N = syNormal('N', 0.0, 1.0)

d1 = (sy.ln(S / K) + (r + 0.5 * vol ** 2) * T) / (vol * sy.sqrt(T))
d2 = d1 - vol * sy.sqrt(T)
d2
```

这将打印出`d2`的公式 [^d2] 如下：

![100%](https://images.jieyu.ai/images/2023/07/bs-d2.png)

```python
tv = sy.exp(-r * T) * (cp *S * syCdf(N)(cp*d1) - cp * K  * syCdf(N)(cp*d2))
tv
```

这将显示以下公式：

![100%](https://images.jieyu.ai/images/2023/07/bs_tv.png)

```python
#BLACK TV
bs_tv = sy.lambdify((S, K, vol, dte, r,cp),tv)

#1ST ORDER GREEKS
delta = sy.lambdify((S, K, vol, dte, r,cp),tv.diff(S))
vega = sy.lambdify((S, K, vol, dte, r,cp),tv.diff(vol)/100.) #WATCH UNITS
theta = sy.lambdify((S, K, vol, dte, r,cp),tv.diff(dte))
rho = sy.lambdify((S, K, vol, dte, r,cp),tv.diff(r))

#2ND ORDER GREEKS
gamma = sy.lambdify((S, K, vol, dte, r,cp),tv.diff(S,S))
vanna = sy.lambdify((S, K, vol, dte, r,cp),tv.diff(S,vol)/100.)
vomma = sy.lambdify((S, K, vol, dte, r,cp),tv.diff(vol,vol)/1e4) #IN TICKS
charm = sy.lambdify((S, K, vol, dte, r,cp),tv.diff(S,dte)) #DELTA DECAY

#3RD ORDER -- WHO CARES ABOUT ANYTHING ABOUT DGAMMA?
speed = sy.lambdify((S, K, vol, dte, r,cp),tv.diff(S,3))

#INVERSE BLACK SCHOLES TO BACK OUT IMPLIED VOLATILITY WHEN GIVEN 
# NSOLVE SOLVES FOR BS(VOL) = 0, SO TO FIND BS(VOL) = X, MUST PASS IN BS(VOL) - X = 0
implied_vol = lambda s,k,DTE,R,CP,x: nsolve(tv.subs([(S, s), (K, k), (dte, DTE), (r, R), (cp, CP)]) - x, vol, 1)

#EXAMPLE: PRICE OF CALL OPTION, SPOT 460, STRIKE 470, 58.2% STANDARD DEVIATION, 2 CALENDAR MONTHS TO EXPIRY, 2% DISCOUNT RATE
bs_tv(460, 470, 58.2 / 100, 62 / 365 * 260, 2 / 100, 1)

#EXAMPLE: CALCULATE IMPLIED VOLATILITY OF ABOVE CALL OPTION SELLING FOR $39.43
implied_vol(460, 470, 62 / 365 * 260, 2 / 100, 1, 39.43)
```
最后求解的结果为 0.582001645721717

最后，我们回顾一下，我们在讲归一化时，提到过 sigmoid 与 tanh 相比，有一个优势就是在求导时更快，因为对其求导的结果为：
$$
d(f) = f(x) * (1 - f(x)) \tag 4
$$

这样在计算中可以利用原函数，所以速度更快。现在，我们利用 sympy 来验证一下，对 sigmoid 函数求导，其结果等于上式。

首先，我们通过 sympy 来实现对 sigmoid 的求导：
```python
from sympy.abc import x
sigmoid = 1/(1 + sy.exp(-x))

ds = sy.diff(sigmoid, x)
ds
```
我们将得到如下的式子：
$$
\frac{e^{-x}}{(1+e^{-x})^2} \tag 5
$$

式子 4 用 sympy 表示为：
```python
df = sigmoid * (1 - sigmoid)
```
这两个式子直接比较的结果似乎是不相等，不过如果我们通过`simplify`方法先对其进行化简：
```python
display(sy.simplify(ds))
display(sy.simplify(df))
```
两者显示的结果都是：
$$
\frac{1}{4cosh^2(\frac{x}{2})} \tag 6
$$
因此，两个式子是相等的。所以，如果当下原函数值为 0.5，则求导后的值为$0.5 * (1 - 0.5) == 0.25$，这个计算是相当快的。这就是为什么说从性能上看，sigmoid 计算会比 tanh 更快的原因。

## 4. statistics
这里讲的 statistics 是指 python 的内建库。实际上，这个库所实现的功能，在我们介绍的 numpy, scipy.stats 等库中都已经实现了。这里介绍它，是出于完备性的考虑。如果读者在其它别的地方看到对这个库的使用，也能理解它是什么，以及是否存在平替。

由于 statistics 是 python 的内建库，所以我们可以随时随地使用它，这是它相对于 numpy 和 scipy 方案的唯一优点。

![100%](https://images.jieyu.ai/images/2023/07/statistics_mindmap.png?1)

## 5. statsmodels

statsmodels 是建立在 numpy、scipy.stats 和 matplotlib 之上的一个 python 库，用于探索数据、估计统计模型并执行统计测试。它在语法和使用上接近于 R，所以给那些从 R 过渡过来的研究者提供了一种选择。

与 scipy.stats 相比，scipy.stats 相当于核心库，它提供了大量的分布、大多数常见的参数和非参数统计检验及描述性统计。而 statsmodels 更专注于估计统计模型，提供了线性回归、时间序列分析、生存分析等多个模型。

statsmodels 的主要功能可见下图：

![100%](https://images.jieyu.ai/images/2023/07/sm_mindmap.png)

在 statsmodels 的文档中，常常会见到 endog 和 exog 这样两个词。这两个词的用法也广受争议，这里给出它们在不同场合下的对应物，帮助大家理解：

| endog               | exog                 |
| ------------------- | -------------------- |
| y                   | x                    |
| left hand side(LHS) | right hand side(RHS) |
| regressand          | regressors           |
| outcome             | design               |
| response variable   | explanatory variable |

在这一节里，我们主要以 OLS 模型为例说明 statsmodels 的用法，然后比较 OLS 模型与 RLM 模型，最后，简单介绍下 ARIMA 模型。

### 5.1. OLS（普通最小二乘法）估计

首先我们导入必要的库：

```python
import numpy as np
import pandas as pd
import statsmodels.api as sm

np.random.seed(78)
```

接着我们生成一些数据，用以演示：

```python
nsample = 100
# 自变量
x = np.linspace(0, 10, 100)
X = np.column_stack((x, x ** 2))
# 回归系数
beta = np.array([1, 0.1, 10])
# 残差
e = np.random.normal(size=nsample)
```

模型需要截距，所以我们给数据加上一列`1`：

```python
# ADD_CONSTANT 将给矩阵加上一列，该列的值均为 1
X = sm.add_constant(X)
# 因变量
y = np.dot(X, beta) + e
```

这里我们实际上构建了一个表达式：
$$
y = 10x^2 + 0.1x + 1 \tag 7
$$
而生成的数据则在此基础上，增加了一个满足标准正态分布的随机值。

接下来，我们尝试寻找这个公式 (7):

```python
model = sm.OLS(y, X)
results = model.fit()
print(results.summary())
```

在实例化 OLS 模型对象时，我们需要传入因变量`y`和自变量`X`。然后我们调用`model.fit`方法来求解，得到`results`对象，最后调用`summary`方法，我们得到以下输出：

![100%](https://images.jieyu.ai/images/2023/07/ols_fit_summary.png)

一些我们感兴趣的量可以直接从模型中提取出来：

```python
print("Parameters: ", results.params)
print("R2: ", results.rsquared)
```
参数则是对公式（7）的一个估计值。R2 是可决系数，取值范围为 [0,1]，越大越好。

我们也可以通过上述模型来进行预测：
```python
results.predict(X[99,:])
```

### 5.2. 比较 OLS 与 RLM
在因子分析中，我们使用过 RLM 模型，下面我们通过示例来演示下两者的不同：
```python
import statsmodels.api as sm
import matplotlib.pyplot as plt
from statsmodels.sandbox.regression.predstd import wls_prediction_std

nsample = 50
x1 = np.linspace(0, 20, nsample)
X = np.column_stack((x1, (x1-5)**2))
X = sm.add_constant(X)
sig = 0.3   # 较小的误差方差可使 OLS<->RLM 之间的对比度更大
beta = [5, 0.5, -0.0]
y_true2 = np.dot(X, beta)
y2 = y_true2 + sig*1. * np.random.normal(size=nsample)
y2[[39,41,43,45,48]] -= 5   # 添加一些异常值 （以 10% 的 nsample 抽样比例）

res = sm.OLS(y2, X).fit()
resrlm = sm.RLM(y2, X).fit()

fig = plt.figure(figsize=(12,8))
ax = fig.add_subplot(111)
ax.plot(x1, y2, 'o',label="data")
ax.plot(x1, y_true2, 'b-', label="True")
prstd, iv_l, iv_u = wls_prediction_std(res)
ax.plot(x1, res.fittedvalues, 'r-', label="OLS")
ax.plot(x1, iv_u, 'r--')
ax.plot(x1, iv_l, 'r--')
ax.plot(x1, resrlm.fittedvalues, 'g.-', label="RLM")
ax.legend(loc="best")
```

输出结果如下：

![50%](https://images.jieyu.ai/images/2023/06/ols_vs_rlm.png)

从图中可以看出，个别异常值对 OLS 的影响比较大，而 RLM 则对异常值不敏感。在量化研究中，究竟选哪一个，还不能一概而论，如果异常值发生成时间序列的末端，而被 RLM 忽略的话，并不一定是好事。

### 5.3. ARIMA 模型与时间序列预测
ARIMA 模型全称差分整合移动平均自回归模型（Autoregressive Integrated Moving Average model）。

标准 ARIMA 模型期望输入参数为 3 个参数，即 p, d, q。这里 p 是滞后观测值的数量， d 是差异程度，q 是移动平均窗口的大小/宽度。

我们以上证指数预测为例，来演示该模型的用法：

```python
import numpy as np 
import pandas as pd 
import matplotlib.pyplot as plt
from pandas.plotting import lag_plot
from pandas import datetime
from statsmodels.tsa.arima.model import ARIMA
from sklearn.metrics import mean_squared_error
from coursea import *
await init()

bars = await Stock.get_bars("000001.XSHG", 250, FrameType.DAY)
```
首先我们检验一下上证指数是否具有自相关特性，这是我们进行 ARIMA 分析的必要条件。我们可以通过 pandas 的 lag_plot 来查看。

在 lag_plot 图中，如果数据显示线性模式，则表明存在自相关。正线性趋势（即从左到右向上）表明正自相关；负线性趋势（从左到右向下）表明负自相关。数据围绕对角线聚集得越紧密，自相关性就越强；完全自相关的数据将聚集在一条对角线上。

```python
df = pd.DataFrame(bars)
lag_plot(df['close'], lag = 3)
```
![100%](https://images.jieyu.ai/images/2023/07/lag_plot.png)

我们选择的参数是：

p = 4
d = 1 
q = 0
```python
close = bars["close"]
frames = bars["frame"]

train_data, test_data = close[0:int(len(close)*0.7)], close[int(len(close)*0.7):]

history = list(train_data)
model_predictions = []
N_test_observations = len(test_data)

for time_point in range(N_test_observations):
    model = ARIMA(history, order=(4,1,0))
    model_fit = model.fit()
    output = model_fit.forecast()
    yhat = output[0]
    model_predictions.append(yhat)
    true_test_value = test_data[time_point]
    history.append(true_test_value)
    
MSE_error = mean_squared_error(test_data, model_predictions)
print('Testing Mean Squared Error is {}'.format(MSE_error))
```
我们得到的 MSE 大约在 600 左右。

我们将训练数据集分为训练集和测试集，然后使用训练集来拟合模型，并为测试集上的每个元素生成预测。

考虑到差分和 AR 模型对先前时间中的观测结果的依赖，需要滚动预测程序。为此，我们在收到每个新观测值后重新创建 ARIMA 模型。
最后，我们手动跟踪称为历史记录的列表中的所有观察结果，该列表以训练数据为种子，并在每次迭代时附加新的观察结果。

现在，让我们把实际走势与预测值对照绘图：

```python
test_set_range = bars["frame"][len(train_data):]
plt.plot(test_set_range, model_predictions, color='#F55892', marker='o', ms = 3, linestyle='dashed',label='Predicted Price')
plt.plot(test_set_range, test_data, color='#F2DCFA', label='Actual Price', alpha=0.8)
plt.title('XSHG Prices Prediction')
plt.xlabel('Date')
plt.ylabel('Prices')
plt.xticks(rotation = 45)
plt.legend()
plt.show()
```
![100%](https://images.jieyu.ai/images/2023/07/arima_predict.png)

单从图形上看，可能会感觉预测相当有效。从 MSE 上，如果不与标准差进行对照，我们也很难分辨出预测的好与坏。但如果我们使用`mean_absolute_percentage_error`，则得到的预测残差大约是 0.5%。考虑到上证多数时间的波动在 1%以内，因此我们也很难说这个预测有多准。

## 6. zipline

zipline 是 quantpian 出品的回测框架。但它的框架不够灵活，对国内市场支持不好。主要限制有，自带的数据 bundle 无法获取国内市场数据，交易日历使用的是纽交所交易日历（NYSE），无法匹配国内股市，交易制度限制等等。

因此，对 zipline 我们不打算详细介绍。我们将在回测那一章，再来详细介绍它的平替 - backtrader。

## 7. pyfolio
pyfolio 也是 Quantpian 出品的，它用于对金融投资组合进行绩效和风险分析。它的核心也是各种"tear sheet"，我们在介绍 alphalens 时，已经接触过了。

我们对 pyfolio 也不打算详细介绍。我们将在回测那一章，介绍它的平替 quantstats 和 empyrical-reloaded。

## 8. ta

ta 是一个基于 numpy 和 pandas 构建的技术分析库，可用以一键提取各种技术指标。

在本课程环境中并没有安装 ta，我们需要单独安装：
```bash
! pip install ta
```

安装完成后，我们可以如下一键提取多达 80 多个指标：

```python
import pandas as pd
from ta import add_all_ta_features
from ta.utils import dropna

# 加载数据，这里的 BARS 来自于 STOCK.GET_BARS
df = pd.DataFrame(bars)

# CLEAN NAN VALUES
df = dropna(df)

# ADD ALL TA FEATURES
df = add_all_ta_features(
    df, open="open", high="high", low="low", close="close", volume="volume")
df
```
这样我们将得到约 86 个技术指标，或者称为因子。这为我们进行因子分析或者机器学习提供了数据。

***本节课有[辅助阅读材料](../supplements/lesson15.ipynb)***

## Footnotes

[^d2]: https://aaronschlegel.me/black-scholes-formula-python.html
