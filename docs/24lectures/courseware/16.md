---
title: Matplotlib 绘图
mainfont: WenQuanYi Micro Hei
---

![75%](https://images.jieyu.ai/images/2023/07/lesson16-outline.png?1)

## 1. matplot 简介
matplotlib 的创始人是 John D. Hunter[^hunter]。他是一名神经生物学家。他最初于 2003 年左右开始开发 matplotlib。2012 年不幸去世后，现在 matplotlib 由一个成熟的社区来进行持续开发和维护。

![25%](https://images.jieyu.ai/images/2023/07/matplotlib_jd_hunter.jpg)

在说到 matplotlib 时，我们常常有几个相关的概念需要立即澄清：那就是 matplot.pylab, matplotlib.pyplot 和 matplotlib 本身。

长话短说，pylab 被设计成一个便利贴，它导入了 matplotlib.pyplot 中的大多数对象，以及 numpy 中的许多对象，从而使得你可以通过一个`from pylab import *`就可以使用许多功能。但这种方法容易引起名字冲突，也不便于版本管理，现在 matplotlib 已经不再推荐使用了。所以，从现在起，当你遇到 pylab 相关的介绍，你可以完全不看。

pyplot 是 matplotlib 中的一个模块，它是一个“有状态的接口”，除此之外，matplotlib 的其它部分提供了“无状态”，或者说面向对象接口。换句话说，我们可以认为，pyplot 是更高级的接口，它自动管理了绘图所需要的状态和对象，这在多数情况下，会增强易用性。
## 2. 图是如何构成的
绘图涉及到一系列的领域知识。在不同的绘图框架中，都存在着相同的概念，这些概念很可能都有相同的名字。掌握这些知识，对我们迅速掌握一种新的绘图工具非常有帮助。因此，在这一章里，我们就以matplotlib为例，深入讨论这些概念以及它们之间的关系，为我们学习绘图打下良好的基础。

我们主要讨论绘图对象及其相互关系，也会涉及到字体、色彩相关的知识。

### 2.1. 最顶层的概念

在matplotlib中，存在这样一些概念，pyplot，Figure, Axes, Artist，它们被认为是顶层的概念。

Figure是最顶层的对象。它是 matplotlib 图形的最外层容器，它可以包含多个 Axes 对象，艺术家（artist）对象和画布对象。

Axes 的名字相当引入困惑，它实际上指的是一个绘图（plot），而不是我们常常认为的 Axis（轴）的复数。不过，Axes 中确实可以包含了若干个 Axis。在matplotlib中，真正的”绘图”（准确地说，是关于图形的定义）都发生在Axes这个层面。一个Figure对象可以有零到多个Axes对象，但要产生实际作用，则必须至少拥有一个Axes对象。

!!! Note
    Axis就是我们通常认为的x轴、y轴这些生成象限的东西。有X axis, Y axis以及Z axis，如果是三维图形的话。

在Figure当中，任何可见的部分都可以认为是一个Artist对象，比如文本，线条以及它们的集合。Artist是一个庞大的家族，派生出许多子类。多数Artist对象是绑定在Axes对象上的，我们不能在多个Axes中共享它，或者将其从一个Axes移动到另一个Axes上。当Figure被渲染（即真正被画到canvas上时），实际上是这些Artist被画到canvas上。

有时候我们提到Figure时，将它等同于canvas，这并没有错，但实际上， Figure维护的是一个虚拟的画布，此外，还存在一个真正的画布，即最终图形被绘制的物理对象，在matplotlib中，有时也被称为backend。

我们通过下面的命令创建一个 Figure:
```python
import matplotlib.pyplot as plt

fig = plt.figure()
fig.show()
```
这样生成的 figure 对象是一个“空“对象，它不包含任何 Axes （注意 Axes 是绘图对象，而不是 Axis （轴）。因此当我们运行上面的代码时，它不会有任何结果显示，除了下面这一句：

```
<Figure size 640 * 480 with 0 Axes>
```

要进行绘图，我们必须首先给它添加一个Axes对象，在这个axes基础上，我们可以开始构建图的一切，比如增加一条直线（Line2D，这是一个Artists类）：

```python
import numpy as np
from matplotlib.lines import Line2D

fig = plt.figure()

# 第一个子图，从1/4处开始，包含了一条直线
axes = fig.add_axes([0.25, 0.25, 0.75, 0.75])
axes.add_line(Line2D(np.arange(10),np.arange(10)))

# 第二个子图， 从原点开始
fig.add_axes([0, 0, 0.5, 0.5])

fig.show()
```

我们给fig增加了两个Axes对象，并在其中的一个Axes上，增加了一条直线。这次将显示以下图形：

![50%](https://images.jieyu.ai/images/2023/07/lesson16-screenshot-1.png?1)

这里的语法比较灵活，但步骤比较繁复。我们可以通过以下命令，同时生成 Figure 和 Axes 对象。

```python
fig, (ax1, ax2) = plt.subplots(2, 1)
```

这样就生成了一个Figure对象，和两个Axes对象，这两个Axes对象成两行一列的方式进行排列。与上面的例子相比，它只能按网格，在固定位置上生成固定大小的Axes对象，这些对象在绘图区域上也不会重叠，少了一些灵活性。但对绝大多数作图，这正是我们想要的。

subplots似乎引出了子图的概念，但实际上，Axes对象就是这里的子图，除此之外，没有别的子图。

!!! Note
    从matplotlib 3.4起，确实引入了子图[(subfigure)](https://matplotlib.org/devdocs/gallery/subplots_axes_and_figures/subfigures.html)的概念，它用以创建具有不同layout的多个子图。

    现在，一个Figure可以有多个SubFigure对象和直接Axes对象。

### 2.2. pyplot, Figure与Axes之间的关系

现在我们来总结一下。我们已经接触到了pyplot, Figure和Axes三个对象。

Figure是最顶层的绘图对象，pyplot只是提供了一些便利性的封装，它本身不提供任何实际的功能，但保持了对全局当前活跃的Figure和Axes对象的跟踪，并且会将一些绘图指令分发到这两个对象上。

我们可以通过plt.gcf和plt.gca分别获取这两个对象。

```python
fig1, (ax1, ax2) = plt.subplots(2, 1)
print("fig1 is plt.gcf(), ax2 is plt.gca()", fig1 is plt.gcf(), ax2 is plt.gca())

fig2, ax3 = plt.subplots()
print("fig1 is plt.gcf(), ax2 is plt.gca()", fig1 is plt.gcf(), ax2 is plt.gca())
print("fig2 is plt.gcf(), ax3 is plt.gca()", fig2 is plt.gcf(), ax3 is plt.gca())
```

实际上，当我们调用plt中的一些方法时，比如plot，它实际上是先检索到当前活动Figure对象的活动Axes对象，再在这个Axes对象上进行的操作。这也正是为什么说pyplot是一个有状态的接口的原因，因为它保持了对活动的Figure和Axes的跟踪。

```python
# matplotlib/pyplot.py
def plot(*args, **kwargs):
    ax = plt.gca()
    return ax.plot(*args, **kwargs)

def gca(**kwargs):
    return plt.gcf().gca(**kwargs)
```

除了plot方法之外，pyplot与Axes共有78个左右的同名方法，Figure与Axes之前也有76个左右的同名方法，但与pyplot之间只有20个左右的共同方法。这些同名方法之间的关系，就正如plot方法一样（Figure对象没有plot方法）。因此，并不是所有的Figure和Axes方法都被封装进了pyplot。很多时候，我们需要直接操作Figure对象和Axes对象，以获得更强大的定制能力。

plt.gca和plt.gcf这两个方法的重要性在于，如果我们直接使用了plt.plot方法开始了一个绘图，如果我们在中间需要调整绘图的一些属性，而plt又没有提供这个封装，那么有时候会需要通过plt.gcf或者plt.gca得到相应的对象再进行操作。

如果当前进程中存在多个Figure对象，我们可以通过plt.get_fignums()和plt.figure()来检索所有的Figure对象：

```python
fig1 = plt.figure()
fig2 = plt.figure()

# now fig2 is gcf, but we can retrieve fig 1 by:
for i in plt.get_fignums():
    if plt.figure(i) is fig1:
        print("found fig1")
```

注意这次我们给plt.figure传入了一个参数，以检索某个Figure对象，而不是创建新的Figure。这个行为在其文档中有说明：

```python
plt.figure(
    num=None,
    figsize=None,
    dpi=None,
    *,
    facecolor=None,
    edgecolor=None,
    frameon=True,
    FigureClass=<class 'matplotlib.figure.Figure'>,
    clear=False,
    **kwargs,
)
Create a new figure, or activate an existing figure.
```

我们可以通过Figure.axes或者Figure.get_axes()来检索该Figure中包含的axes:

```python
fig, ((ax1, ax2, ax3), (ax4, ax5, ax6)) = plt.subplots(nrows = 2, ncols=3)
# 下面的代码也可以
# fig, axes = plt.subplots(nrows = 2, ncols=3)
# ax1, ax2, ax3, ax4, ax5, ax6 = axes.flatten()

print(type(axes), axes.shape)
print(fig.axes)
print(fig.get_axes())
```
上面的代码中，我们通过plt.subplots(2, 3)创建了一个Figure和6个Axes对象。但这一次，我们展开plt.subplots创建的Axes数组的方法略为复杂一些，因为plt.subplots将它组成了一个二维的numpy数组。plt.suplots这样组织，是为了方便调用者将各个子图与网格顺序相对应起来。我们可以直接使用axes[i][j]这样的方式定位到某个Axes对象并进行绘制。

有时候我们会看到fig.add_subplot的调用。因为并不存在所谓的subplot对象，所以这个操作实际上也是调用了fig.add_axes，不同之处在于，它们的参数不一样。fig.add_subplot要求传入子图的网格坐标（即类似111, 241这类的数字），因此它有同时进行布局(layout)安排的含义在里面。
### 2.3. layout

matplotlib中的layout，与大多数图形界面中的布局概念类似，比如盒模型（left, top, right, bottom)，外边距(margin)，内边距（padding）等。

layout在matplotlib中，主要GridSpec类和LayoutEngine一起来实现。 

gridspec 是将图形逻辑划分为行和列，这些行和列中轴的相对宽度由 width_ratios 和 height_ratios 设置。LayoutEngine 主要用来安排Figure中子图的定位，以避免Axes的一些装饰（比如labels, ticks等）相互重叠。主要取值有constrained, compressed, tight等。其中constrained是推荐类型。

!!! attention
    只有Figure对象才有layout_engine对象，SubFigure没有。

我们通过例子来看具体如何使用GridSpec和 layout:

```python
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec

def annotate_axes(fig):
    for i, ax in enumerate(fig.axes):
        ax.text(0.5, 0.5, "ax%d" % (i+1), va="center", ha="center")
        ax.tick_params(labelbottom=False, labelleft=False)


fig = plt.figure(facecolor='0.8')
fig.suptitle("Controlling spacing around and between subplots")

gs1 = GridSpec(3, 3, left=0.3, right=0.48, wspace=0.05)
ax1 = fig.add_subplot(gs1[:-1, :])
ax2 = fig.add_subplot(gs1[-1, :-1])
ax3 = fig.add_subplot(gs1[-1, -1])

gs2 = GridSpec(3, 3, left=0.55, right=0.98, hspace=0.05)
print("gs2 is:", gs2[:, :-1])

ax4 = fig.add_subplot(gs2[:, :-1])
ax5 = fig.add_subplot(gs2[:-1, -1])
ax6 = fig.add_subplot(gs2[-1, -1])

annotate_axes(fig)

def show_grid(gs, pos):
    # Get the grid positions
    bottoms, tops, lefts, rights = gs.get_grid_positions(plt.gcf())

    ax = plt.axes([0,0,1,1], facecolor=(1,1,1,0))

    vlines = sorted([*lefts, *rights])
    for x in vlines:
        ax.axvline(x, ls='-', lw=1, ymin=min(bottoms), ymax=max(tops))

    hlines = sorted([*bottoms, *tops])
    for y in hlines:
        ax.axhline(y, ls='-', lw=1, xmin=min(lefts), xmax=max(rights))
        
show_grid(gs1, [0.3, 0, 0.48, 1])
show_grid(gs2, [0.55,0, 0.98, 1])

plt.show()
```
这段代码中，我们先通过GridSpec创建了两个3*3的网格，一左一右，然后通过add_subplot创建了6个子图，在创建时，将gridspec传入。

在创建子图，我们通过给子图绑定到不同的网格，实现了类似excel中的单元格合并的效果，从而实现了异形网格。

这段代码展示了非常多的绘图技巧，值得好好研究。

首先，每个Gridspec可以指定它们在Figure中的位置和大小。比如，这里的gs1就是从x轴的0.3到0.48处（占据了全部高度，因为没有指定）。

其次，我们为了显示每个网格（即每个3*3的小格子）是如何分配到每个ax的，我们将这些格子进行了描边。

因此，从上图可以看出，1~6的小网格都分配给了ax1，这是由代码gs[:-1,:]来指定的。gs[:-1,:]意味着把直到最后一行（不包括）的所有列对应的网格都分配给ax1,也就是前6个网格。关于如何阅读切片，在我们课程第9讲中有详细说明，并且给出了绘图。

对ax2，分配的是gs1[-1,:-1]，这意味着是把最后一行，直到最后一列的所有grid都分配给它，这对应着两个小的单元格，即7和8号单元格。

最后，ax3分到最后的单元格，gs1[-1,-1]。

对gs2，逻辑类似。ax4得到了gs2[:,:-1]，这意味着所有行的前两列都分配给了ax4；ax5则是得到了最后一列的前两行；ax6得到了gs2[-1,-1]。


我们还指定了网格的横向间距(wspace=0.05)和纵向间距（hspace=0.05)等。

我们还通过facecolor为绘图设置了背景色。

上述代码将输出：

gs2 is: GridSpec(3, 3)[0:3, 0:2]

最终输出的图形如下图所示：

![50%](https://images.jieyu.ai/images/2023/07/using_grid_spec.png?2)

上述方法创建布局简单易懂，但语句较多。我们也可以用 

```python
widths = [2, 3, 1.5]
heights = [1, 3, 2]
gs_kw = dict(width_ratios=widths, height_ratios=heights)
plt.subplots(ncols=3, nrows=3, constrained_layout=True,
        gridspec_kw=gs_kw)
```

不过这样的方法似乎不如上面直观。

我们再看看下面的例子，以演示创建Figure时，layout参数的作用：

```python
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import matplotlib.gridspec as gridspec
import numpy as np

plt.rcParams['figure.facecolor'] = "0.8"
plt.rcParams['figure.figsize'] = 4.5, 4.

def example_plot(ax, fontsize=12, hide_labels=False):
    ax.plot([1, 2])

    ax.locator_params(nbins=3)
    if hide_labels:
        ax.set_xticklabels([])
        ax.set_yticklabels([])
    else:
        ax.set_xlabel('x-label', fontsize=fontsize)
        ax.set_ylabel('y-label', fontsize=fontsize)
        ax.set_title('Title', fontsize=fontsize)

fig, axs = plt.subplots(2, 2, layout=None)
for ax in axs.flat:
    example_plot(ax)
    
fig, axs = plt.subplots(2, 2, layout="tight")
for ax in axs.flat:
    example_plot(ax)
```

LayoutEngine的作用之一，就是为label, title, colorbar等装饰性元素安排绘图空间，在有多个子图时，避免这些对象重叠在一起。从上图可以看出，有没有设置layout，结果大不一样。

### 2.4. Figure Anatomy
Figure的主要功能是：

1. 定义canvas（长宽、像素比，blending模式等）
2. 归集Axes和Artist对象，提供检索能力
3. 管理子图布局（通过GridSpec和LayoutEngine)
4. 设置共同的属性，比如suptitle, supxlabel, subylabel
5. 显示图形窗口，保存图形到io设备上。

Figure的构造函数如下：
```python
class Figure(
    figsize=None,
    dpi=None,
    *,
    facecolor=None,
    edgecolor=None,
    linewidth=0.0,
    frameon=None,
    subplotpars=None,
    tight_layout=None,
    constrained_layout=None,
    layout=None,
    **kwargs,
):  
# The top level container for all the plot elements.
```
当我们无论通过哪个方法（plt.figure, plt.subpots）来创建一个Figure对象时，一些参数最终都被传递给这个函数以创建Figure对象。

我们通过figsize和dpi来指定canvas size。figsize是一个表示长和宽的二元组，单位是inch。

下面，我们通过一张解剖图，来看看Figure对象与它的子成员之间的关系：

```python
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
from matplotlib.patheffects import withStroke
from matplotlib.ticker import AutoMinorLocator, MultipleLocator


royal_blue = [0, 20/256, 82/256]


# make the figure

np.random.seed(19680801)

X = np.linspace(0.5, 3.5, 100)
Y1 = 3+np.cos(X)
Y2 = 1+np.cos(1+X/0.75)/2
Y3 = np.random.uniform(Y1, Y2, len(X))

fig = plt.figure(figsize=(7.5, 7.5))
ax = fig.add_axes([0.2, 0.17, 0.68, 0.7], aspect=1)

ax.xaxis.set_major_locator(MultipleLocator(1.000))
ax.xaxis.set_minor_locator(AutoMinorLocator(4))
ax.yaxis.set_major_locator(MultipleLocator(1.000))
ax.yaxis.set_minor_locator(AutoMinorLocator(4))
ax.xaxis.set_minor_formatter("{x:.2f}")

ax.set_xlim(0, 4)
ax.set_ylim(0, 4)

ax.tick_params(which='major', width=1.0, length=10, labelsize=14)
ax.tick_params(which='minor', width=1.0, length=5, labelsize=10,
               labelcolor='0.25')

ax.grid(linestyle="--", linewidth=0.5, color='.25', zorder=-10)

ax.plot(X, Y1, c='C0', lw=2.5, label="Blue signal", zorder=10)
ax.plot(X, Y2, c='C1', lw=2.5, label="Orange signal")
ax.plot(X[::3], Y3[::3], linewidth=0, markersize=9,
        marker='s', markerfacecolor='none', markeredgecolor='C4',
        markeredgewidth=2.5)

ax.set_title("Anatomy of a figure", fontsize=20, verticalalignment='bottom')
ax.set_xlabel("x Axis label", fontsize=14)
ax.set_ylabel("y Axis label", fontsize=14)
ax.legend(loc="upper right", fontsize=14)

fig.suptitle("this is figure title")

# Annotate the figure

def annotate(x, y, text, code):
    # Circle marker
    c = Circle((x, y), radius=0.15, clip_on=False, zorder=10, linewidth=2.5,
               edgecolor=royal_blue + [0.6], facecolor='none',
               path_effects=[withStroke(linewidth=7, foreground='white')])
    ax.add_artist(c)

    # use path_effects as a background for the texts
    # draw the path_effects and the colored text separately so that the
    # path_effects cannot clip other texts
    for path_effects in [[withStroke(linewidth=7, foreground='white')], []]:
        color = 'white' if path_effects else royal_blue
        ax.text(x, y-0.2, text, zorder=100,
                ha='center', va='top', weight='bold', color=color,
                style='italic', fontfamily='Courier New',
                path_effects=path_effects)

        color = 'white' if path_effects else 'black'
        ax.text(x, y-0.33, code, zorder=100,
                ha='center', va='top', weight='normal', color=color,
                fontfamily='monospace', fontsize='medium',
                path_effects=path_effects)


annotate(3.5, -0.13, "Minor tick label", "ax.xaxis.set_minor_formatter")
annotate(-0.03, 1.0, "Major tick", "ax.yaxis.set_major_locator")
annotate(0.00, 3.75, "Minor tick", "ax.yaxis.set_minor_locator")
annotate(-0.15, 3.00, "Major tick label", "ax.yaxis.set_major_formatter")
annotate(1.68, -0.39, "xlabel", "ax.set_xlabel")
annotate(-0.38, 1.67, "ylabel", "ax.set_ylabel")
annotate(1.52, 4.15, "Title", "ax.set_title")
annotate(1.75, 2.80, "Line", "ax.plot")
annotate(2.25, 1.54, "Markers", "ax.scatter")
annotate(3.00, 3.00, "Grid", "ax.grid")
annotate(3.60, 3.58, "Legend", "ax.legend")
annotate(2.5, 0.55, "Axes", "fig.subplots")
annotate(4, 4.5, "Figure", "plt.figure")
annotate(0.65, 0.01, "x Axis", "ax.xaxis")
annotate(0, 0.36, "y Axis", "ax.yaxis")
annotate(4.0, 0.7, "Spine", "ax.spines")

# frame around figure
fig.patch.set(linewidth=4, edgecolor='0.5')
plt.show()
```

## 3. 高频使用对象
### 3.1. Axis
在一个二维图中，存在着x-axis和y-axis，它们都是Axes的一部分，我们可以通过axes.get_xaxis()来获取x轴，同样地，通过axes.get_yaxis()来获取y轴。

一条轴由spine(轴脊线)，tick（刻度），axis_label(标签)组成。轴有自己的上下界（xmin, xmax, ymin, ymax)。注意spine并不是轴。一张图可以有4个spine，但只有2个轴。

刻度是表示轴上数据点的标记，它有刻度值和刻度标签（tick labels)两个重要属性。Matplotlib 会根据输入数据，自行选择刻度数和刻度位置，以使得轴上有合理的刻度数，并且它们位于四舍五入数字处。因此，图的边缘可能没有刻度。但它也为我们提供了定制化选项，包含自定义刻度和刻度标签。

关于axis，我们这一节主要解决以下几个问题。
#### 3.1.1. spine定位与隐藏

我们常常会遇到需要隐藏某个spine的问题，特别是上边和右边的spine。另外，默认显示的是第一象限图，有时候我们需要显示其它象限。

下面的例子显示了如何调整spine定位以显示其它象限，以及如何隐藏某个spine：

```python {all|26}
x = np.linspace(0, 2*np.pi, 100)
y = 2 * np.sin(x)

fig, ax_dict = plt.subplot_mosaic(
    [['center', 'zero'],
     ['axes', 'data']]
)
fig.suptitle('Spine positions')


ax = ax_dict['center']
ax.set_title("'center'")
ax.plot(x, y)
ax.spines[['left', 'bottom']].set_position('center')
ax.spines[['top', 'right']].set_visible(False)

ax = ax_dict['zero']
ax.set_title("'zero'")
ax.plot(x, y)
ax.spines[['left', 'bottom']].set_position('zero')
ax.spines[['top', 'right']].set_visible(False)

ax = ax_dict['axes']
ax.set_title("'axes' (0.2, 0.2)")
ax.plot(x, y)
ax.spines.left.set_position(('axes', 0.2))
ax.spines.bottom.set_position(('axes', 0.2))
ax.spines[['top', 'right']].set_visible(False)

ax = ax_dict['data']
ax.set_title("'data' (1, 2)")
ax.plot(x, y)
ax.spines.left.set_position(('data', 1))
ax.spines.bottom.set_position(('data', 2))
ax.spines[['top', 'right']].set_visible(False)
```
生成的图如下：

![50%](https://images.jieyu.ai/images/2023/07/lesson16_spine_position.png)


#### 3.1.2. 共享x轴
有时候我们需要多个y轴，特别是一左一右双y轴比较常见。比如，我们在第4课，将投资者人数与上证指数进行关联，但这两组数据的量纲不一致，直接显示的话，量纲小的数据，会被显示成类似一条直线，完全看不清波动。当时我们是将数据进行了缩放。

我们也可以让绘图软件来自动完成这个工作。这样做的好处是，我们不仅保留了数据的走势，还保留了数据本身，这会在刻度、hotspot数据上体现出来。

具体的做法就是使用twinx。我们在第12课已经使用过了。不过，这一次我们再进行一下升级，看看多于两个y轴如何处理。

```python
import matplotlib.pyplot as plt

fig, ax = plt.subplots()
fig.subplots_adjust(right=0.75)

twin1 = ax.twinx()
twin2 = ax.twinx()

# Offset the right spine of twin2. 
twin2.spines.right.set_position(("axes", 1.2))

p1, = ax.plot([0, 1, 2], [0, 1, 2], "C0", label="Density")
p2, = twin1.plot([0, 1, 2], [0, 3, 2], "C1", label="Temperature")
p3, = twin2.plot([0, 1, 2], [50, 30, 15], "C2", label="Velocity")

ax.set(xlim=(0, 2), ylim=(0, 2), xlabel="Distance", ylabel="Density")
twin1.set(ylim=(0, 4), ylabel="Temperature")
twin2.set(ylim=(1, 65), ylabel="Velocity")

ax.yaxis.label.set_color(p1.get_color())
twin1.yaxis.label.set_color(p2.get_color())
twin2.yaxis.label.set_color(p3.get_color())

ax.tick_params(axis='y', colors=p1.get_color())
twin1.tick_params(axis='y', colors=p2.get_color())
twin2.tick_params(axis='y', colors=p3.get_color())

ax.legend(handles=[p1, p2, p3])

plt.show()
```

生成的图如下：

![50%](https://images.jieyu.ai/images/2023/07/lesson16_twinx.png)

#### 3.1.3. 刻度
我们常常遇到这样的问题，一是刻度太过拥挤，或者文本太长；此时我们可以将标签文本进行旋转，或者减少刻度数量。

二是如何处理时间。对普通的时间问题，一般我们用matplotlib自带的就可以了。

下面的例子可供参考：
```python
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
from matplotlib.dates import (AutoDateLocator, YearLocator, MonthLocator,
                              DayLocator, WeekdayLocator, HourLocator,
                              MinuteLocator, SecondLocator, MicrosecondLocator,
                              RRuleLocator, rrulewrapper, MONTHLY,
                              MO, TU, WE, TH, FR, SA, SU, DateFormatter,
                              AutoDateFormatter, ConciseDateFormatter)

locators = [
    ('AutoDateLocator(maxticks=8)', '2003-02-01', '%Y-%m'),
    ('YearLocator(month=4)', '2003-02-01', '%Y-%m'),
    ('MonthLocator(bymonth=[4,8,12])', '2003-02-01', '%Y-%m'),
    ('DayLocator(interval=180)', '2003-02-01', '%Y-%m-%d'),
    ('WeekdayLocator(byweekday=SU, interval=4)', '2000-07-01', '%a %Y-%m-%d'),
    ('HourLocator(byhour=range(0,24,6))', '2000-02-04', '%H h'),
    ('MinuteLocator(interval=15)', '2000-02-01 02:00', '%H:%M'),
    ('SecondLocator(bysecond=(0,30))', '2000-02-01 00:02', '%H:%M:%S'),
    ('MicrosecondLocator(interval=1000)', '2000-02-01 00:00:00.005', '%S.%f'),
    ('RRuleLocator(rrulewrapper(freq=MONTHLY, \nbyweekday=(MO, TU, WE, TH,' +
     ' FR), bysetpos=-1))', '2000-07-01', '%Y-%m-%d')
]

formatters = [
    ('AutoDateFormatter(ax.xaxis.get_major_locator())'),
    ('ConciseDateFormatter(ax.xaxis.get_major_locator())'),
    ('DateFormatter("%b %Y")')
]


def plot_axis(ax, locator=None, xmax='2002-02-01', fmt=None, formatter=None):
    """Set up common parameters for the Axes in the example."""
    ax.spines.right.set_visible(False)
    ax.spines.left.set_visible(False)
    ax.spines.top.set_visible(False)
    ax.yaxis.set_major_locator(ticker.NullLocator())
    ax.tick_params(which='major', width=1.00, length=5)
    ax.tick_params(which='minor', width=0.75, length=2.5)
    ax.set_xlim(np.datetime64('2000-02-01'), np.datetime64(xmax))
    if locator:
        ax.xaxis.set_major_locator(eval(locator))
        ax.xaxis.set_major_formatter(DateFormatter(fmt))
    else:
        ax.xaxis.set_major_formatter(eval(formatter))
    ax.text(0.0, 0.2, locator or formatter, transform=ax.transAxes,
            fontsize=14, fontname='Monospace', color='tab:blue')


fig, ax = plt.subplots(len(locators), 1, figsize=(8, len(locators) * .8),
                       layout='constrained')
fig.suptitle('Date Locators')
for i, loc in enumerate(locators):
    plot_axis(ax[i], *loc)

fig, ax = plt.subplots(len(formatters), 1, figsize=(8, len(formatters) * .8),
                       layout='constrained')
fig.suptitle('Date Formatters')
for i, fmt in enumerate(formatters):
    plot_axis(ax[i], formatter=fmt)
```

但对k线，matplotlib自带的Locator和Formattor都不太好用，需要自行开发。
```python
from coursea import *
await init()
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
from matplotlib.dates import (AutoDateLocator, HourLocator,
                              MinuteLocator, ConciseDateFormatter)

bars = await Stock.get_bars("000001.XSHE", 500, FrameType.MIN30)
frames = bars["frame"]

def plot_axis(frames):
    fig, ax = plt.subplots(figsize=(10, 1), layout='constrained')
    
    ax.spines.right.set_visible(False)
    ax.spines.left.set_visible(False)
    ax.spines.top.set_visible(False)
    ax.yaxis.set_major_locator(ticker.NullLocator())
    ax.tick_params(which='major', width=1.00, length=10)
    ax.tick_params(which='minor', width=0.75, length=2.5)
        
    ax.set_xlim(frames[0], frames[-1])

    major_locator = AutoDateLocator(maxticks=8)
    minor_locator = HourLocator(interval=100)
    
    ax.xaxis.set_major_locator(major_locator)
    ax.xaxis.set_minor_locator(minor_locator)
    
    ax.xaxis.set_major_formatter(ConciseDateFormatter(major_locator))
    ax.xaxis.set_minor_formatter(ConciseDateFormatter(minor_locator))
    ax.tick_params(axis='x', rotation=45)
    
plot_axis(frames)
```
![](https://images.jieyu.ai/images/2023/07/lesson16-conciseformatter.png)

### 3.2. 文本和中文

matplotlib中，与文本打交道的方法主要有以下几种：

| method     | description                                          |
| ---------- | ---------------------------------------------------- |
| text       | 用以在任意位置输出文本(Axes、Figure)                 |
| annotate   | 在axes的任意位置，增加一个文本注释，和一个可选的箭头 |
| set_xlabel | 给x轴增加标签                                        |
| set_ylabel | 给y轴增加标签                                        |
| set_title  | 给Axes增加标题                                       |
| suptitle   | 给Figure增加标题                                     |
| figtext    | 给Figure增加文本                                     |


matplotlib默认不支持中文。在没有对 Matplotlib 进行设置时，直接使用中文，绘制的图像会出现中文乱码。

!!! attention
    如果系统中尚未安装中文字体，首先要安装中文字体。在Linux/Ubunut中一般是先将字体文件拷贝到/usr/share/fonts/truetype/目录下，然后执行 fc-cache -fv 命令。最后，通过 fc-list 查看，如果字体能列出来说明安装成功。

    在新安装字体的情况下，maptplotlib可能因为未更新缓存，导致仍然不能使用新装字体。此时可执行以下python脚本：

    ```python
        import shutil
        import matplotlib

        shutil.rmtree(matplotlib.get_cachedir())
    ```
然后我们还需要设置matplotlib，在绘图时使用新字体：

```python
#绘制折线图
import matplotlib.pyplot as plt
plt.rcParams["font.sans-serif"]=["WenQuanYi Micro Hei"] #设置字体
plt.rcParams["axes.unicode_minus"]=False #正常显示负号

year = [2017, 2018, 2019, 2020]
people = [20, 40, 60, 70]
#生成图表
plt.plot(year, people)
plt.xlabel('年份')
plt.ylabel('人口')
plt.title('人口增长')

#设置纵坐标刻度
plt.yticks([0, 20, 40, 60, 80])
plt.fill_between(year, people, 20, color = 'green')
plt.show()
```

### 3.3. 样式和颜色
在批量绘图时，我们常常会遇到重复设置选项的情况。matplotlib允许我们通过rcParams或者stylesheet来减轻这项繁琐工作。此外，使用stylesheet还将使得绘图结果可以在不同机器上重现。

style用来设置背景、字体及字体大小、线的宽度、标签字体大小，线的颜色等等。

matplotlib提供了许多预置的样式，我们可以用以下命令来显示所有的样式：

```python
print(plt.style.available)
```

这会输出以下样式（依据版本不同而不同）：

```
['Solarize_Light2', '_classic_test_patch', '_mpl-gallery', '_mpl-gallery-nogrid', 'bmh', 'classic', 'dark_background', 'fast', 'fivethirtyeight', 'ggplot', 'grayscale', 'seaborn-v0_8', 'seaborn-v0_8-bright', 'seaborn-v0_8-colorblind', 'seaborn-v0_8-dark', 'seaborn-v0_8-dark-palette', 'seaborn-v0_8-darkgrid', 'seaborn-v0_8-deep', 'seaborn-v0_8-muted', 'seaborn-v0_8-notebook', 'seaborn-v0_8-paper', 'seaborn-v0_8-pastel', 'seaborn-v0_8-poster', 'seaborn-v0_8-talk', 'seaborn-v0_8-ticks', 'seaborn-v0_8-white', 'seaborn-v0_8-whitegrid', 'tableau-colorblind10']
```

我们随机挑一些试试：
```python
plt.style.use('seaborn-v0_8-pastel')

for y in range(20, 100, 5):
    plt.plot([i for i in np.arange(50)], np.linspace(0, y, 50))
```

显示结果如下：

![75%](https://images.jieyu.ai/images/2023/07/lesson16-use-style.png)

有时候我们可能只希望调整一下颜色。许多API允许我们提供颜色，比如在画一条线时，这些方法和参数都比较直观。比较高级的用法是使用set_prop_cycle来控制颜色和其它样式在未来的绘图命令中进行循环，或者使用colormap。

比如，当我们绘制均线系统时，往往希望给每条均线指定不同的颜色，此时就可以使用set_prop_cycle(color='')来指定，这样可以减轻编码的复杂度。

```python
colors = plt.cm.Pastel1.colors
plt.gca().set_prop_cycle('color', colors)
for i, y in enumerate(range(20, 100, 5)):
    plt.plot([i for i in np.arange(50)], np.linspace(0, y, 50), label=f'C{i%len(colors)}')
plt.legend()
```
![75%](https://images.jieyu.ai/images/2023/07/lesson16-color-cycle.png)

#### 3.3.1. colormap
在这一节，我们来看一个实际的例子。关于设计，特别是配色，我们常常需要从他人身上学习。一个常见的例子是，我们发现一张非常具有美感的图，或者网站，我们希望借鉴它的调色。于是，我们通过工具把它的主要配色撷取下来，然后转换成color map，在我们自己的绘图中使用。

这个例子演示了如何转换某款经典口红配色成为colormap。

![50%](https://images.jieyu.ai/images/2023/07/ysl_2021_spring.png)

首先，我们借助工具将这些颜色提取出来，保存为hex方式，然后将其绘制成色板：

```python
import numpy as np
import matplotlib.pyplot as plt
import mpl_toolkits.axes_grid1 as axes_grid1
import re
import math

ysl = [
        "4D0609","8F2B37","7F1C2E","AC5754",
        "790D1D", "FE7082","A20D44","B61629",
        "CE171B","ED322B","F776AE","CE242D",
        "C91D37","A63139","FE52AE","C91715", 
        "DE2025","BE161D","530C1E", "FE82AE"]

def parse_hex(colors):
    cmap = []
    for c in colors:
        cmap.append((int(c[:2], base=16), int(c[2:4], base=16), int(c[4:], base=16)))
            
    return cmap

def lum (r,g,b):
    return .8 * r + .1 * g + .1 * b

colors = sorted(parse_hex(ysl), key = lambda rgb: lum(*rgb), reverse=False)
colors = np.array(colors).reshape(5, 4, 3)

plt.imshow(colors, aspect='auto')
plt.gca().spines.top.set_visible(False)
plt.gca().spines.right.set_visible(False)
plt.gca().spines.left.set_visible(False)
plt.gca().spines.bottom.set_visible(False)
plt.xticks([])
plt.yticks([])

for y in range(colors.shape[0]):
    for x in range(colors.shape[1]):
        plt.text(x, y, f"#{colors[y,x][0]:02X}{colors[y,x][1]:02X}{colors[y,x][2]:02X}", 
                horizontalalignment='center',
                 verticalalignment='center', color='white')
```

![75%](https://images.jieyu.ai/images/2023/07/ysl_palette.png)

现在，我们把上述颜色转换成color map，然后实际运用一下：

```python
from matplotlib.colors import ListedColormap
import numpy as np
import matplotlib.pyplot as plt

# extract palettes from coolors.co

ysl = [
        "4D0609","8F2B37","7F1C2E","AC5754",
        "790D1D", "FE7082","A20D44","B61629",
        "CE171B","ED322B","F776AE","CE242D",
        "C91D37","A63139","FE52AE","C91715", 
        "DE2025","BE161D","530C1E", "FE82AE"]

def parse_hex(colors):
    cmap = []
    for c in colors:
        cmap.append((int(c[:2], base=16)/255, int(c[2:4], base=16)/255, int(c[4:], base=16)/255, 1))
            
    return cmap


ysl_cmp = ListedColormap(parse_hex(ysl))
x = np.arange(20)
y = np.random.randint(5, 20, 20)

fig, ax = plt.subplots(figsize=(10, 7))
ax.bar(x, y, color=ysl_cmp.colors)
ax.set_title("Different colors for each bar")
```

![75%](https://images.jieyu.ai/images/2023/07/lesson16_use_colormap.png)

[^hunter]: https://github.com/fperez/blog/blob/master/130629-In-memoriam-JohnHunter.ipynb
