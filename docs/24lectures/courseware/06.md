---
title: 小市值策略
---

## 1. 小市值策略简介
在一定的时间周期内，持有市值最小的若干只股票，周期结束时卖出；在下一周期开始时，再买入市值最小的若干只股票，在周期结束时卖出，如此循环。1981年Banz基于纽交所长达40年的数据发现，小市值股票月均收益率比其他股票高0.4%。因此，小市值策略是在全球范围来讲，普遍适用的一个策略。

市值因子也被纳入了Fama三因子模型和五因子模型，有时候也被称为规模因子。

![75%](https://images.jieyu.ai/images/2023/10/lesson6-smb-factor.png)


研究表明，A股市场在2016年以前，规模因子的显著性甚至超过了欧美等发达国家市场。不过，任何因子都很难一直有效，到了2017-2018年期间，大市值股票的表现明显优于小市值股票，使得规模因子在A股市场上的有效性存疑。

不过，我们在聚宽平台上，以它提供的示例策略，运行后，得到的结果表明，该因子在最近两年依然有效：

![75%](https://images.jieyu.ai/images/2023/04/**************.png)

下面，我们就先来实现这个策略，然后来讨论小市值策略应该如何使用的问题。

## 2. 策略实现
### 2.1. 初始化

```python
import logging
import os
from IPython.display import clear_output

import cfg4py
from coretypes import FrameType
import omicron
from omicron import tf
from omicron.models.security import Security
from omicron.models.stock import Stock

import jqdatasdk as jq


account = os.environ.get("jq_account")
passwd = os.environ.get("jq_password")
jq.auth(account, passwd)

cfg = cfg4py.init("/etc/zillionare")

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger("test")
# logger.addHandler(logging.StreamHandler(stream=sys.stdout))

await omicron.init()
clear_output()
```

### 2.2. 绘图
```python
# 绘制资产曲线及回测指标
import datetime
import logging
from collections import defaultdict
from copy import deepcopy
from typing import List, Union

import arrow
import numpy as np
import pandas as pd
import plotly.graph_objects as go
from coretypes import BarsArray, Frame, FrameType
from numpy.typing import NDArray
from plotly.subplots import make_subplots

from omicron import tf
from omicron.extensions import fill_nan
from omicron.models.security import Security
from omicron.models.stock import Stock

logger = logging.getLogger(__name__)


class MetricsGraph:
    def __init__(self, bills: dict, metrics: dict):
        self.metrics = metrics
        self.trades = bills["trades"]
        self.positions = bills["positions"]
        self.start = arrow.get(bills["assets"][0][0]).date()
        self.end = arrow.get(bills["assets"][-1][0]).date()

        self.frames = [
            tf.int2date(f) for f in tf.get_frames(self.start, self.end, FrameType.DAY)
        ]

        # 记录日期到下标的反向映射，这对于在不o
        self._frame2pos = {f: i for i, f in enumerate(self.frames)}
        self.ticks = self._format_tick(self.frames)

        self.assets = pd.DataFrame(bills["assets"], columns=["frame", "assets"])[
            "assets"
        ].to_numpy()
        self.nv = self.assets / self.assets[0]

    def _fill_missing_prices(self, bars: BarsArray, frames: Union[List, NDArray]):
        """将bars中缺失值采用其前值替换

        当baseline为个股时，可能存在停牌的情况，这样导致由此计算的参考收益无法与回测的资产收益对齐，因此需要进行调整。

        出于这个目的，本函数只返回处理后的收盘价。

        Args:
            bars: 基线行情数据。
            frames: 日期索引

        Returns:
            补充缺失值后的收盘价序列
        """
        _close = pd.DataFrame(
            {
                "close": pd.Series(bars["close"], index=bars["frame"]),
                "frame": pd.Series(np.empty((len(frames),)), index=frames),
            }
        )["close"].to_numpy()

        # 这里使用omicron中的fill_nan，是因为如果数组的第一个元素即为NaN的话，那么DataFrame.fillna(method='ffill')将无法处理这样的情况(仍然保持为nan)

        return fill_nan(_close)

    def _format_tick(self, frames: Union[Frame, List[Frame]]) -> Union[str, NDArray]:
        if type(frames) == datetime.date:
            x = frames
            return f"{x.year:02}-{x.month:02}-{x.day:02}"
        elif type(frames) == datetime.datetime:
            x = frames
            return f"{x.month:02}-{x.day:02} {x.hour:02}:{x.minute:02}"
        elif type(frames[0]) == datetime.date:  # type: ignore
            return np.array([f"{x.year:02}-{x.month:02}-{x.day:02}" for x in frames])
        else:
            return np.array(
                [f"{x.month:02}-{x.day:02} {x.hour:02}:{x.minute:02}" for x in frames]  # type: ignore
            )

    async def _metrics_trace(self):
        metric_names = {
            "start": "起始日",
            "end": "结束日",
            "window": "资产暴露窗口",
            "total_tx": "交易次数",
            "total_profit": "总利润",
            "total_profit_rate": "利润率",
            "win_rate": "胜率",
            "mean_return": "日均回报",
            "sharpe": "夏普率",
            "max_drawdown": "最大回撤",
            "annual_return": "年化回报",
            "volatility": "波动率",
            "sortino": "sortino",
            "calmar": "calmar",
        }

        # bug: plotly go.Table.Cells format not work here
        metric_formatter = {
            "start": "{}",
            "end": "{}",
            "window": "{}",
            "total_tx": "{}",
            "total_profit": "{:.2f}",
            "total_profit_rate": "{:.2%}",
            "win_rate": "{:.2%}",
            "mean_return": "{:.2%}",
            "sharpe": "{:.2f}",
            "max_drawdown": "{:.2%}",
            "annual_return": "{:.2%}",
            "volatility": "{:.2%}",
            "sortino": "{:.2f}",
            "calmar": "{:.2f}",
        }

        metrics = deepcopy(self.metrics)
        baseline = metrics["baseline"]
        del metrics["baseline"]

        if "code" in baseline:
            baseline_name = await Security.alias(baseline["code"])
            del baseline["code"]
        else:
            baseline_name = "基准"

        metrics_formatted = []
        for k in metric_names.keys():
            if metrics.get(k):
                metrics_formatted.append(metric_formatter[k].format(metrics.get(k)))
            else:
                metrics_formatted.append("-")

        baseline_formatted = []
        for k in metric_names.keys():
            if baseline.get(k):
                baseline_formatted.append(metric_formatter[k].format(baseline.get(k)))
            else:
                baseline_formatted.append("-")

        return go.Table(
            header=dict(values=["指标名", "策略", baseline_name]),
            cells=dict(
                values=[
                    [metric_names[k] for k in metrics],
                    metrics_formatted,
                    baseline_formatted,
                ],
                font_size=10,
            ),
        )

    async def _trade_info_trace(self):
        """构建hover text 序列"""
        X = []
        Y = []
        data = []

        # convert trades into hover_info
        merged = defaultdict(list)
        for _, trade in self.trades.items():
            trade_date = arrow.get(trade["time"]).date()

            ipos = self._frame2pos.get(trade_date)
            if ipos is None:
                logger.warning(
                    "date  %s in trade record not in backtest range", trade_date
                )
                continue

            name = await Security.alias(trade["security"])
            price = trade["price"]
            side = trade["order_side"]
            filled = trade["filled"]

            trade_text = f"{side}:{name} {filled/100:.0f}手 价格:{price:.02f} 成交额{filled * price/10000:.1f}万"

            merged[trade_date].append(trade_text)

        for dt, text in merged.items():
            ipos = self._frame2pos.get(dt)
            Y.append(self.nv[ipos])
            X.append(self._format_tick(dt))

            asset = self.assets[ipos]
            hover = f"资产:{asset/10000:.1f}万<br>{'<br>'.join(text)}"
            data.append(hover)

        trace = go.Scatter(x=X, y=Y, mode="markers", text=data, name="交易详情")
        return trace

    async def plot(self, baseline_code: str = "399300.XSHE"):
        """绘制资产曲线及回测指标图"""
        n = len(self.assets)
        bars = await Stock.get_bars(baseline_code, n, FrameType.DAY, self.end)

        baseline_prices = self._fill_missing_prices(bars, self.frames)
        baseline_prices /= baseline_prices[0]

        fig = make_subplots(
            rows=1,
            cols=2,
            shared_xaxes=False,
            specs=[
                [{"type": "scatter"}, {"type": "table"}],
            ],
            column_width=[0.75, 0.25],
            horizontal_spacing=0.01,
            subplot_titles=("资产曲线", "策略指标"),
        )

        fig.add_trace(await self._metrics_trace(), row=1, col=2)

        print("baseline", len(baseline_prices))
        baseline_trace = go.Scatter(
            y=baseline_prices,
            x=self.ticks,
            mode="lines",
            name="baseline",
            showlegend=True,
        )
        fig.add_trace(baseline_trace, row=1, col=1)

        nv_trace = go.Scatter(
            y=self.nv, x=self.ticks, mode="lines", name="策略净值", showlegend=True
        )
        fig.add_trace(nv_trace, row=1, col=1)

        trade_info_trace = await self._trade_info_trace()
        fig.add_trace(trade_info_trace, row=1, col=1)

        fig.update_xaxes(type="category", tickangle=45, nticks=len(self.ticks) // 5)
        fig.update_layout(margin=dict(l=20, r=20, t=50, b=50), width=1040, height=435)
        fig.show()
```
### 2.3. 策略主体代码

```python
from jqdatasdk import query, valuation
import uuid
from traderclient import TraderClient
import datetime
from typing import List, Optional
import numpy as np
from coretypes.errors.trade import TradeError

# set the envar during debugging mode only
# os.environ["TRADER_CLIENT_TIMEOUT"] = "600"


class SmallCapStrategy:
    def __init__(
        self,
        start: datetime.date,
        end: datetime.date,
        account: Optional[str] = None,
        token: Optional[str] = None,
        url: Optional[str] = None,
        pool_size_ratio=0.01,
    ):

        self.pool_size_ratio = pool_size_ratio
        self.bills = None
        self.metrics = None

        self.start = start
        self.end = end
        self.token = token or uuid.uuid4().hex
        self.account = account or f"smallcap-{self.token[-4:]}"

        self.broker = TraderClient(
            url or cfg.backtest.url,
            self.account,
            self.token,
            is_backtest=True,
            start=self.start,
            end=self.end,
        )

    async def backtest(self):
        for i, frame in enumerate(tf.get_frames(self.start, self.end, FrameType.DAY)):
            # 没到调仓时间
            if i % 5 != 0:
                continue

            dt = tf.int2date(frame)
            positions = self.broker.positions(dt)
            buylist = await self.choose_target(dt)

            to_sell = np.setdiff1d(positions["security"], buylist)
            to_buy = np.setdiff1d(buylist, positions["security"])

            # 优先处理卖出，以释放资金
            for sec in to_sell:
                try:
                    volume = self.broker.available_shares(sec, dt)
                    self.broker.market_sell(
                        sec, volume, order_time=tf.combine_time(dt, 14, 55)
                    )
                except TradeError as e:
                    logger.warning(str(e).split("\n")[0])
                except Exception as e:
                    logger.exception(e)

            cash = self.broker.available_money
            per_stock = cash / len(to_buy)
            for sec in to_buy[:-1]:
                try:
                    await self.broker.buy_by_money(
                        sec, per_stock, order_time=tf.combine_time(dt, 14, 55)
                    )
                except TradeError as e:
                    logger.warning(str(e).split("\n")[0])
                except Exception as e:
                    logger.exception(e)

            # 多余的资金结转到最后一支股票上
            cash = self.broker.available_money
            sec = to_buy[-1]
            await self.broker.buy_by_money(
                sec, cash, order_time=tf.combine_time(dt, 14, 55)
            )

        self.broker.stop_backtest()
        self.bills = self.broker.bills()
        self.metrics = self.broker.metrics(baseline="399300.XSHE")

    async def plot(self):
        mg = MetricsGraph(self.bills, self.metrics)
        await mg.plot()

    async def filter_paused_stock(self, buylist: List[str], dt: datetime.date):
        secs = await Security.select(dt).eval()
        in_trading = jq.get_price(
            secs, fields=["paused"], start_date=dt, end_date=dt, skip_paused=True
        )["code"].to_numpy()

        return np.intersect1d(buylist, in_trading)

    async def choose_target(self, dt: datetime.date):
        # 聚宽示例策略在这里加了20~30亿市值限制，也有它的道理。也许动态调整更好
        q = (
            query(valuation.code, valuation.market_cap)
            .filter(valuation.market_cap.between(20, 30))
            .order_by(valuation.market_cap.asc())
        )

        # 选出低市值的股票，构成buylist
        df = jq.get_fundamentals(q, dt)

        buylist = list(df["code"])

        # 过滤停牌股票
        buylist = await self.filter_paused_stock(buylist, dt)
        logger.debug("got %s secs after paused been filtered", len(buylist))

        # 随着市场扩容，可以多取，但最少取5支
        size = max(int(len(df) * self.pool_size_ratio), 5)
        return buylist[:size]
```

我们通过下面的代码来启动回测，并且生成回测报告：

```python
start = tf.day_shift(datetime.date(2023, 4, 1), 0)
end = tf.day_shift(datetime.date(2023, 4, 28), 0)

s = SmallCapStrategy(start, end)
await s.backtest()
await s.plot()
```

我们会得到类似以下图的一个报告：


![75%](https://images.jieyu.ai/images/2023/10/lesson6-backtest-report.png)

阅读这个例子的源码，我们需要注意以下几点：
1. 一个最简单的策略，它的回测框架至少要包括哪些内容？
2. TraderClient的作用和基本使用方法？
3. 回测结束，如何将资产曲线和策略指标绘制出来？

## 3. 策略优化
有许多因素可能影响到策略优化。

### 3.1. 择时优化

市场的资金量是有限的。当市场发生风格转换时，需要有指标来检测这种转换，在小市值策略有效时进场，无效时离场。一般认为，可以将上证50（或者沪深300）作为大市值代表，中证1000作为小市值代表，对照二者相关性，来决定小市值策略的入场和离场时机。

### 3.2. 规则优化

首先，回测系统可以防止在跌停板上卖出，阻止在涨停板上买入，但不是相反。显然，我们不应该在涨停板上卖出股票，因为根据统计，连板指数大约是以每天1.8%的速度在上涨。因此，在涨停板上卖出股票，意味着我们将在这部分资金上损失1.8%的超额收益。这不是一个需要调节的参数，应该直接写死在我们的策略中。

其次，我们在简单地回测中，就发现买入了好几支ST的股票。实际上，这些标的是应该排除掉的，或者说，至少应该在年报前后排除掉。这一部分，至少排除掉ST个股，应该写死在我们的策略中。

### 3.3. 参数优化

接下来，是一些需要通过反复测试来进行调优的参数。

首先，注意到我们设置了一个pool_size_ratio这个参数。在一个较长跨度的回测中，我们需要注意到A股的交易品种是在不断增长的。如果我们固定地取20支股票，那么在2005年前后，这可能会占到小市值股票中的相当一部分。而策略的用意则是要找出最小市值的那一部分。

因此，我们设置了pool_size_ratio这个参数，并且为防止取值过少，我们还在代码中硬性规定了不得少于5支。

另外，策略并不是严格按照市值来进行筛选的。实际上，它首先排除掉了不在20亿到30亿这个区间的一些标的。这么做有一定的道理，因为一支股票如果市值太小，它的成长能力也可能不够强。但这应该成为一个被测试的参数。

再次，我们对持仓周期的选择是5天。这也是一个可以调节的参数。

最后，我们没有设定首次买入的时间。实际上，如果放在周四收盘买入，情况可能会有所不同。

这些参数应该作为SmallCapStrategy初始化参数的一部分，并且使用sklearn.model_selection.GridSearchCV来自动进行参数搜索。
