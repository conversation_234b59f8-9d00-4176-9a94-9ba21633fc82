---
title: Ta<PERSON><PERSON>b技术分析
mainfont: WenQuanYi Micro Hei
puppeteer:
    format: "A4"
    scale: 1
    margin:
        top: 2.5 cm
        right: 2cm
        bottom: 2.5 cm
        left: 2cm
    displayHeaderFooter: true
    headerTemplate: '<div style="width:100%; text-align:center; border-bottom: 1pt solid #eeeeee; margin: 20px 10px 10px; font-size: 10pt;padding-bottom:10px"><span class=title></span></div>'
    footerTemplate: '<div style="display:flex; justify-content:space-around;width:100%;border-top: 1pt solid #eeeeee; margin: 10px 10px 20px; font-size: 8pt;color:#aaa"><div style="width:30%"><span class=pageNumber></span>/<span class=totalPages></span></div><div style="width:30%">大富翁量化课程</div><div style="width:30%">宽粉（quantfans_99)</div>'
---

![75%](https://images.jieyu.ai/images/2023/07/lesson10-outline.png)

talib是最重要的技术分析库之一。

技术分析是指通过分析证券的历史交易活动和历史数据（如价格和成交量），来预测未来股价运动的分析方式。它基于这样两个假设，或者说信仰：一是所有跟证券相关的信息，都已包含在价格当中。从这个假设出发，技术分析师使用各种技术指标和图形模式来判断趋势、动量以及隐藏在该支证券运动背后的**情绪**。二是历史总在重复自己。

技术分析与EMH的基本原则是相矛盾的。EMH认为，在一个证券市场中，价格完全反映了所有可以获得的讯息，这样的市场就是有效市场。实际上，由于信息鸿沟的存在，信息总是由少数人逐渐向大多数人扩散的，在这个过程中，价格的变化恰好是信息扩散的某种函数，谁优化发现这个函数，谁就找到了致富的密码。

由于AI和算力的增长，技术分析的用法也正在发生变化，特别是运用机器学习来完成多个指标间的组合和权重分配，建立自适应模型越来越普遍。

与技术分析相对的分析手段则是基本面分析。它是通过各种宏观和微观经济学因子，来探寻证券标的的内在价值的，然后将其内在价值与当前价格进行比较，决定交易方向的一种分析方式。

一般而言，技术分析在较短的周期内更加有效，但资金容量也小；基本面分析只在较长的投资周期中有效，但资金容量大。这是因为，证券的价格终究是由公司的价值来决定的；公司价值的变化必然是一个长周期的过程；从实务上讲，公司财务的批露也是以季为单位。而技术分析赚的是波动的钱，它无法预出一家公司未来的发展状况，因此当我们使用技术分析时，几乎只能着眼于短期波动。

传统上看，技术分析基于技术指标和模式识别，前者如移动均线、摆动类指标、动量类指标等，后者如早晨之星，三只乌鸦，十字星等。

talib涵盖了这两方面的分析库。

## 1. 安装talib
作为python程序员，安装ta-lib共分两步。

首先，是安装ta-lib的原生库，其次是安装它的Python wrapper。在不同的平台下，安装原生库的方法是不一样的。
### 1.1. 原生库的安装
#### 1.1.1. Macos
在macos下安装比较容易，我们可以通过以下命令进行安装：
``` bash
brew install TA-Lib
```
#### 1.1.2. Linux
在Linux下的安装需要执行编译过程。首先我们要下载talib的[源代码](https://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz)，然后执行以下命令:

```bash
sudo apt update && sudo apt upgrade -y && sudo apt autoremove -y
sudo apt-get install build-essential -y
curl -L http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz | tar -xzv -C /tmp/
cd /tmp/ta-lib
./configure --prefix=/usr
make
sudo make install
```

这是在Linux下编译安装c库的一个典型的步骤。
#### 1.1.3. Windows
在windows下的安装最为复杂。主要原因在于，如果读者不做windows下的c/c++开发，可能机器上就没有相应的编译工具链。在构建talib之前，还需要先安装visual c++的分发包，MSVC BuildTool等编译工具，设置编译环境变量。

首先编译工具的下载就比较困难，一是编译工具可能有10个G左右之巨，二是这些工具分散在不同的地址处，有的还无法访问。这使得整个过程更加复杂和耗时。如果需要在windows下编译运行talib的读者，可以阅读我们在omicron的帮助文档里提供的指南，这里就不一一演示了。

除上述方案外，也存在一些跨平台的安装方案，比如使用conda，或者它人预编译好的wheel包。
#### 1.1.4. 使用conda
这个方法在最新的TA-Lib的readme中有推荐，但还没出现在正式帮助文档中，我们也没有测试过。大家可以自行尝试一下：
``` bash
conda install -c conda-forge ta-lib
```
#### 1.1.5. 第三方构建的wheel包
另外一个在windows下安装的方法，就是直接使用非官方编译的wheel，一次性完成安装。

在ldf.uci.edu网站上，提供了他们预编译的[wheel包](https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib)，我们可以在这里直接下载wheel包进行安装。

安装ta-lib的python wrapper相对比较简单，我们只要运行以下命令即可：
```bash
pip install TA-Lib
```

!!! attention
    使用它人预构建的wheel包可能存在安装风险。

### 1.2. 安装python wrapper
安装ta-lib的Python封装库比较简单，我们执行以下命令:

```bash
pip install TA-Lib
```

注意这里的大小写。

安装原生库与封装库是没有顺序之分的，但如果在安装过程中，出现以下错误：

```
func.c:256:28: fatal error: ta-lib/ta_libc.h: No such file or directory
compilation terminated.
```

我们可以通过安装原生库后，再安装这个封装库来解决。

## 2. ta-lib概览
### 2.1. 关于帮助文档
ta-lib是一个注册商标，其IP属于一家名叫TicTacTec的公司，这家公司的业务之一就是提供培训和相关分析工具。可能由于这个原因，ta-lib的c库并没有提供帮助文档。

不过，Python封装库提供了一些基础性的[文档](https://ta-lib.github.io/ta-lib-python/doc_index.html)。这部分文档中，除了安装指南，两类接口的说明外，还有一个功能列表。API级的说明通过docstring提供，不过这部分文档不是很详细。比如，象布林带指标，需要传入均线计算类型，这部分API文档就没有说明均线类型如何传入，共有哪些取值。

### 2.2. 两类接口
talib提供了两类接口。一类是函数式的，一类是抽象接口。抽象接口提供了更多的灵活性，比如，我们可以继承abstract.Function类，重载`set_input_arrays`方法，来让talib可以直接操作pandas DataFrame。

函数式接口直接通过talib模块暴露，使用方法如下：

```python
import talib as ta
import numpy as np

ta.SMA(np.arange(20).astype(np.float64), 5)
```

如果我们不想涉及抽象接口的复杂用途，那么使用上跟函数式接口是一样的，只不过导入位置不同：

```python
import talib.abstract as ab

ab.SMA(np.arange(20).astype(np.float64), 5)
```
另外，通过抽象接口暴露出来的对象，会多一个info属性。
```python
print(ab.SMA.info)
```
### 2.3. 方法概览
ta-lib当前版本共包含了158个函数。

```python
import talib
print('There are {} TA-Lib functions!'.format(len(talib.get_functions())))
```

ta-lib函数是按分组进行组织的，我们可以通过`get_function_groups`方法来查看：

```python
for group, funcs in talib.get_function_groups().items():
    print(group)
    print('-----------------------------------------')
    for func in funcs:
        f = Function(func)
        print('{} - {}'.format(func, f.info['display_name']))
    print()
```

这会输出这些分组：
* Cycle Indicators
* Math Operators
* Math Transform
* Momentum Indicators
* Overlap Studies
* Pattern Recognition
* Price Transform
* Statistic Functions
* Volatility Indicators
* Volume Indicators

在本课中，我们更关注指标类函数，以及模式识别函数。其它一些方法，在像numpy这样的库中已经存在了，就没有必要再关注。

## 3. 常用指标函数
技术指标可以用来平滑价格、过滤高频噪声，判断价格的运动方向（及强弱），划出支撑位和压力位和预测波动率。

我们将在这一课里，介绍6个最常用的技术指标。
### 3.1. ATR
ATR是技术分析大师Welles Wilder发明的一个衡量市场平均真实波幅的指标，主要用于研判买卖时机，是显示市场变化率的反趋向指标，也是一个比较常用的技术指标。

Welles Wilder是机械工程师和技术分析师。除了ATR之外，他还贡献了RSI和Parabolic Sar等广泛使用的指标。

它的计算方法是，先用以下公式计算TR：

$$
TR = MAX[(H-L), |H - C_P|, |L - C_P|]
$$

$$ H = Todays'\ High $$

$$ L = Today's\ Low $$

$$ C_p = Yesterday's\ closing\ price $$

$$ Max = Highest\ value\ of\ the\ three\ terms $$

然后再计算TR的指数移动平均值，就得到ATR。我们先手动计算一次：
```python
from coursea import *
await init()

from omicron.talib import moving_average

bars = await Stock.get_bars("000001.XSHE", 40, FrameType.DAY)
close, high, low = bars["close"], bars["high"], bars["low"]

hl = (high - low)[1:]
hc = np.abs(high[1:] - close[:-1])
lc = np.abs(low[1:] - close[:-1])

# 从三个数组中，取对应列中最大的值，相当于
# A = array([0, 1, 2])
# B = array([1, 0, 3])
# C = array([3, 0, 4])
# 结果为 array([3, 1 ,4])
# 此函数等效于以下方法
# np.vstack([A, B, C]).max(axis=0)
tr = np.maximum.reduce((hl, hc, lc))

atr = moving_average(tr, 14)
print(atr)
```
在talib中，我们使用以下方法进行计算:
```python
import talib as ta

code = "000001.XSHE"
bars = await Stock.get_bars(code, 40, FrameType.DAY)

close = bars["close"]
high = bars["high"]
low = bars["low"]
ta.ATR(close = close.astype(np.float64),
        high = high.astype(np.float64),
        low = low.astype(np.float64)
)
```
ATR有着广泛的用途，其用途之一就是仓位管理。比如，如果我们计划买入多支标的，如何在多个标的之间分配资金呢？

这里为方便理解，我们仅以两支标的为例，进行说明。

假设有A、B两支标的，仓位管理上最简单的方法是平分仓位。但这样一来，如果标的A的波动比标的B大，那么总体收益率将由A来决定。从而我们买入多支标的以平抑波动，分散风险的目标就不能达成。

此时，我们可以通过ATR来按权重分配资金，实现分散风险的目标。

假设我们有100万资金，希望保证每日波动在1%以内，也就是损益在1万元以内。

假设出现最坏的情况，次日所有持仓都下跌一个ATR（当然还可能出现都超出一个ATR的更极端的情况），那么实际上，我们每支标的允许亏损就是5千元。我们用这5000除以个股ATR的值，就得到每支标的可以持有的仓位

比如，假设A当前的ATR为0.152，相当于收盘价(3.72)的4%；假设B当前的ATR为4.7，相当于收盘价（70.85）的6.7%。那么我们可以持有A大约65800股：

$$
65800 \approx 10000 / 0.152
$$

这是用5000除以A的ATR得到的结果。此时我们持有A约12.2万元。

我们可以持有B大约2100股：

$$ 2100 \approx 10000/4.7 $$

这是用5000除以B的ATR得到的结果。此时我们持有B约7万元

这样一来，即使A、B股次日双双下跌一个ATR，即A下跌4%，B下跌4.7%，我们也只亏损1万元左右。如果我们对风险比较敏感，还可以对系数进行调整。

我们也可以使用同样的原理，不是以固定的止损比率，而是以标的ATR的一定比例来设置止损。这样一来，避免对某些股票止损设置过小，股性活跃的过早被震荡出来，同时又对某些股票止损设置过大，股性不活跃的止损过慢，利润被侵蚀过多。

此外，正如我们在网格交易法那一课中讲过，ATR还可用来计算网格。它在海龟交易法中也有应用。

### 3.2. 移动平均线

移动平均(moving average)在统计学中是一种通过创建整个数据集中不同子集的一系列平均数来分析数据点的计算方法。计算方法如下：

给定一个数列和一个固定子集大小，移动平均数的第一个元素是由数列的初始固定子集的平均值得到的。然后通过“向前移位”修改子集，即排除序列的第一个数，并在子集中包含下一个值。 

从数学上看，移动平均是卷积的一种。我们在第9章的习题中，也给出了一道题，要求通过np.convolve来计算移动平均。因此，移动平均可以看成信号处理的低通滤波器的例子，它过滤了高频噪声，反映出中长期低频趋势。很多人理解移动平均时，都把它当成噪声过滤、信号平滑的一种方式，就是从这个角度出发的。

在金融领域，由于价格有时序特性，移动平均作为信息平滑工具的主要缺陷是，它是一种滞后指标，使得波的相位落后于真实的波动几个周期，我们以SMA为例：

![](https://images.jieyu.ai/images/2023/06/ma-phase-lag.png)

上图显示，在一个以20为窗口的移动平均线中，ma的相位与原序列相比，晚了10个周期（即窗口的一半）。

关于更详细的讨论，可以参考[石川](https://zhuanlan.zhihu.com/p/38276041)。

talib提供了好几个移动平均线的计算方法。

#### 3.2.1. SMA
SMA是简单移动平均的意思，它是前`n`个数据的未加权平均数。

sma的计算公式如下：

$$
\bar{p}_{SM} = \frac{p_M + p_{M-1} + ... + p_{M - (n - 1)}}{n} \\
= \frac{1}{n}\sum_{i=0}^{n-1}p_{M-i}
$$

在talib中，我们这样计算SMA：
```python
import talib as ta
import numpy as np

ta.SMA(np.arange(20).astype(np.float64), 5)
```
注意talib输出了一个等长的序列，但前面几位的值为np.nan。输出等长的值有利于输入、输出在时间坐标上的对齐，但我们有时候需要手动处理这些无效值。

#### 3.2.2. EMA

指数平均是以指数式递减加权的移动平均。其公式如下：

![50%](https://images.jieyu.ai/images/2023/07/ema_formula.png)

我们对比一下SMA和EMA生成的图形：

```python
import plotly.express as px

close = np.array([-x**2 + x for x in range(-20, 20)]).astype(np.float64)
sma = ta.SMA(close, 20)
ema = ta.EMA(close, 20)

df = pd.DataFrame({
    "close": close,
    "ma": sma,
    "ema": ema
})
df.index = np.arange(-20, 20)

px.line(df)
```
可以看出，由于进行了加权，所以ema比ma下降的更快，更能反映当前的状态。但是，它仍然是滞后指标，它仍然不能反映方向上的变化。

#### 3.2.3. WMA
WMA是加权移动平均（weighted moving average，WMA），计算公式如下：

![50%](https://images.jieyu.ai/images/2023/07/wma_formula.png)

与EMA不同的是，在t0日的权重为0，而在EMA中，t0日的权重仍然大于0，但逼近0。

在talib中，调用方法是:
```python
import plotly.express as px

close = np.array([-x**2 + x for x in range(-20, 20)]).astype(np.float64)
sma = ta.SMA(close, 20)
ema = ta.EMA(close, 20)
wma = ta.WMA(close, 20)

df = pd.DataFrame({
    "close": close,
    "ma": sma,
    "ema": ema,
    "wma": wma
})
df.index = np.arange(-20, 20)

px.line(df)
```
除此之外，还有其它几种计算移动平均的方式，比如分形自适应移动平均（FRactal Adaptive Moving Average，FRAMA）、赫尔移动平均（Hull Moving Average, HMA)等，它们都试图解决移动平均的滞后性问题。但实际上，在这里可能高深的数学技巧并不一定有用。既要去掉高频噪声，又要刻画出当前的微小变化，这似乎本身就是一个悖论。

个人认为，在证券投资领域，我们一定不能忽略移动平均本身的金融意义。它实际上是`win`个周期以内，持有该支证券的所有人的平均成本。从这个角度上看，我们就可以更好地理解均线对证券价格的支撑和压制作用，理解近期波动对持有人心理上的影响，从而预测未来的方向。

以单一均线指标构建的交易策略有单均线策略和双均线策略。单均线策略是指在当股价上穿均线时买入，下穿均线时卖出的策略。双均线策略通过由两根一快一慢的均线构成，当快均线上穿慢均线时买入，反之则卖出。

双均线策略是许多回测平台中必备的示例策略。由于均线有一定的滞后性，双均线策略多数情况下表现都一般。

### 3.3. 布林带
布林带属于波动类指标，它度量股价波动的可能程度，而不是预测方向。
```python
import talib as ta
from talib import MA_Type

close = np.arange(20).astype(np.float64)
ub, mb, lb = ta.BBANDS(close, timeperiod = 5, matype=MA_Type.SMA)
ub, mb, lb
```
我们使用ta.BBANDS来计算布林带。它的第一个输入是收盘价，第二个参数 timeperiod 是中轨的周期，第三个参数用以指明如何计算均线。返回结果为一个三元组，依次是上轨、中轨和下轨。

根据布林带的定义，中轨就是某个长度的均线，所以 timeperiod 参数的含义。在代码中，我们中轨使用的是5日均线。

上下轨的计算是在中轨的基础上，加上（或者减去）n个标准差。这个系数可以通过nbdevup和nbdevdn来指定。

matype是均线计算方式，默认使用简单移动平均，传入值为0。文档没有说明其它几种移动平均对应的传入值是多少，不过它提供了一个枚举类型，从中我们可以看出一些端倪。

```python
import talib as ta
from talib import MA_Type

for item in dir(MA_Type):
    if item.startswith("_"):
        continue
    
    print(item)
```
这段代码显示了MA_Type中包含的均线算法类型
MA_Type也几乎是talib中定义的唯一一个枚举性质的变量

现在我们运行下计算布林带的代码。大家可以分析下结果，中轨是否就是收盘价的n日移动平均线。
### 3.4. MACD
MACD由 Gerald Appel创立于60年代，Appel是技术分析领域的传奇人物。他提供的MACD曾经是实战中最流行的技术指标之一，在国内也是如此。

MACD是Moving Average Convergence Divergence的首字母简写。MACD的计算方法共有三步：

1. 先通过短、长窗口（12日/26日）下的收盘价指数移动平均求出差离值：
    *DIF = EMA<sub>(close,12)</sub> - EMA<sub>(close,26)</sub>*
2. 信号线DEA，是DIF的9日指数移动平均：
   *DEA = EMA(DIF, 9)*
3. 将DIF与DEM的差画成柱状图：
   *MACD = DIF - DEA*

这里变量定义使用了国内券商常用的取值，注意与wiki有所不同。

![](https://images.jieyu.ai/images/2023/06/macd.png)

在上图中，柱状图是MACD，当它大于零时，市场走势较强，反之则走势较弱。当柱状图由正转负时，也正好就是所谓快线（DEA）下穿慢线（DIF）之时；反之亦然。

下面我们看看如何实现MACD：
```python
# using numpy
import numpy as np
from omicron.talib import exp_moving_average

def moving_average_convergence_divergence(close, signal = 9, nshort = 12, nlong = 26):
    dif = exp_moving_average(close, nshort) - exp_moving_average(close, nlong)

    dea = exp_moving_average(dif, signal)
    return array_math_round(dif - dea, 3)

bars = await Stock.get_bars("000001.XSHE", 40, FrameType.DAY)
moving_average_convergence_divergence(bars["close"])
```
在talib中我们调用MACD方法，它返回一个三元组：
```python
# using talib
import talib as ta

fastperiod, slowperiod, signalperiod = 12, 26, 9
close = bars["close"].astype(np.float64)
macd, signal, _ = ta.MACD(close, fastperiod, slowperiod, signalperiod)
```
在实际运用中，单个MACD的效果并不好，这可能应该归咎于因子拥挤效应。
### 3.5. RSI
RSI是Welles Wilder于1978年提出的另一个技术指标，发表在《Commodities》（现为《 Futures》）杂志上。

RSI是一个振荡指标，它在0到100之间摆动，这个特别适合机器学习。它的计算公式如下：

$$
    RSI = \frac{EMA_{(U,n)}}{EMA_{(U,n)} + EMA_{(D,n)}} * 100%
$$

它虽然称作是相对强弱指标，但实际上也反映了标的在区间内的赚钱效应，与**sortino指标**有一定的相似/关性。当区间内价格全部为下跌时，RSI与sortino一样，取值全为零；但当区间内价格全部为上涨时，RSI会取值100，但**sortino则变为正无穷**。

在talib中，我们这样计算RSI：
```python
import talib as ta
import numpy as np

close = np.random.random(10)
rsi = ta.RSI(close, n = 6)
rsi
```
talib返回的rsi值是一个介于0到100之间的浮点数。在ta.RSI中，缺省的时间窗口是14，不过国内券商在快线RSI上一般使用的是6个周期。所以，如果我们只使用单个RSI（即无须象行情软件一样绘制快慢线图）时，应该也使用6个周期，这样才会与散户保持一致（**得散户者得天下**）。

一般认为，作为一个震荡指标，应该把RSI大于70当成超买（即此时应该卖出），小于30当成超卖（即此时应该买入）。这些都是来自于早期公式刚发表不久之后的形成的刻板之见，在当时还比较有效，但现在有效性存疑。在量化交易中，我们应该使用“智能”RSI：

1. 不同的标的，同一标的不同时期，其投资者对亏损和盈利的忍受程度是不一样的
2. 基于第一点，不同的标的，它的RSI的高低水位不一样
3. 基于第一点，同一标的，它的RSI的高低水位也不一样
4. 同样的RSI极大值，可以对应不同的证券价格，对RSI极小值也是如此。
5. 上一个波峰时RSI打到的高水位（极大值），很可能是下一次RSI的高水位
6. 上一个谷底时RSI打到的低水位（极小值），很可能是下一次RSI的低水位。

比如，下图显示了RSI底部与股价走势关系图：

![](https://images.jieyu.ai/images/2023/06/rsi_bottom.png)

如果我们仅以30为标准，那么会出现比较多的失效情况；如果我们以RSI触及前极小值的情况来看，仍只有3/7的胜率；而如果在上述条件下，再加上二次触及前低的条件，则胜率几乎为100%。注意这是在一个比较漫长的下跌通道中捕捉到的机会，因此它的信号质量还是很不错的。

### 3.6. OBV （on-balance volume)
前述所有指标都只考虑了价格因素，但是，单纯的价格是容易被操纵的，有时会给出虚假的信号。技术分析师Joseph Granville于1963年，从成交量的角度入手，指出了推动价格变动的另一个维度。

他把OBV比作被紧紧缠住的弹簧，如果成交量发生了剧烈的变化，而价格在短期内没有反应这种变化，最终也必然发生相应的调整，就象弹簧一样。

OBV是一个提前指标（对应于滞后指标），某种程度上反应了大众投资者的情绪。同时它完全脱离了价格维度，因此如果我们把它用作一个因子，它将是与其它因子正交的，这也是我们推荐它的一个原因。

根据定义计算OBV比较简单，下面的代码显示了一个进行了归一化的OBV：
```python
def onbalance_volume(bars: BarsArray)->np.array:
    close = bars["close"]
    volume = bars["volume"]

    signs = np.sign(np.diff(close))
 
    # OBV[0]值的计算依赖于前一日的收盘价，按talib，这里也取vol[0]
    obv = np.concatenate(([volume[0]], volume[1:] * signs ))
    return obv / volume.sum()
```
在talib中，我们使用OBV函数：
```python
import talib as ta

close = bars["close"].astype(np.float64)
volume = bars["volume"].astype(np.float64)

ta.OBV(close, volume)
```
如果在我们自己的版本中不进行归一化处理的话，二者结果是一致的。

这里就不给出类似传统教科书里那些OBV的指标用法了。我的建议是，把它当成另外一个因子，然后通过机器学习来使用它。

## 4. 模式识别函数
talib的另一个重要功能，是提供了模式识别函数。在talib提供的60种模式中，我们选择有效性居前的几种进行介绍。这个排序来自于Thomas Bulkowski，综合了incrediblecharts.com的结果。前者是一名金融分析师，著有多本技术分析方面的书，包括《图表模式百科全书》等10余种书籍。

需要指出的是，talib的模式识别通常只涵盖了二到三根k线，而一些更重要的pattern，比如圆弧底、cup with handle、BARR等是无法包括的，但这些pattern可能确定性更强，在趋势判断上更为重要。

下面我们分别以一阳穿三线和红三兵为例进行演示，其它几种模式我们简单提一下，感兴趣的读者请参考链接的文档。

### 4.1. CDL3LINESTRIKE
这一模式俗称一阳穿三阴，它的反模式是一阴吞三阳。后者的确定性更强一些。

![](https://images.jieyu.ai/images/2023/06/three-line-strike.png)

根据Bulkowski，下跌途中的一阳穿三阴有84%的概率实现反转，但这一模式出现的概率并不高。而一阴吞三阳，则被认为是更强的返回模式。

下面的代码演示了在平安银行过去2000个交易中进行搜索的情况。结果表明，上述pattern只出现了3次，但反转模式只成功了一次。不过在2017年9月4日及2021年7月15日时的情况，是比较容易排除的。

```python
code = "000001.XSHE"

bars = await Stock.get_bars(code, 2000, FrameType.DAY)


async def find_3lines_strike(bars):
    opn, h, l, c = bars["open"].astype(np.float64), bars["high"].astype(np.float64), bars["low"].astype(np.float64), bars["close"].astype(np.float64)
    scores = ta.CDL3LINESTRIKE(opn, h, l, c)
    
    return bars["frame"][np.argwhere(scores != 0)]
                         
for frame in await find_3lines_strike(bars):
    bars = await Stock.get_bars(code, 120, FrameType.DAY, end=tf.day_shift(frame.item(), 10))
    cs = Candlestick(bars)
    i = np.argwhere(bars["frame"] == frame).flatten()[0]
    cs.add_marks([i], [bars[i]["high"].item() * 1.05], name=str(frame.item().date()))
    cs.plot()
```
读者可以自行更换其它代码进行验证。

这里我们看一下talib在模式识别上，相关函数的用法。talib中以CDL开头的函数，返回值一般为数组，值域为[-100, 0, 100]。如果没能识别出模式，则取值为0，正数表明看涨模式，越大越确定；负数表明看跌模式，越小越确定。注意，这里的确定是指给出的k线属于某种模式的确定性，而不是涨跌的确定性。

### 4.2. CDL3WHITESOLDIERS
下面的代码演示了talib在平安银行这支
```python
code = "000001.XSHE"

async def find_3white_soldiers(bars):
    opn, h, l, c = bars["open"].astype(np.float64), bars["high"].astype(np.float64), bars["low"].astype(np.float64), bars["close"].astype(np.float64)
    scores = ta.CDL3WHITESOLDIERS(opn, h, l, c)
    
    idx = np.argwhere(scores != 0)
    return zip(bars["frame"][idx], scores[idx])

bars = await Stock.get_bars(code, 2000, FrameType.DAY)

for frame, score in await find_3white_soldiers(bars):
    print(frame, score)
    bars = await Stock.get_bars(code, 120, FrameType.DAY, end=tf.day_shift(frame.item(), 10))
    cs = Candlestick(bars)
    cs.add_marks([108], [bars[108]["high"].item() * 1.05], name=str(frame.item().date()))
    cs.plot()
```


其它几种模式我们列举在下面，就不一一演示了：

* 三支乌鸦( Three black crows, CDL3BLACKCROWS)，出现在高位时，为看跌形态；途中为下跌中继。
* 黄昏之星(Evening start, CDLEVENINGSTAR,或CDLEVENINGDOJISTAR，即带十字星的)，出现在高位时，为看跌形态。反转模式为早晨之星，如果出现在低位，为看涨形态。
* Abandoned Baby(CDLABANDONEDBABY)。低位为看涨形态，高位其反转模式为看跌形态。
* 三个白武士（底部红三兵， three white soldiers，CDL3WHITESOLDIERS)，上升形态。出现在底部为反转形态，途中为上涨中继。

进一步阅读可以参考[incrediblecharts.com](https://www.incrediblecharts.com/candlestick_patterns/candlestick-patterns-strongest.php)和[Top 10 Performing candlesticks](https://thepatternsite.com/CandlePerformers.html)

注意，尽管talib的pattern无法涵盖象圆弧顶、圆弧底这样需要较多个bar的模式，但是对于部分模式，比如圆弧底，我们可以通过重采样的方式，在更高一级的周期上来检测。下面的例子显示了周线级别上的早晨之星（带十字），很可能在日线级别上就是圆弧底（或者W底）。

出于演示的目的，这次我们使用talib所谓的抽象接口来完成这个方法：
```python
async def find_pattern(pat: str, code: str, n: int):
    bars = await Stock.get_bars(code, n, FrameType.WEEK)

    
    fn = getattr(talib, pat)
    
    opn, h, l, c = bars["open"].astype(np.float64), bars["high"].astype(np.float64), bars["low"].astype(np.float64), bars["close"].astype(np.float64)
    
    scores = fn(opn, h, l, c)
    return bars["frame"][np.argwhere(scores > 0)]

code = "002195.XSHE"
frames = await find_pattern("CDLMORNINGDOJISTAR", code, 100)
for frame in frames:
    name = await Security.alias(code)
    bars = await Stock.get_bars(code, 120, FrameType.DAY, end=tf.day_shift(frame.item(), 10))
    cs = Candlestick(bars, title = name)
    n = len(bars) - 11
    cs.add_marks([n], [bars[n]["high"] * 1.05], name= 'red soldiers')
    cs.plot()
```
我们在周线上检测到了带下影的早晨之星，通过日线来看，它确实呈现圆弧底的形态：
![](https://images.jieyu.ai/images/2023/06/round_bottom.png)

在3-lines strike那个pattern中，我们找到了平安银行在2021年4月19日的一阳吞三阴，这是一个成功的反转例子，如果你在30分钟级别来查看它的k线图，它也是一个圆弧底。反之，在2021年7月15日，我们也检测到了一阳吞三阴，但是从30分钟来看，它的圆弧底比较平，而且后面几期已经出现向下拐头。所以，在更高层级的周期上相似的图形，也许在微观结构上，正在孕育完全不同的趋势，并最终在宏观上体现出来。这就是在证券投资领域常见的蝴蝶效应。
