---
title: Numpy 和 Pandas
---

## 1. Numpy
NumPy 是 Python 语言的一个扩展程序库。支持高阶大规模的多维数组与矩阵运算，此外也针对数组运算提供大量的数学函数库。

NumPy 的核心功能是 ndarray（即 n-dimensional array，多维数组）数据结构。这是一个表示多维度、同质并且固定大小的数组对象。而由一个与此数组相关系的资料类型对象来描述其数组元素的资料格式（例如其字符组顺序、在存储器中占用的字符组数量、整数或者浮点数等等）。 

ndarray 只能存放同质的数组对象，这样使得它无法表达记录类型的数据。因此，numpy 又拓展了名为 structured array 的数据结构。它用一个 void 类型的元组来表示一条记录，从而使得 numpy 也可以用来表达记录型的数据。

Numpy 的局限性在于，它必须把所有的数据都加载到内存中才能进行运算，也不支持分布式；它只支持 CPU 运算，无法利用 GPU 和 TPU 提供的超强运算能力。

### 1.1. 创建数组

```python
import numpy as np
from termcolor import colored
from typing import Any, Optional

def cprint(formatter: str, *args):
    colorful = [colored(f"{item}", 'red') for item in args]
    print(formatter.format(*colorful))
```
#### 1.1.1. vanilla version
我们通过`np.array`的语法来创建一个简单的数组：

```python
cprint("create a simple numpy array: {}", np.array([1, 2, 3]))
```

#### 1.1.2. 预置特殊数组
Numpy 中预置了一些特殊类型的数组，比如：
* zeros: 创建全 0 的数组
* ones: 创建全 1 的数组
* eys: 创建单位矩阵
* full: 创建一个所有元素都填充为`n`的数组
* empty: 创建一个空数组
* random.random: 创建一个随机数组
* arange: 创建一个递增数组
* linspace: 创建一个线性增长数组。与 arange 的区别在于，此方法默认生成全闭区间数组。

```python
# 创建特殊类型的数组
cprint("creating an array with zeros only: {}", np.zeros(3))
cprint("creating an array with ones only:\n{}", np.ones((2, 3)))
cprint("creating an Eye matrix:\n{}", np.eye(3))
cprint("creating an array with full: {}", np.full((3,2), 5))

cprint("creating an empty array:\n{}", np.empty((2, 3, 4)))
cprint("creating an random array:\n{}",np.random.random(10))
cprint("creating an sequence array:\n{}", np.arange(10))
cprint("creating an linspace array:\n{}", np.linspace(0, 2, 9))
```
#### 1.1.3. 通过已有数组转换

我们还可以从已有的数组中，通过复制、切片、重复等方法，创建新的数组
```python
# 从既存数组中创建
cprint("creating from np.copy: {}", np.copy(np.arange(5)))
cprint("creating from arr.copy: {}", np.arange(5).copy())
cprint("creating from slicing: {}", np.arange(5)[:2])
cprint("creating from concatenate: {}", np.concatenate((np.arange(3), np.arange(2))))
cprint("creating from repeat: {}", np.repeat(np.arange(3), 2))
cprint("creating from tile: {}", np.tile(np.arange(3), 2))
```
注意 axis 对 concatenate 的作用：

```python
arr = np.arange(6).reshape((3,2))

# 在 ROW 方向上拼接，相当于增加行
cprint("concate two arrays on axis 0:\n{}", np.concatenate((arr, arr), axis=0))
# 在 COL 方向上拼接，相当于扩展列
cprint("concate two arrays on axis 1:\n{}", np.concatenate((arr, arr), axis=1))
```

### 1.2. 查看 (inspecting) 数组特性
了解 numpy 的 dtype 类型，shape、ndim、size 和 len 的用法。

ndim 告诉我们数组的维度。shape 告诉我们每个维度的 size 是多少。shape 本身的 size，也等于 ndim。

size 在不带参数时，返回的是 shape 各元素取值的乘积。len 返回的是第一维的长度。

```python

arr = np.ones((3,2))
cprint("dtype is: {}", arr.dtype)
cprint("shape is: {}", arr.shape)
cprint("ndim is: {}", arr.ndim)
cprint("size is: {}", arr.size)
cprint("'len' is also available: {}", len(arr))

# DTYPE
dt = np.dtype('>i4')
cprint("byteorder is: {}", dt.byteorder)
cprint("name of the type is: {}", dt.name)
cprint('is ">i4" a np.int32?: {}', dt.type is np.int32)

# 复杂的 DTYPE
complex = np.dtype([('name', 'U8'), ('score', 'f4')])
arr = np.array([('Aaron', 85), ('Zoe', 90)], dtype=complex)
cprint("A structured Array: {}", arr)
cprint("Dtype of structured array: {}", arr.dtype)
```

### 1.3. 数组操作
介绍引起数组形状、size 等改变的相关操作

#### 1.3.1. 升维
我们可以通过 reshape, hstack, vstack 来改变数组的维度：
```python
# RESHAPE
cprint("increase ndim with reshape:\n{}", np.arange(6).reshape((3,2)))
# 将两个一维数组，堆叠为 2*3 的二维数组
cprint("createing from stack: {}", np.vstack((np.arange(3), np.arange(4,7))))
# 将两个 （3，1）数组，堆叠为（3，2）数组
# 注意如果是 (3, ) 数组，则相当于 CONCATENATE
np.hstack((np.array([[1],[2],[3]]), np.array([[4], [5], [6]])))
```

#### 1.3.2. 降维
通过 ravel, flatten, reshape, *split 等操作对数组进行降维。
ravel 的行为与 flatten 类似，只不过 ravel 是 np 的一个函数，可作用于 ArrayLike 的数组。

```python
# RAVEL
cprint("ravel: {}", arr.ravel())

# FLATTEN
cprint("flatten: {}", arr.flatten())

# RESHAPE
cprint("flatten by reshape: {}", arr.reshape(-1,))

# HSPLIT, VSPLIT
cprint("split:\n{}", np.hsplit(np.arange(6).reshape((3, 2)), 2))

# DIFFERENCE BETWEEN RAVEL AND FLATTEN: RAVEL CAN OP ON PYTHON LIST
np.ravel([[1,2,3],[4, 5, 6]])
```
#### 1.3.3. 转置
```python
cprint("transposing array from \n{} to \n{}", 
    np.arange(6).reshape((2,3)),
    np.arange(6).reshape((3,2)))
```

#### 1.3.4. 增加/删除元素
* append 将`values`添加到`arr`的末尾。
* insert 向指定位置`obj`（可以是下标、slicing）插入数值`value`（可以是标量，也可以是数组）
* delete 删除指定下标处的元素

注意 axis 的取值的影响。

```python
arr = np.arange(6).reshape((3,2))
cprint("append will yield 1-dim array:\n{}", np.append(arr, [[7,8]], axis=0))

cprint("without axis, the array is flattened: {}", np.insert(arr.reshape((3,2)), 1, -10))
cprint("np.insert:\n{}", np.insert(arr.reshape((3,2)), 1, (-10, -10), axis=0))

cprint("deleting col 1:\n{}", np.delete(arr, [1], axis=1))
```

### 1.4. 逻辑运算和比较
* all
* any
* **isfinite 是否为数字且不为无限大**
* isinf 测试无限大
* **isnan 测试非数字类型**
* **isnat 测试非时间类型**
* isneginf 是否为负无限大
* isposinf 是否为正无限大

* logical_and 即'&'
* logical_or 即'|'
* logical_not 即'~'
* logical_xor 即'^'

* **allclose(a, b, tolerance) 两个序列是否相等，如果都相等，返回 True**
* isclose(a, b, tolerance)  同上测试，返回 bool 序列
* equal
* not_equal

这里对`allclose`和`isclose`我们单独介绍一下。我们使用`isclose`来举例，同样的原理也会作用于 allclose 这个方法。
```python
# 使用固定的数字进行随机数初始化，是为了保证再次运行时
# 能得到同样的结果
np.random.seed(78)
arr = np.random.random(10)

np.isclose(arr, arr + 1e-6, 1e-5)
```

### 1.5. 集合运算

* unique(arr)   返回 arr 中惟一的元素
* in1d(a1, a2)  a1 中的元素是否都在 a2 中存在？比如，在调仓换股中，如果当前持仓都在买入计划中，则不需要执行调仓
* intersect1d   两个数组的交集
* setdiff1d(a1, a2) 相当于 set(a1) - set(a2)，留下只在 a1 中存在的元素
* setxor1d(a1, a2)  相当于 set(a1) + set(a2) - intersect1d(a1, a2)

### 1.6. 数学运算
numpy 重载了常见的数学运算符号，因此我们可以将加、减、乘、除、平方、开方等方法运用于单个或者两个数组，或者运用于一个数组与一个标量之间。
```python
a = np.array((1, 2, 3))
b = np.array((1.5, 2, 3))

## 如果 SHAPE OF A, B， 或者其中之一为标量，那么可以进行 +-*/运算
cprint("subtraction: {}", a - b)

```

#### 1.6.1. 点乘

矩阵的乘法在深度学习中使用多一些，在我们这门课中不是重点。

```python

# 点乘 （矩阵乘），一维数组之间的点乘，结果为标量；其它满足条件才能点乘，结果为矩阵
cprint("dot multiply yields: {}", a.dot(b))

# 星乘（ELEMENT WISE MULTIPLY） -> A * B，SHAPE 不一样也可以乘
cprint("element wise multiply: {}", np.multiply(a, b))

# 叉乘 （略）
```

#### 1.6.2. 聚合运算和统计函数
关于聚合运算，特别是当其中可能含有 np.NaN 时，推荐使用 bottleneck，速度更快

* np.max
* np.min
* np.sum
* np.mean
* np.median
* np.quantile 计算序列中的第`q`个分位数

```python
arr = np.arange(10)

cprint("0.25, 0.5, 0.75 分位数分别为：{} {} {}", 
       np.quantile(arr, 0.25), 
       np.quantile(arr, 0.5), 
       np.quantile(arr, 0.75)
)
```

### 1.7. 读取、查找和搜索
#### 1.7.1. 索引和切片

```python
arr = np.arange(6).reshape((3,2))
cprint("original array:\n{}", arr)

# SLICING
cprint("slicing by axis 0: {}", arr[1, :])
cprint("slicing by axis 1: {}", arr[:, -1])
cprint("reversing array:\n {}", arr[: : -1])

# FANCY INDEXING
cprint("fancy indexing, using pos array as index:\n {}", arr[[2, 1, 0]])

```

#### 1.7.2. 查找、替换、筛选

searchsorted, where, nan_to_num, np.max, np.min

和他们的 arg *版本：

np.argwere, np.argmin, np.argmax

```python

# 查找
pos = np.searchsorted([0, 2, 2, 2, 3], 2, 'right')
cprint("searching 2 in arr {} got index of {}, value is {}", arr, pos, arr[pos])

arr = np.arange(6) * 2

# 替换：实现单侧 CLIP 效果
cprint("np.where: {}", np.where(arr > 3, 3, arr))

# 将 NAN, INF 替换成相应的数字
arr = np.array([2, np.inf, np.nan])
cprint("nan_to_num: {}", np.nan_to_num(arr, nan = 0, posinf=99))

# 筛选
arr = np.arange(6)
np.select([arr > 3], [arr], default = 2)
```

### 1.8. 类型转换和 typing module

```python
arr = np.arange(3)
arr.astype(np.float64)

import time

# EPOCH TIME TO DATETIME64[MS]
arr = np.array([time.time() + i for i in range(4)])
cprint("check element's type: {}", type(arr[0]))

cprint("convert to datetime: {}", arr.astype('datetime64[ms]'))
cprint("convert arr[0] to float32: {}", type(arr[0].astype(np.float32)))

# 转换为 PYTHON OBJECT
cprint("convert to python float: {}", type(arr[0].item()))
cprint("convert to python datetime: {}", type(arr.astype('datetime64[ms]')[0].item()))

# TYPING
from numpy.typing import ArrayLike, NDArray

```

### 1.9. Structured Array
Structured array 使得 numpy 数组有了记录的概念，从而我们可以象访问表格一样访问数据。
可以如下定义 structured array：

```python
dtype=[("name", "O"), ("weight", "i4")]

arr = np.array([("lion", 500), 
                ("bear",  750), 
                ("leopard", 350)], 
               dtype=dtype)

cprint("example of structured array: \n{}", arr)
cprint("dim of structured array: {}", arr.ndim)
cprint("len of structured array: {}", len(arr))
cprint("size of structured array: {}", np.size(arr))
cprint("type of element: {}", type(arr[0]))
```

要注意 Structured array 是一维数组，而不是二维数组，但它可以象二维数组一样访问。
作为一维数组，它的每个元素的类型为 void， 实际上是一个 Tuple，因此我们在构造时，也是传入 List[Tuple]，而不是 List[List]。比如，下面的方法会引起错误：

```python
data = [["aaron", 100], ["annie", 80]]
dtypes = [("name", "U8"), ("score", "f4")]
np.array(data, dtype=dtypes)
```

使用 List[List]，而不是 List[Tuple] 来初始化 Structured array，这是初学者比较容易犯的一个错误。

我们可以通过以下方法来访问数据：

```python
cprint("row: {}", arr[0]) # ('lion', 500)
cprint("row+col: {}", arr[0][0]) # 'lion'
cprint("row + col: {}", arr[0]["name"])# 'lion'
cprint("row + col: {}", arr[-1]["name"]) #'leopard'

# BROADCASTING OPERATION, 该列的每一个值都将被+5
cprint("operate on column: {}", arr["weight"] + 5)

# SELECT SEVERAL COLUMNS
cprint("subset: {}", arr[["name", "weight"]]) # 选择其中几列
```

### 1.10. IO
numpy 可以用来读写 csv。但是，推荐使用 pandas 或者 pyarrow 来读取 csv 文件。后二都速度更快。
我们通过下面的代码实现读文件和存取文件：
```python
# 保存数组为.NPY 文件
np.save("new_arrray", arr)

# 从 NPY 文件中加载
np.load("new_array.npy")

# 读取 CSV 文件
np.genfromtxt("data.csv", delimiter=',')
```

### 1.11. 量化交易中常用函数示例

我们以行情软件中，常用的数组操作相关的函数为例，介绍一下这些函数如何通过 numpy 来实现。
#### 1.11.1. REF(close, n)
```python
def ref(ts: np.ndarray, n: int):
    return ts[-n]
```
#### 1.11.2. EVERY(cond, n)
```python
def every(cond_list, n:int):
    return np.all(cond_list[-n:])
```

#### 1.11.3. LAST(cond_list, n, m)
判断是否从前 n 日起到前 m 日都一直满足条件
```python
def last(cond_list, n, m):
    return np.all(cond_list[-n:-m])
```

#### 1.11.4. BARSLAST
判断从上一次条件成立到当前的周期

```python
def bars_last(condlist):
    pos = np.argwhere(condlist).flatten()
    if len(pos) >= 1:
        return len(condlist) - pos[-1] - 1
    else:
        return None
    
bars_last(np.array([0,1,0,1,0,0,0,0]))
```

#### 1.11.5. CROSS
判断序列 f 是否金叉 g，是许多策略的买点信号的成立条件

```python
def cross(f: np.ndarray, g: np.ndarray):
    return np.concatenate(([False], ~((f>g)[:-1]) & (f>g)[1:]))  

f = np.array([1, 2, 3, 4])
g = np.array([2, 2, 2, 2])

cross(f, g)
```

## 2. Pandas

### 2.1. creation
1. 通过 dict[str, List] 来创建，其中 key 为 column 名字，value 为对应 column 的值。
```python
df = pd.DataFrame({
    "a": [1,2,3],
    "b": [4, 5, 6]
})
```
这种方法，我们在前面的课程中已经使用过了。
也可以是通过二维数组来创建，自行指定 column：
```python
df = pd.DataFrame([
    [1, 2, 3],
    [4, 5, 6]
], columns = ['a', 'b', 'c'])
```
### 2.2. 数据访问
在 pandas.DataFrame 中访问和定位数据与 numpy 不一样。pandas 提供了 iloc 和 loc,at 和 iat 这样的方法来访问数据。

```python
# 选择第 1、第 2 行
cprint("select second row: \n{}", df.iloc[1:2])
cprint("select all rows, but 1, 2 cols only:\n{}", df.iloc[:,[1,2]])
cprint("loc can use column label:\n{}", df.loc[:, ['b','c']])
```
at 和 iat 是 loc 和 iloc 的快速版本。前二者返回的是标量，后二者返回的是向量。使用哪一类方法，取决于我们接下来要进行什么操作。
```python
# 这里必须使用 ROW ID(INT) 和标签 (STR)
cprint('access cell by at: {}', df.at[1, 'b'])

# 此时必须使用全整数下标
cprint('access cell by iat: {}', df.iat[1, 0])

# 可以给单元格赋值
df.iat[1, 0] = 99
cprint('after write, the dataframe is:\n{}', df)
```
### 2.3. 遍历 dataframe

遍历 numpy 数组，无论是普通的 ndarray，还是 structured array，都比较直观，但遍历 DataFrame 时，它提供了多种方法：

1. iterrows
2. apply
3. itertuples
4. for i in range(len(df))
5. for row in df.to_numpy()

在选择这些方法时，我们优先选择在 numpy 数组域进行操作，其次是`apply`方法，然后是对 df 按 index 进行循环，一般我们要用 iat/at，而不用 iloc/loc，这样速度会快很多。最后，也可以考虑 iterrows/itertuples 方法。

下图取自知乎博主 Enrique Juan 的一个测试，反映了该测试场景下，各种迭代方法的速度对比：

![](https://images.jieyu.ai/images/2023/05/speed_test_iter_dataframe.png)

## 3. pandas vs numpy

pandas 的底层数据结构是 numpy，pandas 在此基础上，增加了索引和其它数字结构（比如 category)。绝大多数 numpy 中存在的操作，都能在 pandas 中找到。

对于小体量的数据处理，比如 1000 条记录以下的，一般使用 numpy 会更快。但如果体量大于 100 万条记录，pandas 则会明显胜出。

pandas 的功能相较 numpy 而言，会更丰富。比如，它提供了 group（分组查询）功能，join（联接）、merge 等功能，plotting 等。

numpy 中没有类似于 pandas.rolling 的方法，这需要通过扩展库中的`np.lib.stride_tricks.as_stride`方法来间接实现。当然，对一些常规的滑动窗口运算，比如`average`, `sum`等我们也可以借用`bottleneck`库中的`move_*`方法来实现。
