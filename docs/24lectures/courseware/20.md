---
title: backtrader 回测框架（2）
---

![](https://images.jieyu.ai/images/2023/08/lesson20-backtrader-outline-2.png)

## 1. Cerebro

Cerebro 是 backtrader 的基石和中心，它的主要工作是：

1. 组织数据流、策略、观察者、指标分析和日志记录各模块协同工作
2. 执行回测/实盘交易
3. 返回回测结果
4. 提供绘图接口

我们已经见过了好几个示例。但这一次，我们将介绍一个更全面的 cerebro 的示例，作为对 cerebro 功能的回顾与拓展。

```python
# 示例 1
%matplotlib inline
import datetime

import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind
import pandas as pd
from backtrader.analyzers import TimeReturn
from backtrader.strategies import MA_CrossOver
from coursea import *

await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")

cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)

cerebro.addstrategy(MA_CrossOver)

cerebro.addwriter(bt.WriterFile, rounding=2, csv=True, out="...")

cerebro.addanalyzer(
    TimeReturn, timeframe=bt.TimeFrame.Years, data=benchmark, _name="benchmark"
)

cerebro.addanalyzer(
    bt.analyzers.TimeReturn, timeframe=bt.TimeFrame.Years, _name="target", data=target
)

cerebro.addobserver(bt.observers.Benchmark, data=benchmark)

cerebro.broker.set_cash(1_000_000)
print("the cash is:", cerebro.broker.get_cash())

cerebro.broker.setcommission(1.5e-3)

cerebro.broker.set_slippage_perc(0.01)
cerebro.run()
cerebro.plot(iplot=False)
```

在上面的代码中，我们先是创建了一个 cerebro 对象，接着我们增加数据流：

```python
target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

benchmark = await get_sample_feed("000001.XSHG")
cerebro.adddata(benchmark)
```

cerebro 可以接受多个数据流，不同的周期、不同的标的。我们通过加入 cerebro 的顺序，或者使用 name 来区分这些数据流。如果要使用名字来区分它们，可以在 adddata 时，增加一个 name 参数。这是我们前面介绍过的。

这里也可以增加 resampledata 或者 replaydata。

接下来我们增加策略 -- Strategy，为简单起见，我们使用了内置的 MA_CrossOver 策略。

```python
cerebro.addstrategy(MA_CrossOver)
```

!!! attention
    需要再次提醒，我们往 cerebro 中增加策略时，我们提供的是类，而不是它的实例。因为 cerebro 在进行参数优化时，需要通过类来生成多个实例。

到此为止，都是我们已经见过的玩法了。

### 1.1. 增加记录器（日志）

接下来，我们增加一个记录器，以便把 cerebro 的各项运作都记录下来，这需要使用 addwriter 方法。

```python
cerebro.addwriter(bt.WriterFile, rounding=2, csv=True, out="...")
```

addwriter 有一个必选参数，即 writer class，这里我们使用它内置的 Writer，即 bt.WriterFile，这也是 backtrader 提供的惟一一个 Writer。其它参数有：

1. out，可以省略。当它省略时，默认输出到控制台。如果提供该参数，则要求提供文件名，这样信息会写入到该文件中。
2. csv，可以省略，此时默认为 False，当它为 True 时，将输出非常丰富的信息。
3. rouding，用来对某些输出进行小数位限制

我们来检查一下输出结果。

![50%](https://images.jieyu.ai/images/2023/08/lesson20-example1-log.png)

输出内容相当多，这是因为我们开启了 csv=True。如果不使用这一选项，我们将只得到第 21 行（cerebro）以下的部分内容。这些内容主要是输出各个对象的构建参数，但会自动打印出分析器的输出结果。

接下来我们就介绍分析器：analyzer。analyzer 负责评估策略指标。在 backtrader 中，每个要查看的指标都必须手动添加，并不是很方便。

```python
cerebro.addanalyzer(
    TimeReturn, timeframe=bt.TimeFrame.Years, data=benchmark, _name="benchmark"
)

cerebro.addanalyzer(
    bt.analyzers.TimeReturn, timeframe=bt.TimeFrame.Years, _name="target", data=target
)
```

在示例中我们添加了一个收益指标， TimeReturn。

### 1.2. 增加观察者

接下来我们增加一个观察者。

```python
cerebro.addobserver(bt.observers.Benchmark, data=benchmark)
```

这里我们使用了一个名为 Benchmark 的观察者。观察者和分析器都可用作 benchmark，两者的区别是，observer 具有 line 的属性，它会记录每一个值，因此更适合绘图和实时查询。分析器则只会在 cerebro.run 结束后，才能通过 get_analysis 进行查询。

```python
cerebro.broker.set_cash(1_000_000)
print("the cash is:", cerebro.broker.get_cash())

cerebro.broker.setcommission(1.5e-3)
cerebro.broker.set_slippage_perc(0.01)
```
此外，我们还可以更换交易代理，或者设置本金、佣金等等，分别是通过 set_cash, setcommission, set_slippage_perc 来实现的。

顺便说一句，backtrader 在 API 风格上，在是否使用下划线来分割单词，并不统一，看上去它更倾向于不使用下划线，所以我们记忆时，可以只记少数加了下划线的函数。

### 1.3. 执行与绘图

接下来就可以执行回测了，仍然是执行 run 方法。

最后，我们通过绘图和指标分析来查看回测结果，以便进一步优化。

![75%](https://images.jieyu.ai/images/2023/08/lesson20-example-1.png)

前面介绍的示例中，我们都毫不例外地使用了 Strategy。但是，backtrader 允许一种更简化的操作，即不使用策略，而只使用信号。这种情况下，实际上是 backtrader 帮我们生成了一个策略。

```python
# 示例 2
class MySignal(bt.Indicator):
    lines = ("signal",)
    params = (("period", 30),)

    def __init__(self):
        self.lines.signal = self.data - bt.indicators.SMA(period=self.p.period)

cerebro = bt.Cerebro()

target = await get_sample_feed("600000.XSHG")
cerebro.adddata(target)

cerebro.add_signal(bt.SIGNAL_LONGSHORT, MySignal)
cerebro.run()
cerebro.plot(iplot=False)
```

这里的 signal 的语法是，如果数值大于 0，认为是做多信号；小于 0 是做空信号；等于 0 则不操作。在这些地方尤其要注意浮点数比较问题，建议先对收盘价及计算出来的 sma 都取整到小数点后两位，再进行比较，以避免两个数实际相等，但因浮点误差导致它们大于零、或者小于零，从而引起开平仓操作的情况。

第 16 行，我们添加一个信号时，需要指明信号对应的操作。示例中，我们使用的是 LONGSHORT, 其它可能的值还有 LONG, SHORT.
我们先看参数为 LONG 的情况。当参数为 LONG 时，如果 signal>0，则将开多仓；如果 signal < 0，则将平多仓。
如果参数为 SHORT，则刚好相反。
如果参数为 LONGSHORT，则 signal >0 时开多仓，signal < 0 时开空仓。当然我们这门课讲的是 A 股，所以一般我们用 LONG 作为参数就好了。

这里还有一种情况，就是可能会连续发生好几期的 signal > 0，或者 signal < 0 的情况，此时 backtrader 会将信号合并从而只处理一次。这也是我们自己写策略时，需要考虑的问题。

下图显示了回测结果绘图：

![75%](https://images.jieyu.ai/images/2023/08/lesson20-siganls-only.png)

## 2. Order

在 Backtrader 中，Cerebro 是关键的控制系统，Strategy（策略）是终端用户的关键控制点。Strategy 需要一种机制与系统的其他部分沟通，这种机制就是订单 (Order)。

订单翻译了 Strategy 逻辑中做出的决策，将其转化为 Broker 可以执行的动作形式。具体来说：

1. 创建订单，这是通过 strategy 中的 buy、sell 和 close 方法来实现的

2. 撤销订单，这是通过 cancell 来实现的

3. 同时，订单也作为与用户的沟通方式，通知 Broker 中的执行情况，这是通过 notify_order 来实现的

当我们在 Strategy 中调用 buy, sell 和 close 时，就会创建一个 Order。这里给出了 Buy 方法的签名，我们来解读一下它的参数。

* data: 订单关联的标的。默认为 self.data0。注意 bt 与标的关联时，它并不是使用的证券代码或者名字等更容易记忆，或者从内存占用上更小的对象，而是使用了 data feed。可能部分原因是，一是 bt 并不要求给 data feed 指定证券代码或者名字，从而在创建 Order 时，可能获取不到这个名字；二来 data feed 还包含了当前时间、价格等信息，这些信息最终在创建 Order 时使用了。感兴趣的同学可以看看 order.py 文件中的__init__方法。
* size: 订单委托数量。默认通过 getsizer 获取。
* price: 默认为市价；当指定时，将使用该价格进行委托。
* plimit: 仅用于在 StopLimit 单中指定限价
* exectype: 订单类型，默认为市价单，可选类型有：
    1. Order.Market 下一个 bar 的开盘价
    2. Order.Close 下一个 bar 的收盘价。注意在 A 股，只有日线及以上周期的收盘价，才是可以在交易中使用的（利用集合竞价机制），在其它情况下使用收盘价回测，其结果在实盘中将无法重现。
    3. Order.Limit 限价单
    4. Order.Stop 止损单，需要指定止损价格（Stop Price），一旦股价突破止损价格，将会以市价单的方式成交；
    5. Order.StopLimit 止损限价单，需要指定止损价格（Stop price）和限价（Limit Price），一旦股价达到设置的止损价格，将以限价单的方式下单
    6. Order.StopTrail 跟踪止损单
    7. Order.StopTrailLimit 跟踪止损限价单
关于订单类型，不同的交易系统有自己的规则，这些规则也可能随时改变。在进行 A 股回测时，一般只使用 Market、Limit 两种。
* valid: 订单有效期，只在实盘中有效，且取决于交易代理能否实现。一般不用。
* tradeid: 跟踪同一资产的多个交易，用在订单通知中。它是由用户自己在调用 buy 等方法时指定的。
* **kwargs： 生成特定订单时可能需要

此外，在生成订单时，系统还将为每个订单生成一个惟一的编号，被称为 ref。

每个订单还有创建时间，保存在属性 created 中。如果订单被执行，则属性 executed 中保存了 size 和 price 信息。

### 2.1. notify_order

当订单状态发生改变时（比如被提交、被执行等），就会发出一个通知。我们可以在 Strategy 中，通过重写 notify_order 方法，来接收这个通知。

notify_order 会在 next 方法之前被调用。

同一个 order，在同一个 next 周期中，被调用多次，每次可能具有不同、或者相同的状态，可能至少有三个状态会被通知，Submitted, Accepted, Completed，此外，在实盘中，还可能出现多次 Order.Partial 通知，即一个委托被多次部分成交。这在回测中不会出现，但在实盘中常常出现。这些概念，在其它系统中也是一样的。

在这些状态中，我们主要关心的有 Partial, Complete, Rejected 和 Cancelled。在我们的 Strategy 中，可能需要对这些状态进行相应的处理。Margin 状态会发生在现金不够的情况下。

```python
# 示例 5 ORDER STATUS TRANSMITTION
%matplotlib inline

import datetime
import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind

from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")

class TestStrategy(bt.Strategy):
    params = dict(
        pentry=0.015,
        plimits=0.03,
        valid=10,
    )

    def notify_order(self, order):
        dt, dn = self.datetime.date(), order.data._name
        created = bt.num2date(order.created.dt).date()
        size = order.size
        p1 = round(order.created.price, 2)
        p2 = round(order.executed.price, 2)
        
        print('{} {} Order {}: {}\t{}\t{}\t{}\t{}'.format(
            dt, dn, order.ref, order.getstatusname(), created, size, p1, p2)
        )

    def __init__(self):
        self.o = []
        self.holding = 0 # holding periods per data

    def next(self):
        print(f" === {len(self.data)}th day ===")

        dt, dn, d = self.datetime.date(), self.data._name, self.data
        pos = self.getposition(d).size

        if not pos and not self.o:  # no market / no orders
            if dt.weekday() == 3:
                self.o = [self.buy(data=d)]
                print('{} {} Buy {}'.format(dt, dn, self.o[0].ref))

                self.holding = 0

        elif pos:  # exiting can also happen after a number of days
            self.holding += 1
            if self.holding >= 2:
                o = self.close(data=d)
                self.o.append(o)  # manual order to list of orders
                print('{} {} Manual Close {}'.format(dt, dn, o.ref))

cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("600000.XSHG", 6)
cerebro.adddata(data, name="浦发银行")

results = cerebro.run()

cerebro.plot(iplot = False, volume=False)
```

这段代码中实现了一个非常简单的交易逻辑，在周三当天买入，持有 2 天后卖出。

我们通过 weekday 方法来判断今天是否为周三。研究表明，A 股存在节假日效应，所以有时候我们在调仓时，会根据 weekday 来决定是否调仓，比如在小市值策略中。

在__init__方法中，我们将 orders 保存在 self.o 对象中，将当前持仓时间保存在 self.holding 中

在 next 方法中，如果当前没有持仓，也没有 orders，则执行买入；如果有持仓且超过 2 天则卖出。在每个 next 周期，我们先打印当前周期序号，然后在 buy 和 sell 执行后，将这一动作打印出来

最后，我们在 notify_order 中，将 order 的一些属性输出出来。上述代码运行结果如下：

![75%](https://images.jieyu.ai/images/2023/08/lesson20-example5-log.png)

这里我们输出了当前日期，feed 名字，order 的编号--这是通过 order.ref 来指定的

order 有 created 和 executed 两个属性，都是 OrderData 类型。created 保存了创建 order 时的一些信息，在 order 生命期中都保持不变；executed 则保存了 order 执行之后的数据。

在示例中，我们还输出了创建时的价格和执行时的价格，以及创建时间。

从输出信息可以看出，每次 buy 和 sell 都会触发三次通知，此时 order 的状态分别从提交、接受转移为完成。只有在完成时，exectued 属性中的 price 数据才更新，它是实际执行的平均价格。

## 3. 交易代理

交易代理是继前面介绍过的概念之后，最重要的一个概念。在 Backtrader 中，无论是实盘交易还是模拟回测，都需要设置 Broker 对象来处理交易流程。

首先，策略的 buy 和 sell 操作会生成 Order（订单）对象。Order 对象中包含了订单的关键信息，如数量、类型、价格等。

然后，Order 对象会提交到 Broker 进行处理。Broker 根据订单类型、资金情况等来决定订单的成交执行。

一旦订单成交，Broker 就会更新内部的账户状态，如持仓、现金等。同时 Order 的状态也会更新为 Completed（完成）。

最后，策略可以通过 Broker 提供的接口来查询账户的实时状态，如持仓、资金等，以供策略调整使用。

所以简单来说，在 Backtrader 内部，一个完整的交易流程就是：策略生成 Order 订单 -> Broker 接收并处理订单 -> 反馈账户和 Order 状态 -> 策略调整。

Broker 作为中间层，扮演着执行交易的关键角色。

```python
# 示例 3
import backtrader as bt

cerebro = bt.Cerebro()  

class MyStrategy(bt.Strategy):
  def next(self):
    broker = self.broker  
    print("初始资金：", broker.getcash())
    broker.add_cash(50000)
    print("增加后资金：", broker.getcash())

broker = bt.brokers.BackBroker(cash=100_000)
cerebro.broker = broker

broker.set_cash(5_000_000)
print("初始资金：", broker.get_cash())

cerebro.broker.add_cash(-4_500_000)
print("strategy 外修改资金无效：", cerebro.broker.get_cash())

cerebro.addstrategy(MyStrategy)

data = await get_sample_feed("000001.XSHG", 3)
cerebro.adddata(data)

cerebro.run()
```

我们通过 broker.set_cash 来初始化资金。cerebro 默认的 broker，初始资金是 10 万单位。

在增加资金时，要注意在 strategy 类之外修改资金无效，在 next 方法中修改资金后，也需要等待下一次的 next，我们才能看到资金变化。这段代码输出结果如下：

```text
初始资金：5000000
strategy 外修改资金无效：5000000
初始资金：5000000.0
增加后资金：5000000.0
初始资金：5050000.0
增加后资金：5050000.0
初始资金：5100000.0
增加后资金：5100000.0
```

我们使用只有 1 个 bar 的数据流来完成这个测试，这将导致 next 只被调用一次，因此输出也只有 6 行。

### 3.1. 资产与持仓查询

获取持仓数据需要特别提示下，我们既可以通过 getposition 方法，也可以通过 getpositionbyname 方法来获取持仓，getposition 方法接受一个参数，这个参数是 data feed，第 7、8 行演示了它的用法。

```python
# 示例 4
class TestStrategy(bt.Strategy):
    def next(self):
        print('当前可用资金', self.broker.getcash())
        print('当前总资产', self.broker.getvalue())
        
        size = self.broker.getposition(self.data).size
        print('当前持仓量', size)
        
        price = self.getpositionbyname(self.data._name).price
        print('当前持仓成本', price)

        print('当前持仓', self.position.size)
        
cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("000001.XSHG", 1)
cerebro.adddata(data)

cerebro.run()
```

我们也可以按名字来查询持仓，不过这个名字是 data feed 的名字，并不是通常意义上的证券名，除非我们在加入 data feed 时，就使用证券的名字来命名这个 feed。

第 10、11 行演示了 getpositionbyname 的用法。

position 本身也是一个属性，当查询 datas[0] 对应的持仓时，我们也可以只用 self.position，第 13 行演示了它的用法。

注意上面的示例中，还演示了一个用法，即我们可以使用 getposition 和 getpositionbyname 方法。两个方法既在 self.broker 中存在，也在 strategy 中存在。

在第 7 行中，我们是通过 broker 来调用的，但在第 10 行中，我们是直接通过 strategy 来调用的。

我们在上一课还问过一个问题，为什么 self.position 能作为条件表达式？我们的猜测是它也进行了重载。

现在，我们就透过 backtrader 的源码，来揭示这里究竟是如何重载的：

```python
class Position(object):
    def __bool__(self):
        return bool(self.size != 0)

class MyStrategy(bt.Strategy):

    def next(self):
        if self.position:
            ...
```

通过重写过__bool__这个方法，于是我们就可以在第 7 行，直接使用 self.position 作为布尔表达式。现在，即使 self.position 不为 None，但实际持仓数为零，也不会误判为有持仓，因为在 self.position 对象存在时，它会检查其 size 是否为零，如果为零，仍然返回 False。

这个例子过于简单，也不产生任何有效的结果。下面，我们通过一个复杂的例子，来演示仓位的变化，以及仓位查询：

```python
# 示例 6
%matplotlib inline

import datetime
import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind
from tabulate import tabulate
from collections import defaultdict

from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")

class TestStrategy(bt.Strategy):
    params = dict(
        enter1 = [1, 2, 3],
        enter2 = [2],
        exit1 = 3,
        exit2 = 4,
        pentry=0.015,
        plimits=0.03,
        valid=10,
    )

    def notify_order(self, order):
        if order.status == order.Completed:
            dt, dn = self.datetime.date(), order.data._name

            positions = []
            for i, d in enumerate(self.datas):
                pos = self.getposition(d).size
                dn = d._name
                p1 = self.getposition(d).price
                
                if order.data == d:
                    p2 = order.executed.price
                else:
                    p2 = None
                    
                positions.append([dt, dn, pos, p1, p2])
            print(tabulate(positions, ["日期", "股票", "仓位", "均价", "订单"], "simple"))
            print("\n")

    def __init__(self):
        self.o = defaultdict(list)

    def next(self):
        print(f" === {len(self.data)}th day ===")
        for i, d in enumerate(self.datas):
            dt, dn = self.datetime.date(), d._name
            pos = self.getposition(d).size

            if i == 0:
                enter = self.p.enter1
                exit = self.p.exit1
            else:
                enter = self.p.enter2
                exit = self.p.exit2
                
            if len(d) in enter:
                self.o[d].append(self.buy(data=d))
                print('{} {} Buy {}'.format(dt, dn, self.o[d][-1].ref))
            
            if pos:
                for order in self.o[d]:
                    if self.datetime[0] - order.created.dt > exit:
                        o = self.sell(data=d, size=1)
                        self.o[d].append(o)  # manual order to list of orders
                        print('{} {} Manual Close {}'.format(dt, dn, o.ref))

cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("600000.XSHG", 9)
cerebro.adddata(data, name="浦发银行")

data = await get_sample_feed("000002.XSHE", 9)
cerebro.adddata(data, name="万科 A")
results = cerebro.run()

cerebro.plot(iplot = False, volume=False)
```

这个示例在示例 5 的基础上，增加了以下内容：
1. 使用了多标的股票池。由于是多个标的，所以我们通过 defaultdict 来分别管理每个标的的 order
2. 通过参数来决定何时交易，包括买入和卖出
3. 打印持仓
4. 在打印持仓时，我们使用了一个名为 tabulate 的库，用来以文本格式输出表格

我们在 notify_order 中，打印了每支标的当前的持仓。notify_order 是在 next 方法中，当 buy 或者 sell 方法被执行时，自动触发的。当然，触发 notify_order 的时机还有其它，这里我们只要知道，当一个买入、卖出动作完成时，持仓显然会发生变化，这也是我们打印持仓的最佳时机之一。

!!! tip
    上一节讲到 order 有好几种状态，每一个 next 中至少触发三种状态通知，这里我们只在 order 执行完成时进行打印，以避免重复。<br>
    这里我们通过对 self.datas 的遍历，这样才能取出每一个标的的仓位，我们也可以使用 self.positions 来获取所有仓位，不过它是一个 dict 数据结构，之后还是需要进行遍历。

在查询持仓时，我们使用的方法在上一张 slide 中已经介绍过了，这里只使用了 getposition 方法。

我们在这里查询了两个价格，一个是当前正在执行的 order 的价格。另一个是持仓的均价，通过两者的对比，我们可以看出新的成交加入后，对持仓均价的影响。

tabulate 方法（第 49 行）将一个多维数组格式化成为一个字符串，第一个参数是输入数据，第二个参数是表头，第三个参数是表的样式，这里使用 simple, 此外还有 grid, roundgrid 等，大家可以在 notebook 中自行尝试一下。

在 next 方法中，我们通过 len(d) 来判断当前是第几个交易日，如果这个数字在指定的 enter 日期集合中，则我们下一手买单，buy 方法会返回新建的 order，我们把它保存在 self.o 这个对象中。

如果某个标的当前有仓位，且持仓时间大于指定的退出日，我们就通过 sell 方法卖出一手。

我们通过订单的持有时间来判断是否应该退出。

!!! attention
    注意！self.datatime 与 order.created.dt 相减的结果是相差的天数（第 74 行），而不是秒数。这也是对运算符进行重载的结果。不过，似乎它的运算结果始终是以天为单位，而不是根据 datafeed 中指定的 TimeFrame 为单位，或者说是以 bar 为单位。

最终，一个回测期间的仓位变化如下：

![](https://images.jieyu.ai/images/2023/08/lesson20-3.1.png)

### 3.2. 成交量限制

当我们发出一个交易委托时，回测框架如何进行撮合，在设计上有很多难点，最难的可能是成交量匹配的策略。如果回测框架只有普通的行情数据，那么它只能看到价格和已经成交的成交量。如果委托价能够匹配上，此时回测框架应该：

1. 允许仅按价格进行成交吗？
2. 允许把所有的成交量都匹配给回测中的策略吗？

如果我们只允许按成交量的一部分（0,1) 撮合回测委托，显然，由于 A 股的涨停机制，这将不合理地阻止我们在涨停板上卖出股票，在跌停板块买入股票。

如果我们不进行任何限制，这也会使得策略占不少便宜，这样的策略在实盘中，效果将大打折扣。

因此，这些问题没有正确答案，两种策略都可能是对的，但都只在特定的条件下正确。实际上，即使有 order book，也不一定就能有正确答案，因为存在时间差，可能当你看到 order book 上有对手盘而进行下单时，等到真正下单时，对手盘又撤销了的情况。

因此，我们的策略一定要考虑这些情况，特别是高频策略。这也是高频策略量化中比较难做的地方。

在 backtrader 中，提供了四种方式来进行限制，首先是通过 FixedSize。

#### 3.2.1. FixedSize

```python
# 通过 BACKBROKER() 类直接设置
cerebro = Cerebro()
filler = bt.broker.fillers.FixedSize(size=100)
newbroker = bt.broker.BrokerBack(filler=filler)
cerebro.broker = newbroker

# 通过 SET_FILLER 方法设置
cerebro = Cerebro()
cerebro.broker.set_filler(bt.broker.fillers.FixedSize(size=100))
```

我们通过 broker.fillers.FixedSize 生成一个 Sizer 对象，然后将它赋值给 broker，也可以直接通过 broker.set_filler 来完成。

设置了 FixedSize 之后，委托将按委买量、sizer 限制和当天实际成交量中的最小者来填单。未被执行的部分，会在第二个周期来临时被取消。

#### 3.2.2. FixedBarPerc

FixedBarPerc 执行逻辑与 FixedSize 类似：

```python
# 通过 BACKBROKER() 类直接设置
cerebro = Cerebro()
filler = bt.broker.fillers.FixedBarPerc(perc=50)
newbroker = bt.broker.BrokerBack(filler=filler)
cerebro.broker = newbroker

# 通过 SET_FILLER 方法设置
cerebro = Cerebro()
cerebro.broker.set_filler(bt.broker.fillers.FixedBarPerc(perc=50))
# PERC 以 % 为单位，取值范围为 [0.0,100.0]
```

#### 3.2.3. BarPointPerc

BarPointPerc 比较复杂，它的本意是将成交量在 [low, high] 区间里进行均匀划分，然后根据成交价，决定有多少成交量是可以供撮合的。不过从源码来看，它的实现不一定正确。

最后一种方式，当我们不提供 fillers 对象时，就是不限制成交的模式。实际回测中，可以先用不限成交量的方式进行回测，然后按 FixedBarPerc 的方式，比如使用 5%的成交量，再加上适当滑点运行几次，通过这样的试探来发现真实的成交量限制。

### 3.3. 交易时机 - Cheat-On-Open

我们已经进行了好几笔交易，但从输出信息来看，backtrader 并没有提示我们它是如何进行撮合的。比如，它是使用当天的数据？还是次日数据？这一节我们就来讨论这个问题

对于交易订单生成和执行时间，Backtrader 默认是 “当日收盘后下单，次日以开盘价成交”，这种模式在回测过程中能有效避免使用未来数据。考虑到开盘价是集合竞价形成的，那么只要我们在集合竞价中报一个最高价，在不考虑成交量的情况下，就必然能以开盘价成交，因此以次日开盘价成交，是可以真实复现的。

上述模式也可能出现一些小小的问题，比如，我们是在 next 函数中计算信号并发出买入指令的。此时计算 size，我们会使用当天的收盘价进行计算。但以这个价格计算出的 size，如果开盘价比较高，就可能导致第二天因资金不足失败。

为了应对一些特殊交易场景，Backtrader 还提供了一些 cheating 式的交易时机模式：Cheat-On-Open 和 Cheat-On-Close。

Cheat-On-Open 是当日下单，当日以开盘价执行的方式。

这段代码展示了如何使用 cheat-on-open 模式。

```python
%matplotlib inline

import datetime
import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind
from tabulate import tabulate
from collections import defaultdict
from backtrader import Strategy

from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")

class TestStrategy(Strategy):
    def __init__(self):
        self.order = None
        fast = btind.SMA(period = 5)
        slow = btind.SMA(period = 10)
        
        self.crossover = btind.CrossOver(fast, slow)
        
    def notify_order(self, order):
        if order.status == order.Completed:
            price = round(order.executed.price, 2)
            print(order.data._name, order.ordtypename(), order.size, price)
            print("\n")
            
    def next_open(self):
        if self.order:
            self.cancel(self.order)
            
        dt = self.data.datetime.date()
        if not self.position:
            if self.crossover > 0:
                print('{} Send Buy, open {:.2f}'.format(dt, self.data.open[0]))
                self.order = self.buy(size=100)
        elif self.crossover < 0:
            print('{} Send Close, open {:.2f}'.format(dt, self.data.open[0]))
            self.order = self.close()
            
    def next(self):
        dt = self.data.datetime.date()
        print(f"in next: {dt}, {self.crossover[0]}, {self.data.open[1]:.2f}")
            
cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("000001.XSHE", 100)
cerebro.adddata(data, name="平安银行")
results = cerebro.run(cheat_on_open = True)

cerebro.plot(iplot = False, volume=False)
```

这里的要点是，使用 cheat-on-open 方式，需要将策略写在 next_open 中，而不是常见的 next 方法中。此外，我们还需要在 cerebro.run 中，指定 cheat_on_open 参数为 True。

我们在 next 方法中，只打印当前时间、信号，不再进行任何策略信号处理。

!!! attention
    这里注意，我们通过 self.data.open[1] 来偷看了次日的开盘价。backtrader 允许偷看，部分削弱了它通过封装避免未来数据所做出的努力。

在 notify_order 方法中，我们打印执行完成的 order 的信息，价格正是我们在 next 方法中偷看的价格。下面是输出结果：

```text
in next: 2023-04-06, 0.0, 12.27
in next: 2023-04-07, 0.0, 12.34
in next: 2023-04-10, 1.0, 12.40
2023-04-11 Send Buy, open 12.40
2023-04-11 平安银行 Buy 123 12.4

in next: 2023-04-11, 0.0, 12.28
in next: 2023-04-12, -1.0, 12.11
2023-04-13 Send Close, open 12.11
2023-04-13 平安银行 Sell -123 12.11
```

从输出中可以看出，next 提前一天发出信号，或者说，在 next_open 中，它仍然使用的是前一天的信号，但交易发生在当天开盘，利用了当天的开盘价信息。

买入和平仓都是在开盘时完成的。

### 3.4. 交易时机 - Cheat-on-Close

cheat-on-close 是指用当天的收盘价来成交，而不是推迟到下一天用开盘价成交。

使用 cheat-on-close 模式的要点是：
1. 策略实现在 next 中
2. 在回测运行之前，要设置 coc 模式

```python
# 示例 8
%matplotlib inline

import datetime
import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind
from tabulate import tabulate
from collections import defaultdict
from backtrader import Strategy

from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")

class TestStrategy(Strategy):
    def __init__(self):
        self.order = None
        fast = btind.SMA(period = 5)
        slow = btind.SMA(period = 10)
        
        self.crossover = btind.CrossOver(fast, slow)
        
    def notify_order(self, order):
        if order.status == order.Completed:
            price = round(order.executed.price, 2)
            dt = self.data.datetime.date()
            print(dt, order.data._name, order.ordtypename(), order.size, price)
            print("\n")
            
    def next(self):
        if self.order:
            self.cancel(self.order)
            
        dt = self.data.datetime.date()
        if not self.position:
            if self.crossover > 0:
                print('{} Send Buy, price {:.2f}'.format(dt, self.data.close[0]))
                self.order = self.buy(size=123)
        elif self.crossover < 0:
            print('{} Send Close, price {:.2f}'.format(dt, self.data.close[0]))
            self.order = self.close()
            
cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("000001.XSHE", 100)
cerebro.adddata(data, name="平安银行")
cerebro.broker.set_coc(True)
results = cerebro.run()

cerebro.plot(iplot = False, volume=False)
```

!!! attention
    这次我们的策略实现在 next 方法中，这是与 cheat-on-open 不一样的地方

我们在调用 cerebro.run 之前，通过 cerebro.broker.set_coc 设置 cheat-on-close 模式，注意这次我们的设置方法不一样。在 run 方法的参数中，没有 cheat_on_close 或者 coc 参数，因此我们只能通过 broker 的方法来进行设置。

notify_order 的方法仍然与 coo 一样。现在，我们来看看输出结果有什么不同：

```text
2023-04-10 Send Buy, price 12.38
2023-04-11 平安银行 Buy 123 12.38

2023-04-12 Send Close, price 12.18
2023-04-13 平安银行 Sell -123 12.18

2023-04-18 Send Buy, price 12.69
2023-04-19 平安银行 Buy 123 12.69
```

在 next 方法中，我们打印的是当天的收盘价信息。通过 notify_order，我们确认最终执行的价格，也正是当天收盘价。

### 3.5. 交易函数

#### 3.5.1. 普通交易函数
backtrader 在 strategy 中提供了 buy, sell 和 close 这些普通的交易函数：

```python
# 示例 9
class TestStrategy(bt.Strategy):
    def notify_order(self, order):
        dt = self.datetime.date()
        if order.status == order.Completed:
            price = round(order.executed.price, 2)
            print(dt, order.ordtypename(), price, order.size)
        
    def next(self):
        print("in next:", len(self.data), self.datetime.date())
        if len(self.data) == 1:
            self.buy(size = 500)
        elif len(self.data) == 2:
            self.sell(size = 200)
        else:
            self.close()
        
cerebro = bt.Cerebro()  
cerebro.addstrategy(TestStrategy)

data = await get_sample_feed("000001.XSHE", 4)
cerebro.adddata(data)

cerebro.run()
```

这里 buy, sell, close 都是常规交易。close 是平仓函数，在 A 股回测中，则于是单向交易，所以它在不带 size 参数调用时，相当于卖出所有持仓；如果带上 size 参数，则相当于 sell。

这段代码输出如下：

```text
in next: 1 2023-08-11
2023-08-14 Buy 11.76 500

in next: 2 2023-08-14
2023-08-15 Sell 11.69 -200

in next: 3 2023-08-15
2023-08-16 Sell 11.67 -300

in next: 4 2023-08-16
```

我们先是在 8 月 11 日买入了 500 股，然后在下一个交易日卖出 200 股，此时 order.size 为负数。最后，在第三个交易日，即 8 月 15 日，我们下达了平仓指令，但实际执行是在第 4 个交易日，即 8 月 16 日。

显然，根据前一节所学的内容，我们可以知道，这里的交易时机是 backtrader 默认的最常用的交易时机，即次日开盘时，以开盘价成交。

!!! attention
    平仓交易发生在第 4 天。如果我们的数据流只有 3 个 bar，会发生什么情况？最后的平仓指令会下达。但不会执行，所以我们不会等到 8 月 16 日的卖出。

此外，backtrader 还提供了 order_target 系列函数。

#### 3.5.2. order_target 系列

order_target 系列函数是指，当下单后，持仓会达到 target 的状态。比如，order_target_size 是指，假设当前持仓为 n, target 指定为 m，如果 m-n 为正数，则买入 m-n 股，否则，卖出 m-n 股。比如，当前持仓为-3, target 为-7, m-n 为-4，则还要卖出 4 股。

```python
class TestStrategy(bt.Strategy):
   def next(self):
      # 按目标数量下单
      self.order = self.order_target_size(target=size)
      # 按目标金额下单
      self.order = self.order_target_value(target=value)
      # 按目标百分比下单
      self.order = self.order_target_percent(target=percent)
```

order_target_value 与 order_target_size 一样，不过最终是要按市值来算。市值计算用当前 Bar 的 close 计算，但买入卖出操作还是以下一根 bar 的开盘价算。这里也有空单和多单的考虑，由于我们只在 A 股上实践，所以就不详细介绍了。

order_target_percent，是按当前账户的总资产的百分比来决定如何补足仓位。

所有的交易函数都会返回一个订单对象 order，我们可以通过 cancel 方法来取消订单：

```python
self.cancel(order)
```

### 3.6. 组合交易

前面的交易都是独立的交易，每个订单之间彼此没有依赖。但有时候，我们希望针对主定单自动生成止损单 (stop order) 和止盈单 (limit order)。这时候我们就需要使用 buy_bracket 和 sell_bracket，后者用以可以做空的场合。

```python
buy_bracket(# 主订单的参数
            data=None, size=None, price=None,
            plimit=None,exectype=bt.Order.Limit,
            valid=None, tradeid=0,
            trailamount=None, trailpercent=None,
            oargs={},
            # 止损单的参数
            stopprice=None, stopexec=bt.Order.Stop, stopargs={},
            # 止盈单的参数
            limitprice=None, limitexec=bt.Order.Limit, limitargs={},
            **kwargs):......
```

buy_bracket() 用于多头交易场景，买入证券后，如果价格下跌，希望通过止损单卖出证券，限制损失；在价格上升时，希望通过限价单卖出证券，及时获利，通过 buy_bracket() 可以同时提交上述 3 个订单，而无须单独提交三次。

在最简单的例子中，我们只需要传入 stopprice 和 limitprice 就可以了。

这里我们以平安银行在 2023 年 8 月 15 日前 50 天的 k 线为例，使用双均线策略，它将在 7 月 6 日出现一个金叉，此时我们下买单，同时设定下跌 5%时止损，上涨 5%止盈。最后，该订单将触发止盈。

```python
# 示例 10
import backtrader as bt

class TestStrategy(bt.Strategy):
    params = dict(
        ma=bt.ind.SMA,
        win1=5,
        win2=10,
        stop=0.05,
        limit=0.05
    )

    def notify_order(self, order):
        print('{}: Order({}, {}) {} {:.2f}'.format(
            self.data.datetime.date(0),
            order.ref, order.ordtypename(),
            order.getstatusname(),
            self.broker.getvalue()))

        if order.status == order.Completed:
            self.holdstart = len(self)

        if not order.alive() and order.ref in self.orefs:
            self.orefs.remove(order.ref)

    def __init__(self):
        fast, slow = self.p.ma(period=self.p.win1), self.p.ma(period=self.p.win2)
        self.cross = bt.ind.CrossOver(fast, slow)

        self.orefs = list()

    def next(self):
        if self.orefs:
            return

        if not self.position:
            if self.cross > 0.0:
                close = self.data.close[0]
                p1 = math_round(close, 2)
                p2 = math_round(p1 * (1 - self.p.stop), 2)
                p3 = math_round(p1 * (1 + self.p.limit), 2)

                print("buy_bracket", p1, p2, p3)
                os = self.buy_bracket(size=700, price = p1, stopprice=p2, limitprice=p3)

                self.orefs = [o.ref for o in os]

cerebro = bt.Cerebro()
data = await get_sample_feed("000001.XSHE", n=40, end=datetime.date(2023, 8, 15))
cerebro.adddata(data)
cerebro.addstrategy(TestStrategy)
cerebro.addanalyzer(
    bt.analyzers.TimeReturn, 
    timeframe=bt.TimeFrame.Months
)
results = cerebro.run()
analyzer = results[0].analyzers[0]
print(analyzer.get_analysis())
```

在这段代码中，我们在 next 函数中，当金叉发生时，下了一个组合订单，并将组合订单保存在 self.orefs 中。

然后在 notify_order 函数中，打印出我们接收到的每一个通知。这里我们输出当前日期、订单号、订单类型、状态和当前市值。

为了方便进行收益分析，确定是否触发了止盈，我们添加了一个 TimeReturn 分析器，该分析器将按月汇总收益。

最后，收益分析打印出来，结果如下：

```text
buy_bracket 11.34 10.77 11.91

2023-07-06: Order(885, Buy) Submitted 9958.00
2023-07-06: Order(886, Sell) Submitted 9958.00
2023-07-06: Order(887, Sell) Submitted 9958.00

2023-07-06: Order(885, Buy) Accepted 9958.00
2023-07-06: Order(886, Sell) Accepted 9958.00
2023-07-06: Order(887, Sell) Accepted 9958.00

2023-07-06: Order(885, Buy) Completed 9958.00

2023-07-28: Order(887, Sell) Completed 10427.00
2023-07-28: Order(886, Sell) Canceled 10427.00

# 收益分析
[((2023, 6, 30, 0, 0), 0.0), 
((2023, 7, 31, 0, 0), 0.043),
((2023, 8, 31, 0, 0), 0.0)]

```

在 notify_order 的输出中，我们先是注意到在 7 月 6 号，通过 buy_bracket 增加了三个订单，一个主订单，是买入订单，两个卖出订单，一个是止损，一个是止盈。

接下来的三行，是这些订单被接受。在买入时，我们指定了 size 为 700，如果我们指定 size 为 1000，这将超出可用现金范围，这些订单就会被拒绝，大家可以自行尝试一下。

第 11 行，主买订单完成，如果现在查询持仓，我们将看到一个 size 为 700 的持仓。

第 13 行，7 月 28 日，止盈订单触发，卖出操作完成。注意这里没有 submitted 和 accepted 两个状态
第 14 行，同一交易日，由于止盈订单触发，止损订单也因此取消。

### 3.7. OCO 订单

前一节中我们介绍的组合交易，是针对同一个标的，目标是要实现自动止损或者止盈。有时候，我们尝试以不同的延时或者限价来进行委托，但只要其中的一个被执行、或者取消（到期），同一组内的其它订单也随之取消。此时我们就可以使用 OCO(One Cancels Other) 订单。

```python
# 示例 11
def next(self):
   ...
   o1 = self.buy(...)
   ...
   o2 = self.buy(..., oco=o1)
   ...
   o3 = self.buy(..., oco=o1)

# 案例 2
def next(self):
   ...
   o1 = self.buy(...)
   ...
   o2 = self.buy(..., oco=o1)
   ...
   o3 = self.buy(..., oco=o2)
```

在上述案例 1 中，生成的 o1 与 o2 是一组关联订单，其中 o1 是主订单，它的执行情况将会决定 o2 的生死存亡，如果 o1 被执行、取消或到期，就会自动取消订单 o2； o1 与 o3 也是一组关联订单，情况与 o1 - o2 组类似；

案例 2 中，订单 o1 关联着订单 o2，订单 o2 关联着订单 o3，虽然是 2 组关联订单，实质上 o1、o2、o3 是一组订单，因为 o1 以 o2 为媒介，影响 o2 的同时，也影响了 o3 。

很难说这种类型的订单有多大用处。毕竟，在实盘中能否得到执行，很可能依赖于 broker 的实现。所以，这里不作详细介绍。

### 3.8. 滑点、交易费用

backtrader 中有两种滑点，即百分比滑点和固定滑点。

#### 3.8.1. 固定滑点

```python
# 方式 1
cerebro.broker = bt.brokers.BackBroker(slip_perc=0.0001)
# 方式 2
cerebro.broker.set_slippage_perc(perc=0.0001)
```

#### 3.8.2. 百分比滑点

假设设置了 n% 的滑点，如果指定的买入价为 x，那实际成交时的买入价会提高至 x * (1+ n%) ；同理，若指定的卖出价为 x，那实际成交时的卖出价会降低至 x * (1- n%)。

```python
# 方式 1
cerebro.broker = bt.brokers.BackBroker(slip_fixed=0.001)
# 方式 2
cerebro.broker.set_slippage_fixed(fixed=0.001)
```

假设设置了大小为 n 的固定滑点，如果指定的买入价为 x，那实际成交时的买入价会提高至 x + n ；同理，若指定的卖出价为 x，那实际成交时的卖出价会降低至 x - n。

在设置滑点时，需要考虑调整后的价格是否超出次日的最高、最低价的问题，以及在超出时，订单是否执行的问题。backtrader 给出了 slip_open, slip_match, slip_out, slip_limit 等参数来定制相关行为，具体的请参考文档。

### 3.9. 交易费用

在设置交易费用时，允许指定 commission（手续费/佣金）， multi 乘数，margin（保证金）和是否双边征收等参数。

```python
cerebro.broker.setcommission(
    # 交易手续费，根据 MARGIN 取值情况区分是百分比手续费还是固定手续费
    commission=0.0,
    # 期货保证金，决定着交易费用的类型，只有在 STOCKLIKE=FALSE 时起作用
    margin=None,
    # 乘数，盈亏会按该乘数进行放大
    mult=1.0,
    # 交易费用计算方式，取值有：
    # 1.COMMINFOBASE.COMM_PERC 百分比费用
    # 2.COMMINFOBASE.COMM_FIXED 固定费用
    # 3.NONE 根据 MARGIN 取值来确定类型
    commtype=None,
    # 当交易费用处于百分比模式下时，COMMISSION 是否为 % 形式
    # TRUE，表示不以 % 为单位，0.XX 形式；FALSE，表示以 % 为单位，XX% 形式
    percabs=True,
    # 是否为股票模式，该模式通常由 MARGIN 和 COMMTYPE 参数决定
    # MARGIN=NONE 或 COMM_PERC 模式时，就会 STOCKLIKE=TRUE，对应股票手续费；
    # MARGIN 设置了取值或 COMM_FIXED 模式时，就会 STOCKLIKE=FALSE，对应期货手续费
    stocklike=False,
    # 计算持有的空头头寸的年化利息
    # DAYS * PRICE * ABS(SIZE) * (INTEREST / 365)
    interest=0.0,
    # 计算持有的多头头寸的年化利息
    interest_long=False,
    # 杠杆比率，交易时按该杠杆调整所需现金
    leverage=1.0,
    # 自动计算保证金
    # 如果 FALSE, 则通过 MARGIN 参数确定保证金
    # 如果 AUTOMARGIN<0, 通过 MULT*PRICE 确定保证金
    # 如果 AUTOMARGIN>0, 如果 AUTOMARGIN*PRICE 确定保证金
    automargin=False,
    # 交易费用设置作用的数据集（也就是作用的标的）
    # 如果取值为 NONE，则默认作用于所有数据集（也就是作用于所有 ASSETS)
    name=None)
```

如果我们对交易费用要求比较精细，那么还需要定制。

```python
# 示例 12
class StockCommission(bt.CommInfoBase):
    params = (
      ('stocklike', True), # 指定为股票模式
      # 使用百分比费用模式
      ('commtype', bt.CommInfoBase.COMM_PERC), 
      # COMMISSION 不以 % 为单位
      ('percabs', True), 
      # 印花税，默认为 0.1%
      ('stamp_duty', 0.001),
      ('commission', 1e-4)
    )
    
    # 自定义费用计算公式
    def _getcommission(self, size, price, pseudoexec):
            comm_all = self.p.commission + self.p.stamp_duty

            if size > 0: # 买入时，只考虑佣金
                return abs(size) * price * self.p.commission
            elif size < 0: # 卖出时，同时考虑佣金和印花税
                return abs(size) * price * comm_all
            else:
                return 0
            
comminfo = StockCommission()
cerebro = bt.Cerebro()
cerebro.broker.addcommissioninfo(comminfo)
```

这段代码从 bt.CommInfoBase 派出了一个子类，设置了相关参数，可能需要在实例化时指定的参数有 commision 和 stamp_duty。

要点是重写_getcommission 方法，从而返回具体委买 size 和价格下的手续费。对于 A 股股票交易，还应该加上最小 5 元的限制。

最后，我们在第 25 行，实例化这个类，通过 broker 的方法 addcommissioninfo 将其添加到 broker 中。

关于交易费率的设置，我们还要注意，不同历史时期，相关费用是不相同的。对于高频交易，交易费率对策略的影响又比较大，要评估策略在不同时期的影响，可能需要分段设置交易费率。这样的回测结果才能带入到实盘中。

## 4. 可视化

这一节我们将介绍观测器 (observers) 和可视化。observers 为可视化提供数据。

### 4.1. 观察器

我们先介绍几种常见的观察器，熟悉下它们绘制的图形。然后我们再演示如何实现这些绘图。
#### 4.1.1. Broker 观察器

Broker 观察器记录了 broker 中各时间点的可用资金和总资产；可视化时，会同时展示 cash 和 values 曲线；如果想各自单独展示 cash 和 values，可以分别调用 backtrader.observers.Cash 和 backtrader.observers.Value。这个图显示了分别绘制 cash, value 和 broker 的情况，从上到下，依次是 cash,value 和 broker。

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # PRINT(SELF.STATS.BROKER.CASH[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.Cash)
cerebro.addobserver(bt.observers.Value)
cerebro.addobserver(bt.observers.Broker)
cerebro.run()
cerebro.plot(iplot = False)
```

这段代码演示了如何加载 Broker 观察器。我们构建了一个非常简单的策略，如果当前为奇数天，则买入，次日卖出。这一次，我们在实例化 cerebro 时，增加了一个 stdstats = False 的参数，这是什么意思呢？

backtrader 把观察器的集合称为 stats， 我们可以在 strategy 中访问这个 stats 属性，比如在被注释的第 3 行，这里我们就通过 stats.broker.cash 显示了当前可用资金。

Broker, Trades 和 BuySell 3 个观察器被当成默认观察器，它们被称为 stdstats，会自动被 cerebro 加载并绘图。因此，为了单独演示 Broker，我们在第 9 行，传入了 stdstas = False 的参数，同时，这也要求我们通过第 15 到第 17 行，加入这些观察器。

在后面的代码演示中，我们将保持这个基本框架。绘制结果如下：

![75%](https://images.jieyu.ai/images/2023/08/lesson20-plot-broker.png)

#### 4.1.2. BuySell 观察器
BuySell 观察器记录回测过程中的买入和卖出信号；可视化时，会在价格曲线上标注买卖点。与 Broker 不一样，BuySell 信号会自动叠加在主图上。

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # PRINT(SELF.STATS.BROKER.CASH[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.Cash)
cerebro.addobserver(bt.observers.Value)
cerebro.addobserver(bt.observers.Broker)
cerebro.run()
cerebro.plot(iplot = False)
```

第 3 行代码，注意我们一样可以通过 stats 属性来访问 buysell。这将打印每个买卖点上的价格信息。规则就是，将观察器名字全部小写化。
第 15 行，我们添加了 BuySell 这个观察器。输出结果如下：

![75%](https://images.jieyu.ai/images/2023/08/lesson20-plot-buysell.png)

#### 4.1.3. Trade 观察器

记录了回测过程中每次交易的盈亏（从买入建仓到卖出清仓算一次交易）；可视化时，会绘制盈亏点。

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # PRINT(SELF.STATS.TRADES[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.Trades)
cerebro.run()
cerebro.plot(iplot = False)
```

第 3 行代码，这将打印出该时间点上，已结束的交易的盈亏数据。输出结果：

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-trades.png)

#### 4.1.4. TimeReturn 观察器

TimeReturn 记录回测过程中的收益序列；可视化时，会绘制 TimeReturn 收益曲线。

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # PRINT(SELF.STATS.TIMERETURN[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.TimeReturn)
cerebro.run()
cerebro.plot(iplot = False)
```

第 13 行，这会打印出每天的收益，第一天收益则为 0.
第 15 行，加入 TimeReturn 观察器。它与 analyzer 中的 TimeReturn 一样，可以带一个 TimeFrame 参数，如果我们不指定参数，就会使用主循环的默认周期。

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-timereturn.png)

#### 4.1.5. DrawDown 观察器
DrawDown 记录了回测情况。基于这个数据，我们可以计算最大回撤时间和最大回撤比。

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # PRINT(SELF.STATS.DRAWDOWN[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)
cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.DrawDown)
cerebro.run()
cerebro.p
```

第 13 行，这会打印当前的回撤数据。

![75%](https://images.jieyu.ai/images/2023/08/lesson20-plot-drawdown.png)

#### 4.1.6. Benchmark 观察器

Benchmark 用来记录业绩基准。在 backtrader 的官方示例中，benchmark 应该与标的收益是两条曲线，绘制在同一个副图中，但在我们的实验中，只能绘制出 benchmark 线，标的收益并没有绘制。原因不明。

```python
class Day1Strategy(bt.Strategy):
    def next(self):
        # PRINT(SELF.STATS.BENCHMARK[0])
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro(stdstats=False)

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(bt.observers.Benchmark, 
                    data=data)
cerebro.run()
cerebro.plot(iplot = False)
```

输出结果：

![75%](https://images.jieyu.ai/images/2023/08/lesson20-plot-benchmark.png)

### 4.2. 定制绘图

从前面的示例可以看出，backtrader 默认的绘图不是太理想，它在布局、显示大量数据方面存在诸多问题。

不过，backtrader 也提供了一些参数，让我们可以控制绘图效果。它提供了一个名为 PlotScheme 的类来进行绘图控制，我们也可以继承这个类，进一步定制化。

```python {all}{maxHeight:'400px'}
class PlotScheme(object):
    def __init__(self):
        # 无论只有 X 轴还是 XY 轴都适合紧凑的图表排列（参见 MATPLOTLIB)
        self.ytight = False 

        # 子图表的上下边距。这不会覆盖 PLOTINFO.PLOTYMARGIN 选项
        self.yadjust = 0.0

        # 每条新线都在之前线的 Z 轴下方绘制。改为 FALSE 可以使线绘制在之前线的 Z 轴上方
        self.zdown = True  

        # X 轴日期标签的旋转角度 
        self.tickrotation = 15

        # 用来调节主图的高度
        self.rowsmajor = 5

        # 用来调节子图的高度
        self.rowsminor = 1  

        # 子图表之间的距离
        self.plotdist = 0.0

        # 在所有图表的背景上都有网格
        self.grid = True

        # 默认的 K 线图样式（收盘价线）。其他选项：'BAR', 'CANDLE'
        self.style = 'line'

        # 收盘价线的默认颜色 
        self.loc = 'black' 

        # 多头 K 线/蜡烛的默认颜色
        self.barup = '0.75'

        # 空头 K 线/蜡烛的默认颜色
        self.bardown = 'red'

        # 蜡烛线的透明度（当前未使用）
        self.bartrans = 1.0  

        # 蜡烛是否填充还是透明
        self.barupfill = True
        self.bardownfill = True

        # 填充蜡烛的不透明度 (1.0 不透明 - 0.0 透明）  
        self.baralpha = 1.0

        # 填充区域 (FILL_GT 和 FILL_LT 之间）的 ALPHA 混合    
        self.fillalpha = 0.20

        # 是否绘制成交量。如果数据没有成交量，即使设置为 TRUE 也不会绘制成交量
        self.volume = True

        # 成交量是叠加在价格线上还是使用独立子图
        self.voloverlay = True

        # 叠加成交量时的缩放比例 
        self.volscaling = 0.33

        # 向上推移叠加成交量以改善可见性。如果价量重叠太多可能需要调整
        self.volpushup = 0.00 

        # 多头日成交量的默认颜色
        self.volup = '#aaaaaa'  

        # 空头日成交量的默认颜色
        self.voldown = '#cc6073'

        # 叠加成交量时使用的透明度
        self.voltrans = 0.50

        # 图表上标签的默认字体大小
        self.subtxtsize = 9

        # 指标子图是否显示图例
        self.legendind = True

        # 指标图例的位置（参见 MATPLOTLIB)
        self.legendindloc = 'upper left' 

        # 数据源图例的位置（参见 MATPLOTLIB)  
        self.legenddataloc = 'upper left'

        # 在线条名称后面绘制最后一个值
        self.linevalues = True

        # 在每条线的末端打标签显示最后一个值
        self.valuetags = True

        # 水平线的默认颜色 
        self.hlinescolor = '0.66'  

        # 水平线的默认样式
        self.hlinesstyle = '--'

        # 水平线的默认宽度
        self.hlineswidth = 1.0

        # 默认颜色方案：TABLEAU 10
        self.lcolors = tableau10

        # X 轴刻度的显示格式  
        self.fmt_x_ticks = '%Y-%m-%d %H:%M' 

        # 数据点值的显示格式
        self.fmt_x_data = None
```

rowsmajor 参数和 rowsminor 用来调节主图与子图的高度。我们可以把 backtrader 的绘图想像成一个 n*1 的网格，每个子图都占据其中的一行或者多行。所以，通过 rowsmajor 来指定它要占据的行数，也就指定了它的高度。当然，最终都是一个计算比例的过程。

style 这个参数，如果我们希望主图显示蜡烛图的话，可以将其设置为'candle', 不过，如果我们要求绘制 k 线图的话，接下来还要分别指定 barup, bardown, barupfill, bardownfill 这些颜色，以符合 A 股的习惯。

有时候我们觉得 volume 子图比较占空间，也可以通过 volume = False 隐藏这个图。另外，volume 子图的颜色也是可以定制的。

```python
from backtrader.plot import PlotScheme
from backtrader.plot import Plot

class MyStyle(PlotScheme):
    def __init__(self):
        super().__init__()
        self.rowsmajor = 3
        self.barup = 'red'
        self.bardown = 'green'
        self.barupfill = False

        self.volup = 'red'
        self.voldown = 'green'
        self.style = 'candle'
    
class Day1Strategy(bt.Strategy):
    def next(self):
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()
            
cerebro = bt.Cerebro()

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

cerebro.run()
plotter = Plot(scheme = MyStyle())
cerebro.plot(plotter = plotter, iplot = False, numfigs = 5)
```

这段代码显示了如何定制一个 PlotScheme 类，并使用它。要注意官方文档在这部分讲得不太详细，可能导致误解。

我们先从 PlotScheme 派出一个类，称为 MyStyle，然后修改相关的属性。这里我们是修改了主图高度的比例，将主图设置为 k 线图，并调整颜色为我们熟悉的 A 股风格。

然后我们实例化 Plot 类，生成 MyStyle 的一个实例对象，作为 scheme 参数传入，并把这个实例命名为 Plotter，再在调用 cerebro.plot 时，传入 plotter = plotter。

由于我们回测的时间跨度较大，bar 的数量较多，我们还通过参数 numfigs = 5 的设置，总共生成了 5 幅图。

这个图显示了其中的第一个子图。

![](https://images.jieyu.ai/images/2023/08/lesson20-plot-mystyle.png)

当然这个图还是很丑。

如果不进行深度定制，该类的一些属性，可以做为参数，在调用 cerebro.run 时传入。大家可以自行尝试，在调用 cerebro.plot 时，将 PlotScheme 的属性一个个作为参数传入，看看效果。再在这个基础上，按照前面的方法，自己进行定制。

考虑到 backtrader 设计上的固有风格和体系，要想自由定义绘图并不容易，特别是它依赖于 matplotlib，只能生成静态图，所以，backtrader 生成的图，可以在初期作为探索，进行快速筛选，如果要作为正式展示，还是需要用到我们前面介绍的知识，自己动手，绘制更丰富和美观的图形。

### 4.3. 收集回测数据

上一节我们提到，要获得更好的绘图体验，还得自己动手。但是，数据从何而来？

所以，我们又要回到 observers 上来。observers 可以为我们收集全面的数据，用以后续分析和绘图。

我们可以通过这段代码来获取并保存各周期的相关数据。

```python
import csv
from backtrader.observers import DrawDown, TimeReturn, Value, Cash, Broker

class Day1Strategy(bt.Strategy):
    def start(self):
        self.mystats = csv.writer(open("/tmp/mystats.csv", "w"))
        self.mystats.writerow(['datetime',
                               'drawdown', 'maxdrawdown',
                               'timereturn',
                               'value', 'cash'])
    def next(self):
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()  
            
        w = self.mystats
        w.writerow([self.data.datetime.date(-1).strftime('%Y-%m-%d'),
                    '%.2f' % self.stats.drawdown.drawdown[0],
                    '%.2f' % self.stats.drawdown.maxdrawdown[0],
                    '%.2f' % self.stats.timereturn.line[0],
                    '%.2f' % self.stats.broker.value[0],
                    '%.2f' % self.stats.broker.cash[0]])
        
    def stop(self): 
        w = self.mystats 
        w. writerow([self.data.datetime.date(0).strftime('%Y-%m-%d'),
                    '%.2f' % self.stats.drawdown.drawdown[0],
                    '%.2f' % self.stats.drawdown.maxdrawdown[0],
                    '%.2f' % self.stats.broker.value[0],
                    '%.2f' % self.stats.broker.cash[0]])
        
cerebro = bt.Cerebro()

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(DrawDown)
cerebro.addobserver(TimeReturn)
cerebro.addobserver(Value)
cerebro.addobserver(Cash)

cerebro.run()
```

!!! Tip
    这里使用了 csv 这个模块，它是 python 标准库的一部分。

注意我们在 strategy 中，通过 start, stop 来打开文件，写入表头和最后一期的数据。

第 43 到 46 行，先加入对应的观察器。最终输出的 csv 文件如下所示：

```text
datetime,drawdown,maxdrawdown,timereturn,value,cash
2023-08-17,nan,nan,nan,nan,nan
2022-08-09,0.00,0.00,0.00,10000.00,10000.00
2022-08-10,0.00,0.00,0.00,10000.06,10011.83
2022-08-11,0.00,0.00,-0.00,10000.04,10000.04
2022-08-12,0.00,0.00,-0.00,9999.92,10012.01
2022-08-15,0.00,0.00,0.00,10000.01,10000.01
2022-08-16,0.00,0.00,-0.00,9999.99,10011.83
2022-08-17,0.00,0.00,-0.00,9999.97,9999.97
2022-08-18,0.00,0.00,0.00,10000.20,10012.15
```

我们也可以选择不通过 csv 来保存数据，而是直接在内存中存放和处理数据。

```python {all}{maxHeight: '400px'}
from backtrader.observers import Value

class Day1Strategy(bt.Strategy):
    def __init__(self):
        self.values = []
        
    def next(self):
        if len(self) % 2 == 0:
            self.buy()
        else:
            self.sell()  
            
        self.values.append((self.datetime.date(-1), 
                          self.stats.value[0]))
        
    def stop(self): 
        self.values.append((self.datetime.date(0),
                          self.stats.value[0]))
        
cerebro = bt.Cerebro()

data = await get_sample_feed("000001.XSHE")
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

cerebro.addobserver(Value)

results = cerebro.run()
results[0].values
```

我们可以像第 6 行这样，在 strategy 中定义一个属性 self.values，在回测过程中，将收益数据保存到这个 values 中。注意这里我们使用的时间是 date(-1)，只有这样，时间才是对应的。

在回测结束前，第 18 行，在 stop 方法中，我们把最后一期数据添加进来。

最后，在第 31 行，回测结束后，我们通过 results[0].values 来使用我们保存的 returns 数据。cerebro.run 返回的结果，就是它生成的策略对象。如果我们不是通过 cerebro.run 方法来运行回测，而是通过 cerebro.optstrategy 来运行回测时，就有可能生成若干个 Strategy 对象，每个对象都对应不同的参数，供我们择优选择。这时候，我们就可以遍历这个数组，获取它们的 values 进行比较。

在这里，我们只保存了每日总资产。实际上，这已足够用来计算绝大多数收益指标，对策略进行评估了。我们在后面的课程中，将介绍如何使用总资产数据来进行策略评估及绘图。

## 5. 优化

关于回测的运行方式，我们已经多次接触到了 run 方法，也在上一张 slide 中，提到了 cerebro.optstrategy 方法。实际上，有一些策略，我们知道需要调整参数，如何尝试出最优参数，这件事可以让 backtrader 来做。

我们通过一个双均线策略来说明优化过程。这个策略有两个参数，快线和慢线的计算参数。

```python
from backtrader.analyzers import TimeReturn
import backtrader as bt

class TestStrategy(Strategy):
    params = (('fast', 5), ('slow', 10))
    def __init__(self):
        self.order = None
        fast = btind.SMA(period = self.p.fast)
        slow = btind.SMA(period = self.p.slow)
        
        self.crossover = btind.CrossOver(fast, slow)
            
    def next(self):
        if self.order:
            self.cancel(self.order)
            
        if not self.position:
            if self.crossover > 0:
                self.order = self.buy(size = 800)
        elif self.crossover < 0:
            self.order = self.sell(size = 800)
            
cerebro = bt.Cerebro()

data = await get_sample_feed("000001.XSHE", 100)
cerebro.adddata(data, name="平安银行")
cerebro.addanalyzer(TimeReturn, timeframe=bt.TimeFrame.Years)

cerebro.optstrategy(TestStrategy, fast = (5, 10), slow = (20, 30))
results = cerebro.run(maxcpus = 4, optreturn = True)

returns = []
for r in results:
    analyzer = r[0].analyzers
    fast, slow = r[0].params.fast, r[0].params.slow
    item = [(k,v) for k,v in analyzer[0].get_analysis().items()]
    returns.append((fast, slow, f"{item[0][1]:.2%}"))

print(tabulate(returns, ["fast", "slow", "pnl"]))
```

我们通过 optstrategy，而不是 addstrategy 来加入策略。这里每个参数都指定了一个元组，所以组合下来，将生成 4 个 Strategry 实例。

我们仍然使用 cerebro.run 方法来运行回测。考虑到我们一共要运行 4 次回测，所以我们设置 maxcpus=4，这样可以让 4 个核同时运行。optreturn 这里设置为 False，这样，cerebro.run 的返回值，就会与我们前面的运行结果一致，否则，返回的结果将是一个 OptReturn 对象，它只只包含 params 和 analyzers，而策略所包含的 datas， observers，indicators 都被移除掉了。不过，对我们这里的结果并没有影响。

接下来我们处理回测结果，显示了各个参数下的最终收益。

![](https://images.jieyu.ai/images/2023/08/lesson20-opt-log.png)

在进行优化回测时，这里使用了多进程。父子进程在传递参数时，需要进行序列化操作。如果你的程序在未进行优化时可以正常运行，但在参数优化时无法正常运行，请考虑是否遇到序列化问题。

## 6. 小结

到此为止，关于 backtrader 的方方面面基本上都介绍到了。我们知道了如何加载数据、定义策略、定义指标、运行策略、掌握了一些下单知识，还了解了如何绘图和优化。

前面已经介绍过，backtrader 绘制的图形，在 k 线图的表示方法上是与 A 股不一致的。我们已经介绍了如何修改。

如果我们的回测是基于日线的，那么 backtrader 的机制没有问题。如果回测的主周期是日内的，显然 backtrader 是无法处理 T+1 交易规则限制的。此外，backtrader 不能处理涨跌停的情况。

在复权问题上，backtrader 要求我们导入的数据是已进行了复权处理的。但是，尽管 backtrader 不存在偷看数据问题，使用复权数据本身，就已经引入了未来数据。在这方面，大富翁和聚宽的框架都是实时复权，可以避免这样的问题。

我们已经介绍了撮合上存在哪些问题，也给出了减轻这个问题的方法。backtrader 这种机制，它的回测数据与撮合数据没有分开，就会导致如果回测粒度太小，速度慢，很多小周期的数据策略并不关注，加载它们是一种浪费；但在撮合时，数据粒度却是越细越精准。这是 backtrader 及类似的框架解决不了的问题。
