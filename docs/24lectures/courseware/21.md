---
title: 策略回测评估
mainfont: WenQuanYi Micro Hei
puppeteer:
  format: "A4"
  landscape: false
  displayHeaderFooter: true
  headerTemplate: '
  <div style="height:50px; width: 100%;">
    <div style="margin:0 30px 10px 0;text-align:center;font-size:12px">
        大富翁量化金融实战课
    </div>
    <div style="border-top: 1px solid lightgrey;width:100%"/>
  </div/'
  footerTemplate: '<div style="position: relative; width: 100%; border-top: 1px solid black; margin: 0px 30px 30px; padding: 1px, 0px, 0px; font-size: 9px; font-family: Meiryo, Arial, sans-serif;">
  <div style="position: absolute; top: 15px; left: 0px; text-align: left;">
  <span class="title"></span></div>
  <div style="position: absolute; top: 15px; width: 100%; text-align: center;">
  <span class="pageNumber"></span> / <span class="totalPages"></span></div>
  <div style="position: absolute; top: 15px; right: 75px; text-align: right;">by 量化风云</div>
  <div style="position: absolute; top: 0px; right: 0px">
    <img style="opacity:0.8;width:48px" src="data:image/jpg;base64,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">
  </div>
  </div>'
  margin: {
    top: "65px",
    bottom: "70px",
    right: "30px",
    left: "30px"
  }
---

![75%](https://images.jieyu.ai/images/2023/08/lesson21-outline.png)

我们在上一章里，介绍了如何通过 backtrader 来分析策略的绩效。这是通过向 cerebro 添加 analyzer 或者 observer 来实现的。但在 backtrader 那一章中，我们并没有着重介绍这些方法。

原因有二。其一，backtrader 的方法略显复杂。评估一个策略的好坏，往往需要从多个维度来入手。在 backtrader 中，每一个维度都是一个分析器（观测器），都需要我们逐个加入。但主流的方法，都是只收集市值这一个指标，通过它来计算出其它指标；并且，受 backtrader 的绘图框架限制，backtrader 对绩效指标的绘制也不是很理想。

!!! attention
    除了自制的绘图，backtrader 还给出了通过 pyfolio 框架来进行绩效分析绘图的方案，pyfolio 可以生成美观的撕页 (tearsheet)。但是，pyfolio 本身存在着后续无人维护的问题，因此作者也不推荐这一方案。

其二，并非只有回测需要进行绩效评估。实际上，绩效评估方法，不仅回测需要，我们在实盘中，也需要实时或者定期监控各项绩效指标；不仅量化策略需要，主观策略也需要。而且在各种场景下，我们都是应该基于同样的指标和计算方法。因此，有必要介绍一种独立于回测框架的策略绩效评估方案。

这一章我们将介绍各种绩效评估指标，以及 Empyrical 和 Quantstats 这个两个绩效评估库。与 Pyfolio 一样，Empyrical 同样由 Quantpian 开发。在 Quantpian 中止维护之后，现在由 ml4t 在继续维护。Empyrical 在算法的正确性和稳定性上都得到了时间检验，但它只包含指标计算，不能生成 report，所以，我们还需要借助 Quantstats 来生成报告。

!!! Attention
    quantstats 也能计算各种指标，但从其 github issues 及其版本号来看，目前可能还不到稳定的时候，所以，我们可以借助它来生成报表，但在需要进行关键决策时，可能还是要借助 empyrical 生成的指标数据。

无论是 Empyrical 还是 Quantstats，都只依赖于一种数据 -- 每日收益率或者每日资产数据。其中每日收益率，又可以通过每日资产数据轻松转换过来。

这样一来，如果我们是在通过 backtrader 进行回测的话，我们只需要向 cerebro 添加一个 Value 指标（即每日资产），得到这个数据之后，我们将自己进行各类指标 (metrics) 的计算，并进行可视化。

如果我们是使用其它框架进行的回测，或者正在进行实盘交易，只要我们能生成每日资产表，就都能计算出各种绩效指标，对策略进行评估。

## 1. 回报率

### 1.1. 简单回报率

简单回报率 (Simple Returns) 有时候也称为 Rate of Returns，即回报率。回报率代表了一个投资在一段时间内相对于初始成本的净收益或亏损的百分比。在量化策略中，一般是以日为单位，计算每一日相对于前一日的资产变化比。

$$ 
R(t_0,t_1) = \frac{P_{t_1}-P_{t_0}}{P_{t_0}}
$$

有时候我们也看到持有期回报（Holding Period Return）的说法。在上式中，如果$t_0$, $t_1$之间的间隔是一天，则相当于一天持有回报；如果是一秒，则是一秒持有期回报。

如果 values 代表每期的总资产，则我们可以通过下面的代码来计算每期的简单回报：

```python
import pandas as pd

# 假设 values 代表每期的总资产（如 [100, 105, 110, 108]）
values = [1000, 1020, 1050, 1035, 1070]  # 示例数据

# 转换为 Pandas Series
asset_series = pd.Series(values)

# 计算简单回报率（当前期与前一期的百分比变化）
simple_returns = asset_series.pct_change()

print("各期简单回报率：")
print(simple_returns)
```

下面我们给出一个 backtrader 回测中，得到 values，再计算 returns 的代码。在此之后，我们计算其它指标都将从 returns 开始。

```python
%matplotlib inline

from coursea import *
await init()
    
from datetime import datetime
import backtrader as bt

from backtrader.observers import Value

class Day1Strategy(bt.Strategy):
    def __init__(self):
        self.values = []
        
    def next(self):
        if len(self) % 2 == 0:
            self.buy(size = self.broker.get_cash()/self.data.close)
        else:
            self.close()
            
        self.values.append((self.datetime.date(-1), 
                          self.stats.value[0]))
        
    def stop(self): 
        self.values.append((self.datetime.date(0),
                          self.stats.value[0]))

    
cerebro = bt.Cerebro()
cerebro.broker.set_cash(1_000_000)

code = "399300.XSHE"
bars = await Stock.get_bars(code, 1000, FrameType.DAY)
df = pd.DataFrame(bars)

data = bt.feeds.PandasData(dataname=df, datetime='frame')

cerebro.adddata(data)
cerebro.addobserver(Value)
cerebro.addstrategy(Day1Strategy)

results = cerebro.run()
values = results[0].values
values = np.array(values, dtype=[('frame', 'O'), ('values', 'O')])
values = values['values']

# 计算 SIMPLE RETURNS

returns = values[1:]/values[:-1] - 1
```

这段代码的主要部分我们在上一章中已经介绍过了，我们还是再讲解一下，作为对上一章的内容的一个回顾：

在这段代码中，我们先向 cerebro 中增加了一个名为 Value 的 observer，然后在 strategy 的 next 方法中，通过 self.stats.value[0] 来获得上一期总资产并保存到 strategy 的成员变量 values 中。

Cerebro 负责实例化 Strategy 对象，并在回测结束后，以一个数组来返回这些实例对象，保存为 results 变量。在这次回测中，只生成了惟一一个实例化对象，因此我们可以通过 results[0] 来取得被 cerebro 实例化的 strategy 对象，进而获取每天的 values 数据。这是一个 List[Tuple] 对象，所以很适合转换为 numpy structured array，每日资产就保存在'values'列中。

### 1.2. 对数回报率

与 Simple Returns 对应的概念是对数收益率，即 log returns。它的计算公式如下：

$$
log\_return = \ln \frac{P_t}{P_{t-1}}
$$

在金融领域，我们常常因为以下两个原因，使用对数收益率。其一，对数收益率具有时序可加性。比如，我们以简单回报率来计量，假若某个标的去年涨了 50%，今年跌了 50%，此时如果我们将两个简单回报率直接相加，会得到平均收益率为零的结果，但实际上这笔投资是亏损的。使用对数收益率，则这两年的对数收益率分别为 40.5%和-69.3%，其和为-28.77%，再转换为简单收益率为亏损 25%，这与我们的直观印象是一致的。

下面的表格显示了简单回报率与对数回报率的对比：

| 资产       | 1   | 1.5   | 0.75   |
| ---------- | --- | ----- | ------ |
| 简单回报率 | NA  | 50%   | -50%   |
| 对数回报率 | NA  | 40.5% | -69.3% |

通过将两笔收益相加，我们就可以得到两期总的对数回报收益。将其转换为简单收益率，与我们预期是一致的：

$1 - e^{40.5\% - 69.3\%}= -25\%$

另一个原因则是策略建模相关的。收益是复利，具有指数性质，如果收益率比较稳定的话，它的对数将会拟合成一条直线，这样也方便我们看出收益的波动特性。

!!! tip
    有一种观点认为，通过对数据取对数，可以实现降维，这也是在金融领域常用对数回报率的一个原因。这个观点在一定条件下是正确的。从上面的图形可以看出，如果数据取对数后能够较好地拟合成直线，那么确实可以降维。

下面两个图都是资产收益图，对应着 10%的日收益率。从左图我们看不出来它的规律，但从右图我们可以很清楚地知道，该投资是以非常稳健的收益在增长。

![50%](https://images.jieyu.ai/images/2023/08/lesson21-sr-vs-log-r.png)

### 1.3. Cumulative Returns

Cumulative returns 是通过一组简单回报，来计算累积回报的方法。假设我们有以下的每日收益表：

| 日期       | 收益率    |
| ---------- | --------- |
| 2015-07-16 | -0.012143 |
| 2015-07-17 | 0.045350  |
| 2015-07-20 | 0.030957  |
| 2015-07-21 | 0.004902. |

在上面的表格中，每一行都是一个简单回报，那么截止到 2015 年 7 月 21 日，我们在这笔资产上的总收益应该如何表示？这就是 cumulative returns 的含义。

我们可以通过以下方法计算 cumulative returns:

```python
>>> import numpy as np

>>> returns = np.array([-0.012143, 0.045350, 0.030957, 0.004902])
>>> np.cumprod(1+returns) - 1
array([-0.012143  ,  0.03265631,  0.06462426,  0.06984304])

>>> np.exp(np.cumsum(np.log(1 + returns))) - 1
array([-0.012143  ,  0.03265631,  0.06462426,  0.06984304])

>>> from empyrical import cum_returns
>>> cum_returns(returns)
array([-0.012143  ,  0.03265631,  0.06462426,  0.06984304])
```

!!! tip
    Cumlative Returns 本质上仍然是 Rate of Returns。与其说它是某种回报类型，不如说它是一种计算方法：它是由一组时间上连续的简单回报以复利的方式计算总回报的一种方法。

这里我们分别使用了三种方法来计算累积回报。

首先我们使用了 np.cumprod 方法，它是将数组从第 0 个元素起，一直累乘到第 n 个元素止，并将其结果作为结果数组的第 n 个元素的值的计算方法。要通过 cumprod 和简单回报来计算累积回报，我们需要先给每日回报加上 1，然后进行累乘，最后减去基数 1。

第二种方法是对收益取对数，然后进行累加。

第三种方法是使用 empyrical 的 API，cum_returns 来进行计算。

三种方法，计算出来的结果都是一致的。结果数组的每一个元素，都是直到当期为止的累积收益比。

!!! Note
    Empyrical 是 Quantpian 开发并开源的进行风险评估的核心库之一。最初我们一般通过 pyfolio 来间接使用它。但在 quantpian 不再维护 pyfolio 之后，ml4t 将其剥离出来进行维护，并将其发布为 empyrical-reload。<br>在本书中使用的 empyrical，实际上是 empyrical-reload 这个库：

    ```
    pip install empyrical-reloaded
    ```

### 1.4. Aggregate Returns
这个概念可能与 cumulative returns 一样，更多地存在于量化交易中。它是指让我们把按日计算的收益率，聚合成按周、月、季或者年为单位的收益率。

在上面给出的日收益示例（表格一）中，我们可以分别按周、月、年进行统计。

```python
from empyrical import aggregate_returns
import datetime

returns = pd.Series(returns, 
                    index=[datetime.date(2015,7,16), 
                           datetime.date(2015,7,17), 
                           datetime.date(2015,7,20),
                           datetime.date(2015,7,21)]
                   )
for period in ('weekly', 'monthly', 'yearly'):
    print(aggregate_returns(returns, period))
```

我们将得到以下结果：

```text
aggregated weekly
2015  29    0.032656
      30    0.036011

aggregated monthly
2015  7    0.069843

aggregated yearly
2015    0.069843
```

我们的输入数据只跨了两周，不到一个月，更不到一年。所以，当我们按周进行聚合时，我们得到了两个周的周收益率，分别是 3.3%和 3.6%；但月、年聚合收益率都只有一次，都为 6.98%。

### 1.5. Annual Return
Annual Return，即年化收益率，有时候我们也使用 CAGR （compounded Annual Growth Rate) 这个说法，都是一样的含义：它是指按年为单位，通过其它数据计算出来的一个年化均值。

在 Aggregate Returns 的例子中，我们得到的按年累计收益是 7%左右。这个收益是在 4 个交易日内取得的，如果我们假设这四个交易日的收益均值能够保持一年，那么这样一年下来的收益，就是 Annual Return。

这 4 天的累积收益为 6.9843%，我们按下面的公式来手动计算年化收益：

$$
CAGR = (1 + P_n)^{(252/n)} - 1
$$

在公式中，$P_n$ 是 $n$ 日累积收益率， 252 是一年的交易天数，需要根据具体情况进行调整。在我们的例子中，$P_n$是 0.069843，$n$ 是 4，代入后，我们将得到：

$$
(1 + 0.069843)^{(252/4)} - 1 = 69.33
$$

即年化收益是 69.33 倍。

下面这段代码中，我们使用了 empyrical 中的 annual_return 函数来计算年化收益率：

```python
from empyrical import annual_return

returns = np.array([-0.012143, 0.045350, 0.030957, 0.004902])
returns = pd.Series(returns, 
                    index=[datetime.date(2015,7,16), 
                           datetime.date(2015,7,17), 
                           datetime.date(2015,7,20),
                           datetime.date(2015,7,21)]
                   )
annual_return(returns)
```

结果与我们手动计算的结果是一致的。

!!! note
    在 empyrical 中，annual_return 和 cagr 这两个函数同时存在，两者实现并无本质不同，运行结果也是一样的。

## 2. 风险调整收益率
银行存款利率或者国债利率被我们看成无风险利率。当我们进行一项投资时，几乎总是期望收益会比无风险利率要高。因此，一些评估指标也就建立在经过风险利率调整之后的收益之上。这一类指标有 sharpe, sortino, calmar 和 omega 等等。
### 2.1. sharpe ratio
夏普率由著名经济学家威廉。夏普 (William Sharpe) 发表于 1966 年，最初它被称为"Reward-to-Variability"。威廉. 夏普也是著名的资产定价模型 (Capital assets pricing model, CAPM) 理论创始人之一，他于 1990 年获得了诺贝尔经济学奖。

![50%](https://images.jieyu.ai/images/2023/08/lesson21-william-sharp.png)
<cap>图片来源于investopedia网站</cap>

Sharpe ratio 的计算公式如下：

$$
Sharpe\ Ratio = \frac{E_{R_p} - R_f}{\sigma_{(R_p - R_f)}}
$$

这里：

$E_{R_p} = 组合的期望收益率$<br>
$R_f = 无风险利率$<br>
$\sigma_p = 资产超额利润的标准差$

!!! tip
    $E_{R_p} - R_f$是对$E_{(R_p - R_f)}$的化简，因为$R_f$是一个常量。但在计算中，我们会使用后者，这将马上在下面的代码中看到这一点。

下面的代码演示了具体算法：

```python
import numpy as np
n = 20
rf = 0.03

np.random.seed(78)
returns = np.random.normal(size=(n))/100

def sharpe_ratio(returns, annual_rf, annual_factor):
    # CALC RISK-FREE ADJUST RETURNS
    adj_ret = returns - annual_rf/annual_factor

    sr_ = np.mean(adj_ret)/np.std(adj_ret, ddof=1) 
    return sr_ * np.sqrt(annual_factor)

sharpe_ratio(returns, rf, 252)
```

!!! attention
    investopedia.com 是一个重要的金融百科全书类网站。但它给出的 sharpe ratio 的公式：<br><br>$Sharpe\ Ratio = \frac{R_p - R_f}{\sigma_p}$<br><br>是错误的。在没有说明的情况下，$R_p$ 应该理解为累积收益率，而不是平均收益率。<br><br>我们这里使用的公式来自维基百科。

上面的代码中，我们生成了一个 20 天左右的随机序列，把它当成每日收益率。在 sharpe_ratio 方法中，我们先是用无风险收益率对这个收益序列进行了调整，得到 adj_ret，adj_ret的均值就是公式中的分子部分。

代码中不太容易懂的地方是为什么要乘以 np.sqrt(annual_factor)。这跟我们作为输入的 returns 的周期有关。本质上，为了便于比较，sharpe ratio 是一个年化指标。在示例中，我们把 returns 当成日回报收益率，把rf 当成是年化无风险收益率，这种情况下，np.mean(ra) 需要乘以年化因子 252，才能得到年化收益率；对 ra 的标准差部分，我们也需要进行同样的调整，最终结果就是需要乘以 np.sqrt(annual_factor)。

当 returns 数组是日收益率时，annual_factor 是 252；当 returns 数组是月收益率时，则 annual_factor 为 12，以此类推。

在实际运用中，我们可以通过 empyrical.sharpe_ratio 来进行sharpe ratio的计算：

```python
from empyrical import sharpe_ratio

# annual_rf变量，设置为年化无风险利率如0.02（2%）
annual_rf = 0.02

np.random.seed(78)
returns = np.random.normal(size=(n))/100
sharpe_ratio(returns, annual_rf/252)

!!! tip
    risk free 收益率$r_f$一般按国债收益率给出。在调用 empyrical 的 sharpe_ratio 时，我们需要先将它调整为与 returns 对应周期的收益率。

### 2.2. sharpe 比率与资产曲线的关系

当我们掌握了计算sharpe ratio的方法之后，很可能会问这样一个问题：我们的策略在回测中取得什么样的sharpe ratio时，才能认为是一个值得投资的策略？如果从sharpe ratio上看，策略是优秀的，那么对此策略，我们可以期待什么样的年化收益？二者之间是否存在某种相关性？

一般认为，夏普率大于 1.5，才能产生稳定的正收益。大于 2，则策略很可能值得推荐。

下面的代码探索了 sharpe 率与可能的资产收益之间的关系：

```python
import numpy as np
from empyrical import sharpe_ratio
import matplotlib.pyplot as plt

def equity_value(returns):
    return np.cumprod(1 + returns)

n = 252
rf = 0.03/252

np.random.seed(78)
returns = np.random.normal(size=(n))/100

results = {}
target = np.array([1, 1.5, 2, 2.5, 3])
found = {}

for i in range(10000):
    returns = np.random.normal(size=(n))/100
    sharpe = sharpe_ratio(returns, rf)
    
    if np.any(abs(sharpe - target) < 0.1):
        pos = np.argmin(abs(sharpe-target))
        key = target[pos]
        if key not in found or abs(found[key] - key) > abs(sharpe - key):
            results[sharpe] = returns
            found[key] = sharpe
            
    if found == set((1, 1.5, 2, 2.5, 3)):
        break

sharpes = sorted(set(found.values()))
for sharpe in sharpes:
    returns = results[sharpe]
    ev = equity_value(returns)
    plt.plot(ev, label=f"夏普率：{sharpe:.1f}")
    plt.legend()
```

sharpe 率在 1~3 之间时，对应的资产曲线如下所示：

![50%](https://images.jieyu.ai/images/2023/08/lesson21-sharpe-equity.png)

注意，sharpe 比率是收益率的函数，反过来则不是：同一个夏普比率，可能对应无穷多的收益率序列。

!!! tip
    夏普率是基民选择基金时常用的比较指标之一。实际上它也可以成为我们量化策略的一个因子。但我们也要注意，它是一种事后指标，不具备预测能力。
    
当我们使用夏普比率时，需要了解它的局限性。夏普比率在计算时引入了正态分布假设，但我们已经知道，几乎没有任何一种投资品的收益是完全符合正态分布的。这是它的局限之一。

考虑到碾路机前捡钢磞的比喻：你可以很长一段时间都能捡到钢蹦，但最终碾路机会到来，导致巨大的损失。sharpe ratio 就是这样，它可能因一系列很小的、稳定的收益产生较高的数值，但一旦发生几笔大额亏损，夏普率也将迅速下降。

另外，有一些策略，平时亏损很小，但正收益都会比较高，因此，它的标准差也不会低。计算下来，尽管策略的总体收益不错，风险也很小，但夏普率可能并不高。这也是夏普率的局限。

下面，我们就来看如何改进夏普率指标。

### 2.3. sortino 指标
上一节我们讲到，如果有一种策略，平时亏损很小，但正收益都会比较高，因此，它的标准差也不会低。即使最终年化收益比较高，回撤也不大，但夏普指标可能并不高，导致这些策略被我们淘汰。

通常情况下，夏普指标是对的：很可能这几笔较高的正收益有它的偶然性，所以给予较低的夏普值似乎并没有错。

但夏普率的适用条件是，资产收益率要满足正态分布。而这一条件并不存在，所以，如果真的出现这样的收益分布，它并不一定就是偶然的、不可靠的。无论理论上最终如何决定，我们都应该先有一个恰当的指标来表述策略的这一收益特征。这个指标，就是 sortino 指标。

与 sharpe 相比，sortino 区分了波动的好坏：它使用下行标准差，而不是一般标准差来作为分母。它的公式是：

$$
Sortino\ Ratio = \frac{E_{r_p} - r_f}{\sigma_{d}}
$$

这里：

* $r_p$是指组合收益
* $r_f$是指无风险收益
* $\sigma_{d}$是下行标准差

我们可以通过 empyrical 的 sortino_ratio 方法来计算这一指标。

```python
import numpy as np
from empyrical import sortino_ratio, sharpe_ratio, annual_return

n = 20
rf = 0.03/252

returns = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, -0.01])

sortino = sortino_ratio(returns, rf)
sharpe = sharpe_ratio(returns, rf)
cagr = annual_return(returns)

label = f"sortino: {sortino:.1f}\nsharpe: {sharpe:.1f}\ncagr: {cagr:.2%}"
plt.plot(equity_value(returns), label=label)
plt.legend()
```

这段代码以给定的日收益率，分别计算了 sharpe 率，sortino 比率和年化收益。在计算sortino时，我们只使用了 `returns[returns < 0]`的那部分数据来计算标准差。

![50%](https://images.jieyu.ai/images/2023/08/lesson21-sortino-vs-sharpe.png)

可以看出，它的 sharpe 比率只有 1.3，这一指标并不高，但年化收益率达到了 64.4%，相当于前一个例子中 sharpe 比率为 2.5 的策略的资产收益率。

所以，如果我们仅以 sharpe 指标来区分的话，我们将淘汰这一从结果上看比较优秀的策略，无论如何它的年化收益相当优秀。

一些观点认为，sortino大于2的策略被认为是好的策略。但是，当sharpe与sortino相互矛盾时，即使加上一个好的年化指标，也不能驱逐我们心中最后一丝的不安。sortino只是告诉我们，每承担一份风险，就可能获得sortino指标对应倍数的回报。

万一这种下行风险超过了我们的承受能力呢？看起来，我们还需要一个衡量最大下行风险的指标。这个指标就是Max Drawdown。

### 2.4. Max DrawDown （最大回撤）

Max DrawDown 是指账户净值从最高点的滑落程度，它可以帮我们了解从任意一点进场，策略可能承担的最大损失。下图红线部分，标识出了该笔投资中出现的最大回撤区间。

![75%](https://images.jieyu.ai/images/2023/08/lesson21-mdd.png)

最大回撤我们在讲解 numpy 那一章中，已经介绍过它的计算原理。我们一般通过 empyrical 的 max_drawdown 方法来进行计算。不过，为了绘图需要，下面的代码中，还是给出了手动计算最大回落区间坐标的代码，根据这两个坐标，就能够计算出最大回撤：

```python
import numpy as np
from empyrical import max_drawdown

returns = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, -0.01])

mdd = max_drawdown(returns)

xs = np.cumprod(1 + returns) - 1  # 复利累积净值

# end of the period
i = np.argmax(np.maximum.accumulate(xs) - xs) 

# start of period
j = np.argmax(xs[:i])

ev = equity_value(returns)
plt.plot(ev, label=f"mdd: {mdd:.2%}")
plt.plot([i, j], [ev[i], ev[j]], 'o', color='Red')
plt.legend()
```

![75%](https://images.jieyu.ai/images/2023/08/lesson21-mdd-marked.png)

!!! attention
    最大回撤只是一个单纯的风险衡量指标，它并不是风险调整收益类指标。我们放在这一节介绍它，纯粹是为了行文连贯。
### 2.5. Sharpe 与 max drawdown 的关系
如果我们通过 sharpe 指标来挑选基金，或者评估自己的策略，我们有可能遇到这样一个问题：

如果某个策略的 sharpe 率比较好，当收益回撤到什么程度时，我们可以认为，当前市场条件已经不适合该策略，需要中止执行？

[这篇博文](https://python.plainenglish.io/bridging-from-sharpe-ratio-to-max-drawdown-a-numerical-approach-d465a11fac6a) 对此进行了研究。它的大致步骤是：

1. 随机生成 252 天的收益率，计算 sharpe 指标与 mdd。作者共生成了 4000 万组数据。
2. 将上述指标按 sharpe 值进行 quantile 切分
3. 分别统计每种 sharpe 值下，mdd 的分布情况

作者将研究结果绘制成下图：

![75%](https://images.jieyu.ai/images/2023/08/lesson21-draws-as-function-of-sharpe.png)

左图是 sharpe 率与期望回撤、最坏回撤的函数关系图，右图则各种 sharpe 值下，取最坏的回撤情况下的资产走势图。

最终，作者得出以下结论：

![75%](https://images.jieyu.ai/images/2023/08/lesson21-sharpe-mdd-relation.png)

如果在实时投资中，以 sharpe 等于 1 为例，如果最大回撤达到年化波动率的 2.35 倍时，可以认为夏普比率的假设将失效，此时应该终止该策略；如果预期 sharpe 为 1.5，则最大回撤达到年化波动率的 2.18 倍时，可以认为夏普比率的假设失效，此时也应该终止该策略。

在上述研究中，我们注意到使用了年化波动率这一指标。下面，我们就来介绍什么是年化波动率。

### 2.6. 年化波动率
波动率表明资产价格围绕均值的摆动幅度，是衡量回报分散性的统计指标。通常认为，波动性高的资产会比波动性小的资产风险更高，因为价格的可预测性更低。

从数学上看，波动率就是收益的标准差。但波动率与标准差有一个关键区别：波动率是与时间区间绑定的。因此，我们常常讨论的波动率，是指周波动率、月波动率或者年化波动率等等，它的计算公式也因此调整为：

$$
vol = \sigma_r\sqrt{t}
$$

!!!tip
    上述公式中，如果 r（returns）是按天计算的收益率，vol 是年化波动率，则 t 为 252；如果 vol 是周波动率，则 t 为 5（或者 7，视交易品种而定）。<br>一般我们是对这个 t 进行开方运算，但也可能是$t^\frac{1}{\alpha}$，这里的$\alpha$是 levy 稳定系数，一般默认取 2。

我们通过 empyrical 的 annual_volatility 来计算年化波动率：

```python
import numpy as np
from empyrical import annual_volatility

returns = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, -0.01])

av = annual_volatility(returns)
# av is 0.403
```

!!! attention
    与max drawdown一样，年化波动率也不是风险调整收益指标，它也是风险类指标。我们放在这里介绍这一指标，也是为了行文流畅。

### 2.7. Calmar Ratio

!!! note
    Calmar Ratio 是由 Terry W. Young 创建的。这是少数不以创建者名字命名的指标之一。这个指标名字来自于 Young 拥有的一家名为 California Managed Accounts 的公司，这家公司负责管理客户资金，并出版一种名为 CALifornia Managed Accounts Report 的 newsletter。Calmar 的名字，就来自于 CALifornia Managed Accounts Reports。

calmar 比率的计算公式是：

$$
calmar = annual\_return(r)/mdd(r)
$$

其中 r 是过去 36 个月的月收益率。由于 calmar 基于最大回撤，因此该比率往往比夏普或者索提诺更稳定，多数时候，改变的是因子。当然，如果我们使用的收益率周期小于 36 个月，则显然另当别论。

```python
import numpy as np
from empyrical import calmar_ratio

returns = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, -0.01])

calmar_ratio(returns)
```

一些交易员认为，Calmar大于1的策略是可接受的，calmar大于3则认为是优秀的，大于5则是极佳的策略。

### 2.8. Omega Ratio

Omega Ratio 发表于 2002 年，由 Keating 和 Shadwick 在一篇名为《A Universal Performance Measure》的文章中提出。它使用希腊字母的最后一个字母来命名，以显示它是一个终极性的指标。

最初的 Omega Ratio 定义为：

$$
\Omega(r)\triangleq\frac{\int_{r}^{\infty}(1-F(x))dx}{\int_{-\infty}^rF(x)dx}
$$

其中，$r$ 为指定的临界收益率，$F(x)$ 为收益率的累计分布函数。

这个公式的分子部分是上偏矩，分母部分是下偏矩，而阈值 $r$ 是我们设定的临界收益率（也称为最低要求收益率）。临界收益率用来区分收益或损失，高于临界收益率的，视为收益，低于临界收益率的，视为损失。Omega 比率利用了收益率分布的所有信息，考虑了所有的高阶矩，刻画了收益率风险的所有特征。

!!! note
    经典的投资组合理论是建立在均值-方差基础上的，基本的假设是收益率服从正态分布。sharpe ratio 仅使用了收益率分布的两个低阶统计矩，即均值和方差，因此无法捕获某些分布（尤其是不对称分布）和/或投资者将某种效用归因于高阶矩的所有有用信息。<br><br>关于统计矩 (moments)，我们在第 11 章有过介绍。

取不同的临界收益率，可以得到关于 $r$ 递减的 Omega 函数。在临界收益率等于均值的时候，Omega 比率等于 1。在相同的临界收益率下，对于不同的投资选择，Omega 比率值越高，投资绩效也就越好。

可以用下图来理解该指标的计算。Omega 比率就等于上方绿色面积除以下方红色面积。

![75%](https://images.jieyu.ai/images/2023/08/lesson21-omega-ratio.png)

因此，该指标也可以适合不同风险偏好的投资者。对于风险容忍度较低的投资者，可以选择较低的临界收益率，反之，可选择较高的临界收益率。

下面的代码演示了如何计算 omega ratio：

```python
import numpy as np
from empyrical import omega_ratio

returns = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, -0.01])

for rf in (0, 0.03, 0.2, 0.4, 0.6):
    print(f"{omega_ratio(returns, rf/252):.2f}")
```

输出是：

```
1.29
1.28
1.18
1.08
0.99
```

一般认为，比率大于 1 表明风险调整后的业绩良好。该投资组合实现高于目标水平的回报的可能性较高，而产生重大损失的可能性较低；比率等于 1 意味着阈值与投资的平均回报相匹配。该投资组合有 50% 的概率实现高于目标水平的回报，有 50% 的概率发生重大损失； 比率小于 1 表明该投资组合实现高于目标水平的回报的可能性较低，而遭受重大损失的可能性较高，表明该投资组合的风险调整后表现较差。

因此，在上面的示例中，该策略要实现大于 40%年化的可能性高于 50%；但要实现大于 60%的年化收益的可能性则低于 50%。

从这个示例来看，omega 能够根据投资者不同的风险偏好，给出收益值，确实在某种程度上，可算作是终极指标。

Omega 指标的主要缺陷之一，就是它本身是目标收益水平的函数，因此不便于在不同的投资策略间进行比较。

## 3. 基准对照类指标
有一类指标，计算过程中是需要与基准进行对比的，比如information ratio（信息比率），alpha/beta等。
### 3.1. information ratio
信息比率通过两组数据进行计算。其一是投资组合收益，其二是基准收率。两者之差被称为超额收益（或者主动收益），该超额收益的期望与波动性之比，即为信息比率。

从公式上看：

$$
IR = \frac{E[R_p - R_b]}{\sigma} = \frac{E[R_p - R_b]}{\sqrt{var[R_p-Rb]}}
$$

这里$R_p$是投资组合收益（回报），$R_b$是基准回报。$\sigma$在这里被称为跟踪误差，实际上就是超额收益的标准差。

!!! note
    信息比率公式与夏普率计算公式非常像，只不过夏普率计算时，使用无风险利益取代了基准收益率。

我们可以使用下面的代码来计算 information_ratio:

```python
from coursea import *
await init()

from empyrical import sharpe_ratio

rp = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
               -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
               0.07, -0.01, -0.01, -0.005, -0.005, 0.01,
               -0.005, 0.04, -0.005, -0.01])

bars = await Stock.get_bars("399300.XSHE", 23, FrameType.DAY)
close = bars["close"]
rb = close[1:]/close[:-1] - 1

def information_ratio(returns, benchmark_returns):
    if len(returns) < 2:
        return np.nan

    active_return = returns - benchmark_returns
    tracking_error = np.std(active_return, ddof=1)
    if np.isnan(tracking_error):
        return 0.0
    if tracking_error == 0:
        return np.nan
    return np.mean(active_return) / tracking_error

information_ratio(rp, rb)
```

!!! attention
    在本课程中，我们使用的是 empyrical reloaded，该库中移除了原本在 empyrical 包中存在的 information_ratio 方法。演示代码中的 information_ratio，就来自于原来的 empyrical 包。

从上述计算方法可以看出，与本章介绍的多数指标不同，信息比率是时间无关的。

有观点认为，0.4~0.6 之间的信息比率算是较好的数据，长期保持在 1 左右是很困难的事情。信息比率是与基准相关的统计指标，可能出现这样的情况，即信息比率很高，但投资仍然遭受较大的损失。

### 3.2. alpha/beta

威廉.夏普把金融资产的收益拆成两部分：跟随市场一起波动的部分叫贝塔收益；不随市场一起波动、与市场无关的部分就叫阿尔法收益。

Alpha 和 Beta 是用于评估股票、基金或投资组合绩效的两个关键衡量指标。 Alpha 衡量的是与市场指数或其他广泛基准相比的投资回报金额。 Beta 衡量投资的相对波动性。它表明了其相对风险。

Alpha 总是越高越好。高 Beta 可能受到成长型股票投资者的青睐。但寻求稳定回报和较低风险的投资者却会回避。

比如，如果$x$是基准收益，$y$是策略收益，则我们有以下公式：

$$
y = \alpha + \beta x + \epsilon
$$

这里的 𝜖 是残差，是收益中无法解释的随机部分。

给定策略收益和基准（市场）收益，我们就可以计算出策略本身的超额收益（称为𝛼），它相对于基准的波动性（称为𝛽)。

𝛽是由以下公式计算的：

$$
\beta = \frac{cov(x, y)}{\sigma_x^2}
$$

𝛽又被称为风险指数。当𝛽小于时，代表策略收益的波动小于基准；大于 1 时，策略收益的波动将大于基准。𝛽小于 0 时，代表策略的涨跌与基准的涨跌有负相关性。

!!! Note
    𝛽的计算类似于协相关性计算，但它的分母不是$\sqrt{Var[X]Var[Y]}$，而是$\sqrt{Var[X]Var[X]}$，即$\sigma_{x}^2$

计算出𝛽之后，就可以计算𝛼。

$$
\alpha = \frac{y-\epsilon}{\beta}
$$

在实际计算中，我们常用 risk-free 利率当作𝜖。我们可以通过 empyrical 中的 alpha_beta 方法来求解：

```python
from empyrical import alpha_beta
import datetime

rp = np.array([-0.01, -0.02, -0.015, 0.05, -0.01, -0.01, 
                    -0.005, 0.05, -0.01, -0.01, -0.02, -0.01, 
                    0.07, -0.01, -0.01, -0.005,  -0.005, 0.01,
                    -0.005, 0.04, -0.005, -0.01])

end = datetime.date(2023, 8, 21)
bars = await Stock.get_bars("399300.XSHE", 23, FrameType.DAY, end=end)
close = bars["close"]
rb = close[1:]/close[:-1] - 1

alpha_beta(rp, rb, 0.03/252)
```

在上面的例子中，我们最终得到投资 rp 的 alpha 值为 61.8%，而 beta 值为-0.2。沪深 300 取的是 2023 年 8 月 21 日，这段时间以下跌为主，因此，两者的相关性为负。注意，这里的 alpha 值是年化的收益。

## 4. 策略评估的可视化

在 backtrader 那一章，我们指出，backtrader 提供的可视化方案不够美观，可定制性也不够好，会在后面专门讲解如何解决这一问题。

现在，我们就来介绍基于 quantstats 的解决方案。

!!! tip
    我们通过以下命令来安装 quantstats 包：

    ```
    pip install quantstats
    ```
    在我们的课程环境中，该库已经安装。

quantstats 主要有以下功能：

1. 计算多种性能指标，这是由 stats 模块实现的
2. 指标可视化，这是通过 plots 模块实现的
3. 生成 metrics 报告，这是通过 reports 模块来实现的。

下面我们将进入到可视化报告生成部分。我们先来看一个使用 Conner's RSI 的策略，用它来生成一些数据。

```python
%matplotlib inline

from coursea import *
await init()
    
import backtrader as bt
import pandas as pd
from backtrader.observers import Value

class Streak(bt.ind.PeriodN):
    '''
    Keeps a counter of the current upwards/downwards/neutral streak
    '''
    lines = ('streak',)
    params = dict(period=2)  # need prev/cur days (2) for comparisons

    curstreak = 0

    def next(self):
        d0, d1 = self.data[0], self.data[-1]

        if d0 > d1:
            self.l.streak[0] = self.curstreak = max(1, self.curstreak + 1)
        elif d0 < d1:
            self.l.streak[0] = self.curstreak = min(-1, self.curstreak - 1)
        else:
            self.l.streak[0] = self.curstreak = 0

class ConnorsRSI(bt.Indicator):
    '''
    Calculates the ConnorsRSI as:
        - (RSI(per_rsi) + RSI(Streak, per_streak) + PctRank(per_rank)) / 3
    '''
    lines = ('crsi',)
    params = dict(prsi=3, pstreak=2, prank=100)
    params = dict(prsi = 3, pstreak = 2, prank = 20)

    def __init__(self):
        # CALCULATE THE COMPONENTS
        rsi = bt.ind.RSI(self.data, period=self.p.prsi)
        streak = Streak(self.data)
        rsi_streak = bt.ind.RSI(streak.data, period=self.p.pstreak)
        prank = bt.ind.PercentRank(self.data, period=self.p.prank)

        # APPLY THE FORMULA
        self.l.crsi = (rsi + rsi_streak + prank) / 3.0

class ConnerRSI(bt.Strategy):
    params = (('low', 12), ('high', 58))
    
    def __init__(self):
        self.myind = ConnorsRSI()
        self.assets = []

    def notify_order(self, order):
        if order.status == order.Margin:
            print(order)
        
    def next(self):
        if self.myind.crsi[0] <= self.p.low:
            size = self.broker.get_cash()/self.data.close[0]
            if size > 100:
                self.buy(size=size * 0.95)
        elif self.myind.crsi[0] >= self.p.high:
            self.close()
            
        self.assets.append((self.datetime.date(-1), self.stats.value[0]))
        
    def stop(self): 
        self.assets.append((self.datetime.date(0),
                          self.stats.value[0]))

    
cerebro = bt.Cerebro()
cerebro.broker.set_cash(1_000_000)
cerebro.broker.setcommission(commission=0.001)

# CODE = "399300.XSHE"
code = "000001.XSHG"
bars = await Stock.get_bars(code, 1000, FrameType.DAY)
df = pd.DataFrame(bars)

data = bt.feeds.PandasData(dataname=df, datetime='frame')

cerebro.adddata(data)
cerebro.addstrategy(ConnerRSI)
cerebro.addobserver(Value)

print('Starting Portfolio Value: %.2f' % cerebro.broker.getvalue())
results = cerebro.run()
print('Ending Portfolio Value: %.2f' % cerebro.broker.getvalue())

values = results[0].assets
```

最终我们得到约 980 个资产数据。

为了让 quantstats 能使用这些数据，我们需要将其转换成为 pandas.Series：

```python
df = pd.DataFrame(assets, columns=['frame', 'assets'])
df['datetime'] = pd.to_datetime(df['frame'])
assets = df.set_index('datetime').assets
assets
```
转换后的输出结果将是：

```
datetime
2019-08-09    1.000000e+06
2019-08-12    1.000000e+06
2019-08-13    1.000000e+06
2019-08-14    1.000000e+06
2019-08-15    1.000000e+06
                  ...     
2023-08-18    1.234433e+06
2023-08-21    1.219864e+06
2023-08-22    1.230089e+06
2023-08-23    1.214412e+06
2023-08-24    1.215850e+06
Name: assets, Length: 982, dtype: float64
```

### 4.1. Metrics

我们通过 qs.reports.metrics 方法来生成简单的、基于文本的报告。该方法主要有两个参数，其一就是每日资产，它是一个 pd.Series，索引为日期。另一个参数是 mode，可选值有'basic'和'full'两种。

```python
qs.reports.metrics(assets, mode='basic')
```

这将生成以下报告：

```
                    Strategy
..................  ..........
Start Period        2019-08-09
End Period          2023-08-24
Risk-Free Rate      0.0%
Time in Market      63.0%

Cumulative Return   21.58%
CAGR﹪              4.95%

Sharpe              0.48
Prob. Sharpe Ratio  82.41%
Sortino             0.66
Sortino/√2          0.47
Omega               1.11

Max Drawdown        -19.98%
Longest DD Days     699

Gain/Pain Ratio     0.11
Gain/Pain (1M)      0.41

Payoff Ratio        1.02
Profit Factor       1.11
...
```

### 4.2. plots
将显示指标的绘图。

```python
qs.reports.plots(assets)
```

![75%](https://images.jieyu.ai/images/2023/08/lesson21-qs-plots.png)

实际生成的图还会包括 Monthly Returns。如果使用 mode='full'， 则还会有 EOY 回报，Distribution of Monthly Returns 等等。

### 4.3. basic 和 full
这两个命令将表格（文本）输出和绘图组合起来。其中 basic 将显示 metrics(mode='basic'）中的文本报告内容和 plots(mode='basic') 中的绘图，而 full 则将显示 metrics(mode='full') 中的文本报告内容和 plots(mode='full') 中的绘图。

我们以 basic 为例：

```python
qs.reports.basic(assets)
```
输出结果比较长，请大家在 notebook 中自行运行并研究。

### 4.4. html
这种类型的报告将生成一个 html 文件，利用 html 强大的表现能力，quantstats 将文本与绘图有机组合在一起，既提供了丰富的信息，界面也很美观。

```python
qs.reports.html(assets,  output='lesson21.html')
```

生成的部分内容截图如下：

![75%](https://images.jieyu.ai/images/2023/08/lesson21-qs-html.png)
