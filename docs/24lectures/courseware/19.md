---
title: backtrader 回测框架（1）
output: pdf_document
mainfont: WenQuanYi Micro Hei
---

## 1. 快速开始

一次完整的回测一般包括以下几个步骤：

1. 准备回测数据
2. 编写策略
3. 运行回测
4. 获得回测结果
5. 分析回测结果

这个流程我们在第 6 到第 8 课已经接触过了。在我们学习 backtrader 的过程中，需要与之一一对应，看看在 backtrader 中是如何完成这些功能的。

我们先从一个比较简单的例子开始：

!!! Tip
    在 backtrader 的官网上，还有一个更简单的例子，读者也可以从这个例子 [^quick_start] 开始。

```python
# 示例 1
%matplotlib inline
# WITHOUT THIS LINE, WE'L GET JAVASCRIPT ERROR: IPYTHON IS NOT DEFINED

from coursea import *
await init()

import backtrader as bt
from backtrader import feeds
from backtrader.observers import BuySell
from backtrader.analyzers import AnnualReturn

class SmaCross(bt.Strategy):
    # 策略初始化时接受的参数
    params = dict(
        pfast=10,  # period for the fast moving average
        pslow=30   # period for the slow moving average
    )

    def __init__(self):
        """一般需要，自定义参数，提前计算指标等等"""
        sma1 = bt.ind.SMA(period=self.p.pfast)
        sma2 = bt.ind.SMA(period=self.p.pslow)
        self.crossover = bt.ind.CrossOver(sma1, sma2)

    def next(self):
        """必须改写，以实现策略逻辑"""
        if not self.position:  # not in the market
            # IF FAST CROSSES SLOW TO THE UPSIDE
            if self.crossover > 0:
                self.buy()  # enter long

        elif self.crossover < 0:  # in the market & cross to the downside
            self.close()  # close long position

    def notify_order(self, order):
        """可选。打印订单信息"""
        pass

    def notify_trade(self, trade):
        """可选，打印成交信息"""
        pass

cerebro = bt.Cerebro()  # create a "Cerebro" engine instance

# 获取数据
start = datetime.date(2023, 1, 4)
end = datetime.date(2023, 8, 3)
bars = await Stock.get_bars_in_range("000001.XSHG", FrameType.DAY, start, end)

df = pd.DataFrame(bars)
data = feeds.PandasData(dataname=df, datetime = 'frame')

# 向 CEREBRO 增加数据
cerebro.adddata(data)

# 增加策略
cerebro.addstrategy(SmaCross)

# 交易设置：本金
cerebro.broker.setcash(1_000_000)

# 交易设置：佣金
cerebro.broker.setcommission(1.5e-3)

# 交易设置：滑点
cerebro.broker.set_slippage_perc(perc=5e-3)

# 交易设置：
# CEREBRO.ADDSIZER(...)

# 添加策略分析指标
# CEREBRO.ADDANALYZER(ANNUALRETURN)

# 添加观测器
# CEREBRO.ADDOBSERVER(BUYSELL)

cerebro.run()

# 绘图
cerebro.plot(iplot=False)
```

第 1 行 我们声明启用 matplotlib 绘图。

!!! attention
    如果没有此声明，backtrader 会提示 Javascript Error: IPython is not defined。这个错误涉及到我们在之前讲解绘图的时候，接触过的一个概念，每种绘图库，它的图形定义和真正渲染到物理表面都是分开的。在真正需要渲染成图时，都是利用一个所谓的 backend 来进行渲染。有的库在 notebook 环境下能自动识别出正确的 backend，比如 plotly，有的则不行，比如 PyEcharts。较新版的 matplotlib 多数情况下是能正常识别的，但在这里当我们通过 backtrader 来调用绘图时，它使用的 matplotlib 会将 backend 确定为 tkinter，这是一种图形窗口界面，不适用于 notebook/jupyterlab，所以，会产生错误。

第 7 行 我们一般将 backtrader 导入为 bt
这是一种惯例

!!! tip
    我们有如下导入惯例：

    ```python
        # 示例 2
        import backtrader as bt
        import backtrader.indicators as btind
        import backtrader.feeds as btfeeds
    ```

第 8 行，我们导入 feeds 模块，这个模块是用来提供数据的。接下来我们要使用 feeds 模块中的 PandasData 类。

第 10 行，这里我们定义了一个策略类，它是我们使用自己的交易逻辑的地方。它需要继承自 bt.Strategy 类。

几乎所有的策略都有调整参数的需要。在第 12~15 行，我们定义了这个策略需要的参数。双均线策略需要定义两条均线，这里的参数就是两条均线的窗口大小，快线是 10 天，慢线是 30 天。

第 17 行到第 20 行，这里有一个初始化函数。在这里，我们生成了两个技术指标，即慢均线和快均线；另外还计算出了均线相互穿越的信号。

从这里可以看出，backtrader 的功能之一，就是提供了常用的指标计算。它们归类在 backtrader.indicators 包下。

next 函数是我们真正实现策略的地方。每个策略都需要定义这个函数，Cerebro 引擎将会调用它。当每次被调用时，Cerebro 会将数据准备好供我们使用。这里的数据就是我们在__init__方法中计算出来的 self.crossover 数组。从代码上看，这里的逻辑似乎是，当我们有持仓、且当前发生快线上穿慢线时买入；否则，就卖出。

不过，这里也给我们留下一些疑问：

1. self.position 这里是当作布尔量使用的
2. 计算 sma 和 crossover 指标时，数据是从哪里来的？
3. self.crossover 是 CrossOver 对象的实例，它为什么能与一个整数进行比较？
4. 买入和卖出为什么没有参数？
5. 手续费是如何处理的？
6. 整个 next 方法中，并没有出现跟时间相关联的任何迹象。尽管我们猜测框架会一个 bar 一个 bar 地往前推进，但这一节究竟是如何发生的，特别是，如果不与时间关联，我们是如何知道当前处理的数据也在同步更新的？

我们先跳过这些问题，待浏览了整个框架之后，再回过头来深入这些细节。

第 32 行，我们生成了回测引擎。它将替我们保管数据、处理时间步进并驱动我们的策略。

第 34 到 39 行，我们通过第三方数据源获取数据，把它喂给 feeds.PandasData，然后将 PandasData 对象，通过 adddata 方法，加入到 cerebo 引擎的管理之下。

除了要给 cerebo 增加数据之外，我们还要给它增加策略（注意是类而不是实例），这是第 43 行所做的事。

第 44 行，我们调用 cerebro.run，让齿轮开始旋转。

最后，当一切结束，我们调用 cerebro 来绘图。我们将得到这样一张图。

![](https://images.jieyu.ai/images/2023/08/lesson19-bt-plot.png)

这个图并不美观，但揭示了一些实用信息。这张图共由 4 行组成。第一行显示了每个时间点上的现金数和总市值。第二行显示了时间点上的真实损益。

第三行显示了每日收盘曲线、成交量、两条移动平均线，更重要的是标识出了买卖点。第四行显示了我们计算出来的信号在不同时间点的取值。注意它与第三行中的买、卖点、第一行中的现金流都是对应的。

仅仅只用了一行代码就显示出这么多有用的信息，backtrader 干得不错。特别是，我们可能关注的三个指标，两条均线、信号线都自动绘制出来了，这对初学者确实比较友好。

这张图还能读出更多的信息。比如，当 Cerebro 创建时，它自动分配了现金给我们。每次交易，似乎是以全仓的式。

## 2. backtrader 语法糖

backtrader 的代码写得很有技巧，它利用 Python 的一些高级特性（比如 metaclass，运算符重载等等）构建了一些独特的概念和操作符，如果不进行说明的话，会导致阅读障碍。因此，在深入到各个部件的细节之前，我们先来讲解一些 backtrader 中的基础概念，以便我们能更好地理解 backtrader 的代码。

### 2.1. 时间线 (Line)

在回测中中访问时间序列有它的特点。比如，不允许偷看未来数据，时间戳一直向前，等等。

时间线是 backtrader 对时间序列进行的一种封装。

!!! tip
    在 backtrader 中，Strategy 和 Indicator 的基类都指向 DataAccessor，而 DataAccessor 的基类又最终指向 LineRoot；而 DataFeed 类则以组合的方式，包含了多个 LineRoot 的派生对象。LineRoot 类是一个接口，定义了运算符重载、迭代等等方法。

    在本课程中，不需要深入到这些细节，但是，了解这些关系，对于我们理解 backtrader 中的一些特殊语法很有帮助。

数据流 (data feeds)、指标和策略都有所谓的“时间线”（line）属性。时间线是由一系列的点组成，比如，数据流可能有 open, high, low, close, volume 等时间线。由 open, high, .. 等时间线构成一个时间线群--lines。

对时间线的索引，backtrader 有自己独特的方式。一般来说，对于类似于 list 这样的对象，我们一般以 0 为起始点的坐标，用-1 来指代最后一个数据。在 backtrader 的时间线语法中，0 恰好代表当前可见的最新的数据（或者说最后一个数据点），-1， -2， ...， -n 则依次代表前一个， 前两个，... 前 n 个数据。

主要的不同之处在于，它使用 0 来指示最后一个数据，而在 python list 中，这原本是用-1 来指示的。

我们使用 cerebro.adddata 把一个个 data feed 加入进来，并且可以在 Strategy 类中，通过 self.datas 来访问该数据流。注意，尽管 data 本身就是一个复数，在 backtrader 中，它仍然使用了 datas 来强调 self.datas 是关于数据流的一个 list，因此，我们可以使用 list 的下标语法来访问每一个数据流。其顺序是由加入的顺序来确定的。

backtrader 提供了一些关于数据引用的快捷方法和默认行为。了解这些行为有助于我们读懂他人的代码。

| 简写            | 未简写                 | 作用域      |
| --------------- | ---------------------- | ----------- |
| xxx.l           | xxx.lines              | any objects |
| xxx.lines_name  | xxx.lines.name         | any objects |
| self.data_name  | self.data.lines.name   | Strategy    |
| self.data1_name | self.data1.lines.name  | Strategy    |
| self.data1      | self.datas[1]          | Strategy    |
| self.data.close | self.data.lines.close  | Strategy    |
| self.data       | self.datas[0]          | Strategy    |
| self.line       | self.lines[0]          | Indicator   |
| self.lineX      | self.lines[X]          | Indicator   |
| self.line_X     | self.lines[X]          | Indicator   |
| self.dataX_Y    | self.datas[X].lines[Y] | Strategy    |

这里显然使用了某些高级的编程技巧，不过不确定为了这份方便，需要付出多少性能代价。

特别指出，如果我们使用 backtrader 自带的一些指标计算方法来计算指标，如果不明确指明将要使用的数据，它会默认使用 self.datas[0] 所引用的数据源。

时间线 (line) 具有 len 和 buflen 两个相似但有区别的函数。buflen 给出的是 Data Feed 已经预加载的 bar 的总数；而 len 给出的是当前已经处理的 bar 的个数。当策略运行完毕后，两者应该具有相同的取值。

line 对象不提供 Python 中的切片语法。相反，如果我们要获取某一段数据，可以通过 get 函数：

```python
# 示例 3
from coursea import *
await init()

class TestStrategy(bt.Strategy):
    def __init__(self):
        self.sma1 = btind.SimpleMovingAverage(period=5)
        self.sma2 = btind.SMA(self.data, period=5)
        self.sma3 = btind.SMA(self.data.close, period=5)

        self.sma4 = btind.SMA(self.datas[0].lines[0], period=5)
        # SELF.SMA4 = BTIND.SMA(SELF.DATAS[0].LINES.CLOSE, PERIOD=5)
        
    def next(self):
        # 提取当前时间点
        print('datetime', self.datas[0].datetime.date(0))
        # 打印当日、昨日、前日的均线
        print('sma1',self.sma1.get(ago=0, size=3))
        print('sma2',self.sma2.get(ago=0, size=3))
        print('sma3',self.sma3.get(ago=0, size=3))
        print('sma4',self.sma4.get(ago=0, size=3))

        # 通过 FEED 名字，而不是下标来访问
        data = self.getdatabyname(code_1)
        print('close', data.close.get(ago=0, size=3))
        

cerebro = bt.Cerebro()
start = datetime.datetime(2023,1,1)
end = datetime.datetime(2023,8, 3)

code_1 = "000001.XSHG"
code_2 = "000300.XSHG"

bars_1 = await Stock.get_bars_in_range(code_1, FrameType.DAY, start = start, end=end)
bars_2 = await Stock.get_bars_in_range(code_2, FrameType.DAY, start = start, end=end)

data_1 = pd.DataFrame(bars_1)
data_1.set_index("frame", inplace = True)

data_2 = pd.DataFrame(bars_2)
data_2.set_index("frame", inplace = True)

datafeed1 = bt.feeds.PandasData(dataname=data_1)
cerebro.adddata(datafeed1, name=code_1)

datafeed2 = bt.feeds.PandasData(dataname=data_2)
cerebro.adddata(datafeed2, name=code_2)

cerebro.addstrategy(TestStrategy)
result = cerebro.run()
```

这段代码输出如下：

```txt
datetime 2023-01-09
sma1 array('d', [nan, nan, 3145.793115234375])
sma2 array('d', [nan, nan, 3145.793115234375])
sma3 array('d', [nan, nan, 3145.793115234375])
sma4 array('d', [nan, nan, 3145.793115234375])
close array('d', [3155.21630859375, 3157.636474609375, 3176.08447265625])

datetime 2023-01-10
sma1 array('d', [nan, 3145.793115234375, 3156.392138671875])
sma2 array('d', [nan, 3145.793115234375, 3156.392138671875])
sma3 array('d', [nan, 3145.793115234375, 3156.392138671875])
sma4 array('d', [nan, 3145.793115234375, 3156.392138671875])
close array('d', [3157.636474609375, 3176.08447265625, 3169.507080078125])

datetime 2023-01-11
sma1 array('d', [3145.793115234375, 3156.392138671875, 3164.056884765625])
sma2 array('d', [3145.793115234375, 3156.392138671875, 3164.056884765625])
sma3 array('d', [3145.793115234375, 3156.392138671875, 3164.056884765625])
sma4 array('d', [3145.793115234375, 3156.392138671875, 3164.056884765625])
close array('d', [3176.08447265625, 3169.507080078125, 3161.840087890625])
...
```

我们打印出来了收盘价序列和指标序列，两相对比，正好反映了指标冷启动时的逐步生成的过程。

这里的 ago 表明我们切片的起始点。size 则是切片的长度。如果不指定 ago，这意味着将从 ago = 0 开始起。如果我们要指定 ago，它应该是用 0 表示当前时间点，-1 表示前一个时间点，依次类推。使用大于 0 的 ago 是没有意义的。

!!! attention
    第 12 行和第 13 行是等价的。即每个时间线 Line，在 lines 中既可以使用下标来访问，也可以使用它们的名字来访问。但是，推荐使用名字来访问，因为在一个 lines 中各个 line 添加顺序不便记忆。<br>第 24 到第 26 行，这里出现了一个新的函数，getdatabyname。当我们通过 cerebro.adddata 加入一个数据流时，可以给它加上一个名字，然后就可以在 next 方法中，通过 getdatabyname 来获取这个数据流，以及它的 lines 对象。

lines 还提供了一种语法，用以在多周期中，临时拓展较短的序列以便与较长的序列对齐。

比如，我们有日线 sma 和周线 sma，如果策略要求我们比较这两条均线，显然 backtrader 无法处理（考虑到 lines 实际上已经与日期时间脱钩了，所以它甚至都无法参考时间来完成这一操作）。关于这种拓展的具体用法，我们将在多周期的 Data Feed 那一节中讲解。

### 2.2. 运算符重载

在 Python 的基本语法中，象"+", "-"， "*", "/"这样的运算符是只能作用于 Number 及其子类型之间的。我们在前面的课程中看到，Numpy 允许我们在一个数组和数字之间进行操作，结果是通过广播 (broadcasting), 让数字作用于数组的每一个元素身上。这里实际上发生了运算符重载，从而使得本身不能参加运算的两个变量之间，可以进行数学运算。当然，这里的数学运算含义已经发生了变化。

比如，下面两个表达式中：

```python
# 示例 4
>>> import numpy as np

>>> a = [3] * 5
>>> b = np.array(a) * 5

>>> print(a, b)
[3, 3, 3, 3, 3] [15 15 15 15 15]
```

这里发生了两种不同的重载。第 4 行通过对 python list 进行"\*"运算符的重载，实现的是 repeat 的语义；第 5 行对 numpy array 进行"\*"的重载，实现的是按元素分别进行乘法运算的语义。

在 backtrader 中，也大量使用了运算符重载。这些重载，使得代码变得简单易读，更符合人类的直觉。在 backtrader 中，最常见的重载有下标，比较运算符（>, ==, <) 和代数运算符 (+, -, *, /) 等。

在前面我们接触过，我们可以这样访问当日的收盘价：

```python
# 示例 5
# 这行代码需要在 STRATEGY.NEXT 方法中运行
def next(self):
    print(type(self.data0), 
          self.data0[0], 
          self.datas[0].lines.close[0])
```
输出结果可能是：

```md
<class 'backtrader.feeds.pandafeed.PandasData'> 3328.39 3328.39
```

这里 self.data0 是一个 PandasData 对象，但我们可以对它进行下标运算，原因是 PandasData 对象进行了下标运算重载，让它返回收盘价数据。

实际上，这里的 self.datas[0].lines.close 仍然不是一个 python 的 List，或者其它任何天然支持下标运算的 python 对象，它是一个 backtrader.linebuffer.LineBuffer。backtrader 对它也进行了下标运算重载。

我们在快速开始一节中，问了这样一个问题，在这段代码中，为什么指标 Crossover 可以与一个数字进行比较？

```python
# 示例 6
class SmaCross(bt.Strategy):
    # LIST OF PARAMETERS WHICH ARE CONFIGURABLE FOR THE STRATEGY
    params = dict(
        pfast=10,  # period for the fast moving average
        pslow=30   # period for the slow moving average
    )

    def __init__(self):
        sma1 = bt.ind.SMA(period=self.p.pfast)  # fast moving average
        sma2 = bt.ind.SMA(period=self.p.pslow)  # slow moving average
        self.crossover = bt.ind.CrossOver(sma1, sma2)  # crossover signal

    def next(self):
        if not self.position:  # not in the market
            # 为什么 CROSSOVER 能与整数进行比较？
            if self.crossover > 0:
                self.buy()
```

如果我们在第 20 行处，打印 self.crossover 的类型的话，我们会得到：

```md
<class 'backtrader.indicators.crossover.CrossOver'>
```

所以这个类重载了">"运算符，让它实际上与 Crossover 中最新一期的数据进行比较，返回的结果是一个 bool 型的标量。但也有返回另一个对象，比如 LinesOperation 的时候，比如，如果我们将第 10 行的 sma1 除以 10，再检查这个新变量的类型，它将是一个`<class 'backtrader.linebuffer.LinesOperation'>`对象。

我们通过下面的代码，对各种重载方式进行一个集中演示：

```python
    # 示例 7
    def __init__(self):
        self.sma = bt.ind.SMA(period=self.p.pfast)

    def next(self):
        # 实际是 SELF.SMA[0] 与 30.0 进行比较，而 SELF.SMA[0] 也是重载，
        # 它实际上是 SELF.SMA.LINES.SMA
        if self.sma[0] > 30.0:
            print('sma is greater than 30.0')

        # 比较 SELF.SMA[0] 与 SELF.DATAS[0].LINES.CLOSE[0]
        if self.sma > self.data.close[0]:
            print('sma is above the close price')

        # SELF.POSITION 是否有值？注意这里仍然不是 PYTHON 的原生语法
        # 否则，下面的代码几乎在任何时候都为 TRUE
        # 因为 SELF.POSITION 对象在生命期都存在
        if self.position:
            print('当前有持仓')
```

但有些操作符、关键词和函数是 Python 不允许重载的，为了增强条件判断的表达能力，在 backtrader 中，对这些符号/关键字也进行了重载：

``` md
    and, or, if, any, all, cmp, max, min, sum, reduce
```

```python
# 示例 8
class TestStrategy(bt.Strategy):
    def __init__(self):
        self.sma5 = btind.SMA(period=5) # 5 日均线
        self.sma10 = btind.SMA(period=10) # 10 日均线
        
        # BT.AND 中所有条件都满足时返回 1；有一个条件不满足就返回 0
        self.And = bt.And(self.data>self.sma5, self.data>self.sma10, self.sma5>self.sma10)
        
        # BT.OR 中有一个条件满足时就返回 1；所有条件都不满足时返回 0
        self.Or = bt.Or(self.data>self.sma5, self.data>self.sma10, self.sma5>self.sma10)
        
        # BT.IF(A, B, C) 如果满足条件 A，就返回 B，否则返回 C
        self.If = bt.If(self.data>self.sma5, -1, 1)
        
        # BT.ALL, 同 BT.AND
        self.All = bt.All(self.data>self.sma5, self.data>self.sma10, self.sma5>self.sma10)
        
        # BT.ANY，同 BT.OR
        self.Any = bt.Any(self.data>self.sma5, self.data>self.sma10, self.sma5>self.sma10)
        
        # BT.MAX，返回同一时刻所有指标中的最大值
        self.Max = bt.Max(self.data, self.sma10, self.sma5)
        
        # BT.MIN，返回同一时刻所有指标中的最小值
        self.Min = bt.Min(self.data, self.sma10, self.sma5)
        
        # BT.SUM，对同一时刻所有指标进行求和
        self.Sum = bt.Sum(self.data, self.sma10, self.sma5)
        
        # BT.CMP(A,B), 如果 A>B ，返回 1；否则返回 -1
        self.Cmp = bt.Cmp(self.data, self.sma5)
        
    def next(self):
        if self.data.datetime.date(0) >= datetime.date(2023, 8, 3):
            print('---------- datetime',self.data.datetime.date(0), '------------------')
            print('close:', self.data[0], 'ma5:', self.sma5[0], 'ma10:', self.sma10[0])
            print('close>ma5',self.data>self.sma5, 'close>ma10',self.data>self.sma10, 'ma5>ma10', self.sma5>self.sma10)
            print('self.And', self.And[0], self.data>self.sma5 and self.data>self.sma10 and self.sma5>self.sma10)
            print('self.Or', self.Or[0], self.data>self.sma5 or self.data>self.sma10 or self.sma5>self.sma10)
            print('self.If', self.If[0], -1 if self.data>self.sma5 else 1)
            print('self.All',self.All[0], self.data>self.sma5 and self.data>self.sma10 and self.sma5>self.sma10)
            print('self.Any', self.Any[0], self.data>self.sma5 or self.data>self.sma10 or self.sma5>self.sma10)
            print('self.Max',self.Max[0], max([self.data[0], self.sma10[0], self.sma5[0]]))
            print('self.Min', self.Min[0], min([self.data[0], self.sma10[0], self.sma5[0]]))
            print('self.Sum', self.Sum[0], sum([self.data[0], self.sma10[0], self.sma5[0]]))
            print('self.Cmp', self.Cmp[0], 1 if self.data>self.sma5 else -1)
        
start = datetime.datetime(2023,1,1)
end = datetime.datetime(2023,8, 3)

code_1 = "000001.XSHG"
bars_1 = await Stock.get_bars_in_range(code_1, FrameType.DAY, start = start, end=end)

data_1 = pd.DataFrame(bars_1)
data_1.set_index("frame", inplace = True)

datafeed1 = bt.feeds.PandasData(dataname=data_1)

cerebro = bt.Cerebro()
cerebro.adddata(datafeed1, name=code_1)

cerebro.addstrategy(TestStrategy)
result = cerebro.run()
```

输出结果是：

![](https://images.jieyu.ai/images/2023/08/lesson19-output-1.png)

!!! tip
    这里的一些语法似曾相识。比如 If 的功能，有点类似于 np.where 和 np.select。如果被操作对象都是纯 Python 对象，我们没有必要使用 bt.If（或者 bt.And, bt.Or 等）；但是，当其中一个对象是 Python 对象，另一个对象是 Lines 对象时，你会发现这些语法糖很好用。

接下来，我们就要深入 backtrader 的各个模块，了解它们如何工作，如何协作，以及我们如何定制它们的行为。

## 3. Data Feeds

在 backtrader 中，我们必须通过 datafeeds 中定义的类来向策略提供数据。backtrader 已经为我们提供了好几种 data feed，比如，基于 csv 的有：

* GenericCSVData
* VisualChartCSVData
* YahooFinanceData
* YahooFinanceCSVData
* BacktraderCSVData

除此之外，还有 PandasData, PandasDirectData, InfluxDB, MT4CSVData, Quandl 等。

!!! Tip
    在大富翁中，行情数据存放在 Influxdb 中。理论上如果使用 backtrader 来作为回测框架时，应该使用 InfluxDB 这个 data feed。不过 backtrader 自带的 InfluxDB feed 在配置上不接受 url 和 token，另外，从源代码上看，它对数据进行了重采样（取均值），这也不适合大富翁数据库。大富翁在建立数据库时，已经进行了优化。同样地，如果您有自己的 Influxdb 数据库，如果直接使用 InfluxDB 这个数据流时，需要查看一下它的源代码，看看它对数据进行的处理是否与您建立数据库的方式。

无论我们使用哪一个 data feed 类，它都基本上具有以下属性：

* dataname 这个属性的具体含义由各个类自行定义。比如，如果我们使用 PandasData，那么我们应该通过 dataname 传入一个包含行情和指标数据的 DataFrame。如果是 csv 类的 data feed，那么很可能应该传入文件位置，等等。这是一个必须传入的参数。
* name 这个属性将在绘图时使用。
* fromdate 用来指定数据流的起始日期。在该日期（时间）之前，即使数据流能够提供数据，也被忽略。
* todate 用来指定数据流的结束日期。在该日期（时间）之后，即使数据流还能提供数据，也被忽略。
* timeframe 数据流的周期类别，可能是 Ticks, Seconds, ... Years 对象中的一个。这些对象由 TimeFrame 来定义。
* compression, session start, session end 这几个属性只用于重采样。

显然，如果数据源来自于 csv 文件，我们还需要指定 csv 文件是否包含表头（header），分隔符是什么等等。这分别用 headers 和 separator 参数来指定。

### 3.1. GenericCSVData

这里着重介绍一下 GenericCSVData。这是一个封装好的 csv data feed，要使用它，我们需要实例化一个对象，并且给前面提到的参数传入正确的值：

```python
# 示例 9 数据准备工作，您也可以使用自己的 CSV 文件
from coursea import *
await init()

start = datetime.date(2023, 1, 4)
end = datetime.date(2023, 8, 3)
bars = await Stock.get_bars_in_range("000001.XSHG", FrameType.DAY, start, end)

df = pd.DataFrame(bars)
df.to_csv('/tmp/test_generic_data_feeds.csv', index=False)
```

我们来查看一下，保存的数据情况：

```bash
!cat /tmp/test_generic_data_feeds.csv |head -5
```

输出结果如下：

```
2023-07-28,3206.74,3280.28,3200.99,3275.93,41106843400.0,436923617400.0,1.0
2023-07-31,3287.21,3322.13,3281.01,3291.04,45903099400.0,511505348400.0,1.0
2023-08-01,3288.76,3305.34,3279.57,3290.95,45989083300.0,441023318600.0,1.0
2023-08-02,3281.86,3290.82,3252.3,3261.69,40534149900.0,388952704000.0,1.0
2023-08-03,3254.57,3280.86,3247.27,3280.46,38469131700.0,389530672000.0,1.0
```

现在，我们就通过 GenericCSVData 来加载数据，并且查看它的一些属性：

```python
# 示例 10
import backtrader
from backtrader.feeds import GenericCSVData

data = GenericCSVData(dataname='/tmp/test_generic_data_feeds.csv', 
                      datetime=0, 
                      openinterest = -1,
                      fromdate=datetime.date(2023, 7, 25),
                      todate=datetime.date(2023, 8, 3),
                      dtformat = "%Y-%m-%d"
                     )

class DummyStrategy(bt.Strategy):
    def __init__(self):
        print("策略的 lines: ", self.lines.getlinealiases())
        
        print("self.datas[0] lines: ", self.datas[0].lines.getlinealiases())
        
        self.sma = bt.indicators.SMA(self.data.close, period=5)
        print("indicators 对象的 lines: ", self.sma.lines.getlinealiases())
        print("\n")

        
    def next(self):
        rec = self.datas[0]
        dt = bt.num2date(rec.datetime[0])
        
        # 这里实际上会转换成 DATETIME.DATETIME, 而不是 DATETIME.DATE
        pre_dt = bt.num2date(rec.datetime[-1])
        pre_close = rec.close[-1]
        
        print(f"前日：{pre_dt:%Y/%m/%d} {pre_close}"
              f" 现在：{dt:%Y/%m/%d} {rec.close[0]}")
    
cerebro = Cerebro()
cerebro.adddata(data, name="000001")
cerebro.addstrategy(DummyStrategy)
_ = cerebro.run()
```

!!! tip
    我们通过 DummyStrategy 的 next 方法来检查我们的数据是否正确加载了。这几乎是最快的检验方式。否则，你得亲自处理许多脏活。

这段代码将输出：

```
策略的 lines:  ('datetime',)
self.datas[0] lines:  ('close', 'low', 'high', 'open', 'volume', 'openinterest', 'datetime')
indicators 对象的 lines:  ('sma',)

前日：2023/07/28 3275.93 现在：2023/07/31 3291.04
前日：2023/07/31 3291.04 现在：2023/08/01 3290.95
前日：2023/08/01 3290.95 现在：2023/08/02 3261.69
前日：2023/08/02 3261.69 现在：2023/08/03 3280.46
```

在这个例子中，我们顺便查看了各个对象的 lines 属性。首先是策略自身的 lines。在我们定义的 DummyStrategy 中，它只包含了一个名为 datetime 的 line；在 self.datas[0]，也就是我们传入的沪指的行情数据 feed 中，包含了 OHLC 等 7 个 line；在 sma 指标中，包含了一个名为 sma 的 line。

这里需要注意几个问题，我们的数据只有日期，没有时间，但 GenericCSVData 默认我们的数据会提供时间，从而导致解析出错。这是为什么在第 10 行，我们通过 dtformat 来指定日期格式的原因。

其次，注意第 26 行中，我们执行了一个由数字到日期的转换。在内部，GenericCSVData 将日期转换成为 epoch 时间来保存了。因此，为了显示正确的时间，我们又把它转换回来，这里使用了 num2date 这个方法。

第 25 行，这里是读取第一个数据流。在我们的例子中，也只存在这样一个数据流。rec.close[0] 是取当前的收盘价。这里我们也还使用了 rec.close[-1] 来获取前一日收盘价。

另外，如果我们的 csv 文件各列不是按照 datetime, open, high, low, close, volume, openinterest 的顺序排列的，我们需要通过列的序号（由 0 开始）来告诉 GenericCSVData，各列的对应情况。这里可以参见第 15 行对 datetime 的指定。

出于调试目的，我们还使用了 fromdt 和 todt 这两个参数，以便不要输出过多的信息。

### 3.2. Pandas Feed

pandas feed 给出了通过 DataFrame 来生成 feed 的方案。考虑许多框架使用 DataFrame 来作为数据结果，这实际上给出了在 backtrader 没有为某种数据源提供某种 feed 时一个补救方案。我们可以先通过其它方法从这些数据源获取一个 DataFrame，然后再转化成为 Pandas Feed。

下面的例子给出了在大富翁量化环境下，如何将 omicron 获取的数据，转化成为 Pandas feed：

```python
# 示例 11
start = datetime.date(2023, 1, 4)
end = datetime.date(2023, 8, 3)
bars = await Stock.get_bars_in_range("000001.XSHG", FrameType.DAY, start, end)

df = pd.DataFrame(bars)
data = feeds.PandasData(dataname=df, 
                        datetime = 'frame',
                        fromdate=datetime.date(2023, 7,28),
                        todate=datetime.date(2023, 8, 3))
cerebro = Cerebro()
cerebro.addstrategy(bt.Strategy)
cerebro.adddata(data)

cerebro.run()
cerebro.plot(style='bar')
```

!!! tip
    backtrader 给出了如何自定义 csv feed 的步骤。实际上，如果确实有这种需要，我们应该考虑通过 pandas 读取 csv，通过得到的这个 dataframe，来生成一个 pandas feed。<br>这里的理由是，csv 的解析实际上会比你想像的要复杂的很多，就连 backtrader 的解析器也做得并不够好。通过使用 pandas 的 csv 解析器，可以在兼容性和性能上同时得到满足。

omicron 返回的 bars 是 numpy structured array，每一列都有自己的名字，可以直接转换成为 DataFrame。我们通过 dataname 把新生成的 DataFrame 传递给 PandasData。这里要注意 PandasData 是如何完成列名字之间的映射的：通过猜测。刚好 omicron 返回的数据，各列名字就是 open, high, low 等，所以就直接对应上了。但是，omicron 返回的数据中，时间列的名字是 frame，而 PandasData 期望的名字是 datetime，所以，我们在第 8 行，指定了 datetime 列的名字是 frame。

这一次的输出与 csv feed 一致，就不再赘述了。

### 3.3. 自定义一个 Feed

自定义 feed 时，我们需要从 AbstractDataBase 派生出自己的子类，并且实现以下方法：

* start 该方法会被 cerebro 调用，我们一般在这里实现打开文件，或者数据库连接。
* stop  当回测结束时被调用。这是我们关闭数据库连接、关闭打开的文件的地方。
* _load 我们需要自定义_load 方法，它读取一个数据记录，并且附加到 self.lines 的各个时间线上，比如：

```python
# 示例 12 从数据源获取 OHLC 各项数据，注意它们都是标量

def _load:
    o, h, l, c, v, oi = ... 

    # 将 OHLC 等分别赋值给当下的数据点
    self.lines.open[0] = o
    self.lines.high[0] = h
    ...

    # 如果一切正常且还有数据，返回 TRUE
    # 如果数据已经读完，则返回 FALSE
    return True
```

如果读者确实遇到需要开发自定义 feed 的场合，可以参考这篇文章 [^custom_feed]。

### 3.4. 增加新的数据列

上面讲的例子，都只使用了 OHLC 和 volume 数据。如果我们的数据包含其它列，比如 PE 值，PB 等，要增加这样的数据，在 backtrader 中非常简单。

我们仍以 PandasData 为例，给 feed 增加一个 money 时间线：

```python
# 示例 13
class MyPandasData(PandasData):
    lines = ('money', )
    params = (('money', 'amount'),)
    
data = MyPandasData(dataname=df, datetime = 'frame',                      
                        fromdate=datetime.date(2023, 7,28),
                        todate=datetime.date(2023, 8, 3))
    
class DummyStrategry(bt.Strategy):
    def next(self):
        rec = self.datas[0]
        dt = bt.num2date(rec.datetime[0])
        
        # 这里实际上会转换成 DATETIME.DATETIME, 而不是 DATETIME.DATE
        pre_dt = bt.num2date(rec.datetime[-1])
        pre_close = rec.close[-1]
        
        money = rec.money[0]
        
        print(f"前日：{pre_dt:%Y/%m/%d} {pre_close}"
              f"现在：{dt:%Y/%m/%d} {rec.close[0]}")
        print(f"money is: {money:.2f}")
        
cerebro = Cerebro()
cerebro.addstrategy(DummyStrategry)
cerebro.adddata(data)

cerebro.run()
```
输出如下：
```
前日：2023/08/03 3280.4599609375 现在：2023/07/28 3275.929931640625
money is: 436923617400.00
前日：2023/07/28 3275.929931640625 现在：2023/07/31 3291.0400390625
money is: 511505348400.00
前日：2023/07/31 3291.0400390625 现在：2023/08/01 3290.949951171875
money is: 441023318600.00
前日：2023/08/01 3290.949951171875 现在：2023/08/02 3261.68994140625
money is: 388952704000.00
前日：2023/08/02 3261.68994140625 现在：2023/08/03 3280.4599609375
money is: 389530672000.00
```

可以看出，我们已经增加了一个 line，叫作 money。而它的数据来自于 bars["amount"]。要增加一个时间线很容易，我们只需要从 PandasData（或者 GenericCSVData）派生一个类，正如第五行那样，然后定义 lines 和 params 属性：

```python
# 示例 14
lines = ('money', )
params = (('money', 'amount'), )
```

!!! attention
    注意 lines 和 params 是元组，意味着在只有一个元素时，我们一定要在该元素之后，加上一个逗号。

这里我们不仅添加了 money 这个时间线，还通过 params 参数，进行了重命名，即将 bars["amount"] 重命名为 money。在这之后，访问 money 属性就如访问 close 一样，请参考第 22 行代码。

## 4. 多周期数据

有些策略会使用到多周期的数据，典型的应用为：

* 使用周线（大周期）数据判断趋势
* 使用日线（小周期）数据判断买卖点

这就需要同时读入多周期数据进行回测，backtrader 内置了对多周期策略回测的支持。它以两种方式支持多周期，其一是读入多周期的数据，其二是对数据进行重采样 (resample)。

在 backtrader 中，第一个被添加的数据将被作为时钟数据，因此需要将小周期数据首先添加到系统中，以使得小粒度的时间都能被遍历到。backtrader 在回测过程中，不会对时间进行重新排序，只能按照整理好的数据顺序依次处理，因此需要将不同周期的数据都做好对齐。

大周期数据的使用会使得策略的最小周期变大。例如：计算 5 日均线，则至少需要前 5 根日 K 线，才能计算出第一个有效值，也就是至少经过 5 日，这里的 5 日就是 backtrader 里所指的最小周期。不同指标可能具有不同的最小周期，单周期回测时，策略的最小周期就是所有指标最小周期的最大值。而多周期回测中，最小周期则变得复杂。通常，要计算出大周期技术指标的有效值，策略的最小周期会变大，也就是回测开始后，要经过更多的 K 线来保证大周期技术指标能够计算出第一个有效值。

这里我们给出一个多周期的例子：

```python
# 示例 15
import backtrader.indicators as btind

class MultiFrameDataStrategy(bt.Strategy):
    params = (
        # PERIOD FOR THE FAST MOVING AVERAGE
        ('fast', 10),
        # PERIOD FOR THE SLOW MOVING AVERAGE
        ('slow', 30),
        # MOVING AVERAGE TO USE
        ('_movav', btind.MovAv.SMA)
    )

    def __init__(self, params=None):
        if params != None:
            for name, val in params.items():
                setattr(self.params, name, val)

        sma_fast = self.p._movav(period=self.p.fast)
        sma_slow = self.p._movav(period=self.p.slow)

        self.inds = dict()
        self.inds['crossup'] = indicators.CrossUp(sma_slow, sma_fast)
        self.inds['week_rsi'] = indicators.RSI_Safe(self.data1)    

    def next(self):
        print(f"Week: {bt.num2date(self.data1.datetime[0]).date()}"
              f"\tDay: {bt.num2date(self.data0.datetime[0]).date()}"
              f"\tWeek-Rsi: {self.inds['week_rsi'][0]:.1f}"
              f"\tDay-Sma-cross: {self.inds['crossup'][0]:.1f}")

code = "000001.XSHG"
daily =  await Stock.get_bars_in_range(code, FrameType.DAY, start, end)
daily = pd.DataFrame(daily)
daily = PandasData(dataname = daily, datetime = 'frame',                      
                   fromdate=datetime.date(2023, 4, 22),
                   todate=datetime.date(2023, 8, 3))

weekly = await Stock.get_bars_in_range(code, FrameType.WEEK, start, end)
weekly = pd.DataFrame(weekly)
weekly = PandasData(dataname = weekly, datetime = 'frame',                      
                   fromdate=datetime.date(2023, 1, 22),
                   todate=datetime.date(2023, 8,3))

cerebro = Cerebro()
params = {'fast': 9, 'slow': 20}
cerebro.addstrategy(MultiFrameDataStrategy, params)

cerebro.adddata(daily)
cerebro.adddata(weekly)

cerebro.run()
```

这段代码演示了以下几个方面的技巧：

1. 如何向 strategy 中添加参数
2. 如何在构造函数和 next 方法中引用不同周期的数据和指标
3. 如何添加多周期数据

我们先来看一下运行结果：

```
Week: 2023-07-14	Day: 2023-07-18	Week-Rsi: 49.0	Day-Sma-cross: 0.0
Week: 2023-07-14	Day: 2023-07-19	Week-Rsi: 49.0	Day-Sma-cross: 0.0
Week: 2023-07-14	Day: 2023-07-20	Week-Rsi: 49.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-21	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-24	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-25	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-26	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-21	Day: 2023-07-27	Week-Rsi: 42.0	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-07-28	Week-Rsi: 53.2	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-07-31	Week-Rsi: 53.2	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-08-01	Week-Rsi: 53.2	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-08-02	Week-Rsi: 53.2	Day-Sma-cross: 0.0
Week: 2023-07-28	Day: 2023-08-03	Week-Rsi: 53.2	Day-Sma-cross: 0.0
```

实际输出要更长一些，这里只节选了一部分。

我们可以看到，在每个步进的周期内，backtrader 向我们正确地传递了当前周期的行情数据。比如，在 2023-08-03 这一天，它传给我们的周线数据是 2023-7-28 这一天的，这是最近的一个已经结束的周期。2023 年 7 月 28 日这一天是周五，当天正好是周线结束日，所以这一天的周线也是 7 月 28 日，这些地方都是正确的。

比较有意思的是 6 月 22 那一周。6 月 22 日是端午，这周的周五是 6 月 23 日，但 A 股当周收盘于 6 月 21 日。我们看到 backtrader 也正确处理了。

第 32 行到第 43 行，这是通过 omicron 获取数据，生成 PandasData 对象。
第 49 到第 50 行，我们先添加小周期的数据 -- 这样 backtrader 才能得到正确的时钟概念 -- 然后添加大周期的数据。

向 strategy 中添加参数是通过第 4 到第 11 行，以及第 13 到第 16 行完成的。首先，我们声明了三个参数，并且给了默认值。然后在构造函数中， 我们通过 setattr 方法来设置这些参数。最后，我们在第 47 行，向 cerebro 添加策略时，传递了 params 参数。

!!! tip
    在引用 params 时，我们使用了 self.p，而不是 self.params，这是 backtrader 实现的另一个快捷访问方式。

在 strategy 中引用不同周期的数据和指标很简单。不同周期的数据是按照由小到大的顺序先后添加的，因此，self.datas[0] 就是日线数据；self.data1 就是周线数据。这里 self.data1 是 self.datas[1] 的快捷访问方式，前面已经介绍过了。

在生成指标后，我们把它保存在 self.inds 字典里，这里并没有什么技巧，只是普通的 Python 方法。值得一提的是，通过 CrossUp 和 RSI_Safe 生成的指标对象，天然支持下标索引访问，并且同样地，0 指向当前最新的数据，-1 指向前一期数据，等等。

!!! tip
    如果遇到 array assignment index out of range 的问题，请检查传入的数据长度是否足于 cover 多周期的各项技术指标计算。当然，即使是单周期，同样可能发生数据长度不够导致的类似问题。

### 4.1. 多周期技术指标比较

有时候我们需要进行多周期的数据或者指标比较。显然，这种比较无法直接进行，因为两个序列的长度不一致，实际上每个元素对应的时间点也不一致。

backtrader 实现了一种称作 line coupling 的机制，使得大周期线上的数据，可以扩展 (spread) 并对齐到小周期。我们可以通过 ()（空括号调用）这样的表示法来对短序列进行拓展：

```python
# 示例 16
class MyStrategy(bt.Strategy):
    params = (
        ('day', 10),
        ('week', 5)
    )

    def __init__(self, params=None):
        if params != None:
            for name, val in params.items():
                setattr(self.params, name, val)

        sma_day = btind.SMA(self.data0, period=self.p.day)
        sma_week = btind.SMA(self.data1, period=self.p.week)

        # 这里是施展魔法的地方
        self.buysig = sma_day > sma_week()

    def next(self):
        print(f"Week: {bt.num2date(self.data1.datetime[0]).date()}"
              f"\tDay: {bt.num2date(self.data0.datetime[0]).date()}"
              f"\tDay-cross-week: {self.buysig[0]:.1f}")
        
            
code = "000001.XSHG"
end = tf.day_shift(datetime.date(2023, 8, 3), 0)
start = tf.day_shift(end, -250)

daily =  await Stock.get_bars_in_range(code, FrameType.DAY, start, end)
daily = pd.DataFrame(daily)
daily = PandasData(dataname = daily, datetime = 'frame',                      
                   fromdate=datetime.date(2023, 1, 22),
                   todate=datetime.date(2023, 8, 3))

weekly = await Stock.get_bars_in_range(code, FrameType.WEEK, start, end)
weekly = pd.DataFrame(weekly)
weekly = PandasData(dataname = weekly, datetime = 'frame',                      
                   fromdate=datetime.date(2023, 1, 22),
                   todate=datetime.date(2023, 8, 3))

cerebro = Cerebro()
cerebro.addstrategy(MyStrategy)

cerebro.adddata(daily)
cerebro.adddata(weekly)

cerebro.run(runonce=False)
```

注意这里第 18 行语法，它使用一个空括号的调用，将 sma1 这个序拓展到与 sma0 一样长。

!!! attention
    注意第 47 行： 我们特别增加了 runonce = False 这个参数。否则，即使进行了拓展（line coupling）, 我们仍将得到 IndexError: array index out of range 错误。

line coupling 的使用很容易，我们也无须关心它是如何实现的，但我们需要知道它具体是如何工作的。让我们将第 13 行、第 14 行的 sma_day， sma_week 的局部变量声明为成员变量，以便将它们带到 next 方法中：

```python
# 示例 17
self.sma_day = btind.SMA(self.data0, peroid = self.p.day)
self.sma_week = btind.SMA(self.data1, peroid = self.p.week)

# 在 NEXT 方法中，我们增加这样一行
print(self.buysig.buflen(), self.sma_week.buflen(), f"{self.sma_week[0]:.1f}", self.buysig[0])
```
这样我们会依次看到以下输出：

```
25 5 3268.7 1.0
26 5 3268.7 1.0
27 5 3268.7 1.0
28 5 3268.7 1.0
29 5 3268.7 1.0
30 6 3262.1 1.0
31 6 3262.1 1.0
32 6 3262.1 1.0
33 6 3262.1 1.0
34 6 3262.1 1.0
35 7 3260.0 1.0
```
这个输出展示了 buysig() 和 self.sma_week 的展开过程：原始数据 self.sma_week 的 buflen 和取值在它自己的周期里保持不变，但在每次 next 调用中，会输出这个不变的值，直到调用 next 的次数足以计入到下一个周期。

## 5. 指标

我们在前面的例子中，已经广泛接触了指标。我们已经知道，通常在策略的__init__函数中定义和计算指标（一次性），然后在 next() 方法中使用它们。

指标本身也是 line 对象，所以，我们在 line 那一节中所讲的引用方法、简写方法都一样适用。

### 5.1. 内置指标库
Indicators 指标模块提供了 140 多个技术分析指标计算函数，大部分指标与 TA-Lib 库里的指标是一致的，各函数的用途、算法、参数、返回的结果等信息可以查阅官网 [^indicators]。

文档对各函数介绍的内容大致分为如下几个部分：

* Alias 函数别名。在我们前面的例子中，计算移动平均线指标，我们分别使用过 MovingAverageSimple 和 SMA，这里的 SMA 就是别名。
* Formula 技术指标的算法说明。它一般给出一个简要说明，以及一些参考网址。这对我们完全掌握这些指标帮助较大。
* Lines 说明函数返回的指标对象中包含哪些 lines，如 MACD 函数返回的指标对象就包含 2 条线：macd 线和 signal 线，可通过 xxxx.lines.macd/xxxx.macd 的形式调用具体的线。请参见我们在示例 10 中，对`self.sma.lines.getlinealiases()`。
* Params 指标函数可以设置的参数，如移动均线 MovingAverageSimple 包含一个参数：period (30)，括号里是该参数的默认值，默认情况下是计算 30 日均值。
* PlotInfo backtrader 指标的一个重要功能就是实现了指标的自动绘图。这里指定绘图的各项参数。

### 5.2. 自定义指标

有时候我们可能需要自定义新的指标。在自定义新的指标时，我们需要继承 bt.Indicator 类，并且设置以下属性：

* lines 比如在 SMA 指标中，它就设置了 lines = ('sma', )，以便后面按名字调用。这部分可参见 sma.py 源码
* params 参见前一节说明
* __init__ 同策略 Strategy 里的 __init__() 类似，对整条 line 进行运算，运算结果也以整条 line 的形式返回；
* next 同策略 Strategy 里的 next() 类似，每个 bar 都会运行一次，在 next() 中是对数据点进行运算；
* once 这个方法只运行一次，但是需要从头到尾循环计算指标

下面，我们就以 ATR 为例，看看如何自定义一个指标类：

```python
from . import Indicator, Max, Min, MovAv

class TrueHigh(Indicator):
    lines = ('truehigh',)

    def __init__(self):
        self.lines.truehigh = Max(self.data.high, self.data.close(-1))
        super(TrueHigh, self).__init__()

class TrueLow(Indicator):
    lines = ('truelow',)

    def __init__(self):
        self.lines.truelow = Min(self.data.low, self.data.close(-1))
        super(TrueLow, self).__init__()

class TrueRange(Indicator):
    alias = ('TR',)

    lines = ('tr',)

    def __init__(self):
        self.lines.tr = TrueHigh(self.data) - TrueLow(self.data)
        super(TrueRange, self).__init__()

class AverageTrueRange(Indicator):
    '''
    Defined by J. Welles Wilder, Jr. in 1978 in his book *"New Concepts in
    Technical Trading Systems"*.

    The idea is to take the close into account to calculate the range if it
    yields a larger range than the daily range (High - Low)

    Formula:
      - SmoothedMovingAverage(TrueRange, period)

    See:
      - http://en.wikipedia.org/wiki/Average_true_range
    '''
    alias = ('ATR',)

    lines = ('atr',)
    params = (('period', 14), ('movav', MovAv.Smoothed))

    def _plotlabel(self):
        plabels = [self.p.period]
        plabels += [self.p.movav] * self.p.notdefault('movav')
        return plabels

    def __init__(self):
        self.lines.atr = self.p.movav(TR(self.data), period=self.p.period)
        super().__init__()
```

这段代码分别由四个类组成，TrueHigh, TrueLow, TrueRange 是辅助类，在 AverageTrueRange 中，我们使用 TR 来引用 TrueRange，这是使用了 TrueRange 中定义的别名。

正如前面所介绍的，我们首先要定义指标的别名（可选），lines 和 params。我们没有去实现它的 next 方法，因为我们完全可以只在__init__中就完成所有的计算。如果遇到无法在__init__中完成计算的情况，那我们就必须实现 next 方法，并且将计算出的指标（应该为对应于当前时间点的一个标量）赋值给 lines[0]/line[0]。

!!! Note
    我们没有定义 plotinfo，但定义了_plotlabel，最终，它将合并到基类的 PlotInfo 对象中去，因为每一个 Indicator，都是一个 LineSeries 对象。

在前面的叙述中，我们没有交代数据从哪里来。也许你已经发现了，它是通过构造函数的第一个参数传入的，并且通过 self.data 来引用。

!!! attention
    在自定义指标时，一定要注意指标的实现必须是冪等的。同一个 bar（比如日线）可能被发送多次，但它们的值可能变化（特别是收盘价）。这样做的目的，是允许重放日内会话。

#### 5.2.1. 最小周期
许多指标都带有冷启动期。系统应该忽略这段时间。但是，有时候 backtrader 无法自动获得这一信息，我们必须手动指定它。我们可以在__init__方法中，调用 addminperoid 来进行声明：

```python
class SimpleMovingAverage1(Indicator):   
    lines = ('sma',)   
    params = (('period', 20),)   

    def __init__(self):   
        self.addminperiod(self.params.period)   

    def next(self):   
        datasum = math.fsum(self.data.get(size=self.p.period))   
        self.lines.sma[0] = datasum / self.p.period
```

[^quick_start]: https://www.backtrader.com/docu/quickstart/quickstart/
[^custom_feed]: https://www.backtrader.com/docu/datafeed-develop-general/datafeed-develop-general/
[^indicators]: https://www.backtrader.com/docu/indautoref/
