---
title: 技术分析实战
---

![100%](https://images.jieyu.ai/images/2023/06/lesson13_outline.png)

我们已经足够熟悉numpy、scipy，甚至还接触了一些sklearn的知识。我们的经验和技巧正在迅速积累。现在，也许是时候接触到一些实战了。

传统的技术分析理论和学派中，涉及到波浪理论，顶底判断，支撑和压力位确定，平台突破等技巧。

## 1. 箱体的检测

在第11章我们讨论了数据的分布问题。如果在一组数据中，如果大部分数据都落在一个比较狭窄的区间内，我们就可以认为，该区间内存在一个箱体。当股价突破箱体上沿时，形成向上突破；当股价突破箱体下沿时，形成向下突破。在突破之前，股价在做箱体震荡。

上述问题可以这样定量来描述：

如果有一个时间序列ts，超过n个数据都落在一定范围p%内，则认为该ts中包含了一个箱体。

### 1.1. 基于统计的方法
实现如下：
```python
import numpy as np
from numpy.typing import ArrayLike
from scipy.stats import rv_histogram

def find_range_v1(ts: ArrayLike, p:float, min_size:int):
    """ 寻找`ts`序列中是否存在长度超过`min_size`，且振幅不超过m%的区间

    Args:
        ts: 时间序列数组
        p: 区间振幅
        min_size: 区间最小长度
    Returns:
        返回一个三元组，分别为区间长度，最低价，最高价。如果不存在，则返回None
    """
    counts, edges = np.histogram(ts, len(ts) // 5, density=False)
    
    # 这里 grids * bins的跨度可能略大于p
    bins = int(math_round(p/(edges[1]/edges[0]-1),0))
    
    # 移动求和，找出最大位置
    range_sum = np.convolve(counts, [1] * bins, mode='valid')
    i = np.argmax(range_sum)
    
    if range_sum[i] >= min_size:
        return range_sum[i], math_round(edges[i], 2), math_round(edges[i + bins], 2)
    
    return None
```
我们首先是对ts做直方图分布。注意这里我们根据ts的长度来决定bins个数。假设ts是一个均匀分布的话，我们这里的做法是要求每个桶可以容纳5个元素。

然后我们根据bins划分的情况，求出每个bins格距，并以此计算出我们需要多少个格子(记为bins)才能覆盖振幅p。

接下来，我们对counts序列进行移动求和。注意np中没有move_sum这样的方法，我们使用np.convolve来代替。由于我们给的卷积核是[1] * bins，所以最终效果是将窗口内所有元素加起来。

然后我们通过argmax找到这个移动和序列中的最大值，这就是在振幅p以内，所能覆盖的最大的区域面积（个数），如果这个数大于等于n，问题得到求解，我们返回个数，最低价和最高价。

我们来测试一下该方法:
```python
import matplotlib.pyplot as plt
from coursea import *
await init()
    
start = datetime.date(2022, 11, 18)
end = datetime.date(2023, 4, 28)

bars = await Stock.get_bars_in_range("002344.XSHE", FrameType.DAY, start=start, end=end)

close = bars["close"]

l, llv, hhv = find_range_v1(close, 0.1, 10)

plt.plot(close)
plt.hlines(y=[llv, hhv], xmin=0, xmax=100)
plt.gca().spines.top.set_visible(False)
plt.gca().spines.right.set_visible(False)
```
在2022年11月18日到2023年4月28日共109个交易日内，其中有84个交易日，股价处在[4.29, 4.71]（振幅9.8%）的区间内。

图形绘制如下：

![50%](https://images.jieyu.ai/images/2023/06/find_range.png)

上述方法中，我们使用的是统计方法，速度很快，鲁棒性也好。但是，它没有照顾时间序列应该有的一些特性，比如，我们取同期上证指数，来检测它是否处于震荡区间。

由于指数的波动一般比较小，所以我们把振幅定在5%。这样绘制的图形如下：

![50%](https://images.jieyu.ai/images/2023/06/xshg_range.png)

它的问题是，在x轴20之前是一段不连续区域，也被包括进来了。这种情况在分析中，可能属于两个箱体。

!!! Note
    我们这里使用了比较简单的统计方法。其它统计方法包括通过KDE或者ECDF来求出pdf函数，pdf函数的局部极小值位置就是将一维数组进行划分的位置。

    更多信息，可以参考这个评论[^kde_method]。

### 1.2. 基于聚类的算法

箱体振荡是股价堆积在一个狭窄区间内，它也可以被描述一维数组的聚类(clustering)问题。

关于二维和更高维数组的聚类，在机器学习中研究得很多，sklearn中有许多模型可以用来做聚类。对于一维数组的聚类，我们可以使用ckwraps库。它是R语言中的CKmeans.1d.dp实现的一个Python封装，Ckmeans.1d.dp实现的是一个kmeans聚类算法。

下面，我们通过一个例子来讨论它的使用方法：

```python
import ckwrap
from typing import Optional
from numpy.typing import ArrayLike

def find_range_v2(ts: ArrayLike, min_size:int=20, p: Optional[float] = None, fall_in_range_ratio: float = 0.95):
    segs = ckwrap.cksegs(ts, len(ts) // min_size)

    clusters = []
    for pos, size in zip(segs.centers, segs.sizes):
        if size < min_size:
            continue

        start, length = int(pos - size // 2 - 1), int(size)
        y = close[start : start + length]
        
        if p is not None:
            m = np.median(y)
            high = m * (1 + p/2)
            low = m * (1 - p/2)
        else:
            m = np.mean(y)
            std = np.std(y)
            high = m + 2 * std
            low = m - 2 * std


        inrange = np.count_nonzero((y <= high) & (y >= low))

        if length >= min_size and inrange > fall_in_range_ratio:
            clusters.append((start, length))
            
    return clusters
        
start = datetime.date(2022, 11, 18)
end = datetime.date(2023, 4, 28)

bars = await Stock.get_bars_in_range("002344.XSHE", FrameType.DAY, start=start, end=end)

close = bars["close"]
bbox = find_range_v2(close)

# 绘制k线图和bbox图
cs = Candlestick(bars)
cs.add_bounding_box(bbox)
cs.plot()
```
这次我们使用聚类方法来实现`find_range`,将其命名为`v2`版本。除了仍然需要`ts`， `min_size`和`p`参数之外，这次还多了一个参数`fall_in_range_ratio`，即在我们划定的区间内，落在振幅区间内的数据点必须超过这个比率，这是为了防止ckwraps在聚类时，强行把某些不合条件的点聚在一起。

第7行代码:
```python
segs = ckwrap.cksegs(ts, len(ts) // min_size)
```
是在一维数组中进行聚类，它要求传入一维数组以及分类的个数。这是一个超参数，不太容易确定。但是，从应用层面来讲，我们总是希望聚类后，每个簇中成员的个数不小于min_size，所以，我们这里就传入`len(ts) // min_size`。

`ckwraps.cksegs`返回每个簇的中心位置（即索引值）和簇的长度。从应用的角度来看，我们需要将它转换为(start, length)，即簇的起始点和簇的长度。

第17到25行，如果调用者指定了区间振幅要求，那么我们将使用该簇的中位数来计算区间最高价和最低价；否则，我们使用均值的2倍标准差来计算最高价和最低价。

为了展示聚类的结果，这一次我们使用k线图加上bounding box的做法。bounding box是目标检测深度学习中，常用的数据标注方法。在`omicron.plotting.candlestick`中，我们已经实现了该方法，所以，要在k线图上叠加bounding box，只需要如第46行一样调用就可以了。

![100%](https://images.jieyu.ai/images/2023/06/cluster_bounding_box.png)

!!! Tip
    箱体检测的结果如何使用？
    
    首先，我们可以把箱体的上沿作为压力位，下沿作为支撑位，来判断是否实现了突破，或者突破失败；我们也可以使用这个结果来作为网格交易的一些依据。
    
    其次如上图，当检测到多个箱体时，如果箱体的中位数在上升，则该股可能有人吸筹；如果箱体的中位数在下降，则该股后面可能风险较大。

## 2. 寻找山峰与波谷

经济活动是有周期的。作为经济活动的晴雨表，证券价格的波动也必然有周期。对具有周期规律的运动，我们通过数字信号处理的方法来寻找它的规律是很自然的想法。

!!! Attention
    寻找山峰(peaks)与波谷(valleys or troughs)的算法都有滞后性，也即，只有在趋势走出来之后，才能够发现我们刚刚经过了一个山峰，或者低谷。但是，这里的讨论仍然有重要的实际意义：

    1. 数据标注。如果我们开发一种机器学习算法来预测山峰与波谷（比如RSI），显然我们需要大量的标注数据。获得大量标注数据最好的方法当然不是人工，而是某种算法。
   
    2. 有一些被证明有效的模式，一旦走出来之后，趋势会延续一段时间，此时我们也需要自动发现这些模式的算法。象W底、M顶这样的模式，在寻找的过程中，是需要有能力找到顶和底的。

### 2.1. scipy中的实现
在scipy.signals包中有好几个用于寻找区域极值[^peak_finding]的方法，比如find_peaks,argreextrema,find_peaks_cwt等。这里我们主要以argreextrema为例来讨论下。

```python
from scipy.signal import argrelextrema

def peaks_and_valleys(ts):
    ma = moving_average(ts, 5)
    peak_indexes = argrelextrema(ma, np.greater)
    peaks = peak_indexes[0]

    # Find valleys(min).
    valley_indexes = argrelextrema(ma, np.less)
    valleys = valley_indexes[0]

    assert abs(len(peaks) - len(valleys)) <= 1

    plt.plot(np.arange(len(bars)), ts, color='c', label='ts')
    plt.plot(np.arange(len(bars)), ma, color='b', label='ma')

    # Plot peaks.
    peak_x = peaks
    peak_y = bars['close'][peak_x]
    plt.plot(peak_x, peak_y, 'gv', label="Peaks")

    # Plot valleys.
    valley_x = valleys
    valley_y = bars['close'][valley_x]
    plt.plot(valley_x, valley_y, 'r^', label="Valleys")
    
    plt.legend()
    
bars = await Stock.get_bars("000001.XSHG", 120, FrameType.DAY, end=datetime.date(2023, 6, 20))

peaks_and_valleys(bars["close"])
```

argrelextrema有两个重要的参数，一是输入数据data，二是comparator。这个参数是一个callable，它接受`x1`,`x2`两个参数，x1和x2都是数组，其中x1是输入数组，x2是向后移动一位后的ts数组。这个callable在接收上述参数后，要求返回逐元素比较x1和x2大小的布尔值。如果我们不想自己去实现这个回调方法，可以简单地使用np.greater或者np.less。

我们需要调用argrelextrema两次。第一次传入np.greater，以找到所有“最高点”的坐标，第二次传入np.less，以找到所有“最低点”的坐标。这里我们给“最高点“和”最低点“加上了引号，是因为，它们只是局部的”最高点“和”最低点“，用数学的语言来说，是极大值和极小值。这也是函数名的来源。

最后，绘制的图形如下：

![50%](https://images.jieyu.ai/images/2023/06/argrelextrema.png?2)

从图中可以看出，结果不是很理想：在1处，我们希望它被标记为底部，但这里却出现了两种信号；在2处，我们不希望它被标记，因为这里只是下跌中继；第3处的情况和第1处是一样的。

能改变argrelextrema行为的地方只有一处，就是第二个参数comparator。如果我们定制一个comparator，让它只在n个窗口以内，当极高点大于最低点m%以上时，才将该极高点标记，然后我们来调节这个参数，看能不能达到理想的结果。

我们尝试下面的方法：
```python
import numpy as np
from numpy.lib.stride_tricks import sliding_window_view

def my_comparator(x, y, n=5, m=0.03):
    result = []
    return [np.max(s) >= np.min(s) * (1 + m) for s in sliding_window_view(x, n)]
```
对于序列[1, 1.01, 1.02, 1.03, 1.04, 1.1, 1.2, 1.2, 1.2]，my_peaks(ts, ts, m = 0.1)将返回[False, False, True, True, True]。

这里连续三个True让我们不太满意，我们需要将连续n个窗口内相同的True合并，只发出一个信号，但是具体是哪个点发，也比较费斟酌。另外一方面，sliding_window_view也并不是numpy建议使用的函数，因为它的性能比较弱。所以，这里给出的my_peak，就作为一个参考，演示应该如何定义和使用comparator。

其它几个函数也各有各的问题。实际上，在证券分析领域，我们需要的函数应该具有这样的属性，它能让我们指定：如果一个点被确定为极高/极低时，它必须比周边的点高出多少百分比。

### 2.2. 第三方库：zigzag
第三方库zigzag比较好地解决了这个问题。
```python
from zigzag import peak_valley_pivots

def peaks_and_valleys(ts, up_thres, down_thres):
    ma = moving_average(ts, 5, padding=False).astype(np.float64)
    
    pvs = peak_valley_pivots(ma, up_thres, down_thres)

    plt.plot(np.arange(len(ma)), ts[4:], color='#49DBF5', label='ts', alpha=0.2)
    plt.plot(np.arange(len(ma)), ma, color='#D77AF5', label='ma')

    # Plot peaks.
    peak_x = np.argwhere(pvs > 0).flatten()
    peak_y = ma[peak_x]
    plt.plot(peak_x, peak_y, 'v', label="Peaks", color='#8D50A1')

    # Plot valleys.
    valley_x = np.argwhere(pvs < 0).flatten()
    valley_y = ma[valley_x]
    plt.plot(valley_x, valley_y, '^', label="Valleys", color="#36A2B5")
    
    plt.gca().spines.top.set_visible(False)
    plt.gca().spines.right.set_visible(False)
    
bars = await Stock.get_bars("000001.XSHG", 120, FrameType.DAY, end=datetime.date(2023, 6, 20))

peaks_and_valleys(bars["close"], 0.015, -0.015)
```

上面的代码中，peak_valley_pivots返回的是一个各元素取值为（-1，0，1）的一维数组，长度同输入数组。注意它的开始和结束元素一定会被标记，这些地方是我们要额外检查的。

然后我们通过argwhere找出大于0的位置，这些地方将是曲线的极高点；小于0的位置，这些地方将是曲线的极低点。有时候我们会把头尾处的标记看成干扰，此时可以将头尾元素置为零。

上面的代码将输出下图：

![50%](https://images.jieyu.ai/images/2023/06/zigzag.png)

显然，这次的结果非常令人满意。不过，这里也有一个问题，那就是如何确定up_thres和down_thres参数。我们希望能找到一个自适应的参数。因为不同的标的，不同的周期级别，我们对波动的看法会完全不一样。比如对30分钟级别的沪指，我们可能希望在0.3%的级别上，就能识别出顶跟底；否则，我们可能面对满屏的波动，却一个顶与底都找不出来。而对一些活跃的个股，在日线或者周线级别，我们也会希望这个值大一点。

回想我们在学习正态分布时得到的一些经验，也许，我们应该以2个标准差作为标准，如果某个点比周边的点高出两个标准差的涨幅，这已经是属于5%的小概率事件了，显然这个点有资格作为一个顶点。

这样，我们的代码需要进行以下改动，在我们调用peak_valley_pivots之前，我们先计算出ts序列的标准差：
```python
pct = ma[1:] / ma[:-1] - 1
std = np.std(pct)
up_thres = 2 * std
down_thres = -2 * std

pvs = peak_valley_pivots(ma, up_thres, down_thres)
```

### 2.3. 如何平滑曲线

在顶底寻找的示例中，我们没有使用收盘价序列，而是使用的移动平均值序列。收盘价序列不能直接使用的原因是，它包含的噪声太多，很容易导致算法陷入局部最优解，从而给出很多无效的极值点。

一维数组的平滑方法有很多，在scipy.signals中的很多filter方法（也包括各种convolve方法）都是，另外，在statsmodels.nonparametric库中，也提供了kernel regression, lowess等方法。

在非金融场景的多数情况下，我们使用`savgol_filter`就可以得到比较好的结果。当然，它的性能会比简单移动平均慢。关于savgol filter的一些讨论，可以参考这篇文章[^savgol]。该方法也被集成在omicron中。

数据平滑（或者说去除噪声）除了性能上的代价之外，还会带来数据和信号失真。所以，考虑到收盘价的移动平均值的金融属性，多数情况下我们使用简单移动平均值就好。

### 2.4. 双顶模式的检测
我们来看顶底检测的一个实际应用：如何检测M顶？

首先，我们定义什么是M顶（又称双头）。在技术分析中，一般把如下的图形看成是M顶[^investopedia]：

1. 股价经过长期上涨后，形成一个顶，随后下跌，形成波谷。此位置称为颈线。
2. 随后股价再次上涨，但并未创新高（或者显著创新高）。
3. 随后股价再次下跌，并且低于前一次形成的颈线。
4. 从视觉上看，走势类似于字母M。

一个典型的M顶走势如下图所示：

![50%](https://images.jieyu.ai/images/2023/06/m_top.png)


这里我们将实现一个比较有技巧的算法，充分利用我们之前学过的知识，并且要求有较强的自适应能力，而无须提供过多参数。

```python
from zigzag import peak_valley_pivots
from sklearn.metrics import max_error
from omicron.talib import moving_average

def find_double_top(ts, win):
    change_rate = ts[1:] / ts[:-1] - 1
    std = np.std(change_rate)
    
    pvs = peak_valley_pivots(ts.astype(np.float64), 2 * std, -2 * std)
    
    if pvs[-1] != -1:
        return None
    
    verts = np.argwhere(pvs !=  0).flatten()
    
    # m头必须有5个点
    if len(verts) < 5:
        return None
    
    verts = verts[-5:]
    
    # 第一个点出现在窗口内
    c1 = (len(ts) - verts[0]) <= win
    
    # 现价低于颈线
    c2 = ts[-1] < ts[verts[-2]]
    
    # 判断是否接近均匀分布。如果拟合值与原序列的极差小于均匀分布的1/2，则认为是均匀分布
    y_hat = np.linspace(verts[0], verts[-1], 5)
    grid = y_hat[1] - y_hat[0]
    c3 = max_error(verts, y_hat) <= grid / 2
    
    if np.all([ c2, c3]):
        return verts
    else:
        return None


start = datetime.date(2023, 4, 20)
end = datetime.date(2023, 6, 19)

code = "002344.XSHE"

bars = await Stock.get_bars_in_range(code, FrameType.DAY, start, end)
close = bars["close"]
plt.plot(close)

ma = moving_average(close, 5)
plt.plot(ma, alpha = 0.2)

x = find_double_top(close, 40)
if x is not None:
    plt.scatter(x, close[x])

```
代码由模式检测函数`find_double_top`和一段演示代码组成。

在`find_double_top`中，我们传入两个输入参数，一个当然就是待检的序列；另一个则是我们希望在多少个周期类检测到这个M头。如果跨度太长，则信号也可能失真。当然，如果这个假设不成立，我们也可以在调用时，把这个值设得跟ts的长度一样大，或者更大都可以。

我们首先通过`peak_valley_pivots`来找出时间序列ts中的顶和底。这里我们使用了自适应参数。

`peak_valley_pivots`返回一个数组，其中元素值为1的位置，表明此处有一个顶点；-1的位置表明此处有一个底；0则表明此处无信号。如果该数组以-1结尾，显然，这并不是我们想要的结果，于是我们返回None。

接下来，我们判断，是否至少有5个顶点，否则将不构成M顶。然后我们判断，第一个底部到现在的距离，因为我们要求在`win`个窗口内检测出来双顶的模式；以及现价是否低于颈线。

接下来的代码判断这个双顶的分布是否基本匀称。如果过于偏向某一侧，也可能不是好的分布，但我们也不能机械地要求所谓完美的形态。这个判断实际上等同于，要求各个顶点之间的距离大致相同，呈现均匀分布。

这里利用到我们上一节课学习到的极差。我们先是在区间内，生成一个均匀分布，然后求原分布与生成分布的极差，如果这个极差小于格距的一半，显然这个双顶在分布上是基本匀称的。

最终，我们的演示代码将输出以下图形：

![50%](https://images.jieyu.ai/images/2023/06/double_top_detected.png)

注意，在这里我们并没有事先对股价进行平滑处理。zigzag及根据标准差来确定阈值的做法，展示了它强大的适应能力，从而使得我们对未经平滑的数据，也能较好地完成检测。

W底的检测与M顶检测类似，只需要peak_valley_pivots返回的信号中，最后一个元素为1（表明当前在向上），并且股价大于颈线即可。

!!! Tip
    顶部头肩顶形态和底部头肩底形态被认为是可靠的反转模式。利用这里的方法，也可以比较轻松地检测出来。

### 2.5. 圆弧底的检测
我们在介绍talib那一章时，提到过一些比较有名的关于模式识别的网站，认为圆弧底（顶）是成功率比较高的反转模式。现在，我们就来讨论如何实现这种模式的检测。

利用我们已经学到的知识，我们至少有两种方法可以检测这个圆弧底。其一是进行二次多项式拟合，在误差可以接受时，可以认为我们检测到了一个圆弧底。由于上一章我们已经使用过二次项拟合方法，所以这个方法，我们留成作业题。

另一个方法就是利用顶底检测。一个圆弧底必须是V字结构，然后我们来给这个V字结构打分，来决定它的有效性。我们先给出实现代码如下：

```python
from zigzag import peak_valley_pivots

def scaled_sigmoid(x, start, end):
    """当`x`落在`[start,end]`区间时，函数值为[0,1]且在该区间有较好的响应灵敏度
    """
    n = np.abs(start - end)

    score = 2/(1 + np.exp(-np.log(40_000)*(x - start - n)/n + np.log(5e-3)))
    return score/2
    
def score_width(verts):
    width = verts[-1] - verts[0]
    return scaled_sigmoid(width, 5, 25)

def score_height(ts, vert_price):
    std = np.std(ts)
    
    high = max(vert_price)
    low = min(vert_price)
    x = (high - low) / std

    return scaled_sigmoid(x, 1, 3)

def score_skew(verts):
    v0, v1, v2 = verts

    # range from 0 - 1
    ss = (v2 - v1) / (v2 - v0)

    # print("ss is:", ss)
    if ss < 0.33:
        return scaled_sigmoid(ss, 0, 0.33)
    else:
        return 1 - scaled_sigmoid(ss, -0.1, 1)

def score_bounce(vert_price):
    high, low = np.max(vert_price), np.min(vert_price)
    now = vert_price[-1]
    
    ss = (now - low)/(high - low)

    if ss < 0.618:
        return scaled_sigmoid(ss, 0, 0.618)
    else:
        return 1 - scaled_sigmoid(ss, 0.5, 1)

def round_bottom(ts):
    change_rate = ts[1:] / ts[:-1] - 1
    std = np.std(change_rate)
    
    pvs = peak_valley_pivots(ts.astype(np.float64), 2 * std, -2 * std)
    
    if pvs[-1] != 1:
        return None
    
    verts = np.argwhere(pvs != 0).flatten()[-3:]
    
    if len(verts) < 3:
        return None
    
    vert_price = ts[verts]

    width = verts[-1] - verts[0]
    skew = (verts[-1]-verts[-2])/width

    sw = score_width(verts)
    sh = score_height(ts, vert_price)
    ss = score_skew(verts)
    sb = score_bounce(vert_price)

    # 如何利用上述四项，给出一个综合评分？我们通过对权重的卷积来实现
    # 权重需要自行调整
    w = np.array([1, 1, 1, 1])
    return np.array([sw, sh, ss, sb]).dot(w) / len(w)
    
end = datetime.date(2023, 4, 13)
code = "000600.XSHE"

bars = await Stock.get_bars_in_range(code, FrameType.DAY, start=start, end=end)
close = bars["close"][-50:]
ma = moving_average(close, 5)
plt.plot(close, alpha=0.4)
plt.plot(ma)

round_bottom(ma)
```

检测圆弧底的逻辑部分比较简单。同样的，我们先找出顶和底，最后一个信号必须为1；顶底个数必须大于等于3；接下来就是评估我们找到的这个结构，有多像一个圆弧底。

在代码中，我们已经结合了给圆弧底打分的逻辑。我们把这圆弧底的宽度、下跌的幅度（以下称为高度）以及分布的对称性（以下称为偏度）和反弹力度纳入了评分标准，然后给出四者相互结合的一个分数。

!!! Attention
    给出恰当的评分函数和权重网络并不是我们这门课的任务。毕竟，这门课的目标是传授量化需要的方法、技能，并不是直接给出能赚钱的策略。对形态检测感兴趣的读者，可以自行研究如何确定打分函数及权重参数。

我们希望所有的score函数都返回归一化的结果，即分数都在[0,1]间。这样我们的调整权重数组就变得简单。

对宽度的打分逻辑比较简单，我们直接使用`scaled_sigmoid`函数，把敏感区间设置在[5,25]即可。

在对高度进行打分时，我们使用振幅与三倍标准差的倍数来衡量。如果这个倍数在一倍以内，很可能这就是一个非常普通的波动，不蕴含任何洗盘的信号，所以我们希望在这种情况下，给出较小的分数；从二倍起，我们希望能给一个较大的分数比如0.4左右，如果倍数达到三倍，我们希望能给到1左右。因此，`scaled_sigmoid`的区间可能在[1, 3]之间比较好。下图显示了`scaled_sigmoid`在此区间的取值：

![50%](https://images.jieyu.ai/images/2023/06/scaled_sigmoid_1_3.png)

在对偏度打分时，我们遵循这样的原则，以最低点为界，如果右侧宽度占总宽度的1/3，可能此时介入时机不早不晚。再早一点，反转趋势还未形成；再晚一点，反弹时间过久，可能多方已用尽了短期头寸，从RSI等指标看，短期出现小幅调整的需要。所以，我们可能更期望这样一个打分函数：

定义 $ss = (v2 - v1)/(v2-v0)$，其中$v0, v1, v2$为圆弧底的三个顶点，从左到右排列。此时$ss$服从$(0,1)$的分布，我们希望它在0.33处达到最大值1，然后随着ss的增长，逐渐减少。我们通过一个分段函数来实现这一点，其结果如下图所示：

![50%](https://images.jieyu.ai/images/2023/07/score_skew.png)

当ss为0.5时，意味着此时左右等距。

`score_skew`并不算完美。有时候反弹的时间够了，但力度不够，这种圆弧底仍然不够理想，此时更像是一种“L”型的反弹：

![50%](https://images.jieyu.ai/images/2023/07/score_bounce_poor.png)

因此，我们还要增加一个score_bounce函数。我们可以用反弹的高度占振幅的百分位来打分，与skew类似，我们猜测当反弹到黄金分割点时，评分应该为最高，随后单调下降。

![50%](https://images.jieyu.ai/images/2023/07/score_bounce_good.png)

最后，`round_bottom`函数在2023年1月5日给标的打出了0.92的高分。随后该标的出现连续多个涨停。

![50%](https://images.jieyu.ai/images/2023/07/round_bottom_detected.png)

!!! attention
    再次说明，如何打分以及权重的调优已经超出本课程的范围。这里给出的参数，也可能只是在个别标的上过拟合的结果（不过仅以2023年6月29日为例，设置分数阈值为0.9，将能检测出像大连重工、华昌化工、大庆华科等形态上非常完美的圆弧底个股）。我们重点掌握这里的思路和使用的数学方法及编程技巧。


## 3. 凹凸性检测
在介绍布林带策略时，我们提出过，如果布林带所有的轨道都朝下，则即使股价触及下轨，也不要买入；反之，如果布林带所有轨道都朝上，即使股价突破上轨，也不要轻易卖出。现在，我们就来讨论，如何检测所谓的“朝上”或者“朝下”。

首先，比较符合直觉的做法，就是对股价，或者某个均线做线性或者二次多项式回归，在拟合残差符合要求的情况下，判断当前处在什么趋势下。对于线性拟合，我们只要看直线斜率大于零即可；对于二次多项式回归，我们还要找出顶点位置，结合顶点位置来判断是上升还是下降趋势。

这里我们介绍一种更简单，泛化能力也不错的凹凸性检测方法，它是从凹凸性本身的几何原理来解释的。

```python
from numpy.typing import ArrayLike
def convex_score(ts: ArrayLike, thresh: float = 1.5e-3) -> float:
    """评估时间序列`ts`的升降性

    如果时间序列中间的点都落在端点连线上方，则该函数为凸函数；反之，则为凹函数。使用点到连线的差值的
    平均值来表明曲线的凹凸性。进一步地，我们将凹凸性引申为升降性，并且对单调上升/下降（即直线)，我们
    使用平均涨跌幅来表明其升降性，从而使得在凹函数、凸函数和直线三种情况下，函数的返回值都能表明
    均线的未来升降趋势。

    Args:
        ts:  时间序列
        n: 用来检测升降性的元素个数。

    Returns:
        返回评估分数，如果大于0，表明为上升曲线，如果小于0，表明为下降曲线。0表明无法评估或者为横盘整理。
    """
    n = len(ts)

    if n < 5:
        return 0

    ts_hat = np.linspace(ts[0], ts[-1], len(ts))

    # 如果点在连线下方，则曲线向上，分数为正
    interleave = ts_hat - ts

    # 当前序列不能再分段处理了
    if np.all(interleave >= 0) or np.all(interleave <= 0):
        score = np.mean(ts_hat[1:-1] / ts[1:-1] - 1)

        if abs(score) < thresh:
            # 弧度不明显，按直线处理
            score = (ts[-1] / ts[0] - 1) / (n - 1)

        return score * 100
    # 存在分段情况，取最后一段
    else:
        _, start, length = find_runs(interleave >= 0)
        if length[-1] == 1:  # 前一段均为负，最后一个为零时，会被单独分为一段，需要合并
            n = length[-2] + 1
            begin = start[-2]
        else:
            n = length[-1]
            begin = start[-1]

        if n >= len(ts) // 2:
            return convex_score(ts[begin:])
        else:
            # 无法识别的情况
            return 0
```
我们首先通过序列的两端作一条直线，我们把这条直线当成拟合性，然后判断残差的mean percentage error(注意与sklearn的mape相区别)。如果这个残差的绝对值小于某个阈值，我们就认为该序列是一条直线，从而改用直线的斜率来给它打分。否则，我们就返回这个mpe对应的分数。

这里我们还判断了一种情况，即序列可能与拟合直线相交，在这种情况下，我们取最后一段来进行判断，这样可以较好地检测到最新的趋势变化。

下面的代码演示了计算均线的convex_score分数的情况：
```python
bars = await Stock.get_bars("002344.XSHE", 60, FrameType.DAY, end=datetime.date(2023, 6, 21))

close = bars["close"]

for win, size in zip((5,), (10,)):
    ma = moving_average(close, win, padding=False)[-size:]
    plt.plot(ma)
    plt.plot([0, len(ma)-1], [ma[0], ma[-1]])
    x = np.arange(0, len(ma))
    
    ymin = np.linspace(ma[0], ma[-1], len(ma))
    plt.vlines(x, ymin, ma[x], alpha = 0.2)
    plt.title(f"convex score of ma{win}: {convex_score(ma):.1f}")
```
输出图形如下：
![50%](https://images.jieyu.ai/images/2023/06/convex_score.png)

从上图可以想到，我们也可以使用求面积的方法来检测圆弧顶/底。

[^kde_method]: 使用KDE来切分一维数组: https://stackoverflow.com/a/11516590/13395693
[^savgol]: savgol filter: https://bartwronski.com/2021/11/03/study-of-smoothing-filters-savitzky-golay-filters/
[^peak_finding]: Peak finding: https://docs.scipy.org/doc/scipy/reference/signal.html#peak-finding

[^investopedia]: M顶（双头）: https://www.investopedia.com/terms/d/double-top-and-bottom.asp

