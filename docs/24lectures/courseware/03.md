---
title: 数据源之 Tushare、JqDataSdk
---
## 1. TUSHARE
Tushare 是一个提供股票、期货、基金等金融数据的 Python 库。要使用 Tushare 获取 A 股数据，您需要首先安装 Tushare 库，并在 Tushare官网注册一个账号以获取 Token，这个 Token 用于验证您的数据访问权限。

在 tushare 中，如果某个调用需要传入交易所代码参数，一般使用 SSE 指代上交所，SZSE 指代深交所，BSE 指代北交所，HKEX 指代港交所。对应地，其股票规范码的后缀则是.SH, .SZ, .BJ 和.HK。

tushare 暂时没有在盘中提供实时数据。它所提供的数据，除了数字货币之外，都是盘后数据。

### 1.1. 在课件环境下安装和设置 token

在课件中安装 tushare，并设置你的 token：

```python
! pip install tushare

%env tushare_token=xxx-yyy
```

注意上述单元格执行完成后，请删除本单元格，以防分享 notebook 时导致 token 泄露。

!!! tip
    您也可以参考[Jupyter Notebook中如何设置环境变量?](http://www.jieyu.ai/blog/2024/01/14/how-to-set-env-in-jupyter-notebook/)这篇文章，来永久性地为所有的notebook设置环境变量。

### 1.2. 股票历史数据
以下是一个简单的示例，用于获取某只股票的历史价格数据：
```python
import tushare as ts
import os

# 在 TUSHARE 官网注册并获取 TOKEN
token = os.environ.get("tushare_token")
ts.set_token(token)

# 初始化 PRO 接口
pro = ts.pro_api()

# 获取某只股票的历史价格数据
df = pro.daily(ts_code='000001.SZ', start_date='20190101', end_date='20220218')

# 打印历史价格数据
print(df[:10])
```
在上面的代码中，我们首先通过调用 ts.set_token() 函数设置 Tushare 的 Token，以验证我们的数据访问权限。然后，我们初始化 Tushare 的 pro 接口，以便进行数据访问。接着，我们调用 pro.daily() 函数，用于获取某只股票（000001.SZ）的历史价格数据，包括起始日期和结束日期。最后，我们将数据打印出来。

这里我们看到了另一种交易所代码表示方式。在 tushare 里，上交所的代码后缀是.SH，深交所的代码后缀是.SZ，北交所的代码后缀是.BJ。tushare 还能提供港交所的数据，它的后缀是.HK。

### 1.3. 证券列表

我们通过 stock_basic 来获取证券列表。
```python
import tushare as ts
import os

# 在 TUSHARE 官网注册并获取 TOKEN
token = os.environ.get("tushare_token")
ts.set_token(token)

# 初始化 PRO 接口
pro = ts.pro_api()

fields = 'ts_code,symbol,name,area,industry,list_date'
df = pro.stock_basic(exchange='', list_status='L', fields = fields)
print(df[:10])
```
输出字段包括了规范代码、简码、名称、地区、行业和上市日期。此外，还支持获取其拼音缩写、市场类型（主板/创业板、科创板）、是否沪深港通标的等字段。

### 1.4. 交易日历
tushare 的交易日历返回的是自然日历，但在日期后面通过 is_open 进行了标注。此外，它还支持按起始日期进行查询：

```python
import tushare as ts
import os

# 在 TUSHARE 官网注册并获取 TOKEN
token = os.environ.get("tushare_token")
ts.set_token(token)

# 初始化 PRO 接口
pro = ts.pro_api()

# 获取某只股票的历史价格数据
pro.trade_cal(exchange='', start_date='20180101', end_date='20250101')
```
上述查询返回了一个自然日历，不过在每一行中，通过 is_open 来显示该日是否为交易日。tushare 这样做，可能在一定程度上减轻调用都后续计算的工作量。不过，关系到交易日历的计算十分复杂，我们必须使用专门的库才行。

tushare 是一个老牌的数据服务提供商。起初完全免费，后来升级到 pro 版本后，开始使用积分制。用户反馈较多的可能也是这个积分制。它的积分体系比较复杂。一个注册的新用户，会有 100 积分，拥有获取股票日线的权限，但如果要获取证券列表，则需要 120 积分。权限粒度过细，会导致用户体验下降。尽管如此，它仍然是一个优秀的、可负担得起的数据源。

## 2. 聚宽本地数据

聚宽 [^joinquant] 是相对于 tushare 更贵的选择，它的价格在逐年攀升，免费试用期 [^free_trial] 也在逐年减少。当前应该是给用户提供了 3 个月的试用期 [^free_trial]。一个合用的授权版本大概是 6999 元每年。不过它的数据质量和服务不错。

按照官方文档，聚宽并不提供实时数据，但目前我们仍能在盘获取粒度低至一分钟的准实时数据。不过，使用者应该尽早排除对这种非官方支持的数据调用的依赖。

聚宽通过 jqdatasdk 向用户提供本地数据 [^jqdata]，这是一个 python 的 SDK。我们通常用以下命令来安装：

```
$ pip install jqdatasdk
```

### 2.1. 在课件环境下安装和设置账号
我们通过以下命令来安装使用 jqdatasdk：

```python
! pip install jqdatasdk

# 以下冒号处分别填入你的账号和密码。运行完成后，请删除本单元格
%env jq_account=...
%env jq_password=...
```

我们在使用它之前，需要登录聚宽的主页申请使用，然后进行认证和获得授权。

```python
import jqdatasdk as jq
import os

account = os.environ.get("jq_account")
password = os.environ.get("jq_password")

jq.auth(account, password)
quota = jq.get_query_count()
print(quota)

jq.logout()
```
如果认证成功，就会打印出 auth success。注意 jqdatasdk 对同时登录的会话数有要求，如果当前使用完成，最好调用 jq.logout 来退出，以释放会话。

我们还要注意在购买聚宽的数据时，它有一个每日使用的 quota 限制，一旦超出这个限制，当日将不能继续使用。

在聚宽中，上交所的代码是.XSHG，深交所的代码是.XSHE。我们可以按照这个规则来拼出股票和指数的规范代码。

### 2.2. 股票历史数据

我们可以使用 get_price 与 get_bars 两个 API[^jq_diff][^pitfalls] 来获得股票的历史数据。这里我们仅以 get_bars 为例来演示其用法：
```python
import jqdatasdk as jq
import os

account = os.environ.get("jq_account")
password = os.environ.get("jq_password")

jq.auth(account, password)
quota = jq.get_query_count()
spare= quota.get('spare')
bars = jq.get_bars("000001.XSHE", 250, unit='1d',
             fields=('date', 'open', 'high', 'low', 'close', 'volume', 'money', 'factor'),
             include_now=False,
             end_dt=None,
             fq_ref_date=None,
             df=False)

print(bars[:10])
quota = jq.get_query_count()
print("消耗的流量为：", spare - quota.get('spare'))
jq.logout()
```
返回的数据将包括记录所属的时间、开盘价、最高价、最低价、收盘价、成交量（以股为单位）、成交金额（以元为单位）和复权因子。上述查询，消耗了 250 条流量。

输入参数中，"000001.XSHE"是证券标的，250 在这里是要获取的记录数，unit 是指记录的时间周期，比如是日线、周线这样日线级别的周期，还是象 1 分钟，30 分钟这样的分钟级别的周期。

如果当天是交易日，我们在盘中获取某支标的的当日行情数据，此时 jqdatasdk 会面临两个选择，是返回截止到当前的日线数据呢，还是返回截止到上一个成交日收盘时的行情数据呢？这就是 include_now 这个参数的意义，它告诉 jqdata_sdk 应该如何返回数据。

end_dt 是指我们请求的数据，将截止到哪一天。如果不传入，则会获取到调用时（如果是非交易时间，则是上一个收盘时间）。

fq_ref_date 告诉 jqdatasdk 如何处理复权。如果不提供这个参数，则返回的数据将不会复权。如果提供了这个日期，则在日期之前，相当于前复权，在日期之后，相当于后复权。这是与其它库不一样的地方。

### 2.3. 证券列表
在聚宽中，获取证券列表的函数是 get_all_securities[^pitfalls]。
```python
import jqdatasdk as jq
import os

account = os.environ.get("jq_account")
password = os.environ.get("jq_password")

jq.auth(account, password)
quota = jq.get_query_count()
spare= quota.get('spare')
bars = jq.get_all_securities()

print(bars[:10])
quota = jq.get_query_count()
print("消耗的流量为：", spare - quota.get('spare'))
jq.logout()
```
查询返回了 5000 多条记录。返回的记录包含了字段中文名称（如平安银行）、缩写简称（如 PAYH）、上市日期、退市日期和证券类型（如 stock, index, futures 等）。返回的记录种类跟账户的权限相关。

### 2.4. 交易日历
我们通过 get_trade_days 这个 API 来获取指定时间范围内的交易日历，使用 get_all_trade_days 这个 API 来获取所有交易日历。
```python
import jqdatasdk as jq
import os

account = os.environ.get("jq_account")
password = os.environ.get("jq_password")

jq.auth(account, password)
quota = jq.get_query_count()
spare= quota.get('spare')
calendar = jq.get_trade_days(count=10)

print(calendar)

calendar = jq.get_all_trade_days()

print(calendar[-10:])
quota = jq.get_query_count()
print("消耗的流量为：", spare - quota.get('spare'))
jq.logout()
```

## 3. BAOSTOCK
Baostock 是一个免费、开源的证券数据平台，大概从 2018 年起开始对外提供服务。它可以提供到 5 分钟级的数据。Baostock 使用上无需注册，但它有会话的概念，仍然需要登录。它只能提供历史数据。从官网来看，提供的数据种类较少。使用稳定性、响应速度等指标上能见到的报道不多。另外，我们也没找到它提供交易日历的相关函数。

Baostock 的证券代码使用前缀式标示法，即采用"证券交易所代码"加"证券简码"的方式来表示。其中，上交所代码为"sh"， 深交所为"sz"。举例来说，中国平安的代码就是"sz.000001"，沪指的代码就是"sh.000001"

我们通过以下命令来安装：
```
$ pip install baostock
```
### 3.1. 股票历史数据
我们可以通过下面的代码来获取历史数据。

```python
import baostock as bs
import pandas as pd

# 登录 BAOSTOCK
lg = bs.login()

# 获取个股行情数据
rs = bs.query_history_k_data_plus("sz.000001", "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST", start_date='2021-01-01', end_date='2021-12-31', frequency="d", adjustflag="3")
print('query_history_k_data_plus respond error_code:' + rs.error_code)
print('query_history_k_data_plus respond  error_msg:' + rs.error_msg)

# 打印结果
data_list = []
while (rs.error_code == '0') & rs.next():
    data_list.append(rs.get_row_data())
result = pd.DataFrame(data_list, columns=rs.fields)
print(result)

# 登出 BAOSTOCK
bs.logout()
```
### 3.2. 证券列表
我们可以通过下面的代码来获取证券列表。
```python
import baostock as bs

# 登录 BAOSTOCK
lg = bs.login()

# 获取股票列表
rs = bs.query_stock_industry()
print('query_stock_industry respond error_code:' + rs.error_code)
print('query_stock_industry respond  error_msg:' + rs.error_msg)

# 打印结果
data_list = []
while (rs.error_code == '0') & rs.next():
    data_list.append(rs.get_row_data())
result = pd.DataFrame(data_list, columns=rs.fields)
print(result)

# 登出 BAOSTOCK
bs.logout()

```

## 4. YFINANCE

yfinance 是一个 Python 库，它可以用来获取 Yahoo Finance 的数据，包括股票、ETF、指数、期货等多种金融产品的历史价格和相关信息。在使用 yfinance 之前，需要先安装该库，可以使用 pip 命令来安装：
```
$ pip install yfinance
```

yfinance 可以用来获取全球市场的数据，包括美国、加拿大、欧洲、亚洲等地的股票和其他金融产品，但是从 2021 年 11 月起，中国大陆用户已不能使用 yfinance。这里介绍它，只是作为一个参考，以防学员需要使用其它地区的金融数据。

下面是使用 yfinance 来获取股价的一个例子：
```python
import yfinance as yf

# 获取某只股票的历史价格数据
msft = yf.Ticker("MSFT")
msft_history = msft.history(period="max")
print(msft_history)
```
上述代码中，我们通过 yf.Ticker 函数指定了要获取的股票，这里以微软公司（MSFT）为例。然后，我们调用 history 方法来获取该股票的历史价格数据，使用 period="max"表示获取该股票的所有历史数据。
