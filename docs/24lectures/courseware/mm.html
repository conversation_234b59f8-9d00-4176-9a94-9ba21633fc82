<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Markmap</title>
<style>
* {
  margin: 0;
  padding: 0;
}
#mindmap {
  display: block;
  width: 100vw;
  height: 100vh;
}
</style>
<link rel="stylesheet" href="https://unpkg.com/markmap-toolbar@0.15.6/dist/style.css">
</head>
<body>
<svg id="mindmap"></svg>
<script src="https://unpkg.com/d3@7.8.5/dist/d3.min.js"></script><script src="https://unpkg.com/markmap-view@0.15.6/dist/browser/index.js"></script><script src="https://unpkg.com/markmap-toolbar@0.15.6/dist/index.js"></script><script>(r => {
                setTimeout(r);
              })(() => {
  const {
    markmap,
    mm
  } = window;
  const {
    el
  } = markmap.Toolbar.create(mm);
  el.setAttribute('style', 'position:absolute;bottom:20px;right:20px');
  document.body.append(el);
})</script><script>((getMarkmap, getOptions, root2, jsonOptions) => {
                const markmap = getMarkmap();
                window.mm = markmap.Markmap.create(
                  "svg#mindmap",
                  (getOptions || markmap.deriveOptions)(jsonOptions),
                  root2
                );
              })(() => window.markmap,null,{"type":"root","depth":0,"content":"","children":[{"type":"paragraph","depth":1,"payload":{"lines":[0,1]},"content":"第1课 导论","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[1,12]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[1,2]},"content":"<a href=\"#1-量化交易策略分类\">1.1. 量化交易策略分类</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[2,3]},"content":"<a href=\"#11-对冲策略\">1.1.1. 对冲策略</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[3,4]},"content":"<a href=\"#12-高频套利\">1.1.2. 高频套利</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[4,5]},"content":"<a href=\"#13-技术分析类策略\">1.1.3. 技术分析类策略</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[5,6]},"content":"<a href=\"#2-本课程的定位\">1.2. 本课程的定位</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[6,7]},"content":"<a href=\"#3-如何学习本课程\">1.3. 如何学习本课程</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[7,8]},"content":"<a href=\"#31-知识储备\">1.3.1. 知识储备</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[8,9]},"content":"<a href=\"#32-在线实验环境\">1.3.2. 在线实验环境</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[9,10]},"content":"<a href=\"#4-小结\">1.4. 小结</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[10,11]},"content":"<a href=\"#5-参考资料\">1.5. 参考资料</a>","children":[]}]},{"type":"paragraph","depth":1,"payload":{"lines":[12,13]},"content":"第2课","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[13,17]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[13,14]},"content":"<a href=\"#1-交易所及证券代码\">2.1. 交易所及证券代码</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[14,15]},"content":"<a href=\"#2-第三方数据源\">2.2. 第三方数据源</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[15,16]},"content":"<a href=\"#21-akshare\">2.2.1. Akshare</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[17,18]},"content":"第3课","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[18,33]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[18,19]},"content":"<a href=\"#1-tushare\">3.1. tushare</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[19,20]},"content":"<a href=\"#11-在课件环境下安装和设置-token\">3.1.1. 在课件环境下安装和设置 token</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[20,21]},"content":"<a href=\"#12-股票历史数据\">3.1.2. 股票历史数据</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[21,22]},"content":"<a href=\"#13-证券列表\">3.1.3. 证券列表</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[22,23]},"content":"<a href=\"#14-交易日历\">3.1.4. 交易日历</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[23,24]},"content":"<a href=\"#2-聚宽本地数据\">3.2. 聚宽本地数据</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[24,25]},"content":"<a href=\"#21-在课件环境下安装和设置账号\">3.2.1. 在课件环境下安装和设置账号</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[25,26]},"content":"<a href=\"#22-股票历史数据\">3.2.2. 股票历史数据</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[26,27]},"content":"<a href=\"#23-证券列表\">3.2.3. 证券列表</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[27,28]},"content":"<a href=\"#24-交易日历\">3.2.4. 交易日历</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[28,29]},"content":"<a href=\"#3-baostock\">3.3. Baostock</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[29,30]},"content":"<a href=\"#31-股票历史数据\">3.3.1. 股票历史数据</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[30,31]},"content":"<a href=\"#32-证券列表\">3.3.2. 证券列表</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[31,32]},"content":"<a href=\"#4-yfinance\">3.4. yfinance</a>","children":[]}]},{"type":"paragraph","depth":1,"payload":{"lines":[33,34]},"content":"第4课","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[34,44]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[34,35]},"content":"<a href=\"#1-omicron\">4.1. Omicron</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[35,36]},"content":"<a href=\"#11-初始化-omicron\">4.1.1. 初始化 omicron</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[36,37]},"content":"<a href=\"#12-实时股票数据\">4.1.2. 实时股票数据</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[37,38]},"content":"<a href=\"#13-股票历史数据\">4.1.3. 股票历史数据</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[38,39]},"content":"<a href=\"#14-证券列表\">4.1.4. 证券列表</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[39,40]},"content":"<a href=\"#15-交易日历\">4.1.5. 交易日历</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[40,41]},"content":"<a href=\"#16-板块数据\">4.1.6. 板块数据</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[41,42]},"content":"<a href=\"#2-通过数据解读-a-股\">4.2. 通过数据解读 A 股</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[42,43]},"content":"<a href=\"#21-投资者人数与市场走势\">4.2.1. 投资者人数与市场走势</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[44,45]},"content":"第6课 小市值策略","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[45,54]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[45,46]},"content":"<a href=\"#1-策略实现\">6.1 策略实现</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[46,47]},"content":"<a href=\"#初始化\">6.初始化</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[47,48]},"content":"<a href=\"#绘图\">6.绘图</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[48,49]},"content":"<a href=\"#策略主体代码\">6.策略主体代码</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[49,50]},"content":"<a href=\"#2-策略优化\">6.2 策略优化</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[50,51]},"content":"<a href=\"#择时优化\">6.择时优化</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[51,52]},"content":"<a href=\"#规则优化\">6.规则优化</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[52,53]},"content":"<a href=\"#参数优化\">6.参数优化</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[54,55]},"content":"第7课 布林带策略","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[55,61]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[55,56]},"content":"<a href=\"#1-新的初始化方案\">7.1. 新的初始化方案</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[56,57]},"content":"<a href=\"#2-基于基类的布林带策略\">7.2. 基于基类的布林带策略</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[57,58]},"content":"<a href=\"#3-策略优化方向讨论\">7.3. 策略优化方向讨论</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[58,59]},"content":"<a href=\"#31-参数优化\">7.3.1. 参数优化</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[59,60]},"content":"<a href=\"#32-趋势判断\">7.3.2. 趋势判断</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[61,62]},"content":"第8课","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[62,66]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[62,63]},"content":"<a href=\"#什么是网格交易\">8.什么是网格交易？</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[63,64]},"content":"<a href=\"#代码实现\">8.代码实现</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[64,65]},"content":"<a href=\"#初始化\">8.初始化</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[66,67]},"content":"第9课 Numpy 和 Pandas","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[67,85]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[67,68]},"content":"<a href=\"#1-numpy\">9.1. Numpy</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[68,69]},"content":"<a href=\"#11-创建数组\">9.1.1. 创建数组</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[69,70]},"content":"<a href=\"#12-查看-inspecting-数组特性\">9.1.2. 查看 (inspecting) 数组特性</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[70,71]},"content":"<a href=\"#13-数组操作\">9.1.3. 数组操作</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[71,72]},"content":"<a href=\"#14-逻辑运算和比较\">9.1.4. 逻辑运算和比较</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[72,73]},"content":"<a href=\"#15-集合运算\">9.1.5. 集合运算</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[73,74]},"content":"<a href=\"#16-数学运算\">9.1.6. 数学运算</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[74,75]},"content":"<a href=\"#17-读取查找和搜索\">9.1.7. 读取、查找和搜索</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[75,76]},"content":"<a href=\"#18-类型转换和-typing-module\">9.1.8. 类型转换和 typing module</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[76,77]},"content":"<a href=\"#19-structured-array\">9.1.9. Structured Array</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[77,78]},"content":"<a href=\"#110-io\">9.1.10. IO</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[78,79]},"content":"<a href=\"#111-量化交易中常用函数示例\">9.1.11. 量化交易中常用函数示例</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[79,80]},"content":"<a href=\"#2-pandas\">9.2. Pandas</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[80,81]},"content":"<a href=\"#21-creation\">9.2.1. creation</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[81,82]},"content":"<a href=\"#22-数据访问\">9.2.2. 数据访问</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[82,83]},"content":"<a href=\"#23-遍历-dataframe\">9.2.3. 遍历 dataframe</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[83,84]},"content":"<a href=\"#3-pandas-vs-numpy\">9.3. pandas vs numpy</a>","children":[]}]},{"type":"paragraph","depth":1,"payload":{"lines":[85,86]},"content":"第10课 Ta-Lib","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[86,104]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[86,87]},"content":"<a href=\"#1-安装talib\">10.1. 安装talib</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[87,88]},"content":"<a href=\"#11-原生库的安装\">10.1.1. 原生库的安装</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[88,89]},"content":"<a href=\"#12-安装python-wrapper\">10.1.2. 安装python wrapper</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[89,90]},"content":"<a href=\"#2-ta-lib概览\">10.2. ta-lib概览</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[90,91]},"content":"<a href=\"#21-关于帮助文档\">10.2.1. 关于帮助文档</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[91,92]},"content":"<a href=\"#22-两类接口\">10.2.2. 两类接口</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[92,93]},"content":"<a href=\"#23-方法概览\">10.2.3. 方法概览</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[93,94]},"content":"<a href=\"#3-常用指标函数\">10.3. 常用指标函数</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[94,95]},"content":"<a href=\"#31-atr\">10.3.1. ATR</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[95,96]},"content":"<a href=\"#32-移动平均线\">10.3.2. 移动平均线</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[96,97]},"content":"<a href=\"#33-布林带\">10.3.3. 布林带</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[97,98]},"content":"<a href=\"#34-macd\">10.3.4. MACD</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[98,99]},"content":"<a href=\"#35-rsi\">10.3.5. RSI</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[99,100]},"content":"<a href=\"#36-obv-on-balance-volume\">10.3.6. OBV （on-balance volume)</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[100,101]},"content":"<a href=\"#4-模式识别函数\">10.4. 模式识别函数</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[101,102]},"content":"<a href=\"#41-cdl3linestrike\">10.4.1. CDL3LINESTRIKE</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[102,103]},"content":"<a href=\"#42-cdl3whitesoldiers\">10.4.2. CDL3WHITESOLDIERS</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[104,105]},"content":"第11课 Python与数据科学（1）","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[105,112]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[105,106]},"content":"<a href=\"#1-考察数据分布\">11.1. 考察数据分布</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[106,107]},"content":"<a href=\"#11-寻找数据的中心\">11.1.1. 寻找数据的中心</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[107,108]},"content":"<a href=\"#12-量化数据的分散程度\">11.1.2. 量化数据的分散程度</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[108,109]},"content":"<a href=\"#13-数据的分布形态\">11.1.3. 数据的分布形态</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[109,110]},"content":"<a href=\"#14-中心矩的概念\">11.1.4. 中心矩的概念</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[110,111]},"content":"<a href=\"#15-偏度峰度在投资中的解释与应用\">11.1.5. 偏度、峰度在投资中的解释与应用</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[112,113]},"content":"第12课 数据分析与Python实现（2）","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[113,128]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[113,114]},"content":"<a href=\"#1-统计推断方法\">12.1. 统计推断方法</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[114,115]},"content":"<a href=\"#11-分位图\">12.1.1. 分位图</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[115,116]},"content":"<a href=\"#13-假设检验方法\">12.1.3. 假设检验方法</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[116,117]},"content":"<a href=\"#2-拟合回归和残差\">12.2. 拟合、回归和残差</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[117,118]},"content":"<a href=\"#21-残差及其度量\">12.2.1. 残差及其度量</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[118,119]},"content":"<a href=\"#22-回归分析\">12.2.2. 回归分析</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[119,120]},"content":"<a href=\"#3-相关性\">12.3. 相关性</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[120,121]},"content":"<a href=\"#31-协方差和相关系数\">12.3.1. 协方差和相关系数</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[121,122]},"content":"<a href=\"#32-皮尔逊相关性和斯皮尔曼相关性\">12.3.2. 皮尔逊相关性和斯皮尔曼相关性</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[122,123]},"content":"<a href=\"#33-相关性分析示例\">12.3.3. 相关性分析示例</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[123,124]},"content":"<a href=\"#4-距离和相似性\">12.4. 距离和相似性</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[124,125]},"content":"<a href=\"#41-常见距离定义列举\">12.4.1. 常见距离定义列举</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[125,126]},"content":"<a href=\"#42-如何计算距离\">12.4.2. 如何计算距离</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[126,127]},"content":"<a href=\"#5-归一化\">12.5. 归一化</a>","children":[]}]},{"type":"paragraph","depth":1,"payload":{"lines":[128,129]},"content":"第13课 技术分析实战","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[129,140]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[129,130]},"content":"<a href=\"#1-箱体的检测\">13.1. 箱体的检测</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[130,131]},"content":"<a href=\"#11-基于统计的方法\">13.1.1. 基于统计的方法</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[131,132]},"content":"<a href=\"#12-基于聚类的算法\">13.1.2. 基于聚类的算法</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[132,133]},"content":"<a href=\"#2-寻找山峰与波谷\">13.2. 寻找山峰与波谷</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[133,134]},"content":"<a href=\"#21-scipy中的实现\">13.2.1. scipy中的实现</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[134,135]},"content":"<a href=\"#22-第三方库zigzag\">13.2.2. 第三方库：zigzag</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[135,136]},"content":"<a href=\"#23-如何平滑曲线\">13.2.3. 如何平滑曲线</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[136,137]},"content":"<a href=\"#24-双顶模式的检测\">13.2.4. 双顶模式的检测</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[137,138]},"content":"<a href=\"#25-圆弧底的检测\">13.2.5. 圆弧底的检测</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[138,139]},"content":"<a href=\"#3-凹凸性检测\">13.3. 凹凸性检测</a>","children":[]}]},{"type":"paragraph","depth":1,"payload":{"lines":[140,141]},"content":"第14课 第14课 因子分析","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[141,152]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[141,142]},"content":"<a href=\"#1-因子分类\">14.1. 因子分类</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[142,143]},"content":"<a href=\"#2-因子分析\">14.2. 因子分析</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[143,144]},"content":"<a href=\"#21-预处理\">14.2.1. 预处理</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[144,145]},"content":"<a href=\"#3-单因子测试\">14.3. 单因子测试</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[145,146]},"content":"<a href=\"#31-回归法\">14.3.1. 回归法</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[146,147]},"content":"<a href=\"#32-ic分析法\">14.3.2. IC分析法</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[147,148]},"content":"<a href=\"#33-分层回测法\">14.3.3. 分层回测法</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[148,149]},"content":"<a href=\"#代码实现\">14.代码实现</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[149,150]},"content":"<a href=\"#34-三种方法的区别与联系huatai\">14.3.4. 三种方法的区别与联系[14.^huatai]</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[150,151]},"content":"<a href=\"#因子评价体系fangzheng\">14.因子评价体系[14.^fangzheng]</a>","children":[]}]},{"type":"paragraph","depth":1,"payload":{"lines":[152,153]},"content":"第15课 第15课 Alphalens及其它","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[153,169]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[153,154]},"content":"<a href=\"#1-alphalens\">15.1. Alphalens</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[154,155]},"content":"<a href=\"#11-alphalens调用流程\">15.1.1. Alphalens调用流程</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[155,156]},"content":"<a href=\"#12-数据预处理\">15.1.2. 数据预处理</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[156,157]},"content":"<a href=\"#13-因子分析\">15.1.3. 因子分析</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[157,158]},"content":"<a href=\"#14-alphalens常见错误和警告\">15.1.4. Alphalens常见错误和警告</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[158,159]},"content":"<a href=\"#2-jqfactor和jqfactor-analyzer\">15.2. JQFactor和jqfactor-analyzer</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[159,160]},"content":"<a href=\"#3-sympy\">15.3. sympy</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[160,161]},"content":"<a href=\"#4-statistics\">15.4. statistics</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[161,162]},"content":"<a href=\"#5-statsmodels\">15.5. statsmodels</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[162,163]},"content":"<a href=\"#51-ols普通最小二乘法估计\">15.5.1. OLS（普通最小二乘法）估计</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[163,164]},"content":"<a href=\"#52-比较ols与rlm\">15.5.2. 比较OLS与RLM</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[164,165]},"content":"<a href=\"#53-arima模型与时间序列预测\">15.5.3. ARIMA模型与时间序列预测</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[165,166]},"content":"<a href=\"#6-zipline\">15.6. zipline</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[166,167]},"content":"<a href=\"#7-pyfolio\">15.7. pyfolio</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[167,168]},"content":"<a href=\"#8-ta\">15.8. ta</a>","children":[]}]},{"type":"paragraph","depth":1,"payload":{"lines":[169,170]},"content":"第16课 Matplotlib","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[170,181]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[170,171]},"content":"<a href=\"#matplot-简介\">16.matplot 简介</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[171,172]},"content":"<a href=\"#图是如何构成的\">16.图是如何构成的</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[172,173]},"content":"<a href=\"#最顶层的概念\">16.最顶层的概念</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[173,174]},"content":"<a href=\"#pyplot-figure与axes之间的关系\">16.pyplot, Figure与Axes之间的关系</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[174,175]},"content":"<a href=\"#layout\">16.layout</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[175,176]},"content":"<a href=\"#figure-anatomy\">16.Figure Anatomy</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[176,177]},"content":"<a href=\"#高频使用对象\">16.高频使用对象</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[177,178]},"content":"<a href=\"#axis\">16.Axis</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[178,179]},"content":"<a href=\"#文本和中文\">16.文本和中文</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[179,180]},"content":"<a href=\"#样式和颜色\">16.样式和颜色</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[181,182]},"content":"第17课 Plotly","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[182,205]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[182,183]},"content":"<a href=\"#1-plotly-中的基本概念\">17.1. Plotly 中的基本概念</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[183,184]},"content":"<a href=\"#2-plotly-模块结构\">17.2. Plotly 模块结构</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[184,185]},"content":"<a href=\"#21-plotly-express\">17.2.1. Plotly Express</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[185,186]},"content":"<a href=\"#22-graph-objects\">17.2.2. Graph Objects</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[186,187]},"content":"<a href=\"#23-其它\">17.2.3. 其它</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[187,188]},"content":"<a href=\"#3-比较-plotly-express-与-gofigure\">17.3. 比较 plotly express 与 go.Figure</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[188,189]},"content":"<a href=\"#4-plotly-股票分析图绘制\">17.4. Plotly 股票分析图绘制</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[189,190]},"content":"<a href=\"#41-k-线图绘制\">17.4.1. K 线图绘制</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[190,191]},"content":"<a href=\"#42-叠加技术指标\">17.4.2. 叠加技术指标</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[191,192]},"content":"<a href=\"#43-子图\">17.4.3. 子图</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[192,193]},"content":"<a href=\"#44-显示区域\">17.4.4. 显示区域</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[193,194]},"content":"<a href=\"#45-交互式提示\">17.4.5. 交互式提示</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[194,195]},"content":"<a href=\"#5-色彩\">17.5. 色彩</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[195,196]},"content":"<a href=\"#51-离散色彩序列\">17.5.1. 离散色彩序列</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[196,197]},"content":"<a href=\"#52-连续色阶\">17.5.2. 连续色阶</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[197,198]},"content":"<a href=\"#6-主题和模板\">17.6. 主题和模板</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[198,199]},"content":"<a href=\"#7-dash-简介\">17.7. Dash 简介</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[199,200]},"content":"<a href=\"#71-hellow-world\">17.7.1. Hellow World</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[200,201]},"content":"<a href=\"#72-连接到数据\">17.7.2. 连接到数据</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[201,202]},"content":"<a href=\"#73-增加交互式控件\">17.7.3. 增加交互式控件</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[202,203]},"content":"<a href=\"#74-美化应用程序\">17.7.4. 美化应用程序</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[203,204]},"content":"<a href=\"#75-深入dash\">17.7.5. 深入Dash</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[205,206]},"content":"第18课 Seaborn 与 PyEcharts","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[206,217]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[206,207]},"content":"<a href=\"#1-seaborn\">18.1. Seaborn</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[207,208]},"content":"<a href=\"#11-seaborn-绘图概览\">18.1.1. Seaborn 绘图概览</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[208,209]},"content":"<a href=\"#12-主题\">18.1.2. 主题</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[209,210]},"content":"<a href=\"#13-调色板的使用\">18.1.3. 调色板的使用</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[210,211]},"content":"<a href=\"#2-pyecharts\">18.2. PyEcharts</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[211,212]},"content":"<a href=\"#21-在-notebookjupyterlab-中运行\">18.2.1. 在 Notebook/Jupyterlab 中运行</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[212,213]},"content":"<a href=\"#22-调用习惯\">18.2.2. 调用习惯</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[213,214]},"content":"<a href=\"#23-使用选项\">18.2.3. 使用选项</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[214,215]},"content":"<a href=\"#24-子图和布局\">18.2.4. 子图和布局</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[215,216]},"content":"<a href=\"#3-关于颜色和美学\">18.3. 关于颜色和美学</a>","children":[]}]},{"type":"paragraph","depth":1,"payload":{"lines":[217,218]},"content":"第19课 backtrader 回测框架","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[218,233]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[218,219]},"content":"<a href=\"#1-快速开始\">19.1. 快速开始</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[219,220]},"content":"<a href=\"#2-backtrader-语法糖\">19.2. backtrader 语法糖</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[220,221]},"content":"<a href=\"#21-时间线-line\">19.2.1. 时间线 (Line)</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[221,222]},"content":"<a href=\"#22-运算符重载\">19.2.2. 运算符重载</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[222,223]},"content":"<a href=\"#3-data-feeds\">19.3. Data Feeds</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[223,224]},"content":"<a href=\"#31-genericcsvdata\">19.3.1. GenericCSVData</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[224,225]},"content":"<a href=\"#32-pandas-feed\">19.3.2. Pandas Feed</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[225,226]},"content":"<a href=\"#33-自定义一个-feed\">19.3.3. 自定义一个 Feed</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[226,227]},"content":"<a href=\"#34-增加新的数据列\">19.3.4. 增加新的数据列</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[227,228]},"content":"<a href=\"#4-多周期数据\">19.4. 多周期数据</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[228,229]},"content":"<a href=\"#41-多周期技术指标比较\">19.4.1. 多周期技术指标比较</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[229,230]},"content":"<a href=\"#5-指标\">19.5. 指标</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[230,231]},"content":"<a href=\"#51-内置指标库\">19.5.1. 内置指标库</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[231,232]},"content":"<a href=\"#52-自定义指标\">19.5.2. 自定义指标</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[233,234]},"content":"第20课 backtrader回测框架(2)","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[234,257]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[234,235]},"content":"<a href=\"#1-cerebro\">20.1. Cerebro</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[235,236]},"content":"<a href=\"#11-增加记录器日志\">20.1.1. 增加记录器（日志）</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[236,237]},"content":"<a href=\"#12-增加观察者\">20.1.2. 增加观察者</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[237,238]},"content":"<a href=\"#13-执行与绘图\">20.1.3. 执行与绘图</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[238,239]},"content":"<a href=\"#2-order\">20.2. Order</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[239,240]},"content":"<a href=\"#21-notify_order\">20.2.1. notify_order</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[240,241]},"content":"<a href=\"#3-交易代理\">20.3. 交易代理</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[241,242]},"content":"<a href=\"#31-资产与持仓查询\">20.3.1. 资产与持仓查询</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[242,243]},"content":"<a href=\"#32-成交量限制\">20.3.2. 成交量限制</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[243,244]},"content":"<a href=\"#33-交易时机---cheat-on-open\">20.3.3. 交易时机 - Cheat-On-Open</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[244,245]},"content":"<a href=\"#34-交易时机---cheat-on-close\">20.3.4. 交易时机 - Cheat-on-Close</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[245,246]},"content":"<a href=\"#35-交易函数\">20.3.5. 交易函数</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[246,247]},"content":"<a href=\"#36-组合交易\">20.3.6. 组合交易</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[247,248]},"content":"<a href=\"#37-oco-订单\">20.3.7. OCO 订单</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[248,249]},"content":"<a href=\"#38-滑点交易费用\">20.3.8. 滑点、交易费用</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[249,250]},"content":"<a href=\"#39-交易费用\">20.3.9. 交易费用</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[250,251]},"content":"<a href=\"#4-可视化\">20.4. 可视化</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[251,252]},"content":"<a href=\"#41-观察器\">20.4.1. 观察器</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[252,253]},"content":"<a href=\"#42-定制绘图\">20.4.2. 定制绘图</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[253,254]},"content":"<a href=\"#43-收集回测数据\">20.4.3. 收集回测数据</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[254,255]},"content":"<a href=\"#5-优化\">20.5. 优化</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[255,256]},"content":"<a href=\"#6-小结\">20.6. 小结</a>","children":[]}]},{"type":"paragraph","depth":1,"payload":{"lines":[257,258]},"content":"第21课 第 15 课","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[258,282]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[258,259]},"content":"<a href=\"#1-回报率\">21.1. 回报率</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[259,260]},"content":"<a href=\"#11-简单回报率\">21.1.1. 简单回报率</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[260,261]},"content":"<a href=\"#12-对数回报率\">21.1.2. 对数回报率</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[261,262]},"content":"<a href=\"#13-cumulative-returns\">21.1.3. Cumulative Returns</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[262,263]},"content":"<a href=\"#14-aggregate-returns\">21.1.4. Aggregate Returns</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[263,264]},"content":"<a href=\"#15-annual-return\">21.1.5. Annual Return</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[264,265]},"content":"<a href=\"#2-风险调整收益率\">21.2. 风险调整收益率</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[265,266]},"content":"<a href=\"#21-sharpe-ratio\">21.2.1. sharpe ratio</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[266,267]},"content":"<a href=\"#22-sharpe-比率与资产曲线的关系\">21.2.2. sharpe 比率与资产曲线的关系</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[267,268]},"content":"<a href=\"#23-sortino-指标\">21.2.3. sortino 指标</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[268,269]},"content":"<a href=\"#24-max-drawdown-最大回撤\">21.2.4. Max DrawDown （最大回撤）</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[269,270]},"content":"<a href=\"#25-sharpe-与-max-drawdown-的关系\">21.2.5. Sharpe 与 max drawdown 的关系</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[270,271]},"content":"<a href=\"#26-年化波动率\">21.2.6. 年化波动率</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[271,272]},"content":"<a href=\"#27-calmar-ratio\">21.2.7. Calmar Ratio</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[272,273]},"content":"<a href=\"#28-omega-ratio\">21.2.8. Omega Ratio</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[273,274]},"content":"<a href=\"#3-基准对照类指标\">21.3. 基准对照类指标</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[274,275]},"content":"<a href=\"#31-information-ratio\">21.3.1. information ratio</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[275,276]},"content":"<a href=\"#32-alphabeta\">21.3.2. alpha/beta</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[276,277]},"content":"<a href=\"#4-策略评估的可视化\">21.4. 策略评估的可视化</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[277,278]},"content":"<a href=\"#41-metrics\">21.4.1. Metrics</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[278,279]},"content":"<a href=\"#42-plots\">21.4.2. plots</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[279,280]},"content":"<a href=\"#43-basic-和-full\">21.4.3. basic 和 full</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[280,281]},"content":"<a href=\"#44-html\">21.4.4. html</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[282,283]},"content":"第22课 回测陷阱","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[283,312]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[283,284]},"content":"<a href=\"#1-幸存者偏差\">22.1. 幸存者偏差</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[284,285]},"content":"<a href=\"#2-look-ahead-bias\">22.2. Look-ahead bias</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[285,286]},"content":"<a href=\"#21-引用错误\">22.2.1. 引用错误</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[286,287]},"content":"<a href=\"#22-偷价\">22.2.2. 偷价</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[287,288]},"content":"<a href=\"#23-复权引起的前视偏差\">22.2.3. 复权引起的前视偏差</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[288,289]},"content":"<a href=\"#24-pit数据\">22.2.4. PIT数据</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[289,290]},"content":"<a href=\"#3-复权引起的问题\">22.3. 复权引起的问题</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[290,291]},"content":"<a href=\"#31-使用复权数据的必要性\">22.3.1. 使用复权数据的必要性</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[291,292]},"content":"<a href=\"#32-后复权的问题\">22.3.2. 后复权的问题</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[292,293]},"content":"<a href=\"#33-复权相关的其它问题\">22.3.3. 复权相关的其它问题</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[293,294]},"content":"<a href=\"#4-交易规则\">22.4. 交易规则</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[294,295]},"content":"<a href=\"#41-t1交易\">22.4.1. T+1交易</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[295,296]},"content":"<a href=\"#42-涨跌限制\">22.4.2. 涨、跌限制</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[296,297]},"content":"<a href=\"#5-过度拟合\">22.5. 过度拟合</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[297,298]},"content":"<a href=\"#6-回测时长\">22.6. 回测时长</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[298,299]},"content":"<a href=\"#7-回测与实盘的差异\">22.7. 回测与实盘的差异</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[299,300]},"content":"<a href=\"#71-信号闪烁\">22.7.1. 信号闪烁</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[300,301]},"content":"<a href=\"#72-冲击成本\">22.7.2. 冲击成本</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[301,302]},"content":"<a href=\"#73-不可能成交的价格\">22.7.3. 不可能成交的价格</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[302,303]},"content":"<a href=\"#74-撮合问题\">22.7.4. 撮合问题</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[303,304]},"content":"<a href=\"#1-回测功能简介\">22.8 大富翁回测功能简介</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[304,305]},"content":"<a href=\"#11-架构和风格\">22.8.1. 架构和风格</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[305,306]},"content":"<a href=\"#12-策略框架\">22.8.2. 策略框架</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[306,307]},"content":"<a href=\"#13-数据和数据格式\">22.8.3. 数据和数据格式</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[307,308]},"content":"<a href=\"#14-跨周期数据\">22.8.4. 跨周期数据</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[308,309]},"content":"<a href=\"#15-驱动模式与性能\">22.8.5. 驱动模式与性能</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[309,310]},"content":"<a href=\"#16-回测报告\">22.8.6. 回测报告</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[310,311]},"content":"<a href=\"#2-一个完整的策略示例\">22.8.7. 一个完整的策略示例</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[311,312]},"content":"<a href=\"#3-参数优化\">22.8.8. 参数优化</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[314,315]},"content":"第23课 实盘交易接口 (1)","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[315,330]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[315,316]},"content":"<a href=\"#1-easytrader\">23.1. easytrader</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[316,317]},"content":"<a href=\"#11-安装\">23.1.1. 安装</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[317,318]},"content":"<a href=\"#12-生命期\">23.1.2. 生命期</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[318,319]},"content":"<a href=\"#13-服务器模式\">23.1.3. 服务器模式</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[319,320]},"content":"<a href=\"#14-自动跟单\">23.1.4. 自动跟单</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[320,321]},"content":"<a href=\"#2-东方财富-emc-智能交易终端\">23.2. 东方财富 EMC 智能交易终端</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[321,322]},"content":"<a href=\"#21-安装\">23.2.1. 安装</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[322,323]},"content":"<a href=\"#22-运行和维护\">23.2.2. 运行和维护</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[323,324]},"content":"<a href=\"#23-故障排除与帮助\">23.2.3. 故障排除与帮助</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[324,325]},"content":"<a href=\"#24-撮合配置规则\">23.2.4. 撮合配置规则</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[325,326]},"content":"<a href=\"#3-trader-gm-adaptor\">23.3. Trader-gm-adaptor</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[326,327]},"content":"<a href=\"#31-冒烟测试\">23.3.1. 冒烟测试</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[327,328]},"content":"<a href=\"#32-客户端与服务器交互\">23.3.2. 客户端与服务器交互</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[328,329]},"content":"<a href=\"#33-api-示例\">23.3.3. API 示例</a>","children":[]}]}]},{"type":"paragraph","depth":1,"payload":{"lines":[330,331]},"content":"第24课 PTrade和QMT","children":[]},{"type":"bullet_list","depth":1,"payload":{"lines":[331,344]},"content":"","children":[{"type":"list_item","depth":2,"payload":{"lines":[331,332]},"content":"<a href=\"#1-ptrade\">24.1. ptrade</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[332,333]},"content":"<a href=\"#11-策略框架概述\">24.1.1. 策略框架概述</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[333,334]},"content":"<a href=\"#12-一个双均线策略\">24.1.2. 一个双均线策略</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[334,335]},"content":"<a href=\"#13-复权机制\">24.1.3. 复权机制</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[335,336]},"content":"<a href=\"#2-qmt\">24.2. QMT</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[336,337]},"content":"<a href=\"#21-安装和申请量化权限\">24.2.1. 安装和申请量化权限</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[337,338]},"content":"<a href=\"#22-功能概览\">24.2.2. 功能概览</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[338,339]},"content":"<a href=\"#3-qmt-mini\">24.3. QMT-Mini</a>","children":[]},{"type":"list_item","depth":2,"payload":{"lines":[339,340]},"content":"<a href=\"#xtdata\">24.4 XtData</a>","children":[{"type":"list_item","depth":3,"payload":{"lines":[340,341]},"content":"<a href=\"#获取证券列表\">24.4.1. 获取证券列表</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[341,342]},"content":"<a href=\"#获取交易日历\">24.4.2. 获取交易日历</a>","children":[]},{"type":"list_item","depth":3,"payload":{"lines":[342,343]},"content":"<a href=\"#获取行情数据\">24.4.3. 获取行情数据</a>","children":[]}]},{"type":"list_item","depth":2,"payload":{"lines":[343,344]},"content":"<a href=\"#xttrader\">24.5. XtTrader</a>","children":[]}]}],"payload":{}},{})</script>
</body>
</html>
