---
title: 因子分析
mainfont: WenQuanYi Micro Hei
puppeteer:
  format: "A4"
  landscape: false
  displayHeaderFooter: true
  headerTemplate: '
  <div style="height:50px; width: 100%;">
    <div style="margin:0 30px 10px 0;text-align:center;font-size:12px">
        大富翁量化金融实战课
    </div>
    <div style="border-top: 1px solid lightgrey;width:100%"/>
  </div/'
  footerTemplate: '<div style="position: relative; width: 100%; border-top: 1px solid black; margin: 0px 30px 30px; padding: 1px, 0px, 0px; font-size: 9px; font-family: Meiryo, Arial, sans-serif;">
  <div style="position: absolute; top: 15px; left: 0px; text-align: left;">
  <span class="title"></span></div>
  <div style="position: absolute; top: 15px; width: 100%; text-align: center;">
  <span class="pageNumber"></span> / <span class="totalPages"></span></div>
  <div style="position: absolute; top: 15px; right: 75px; text-align: right;">by 量化风云</div>
  <div style="position: absolute; top: 0px; right: 0px">
    <img style="opacity:0.8;width:48px" src="data:image/jpg;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAEBRJREFUeF7tm2uMVdUVx9e5d+bOm2F4qgj4aCX4RtovJkZUQGNSGl8pihjTDyb6sWkw2rRNmiYS9EMbGzXpF8AH9GGjjREfqLVpKB+sOKAFqlQtCMpzHBgY5t57TvPb8L9ds+fcmTttTBr1JJM5d9+9115r7fX4r3XOTbIsy+wrfCVfK+BrCxjuAv+rPpIkGeZQose4p6159faL6YiopxePNeLJMd1hLgDxxx57zDZv3jyM2XqEn3zySVu+fLlJuNWrV1uxWAyfdd15553GeFNTk6VpGuYXCgVbu3at8V2eoGvWrAlz/HfwpvncP/XUU4FH5tSjE/N95ZVX2n333TdseEQMgNi6desCs2Nd5XLZmpubg3CVSiWskTLEPP+r1WoQiIvP3LMWZeVdmi8BmcM982UBzBE9Pz4az8uWLQuK89cIC+CEnn766SCQmI6JTp061Q4dOhQE1knBmFeA1sYmLuH4XkJIAObW23MY00kSFKi9UerkyZPt4MGDYZwD8Rd8MTYuBbBoxYoVtmXLlmHEXn31VfMK2LhxY81Ur7rqKmtpaQmnFCtACrr++uvDfGg//PDDQWmvvfZaWBMr4JVXXsl1kUWLFhl/XJqDAj799FO74YYbwt6y4Hnz5oV92HPcCli8eLEhYKxNrwB/oszjBNhMihAjsgSZPetik5Z7MM73sijo8lluVSqVatYjN5kyZYrt379/hAUtXLgwKAmlfCEK8OYrRmMTlK8ihPdrCcV8nRpjxBWEjxWAUuV2cp8vVAGYGeY5mgXI1C+66KJaUHr33XdrS7Zt22aXXnppEBymuZewW7duDfPee++92unefvvtIQhzLV26tEaHNc8880ygc9lll9WC4f+NAupFX50Y3+teJu4juVwEaxgaGgoKiS1Jgc9njy9UAfWEirOA5vnUGacw+TdzZOZKmRoThvAgSVGdsTzMoH0IggcOHMhlmTko84477mg8DUKw3jUwMGCDg4O1E2XeZ599Fsyb9Ek0PuOMMwLDBKZp06YFk92+fbvNnTs3kN2xY0eYgzJYM3369BAUoSNB640z58wzzww0FSeID52dnbksQ48UOa4gWFd69wWbKwjGpu7Hdc9pEsHFeN4cbzkoQG6grIEwAmCi0wh2gO2GFCCNeqKeKW/uMOMFzxOoUUV6DJBn6ijAI0T4488roV4doiyUx8uY5TCnQFoiYl944YXBPDFpIUFvrlKA/J0NFdz0X4zHUJvPUoJo+jQYC+fnsw4Thy9/wfvrr79ugC/mjGkBeRpSAHnnnXdszpw5QRn4KwGHDRSRheTEqCwojvQKdFKItyi5lFdADKDigKtAmqcA1oJcAXRjuoDM3J+UNkNQcu/OnTvt5MmTudHYuwzzFbWFw6HFOJev9FCQ/FzC+IMQsvQpEZqKA3F2gH9wyCWXXBJcBhzTsAJYfNddd4ViyF9s0tvba4Ad7uulQSE76OikBWkFfhBEscMrxQfHOM7EPiylsoeUrTVeAXy/YMEC27BhQ4DmuAAl/DDZfEuMxaoGtaksAwVcfPHFYS0KOHz4cNhcJ+NTklcAJ6XPXhDBYlmOdxlvhQp8PvqzLwrj8kFYn7EAUjLXddddZy+//PL4q0EWsxACCEkQRAEwumfPnpCH5f/yWfm7gp9MWsWLPksYFUwIJEUjkDC/7yOwr9wPfnw/gXl9fX3hYLiwVJQgRf5X5TCECCAogM3B9BCGKEGQoOMv78v+1GQZYkJFDsFUVgAdH0x9EIzTsbcmNVb4LyRYDy2OuxqspwBO6+yzzw4Iz1/g97a2tiCUApSEAjVyL5+tpqm1trSEEz0lYGLF4qkWmE99sXkr03ilaI4UkBfElV7HTIM+BkBY1SBCEVWBr8zZt2/fiJyrIKeTVvDjpPkO8yyVWsyyghWtYh/+a5edd843rGy00cw+2rXLytWiNTcNBzi4jzCDLEb9RbmNPiuoUl2StcaVBeS3CoJeATJRDz5iIINltLa21rC9Tl8mf+TIkVMBM0sR2cxarJwMWClrsiQrWMZQVjZLitbf328zZsyo0ZOlCfnFbS8sRx0hnxbh4dprr7UXX3wx0BpXFoBx8idISmYZgxfvAqPVBWSMSZMmnYraSdV4FFUspzZUTKySmZWSslmh3ZrTIbPCqVrhk08+sfnz549wNWjgbgqg4gELowiTkhjnno6QssC4qkEEAkICJNA4JqXImqcINO8BEKfNPJg9evSo9fT00BK2ampWsLI9v6ti319dtSlDu6135VxrL5QtS5otCepJDIthz927dw/P26db7r4pqkArS/XRX1C4Xhe6VgtokaoutKfGBYRVC3BPFlAtIFP3sFhmD41KObNjA/3W09NilpYsrVbtr4cyu+mXx61sPVZNBm3bg602q71qVRsK7sC6vr6B4DLd3RNq+d6n17hooydImcxaIUHhAHqCsohh2kTVMRDy2NvnbWoBcABjqgV8tJeGWf8fdJZZWqla/8CgdfV0Wmapbe1LbeGqo1ZOJ1jJqtZ8ZND++KM2mzejyR791QarNpXth/feZANHD1pS7LQJXW2BZ19TSAifEtUUhb9YATR28zIIdOo+F1AaJIhwgayIripLY2yO5pXbBYlD1E7NjvUdtvZJHfbBgZIt+MVhGyxOt47B1E58/L5NLfdb+1CvHfrHn62/3GXFJLPlt82zn/34ZmtubrIJXT1hf+92cXpUEMQC5HpSkrrCWOq4YgCneOONN4aWsoohKYBODkBIrS0Bn2DypwshfJ9Nz5t1vv1l+1u2dOUH9lYyx5rLXdZa3Wel99+21kqPWdpmRw73mu35mw02l83KBWsr7Le/966xzlJmzaW2GoaQULECFA9CkE3TcFCXX355zex1+uPCAQhFFiAIQhQcQEsLAVULeMF9WuREZAWzZp9jW97ebM2TptnPn/unpVnVVnz3m9ZjiVWzzK743gYrn5hhH+74rXW1zLTfrb3Frr6i2471V6yltWxtrd3D/Fe0PRiKA5xqAV9dct+QBQhc+OYGHFAM0c9jXAHIPxPE/FXuCpgQsE+eHLS+wYpN7ewgMVlmVQP7ZYWKlbOSPb9xh/3g0YpV0hZbMn+fPfqTq60pG7S+/s9t/re+bR9/dCoLSMFxBvIdYlkIkJ2YBYjChV966aXgng1ZAOUwDxDRGOYPATYR0lOjktyOmTOuzCHIqVzM59mzz7etWzbZxEmTLDOe2Z16SJpVzZLCoP3012/buud7bHL7TvvT+u9YSzGzxBI7dPiEtZQq1tHZXfP/2L+lGB2WVwC1C0p74403AqL1hZXPBLlPh/UAAgDBYl+oQFRZQMhQBYjPIPK72efMst7erdYxscdKNhQUUMmqNpQVbdPmj+3eB7da69RO2/SbBdZZKVjWXLRCYnbk8wPWVuiw1q72mgK82UsIKd8LhQWgAAVynhd6jDKqAvQlQlENYgG+VMWsACdnnXVWzR108hQ9nJJcAAvhoo0uJMjrGKlVbWCoaNt27LX2iV02Z2a7tRZSK6TNMpAAhDDbrq6uQCMWQIFQFugRoGSILWNMFximmdMKuOaaa4JA+JQea3l/lxXovzaVNaAonjDzPwsor2pJeC0rDcAoIMMktaRYYCScPhdZBvwuBUhgeDl27Jh1dHQEE/cxwZfCOhQfnMetAGIAeRTtggQxrbwmiN9Y0V9jSo1gex5cFLKSWSGzSjJo1TSzAu9IpE1WtKIloTI8BaknTpwYIDiCCurKsvJwQBwc5S4K2Ch1zCzgAYcIwhDEfEqhFwCD6uPJRXxQ8tZEt4Yu8uLFi6ySVgxDeO4Pv7fbbl5maTZkz/7hWeB/KJVvuuVmO3HiRLAABVnoY3XiT80XxoVGQYIUQ+o5quvkawHWx3FkBBKMswAuIAXIF2mIgLrEiE6Z7303iM8wSKNE5ur7BBIQ4bjXyUqxXumipcaKoLEXKJ6vmoTxhp4NsgnvCKkrrDQYt6X0YEQRWJ0cnVBsER42y0XyGijebaAVCyShhUXyTtRbnscOrBk1Bohp/mujJUuWhJayzwycKK0s6nEE414vOMhCdIJaR3ZQZ0j+rNoBJrWfBCIGMEYQ1OV92ccA34eQBWqMPchiZLOGFABzd999dwBCMIBZwjjjNCdAgyhg5syZwd/YSODEa5t7mBcy1BsfKAt38L18rdPpq9UO8yhOHeLYquK+oE99BGt4VRxjLn9jZgEW0BIDCPlTYVzVIAzrwUjMlDc/MaggqpP24951ZO5SqvxX8+EHZaCgOAvooKT4Cy64IJTE3nrGtAD5rwcYnoAXVnN9SvTahkmfhkZE3tOpTusVRJW7RYv/HutLSTocvs97iySuD6RU4tuYT4aUBeJgQnsKeIkLUA6D1EhXekKjjODXYfqYvVde3NFV6lSm8c/+PS2vlDxIrLnsCY34GlctED8bRIO4AGYV1wJ5Zh8rzwfY+AGIgA/jeaWut0IFOWGCEVKOMhADtFpgV0sszgJ5ZWYefU6jvb09wFOdovqDBEICmSK/p6n9ZP7CCRIuTzHCAMePH69ZXtwLgJ7KYebTDhvX02FlgbG0q3SJgER2GOdefgpjKMCXxjFNmTL/cRWvIJmsR31KsyhccUBYBGyyd+/eEBNUDcYItuEsgAsALeudOP6vp741Uzr9QlS8hrdKSJlcrPO+zANWTp6HGnrURrfZX3pZi3XAaZTk3wRBARRaEyZMCO8vyAJwVw6DGKWC6tZbb7UnnnhiGP26TVGf132xw2qfBr0C8hTmX4qIYavSoHCBx/yipfUIqoAbx5g4DnkL4OEuHSFvbX5+XQVwwjQSRntT1FdgMCjQNGwD92Z3HnhB0coWCoSxQMLyuJhcSh3oeC/xhCuAD4gBPOBRkzY+zFEtYKxXZdlMgINnBjROGaMr6y/5q15a4Lv169fXHrfrAQzMsVav3XKv12W59+7DXlyaj6XgAv6VXQAdP/645557Qgrv7u4OKLYhC+BkxlKAQBPBjiyAljHT2BJUCyhACsAwX3UEYwJCii/Q1b0gt7KHagMsAgUTH4gpQooqi72LjvvhaCMKUGEEE3o/IC5uxDxzVSPIt1FKXN0xxp9abB4eKxOoxGYv1Q9yJ6yRWgDlkAJfeOGFAKPHbIioFiALQPj+++/P/cEEUZiGha/Q/OtojOt3BjBAIOJijuKAntcxzhzR0g8hGKcp62sHBWbikubJcrCUN998M9A699xz7fHHH7dHHnnE+MHEQw89FOIAfDzwwAONu0BeVIcJTA2T8wqI22KqANWZUfDyrW2/Jq9r6+Er61Ug+QpRAIoDoVvtewhxJpOLNRQD8oSPx3wW8MIw7hGamFJk5rO6QDH4UXGjN8HUNfL0Ecy/OQJfXnACIT1MxhpGghCBcUxn06ZNjcgfKisxRqWli3GY9Lgf2iq0+I57fjonE5cyRU9z/B6yOOb4/dhXmYR7WnYrV64MPJClVq1aFVhr6GdzDUn+JZo05svSXyJZc0X5WgFf9V+P/xsclWUipHo33QAAAABJRU5ErkJggg==">
  </div>
  </div>'
  margin: {
    top: "65px",
    bottom: "70px",
    right: "30px",
    left: "30px"
  }
---

![75%](https://images.jieyu.ai/images/2023/06/lesson14_toc.png)

## 1. 因子分类

目前讨论较多的因子按风格分类，大约有这样一些：

| 因子名     | 英文名             | 因子名     | 英文名           | 因子名         | 英文名                   |
| ---------- | ------------------ | ---------- | ---------------- | -------------- | ------------------------ |
| 估值因子   | Value Factor       | 成长因子   | Growth Factor    | 财务质量因子   | Finacial Quality Factor  |
| 杠杆因子   | Leverage Factor    | 规模因子   | Size Factor      | 动量因子       | Momentum Factor          |
| 波动率因子 | Volatility Factor  | 换手率因子 | Turnover Factor  | 分析师情绪因子 | Sentiment Factor         |
| 股东因子   | Shareholder Factor | 技术因子   | Technical Factor | 改进的动量因子 | Modified Momentum Factor |

此外，所谓另类因子的研究，近年来也比较受关注。

数据来源一般有以下几种，分别为交易数据、高频数据、财务数据、另类报表、公告事件、盈利预期、行业特色、股东持股、公募持仓、新闻舆情、指数成分和衍生产品等。

在这些数据中，有结构化的数据（比如交易、财务数据），也有非结构化的数据（比如新闻舆情）。对于新闻舆情这一类的非结构化数据，还需要先进行结构化处理和数字化处理，这也是AI技术目前可以大显身手的地方这一。
## 2. 因子分析

![75%](https://images.jieyu.ai/images/2024/02/factor-analysis-framework.png)


### 2.1. 预处理

#### 2.1.1. 异常值处理
交易数据中产生的异常值会少一些。但在财务数据和其它数据中，异常值的出现比较常见。比如，如果某公司去年同期净利润接近于0，则净利润增长率因子则会变成一个异常大的数值。

因此，在进行后续的回测前，我们要对该类数据做预处理。如果不进行异常值的修正，异常值会干扰因子中性化（回归）的结果，对IC等指标的计算也会带来误差。

异常值修正的方法大致有以下几种：

##### *******. 均值标准差修正法
将偏离均值3倍标准差的数据拉回3倍标准差，这种修正法也称为$3\sigma$法。这是因为，基于正态分布的假设，超过均值$3\sigma$的数值就非常罕见了。

在代码实现上， pandas和numpy都提供了一个名为`clip`的函数：
```python
import numpy as np

arr = np.append(np.random.randint(1, 3, 20), [15, -10])
std = np.std(arr)
mean = np.mean(arr)

print(mean + std*3)

np.clip(arr, mean - 3 * std, mean + 3 * std)
```
`clip`函数的第一个参数是待处理的数组，第二个参数和第三个参数分别是要替换的边界值。超过这个边界值的数据，都将分别被这两个值替换。

当数据量比较小时，个别的离群值将显著影响到std的计算，从而导致clip方法失效（即所有的点都落在$\pm3\sigma$内）。大家可以通过缩小第3行中，np.random.randint中的size参数，自行尝试下。

##### *******. 分位数修正法

将数据排序后，按分位数分布，将头尾的数据拉回。这种方法也叫winsorizing，是根据生物统计学家Charles Winsor的名字来命名的。

我们可以使用scipy.stats.mstats中的winsorize方法来实现。
```python
from scipy.stats.mstats import winsorize

arr = np.append(np.random.randint(1, 3, 20), [15, -10])
print(arr)
print(winsorize(arr, 0.05).data)
```
winsorize的第一个参数是待处理的数组；第二个参数，可以是一个数组，也可以是一个标量。比如，如果我们要对称缩尾10%，则可以传入[0.1, 0.1]，也可以只传入0.1。如果要非对称缩尾，则应该使用数组的方式。

winsorize返回的结果中，我们需要的数据保存在`data`属性中。


##### *******. 中位数极值法
将偏离中位数`n`倍的数据拉回。一般使用5倍左右的数值。

这里需要先介绍绝对中位差（median absolute deviation)的概念：

$$MAD = median(|X_i - median(X)|)$$

为了能MAD当成与标准差$\sigma$估计相一致的估计量，即
$$\hat{\sigma} = k. MAD$$

这里k为比例因子常量，如果分布是正态分布，可以计算出：
$$
k = \frac{1}{(\Phi^{-1}(\frac{3}{4}))} \approx 1.4826
$$

基于这个k值，取3倍则近似于5。

代码实现如下：
```python
from numpy.typing import ArrayLike

def mad_clip(arr: ArrayLike, k: int = 3):
    med = np.median(arr)
    mad = np.median(np.abs(arr - med))
    
    return np.clip(arr, med - k * mad, med + k * mad)

np.random.seed(78)
arr = np.append(np.random.randint(1, 4, 20), [15, -10])
mad_clip(arr, 3)
```
该数组的中值是1.5，MAD为0.5，于是数组中的15和-10被替换为3和0。

以下是二维数组版：
```python
def mad_clip(arr, k: int = 3, axis = 1):

    med = np.median(arr, axis=axis).reshape(arr.shape[0], -1)
    mad = np.median(np.abs(arr - med), axis=axis)

    return np.clip(arr.T, med.flatten() - k * mad, med.flatten() + k * mad).T

arr = np.array([[1, 1.1, 1.2],
                [-5, 1.1, 6]])
mad_clip(arr, k = 0.1)
```
二维数组版在因子预处理中，可以减少循环，极大地加速运算。

我们也可以使用scipy当中的方法`stats.median_abs_deviation`来完成mad的计算：

```python
from scipy import stats

arr = np.array([[1, 1.1, 1.2],
                [-5, 1.1, 6]])

mad = stats.median_abs_deviation(arr, axis=1)
med = np.median(arr, axis = 1).reshape(arr.shape[0], -1)

np.clip(arr.T, med.flatten() - 0.1 * mad, med.flatten() + 0.1 * mad).T
```
上述修正方法中，分位修正法和中位数极值法更常见[^fangzheng1]。特别是中位数极值与均值标准差相比，中位数极值法不受标准差计算的影响，因此一般认为比均值标准差法更好。



#### 2.1.2. 缺失值处理
在第9课我们有一个练习，就是使用numpy来实现pandas的fillna的功能。我们在因子分析的预处理过程中，就会常常遇到这个场景。

财务类因子有可能出现某些数据缺失；一些分析师评级数据更是常常出现没有覆盖到某个标的的情况；有一部分技术指标类的因子，常常会有冷启动情况，表现为前几项往往无法计算，于是表示为np.nan。

在这些情况下，我们都要先进行缺失值处理。对不同的因子，我们处理方法是不一样的。一般有延用上一期有效数据（也即pandas.fillna中mode='ffill'的情况）、中位数替代法、相似公司替代法等。

对财务因子，我们一般延用上期因子值；其它因子需要根据具体情况具体分析，可以延用上一期值，也可以考虑其它不影响分析的替代值；如果因子值缺失过多，导致覆盖度特别低，则应该考虑剔除。

一些文章建议把缺失值处理放在去极值之前进行。这可能是有争议的。

!!! tip
    缺失值与去极值谁放在前面处理，得到的结果是不一样的。比如，如果我们使用均值标准差法来进行去极值，那么如果我们先处理缺失值，这些替换进来的值显然会影响均值和标准差。

#### 2.1.3. 分布调整

理想情况下，因子在截面上的暴露应呈现正态分布。如果有严重偏离，可以尝试用对数法、开根号方法对原始数据的分布进行调整。具体使用哪一种方法调整，可以参考第12课内容，先进行分布推断，再尝试进行调整。

市值因子就是一个需要进行分布调整的例子。因为A股小票众多，但少数股市值巨大，因此原始的市值因子呈现明显的右偏、尖峰、后尾的分布特点。

![75%](https://images.jieyu.ai/images/2023/06/市值因子对数调整.png)

上图显示了对市值进行对数调整后，分布呈现出近似正态分布的情况。

#### 2.1.4. 标准化

经过上述处理之后，我们还要进行因子的标准化，这样可以让不同因子的暴露度之间有可比性。

标准化的方法，是将上述处理后的因子分布看成近似正态分布，于是可以根据以下公式：

$$
    Z = (x - \mu) / \sigma
$$

就得到z-score，它是一个近似符合N(0,1)的分布。

!!! attention
    我们需要注意这里的假设是否成立。可以通过前面讲的统计检验的方法来进行测试。另外，z-score是一个服从N(0,1)的正态分布，并不意味着z-score的取值为(0,1)，它的意思是均值为0，标准差为1。

    如果我们将z-score用以机器学习，可能还要视情况进一步处理。

代码实现上很简单，我们使用`scipy.stats`中的`zscore`方法就好：

```python
from scipy.stats import zscore
import numpy as np

factors = np.random.random((8, 20))
factors_zscored = zscore(factors)
factors_zscored
```

#### 2.1.5. 中性化

因子对不同的行业，其暴露程度是不一样的。比如，如果我们在2022年用市盈率因子选股，我们会发现市盈率最低的（且为正），多是银行股。如果我们凭这个因子来选股，实际上选择的是银行股，其涨跌是受政策宏观调控与经济周期的系统性影响，不能选择出具有独特alpha的个股。

我们常做的中性化有行业中性化和市值中性化。

对离散数据的回归，一般采用哑变量线性回归法。行业中性化常常使用这种方法，其公式为：

$$
Y = \sum_{j=1}^nIndustry * \beta_j + \alpha  + \epsilon \tag 1
$$

这里的常量，或者称残差$\epsilon$就是行业中性化后的因子。

市值中性化回归公式如下：
$$
Y = \beta * \log(MarketValue) + \alpha + \epsilon \tag 2
$$

我们可以同时进行市值中性化和行业中性化，并且可以对多个因子同时实施。其代码实现如下：

```python
# copy industry.csv to each students docker container home
import pandas as pd
from coursea import *

await init()

codes = [
    '000001.XSHE',
    '000002.XSHE',
    '000004.XSHE',
    '000006.XSHE',
    '000007.XSHE',
    '000008.XSHE',
    '000009.XSHE',
    '000011.XSHE'
]

end = datetime.date(2023, 6, 26)
start = tf.day_shift(end, -10)

factors = []
async for code, bars in Stock.batch_get_day_level_bars_in_range(codes, FrameType.DAY, start, end):
    df = pd.DataFrame()
    df["date"] = bars["frame"][1:]
    df["instrument"] = [code] * (len(bars) - 1)
    df["factor"] = bars["close"][1:] / bars["close"][:-1] - 1
  
    factors.append(df)

factors = pd.concat(factors)
factors
```
输出如下：

![50%](https://images.jieyu.ai/images/2023/06/factors_pct.png)

```python
import pandas as pd
from sklearn.linear_model import LinearRegression

df = pd.read_csv("/data/ro/industry.csv")
pd.options.display.max_columns = 10
display(df)

# get_dummies是一个 one-hot 编码的矩阵
industry = pd.get_dummies(df["industry_zx"])
lncap = np.log(df[["MKT_CAP_FLOAT"]])

# y可以是一个矩阵，但这里我们只有一个因子要中性化，所以是一个(1,n)的矩阵
y = factors[factors["date"]==datetime.datetime(2023, 6, 26)][["factor"]]

y = y.dropna(how="any", axis=1)
    
X = pd.concat([lncap, industry], axis=1)

model = LinearRegression(fit_intercept = False)
res = model.fit(X, y)

# 根据公式求残差，这个残差，就是我们新的因子
coef = res.coef_
residue = y - np.dot(X, coef.T)
display(residue)
```
最终我们求得的残差，就是进行了中性化之后的因子。

!!! attention
    我们常常对市值因子进行分布调整，但要注意，分布调整与下面要讲的市值中性化是相区别的。分布调整直接作用在市值因子上。而中性化是其它因子，借由市值因子来进行中性化。

在第19行代码中，有一个参数`fit_intercept`。它的作用如下图所示：

![50%](https://images.jieyu.ai/images/2023/06/fit_intercept.png)

因为我们在之前已经做了标准化，所以，这里`fit_intercept`应该传入`False`。另外，从公式（1）上看，因为我们要求得残差$\epsilon$，这也要求$\alpha$为零，此时当$x$为零时，$y$即为残差。

## 3. 单因子测试
因子检验通常有回归法、IC法和分层法等。现在一般认为，分层法鲁棒性会更好。


### 3.1. 回归法
回归法的思想非常直观，是将因子在第T期的暴露度与T + 1 期的股票收益进行线性回归，所得到的回归系数即为因子在T期的因子收益率，同时还能得到该因子收益率在本期回归中的显著度水平 t 值。

回归公式如下：

$$
r_i^{T+1} = X^T\alpha^T + \epsilon^T
$$

其中：
1. $r^{T+1}$是所有个股在第$T+1$期上的收益率
2. $X^T$是所有个股第T期在被测因子上的暴露度向量
3. $\alpha^T$是所有个股在第$T$期上对应的因子收益率
4. $\epsilon^T$是所有股票在第$T$期的残差收益率。

在所有截面期上，我们对因子 X 进行回归测试，从而得到该因子的因子收益率序列（即所有截面期回归系数$\alpha^T$构成的序列）和对应的 t 值序列。

t 值指的是对单个回归系数$\alpha^T$的 t 检验统计量，描述的是单个变量的显著性，t 值的绝对值大于临界值，就说明该变量是显著的， 即该解释变量（T期个股在因子 X 的暴露度）是真正影响因变量(T + 1期个股收益率)的一个因素。也就是说，在每个截面期上，对于每个因子的回归方程，我们设：

$假设检验⁡ 𝐻0:⁡𝑎𝑇 = 0$
$备择假设⁡ 𝐻1:⁡𝑎𝑇 ≠ 0$

该假设检验对应的t统计量为:
$$
t = \frac{\alpha^T}{SE(\alpha^T)}
$$

其中$𝑆𝐸(𝑎^𝑇)$代表回归系数𝑎𝑇的标准差的无偏估计量。一般 t 值绝对值大于 2 我们就认为本期回归系数$𝑎^𝑇$是显著异于零的(也就是说，本期因子 X 对下期收益率具有显著的解释作用)。

!!! tip
    t值1.96时，对应p值0.05。这是上文中认为t值绝对值大于2时，认为$𝑎^𝑇$是显著异于零的的原因。

#### 3.1.1. 回归法的因子评价
1. 使用t值序列绝对值均值来评价因子的显著性
2. 使用 t 值序列绝对值大于 2 的占比来判断因子的显著性是否稳定;
3. 使用 t 值序列均值 —— 与 1）结合，能判断因子 t 值正负方向是否稳定;
4. 通过因子收益率序列均值来判断因子收益率的大小。

### 3.2. IC分析法
因子的IC值是指因子在第 T 期的暴露度向量与 T+1 期的股票收益向量的相关系数，即

$$
IC^T = corr(r^{T+1}, X^T)
$$

这里的$X$仍然是经过预处理之后的因子值。计算相关性有Pearson和Spearman两种方法，现在一般使用Spearman秩相关系数，这样算出来的IC一般称为Rank IC。

#### 3.2.1. IC分析法的因子评价

当IC值为正时，表明因子值和未来收益正相关，例如净利润增长率因子，一般值越大股票未来收益越高；当IC值为负，表明因子值与未来收益负相关，例如PE因子，值越小未来收益越大。

因此，IC的方向并不重要，重要的是IC的绝对值。

下图显示了市值因子与IC的关系。当IC绝对值>0.05时，就表明IC显著比率很高。IC为负时，表明小市值股票表现较好，而IC为正是，大市值表现占优。从下图中可以看出，17年以来，因子的方向出现了明显的变化。

![](https://images.jieyu.ai/images/2023/06/size_factor_and_ic.png)

### 3.3. 分层回测法
无论是回归法还是IC法，都假定了在截面上因子暴露与远期收益之间存在线性关系。但实际上两者的关系完全有可能是非线性的。于是，一种名为分层回测法的方法被提了出来。

依照因子值对股票进行打分，构建投资组合回测，是最直观的衡量因子优劣的手段。分层测试法与回归法、IC 值分析相比，能够发掘因子对收益预测的非线性规律。也即，若存在一个因子分层测试结果显示，其 Top 组和 Bottom 组的绩效长期稳定地差于 Middle 组，则该因子对收益预测存在稳定的非线性规律，但在回归法和 IC 值分析过程中很可能被判定为无效因子。

分层测试在模型构建上与前两者有所不同，它有一个明显地换仓期，即在每个截面期核算因子值，构建分层组合，在截面期下一个交易日按当日收盘价换仓，并且引入了交易费用，从而比前两种方法更贴近实操。

分层回测模型的大致方法是：
1. 换仓：在每个截面期核算因子值，构建分层组合，在截面期下一个交易日按当日收盘价换仓。
2. 分层方法: 先将因子暴露度向量进行一定预处理，将股票池内所有个股按处理后的因子值从大到小进行排序，等分 N 层，形成N个投资组合。
3. 多空组合收益计算方法: 用 Top 组每天的收益减去 Bottom 组每天的收益，得到每日多空收益序列$r_1, r_2, ..., r_n$，则多空组合在第 n 天的净值等于$(1 + 𝑟_1 )(1 + 𝑟_2 ) ⋯ (1 + 𝑟_n )$。
4. 评价方法: 全部 N 层组合年化收益率(观察是否单调变化)，多空组合的年化收益率、 夏普比率、最大回撤、月胜率等。

### 3.4. 代码实现

本部分内容比较多，为了提供一个完整的思路，我们把代码单独抽取出来，见[光大因子测试框架](chap03-%E7%AC%AC14%E8%AF%BE-%E9%99%84%E4%BB%B6-1.md)。

### 3.5. 三种方法的区别与联系

_本节内容选自华泰证券研报，供参考。_[^huatai]

首先介绍一下回归法和 IC 值分析法之间的关系。

我们先介绍一个引理。设𝑋, 𝑌为两个向量，则$[𝑐𝑜𝑟𝑟(𝑋, 𝑌)]^2 = 𝑅^2$，其中$𝑅^2$为线性回归$𝑌 =
𝑎𝑋 + 𝑏$或线性回归$X = 𝑎𝑌 + 𝑏$的可决系数(其中𝑎，𝑏是待回归系数)。

如果我们在单因子测试(线性回归法)中使用模型
$$
r = \beta𝑋 + 𝑐
$$

r 是股票收益率，X 是因子暴露度，c 是常数项，c 可以理解为市场因子。

假设我们在计算因子 IC 值的时候，不预先对因子暴露度进行市值、行业调整了，就使用原始的因子暴露度 X，则本期因子 IC 值为$𝑐𝑜𝑟𝑟(𝑋, 𝑟)$，根据引理，因子 IC 值的平方就等于单因子测 试的回归模型的$𝑅^2$。

所以，因子 IC 值本质上反映的是下期收益率和本期因子暴露度的线性相关程度($𝑅^2$的平方根)，是使用该因子预测收益率的稳健性(IC 值越大，这个因子的收益越稳定，波动越小)；而回归法中计算出的因子收益率本质上是一个斜率，反映的是从该因子可能获得的收益率的大小，这并不能说明任何关于线性拟合优度的信息（也就是说，因子收益率很大时，也可能出现$𝑅^2$很小的情形）;至于回归法中计算出的 t 值，在一元线性回归中 t 值与𝑅2 反映的信息一致（二者对应关系为，当$𝑅^2 = 0$时 t 值也为 0，当$𝑅^2 = 1$时 t 值为无穷大）， 但是由于我们所采用的回归模型包括了行业变量，所以 t 值仅代表被测因子对股票收益的 解释能力(而不能代表模型的整体拟合优度)。

实际计算过程中因子会进行一些预处理，回归方程也有可能引入其它风格变量使其表达形式更复杂，导致 IC 值和 t 值无法理论上互 推，但前面所述结论的本质不变。

总结一下，IC 值反映模型整体线性拟合优度，t 值反映被测单因子对模型的解释能力是否 显著，因子收益率与前两者差别较大，它反映的是可能获得的收益率的大小，而对这个收 益是否稳健未知。

其次介绍一下回归法和分层测试法之间的关系。

假设本期因子值𝑋与下期收益𝑟完全线性相关，满足$𝑟 = \beta𝑋 + c$。此时 IC 值绝对值为 1，回 归法中的因子收益率为𝛽。并且假设本期因子值 X 服从[0,1]均匀分布，那么当按因子从小 到大等分 N 层测试时，第 i 层组合的下期收益为$\beta(2𝑖 − 1)/2𝑁 + 𝑐$，多空收益(第 N 层收 益减去第 1 层收益)为$\beta(𝑁 − 1)/𝑁$，也即说明分层测试法中的多空收益与回归法中的因子 收益率具有一定程度的等价关系。实际上因子 IC 值大部分在 0.01~0.1 区间波动，所以 回归拟合的因子收益率与分层测试下的多空收益也未必完全一致。

 ## 4. 因子评价体系

部分结论来自方正证券[^fangzheng]，供参考。
 ![50%](https://images.jieyu.ai/images/2023/06/因子评价体系.png)

 前面已经提到并演示了一些因子评价指标。在我们的代码中，缺少比较重要的一个指标，就是IC随时间衰减的度量。即在上图中的IC半衰期。

 实际上，在上面的示例代码中，我们只使用了$T+1$期的收益率来进行因子有效性的评估。在实践中，我们往往还会使用$T+5, T+10$等多个周期的收益率来评估因子的有效性。这样一来，代码就更加复杂了。

 此时，我们就需要借助第三方框架来协助了。

 ***本课程有[补充阅读资料](../supplements/lesson14.ipynb)***

## Footnotes

[^fangzheng1]: 《规矩：方正单因子测试之评价体系》第18页，韩振国等， 2018年3月6日
[^huatai]: 《华泰单因子测试之海量技术因子》，林晓明等，2019年5月21日
[^fangzheng]: 《规矩：方正单因子测试之评价体系》，韩振国等， 2018年3月6日
