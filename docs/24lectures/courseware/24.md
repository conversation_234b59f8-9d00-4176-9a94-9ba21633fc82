---
title: 实盘交易接口（2）
output: pdf_document
mainfont: WenQuanYi Micro Hei
---

![50%](https://images.jieyu.ai/images/2023/10/lesson24-outline.png)

## 1. ptrade

### 1.1. 申请与安装

Ptrade由恒生电子开发，券商采购后提供给用户使用。申请使用的一般做法是，先找到与ptrade有合作的券商进行开户，然后申请量化权限。申请量化权限一般有资金门槛。

!!! tip
    2023年，能开通ptrade的券商主要有国金、国盛、东莞、湘财等。

Ptrade提供了一个Windows下的客户端，我们可以在该客户端中开发策略，进行回测，成功后，托管到券商的机房里运行。Ptrade支持的交易品种：股票，基金ETF，可转债（T+0），债券，行情级别为tick级，最小时间粒度是3s。委托档位默认可以获取到十档。

托管模式的优点是，行情速度更快，运行更稳定，不需要额外购买云服务器，也不需要进行维护。其缺点是，运行环境无法定制，比如ptrade目前使用的python版本还是3.5，它的运行环境里，预安装了比较丰富的库，但这些库的版本也受限于python版本，也显得不够新。另外，除了ptrade提供的数据之外，临时计算出来的指标、模型等数据如何存储，这些也尚不清楚。

!!! note
    在ptrade的环境下，预安装有pytorch, keras, mysql等第三方库，但不清楚这些库将如何使用。比如，pytorch能否使用GPU，GPU规格如何？mysql连接的数据库在哪里，等等。

    一般来说，券商的托管机房不允许连接互联网，因此，也无法自行安装和更新软件。

下图是东莞证券2022年5月版的登录界面：

![50%](https://images.jieyu.ai/images/2023/09/lesson24-ptrade-login.png)

Ptrade自带了一些算法交易工具，不需要编程也可以使用，考虑到Ptrade部署在券商机房里，有一定的速度优势，这些工具也可以尝试一下。这些工具有追涨停、拐点交易、网格交易、ETF趋势交易等。

![50%](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-tools.png)

我们从事量化研究的主要界面如下图所示：

![50%](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-quant.png)

它由研究、回测、交易三个tab页组成。下图显示了回测运行的情况：

![](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-sma.png)

本节关于Ptrade的介绍是概述性的。恒生电子在其官网上提供了[视频课程](https://www.hs.net/cloud/open/faq/productTemplateDetail.html?id=1114)，可以免费注册后听课。这个课程在部分内容上会更详尽一些。

### 1.2. 策略框架概述

我们通过下面的代码来演示策略的基本框架、重要对象和函数：

```python
# 示例1
def get_members(obj):
    items = []
    for item in dir(obj):
        if not item.startswith('_'):
            items.append(item)
            
    return items
    
def initialize(context):
    _type = str(type(context)).split(".")[-1]
    print("context type:", context_type) 
    print("members:", get_members(context))

    if is_trade():     
        set_benchmark('000001.SS')
    
        set_commission(commission_ratio=0.0003, 
                       min_commission = 5.0)
        set_fixed_slippage(fixedslippage=0.2)
        set_volume_ratio(volume_ratio=0.2)
    
    g.security = '000001.SZ'
    set_universe(g.security)
            
def before_trading_start(context, data):
    print(">>> before trading:", context.startdate)
    print("data is type:", type(data))
    print("each item contains member:", get_members(data[g.security]))

    item = data[g.security]
    print('data is', item.price, item.pre_close, item.close)
    
    # 去掉ST，停牌、退市
    stocks = get_index_stocks('000300.XBHS')

    st = get_stock_status(stocks, 'ST')
    halt = get_stock_status(stocks, 'HALT')
    delisted = get_stock_status(stocks, 'DELISTING')

    for stock in stocks.copy():
        if st[stock] or halt[stock] or delisted[stock]:
            stocks.remove(stock)

    log.info("{} stocks in pool".format(len(stocks)))

def handle_data(context, data):
    print(">>> handle_data:", context.startdate)
    item = list(data.values())[0]
    print('data is', item.price, item.pre_close, item.close)
    
    _id = order(g.security, 100)
    log.info(get_order(_id))

    order_obj = get_orders()
    log.info(order_obj)

    position = get_position(g.security)
    log.info(position)
    
    log.info(context.portfolio)

def after_trading_end(context, data):
    print(">>> after_trading:", context.enddate)
```

要实现一个策略，我们必须实现这样一些方法：

* initialize 用来做策略初始化，在回测和交易期间，它只在最初被调用一次
* before_trading_start 用来做盘前初始化，每天执行一次
* handle_data 相当于backtrader的next方法，这是我们实现策略逻辑，发出交易指令的地方
* after_trading_end 处理收盘工作。

这些函数都是既可以在回测期间被调用，又可以在交易期间被调用的。除这些函数外，还有几个函数仅在交易期间被调用：

比如，我们可以实现tick_data，这样可以在tick级别实现交易。或者实现on_order_response，on_trade_response方法，以便在委托和交易状态改变时，策略可以进行相应的处理。

下面我们就进一步详细介绍前四个函数，即既可以在回测期间使用，又可以在交易期间使用的函数。

#### 1.2.1. initialize

初始化函数只在策略回测，或者交易启动时，被调用一次。一般来说，我们通过它来设置佣金、滑点、回测撮合量比、回测比较基准以及股票池。当然，关于佣金、回测撮合量比、回测比较基准的设置，在交易中并没有意义。

这个方法接受一个StrategyContext参数，这个参数被称为上下文对象。在该对象中，包含了佣金、滑点等设置信息，也包括账户信息、收益曲线值等。这个对象有以下属性：

```
capital_base -- 起始资金
previous_date –- 前一个交易日
sim_params -- SimulationParameters对象
    capital_base -- 起始资金
    data_frequency -- 数据频率
portfolio -- 账户信息，可参考Portfolio对象
initialized -- 是否执行初始化
slippage -- 滑点，VolumeShareSlippage对象
    volume_limit -- 成交限量
    price_impact -- 价格影响力
commission -- 佣金费用，Commission对象
    tax—印花税费率
    cost—佣金费率
    min_trade_cost—最小佣金
blotter -- Blotter对象（记录）
recorded_vars -- 收益曲线值
```

is_trade方法来用判断当前运行环境是回测还是实盘交易，有一些方法在交易中是无效的，而另一些方法在回测中是无效的，我们需要通过is_trade来进行环境的区分。

set_benchmark用来设置业绩比较基准，如果不设置，默认采用沪深300。这里使用的是上证指数。关于指数的代码，可以从帮助文档中的get_index_stocks函数的帮助中的”指数列表“链接进去查看。

![50%](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-index-list.png)

set_fixed_slippage用来设置滑点。

set_volume_ratio用来设置撮合时，按成交量的比例进行委托匹配。

g是一个全局对象。这个对象初始值为空，我们可以通过属性语法来为它赋值，以便我们在不同的函数中传递数据。

!!! tip
    对任何python对象，我们都可以通过属性赋值语法来给它增加一个新的属性。但是，我们必须先增加这个属性，然后才能访问它。
    ```python
        class Foo:
            pass

        foo = Foo()
        foo.a = 5
        print(foo.a)
        try:
            print(foo.b)
        except AttributeError as e:
            print(e)
    ```

在这里，我们给g.security赋值了一个字符串，此处也可以是一个List[str]。

!!! tip
    这里也顺便提一下股票代码的格式。Ptrade使用后缀式，上交所编码为'SS'，我们也可以用'XSHG'。深交所代码为'SZ'或者'XSHE'。从文档看，目前还不支持北交所。在Ptrade的代码、函数返回值中，同一个标的的后缀也不是一致的。没有专门的文档来介绍这些规则。

通过set_universe我们要设置了要操作的股票池。这里我们只加入了一支股票。该函数并非必须调用，但如果我们在初始化时调用了该函数，则后面在get_price、get_history时，我们可以不用传入security_list这个参数。另外，在handle_data函数中，也才有数据可传递。

#### 1.2.2. before_trading_start

在before_trading_start中，我们一般进行股票池的操作。比如，无论是在回测还是在交易中，我们都希望过滤到ST、退市股和当天停牌股。这是进行此类设置的恰当地方。如果我们在initialize中进行股票池设定，则在回测中会带来前视偏差，在实盘交易中，会得到错误的ST和停牌信息。

before_trading_start接收两个参数，其一是context，这个参数与initialize方法中的一样；第二个是data参数，它是一个BarDict对象，键是我们通过set_universe设置的那些股票代码，值则是BarData对象，它包括这些属性:

```
'dt': bar的归属时间
'is_open'
'mavg'
'money'： 成交额
'pre_close'： 前收盘价
'price'： 现价
'returns'： 当前bar的涨跌幅
'set_frequency'
'sid'： 证券代码，比如603986.XSHG
'stddev'
'vwap'
```

没有关于这些字段的文档。一些经过验证的字段的含义，已经标在上面的片段中了。

在before_trading_data函数实现中，调用了几个ptrade函数，需要介绍一下：

第35行，get_index_stocks用来获取指数成份股。这里我们没有传入时间，在回测中，它会自动取回测周期对应的成份股列表。

我们通过get_stock_status来获取当前股票的状态，查询类型可以是ST,HALT或者DELISTING中的任何一个。

第41行到44行，我们从stocks中移除掉当前属于ST, HALT和DELISTING的股票。这里使用了一个循环，我们不能在列表迭代过程中删除元素，所以第40行使用了copy方法来获得一个副本。

#### 1.2.3. handle_data
handle_data是我们书写策略逻辑的地方。这个示例中我们没有实现任何策略，只是简单地下单，然后查看仓位、资产和委托。

我们通过order函数来买入了100股股票。它接受三个参数，分别是security, amount和委买限价。如果不提供限价，则会以当前价交易。除了order函数外，还有order_target, order_value，order_target_value等函数可用于交易。

在价格变动激烈时，这些order函数可能无法立即成交。Ptrade还提供了一个order_market函数，供我们按市价成交。

当amount为正时，意味着买入；为负则意味着卖出。

order系列函数的返回值是order_id，尽管它没有在文档中进行说明。如果我们需要撤销这个order,可以调用cancel_order，传入这个id来实现。我们还可以通过get_order，传入这个order_id来查询order的状态。

order对象有以下属性:

```
    'id': '4c348b450b8747a996e3f25d2e430a76', 
    'dt': datetime.datetime(2023, 9, 7, 15, 0), 
    'priceGear': 0, 
    'created': datetime.datetime(2023, 9, 7, 15, 0), 
    'amount': 100, 
    'status': '8', 
    'entrust_no': None, 
    'limit': None, 
    'symbol': '000001.XSHE', 
    'filled': 100
```

这里最重要的属性是status字段:

```
    '0' -- "未报"
    '1' -- "待报"
    '2' -- "已报"
    '3' -- "已报待撤"
    '4' -- "部成待撤"
    '5' -- "部撤"
    '6' -- "已撤"
    '7' -- "部成"
    '8' -- "已成"
    '9' -- "废单"
    '+' -- "已受理"
    '-' -- "已确认"
    'V' -- "已确认"
```

get_orders供我们查询账户当日在柜台的全部委托记录。如果我们要撤销某个委托，可以通过cancel_order来实现。

我们通过get_position来获得当前持仓。它接受一个参数，security，用来查询某个标的的持仓。如果不提供，则返回全部持仓信息。

持仓信息示例如下：

```
    'enable_amount': 0, 
    'amount': 100, 
    'last_sale_price': 11.33, 
    'sid': '000001.SZ', 
    'business_type': 'stock', 
    'cost_basis': 11.480556641
```

enable_amount意味着当前可卖数据。 sid为证券代码。

!!! attention
    注意这里使用的后缀是.SZ，而在get_order中，后缀是.XSHE。Ptrade在这方面出现了不一致的情况。


最后，我们通过context.portfolio来获取当前账户的基础信息，这包括了市值、现金、当前损益（绝对值）、回报率和持仓:

```
    'portfolio_value': 99984.9443359, 
    'positions': {
        '000001.SZ': {'enable_amount': 0, 
                    'amount': 100, 
                    'last_sale_price': 11.33,
                    'sid': '000001.SZ', 
                    'business_type': 'stock', 'cost_basis': 11.480556641
                    }
        }, 
    'positions_value': 1133.0, 
    'start_date': datetime.date(2023, 9, 7), 
    'cash': 98861.944822899997, 
    'capital_used': 1148.0556641000001, 
    'pnl': -15.055664100005473, 
    'returns': -0.00015055664100005473
```

注意，尽管这里显示为字典，但portfolio是一个对象。

#### 1.2.4. after_trading_end

如果我们希望有一些工作安排在收盘后做，则可以在after_trading_end中进行。比如，在交易中，我们可以安排收盘后，对一些重要信息进行持久化。

!!! note
    上述这些函数构成了策略生命期的框架。除这些函数外，其它相关函数还有：
    * run_daily 在initialize方法中调用，设置定时任务
    * run_interval 在initialize方法中调用，设置定时任务。仅在交易模块中可用。
    * on_order_response 仅在交易模块可用，在委托回调时被调用。
    * on_trade_response 仅在交易模块可用，在成交回调时被调用。

示例1在2023年9月7日到9月10日期间，运行回测，将输出以下日志：

```
2023-09-11 20:03:00 开始运行回测, 策略名称: zillionare
2023-09-07 00:00:00 - INFO - context is type of StrategyContext'>
2023-09-07 00:00:00 - INFO - context contains member: ['blotter', 'capital_base', 'commission', 'enddate', 'initialized', 'on_initialize', 'pboxUsername', 'portfolio', 'previous_date', 'recorded_vars', 'sim_params', 'slippage', 'startdate']
2023-09-07 00:00:00 - INFO - 

2023-09-07 08:30:00 - INFO - >>> before trading: 2023-09-07
2023-09-07 08:30:00 - INFO - data is type: <class 'IQEngine.core.bar.BarDict'>
2023-09-07 08:30:00 - INFO - value is type: <class 'IQEngine.plugins.plugin_fly_data.local_variables.bar.BarData'>
2023-09-07 08:30:00 - INFO - each item contains member: ['dt', 'is_open', 'mavg', 'money', 'pre_close', 'price', 'returns', 'set_frequency', 'sid', 'stddev', 'vwap']
2023-09-07 08:30:00 - INFO - data is 11.43 11.39 11.43 0.00351185250219
2023-09-07 08:30:00 - INFO - got 300 stocks in pool
2023-09-07 08:30:00 - INFO - 

2023-09-07 15:00:00 - INFO - >>> handle_data: 2023-09-07
2023-09-07 15:00:00 - INFO - data is 11.33 11.43 11.33 -0.***********
2023-09-07 15:00:00 - INFO - 生成订单，订单号:c238537736374a63b254b92bfc8b6cc8，股票代码：000001.XSHE，数量：买入100股
2023-09-07 15:00:00 - WARNING - 股票可卖持仓数量为0, 无法卖出, 委托取消
2023-09-07 15:00:00 - INFO - <Position {'last_sale_price': 11.33, 'cost_basis': 11.480556641, 'business_type': 'stock', 'sid': '000001.SZ', 'enable_amount': 0, 'amount': 100}>
2023-09-07 15:00:00 - INFO - [<Order {'created': datetime.datetime(2023, 9, 7, 15, 0), 'limit': None, 'entrust_no': None, 'symbol': '000001.XSHE', 'filled': 100, 'status': '8', 'dt': datetime.datetime(2023, 9, 7, 15, 0), 'amount': 100, 'priceGear': 0, 'id': 'c238537736374a63b254b92bfc8b6cc8'}>]
2023-09-07 15:00:00 - INFO - <Portfolio {'start_date': datetime.date(2023, 9, 7), 'pnl': -15.055664100005473, 'capital_used': 1148.0556641000001, 'positions_value': 1133.0, 'returns': -0.00015055664100005473, 'portfolio_value': 99984.9443359, 'cash': 98861.944822899997, 'positions': {'000001.SZ': <Position {'last_sale_price': 11.33, 'cost_basis': 11.480556641, 'business_type': 'stock', 'sid': '000001.SZ', 'enable_amount': 0, 'amount': 100}>}}>
2023-09-07 15:00:00 - INFO - 

2023-09-07 15:30:00 - INFO - >>> after_trading: 2023-09-08
2023-09-07 15:30:00 - INFO - 

2023-09-08 08:30:00 - INFO - >>> before trading: 2023-09-07
2023-09-08 08:30:00 - INFO - data is type: <class 'IQEngine.core.bar.BarDict'>
2023-09-08 08:30:00 - INFO - value is type: <class 'IQEngine.plugins.plugin_fly_data.local_variables.bar.BarData'>
2023-09-08 08:30:00 - INFO - each item contains member: ['dt', 'is_open', 'mavg', 'money', 'pre_close', 'price', 'returns', 'set_frequency', 'sid', 'stddev', 'vwap']
2023-09-08 08:30:00 - INFO - data is 11.33 11.43 11.33 -0.00***********
2023-09-08 08:30:00 - INFO - got 300 stocks in pool
2023-09-08 08:30:00 - INFO - 

2023-09-08 15:00:00 - INFO - >>> handle_data: 2023-09-07
2023-09-08 15:00:00 - INFO - data is 11.27 11.33 11.27 -0.529567519859
2023-09-08 15:00:00 - INFO - 生成订单，订单号:cb375ac35fd44d61af8cddf2b22cee66，股票代码：000001.XSHE，数量：买入100股
2023-09-08 15:00:00 - INFO - 生成订单，订单号:4998a536ba274e7eb75b75513376d3a4，股票代码：000001.XSHE，数量：卖出100股
2023-09-08 15:00:00 - INFO - <Position {'last_sale_price': 11.27, 'cost_basis': 11.792824339000003, 'business_type': 'stock', 'sid': '000001.SZ', 'enable_amount': 0, 'amount': 100}>
2023-09-08 15:00:00 - INFO - [<Order {'created': datetime.datetime(2023, 9, 8, 15, 0), 'limit': None, 'entrust_no': None, 'symbol': '000001.XSHE', 'filled': 100, 'status': '8', 'dt': datetime.datetime(2023, 9, 8, 15, 0), 'amount': 100, 'priceGear': 0, 'id': 'cb375ac35fd44d61af8cddf2b22cee66'}>, <Order {'created': datetime.datetime(2023, 9, 8, 15, 0), 'limit': None, 'entrust_no': None, 'symbol': '000001.XSHE', 'filled': -100, 'status': '8', 'dt': datetime.datetime(2023, 9, 8, 15, 0), 'amount': -100, 'priceGear': 0, 'id': '4998a536ba274e7eb75b75513376d3a4'}>]
2023-09-08 15:00:00 - INFO - <Portfolio {'start_date': datetime.date(2023, 9, 7), 'pnl': -52.282433900008307, 'capital_used': 1179.2824339000003, 'positions_value': 1127.0, 'returns': -0.00052282433900008307, 'portfolio_value': 99947.717566099993, 'cash': 98840.718540099988, 'positions': {'000001.SZ': <Position {'last_sale_price': 11.27, 'cost_basis': 11.792824339000003, 'business_type': 'stock', 'sid': '000001.SZ', 'enable_amount': 0, 'amount': 100}>}}>
2023-09-08 15:00:00 - INFO - 

2023-09-08 15:30:00 - INFO - >>> after_trading: 2023-09-08
2023-09-08 15:30:00 - INFO -
2023-09-11 20:03:08 策略回测结束
```
在日志部分，输出的有系统日志和策略日志，所以我们看到日期上并不连续。2023-09-11是我们执行这个策略的时间。

### 1.3. 一个双均线策略

下面的代码演示了一个完整的双均线策略：

```python
# 示例2
def initialize(context):
    set_benchmark('000001.SS')
    
    set_commission(commission_ratio=0.0003, min_commission = 5.0)
    set_fixed_slippage(fixedslippage=0.2)
    set_volume_ratio(volume_ratio=0.2)
    
    g.security = '000001.SZ'
    set_universe(g.security)

#当五日均线高于十日均线时买入，当五日均线低于十日均线时卖出
def handle_data(context, data):
    security = g.security

    # 得到十日历史价格
    df = get_history(10, '1d', 'close', security, fq='pre', include=False)
    
    # 得到五日均线价格
    ma5 = round(df['close'][-5:].mean(), 3)
    
    # 得到十日均线价格
    ma10 = round(df['close'][-10:].mean(), 3)

    # 得到当前资金余额
    cash = context.portfolio.cash
    
    # 如果当前有余额，并且五日均线大于十日均线
    if ma5 > ma10:
        # 用所有 cash 买入股票
        order_value(security, cash)
        # 记录这次买入
        log.info("Buying %s" % (security))
        
    # 如果五日均线小于十日均线，并且目前有头寸
    elif ma5 < ma10 and get_position(security).amount > 0:
        # 全部卖出
        order_target(security, 0)
        # 记录这次卖出
        log.info("Selling %s" % (security))
```

回测运行后，右侧面板将输出策略评估报告：

![75%](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-backtest-report.png)

### 1.4. 复权机制

在Ptrade中，复权按以下规则进行：

* 前复权是按照回测**执行**日期，或者当下时间（交易中）进行的
* 动态前复权是按照回测当日往前做的前复权
* data取的数据都是不复权数据
* 回测框架撮合成交的机制是不复权

请读者在2018年5月10日到2018年5月14日间进行以下策略的回测：

```python
# 示例3
def initialize(context):
    # 初始化策略
    g.security = "000541.SZ"
    set_universe(g.security)

def handle_data(context, data):
    log.info("前复权不包括当前价")
    bars = get_history(3, '1d', 'close', g.security, fq='pre', include=False)
    log.info(bars)

    log.info("前复权包括当前价")
    bars = get_history(4, '1d', 'close', g.security, fq='pre', include=True)
    log.info(bars)

    log.info("动态前复权不包括当前价")
    bars = get_history(3, '1d', 'close', g.security, fq='dypre', include=False)
    log.info(bars)

    log.info("动态前复权包括当前价")
    bars = get_history(4, '1d', 'close', g.security, fq='dypre', include=True)
    log.info(bars)   

    log.info("不复权且不包括当前价")
    bars = get_history(3, '1d', 'close', g.security, fq=None, include=False)
    log.info(bars)   

    log.info("不复权且包括当前价")
    bars = get_history(4, '1d', 'close', g.security, fq=None, include=True)
    log.info(bars)    
```
在输出结果中，我们注意使用前复权时，价格都要低于动态前复权，因为在2018年5月11日之后，该标的又发生了多次复权，所以，以我们执行回测的日期来进行前复权得到的数据中，在2018年5月11日当天的收盘价，肯定要低于以2018年5月11日为基准日进行前复权的价格的。

当使用动态前复权时，在包含当天和不包含前一天时，最初的三天（即5月8日~5月9日）的价格是不一样的：

![50%](https://images.jieyu.ai/images/2023/09/lesson25-ptrade-dyna-adjust.png)

从上图可以看出，同样是动态前复权，同样是回测到5月11日这一天，但include参数不一样时，价格差异很大。当include为True时，会以当天为基准进行前复权，而当include为False时，会以前一天为基准进行前复权；而当天的data中的close，始终是不复权的，也就是等于5月11日为基准的前复权价，也就是7.23。

看上去使用动态前复权，并且设置include=True最好，但由于Ptrade的实现机制，在Ptrade中使用动态前复权，在除权当天可能是有问题的。具体详情，请见官方教程视频的第5和第6课。

## 2. QMT

QMT是北京睿智融科研发的集行情显示，投资研究和产品交易于一身，并自备完整风控系统的综合性平台。它既有传统的行情研究系统和交易系统，也有投研量化平台，可以进行策略编写、回测和程序化交易。另外，它还自带了风控系统，这也是它的一个特色。

在量化支持方面，它支持vba这种脚本语言，以对接传统的通达信公式；也支持python这种当前量化策略编写的主力语言。在程序化交易方面，它支持文件扫单模式和API模式，可以说是目前对实盘接入支持得较好的一个软件。

QMT有两种运行模式，一种称为极速策略交易系统，是全功能版本，它提供了行情、交易、策略编辑和回测等界面，量化策略在QMT内部编写、回测和执行交易；另一种是精简模式，即QMT-mini，在这种模式下，只保留了策略调度运行和交易界面，允许量化策略使用第三方框架和工具来编写、回测，并通过xtquant -- 一个python SDK来与QMT-mini通讯，以获得数据和交易接口。

### 2.1. 安装和申请量化权限

与Ptrade一样，我们无法从网上直接下载QMT软件。需要在开户后，找券商客服索取。安装后，需要申请才能开通量化权限。申请量化权限有一定的资金门槛。

安装时，我们要给QMT找一个大一点的分区，因为QMT需要缓存行情数据，这些数据占用空间比较大。

启动后，登录界面如图所示，这里的极简模式如果被选中，则登录的就是qmt-mini。当我们以外部方式运行量化策略时，就需要启动qmt-mini，xtquant sdk之所以能提供数据和交易，其实是经与qmt-mini通讯来实现的。

QMT只有windows 64位版本。它自带的策略运行环境有VBA和Python两种。VBA用来与传统行情软件中的公式保持兼容。目前自带的Python版本是3.6版本。

![50%](https://images.jieyu.ai/images/2023/09/lesson25-qmt-login.png)

### 2.2. 功能概览
#### 2.2.1. 我的板块
我的板块是一个dashboard性质的界面，主要包括大盘指数、自选股、热门板块、策略列表、 快速交易、用户信息、账户信息和运行中交易 8 个模块。

![75%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-home.png)

#### 2.2.2. 模型研究

模型研究界面提供技术指标和自定义策略模型指标的研究、编辑和回测。

![75%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-research.png)

下面我们介绍模型研究界面下的几个子功能。

##### 2.2.2.1. 新建策略
我们在模型研究tab页，找到新建策略，即可创建新的python量化策略。注意第一次运行时，需要点击“下载Python库”这个按钮，来初始化python运行环境。

![75%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-new-strategy-1.png)
##### 2.2.2.2. 启动和停止策略

当策略编写好后，点击编译以保存策略。这里的按钮名字不是保存，而是编译，是因为QMT还支持其它开发语言。保存之后，可以点击运行。QMT会快速运行这个策略，以便我们检查编写中的各种错误。

![75%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-editor-1.png)

进行回测时，往往有一些参数要指定。这些参数大致可以分为两类，一类是策略本身的参数，比如双均线策略中的长短周期；另一类则是回测相关的参数，比如佣金、滑点、起止时间等。

在backtrader这样的框架中，所有的参数都通过代码来指定；在一些集成回测框架，比如Ptrade和聚宽中，回测相关的参数可以在界面上指定；而在QMT中，无论是哪一类参数，一般都通过界面来指定。在老一点的股票软件，比如通达信中，用户可以通过公式来决定买卖点，这些公式需要从界面上设置一些参数。QMT通过VBA策略，保留了大多数公式。QMT这么设计，应该是要要保持与这些功能兼容。

QMT一共提供了三个信息卡来进行设置。其一是基本信息。在这里最重要的是默认周期、默认品种和复权方式。
##### 2.2.2.3. 基本信息

在基本信息设置中，最重要的是默认周期、默认品种和复权方式。QMT中量化策略都有自己的绘图界面，在这个界面中，存在一个主图和若干个副图。主图为k线图，因此需要我们指定k线显示的品种、周期和复权方式。这是这些设置的意义所在。

![33%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-editor-2.png?1)

这里的启动本地Python选上后，就可以通过第三方IDE来编写策略。
##### 2.2.2.4. 回测参数

参数设置选项卡用来设置策略自身能用到的参数。比如在这个图中，我们设置了win这个参数，这样我们就可以在策略中直接使用win这个变量。

这里还提供了参数优化功能（参照backtrader的optstrategy）。

![33%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-editor-3.png?1)

##### 2.2.2.5. 参数设置

参数设置选项卡用来设置策略自身能用到的参数。比如在这个图中，我们设置了fast_peroid这个参数，这样我们就可以在策略中直接使用win这个变量。

```python

def init(ci):
	#init handlebar函数的入参是ContextInfo对象 可以缩写为ci
	#line1和line2分别为两条均线期数
	ci.line1 = fast_peroid   #快线参数
	ci.line2 = slow_peroid   #慢线参数
  
```

init是在QMT策略中必写的方法。它接受一个参数，ContextInfo对象，在QMT中，大量方法封装在这个对象中。

公式测评还提供了参数优化功能，相当于策略参数优化，参照backtrader的optstrategy。

![50%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-editor-4.png?1)

##### 2.2.2.6. 补充数据

在QMT中，数据获取共分两步。首先是将数据下载到本地缓存，然后才是调用api来从本地读取数据。这种做法，在性能上要优秀很多（类似于大富翁框架，只不过大富翁框架会以服务的方式运行Omega，始终保持对全市场为数据的实时同步，因此普通用户只需要了解一步操作）。因此，在回测之前，我们需要先通过数据管理功能，将必要的数据保存到本地。

![75%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-download-data.png)
#### 2.2.3. 模型交易
我们在模型交易界面安排策略进入模拟或者实盘。模型运行过程中，会有相应的模型交易信号，输出日志，持仓，委托和成交产生。

![75%](https://images.jieyu.ai/images/2023/09/lesson24-qmt-trade.png)

## 3. QMT-Mini
前面讲过，QMT有一个极简版本，具有高稳定运行、高频率访问、高性能响应、低延迟报单等特点，同时支持 DBF、TXT、CSV 格式文件交互，支持 Python 和 C++策略。

QMT极简版本可以说是专门为QMT体外运行策略而设计的。我们通过xtquant这个python包来与QMT-mini通讯，从而使用QMT提供的行情服务和交易功能。

在极简模式下，界面上只提供了交易功能和策略调度功能（即，安排策略进入实盘运行）。交易部分又分为手工交易和策略交易。手工交易提供了紧急情况下执行交易的可能。策略交易则允许我们通过文件扫单的方式与独立运行的量化策略进程集成。文件扫单方式我们在上一课已经讲过了，这一课里，我们将只介绍通xtquant sdk来与QMT-mini集成的方式。

!!! attention
    在使用xtquant sdk时，本机必须有QMT-mini在运行。两者之间通过socket进行通讯。

我们再次重温一下，进行极简模式的方式是：

![](https://images.jieyu.ai/images/2023/09/lesson25-qmt-login.png)


登录后，我们还可以在 设置 > 自动重启 设置中，选中 启用开机自动启动 和 启用自动初始化 选项。


在外部使用xtquant时，我们需要安装xtquant。xtquant没有提供whl的安装方式，只能通过源代码拷贝的方式安装。

我们在完全模式下，下载安装Python库之后，在图中所示位置的Lib/site-packages下，就可以找到一个名为xtquant的目录。

![50%](https://images.jieyu.ai/images/2023/09/lesson25-qmt-mini-download-python.png)

假设我们为量化策略创建了一个名为xt的conda 虚拟环境。为了在这个虚拟环境下使用xtquant，我们有以下几咱方法：

1. 将xtquant目录拷贝到虚拟环境的lib/site-packages目录下。这种方法的缺点是，如果xtquant的版本更新了，我们将无法自动获得更新。
2. 建立从虚拟环境的lib/site-packages/xtquant到QMT中上述xtquant目录位置的软链接。这需要有一定的windows操作经验。其优点是，当xtquant版本得到更新，我们的量化策略也将自动获得此更新。
3. 在量化策略初始化时，加入以下代码

```python
import sys

path = r'c:\gszqqmt\bin.x64\Lib\site-packages'
sys.path.append(path)
```

这样也会在当前环境下，自动加入对xtquant库的引用。

!!! tip
    在conda环境下，我们可以用命令 `conda info`来查找当前虚拟环境的文件目录。在`conda info`的输出中，有一项为"active env location"，我们要找到Lib\site-packages子目录就位于该目录下。

xtquant包含两个模块，其一是XtData，Xtdata作为行情模块，本模块旨在提供精简直接的数据满足量化交易者的数据需求，主要提供行情数据（历史和实时的K线和分笔）、财务数据、合约基础信息、板块和行业分类信息等通用的行情数据。

另一个是XtTrader, XtTrader作为交易模块，封装了策略交易所需要的Python API接口，可以和MiniQMT客户端交互进行报单、撤单、查询资产、查询委托、查询成交、查询持仓以及接收资金、委托、成交和持仓等变动的主推消息。

下面，我们就介绍一下这两组API。

## 4. XtData

qmt的文档一般把证券品种称为合约。所以证券代码在其文档中，常称为合约代码。xtquant中，证券代码格式是后缀式，上交所代码为SH，深交所为SZ。

| 市场     | 代码 |
| -------- | ---- |
| 上交所   | SH   |
| 深交所   | SZ   |
| 上期所   | SF   |
| 大商所   | DF   |
| 郑商所   | ZF   |
| 中金所   | IF   |
| 能源中心 | INE  |
| 沪港通   | HGT  |
| 深港通   | SGT  |

所以，平安银行的代码是000001.SZ，浦发银行的代码是600000.SH。

我们在最早的课程中就讲过，数据源最基础的API就是证券列表、市场日历和行情数据。我们先来看如何获取证券列表。

### 4.1. 获取证券列表

```python
# 示例4
from collections import defaultdict

sectors = defaultdict(list)
prefixes = [
    '1000SW', '500SW', '300SW', 'CSRC', '迅投一级', '迅投二级', '迅投三级', 'HKSW',
'TGN', 'TDY', 'THY', 'DY1', 'DY2', 'SW1', 'SW2', 'SW3', 'ETF','GN', '转债', '国证', '上证', '沪深', '中证','深证'
]
for item in get_sector_list():
    for i in range(6, 1, -1):
        key = item[:i]

        if key in prefixes:
            sectors[key].append(item)
            break
    else:
        sectors['NA'].append(item)

print(sectors['GN'][:10])
all_stocks = get_stock_list_in_sector('沪深A股')
print(f"当前共计{len(all_stocks)}股A股")
print(all_stocks[:5])
```

xtquant把证券进行了板块分类，在知道板块名字的情况下，就可以通过get_stock_list_in_sector获取板块的全部成分股。板块分类则可以通过get_sector_list来获取。

上述归类中，SW代表申万行业分类，CSRC为证监分行业分类。除引之外，我们猜测，GN代表概念板块，DY代表地域板块，TGN可能代表同花顺概念板块。没有文档明确说明这些板块的编制者是谁，以及如何编制，数据发布的时间。

我们注意到，无论是get_sector_list还是get_stock_list_in_sector，都无法传入时间，考虑到xtquant可以在QMT外部运行，所以在回测期间，这样的调用是无法获取当时点的板块数据的。因此作为辅助数据比较好，如果用作量化，可能会导致使用未来数据。

上述示例还演示了如何获取全市场证券列表，即通过get_stock_list_in_sector，传入板块名"沪深A股"即可。

示例4的输出如下：

```
[
    'GN3D感应', 'GN3d打印', 'GN3D玻璃', 'GN4680电池', 
    'GN4D打印', 'GN5G', 'GN5G主设备', 'GN5G运营商', 
    'GN6G', 'GNAH溢价股'
]

当前共计5059股A股

[
    '601882.SH', '603995.SH', '601128.SH', 
    '600740.SH', '603909.SH'
]
```

在获取到证券列表后，我们可以通过以下方法，来得到某支证券的基本信息：

```python
# 示例5
get_instrument_detail('000001.SZ')
```
我们将得到一个较长的输出。这些输出中，最重要的有：

* InstrumentName: 该证券的显示名，如平安银行
* OpenDate: 该证券的IPO日
* ExpireDate: 该证券的退市日，如果未退市，则为99999999
* UpStopPrice: 当日涨停价
* DownStopPrice: 当日跌停价

get_instrument_detail只能获取到当前的数据，而无法获取历史PIT数据。因此，我们在回测中，不能依赖它返回的信息。

### 4.2. 获取交易日历

我们通过get_trading_dates来获取交易日历。它的参数依次是：
* 市场代码，比如获取上交所交易日历，使用SH
* start_time 开始时间，8位字符串，比如20200101
* end_time 结束时间，字符串
* count 返回记录数，-1为全部返回

返回值是List[Int64]，是毫秒为单位的unix epoch时间。

!!! attention
    xtquant许多函数在参数校验和错误提示方面还需要完善。比如在本函数中，如果end_time的格式不是象‘20230808’这样的8位数字，而是象‘2023-08-08’这样的表示法（尽管这是一个合法的日期格式）get_trading_dates也不会报错，而是悄悄地返回一个空数组。


```python
# 示例6
import numpy as np

days = get_trading_dates('SH', start_time='', end_time='', count=10)
np.array(days, dtype='datetime64[ms]').astype(datetime.date)
```

其输出结果如下：

```
    array([datetime.datetime(2023, 8, 31, 16, 0),
        datetime.datetime(2023, 9, 3, 16, 0),
        datetime.datetime(2023, 9, 4, 16, 0),
        datetime.datetime(2023, 9, 5, 16, 0),
        datetime.datetime(2023, 9, 6, 16, 0),
        datetime.datetime(2023, 9, 7, 16, 0),
        datetime.datetime(2023, 9, 10, 16, 0),
        datetime.datetime(2023, 9, 11, 16, 0),
        datetime.datetime(2023, 9, 12, 16, 0),
        datetime.datetime(2023, 9, 13, 16, 0)], dtype=object)
```
在多数情况下，这里的时间转换是必须的。我们注意到输出数组是object类型，即每一个元素都已经是python的datetime.date对象了。

在量化环境下，我们一般很难离得开numpy，所以使用上面的代码进行转换是OK的。

不过，qtm官方也建议了以下转换方式：

```python
import time
def conv_time(ct):
    '''
    conv_time(1476374400000) --> '20161014000000.000'
    '''
    local_time = time.localtime(ct / 1000)
    data_head = time.strftime('%Y%m%d%H%M%S', local_time)
    data_secs = (ct - int(ct)) * 1000
    time_stamp = '%s.%03d' % (data_head, data_secs)
    return time_stamp

conv_time(1693152000000)
```
输出结果为:

```
'20230828000000.000'
```


### 4.3. 获取行情数据

前面介绍过，在xtquant中，获取数据是分两阶段的。首先，我们下载历史数据，或者订阅数据。然后，我们通过get_market_data等方法来读取本地已缓存的数据。

我们先看如何下载历史数据并读取。

```python
# 示例7
from xtquant.xtdata import *

stocks = ['000001.SZ', '600000.SH']
end = "20050105"

download_history_data(stocks[0], '1d')
download_history_data(stocks[1], '1d')

bars = get_market_data(stock_list=stocks, period='1d', end_time=end, count=3)
print(bars.keys())
display(bars['time'])
print(type(bars['open']))
```

我们使用download_history_data来为单只股票下载历史行情。该函数并不直接返回数据。相反，它将数据缓存在本地。我们得通过get_market_data来获取数据。

我们没有指定起止时间，所以xtquant把全部的数据给给了我们。第一个参数是证券代码，只接收str作为参数，不能传入List[str]。第二个参数是行情的周期，合法的值有'tick', '1m', '5m'和'1d'。

然后，我们通过get_market_data来从本地读数据。我们特别选取了20050105作为end_time，结果发现，当读取3个日线bar时，它取回了包括2004年的数据。因此，在xtquant中，数据的起始时间早于2005年。

get_market_data的第一个参数是一个List[str]，即可以传入多个证券代码。它的返回值是Dict[str, pd.DataFrame]：

![](https://images.jieyu.ai/images/2023/09/lesson25-xtquant-dhd.png)

返回的字典中，有哪些项取决于我们在get_market_data的fields参数中传入哪些字段。当周期参数period为’tick'时，字段与其它周期是不同的。具体请参考文档。

如果我们要下载多支股票的历史行情数据，使用download_history_data就没有那么方便了。此时我们应该使用download_history_data2，它允许同时下载多支股票的历史行情。它与download_history_data的不同之处有二，其一，第一个参数是List[str]，而不是单支股票代码。其二，我们需要传入一个callback函数。

```python
# 示例8

def on_progress(data):
    '''补充历史数据回调函数'''
    print(data) 
    

stock_list = ['603909.SH','300450.SZ','600740.SH']
field_list = ['time','open','close','low','high','volume']

download_history_data2(stock_list, 
                       period='1d', 
                       start_time='20230201', 
                       end_time='20230223', 
                       callback=on_progress
                    )

print('download_history_data2 finished')

# 获取股票close数据
ret = get_market_data(field_list, 
                      stock_list, 
                      period='1d', 
                      start_time='', 
                      end_time='', 
                      count=5, 
                      dividend_type='front', 
                      fill_data=True
                      )

print(ret['close'].T)
```

如果我们要在盘中实时获取最新的数据，此时使用download_history_data就显得不够高效，此时，应该改用subscribe_quote来订阅更新。但获取数据仍然是get_market_data。

```python

def on_data(datas):
    for stock_code in datas:
        	print(stock_code, datas[stock_code])

subscribe_quote(stock_code, period='1d', start_time='', end_time='', count=0, callback=None)
```

如果是我们的策略转实盘，则可以在on_data中调用策略的next方法。我们也可以自己周期性地调用next方法，在其中通过get_market_data来获取最新的数据，以决定是否会触发交易信号。

## 5. XtTrader
xttrader是基于迅投MiniQMT衍生出来的一套完善的Python策略运行框架，对外以Python库的形式提供策略交易所需要的交易相关的API接口。该接口需开通A股实盘版权限方可登录使用。

XtTrader支持多线程操作，这样可以方便进接受回调消息。在实盘交易中，一个委托往往要等待一段时间才能成交，拥有回调机制是很重要的。在交易函数方面，它提供了两类交易函数，一类是同步函数，另一类是异步函数。异步函数都在函数名后面增加了_async后缀。

我们将通过一个demo来讲解XtTrader的生命周期及主要交易函数的用法。下图显示了它的主要生命期。

![50%](https://images.jieyu.ai/images/2023/09/lesson25-xttrade-lifecycle.png)

```python
#coding=utf-8
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
import numpy as np


class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def on_disconnected(self):
        """
        连接断开
        :return:
        """
        print("connection lost")

    def on_stock_order(self, order):
        """
        委托回报推送
        :param order: XtOrder对象
        :return:
        """
        print("on order callback:")
        print(order.stock_code, order.order_status, order.order_sysid)
 
    def on_stock_trade(self, trade):
        """
        成交变动推送
        :param trade: XtTrade对象
        :return:
        """
        print("on trade callback")
        print(trade.account_id, trade.stock_code, trade.order_id)

    def on_stock_position(self, position):
        """
        持仓变动推送  注意，该回调函数目前不生效
        :param position: XtPosition对象
        :return:
        """
        print("on position callback")
        print(position.stock_code, position.volume)

    def on_order_error(self, order_error):
        """
        委托失败推送
        :param order_error:XtOrderError 对象
        :return:
        """
        print("on order_error callback")
        print(order_error.order_id, order_error.error_id, order_error.error_msg)

    def on_cancel_error(self, cancel_error):
        """
        撤单失败推送
        :param cancel_error: XtCancelError 对象
        :return:
        """
        print("on cancel_error callback")
        print(cancel_error.order_id, cancel_error.error_id, cancel_error.error_msg)

    def on_order_stock_async_response(self, response):
        """
        异步下单回报推送
        :param response: XtOrderResponse 对象
        :return:
        """
        print("on_order_stock_async_response")
        print(response.account_id, response.order_id, response.seq)

    def on_account_status(self, status):
        """
        :param response: XtAccountStatus 对象
        :return:
        """
        print("on_account_status")
        print(status.account_id, status.account_type, status.status)

# path为mini qmt客户端安装目录下userdata_mini路径
path = 'D:\\国金QMT交易端模拟\\userdata_mini'
session_id = np.random.randint(20000, 60000)
xt_trader = XtQuantTrader(path, session_id)

# path为mini qmt客户端安装目录下userdata_mini路径
path = 'D:\\国金QMT交易端模拟\\userdata_mini'

session_id = np.random.randint(20000, 60000)
xt_trader = XtQuantTrader(path, session_id)

# 创建交易回调类对象，并声明接收回调
callback = MyXtQuantTraderCallback()
xt_trader.register_callback(callback)

# 启动交易线程
xt_trader.start()
# 建立交易连接，返回0表示连接成功
connect_result = xt_trader.connect()
assert connect_result == 0

acc = StockAccount('********', 'STOCK')
# 对交易回调进行订阅，订阅后可以收到交易主推，返回0表示订阅成功
subscribe_result = xt_trader.subscribe(acc)
assert subscribe_result == 0
```

这段代码实现了到订阅账号通知时为止的主要过程。

首先我们定义了XtQuantTraderCallback的一个子类。这个子类的定义和使用并不是必须的。但是，我们知道，在实盘中，报单之后，往往并不能马上返回委托执行的结果，因此，使用回调机制来得到委托状态更新是必要的。

实例化XtQuantTrader时，需要传入两个参数。其中之一是极简客户端安装目录下userdata_mini的路径。第二个参数则是会话ID，它是一个整数。每一次调用connect，理论上都应该使用一个新的、惟一的id。

接下来，我们注册了处理callback事件的自定义类。但是，我们还没有发起消息订阅，只有当消息订阅成功，这个自定义类才会接收到消息。

我们通过xt_trader.start()来启动交易线程。通过connect方法连接到qmt mini。如果连接成功，返回值应该为0。

我们通过以下代码来实现交易和账户相关信息查询。

```python
stock_code = '600000.SH'
print("order using the fix price:")
order_id = tr.order_stock(acc, 
                        stock_code, 
                        xtconstant.STOCK_BUY, 
                        200, 
                        xtconstant.FIX_PRICE, 
                        10.5, 
                        'strategy_name', 
                        'remark'
                        )
print(order_id)
```
这段代码实现了买入委托。如果出现错误，则我们会得到一个小于0的订单号；如果成功，则会得到一个大于0的订单号。

如果要撤单，则使用下面的代码:

```python
print("cancel order:")
cancel_order_result = xt_trader.cancel_order_stock(acc, fix_result_order_id)
print(cancel_order_result)
```

通过下面的代码查询证券资产：

```python
print("query asset:")
asset = xt_trader.query_stock_asset(acc)
if asset:
    print("asset:")
    print("cash {0}".format(asset.cash))
```

通过下面的代码查询委托：

```python
print("query order:")
order = xt_trader.query_stock_order(acc, fix_result_order_id)
if order:
    print("order:")
    print("order {0}".format(order.order_id))
```


### 5.1. 封装成web服务

最简单的情况下，我们可以这样使用xttrader: 策略框架本身应该设计成为后台程序，在它运行期间，xttrader的实例化对象是一个全局变量。我们通过这个全局变量来执行交易操作。如果需要接收回调消息，也可以通过此对象来注册回调响应类，并且订阅账户变动通知消息。


在需要交易的时候，我们在策略的主线程中，具体地说，可能是在生成交易信号的地方，比如backtrader中的next方法中，我们调用xtrader的order函数来执行交易。为了兼顾回测，我们需要在这些地方加上条件判断，以便在不同的场景下，调用不用的交易函数。当然，对于初始化代码也是一样。

我们也可以考虑像zillionare-trader-client那样，仅用一套API，服务两种场景，只需要进行服务器地址切换即可。这样，我们首先要对xttrader进行web封装。

对xttrader进行web封装还有另一层好处。xttrader必须与qmt-mini运行在同一台机器上，如果我们不把它转换成为web服务的话，意味着我们的策略也必须fcgtf在Windows机器上。但是，很多人更愿意使用Linux作为策略运行的服务器。

下面，我们就给出一个简单的封装示例代码。

```python
from xtquant import xtdata, xttrader, xtconstant
from xtquant.xttrader import XtQuantTrader
from sanic import Sanic, Blueprint, response
 
api = Blueprint('xt', url_prefix='/xt/trade')

trader = None

@api.listener('before_server_start')
async def before_server_start(app, loop):
    """初始化xtrader clients"""
    global trader

    # read config, get qmt_dir
    qmt_dir = "d:\\qmt"
    session_id = random.randint(20000, 60000)
    trader = XtQuantTrader(qmt_dir, session_id)

    # 启动交易线程
    trader.start()
    # 建立交易连接，返回0表示连接成功
    trader.connect()
 
@api.listener('after_server_stop')
async def after_server_stop(app, loop):
    '''关闭session'''
    trader.stop()

```

这是利用sanic的钩子，实现trader的实例化、启动和退出。这里我们没有做错误处理，实际上，如果启动和连接不成功，我们应该进行重试，要么就需要中断sanic服务。

```python

@api.route('/query/assets', methods=['POST'])
async def query_assets(request):
    '''
    查询总资产
    '''
    global trader
    asset = trader.query_stock_asset(trader.account)
    return response.json({"总资产": asset.total_asset, 
                         "现金": asset.cash, 
                         "持仓市值": asset.market_value, 
                         "冻结金额": asset.frozen_cash
                         })

@api.route('/place_order', methods=['POST'])
async def trade_place_order(request):
    '''
    下单
    '''
    global trader
    stock_code = request.args.get('stock_code')
    volume = int(request.args.get('volume'))
    price = float(request.args.get('price'))

    if stock_code is None or volumn is None or price is None
        # error handling
        return

    if request.args.get('direction', 'buy') == 'buy':
        direction = xtconstant.STOCK_BUY 
    else:
        direction = xtconstant.STOCK_SELL

    order_id = trader.order_stock(trader.account, 
                                  stock_code, 
                                  direction, 
                                  volume, 
                                  xtconstant.FIX_PRICE,
                                   price, 
                                   'strategy_name', 
                                   'remark')

    return response.json({'order_id': order_id})

if __name__ == '__main__':
    app = Sanic(name='xtquant')
    app.config.RESPONSE_TIMEOUT = 600000
    app.config.REQUEST_TIMEOUT = 600000
    app.config.KEEP_ALIVE_TIMEOUT = 600

    # add route
    app.blueprint(api)
    app.run(host='0.0.0.0',
            port=7878, 
            workers=1, 
            auto_reload=True)
```

上面的代码增加了交易接口，我们可以仿此例增加更多交易接口。最后我们启动了sanic服务器，接下来就可以通过http client，发出交易请求了。

我们可以在这个例子基础上进一步修改，增加消息回调的处理，也增加客户端查询回调消息的接口。
