
---
title: 使用Zillionare来获取数据
---

## 1. Omicron
### 1.1. 初始化 omicron

```python
import cfg4py

cfg = cfg4py.init("/etc/zillionare")

from coretypes import FrameType
import omicron

await omicron.init()
```

### 1.2. 实时股票数据

```python
code = "000001.XSHE"

from omicron.models.stock import Stock

await Stock.get_latest_price([code])
```

### 1.3. 股票历史数据

```python
await Stock.get_bars(code, 2, FrameType.DAY)
```

### 1.4. 证券列表

```python
import datetime
from omicron.models.security import Security

secs = (
    await Security.select()
    .types(["stock"])
    .exclude_st()
    .exclude_cyb()
    .alias_like("平安")
    .eval()
)
for sec in secs:
    print(await Security.info(sec))
```

### 1.5. 交易日历

```python
from omicron import tf

start = datetime.datetime(2023, 4, 18, 15)
end = datetime.datetime(2023, 4, 23, 10, 30)

moment = datetime.date(2023, 4, 23)
tf.int2date(tf.date2int(moment))

tf.combine_time(moment, 14, 55)

tf.day_shift(moment, 0)
```

### 1.6. 板块数据

我们通过 omicron.models.board 这个模块来获取板块相关的信息。
它的数据来自于同花顺网站
是通过 akshare 来抓取的

这个模块提供了以下方法：
* board_info_by_id, 通过板块代码来查询板块的名字、成员数目及成员列表
* board_info_by_security, 查询某支证券所属的板块
* fuzzy_match_board_name, 按板块名进行模糊匹配
* get_bars_in_range, 获取板块的行情数据

```python
# 获取某个板块的板块信息

from omicron.models.board import Board, BoardType

Board.init('192.168.100.101')

board_code = '881128' # 汽车服务 可自行修改
board_info = await Board.board_info_by_id(board_code)
print(board_info) # 字典形式
```

```python
# 获取某支股票的板块信息

stock_code = '002236'  # 大华股份，股票代码不带字母后缀
stock_in_board = await Board.board_info_by_security(stock_code, _btype=BoardType.CONCEPT)
print(stock_in_board)
```

```python
# 模糊匹配板块名，这在追概念炒作时比较有用。比如，我们只知道最近在炒汽车
# 但不知道究竟有哪些板块是属于汽车板块的，就需要进行这个查询

await Board.fuzzy_match_board_name("汽车", BoardType.INDUSTRY)
```

```python
# 获取某个板块的行情数据。 比如，我们的策略可能是
# 只有在板块走好的情况下，才去选个股，这时我们就需要板块的行情数据
import datetime

start = datetime.date(2022, 9, 1)  # 起始时间， 可修改
end = datetime.date(2023, 3, 1)  # 截止时间， 可修改
board_code = '881128' # 汽车服务， 可修改
bars = await Board.get_bars_in_range(board_code, start, end)
bars[-3:] # 打印后 3 条数据

```

## 2. 数据解读 A 股: 投资者人数与市场走势关系
到目前为止，我们已经介绍完了所有的数据源，掌握了他们最基础的用法。这里，我们也稍微拓展一下。
让我们来获取每月的投资者人数数据，并与上证指数的走势进行一个对比，看看会不会有什么规律。

A 股是一个散户市场。这个判断是基于两个事实，一是散户人数多，资金总量占比大；二是基金操作也呈散户化趋势。因此，在 A 股，新开户账户数与 A 股走势有关联性。

下面，我们通过 akshare 来获取 A 股开户数，并绘制与上证指数走势的关系图。

首先，我们取投资者账户数据，它没有参数，我们只能根据它返回的数据，再来对应上证指数。

```python
import akshare as ak

accounts_df = ak.stock_account_statistics_em()
print(accounts_df)
```

这样我们得到了2015年4月以来的投资者账户数据。它的返回值中，有一列为数据日期，其格式形如"2015-04"，我们需要将其转换成为正规的日期格式。

```python
# 将返回数据日期转换成为规整的日期格式
start = accounts_df.iloc[0]["数据日期"]
end = accounts_df.iloc[-1]["数据日期"]

yr, month = end.split("-")
end = datetime.date(int(yr), int(month) + 1, 1)
end = tf.floor(end, FrameType.MONTH)

yr, month = start.split("-")
start = datetime.date(int(yr), int(month) + 1, 1)
start = tf.floor(start, FrameType.MONTH)

print(start, end)
```

现在，我们来取对应的上证指数，并将其绘制成图：

```python
import plotly.express as px
import pandas as pd

bars = await Stock.get_bars_in_range("000001.XSHG", FrameType.MONTH, start, end)

df = pd.DataFrame({"xshg": bars["close"][::-1] / 10, "investor": accounts_df["新增投资者-数量"]})
df.index = accounts_df["数据日期"]

fig = px.line(df)
fig.update_layout(hovermode="x unified")
fig.show()
```

可以看出，月新增投资者的人数的上限大约是在220万左右，下限在100万左右。这个上下限，与随后上证的指数走势，有一定的相关性。目前我们可以数出来的是，大约新增投资者人数处于下限时，上证大约有70%左右的概率处于底部；新增投资者人数处于上限时，上证也大约有70%处在头部。

这只是一个视觉上的规律。我们会在课程的第三部分，介绍如何用统计学的规律来描述这种相关性。另外，在学习了第三部分之后，我们也将知道：如果当月新增投资者人数达到200万，下个月投资者人数继续增加的可能性会是多少？

这一点很重要。毕竟，在击鼓传花的游戏中，鼓点是不能停的。
