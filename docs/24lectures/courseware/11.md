---
title: Python与数据科学（1）
mainfont: WenQuanYi Micro Hei
puppeteer:
    format: "A4"
    scale: 1
    margin:
        top: 2.5 cm
        right: 2cm
        bottom: 2.5 cm
        left: 2cm
    displayHeaderFooter: true
    headerTemplate: '<div style="width:100%; text-align:center; border-bottom: 1pt solid #eeeeee; margin: 20px 10px 10px; font-size: 10pt;padding-bottom:10px"><span class=title></span></div>'
    footerTemplate: '<div style="display:flex; justify-content:space-around;width:100%;border-top: 1pt solid #eeeeee; margin: 10px 10px 20px; font-size: 8pt;color:#aaa"><div style="width:30%"><span class=pageNumber></span>/<span class=totalPages></span></div><div style="width:30%">大富翁量化课程</div><div style="width:30%">宽粉（quantfans_99)</div>'
  margin: {
    top: "65px",
    bottom: "70px",
    right: "30px",
    left: "30px"
  }
---

## 1. 考察数据分布

在网格交易策略中，我们没有考虑进入和退出条件。我们甚至设计了动态跟踪趋势网格，试图捕捉上升期间的行情，但是，如果能判断什么时候不适合网格交易，从而停止网格交易策略，改用其它策略，会不会更好？

下面，我们就来看一个例子，看是否能找到一些线索。

```python
from coursea import *
await init()

from plotly.subplots import make_subplots
import plotly.graph_objects as go

fig = make_subplots(rows=1, cols=3)

code = "603083.XSHG"
end1 = datetime.date(2023, 4, 20)
bars1 = await Stock.get_bars(jqkj, 120, FrameType.DAY, end= end1)
cs1 = Candlestick(bars)

end2 = datetime.date(2023, 1, 19)
bars2 = await Stock.get_bars(jqkj, 120, FrameType.DAY, end= end2)
cs2 = Candlestick(bars2)

end3 = datetime.date(2022, 4, 29)
bars3 = await Stock.get_bars(jqkj, 120, FrameType.DAY, end= end3)
cs3 = Candlestick(bars3)

fig.add_trace(cs1.figure.data[0], row=1, col=1)
fig.add_trace(cs2.figure.data[0], row=1, col=2)
fig.add_trace(cs3.figure.data[0], row=1, col=3)
```

![100%](https://images.jieyu.ai/images/2023/06/is_ma20_good_enough.png)
<figcaption>20日均线一定是好的中枢价格吗？</figcaption>

上图左一和右一，由于暴涨暴跌的原因，近期股价离均线、离前期平台较远，不太适合使用网格交易策略。如果我们是人工来进行择时的话，很有可能会在这两种情况下停掉网格交易。

如果是以量化的角度，我们有什么办法来中止（或者拒绝进入）这个时间段的网格交易呢？方法之一，就是考察近段时间的股价分布。如果股价分布比较集中，说明它还处在震荡期；否则，则可能是左一或者右一的情况。

股价的分布问题，就可以看作是一个统计上的数据分布问题来进行研究。在对数据进行任何建模之前，我们往往都需要大致了解数据的分布情况，以便有的放矢地选择模型。一般而言，考察数据的分布，主要是要了解数据的中心趋势、离散程度和分布形态。

我们可以通过均值、中位数、众数等概念来描述数据的中心（和质心）分布；通过极差、方差、标准差、分位数等来量化数据的分散程度，通过偏度、峰度来分析数据的分布形态。

### 1.1. 寻找数据的中心

#### 1.1.1. 均值和质心

均值是数列的算术平均值，反应了样本的**集中趋势**，它在数值上等于有效数值的和除以有效数值的个数。我们一般使用 `np.mean` 来计算（如无特别说明，在pandas、statistics、scipy.stats中可能会存在同名方法，下同）。

如果是多维数据，多个维度在均值上重合的部分，可认为是数据的质心(centroid)。有些场合，我们可以用质心点代表一组数据样本。显然，不同的维度往往有不同的量纲，在寻找质心的过程中，我们可能需要进行归一化或者scaling相关的操作。

![50%](https://images.jieyu.ai/images/2023/06/centroid.png)

<figcaption>图片来源《鸢尾花书系列-统计至简》图21。</figcaption>

上图中，分别以红、绿、蓝色的三个 X 标记了各个分类的质心。

#### 1.1.2. 中位数

中位数是排序后**中间位置**的值，当数列长度为偶数时，取中间两个值的均值。我们一般使用 `np.median` 来计算。

为了帮助大家更好地理解中位数，我们这里给出求中位数的示例代码：
```python {cmd=true}
import numpy as np
import pandas as pd

def median(arr):
    idx = np.argsort(arr)
    half_idx = len(arr) // 2
    sx = idx[half_idx]

    if len(arr) == len(arr) // 2 * 2:
        ex = idx[half_idx - 1]
        return (arr[sx] + arr[ex]) / 2

    return arr[sx]

arr = np.array([35, 17, 9, 22, 1])

odd = median(arr)
print(f"odd-sized array: {odd}")
assert odd == np.median(arr)

arr = np.append(arr, [6])
even = median(arr)
print(f"even-sized array: {even}")
assert np.isclose(even, np.median(arr))
```

#### 1.1.3. 众数

众数是数列中出现次数最多的数字，当多个数字都出现最高次数时，多个数字都是众数。numpy中没有计算众数的方法，我们一般使用 `scipy.stats.mode` , `statistics.mode`[^statistics] 或者 `df.mode` 来计算。对离散变量，我们一定可以找出它的众数，但对连续随机变量，寻找众数不一定有意义，此时可用改用直方图来寻找分布最多的区间。

如果是出于绘图的目的（见 `skew_examples` )，众数的位置可以通过 `pdf` 最大值所处的位置来定位，毕竟，这就是 `pdf` 的物理意义所在。关于 `pdf` ，将在[频数、pmf、pdf...](#23-频数pmfpdfcdf和直方图)那一节中介绍。


### 1.2. 量化数据的分散程度

量化数据分散程度最简单的方法查看它的min, max和中位数，以及它们之间的距离。其中其最大值与最小值之间的差距又称为极差，这实际上是技术分析指标ATR[^ATR]的思想来源。不过，在这一节里，我们将介绍一些更加复杂的概念。

#### 1.2.1. 分位数

分位数亦称分位点，是指将一个随机变量的概率分布划分为几个等份的数值点，常用的有中位数（即二分位数）、四分位数、75%分位数等。我们可以使用 `np.quantile` 或者 `np.percentile` 来计算，在计算结果上两者没有区别。

我们也可以绘制简单的箱形图，来查看其分布。箱形图会显示最小值、25%分位、中位数、75%分位数和最大值：

```python
df = pd.DataFrame(data=arr)
props = dict(boxes="#B797D9", whiskers="#F2BEC7", 
            medians="#3C74A6", caps="#5BA582")
df.plot.box(color=props, patch_artist=True)
```
![100%](https://images.jieyu.ai/images/2023/06/nagwa_boxplot.png)
<figcaption>boxplot explained</figcaption>

这张图来自于这篇文章[^nagwa]。

现在，如果我们将问题中的价格波动区间绘制成箱形图，会不会有个初步的答案？

![100%](https://images.jieyu.ai/images/2023/06/box_and_candlestick.png)

从图中可以看出，显然中间的图，区间分布更为集中，适合做网格；而左右两图，都出现了较多离群值，说明波动过大，网格难以覆盖。

下面，我们将到目前为止，所有提到过的概念，通过图示的方式展现出来：

```python
from scipy import stats
import matplotlib.pyplot as plt
import numpy as np
from statistics import mode, median

np.random.seed(78)

# choose some parameters
a, loc, scale = 5.3, -0.1, 2.2

# draw a sample
data = stats.skewnorm(a, loc, scale).rvs(1000)

# estimate parameters from sample
ae, loce, scalee = stats.skewnorm.fit(data)

plt.figure()
plt.title("mean,median,percentile...")

plt.hist(data, bins=100, density=True, linewidth=1,
        alpha=0.6, fc='#EABFC7', ec='#EABFC7')

xmin, xmax = plt.xlim()
x = np.linspace(xmin, xmax, 100)
p = stats.skewnorm.pdf(x, ae, loce, scalee)

plt.plot(x, p, '#B297D5')

ymin, ymax = plt.ylim()

dmean = np.mean(data)
dmedian = median(data)
mode_pos = np.argmax(p)
mode_x = x[mode_pos]

x25 = stats.skewnorm.ppf(0.25, ae, loce, scalee)
x75 = stats.skewnorm.ppf(0.75, ae, loce, scalee)

# 在连续分布下，mode值没有意义，但其位置为pdf最大值处
plt.vlines(x=[mode_x], ymin=ymin, ymax=p[mode_pos], color='red')
plt.annotate(f"mode", xy=(mode_x, p[mode_pos]), color='red')

plt.vlines(x=[dmedian], ymin=ymin, ymax=0.4, color='#4A73A2')
plt.annotate(f"median {dmedian:.2f}", xy=(0.5, ymax * 0.9), color='#4A73A2')

plt.vlines(x=[dmean], ymin=ymin, ymax=0.4, color='#5BA582')
plt.annotate(f"mean {dmean:.2f}", xy=(dmean * 1.1, 0.4), color='#5BA582')

plt.vlines(x=[x25], ymin = ymin, ymax = ymax, color='#EEDD7C')
plt.annotate(f"25% {x25:.2f}", xy=(-0.5, ymax), color='#EEDD7C')

plt.vlines(x=[x75], ymin = ymin, ymax = ymax, color='#B297D5')
plt.annotate(f"75% {x75:.2f}", xy=(x75, ymax), color='#B297D5')

plt.show()
```

![75%](https://images.jieyu.ai/images/2023/06/put_it_all_together.png)

图中从左到右，依次为25%分位、众数、中位数（与50%分位重合）、均值和75%分位数。

这里我们用到了skewnorm这个模型，它有几个形态类参数，即`a`, `loc`和`scale`。关于这几个参数的命名和更进一步的讨论，可以查看这个链接[^scipy_stats_loc]。



#### 1.2.2. 方差和标准差

从测量的角度看，我们常常使用一组数据的均值来代表被测量属性的真实值，测量值与均值之间的差值就构成误差。方差和标准差本来是用来表示度量误差大小的方法。但这两个概念也可以延伸出来用以刻画数据本身的分散程度。

从概率统计的角度看，设总体$X$，变量数据为$N$，设$X_{i} (i=1, 2, ...,n)$为来自总体的样本，则有：

$$E(X) = \mu \tag{1.2.2a}$$
我们可通过`np.mean(X)`来计算，这个均值也被称为$X$的数学期望。

我们用方差`Variance`来描述总体中随机变量与其均值之间的偏离程度：
$$D(X) = \sigma^2 = E(X^2) - E^2(X) = \frac{\sum_{i=1}^{n}{(x_i-\mu)^2}}{N}\tag{1.2.b}$$

我们可以用`np.var`来计算方差。比如：
```python
>>> arr = np.arange(10)
>>> np.var(arr)
    8.25
```

方差的量纲与随机变量并不一致，所以我们更常使用标准差这个概念，它是通过对方差取开方得到的：
$$\sigma(X) = \sqrt\frac{\sum_{i=1}^{N}{(x_i-\mu)^2})}{N}\tag{1.2.2b}$$

我们可以通过 `np.std(X)` 来计算标准差。

```python
>>> arr = np.arange(10)
>>> np.std(arr)
    2.8722

>>> np.is_close(np.sqrt(np.var(arr)), np.std(arr), 10)
    True
```

上述方法计算出来的都是总体（population）均值、总体方差和总体标准差。

统计上还有一个样本均值、样本方差和样本标准差的概念，当我们谈及样本方差和标准差时，要注意公式是：

$$S^2 = \frac{1}{n-1}\sum_{i=1}^{n}(Y_i - \bar{Y})^2\tag{1.2.2c}$$
与总体方差的区别是，我们求平均值时，应该使用$n-1$作为分母，而不是$N$。

#### 1.2.3. 频数、PMF、PDF、CDF、PPF和直方图

即便有了上述概念，我们对数据分布的情况仍然没有全面掌握。我们仅仅是把握了它的几个关键点而已。

回想在网格交易法中，我们采用了一个给数据打格子的方法。直方图就是使用类似的方法，在一定的数据范围内，打上格子（bins），再把数据往这些格子里装，然后统计每个格子有多少数据，这个统计值就是频数(frequency count)。将频数、格子绘制成柱状图，就构成了直方图。

直方图除了可以直观地展示数据分布外，还可以判断数据是否存在离群值(outlier)。

下图显示了我们给 `data` 分组并统计频数，并在此基础上，计算概率、累积概率和概率密度的情况：

```python
from scipy import stats
import pandas as pd
import numpy as np

np.random.seed(78)

# choose some parameters
a, loc, scale = 5.3, -0.1, 2.2

# draw a sample
data = stats.skewnorm(a, loc, scale).rvs(1000)

ae, loce, scalee = stats.skewnorm.fit(data)
minx, maxx = np.min(data), np.max(data)
x = np.linspace(minx, maxx, 100)
p = stats.skewnorm.pdf(x, ae, loce, scalee)

n, edges = np.histogram(data)

df = pd. DataFrame({
    '频数<br>n': n,
    '累积频数 cumsum(n)': np.cumsum(n),
    '概率<br>PMF': n / np.sum(n),
    '累积概率<br>CDF': np.cumsum(n) / np.sum(n),
    '概率密度<br>PDF': (n/np.sum(n))/ np.diff(edges)
})

df.index = [f"[{s:.2f}-{e:.2f})" for s, e in zip(edges[:-1], edges[1:])]

style = df.style\
    .set_properties(**{'text-align': 'left'})\
    .set_table_styles(
        [dict(selector='th', props=[('text-align', 'left')])]
    )

colors = ['#5BA682', '#F2BEC7', '#B797D9', '#F2DC6A', '#3C74A6']
for col, color in zip(df.columns, colors):
    style = style.bar(subset=col, color=color)
    
style
```

显示结果如下：

![](https://images.jieyu.ai/images/2023/06/color_histo.png)

注意上图中最后三列的表头。这里的中文是正确的，而对应的英文，只在连续随机变量中才有效，而直方图表示的是各个区间对应的值--是离散变量。

#### 1.2.4. 概率密度和概率密度函数

在上图中，将每一个格子的频数除于总数( `len(data)` )，就得到了每个格子的**概率（PMF）**。

每个格子都有自己的上下界。上下界之差，构成了格距。每一行的概率除于格距，就构成了**概率密度(probability density)**。

$$
f_i = p_i/\Delta = n_i/n\Delta \tag{1.2.4a}
$$

当格距趋于无限小时，$f_i$就为**概率密度函数pdf**。

#### 1.2.5. 累积概率和累积概率函数CDF

在上图中，将每个格子的概率，从左到右加起来，直到当前的格子，就构成了当前格子的累积概率。在连续作用域下，概率密度函数的积分就成为CDF。CDF有以下特性，它永远大于零，并在正无穷处逼近1。

```python
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon     
from scipy.stats import norm

np.random.seed(78)
rv = norm(loc=0)

x = np.linspace(rv.ppf(0.001), rv.ppf(0.999), 1000)

mu = rv.stats('m')
assert mu != np.mean(rv.rvs(size=1000))

pdf = norm.pdf(x)
cdf = norm.cdf(x)

fig, ax = plt.subplots(figsize = (7, 5))
plt.plot(x, pdf, color='#B297D5', label='sample pdf')
plt.plot(x, cdf, color='#4A73A2', alpha=0.6, label='sample cdf')

pos = np.argwhere(x <= rv.ppf(0.5)).flatten()[-1]

ix= x[:pos]
iy= pdf[:pos]

a, b = x[0], x[pos]

verts = [(a, 0), *zip(ix, iy), (b, 0)]
# 通过Polygon来绘制积分部分
poly = Polygon(verts, facecolor='0.7', edgecolor='0.5')
ax.add_patch(poly)       
 
ax.text(x[pos], pdf[pos], 
        f"{cdf[pos]:.2f} ({x[pos]:.2f}, {pdf[pos]:.2f})")

ax.plot(x[pos], cdf[pos], f"{cdf[pos]:.2f}", marker='.', 
        markerfacecolor='b', markersize=12)

plt.legend(['pdf', 'cdf'])
plt.show()
```

![75%](https://images.jieyu.ai/images/2023/06/cdf_and_pdf.png)

#### 1.2.6. CDF估计及其应用

在不知道概率分布特性的情况下，要获得cdf/pdf函数往往是困难的。但是，如果我们有足够多的随机变量取值，我们可以估计出它的cdf/pdf。

这里我们需要使用 `statsmodels` 这个库:
```
!pip install statsmodels
```

接下来，我们模拟一个双峰分布，然后来求它的经验累积分布函数(ECDF, emperical cumulative distribution function):

```python
from matplotlib import pyplot
from numpy.random import normal
from numpy import hstack
from statsmodels.distributions.empirical_distribution import ECDF

# generate a sample

sample1 = normal(loc=20, scale=5, size=300)
sample2 = normal(loc=40, scale=5, size=700)
sample = hstack((sample1, sample2))

# plot the histogram
pdf, edges, _ = plt.hist(sample, bins=100, density=True)

ecdf = ECDF(sample)
plt.plot(ecdf.x, ecdf.y)
plt.show()
```
![100%](https://images.jieyu.ai/images/2023/06/双峰.png)

#### 1.2.7. 几个概念之间的关系
PMF是概率质量函数的意思，它是离散作用域下的PDF。

PDF是CDF的导数，CDF是PDF的积分。二者之间的关系可如下推导：

```python
import scipy.stats as stats
import matplotlib.pyplot as plt
import numpy as np

# shape, loc, scale - creates weibull object
wei = stats.weibull_min(2, 0, 2) 
sample = wei.rvs(1000)
shape, loc, scale = stats.weibull_min.fit(sample, floc=0) 

x = np.linspace(np.min(sample), np.max(sample))
dx = x[1]-x[0]
deriv = np.diff(wei.cdf(x))/dx
plt.hist(sample, density=True, fc="none", 
        ec="grey", 
        label="frequency")

plt.plot(x, wei.cdf(x), label="cdf", color="#6DA485")
plt.plot(x, wei.pdf(x), label="pdf", color="#B297D5")
plt.plot(x[1:]-dx/2, deriv, '.', 
        label="derivative", color="#EABFC7")
plt.legend(loc=1)
plt.show()
```
![75%](https://images.jieyu.ai/images/2023/06/weibull_cdf_pdf.png)

上图中，原始`pdf`曲线为绿色，通过`cdf`微分出来的结果，我们以红色点来表示，可以看出，它们完全重合。

CDF的反函数(inverse function)是PPF。 `skewnorm.ppf(0.99, a, loc, scale)` 给出的是在以参数 `(a, loc, scale)` 定义的偏度分布中，99%分位数对应的数值。请看下面的变换：

```python
import numpy as np
from scipy.stats import norm

np.random.seed(78)
data = np.random.normal(size=100)

loc, scale = norm.fit(data)

# 求99.5%概率处对应的值，记为v, 2.59
v = norm.ppf(0.995, loc, scale)
print(v)

# 求`v = 2.59`处的累积概率，也为0.995
print(norm.cdf(v, loc, scale))
np.isclose(norm.cdf(v, loc, scale), 0.995)
```

!!! Question
    假设现在是交易时间，沪指已经跌了4%。你应该有足够的知识来回答这个问题，沪指继续往下跌的概率是多少？如果我们希望以99%的获胜概率来抄底，那么应该等待沪指跌到多少时入场较好？

    我们把这个题留成作业题。在计算时，可以考虑取最近1000个交易日的沪指涨跌幅为原始数据。
    
对于随机序列`r`，如果已知分布，则可以通过`scipy.stats`中的各个模型来拟合；如果已知模型特征，生成随机序列，一般是通过`rvs`函数。

### 1.3. 数据的分布形态

对于未知的分布，我们不仅要了解其集中趋势和离散程度，还要知道数据分布的形状是否对称、偏斜的程度以及分布的扁平程度等，统称为分布的形态。偏态和峰态就是对分布形态的测度。其中：

偏态(skewness)一词是由统计学家皮尔逊(K. Pearson)于1895年首次提出的，它是对数据分布对称性的测度。

峰态(kurtosis)一词是由统计学家皮尔逊于1905年首次提出的。它是对数据分布平峰或尖峰程度的测度。

计算分布的偏度，我们使用以下的公式：

$$
S = skewness = \frac
                {\frac{1}{n}\sum_{i=1}^{n}(x^{(i)} - \mu_x)^3}
                {\Biggl(\frac{1}{n}\sum_{i=1}^{n}(x^{(i)}-\mu_x)^2\Biggl)^{\frac{3}{2}}} \tag{1.3.a}
$$

如果$S>0$，即为正偏，或称右偏。正偏分布**右侧尾部更长**，此时均值 > 中位数 > 众数。可以这么理解和记忆。如果在样本中引入少数几个特别大离群值，均值肯定增大（向右移动），中位数因样本数量增加，微微向右移动，众数则保持不变。

反之则为负偏，或称左偏。特点是分布的左侧尾部更长。

我们一般利用 `scipy.stats.skew` 来计算一个随机序列的偏度。

计算分布的峰度，我们有以下公式：

$$
K = kurtosis = \frac
                {\frac{1}{n}\sum_{i=1}^n\bigl(x^{(i)}-\mu_x\bigl)^4}
                {\Biggl(\frac{1}{n}\sum_{i=1}^n\bigl(x^{(i)}-\mu_x\bigl)^2\Biggl)^2}
                 \tag{1.3.b}
$$

式$1.3.b$减去3，就构成**超值峰度**，这是我们在实践中真正用到的峰度， `scipy.stats.kurtosis` 采用的峰度计算就是这个算法。以后除非特别说明，我们提到峰度时，指的都是这个峰度。

利用**超值峰度**计算出来的正态分布的峰度为0。因此，如果某个分布的峰度大于0，则为高峰态，体现为有明显的尖峰，两端有肥尾。如果小于0，则为低峰态。

我们还可以利用中位数和平均数，从视觉上区分数据分布是对称、左偏还是右偏：

```python
from scipy.stats import norm, skewnorm, skew, kurtosis
import matplotlib.pyplot as plt
import numpy as np
from scipy import stats
def skew_examples():

    """Visualize left, right, and no skew distributions."""
    np.random.seed(78)

    # create subplots
    fig, ax = plt.subplots(1, 3, figsize=(8, 3))

    # determine skew
    a = 4

    # find stats for annotation
    mean_skew_val = skewnorm.mean(a)
    median_skew_val = skewnorm.median(a)

    # get x data where PDF has value
    x = np.linspace(skewnorm.ppf(0.001, a), 
                skewnorm.ppf(0.999, a), 100)
    
    # 在连续分布中，众数出现在pdf最大值处
    mode_pos = np.argmax(skewnorm.pdf(x, a))
    mode_x = x[mode_pos]
    
    print(f"mean: {mean_skew_val:.2f},",
        f" median: {median_skew_val:.2f}, ",
        f"mode: {mode_x:.2f}, ",
        f"skew: {skew(skewnorm.rvs(x, a)):.2f}",
        f"kurtosis: {kurtosis(skewnorm.rvs(x, a)):.2f}")

    # plot left skew
    ax[0].plot(x * -1, skewnorm.pdf(x, a))
    ax[0].set_title('Left/Negative Skewed')

    # annotate left skew's mode
    ax[0].axvline(-mode_x, 0.72, 0.925, color='orange')
    ax[0].text(s='mode', x=-0.49, y=0.4, rotation=90)
    ax[0].axvline(-mode_x, 0, 0.53, color='orange')

    # annotate left skew's median
    ax[0].axvline(median_skew_val * -1, 0.52, 0.83, color='orange')
    ax[0].text(s='median', x=-0.74, y=0.25, rotation=90)
    ax[0].axvline(median_skew_val * -1, 0, 0.3, color='orange')

    # annotate left skew's mean
    ax[0].axvline(mean_skew_val * -1, 0.26, 0.77, color='orange')
    ax[0].text(s='mean', x=-0.84, y=0.1, rotation=90)
    ax[0].axvline(mean_skew_val * -1, 0, 0.09, color='orange')

    # plot no skew normal
    ax[1].plot(x, norm.pdf(x, loc=x.mean(), scale=0.56))
    ax[1].set_title('No Skew')

    # annotate mean, median, and mode
    ax[1].text(s='  mean\nmedian\n  mode', x=x.mean() - 0.25, y=0.25)
    ax[1].axvline(x.mean(), 0.5, 0.94, color='orange')
    ax[1].axvline(x.mean(), 0, 0.3, color='orange')

    # plot right skew
    ax[2].plot(x, skewnorm.pdf(x, a))
    ax[2].set_title('Right/Positive Skewed')

    # annotate right skew's mode
    ax[2].axvline(mode_x, 0.72, 0.925, color='orange')
    ax[2].text(s='mode', x=0.35, y=0.4, rotation=90)
    ax[2].axvline(mode_x, 0, 0.53, color='orange')

    # annotate right skew's median
    ax[2].axvline(median_skew_val, 0.52, 0.83, color='orange')
    ax[2].text(s='median', x=0.6, y=0.25, rotation=90)
    ax[2].axvline(median_skew_val, 0, 0.3, color='orange')

    # annotate right skew's mean
    ax[2].axvline(mean_skew_val, 0.26, 0.77, color='orange')
    ax[2].text(s='mean', x=0.72, y=0.1, rotation=90)
    ax[2].axvline(mean_skew_val, 0, 0.09, color='orange')

    # label axes and set y-axis limits
    for axes in ax:
        axes.set_xlabel('x')
        axes.set_ylabel('f(x)')
        axes.set_ylim(0, 0.75)

    plt.show()

skew_examples()
```

![100%](https://images.jieyu.ai/images/2023/06/3_skew_examples.png)

### 1.4. 中心矩的概念

从均值、方差，到偏度和峰度，我们发现从其数学公式来看，我们分别用到了误差的一到四次方。这并非偶然。

实际上，在物理学中存在名为矩（moment)的概念，正好与这些概念相对应。在物理学中，矩是表示距离和物理量乘积的物理量，表征物体的空间分布。由其定义，矩通常需要一个参考点（基点或参考系）来定义距离。如力和参考点距离乘积得到的力矩（或扭矩），原则上任何物理量,如质量，电荷分布等，和距离相乘都会产生矩。

因此，当我们来描述数据分布时，使用矩的概念也就非常自然了。

这里，零阶矩表示随机变量的总概率，也就是 1。常用的中心矩为一至四阶矩，分别表示数据分布的位置、分散度、偏斜程度和峰度程度。

如果你在scipy.stats的相关方法中见到`moment`或者`moments`这个参数，它多半是指我们这里所的`矩`:

![50%](https://images.jieyu.ai/images/2023/06/stats_moments.png)
<figcaption>stats中的moments参数</figcaption>

### 1.5. 偏度、峰度在投资中的解释与应用

在投资中，许多预测资产未来表现的金融模型都基于正态分布。但实际上，收益分布接近正态分布的情况很少，更多的时候，它服从偏度分布。我们需要注意，当实际运行结果偏离了正态分布时，要谨惕偏度风险。偏度风险是指在偏态分布中发现高偏度数据点的风险增加。如果数据偏斜，这种模型在其预测中总是会低估偏度风险。数据偏差越大，此财务模型的准确性就越低。

在过去二十年中，从1990年代后期的互联网泡沫开始，人们更频繁地观察到资产收益偏离正态分布的情况。事实上，资产回报往往越来越右偏。这种波动发生在值得注意的事件中，例如9月11日的恐怖袭击，房地产泡沫破裂和随后的金融危机，以及量化宽松（QE）期间。

而股票市场通常被认为具有负偏态分布。这个概念是，市场经常地返回若干小的正回报，而更经常地返回一个大的负损失。如果我们找到了某种正偏态分布，那么往往意味着可能实现巨大的收益。

峰度解释了某些数据集中的观测值落在概率分布的尾部与中心的频率。在金融和投资中，过大的峰度被解释为一种称为“尾部风险”的风险，即由于罕见事件而发生损失的可能性。如果此类事件比分布预测的更常见，则称其为“肥尾”。

[^statistics]: statistics是Python内置标准库，用于统计分析。
[^ATR]: 真实波动率的移动平均值。
[^scipy_stats_loc]: https://stats.stackexchange.com/a/560290
[^nagwa]: https://www.nagwa.com/en/explainers/812192146073/
