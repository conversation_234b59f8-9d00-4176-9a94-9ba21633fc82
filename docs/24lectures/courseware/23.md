---
title: 实盘交易接口 (1)
output: pdf_document
mainfont: WenQuanYi Micro Hei
---

程序化交易是量化交易的主要特征，它可以确保我们精确地执行量化策略，而不受人的思维速度、情绪和其它人为因素的影响。通过前面的学习，我们已经掌握了如何编写量化策略，如何通过回测评估我们的策略。如果一切顺利，将策略付之实盘是理所当然的事。

从 2005 年之后，券商对程序化交易进行了一些限制，只允许一些合格投资者参与。参与的途径大概有这么几种，一是券商机房托管，比如 ptrade 和一创聚宽都是；二是通过掘金量化、东财 EMC 或者 QMT，也可以在本地运行策略并实现程序化交易。

!!! info
    一创聚宽已宣布自2023年9月起停止接受新的实盘客户，存量客户也将于2024年1月起停止实盘交易。

这些方案都需要向券商申请开通权限，并且有一定的资金门槛。在开通这些账户之前，我们也可以先通过 easytrader 来完成一段时间的模拟交易。
## 1. easytrader

[easytrader](https://github.com/shidenggui/easytrader) 是通过模拟键鼠事件，通过操作券商客户端来实现交易功能的交易代理。我们可以通过它来实现模拟盘交易和智能跟单。虽然从功能上看，我们也可以通过它来实现实盘交易，但这样的操作是有合规风险的。

easytrader 目前支持的券商客户端有海通、华泰、国金和同花顺。由于同花顺支持很多券商，因此，其它券商账户，实际上也是可以通过 easytrader 来下单。除实盘支持外，它还可以支持同花顺模拟盘及雪球组合。

在智能跟单上，它支持跟踪 jointquant, ricequant 的模拟交易，以及雪球组合。

!!! attention
    在 2015 年之后，A 股程序化交易只对特定的、符合资质要求的投资者开放，因此，使用 easytrader 进行实盘交易是不合规的。如果您有程序化交易实盘要求，请使用本书推荐的其它合规方案。这些方案具有 easytrader 不具备的响应速度和可靠性优势。

    量化策略在回测通过之后，一定要通过一段时间的仿真交易来证实回测的有效性。仿真交易可以通过 easytrader，在同花顺模拟盘或者雪球组合上完成。模拟运行的结果，可以成为策略有效性的第三方证明，我们向大家介绍 easytrader，是出于这样的使用场景。

### 1.1. 安装
easytrader 是一个 python 库，它的主要功能通过对应的客户端来实现。因此，安装 easytrader 实际上包含了两步。

首先，需要安装券商的交易客户端。这些客户端一般是 windows 的版本，因此需要安装在 windows 机器上。easytrader 有一种服务器模式，在该模式下，交易服务端和量化策略端分别运行在不同的服务器上（可以是相同的操作系统，也可以是不同的操作系统）。在这种模式下，需要在券商交易客户端所在的 windows 机器上安装运行 easytrader 库，也需要在量化策略运行端安装 easytrader 库。

安装 easytrader 的命令是：

```python
pip install easytrader
```

在 windows 下安装时，常常会提示某些模块找不到的问题。此时可以按提示，补安装这些模块后，再安装 easytrader。

!!! tip
    有一些客户端在登录时，需要输入验证码。easytrader 可以调用 [tesseract](https://tesseract-ocr.github.io/) 来识别这些验证码，但 tesseract 需要自行安装。tesseract 是一个图形文字识别（OCR）方案。

### 1.2. 生命期

我们以雪球组合的管理为例，来说明 easytrader 的使用。如果您要使用其它功能，可以参考它的 [官方文档](https://easytrader.readthedocs.io/zh/master/usage/)。

easytrader 在使用上，一般有以下几个步骤：

1. 导入 easytrader，这是通过`import easytrader`来完成的。
2. 创建和连接客户端
3. 调用交易相关 API，实现交易
4. 退出客户端，调用 `user.exit()`来完成。

#### 1.2.1. 连接客户端

由于 easytrader 支持的客户有好几种，因此，首先我们需要设置客户端类型。比如，如果我们要通过同花顺客户端使用它的模拟盘功能，我们就需要这样设置：

```python
user = easytrader.use('universal_client')
```

这个版本可称为通用同花顺客户端，从同花顺官网上下载，在券商没有提供专门版本的情况下使用。如果券商已经提供了专门版本，则我们要将上面示例中的参数改为'ths'。

如果是用来管理雪球组合，则需要这样设置：

```python
user = easytrader.use('xq')
```

这样我们就生成了一个 XueQiuTrader 对象。

然后我们需要登录客户端。我们使用以下方法进行登录：

```python
user.prepare(user='用户名', password='明文密码', comm_password='华泰通讯密码，其他券商不用')
```

!!! attention
    如果使用的是 universal_client，我们需要手动登录一次，并且添加券商，填入账户号、密码、验证码，勾选“保存密码”。

在我们的例子中，我们将管理雪球模拟组合。它的登陆参数有所不同。我们需要先通过网页登陆雪球，获取组合信息及 cookie。

登录到雪球后，点击首页，就会出现截图所示界面，按提示找到组合信息。如果还没有创建过组合，您需要通过“创建组合”事先创建至少一个组合。雪球通过组合的方式，相当于帮我们实现了多个资金账户，因此，我们在操作之前，需要通过创建组合，来创建一个资金账户，这样后续才能操作。

![](https://images.jieyu.ai/images/2023/09/lesson24-xq-portfolio.png)

图中序号红框中即为组合 ID。

!!! tip
    雪球在创建组合时，必须同时指定至少一支个股。我们可以选一个价格较低的个股，买入 1%，次日抛出。此后就可以完全从 easytrader 端管理了。

获取 cookie 的方法如下。在登录到雪球后，打开浏览器的开发者窗口，在网络 > HTML > cookie 下，找到`xq_a_token`，拷贝。

![](https://images.jieyu.ai/images/2023/09/lesson24-xq-cookie.png)

现在，我们就可以通过下面的代码，连接雪球：

```python
cookies = "" # xq_a_token=0206ca...
pc = 'ZH3285850'
user.prepare(cookies=cookies, portfolio_code=pc, portfolio_market='cn')
```

#### 1.2.2. 获取账户信息

我们可以通过`balance`，`entrust`, `position`等属性，及其对应方法`get_balance`, `get_entrust`和`get_position`来分别获取当前账户余额信息、最近 20 笔委托信息及当前持仓信息。

```python
user.balance
```

我们将得到以下输出：

```
[{
    'asset_balance': 1000000.0,
    'current_balance': 990000.0,
    'enable_balance': 990000.0,
    'market_value': 10000.0,
    'money_type': '人民币',
    'pre_interest': 0.25
}]
```

从输出可以看出，默认的资金账户是 1 百万。

```python
user.position
```

我们将得到以下输出：

```python
[{'cost_price': 100.0,
  'current_amount': 100,
  'enable_amount': 100,
  'income_balance': 0,
  'keep_cost_price': 100.0,
  'last_price': 100.0,
  'market_value': 10000.0,
  'position_str': 'random',
  'stock_code': 'SH601398',
  'stock_name': '工商银行'}]
```

雪球是按比例进行调仓的，所以上面的输出跟其它交易客户端呈现的信息不一样。

#### 1.2.3. 交易

我们在雪球上进行交易，可以通过 buy, sell, 也可以通过 adjust_weight 方法来实现：

```python
# 市价买入 100 股工商银行，我们也可以加上 PRICE 参数
user.buy('SH601398', volume=100)

# 将持有的工商银行全部平仓
user.adjust_weight('SH601398', 0)

# 买入工商银行，使之占比达 100%
user.adjust_weight('SH601398', 100)
```
!!! tip
    adjust_weight 方法类似于 backtrader 中的 order_target_percent 函数。

我们可以在非交易时间执行上述操作。雪球会记录这些指令，但只会在次日开盘后才真正执行。

![50%](https://images.jieyu.ai/images/2023/09/lesson24-xq-log.png)
![50%](https://images.jieyu.ai/images/2023/09/lesson24-xq-tiaocang.png)

当交易函数 buy, sell 和 adjust_weight 被调用时，xqtrader.py 中的_search_stock_info 被调用，以决定该股当前能否交易及其它信息。该方法将返回一个字典，包括以下信息：

```python
def _search_stock_info(self, code):
    """
    通过雪球的接口获取股票详细信息
    :param code: 股票代码 000001
    :return: 查询到的股票 {
            u'stock_id': 1000279, 
            u'code': u'SH600325',
            u'name': u'华发股份', 
            u'ind_color': u'#d9633b', 
            u'chg': -1.09,
            u'ind_id': 100014, 
            u'percent': -9.31, 
            u'current': 10.62,
            u'hasexist': None, 
            u'flag': 1, 
            u'ind_name': u'房地产', 
            u'type': e,
            u'enName': None
        }
    flag : 未上市 (0)、正常 (1)、停牌 (2)、涨跌停 (3)、退市 (4)
    """
```
但不是所有的查询都能返回全部上述字段。在我们的测试中，有时候不能返回 enName 和 type 两个字段，这会引起交易失败。

下面的代码修正了这个问题，但目前尚未合并到 easytrader，需要您手动修改：

```python
def _search_stock_info(self, code):
    """
    通过雪球的接口获取股票详细信息
    :param code: 股票代码 000001
    :return: 查询到的股票 {
            u'stock_id': 1000279, 
            u'code': u'SH600325',
            u'name': u'华发股份', 
            u'ind_color': u'#d9633b', 
            u'chg': -1.09,
            u'ind_id': 100014, 
            u'percent': -9.31, 
            u'current': 10.62,
            u'hasexist': None, 
            u'flag': 1, 
            u'ind_name': u'房地产', 
            u'type': e,
            u'enName': None
        }
    flag : 未上市 (0)、正常 (1)、停牌 (2)、涨跌停 (3)、退市 (4)
    """
    data = {
        "code": str(code),
        "size": "300",
        "key": "47bce5c74f",
        "market": self.account_config["portfolio_market"],
    }
    r = self.s.get(self.config["search_stock_url"], 
                   params=data)
    stocks = json.loads(r.text)
    stocks = stocks["stocks"]
    stock = None
    if len(stocks) > 0:
        stock = stocks[0]

    if 'enName' not in stock:
        stock['enName'] = ''
    if 'type' not in stock:
        stock['type'] = ''

    logger.info("stock info of %s is %s", code, stock)
    return stock
```
这里的第 36~39 行是增加部分。

### 1.3. 服务器模式

我们可以把 easytrader 及对应的客户端安装在一台远程 windows 机器上，然后在另一台机器上运行量化策略。我们可以通过下面的方法，在远程 windows 机器上，启动交易服务端：

```python
from easytrader import server
server.run(port=1430)
```

在量化策略端，我们通过以下的代码来实现连接和交易：

```python
from easytrader import remoteclient

# 使用客户端类型，可选 YH_CLIENT, HT_CLIENT, THS, XQ 等'
client_type = 'ths'
host = '服务器 ip'
port = '服务器端口，默认为 1430'

ser = remoteclient.use(client_type, host=host, port=port)
ser.prepare(...)

#USER.BUY(......)
#USER.SELL(......)
```

!!! attention
    当以上述方式部署时，注意要在 windows 机器上打开端口的防火墙设置。建议在部署时，首先在远程 windows 本机上运行客户端代码（host 使用 127.0.0.1）, 待调试通过，再分开部署。

如果我们仅仅是使用 easytrader 来管理雪球组合的话，这种远程服务器模式是不必要的。

### 1.4. 自动跟单

easytrader 支持自动跟踪 joinquant/ricequant 的模拟交易。这样就可以实现自动跟踪他人优秀的策略。我们以向雪球同步为例，介绍这个功能如何实现。

``` python
import os
import easytrader

# 初始化雪球
xq_user = easytrader.use('xq')
cookies = os.environ.get('xq_cookie')
pc = 'ZH3285850'
xq_user.prepare(cookies=cookies, portfolio_code=pc)

# 初始化跟踪 JOINQUANT
follower = easytrader.follower('jq')

jq_account = os.environ.get("jq_account")
jq_password = os.environ.get("jq_password")

follower.login(user=jq_account, password=jq_password)

backtest = '7dde84fea86cd80dc36e5e7e90ee0691'
url = 'https://www.joinquant.com/algorithm/live/' + \
      f'index?backtestId={backtest}'
    
follower.follow(xq_user, url)
```

这会立即输出以下信息：

```
2023-09-05 19:52:46,108 [INFO] follower.py 58: 登录成功
2023-09-05 19:52:46,350 [INFO] joinquant_follower.py 78: 开始跟踪策略：easytrader
```
easytrader 是账号 `$jq_account`下回测 id 为`...0691`的策略名。如果上述策略处在实盘执行中，并且有交易动作，那么上述代码还将同步这些交易，并输出以下信息：

```
2023-09-05 19:32:28,759 [INFO] follower.py 202: 策略 [easytrader] 发送指令到交易队列，股票：sz000001 动作：buy 数量：100 价格：11.42 信号产生时间：2023-09-05 13:44:00

2023-09-05 19:32:28,761 [INFO] follower.py 262: execute trade cmd for <easytrader.xqtrader.XueQiuTrader object at 0x7f3ee41c8730>:buy

...

2023-09-05 19:32:29,304 [INFO] xqtrader.py 490: weight:0.110000, cash:99.880000
2023-09-05 19:32:29,436 [INFO] xqtrader.py 507: 调仓 buy 平安银行：200

2023-09-05 19:32:29,438 [INFO] follower.py 335: 策略 [easytrader] 指令（股票：sz000001 动作：buy 数量：100 价格（考虑滑点）: 11.42 指令产生时间：2023-09-05 13:44:00) 执行成功，返回：[{'entrust_no': *********, 'init_date': '2023-09-05 19:32:29', 'batch_no': '委托批号', 'report_no': '申报号', 'seat_no': '席位编号', 'entrust_time': '2023-09-05 19:32:29', 'entrust_price': 11.42, 'entrust_amount': 100, 'stock_code': 'sz000001', 'entrust_bs': '买入', 'entrust_type': '雪球虚拟委托', 'entrust_status': '-'}]
```

为保证上述代码正常运行，您不仅需要先在雪球上创建组合（参照本章 1.3 节），还需要在聚宽上创建一个模拟策略。创建模拟策略的页面地址是 [模拟策略列表](https://www.joinquant.com/algorithm/trade/list?process=1)。页面如下图所示：

![](https://images.jieyu.ai/images/2023/09/lesson24-jq-simulator.png)

!!! attention
    在聚宽上创建模拟交易，需要通过积分兑换，或者购买，才能使模拟交易在盘中实时运行，否则，您的模拟交易将会放在次日凌晨 2：00 左右开始执行。
## 2. 东方财富 EMC 智能交易终端

东方财富是颇具实力的互联网券商，EMC 智能交易终端是他们推出的可实现程序化交易的客户端。该客户端通过文件扫单功能与量化策略进行通讯。量化策略生成订单后，以 CSV 的文件格式推送给该客户端监视的目录下，该客户端即可解析该 CSV 文件，向券商柜台发出交易指令。指令结果也通过 CSV 文件输出，量化策略需要监视该目录，及时读取输出的 CSV 文件以获得结果。

这种方式尽管不如 API 那样简洁，但在满足合规要求的前提下，使得量化策略能在本地运行，最大程度保护了策略的安全性，也使得我们编写策略可以完全不受限制。同时，文件扫单性能也非常高，响应速度可以小于 10ms，除了高频交易之外，其它类型的交易都可以很好地满足。

### 2.1. 安装
EMC 智能交易终端可以通过这里的 [链接](https://emt.eastmoneysec.com/down) 下载安装，目前只有 windows 版本。

![](https://images.jieyu.ai/images/2023/03/20230403154605.png)

!!! attention
    开通实盘需要申请权限，目前看（2023 年）主要门槛是要求 100 万初始资金。申请可加入东方财富量化仿真交流群：971584613，按群文件指示进行开通。

申请后，记录普通资金账号和密码，如下图：

![75%](https://images.jieyu.ai/images/2023/04/仿真.jpg)

在登录界面中，选择仿真交易：

![75%](https://images.jieyu.ai/images/2023/04/login.jpg)

!!! tip
    实盘交易待账号和权限申请下来后，也通过此界面登录。此时的账号为您的实盘资金账号。

登录后，界面显示如下：

![75%](https://images.jieyu.ai/images/2023/04/20230403200024.png)

前面已经介绍过，EMC 是通过 CSV 文件来读取交易指令的，交易执行执行的结果，也通过 CSV 文件输出。现在，我们就来介绍如何存放输入、输出 CSV 文件的目录。
#### 2.1.1. 配置文件单目录

打开 EMC 终端，在 量化 > 文件单 > 文件单输出 中，对下图中的 4，5，6，7 进行配置。其中 4 为指令结果输出文件目录；5 选择`csv`作为输出格式；6 选择自动启动；7 将所有项目全选中。

![75%](https://images.jieyu.ai/images/2023/04/output.jpg?1)

在 量化 > 文件单 > 文件单输入 中，对下图中的第 3 项和第 4 项进行配置。第 3 项为交易指令输入文件的目录，第 4 项自动启动需要选中，这样一旦 EMC 启动，就会自动监视这些文件夹。

![75%](https://images.jieyu.ai/images/2023/04/input.jpg?1)

### 2.2. 运行和维护

另外启动一个计划任务，在每天早上 8:45 左右启动 EMC。
#### 2.2.1. 启动
使用下面的脚本来启动：
```
@echo off
call C:\ProgramData\Anaconda3\Scripts\activate.bat C:\ProgramData\anaconda3
call conda activate gmclient
python -m gmadaptor.server
pause
```

#### 2.2.2. 每日维护
EMC 量化终端有时候不稳定。我们可以通过定时重启来提高起稳定性。通过以下代码，在盘后退出 EMC：

``` shell
REM kill process
TASKKILL /F /IM EMCTrade.exe

REM sleep 5 seconds
TIMEOUT  /T 5

REM remove all file orders after process killed

DEL /Q C:\zillionare\FileOrders\real_input\*.csv
```

!!! Warning
    如果在输入输出目录中还有未归档的文件，则量化交易将无法自动启动。上述代码中最后一行的作用就是清理未归档文件。
    这也要求使用者自行对委托进行核验，确保这些文件可以被自动删除。

### 2.3. 故障排除与帮助

关于东财文件单，请参考：https://emquant.18.cn/file-help/?doc=file_order
东财量化 Q 群：971584613

即使实现了 EMC 的每日自动重启，也有可能偶发连接异常或者其它错误。此时可能需要手动执行：
1. 重新连接
2. 清除文件单，重新启动

### 2.4. 撮合配置规则
在仿真交易测试中，EMTrader 对每个品种，都指定了对应的响应。比如，对 000572 这个品种，买入一定会全部成交，对 000010 这个品种，则一定会拒绝。这是为了方便测试的需要。东财提供了名为《撮合配置规则》的文件，该文件可能随时更新，所以，需要在测试前，加他们技术人员 QQ 领取。

该文件 2023 年 3 月份部分内容如下：
```
[全部成交]
000572	full
000725	full

[分笔成交] 
分成两笔：
000002	lot      	2

[部分成交] 成交一半
000001	part
000004	part
...
[挂单]	只有响应，没有成交
018014	pending
020417	pending
...

[拒单]	
000010	reject
010609	reject

[拒绝撤单] 部分成交，不可撤单
000008	cancel_reject
000151	cancel_reject
```

## 3. Trader-gm-adaptor

通过上述配置，EMC 智能交易终端已经可以监听文件单了。现在，我们要做的事情，就是将量化策略中的交易指令，翻译成 EMC 智能交易终端可以理解的格式，并存为 CSV 文件，放到文件单输入目录下，并从输出目录读取交易指令的结果。

这涉及到量化策略的交易指令翻译、CSV 文件解析等等工作。这项工作，我们已经实施为 [trader-gm-adaptor](https://github.com/zillionare/trader-gm-adaptor)。下面，我们就结合该项目来介绍如何将 EMC 智能交易终端封装成一个网络服务，从而使得我们的量化策略可以远程调用该服务以完成下单指令。

!!! tip
    trader-gm-adaptor 不依赖于大富翁框架的其它模块，仅依赖于 sanic, httpx 等少数第三方库，可独立使用。
    
我们需要实现一个 web 服务器，与 EMC 部署在同一台机器上，并且，将文件单输入、输出目录作为该服务的一个配置项，从而使得该服务可以读写这些目录。

既然我们将这项服务通过网络发布，我们也必须提供鉴权功能。这可以要求量化策略在使用这项服务时，传递一个预先生成的 API token 过来。此外，EMC 智能终端上可能存在着多个账号（至少有一个模拟账号、一个实盘账号），所以，我们还需要将这个 token 与账号关联起来。

在 gmadaptor 中，上述信息通过一个 yaml 文件来提供：

```yaml
# DEFAULTS.YAML
log_level: INFO

server_info:
    port: 9000
    # CLIENT 使用这一 TOKEN 来访问 GMADAPTOR 提供的服务
    access_token : "84ae0899-7a8d-44ff-9983-4fa7cbbc424b"

gm_info:
    fake: false
    # 文件单输出目录
    gm_output: "~/gmadaptor/FileOrders/out"
    trade_fees:
        commission: 2.5
        stamp_duty: 10.0
        transfer_fee: 0.1
        minimum_cost: 5.0
    accounts:
        # 账号名
        - name: fileorder_s01
          acct_id: 1a66e81c-ae5d-11ec-aef5-00163e0a4100
          # 文件单输入目录。东财量化终端将从这里读取文件单
          acct_input: "~/gmadaptor/FileOrders/inputs/fileorder_s01"
```

!!! attention
    需要将量化软件中的实盘账号配置到 gmadaptor 的配置文件中。在用户目录下，创建`gmadaptor/config`目录，将上述配置文件（defaults.yaml) 放入其中。

上述配置中，access_token 可任意指定，任何要访问此服务的客户端，必须持有此 token。

gm_output/acct_input 文件设置后，如果未创建，gmadaptor 将在启动时自动创建，请确保 gmadaptor 有权限读写这些文件夹。

accounts > name 中的值来自于在 EMC 终端中，您创建文件单输入时，指定的名称，见下图中的序号 2：

![](https://images.jieyu.ai/images/2023/04/**************.png)

accounts > acct_id 来自于下面序号 3 的位置，点击`ID`即可复制：

![](https://images.jieyu.ai/images/2023/04/**************.png)

### 3.1. 冒烟测试

在前面生成的 gmclient 虚拟环境中，执行以下命令，以启动 gmadaptor 服务器：
```
python -m gmadaptor.server
```
如果出现如下界面，表明服务器启动成功：

![](https://images.jieyu.ai/images/2023/04/started.jpg)

此时我们另开一个`conda`窗口，同样使用`gmclient`的虚拟环境，通过以下命令进行测试：
```
python -m gmtest %account %token %server %port
```

这里的 account 即 gmadaptor 配置文件中的 gm_info > accounts > account_id, token 即 server_info > access_token

这里的 server 即 gmadaptor 所在的机器 IP， port 为端口。如果不提供，默认地，这两项分别为 localhost 和 9000。

如果配置正常，这将打印出初始账号资金，当前持仓，和一笔买、卖的信息。

### 3.2. 客户端与服务器交互
#### 3.2.1. 客户端请求

客户端通过 http request 来请求 gmadaptor。以下示例均以同步请求实现，但您也可以根据需要，改为异步请求。服务器对客户端的鉴权是通过`headers`来实现的，具体请看示例（任意一示例均可）。客户端向服务器发送数据，都使用的是 post 方法，gmadaptor 所有的方法都只响应 post 请求。如果请求成功完成，则返回代码是 200。

在所有的操作中，股票代码都必须以简码+交易所后缀方式出现，其中上交所为.XSHG，深交所为.XSHE。在仿真测试时，对股票进行操作时，都有特定的响应（比如对某支股票，进行买入时，无论给的参数是多少，返回都会是部分成交；对另一支股票，则永远返回限制买入等等），具体响应文档请在东财量化 Q 群中，找管理员要文档。

在一些会改变状态的请求中（比如买入操作），往往会需要`cid`参数和`timeout`参数，其作用是，调用会在指定的 timeout 期间等待 EMC 处理请求，并返回结果；但 EMC 也可能无法在指定的 timeout 期间返回结果，比如委买单报价过低，一直不能成交，此时将无法产生回报结果。在这种情况下，调用会在 timeout 之后返回。之后的委买结果查询，就需要依赖`cid`参数。注意`cid`是必选参数，`timeout`是可选参数。

`cid`参数（即 client entrust id）由客户端自行产生，建议使用以下代码：
```python
import uuid

cid = str(uuid.uuid4())
```
`cid`产生之后，请在客户端保存，直到事务结束，不再需要为止。

为简练起见，后面的示例中，可能删除了这些代码：
```python
import httpx

headers = {
    # TOKEN UNDER SERVER_INFO > ACCESS_TOKEN
    "Authorization": token
    # ACCT_ID UNDER GM_INFO > ACCOUNTS > ACCT_ID
    "Account-ID": acct_id
}

_url_prefix = "http://192.168.100.100:9000/"

buy_entrust_no = None
sell_entrust_no = None
```

#### 3.2.2. 返回结果
返回错误可能发生在三个层面。一是 http 层（包括 bad request 或者 Internal server error); 二是 gmadaptor 层，三是 emc 可能返回错误。

第一层的错误，我们通过 http status code 来检查。比如，如果我们使用的客户端是 httpx，则可以检查 `response.status_code`是否为 200。

gmadaptor 始终通过`json`来返回响应，响应包括三个字段，即：
```
status: int, 如果为零，则表明在此层没有发生错误，即 gmadaptor 已经将请求正确上报
msg: str, human readable message
data: dict 如果一切顺利，则返回数据在此项中
```
第三层的错误由 emc trader 给出。即使 gmadaptor 正确上报的请求，也可能 emc trader 无法执行，此时它也会通过`status`和`reason`来给出错误信息。

下面的示例给出了一个`response.json()`的输出：
```json
{
    "status": 0,
    "msg": "OK",
    "data": {
        "code": "000001.XSHE",
        "price": 0.0,
        "volume": 100,
        "order_side": 1,
        "bid_type": 2,
        "time": "2023-04-04 15:27:38.921555",
        "entrust_no": "0d23bb4e-d81e-4ef2-ab21-c58e0fe6814f",
        "status": -1,
        "average_price": 0.0,
        "filled": 0,
        "filled_amount": 0,
        "eid": "",
        "trade_fees": 0,
        "reason": "[Counter] [EMC_PC] 不支持该下单类型",
        "recv_at": "2023-04-04 15:27:38.924565"
    }
}
```
因此，即使在 gmadator 层面给出的状态是成功，也并不意味着该笔委托成功。另一个例子是，以过低的价格委买，只要参数合法且被 EMC 接收，gmadaptor 都会返回成功，但该委托是否真正成交，还得通过 entrust_no 来查询。

### 3.3. API 示例
#### 3.3.1. 资产表
```python
# 请求资金信息
import httpx
headers = {
    "Authorization": "84ae0899-7a8d-44ff-9983-4fa7cbbc424b",
    "Account-ID": "780dc4fda3d0af8a2d3ab0279bfa48c9"
}

_url_prefix = "http://192.168.100.100:9000/"

def get_balance():
    r = httpx.post(_url_prefix + "balance", headers=headers)
    resp = r.json()
    if r.status_code == 200 and resp['status'] == 0:
        print("\n------ 账户资金信息 ------")
        print(resp["data"])
```

#### 3.3.2. 持仓表
```python
def get_positions():
    r = httpx.post(_url_prefix + "positions", headers=headers)
    
    resp = r.json()
    if r.status_code == 200 and resp['status'] == 0:
        print("\n----- 持仓信息 ------")
        print(resp["data"])
```

#### 3.3.3. 限价买入
```python
    r = httpx.post(_url_prefix + "buy", headers=headers, json={
        "security": "000001.XSHE",
        "price": 13,
        "volume": 100,
        "cid": str(uuid.uuid4()),
        "timeout": 1
    })

    print(r.json())
```

#### 3.3.4. 市价买入
```python
def market_buy():
    global buy_entrust_no
    r = httpx.post(_url_prefix + "market_buy", headers=headers, json={
        "security": "000001.XSHE",
        "volume": 100,
        "cid": cid,
        "timeout": 1
    })

    resp = r.json()
    if r.status_code == 200 and resp["status"] == 0:
        print("\n ------ 委买成功 ------")
        print(resp["status"], resp["msg"], resp["data"])
        buy_entrust_no = resp["data"]["entrust_no"]
    else:
        print("委买失败：", r.status_code, resp)
```

#### 3.3.5. 限价卖出
```python
def sell():
    global sell_entrust_no

    r = httpx.post(_url_prefix + "sell", headers=headers, json={
        "security": "000001.XSHE",
        "price": 10,
        "volume": 100,
        "cid": cid,
        "timeout": 1
    })

    resp = r.json()
    if r.status_code == 200 and resp["status"] == 0:
        print("\n ------ 限价委卖成功 ------")
        data = resp["data"]
        print(data)
        sell_entrust_no = data["entrust_no"]
    else:
        print("卖出失败：", r.status_code, resp)
```

#### 3.3.6. 市价卖出
```python
def market_sell():
    r = httpx.post(_url_prefix + "market_sell", headers=headers, json = {
        "security": "000001.XSHE",
        "volume": 100,
        "cid": cid
    })

    resp = r.json()
    if r.status_code == 200 and resp["status"] == 0:
        print("\n ------ 市价委卖成功 ------")
        print(resp["data"])
    else:
        print(resp)
```
#### 3.3.7. 取消委托
```python
def cancel_entrust():
    global buy_entrust_no

    r = httpx.post(_url_prefix + "cancel_entrust", headers=headers, json = {
        "entrust_no": buy_entrust_no,
        "timeout": 1
    })

    resp = r.json()
    print(resp["status"], resp["msg"], resp["data"])

```
#### 3.3.8. 查询当日委托
```python
def today_entrusts():
    r = httpx.post(_url_prefix + "today_entrusts", headers=headers, json = {
        # 此处可以传入记录的委托号。传入空数组时，表明取当天所有委托。
        "entrust_no": [],
        "timeout": 1
    })

    resp = r.json()
    print(resp["status"], resp["msg"], resp["data"])
```
