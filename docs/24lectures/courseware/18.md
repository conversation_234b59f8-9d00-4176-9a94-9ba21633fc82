---
title: Seaborn 与 PyEcharts 绘图
output: pdf_document
mainfont: WenQuanYi Micro Hei
---


![](https://images.jieyu.ai/images/2023/07/lesson18-outline.png)
## 1. Seaborn

Seaborn 是一个制作统计图形的 Python 库。它建立在 matplotlib 之上，并与 Pandas 数据结构紧密集成。

与 matplotlib 不同，Seaborn 站在更高的层面帮助我们探索和理解数据。我们可以告诉 Seaborn，我们想要探索和展示数据之间的什么样的关系，Seaborn 就以恰当的形式将其绘制出来，而不需要我们去关心绘图细节。当然，在任何时候，我们仍然可以深入到最底层的细节，进行强大的定制。

下面的例子演示了 Seaborn 是如何向我们屏蔽绘图的细节，而直接进入到数据关系的探索的：

```python
# IMPORT SEABORN
import seaborn as sns

# APPLY THE DEFAULT THEME
sns.set_theme()

# LOAD AN EXAMPLE DATASET
tips = sns.load_dataset("tips")

# CREATE A VISUALIZATION
sns.relplot(
    data=tips,
    x="total_bill", y="tip", col="time",
    hue="smoker", style="smoker", size="size",
)
```

这会展示一个 facet 图，通过一次简单地调用，显示了 Tips 数据集中 5 个变量之间的关系：账单额与就餐人数（size）、小费金额 (tip)、是否吸烟、时间（中午或者晚餐）。

与直接使用 matplotlib 不同，我们只需要给 Seaborn 提供变量的名称及其在图中的角色，不需要根据颜色值或标记代码指定绘图元素的属性。在幕后，seaborn 处理从数据帧中的值到 matplotlib 可以理解的参数的转换。这种声明性方法可以让我们专注于想要回答的问题，而不是关注如何控制 matplotlib 的细节。

这个图如下所示：

![75%](https://images.jieyu.ai/images/2023/07/lesson17-seabron-demo-1.png)

现在，我们就来解读一下代码：

第 2 行，我们导入 Seaborn 库。Seaborn 库是这个简单示例中我们需要导入的唯一库。按照惯例，我们导入后，重命名为 sns。

第 5 行， 我们设置 Seaborn 使用默认主题。这使用了 matplotlib rcParam 系统，并且会影响所有 matplotlib 绘图的外观，即使是直接使用 matplotlib 来绘制。除了默认主题之外，还有其他几个选项，我们将在样式部分专门介绍。

接下来，第 8 行，我们通过 load_dataset 加载了一个演示用的数据集。这些数据集都是 csv 数据集，我们也可以通过 pandas.read_csv() 来读入。

第 11 到第 15 行，我们调用 relplot，一次显示了 Tips 数据集中五个变量之间的关系。如果你对上一章的内容还有印象，就能理解这里 relplot 进行的操作，就是绘制了一个基于散点图的 facet 图：

```python
import plotly.express as px
import seaborn as sns

tips = sns.load_dataset("tips")

fig = px.scatter(
    tips,  # 数据集
    x="total_bill",
    y="tip",
    color="smoker",  # 按国家所在大陆进行颜色区分
    size="size",  # 按人口数决定大小
    facet_col="time",  # 列切面字段
    title="relationship plotting using plotly",
)
fig.show()
```
代码几乎差不多，只不过 Seaborn 更强调它是在语义层面进行操作，脱离了绘图细节。比如， 我们并没有告诉 Seaborn 要绘制散点图和切面图，但 Seaborn 在这里进行了正确的猜测，最终绘制出我们想要的图形。

不过，有时候，Seaborn 也需要一点点帮助。比如，在 relplot 关系图绘制时，有时候可能绘制成折线图更好。在这种情况下，我们可以给 relplot 传一个参数，以切换到折线图，比如：

```python
dots = sns.load_dataset("dots")
sns.relplot(
    data=dots, kind="line",
    x="time", y="firing_rate", col="align",
    hue="choice", size="coherence", style="choice",
    facet_kws=dict(sharex=False),
)
```

![75%](https://images.jieyu.ai/images/2023/07/lesson18-sns-demo-2.png)

注意到转换成折线图之后，size 和 style 参数依然在使用，但它们对可视化结果的影响不一样：在散点图中，size 代表点的大小，style 则用来选择不同的符号（marker），在折线图中，则分别代表线的宽度与样式 -- 从虚线到实线。这也正好反映了 Seaborn 基于语义绘制图形的含义，这些本来应该由人完成的语义映射，Seaborn 都能为我们考虑到，从而让我们可以专注于绘制的整体结构和希望它传达的信息。

### 1.1. Seaborn 绘图概览

Seaborn 命名空间是平的，所有功能都可以在顶层访问。在模块组织上，它仍然是分层的，并且把相似的功能归在相同的类别中。最主要的归类为"relational", "distributional"和"categorical"，此外，还有其它一些归类和无法归类的情况。

下面的脑图显示了 sns 的绘图功能归类：

![](https://images.jieyu.ai/images/2023/07/lesson18-sns-arch.png)

!!! Tip
    在上面的分类中，在 relational, distributitions 和 categoical 类别下，涉及到 figure-level 和 Axes-level 两个级别的绘图。比如， relplot 是 figure-level 的，而 lineplot 则是 Axes-level 的。

    在 Axes-level 的绘图中，数据将被绘制到 matplotlib.pyplot.Axes 对象上，并且返回该 Axes。

    而在 figure-level 绘图中，seaborn 将生成一个 seaborn 对象（通常是 FacetGrid)，其中可能包含多个子图。figure-level 函数始终将一个或多个 axes-level 函数与管理布局的对象结合在一起。例如，relplot() 是一个 figure-level 函数，它结合了 scatterplot() 或 lineplot() 和一个 FacetGrid。

    在简单的使用中，我们可以不关心这两个概念的区分，但是，当我们想要在超出每个函数的 API 提供的级别（figure-level 与 axes-level) 上进行自定义时，了解这些概念的区分就很重要。

下面，我们分别就如何通过 seaborn 来探索数据的统计特征、可视化它的分布、类别特征进行举例说明。

#### 1.1.1. 统计关系的可视化
统计分析是了解数据集中的变量如何相互关联以及这些关系如何依赖于其他变量的过程。可视化可以成为此过程的核心组成部分，因为当数据被恰当地可视化呈现时，人类视觉系统可以看到数据中隐含的趋势和模式。

这一节中将讨论三个 seaborn 函数。我们最常用的是 relplot() 。这是一个 figure-level 级的函数，可用以绘制散点图和线图。 relplot() 将 FacetGrid 与 axes-level 级的函数 -- scatterplot 或者 lineplot -- 进行组合。

这些函数非常具有启发性，因为它们使用简单且易于理解的数据表示，但可以表示复杂的数据集结构。他们之所以能做到这一点，是因为他们绘制的二维图形可以通过使用色调、大小和样式语义映射最多三个附加变量来增强。

散点图是统计可视化的支柱。它使用点云描述两个变量的联合分布，其中每个点代表数据集中的一个观察值。这种描述使眼睛能够推断出关于它们之间是否存在任何有意义的关系的大量信息。

在 seaborn 中绘制散点图有多种方法。当两个变量都是数字时应该使用最基本的函数 scatterplot() 。在分类可视化教程中，我们将看到使用散点图可视化分类数据的专用工具。 scatterplot() 是 relplot() 中默认的 kind：

```python
tips = sns.load_dataset("tips")
sns.relplot(data=tips, x="total_bill", y="tip")
```

![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-1.png)

虽然这些点是以二维绘制的，但可以通过根据第三个变量对点进行着色来将另一个维度添加到图中。在 seaborn 中，这被称为使用“色调语义”，因为点的颜色获得了意义：

```python
sns.relplot(data=tips, x="total_bill", y="tip", hue="smoker")
```

![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-2.png)

为了强调类之间的差异并提高可读性（accessibility），您可以为每个类使用不同的标记样式：

```python
sns.relplot(
    data=tips,
    x="total_bill", y="tip", hue="smoker", style="smoker"
)
```
![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-3.png)

在上面的示例中，色调语义是分类的，因此应用了默认的定性调色板。如果色调语义是数字（具体来说，如果它可以转换为浮动），则默认颜色切换到顺序调色板：

```python
sns.relplot(
    data=tips, x="total_bill", y="tip", hue="size",
)
```

![](https://images.jieyu.ai/images/2023/07/lesson18-scatter-4.png)

我们也可以将某列数值与点的大小进行语义关联：

```python
sns.relplot(data=tips, x="total_bill", y="tip", size="size")
```
在这种情况下，变量的字面量不被看成是点的面积，而是将数据的取值范围标准化为点的面积单位值。这个范围我们可以定制：

```python
sns.relplot(
    data=tips, x="total_bill", y="tip",
    size="size", sizes=(15, 200)
)
```
在这里，sizes[0] 是最小的点的面积；sizes[1] 是最大的点的面积。如果两者接近，则所有的点看起来大小差不多。如果 sizes[1] 较小，则所有的点都会较小；如果 sizes[0] 较大，则所有的点都会较大。

对某些数据集，比如时间序列，画线图是一个不错的选择。在 seaborn 中，这可以通过 lineplot() 函数直接完成，或者通过设置 kind="line" 与 relplot() 一起完成：

```python
dowjones = sns.load_dataset("dowjones")
sns.relplot(data=dowjones, x="Date", y="Price", kind="line")

# -- 或者，使用 LINEPLOT --
sns.lineplot(dowjones, x = "Date", y = "Price")
```

散点图和线图绘制并不是 seaborn 的专利，其它框架也一样能绘制。除了 seaborn 绘制更强调语义特征之外，它还有一个功能，就是能通过绘制平均值和围绕平均值的 95% 置信区间来聚合每个 x 值的多个测量值：

```python
fmri = sns.load_dataset("fmri")
sns.relplot(data=fmri, x="timepoint", y="signal", kind="line")
```

这将绘制出下图：

![](https://images.jieyu.ai/images/2023/07/lesson18-confidence.png)

注意到我们同样的调用，但在 dowjonse 数据集上并没有显示出上图这样的包络图，这是为什么？原因在于，只有当同一 x 值，存在多个测量值是，seaborn 才会绘制置信区间包络图。我们来看以下 fmri 这个数据集：

```python
fmri.groupby('timepoint').aggregate('count')
```

我们从输出结果发现，它的每一个数据点（即同一个'x'值）都对应着 56 次采样。seaborn 会在采样值达到什么条件下启用置信区间包络图，文档没有介绍。不过 seaborn 是开源的，如果对这一问题特别感兴趣，可以阅读他们的源码来了解。

!!! attention
    seaborn 绘图比较慢，因为绘图前需要进行较多的推导和猜测，尤其是在绘制带置信区间包络图时。
    
    如果已知数据量比较大，且存在多个采样点，可以通过将 errorbar 设置为 sd（即不用置信区间，而用一个标准差来表示时间点上的分布）。我们也可以干脆取消掉这一项计算，这可以通过设置 estimator 来完成。
    
    显然，当同一个时间点上存在多个采样时，如果不事先进行聚合，这样绘制出来的图会比较奇怪。

比如，如果我们对 fmri 数据集取消聚合计算，直接进行绘图：

```python
sns.relplot(data=fmri, x="timepoint",
            y="signal", kind="line", 
            estimator=None)
```

将会绘制出下面的图：

![](https://images.jieyu.ai/images/2023/07/lesson18-fmri-no-agg.png)

我们已经学习了在 relplot 中，通过指定 col 参数，将同一数据集按不同的子集，按分面图进行显示。我们也可以按色调语义，将数据集集分别显示，并显示误差包络带。

下面，我们就对比演示一下两种方法（分面图和按 hue 划分子集）下的绘图：

```python
import matplotlib.pyplot as plt

fig, axes = plt.subplots(nrows=1, ncols=3)

sns.lineplot(
    data=fmri, 
    x="timepoint", y="signal", hue="event",
    ax=axes[0]
)

grid = sns.relplot(
    data=fmri,kind='line',
    x="timepoint", y="signal", col="event"
)

def move_axes(ax, fig, pos):
    """Move an Axes object from a figure to a new pyplot managed Figure in
    the specified subplot."""

    # REMOVE THE AXES FROM IT'S ORIGINAL FIGURE CONTEXT
    ax.remove()

    # SET THE POINTER FROM THE AXES TO THE NEW FIGURE
    ax.figure = fig

    # ADD THE AXES TO THE REGISTRY OF AXES FOR THE FIGURE
    fig.axes.append(ax)
    # TWICE, I DON'T KNOW WHY...
    fig.add_axes(ax)

    # THEN COPY THE RELEVANT DATA FROM THE DUMMY TO THE AX
    ax.set_position(axes[pos-1].get_position())

    # THEN REMOVE THE DUMMY
    axes[pos-1].remove()

move_axes(grid.axes[0][0], fig, 2)
move_axes(grid.axes[0][1], fig, 3)

fig.show()
```

这将显示如下的图：

![](https://images.jieyu.ai/images/2023/07/lesson18-facet-vs-hue.png)

facet 绘图将绘制一个 1*2（一行两列）的图。加上通过 hue 来进行子集划分的子图，我们一共要绘制 3 个子图。所以， 第 3 行中，我们先生成了一个 1*3（一行三列）的图，然后尝试将 sns 的绘图分别填充到这三个 Axes 中。

我们立即使用 axes[0] 这个 Axes，在其中绘制了线图。这个绘制使用的是 sns.lineplot，它是一个轴级函数，允许我们指定一个 Axes 对象。这个线图我们将数据集按 event 划分成不同的子集，并用不同的色调来显示。

接下来，我们使用 sns.relplot 来绘制分面图。由于 relplot 是 figure-level 的绘图，所以，它不接受 Axes 对象，而是会自己生成一个新的图（figure)，因此，我们必须将 relplot 中生成的 Axes 移动到第三行生成的 fig 中，这样最终才能融合成一张图。

第 11 行，sns.replot 返回的对象中，包含了 axes 对象数组。我们要移动的 Axes 就在这个数组中。

第 21~24 行，将 ax 从它之前所在的 figure 中移除，加入到新的 figure 中。注意每一个 axes 对象都保留了对 figure 的引用，我们可以通过 ax.remove() 来移除掉这个引用，并通过 ax.figure 来设置新的引用。如果我们不执行第 21 行，则 grid 所绘制的图形将仍然显示出来。

第 27 和 29 行将 ax 加入到新的 figure 中。这里的语句看上去有所重复，是因为它们发生在不同的层面。有的是为了维护数据结构的完整，有的是为了触发图形绘制。

第 32 行，我们将新加入的 axes 定位到 pos 索引指定的位置。我们先是通过 get_position 获得索引处原 axes 的位置，再通过 set_position 将新加入的 axes 定位到该处。

第 35 行，我们将被替换的 axes（即 axes[pos-1] 从 figure 中移除。

!!! tip
    如果我们想掌握绘图的一些高级技巧，这里移动 Axes 及重新定位的技巧是必须掌握的。正如这里演示的那样，有时候我们想利用 seaborn 提供的 facet 绘图，又需要额外添加一些别的绘图，这样的场景还是很常见的。
    
    另外，第 37 行、第 24 行也揭示了 figure 与 axes 之间的关系。掌握并灵活运用这些关系，我们就能在定制图形上面游刃有余。

#### 1.1.2. 数据分布的可视化

了解变量是如何分布的，是任何数据分析或建模工作的早期步骤。通过对数据分布进行可视化，可以为许多重要问题提供快速答案：观察范围涵盖哪些范围？他们的中心倾向是什么？它们是否严重偏向一个方向？有双峰性的证据吗？是否存在显着的异常值？这些问题的答案是否因其他变量定义的子集而异？

在数据分布模块（distributions module）中，包含了一些 axes-level 的函数，比如 histplot, kdeplot, ecdfplot 和 rugplot 等，它们有时也被打包在一起，构成 figure-level 的函数，比如 displot, jointplot, pairplot 函数。

##### *******. 单变量直方图
直方图是最常用的数据分布可视化方案之一。它是 displot 中的默认方法。直方图是一个条形图，其中表示数据变量的轴被分为一组离散的箱，并且使用相应条的高度显示落入每个箱内的观测值的计数：

```python
penguins = sns.load_dataset("penguins")
sns.displot(penguins, x="flipper_length_mm")
```

绘制结果如下：

![](https://images.jieyu.ai/images/2023/07/lesson18-hist.png)

该图立即提供了有关 flipper_length_mm 变量的一些见解。例如，我们可以看到最常见的鳍状肢长度约为 195 毫米，但分布呈双峰，因此这个数字并不能很好地代表数据。

在直方图绘制中，箱 (bins) 的大小是一个重要参数。使用错误的箱大小可能会因为模糊数据的重要特征，凭空创造出特征而产生误导。默认情况下， displot() / histplot() 根据数据方差和观测值数量选择默认箱大小。但您不应该过度依赖此类自动方法，因为它们依赖于有关数据结构的特定假设。始终建议您检查不同 bin 大小的分布印象是否一致。要直接选择大小，请设置 binwidth 参数：

```python
sns.displot(penguins, x="flipper_length_mm", binwidth=3)
```
在某些情况下，指定 bin 的数量而不是它们的大小可能更有意义：

```python
sns.displot(penguins, x="flipper_length_mm", bins=20)
```

此外，我们也可以给 bins 传入一个数组，这种情况下，数组元素值会被当成 edges 的值来对数据集进行切分：

```python
sns.displot(tips, x="size", bins=[1, 2, 3, 4, 5, 6, 7])
```

![](https://images.jieyu.ai/images/2023/09/lesson18-hist-bin-edges.png)

在 size 列可以被 categorize 化的情况下，我们也可以通过传入 discrete = True，实现类似效果：

```python
sns.displot(tips, x="size", discrete=True, shrink=0.8)
```

![](https://images.jieyu.ai/images/2023/07/lesson18-hist-category.png)

这在视觉上看起来就象是柱状图。

在 penguins 的例子中，我们发现企鹅的鳍状肢长度的直方图呈现双峰分布，直觉告诉我们，这个数据似乎不合常理。究竟是什么原因造成的呢？

我们可以通过 hue 语义来对提供对条件子集的设置，以使得它背后隐藏的意义显露出来：
```python
penguins = sns.load_dataset("penguins")
sns.displot(penguins, x="flipper_length_mm", hue="species")
```

![](https://images.jieyu.ai/images/2023/07/lesson18-hist-conditional.png)

现在我们明白了，这种双峰，背后其实是多个物种叠加造成的。如果觉得上图还不够直观，我们还可以将样式改为阶梯图：

```python
sns.displot(penguins, x="flipper_length_mm", hue="species", element="step")
```

或者使用堆叠直方图：

```python
sns.displot(penguins, x="flipper_length_mm", hue="species", multiple="stack")
```

![](https://images.jieyu.ai/images/2023/07/lesson18-stacked-hist.png)

或者使用闪避条形图（dodge）：

```python
sns.displot(penguins, x="flipper_length_mm", hue="sex", multiple="dodge")
```

![](https://images.jieyu.ai/images/2023/07/lesson18-dodge-hist.png)

当然我们也可以使用 facet 图来进行分别显示。此时我们只需要将'sex'字段赋值给'col'就好（而不再使用 hue)。

!!! Note
    这是 seaborn 让我们可以直接深入到数据的语义联系，而不会陷入到图形绘制细节中的又一个例子。熟悉了 seaborn 的语法之后，我们就可以在数据探索阶段，快速而大量地进行尝试，从而迅速发现数据之间的规律。

##### *******. 核密度估计

直方图旨在近似通过对观测值进行分箱和计数来生成数据的潜在概率密度函数。核密度估计（KDE）为同一问题提供了不同的解决方案。 KDE 图不使用离散箱，而是使用高斯核平滑观测值，从而产生连续密度估计：

!!! Tip
    如果对这些术语不太熟悉，建议回过头仔细阅读行 11 章。

``` python
sns.displot(penguins, x="flipper_length_mm", kind="kde")
```

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-default.png)

与直方图中的 bin 大小非常相似，KDE 准确表示数据的能力取决于平滑带宽的选择。过度平滑的估计可能会消除有意义的特征，但平滑不足的估计可能会掩盖随机噪声中的真实形状。检查估计稳健性的最简单方法是调整默认带宽：

```python
sns.displot(penguins, x="flipper_length_mm", kind="kde", bw_adjust=.25)
```

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-bw-025.png)

请注意，窄带宽如何使双峰性更加明显，但曲线却不太平滑。相反，较大的带宽几乎完全掩盖了双峰性：

```python
sns.displot(penguins, x="flipper_length_mm", kind="kde", bw_adjust=2)
```

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-bw-2.png)

我们同样可以给核密度估计叠加上其它条件：

```python
sns.displot(penguins, x="flipper_length_mm", hue="species", kind="kde")
```

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-hue.png)

同样地，我们可以应用'stack'样式：

```python
sns.displot(penguins, x="flipper_length_mm", hue="species", kind="kde", multiple="stack")
```

![](https://images.jieyu.ai/images/2023/07/lesson18-kde-stack.png)

seaborn 还可以使用不同的透明度来填充上图，从而不同的密度分布之间可以更好地区分：

```python
sns.displot(penguins, x="flipper_length_mm", hue="species", kind="kde", fill=True)
```

![50%](https://images.jieyu.ai/images/2023/07/lesson18-kde-fill.png)

!!! attention
    KDE 图有很多优点。通过 KDE 图，我们可以很容易地辨别数据的一些重要特征，比如集中趋势、双峰性、偏斜等等，并且可以在子集之间轻松进行比较。

    但在某些情况下，KDE 可能无法很好地表示数据。这是因为 KDE 的假设底层分布是平滑且无界的。一旦变量的取值有界时，这种假设就可能失败。如果观察值接近边界（例如，变量的值不能为负），KDE 曲线可能会延伸到不切实际的值。

    这些不是 seaborn 的问题，只是说明这种情况下，不适合使用 kde 来进行图形绘制。

##### *******. ECDF
可视化分布的第三个选项是计算“经验累积分布函数”（ECDF）。该图通过每个数据点绘制一条单调递增曲线，使得曲线的高度反映具有较小值的观测值的比例：

```python
sns.displot(penguins, x="flipper_length_mm", hue="species", kind="ecdf")
```

![50%](https://images.jieyu.ai/images/2023/07/lesson18-ecdf.png)

ECDF 图有两个主要优点。与直方图或 KDE 不同，它直接表示每个数据点。这意味着无需考虑箱大小或平滑参数。此外，由于曲线是单调递增的，因此它非常适合比较多个分布。

ECDF 图的主要缺点是它表示分布形状的方式不如直方图或密度曲线直观。考虑一下鳍状肢长度的双峰性如何在直方图中立即显现出来，但要在 ECDF 图中看到它，您必须寻找变化的斜率。尽管如此，通过练习，您可以通过检查 ECDF 来学会回答有关分布的所有重要问题，这样做可能是一种强大的方法。

#### 1.1.3. 二元分布的可视化

到目前为止，所有示例都只考虑了单变量分布，也许通过分配给 hue 的第二个变量，有条件地展示多个变量之间的关系。但是，我们也可将第二个变量分配给 y 以绘制更直接的二元分布。

直方图的二元分布如下绘制，这将产生一个类似热力图的绘图：

```python
sns.displot(penguins, x="bill_length_mm", y="bill_depth_mm")
```

![50%](https://images.jieyu.ai/images/2023/07/lesson18-hist-bivariant.png)

双变量直方图将数据分箱在平铺绘图的矩形内，然后用填充颜色显示每个矩形内的观测值计数（类似于 heatmap() ）。类似地，双变量 KDE 图使用 2D 高斯平滑 (x, y) 观测值。双变量 KDE 具有三维钟形外观。尽管 3D 绘图在视觉上很吸引人，但它们很难阅读，因为绘图的某些部分被其他部分遮挡，并且并非所有应用程序都支持 3D 绘图的旋转。

![50%](https://images.jieyu.ai/images/2023/07/lesson18-kde-bivar-3d.png)

仅使用二维显示 3D 表面信息的一种常见方法是使用水平曲线或等高线。通过 sns 的 kdeplot 就可以实现这一点。通过改变 x 和 y 值并将第三个变量保持为常数，在 xy 平面中绘制每条轮廓线。这意味着每条线都是通过连接具有相等值或具有相同密度的点来绘制的。默认表示显示二维密度的轮廓：

```python
sns.kdeplot(
    penguins,
    x="bill_length_mm",
    y="bill_depth_mm",
    cmap="Greens",
    cbar=True,
    fill=True,
)
```

![50%](https://images.jieyu.ai/images/2023/07/lesson18-kde-bivariant.png?1)

这里我们通过 cbar 设置了色条，从中可以看出，越居于中心位置，数值越大。这与我们的直觉相符。我们通过 cmaps 给轮廓线指定了颜色。

上述两个类型的绘图中，我们仍然可以添加 hue:

```python
sns.displot(penguins, x="bill_length_mm", y="bill_depth_mm", hue="species", kind="kde")
```

![50%](https://images.jieyu.ai/images/2023/07/lesson18-kde-hue-bivar.png)

在单变量绘图中的那些参数，比如箱大小、平滑带宽等，仍然有效，这里就不再赘述。

#### 1.1.4. 联合分布和边缘分布

jointplot 是一种通过对双变量关系图或者双变量分布图进行边际分布增强的图形。默认情况下，jointplot() 函数使用 scatterplot() 来表示双变量分布，并使用 histplot() 来表示边缘分布。我们可以通过指定 kind 的类型，来绘制各种联合分布：

```python
from PIL import Image
from io import BytesIO
import numpy as np
import matplotlib.pyplot as plt

def to_image(fig):
    fig.canvas.draw()

    # NOW WE CAN SAVE IT TO A NUMPY ARRAY.
    data = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
    data = data.reshape(fig.canvas.get_width_height()[::-1] + (3,))
    return data

params = dict(data=penguins, x="bill_length_mm", y="bill_depth_mm")
allow_hue = (True, True, False, False, False, False)
kinds = ('scatter', 'kde', 'hist', 'reg', 'hex', 'resid')

figures = []

for i, kind in enumerate(kinds):
    if allow_hue[i]:
        fig = sns.jointplot(**params, kind=kind, hue='species')
    else:
        fig = sns.jointplot(**params, kind=kind)
        
    plt.close(fig.figure)
    _ = figures.append(to_image(fig.figure))
    
fig, axes = plt.subplots(nrows=2, ncols=3)

for i, ax in enumerate(axes.flatten()):
    _ = ax.imshow(figures[i])
    _ = ax.annotate(kinds[i], (0.5,0.5), xycoords='axes fraction')
```

这段代码将 jointplot 支持的 6 种模式全部绘制出来，并输出到一个由 2 行三列的子图构成的 figure 中。

![](https://images.jieyu.ai/images/2023/07/lesson18-jointplot-kinds.png)

这段代码中，我们遍历 kinds 元组，依次调用 sns.jointplot 方法，绘制每一类分布图。它们的参数基本相同，除了 scatter 和 kde 还支持 hue 这个参数。注意这里我们在传递参数时，是如何进行展开的。

jointplot 是 figure-level 函数，我们对其每调用一次，就会生成一个 Figure 对象。为了将这些对象排列成 2*3 的图，我们需要获取这些Figure对象的 Axes 对象，再重新生成一个 2*3 的图，并将原图中的 Axes 对象绑定到这个新生成对象中，对应的 Axes 对象上。这是我们在本次课中已经使用过的技巧。

但是，这次情况有所不同。jointplot 返回的结果中，它返回的结果中，包含了 Figure 属性。Figure 对象中又包含了 Axes 对象，但 Axes 对象有三个，我们要将这三个对象，重新在新生成的 Figure 中，对应的某个 Axes 上绑定，代码量将会比较大。

于是，我们采用另一种方法，将 jointplot 生成的 figure 转换成 Image，再通过 ax.imshow 来在恰当的位置显示它。这里的 ax，就是我们新创建的 2*3 子图中的某一个。

此外，在 jointplot 方法中，是没有办法指定图的标题的。我们通过在新的 ax 重新绘制的方法，也给每一个图叠加上了标题。在 annotate 中，需要传入要绘制的文本，xy 坐标，以及这个坐标是以何种方式对齐到坐标系的。这里我们通过 xycoords = 'axes framction'，将其对齐到 ax 子图上。此外还可以按 figure 进行对齐。

这里还有一个技巧需要交待一下。在第 28 行，33，34 行出现了_ 变量。在 notebook 中使用 seaborn，我们常常需要此技巧，即将函数返回值赋值给某个哑变量，以压制某些我们不需要的绘图显示。在这里，如果不这么做，那么我们将在最后作为一个整体的图之前，还将得到 jointplot 绘制的各个子图。读者可以把这里的赋值取消掉，自行尝试一下。

!!! Tip
    在第 17 课中，我们将 plotly 的绘图转换成以 RGB 值表示的 numpy array，是通过 plotly.io 模块中的 to_image 方法。

    在 matplotlib 中，没有统一的方法。除了这里的方案，我们还可以使用以下方法：
    
    ```python
    import numpy as np
    import io

    def save_img(fig):
        with io.BytesIO() as io_buf:
            fig.savefig(io_buf, format='raw', dpi=dpi)
            image = np.frombuffer(io_buf.getvalue(), np.uint8).reshape(
                int(fig.bbox.bounds[3]), int(fig.bbox.bounds[2]), -1)

        return io_buf
      ```

#### 1.1.5. 回归拟合
如果我们拿到一些数据，在显示这些数据之外，还要加一条回归线，通过我们需要自己来计算并绘制。在 seaborn 中，我们可以通过 regplot 来轻松绘制这种类型的图：

```python
tips = sns.load_dataset("tips")
sns.regplot(x="total_bill", y="tip", data=tips)
```

regplot 是一个 axes-level 的绘图函数，对应的 figure-level 的函数是 lmplot。最终，它们都绘制出一样的图：

![](https://images.jieyu.ai/images/2023/09/lesson18-regplot.png)

我们还可以在进行线性回归时，指定拟合多项式的阶数，比如

```python
anscombe = sns.load_dataset('anscombe')
sns.lmplot(x="x", y="y", data=anscombe.query("dataset == 'II'"),
           order=2, ci=None, scatter_kws={"s": 80})
```

![](https://images.jieyu.ai/images/2023/09/lesson18-lm-order-2.png)

在进行回归时，seaborn 也能处理离群值的影响。我们可以传入 robust 参数来消除离群值的影响：

```python
fig, (ax1, ax2) = plt.subplots(1, 2)

anscombe = sns.load_dataset("anscombe")
sns.regplot(x="x", y="y", data=anscombe.query("dataset == 'III'"),
           ci=None, scatter_kws={"s": 80}, ax=ax1)

sns.regplot(x="x", y="y", data=anscombe.query("dataset == 'III'"),
           robust=True, ci=None, scatter_kws={"s": 80}, ax=ax2)

_ = ax1.set_title("ols")
_ = ax2.set_title('rlm')
```

![50%](https://images.jieyu.ai/images/2023/07/lesson18-reg-ols-vs-rlm.png)

这次由于我们可以直接使用 axes-level 的绘图函数，所以代码要简单不少，很轻松就实现了两个图并排对照显示。在右图中，我们通过传入 robust=true，来让 seaborn 使用 rlm 模型来排除离群值的影响。

##### 1.1.5.1. Pairgrid 绘图

前面我们介绍了许多高级绘图函数。但是，它们还是缺少一种可以立即、全面展示数据集中两两关系的图。PairGrid 则弥补了这一缺陷。使用 PairGrid 可以为您提供数据集中有趣关系的非常快速、非常高级的摘要。

```python
iris = sns.load_dataset("iris")
g = sns.PairGrid(iris)
g.map(sns.scatterplot)
```

![](https://images.jieyu.ai/images/2023/07/lesson18-pair-grid.png)

显然，在这个图中的对角线位置上的绘图意义并不大。我们也可以单独为它指定一些有意义的单变量绘图，比如直方图：

```python
g = sns.PairGrid(iris, hue="species")
g.map_diag(sns.histplot)
g.map_offdiag(sns.scatterplot)
g.add_legend()
```

![](https://images.jieyu.ai/images/2023/07/lesson18-pairgrid-diag.png)

### 1.2. 主题

画出有吸引力的图形很重要。当我们为自己制作图表，或者探索数据集时，一张赏心悦目的图能提升我们的工作乐趣。可视化对于向观众传达定量见解也是至关重要的，在这种情况下，更需要有吸引观众注意力并吸引观众的图形。

Matplotlib 是高度可定制的，但很难知道需要调整哪些设置才能获得有吸引力的绘图。 Seaborn 附带了许多定制主题和一个用于控制 matplotlib 图形外观的高级接口。

在 seaborn 中，自带了 5 个预设主题，即"darkgrid", "whitegrid", "dark", "white"和"ticks"。默认的主题是 darkgrid。各个主题的风格正如它们的名字所暗示的那样。如果主题带 grid，则将显示网格线；反之则不带。

我们通过 set_style 来设置使用哪一个主题。在 matplotlib 那一章，我们有个例子隐藏了 spine。这个任务在 seaborn 中将非常简单：

```python
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

def sinplot(n=10, flip=1):
    x = np.linspace(0, 14, 100)
    for i in range(1, n + 1):
        plt.plot(x, np.sin(x + i * .5) * (n + 2 - i) * flip)
        
sns.set_style('white')
sinplot()
sns.despine()
_ = plt.title("white theme without spine")
```

![75%](https://images.jieyu.ai/images/2023/07/lesson18-sinplot.png)

绘图我们并没有使用 seaborn 的函数，但我们使用了 seaborn 的方法来进行主题设置，以及移除 spine。这说明，seaborn 与 matplotlib 之前有着紧密的集成。

seaborn 还支持通过上下文管理语法，来临时设置绘图参数：

```python
f = plt.figure(figsize=(6, 6))
gs = f.add_gridspec(2, 2)

with sns.axes_style("darkgrid"):
    ax = f.add_subplot(gs[0, 0])
    sinplot(6)

with sns.axes_style("white"):
    ax = f.add_subplot(gs[0, 1])
    sinplot(6)

with sns.axes_style("ticks"):
    ax = f.add_subplot(gs[1, 0])
    sinplot(6)

with sns.axes_style("whitegrid"):
    ax = f.add_subplot(gs[1, 1])
    sinplot(6)

f.tight_layout()
```

![75%](https://images.jieyu.ai/images/2023/07/lesson18-temp-style.png)

我们也可以更改 seaborn 的样式。要知道哪些样式可以更改，我们通过以下方法：

```python
sns.axes_style()
```

然后，我们调用 set_style 方法，传入 theme 名字和要更改的样式：

```python
sns.set_style("darkgrid", {"axes.facecolor": ".9"})
sinplot()
```

除了样式更改之外，seaborn 还为不同场景下的绘图，提供了缩放功能。这四个场景分别是'paper', 'notebook', 'talkt'和'poster'。默认场景是 notebook。由于场景切换不是高频应用，这里我们就不举例了。

### 1.3. 调色板的使用
seaborn 可以轻松使用非常适合我们的数据特征和可视化目标的颜色。使用调色板的最重要的函数是 color_palette() 。调用它将生成一个调色板，可供任何具有 palette 参数的函数使用。

color_palette() 的主要参数是一个字符串：预置调色板的名称，比如deep, muted等，或者是matplotlib的colormap名称，或者是”husl"/"hsl"等。

如果不带参数调用 color_palette() ，则将返回当前使用的默认调色板。可以使用相应的 set_palette() 函数设置此默认调色板，该函数在内部调用 color_palette() 并接受相同的参数。

一般来说，调色板分为以下三类之一：

* 定性调色板（qualitative palettes），适合表示分类数据
* 顺序调色板（sequential palettes），适合表示数值数据
* 发散的调色板（diverging palettes），适合表示具有分类边界的数字数据

#### 1.3.1. 定性调色板

定性调色板非常适合呈现分类数据，因为可以给这些有限的分类都分配上恰当的颜色。

Seaborn 中预置了 matplotlib 调色板的六种变体，称为 deep 、 muted 、 pastel 、 bright 、 dark 和 colorblind 。我们通过下面的代码来查看这些调色板：

```
f = plt.figure(figsize=(6, 6))
gs = f.add_gridspec(2, 3)


for i, name in enumerate(("deep", "muted", "pastel", "bright", "dark", "colorblind")):
    sns.set_palette(name)
    r = i // 3
    c = i % 3
    ax = f.add_subplot(gs[r, c])
    plt.pie([1] * 10)
    plt.gca().set_title(name)
```
结果请在notebook中运行和查看。

这些预置调色板在亮度和色饱和度上有这些特点：

![50%](https://images.jieyu.ai/images/2023/07/lesson18-palette.png)

这些调色板类似于 matplotlib 中的 tab10, 但更柔和一些。

如果类别的数量不确定，最方便的方法是使用循环色系统。它的具体方法是在循环色彩空间中，绘制均匀间隔的颜色。下面的代码演示了 seaborn 是如何做到这一点的：

```python
p1 = sns.color_palette("husl", 3)
sns.palplot(p1)

p2 = sns.color_palette("husl", 5)
sns.palplot(p2)

p3 = sns.color_palette("husl", 8)
sns.palplot(p3)
```

![](https://images.jieyu.ai/images/2023/07/lesson18-circular-palette.png)

这段代码演示了如何生成一个有着不同采样次数的**循环色彩系统**下的调色板，并且绘制它。在我们挑选色彩时，可能需要用到这一技巧。

```python
p4 = sns.color_palette("RdPu", 10)
sns.palplot(p4)

def sinplot(n=10, flip=1):
    x = np.linspace(0, 14, 100)
    for i in range(1, n + 1):
        plt.plot(x, np.sin(x + i * .5) * (n + 2 - i) * flip)
        
sns.set_palette(p4)
sinplot()
```

这段代码演示了如何设置一个调色板，以便在后面的代码中使用这个调色板。

第 10~11 行，这里我们生成了一个单一色调的颜色系列，接下来，我们通过 set_palette 来设置它，并且绘制 sin 曲线：

![](https://images.jieyu.ai/images/2023/07/lesson18-sin-with-circular.png)

此外，还有来自 color brewer 工具的分类调色析，比如"Set2"就是其中一种。

#### 1.3.2. 连续调色板

第二类主要调色板称为“连续调色板”（Sequential color palettes）。当数据取值为连续域，并且数据范围是从相对较低或不感兴趣的值到相对较高或感兴趣的值（反之亦然）时，这种映射是最合适的。如果我们映射的数据是数值类型（相之于分类数据），seaborn函数一般将默认使用连续调色板。正如我们在上面看到的，连续调色板中变化的主要维度是亮度。

在连续调色板中，又有一种所谓视觉统一的调色板：两种颜色的相对可辨别性与相应数据值之间的差异成正比。我们在上面绘制 sin 曲线的例子中已经接触到过这个概念了。在那个图中，颜色的基调都是红色，但亮度不一样。

在 seaborn 中，这类调色板中，预定义的有 rocket, mako, flare 和 crest。此外，matplotlib 中也有一些，比如 magma, viridis 等。每个连续调色板都有一个反转版本，其后缀为"_r"。

```python
palettes = ["rocket", "mako", "flare", "crest"]

fig, axes = plt.subplots(nrows=8, ncols=1, layout="tight", figsize=(12, 4))
i = 0
while i < 4:
    p = sns.color_palette(palettes[i], as_cmap=True)
    ax = axes[i * 2]
    ax.set(xticklabels=[], yticklabels=[])
    ax.imshow(np.arange(100).reshape((1, 100)), cmap=p, aspect="auto")

    name = palettes[i] + "_r"
    p = sns.color_palette(name, as_cmap=True)
    ax = axes[i * 2 + 1]
    ax.set(xticklabels=[], yticklabels=[])
    ax.imshow(np.arange(100).reshape((1, 100)), cmap=p, aspect="auto")

    i += 1

fig.subplots_adjust(hspace=0.01)
```

![](https://images.jieyu.ai/images/2023/07/lesson18-palette-reverse.png)

这段代码中，有这样几点值得注意：

1. 绘制调色板在seaborn中有专门的方法，叫palplot。这里的rocket, mako等调色板，是同时具有离散和连续两种定义的，本来也是可以通过palplot绘制的。但我们的示例中，我们设置了 as_cmap = True。此时返回的palette就不再具有离散色定义列表。如果我们仍然使用palplot来绘制，就会抛出 ListedColorMaps has no Len 错误。因此我们就不能使用 sns.palplot 来绘制，这是这里我们使用imshow来显示调色板的原因。
2. 在绘制调色板时，我们通过 ax.set(xticklabels=[], yticklabels=[]) ，隐藏了数轴上的刻度及文本标签。这样会美观一些。
3. 我们调用 imshow 时，指定 aspect="auto"，这对绘图有何影响，请大家自行通过修改代码运行并体会。在下面一个示例中，我们将不加这一参数进行调用，大家也可以比较一下

Color Brewer 中也存在一些连续调色板，比如我们在绘制二元 kde 等值图时，就使用过'Greens'。它就是Color Brewer中的一种。

```python
fig, axes = plt.subplots(nrows=3, ncols=1, layout="tight")

sns.despine(left=True, bottom=True)

for i, kind in enumerate(("Blues", "YlOrBr", "Greens")):
    p = sns.color_palette(kind, as_cmap=True)
    ax = axes[i]
    ax.set(xticklabels=[], yticklabels=[])
    ax.imshow(np.arange(100).reshape((1, 100)), cmap=p)
```

![](https://images.jieyu.ai/images/2023/07/lesson18-color-brew.png)

这次我们没有指定aspect = "auto"，大家可以比较一下，看看这个参数的作用是什么。

#### 1.3.3. 发散调色板
发散调色板（diverging color palettes) 适用于这样一些数据集：数据集中有着比较大的最低值和最高值，数据集中有着比较大的最低值和最高值，而且我们只对这些绝对值比较大的数据感兴趣，对中间部分则不那么在意。因此，数据在跨越中位数（通常为零）时，应该被弱化。这一类调色板也有所谓感知统一的说法，主要体现在 vlag 和 icefire 两个调色板上。它们的极点都使用蓝色和红色，许多人直观地将其称之为“冷”和“热”：

```python
fig, axes = plt.subplots(nrows=2, ncols=1, layout="tight", figsize=(12,4))

for i, kind in enumerate(("vlag", "icefire")):
    p = sns.color_palette(kind, as_cmap=True)
    ax = axes[i]
    ax.set(xticklabels=[], yticklabels=[])
    ax.imshow(np.arange(100).reshape((1, 100)), cmap=p, aspect='auto')
```

![](https://images.jieyu.ai/images/2023/07/lesson18-divergeing.png)

!!! Tip
    学到这里，我们发现关于颜色的配制，从 matplotib 到 plotly 和 seaborn，分别使用了 color map, color scale 和 color palette 等术语。它们的含义大致相近。

## 2. PyEcharts
Echarts 是一个开源的数据可视化 JS 库，pyecharts 是一个生成 Echarts 图表的 python 类库。Echarts 最初由百度开发，后来开源，贡献给 Apache 基金会，现在是其顶级成员之一。Echarts 使用了很多优化技术，据称可以实现千万级数据的流畅交互。

ECharts 提供了常规的折线图、柱状图、散点图、饼图、K 线图，用于统计的盒形图，用于地理数据可视化的地图、热力图、线图，用于关系数据可视化的关系图、treemap、旭日图，多维数据可视化的平行坐标，还有用于 BI 的漏斗图，仪表盘，并且支持图与图之间的混搭。

![](https://images.jieyu.ai/images/2023/07/lesson18-pyecharts-intro.jpg)

根据文档，它提供了基于 WebGL 的 EchartsGL，可以绘制出三维的地球，建筑群，人口分布的柱状图，在这基础之上我们还提供了不同层级的画面配置项，几行配置就能得到艺术化的画面！

![](https://images.jieyu.ai/images/2023/07/lesson18-pyecharts.jpg)

通过 pyecharts 来绘图，原理跟 plotly 类似，也是在 python 域内完成图形定义，然后转换成 json 对象，再交给 echarts 来渲染。但在这个转换中，与 plotly 中不同的是，我们需要自行保证 json 序列化能够成功。而通过 Plotly 定义的图形，哪怕数据使用了 dataframe, numpy，plotly 一般情况下都是能将其 json 序列化的。pyecharts 没有这么做，是为了避免打包这两个库，导致包体积变大。

现在，pyecharts 最新的版本是 v2，支持 python3.6~python3.11，它的底层框架是最新的 Echarts 5。

!!! Note
    pyecharts 及 echarts 在国内使用较多。
    
    除了上述优点之外，它的缺点也比较明显。
    
    pyecharts 在文档上不做版本管理，如果我们使用了它的某个版本，比如 v1 进行开发，在开发完成后，我们将很难搜索到 v1 版本的文档，因为现在 pyecharts 已经升级到了 v2。
    
    在设计上，echarts 在一些领域术语的使用上，也与其它框架有所不同。比如，它不使用 figure 这样的顶级图形概念，而是使用的 global options 概念。

其它框架中，当图形定义完成后，最终我们一般会调用 show 方法来将图形呈现出来。show 方法会根据预定义的渲染 backend，自行决定如何处理。如果是在 notebook 中，单元格中的最后一个语句的返回值如果是某个图形对象，它还会自动显示。

在 pyecharts 中没有类似的行为。pyecharts 被设计成为能与 python web 服务器集成， 也能在 notebook 中运行。但是，在不同的环境下，显示的函数不一样。如果是在 notebook 中运行，需要调用 render_notebook 方法；如果是在服务器中运行，则要调用 render 方法。当我们在 notebook 中把代码调通后，如果要移植到服务器项目中，还需要将这些方法全部进行修改。

在本课的示例中，我们将只使用 jupyterlab 作为渲染后台，因此，我们显示图形时，将使用 render_notebook 方法。

### 2.1. 在 Notebook/Jupyterlab 中运行

与 plotly 相比，pyecharts 在环境自适应上略逊一筹。要在 Notebook/jupyterlab 中使用，我们需要声明具体的 notebook 环境（即是 notebook 还是 jupyterlab）。pyecharts 默认环境为 notebook，所以，在这种情况下，也可以不用声明。

!!! attention
    在课程实验环境中，并没有安装 pyecharts。请读者自行安装。安装命令如下：
    ```bash
    !pip install pyecharts
    ```

下面，我们就以在 jupyter lab 中如何使用 pyecharts 为例进行说明：

```python
from pyecharts.globals import CurrentConfig, NotebookType
CurrentConfig.NOTEBOOK_TYPE = NotebookType.JUPYTER_LAB

from pyecharts.charts import Bar
from pyecharts import options as opts
from pyecharts.globals import ThemeType

import pyecharts.options as opts
from pyecharts.charts import Bar, Line

bar = (
    Bar()
    .add_xaxis(["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"])
    .add_yaxis("商家 A", [5, 20, 36, 10, 75, 90])
    .add_yaxis("商家 B", [15, 6, 45, 20, 35, 66])
    .set_global_opts(title_opts=opts.TitleOpts(title="主标题", subtitle="副标题"))
)

bar.load_javascript()
```

这段代码中，第 2 行设置了环境类型为 jupyter lab。接下来注意第 19 行，这里是加载了基本的 js 文件及绘制 bar 所需要的 js 文件到 notebook 当中。pyecharts 可能为了速度上的考虑，在加载 js 的策略上比较保守，不会一次性把所有的 js 都加载到 notebook。这里我们是通过 bar 对象调用的 load_javascript，如果我们后面绘制其它图形对象，也可能要通过该对象，再次调用 load_javascript 方法。

上述代码运行后，并不会有输出。我们还必须调用 render_notebook 方法（无论环境是 notebook 还是 jupyter lab，都是调用这个方法）才能输出最终的图形：

```python
bar.render_notebook()
```

这个条状图比较简单，我们就不进行介绍了。

!!! attention
    注意 render_notebook() 与 bar.load_javascript() 必须在不同的单元格中调用，否则 pyecharts 将无法显示图形。

### 2.2. 调用习惯

pyecharts 在构建图形时，支持了链式调用，这是它与其它框架明显不同的特点之一：

```python
from pyecharts.charts import Bar

bar = (
    Bar()
    .add_xaxis(["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"])
    .add_yaxis("商家 A", [5, 20, 36, 10, 75, 90])
)
bar.render_notebook()
```
这一次，我们不需要调用 bar.load_javascript()，因为之前已经为 bar 对象加载过 javascript 了。

当然，我们也可以仍按传统方式来调用。

### 2.3. 使用选项

在 pyecharts 中，title, colormap, sliderranger, legend, axis 等对象都是通过选项来进行设置。

![](https://images.jieyu.ai/images/2023/07/lesson18-pyecharts-options.png)

有一些选项归类在全局选项中，我们通过 set_global_opts 来设置，比如下面的代码用来设置标题：

```python
bar.set_global_opts(title_opts=opts.TitleOpts(title="主标题", subtitle="副标题"))
```

标题对应的属性由 TitleOpts 进行管理。如果我们要设置坐标轴线，可以用 AxisLineOpts 设置；设置坐标轴刻度可以用 AxisTickOpts，等等。
### 2.4. 子图和布局

在 Pyecharts 中，支持的布局种类更多，它在 grid 布局之外，还提供了 page, tab, timeline 等方案。

!!! Tip
    如果要给一个理由选择 pyecharts 而不是我们前面介绍的那些框架，那一定是 Pyecharts 的布局功能。Pyecharts 的布局已经超出了绘图本身，某咱程度上，可以看成是提供了一个跟绘图有关的 webpage 解决方案，这一点在 tab 布局和 DraggablePageLayout 上体现的尤为明显。

#### 2.4.1. Grid 布局
**Grid 布局**是我们在之前最熟悉的一种：

```python
from pyecharts import options as opts
from pyecharts.charts import Bar, Grid, Line
from pyecharts.faker import Faker

bar = (
    Bar()
    .add_xaxis(Faker.choose())
    .add_yaxis("商家 A", Faker.values())
    .add_yaxis("商家 B", Faker.values())
    .set_global_opts(title_opts=opts.TitleOpts(title="Grid-Bar"))
)
line = (
    Line()
    .add_xaxis(Faker.choose())
    .add_yaxis("商家 A", Faker.values())
    .add_yaxis("商家 B", Faker.values())
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Grid-Line", pos_top="48%"),
        legend_opts=opts.LegendOpts(pos_top="48%"),
    )
)

grid = (
    Grid()
    .add(bar, grid_opts=opts.GridOpts(pos_bottom="60%"))
    .add(line, grid_opts=opts.GridOpts(pos_top="60%"))
)

grid.load_javascript()
```

为了能显示这个图，我们需要在 notebook 中，另起一个 cell，输入：

```python
grid.render_notebook()
```

最终图形显示如下：

![](https://images.jieyu.ai/images/2023/07/lesson18-pyecharts-grid.png)

首先，我们分别构建了一个柱状图和一个折线图。然后构建了一个 Grid 对象用来管理布局，第 25~26 行，我们将这两个子图加到 grid 当中，并且指定第一个子图占据高度的 40% (1 - pos_bottom)。

#### 2.4.2. Page 布局
在** Page 布局**中内置了 SimplePageLayout 和 DraggablePageLayout。后者允许你在生成图表后，手动拖拽图形进行所见即所得的布局。这会生成一个 json 格式的选项，将这个选项与生成的 html 文件一起部署。

page 布局演示代码比较长，而且在 notebook 中不便演示，这里我们就不提供代码了。感兴趣的读者可以查看 [这个例子](https://gallery.pyecharts.org/#/Page/page_draggable_layout)

这个[视频](https://images.jieyu.ai/images/2023/10/pyecharts_page_layout.mp4) 演示了它的用法。

#### 2.4.3. tab 布局

下面的例子显示了如何进行 tab 布局：

```python
from pyecharts import options as opts
from pyecharts.charts import Bar, Grid, Line, Pie, Tab
from pyecharts.faker import Faker

def bar_datazoom_slider() -> Bar:
    c = (
        Bar()
        .add_xaxis(Faker.days_attrs)
        .add_yaxis("商家 A", Faker.days_values)
        .set_global_opts(
            title_opts=opts.TitleOpts(title="Bar-DataZoom（slider-水平）"),
            datazoom_opts=[opts.DataZoomOpts()],
        )
    )
    return c

def line_markpoint() -> Line:
    c = (
        Line()
        .add_xaxis(Faker.choose())
        .add_yaxis(
            "商家 A",
            Faker.values(),
            markpoint_opts=opts.MarkPointOpts(data=[opts.MarkPointItem(type_="min")]),
        )
        .add_yaxis(
            "商家 B",
            Faker.values(),
            markpoint_opts=opts.MarkPointOpts(data=[opts.MarkPointItem(type_="max")]),
        )
        .set_global_opts(title_opts=opts.TitleOpts(title="Line-MarkPoint"))
    )
    return c

tab = Tab()
tab.add(bar_datazoom_slider(), "bar-example")
tab.add(line_markpoint(), "line-example")

tab.render_notebook()
```

输出结果如下：

![50%](https://images.jieyu.ai/images/2023/07/lesson18-pyecharts-tab.png)

#### 2.4.4. Timeline

```python
from pyecharts import options as opts
from pyecharts.charts import Pie, Timeline
from pyecharts.faker import Faker

attr = Faker.choose()
tl = Timeline()
for i in range(2015, 2020):
    pie = (
        Pie()
        .add(
            "商家 A",
            [list(z) for z in zip(attr, Faker.values())],
            rosetype="radius",
            radius=["30%", "55%"],
        )
        .set_global_opts(title_opts=opts.TitleOpts("某商店{}年营业额".format(i)))
    )
    tl.add(pie, "{}年".format(i))
    
tl.load_javascript()
```

同样地，我们需要在另一个单元格中渲染它：

```python
tl.render_notebook()
```

由于时间线将具有动画一样的效果，这里就不展示了，请读者在实验环境下自行运行。

!!! Tip

    pyecharts 以及 echarts 在软件设计上体现了与前面完全不一样的设计理念与软件工程美学。学习 pyecharts 最好的方法，不是自顶而下，从领域知识、框架入手，逐步深入。相反，pyecharts 提供了大量的示例，对照这些示例进行仿写，可能是最快的方式。

    pyecharts 的示例请见 [gallery](https://gallery.pyecharts.org/)。

## 3. 关于颜色和美学

正确使用颜色对于绘图非常重要。介绍这部分知识已经超出本课程范围，这里列举一些网站，供参考：

[NASA 地球观察站](https://earthobservatory.nasa.gov/blogs/elegantfigures/2013/08/05/subtleties-of-color-part-1-of-6/)
[Adobe 色轮产生器](https://color.adobe.com/zh/create/color-wheel)
[从图像中取色](https://coolors.co/image-picker)
[色彩工具](https://www.generateit.net/invert-color-codes)
