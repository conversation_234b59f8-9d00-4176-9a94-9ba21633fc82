## 1. 什么是网格交易？

所谓网格交易，简单来说，就是将账户资金分成多份，股价每下跌一定幅度就买入一个单位资金的股票，待反弹一定幅度就卖出这份股票，然后又重新等待机会买入，就这样不断地低买高卖、高抛低吸，从而获利的方式。

![](https://images.jieyu.ai/images/2023/06/grid.png)

在上图中，交易者在事先确定好的压力位和阻力位之间，划分出若干等距网格，当股价从某个网格往下走时，如果有头寸则买入；等待股价向上走时，如果存在已获利仓位则卖出。当股价波动向上超过压力位时，一般选择全部抛出止盈；如果向下突破阻力位，则可能选择继续持有，也可能止损出局，或者反手做空已减轻损失，这时要根据自己的资金管理压力来决定。

我们把上述版本称为 vanilla 版本，它的特点是，使用事先确定的固定中枢和网格数。

网格交易法最早来自于信息论之父香农。据称他运用这一方法，在十多年的交易生涯中，资金获得了 29%的年复利增长。香农版的网格交易法是这样的，在股票的任一个价位上，买入资金的 50%。当股票价格上涨到一定幅度，就卖出一部分股票套现，但保持市值与资金余量仍为 1：1，如果股票价格下跌，就买入股票，仍然保持资金与市值仍为 1：1。其核心是永远保持钱和币的价值是 1：1。

本次课程中，我们将首先介绍这个版本如何实现。然后讨论该实现中，存在的技术问题与改进，最后从标的选择、参数和资金利用率优化等方面入手，讨论如何优化 vanilla 版本，使得它在同一支标的、同一个时间区内，收益率从 25.7%提升到 79.83%（没有过拟合和未来数据）。

最后，我们将讨论动态网格，以实现网格对趋势的动态跟随。这样的策略收益可能会低一些，但是，如果你不知道哪一支标的未来将处于震荡期的话，动态网格将帮到你，因为它对震荡期和震荡上升期的标的都适用。

## 2. 代码实现
```python
from coursea import *
from omicron.strategy.base import BaseStrategy

await init()

class GridStrategy(BaseStrategy):
    def __init__(
        self,
        url: str,
        sec: str,
        pivot_price: float,
        unit: float,
        interval: float = 0.02,
        grids=10,
        **kwargs,
    ):
        self.sec = sec
        self.pivot_price = pivot_price
        self.interval = interval
        self.unit = unit
        self.grids = grids
        self.records = [0] * grids

        _low = pivot_price * ( 1 - grids * interval / 2)
        _high = pivot_price * ( 1 + grids * interval / 2)
        self.bins = array_math_round(np.linspace(_low, _high, grids - 1), 2)
        print("each grid: ", self.bins)

        self._last_grid = 0

        super().__init__(url, **kwargs)

    def grid_level(self, price: float) -> int:
        """通过价格搜索对应的网格
        """
        return np.digitize(price, self.bins).item()

    def _find_position_for_sell(self, grid: int, asked: float) -> float:
        """查找可售出仓位

        """
        vol = 0
        margin = asked
        for i in range(grid - 1):
            if self.records[i] > 0:
                size = min(margin, self.records[i])
                self.records[i] -= size  # type: ignore
                vol += size

                margin = asked - vol  # margin 与 vol 相加 恒等于 asked
                if margin <= 0:
                    return vol

        if margin > 0 or vol == 0:
            logger.info("找不到足够的持有仓位出售。要求%s, 实际%s。这不一定是错误。", asked, vol)

        return vol

    async def predict(self, frame: Frame, frame_type: FrameType, i: int, **kwargs):
        bars = await Stock.get_bars(self.sec, 1, frame_type=frame_type, end=frame)

        price = math_round(bars["close"][0].item(), 2)
        cur_grid = self.grid_level(price)
        last_grid = self._last_grid
        self._last_grid = cur_grid

        logger.info("%s %s->%s", frame, last_grid, cur_grid, date=frame)
        if last_grid == 0:
            return
        strides = cur_grid - last_grid # 步数
        # 达到网格顶部，全部售出
        avail = self.available_shares(self.sec, frame)
        if cur_grid >= self.grids - 1 and avail > 0:
            await self.sell(
                self.sec, vol=avail, order_time=tf.combine_time(frame, 14, 55)
            )
            logger.info("触及网格顶部，全部止盈。", date=frame)
            return

        # 如果新网格大于前一天网格，查看是否有可卖出股份
        if strides > 0:
            asked = (strides * self.unit / price)//100 * 100
            vol = min(avail, self._find_position_for_sell(cur_grid, asked))
            if vol > 0:
                result = await self.sell(
                    self.sec, vol=vol, price = price, order_time=tf.combine_time(frame, 9, 31)
                )
                logger.info("%s 卖出 %s", frame, vol, date=frame)
        elif strides < 0:
            # 如果新网格小于前一天网格，买入
            if self.cash >= self.unit:
                result = await self.buy(
                    self.sec,
                    money=abs(self.unit * strides),
                    order_time=tf.combine_time(frame, 14, 55),
                )

                filled = result.get('filled', 0)
                self.records[cur_grid] += filled
                logger.info("%s 买入 %s, 记入 grid %s", frame, filled, cur_grid, date=frame)

start = datetime.date(2023, 1, 4)
end = datetime.date(2023, 4, 14)
gs = GridStrategy(
    cfg.backtest.url,
    "002344.XSHE",
    4.63,
    100_000,
    interval = 0.02,
    start=start,
    end=end,
    frame_type=FrameType.DAY
)

await gs.backtest(baseline = "002344.XSHE")
await gs.plot_metrics()
```
我们先来看初始化部分。

### 2.1. 初始化

初始化需要确定网格交易最重要的几个参数，即中枢价、网格数、网格间距。这几个参数共同决定了网格的划分。在 init 函数中，我们是这样划分网格的：

1. 通过中枢价、网格数和间距决定上、下界，即：
    ```python
        _low = pivot_price * ( 1 - (grids - 1)// 2 * interval)
        _high = pivot_price * ( 1 + (grids - 1) // 2 * interval)
        ```
2. 通过`np.linspace`方法，将 [_low, _high] 之间的区间网格化。注意我们在这个区间里，只划分`grids - 1`个格子。并且要求`grids`本身为偶数，以便中心对称。假设中枢价为 5 元，网格为 10，间距为 2%，则这些格子为：
    ```
    4.5  , 4.625, 4.75 , 4.875, 5, 5.125, 5.25 , 5.375, 5.5
    ```
    对任意一个价格，我们可以通过`numpy.digitize`方法，利用上述格子来确定它的网格编号，比如：
    ```
    np.digitize(4.4) -> 0
    np.digitize(4.49) -> 0
    np.digitize(4.5) -> 1
    np.digitize(4.9) -> 4
    np.digitize(5.4) -> 8
    np.digitize(5.6) -> 9
    ``` 
3. 我们还要求传入了`unit`，即触发信号后，每一个网格买/卖的金额数。
4. 我们还初始化了一个`self.records`数组，用来保存每个网格当前的持仓数，初始值均为 0。

### 2.2. 评估函数

评估函数首先根据当前的`frame`来获取行情数据，注意在这里，我们只需要获取一个`bar`的数据即可。

接下来，我们计算当前的网格`cur_grid`，如果`cur_grid`已经大于等于最大的网格，意味着当前价格已经触及压力位，我们需要抛出全部仓位以止盈。

然后我们将`cur_grid`与`self._last_grid`相减，得到当前位置距离上一次的网格位置的步数，记作`strides`。当这个值大于 0 时，网格在上移，需要考虑卖出；当这个值小于 0 时，网格在下移，需要考虑买入。

在卖出时，我们需要从当前的网格`cur_grid`向下搜索，检查位于其下的网格中，是否存在持仓，如果有，则卖出。这里要注意，如果当前网格是`a`，我们不会卖出`a-1`网格处的仓位。这是由我们买入和卖出的行为决定的。在上面的代码中，我们买入和卖出并不是在网格标示的边界价格上，而是以市价买入和卖出，那么就可能出现这样的情况，比如我们在 4.49 处买入一手，网格为 0；现价为 4.5，对应网格 1，应该卖出。但如果我们真的卖出的话，并没有赚到一个网格间距的差价。所以，我们实际上会从 0 号网格搜索到`a-2`号网格为止。

另外要注意的是，无论是买入还是卖出，我们都不会只操作一个单位（即`unit`)，而是要操作`unit * abs(strides)`个单位。读者可以想一下为什么。

最后，如果是买入的情况，我们需要将服务器返回的成交额，记录（增加）到`self.records`对应的网格中。

### 2.3. 策略行为分析

我们在 2023 年 1 月 4 日到 4 月 14 日这段期间，对某个标的进行一次回测。结果见下图：

![75%](https://images.jieyu.ai/images/2023/06/grid_log.png)

1 月 4 日，标号 1，第一次进入评估函数，初始始化网格值为 5，然后退出评估函数。
1 月 5 日，标号 2，处于下跌中，我们由网格 5 进入到网格 4，买入 19500 股，记在 4 号网格处。
1 月 6 日，标号 3，继续下跌，我们由网格 4 下降到网格 3，买入 20000 股，记在 3 号网格处。
1 月 11 日，标号 4，继续下跌，我们由网格 3 下降到网格 2，买入 20600 股，记在 2 号网格处。
1 月 12 日，标号 5，继续下跌，我们由网格 2 下降到网格 1，买入 21100 股，记在 1 号网格处。此时距回测开始，股价已经下跌了 8%左右，但理论上，策略此时的浮亏仅为`(-6% * 10% + -4% * 10% + -2% * 10%)`即 1.2%左右。

1 月 13 日，标号 6，价格由网格 1 上升到网格 2。由于最低的仓位在网格 1 处，不满足卖出条件，放弃卖出（此时如果卖出，可能赚不到一个网格的差价）。
1 月 30 日，标号 7，由网格 2 上升到网格 3。此时卖出标号 5 处买入的仓位，即记录在 1 号网格的仓位。
2 月 2 日，标号 8，由网格 3 上升到网格 4，此时卖出标号 4 处买入的仓位，即记录在 2 号网格的仓位。

现在，我们拉长回测时间，使用 2021 年 1 月到 2022 年底两年的时间，选择一支期间下跌的个股来运行一下。我们使用它在回测开始时的开盘价，网格数还是选择 10 个，网格间距使用 1%。

![50%](https://images.jieyu.ai/images/2023/06/grid-bt-random-down.png)

回测期间，该标的下跌 49.1%，与之对照，策略收益为正 1.37%，年化超额达到 30%以上。作为随机选择的一个标的，买入时几乎就是最高点，策略表现不能说很差，但毕竟我们追求的是绝对收益，所以肯定有优化空间。

下面，我们就来讨论如何优化该策略。在此之前，我们先看上述实现中存在的一些技术问题。

## 3. 技术实现问题
### 3.1. 委托价格和交易时机

在上述示例中，我们并没有按网格边缘价格来买入，而是当股价进入到新的网格区间触发交易信号时，以市价进行的买入。但实际上，无论是回测还是实盘，我们都可以令其按网格边缘价格来买入。

在实盘中，我们可以提前计算好价格，在集合竞价时就进行上下一个网格的预埋单。不过要注意，当天的价格波动可能贯穿好几个网格，因此，即使我们进行了预埋单，还是应该实时监控价格的变动并作出响应。

在回测中，我们可以以收盘价为准来检测当天信号是否触发，如果触发了，再以开盘时间，以网格边缘价格来下委托。这样做并不会引入未来信息。

### 3.2. 送转问题

另外一个问题是，上述示例代码中，无法正确处理发生送转的情况。因为我们在两处都记录了持仓，一个是交易代理记录的持仓，这个持仓里包含了送转记录；另一处是`self.records`，送转的股份数是不会进入到这个记录中的，因此也就没有卖出的机会。

 我们使用`self.records`来记录每个网格的持仓记录，纯粹是为了演示目的。实际上，我们可以不使用这个记录，而是在出现卖出交易信号时，从当前持仓中取出成本价，计算成本价对应的网格，如果存在盈利空间再卖出。这样做，如果发生了送转股，持仓成本表面上降低，但现价因为除权的关系，对应的网格也会相应降低，所以结果也是正确的。

 当然，这里也会有一个小问题，就是如果除权后，现价超出了网格的下界，这时候需要重新整网格。

### 3.3. 交易单位

提高资金利用率是优化网格交易策略的要点之一，而资金利用率又是网格数与单笔买入单位的函数。

在策略初始化时，我们传入了`unit`这个参数。这是为了简化策略的逻辑，便于理解。但实际上，它应该是本金与网格数之间的一个因变量。也即：

$$
    unit = principal / grids
$$
这样做的好处是，如果我们的网格数是动态计算的，这样单笔买入资金也会随网格数的减少而增加，随网格数的增加而减少，从而实现更高的资金利用率。

在上述策略中，我们把买入单位解释为买入和卖出的金额，而不是成交量（股数）。这样做会产生一个类似马丁格尔的优化，即在下跌过程中，我们买入的股数越来越多；而在上涨过程中，我们只需要卖出其中一部分，就可以收回成本，剩下的那一部分仓位将成为绝对收益。

我们以交易单位 1 万元为例。假设股价从 5 元跌到 4 元时，我们买入 2500 股；当股价涨回 5 元时，我们只需要卖出 2000 股即可收回成本。这样多出来的 500 股成为我们的绝对收益，它会在进一步上涨时被抛出。即使下跌也无所谓被套，因为这 500 股已经是零成本了。

## 4. 策略优化: 从1.37%到79.8%!

一般来说，vanilla 版本的网格没有趋势判断和跟踪的能力，如果价格不在由中枢、网格数和间距决定的网格区间波动，则一定会出现交易次数不足，利润率不高的情况。所以，增强网格交易盈利能力的关键，在于寻找到一个标的，它的价格波动正好在中枢、网格数和间距决定的网格区间内震荡。

比如，上次测试中，我们选择了一支大幅下跌的个股，这样在长达两年时间里，总共只实现了 12 次交易。原因是，我们选择的网格只覆盖了价格波动区间中的一小部分，请看下图：

![75%](https://images.jieyu.ai/images/2023/06/grid_coverage.png)

该图中，黄色和蓝色的线分别为该标的的最高价和最低价，它们共同构成了股价的波动范围。而彩色线则是我们的网格。

现在问题很明确，就是要让网格尽可能地覆盖到股价波动区间，除非股价的波动完全是单边走势。

那么，我们应该如何下手？

### 4.1. 选择有“界”的标的

有一些标的，天然具有上下界。比如，网格交易最初诞生于外汇交易市场，这是因为汇率关系到国家的经济，除非一国的经济发生重大变化，否则，不太容易出现单边大行情，多数情况下是进行震荡。

在 A 股市场上，也存在一些类似有“界”的标的。

#### 4.1.1. 大市值股票

我们在小市值策略那节课中讲过，大市值股票对应的公司，它们的成长性比较弱，以十年为期，不太可能出现市值涨几倍的情况（相反，小市值公司则是可能的）。另一方面，由于它们往往具有垄断地位、品牌优势，除非是像科技这样容易出现颠覆性创新的行业，一般也不会衰退。

工商银行就是这些股票中的典型代表。现在，我们就以工商银行为例，在同样的时间，使用同样的参数（开盘价、10 个网格，1%间距）来测试一下。

结果有点出人意料，只有 17 次交易。原因还是网格覆盖率不够，另外，在测试区间里，股价的波动也确实很小，只有 4%左右。如果我们把间距调整到 1.5%，则交易次数上升到 44 次，年化超额上升到 4%。

大市值公司确实满足了有“界”的条件，但是我们得找波动适当大一点的标的，此外，由于每次交易的差价很小，我们也必须考虑交易手续费的问题。当然，对大市值公司，我们也没必要对绝对收益有过高的期望。毕竟，小的收益对应小的风险。

#### 4.1.2. 超跌股票

从行为金融学的角度来看，超跌的股票很容易出现人气不足的情况。这时候散户往往已经躺平，因此没有太大的抛压，而上攻也无人跟随，所以比较容易出现震荡行情。

比如，某支股票 2013 年最高价为 43.44（不复权，下同），随后一路下跌，2018 年跌到 5 元附近，随后一直长期横盘震荡。我们以这支股票为例，以同样的参数（开盘价，10 个网格，1%间距）回测一下看看效果。

![50%](https://images.jieyu.ai/images/2023/06/grid_opn_up_001.png)

这次我们得到了比较理想的结果。年化回报 13.2%，超额 6%左右。

注意超跌股票的底，是市场选择的结果，并非刚性的，所以对底的确定，有一定的技巧性。在实操上，可以选择超跌后、已经横盘整理至少半年以上的股票。

#### 4.1.3. 可转债

可转债本质上是一张债券，发行时，每张面值为 100 元。作为债券，它只能到期兑付，所以一些需要短期资金的投资者可能将其拿出来交易，从而导致其价格发生波动，也可能跌破 100 元，但只要持有不动，那么最终会由公司按面值兑付，并支付利息，这样就形成了它的保底属性。

另外一方面，由于它随时可以转换成股票，所以又有了期权的性质，因此它的价格会随正股上涨而上涨。这样就形成了上不封顶、下有保底的属性。

有了这个保底属性，我们就可以将 100 元上方的某个位置作为中枢，确定网格来进行交易。

当然，持有可转债需要考虑债务违约的情况。在今年（2023 年）就发生了搜特转债因正股退市而违约的情况。不过，这种情形比较罕见，一般来说，只要不持有 ST 类个股的转债，加上分散持仓，安全性上是有保证的。

### 4.2. 基于历史数据，确定网格参数

在前面的示例中，我们看到了网格对股价波动区间的覆盖率，是决定收益的重要因素。在上一节的讨论中，我们介绍了如何选择有“界”的标的，来尽量避免单边走势，并且看到，一旦选择了正确的标的，其收益是相当不错的。另一方面，即使选择的标的呈现出震荡走势，如何选择中枢、网格数和间距，仍然需要讨论。

我们仍以前面那个两年实现了 25.7%的利润率的个股为例。先假设我们能开上帝视角，我们取回测区间内标的收盘价，查看它的直方图和分位数：

![50%](https://images.jieyu.ai/images/2023/06/grid_hist.png)

![](https://images.jieyu.ai/images/2023/06/grid_hist_table.png)

它的均价是 4.28，在 3.75~4.75 之间分布最多，这个区间幅度正好是 20%左右，所以我们划分 10 个网格，每个网格 2%。以这个参数进行回测：

![50%](https://images.jieyu.ai/images/2023/06/grid_best.png)

结果相当理想。最大回撤远低于参照，年化回报接近 30%，年化超额 21%，利润率也有 57.44%。

现在的问题是，这个结果利用了未来信息，有后见之明，在实盘中不可能这么做。如果关掉上帝视角，我们要如何重现这个结果？

在之前的方案中，我们使用的是回测时的开盘价，或者上一期的收盘价，这两个其实没有差别。在刚刚的版本中，我们使用的是均价，但包含了未来数据。

如果我们使用过去`n`个周期（比如说 20 天）的均价来作为中枢价呢？显然要比上一期开盘价好，因为股价围绕均价波动的可能性还是比较大的。

另一方面，如何确定网格数和间隔？

在我们的策略当中，交易次数是一个比较重要的指标。对网格交易来说，几乎每一笔交易都应该是盈利的，所以，交易次数越多，盈利就越多。

交易次数受哪些因素影响？直觉上，如果我们能每天交易一次是最理想的。要满足每天交易一次，那么间距就要约等于日内的波动。这让我们想起来 ATR 这个指标。

我们取回测开始前的 20 天的行情数据，得到 ATR 为 0.12，对应幅度 2.8%。这次，我们分别以 ATR 的 0.6, 0.8, 1 和 1.2 倍系数各运行一次，来看看结果如何。

![50%](https://images.jieyu.ai/images/2023/06/grid-atr.png)

结果表明，年化收益相差不大，但最大回撤表现不同。此外，使用小的间距，交易次数更频繁；使用大的间距，交易次数变少，但可能单笔利润增加，所以总得来看收益差不太多。

最后在 3.4%的间隔上，年化收益反倒最高，这可能与该标的长期趋势向上（年化 8.16%）有关。

现在，如果**间距由 ATR 来决定**，那么**网格数**应该怎么定？我们可以使用**最近 20 天的振幅，除以 ATR 来定**。不过，我们需要将这个值舍入到最近的一个偶数上，因为我们的网格划分应该是中心对称的。

这样我们得到的网格数应该为 6，再进行一次测试，各项指标有了新的提升：

![50%](https://images.jieyu.ai/images/2023/06/grid-atr-auto-grids.png)

现在，**年化达到了 33.2%，年化超额达到 25%**。更重要的是，这一次，我们没有使用任何超参数，中枢价、间距和网格数都是通过回测前 20 天的行情数据计算出来的。**我们只使用了过去的数据**。

### 4.3. 提高资金利用率

上述结果仍然不是最优。测试表明，资金利用率只有 10%左右。也就是，上述收益是仅仅使用了 10%的资金就获得的。但是，这并不意味着我们可以仅凭 10 万左右的资金就可以获得接近 70 万的收益。我们必须真金白银地拿出 100 万放在账上，以备不时之需。

在我们之前划分 10 个网格的方案中，每一个网格我们只使用了 10%的仓位。这样从中枢价格开始，一直到走完所有下跌网格，我们也只占用了 50%的仓位。因此，如果每一个网格我们加倍使用仓位，收益似乎应该加倍。

![50%](https://images.jieyu.ai/images/2023/06/grid_full_positions.png)

回测结果表明，收益在进一步提升，不过没有实现收益加倍。我们注意到交易次数减少了。这说明出现了满仓后无法买入的情况。不过，既然我们已经有了年化 28%左右的超额，两年时间实现了 79.8%的绝对收益，似乎也没什么不满。

当然，我们也可以参考香农的做法，在中枢价格位置处，一次性地买入 50%的仓位。这种做法的风险会更大一些，但如果买入后首先向上波动，那么收益也会更可观。
## 5. 趋势跟踪网格

上述收益是标的在回测区间，一直处于震荡时获得的。我们当然不会期望股价单边下跌时，会有任何一种策略帮助我们仍然能获得较好的绝对收益--对这种情况，惟一该做的，就是远离这种走势的个股。但是，如果个股处于震荡盘升趋势，我们也希望仍然能够使用上网格交易。

不是没有这种可能。

在上述策略中，中枢价格、间距和网格数是回测一开始就确定下来的，无论后面行情如何演绎，它们都不会与时俱进。

为了做到这一点，我们应该每隔一定时间，就重新计算一次上述参数，这样即使股价是震荡盘升的，我们的网格交易也一样能够使用。

下面的代码显示了动态网格会随价格上升自动调整，从而保证能在一定程度继续覆盖：
```python
import plotly.express as px

code = "603019.XSHG"

fixed = await Stock.get_bars_in_range(code, FrameType.DAY, datetime.date(2023, 1, 4), datetime.date(2023, 6, 8))
len(fixed)

async def draw_grids(end):
    bars = await Stock.get_bars(code, 20, FrameType.DAY, end = end)
    pp = np.mean(bars["close"])
    low = pp * 0.9
    high = pp * 1.1

    bins = array_math_round(np.linspace(low, high, 9), 2)
    
    grids = {str(i): [bins[i]] * 103 for i in range(9)}
    grids.update({"close": fixed["close"]})
    df = pd.DataFrame(grids)
    df.index = [str(tf.date2int(f.item())) for f in fixed["frame"]]

    px.line(df, title=str(end)).show()
    
for i in (0, 20, 40, 60, 80):
    end = tf.day_shift(datetime.date(2023, 1, 4), i)
    await draw_grids(end)
```

运行结果如下：

![75%](https://images.jieyu.ai/images/2023/06/dyna-grids.png)

可以看到，网格随着股价上升也在自动上升，并且保持了较好的覆盖。
