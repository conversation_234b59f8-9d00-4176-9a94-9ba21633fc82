---
title: 回测陷阱
output: pdf_document
mainfont: WenQuanYi Micro Hei
---


数学家、统计学家沃德在美国The Statistical Research Group (SRG)工作期间，他们小组接到这样一个任务：二战期间,有大量的美国飞机被摧毁（事后统计是9500左右辆），美国军方对返航的飞机进行了研究，得到了飞机被击中的着弹点。当美国飞机从欧洲上空的战斗中返回时，它们布满了弹孔。但损坏并没有均匀分布在飞机上。机身上的弹孔较多，但发动机上的弹孔不多。军方需要数学家进一步研究，如何既加固这些着弹点，又使用尽可能少的加固材料 -- 这对飞机的机动性和续航能力至关重要 -- 军方需要数学家找到这个平衡点。

![50%](https://images.jieyu.ai/images/2023/08/lesson22-wald-plane.png)

但是沃德几乎没有沿着军方的要求进行任何研究，他指出：

!!! quote
    The armor, said <PERSON><PERSON>, doesn’t go where the bullet holes are. It goes where the bullet holes aren’t: on the engines. <br><br>先生们，你们需要在没有弹孔的地方用上更多的装甲板，因为那里才是没有返航的飞机被击中的地方。

这个故事讲的是幸存者偏差，也是回测陷阱这一章要讨论的内容之一。但这个故事透露出的寓意，却具有超越回测、甚至超越量化本身的意义：最重要的也许不是数学，而是我们的常识与洞察力。

在前面的章节中，我们介绍了足够多的数学理论、量化库和编程技巧，这一切都运行在理想国，一个高度简化的童话世界。这一章将会有所不同，我们需要回归到经验和常识，回归到数据处理中那些脏活的细节上，回归到现实世界中的各种折衷方案上来，再来看看在理想状态下运行良好的策略，投射到现实世界中时，究竟会撞上什么样的南墙。

而当你身陷回测的迷雾，找不到方向时，请记住：

最重要的也许不是数学，而是我们的常识与洞察力。
## 1. 幸存者偏差

现在，拜各种知识付费项目所赐，几乎没有人不知道什么是幸存者偏差，量化从业者也不例外。困难在于，如何知道你正在处理的数据，正在使用的系统，它们是否已经引入了幸存者偏差。

比如，如果你正在做一个课题，要比较自己买股票与通过基金进行投资，究竟哪一种选择会更好。你可以做出一个非常漂亮的数学模型，用上最前沿的人工智能技术。但你可能不知道，你使用的数据源，他们没有统计过去几十年来已经消失的那些基金产品 -- 他们并不象股票系统那么严格，即使某支股票已经退市，它的历史交易数据、以及退市时间这件事，还可以比较容易地查到。

有一些幸存者偏差是我们已经了解的，比如股票退市引起的幸存者偏差。获得证券列表、并且是获得历史上每天的、正确的证券列表信息，是我们在讲证券数据源那一部分时，重点强调的三组API之一。要避免因退市引起的幸存者偏差，意味着你不能依靠最新的证券列表，并以此为基础进行遍历来搜索交易信号 -- 你必须使用交易日当天的最新的证券列表。

!!! attention
    有人总结出大牛股的五大特征，一是上市时间足够长，二是高增长+股本扩张，三是独有的竞争力和资源优势，四是主营契合经济发展热点，五是消费升级换代是永远的主题。实际上，这些特征要么倒因为果，要么就是某种幸存者偏差。也有人认为，巴菲特就是幸存者偏差的一个典型例子。你觉得这个观点对吗？<br><br>
    不过，不管其余四个特征如何，上市时间足够长这一特征 是显然易见的幸存者偏差。

另一种不太引入注意的幸存者偏差，或者称为注意力偏差也未尝不可。在我们研究策略时，首先是来自于某些线索，这可能是来自于同行交流，也可能来自于我们对市场的观察 -- 这时候我们往往对当下市场上最活跃的标的给予格外的关注，并着手从各个维度想办法来拟合它的走势，试图发现进场信号。当我们这样做的时候，我们实际上只是在飞驰的列车上统计乘客：似乎每个人都上车了；或者仅仅根据某个明星令人咋舌的财富而预言娱乐行业会比较其它行业更容易成功，而忽略了大批无法出人头地的无名之辈的绝望。

当这种偏差发生在量化领域时，似乎没有太多的危害 -- 最终你会放弃该策略的研发，从而避免金钱的损失 --浪费时间几乎是唯一的危害。但如果我们理解策略失败的根本原因，是从一开始就找错了线索的话，无疑我们将有更多的时间去做正确的事。

从理论上讲，如果我们能够对数据的来源、它的搜集、加工和预处理的过程都了如指掌的话，我们就能很容易知道，这里面是否存在幸存者偏差。所以，如果有可能，当我们拿到一个数据集，一定要去研究它的文档，了解它的信息来源、搜集整理方法，又进行了哪些加工、异常数据处理。但是，数据可能会被修改、修饰、消失以及相互矛盾，这也是常常会发生的事。

## 2. Look-ahead bias

在回测中，我们可能使用未来数据，这样产生的偏差被称为Look-ahead bias（前视偏差）。我们都理解不能在回测中使用未来函数/数据，但什么情况下会使用到未来函数，却不容易判断。

### 2.1. 引用错误
有一些回测框架在设计时，会尽力防止研究人员使用未来数据。比如，backtrader设计了Line这种数据结构，以保证它步进提取数据、在多个对象（feed, strategy, indicator）之间做到同步。甚至，backtrader还在比较让人头疼的多周期数据同步上，保证了你在一个小周期步进期间，不会引用到未结束的大周期的数据。

!!! note
    关于多周期，我们在backtrader一章特别进行了演示，以证明在小周期的时钟前进时，我们可以存取大周期上的数据，backtrader会保证我们只取到已经结束的周期的数据。在该示例中，我们使用的是主周期（小周期）是日线，大周期是周线。

但是，我们也提到至少一种情况下，我们仍然可能使用未来数据：在Line派生的对象中，比如Strategy中，我们可以通过self.data.close[0]来读取最新的收盘价，-1则代表之前发生过的一期收盘价。也就是说，正常情况下，Line对象的下标应该是小于等于0的整数。但backtrader并未阻止我们使用self.data.close[1]这样方法来获取未来数据 -- 甚至它都不愿意对此发出一个警告！这种机制上的漏洞终究会有一天带来问题。比如，有可能我们本来是希望使用self.data.close[-1]的，但误输入成为self.data.close[1]。这样的bug一般会使得策略的各项指标变得好看，因此除非指标过于好看，或者是在实盘中碰得头破血流，我们都很难有机会发现它。

!!! attention
    根据卡内基.梅隆CyLab的一项研究，商业软件一般有每千行20~30个bug。另有机构研究认为，开发人员把75%的时间用在查错上。多数策略研究员接受的软件工程训练少于他们在数学、统计学、机器学习等方面接受的训练，缺少关于代码质量把控和单元测试的技巧，如果他们把策略回测看成软件测试，也不会让人感到奇怪。

但这远非事情的全部。我们必须特别小心地对待策略初始化，以及像撮合这种不容易看到的地方。

比如，在next等方法中，只要我们足够小心，我们就无法访问未来数据，但是，在策略初始化部分，backtrader允许我们访问全部数据：

```python
# 示例1
import backtrader as bt
import backtrader.feeds as btfeeds
from coursea import *
await init()

async def get_sample_feed(
    code: str, n: int = 250, ft: FrameType = FrameType.DAY, end: Frame = None
):
    bars = await Stock.get_bars(code, n, ft, end=end)
    df = pd.DataFrame(bars)
    return btfeeds.PandasData(dataname=df, datetime="frame")


class Day1Strategy(bt.Strategy):
    def __init__(self):
        self.max = round(max(self.data.close), 2)
        
    def next(self):
        print(len(self), self.max, round(self.data.close[0], 2))

        
cerebro = bt.Cerebro()

end = datetime.date(2023, 8, 29)
data = await get_sample_feed("000001.XSHE", 5, end=end)
cerebro.adddata(data)

cerebro.addstrategy(Day1Strategy)

results = cerebro.run()
```

这段代码输出如下：

```
1 11.52 11.25
2 11.52 11.13
3 11.52 11.23
4 11.52 11.52
5 11.52 11.31
```
显然，最大值出现在第4个周期上，但我们从第一个周期就能访问它 -- 这里出现了未来数据。

这个未来数据是由聚合统计函数max引起的。它将统计整个data feed流的最大值。聚合统计函数是未来函数的主要贡献者：只要你传入的参数带有未来数据，这类函数几乎将保证使用未来数据。聚合统计函数一般是指：

* mean/average
* min
* sum
* median
* rank
* count
  
!!! attention
    调用聚合统计函数并不必然导致未来函数。只有当你在未来数据上调用它们时，这些函数才会成为未来函数。任何时候，我们都可以对过去的数据安全地调用这些函数。

除了聚合统计函数，一些算法也会引起未来函数。在第13章，我们介绍技术形态分析时，介绍了一个名为zigzag的库，用来寻找过去出现的k线上形态上的波峰与波谷 -- 这是突破、支撑和压力线、波浪技术分析的基础。但是，如果我们象示例1一样，在backtrader的初始化部分去计算这些波峰与波谷的话，我们也是在使用未来函数。

```python
# 示例2
from zigzag import peak_valley_pivots

def peaks_and_valleys(ts, up_thres, down_thres):
    ma = moving_average(ts, 5, padding=False).astype(np.float64)
    
    pvs = peak_valley_pivots(ma, up_thres, down_thres)

    plt.plot(np.arange(len(ma)), ts[4:], color='#49DBF5', label='ts', alpha=0.2)
    plt.plot(np.arange(len(ma)), ma, color='#D77AF5', label='ma')

    # Plot peaks.
    peak_x = np.argwhere(pvs > 0).flatten()
    peak_y = ma[peak_x]
    plt.plot(peak_x, peak_y, 'v', label="Peaks", color='#8D50A1')
    for i in range(len(peak_x)):
        plt.text(peak_x[i], peak_y[i]*1.005, f"{i}")

    # Plot valleys.
    valley_x = np.argwhere(pvs < 0).flatten()
    valley_y = ma[valley_x]
    plt.plot(valley_x, valley_y, '^', label="Valleys", color="#36A2B5")
    
    plt.gca().spines.top.set_visible(False)
    plt.gca().spines.right.set_visible(False)
    
end = datetime.date(2023, 4, 25)
bars = await Stock.get_bars("000001.XSHG", 120, FrameType.DAY, end=end)

peaks_and_valleys(bars["close"], 0.015, -0.015)
```

这段代码将输出以下图形：

![](https://images.jieyu.ai/images/2023/08/lesson22-zigzag.png)

让我们把注意力放在序号为2的波峰上。这个波峰对应的时间是2023年4月20，但只有到4月25日，我们才能检测到它。如果我们在初始化函数中，预先生成了这些波峰波谷的话，我们就能在next函数中，在4月20日对应的那个周期，得到当天是波峰的提示，这就形成了未来函数。

!!! tip
    除了zigzag，所有类似检测波峰波谷的方法都不可避免地引入类似的未来函数。这是它们的检测原理导致的。波峰波谷检测可能引起的另一类错误是偷价。即在4月25日检测到4月20日出现一个波峰之后，试图在4月20日以当天的价格卖出。听上去这令人难以置信，但是这种情况确实经常出现。代码的复杂性，会导致一些粗心的人更容易相信对自己有利的结果，而忽略那些需要足够小心仔细才能发现的bug。<br><br>
    利用数字信号处理技术来分析价格波动和周期也是证券时序分析的一个分支。如果你利用了傅立叶变换、小波变换或卡尔曼滤波，也一定要小心，它们可能引入未来函数。

### 2.2. 偷价
偷价行为是指利用过去的价格去交易。这样的场景下可能出现偷价：

1. 如果最高价大于某个固定价位即以开盘价买入。在实盘中，当最高价高于某个价位的条件得到满足时，价格已经高于开盘价了，不可能再返回到过去买入。不过在回测中，如果使用的框架是backtrader中，这样的偷价行为不容易发生（除非使用self.data.high[1])，因为在backtrader中，订单的执行都发生在下一个bar中；cheat-on-close也只能用来以当前bar的收盘价买入。在其它框架下，需要了解框架实现原理，再行判断。
2. 对k线进行峰谷检测，再以峰、谷价卖出（买入）。这也是偷价。如果回测框架是backtrader，这样的偷价不容易发生。但其它框架下，需要了解框架实现原理，再行判断。

### 2.3. 复权引起的前视偏差

Look-ahead bias的另一个常见例子是使用了前复权数据。我们在第一章中讲过，前复权和后复权本身存在线性变换关系，所以，如果使用前复权数据会带来未来信息，那么使用后复权数据也同样是。但这个结论，只有在我们把这些数据当成整体来使用时才正确。

在backtrader中，假设我们用2020年到2022年12月31日期间的数据进行回测，并且分别执行前复权和后复权。如果我们站在2022年12月31日以后来看这两个数组，它们包含了一模一样的信息。但是如果在2020年6月1日来看这两个数组，显然要得到这一天及之前的数据，显然利用了6月1日之后的复权因子。而对2020年6月1日及之前的价格进行后复权计算时，我们只利用了2020年6月1日之前那一部分复权因子。

我们可以通过下面的代码及其绘图来验证上述结论：

```python
# 示例3
import numpy as np
import matplotlib.pyplot as plt

np.random.seed(78)
factors = np.random.choice((1, 1.1, 1.05, 1.02), size=20, p = (0.8, 0.05, 0.1, 0.05))
factors = np.cumprod(factors)

close = np.ones(20) * 10
pc = close[:6]
pf = factors[:6]

forward_adjust = close * factors/factors[-1]
partial_forward_adjust = pc * pf/pf[-1]

backward_adjust = close * factors/factors[0]
partial_backward_adjust = pc * pf/pf[0]

p1, = plt.plot(close, label="close")

p2, = plt.plot(forward_adjust, "r", label="forward")
p3, = plt.plot(partial_forward_adjust, "r.", label="partial forward")

p4, = plt.plot(backward_adjust, label="backward")
p5, = plt.plot(partial_backward_adjust, "g+", label="partial backward")

ax1 = plt.gca()
ax2 = ax1.twinx()
p6, = ax2.plot(factors, label="factors", color='b')

plt.legend(handles=(p1,p2,p3,p4,p5,p6))
```

![50%](https://images.jieyu.ai/images/2023/08/lesson22-adjust-problem-1.png)

后复权处理时，没有利用未来数据。假设当时时间点为j，观察点为i，i < j，即i是j之前的某一天。无论我们是在j之后，一次性对全部数据进行后复权处理，然后取到i这一天的数据（图中黄线），还是在i这一天，就已知的数据pc (即close[:6]), pf(即factors[:6])进行后复权处理（图中绿色+号），最终结果都一样。

但前复权处理则不一样。从图中可以看出，如果我们在j这一天对全部数据进行前复权，绘制出红色线（label为forward），再用到i这一天，就已知数据(即close[:6]), pf(即factors[:6])进行前复权处理（图中红色圆点），两者的图形并不会象后复权那样发生重合。它们是相似图形，但发生了平移--意味着价格绝对值发生了变化。如果我们的策略完全只利用趋势，这可能不会有问题，但我们能保证不使用价格本身吗？

!!! note
    我们已经列举了一些可能导致使用未来函数/数据的例子。在回测中出现这样的问题，根本原因可能是所谓的基于向量化回测的加速机制引起的。backtrader和其它一些回测框架允许我们在初始化时计算各种技术指标，这里引入了向量（或者numpy的广播机制），计算速度很快，但它也往往是使用未来数据的根源。<br><br>有一些策略研究员在没有回测框架支持的情况下，使用pandas来计算和保存交易信号，然后再根据这些交易信号，自己进行简单的撮合。如果我们需要这样做，一定要注意这很容易引起未来函数/数据的使用。<br><br>在next函数中，一个bar一个bar地向前推进，每次只能取得该周期及之前的数据，这种方式被称为event-driven的回测方式，它的速度慢，但可以更容易地避免使用未来数据/函数。

### 2.4. PIT数据

有一些经济数据，在发布之后，往往还会进行修正。比如宏观数据中的GDP，公司财务报表等等。我们以公司财报为例，如果它在2021年1季末发布了财报1.0，在半年报发布时，对1季度的财报进行了修正，此时1季度的财报的版本应该是2.0。如果数据源在发布时，记录了数据版本和它生成的时间（而不仅仅是归属时间），这种数据就称为Point-in-Time data (PIT)。

!!! note
    以2019年一季度美国的宏观经济数据发布为例。GDP预估值发布于2019年4月25日；5月30日进行第一次修正；最终修正则是在当年的6月27日。<br><br>
    举个真实的例子。某海产品养殖公司，2014，2015年连续两年亏损被ST，如果2016年再亏损，按当时规则将进入退市流程。于是，2017年3月，该公司公布了2016年年报，净利润7571万，扭亏为盈，成功保壳。2019年，最终查实他们虚增利润1.3亿，追溯修订2016年财报为亏损-5543万。<br><br>
    如果我们现在来进行回测，当运行到2017年3月，财报可获取时，我们取得的数据将是-5543万，避开这个雷；但在2017年5月，实盘中获取到的该公司上年净利润为7571万。

如果数据源不提供PIT数据，我们在2023年对该公司的股票进行回测时，当我们回测到2021年1季度时，我们看到的数据是1.0的，还是三个月之后发布的2.0的？如果我们看到的数据是三个月之后发布的2.0的数据，这样就引入了未来数据。

另一个与此关联的数据偏差是，即使基本面数据没有进行修正，它们的发布时间也往往晚于归属时间。比如，以上述美国宏观数据为例，它的一季度数据最早要在2019年4月25日才能得到。但在回测时，我们可能在回测2019年1月左右时，就能拿到4月25日才发表的这项数据，这取决于数据服务商如何给该项数据索引。

在A股，上市公司的财报披露截止时间分别为当年的4月底、8月底、10月底和次年4月底。由于各家上市公司的披露日期不固定，一般我们在构建量化模型时，如果使用了基本面数据，我们需要从次月的第一个交易日起，才能放心地调用。

## 3. 复权引起的问题

本章第2节提到，在回测中使用前复权数据，很可能引起前视偏差。既然如此，如果我们使用不复权的数据，或者后复权的数据，是否就可以避免这些问题？

### 3.1. 使用复权数据的必要性
首先，我们在进行回测时，必须使用复权数据。我们以最简单的买入并持有（buy and hold）策略为例，看看使用不复权数据和复权数据都有多大的差异：

```python
# 示例4
import backtrader as bt
import backtrader.feeds as btfeeds
from coursea import *
await init()

code = "600750.XSHG"
end = datetime.date(2023, 8, 29)
bars = await Stock.get_bars(code, 500, FrameType.DAY, end=end)

class BuyAndHold(bt.Strategy):
    def notify_order(self, order):
        if order.status in (order.Margin, order.Completed):
            print(order)
            
    def start(self):
        size = self.broker.get_value() // self.data.open[1]
        self.buy(size = size)
        
    def stop(self):
        self.close()

        
cerebro = bt.Cerebro()

df = pd.DataFrame(bars)
data = btfeeds.PandasData(dataname=df, datetime="frame")
cerebro.adddata(data)

cerebro.addstrategy(BuyAndHold)

print(cerebro.broker.get_value())
results = cerebro.run(cheat_on_open = True)
print(cerebro.broker.get_value())
```

我们的buy and hold策略是这样实现的，首先我们是通过start和stop来完成开盘买入和回测结束时的平仓动作，而不是在next方法中。为了最大限度地利用资金，我们设置了cheat_on_open，以便能以开盘价全仓买入，并且在计算size时，我们通过self.data.open[1]来偷看了第二天的开盘价。

omicron默认返回前复权价格，这次运行我们将得到两个输出，从输出中我们可以计算出，这笔交易赚了8204元左右。现在，让我们以复权价格运行一次。获取不复权的价格，是通过下面的代码：

```python
bars2 = await Stock.get_bars(code, 500, FrameType.DAY, fq=False)
```

我们需要传入fq=False这个参数。用这句代码替换示例4中的第9行，再运行，结果显示这笔交易只赚了6124.7元。如果我们用前后两次最后的资产相除，会发现它们刚好等于复权因子首尾相除的结果 -- 0.8858。

这个结果表明，如果我们不使用复权数据进行回测，我们将遭受较大的权益损失 -- 更不要说还会导致许多技术指标无法计算。

### 3.2. 后复权的问题

但是，使用复权数据，带来的问题也不会少，比如，在backtrader框架中使用前复权，这将带来未来数据。如果使用后复权呢？遇到的问题并不比使用前复权少：

1. 最小交易单位问题。在A股，除了最后一次平仓之外，其它交易中，买入和卖出都必须以100股为最小单位。在使用后复权数据时，越靠近回测末端，价格越高（比如万科A在2018年后复权价格达到过5900元），这样受1手整数限制，会导致策略无法用完资金。一些研究员会使用无限大本金来缓解这个问题，但这又会引起成交量匹配问题。

!!! attention
    在使用后复权时，加大本金可以缓解资金利用率问题。比如，在万科A后复权价达到5900元时，购买一手万科也要59万元，如果本金只有10万，则资金利用率为0%；资金100万时，利用率59%；资金1000万时，利用率可达94.4%;资金1个亿时，利用率达99.7%。但是，过大的交易本金很可能会导致成交量匹配不足的问题，从而又降低了资金利用率。<br><br>backtrader本身没有强加最小购买单元100股的限制，它甚至允许成交量不足1股。但使用小数股成交也违反了A股交易规则，也会导致回测与实盘的差异。

2. 在配对交易中，后复权时的价格差可能导致无法像实盘那样触发信号。一些依靠盘口数据来产生信号的策略可能也会遇到同样的情况。
3. 后复权会使得一些信号消失，比如整数关口的压力位、支撑位等。
4. 后复权下，只能使用百分比滑点，而不适合固定滑点。

### 3.3. 复权相关的其它问题

复权的计算本身十分复杂，既涉及到拆股，也涉及到派息，还会遇到小数点引起的调整问题。为了简化这些计算，一些商用数据源采用复权因子来进行简化。这样的操作也带来另外一个问题：它丢失了部分信息，导致回测框架无法真实复权。

!!! note
    在A股我们只遇到了拆股。但在美股还存在反向拆股，即将若干股合并成一股。2023年8月，法拉迪未来就进行了这种操作，以避免因股价低于要求的最低价而被退市。

比如，某股连续两日收平，复权价格分别为[11,10]，复权因子分别为[1, 1.1]。假设股民在第一日持有100股，市值为1100元，第二天按后复权价格计算，市值为100 * 11，仍为1100元，权益不变。从复权因子我们看不出来究竟是发生了除权，还是除息。这两者实际上会引起权益的不一致：如果是派息引起的除权，那么账户的现金增加，持股数不变。如果持有人在一个月内、一年内卖出，将分别扣除红利税。在像backtrader这样的框架中，我们使用复权数据，实际上是把所有的分红都折算成了拆股，这样可能少扣除红利税，并且加大了账户波动。

使用复权因子复权的另一个问题，是在允许做空的情况下，分红的处理问题：比如，做多股票时，我们可以获得分红；如果做空股票，我们需要返还分红。使用复权因子时，由于丢失了分红信息，回测框架无法做到这一点。

此外，如果使用前复权，并且回测时间很长的话，还有可能遇到最早期的价格都低于0.01元，从而导致无法区分价格的问题。

复权相关的另一个问题是，股价除权除息所带来的价格调整是实时的。当价格调整之后，相应的，类似EPS（earnings per share)这样的数据也应该进行调整。但是，EPS数据和其它基本面数据，往往都是每个季度才公布一次，它们在时间点上不可能对齐。

!!! attention
    上述结论是否成立，取决于你的数据提供商。比如，仅以EPS为例，当股价除权除息发生之后，行情软件上显示的EPS会立即反应这一变化。但是，量化数据源一般分开提供行情数据和财务数据，财务数据一般都是按季度发布和编纂的。

为了避免这些复权问题，最好的处理方式是动态复权下的前复权，并且策略与撮合机制相分离。据作者所知，聚宽和大富翁回测框架使用了动态复权下的前复权，并且大富翁实现了策略与撮合机制分离。具体的细节我们在大富翁回测框架中进行介绍。

## 4. 交易规则
一些回测框架（即使是国内厂商的）不能完全遵守交易规则。与期货相比，A股的交易规则还相对简单一点，但关于佣金和印花税、分红计息的规定也时有变化。尚不清楚这些变化对回测的影响。但有一些交易规则非常重要，比如：
### 4.1. T+1交易
T+1交易制度是A股独有的制度，因此国外生产的回测框架，比如backtrader，一般都不会遵守这些规则限制。如果我们使用的是日线及以上级别的交易信号，可能问题不大。但如果我们要做日线以下级别的交易信号，则必须自行判断持仓是否可以卖出。

!!! attention
    止损止盈单在backtrader中，是在主订单完成之后的下一个bar触发，还是在当前bar就可能触发，backtrader没有文档说明处理的细节。如果读者有这方面的担心，请自行研究其源码。

### 4.2. 涨、跌限制
涨跌停限制也是A股独有的机制。一些回测框架不会限制在涨停板买入，也不会限制在跌停板上卖出。但涨停板上买入、跌停板上卖出会对策略业绩有比较显著的向上修正效果，是我们回测是必须设法避免的。

在backtrader中，要实现这一限制，必须同时引入三个数据流：未复权收盘信息（分钟级）、涨停限价、跌停限价。有一些框架只使用日线级别的数据流来实现涨跌停限制，这是错误的。除非一字涨停，框架必须在股价未涨停的期间，允许策略买入。关于涨跌停价格，必须从数据源获取，而不应该自行计算，因为在A股，一支股票的涨跌停限价，受它是否是ST影响，因此是无法计算的。另外，在除权前后的涨跌停限价也是无法仅从前一日收盘价来计算的。通过这三个数据流，在下单前，如果当前bar的最低价等于涨停限价，则不允许买入；当前价的最高价等于跌停价，则不允许卖出。

## 5. 过度拟合

只要系统设计得足够复杂、参数足够多，这个系统就一定能对过去发生的一切都自圆其说：却无法预测未来。下面这张来自 [AlgoTrading 101 Blog](https://algotrading101.com/learn/what-is-overfitting-in-trading/)的图片，形象地说明了这一点：

![50%](https://images.jieyu.ai/images/2023/08/lesson22-overfitting.png)

我们在第12章中，提到过在使用np.polyfit进行曲线拟合时，deg参数（多项式幂次）一般使用1~2次就够了，使用的次数越高，拟合残差就越小，但这样拟合出来的结果，完全无法预测未来的趋势：

```python
# 示例5
y = np.array([x**2 -16* x +3 for x in range(20)]) + np.random.normal(size=20) * 10
plt.scatter(np.arange(20), y)

x = np.arange(20)

for deg in (1, 2, 17):
    coeff = np.polyfit(x, y, deg=deg)
    p = np.poly1d(coeff)
    plt.plot(p(x))
```

![50%](https://images.jieyu.ai/images/2023/08/lesson22-overfit-polyfit.png)

当我们使用deg = 17时，几乎解释了过去的每一个数据，但是，我们无法理解这样的曲线，它的的未来会是如何；但当deg = 2时，它也较好地拟合了过去的数据，我们可以直观地得到一个印象，数据是跨过了最低点向上了！

在实际回测中，产生过拟合的主要原因可能来自于调优。当我们有了一个初始的模型之后，从收益图、买卖点上看到哪个地方不够优化，然后就去猜想如何纠正它，比如加入一个在该点适用的指标等等。或者让backtrader对已有的参数，在参数空间里进行搜索，这样很可能会得到一个理想的结果。

防止过拟合，并不是要完全取消参数搜索与优化。我们可以通过使用带外数据来防止过拟合。比如，如果我们要回测过去两年的数据，我们可以把最后一个季度视为带外数据，前7个季度作为训练数据。参数搜索和优化只在前7个季度上进行测试，这样得到的回测结果，再在最后一个季度上进行回测，如果指标与之前差异显著，则有可能出现了过拟合。

参数平原（parameter surface）是检查过拟合的另一个方案。将策略表现的指标（比如收益率或者最大回撤）画成参数的函数。对于一个好的策略，在微调参数取值时，它的表现应该比较稳定，参数平原应该十分光滑。比如，假设一个策略中使用了6日RSI，则当我们使用5日或者7日RSI指标时，策略的表现应该与6日差不太多。如果策略的参数平原非常不规则，则意味着参数背后没有合理的业务逻辑，而更多的是数据挖掘的结果，这时就要小心。

## 6. 回测时长

经济是有周期的，证券波动同样如此。如果我们的回测时间过短，刚好赶上策略的顺周期，就可能表现很好；但投入实盘后，很快进入逆周期，导致实盘失败。此外，回测时间足够长时，策略见到的意外情况就越多。因此，回测时间的长短，对客观评估策略也很重要。

![50%](https://images.jieyu.ai/images/2023/08/lesson22-period.png)

回测时间的长短取决你使用的数据，如果我们不采用自然时间，而是bar数来衡量回测的时长可能更便于相互比较。如果我们使用了基本面数据，它是按季度发布的，所以我们应该回测10年以上，至少要穿越一到两个经济周期。如果我们只使用分钟线数据，那么实际上1000个bar，或者2000个bar有可能就把所有的模式覆盖了。

## 7. 回测与实盘的差异
### 7.1. 信号闪烁

信号闪烁是指算法发出了不稳定的交易信号，这种情况多在实盘中出现，但在回测中一般则不会发生。如果是在T+0交易中，出现这种情况，程序会在极短的时间之内，反复多次进行开平仓操作。这是非常危险的，如果不立即进行应急处理，程序很可能一直进行这种不合理的操作，产生大量的交易手续费成本和滑点成本，造成交易事故。

信号闪烁主要由以下两个原因造成：第一，所使用的判断条件不稳定，即判断条件时而成立时而不成立。比如，如果我们的策略是收盘价上穿均线时立即买入，下穿时卖出，那么，这很可能引起信号闪烁，但在回测中是不会出现的。

```python
# 示例6
import numpy as np
import matplotlib.pyplot as plt
from omicron.talib import moving_average

np.random.seed(78)

returns = np.random.normal(size=26) /100
close = 10 * np.cumprod(1 + returns)


fig, axes = plt.subplots(nrows = 3, ncols = 3)

axes = axes.flatten()
live_prices = np.linspace(10, 11, 9)
np.random.shuffle(live_prices)

for i, c in enumerate(live_prices):
    close_ = np.concatenate((close, [c]))
    ma = moving_average(close_, 20)
    
    axes[i].plot(close_)
    axes[i].plot(ma)
    
_ = plt.suptitle("信号闪烁")
```
![](https://images.jieyu.ai/images/2023/08/lesson22-flashing-signals.png)

这段代码反映了以成交价上穿20日均线作为买入条件时，由于当天成交价格的波动，造成的信号反复触发的情况。在backtrader中，默认是当前bar计算信号，下一个bar进行撮合，所以在回测中，信号是稳定的。如果我们把这段代码也用来进行实盘，那么当天就会发出至少6次买入信号和卖出信号（如果允许T0交易），这是回测中没有覆盖到的情况。

解决这个问题的关键，是不要使用实时价格参与信号判断，而一定只使用已收盘的价格来参与信号判断。这个道理浅显易懂，但在实际工作中，则不一定能发现这个错误。

```python
# 示例7
from coursea import *
from omicron.strategy.base import BaseStrategy

class CrossStrategy(BaseStrategy):
    def predict(self, frame: Frame, frame_type: FrameType, i: int, **kwargs):
        bars = await Stock.get_bars(self.sec, 27, frame_type, end=frame)
        close = bars["close"]
        ma = moving_average(bars["close"], kwargs.get("win"))

        close = array_math_round(close, 2)
        ma = array_math_round(ma, 2)

        if close[-1] > ma[-1] and close[-2] < ma[-2]:
            self.buy()

    start = datetime.date(2023, 1, 4)
    end = datetime.date(2023, 4, 14)
    gs = GridStrategy(
        cfg.backtest.url,
        "002344.XSHE",
        4.63,
        100_000,
        interval = 0.02,
        start=start,
        end=end,
        frame_type=FrameType.DAY
    )

await gs.backtest(baseline = "002344.XSHE")
await gs.plot_metrics()
```

上述示意代码既可用以回测，也可用以实盘。在回测时，将会发出稳定的信号。但在实盘中，如果我们每分钟调用一次predict方法，并在盘后，用当天的数据进行回测，则可能发生实盘有成交，但回测没有的情况。也就是，实盘中至少有一次现价超过了均线，但最终回落，当天的收盘价未能突破均线。

这是因为，代码在回测中的正确性，是回测框架特殊的调用条件和撮合机制决定的。当这段代码转为实盘运行后，调用条件发生了变化，从而导致代码的运行情况与我们的预期就发生了很大的不一致。

第二，信号虽然确定，但在某些情况下，开仓和平仓条件同时得到了满足。此时，程序可能先执行开仓交易，又立即把刚开的仓位平掉。接下来推送过来一个新Tick，开仓和平仓条件又同时得到了满足，程序会再次开仓然后平仓。如此反复，直到价格变动到不再满足开仓平仓条件，才会停止下来。

A股目前还是实行的T+1交易制度，上述情况不太容易发生，但可能出现一天之内，止损和止盈同时出现的情况。这是一个回测中会遇到，而在实盘中不会出现的场景。比如，在一个大幅低开，随后上涨的场景下，在实盘中可能先触发止损，从而不再有止盈的机会；而在回测中，是先止损还是先止盈，如果没有框架的支持，则只取决于我们代码的顺序。

```python
# 示例8
class DummyStrategy(bt.Strategy):
    def next(self):
        if True:
            orders = self.buy_bracket(price=13.5, limitprice=14., stopprice=13.)
```

如果主订单买入成功，在接下来的bar(13.6, 14.5, 12.8, 13.5)到来时，止损单和止盈单都满足了触发条件，究竟谁先执行呢？实际上，backtrader无法判断。

!!! note
    backtrader是按订单顺序来执行的。当创建bracket订单时，主订单最先创建，其次是止损订单，然后是止盈订单。因此，当一个bar能同时满足止损、止盈条件时，backtrader会先进行止损。<br><br>
    但不是所有的回测框架都这么做 -- 也可能backtrader会在后面的实现中变更这一行为 -- trading view，另一个热门的回测平台，会做一个危险的假设，它认为下一个bar的最高价和最低价中，谁最接近上一个收盘价，则谁先到。然后它根据这个假设来执行止损止盈。

要解决这个问题，必须引入至少是细至分钟线粒度，才有可能一定程度上，决定真实止损、止盈的顺序。如果我们使用backtrader，那么，你必须添加一个分钟线粒度的数据供buy/sell/buy_bracket/sell_bracket方法来执行：

```python
def buy(self, data=None,
        size=None, price=None, plimit=None,
        exectype=None, valid=None, tradeid=0, oco=None,
        trailamount=None, trailpercent=None,
        parent=None, transmit=True,
        **kwargs):
```
在Strategy.next方法中，我们通过self.datas[x].close来访问收盘价，这可能是日线级别的数据，但在下订单时，backtrader允许我们传入一个其它的数据源：这是通过data参数来指定的。要解决我们这里讨论的问题，你至少得传入分钟级粒度的k线数据，这样backtrader才有可能按照接近正确的顺序来执行订单。

### 7.2. 冲击成本
回测使用的是静态数据。比如，假设我们用backtrader回测并使用了1分钟数据。我们下了一个市价单，如果不考虑成交量匹配，这个市价单几乎一定是在下一分钟内完成匹配。但在实盘中，我们获取数据会有延迟，计算信号会有延迟，下单传输到券商排队也会有延时，这样有可能匹配用的数据，在事后看来是属于T+2的，这样导致价格偏离。为了减轻这一类影响，我们一般需要增加滑点。
### 7.3. 不可能成交的价格
在套利策略中，回测总是能同时在两个方向上都成交；但在实盘中，会存在很多价差抢不到的情况，或者只抢到了不利于自己的方向先成交，再补另一个方向时，滑点可能比预期的要大。

价格真空情况。如果在日线级别上进行回测撮合时，如果我们下限价买入单，即使该限价单在下一个bar中的最低价能够打到，但实际上在最低价上成交量严重不足。此时即使回测框架执行了订单，在实盘中也是无效的。即使是在分钟级别上进行回测撮合，也不能完全避免上述情况（特别是个别乌龙指发生时，会单笔打出一个最高价或者最低价），但要可靠得多。

### 7.4. 撮合问题
回测框架必须以有限的知识，来决定某个订单是否能成交。它们一般主要按价格进行匹配，有的会辅以成交量。比如，某一天某支股票共成交1千万，那么就只允许策略成交最多1千万。

我们在backtrader那一章中，讨论过这个方法有多简陋。特别是，如果你只使用日线数据来进行回测的话，这1000万成交量是分布在最高价与最低价之间的，而不是分布在你给定的价格与最高价（委卖）、最低价（委买）之间，并且，只使用日线的话，我们不可能知道这个分布。

使用tick级和分钟级的数据来进行回测会精准很多。但是，撮合本质上是看order book的。也许在卖一的位置上有天量的卖单，你的策略也发出了买入信号，但当天市场成交量很小，如果回测中的撮合机制使用了成交量限制的话，就会导致你的策略并不能在你想要的时间和价格上拿到足够多的筹码 -- 但在实盘中，发出的委买很可能都是可以成交的。

## 8. 大富翁回测框架
在策略与回测上，大富翁采用了与众不同的设计。它的设计特点是：

1. 基于前复权的动态复权机制
2. 回测驱动与撮合分开
3. 不过度设计，学习曲线简单，可拓展性强
4. 已为分布式回测做好准备
5. 回测转换实盘无须变更API
6. 自动实现的T+1和涨跌停限制
7. 更精准的成交量匹配（需要分钟线支持）

### 8.1. 回测功能简介
我们在第6~8章分阶段讨论了策略与回测框架的编写，并分阶段引入了大富翁回测框架。在学习了backtrader之后，我们通过与backtrader进行对照，对这个框架的组成和特点再进行一些说明。

#### 8.1.1. 架构和风格
backtrader是一个单体式的架构，它支持多进程，每个进程都运行同样的代码，并且运行在同一台机器上。它在设计风格上，功能大而全，包括了对数据的格式封装、策略框架、指标计算和绘图等。

大富翁是一个分布式架构（但还未完全实现分布式回测），它的回测驱动与撮合服务是独立进程，可以部署在不同的机器上。大富翁在设计风格上，除非必要，不重新发明轮子。比如指标计上我们使用了empyrical，也提供了assets数据，用户可以通过quantstats来绘图。但是，大富翁也提供了自己的策略收益报告，该报告是基于交互式的。

大富翁是通过omicron和backtesting server来共同实现策略编写与回测的，安装好omicron之后，就有了策略基类、绘图函数、交易客户端等功能。backtesting server是一个独立的发行包，需要单独安装和管理。

两者架构的最大区别是，大富翁的回测是一个服务器，它可以有自己的数据和运行方式，与策略完全不相关，类似于一个真正的交易代理。策略可以运行在不同的周期上（比如日线），但回测服务器可以运行在更细粒度的周期上（比如tick或者分钟），这样就解决了上一章中讲到的许多问题（成交量匹配，T+1,涨跌停限制、复权分红和止盈止损顺序）。

!!! attention
    当前回测服务器的最新版本是0.4.19，尚未实现复权分红处理（需要额外的数据支持）和自动止盈、止损。

### 8.2. 策略框架
要实现一个策略，在backtrader中，需要继承自backtrader.Strategy类，并实现初始化和next方法。此外，backtrader的策略基类还提供了生命期管理（比如有stop, start函数可以重载）订单函数和事件通知。

在大富翁中要实现一个策略，也需要继承自Strategy基类（但完全跳过这一部分，从traderclient开始，编码量也并不大），实现初始化方法和predict方法（类似于backtrader中的next)。

大富翁的订单函数没有backtrader丰富，主要是缺少target_order_*函数，以及buy_bracket, sell_bracket函数。*_bracket函数实现的功能将在后续版本中实现。

最新版本的回测服务器已经实现了事件通知，但omicron中的Strategy基类还未实现这一功能。大富翁的事件通知机制基于网络通信，因此要比backtrader强大很多。

#### 8.2.1. 数据和数据格式

backtrader读取来自于csv， dataframe等来源的数据，并将其转换为data feed数据格式。它的特点是实现了步进机制和特殊的切片语法。

大富翁回测框架对数据的要求来自两个方面。backtesting server和策略分别读取独立的数据源。backtesting server通过data feed接口读取数据，当前已实现的feed是zillionare feed，它通过omicron从influxdb中读取数据。用户可以拓展它从文件或者其它网络接口读取数据。

strategy中，用户自己处理数据来源，也对数据格式没有任何要求。一般而言，可以通过omicron的接口读取数据来编写策略，但也可以完全使用其它数据源。

#### 8.2.2. 跨周期数据
backtrader实现了漂亮的跨周期数据同步。大富翁框架在策略端基本不直接处理数据，所以并没有直接进行跨周期数据同步。但是，omicron的timeframe模块提供了相应的功能：

```python
# 示例1
from coursea import *
from omicron.strategy.base import BaseStrategy

await init()

class DummyStrategy(BaseStrategy):
    async def predict(self, frame: Frame, frame_type: FrameType, i: int, **kwargs):
        code = kwargs.get("code")
        daily = await Stock.get_bars(code, 5, frame_type, end=frame)
        
        weekend = tf.floor(frame, FrameType.WEEK)
        weekly = await Stock.get_bars(code, 1, FrameType.WEEK, end=weekend)
        
        print(i, frame, daily[-1]["frame"], weekly[-1]["frame"])
        
s = DummyStrategy(
    url=cfg.backtest.url,
    is_backtest=True,
    start=datetime.date(2023, 4, 22),
    end=datetime.date(2023, 8, 3),
    frame_type=FrameType.DAY,
)

await s.backtest(code = "000001.XSHE", stop_on_error=True)
```

这里节选输出结果的最后几项：

```
62 2023-07-25 2023-07-25T00:00:00 2023-07-21T00:00:00
63 2023-07-26 2023-07-26T00:00:00 2023-07-21T00:00:00
64 2023-07-27 2023-07-27T00:00:00 2023-07-21T00:00:00
65 2023-07-28 2023-07-28T00:00:00 2023-07-28T00:00:00
66 2023-07-31 2023-07-31T00:00:00 2023-07-28T00:00:00
67 2023-08-01 2023-08-01T00:00:00 2023-07-28T00:00:00
68 2023-08-02 2023-08-02T00:00:00 2023-07-28T00:00:00
69 2023-08-03 2023-08-03T00:00:00 2023-07-28T00:00:00
```

我们可以将这个结果与第19课中，多周期一节中的示例15的输出结果相对照，以验证结果的正确性。所以，在大富翁框架里，实现跨周期数据的正确存取并不困难。

#### 8.2.3. 驱动模式与性能
backtrader允许我们在策略初始化时，就读取全部数据，计算出技术指标与信号，这样在next方法中，就只是一个非常简单的步进循环，所以在这种情况下，它的性能比较快。

大富翁完全是事件驱动模式，这种模式下，策略的编写需要的技巧不如向量式复杂，出错的可能性也更小。但是，backtrader在一开始就将全部数据加载到了内存中，因此无论之后是向量式还是事件驱动式，它的运算速度都要显著快于大富翁。在大富翁中，推荐的模式是，在每一个predict方法中，我们都根据frame来获取想要的数据。如果这些数据来自于omicron的话，考虑到omicron将访问网络来获取数据，显然会比backtrader慢不少。

此外，大富翁策略中调用 sell/buy/position等函数（属性）时，都会触发网络访问，这也会相应地比backtrader慢。因此，在性能上，大富翁的回测是比backtrader慢的。

!!! tip
    如果大富翁回测的性能不能满足要求，当前一个简单的方法是在predict中，在i==0时，加载全部所需要的数据并保存在内存中。此后，根据i来换算下标，访问与frame对应的数据。<br><br>但是这样一来，你将可能失去动态前复权支持。

#### 8.2.4. 回测报告
大富翁的回测报告要优于backtrader，它充分利用了plotly的交互式绘图能力。它的回测报告不仅提供了买卖点，并且在这些买卖点上通过hoverinfo提供了热点信息，这样查错比较方便。它也没有backtrader那样当回测周期过长时出现的图形拥挤问题。

但是大富翁报告对技术指标的支持不如backtrader那样多。如果有必要，用户需要自己绘制这些指标。在策略指标上，大富翁提供了几乎所有empyrical支持的技术指标。如果这些还不能完全反映策略的特征的话，可以通过assets数据，调用quantstats进行报告分析。

### 8.3. 一个完整的策略示例

```python
# 示例2
import pandas as pd
import talib as ta
from coursea import *
from omicron.strategy.base import BaseStrategy

await init()

def streaks(a):
    df = pd.DataFrame(a)
    lt = df[0] < df[0].shift(1)
    eq = df[0] == df[0].shift(1)

    def count(x):
        n = 0 if count.called == 0 or eq[count.sum] else 1
        r = np.arange(n, len(x) + n)
        if count.called % 2 == 1:
            r = -r

        count.called += 1
        count.sum += len(x)
        return pd.Series(r, x.index)
    count.called = 0
    count.sum = 0

    return np.array(df.groupby((lt != lt.shift(1)).cumsum()).apply(count), dtype=float)

def percent_rank(close, n):
    close = array_math_round(close, 2)
    pct = close[1:]/close[:-1] - 1
    x = np.array([sum(pct[i+1-n:i+1] < pct[i]) / n for i in range(len(pct))])
    x[:n-1] = np.nan
    return x * 100

def conners_rsi(close, x = 6, y = 2, z=20):
    rsi = ta.RSI(close, x)
    streak = ta.RSI(streaks(close), y)
    pr =  percent_rank(close, z)
    
    return (ta.RSI(close, x)[-1] + ta.RSI(streaks(close), y)[-1] + percent_rank(close, z)[-1]) / 3


class CornerRSI(BaseStrategy):
    def __init__(self, url, **kwargs):
        super().__init__(url, **kwargs)
        self.is_backtest = kwargs.get("is_backtest", True)
        self.crsis = []
        

    async def predict(self, frame: Frame, frame_type: FrameType, i: int, **kwargs):
        code = kwargs.get("code")
        hrsi = kwargs.get("hrsi")
        lrsi = kwargs.get("lrsi")
        
        bars = await Stock.get_bars(code, 40, frame_type, end=frame)
        crsi = conners_rsi(bars["close"].astype(np.float64))
        self.crsis.append(crsi)
        if crsi >= hrsi:
            await self.sell(code, percent = 1, order_time=tf.combine_time(frame, 14, 55))
        elif crsi <= lrsi:
            await self.buy(code, money = self.cash, order_time=tf.combine_time(frame, 14, 55))
            
s = CornerRSI(
    url=cfg.backtest.url,
    is_backtest=True,
    start=datetime.date(2022, 1, 22),
    end=datetime.date(2023, 8, 3),
    frame_type=FrameType.DAY
)

await s.backtest(code = "000001.XSHE", stop_on_error=False, hrsi=75, lrsi=15, baseline="000001.XSHE")

# 通过omicron绘图
await s.plot_metrics()

# 通过quantstats绘图
import quantstats as qs

df = pd.DataFrame(s.bills["assets"], columns=("frame", "assets"))
df["frame"] = pd.to_datetime(df["frame"])
assets = df.set_index("frame").assets

qs.reports.html(assets, output="000001.html")
```

omicron输出报告如下：

![75%](https://images.jieyu.ai/images/2023/08/lesson23-payh.png)

与自身对比，平安银行在回测区间内下跌25.25%，策略回撤仅-5.33%。如果我们从2023年1月起回测，则策略收益是1.16%，对比buy and hold收益为-11.8%，Conner's RSI策略连续两年跑赢参照。

### 8.4. 参数优化

大富翁框架目前没有提供参数搜索和自动优化。但是，类似backtrader的参数优化机制，可以非常容易地实现，我们需要用到python内置库中的itertools包：

```python
# 示例3
from itertools import product

for a, b in product((3, 5), (0.2, 0.5)):
    print(a, b)
```

输出结果将是：

```
3 0.2
3 0.5
5 0.2
5 0.5
```
利用这个原理，我们可以很容易地实现参数优化：

```python
# 示例4
import asyncio

hrsis = (60, 70)
lrsis = (15, 20, 25)

strategies = []
tasks = []

for hrsi, lrsi in product(hrsis, lrsis):
    s = CornerRSI(
    url=cfg.backtest.url,
    is_backtest=True,
    start=datetime.date(2023, 1, 4),
    end=datetime.date(2023, 8, 3),
    frame_type=FrameType.DAY
)

    strategies.append(s)
    tasks.append(asyncio.create_task(s.backtest(code = "000001.XSHE", stop_on_error=False, hrsi=hrsi, lrsi=lrsi, baseline="000001.XSHE")))

await asyncio.gather(*tasks)
# do the report 
for s in strategies:
    await s.plot_metrics()
```

不过，上述代码虽然是异步并发，但它只能使用一个进程，而且每一个predict方法都要从influxdb中读取数据，而backtrader是多进程并发执行的，一次性加载数据，速度会快不少。不过, haste makes waste。与大富翁框架相比，backtrader更适合作为策略探索工具，进行初步研究。

## 9. 参考文献
1. 《Seven sins of quantitative investing》， Lou Y. QWAFAFEW Presentation, Jan 2015


