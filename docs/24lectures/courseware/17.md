---
title: Plotly 绘图
mainfont: WenQuanYi Micro Hei
---

Plotly 是一个交互式的数据可视化库，支持多种编程语言，包括 Python、R、JavaScript 和 MATLAB 等。它可以用于生成静态图表，也可以创建交互式图表，让用户能够与数据进行探索和交互。其中，Python 版本的 plotly 有时也被称为 Plotly.py，与 plotly.js 对应。在本课程中，如果没有特别指出，当我们提到 plotly 时，都是指 Plotly 的 Python 库，即 Plotly.py。

Plotly 中的交互能力允许用户缩放、平移、悬停并获取图表上每个数据点的详细信息。这为数据可视化增加了一个全新的维度，使其成为一种更加身临其境的体验。这一能力在某些场景将非常有用，甚至不可或缺。

比如，我们要绘制一个蜂群图 (bee swarm)。蜂群图的特点就是，图上可能有数百或数千个数据点。如果是静态图像，除非添加注释，否则我们无法获取有关各个数据点的具体信息；但添加这么多注释，又将使图表变得混乱。相反，使用 Plotly，我们只需将鼠标悬停在您感兴趣的点上，即可获得该点的详细信息。

Plotly 的别一个优势是 Dash。2017 年，Plotly 发布了 Dash, 使得 Python 程序员也可以在不熟悉 Html/js 的情况下，就能完整地生成一个网站应用。

此外，Plotly 还提供了 SaaS 服务，可以轻松共享和协作，使得数据可视化更加便捷和灵活。

## 1. Plotly 中的基本概念
与 matplotlib 类似，在 API 组织上，Plotly 同样提供了功能强大的低级别 API，称作 Graph Object；以及更为简便易用的高级接口，称作 Plotly Express。

Plotly.py 只负责图形对象的定义。图形渲染将由 plotly.js 引擎来完成。两者之间，通过 json 对象来传递数据。这个 json 对象，我们可以将其打印出来：

```python
import plotly.express as px

fig = px.line(x=["a","b","c"], y=[1,3,2], title="sample figure")
print(fig.to_json())
```

这个结果比较长，一般我们只需要关注非自定义的部分，我们可以改用下面的语句来查看 fig 对象的关键属性：

```python
print(fig)
```

其输出结果如下：

```
Figure({
    'data': [{'hovertemplate': 'x=%{x}<br>y=%{y}<extra></extra>',
              'legendgroup': '',
              'line': {'color': '#636efa', 'dash': 'solid'},
              'marker': {'symbol': 'circle'},
              'mode': 'lines',
              'name': '',
              'orientation': 'v',
              'showlegend': False,
              'type': 'scatter',
              'x': array(['a', 'b', 'c'], dtype=object),
              'xaxis': 'x',
              'y': array([1, 3, 2]),
              'yaxis': 'y'}],
    'layout': {'legend': {'tracegroupgap': 0},
               'template': '...',
               'title': {'text': 'sample figure'},
               'xaxis': {'anchor': 'y', 'domain': [0.0, 1.0], 'title': {'text': 'x'}},
               'yaxis': {'anchor': 'x', 'domain': [0.0, 1.0], 'title': {'text': 'y'}}}
})
```

这里出现了一个我们在上一课已经熟悉的词：Figure。

在 Plotly 中， Figure 仍然是最顶层的图形对象。Figure 类同样用来定义和容纳布局、子图、绘图对象。在 plotly 中，Figure 有三个顶层属性，即 data, layout 和 frames。

一个 Figure 中，可能包含多个图形对象（比如 line, bar 等），类似于 matplotlib 中的 artist。这些对象被称为 trace, data 则是这些 trace 的集合。在 Plotly 中，大约有 40 多种 trace，比如 scatter, bar, pie 等。

layout 定义了图形中的尺寸和边距、图例、模板、字体、颜色、坐标轴、标记、子图类型和操作控件。上面的输出中，有些属性没有打印出来，意味着它们将取默认值。在 plotly 中，子图和布局并没有很严格地层次化，plotly 中没有象 matplotlib 中 axes 那样的子图概念的对应物，我们也没必要深究，能熟练运用就够了。layout 必须是一个字典类型。

上述输出中还有一些之前没有接触过的概念，比如操作控件、hover template 和 hover label，这是因为 plotly 支持交互式操作，所以额外定义了这些概念。

frames 属性只在定义了动画的 Figure 对象中出现。

在 plotly 中也有样式的概念。另外，它使用 color scale/sequence 来代替 color map 的概念。

我们在上一课中学习到的其它概念，比如坐标轴、图例等仍然适用。我们一样可以创建双轴图，轴可以设置对数坐标或者时间坐标等。

## 2. Plotly 模块结构
Plotly 中，主要的绘图是通过 Plotly Express 和 Grpah Objects 来完成的，Subplots 实现布局， Figure Factories 用来帮助构建复杂的图。I/O 模块提供了读、写和渲染接口。

### 2.1. Plotly Express
Plotly Express 提供了各种常见图形的绘制方案，包括散点图、折线图、面积图、柱状图等约 32 种图形。如果我们需要简单快速地绘制图形，可以在这个包里查找。

下图给出了一个分类索引：

![75%](https://images.jieyu.ai/images/2023/07/lesson17-px-outline.png)

在 plotly 中，我们一般如下导入 plotly express 模块：

```python
import plotly.express as px
```

### 2.2. Graph Objects

在 Plotly 中，我们可以只用 json 来表示一个图，正如上面例子中的 scatter trace。但是，使用 json 也有很多不便之处，比如，它缺乏属性验证，读写操作也不方便。因此，这些 json 都关联到 trace 对象，所有的 trace 对象，它们的类定义都被包含在 plotly.graph_objects 模块中。

这个模块中，定义了 Figure 类，Layout 类，还有就是 Trace 类。这些 Trace 类中，除了 plotly express 中有的都有之外，还包括了一些领域相关的，比如 Finance Traces（比如 OHLC, Candlestick) 等。

在 Plotly 中，我们一般如下导入 graph objects:

```python
import plotly.graph_objects as go
```

每个 Plotly Express 函数在内部都使用图形对象，并且返回值都是一个 plotly.graph_objects.Figure 实例。任何使用 Plotly Express 的地方都可以使用 Figure 来创建，但代码量可能要多好几倍。

在 plotly 中，推荐优先使用 plotly express 来构建图形，除非某种图在 plotly express 中尚不存在，或者我们要构建的图过于复杂，此时，从空的 go.Figure 对象开始则可能更容易。

下图显示了grahp_objects的主要构成部分：

![75%](https://images.jieyu.ai/images/2023/07/lesson17-go-outline.png)

### 2.3. 其它
subplots 包提供了生成多个子图的方法。

Figure Factories 主要用于某些特殊领域的绘图，一般可以略过不看。

I/O 模块提供了读写和渲染接口。比如，我们可以直接从一个字典来渲染成图：

```python
fig = dict({
    "data": [{"type": "bar",
              "x": [1, 2, 3],
              "y": [1, 3, 2]}],
    "layout": {"title": {"text": "A Figure Specified By Python Dictionary"}}
})

# TO DISPLAY THE FIGURE DEFINED BY THIS DICT, USE THE LOW-LEVEL PLOTLY.IO.SHOW FUNCTION
import plotly.io as pio

pio.show(fig)
```
这个示例也揭示了 plotly 中图的定义及 plotly 运作的本质：plotly 中图的定义本质上是一个 json 数据结构，python 对象只是对这个数据结构的一个映射；图的最终生成，也是基于这个数据结构，由 plotly.js 来生成的。

在 io 包里，除了 show 方法外，还有写入到图像文件、转换成 json，或者将 json 转换为 Figure 对象，以及转换为 Html 等方法。

plotly.colors 模块提供了跟色彩相关的操作。

plotly.data 模块提供了出于演示目的需要使用的数据，共约 11 种数据，比如 iris, carshare, stocks 等。数据集的名字同时又是加载器（即 iris 是一个函数），其返回结果是 DataFrame。

## 3. 比较 plotly express 与 go.Figure

在 plotly 中，我们主要通过以下两种方式创建图形。一是通过 plotly.express，二是通过 Figure 的构造函数。这一节我们通过一个简单的例子，说明一下两种方法一般情况下如何调用，以及它们的异同。

首先是 plotly.express 的版本：

```python
import plotly.express as px

df = px.data.iris()
fig = px.scatter(
    df,
    x="sepal_width",
    y="sepal_length",
    color="species",
    title="A Plotly Express Figure",
)

fig.show()
```

下面是 Figure 的版本：

```python
import plotly.express as px

df = px.data.iris()

fig = go.Figure(
    data = [go.Scatter(x=df["sepal_width"], 
                       y = df["sepal_length"], 
                       mode="markers",
                       marker = dict(color = df.species.astype('category').cat.codes)
                      )],
    layout = dict(title=dict(text="Grpah Object"))
)

fig.show()
```

可以看出，两个版本相比较，Figure 版本的代码量确实要多一些。px 根据输入数据和 trace 的类型，自动进行了一些猜测。另外，由于 px 函数返回的就是一个 Figure，所以，我们还可以先通过 px 来创建某种类型的图形，然后对不满意的地方进行更新。

在 Figure 版本的代码中，我们需要给 Figure 对象传入 data 和 layout 两个参数，Figure 总共有三个顶层属性，但 frames 属性只在动画中才存在。

我们使用 go.Scatter 来生成这个 trace。在构造这个 trace 时，工作量将会大不少。首先，我们需要显式地指定 x 轴和 y 轴的值，指定 trace 的模式为 markers。这里如果不指定，默认将会是生成折线图。

指定各个种类的颜色更为复杂一点。我们不能象调用 px.scatter 那样，传入 color = 'species'或者 color=df['species']。首先，我们得理解这个着色是发生在 marker 上的，因此，我们应该通过参数 marker 来指定 marker 的 color 属性，而不是使用 fillcolor 这个参数。其次， species 的数据类型是字符串，在指定 color 属性时，我们还得事先将其先类型化（category），再转换为数值型，才能成为合法的颜色输入。

<!--
显然，px.scatter 同时生成了 Scatter trace 和 Figure 对象。因此，px.scatter 接收的参数中，有一部分是用来生成 Figure 的。这一部分参数是共性的，不仅在 px 的其它方法中使用，也会用在 go.Figure 的构造中。接下来，我们就讲解一下这些参数。

-->

px.scatter 与 go.Figure 共享三个参数，分别是 title, width 和 height, 用来指定标题和图形尺寸。在 plotly 中，图形尺寸的单位是像素。其它一些参数，主要用于构建 trace 本身，以及 layout。注意在 plotly 中，layout 与 trace 绑定比较深，不同的 trace，能使用的 layout 是有范围的。

## 4. Plotly 股票分析图绘制
一张股票分析图一般由多个子图构成。其主要部分是 k 线图，或称蜡烛图 (candlestick)，次图部分可能是一到多个指标图，如成交量、MACD 等。

主图与子图一般共享同一个 x 轴。无论是子图、还是主图，一般都会叠加多项指标或者指标。

此外，在交互式图形中，主图还应该显示十字光标，当主图的光标移动时，子图光标也要跟随一起移动。

### 4.1. K 线图绘制
plotly.graph_objects 中，提供了 Candlestick 绘图，我们先来看看它提供的功能：

```python
from coursea import *
import plotly.graph_objects as go

import pandas as pd
from datetime import datetime

await init()
bars = await Stock.get_bars('000001.XSHG', 20, FrameType.DAY)

fig = go.Figure(data=[go.Candlestick(x=bars['frame'],
                open=bars['open'],
                high=bars['high'],
                low=bars['low'],
                close=bars['close'])])

fig.show()
```

绘制的图形如下：

![](https://images.jieyu.ai/images/2023/07/lesson17-default-candlestick.png)

这个图形与我们常见的图形有所不同：

1. 它使用了红色表示下跌，绿色表示上涨。此外边框线的宽度与实体相比也要宽一些。
2. 它使用连续坐标轴，所以在遇到节假日时，会出现断档
3. 坐标刻度上它使用了英文，我们希望看到使用纯数字或者中文

下面我们就分别讨论这几个问题。

关于第一个问题，plotly 已经提供了方案，我们可以通过定制 increasing, decreasing, line 等参数来实现：

```python
bars = await Stock.get_bars('000001.XSHG', 20, FrameType.DAY)

RED = "#FF4136"
GREEN = "#3DAA70"

cs = go.Candlestick(
    x=bars["frame"],
    open=bars["open"],
    high=bars["high"],
    low=bars["low"],
    close = bars["close"],
    line=dict({"width": 1}),
    name="K 线",
    increasing = {
        "fillcolor": "rgba(255,255,255,0.9)",
        "line": dict({"color": RED})
    },
    decreasing = dict(fillcolor =GREEN, line = dict(color =  GREEN)
    )
)

fig = go.Figure(data=cs)
fig.show()
```

上述代码中，对 increasing 和 decreasing 我们分别使用了两种格式来赋值。两种格式都比较繁琐易错，建议使用属性访问法来赋值，这样可以利用 IDE 的属性检查和自动代码提示。使用属性访问法，第 14 行我们可以改造成为：

```python
cs.increasing.fillcolor = "rgba(255,255,255,0.9)"
cs.increasing.line.color = RED
```

第 2 个问题和第 3 个问题需要一起解决。出现这个问题的原因是，当我们给参数 x 赋值为一个时间日期数组时，plotly 自动推断它坐标轴类型为日期，这是一个连续类型的轴。

!!! note
    plotly 中的轴类型有 linear（默认）, log, date, category 和 multicategory。如果我们没有指定轴类型，plotly 会按以下顺序猜测轴类型：multicategory（只有数据本身是嵌套数组时）、date 或者 category（在我们最初的例子，会被优先判定为 date 型，从而终推断），如果还不能决定，将使用 linear。

只要轴属于连续型的、数值型的，plotly（或者其它绘图库）都会自动推断 x 之间的距离并在显示时，复现这种距离之间的比率。比如，如果 x 的取值为 [1, 3, 9]，当坐标轴指定为线性（缺省类型）时，plotly 会计算出各元素之间的横向距离为 [2, 6]，这样 3 和 9 之间的距离就是 1 和 3 之间的 3 倍，在显示时，plotly 会复现这个比例，即使是发生缩放，这个比例也保持不变。如果我们把坐标轴指定为类别型的，则各相邻元素之间的距离均为 1，即 1 和 3 之间，3 和 9 之间，距离都为 1。

我们通过下面的代码来进一步说明这段文字：

```python
import plotly.express as px
from plotly.subplots import make_subplots

fig = make_subplots(rows=1, cols=2)
fig.add_trace(go.Scatter(x=[1, 3, 9], y = [1,1,1]), row=1, col=1)
fig.add_trace(go.Scatter(x=[1, 3, 9], y = [1,1,1]), row=1, col=2)

fig.update_xaxes(type='category', row=1, col=2)
```
结果显示如下

![](https://images.jieyu.ai/images/2023/07/lesson17-ax-type-linear-vs-category.png)

这个结果很好地揭示了 plotly 把数据定位到图中的原理。

在 plotly 中，离散型的轴似乎只有 category 类别。在绘制 k 线时，我们需要将 xaxis 的类型设置为 category，这样一来，无论 k 线数据中存在多少个缺失值，相邻两个 bar 之间的距离将始终为 1，从而消灭了”断档“的现象。

!!! note
    在 plotly 中，还可以通过给坐标轴设置 rangebreaks 属性来隐藏节假日。尽管这是官方给出的方案，但在我们的场景下并不好用 -- 除非你使用 tushare 给出的日历 -- 它显式地标出来节假日。其它日历都只提供了交易日历。

!!! attention
    使用 category 类型并非全无代价。任何一个坐标体系，都必须满足坐标轴的有序和距离可计算。对 category 类型的数据，如何进行排序是一个问题。在我们的 k 线图绘制中幸运地没有遇到。在其它场景，我们可能需要借助 categoryarray, categoryorder 等参数来解决排序问题。

我们可以使用下面的代码来将 x 轴指定为 category 类型：

```python
nticks = len(bars) // 10
fig.update_xaxes(type='category', tickformat="%y/%m/%d", nticks = nticks)
```
在代码中，我们通过 tpye 参数指定 x 轴类型为 category 型，通过 nticks 指定每隔 10 个 bar 才显示一次 tick 文本。这里我们使用了 update_xaxes 方法来更新 x 轴的相关参数。我们也可以用以下方法：

```python
fig.update_layout(dict(xaxis=dict(type='category')))
```

!!! attention
    与 matplotlib 不同，在 Plotly 中，axes, axis 都指的是坐标轴。

关于 tick labels，我们也通过 tickformat 指定了格式化的方法。这里的格式化串来自于 [d3.js](https://github.com/d3/d3-time-format/blob/main/README.md#locale_format)，这是 plotly.js 的要求。这个格式串要求，x 轴刻度显示为年/月/日，都使用带零填充的两位数字。但是，输出结果表明，这个格式化并未按要求进行。

!!! note
    如果我们能将 tickformat 指定为 lambda 表达式或者函数，将会非常方便，但 Plotly 不允许我们这样做，而且这一决定几乎是永久性的。原因是，最终的图形渲染引擎是 plotly.js，所有不能串行化的属性都不能允许。如果 plotly.py 在这里允许我们指定一个函数来进行格式化，而这个方法将在 plotly.js 中无法还原，因此 plotly 禁止了所有类似的需求。<br><br>
    这是不同的实现机制带来的功能上的不同。<br><br>
    类似的，在 matplotlib 或者其它框架中，我们可以很容易地通 get_xlim/get_ylim 来获取绘制的图形的坐标上下限。但在 plotly.py 中，目前来看还是困难的，因为这些数值只在绘制发生后才能最终确定，而绘制发生成 plotly.js 中，它没有回传给 plotly.py 的机制。

最终，我们必须使用以下代码：

```python
from datetime import datetime

import pandas as pd
import plotly.graph_objects as go

bars = await Stock.get_bars("000001.XSHG", 120, FrameType.DAY)

def _format_ticks(tm):
    return np.array(
        [f"{x.item().year:02}-{x.item().month:02}-{x.item().day:02}" for x in tm]
    )

cs = go.Candlestick(
    x=_format_ticks(bars["frame"]),
    open=bars["open"],
    high=bars["high"],
    low=bars["low"],
    close=bars["close"],
    line=dict({"width": 1}),
    name="K 线",
)

fig = go.Figure(cs)

fig.update_xaxes(
    type="category",
    nticks=10,
    ticklen=10,
    ticks="outside",
    minor=dict(nticks=5, ticklen=5, tickcolor="blue", ticks="inside"),
)

fig.show()
```

代码中，我们把格式化好的字符串传递给参数 x，并且设置 xaxis 的类型为 category。这样一来，x 轴的刻度将能如我们所期望的那样显示了。

代码中还演示了 minor 级别的刻度的用法。这是通过 minor 属性来指定的。无论是 major，还是 minor，它们都共享一些共同的属性，比如 nticks, ticklen 等。在这里，nticks 是在整个区间里要显示的刻度数，ticklen 是刻度线的长度，ticks 指定标注的方向，如果是 inside，则刻度将标记在图的内部；如果是 outside，则将标注在图的外部。我们可以从上例的输出中明白它们的用法。tickcolor 用来指定刻度线的颜色。

![](https://images.jieyu.ai/images/2023/07/lesson17-candlestick-by-category.png)

!!! note
    如果我们拖动 notebook 所在的窗口，放大或者缩小窗口，你将会看到，tick 刻度会在显示空间不够用时，自动旋转 45 度甚至 90 度。这也是 plotly 响应式设计的另一个特性。

### 4.2. 叠加技术指标

我们以叠加均线系统为例，说明如何在一个（子）图中，叠加多个 trace。

首先，我们为均线指定颜色。尽管我们在这里可以使用循环色让系统随机挑选颜色，但这样生成的图会难以阅读。如果我们需要产生和阅读大量 k 线图，我们希望均线的颜色最好固定下来 -- 无论阅读器是人，还是机器学习模型。

```python
MA_COLORS = {
    5: "#1432F5",
    10: "#EB52F7",
    20: "#C0C0C0",
    30: "#882111",
    60: "#5E8E28",
    120: "#4294F7",
    250: "#F09937",
}
```
我们定义了七组均线的颜色。每根均线都是一个 trace。

```python
ma_groups = [5, 10, 20, 30, 60, 120, 250]

bars = await Stock.get_bars("000001.XSHG", 130, FrameType.DAY)

# DRAW THE CANDLESTICK AND GET THE FIG
# ...

# ADD MA LINE
for win in ma_groups:
    name = f"ma{win}"
    if win > len(bars):
        continue

    ma = moving_average(bars["close"], win)
    line = go.Scatter(
        y=ma,
        x=_format_ticks(bars["frame"]),
        name=name,
        line=dict(width=1, color= MA_COLORS.get(win)),
    )

    fig.add_trace(line)

fig.update_layout(height = 600)
fig.show()
```

最终显示结果如下：

![](https://images.jieyu.ai/images/2023/07/lesson17-cs-with-ma.png)

在这里，我们复用了绘制 k 线图时产生的那个 Figure 对象，该对象只包含一个子图，这个子图是默认产生的。因此，当我们给 fig 对象增一个 trace 时，并且 trace 的坐标轴也一致，这个 trace 就自然叠加在图上。

### 4.3. 子图
接下来，我们尝试给分析图增加成交量图。成交量图一般绘制成柱状图。通过这一节，我们学习如何生成多个子图，同时也介绍一种可能是 plotly 独有的多子图模式 -- faceting。

facet 图（分面图）也称为 trellis（网格图），是由具有相同轴集的多个子图组成的图形，其中每个子图显示数据的子集。下面这段代码将国家 GDP 与人均寿命进行关联绘图图，并按 year 字段进行划分，最终生成了 3 行 4 列的 facet 图：

```python
gdp = px.data.gapminder()

fig = px.scatter(gdp,   # 数据集
                 x='gdpPercap', 
                 y='lifeExp', 
                 color='continent',  # 按国家所在大陆进行颜色区分
                 size='pop',         # 按人口数决定大小
                 facet_col='year',   # 列切面字段
                 facet_col_wrap=3,   # 每行最多 3 个图形
                 width = 800,
                 height = 600,
                 title = "faceting example"
                )
fig.show()
```

这将生成以下图形：

![](https://images.jieyu.ai/images/2023/07/lesson17-faceting-example.png)

facet 图用来显示同一数据集的多个子集，适用范围比较窄。下面，我们就介绍更为通用的子图创建方式。

在 Plotly 中，创建子图的方式相对于 matplotlib 要少一些，只能通过 plotly.subplots.make_subplots 方法来创建子图。我们一般通过 rows,cols 来指定要创建的子图的行数和列数，子图的大小比例 column_widths 和 row_heights 来指定，如果需要创建异形的子图，我们需要通过 specs 参数，通过 colspan 或者 rowspan 来做单元格的合并。

我们来看一个例子：

```python
fig = make_subplots(rows=2, cols=2,
                    column_widths=[0.3, 0.7], 
                    row_heights = [0.8, 0.2],
                    specs=[[{}, {}],
                    [{'colspan': 2}, None]],
                    horizontal_spacing = 0.05
                    )
                    
fig.add_trace(go.Scatter(x=[1,2,3], y=[2,1,2]), row=1, col=1) 
fig.add_trace(go.Scatter(x=[1,2,3], y=[2,1,2]), row=1, col=2) 

fig.add_trace(go.Scatter(x=[1,2,3], y=[2,1,2]), row=2, col=1) 
fig.show()
```

这将生成以下图形：

![](https://images.jieyu.ai/images/2023/07/lesson17-make-subplots-example.png)

示例中，我们先是生成了一个 2*2 的网格，然后通过 specs 进行单元格合并，将第 2 行的第一个单元格，跨了一列，这样的结果导致后面一个单元格的 spec 变为 None。注意，当我们使用 span 功能时，子网格的索引顺序就很重要了。在此函数中，通过 start_cell 来指定网格索引顺序，默认是从左上角开始，即左上角的第一个单元格被记作（1，1）。

单元格的的尺寸大小按比率指定。如果未指定，则它们将均分图的行宽和列高。在示例中，我们指定了第一列占图的 30%宽，第一行占图的 80%高。

最后，我们通过 horizontal_spacing 指定每个子图之间的横向间距是 5%的行宽。如果要指定纵向间距，则应该使用 vertical_spacing。

这里对 specs 属性再进行一点讨论。specs 是一个 List[Dict]，它的元素除了我们这里看到的 colspan 之外，还有 rowspan, l,r,t,b（分别用来指定单元格的左、右、上、下内边距）。此外，还有一个重要参数，即 type，它用来为每个子图指定坐标轴类型。每种 trace 都有唯一的坐标轴类型；一种坐标轴类型可以容纳多种 trace。区分 trace 的坐标轴类型是必要的，比如，2D trace 和 3D trace 不可能绘制在同一个子图中，我们必须进行区分，分别绘制。

这一次，我们要首先创建一个包含两个子图的 Figure 对象，然后往两个子图中，分别添加对应的 trace。

```python

from plotly.subplots import make_subplots

fig = make_subplots(
    rows=2,
    cols=1,
    shared_xaxes=True,
    vertical_spacing=0.1,
    subplot_titles=("K 线图", "Volume"),
    row_heights=[0.8, 0.2],
)

bars = await Stock.get_bars('000001.XSHG', 30, FrameType.DAY)

def _format_ticks(tm):
    return np.array(
        [f"{x.item().year:02}-{x.item().month:02}-{x.item().day:02}" for x in tm]
    )

ticks = _format_ticks(tm)

# CANDLESTICK
cs = go.Candlestick(
    x=ticks,
    open=bars["open"],
    high=bars["high"],
    low=bars["low"],
    close=bars["close"],
    line=dict({"width": 1}),
    name="K 线",
)
fig.add_trace(cs, row=1, col=1)

# MOVING AVERAGE
ma_groups = [5, 10, 20, 30, 60, 120, 250]
for win in ma_groups:
    name = f"ma{win}"
    if win > len(bars):
        continue

    ma = moving_average(bars["close"], win)
    line = go.Scatter(
        y=ma,
        x=_format_ticks(bars["frame"]),
        name=name,
        line=dict(width=1, color= MA_COLORS.get(win)),
    )

    fig.add_trace(line, row=1, col=1)
    
# VOLUME WILL BE ADD TO SECONDARY PLOT
colors = np.repeat(RED, len(bars))
colors[bars["close"] <= bars["open"]] = GREEN

vol = go.Bar(
    x=ticks,
    y=bars["volume"],
    showlegend=False,
    marker={"color": colors},
)

fig.add_trace(vol, row=2, col=1)

fig.update_layout(xaxis_rangeslider_visible=False)
fig.update_xaxes(
    type="category",
    nticks=len(bars) // 5,
    ticklen=10,
    ticks="outside",
    minor=dict(nticks=5, ticklen=5, ticks="outside"),
    row=2,
    col=1
)

fig.show()
```
这里我们需要重点关注的代码有以下几处：

1. 第 28 行，在 make_subplots 调用中，我们指定了 shared_xaxes = True。如果缺少这一指定，则每个子图都会显示一个 x 轴，显然不美观。其它参数我们已经讨论过了。比如，通过 row_heights 的指定，我们让 k 线图占据 80%的高度，成交量图则只占 20%的高度。通过 vertical_spacing，使得两图之间产生 10%的间距。
2. 第 38 行和第 64 行，我们将 k 线图和均线加入到第一个子图中。
3. 第 67 到 77 行，我们构建了成交量 trace，加入到第二个子图中。这里我们为柱状图的每一个 bar 指定了颜色，这是通过 marker={"color": colors}来指定的。而 colors 则是一个等长的数组，取值要么为 RED，要么为 GREEN。
4. 第 79 行，我们隐藏了 x 轴上的滑动区域控制条，也是出于美观的需要。在此之前的绘图中，都是存在这一控制条的。
5. 第 80 行，将 x 轴切换为 category 类型，这在之前就已经见过了。同时，我们还设置了刻度标签旋转 45 度，并且每 5 个交易日显示一次刻度。

最终，上述代码将绘制以下图形：

![](https://images.jieyu.ai/images/2023/07/lesson17-candlestick-with-vol.png)

!!! Note
    你可能已经注意到，在图中我们使用了中文。与 matplotlib 不同，这一次我们并没有设置字体。Plotly 自动提供了中文显示方案。

### 4.4. 显示区域

默认地，plotly 会在 Figure 中，加载全部数据。如果数据过多，它将根据指定的图形窗口大小，自动缩放 trace 元素的大小。因此，如果我们一次性提供过多的 bars，有可能导致这些 bars 会缩小到几乎看不清。我们需要通过设置显示区域来解决这个问题。

这是象 plotly 这样具有交互式绘图能力的工具才能提供的功能。具体地说，是通过设置 xaxis 的 range 来实现的。range 对象是一个二元组，分别定义了显示区域的起点和终点。根据坐标轴类型不同，指定方法也不一样。对于对数轴，如果我们要设置显示 x 从 1 到 100，则要指定 (1, 2)；对于日期，可以接受字符串（但要能转换成日期）、date 对象或者 unix epoch 时间；对于我们这里的 categorical 坐标轴，则是传入下标数字。

下面的代码将最初的显示区域固定在最后的 120 根 bar 下。当然，这个数值应该根据图形的尺寸来设置：

```python
fig.update_layout(height = 600, width=800)
fig.update_xaxes(range=[len(bars) - 120, len(bars)-1])
```

这两行代码，可以插入到前一个示例代码的第 57 行到 62 行之间。最终，在 Notebook 中，我们将得到一个可以左右拖动的图形，随着拖动，之前不能显示的部分被显示出来。

### 4.5. 交互式提示

在 k 线图中，我们还需要显示十字光标，并且，当光标移动时，显示个股的高开低收等信息。前者需要通过 spike 功能来实现，后者可以通过 hovertemplate 来实现。不过，在这里我们实现了一个简单的方法，自动添加了所有信息。

spike 是指当前数据点到 x 轴或者 y 轴的线。正常情况下，它显示为下图中的 1 和 2 标识处的线：

![](https://images.jieyu.ai/images/2023/07/lesson17-spike-lines.png)

在 k 线图中，我们需要如下定制：

```python
fig.update_xaxes(
    showgrid=False,
    showspikes=True,
    spikemode="across",
    spikesnap="cursor",
    spikecolor="grey",
    spikedash="solid",
    spikethickness=1,
)
fig.update_yaxes(
    showspikes=True,
    spikemode="across",
    spikesnap="cursor",
    spikedash="solid",
    spikecolor="grey",
    spikethickness=1,
)
```
这将从当前鼠标处，显示一个十字光标。

当我们在绘制的 k 线图中移动光标时，会自动提示当前光标最接近的数据点的数据。有时候会由于光标附近数据点比较密集，导致我们也不知道显示的究竟是哪一个指标的数据。此时我们可以设置 hovermode 为'x unified'，则会在 hoverinfo 中，自动显示当前 x 坐标对应的全部数据：

```python
fig.update_layout(hovermode="x unified")
```

加上十字光标和 hovermode = 'x unified'之后，交互式信息现在显示为：

![](https://images.jieyu.ai/images/2023/07/lesson17-crosshair-x-unified.png)

上图跟国内标准的分析图还有一点点差距，主要是 hoverifo 的显示位置。国内主流的分析图，Hoverinfo 的显示位置不是光标跟随，而是固定在左上角。不过，在固定位置上显示 hoverinfo，目前 plotly.py 还不支持。

## 5. 色彩

在 plotly 中，色彩序列（color sequences）是映射到离散数据值上的一个色彩列表，在使用时，不会采用色彩插值。色阶 (color scale) 则是一种连续色彩表示方法，它将 [0,1] 之间的所有值映射到某个颜色域上。比如，我们可以定义 [(0, "blue"), (1, "red")] 这样一个简单的色阶，通过在蓝色与红色之间插值，就可以提供无数种色彩方案。

在 plotly 中，当前活动模板 (template) 的 layout.colorway 属性指定了当前使用的离散色彩序列，而 layout.colorscales 则指定了当前使用的连续色彩序列。 

在 plotly 中，许多函数接受 color 参数。如果数据是数值类型，该参数会自动将数据值映射到连续色彩。如果数据是字符串类型，则颜色将被视为离散的（也称为分类或定性）。

plotly 中提供了许多内置的离散色彩序列和连续色阶。这段代码将显示内置色阶：

!!! Warning
    这段代码将运行约10分钟左右。

```python
from plotly.subplots import make_subplots
from plotly.io import to_image
import plotly.express as px
import plotly.graph_objects as go
from PIL import Image
from io import BytesIO

fig = make_subplots(3, 2, horizontal_spacing = 0.01, vertical_spacing=0.01)

traces = []

for i, pkg in enumerate(('sequential', 'diverging', 'cyclical')):
    for j, method in enumerate(('swatches', 'swatches_continuous')):
        f = getattr(getattr(px.colors, pkg), method)
        img = to_image(f(), width=300, height=400)
        rgb = Image.open(BytesIO(img))
        trace = go.Image(z = rgb)
        fig.add_trace(trace, row = i + 1, col = j + 1)

fig.update_layout(width = 400, height = 900)
fig.update_layout(margin=dict(l=0,r=0,b=0,t=0))
fig.update_layout(showlegend=False)
fig.update_xaxes(visible=False)
fig.update_yaxes(visible=False)
fig.show()
```
代码将遍历'sequential', 'diverging', 'cyclical'这三个包中，定义的所有色阶，并分别以离散和连续两种方式显示它们。

这段代码使用了较多技巧，比如使用 get_attr，将 fig 转换成静态图，以及将静态图读作 RGB 数值（去掉格式串），生成 Image trace, 移除掉子图之间的间距（通过设置 lrbt 边距）等。

输出比较长，请读者自行运行查看。

### 5.1. 离散色彩序列

```python
import plotly.express as px

fig = px.colors.qualitative.swatches()
print(px.colors.qualitative.Plotly)
fig.show()
```
这将打印出 Plotly 这种内置色彩方案所定义的各种颜色，以及输出一个色阶图：

![](https://images.jieyu.ai/images/2023/07/lesson17-qualitative-colors.png)

如果要切换到另一种色彩方案，我们可以使用下面的代码：

```python
import plotly.express as px
df = px.data.gapminder()
fig = px.line(df, y="lifeExp", x="year", color="continent", line_group="country",
              line_shape="spline", render_mode="svg",
             color_discrete_sequence=px.colors.qualitative.G10,
             title="Built-in G10 color sequence")

fig.show()
```
这里我们使用了 G10 这种色彩方案来显示一条直线。

在 plotly 中，定义和使用自己的离散色彩方案非常容易。这次，我们将使用上次课给出的 YSL 色彩，再实现一次柱状图配色：

```python
import numpy as np

# EXTRACT PALETTES FROM COOLORS.CO

ysl = [
    "#4D0609","#8F2B37","#7F1C2E","#AC5754",
    "#790D1D","#FE7082","#A20D44","#B61629",
    "#CE171B","#ED322B","#F776AE","#CE242D",
    "#C91D37","#A63139","#FE52AE","#C91715",
    "#DE2025","#BE161D","#530C1E","#FE82AE",
]

x = np.arange(20)
y = [str(i) for i in np.random.randint(5, 20, 20)]
# Y = NP.RANDOM.RANDINT(5, 20, 20)

fig = px.bar(x=x, y=y, color = y, color_discrete_sequence = ysl)
fig.update_layout(title = "Plotly 自定义离散色彩方案")
```
我们只需要将 color 属性与 y 关联（如果使用 dataframe，这里要指定列名字），并且通过 color_discrete_sequence 参数把我们定义的调色板传入就好。

不过，我们也注意到第 14 行，我们将 y 序列转换成了字符串。如果 y 序列是数值类型，生成的图将不会采用离散色，而是使用连续色阶。

### 5.2. 连续色阶

plotly 中，前面提到的三个包：'sequential', 'diverging', 'cyclical'中的色阶都可以当成连续色阶：

```python
import plotly.express as px
df = px.data.iris()
fig = px.scatter(df, x="sepal_width", y="sepal_length",
                 color="sepal_length", color_continuous_scale=px.colors.sequential.Viridis)

fig.show()
```
此外，它还提供了命名色阶方案：

```python
import plotly.express as px
from textwrap import wrap

named_colorscales = px.colors.named_colorscales()
print("\n".join(wrap("".join('{:<12}'.format(c) for c in named_colorscales), 96)))
```

我们可以直接使用这些名字命名的色阶：

```python
import plotly.express as px
df = px.data.iris()
fig = px.scatter(df, x="sepal_width", y="sepal_length",
                 color="sepal_length", color_continuous_scale="viridis")

fig.show()
```
输出结果与上一段代码完全一致。

使用自定义色阶也很容易。在 YSL 的示例代码中，我们只要将 y 值修改为数值型，将 color_discrete_sequence 参数改为 color_continuous_scale 即可：

```python
import numpy as np

# EXTRACT PALETTES FROM COOLORS.CO

ysl = [
    "#4D0609","#8F2B37","#7F1C2E","#AC5754",
    "#790D1D","#FE7082","#A20D44","#B61629",
    "#CE171B","#ED322B","#F776AE","#CE242D",
    "#C91D37","#A63139","#FE52AE","#C91715",
    "#DE2025","#BE161D","#530C1E","#FE82AE",
]

x = np.arange(20)
y = np.random.randint(5, 20, 20)

fig = px.bar(x=x, y=y, color = y, color_continuous_scale = ysl)
fig.update_layout(title = "Plotly 自定义连续色阶方案")
```
我们可以从 colorbar 看出，由于我们给定的颜色没有严格排序，所以 colorbar 经插值后，出现了明暗相间的现象，而不是常见的单调递增，或者两端分布。

## 6. 主题和模板
plotly 中主题和模板一起，构成了类似 matplotlib 中的 stylesheet 和 rcParams 的概念。在 plotly 中，模板归类在 plotly.io.templates 下：

```python
import plotly.io as pio
pio.templates
```
这会输出 seaborn, simple_white, plotly 等好几种主题。其中 plotly 是默认主题。在许多 px 绘制函数中，都允许指定主题：

```python
import plotly.express as px

df = px.data.gapminder()
df_2007 = df.query("year==2007")

fig = px.scatter(df_2007,
                 x="gdpPercap", y="lifeExp", size="pop", color="continent",
                 log_x=True, size_max=60,
                 template='plotly_dark', title="Gapminder 2007: 'plotly dark' theme")
fig.show()
```
这将显示一个深色背景的图：

![](https://images.jieyu.ai/images/2023/07/lesson17-plotly-dark.png)

## 7. Dash 简介
Dash 为 Python 程序员提供了一种只用 Python 就可以端到端完整构建网页的能力。

!!! Note
    除了 Dash，wave 和 streamlit 也是常见的 Python 全栈构建 web 应用的框架。Wave 的服务器通过 Go 语言开发，在性能上比 Dash 会强不少。但 Dash 功能更全，社区更活跃一点。Wave 还处于比较早期的阶段，但发展潜力比较大，未来可能一些普通的web程序也会使用它来构建。<br>streamlit似乎比dash更简单，目前百度的人工智能社区，就有许多用streamlit构建的交互式应用。<br><br>有一种数学货币也叫Dash（达世币）。如果你在网上只使用Dash来进行搜索，你可能会被导入进达世币的网站。所以，我们一般使用plotly dash来进行搜索。

### 7.1. Hellow World

我们先从一个 Hellow World 开始：

```python
from dash import Dash, html

app = Dash(__name__)

app.layout = html.Div([
    html.Div(children='Hello World')
])

if __name__ == '__main__':
    app.run(debug=True)
```

将这段代码存为 app.py 文件。现在，让我们安装 dash，并运行这个应用：

```
python app.py
```

!!! attention
    注意上述代码可以在Notebook中运行，由于网络限制，不能在大富翁课程实验环境中运行。

首先，我们从dash中导入Dash和html。创建 Dash 应用程序时，我们几乎总是会使用上面的 import 语句。

第3行被称为 Dash 构造函数，负责初始化我们的应用程序。对于我们创建的任何 Dash 应用程序来说，它几乎总是相同的。

第5~7行，app.layout代表了将在 Web 浏览器中显示的应用程序组件，通常包含在 html.Div 中。在此示例中，添加了一个组件：另一个 html.Div 。 Div 有一些属性，例如 children ，我们用它来向页面添加文本内容：“Hello World”。

第9~10行，用于运行我们的应用程序，对于我们创建的任何 Dash 应用程序来说，它们几乎总是相同的。

### 7.2. 连接到数据

向应用添加数据的方法有很多：API、外部数据库、本地 .txt 文件、JSON 文件等等。在示例中，为简单起见，我们使用plotly.data模块提供的数据。

```python
# Import packages
from dash import Dash, html, dash_table
from plotly.data import gapminder
import pandas as pd

# Incorporate data
df = gapminder()

# Initialize the app
app = Dash(__name__)

# App layout
app.layout = html.Div([
    html.Div(children='My First App with Data'),
    dash_table.DataTable(data=df.to_dict('records'), page_size=10)
])

# Run the app
if __name__ == '__main__':
    app.run(debug=True)
```

这将输出以下界面：

![](https://images.jieyu.ai/images/2023/07/lesson17-dash-1.png)

我们几乎立即感受到了dash的强大。我们得到了一个分页显示的表格。尽管在notebook中我们也能把dataframe显示成为表格，但它不能提供分页功能，而且一旦行数和列数超过预设值，就有一些行或者列不会被显示。

这些强大的功能，是由dash_table带来的。它是在第2行代码中被导入的。

第13~14行，这次我们在顶层的html.Div构造时，把dash_table.DataTable加了进来，并且设置了每页显示10条记录。

现在，我们给这个应用增加可视化效果：

```python
# Import packages
from dash import Dash, html, dash_table, dcc
from plotly.data import gapminder
import pandas as pd
import plotly.express as px

# Incorporate data
df = gapminder()

# Initialize the app
app = Dash(__name__)

# App layout
app.layout = html.Div([
    html.Div(children='My First App with Data and a Graph'),
    dash_table.DataTable(data=df.to_dict('records'), page_size=10),
    dcc.Graph(figure=px.histogram(df, x='continent', y='lifeExp', histfunc='avg'))
])

# Run the app
if __name__ == '__main__':
    app.run(debug=True)
```

我们通过plotly.express生成了一个直方图，这部分跟我们之前学过的并无二致；但在顶层的html.Div对象构造时，我们增加了第三项，即一个dcc.Graph。它自动封装了直方图对象。注意，直方图对象是通过figure参数来传递的。

实际上，plotly要实现这里的逻辑非常容易。因为px.histogram返回的是一个Figure对象，这个Figure对象在传递给dcc.Graph时，被串行化为json对象，后者是plotly.js可以识别并绘制的对象。

到目前为止，我们已经构建了一个显示表格数据和图表的静态应用程序。但是，我们使用Dash的真正目标，可能是希望为用户提供与应用程序交互的能力。尽管plotly绘制的图可以进行某种交互，但它提供的输入手段还远远不够。

### 7.3. 增加交互式控件
为此，我们需要使用回调函数向应用程序添加控件。在此示例中，我们将向应用程序布局添加单选按钮。然后，我们将构建回调以创建单选按钮和直方图之间的交互。

```python
# Import packages
import pandas as pd
import plotly.express as px
from dash import Dash, Input, Output, callback, dash_table, dcc, html
from plotly.data import gapminder

# Incorporate data
df = gapminder()

# Initialize the app
app = Dash(__name__)

# App layout
app.layout = html.Div(
    [
        html.Div(children="My First App with Data, Graph, and Controls"),
        html.Hr(),
        dcc.RadioItems(
            options=["pop", "lifeExp", "gdpPercap"],
            value="lifeExp",
            id="controls-and-radio-item",
        ),
        dash_table.DataTable(data=df.to_dict("records"), page_size=6),
        dcc.Graph(figure={}, id="controls-and-graph"),
    ]
)

# Add controls to build the interaction
@callback(
    Output(component_id="controls-and-graph", component_property="figure"),
    Input(component_id="controls-and-radio-item", component_property="value"),
)
def update_graph(col_chosen):
    fig = px.histogram(df, x="continent", y=col_chosen, histfunc="avg")
    return fig


# Run the app
if __name__ == "__main__":
    app.run(debug=True)
```


![](https://images.jieyu.ai/images/2023/07/lesson17-dash-control.png)

这一次的代码中，我们依然导入了dcc。这次除了使用Graph之外，我们还使用了单选按钮组件dcc.RadioItems。在第18到22行中，我们生成这个控件。为该控件提供了三个选项（options），当前取值为"lifeExp"，因此一开始时，这个按钮将被选中。最后，我们给这个控件一个id。每一个网页元素都可以有一个ID，这个ID在一个html网页中必须唯一。

!!! attenttion
    注意这一次，我们给gcc.Graph控件也增加了ID属性。另外，我们在第24行，构建了一个空的图对象。它的内容，将通过radio button的选择来确定。

当用户点击Radio button之后，需要服务器作出响应，从而更新页面显示。Dash通过callback模块和回调中常用的两个参数Input/Output来连接前端应用程序与后台服务程序。

第29行到第35行，我们定义了一个名为update_graph的函数。它的作用正如名字暗示的那样，更新直方图。更新直方图的依据是当前用户选择的新的radio button的属性值 -- 实际上是对应的dataframe的列名字，在这里被记为col_chosen。它的输出是一个Figure对象。

现在唯一的问题，就是如何将radio button的输入值传递给col_chosen参数，以及如何将update_graph函数返回的Figure对象，传递给gcc.Graph。这个机制比较复杂，我们需要知道的是，通callback机制和Input/Output组件， Dash就将这一切联系起来。

!!! Note
    这个机制可以大致描述为，Dash维护了一个由浏览器到服务器的websocket通信，并且根据callback的定义，在前端监听注册的Input/Output组件的值的变化。当Input组件中，某个被注册的属性（比如这里radio button的value属性）发生改变时，就把这个控件的id、属性名和属性值通过websocket传递给服务器，最终进入到第29行的callback中。callback对输入值进行解码，然后调用我们的update_graph函数，并且把它生成的Figure对象串行化后，将输出对象的ID、属性名及属性值（即这里的controls-and-graph, 'figure'和Figure.to_json())通过websocket返回给前端，最终完成对图对象的更新。

    得益于plotly.js之前打下的基础，Dash实现这些工作，工作量相对比较少。

在Input/Output组件定义中，最重要的就是compoent_id, component_property。Dash通过它来定义要监听（和修改）的控件属性。


### 7.4. 美化应用程序

Dash中的每一个html控件（即在模块html之下的组件）都可以设置style属性，这些属性将在最终生成控件时，被绑定到html元素的style属性上。此外，html元素的class属性可以通过className指定（Python中class是关键字），html元素的其它属性，只要不与Python关键字、Dash定义的组件的某些保留字冲突，都可以使用。

示例如下：

```python
# Import packages
import pandas as pd
import plotly.express as px
from dash import Dash, Input, Output, callback, dash_table, dcc, html
from plotly.data import gapminder

# Incorporate data
df = gapminder()

# Initialize the app - incorporate css
external_stylesheets = ["https://codepen.io/chriddyp/pen/bWLwgP.css"]
app = Dash(__name__, external_stylesheets=external_stylesheets)

# App layout
app.layout = html.Div(
    [
        html.Div(
            className="row",
            children="My First App with Data, Graph, and Controls",
            style={"textAlign": "center", "color": "blue", "fontSize": 30},
        ),
        html.Div(
            className="row",
            children=[
                dcc.RadioItems(
                    options=["pop", "lifeExp", "gdpPercap"],
                    value="lifeExp",
                    inline=True,
                    id="my-radio-buttons-final",
                )
            ],
        ),
        html.Div(
            className="row",
            children=[
                html.Div(
                    className="six columns",
                    children=[
                        dash_table.DataTable(
                            data=df.to_dict("records"),
                            page_size=11,
                            style_table={"overflowX": "auto"},
                        )
                    ],
                ),
                html.Div(
                    className="six columns",
                    children=[dcc.Graph(figure={}, id="histo-chart-final")],
                ),
            ],
        ),
    ]
)

# Add controls to build the interaction
@callback(
    Output(component_id="histo-chart-final", component_property="figure"),
    Input(component_id="my-radio-buttons-final", component_property="value"),
)
def update_graph(col_chosen):
    fig = px.histogram(df, x="continent", y=col_chosen, histfunc="avg")
    return fig


# Run the app
if __name__ == "__main__":
    app.run(debug=True)
```
在上面的例子中，我们通过第11，12行定义并加载了外部css样式表（这可以经由设计师设计），在第18行、23、37，47等行中使用了样式表中定义的row和six columns class；我们还在第20行，直接给元素定义了内联样式，指定了文本左对齐、字体使用30px及蓝色等。

html的radio button有inline属性，我们在第28行也进行了指定。现在，三个radio选项将呈一行三列排列，而不是象之前那样，成三行排列。

### 7.5. 深入Dash
考虑到我们这门课毕竟是量化课程，深入研究Dash似乎已经超出了这门课的范畴。如果读者希望进一步研究，可以思考以下问题，并在dash的github及社区中找到答案：

1. 示例只演示了单输入的情况。但很多时候，界面的改变尽管只由一个输入控件触发，但要生成新的输出，却需要其它控件的值，这种情况下，我们如何得到其它控件的属性值？
2. 示例只演示了单输出的情况。但很多时候，某个控件值的改变，可能会导到多个输出控件的改变，比如，如果我们通过Dash构建了一个交易界面，当用户点击持仓股时，应该将当前持仓卖出，此时，需要用持仓股名字、代码、持仓数、当前价格等好几个属性填充到输出控件中。此时应该如何实现？
3. 如果不是由用户驱动，而是后台发生的某些事件来驱动，比如，每分钟我们获取到新的行情数据，需要对分析图进行自动更新，Dash的机制可以实现吗？此时，不存在输入控件，Dash的callback能否支持？

还有很多其它问题。总之，对简单的图表分析任务，Dash为仅仅使用Python语言进行编程的研究员提供了一种可能性，但经验表明，如果你的网页程序有可能超过10页以上，或者每页的控件超过10个以上，你或许应该求助专业的开发人员，使用更强大的前端开发语言来完成。

关于Dash内部机制的一些研究，可以参考[Dash: 核心概念、路由、Auth与Pitfall](https://zhuanlan.zhihu.com/p/645188443)这篇文章。这篇文章成文于一年前，当时我们为大富翁的交易界面寻找一种完全基于Python的实现方案，对Dash进行了尝试。后来我们又尝试了Wave，最终，我们改用前后端分离模式，使用Python服务器 + Vuejs来完成了大富翁的UI。
