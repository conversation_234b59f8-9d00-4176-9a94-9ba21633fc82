---
title: 01 课程导论
date: 2024-01-21
mainfont: WenQuanYi Micro Hei
puppeteer:
    format: "A4"
    scale: 1
    margin:
        top: 2.5 cm
        right: 2cm
        bottom: 2.5 cm
        left: 2cm
    displayHeaderFooter: true
    headerTemplate: '<div style="width:100%; text-align:center; border-bottom: 1pt solid #eeeeee; margin: 20px 10px 10px; font-size: 10pt;padding-bottom:10px"><span class=title></span></div>'
    footerTemplate: '<div style="display:flex；justify-content:space-around;width:100%;border-top: 1pt solid #eeeeee; margin: 10px 10px 20px; font-size: 8pt;color:#aaa"><div style="width:30%"><span class=pageNumber></span>/<span class=totalPages></span></div><div style="width:30%">大富翁量化 24 课</div><div style="width:30%">助教：宽粉（quantfans_99)</div>'
---
- [1. 证券投资和量化交易的发展](#1-证券投资和量化交易的发展)
- [2. 量化交易知识体系](#2-量化交易知识体系)
- [3. 什么样的人适合做量化？](#3-什么样的人适合做量化)
- [4. 量化策略浅探](#4-量化策略浅探)
  - [4.1. Alpha 策略](#41-alpha-策略)
  - [4.2. 市场中性策略](#42-市场中性策略)
  - [4.3. 高频套利策略](#43-高频套利策略)
  - [4.4. 技术分析类策略](#44-技术分析类策略)
  - [4.5. 策略研究方法](#45-策略研究方法)
- [5. 课程内容简介](#5-课程内容简介)
- [6. 如何学习本课程](#6-如何学习本课程)
  - [6.1. 知识储备](#61-知识储备)
  - [6.2. 在线实验环境](#62-在线实验环境)
    - [6.2.1. Python 包的管理](#621-python-包的管理)
- [版权声明](#版权声明)
- [Footnotes](#footnotes)


什么是量化交易？**量化交易是指利用计算机程序来分析、预测证券市场走势并进行交易的方法**。在量化分析中，投资者使用计算机编写程序，以历史市场数据为基础，通过建立模型来判断市场趋势和进行投资决策。其优势是快速处理大量信息、减少主观因素的干扰。**量化交易的本质特征是，在执行交易前，对该笔交易可能产生的风险和收益都有一个明确的数学期望**，而不是像主观交易那样：尽管我们也可以做出收益预估，但无法同时确定收益的幅度、时间和实现概率。

量化分析使用了统计分析、时间序列分析、机器学习、人工神经网络等技术。这些技术可以帮助投资者高效地从海量的信息中找出资产价格波动的规律。

在学习这门课程之前，很多人会有这样的疑问，什么是量化交易，它在证券投资中处于什么样的地位，又是怎么发展起来的？量化交易又有哪些交易策略？

这就是导论要回答的问题。除此之外，我们还将介绍这门课程的主要内容，以及如何进行学习等等。

## 1. 证券投资和量化交易的发展

我们首先简要地介绍全球证券投资和量化交易发展史。了解这段历史，特别是了解各种理论流派出现的时间顺序、他们要解决的问题及遇到的困难，对于我们打开量化投资的黑箱，拨开量化投资的迷雾，明确量化交易的未来发展方向，会有不小的帮助。


<div style="position:relative;float:right">
<img src="https://images.jieyu.ai/images/2024/01/first-security.jpg" align="right" style="width: 120px;margin:10px">
</div>

人类的证券交易活动开始于 1606 年，即我们的明朝万历年间。第一家上市公司是荷兰的东印度公司，第一张股票（见右图）发行于荷兰的阿姆斯特丹证券交易所。人类历史上第一次疯狂的金融投机活动，郁金香泡沫，也发生在这一时期的阿姆斯特丹。

尽管现代意义上的量化交易的出现较晚，但人类对资产证券进行量化分析大概可以追溯到 19 世纪。

<div style="position:relative;float:left">
<img src="https://images.jieyu.ai/images/2024/01/regnault.jpg" align="left" style="width: 120px;margin:10px">
<p style="font-size:10px;text-align:center">朱尔斯.雷格纳</p>
</div>

朱尔斯·雷格纳特算可能是量化分析的第一人。他出生于 1834 年，在巴黎证券交易所担任经纪人助理期间，他对股票价格数据进行了大量的数据分析，发现股票价格涨跌的背后隐藏的规律，实现了个人的财富自由。后来，他把这些分析方法写成了《概率计算与股票交易哲学》一书。雷格纳特在两个世纪之前，就以自己的实践证明了量化分析在证券交易中的重要价值。

1882 年，美国人、记者查尔斯.道创办了道琼斯公司，并开始编制道琼斯指数。最初道琼斯共有三类指数，不过到今天还在使用的只有道琼斯工业平均指数。道.琼斯对股票市场发表了一些看法，后来被称为道氏理论。

!!! note
    道氏理论主要有四点。一是市场平均价格指数可以反映市场的大部分行为。二是市场趋势可以划分为主要趋势、次要趋势和短暂趋势。这为后面出现的波浪理论打下了基础。三是成交量在趋势的确定中有很重要的作用。四是收盘价是最重要的价格。<br><br>尽管道氏理论看上去不够学术化，但它的重要性和正确性不容置疑。这四点结论，直到今天，无论是在主观投资还是在量化投资中，都在广泛使用。


法国数学家路易斯。巴什利耶是第一个将高等数学应用于金融研究的人，1900 年，他在博士论文中，介绍了布朗运动的数学模型，将股票的价格变化当成随机过程，提出了价格变化服从鞅过程的结论，第一次为股票期权的估值建立了数学模型。该模型也启发了 BS 公式的诞生。

<div style="position:relative;float:left">
<img src="https://images.jieyu.ai/images/2024/01/shannon.png" align="left" style="width:120px;margin:10px">
<p style="font-size:10px;text-align:center">信息论之父 香农</p>
</div>

1916 年，信息论之父，克劳德.香农，发明了网格交易法。网格交易法具有不择时的特点，是重要的套利和做市策略之一。如果能找到合适的标的，收益风险比很高。

我们将在第 8 课介绍网格交易法，并通过程序来实现这一模型。并且，在优化那一节中，给出了如何寻找网格交易法合适的标的思路。

1930 年代，经济学家威廉姆斯论证了股票价格由其未来的股利决定，提出了现金流贴现模型。直到今天，许多投资者使用的基于未来的现金流给公司定价的模型，就起源于威廉姆斯的内在价值公式：

$$
P = \frac{D_1}{1+r_1} + \frac{D_2}{(1+r_2)^2} + ...+ \frac{D_t}{(1+r_t)^t} +  \frac{P_t}{(1+r_1)^t}
$$

这里$P$是股票的理论价值，$D_t$表示第$t$年的预期股息，$P_t$则表示第$t$年的预期价格，$r_t$则表示第 $t$ 年的贴现率。


1938 年，经济学家 Ralph Nelson Elliott（艾略特）与 Charles J.Collins（柯林斯）发表了 Wave Principle（波浪原理）一书，指出虽然股市价格可能看起来是随机且不可预测的，但它们实际上遵循可预测的自然规律，并且可以使用斐波那契数来衡量和预测。

从 20 世纪中叶开始，哈理.马科维茨、夏普、米勒等人开始为微观金融学的大厦打下基础。资产定价、公司财务、金融市场及机构共同构成了现代金融学的三大核心研究领域 [<sup>1</sup>](#吴晓求)。其中资产定价理论对量化交易的推动尤其巨大。

<div style="position:relative;float:right">
<img src="https://images.jieyu.ai/images/2023/10/markvoz.png" style="width:120px;margin:10px">
<p style="font-size:10px;text-align:center">哈理.马科维茨</p>
</div>

1952 年，哈理.马科维茨发表了现代投资组合理论。它用数学术语描述了多元化和风险管理等概念，为投资者提供了构建多元化投资组合的工具集。它假定投资者投资于多个资产，在满足给定预期回报率下，可以通过优化求解出风险最小的投资组合。所有的风险收益比最优的资产组合构成一条曲线（以资产组合的标准差为横轴，预期回报率为纵轴），称为前沿资产组合曲线，其中曲线的上半部分又被称为有效前沿。

哈理.马科维茨于1990年获得得诺奖。现代投资组合理论（MPT）直到今天，还在风险管理中普遍得到使用。

在补充阅读材料中，我们完整地实现了 MPT 和有效前沿理论。这些代码可以在自己的资产组合管理中使用。


<div style="position:relative;float:left">
<img src="https://images.jieyu.ai/images/2023/10/william-sharpe.jpg" style="width:120px;margin:10px">
<p style="font-size:10px;text-align:center">威廉.夏普</p>
</div>


在马科维茨那个年代，计算能力严重不足，因此，MPT 理论难以在实际中得到应用。于是威廉.夏普运用均值 -- 方差 -- 协方差的概念及求极值的思想，推导出 CAPM 模型，大大简化了投资组合的选择过程。1990 年，威廉.夏普与他的老师马科维茨一起获得诺奖。关于均值、方差和协方差等基本概念，我们将从第 11 课起进行介绍。

CAPM 模型，在我们的课程中也有使用说明和代码示例，也在补充材料目录下。CAPM 理论首先提出了将资产收益分解为市场暴露、组合收益和无风险收益三大来源，可以认为是现代多因子策略的发端。

威廉.夏普的另一大贡献则是今天仍在广泛使用的 Sharpe Ratio。它是评估策略表现的重要指标之一。

$$
Sharpe Ratio = \frac{R_p - R_f}{\sigma_p}
$$

我们会在第 21 课，介绍策略评估指标时再详细介绍这个公式。

1956 年，约翰·拉里·凯利在《贝尔系统技术期刊》中发表了凯利公式 (Kelly formula)。将凯利公式应用于多次的随机赌博游戏，资金的期望增长率最高，且永远不会导致完全损失所有资金的后果。MIT的数学教授爱德华.索普最先在赌场里运用这个公式，横扫全美赌场，最终被所有赌场列入禁入名单。



随后，他于 70 年代转战资本市场，成立了自己的投资公司，在 1969 年到 1988 年之间没有出现过任何一年亏损。爱德华.索普的故事最终被拍成电影《决胜 21 点》。

!!! note
    凯利公式本身并不是关于投资的一个公式，它本来是用来协助规划电子比特流量设计的。它在投资上的成功，也说明跨界融合对于策略研发的重要性。另外，我们也要注意一个残酷的事实，凯利公式在赌场上是无往不胜的，但在资本市场上却非如此。有人可能会因此得出结论，资本市场还不如赌场。<br><br>关于爱德华.索普，流传的另一则轶事是，他坚持认为他最早发现了 BS 模型，但是为了赚钱，他没有发表这一模型，而是把它用在一资本市场。<br><br>我们常常会问，如果真有赚钱的量化策略，你会公布出来吗？也许凯利公式和 BS 公式，已经给了我们答案。

1963 年，金融技术作家 Joseph Granville（约瑟夫·格兰维尔）在《Granville's New Key to Stock Market Profits》这本书中，推广了 OBV（on-balance-volume）这个指标。OBV 指标可以看成是对道氏理论中关于成交量论述的一个应用。在技术分析指标中，多数指标都是基于价格进行构造的，OBV 指标则完全是另外一个维度，因此它必然能给我们的策略系统增加新的信息。这是我们在这里介绍它的原因。

!!! note
    即使是基于价格，我们也可能构建出完全属于不同维度的因子。比如基于统计和基于时序的因子往往是不同的维度。


<div style="position:relative;float:left">
<img src="https://images.jieyu.ai/images/2023/10/Eugene_Fama.jpg" style="width:120px;margin:10px">
<p style="font-size:10px;text-align:center">尤金.法马</p>
</div>

1970 年，尤金.法马提出有效市场假说 (EMH)，这是现代微观金融学的理论基石，也是 MPT、CAPM 等理论成立的基础假设。尤金.法马还在 1993 年提出著名的三因子模型。他于 2013 年获得了诺奖。我们将在第 6 课介绍三因子模型中的小市值因子。这也是包括 A 股在内，全球市场上普遍生效的因子。

1973 年是量化金融理论的一次高峰。在期权交易诞生近 300 年之后，通过布莱克.费雪和迈伦.斯科尔斯，人类第一次给出了期权定价公式即 BS 公式。

<div style="display:flex;">
<div style="flex:50%; padding-left: 20%">
<img src="https://images.jieyu.ai/images/2023/10/Scholes.png" style="width:150px"/>
<p style="font-size:10px;text-align:center">斯科尔斯</p>
</div>
<div style="flex:50%; padding-right: 20%">
<img src="https://images.jieyu.ai/images/2023/10/merton.png" style="width:150px"/>
<p style="font-size:10px;text-align:center">默顿</p>
</div>
</div>

这是个被称为价值十万亿的公式。一经发表，就立即被芝加哥应用到新的期权交易中。后来罗伯特.默顿又对此公式进行了改进，因此他与斯科尔斯一起，斩获了 1997 年的诺奖（布莱克两年前去世，因而未能获得诺奖）。这个公式在数学上使用了伊藤引理，伊藤引理的重要性是给出了对随机过程进行微分的框架。这个领域也是量化分析中对数学要求最高的一个。



!!! note
    在一些介绍量化交易的传奇类书籍中，从巴舍利耶到 BS 公式这段激荡人心的故事往往是大书特书的对象。但是，我们也要明白，量化交易可用于证券市场的各个标的，而不仅仅是期权交易。因此，只介绍期权交易的量化交易史是不完整的。

CAPM 在 20 世纪 70 年代之后受到了很大的挑战。一些学者开始从不同的角度建立新的理论模型，来解释 CAPM 不能解释的现象。其中最具影响力的是套利定价理论 (APT)。

1976 年，罗斯提出套利定价理论，与 CAPM 相比，它需要的假设少得多。APT 在更广泛的意义上，建立了证券收益与宏观经济中其它因素的联系，并且其套利均衡的思想，为将来期权定价的推导提供了重要的思想武器。

1978 年，Welles Wilder 发表了《技术交易系统新概念》一书，提出了 RSI 等多个重要的技术指标。直到今天，RSI 仍然有效，并且对它的研究和改进从未停止。

MPT、CAPM 和有效市场假说相互支持，体系完备，而 BS 公式横空出世，更是令万人瞩目。一时间，似乎金融学这座宏伟的大厦已经建成，后面的工作，无非是裱糊装饰而已。但谁曾想，在这座大厦的上方，乌云正在聚集。人们开始注意到，一些传统理论无法解释的市场异象越来越多，并且再也无法视而不见。

这些 CAPM 和有效市场假说无法解释的异象很多，这里仅举几例。



一是长期反转与中期惯性现象。邦特 (De Bondt) 和塞勒 (Thaler, 行为金融学家，2017 年诺奖）发现，过去三年表现最好的股票，会在未来 5 年表现欠佳；而过去三年表现最差的股票，会在未来 5 年表现较好。这被称为长期反转。杰格迪什和泰特曼则发现，平均而言，过去 3~12 个月赚钱的股票组合，在随后 3~12 个月仍然表现较好，这就是所谓的中期惯性。

!!! note
    长期反转与中期惯性在宏观层能得到比较好的解释。处在顺周期行业的公司，由于产品在市场上短缺，利润丰厚，这种情况会持续一段时间。随着扩大生产和外部竞争者也跟随进入，导致利润率下降，最终行业和公司进入逆周期。

二是日历效应。实证研究发现，一周之中，总有一些日期取得正收益的概率高，而另一些日期取得负收益的概率高 -- 在 A 股，一般是周一更高，周四是最差。而一年之中，1 月份股票收益率相对其它月份最高 -- 在 A 股，这被称为春节效应。

三是股权溢价。梅拉和普雷斯科特发现，股票比国库券的年收益高出 7%。这一收益无法由 CAPM 及其后继理论解释，被称为股权溢价之谜。

此外，还有投机性泡沫。按照理性人和有效市场假说，当资产价格过度高于其基本价值时，资产将被抛售，从而实现价值回归，因此价格无法长期过度单向偏离价值。



但是，无论是 1636 年的郁金香泡沫，还是 1720 年的南海泡沫，以及当代的日本泡沫经济、2002 年的互联网泡沫等等，都说明有效市场假说存在较大的局限性。

有效市场假说是现代微观金融理论大厦的基石。1976 年，格罗斯曼和斯蒂格利茨（2001 年诺奖）对其发动了致命一击。他们在发表的论文《信息与竞争性价格体系》中提出了**格罗斯曼–斯蒂格利茨悖论**：如果均衡价格完全揭示了私人信息，那么交易者都有“搭便车”的动机，从而不愿支付成本来收集私人信息；而如果全体交易者都不收集私人信息，只想从价格中推测信息的话，那么价格就没有什么信息可汇总和传递；如果大家都将不收集信息视为共识，那么收集信息就会产生超额信息，因此又有了个人搜集私人信息的动力。这就形成了悖论。

有效市场假说的基石地位一旦受到挑战，现代金融学这座金碧辉煌的大厦瞬间就进入了风雨飘摇之中，现代金融学不得不寻求新的理论庇护。

于是，1979年起，卡尼曼与特维斯基合作提出了前景理论（Prospect theroy）。它使用通过损益来定义的价值函数替代预期效用函数来定义决策权重，即认为投资者是通过最大化收益来进行决策，从而将人的投资行为模型化，为行为金融学的发展奠定了基础。长久以来，主流经济学都假设每个人作决定时都是“理性”的，然而现实情况并不如此；展望理论加入了人们对得失、发生概率高低等条件的不对称心理效用，成功解释了许多看来不理性的现象。



<div style="display:flex;">
<div style="flex:50%; padding-left: 20%">
<img src="https://images.jieyu.ai/images/2023/10/Daniel-Kahneman.png" style="width:150px;"/>
<p style="font-size:10px;text-align:center">卡内曼</p>
</div>
<div style="flex:50%; padding-right: 20%">
<img src="https://images.jieyu.ai/images/2023/10/richard-thaler.png" style="width:150px"/>
<p style="font-size:10px;text-align:center">塞勒</p>
</div>
</div>

1980 年起，理查德.塞勒在行为金融学方面频频提出重要见解，包括心理账户，损失厌恶，禀赋效应等等。行为经济学起初是不被主流经济学家所接受的，前景理论的提出者卡尼曼也只是一位心理学家，而非经济学家。由于塞勒的大力推动，行为经济学今天已成为一门显学，前景理论论文被引用超过 8 万次，是经济学领域被引用的次数最多的论文的前三名。塞勒于 2017 年获得了诺奖。他还在电影《大空头》中扮演了自己。

1980年代，John Bollinger 发明了被称为布林带的技术分析工具。基于布林带的策略在 80 年代一度非常成功， Bolling Bands 还被注册成为商标。John Bollinger 是 Bollinger 资本创始人，CFA 和 CMT。

1994年，谢夫林 [<sup>2</sup>](#shefrin) 和斯塔特曼发现了《行为资本资产定价模型》（即 BCAPM），为行为金融学研究打下一个全面广泛的理论框架，包括行为均值方差效率理论、行为期权价格理论和行为利率期限结构理论等等。1999年，两人又挑战资产组合理论，提出了行为组合理论。



个人认为，以 CAPM 为代表的金融理论，与行为金融学为代表的金融理论，最大的差别在于是否存认价值的主观性。CAPM 作为一种定价模型，它的根基在于认为价值是客观的，因而是可以被客观定价的。这也是一些经济学流派立论的基础。

但是，价值真的是客观的吗？**甲之蜜糖，乙之砒霜**。普通食盐只要 2 元一袋，而夏威夷红火山盐一瓶则要卖近 200 元，是因为它能产生 100 倍的钠离子浓度呢，还是因为我们的主观感受和心理因素作用，从而我们愿意为之付出 100 倍的溢价？

!!! note
    也许，要回答价值是否是客观的，就要追问到发展经济的根本目的：人是发展经济的目的，还是发展经济的手段？如果人是发展经济的目的，那么价值的判断就要以是否满足人的需求为标准，因而就是主观的。

行为金融学近年来在量化中的应用越来越多，一些常见的行为金融概念，包括损失厌恶、共识偏差、熟悉倾向、心理账户、羊群效应、锚定效应、自我归因等，对中高频量化中一些现象的解释非常有效。

实际上在一些市场里，价格的决定主要是由多空双方的博弈，而非公司的内在价值决定。在这种情形下，我们使用行为金融学的理论来解释和预测价格的波动就更为合理。比如追涨杀跌、整数关口、缺口、日历效应等诸多有效的操作手法，归根结底是交易者的心理因素在起作用。长期看行业和公司治理，短期看心理。



与量化交易相关的理论我们就介绍到这里。我们既介绍了名门正派、特有范儿 [<sup>3</sup>](#paradigm) 的微观金融学理论，也介绍了颇有江湖感的道氏理论、波浪理论和一些技术分析指标。

!!! note
    还有一些理论，比如神奇九转（它的提出者 Tom DeMark 是技术分析大师，长期以来为著名的 Point 72 公司提供咨询），缺口理论，整数价格等等。量化的好处是，可以把这些因素都同时考虑进来。

每一种理论，都有它的信众。资本市场是一种赤裸裸的“金钱民主”，每份资金都在为自己的信仰投票。在这些理论共同选择看多的时候，价格就更有可能上涨；相反，价格则有可能下跌。因此，我们既不能因为某一种理论不符合大众认可的科学范式而拒绝它，也不能因为某一种理论曲高合寡、假设条件无法满足就拒绝它。关键在于，看有多少资金相信这些理论。我以为，这就是行为金融学的研究态度。

!!! note
    因为这样的原因，也由于课程篇幅及编著者水平局限，这门课程在取材上，在跟策略相关的部分，除了会介绍从 CAPM 一路走来的因子分析方法之外，也会注重对技术分析（拓展版）的介绍。根据 2023 年私募路演报告分析，当下私募最赚钱的策略，还是以泛技术类为主，即动量和反转为主。因此，我们这种安排，也是在有限的课程里，优先满足学员未来的需要。<br><br>我们对技术分析的看法已经超出了经典技术指标的运用。我们把以量价数据为基础的时间序列分析都称之为技术分析，也就是我们说的拓展版。



说到量化交易的历史，除了现代金融理论的发展之外，不得不提到的就是电子化交易的进展。1986年，敦证券交易所启用 SEAQ，开启了电子交易时代。两年后，美国证券交易委员会（SEC）也批准了电子交易。这样一来，量化交易的另一翼，即程序化交易也装上了，从此就开启了高频交易时代，又出现了许多适应高频交易场景的算法和模型。直到今天，高频交易仍然是市场上的不败神话。

国内的量化交易史可以追溯到 2005 年 8 月。当时证监会批准了首只以量化投资策略为主的股票型基金——嘉实元和稳健成长证券投资基金成立。2014 年，中国证券投资基金业协会发布了《量化投资基金管理办法》，这是 A 股量化投资行业的重要里程碑，标志着该行业进入了规范化阶段。同时，随着科技的进步和数据的积累，出现了越来越多的量化投资公司和基金。

今天，国内证券市场的量化渗透率在 30%左右，国外量化渗透率大约在 95%上下，这一数据表明，当下是进入量化行业最好的时机。

## 2. 量化交易知识体系

量化交易是一门交叉性学科，数学和计算机知识是基础，也需要掌握一些金融、财会和相关法律法规知识。

我们按照对这门学科的知识结构进行了梳理和分类。最下面的是基础。中间是多数从事量化交易和研究的人，从事日常工作所需要掌握的知识和技能。最上面一层，则是我们深入研究，培养自己的核心优势可能需要掌握的一些技能。



<div style="width:100%;text-align:center">
<img src="https://images.jieyu.ai/images/2023/10/cheese-course-roadmap.png" style="width:80%;margin: 10px auto;">
</div>

对于学习这门课的学员而言，在开始之前，需要掌握基本语法、pip 包管理和简单的代码调试能力。

这里提到的其它 Python 基础和编程工具，在你正式开始工作之前，也最好熟悉掌握它们。欲善工事，先利其器。无论你未来是从事量化策略研究，还是量化系统开发，都会有大量的时间花在查错上。建立良好的测试习惯、培养自己 trouble shooting 的能力，对提升工作效率非常有帮助。

关于数学部分，如果你是大学理工科，特别是概率与统计，对于完成本课程就足够了。



当我们学完这门课之后，就能自己搭建一个量化研究环境，进而开始量化研究了。此时，可以根据自己的特长和兴趣，选择一个方向，深入研究，以培养自己的核心优势。

我们这门课程中的示例都以 A 股为基础。之后，您可以根据自己的实际情况，确定主攻的品种。每一个领域都有自己的领域知识、交易规则，也有自己领域的经验常数，对研究者的技能要求也不一样。

比如，一般情况下，我们做相关性分析，会要求相关系数接近 1；但在因子分析中，这个数值会小到惊人的 0.02。只要数值大于 0.02，我们就考虑这个因子，而如果数值大于 0.05，我们就会认为因子表现很好，这就是不同的领域，有着不同的经验常数的一个例子。

我们在前面讲到，BS公式被用于期权投资，但它对数学的要求比较高。为了击败市场中的其它竞争者，您不可能满足于只做一个公式的运用者，而是要有能力发现公式的边界条件，找到它生效和失效的区间。

再比如，很多人觉得期货容易做，上手就赚钱，因而忽略了对交易规则、技术实现细节等方面的深入研究，从而导致最终一把亏完。
    
从技能上看，做期权这一类衍生品投资，一般对数学的要求比较高，而股票和期货的 CTA 策略几乎适合所有人入门。如果您有人工智能背景，也可以考虑向机器学习、强化学习等方向进行研究。

## 3. 什么样的人适合做量化？

这里也需要指出，在证券投资领域，特别是说到赚钱能力，并不存在显著的智力优势一说，或者数学好才能进行研究，等等。一些大型机构为了募资的原因，往往会夸大自己团队的学历含金量。但实际上，在绝大多数领域，都不并是最聪明的人最赚钱。

历史上最聪明的人，可能莫过于牛顿。天不生牛顿，万古如长夜。在1720年时，牛顿拥有一些南海公司的股票，这是当时最炙手可热的一家公司。看到股票市场正在失去理智，他果断地清空了所有股票，赚了7000镑，回报率高达100%。然而，仅过了一个月，在市场情绪的感染下，他又不得不以更高的价格买回这支股票，结果赔了2万英镑。此后，再也没有涉足过股票市场。对于这件事，他还留下一句名言，我可以计算出天体的运动，却无法计算人心的疯狂。

即使在量化领域也是如此。1998年，长期资本管理公司进行了一场豪赌，赌债券市场将回归”正常“状态，结果在几个星期内就损失了20亿美金。最终，这家公司不得不倒闭，还几乎使得全球的金融体系倾覆。这家公司的策略是统计套利、配对交易套利。他们并不缺数学家、高级程序员和算法。它的管理者包括了因BS公式获得诺奖的罗伯特.默顿和另一位诺奖获得者，麦伦.休斯。可以说，没有人比他们更懂量化、更懂期权投资了。

那么，做量化的人，究竟需要什么样的条件呢？对所有人来讲，要成为某个行业的专才，首先都适用一万小时定律。我们只有持之以恒地专注某个方面进行学习和探索，才能提升自己的认知和价值。赚钱最终只不过是自身的价值变现 、认知识变现。这一万小时，必须是高效地、有计划学习的一万小时，因此，往往只有热爱量化、热爱数据和能忍受孤独的人才能坚持下来。

!!! quote
    _**Not everyone can become a great artist, but a great artist can come from anywhere**_
    <br>_不是每个人都能成为伟大的艺术家，但伟大的艺术家可能来自任何地方。 -- 迪斯尼电影 《Routouline》_


至于才能，肯定也是重要的，只不过，它往往不是我们看到的外在的那些东西：学历证书、编程技巧或者数学能力。在电影《奥本海默》里，奥本海默担心他的数学不够好，不够条件学习量子物理时，玻尔告诉他：

!!! quote
    Algebra is like sheet of music.The important thing isn't can you read music, it's can you hear it.Can you hear the music, Robert?<br>数学就像音乐。最重要的不是你能否看懂五线谱，而是你能否听懂音乐。

对于量化交易也是一样。数学只是量化交易的五线谱。核心是你能否读懂股市波动的原因。关于编程，编程当然很重要，但普通人经过刻意的练习，都能掌握。
    
## 4. 量化策略浅探

!!! tip
    本课程不是讲解策略为主的课程。策略的研发属于知识体系中的进阶研究。但是，我们也在此略为介绍一下。

我们很难对量化交易策略进行一个系统和完备的分类。从前面的介绍可以看出，不同的投资标的，所能使用的投资策略有相通的，也有不同的。

策略分类标准很多，分类边界之间相互渗透进入，而且新的策略还在不断探索和发现中。这里谨给出我们自己的看法。

### 4.1. Alpha 策略

根据 CAPM 理论，资产的收益由无风险利率、市场暴露和 Alpha 共同组成，如果通过对冲将系统性风险进行度量和隔离，就可以获得超额绝对收益。



获取阿尔法收益的投资策略有很多种，其中既包括传统的基本面分析选股策略、估值策略、固定收益策略等等，也包括利用衍生工具对冲掉贝塔风险、获取阿尔法收益的可转移阿尔法策略。

### 4.2. 市场中性策略

对冲（市场中性）是通过**同时做空和做多多个相关**金融资产的交易策略，其目标是**减少或消除市场风险**。又可细分为套期保值、配对交易和期权交易等。

对冲策略是大型投资机构的必备策略。全球排名靠前的基金公司多数是对冲基金，包括桥水、文芝复兴等等。从 2023 年国内私募的路演报告来看，对冲策略是他们资产配置的一个重要方面。

### 4.3. 高频套利策略

高频套利是一种抢帽子的策略。它又有做市、跨交易所套利（数字货币）等种类。我们这里介绍一个做市策略。假设有下面的委买委卖单（order book）：

<div style="width:100%;text-align:center">
<img src="https://images.jieyu.ai/images/2023/03/20230308155751.png" style="width:400px;margin: 10px auto;">
</div>

在这个委买委卖单中，是不可能成交的。但是，如果有一个做市商，同时下了如下的一个买单和卖单：

<div style="width:100%;text-align:center">
<img src="https://images.jieyu.ai/images/2023/03/20230308163339.png" style="width:400px;margin: 10px auto;">
</div>

这样他就以 9.8 元的价格持有了该品种的 5 手股票。如果有人想主动买入该品种，无疑该做市商卖一的 5 手最先成交，因此，在这样一笔交易中，该做市商就获得了接近 2%的收益（不算手续费）。当然这里有一个前提，就是股价不能大幅波动。如果股价大幅向下波动的话，这笔做市单就会赔掉。此时，算法一般会根据风控要求，在第一时间小亏出局。

这里讲的，只是最简单的高频套利，它只利用了公开信息。还有利用交易所报价协议，从未公开信息中进行套利的策略。此外，还有跨交易所的价差套利，这在数字货币领域是比较常见的。

高频套利本质上是同时挂买单和卖单，中间赚差价。高频套利策略简单易懂，确定性高，风险较低，但它对速度要求很高。现在做这项套利的，一般都用上了 FPGA，还把计算中心搭建在交易所旁边，以期获得最小的网络延时。

!!! tip
    关于高频交易，可以参见《高频交易--华尔街的速度游戏》一书。尽管高频交易是市场上的不败神话，但它投入大，资金容量小，已经不见得是一门好生意。<br><br>另外，现在也有高频因子低频使用的做法。在订单数据（高频）中，往往隐藏了普通行情数据中看不到的一些信息，这些信息有可能揭示主力攻击意图，因此即使不做高频交易，也可以提取出来，与其它中频因子一起使用。

### 4.4. 技术分析类策略

我们把趋势跟踪、动量策略和均值回归等策略统一归类到技术分析类。比如商品期货中的 CTA 策略就是典型的趋势跟踪策略。技术指标中很多是动量（反转）指标，比如 MACD，RSI/WR，也应该归在这一类。

这一类策略的特点是以单个标的时序分析为主。

技术分析类策略非常适合量化投资初学者。这一类策略的原理来自于自然或者生活中的哲理、以及交易心理学。比如，物理运动有这样的特点，运动的物体具有运动惯性 -- 运用到投资上，就是趋势一旦形成，就很可能持续一段时间，这就是趋势跟踪；另一方面，做周期运动的物体，当它运动到周期的尽头时，就会产生反转。表现在股价上，就是涨到一定程度，就会发生回归；跌到一定程度，也会发生回归，这就是价值回归。

尽管技术分析类策略不像对冲策略那样有坚实的理论基础，但投资在很大程度上，就是人与人之间的博弈，本来也就很难用数学公式来刻画人的贪婪与恐惧。所以在小规模的资金量上，遇上恰当的时机，这些策略也非常好用，往往能创造出高收益的奇迹。

!!! note
    如果按流程来划分，我们还可以把量化策略分为选股策略、风控策略和下单策略。

### 4.5. 策略研究方法

首先我们要学习和熟知市场上流行过和正在流行的策略。为什么呢？交易策略对时机和标的有选择性，就像时装的流行一样，存在着周期轮回现象。所以今天失效的策略，也许在下一个周期就会重新发挥作用。其次，在旧的标的上失去光芒的策略，也可能在某些新兴标的上大放异彩。

此外，我们学习他人的策略，更要注意探究这些策略背后的原理，分析方法和技巧。策略可能一时失效，但如果我们掌握了策略背后的原理和方法，就有可能结合当前市场的特点，找到新的交易圣杯。比如，我们可以对参数进行调优，或者将多个简单策略（因子）组合起来，构成更复杂的策略。

最后，我们也要擅于利用最新的技术，这在人工智能时代有更重要的意义。过去，因子的挖掘都是精通数学和金融的分析师才能干的活。今天，普通人也可能利用机器学习，对因子进行暴力挖掘。只要效果好，不用在乎现象背后的深层次驱动原理。关于这一点，人工智能已经在图像领域充分证明过了。

## 5. 课程内容简介

我们对量化交易进了一番全景式鸟瞰后，学员可能要问，我们正在学习的这门课程，它是如何构成的，在量化交易中又处于什么样的定位呢？

一开始我们就讲过，量化交易是通过建立数学模型和利用计算机程序来分析金融市场走势的方法。对金融市场进行数学建模，是策略研究的范畴；而计算机程序则是手段，是登堂入室的门槛。

我们这门课，就是要把学员引进量化交易的宝殿，让大家具备独立进行量化策略研究所需要的编程、以及量化分析相关的技能。

这门课共分六个部分。

第一部分，我们介绍如何获取数据。我们介绍的数据源，从免费到付费的都有，适合不同需要的人。这些数据源包括了 Akshare，tushare，聚宽， Baostock 和 yfinance 等等。在这部分的最后，我们介绍了我们自己开发的大富翁框架在数据方面的用法。在课程的学习过程中，我们将使用大富翁提供的数据，这样可以为学员提供一个稳定、免费和实时的数据。

学完这一部分，你将掌握获取证券列表、k 线数据以及对交易日历进行运算等基本操作。

第二部分，我们将介绍一些常见的策略。其目标是帮助大家练习和巩固第一阶段学习到的知识，并为下一阶段 —— 深入数据处理进行铺垫。这里我们会介绍横扫 A 股十余年的小市值策略、80 年代傲视群视的布林带策略和网格交易策略。

<div style="width:100%;text-align:center">
<img src="https://images.jieyu.ai/images/2023/06/grid.png" style="width:50%;margin: 10px auto;">
<p style="text-align: center;font-size:10px">网格交易法</p>
</div>

第三部分，我们将介绍数据处理的一些基础知识。尽管我们在这里称之为基础，但相对于其它同类课程，可能会更有深度，毕竟作为量化框架的开发者，在这个话题上显然更有实战经验。

<img src="https://images.jieyu.ai/images/2024/01/numpy-logo.png" style="width:120px;margin:10px" align="left">

我们将先介绍 Numpy 和 Pandas 这两个常用的数据分析工具；然后是 ta-lib 这个几乎无人不知的指标库，我们会在这里介绍部分指标函数，以及部分在有效性排名上居前列的模式。接下来，我们会稍微提高一点难度，介绍几个有用的数字信号处理方法，比如，如何寻找时间序列中的顶和底、如何寻找平台整理期。

顶和底的寻找固然只有事后才能发现，但是，一旦顶和底形成，趋势也将延续一段时间。而且自动发现顶和底，也能为我们进行机器学习标注提供非常好的素材。

<div style="width:100%;text-align:center">
<img src="https://images.jieyu.ai/images/2023/08/lesson12-resist-line.png" style="width:50%;margin: 10px auto;">
<p style="text-align: center;font-size:10px">算法寻找的顶底及压力线</p>
</div>

发现平台整理期也一样。尽管平台整理不是上涨的充分条件（也可能下跌，或者继续整理），我们可以比较各个标的之间平台整理期的长短和累积换手大小。平台整理期长，累积换手率大的，后面一旦选择方向，则上涨（下跌）的幅度也越大。此时介入，趋势必然会持续一段时间（和幅度），给了我们获利的机会。

<div style="width:100%;text-align:center">
<img src="https://images.jieyu.ai/images/2023/06/cluster_bounding_box.png" style="width:50%;margin: 10px auto;">
<p style="text-align: center;font-size:10px">算法发现平台整理期</p>
</div>

在这一部分的后面，我们会学习到统计学中的一阶矩到四阶矩的概念（即均值、标准差、偏度和峰度等），我们在因子分析等各种场合都会使用。我们还将学习协方差。当我们进行配对交易时，我们常用协方差来寻找有相关性的资产。我们还将介绍 PDF/CDF 等概念，当我们拿到一个数据集，却无法描述其概率分布时，就可以使用这些 PDF/CDF 的方法，得到一些简单、但重要的结论，比如基于过去的数据，判断某件事发生的概率是多少？

第四部分，我们将介绍数据可视化的内容。我们对策略进行评估时，会用到很多指标。要理解和比较这些指标，绘图是更直观和更容易理解的方式。另外，当策略发出买卖点信号时，我们在很多情况下，都希望将买卖点标识在行情 k 线图上，以确定这些买卖点是否还有优化空间。因此，可视化是量化交易开发中必不可少的技能。我们将从制图原理讲起，分类介绍几个常用的制图工具。在介绍这些工具时，我们都会以 k 线图绘制为例。

第五部分，我们将介绍回测。能够利用历史数据进行回测，从而预测未来，是量化最根本的特征。在这一部分，我们将首先介绍评价策略优劣的常见误区，进而介绍最常用的策略评估指标。然后我们将介绍一个广泛使用、可以一键将这些指标可视化展示的一个 Python 库；在做完这些准备之后，我们将讨论回测的两种主流驱动模型，以及应该如何编写对应的代码。最后，我们还将介绍回测陷阱，避开这些陷阱，才能保证从回测到实盘的收益一致性。

第六部分我们将介绍几种接入实盘的方式，比较它们的优劣，以便大家根据需要来选用。

## 6. 如何学习本课程

### 6.1. 知识储备
这门课的内容以 A 股市场的股票投资为主。学员应该具备基本的理财知识，对股票交易的相关法律法规和交易制度有一定了解。

此外，学员应该具备基础的 Python 知识，比如至少写过 500 行以上的代码，实现过一些完整的功能，有一定的调试能力。在本课程中，我们使用 Python 3.8 以上的版本。有一些语法，比如 asyncio， type hint 等，学员如果还不熟悉的，需要自学一下。

如果学员在上课之前，还不满足上述知识储备要求，可以根据我们列出的参考资料，在课前进行预习。

### 6.2. 在线实验环境

为了确保大家尽快进入学习状态，不把时间花在环境和查错上，我们为开设这门课，提供了一个免安装的在线实验环境。您现在看到的这个 notebook，就应该是运行在我们的课件环境中。

我们使用的技术是 Jupyter Lab。Jupyter Lab 是探索式编程的利器。可以把 Jupyter Lab 看成按单元格组织的网页。在每一个单元格，我们都可以书写代码片段，或者是描述性文字。如果是代码，其运行结果将展示在单元格下方。因此，我们可以通过 Jupyter Lab 来编写策略，得到策略运行的资产曲线图，并且将说明文字、代码及运行结果分享给团队中的其它人。

关于如何使用 Jupyter Lab，在本课件的使用说明（见《使用说明.ipynb》在工作区的根目录下）已经有介绍了，这里还做一点简单说明：

<div style="width:100%;text-align:center">
<img src="https://images.jieyu.ai/images/2023/03/20230310113821.png" style="width:50%;margin: 10px auto;">
</div>

在上图中，我们看到有 Markdown 类型的单元格，也有代码单元格，还有代码执行的输出单元。在每个单元格的右上侧，都有一个工具条，用来复制、移动、增加和删除单元格。单元格有所谓的命令模式和编辑模式。如果我们要运行某个单元格，可以点击顶部工具条上的运行键，也可以使用快捷键（Ctrl + Enter）。

学习代码最好的方式之一，就是自己敲一遍代码，运行一下，改改参数，看看它的结果。如果我们对某个函数的用法不太熟，可以将光标定位到该函数上，再按 Shift + Tab 键，这样就会出现如下帮助文档：

<div style="width:100%;text-align:center">
<img src="https://images.jieyu.ai/images/2023/03/20230310115001.png" style="width:50%;margin: 10px auto;">
</div>

!!! tip
    请大胆尝试！我们的课件和示例都是只读的，学员可以修改并运行，但无法保存，所以，你完全不用担心把文件改坏了！任何时候，你都可以点击 F5 刷新，恢复到教材本来的状态！。
    <br>如果你需要保存自己的修改，可以先将课件/示例中的 notebook 文档拷贝到工作区（根目录），就可以保存自己的修改了。

#### 6.2.1. Python 包的管理
如果你需要在实验环境下安装新的库，可以在任何一个单元格里运行（这里以安装 pandas 为例）：

```bash
! pip install pandas
```

如果要卸载，请运行以下命令（注意参数 -y）：

```bash
! pip uninstall -y pandas
```

## 版权声明

本课程全部文字、图片、代码、习题等所有材料，除声明引用外，均由作者本人开发。所有草稿版本均通过github进行管理，并作为拥有版权的证明。未经作者授权，请勿引用。

## Footnotes

<span id="吴晓求"> 1.本节部分事实及观点摘录自吴晓求编著的《证券投资学》，该书是高校本科段教材。</span>

<span id="shefrin">2.Hersh Shefrin，加拿大行为经济学家。著有《Beyond Greed and Fear》，《Behavioral Corporate Finance》等。</span>

<span id="paradigm">3.科学范式是指当代科学共同体所共同信奉与接受的理论体系，并以此作为常规科学工作的理论预设。人类已经经历了经验范式、理论科学范式，也有人认为我们已经进入了计算科学和数据科学范式</span>
