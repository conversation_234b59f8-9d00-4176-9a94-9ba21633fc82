---
title: 数据分析与Python实现（2）
---

![](https://images.jieyu.ai/images/2023/07/lesson12-outline.png)

在学习了数据分布知识之后，我们来思考这样一个问题，上证指数究竟是一种什么样的分布？

如果它属于任何一种已知的分布，这将是一个统计推断（或者假设检验）问题。如果它是一种神秘、未知的分布，那么我们就退而求其次，尝试回答上次课留下的问题：

!!! Question
    能否基于过去1000个交易日的表现，推断当上证下跌4%之后，继续下跌的概率是？


## 1. 统计推断方法

### 1.1. 分位图

分位图（quantile-quantile plot）又称Q-Q图，在统计学中是通过比较两个概率分布的分位数，来比较这两个概率分布的一种方法。

Q-Q图是基于这样一个原理：

假设X是一个实数集，以$[X_i, X_i], X_i \in X$为坐标的所有点，都会落在 $y = x$，即一条过零点，45度角向上的直线上。

如果X是随机变量，Y是它的理论分布，那么对X, Y的n次抽样结果进行排序后得到的$\bar{X}, \bar{Y}$，以$[\bar{X_i}, \bar{Y_i}], X_i \in \bar{X}, Y_i \in \bar{Y}$为坐标的点，也应该落在$y = x$这条直线附近（因为有随机性，所以不可能完全相等）。

这里我们先以标准正态分布为例进行说明。

```python
import matplotlib.pyplot as plt

fig, (ax1,ax2,ax3) = plt.subplots(nrows=1,ncols=3, figsize = (9, 3))

n = 1000
X = sorted(stats.norm.rvs(size=n))
Y = sorted(stats.norm.rvs(size=n))

ax1.scatter(np.arange(n), X, s=1)
ax1.text(100, 3, "X")
ax3.scatter(np.arange(n), Y, s=1)
ax3.text(100, 2.8, "Y")

ax2.scatter(X, Y, s=1)
ax2.plot(X,X, color='orange')
ax2.text(-2, 3, "y = x")
```

我们对标准正态分布分别作两次size = 1000的取样。第一次记为 X，假定它来自随机样本。第二次记为Y，是对理论分布的一次采样。然后我们对这两个数组进行排序，分别作图，我们会得到下图：

![](https://images.jieyu.ai/images/2023/07/lesson12-qq-plot-0.png)

从视觉上看，左图与右图几乎一致，除了开始和结束处。我们知道，在这些地方分布已经很稀少了，个别离群值可以忽略。中图反映了以[x,y]为坐标的点，基本上都会落在直线两侧。

如果X是均值不为零，且方差不为1的正态分布，此时对 [X, Y]进行绘图，数据点仍然会落在一条直线两侧，但这条直线不是$y=x$, 而是 

$$
y = \frac{x-\mu}{\sigma}
$$

上述直线方程，刚好也是z-score标准化方程：

$$
z = \frac{x - \mu}{\sigma}
$$

因此，我们也可以先将X z-score化得到 Z，再与标准正态分布的抽样Y一起绘图。如果X是正态分布，则绝大多数数据点，将落在 $y = x$直线附近。

!!! Attention
    注意绘图前，必须将X（或者Z）, Y进行排序。

    scipy.stats中有z-score函数：

    ```python
    from scipy.stats import zscore
    ```


这就是利用QQ图进行正态性检验的原理。并且，如果样本曲线与直线不重合，我们还有可能得出它是左偏、还是右偏的结论。

下面，我们就分别生成左偏、无偏和右偏数据集各一份，绘制它们的QQ图。
```python
import matplotlib.pyplot as plt
from scipy import stats
import numpy as np

np.random.seed(318)

fig=plt.figure(figsize=(12,3))

ls = stats.skewnorm(-5).rvs(size=1000)
zs = stats.norm.rvs(size=1000)
rs = stats.skewnorm(5).rvs(size=1000)

x = np.arange(-5, 5)
for i, (name, data) in enumerate(zip(['left-skew', 'zero-skew', 'right-skew'], [ls, zs, rs])):
    ax = fig.add_subplot(1,3,i+1) 
    mu = np.mean(data)
    sigma = np.std(data)
    y = sigma * x + mu 
    plt.plot(x,y) #绘制截距为mu,斜率为sigma的直线
 
    xs = stats.norm.rvs(loc=0,scale=1,size=len(data))
    xs.sort()
    data.sort()
    
    plt.scatter(xs, data, color='r')
    plt.title(name)
    
plt.show()
```

绘制的图形如下：

![100%](https://images.jieyu.ai/images/2023/06/qq-three-skew.png)

注意，在上述代码中，在绘制样本散点图之前，我们需要先对`xs`和`data`（即样本数据）进行排序，以便它们能一一对应起来。

可以看出，如果样本分布是正态分布的话，它会呈现为一条直线；如果是左偏的话，它可能是一条向下拐头的曲线；如果是右偏，则可能是一条微笑曲线。

如果我们估计样本分布为其它分布，Q-Q图绘制原理跟上面类似。

### 1.3. 假设检验方法

前一节讲的方法可以帮我们快速建立起一个直觉。但要更精确地描述两个变量间分布的相似度，我们需要借助假设检验方法。

比如，如果我们假设指数涨跌服从正态分布，则可以通过`normaltest`来判断它是否服从正态分布:

```python
from scipy.stats import normaltest
import numpy as np

np.random.seed(78)

data = np.random.normal(size=1000)

normaltest(data)
```

我们先是通过random.normal生成了一组随机数，然后对它进行normaltest，输出结果中的p值将接近1，远大于显著性水平0.05，表明我们生成的样本`data`服从正态分布。这个结果是显然易见的。

这里的normaltest是假设检验的一种，用来判定一组样本是否服从正态分布。

统计检验方法很多，这里介绍下比较常用的K-S检验。K-S是一种非参数检验，用来检验一组样本是否来自某个概率分布(one-sample K-S test)，或者比较两组样本的分布是否相同(two-sample K-S test)。K-S检验是以它的两个提出者，俄国统计学家Kolmogorov和Smirnov的名字来命名的。

我们可以通过`scipy.stats.kstest`来执行k-S检验。该方法的签名如下：
```python
kstest(rvs, cdf, args=(), N=20, alternative='two-sided', method='auto')
```
这里`rvs`参数的类型可以是`str`, `array_like`或者`callable`。当它的类型为`str`时，必须是`scipy.stats`支持的某种分布的名字；如果类型是`callable`，则要求它能生成随机变量。如果类型为`array_like`，则必须为随机变量观测值。

`cdf`具有与`rvs`一样的参数类型要求和解释。如果`cdf`是观测值的一维数组，则`kstest`将执行 two-sample test，此时`rvs`也必须为同样类型。

如果`rvs`或者`cdf`需要参数，通过`args`参数传入。

`N`是用来生成`rvs`的数组大小，缺省为20。

返回结果为一个`KstestResult`类，它包括`statistic`， `pvalue`等重要属性。

现在，我们就通过`kstest`，对上证指数，运用`scipy.stats`中已实现的分布模型，逐一进行 One-Sample test，看看能否有通过检验的：
```python
from coursea import *
await init()
import scipy
import matplotlib.pyplot as plt
import pandas as pd

import warnings
warnings.filterwarnings('ignore')

bars = await Stock.get_bars("000001.XSHG", 1001, FrameType.DAY)
close = bars["close"]

pct = close[:-1]/close[1:] - 1

dist_names = ['burr12', 'dgamma', 'dweibull', 'fisk', 'genhyperbolic', 
              'genlogistic', 'gennorm', 'hypsecant', 'johnsonsu', 
              'laplace', 'laplace_asymmetric', 'logistic', 'loglaplace',
              'nct', 'norminvgauss']
xmin, xmax = min(pct), max(pct)


# for name in dir(scipy.stats):
#     dist = getattr(scipy.stats, name)
#     if not isinstance(dist, scipy.stats.rv_continuous):
#         continue
        
dist_pvalue = []
for name in dist_names:
    dist = getattr(scipy.stats, name)
    if getattr(dist, 'fit'):
        params = dist.fit(pct)

        ks = scipy.stats.kstest(pct, name, args=params)
        dist_pvalue.append(round(ks.pvalue, 2))
        
df = pd.DataFrame({
    "name": dist_names,
    "pvalue": dist_pvalue
})

df.sort_values("pvalue", ascending=False).transpose()
```
第22到第25行代码被注释是因为，其中有一些分布，在检测时耗时非常长。因此，最终我们只挑选了一部分分布来进行`kstest`。输出结果如下：

![100%](https://images.jieyu.ai/images/2023/06/sh_kstest_result.png)

看起来上证指数与`genhyperbolic`（广义双曲分布）匹配度最高，我们通过绘图来查看一下：

```python
from scipy.stats import genhyperbolic

params = genhyperbolic.fit(pct)
rv = genhyperbolic(*params)

fig, ax = plt.subplots(1,1)
x = np.linspace(rv.ppf(0.01), rv.ppf(0.99), 100)
ax.plot(x, rv.pdf(x), label = 'genhyperbolic pdf', color="#EABFC7")

ax2 = ax.twinx()
_ = ax2.hist(pct, bins=50)
```

在这段代码中，我们先是通过第3行，对沪指抽样进行一个拟合，得到一组特征参数。然后构建了广义双曲分布对象 rv，再通过第7行，我们取它ppf 0.01和ppf 0.99的线性空间构成x轴。

第8行，我们以pdf(x)为y值，绘制pdf图，然后在第二个y轴上，绘制它的直方图。

这里直方图来自数据本身，pdf则是按广义双曲分布拟合出来的分布。如果沪指确实服从广义双曲分布，那么我们应该看到，pdf曲线与直方图外边缘相重叠：

![50%](https://images.jieyu.ai/images/2023/06/sh_histo_pdf.png)

结果表明，沪指确实与广义双曲分布吻合得较好。

现在，要回答“当上证下跌4%之后，还会继续下跌的概率为多大”这个问题，我们可以通过`rv`对象的`cdf`方法来获得：
```python
>>> rv.cdf(-0.04)

0.0016
```
即继续下跌的概率仅为0.16%。所以，下次遇到这样的大跌，是不是可以大胆地抄底？

如果我们不能幸运地为上证找到任何一种合适的已知分布，我们还能回答上述问题吗？答案是肯定的。我们可以使用上次课中介绍的`statsmodels.ECDF`。

不过，出于增加知识面的目的，我们这次使用`scipy.stats.rv_histogram`模型来求上证的经验分布：

```python
import scipy.stats as st
import numpy as np
import matplotlib.pyplot as plt

bars = await Stock.get_bars("000001.XSHG", 1000, FrameType.DAY)
close = bars["close"]

pct = close[1:]/close[:-1] - 1
hist = np.histogram(pct, bins = 100, density=False)
hist_dist = st.rv_histogram(hist, density=False)

x = np.linspace(hist_dist.ppf(0.001), hist_dist.ppf(0.999), 100)

fig, ax1 = plt.subplots()

ax1.plot(x, hist_dist.cdf(x), color='#4A73A2')

ax2 = ax1.twinx()
ax2.plot(x, hist_dist.pdf(x), color="#B297D5")

print(f"当上证下跌4%以后，继续下跌的概率是: {hist_dist.cdf(-0.04):.2%}")
```

经验分布表明，**当上证下跌4%以后，继续下跌的概率为0.4%**，高出`genhyperbolic`的预测不少。

使用该模型，首先我们要通过`np.histogram`来生成`hist`对象（包括`hist`和`edges`两个数组），然后将其传递给`scipy.stats.rv_histogram`以构建一个`rv_frozen`对象，此后我们便可以象其它随机变量一样，调用它的`pdf`, `ppf`和`cdf`函数。

上面的代码绘制的上证的`cdf`和`pdf`曲线图如下：

![50%](https://images.jieyu.ai/images/2023/06/下跌0.04后.png)

注意这里的`pdf`曲线，它的数值非常大，但它在`pct`作用域上的积分为1。这并不矛盾。因为这里给出的是`pdf`，而不是`pmf`。`pdf`是`cdf`的导数，当它的数值比较大时，只说明在该处上升较快。

我们可以将同样的方法，运用在一些技术指标上，比如：

!!! Question

    如果某支股票的`RSI`值当前为`x`，则`RSI`大于`x`的概率将会是多少（即从`RSI`的角度来看，能够进一步上涨的概率是多少）？

    上证指数已经6连阳了，接下来继续收阳线的概率会是多少？

## 2. 拟合、回归和残差

我们已经接触过拟合的概念。我们学习了`skewnorm`, `weibull`和`norm`等分布的`fit`函数，通过`fit`函数，我们可以得到样本分数据在某种分布下的特征参数。为了检验样本分布是否符合某一种分布，我们使用了假设检验。假设检验返回t值和p值，如果p值大于0.05，我们一般就认为样本是服从该分布的。

但我们对时间序列的研究，不仅仅限于它的分布和统计特征，也许还希望预测它的走势，这可能就要用到回归分析，甚至神经网络等技术。但是，无论我们怎么进行拟合，都会需要定义一种度量拟合值与观测值之间差异的方式。

!!! Note
    拟合与回归是相近但有区别的两个概念。一般认为，回归分析的特点之一，是在寻求趋势预测。有时候，它也专指线性回归或者最小二乘法估计。拟合有时候是指曲线拟合(curve-fitting，但从一些软件命名来看，神经网络、概率模型也都在用`fit`这个词)。在这里，我们一般不进行区分。

### 2.1. 残差及其度量

在数据分布一节，我们接触到了误差的概念，并且学习了方差和标准差。它们是关于测量值与真实值之间差异的度量标准。

残差（residual）是拟合值与观测值之间的差异。在 `sklearn.metrics` 中包含了许多跟残差相关的度量标准，我们就来一一认识下：

#### 2.1.1. max_error

拟合值与真值之间的最大残差。我们可以用`sklearn.metrics.max_error`来计算。
```python
from sklearn.metrics import max_error
import numpy as np

np.random.seed(78)
y = np.arange(10)
y_hat = np.arange(10) + np.random.randint(1, 10, size=10)
max_error(y_hat, y)
```

#### 2.1.2. mean_absolute_error

原序列（真值）与预测值之间的平均绝对值差。

```python
from sklearn.metrics import mean_absolute_error
import numpy as np

np.random.seed(78)
y = np.arange(10)
y_hat = y + np.random.randint(-10, 10, size=10)
mae = mean_absolute_error(y_hat, y)

print(y)
print(y_hat)
print(mae)
```

上述残差值为绝对值，不便于在不同序列之间进行比较，因此我们还有MAPE:

#### 2.1.3. mean_absolute_percentage_error

```python
from sklearn.metrics import mean_absolute_percentage_error
import numpy as np

np.random.seed(78)
y = np.arange(10)
y_hat = np.arange(10) + np.random.randint(1, 10, size=10)
mean_absolute_percentage_error(y_hat, y)
```

结果为0.62。注意，这不是0.62%，而是62%。

#### 2.1.4. mean_squared_error

这个类似于方差，但它是在序列$y - \hat{y}$上的方差，要求同时传入 `y_true` 和 `y_pred` 两个序列。它有一个 `squared` 的参数，如果为False，则返回rooted mean squared error，即相当于标准差。

#### 2.1.5. rooted mean squared error
见 mean_squared_error

### 2.2. 回归分析

回归分析很多时候专指线性回归，我们今天讲的也是线性回归。但实际上是存在多种回归类型的。从sklearn来看，至少有十几种已知的回归模型。

在讲线性回归时，我们也要知道，在不同的领域、文章和软件实现中，线性回归的概念可能不尽相同。狭义地来讲，它是指一元一次或者多元一次线性回归；广义来讲，一元多次或者多元多次也是线性回归的一种，尽管它们从图形上看，并不呈现出线性的特征。甚至更广义地来讲，我们有时候也会看到这样的定义，只要系数不出现在自变量函数体内，都可以看成线性回归。

下图是一个典型的一元一次线性回归，它是惟一一种无须变换，即可在视觉上呈现线性相关的回归：

![400px](https://images.jieyu.ai/images/2023/08/lesson12-lr-1.png)

多元一次回归比较难可视化，此时的”回归线“将是一个超平面。

一元多次回归也被看成是线性回归的一种。在下式中：

$$
y = a_0 + a_1x_1 + a_2x_2 + a_3x_3 + ...
$$

如果我们将$x_n$替换成$x_n = f_n(x)$得到了多项式回归：

$$
y = a_0 + a_1x +a_2x^2 + a_3x^3 + ...
$$

从可视化的角度，如果我们以$x_n = f_n(x)$为横坐标，则(x, y)仍然是线性相关的。如果这里的$x_n = f_n(x)$不是一个多项式，而是其它基本函数，只要系数$a_n$不是$f_n(x)$的一部分，我们都认为它是线性相关。这是线性回归最泛化的概念。

一元一次和一元多次线性回归又称为多项式回归。在本节中，我们主要以技术形态分析为例，讨论一元一次和一元多次线性回归的实现。

比如有以下股价序列：

```python {cmd=true continue=true}
arr = ['102.8', '85.5', '62.1', '48.5', '35.0', '27.1', '16.5', '6.8', '8.1', '-2.4', '0.0', '2.5', '6.3', '12.8', '12.6', '28.0', '38.8', '45.6', '63.1', '82.8']
```

我们想知道这个序列未来会如何演绎，我们分别对它进行一次曲线和二次曲线拟合：

```python
import numpy as np
import plotly.express as px
np.random.seed(78)

y = np.array([ x**2 -5 for x in range(-10, 10)]) + np.random.random(20) * 10

coff_1 = np.polyfit(x = np.arange(-10, 10), y = y, deg=1)
coff_2 = np.polyfit(x = np.arange(-10, 10), y = y, deg=2)

f1 = np.poly1d(coff_1)
f2 = np.poly1d(coff_2)

px.line(
    {"y_fit_deg2": f2(np.arange(-10, 10)),
     "y_fit_deg1": f1(np.arange(-10, 10)),
     "y": y
    })
```

!!! Note
    我们也可以使用`sklearn.linear_model.LinearRgression`来进行线性回归。但是它并没有多项式回归算法，关于多项式的算法放在`sklearn.preprocessing.PolynomialFeature`中。

在上面的代码中，我们通过`np.polyfit`来进行多项式拟合，具体是进行线性拟合，还是二次项拟合，取决于`deg`这个参数。我们来看看`np.polyfit`这个方法的签名：
```python
def np.polyfit(x, y, deg, rcond=None, full=False, w=None, cov=False):
    pass
```
`deg`给出待拟合多项式的幂次。注意，幂次越高，拟合残差就越小，但这样越容易引起过拟合，对未来的预测没有任何意义。一般我们用到2次就足够了。

`full`这个参数默认为False，此时只返回拟合后的系数项；当它为True时，还将返回残差等数据。

当`cov`为True时，还将返回样本与拟合数据间的协方差矩阵。下面，我们就给出一个`np.polyfit`返回结果最详细的例子：
```python
>>> np.polyfit(np.arange(10), 2 * np.arange(10) + 3, deg = 1, full=True, cov=True)
(array([2., 3.]),
 array([3.0460508e-29]),
 2,
 array([1.35754456, 0.39632407]),
    2.220446049250313e-15)
```

在得到系数项之后，我们将它传给`np.poly1d`，就生成了一个新的多项式函数，当我们传入自变量`x`后，就生成了对应的$\hat{y}$。

结果显示如下图：

![](https://images.jieyu.ai/images/2023/06/fit_and_residual.png)

直观上看，上述数据更贴近某个二次曲线。现在，我们通过残差的概念，来检查一下，两次拟合的具体情况。

```python
from sklearn.metrics import max_error, mean_absolute_percentage_error, mean_squared_error

y = np.array([ x**2 -5 for x in range(-10, 10)]) + np.random.random(20) * 10

coff_1 = np.polyfit(x = np.arange(-10, 10), y = y, deg=1)
coff_2 = np.polyfit(x = np.arange(-10, 10), y = y, deg=2)

f1 = np.poly1d(coff_1)
f2 = np.poly1d(coff_2)

yhat2 = f2(np.arange(-10, 10))
yhat1 = f1(np.arange(-10, 10))

pos2 = np.argmax(abs(y - yhat2))
print(f"max error between yhat2 and y: {pos2}, {max_error(y, yhat2):.2f}")

pos1 = np.argmax(abs(y -yhat1))
print(f"max error between yhat1 and y: {pos1}, {max_error(y, yhat1):.2f}")

print(f"mape between yhat2 and y:{mean_absolute_percentage_error(y, yhat2):.2%}")
print(f"rmse between yhat2 and y:{mean_squared_error(y, yhat2, squared=False):.2f}")
```

在上述示例中，二次曲线拟合与原序列的 `max_error` 为5.24，发生在第0个元素上；一次曲线拟合与原序列的 `max_error` 为56.31，发生在第19个元素上。看起来二次曲线的拟合结果更好。

但是，拟合曲线与原曲线之间的相对误差达到了71.57%，标准差达到了2.76，这说明拟合曲线与原曲线实际上也不能较好地拟合。

!!! Attention
    在统计分析中，我们很可能愿意忽略个别的`max_error`，因为它们可能是分布上的所谓离群值。但在证券交易中，我们必须警惕这个`max_error`，如果我们按照拟合的函数来预测未来，当`max_error`真的出现时，可能会使得我们在某个加杠杆的操作中，一次性地被爆仓。

在k线图中，我们常常把一些重要的顶点连线起来，将它的延伸线作为压力线。反之，一些重要的底部的连线，我们将它的延伸线当成支撑线。这些都是线性回归的例子。

![](https://images.jieyu.ai/images/2023/08/lesson12-resist-line.png)

这个图，显示2023年8月3日，沪指的5日均线图。我们通过程序自动捕捉它的顶点，进行拟合连线，可以看出，有时候确实存在这样的压力线。这段期间还存在着一条支撑线，这里没有绘制出来。

一些长周期的均线往往能进行较好的一次曲线拟合。这对我们判断它的整体趋势是有益的。一般情况下，我们应该避开整体向下的标的，短周期的均线则有可能拟合成二次曲线。这对我们判断它的动量特征，发现短期是否可能反转有一定帮助。

## 3. 相关性

经济活动中往往具有关联性。比如，上游原材料涨价后，下游企业的利润就可能会受影响。这是我们从经济活动的原理推导出来的结论。如果从数据分析的角度，是否存在某种方法，可以揭示两组数据之间的关联？


这种方法如果存在，它在证券分析中的作用是不言而喻的。

比如，如果我们从历史数据中发现，美股中的某支证券与A股的某个板块有较强的关联度，那么如果近期该美股大幅上涨，那么可能迟早会引起A股某个板块上涨。

又比如，如果我们打算购买某支指数基金。评价指数基金的指标之一，是它能不能盯住指数。这也是一个相关性问题。

实际上，利用两个相关性较高的证券品种进行配对交易，卖出相对高估的品种，买入相对低估的品种，这种交易策略被称之为配对交易，是一种主流的量化中性策略。

!!! Note
    关于配对交易，涉及到平稳性检验和协整理论，这里不深入介绍。协整性可以通过statsmodels中的`conint`来计算。

### 3.1. 协方差和相关系数

协方差(covariance)描述的是随机变量联合变化程度。通俗一点讲，假设股票B是板块A中一支股票，那么当板块A上涨时，B也可能上涨；当板块A下跌时，B也可能下跌，这就是联合变化。协方差就是以量化的方式来定量分析这种联合变化的程度。

假设板块A每日涨跌幅记为$X$，股票B的每日涨跌记为$Y$，则两者的$n$日协方差为：

$$ cov(X, Y) = \frac{1}{n-1}\sum_{i=1}^n(X_i - \mu_X)(Y_i - \mu_Y) \tag 3
$$

上述方法计算出来的$cov$的大小，跟$X$,$Y$的大小相关。为了无量纲化，要对其进行标准化，就有了相关系数的概念：

$$\rho_{XY} = \frac{cov(X, Y)}{\sigma_X\sigma_Y}$$

!!! Note
    我们可以使用`np.cov`来计算协方差，使用`np.corrcoef`来计算相关系数。在pandas中，对应的分别是`df.cov`和`df.corr`。


下面，我们就选取一个板块和其中的几支股票，求一下它们的相关系数，并绘制成热力图：
```python
import matplotlib.pyplot as plt

codes = ["300607.XSHE", "300165.XSHE", "300535.XSHE", "603392.XSHG", "603718.XSHG", "002030.XSHE"]

start = datetime.date(2022, 9, 1)  # 起始时间， 可修改
end = datetime.date(2023, 3, 1)  # 截止时间， 可修改

board = "300941" #抗原检测

bbars = await Board.get_bars_in_range(board, start, end)

data = [ bbars["close"][1:]/bbars["close"][-1:] - 1]
names = [board]

for code in codes:
    bars = await Stock.get_bars_in_range(code, FrameType.DAY, start, end)
    
    pc = bars["close"][1:] / bars["close"][:-1] - 1
    data.append(pc)
    names.append(code.split(".")[0])


f = plt.figure(figsize=(19, 15))
plt.matshow(np.corrcoef(data), fignum = f.number)

plt.xticks(range(len(names)), names, fontsize=14, rotation=45)
plt.yticks(range(len(names)), names, fontsize=14)
cb = plt.colorbar()
cb.ax.tick_params(labelsize=14)
plt.title('Correlation Matrix', fontsize=16);
```

![50%](https://images.jieyu.ai/images/2023/06/corr_heatmap.png?1)

从热力图可以看出，板块指数（300941）与300535相关性很强，而与603392相关性则较弱。

我们把股价走势绘制成下图，显然，相关性系数完全反映了股价的走势对比：

![100%](https://images.jieyu.ai/images/2023/06/stock_trend_compare.png?1)

!!! Attention
    注意在热力图中，轴的取值区间是[0,1]。这是因为我们使用的`matshow`方法对取值进行了rescale。实际上，`np.corrcoef`的输出值范围是[-1, 1]。相关系数越接近-1，表明走势越相反；越接近1，表明走势越接近。如果相关性为0，则表明两者是正交关系。

    在因子选择时，我们应该选择相关系数接近于0的因子，每个因子都能独立对收益有贡献；在进行资产组合投资时，应该选择资产收益相关性接近零的品种，以分散风险。

上述热力图对应的相关系数为：

![50%](https://images.jieyu.ai/images/2023/06/corr_values.png)

### 3.2. 皮尔逊相关性和斯皮尔曼相关性

我们刚刚计算的相关系数，实际上叫皮尔逊(pearson)相关系数。

皮尔逊相关性的适用条件是，两个随机变量都要服从正态分布，数据至少在逻辑范围内是等距的。由于金融数据分布往往只是近似于正态分布，所以在出现尾部风险时，这种相关性计算方法就不能用了。

另外，有一些场合，数据取值并不是等距的。比如，近期收益回报前3的股票，第1名与第2名的收益差与第2名和第3名的收益差往往是不相等的。这种情况下，也不满足皮尔逊相关性条件，这就需要使用斯皮尔曼相关系数。

Spearman是一种秩相关，它是一个非参数性质（与分布无关）的秩统计参数，是用来度量两个连续型变量之间单调关系强弱的相关系数，取值范围也是 [−1,1]。在没有重复数据的情况下，如果一个变量是另外一个变量的严格单调函数，则 Spearman 秩相关系数就是 1 或 −1，称变量完全 Spearman 秩相关。

在因子分析中，现在越来越倾向于认为RANK-IC的鲁棒性更好，因此因子分析中Spearman相关更常用。

$$
 r_s =
 \rho_{\operatorname{R}(X),\operatorname{R}(Y)} =
 \frac{\operatorname{cov}(\operatorname{R}(X), \operatorname{R}(Y))}
      {\sigma_{\operatorname{R}(X)} \sigma_{\operatorname{R}(Y)}} \tag 4
$$

这里的秩相关 (Rank Correlation)，又称等级相关，是将两变量的样本值按数据的大小顺序排列位次，以各要素样本值的位次代替实际数据而求得的一种统计量。排序不论从大到小还是从小到大排都无所谓，只要保证大家排序的标准一致即可。


下面，我们仍以上面的板块和股票为例，来计算Spearman相关系数：

```python
from scipy import stats

corr, pvalue = stats.spearmanr(data, axis=1)

f = plt.figure(figsize=(9, 7))
plt.matshow(corr, fignum = f.number)

plt.xticks(range(len(names)), names, fontsize=14, rotation=45)
plt.yticks(range(len(names)), names, fontsize=14)
cb = plt.colorbar()
cb.ax.tick_params(labelsize=14)
plt.title('Correlation Matrix', fontsize=16);
```
生成的热力图与使用pearson的相差不大，这里就不展示了。

!!! Tip
    在numpy中没有计算皮尔逊相关性的函数。这次我们将使用`scipy.stats.spearmanr`来计算相关性。如果数据已经是DataFrame格式，也可以使用`df.corr(method='?')`来计算。这里`method`可以是空（pearson)，或者`spearman`和`kendall`。

`stats.spearmanr`方法的签名如下：
```python
stats.spearmanr(
    a,
    b = None,
    axis = 0,
    nan_policy = 'propagate',
    alternative = 'two-sided'
)
```
`a`和`b`是待检验相关性的数据。如果`a`和`b`是二维的数组，则`axis`有意义。在示例中，由于我们的观察数据是按列存放的（即每一行是一组数据），所以这里指定`axis=1`。在`b`没有指定的情况下，相关性将在`a`和`a.T`之间进行计算。

`spearmanr`方法返回相关性系数矩阵和`pvalue`。

!!! attention
    `spearmanr`的原假设（null hypothesis）是两组数据是**非线性相关**。因此，当p值小于5%时，我们拒绝原假设，即两组数据是线性相关的。

    `spearmanr`要求观察数据至少在50个以上。我们的示例中，观察值是118个，满足条件。如果观察数据不足50，scipy的建议是使用`stats.permutation_test`。


### 3.3. 相关性分析示例

我们来做这样一个实验。取一支股票，获取它的每日收盘价，分别计算以下指标：

1. 每日涨跌比率，即: $close[1:] / close[:-1] - 1$
2. 移动5日收益率，即 $close[5:] / close[:-5] - 1$
3. 移动10日收益率，即 $close[10:] / close[:-10] - 1$
4. RSI，窗口为6
5. RSI的导数，即 $rsi[1:] - rsi[:-1]

我们分别取前一日的2~5的数据，生成`DataFrame`，再取每日涨跌比率，生成`pd.Series`，最终计算1和其它各项的相关系数：

```python
code = '603392.XSHG'
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 3, 1)
bars = await Stock.get_bars_in_range(code, FrameType.DAY, start, end)

close = bars["close"].astype(np.float64)
rsi = ta.RSI(close, 6)

pct1 = (close[1:]/close[:-1] - 1)

data = {}
for n in (5, 10):
    data[f"pct{n}"] = (close[n:]/close[:-n] - 1)[-21:-1] * 10

data["pre_rsi"] = rsi[-21:-1]
data["rsi"] = rsi[-20:]

drsi = np.diff(rsi)
data["pre_drsi"] = drsi[-21:-1]
data["drsi"] = drsi[-20:]

df = pd.DataFrame(data)
s = pd.Series(pct1)

df.corrwith(s)
```

!!! Tip
    相关系数是量纲无关的。读者可以把第8行的`rsi`归一化到区间[0, 1]间再试试，输出的结果将会是一致的。

最终输出结果如下：
```
pct5        0.028
pct10       0.215
pre_rsi     0.36
rsi        -0.029
pre_drsi    0.149
drsi       -0.34
```
数据表明，当日涨跌与前一日的`rsi`的相关系数是0.36，和前一日`drsi`的相关系数是-0.34，有观点认为0.3以上的系数也可以认为存在弱相关；但与近5日收益率、10日收益率的相关性比较弱。

如果我们将当日涨跌与当日`drsi`对照绘图，会发现它们的走势几乎完全一致，这不难理解，毕竟涨跌影响着RSI的取值。

但如果用前一天的drsi，即pre_drsi与pct1对照绘图，它们的走势也极为相似，但有一天的相位差。也就是前一日的rsi的涨跌，会影响到次日股价涨跌。

![75%](https://images.jieyu.ai/images/2023/06/pct_drsi.png)

也就是说，pct1与drsi之间，pct1与pre_drsi之间都存在某种关联。不管这种关联有没有实际利用价值，但是我们在这里发现了一个现象，即存在关联的两个序列，并不一定能通过相关性分析给找出来。

那么这种情况下，我们又应该通过什么方法来找出它们之间的联系呢？

这就引入了相似性的概念。

## 4. 距离和相似性

判断两组数据是否相似，需要先定义度量方法。

相似性度量方法在多元统计中的聚类分析、机器学习等方面都有应用。随着机器学习在证券分析中的应用越来越广泛，我们也有必要介绍一些入门知识。

相似性是通过距离来度量的。如果两组数据之间的距离越大，那么相似性越小；反之，相似性越大，那么距离越小。

!!! Tip
    一般而言，定义一个距离函数 d(x, y), 需要满足下面几个准则：

    1. 到自己的距离为0，即：$$d(x,x) = 0$$           
    2. 距离非负，$$d(x,y) >= 0$$
    3. 对称性，即对称性: 如果 A 到 B 距离是 a，那么 B 到 A 的距离也应该是 a:
    $$
    d(x, y) = d(y, x)             
    $$
    1. 三角形法则: (两边之和大于第三边)
    $$
    d(x, k)+ d(k, y) >= d(x, y)
    $$

### 4.1. 常见距离定义列举
1. **欧氏距离**
    <br><br>这是最直观的一种距离，即两点之间的直线距离。假设有n维空间的两点x,y,则它们之间的距离为：
    $$ d = \sqrt{\sum_{i=1}^n(x_i - y_i)^2} \tag 5$$

2. **余弦相似度**
    <br><br>通过求两个向量之间的夹角来定义其相似度。如果两个向量完全相似，则夹角为零。早期在计算文本相似度上有较多应用。
    $$ S = \frac{x.y}{|x||y|} \tag 6$$

3. **马氏距离**
    <br><br>马氏距离是基于样本分布的一种距离。

4. **编辑距离**
    <br><br>是指两个字串之间，由一个转换成另一个所需的最少编辑操作次数；在软件中常用此方法来查找文章中的拼写错误。如果一个词不在词库里，但又与词库中的某个词在编辑距离上很接近，则可能判断这里出现了拼写错误。

5. **杰卡德距离**
    <br><br>用来判断两个集合之间的距离。可用以推荐算法中。

6.  **皮尔逊相关系数**
   <br><br>形式上类似于余弦相似性。

### 4.2. 如何计算距离
`scipy.spatial.distance`包和`sklearn.metrics.pairwise`都提供了距离计算的工具。

下面，我们就以以下三条线为例，展示如何计算它们之间的距离：

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

x=np.arange(0,np. pi*2, 0.1)
y1=np.sin(x)
y2=np.cos(x)-2
y3=y1-2

plt.plot(y1)
plt.plot(y2)
plt.plot(y3)
plt.legend(['y1','y2','y3'])
```

![50%](https://images.jieyu.ai/images/2023/06/three_sim_lines.png)

从视觉上看，我们一般认为`y1`, `y2`和`y3`都是相似的曲线。y3相当于y1的缩小版本，y2则是在y1的基础上进行了平移。如果我们还有曲线`y4`，假设它是`y1`在平面上进行了90度旋转，我们一般也认为`y4`和`y1`相似，这一点在图像处理领域尤其如此。比如，一张泰迪熊的照片，无论是横着放、还是竖着放，都不会改变它是一张泰迪熊的事实。

```python
from sklearn.metrics.pairwise import euclidean_distances, cosine_distances, cosine_similarity
from scipy.spatial.distance import euclidean, cosine
import pandas as pd

def corr(x1, x2):
    return round(np.corrcoef(x1, x2).flatten()[1], 2)

def calc_dist(label, x1, x2):
    return [
        label,
        round(euclidean_distances([x1], [x2]).flatten()[0], 2),
        round(cosine_distances([x1], [x2]).flatten()[0], 2),
        round(cosine_similarity([x1], [x2]).flatten()[0], 2),
        corr(x1, x2)]

data = []
data.append(calc_dist("y1->y2", y1, y2))
data.append(calc_dist("y2->y3", y2, y3))
data.append(calc_dist("y1->y3", y1, y3))

df = pd.DataFrame(data, columns="label,eucl,cos_dis,cos_sim,corr".split(","))
df
```
这段代码分别两两计算了`y1,y2,y3`之间的欧氏距离、余弦距离、余弦相似度和皮尔逊距离。余弦距离和余弦相似度几乎完全相同。用1减去余弦距离，就得到了余弦相似度。

输出结果如下：

![50%](https://images.jieyu.ai/images/2023/06/dist_results.png)

从欧氏距离和余弦距离（相似度）的角度看，都是y2和y3最接近；但从相关性上看，y1和y3相关性很强，但其它两对关系中则不相关。总的来说，我们使用的这些相似性检测算法，没有一个能够完全反映出三条曲线实际是都是相似的这一结论。

这个结果反映了我们要找出一组证券中，具有相同特点的证券的难度。如果仅仅是要检测股价走势的相似性，可以考虑`fastdtw`[^fastdtw]方法。

## 5. 归一化

归一化是机器学习中，预处理的重要步骤。在因子分析中也常常用到。这里我们简单讨论归一化数学函数。更多关于归一化（或者更泛化地讲，缩放甚至预处理，可参见sklearn.preprocessing包）。

在[第9课 Numpy和Pandas](chap03-%E7%AC%AC9%E8%AF%BE.md)那一节中，有一个习题，求当日的成交量是多少个周期以来的地量。这道题的结果，理论上是 $[1, +\infin]$ 。但在机器学习中，或者任何其它需要统一量纲的场合，我们都需要一种方法，将其进行缩放，使之被压缩到一个狭窄的小区间，同时保持单调性不变。

一般情况下，我们可以通过sigmoid函数（又称为logistic）来完成这个转换。sigmoid函数的公式如下：
$$
    S = \frac{1}{1 + e^{-x}}
$$

当x趋向$-\infin$时，$S$取值趋向0，当x取值趋向$+\infin$时，$S$取值趋向1；当x为零时，取值为0.5，在0.5附近,$S$对X的变化更为敏感。

另外一个常用的函数是tanh。我们将两者的变换图对比如下：

![50%](https://images.jieyu.ai/images/2023/06/comarison_tanh_sigmoid.png)

两者的主要区别在函数值域上。sigmoid取值为(0, 1)，而tanh的取值为(-1,1)。另外一个不同之处是，在中心区，似乎tanh上升得更快，不过这些都可以通过变换来消除。

另一个比较重要的区别就是在机器学习中，出于性能的考虑，更多时候我们使用sigmoid函数而不是tanh，这是因为对sigmoid函数求导的计算存在简便算法，因此会快很多。

直接使用sigmoid函数仍会有很多不便之处。比如，假设有随机变量的值域是$(-\infin, +\infin)$，但90%以上的取值分布在[10,30]之间（比如当我们寻找圆弧底时，其宽度的最小值为3，最大不限，但我们倾向寻找10\~30的宽度，这个区间能更好地体现筹码博弈，由下跌到止跌回升的过程。这样我们就倾向于让10\~30处的数值，在归一化时，有最好的响应灵敏度）。

因此，我们希望sigmoid在这一区域，有更好的响应灵敏度。但是，如果我们直接使用sigmoid函数，结果如下：

```python
import matplotlib.pyplot as plt

def sigmoid(x):
    return 1/(1 + np.exp(-x))

x = np.linspace(10, 30, 20)
plt.plot(x, [sigmoid(i) for i in x])
```
输出结果如下：

![50%](https://images.jieyu.ai/images/2023/06/sigmoid_no_tuned.png)

这个结果很难说理想。事实上，从x = 12开始，它们的sigmoid值就不再有区分度。

从下面的例子可以看出，尽管从数学的角度，sigmoid(12)和sigmoid(13)是不相等的，但在实际的运用中，一般认为它们是相等的：

```python
def sigmoid(x):
    return 1/(1 + np.exp(-x))

print("sigmoid(12) == sigmoid(13)?", np.isclose(sigmoid(12), sigmoid(13)))
```
所以，如果我们使用原始的sigmoid来进行归一化，12个周期以来的地量与200个周期以来的地量将没有任何区别，但实际上，后者出现的频率很低，一旦出现，它的信号意义将很强。

我们给出如下的方法，以对sigmoid函数进行调整，使之能在我们期望的区间内，有较好的响应灵敏度：
```python
import matplotlib.pyplot as plt

def scaled_sigmoid(x, start, end):
    """当`x`落在`[start,end]`区间时，函数值为[0,1]且在该区间有较好的响应灵敏度
    """
    n = np.abs(start - end)

    score = 2/(1 + np.exp(-np.log(40_000)*(x - start - n)/n + np.log(5e-3)))
    return score/2


fig, (ax1, ax2, ax3,ax4) = plt.subplots(nrows = 1, ncols = 4, figsize=(12,3))

x = np.linspace(0, 1)
ax1.plot(x, [scaled_sigmoid(i, x[0], x[-1]) for i in x])
ax1.set_title("fit (0,1)")

x = np.linspace(0, 100)
ax2.plot(x, [scaled_sigmoid(i, x[0], x[-1]) for i in x])
ax2.set_title("fit (0, 100)")

x = np.linspace(18, 38)
ax3.plot(x, [scaled_sigmoid(i, x[0], x[-1]) for i in x])
ax3.set_title("fit (18, 38)")

x = np.linspace(0, 100)
ax4.plot(x, [sigmoid(i) for i in x])
ax4.set_title("fit (0,100) with original")
```
下图显示了经过调整后的sigmoid图在各个区间的响应情况，以及和未调整的sigmoid的对照：

![](https://images.jieyu.ai/images/2023/06/scaled_sigmoid.png)

从图中可以看出，无论`x`的值域在哪一个范围，调用后的sigmoid函数都能在该区间给出非常好的区分度。

[^fastdtw]: https://github.com/slaypni/fastdtw

本章结束后，我们基本上就掌握了必备的统计学知识。下图是我们介绍过的知识点，与wiki词条覆盖率对照：

![](https://images.jieyu.ai/images/2023/06/statistics_overview.png)

