## 1. 小市值策略
在一定的时间周期内，持有市值最小的若干只股票，周期结束时卖出；在下一周期开始时，再买入市值最小的若干只股票，在周期结束时卖出，如此循环。1981年Banz基于纽交所长达40年的数据发现，小市值股票月均收益率比其他股票高0.4%。因此，小市值策略是在全球范围来讲，普遍适用的一个策略。市值因子也被纳入了Fama三因子模型和五因子模型，有时候也被称为规模因子。

研究表现，A股市场上，2016年以前，规模因子的显著性甚至超过了欧美等发达国家市场。不过，任何因子都很难一直有效，到了2017-2018年期间，大市值股票的表现明显优于小市值股票，使得规模因子在A股市场上的有效性存疑。

我们在聚宽平台上，以它提供的示例策略，运行后，得到的结果表明，该因子在最近两年依然有效：

![](https://images.jieyu.ai/images/2023/04/**************.png)

下面，我们就先来实现这个策略，然后来讨论小市值策略应该如何使用的问题。

### 1.1. 初始化

```python
import logging
import os
from IPython.display import clear_output

import cfg4py
from coretypes import FrameType
import omicron
from omicron import tf
from omicron.models.security import Security
from omicron.models.stock import Stock

import jqdatasdk as jq


account = os.environ.get("jq_account")
passwd = os.environ.get("jq_password")
jq.auth(account, passwd)

cfg = cfg4py.init("/etc/zillionare")

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger("test")
# logger.addHandler(logging.StreamHandler(stream=sys.stdout))

await omicron.init()
clear_output()
```
### 1.2. 策略主体代码
```python
from jqdatasdk import query, valuation
import uuid
from traderclient import TraderClient
import datetime
from typing import List, Optional
import numpy as np
from traderclient.errors import TradeError
# from omicron.plotting.metrics import MetricsGraph

# set the envar during debugging mode only
# os.environ["TRADER_CLIENT_TIMEOUT"] = "600"


class SmallCapStrategy:
    def __init__(
        self,
        start: datetime.date,
        end: datetime.date,
        account: Optional[str] = None,
        token: Optional[str] = None,
        url: Optional[str] = None,
        pool_size_ratio=0.01,
    ):

        self.pool_size_ratio = pool_size_ratio
        self.bills = None
        self.metrics = None

        self.start = start
        self.end = end
        self.token = token or uuid.uuid4().hex
        self.account = account or f"smallcap-{self.token[-4:]}"

        self.broker = TraderClient(
            url or cfg.backtest.url,
            self.account,
            self.token,
            is_backtest=True,
            start=self.start,
            end=self.end,
        )

    async def backtest(self):
        for i, frame in enumerate(tf.get_frames(self.start, self.end, FrameType.DAY)):
            # 没到调仓时间
            if i % 5 != 0:
                continue

            dt = tf.int2date(frame)
            positions = self.broker.positions(dt)
            buylist = await self.choose_target(dt)

            to_sell = np.setdiff1d(positions["security"], buylist)
            to_buy = np.setdiff1d(buylist, positions["security"])

            # 优先处理卖出，以释放资金
            for sec in to_sell:
                try:
                    volume = self.broker.available_shares(sec, dt)
                    self.broker.market_sell(
                        sec, volume, order_time=tf.combine_time(dt, 14, 55)
                    )
                except TradeError as e:
                    logger.warning(str(e).split("\n")[0], date=dt)
                except Exception as e:
                    logger.exception(e)

            cash = self.broker.available_money
            per_stock = cash / len(to_buy)
            for sec in to_buy[:-1]:
                try:
                    await self.broker.buy_by_money(
                        sec, per_stock, order_time=tf.combine_time(dt, 14, 55)
                    )
                except TradeError as e:
                    logger.warning(str(e).split("\n")[0], date=dt)
                except Exception as e:
                    logger.exception(e)

            # 多余的资金结转到最后一支股票上
            cash = self.broker.available_money
            sec = to_buy[-1]
            await self.broker.buy_by_money(
                sec, cash, order_time=tf.combine_time(dt, 14, 55)
            )

        self.broker.stop_backtest()
        self.bills = self.broker.bills()
        self.metrics = self.broker.metrics(baseline="399300.XSHE")

    async def plot(self):
        mg = MetricsGraph(self.bills, self.metrics)
        await mg.plot()

    async def filter_paused_stock(self, buylist: List[str], dt: datetime.date):
        secs = await Security.select(dt).eval()
        in_trading = jq.get_price(
            secs, fields=["paused"], start_date=dt, end_date=dt, skip_paused=True
        )["code"].to_numpy()

        return np.intersect1d(buylist, in_trading)

    async def choose_target(self, dt: datetime.date):
        # 聚宽示例策略在这里加了20~30亿市值限制，也有它的道理。也许动态调整更好
        q = (
            query(valuation.code, valuation.market_cap)
            .filter(valuation.market_cap.between(20, 30))
            .order_by(valuation.market_cap.asc())
        )

        # 选出低市值的股票，构成buylist
        df = jq.get_fundamentals(q, dt)

        buylist = list(df["code"])

        # 过滤停牌股票
        buylist = await self.filter_paused_stock(buylist, dt)
        logger.debug("got %s secs after paused been filtered", len(buylist))

        # 随着市场扩容，可以多取，但最少取5支
        size = max(int(len(df) * self.pool_size_ratio), 5)
        return buylist[:size]
```
上述代码中，依赖了一个回测指标绘图的类，这里我们给出代码，暂不做展开：

```python
import datetime
import logging
from collections import defaultdict
from copy import deepcopy
from typing import List, Union

import arrow
import numpy as np
import pandas as pd
import plotly.graph_objects as go
from coretypes import BarsArray, Frame, FrameType
from numpy.typing import NDArray
from plotly.subplots import make_subplots

from omicron import tf
from omicron.extensions import fill_nan
from omicron.models.security import Security
from omicron.models.stock import Stock

logger = logging.getLogger(__name__)


class MetricsGraph:
    def __init__(self, bills: dict, metrics: dict):
        self.metrics = metrics
        self.trades = bills["trades"]
        self.positions = bills["positions"]
        self.start = arrow.get(bills["assets"][0][0]).date()
        self.end = arrow.get(bills["assets"][-1][0]).date()

        self.frames = [
            tf.int2date(f) for f in tf.get_frames(self.start, self.end, FrameType.DAY)
        ]

        # 记录日期到下标的反向映射，这对于在不o
        self._frame2pos = {f: i for i, f in enumerate(self.frames)}
        self.ticks = self._format_tick(self.frames)

        self.assets = pd.DataFrame(bills["assets"], columns=["frame", "assets"])[
            "assets"
        ].to_numpy()
        self.nv = self.assets / self.assets[0]

    def _fill_missing_prices(self, bars: BarsArray, frames: Union[List, NDArray]):
        """将bars中缺失值采用其前值替换

        当baseline为个股时，可能存在停牌的情况，这样导致由此计算的参考收益无法与回测的资产收益对齐，因此需要进行调整。

        出于这个目的，本函数只返回处理后的收盘价。

        Args:
            bars: 基线行情数据。
            frames: 日期索引

        Returns:
            补充缺失值后的收盘价序列
        """
        _close = pd.DataFrame(
            {
                "close": pd.Series(bars["close"], index=bars["frame"]),
                "frame": pd.Series(np.empty((len(frames),)), index=frames),
            }
        )["close"].to_numpy()

        # 这里使用omicron中的fill_nan，是因为如果数组的第一个元素即为NaN的话，那么DataFrame.fillna(method='ffill')将无法处理这样的情况(仍然保持为nan)

        return fill_nan(_close)

    def _format_tick(self, frames: Union[Frame, List[Frame]]) -> Union[str, NDArray]:
        if type(frames) == datetime.date:
            x = frames
            return f"{x.year:02}-{x.month:02}-{x.day:02}"
        elif type(frames) == datetime.datetime:
            x = frames
            return f"{x.month:02}-{x.day:02} {x.hour:02}:{x.minute:02}"
        elif type(frames[0]) == datetime.date:  # type: ignore
            return np.array([f"{x.year:02}-{x.month:02}-{x.day:02}" for x in frames])
        else:
            return np.array(
                [f"{x.month:02}-{x.day:02} {x.hour:02}:{x.minute:02}" for x in frames]  # type: ignore
            )

    async def _metrics_trace(self):
        metric_names = {
            "start": "起始日",
            "end": "结束日",
            "window": "资产暴露窗口",
            "total_tx": "交易次数",
            "total_profit": "总利润",
            "total_profit_rate": "利润率",
            "win_rate": "胜率",
            "mean_return": "日均回报",
            "sharpe": "夏普率",
            "max_drawdown": "最大回撤",
            "annual_return": "年化回报",
            "volatility": "波动率",
            "sortino": "sortino",
            "calmar": "calmar",
        }

        # bug: plotly go.Table.Cells format not work here
        metric_formatter = {
            "start": "{}",
            "end": "{}",
            "window": "{}",
            "total_tx": "{}",
            "total_profit": "{:.2f}",
            "total_profit_rate": "{:.2%}",
            "win_rate": "{:.2%}",
            "mean_return": "{:.2%}",
            "sharpe": "{:.2f}",
            "max_drawdown": "{:.2%}",
            "annual_return": "{:.2%}",
            "volatility": "{:.2%}",
            "sortino": "{:.2f}",
            "calmar": "{:.2f}",
        }

        metrics = deepcopy(self.metrics)
        baseline = metrics["baseline"]
        del metrics["baseline"]

        if "code" in baseline:
            baseline_name = await Security.alias(baseline["code"])
            del baseline["code"]
        else:
            baseline_name = "基准"

        metrics_formatted = []
        for k in metric_names.keys():
            if metrics.get(k):
                metrics_formatted.append(metric_formatter[k].format(metrics.get(k)) )
            else:
                metrics_formatted.append("-")

        baseline_formatted = []
        for k in metric_names.keys():
            if baseline.get(k):
                baseline_formatted.append(metric_formatter[k].format(baseline.get(k)) )
            else:
                baseline_formatted.append("-")
                
        return go.Table(
            header=dict(values=["指标名", "策略", baseline_name]),
            cells=dict(
                values=[
                    [metric_names[k] for k in metrics],
                    metrics_formatted,
                    baseline_formatted
                ],
                font_size=10,
            ),
        )

    async def _trade_info_trace(self):
        """构建hover text 序列"""
        X = []
        Y = []
        data = []

        # convert trades into hover_info
        merged = defaultdict(list)
        for _, trade in self.trades.items():
            trade_date = arrow.get(trade["time"]).date()

            ipos = self._frame2pos.get(trade_date)
            if ipos is None:
                logger.warning(
                    "date  %s in trade record not in backtest range", trade_date
                )
                continue

            name = await Security.alias(trade["security"])
            price = trade["price"]
            side = trade["order_side"]
            filled = trade["filled"]

            trade_text = f"{side}:{name} {filled/100:.0f}手 价格:{price:.02f} 成交额{filled * price/10000:.1f}万"

            merged[trade_date].append(trade_text)

        for dt, text in merged.items():
            ipos = self._frame2pos.get(dt)
            Y.append(self.nv[ipos])
            X.append(self._format_tick(dt))

            asset = self.assets[ipos]
            hover = f"资产:{asset/10000:.1f}万<br>{'<br>'.join(text)}"
            data.append(hover)

        trace = go.Scatter(x=X, y=Y, mode="markers", text=data, name="交易详情")
        return trace

    async def plot(self, baseline_code: str = "399300.XSHE"):
        """绘制资产曲线及回测指标图"""
        n = len(self.assets)
        bars = await Stock.get_bars(baseline_code, n, FrameType.DAY, self.end)

        baseline_prices = self._fill_missing_prices(bars, self.frames)
        baseline_prices /= baseline_prices[0]

        fig = make_subplots(
            rows=1,
            cols=2,
            shared_xaxes=False,
            specs=[
                [{"type": "scatter"}, {"type": "table"}],
            ],
            column_width=[0.75, 0.25],
            horizontal_spacing=0.01,
            subplot_titles=("资产曲线", "策略指标"),
        )

        fig.add_trace(await self._metrics_trace(), row=1, col=2)

        print("baseline", len(baseline_prices))
        baseline_trace = go.Scatter(
            y=baseline_prices,
            x=self.ticks,
            mode="lines",
            name="baseline",
            showlegend=True,
        )
        fig.add_trace(baseline_trace, row=1, col=1)

        nv_trace = go.Scatter(
            y=self.nv, x=self.ticks, mode="lines", name="策略净值", showlegend=True
        )
        fig.add_trace(nv_trace, row=1, col=1)

        trade_info_trace = await self._trade_info_trace()
        fig.add_trace(trade_info_trace, row=1, col=1)

        fig.update_xaxes(type="category", tickangle=45, nticks=len(self.ticks) // 5)
        fig.update_layout(margin=dict(l=20, r=20, t=50, b=50), width=1040, height=435)
        fig.show()
```

接下来，我们以一个较短的时间窗口调用一下这个策略。

```python
start = tf.day_shift(datetime.date(2023, 4, 1), 0)
end = tf.day_shift(datetime.date(2023, 4, 28), 0)

s = SmallCapStrategy(start, end)
await s.backtest()
await s.plot()
```

运行后，我们将得到下面的输出：

![](https://images.jieyu.ai/images/2023/05/smallcap.png)

阅读这个例子的源码，我们需要注意以下几点：
1. 一个最简单的策略，它的回测框架至少要包括哪些内容？
2. TraderClient的作用和基本使用方法？
3. 回测结束，如何将资产曲线和策略指标绘制出来？

### 1.3. 策略优化
有许多因素可能影响到策略优化。

首先，回测系统可以防止在跌停板上卖出，阻止在涨停板上买入，但不是相反。显然，我们不应该在涨停板上卖出股票，因为根据统计，连板指数大约是以每天1.8%的速度在上涨。因此，在涨停板上卖出股票，意味着我们将在这部分资金上损失1.8%的超额收益。这不是一个需要调节的参数，应该直接写死在我们的策略中。

其次，我们在简单地回测中，就发现买入了好几支ST的股票。实际上，这些标的是应该排除掉的，或者说，至少应该在年报前后排除掉。这一部分，至少排除掉ST个股，应该写死在我们的策略中。

接下来，是一些需要通过反复测试来进行调优的参数。

首先，注意到我们设置了一个pool_size_ratio这个参数。在一个较长跨度的回测中，我们需要注意到A股的交易品种是在不断增长的。如果我们固定地取20支股票，那么在2005年前后，这可能会占到小市值股票中的相当一部分。而策略的用意则是要找出最小市值的那一部分。

因此，我们设置了pool_size_ratio这个参数，并且为防止取值过少，我们还在代码中硬性规定了不得少于5支。

另外，策略并不是严格按照市值来进行筛选的。实际上，它首先排除掉了不在20亿到30亿这个区间的一些标的。这么做有一定的道理，因为一支股票如果市值太小，它的成长能力也可能不够强。但这应该成为一个被测试的参数。

再次，我们对持仓周期的选择是5天。这也是一个可以调节的参数。

最后，我们没有设定首次买入的时间。实际上，如果放在周四收盘买入，情况可能会有所不同。

这些参数应该作为SmallCapStrategy初始化参数的一部分，并且使用sklearn.model_selection.GridSearchCV来自动进行参数搜索。


## 2. 布林带策略
布林带（Bollinger Band）是20世纪80年代由约翰·布林格（John Bollinger）开发的市场趋势技术指标，用于衡量市场波动性和价格动态。在整个80年代，这个指标都非常好用。

布林带由上、中、下三条线组成。位于中间的中轨线主要用于衡量中期趋势，通常是简单的移动平均线，是上轨线和下轨线的基础。如今，布林带是MetaTrader 4交易平台上的一种标准工具，主要用于衡量波动性和预测当前趋势是否发生反转。

某支股票在2023年4月期间的布林通道图:
![](https://images.jieyu.ai/images/2023/05/bollingerbands.png)

从图上可以粗略地看出，bolling带似乎能给出较好的买卖点。但实际上还有几处失败的买点是我们没有标记出来的。

下面，我们看看布林带策略如何构建。这一次，我们将使用coursea库与omicron中的相关模块来简化策略编写。

首先，我们在之前的代码中，都要导入比较多的模块，并且进行了初始化。实际上，我们可以用coursea库来简化这个过程：

```python
from coursea import *
await init()
```
这相当于实现了下面的功能:

```python
import datetime
import logging
from typing import Dict, List, Optional, Union

import cfg4py
import numpy as np
import omicron
import pandas as pd
import talib
from coretypes import Frame, FrameType
from omicron import tf
from omicron.extensions import (
    array_math_round,
    array_price_equal,
    bars_since,
    count_between,
    fill_nan,
    find_runs,
    math_round,
    price_equal,
    smallest_n_argpos,
    top_n_argpos
)
from omicron.models.security import Security
from omicron.models.stock import Stock
from omicron.plotting.candlestick import Candlestick
from omicron.plotting.metrics import MetricsGraph
from omicron.strategy.base import BaseStrategy
from traderclient import TraderClient

cfg = cfg4py.get_instance()

async def init():
    cfg4py.init("/etc/zillionare")
    await omicron.init()
```

其次，`omicron.strategy.base.BaseStrategy`库已经为我们提供了一个策略基类。这个基类封装了`trader-client`，提供了策略回测的基础框架。如果我们的策略子类继承于这个基类，那么，很多情况下，我们只需要实现构造函数和`predict`方法即可完成一个策略。这样策略的实现会变得非常简单，因此出错的机会也会变少。

```python
class BollingerBandsStrategy(BaseStrategy):
    def __init__(self, url: str, win: int = 20, k: int = 2, **kwargs):
        """
        Args:
            url: 回测/实盘服务器
            win: 计算移动平均线的周期数
            k: 使用`k`个标准差来构成上下band
        """
        self.win = win
        self.k = k
        super().__init__(url, **kwargs)

    async def predict(self, frame: Frame, frame_type: FrameType, i: int, sec: str):
        bars = await Stock.get_bars(sec, self.win + 1, frame_type, end=frame)
        close = array_math_round(bars["close"], 2).astype(np.float64)

        hb, mid, lb = talib.BBANDS(close, self.win)

        if close[-1] > hb[-1] and close[-2] <= hb[-2]:
            if self.available_shares(sec, frame) > 0:
                await self.sell(
                    sec, percent=1.0, order_time=tf.combine_time(frame, 14, 55)
                )
            return

        if close[-1] < lb[-1] and close[-2] >= lb[-2]:
            if len(self.positions(frame)) == 0:
                await self.buy(
                    sec, money=self.cash, order_time=tf.combine_time(frame, 14, 55)
                )
                print(i, frame, self.positions(frame))

    async def kplot(self, sec: str, start: Frame, end: Frame, frame_type: FrameType):
        bars = await Stock.get_bars_in_range(sec, frame_type, start, end)
        cs = Candlestick(bars)
        cs.add_indicator("bbands")

        close = array_math_round(bars["close"], 2)
        hb, mid, lb = talib.BBANDS(close, self.win)

        xpos = np.argwhere(close > hb).flatten()
        cs.add_marks(xpos, hb[xpos] * 1.05, "upbreak", color="#00f")

        xpos = np.argwhere(close < lb).flatten()
        cs.add_marks(xpos, lb[xpos] * 0.95, "downbreak", color="#f00")

        cs.plot()


start = tf.day_shift(datetime.date(2022, 1, 4), 0)
end = tf.day_shift(datetime.datetime.now(), 0)

bbs = BollingerBandsStrategy(
    cfg.backtest.url, start=start, end=end, frame_type=FrameType.DAY
)
await bbs.backtest(sec="000001.XSHE")
await bbs.kplot("000001.XSHE", start, end, FrameType.DAY)
await bbs.plot_metrics()
```
## 3. 网格交易法
### 3.1. 简介
网格交易是指以固定间隔的价格下单，利用资产价格的波动来获得的交易方式。

一般而言，在单边做多的市场，网格交易有顺势或者逆势（区间震荡）两种方式。所谓顺势，就是在资产价格高于某个基线后，以固定的价格区间来买入并持有，直到所有网格被贯穿后，再通过止赢策略退出；在逆势模式下，是指交易者在设定的价格（中枢）以下，以固定的网格价格下多单，而在中枢以上分批卖出获利。

顺势交易背后的想法是，如果价格沿趋势上涨，那么头寸就会变大，从而带来更大的收益；难点在于何时结束网格，退出交易，否则，价格也可能反转，利润会消失。逆势交易的问题在于，风险不容易控制。如果价格继续下跌，交易者持续买入，

经典的网格交易（vanlila grid trading）没有预言市场走势的能力，所以，很难指望我们对任意品种，在任意时间进场，都能获得较好的收益。但是，通过恰当的择时和标的的选择，则有可能实现稳定的赢利。

本次课程中，我们将介绍 vanlila grid trading 如何实现，并且讨论如何改进网格交易，提高它的成功率。

### 3.2. 震荡场景下单边做多的网格交易



#### 3.2.1. 代码
```python
from coursea import *
from omicron.strategy.base import BaseStrategy

await init()


class GridStrategy(BaseStrategy):
    def __init__(
        self,
        url: str,
        sec: str,
        pivot_price: float,
        tranch_size: float,
        interval: float = 0.02,
        grids=10,
        **kwargs,
    ):
        self.sec = sec
        self.pivot_price = pivot_price
        self.interval = interval
        self.tranch_size = tranch_size
        self.grids = grids
        self.records = {g: 0 for g in range(-grids // 2, grids // 2 + 1)}

        self.bands = array_math_round(
            [
                self.pivot_price * (1 + i * interval)
                for i in range(-grids // 2, grids // 2 + 1)
            ],
            2,
        )

        self._last_grid = 0

        super().__init__(url, **kwargs)

    def grid_level(self, price: float) -> int:
        """通过价格搜索对应的网格

        Examples:
            假如网格如下所示：
             0     1     2      3     4
            -2    -1     0      1     2
            4.44  4.54  4.63   4.72  4.82

            则有:
            >>> gs = GridStrategy("", "", 4.63, is_backtest=False)
            >>> gs.grid_levels(4)
            -2

            >>> gs.grid_levels(4.43)
            -1

            >>> gs.grid_levels(4.55)
            0

            >>> gs.grid_levels(4.71)
            0

            >>> gs.grid_levels(4.81)
            1

            >>> gs.grid_levels(100)
            2
        """
        if price >= self.pivot_price:
            pos = np.searchsorted(self.bands, price, side="right").item()
            return pos - self.grids // 2 - 1
        else:
            pos = np.searchsorted(self.bands, price, side="left").item()
            return pos - self.grids // 2

    def _find_position_for_sell(self, grid: int, asked: float) -> float:
        """查找可售出仓位

        """
        vol = 0
        margin = 0
        for i in range(-self.grids // 2, grid):
            margin = asked - vol
            if margin <= 0:
                return vol

            if self.records[i] > 0:
                size = min(margin, self.records[i])
                self.records[i] -= size  # type: ignore
                vol += size

        if margin > 0:
            logger.info("找不到足够的持有仓位出售。要求%s,实际%s。这不一定是错误。", asked, vol)

        return vol

    async def predict(self, frame: Frame, frame_type: FrameType, i: int, **kwargs):
        bars = await Stock.get_bars(self.sec, 1, frame_type=frame_type, end=frame)

        price = bars["close"][0]
        grid = self.grid_level(price)
        logger.info("%s %s->%s", frame, self._last_grid, grid, date=frame)

        strides = grid - self._last_grid
        self._last_grid = grid
        # 达到网格顶部，全部售出
        avail = self.available_shares(self.sec, frame)
        if grid == self.grids // 2 and avail > 0:
            await self.sell(
                self.sec, vol=avail, order_time=tf.combine_time(frame, 14, 55)
            )
            logger.info("触及网格顶部，全部止盈。", date=frame)
            return

        # 如果新网格大于前一天网格，查看是否有可卖出股份
        if strides > 0:
            asked = (strides * self.tranch_size / price)//100 * 100
            vol = min(avail, self._find_position_for_sell(grid, asked))
            if vol > 0:
                await self.sell(
                    self.sec, vol=vol, order_time=tf.combine_time(frame, 14, 55)
                )
                logger.info("%s 卖出 %s", date=frame)
        elif strides < 0:
            # 如果新网格小于前一天网格，买入
            result = await self.buy(
                self.sec,
                money=self.tranch_size,
                order_time=tf.combine_time(frame, 14, 55),
            )

            filled = result.get('filled', 0)
            self.records[grid] += filled
            logger.info("%s 买入 %s, 记入grid %s", frame, filled, grid, date=frame)



start = datetime.date(2023, 3, 9)
end = datetime.date(2023, 4, 11)
gs = GridStrategy(
    cfg.backtest.url,
    "002344.XSHE",
    4.63,
    100_000,
    interval = 0.04,
    start=start,
    end=end,
    frame_type=FrameType.DAY,
    baseline = "002344.XSHE"
)

await gs.backtest()
await gs.plot_metrics()
```
### 3.3 改进网格交易

网格交易的最大问题，是它自己没有判断趋势的能力。在踏不准趋势的情况下，网格交易可能亏损，或者产生负超额。

我们这里以提到的顺势模型和震荡模型，加上下跌走势、震荡走势和上升走势来讨论各个组合下的收益情况。

|          | 下跌走势 | 震荡走势         | 上升走势       |
| -------- | -------- | ---------------- | -------------- |
| 顺势模型 | 无信号   | 少信号           | 信号多，超额少 |
| 逆势模型 | 被套     | 赢利，超额收益好 | 无信号         |

可以看出，如果不能正确确定标的走势，选错模型的话，收益也会大打折扣，甚至亏损。

这里我们从以下几个方向来讨论网格的改进方案。

#### 3.3.1 择时和标的选择

在任何时候，预言趋势都是非常困难的。实际上，如果我们能以较大的概率成功预测趋势的话，就不一定要用网格交易了。比如，如果我们能正确预测一段上升走势，那么显然应该在底部重仓，而不是使用网格交易的顺势交易模型。

但就网格交易而言，我们还是可以提供一些思路，供大家在选择标的和择时时参考。

网格交易最适合的区间是震荡行情，所谓震荡行情，就是上有封顶，下有保底的行情。我们可以围绕这两个特征来深入思考一下，什么样的时间、什么样的标点容易满足这两个条件，或者，至少满足其中之一。

#### 3.3.2. 超跌标的可能出现震荡走势

从行为金融学的角度上来讲，如果某个标的短期内大幅下跌，这对持仓者心理上的创伤将是巨大的。在这种情况下，多数人会选择持股不动。此时因为人气不足，拉升将非常困难。但如果主力看好该标的的未来，有可能进行一段较长时间的建仓。因此，在后期的走势上，就表现为震荡走势。这就是网格交易候选标的之一。

大家可以通过同花顺的智能选股，以1年内跌幅大于50%为条件选几支看看。这些标的当中，有的是已经出现震荡行情一段时间了，有的是还在下跌当中。可以看出，这个思路是有一定的道理的。

#### 3.3.3. 可转债天然具有保底属性

市场常常以”下有保底，上不封顶“来形容可转债。这是因为，可转债既有债券属性，也有股票属性。债券属性让可转债在下跌过程中能够和普通债券一样，具有“保底+付息”的特性，可转债发行的面值为100元，市场上少有看到百元以下可转债。这是债券属性在发挥作用。另一方面，可转债又附带股票属性，让其在上涨的过程中跟随正股涨幅，可转债市场上也可以看到增长几倍乃至几十倍的可转债，这是股票属性在发挥作用。

既然可转债有保底能力，我们就可以从某个中枢开始，每下跌一定比例就加一部分仓位，直到进入保底价位时重仓，再在上涨过程中抛出。这属于逆势模型，但由于可转债的保底属性，因此我们不必承担逆势模型下的各种风险。

当然，可转债不是完全没有风险，它的主要风险是债务违约。比如今年发生的搜特转债。如果随正股一起退市，那么这支债券持有人恐怕也会血本无归。当然，我们也有办法避免，就是不选择正股可能退市的转债。


### 3.4 技术优化

#### 3.4.1 基线价格确定

#### 3.4.2 动态中枢

某个品种在长期震荡吸筹之后，往往有两种走势。一是震荡盘升；二是拉升失败，选择向下突破，短线再拿一波筹码，再以这些筹码为基础，实现拉升。这里我们只讲前一种情况应该如何运用网格交易。至于后面的情况，我们应该是通过一些数学的方法，第一时间判断主力拉升失败，从而第一时间止损出局。这种情况下，要看我们是何时跟踪这支标的来做网格的，如果在之前的震荡部分我们介入较早，则仍然是大概率盈利的。

在震荡上升区间，如果我们采用的网格模型是逆势模型，那我们将在股价突破平台的那一刻，失去全部仓位。如果我们考虑波浪理论，一个幅度较大、时间较长的拉升只会在C浪时才出现，而在震荡转上升区间时，股价还会有反复，此时仍然可以用网格来跟踪，只不过我们需要及时修正中枢价格。

从实现上看，动态中枢略微复杂一些，因为随着中枢的抬高，有可能之前的卖出线转换成为买入线，所以，这部分仓位的卖出价格也需要相应调整。
