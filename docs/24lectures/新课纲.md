## 量化交易概论
### 证券投资和量化交易简史
### 量化交易知识体系
### 量化策略浅探
### 做量化交易需要的知识储备
#### Python知识
#### 数学和统计知识
#### 基础证券知识
### 什么样的人适合做量化？
  
## 证券常识
### 交易各方及交易所、证券编码
### 交易制度及其对量化的影响
<!--交易制度、规则的改变对交易(也包括量化交易)有重大影响。在我们进行跨较长历史周期的回测时，一定要了解这期间是否有交易制度的重大变更。这些变更，可能导致数据统计口径前后不一致，要么导致定价体系不一致--错误定价会带来价格回归炒作。比如如IPO发行抑价，导致新股上市炒作。如果我们不了解这些因素对量化回测的影响，就不能正确解释历史上某段时间量化策略过好、或者过差的表现，并且很可能进行错误的优化。-->
#### 发行和退市
<!--发行抑价、IPO暂停带来的新股炒作机会-->
#### 竞价规则
<!--盘后固定价格。一些回测软件认为以当日收盘价进行买入，是一种偷价行为。但实际上对创业板、科创板，如果委托量不大，这是可能的-->
#### 复权
#### 风险警示处理
#### 股权分置改革
#### 融资融券制度
#### 沪深港通（北向）

## 量化交易之数据篇
### Akshare
#### 
- [在课件环境下安装 akshare](#211-在课件环境下安装-akshare)
- [*******. 实时股票数据](#212-实时股票数据)
- [*******. 股票历史数据](#213-股票历史数据)
- [*******. 证券列表](#214-证券列表)
- [*******. 交易日历](#215-交易日历)
- [*******. 封装和改进建议](#216-封装和改进建议)
- [*******. 练习](#217-练习)
  
## 第3课 数据源之 Tushare、JqDataSdk
  - [3.1. TUSHARE](#1-tushare)
    - [3.1.1. 在课件环境下安装和设置 token](#11-在课件环境下安装和设置-token)
    - [3.1.2. 股票历史数据](#12-股票历史数据)
    - [3.1.3. 证券列表](#13-证券列表)
    - [3.1.4. 交易日历](#14-交易日历)
  - [3.2. 聚宽本地数据](#2-聚宽本地数据)
    - [3.2.1. 在课件环境下安装和设置账号](#21-在课件环境下安装和设置账号)
    - [3.2.2. 股票历史数据](#22-股票历史数据)
    - [3.2.3. 证券列表](#23-证券列表)
    - [3.2.4. 交易日历](#24-交易日历)
  - [3.3. BAOSTOCK](#3-baostock)
    - [3.3.1. 股票历史数据](#31-股票历史数据)
    - [3.3.2. 证券列表](#32-证券列表)
  - [3.4. YFINANCE](#4-yfinance)
  
## 第4课 使用Zillionare来获取数据
  - [4.1. Omicron](#1-omicron)
    - [4.1.1. 初始化 omicron](#11-初始化-omicron)
    - [4.1.2. 实时股票数据](#12-实时股票数据)
    - [4.1.3. 股票历史数据](#13-股票历史数据)
    - [4.1.4. 证券列表](#14-证券列表)
    - [4.1.5. 交易日历](#15-交易日历)
    - [4.1.6. 板块数据](#16-板块数据)
  - [4.2. 数据解读 A 股: 投资者人数与市场走势关系](#2-数据解读-a-股-投资者人数与市场走势关系)
  
## 第6课 小市值策略
  - [6.1. 小市值策略简介](#1-小市值策略简介)
  - [6.2. 策略实现](#2-策略实现)
    - [6.2.1. 初始化](#21-初始化)
    - [6.2.2. 绘图](#22-绘图)
    - [6.2.3. 策略主体代码](#23-策略主体代码)
  - [6.3. 策略优化](#3-策略优化)
    - [6.3.1. 择时优化](#31-择时优化)
    - [6.3.2. 规则优化](#32-规则优化)
    - [6.3.3. 参数优化](#33-参数优化)
  
## 第7课 布林带策略
  - [7.1. 使用coursea的初始化](#1-使用coursea的初始化)
  - [7.2. 基于基类的布林带策略](#2-基于基类的布林带策略)
  - [7.3. 策略优化方向讨论](#3-策略优化方向讨论)
    - [7.3.1. 参数优化](#31-参数优化)
    - [7.3.2. 趋势判断](#32-趋势判断)
  
## 第8课 
  - [8.1. 什么是网格交易？](#1-什么是网格交易)
  - [8.2. 代码实现](#2-代码实现)
    - [8.2.1. 初始化](#21-初始化)
    - [8.2.2. 评估函数](#22-评估函数)
    - [8.2.3. 策略行为分析](#23-策略行为分析)
  - [8.3. 技术实现问题](#3-技术实现问题)
    - [8.3.1. 委托价格和交易时机](#31-委托价格和交易时机)
    - [8.3.2. 送转问题](#32-送转问题)
    - [8.3.3. 交易单位](#33-交易单位)
  - [8.4. 策略优化: 从1.37%到79.8%!](#4-策略优化-从137到798)
    - [8.4.1. 选择有“界”的标的](#41-选择有界的标的)
      - [8.4.1.1. 大市值股票](#411-大市值股票)
      - [8.4.1.2. 超跌股票](#412-超跌股票)
      - [8.4.1.3. 可转债](#413-可转债)
    - [8.4.2. 基于历史数据，确定网格参数](#42-基于历史数据确定网格参数)
    - [8.4.3. 提高资金利用率](#43-提高资金利用率)
  - [8.5. 趋势跟踪网格](#5-趋势跟踪网格)
  
## 第9课 Numpy 和 Pandas
  - [9.1. Numpy](#1-numpy)
    - [9.1.1. 创建数组](#11-创建数组)
      - [9.1.1.1. vanilla version](#111-vanilla-version)
      - [9.1.1.2. 预置特殊数组](#112-预置特殊数组)
      - [9.1.1.3. 通过已有数组转换](#113-通过已有数组转换)
    - [9.1.2. 查看 (inspecting) 数组特性](#12-查看-inspecting-数组特性)
    - [9.1.3. 数组操作](#13-数组操作)
      - [9.1.3.1. 升维](#131-升维)
      - [9.1.3.2. 降维](#132-降维)
      - [9.1.3.3. 转置](#133-转置)
      - [9.1.3.4. 增加/删除元素](#134-增加删除元素)
    - [9.1.4. 逻辑运算和比较](#14-逻辑运算和比较)
    - [9.1.5. 集合运算](#15-集合运算)
    - [9.1.6. 数学运算](#16-数学运算)
      - [9.1.6.1. 点乘](#161-点乘)
      - [9.1.6.2. 聚合运算和统计函数](#162-聚合运算和统计函数)
    - [9.1.7. 读取、查找和搜索](#17-读取查找和搜索)
      - [9.1.7.1. 索引和切片](#171-索引和切片)
      - [9.1.7.2. 查找、替换、筛选](#172-查找替换筛选)
    - [9.1.8. 类型转换和 typing module](#18-类型转换和-typing-module)
    - [9.1.9. Structured Array](#19-structured-array)
    - [9.1.10. IO](#110-io)
    - [9.1.11. 量化交易中常用函数示例](#111-量化交易中常用函数示例)
      - [9.1.11.1. REF(close, n)](#1111-refclose-n)
      - [9.1.11.2. EVERY(cond, n)](#1112-everycond-n)
      - [9.1.11.3. LAST(cond_list, n, m)](#1113-lastcond_list-n-m)
      - [9.1.11.4. BARSLAST](#1114-barslast)
      - [9.1.11.5. CROSS](#1115-cross)
  - [9.2. Pandas](#2-pandas)
    - [9.2.1. creation](#21-creation)
    - [9.2.2. 数据访问](#22-数据访问)
    - [9.2.3. 遍历 dataframe](#23-遍历-dataframe)
  - [9.3. pandas vs numpy](#3-pandas-vs-numpy)
  
## 第10课 Ta-Lib
  - [10.1. 安装talib](#1-安装talib)
    - [10.1.1. 原生库的安装](#11-原生库的安装)
      - [10.1.1.1. Macos](#111-macos)
      - [10.1.1.2. Linux](#112-linux)
      - [10.1.1.3. Windows](#113-windows)
      - [10.1.1.4. 使用conda](#114-使用conda)
      - [10.1.1.5. 第三方构建的wheel包](#115-第三方构建的wheel包)
    - [10.1.2. 安装python wrapper](#12-安装python-wrapper)
  - [10.2. ta-lib概览](#2-ta-lib概览)
    - [10.2.1. 关于帮助文档](#21-关于帮助文档)
    - [10.2.2. 两类接口](#22-两类接口)
    - [10.2.3. 方法概览](#23-方法概览)
  - [10.3. 常用指标函数](#3-常用指标函数)
    - [10.3.1. ATR](#31-atr)
    - [10.3.2. 移动平均线](#32-移动平均线)
      - [10.3.2.1. SMA](#321-sma)
      - [10.3.2.2. EMA](#322-ema)
      - [10.3.2.3. WMA](#323-wma)
    - [10.3.3. 布林带](#33-布林带)
    - [10.3.4. MACD](#34-macd)
    - [10.3.5. RSI](#35-rsi)
    - [10.3.6. OBV （on-balance volume)](#36-obv-on-balance-volume)
  - [10.4. 模式识别函数](#4-模式识别函数)
    - [10.4.1. CDL3LINESTRIKE](#41-cdl3linestrike)
    - [10.4.2. CDL3WHITESOLDIERS](#42-cdl3whitesoldiers)
  
## 第11课 Python与数据科学（1）
  - [11.1. 考察数据分布](#1-考察数据分布)
    - [11.1.1. 寻找数据的中心](#11-寻找数据的中心)
      - [11.1.1.1. 均值和质心](#111-均值和质心)
      - [11.1.1.2. 中位数](#112-中位数)
      - [11.1.1.3. 众数](#113-众数)
    - [11.1.2. 量化数据的分散程度](#12-量化数据的分散程度)
      - [11.1.2.1. 分位数](#121-分位数)
      - [11.1.2.2. 方差和标准差](#122-方差和标准差)
      - [11.1.2.3. 频数、PMF、PDF、CDF、PPF和直方图](#123-频数pmfpdfcdfppf和直方图)
      - [11.1.2.4. 概率密度和概率密度函数](#124-概率密度和概率密度函数)
      - [11.1.2.5. 累积概率和累积概率函数CDF](#125-累积概率和累积概率函数cdf)
      - [11.1.2.6. CDF估计及其应用](#126-cdf估计及其应用)
      - [11.1.2.7. 几个概念之间的关系](#127-几个概念之间的关系)
    - [11.1.3. 数据的分布形态](#13-数据的分布形态)
    - [11.1.4. 中心矩的概念](#14-中心矩的概念)
    - [11.1.5. 偏度、峰度在投资中的解释与应用](#15-偏度峰度在投资中的解释与应用)
  
## 第12课 数据分析与Python实现（2）
  - [12.1. 统计推断方法](#1-统计推断方法)
    - [12.1.1. 分位图](#11-分位图)
    - [12.1.3. 假设检验方法](#13-假设检验方法)
  - [12.2. 拟合、回归和残差](#2-拟合回归和残差)
    - [12.2.1. 残差及其度量](#21-残差及其度量)
      - [12.2.1.1. max_error](#211-max_error)
      - [1*******. mean_absolute_error](#212-mean_absolute_error)
      - [1*******. mean_absolute_percentage_error](#213-mean_absolute_percentage_error)
      - [1*******. mean_squared_error](#214-mean_squared_error)
      - [1*******. rooted mean squared error](#215-rooted-mean-squared-error)
    - [12.2.2. 回归分析](#22-回归分析)
  - [12.3. 相关性](#3-相关性)
    - [12.3.1. 协方差和相关系数](#31-协方差和相关系数)
    - [12.3.2. 皮尔逊相关性和斯皮尔曼相关性](#32-皮尔逊相关性和斯皮尔曼相关性)
    - [12.3.3. 相关性分析示例](#33-相关性分析示例)
  - [12.4. 距离和相似性](#4-距离和相似性)
    - [12.4.1. 常见距离定义列举](#41-常见距离定义列举)
    - [12.4.2. 如何计算距离](#42-如何计算距离)
  - [12.5. 归一化](#5-归一化)
  
## 第13课 技术分析实战
  - [13.1. 箱体的检测](#1-箱体的检测)
    - [13.1.1. 基于统计的方法](#11-基于统计的方法)
    - [13.1.2. 基于聚类的算法](#12-基于聚类的算法)
  - [13.2. 寻找山峰与波谷](#2-寻找山峰与波谷)
    - [13.2.1. scipy中的实现](#21-scipy中的实现)
    - [13.2.2. 第三方库：zigzag](#22-第三方库zigzag)
    - [13.2.3. 如何平滑曲线](#23-如何平滑曲线)
    - [13.2.4. 双顶模式的检测](#24-双顶模式的检测)
    - [13.2.5. 圆弧底的检测](#25-圆弧底的检测)
  - [13.3. 凹凸性检测](#3-凹凸性检测)
  
## 第14课 因子分析
  - [14.1. 因子分类](#1-因子分类)
  - [14.2. 因子分析](#2-因子分析)
    - [14.2.1. 预处理](#21-预处理)
      - [********. 异常值处理](#211-异常值处理)
      - [********. 缺失值处理](#212-缺失值处理)
      - [********. 分布调整](#213-分布调整)
      - [********. 标准化](#214-标准化)
      - [********. 中性化](#215-中性化)
  - [14.3. 单因子测试](#3-单因子测试)
    - [14.3.1. 回归法](#31-回归法)
      - [********. 回归法的因子评价](#311-回归法的因子评价)
    - [14.3.2. IC分析法](#32-ic分析法)
      - [********. IC分析法的因子评价](#321-ic分析法的因子评价)
    - [14.3.3. 分层回测法](#33-分层回测法)
    - [14.代码实现](#代码实现)
    - [14.3.4. 三种方法的区别与联系](#34-三种方法的区别与联系)
  - [14.因子评价体系](#因子评价体系)
  
## 第15课 Alphalens及其它
  - [15.1. Alphalens](#1-alphalens)
    - [15.1.1. Alphalens调用流程](#11-alphalens调用流程)
    - [15.1.2. 数据预处理](#12-数据预处理)
    - [15.1.3. 因子分析](#13-因子分析)
    - [15.1.4. Alphalens常见错误和警告](#14-alphalens常见错误和警告)
      - [15.1.4.1. 时区问题](#141-时区问题)
      - [15.1.4.2. MaxLossExceedError](#142-maxlossexceederror)
      - [********. FutureWarning](#143-futurewarning)
  - [15.2. JQFactor和jqfactor-analyzer](#2-jqfactor和jqfactor-analyzer)
  - [15.3. sympy](#3-sympy)
  - [15.4. statistics](#4-statistics)
  - [15.5. statsmodels](#5-statsmodels)
    - [15.5.1. OLS（普通最小二乘法）估计](#51-ols普通最小二乘法估计)
    - [15.5.2. 比较OLS与RLM](#52-比较ols与rlm)
    - [15.5.3. ARIMA模型与时间序列预测](#53-arima模型与时间序列预测)
  - [15.6. zipline](#6-zipline)
  - [15.7. pyfolio](#7-pyfolio)
  - [15.8. ta](#8-ta)
  
## 第16课 Matplotlib 绘图
  - [16.1. matplot 简介](#1-matplot-简介)
  - [16.2. 图是如何构成的](#2-图是如何构成的)
    - [16.2.1. 最顶层的概念](#21-最顶层的概念)
    - [16.2.2. pyplot, Figure与Axes之间的关系](#22-pyplot-figure与axes之间的关系)
    - [16.2.3. layout](#23-layout)
    - [16.2.4. Figure Anatomy](#24-figure-anatomy)
  - [16.3. 高频使用对象](#3-高频使用对象)
    - [16.3.1. Axis](#31-axis)
      - [********. spine定位与隐藏](#311-spine定位与隐藏)
      - [********. 共享x轴](#312-共享x轴)
      - [********. 刻度](#313-刻度)
    - [16.3.2. 文本和中文](#32-文本和中文)
    - [16.3.3. 样式和颜色](#33-样式和颜色)
      - [********. colormap](#331-colormap)
  
## 第17课 Plotly 绘图
  - [17.1. Plotly 中的基本概念](#1-plotly-中的基本概念)
  - [17.2. Plotly 模块结构](#2-plotly-模块结构)
    - [17.2.1. Plotly Express](#21-plotly-express)
    - [17.2.2. Graph Objects](#22-graph-objects)
    - [17.2.3. 其它](#23-其它)
  - [17.3. 比较 plotly express 与 go.Figure](#3-比较-plotly-express-与-gofigure)
  - [17.4. Plotly 股票分析图绘制](#4-plotly-股票分析图绘制)
    - [17.4.1. K 线图绘制](#41-k-线图绘制)
    - [17.4.2. 叠加技术指标](#42-叠加技术指标)
    - [17.4.3. 子图](#43-子图)
    - [17.4.4. 显示区域](#44-显示区域)
    - [17.4.5. 交互式提示](#45-交互式提示)
  - [17.5. 色彩](#5-色彩)
    - [17.5.1. 离散色彩序列](#51-离散色彩序列)
    - [17.5.2. 连续色阶](#52-连续色阶)
  - [17.6. 主题和模板](#6-主题和模板)
  - [17.7. Dash 简介](#7-dash-简介)
    - [17.7.1. Hellow World](#71-hellow-world)
    - [17.7.2. 连接到数据](#72-连接到数据)
    - [17.7.3. 增加交互式控件](#73-增加交互式控件)
    - [17.7.4. 美化应用程序](#74-美化应用程序)
    - [17.7.5. 深入Dash](#75-深入dash)
  
## 第18课 Seaborn 与 PyEcharts 绘图
  - [18.1. Seaborn](#1-seaborn)
    - [18.1.1. Seaborn 绘图概览](#11-seaborn-绘图概览)
      - [18.1.1.1. 统计关系的可视化](#111-统计关系的可视化)
      - [18.1.1.2. 数据分布的可视化](#112-数据分布的可视化)
      - [18.1.1.3. 二元分布的可视化](#113-二元分布的可视化)
      - [18.1.1.4. 联合分布和边缘分布](#114-联合分布和边缘分布)
      - [18.1.1.5. 回归拟合](#115-回归拟合)
    - [18.1.2. 主题](#12-主题)
    - [18.1.3. 调色板的使用](#13-调色板的使用)
      - [18.1.3.1. 定性调色板](#131-定性调色板)
      - [18.1.3.2. 连续调色板](#132-连续调色板)
      - [18.1.3.3. 发散调色板](#133-发散调色板)
  - [18.2. PyEcharts](#2-pyecharts)
    - [18.2.1. 在 Notebook/Jupyterlab 中运行](#21-在-notebookjupyterlab-中运行)
    - [18.2.2. 调用习惯](#22-调用习惯)
    - [18.2.3. 使用选项](#23-使用选项)
    - [18.2.4. 子图和布局](#24-子图和布局)
      - [18.2.4.1. Grid 布局](#241-grid-布局)
      - [18.2.4.2. Page 布局](#242-page-布局)
      - [18.2.4.3. tab 布局](#243-tab-布局)
      - [18.2.4.4. Timeline](#244-timeline)
  - [18.3. 关于颜色和美学](#3-关于颜色和美学)
  
## 第19课 backtrader 回测框架（1）
  - [19.1. 快速开始](#1-快速开始)
  - [19.2. backtrader 语法糖](#2-backtrader-语法糖)
    - [19.2.1. 时间线 (Line)](#21-时间线-line)
    - [19.2.2. 运算符重载](#22-运算符重载)
  - [19.3. Data Feeds](#3-data-feeds)
    - [19.3.1. GenericCSVData](#31-genericcsvdata)
    - [19.3.2. Pandas Feed](#32-pandas-feed)
    - [19.3.3. 自定义一个 Feed](#33-自定义一个-feed)
    - [19.3.4. 增加新的数据列](#34-增加新的数据列)
  - [19.4. 多周期数据](#4-多周期数据)
    - [19.4.1. 多周期技术指标比较](#41-多周期技术指标比较)
  - [19.5. 指标](#5-指标)
    - [19.5.1. 内置指标库](#51-内置指标库)
    - [19.5.2. 自定义指标](#52-自定义指标)
      - [19.5.2.1. 最小周期](#521-最小周期)
  
## 第20课 backtrader 回测框架（2）
  - [20.1. Cerebro](#1-cerebro)
    - [20.1.1. 增加记录器（日志）](#11-增加记录器日志)
    - [20.1.2. 增加观察者](#12-增加观察者)
    - [20.1.3. 执行与绘图](#13-执行与绘图)
  - [20.2. Order](#2-order)
    - [20.2.1. notify_order](#21-notify_order)
  - [20.3. 交易代理](#3-交易代理)
    - [20.3.1. 资产与持仓查询](#31-资产与持仓查询)
    - [20.3.2. 成交量限制](#32-成交量限制)
      - [20.3.2.1. FixedSize](#321-fixedsize)
      - [20.3.2.2. FixedBarPerc](#322-fixedbarperc)
      - [20.3.2.3. BarPointPerc](#323-barpointperc)
    - [20.3.3. 交易时机 - Cheat-On-Open](#33-交易时机---cheat-on-open)
    - [20.3.4. 交易时机 - Cheat-on-Close](#34-交易时机---cheat-on-close)
    - [20.3.5. 交易函数](#35-交易函数)
      - [20.3.5.1. 普通交易函数](#351-普通交易函数)
      - [20.3.5.2. order_target 系列](#352-order_target-系列)
    - [20.3.6. 组合交易](#36-组合交易)
    - [20.3.7. OCO 订单](#37-oco-订单)
    - [20.3.8. 滑点、交易费用](#38-滑点交易费用)
      - [20.3.8.1. 固定滑点](#381-固定滑点)
      - [20.3.8.2. 百分比滑点](#382-百分比滑点)
    - [20.3.9. 交易费用](#39-交易费用)
  - [20.4. 可视化](#4-可视化)
    - [20.4.1. 观察器](#41-观察器)
      - [20.4.1.1. Broker 观察器](#411-broker-观察器)
      - [20.4.1.2. BuySell 观察器](#412-buysell-观察器)
      - [20.4.1.3. Trade 观察器](#413-trade-观察器)
      - [20.4.1.4. TimeReturn 观察器](#414-timereturn-观察器)
      - [20.4.1.5. DrawDown 观察器](#415-drawdown-观察器)
      - [20.4.1.6. Benchmark 观察器](#416-benchmark-观察器)
    - [20.4.2. 定制绘图](#42-定制绘图)
    - [20.4.3. 收集回测数据](#43-收集回测数据)
  - [20.5. 优化](#5-优化)
  - [20.6. 小结](#6-小结)
  
## 第21课 策略回测评估
  - [21.1. 回报率](#1-回报率)
    - [21.1.1. 简单回报率](#11-简单回报率)
    - [21.1.2. 对数回报率](#12-对数回报率)
    - [21.1.3. Cumulative Returns](#13-cumulative-returns)
    - [21.1.4. Aggregate Returns](#14-aggregate-returns)
    - [21.1.5. Annual Return](#15-annual-return)
  - [21.2. 风险调整收益率](#2-风险调整收益率)
    - [21.2.1. sharpe ratio](#21-sharpe-ratio)
    - [21.2.2. sharpe 比率与资产曲线的关系](#22-sharpe-比率与资产曲线的关系)
    - [21.2.3. sortino 指标](#23-sortino-指标)
    - [21.2.4. Max DrawDown （最大回撤）](#24-max-drawdown-最大回撤)
    - [21.2.5. Sharpe 与 max drawdown 的关系](#25-sharpe-与-max-drawdown-的关系)
    - [21.2.6. 年化波动率](#26-年化波动率)
    - [21.2.7. Calmar Ratio](#27-calmar-ratio)
    - [21.2.8. Omega Ratio](#28-omega-ratio)
  - [21.3. 基准对照类指标](#3-基准对照类指标)
    - [21.3.1. information ratio](#31-information-ratio)
    - [21.3.2. alpha/beta](#32-alphabeta)
  - [21.4. 策略评估的可视化](#4-策略评估的可视化)
    - [21.4.1. Metrics](#41-metrics)
    - [21.4.2. plots](#42-plots)
    - [21.4.3. basic 和 full](#43-basic-和-full)
    - [21.4.4. html](#44-html)
  
## 第22课 回测陷阱
  - [22.1. 幸存者偏差](#1-幸存者偏差)
  - [22.2. Look-ahead bias](#2-look-ahead-bias)
    - [22.2.1. 引用错误](#21-引用错误)
    - [22.2.2. 偷价](#22-偷价)
    - [22.2.3. 复权引起的前视偏差](#23-复权引起的前视偏差)
    - [22.2.4. PIT数据](#24-pit数据)
  - [22.3. 复权引起的问题](#3-复权引起的问题)
    - [22.3.1. 使用复权数据的必要性](#31-使用复权数据的必要性)
    - [22.3.2. 后复权的问题](#32-后复权的问题)
    - [22.3.3. 复权相关的其它问题](#33-复权相关的其它问题)
  - [22.4. 交易规则](#4-交易规则)
    - [22.4.1. T+1交易](#41-t1交易)
    - [22.4.2. 涨、跌限制](#42-涨跌限制)
  - [22.5. 过度拟合](#5-过度拟合)
  - [22.6. 回测时长](#6-回测时长)
  - [22.7. 回测与实盘的差异](#7-回测与实盘的差异)
    - [22.7.1. 信号闪烁](#71-信号闪烁)
    - [22.7.2. 冲击成本](#72-冲击成本)
    - [22.7.3. 不可能成交的价格](#73-不可能成交的价格)
    - [22.7.4. 撮合问题](#74-撮合问题)
  - [22.8. 大富翁回测框架](#8-大富翁回测框架)
    - [22.8.1. 回测功能简介](#81-回测功能简介)
      - [22.8.1.1. 架构和风格](#811-架构和风格)
    - [22.8.2. 策略框架](#82-策略框架)
      - [22.8.2.1. 数据和数据格式](#821-数据和数据格式)
      - [22.8.2.2. 跨周期数据](#822-跨周期数据)
      - [22.8.2.3. 驱动模式与性能](#823-驱动模式与性能)
      - [22.8.2.4. 回测报告](#824-回测报告)
    - [22.8.3. 一个完整的策略示例](#83-一个完整的策略示例)
    - [22.8.4. 参数优化](#84-参数优化)
  - [22.9. 参考文献](#9-参考文献)
  
## 第23课 实盘交易接口 (1)
  - [23.1. easytrader](#1-easytrader)
    - [23.1.1. 安装](#11-安装)
    - [23.1.2. 生命期](#12-生命期)
      - [23.1.2.1. 连接客户端](#121-连接客户端)
      - [23.1.2.2. 获取账户信息](#122-获取账户信息)
      - [23.1.2.3. 交易](#123-交易)
    - [23.1.3. 服务器模式](#13-服务器模式)
    - [23.1.4. 自动跟单](#14-自动跟单)
  - [23.2. 东方财富 EMC 智能交易终端](#2-东方财富-emc-智能交易终端)
    - [23.2.1. 安装](#21-安装)
      - [23.2.1.1. 配置文件单目录](#211-配置文件单目录)
    - [23.2.2. 运行和维护](#22-运行和维护)
      - [23.2.2.1. 启动](#221-启动)
      - [23.2.2.2. 每日维护](#222-每日维护)
    - [23.2.3. 故障排除与帮助](#23-故障排除与帮助)
    - [23.2.4. 撮合配置规则](#24-撮合配置规则)
  - [23.3. Trader-gm-adaptor](#3-trader-gm-adaptor)
    - [23.3.1. 冒烟测试](#31-冒烟测试)
    - [23.3.2. 客户端与服务器交互](#32-客户端与服务器交互)
      - [23.3.2.1. 客户端请求](#321-客户端请求)
      - [23.3.2.2. 返回结果](#322-返回结果)
    - [23.3.3. API 示例](#33-api-示例)
      - [23.3.3.1. 资产表](#331-资产表)
      - [23.3.3.2. 持仓表](#332-持仓表)
      - [23.3.3.3. 限价买入](#333-限价买入)
      - [23.3.3.4. 市价买入](#334-市价买入)
      - [23.3.3.5. 限价卖出](#335-限价卖出)
      - [23.3.3.6. 市价卖出](#336-市价卖出)
      - [23.3.3.7. 取消委托](#337-取消委托)
      - [23.3.3.8. 查询当日委托](#338-查询当日委托)
  
## 第24课 实盘交易接口（2）
  - [24.1. ptrade](#1-ptrade)
    - [24.1.1. 申请与安装](#11-申请与安装)
    - [24.1.2. 策略框架概述](#12-策略框架概述)
      - [24.1.2.1. initialize](#121-initialize)
      - [24.1.2.2. before_trading_start](#122-before_trading_start)
      - [24.1.2.3. handle_data](#123-handle_data)
      - [24.1.2.4. after_trading_end](#124-after_trading_end)
    - [24.1.3. 一个双均线策略](#13-一个双均线策略)
    - [24.1.4. 复权机制](#14-复权机制)
  - [24.2. QMT](#2-qmt)
    - [24.2.1. 安装和申请量化权限](#21-安装和申请量化权限)
    - [24.2.2. 功能概览](#22-功能概览)
      - [24.2.2.1. 我的板块](#221-我的板块)
      - [24.2.2.2. 模型研究](#222-模型研究)
      - [24.2.2.3. 模型交易](#223-模型交易)
  - [24.3. QMT-Mini](#3-qmt-mini)
  - [24.4. XtData](#4-xtdata)
    - [24.4.1. 获取证券列表](#41-获取证券列表)
    - [24.4.2. 获取交易日历](#42-获取交易日历)
    - [24.4.3. 获取行情数据](#43-获取行情数据)
  - [24.5. XtTrader](#5-xttrader)
    - [24.5.1. 封装成web服务](#51-封装成web服务)
  
