None
# 欢迎预览QuanTide量化课程！

<img src="https://images.jieyu.ai/images/hot/quantfans.png?1" width="150px"
align="right">

Hi! 我是助教宽粉，跟您一样，也是正在学习量化。

## 1. 正在开讲的量化课程

### 1.1 量化人的Numpy和Pandas

Numpy与Pandas是量化领域不可缺少的工具。课程的特点是，紧密结合量化场景来讲解Numpy&Pandas知识，确保把量化场景中高频使用的API、以及使用它们需要理解的前置知识原理讲透，在工作中见名知义，能灵活组合使用。

本课程适合尚处在入门期的量化学习者和数据科学的学习者。如果已经是从业多年的老量化人，但对感觉自己的程序性能不高、不简洁，那很可能也需要回锅学习。

### 1.2 量化24课

量化24课是一门中级课程，涵盖了量化交易全流程，学完后将拥有完整的量化技能，可以独立从事量化交易。适合独立交易者、打算转量化的码农、正在从事主观交易的基金、私募从业者。这门课开课以来，有不少私募总、基金业者、海外名校博士及互联网大厂员工在学习。

### 1.3 因子分析与机器学习策略

因子分析与机器学习策略（即本课程）是一门2025年2月推出的高级课程。适合已经掌握了一定量化交易知识，打算在因子和策略方向上持续深耕的学习者。

该课程前1/3将为您介绍超过400+因子（含付费因子），如何进行因子检验及发现属于自己的新因子。在拥有了大量可用因子的基础上，后2/3课程将介绍如何用最先进的机器学习模型，将这些因子组装成策略。最终，您将带走350+因子（有代码）和三个可以实用的机器学习模型。

本课程大部分视频完全免费。

[前往课程预览](http://ke.quantide.cn/course/fa/preview/)

## 2. 如何使用本环境

### 2.1 文件说明

<i> 我们提供了多个环境，不同环境的课程不一样，数据和程序库也不同，请注意区分。</i>

1. 课程指南： 即本文件。
2. courseware: 此目录下存放正课的预览部分内容。
3. assignments: 练习题。
4. quantide-weekly: QuanTide发布的每周小报，精选论文、文章，系统介绍量化编程知识
5. supplements: 课程的补充阅读材料。不同的课程，内容不同。
6. videos: 本课程视频，请在本文件结束处查看。
7. workspace：你的工作区间。此目录下可创建和修改文件。其它目录下的文件均为只读。
8. noteboo入门及notebook高级技巧：关于如何使用notebook的技巧。

### 2.2 量化24课

在此环境中（正式注册后），您将能
* **亲自体验** QuanTide量化课程的部分章节、习题，在courseware和exercise目录下。
* **获取A股数据**（实时及历史数据。历史数据从2005年以来的全部A股行情数据，粒度细至分钟线，最近5秒数据）
* **编写策略**，
* **运行回测**
* **若干量化策略**

**如果策略需要实盘运行，需要开通量化权限，可联系我，微信号：quantfans_99**，以较低门槛开通权限。

## 3. 金融平民化运动

我们非常认同 Fawcett
关于金融平民化的理念。我们认为，在国内，金融平民化运动应该包含两方向的内容，一方面要致力于传播正确的量化交易知识与理念，减少普通人免于被割韭菜；另一方面，要提供低价甚至免费的工具、书籍和课程，让更多的人有机会学习到正规的量化课程。

![image.png](attachment:38cce812-8c8f-40e1-9a43-236a50bcee35.png)

### 3.1 开源量化框架

从2019年以来，我们就开始Zillionare量化框架的开发，前后投入约300万元。这个项目已经发布了2.0，采用先进的时序数据库，在运行中，已经管理了超过40亿条行情数据。

除了Zillionare之外，我们还开发了许多其它开源项目，比如[Python Project
Wizard](https://pypi.org/project/ppw/)，已经成为许多人开启新Python项目的起手式。我们的开源项目可以在[这里](https://github.com/zillionare)上找到，欢迎点赞鼓励！

### 3.2 免费书籍

我们出版了《Python高效编程实战指南》（机械工业出版社），根据我们与版社达成的协议，这本书的电子版可以在我们的[网站上](https://www.jieyu.ai/articles/python/best-
practice-python/chap01/)免费阅读。

### 3.3 免费课程

#### 3.3.1 量化交易场景下的Numpy和Pandas

这是量化人必须掌握的量化母语。这门课程我们免费提供全部PDF课件（（在有些平台，因为无法放链接，可能会以一元课的形式发布））。同时，你也可以登录我们的在线环境，通过对应的notebook文档来进行学习。使用这个环境，我们仅收取计算资源使用费，每人49元，有效期半年。

#### 3.3.2 因子分析与机器学习策略

我们免费开放了这门课程的视频（在有些平台，因为无法放链接，可能会以一元课的形式发布），共讲19课，约24个小时的1.2倍语速课程。您可以在[QuanTide@B站](https://space.bilibili.com/1229001873)找到全部视频（除第19课、第20课）外。


但是我们也必须生产付费产品。我们的付费产品主要是基于提供的计算资源、数据、实用模型和辅导来进行定价。**感谢我们的付费学员！正是你们的赞助，我们才能有时间做一些免费的项目。**

## 4. Follow us

<img src="https://images.jieyu.ai/images/hot/xhs-logo.jpg" width="120px"
align="left" style="margin: 40px 10px 0 0"/>

### Quantide@小红书 （5万粉）
我们在[小红书](https://www.xiaohongshu.com/user/profile/5ba12feef7e8b9437f3aca0c)上有接近5万人关注。如果您按量化关键词进行搜索，再按用户排序，一眼就能看到我们。

欢迎点击[Follow](https://www.xiaohongshu.com/user/profile/5ba12feef7e8b9437f3aca0c)
<br>
<br>



<img src="https://images.jieyu.ai/images/hot/logo/gzh.jpg" width="120px"
align="left" style="margin: 40px 10px 0 0"/>

### Quantide@公众号

每日更新，内容涵盖 Python 编程和量化策略研究、量化框架开发。通过公众号，可以及时获得我们的内容更新，联系到课程助理。欢迎扫码关注。

您可以通过自助消息，发送"资源" 一词，获取以下资源网盘链接：

1. 量化研报、论文等超500份
2. 151个量化策略pdf
3. 量化入门电子书
4. QuantTide Weekly

<img src="https://images.jieyu.ai/images/hot/logo/zhihu.png" width="120px"
align="left" style="margin: 40px 10px 0 0"/>

### Quantide@知乎 （万粉）
我们在[知乎](https://www.zhihu.com/people/hbaaron)上有1.2万粉丝。欢迎点击[Follow](https://www.zhihu.com/people/hbaaron)

## 5. 视频预览
### 第2课
**请选中下面的单元格，再点击菜单栏上的▶️按钮运行，查看视频预览。**

```python
from IPython.display import HTML

HTML("""
<video width="640" height="360" controls>
  <source src="video/第2课.mp4" type="video/mp4">
  Your browser does not support the video tag.
</video>
""")
```

### 第13课预览

**请选中下面的单元格，再点击菜单栏上的▶️按钮运行，查看视频预览。**

```python
HTML("""
<video width="640" height="360" controls>
  <source src="video/lesson13-with-sub.mp4" type="video/mp4">
  Your browser does not support the video tag.
</video>
""")
```
