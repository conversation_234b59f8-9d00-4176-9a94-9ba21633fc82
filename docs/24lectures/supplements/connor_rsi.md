None
# The strategy

```{.python .input  n=21}

from numpy.typing import NDArray
from coursea import *
await init()
import talib as ta
from coretypes.errors.trade import CashError

class ConnorRSIStrategy(BaseStrategy):
    def __init__(self, url: str, sec: str, prsi=6, pstreak=6, prank=20, **kwargs):
        self.sec = sec
        self.prsi = prsi
        self.pstreak = pstreak
        self.prank = prank
        
        self.low_watermark = 9
        self.high_watermark = 81
        self.indicators = []
        super().__init__(url, warmup_period=68, **kwargs)
        
    def percent_rank(self, close):
        roc = close[1:]/close[:-1] - 1
        return np.array([sum(roc[i + 1 - self.prank:i + 1] < roc[i]) / self.prank for i in range(len(roc))]) * 100
    
    def streaks(self, close):
        result = []
        conds = [close[1:]>close[:-1], close[1:]<close[:-1]]

        flags = np.select(conds, [1,-1], 0)
        v, _, l = find_runs(flags)
        for i in range(len(v)):
            if v[i] == 0:
                result.extend([0] * l[i])
            else:
                result.extend([v[i] * x for x in range(1, (l[i] + 1))])
                
        return np.insert(result, 0, 0)
    
    def calc_crsi(self, close: NDArray):
        rsi = ta.RSI(close, self.prsi)
        streaks = self.streaks(close)
        rsi_streak = ta.RSI(streaks.astype(np.float64), self.pstreak)
        
        prank = self.percent_rank(close)
        
        ml = min(len(rsi), len(rsi_streak), len(prank))
        return (rsi[-ml:] + rsi_streak[-ml:] + prank[-ml:])/3.0

    async def before_start(self):
        end = self.bs.start if self.is_backtest else datetime.datetime.now()
        if self.is_backtest:
            end = self.bs.start
        else:
            now = datetime.datetime.now()
            if self._frame_type in tf.minute_level_frames:
                end = tf.floor(now, self._frame_type)
            else:
                end = tf.day_shift(now, 0)

        bars = await Stock.get_bars(self.sec, self.warmup_period + self.prsi * 3, self._frame_type, end=end)
        self.calc_watermarks(bars)

    def calc_watermarks(self, bars):
        close = bars["close"].astype(np.float64)
        end = bars["frame"][-1]
        crsi = self.calc_crsi(close)

        # first self.prsi * 3 of rsi is not accurate
        crsi_ = crsi[self.prsi * 3:]
        # self.low_watermark = np.nanquantile(crsi_, 0.05)
        # self.high_watermark = np.nanquantile(crsi_, 0.95)
        self.high_watermark = max(crsi_) * 1.01
        self.low_watermark = min(crsi_) * 0.99

        logger.info("wartermark is: (%.2f, %.2f)", self.low_watermark, self.high_watermark, date=end)

    async def predict(self, frame: Frame, frame_type: FrameType, i: int, barss, **kwargs):
        bars = barss[self.sec]
        if bars is None or len(bars) < self.warmup_period:
            return
        
        close = bars["close"].astype(np.float64)
        
        if (i + 1) % 10 == 0:
            self.calc_watermarks(bars)

        # 性能优化，只计算必要的数据
        # n = max(3 * self.prsi, 3 * self.pstreak, self.prank)
        crsi = self.calc_crsi(close)[-1]
        
        self.indicators.append((frame, crsi))
        # logger.info("low, cris, high: %.1f, %.1f, %.1f", self.low_watermark, crsi, self.high_watermark, date=frame)
        if crsi <= self.low_watermark:
            if self.cash > 100 * close[-1]:
                next_open = tf.combine_time(tf.day_shift(frame, 1), 9, 30)
                logger.info("crsi %.2f < %.2f, buy %.1f万 on: %s", crsi, self.low_watermark, self.cash/10000, next_open, date=frame)
                try:
                    await self.buy(self.sec, money=self.cash, order_time=next_open)
                except CashError:
                    pass
        elif crsi >= self.high_watermark:
            avail = self.available_shares(self.sec, frame)
            if avail > 0:
                next_open = tf.combine_time(tf.day_shift(frame, 1), 9, 30)
                logger.info("csri %.2f > %.2f, sell %.1f万 on: %s", crsi, self.high_watermark, avail * close[-1]/10000, next_open, date=frame)
                await self.sell(self.sec, vol = avail, order_time=next_open)

def summary(backtests):
    data = []
    cols = ["total_profit_rate", "win_rate", "sharpe", "sortino", "max_drawdown", "annual_return", "window", "total_tx"]

    for bt in backtests:
        if bt.metrics is None:
            continue
        metrics = [bt.metrics[k] for k in cols]
        baseline = [bt.metrics["baseline"].get(k) for k in cols]

        values = [item for pair in zip(metrics, baseline) for item in pair]
        extra_cols = [item for pair in zip(cols, map(lambda x: "bs_" + x, cols)) for item in pair]
        data.append((bt.sec, bt.bs.start, bt.bs.end, *values))
    return pd.DataFrame(data, columns=["sec", "start", "end", *extra_cols])

```

```{.json .output n=21}
[
 {
  "name": "stderr",
  "output_type": "stream",
  "text": "2024-01-18 23:36:42,711 I 436996 cfg4py.core:update_config:280 | configuration is\nalpha: {data_home: ~/zillionare/alpha/data, tts_server: 'http://127.0.0.1:5002/api/tts?'}\nbacktest: {url: 'http://***************:7080/backtest/api/trade/v0.5/'}\ninfluxdb: {bucket_name: zillionare, enable_compress: true, max_query_size: 5000, org: zillionare,\n  token: hwxHycJfp_t6bCOYe2MhEDW4QBOO4FDtgeBWnPR6bGZJGEZ_41m_OHtTJFZKyD2HsbVqkZM8rJNkMvjyoXCG6Q==,\n  url: 'http://***************:58086'}\nnotify: {dingtalk_access_token: 58df072143b52368086736cb38236753073ccde6537650cad1d5567747803563,\n  keyword: trader}\npluto: {store: ~/zillionare/pluto/store}\nredis: {dsn: 'redis://***************:56379'}\ntasks: {pooling: false, wr: false}\n\n"
 }
]
```

## 单个测试

```{.python .input  n=22}
start = datetime.date(2019,1,1)
end = datetime.date(2023, 11,30)

code = "002344.XSHE"
cs = ConnorRSIStrategy(cfg.backtest.url, 
                       code, 
                       is_backtest = True,
                       start=start, 
                       end=end, 
                       frame_type=FrameType.DAY,
                       prsi=6,
                       pstreak=6)
await cs.backtest(prefetch_stocks = [code], baseline=code)

await cs.make_report(cs.indicators)

```

```{.json .output n=22}
[
 {
  "name": "stderr",
  "output_type": "stream",
  "text": "[\u56de\u6d4b] 2018-12-28T00:00:00 | wartermark is: (12.00, 78.25)\n[\u56de\u6d4b] 2019-01-14T00:00:00 | wartermark is: (23.73, 77.04)\n[\u56de\u6d4b] 2019-01-23 | crsi 19.22 < 23.73, buy 100.0\u4e07 on: 2019-01-24 09:30:00\n[\u56de\u6d4b] 2019-01-28T00:00:00 | wartermark is: (19.03, 77.04)\n[\u56de\u6d4b] 2019-02-18T00:00:00 | wartermark is: (19.03, 77.04)\n[\u56de\u6d4b] 2019-02-25 | csri 80.27 > 77.04, sell 101.7\u4e07 on: 2019-02-26 09:30:00\n[\u56de\u6d4b] 2019-03-04T00:00:00 | wartermark is: (19.02, 81.07)\n[\u56de\u6d4b] 2019-03-18T00:00:00 | wartermark is: (18.99, 84.91)\n[\u56de\u6d4b] 2019-04-01T00:00:00 | wartermark is: (18.97, 84.91)\n[\u56de\u6d4b] 2019-04-16T00:00:00 | wartermark is: (18.53, 84.92)\n[\u56de\u6d4b] 2019-04-30T00:00:00 | wartermark is: (20.65, 84.82)\n[\u56de\u6d4b] 2019-05-06 | crsi 19.84 < 20.65, buy 111.5\u4e07 on: 2019-05-07 09:30:00\n[\u56de\u6d4b] 2019-05-17T00:00:00 | wartermark is: (19.65, 84.24)\n[\u56de\u6d4b] 2019-05-31T00:00:00 | wartermark is: (19.65, 71.73)\n[\u56de\u6d4b] 2019-06-17T00:00:00 | wartermark is: (19.63, 72.36)\n[\u56de\u6d4b] 2019-06-20 | csri 74.51 > 72.36, sell 100.2\u4e07 on: 2019-06-21 09:30:00\n[\u56de\u6d4b] 2019-07-01T00:00:00 | wartermark is: (19.55, 75.25)\n[\u56de\u6d4b] 2019-07-15T00:00:00 | wartermark is: (19.61, 75.25)\n[\u56de\u6d4b] 2019-07-29T00:00:00 | wartermark is: (19.48, 75.22)\n[\u56de\u6d4b] 2019-08-06 | crsi 14.15 < 19.48, buy 110.1\u4e07 on: 2019-08-07 09:30:00\n[\u56de\u6d4b] 2019-08-12T00:00:00 | wartermark is: (14.01, 75.40)\n[\u56de\u6d4b] 2019-08-26T00:00:00 | wartermark is: (14.01, 75.70)\n[\u56de\u6d4b] 2019-09-09T00:00:00 | wartermark is: (14.01, 74.40)\n[\u56de\u6d4b] 2019-09-24T00:00:00 | wartermark is: (13.97, 74.40)\n[\u56de\u6d4b] 2019-10-14 | csri 78.10 > 74.40, sell 105.2\u4e07 on: 2019-10-15 09:30:00\n[\u56de\u6d4b] 2019-10-15T00:00:00 | wartermark is: (13.76, 78.88)\n[\u56de\u6d4b] 2019-10-29T00:00:00 | wartermark is: (23.80, 78.88)\n[\u56de\u6d4b] 2019-11-11 | crsi 19.66 < 23.80, buy 115.4\u4e07 on: 2019-11-12 09:30:00\n[\u56de\u6d4b] 2019-11-12T00:00:00 | wartermark is: (19.47, 78.87)\n[\u56de\u6d4b] 2019-11-26T00:00:00 | wartermark is: (19.47, 78.92)\n[\u56de\u6d4b] 2019-12-10T00:00:00 | wartermark is: (18.98, 78.92)\n[\u56de\u6d4b] 2019-12-24T00:00:00 | wartermark is: (19.45, 76.94)\n[\u56de\u6d4b] 2019-12-30 | csri 80.06 > 76.94, sell 112.8\u4e07 on: 2019-12-31 09:30:00\n[\u56de\u6d4b] 2020-01-08T00:00:00 | wartermark is: (19.61, 80.86)\n[\u56de\u6d4b] 2020-01-22T00:00:00 | wartermark is: (18.39, 80.86)\n[\u56de\u6d4b] 2020-02-03 | crsi 13.40 < 18.39, buy 122.7\u4e07 on: 2020-02-04 09:30:00\n[\u56de\u6d4b] 2020-02-13T00:00:00 | wartermark is: (13.26, 80.86)\n[\u56de\u6d4b] 2020-02-27T00:00:00 | wartermark is: (13.26, 80.89)\n[\u56de\u6d4b] 2020-03-12T00:00:00 | wartermark is: (13.26, 80.99)\n[\u56de\u6d4b] 2020-03-26T00:00:00 | wartermark is: (13.27, 74.50)\n[\u56de\u6d4b] 2020-04-10T00:00:00 | wartermark is: (13.58, 74.50)\n[\u56de\u6d4b] 2020-04-24T00:00:00 | wartermark is: (25.52, 75.09)\n[\u56de\u6d4b] 2020-05-13T00:00:00 | wartermark is: (26.07, 75.12)\n[\u56de\u6d4b] 2020-05-22 | crsi 24.80 < 26.07, buy 15.1\u4e07 on: 2020-05-25 09:30:00\n[\u56de\u6d4b] 2020-05-27T00:00:00 | wartermark is: (20.84, 75.09)\n[\u56de\u6d4b] 2020-06-01 | csri 78.64 > 75.09, sell 135.7\u4e07 on: 2020-06-02 09:30:00\n[\u56de\u6d4b] 2020-06-10T00:00:00 | wartermark is: (24.55, 85.58)\n[\u56de\u6d4b] 2020-06-24T00:00:00 | wartermark is: (24.55, 85.58)\n[\u56de\u6d4b] 2020-07-10T00:00:00 | wartermark is: (24.57, 85.58)\n[\u56de\u6d4b] 2020-07-24T00:00:00 | wartermark is: (23.38, 85.60)\n[\u56de\u6d4b] 2020-08-07T00:00:00 | wartermark is: (23.38, 85.71)\n[\u56de\u6d4b] 2020-08-21T00:00:00 | wartermark is: (23.38, 79.73)\n[\u56de\u6d4b] 2020-09-04T00:00:00 | wartermark is: (23.45, 79.90)\n[\u56de\u6d4b] 2020-09-18T00:00:00 | wartermark is: (23.38, 79.33)\n[\u56de\u6d4b] 2020-09-24 | crsi 19.73 < 23.38, buy 139.0\u4e07 on: 2020-09-25 09:30:00\n[\u56de\u6d4b] 2020-10-12T00:00:00 | wartermark is: (19.54, 79.34)\n[\u56de\u6d4b] 2020-10-26T00:00:00 | wartermark is: (19.54, 79.33)\n[\u56de\u6d4b] 2020-10-28 | crsi 18.80 < 19.54, buy 12.6\u4e07 on: 2020-10-29 09:30:00\n[\u56de\u6d4b] 2020-10-30 | crsi 10.08 < 19.54, buy 1.3\u4e07 on: 2020-11-02 09:30:00\n[\u56de\u6d4b] 2020-11-09T00:00:00 | wartermark is: (9.98, 79.57)\n[\u56de\u6d4b] 2020-11-23T00:00:00 | wartermark is: (9.98, 70.54)\n[\u56de\u6d4b] 2020-12-07T00:00:00 | wartermark is: (9.99, 70.64)\n[\u56de\u6d4b] 2020-12-21T00:00:00 | wartermark is: (9.91, 70.02)\n[\u56de\u6d4b] 2020-12-28 | csri 71.46 > 70.02, sell 132.4\u4e07 on: 2020-12-29 09:30:00\n[\u56de\u6d4b] 2021-01-05T00:00:00 | wartermark is: (9.57, 72.17)\n[\u56de\u6d4b] 2021-01-19T00:00:00 | wartermark is: (19.86, 72.17)\n[\u56de\u6d4b] 2021-02-02T00:00:00 | wartermark is: (19.86, 72.17)\n[\u56de\u6d4b] 2021-02-23T00:00:00 | wartermark is: (19.86, 80.22)\n[\u56de\u6d4b] 2021-03-09T00:00:00 | wartermark is: (19.87, 80.22)\n[\u56de\u6d4b] 2021-03-23T00:00:00 | wartermark is: (19.94, 80.22)\n[\u56de\u6d4b] 2021-04-07T00:00:00 | wartermark is: (21.19, 80.26)\n[\u56de\u6d4b] 2021-04-21T00:00:00 | wartermark is: (19.24, 80.18)\n[\u56de\u6d4b] 2021-05-10T00:00:00 | wartermark is: (23.76, 78.65)\n[\u56de\u6d4b] 2021-05-24T00:00:00 | wartermark is: (23.89, 84.71)\n[\u56de\u6d4b] 2021-06-07T00:00:00 | wartermark is: (23.83, 84.70)\n[\u56de\u6d4b] 2021-06-22T00:00:00 | wartermark is: (25.33, 84.71)\n[\u56de\u6d4b] 2021-07-01 | crsi 23.70 < 25.33, buy 131.8\u4e07 on: 2021-07-02 09:30:00\n[\u56de\u6d4b] 2021-07-06T00:00:00 | wartermark is: (23.40, 84.70)\n[\u56de\u6d4b] 2021-07-08 | crsi 22.10 < 23.40, buy 10.5\u4e07 on: 2021-07-09 09:30:00\n[\u56de\u6d4b] 2021-07-20T00:00:00 | wartermark is: (21.88, 69.18)\n[\u56de\u6d4b] 2021-07-26 | crsi 19.55 < 21.88, buy 1.0\u4e07 on: 2021-07-27 09:30:00\n[\u56de\u6d4b] 2021-07-27 | crsi 19.66 < 21.88, buy 0.1\u4e07 on: 2021-07-28 09:30:00\n[\u56de\u6d4b] 2021-08-03T00:00:00 | wartermark is: (16.69, 67.35)\n[\u56de\u6d4b] 2021-08-09 | csri 67.70 > 67.35, sell 124.4\u4e07 on: 2021-08-10 09:30:00\n[\u56de\u6d4b] 2021-08-17T00:00:00 | wartermark is: (16.69, 77.39)\n[\u56de\u6d4b] 2021-08-31T00:00:00 | wartermark is: (16.69, 77.39)\n[\u56de\u6d4b] 2021-09-14T00:00:00 | wartermark is: (16.65, 77.44)\n[\u56de\u6d4b] 2021-09-30T00:00:00 | wartermark is: (16.20, 77.44)\n[\u56de\u6d4b] 2021-10-21T00:00:00 | wartermark is: (25.40, 77.47)\n[\u56de\u6d4b] 2021-11-04T00:00:00 | wartermark is: (25.40, 77.33)\n[\u56de\u6d4b] 2021-11-18T00:00:00 | wartermark is: (25.43, 78.76)\n[\u56de\u6d4b] 2021-11-29 | crsi 20.32 < 25.43, buy 125.1\u4e07 on: 2021-11-30 09:30:00\n[\u56de\u6d4b] 2021-12-02T00:00:00 | wartermark is: (20.12, 78.76)\n[\u56de\u6d4b] 2021-12-16T00:00:00 | wartermark is: (20.12, 78.77)\n[\u56de\u6d4b] 2021-12-21 | csri 81.00 > 78.77, sell 122.4\u4e07 on: 2021-12-22 09:30:00\n[\u56de\u6d4b] 2021-12-30T00:00:00 | wartermark is: (20.12, 81.81)\n[\u56de\u6d4b] 2022-01-14T00:00:00 | wartermark is: (20.15, 81.81)\n[\u56de\u6d4b] 2022-01-28T00:00:00 | wartermark is: (20.10, 81.82)\n[\u56de\u6d4b] 2022-02-18T00:00:00 | wartermark is: (21.68, 81.96)\n[\u56de\u6d4b] 2022-03-04T00:00:00 | wartermark is: (21.63, 81.16)\n[\u56de\u6d4b] 2022-03-18T00:00:00 | wartermark is: (21.67, 74.03)\n[\u56de\u6d4b] 2022-04-01T00:00:00 | wartermark is: (22.59, 74.03)\n[\u56de\u6d4b] 2022-04-19T00:00:00 | wartermark is: (22.59, 74.42)\n[\u56de\u6d4b] 2022-04-21 | crsi 21.06 < 22.59, buy 136.5\u4e07 on: 2022-04-22 09:30:00\n[\u56de\u6d4b] 2022-04-25 | crsi 13.83 < 22.59, buy 12.7\u4e07 on: 2022-04-26 09:30:00\n[\u56de\u6d4b] 2022-04-26 | crsi 14.99 < 22.59, buy 1.1\u4e07 on: 2022-04-27 09:30:00\n[\u56de\u6d4b] 2022-05-06T00:00:00 | wartermark is: (13.69, 74.41)\n[\u56de\u6d4b] 2022-05-20T00:00:00 | wartermark is: (13.69, 74.43)\n[\u56de\u6d4b] 2022-05-30 | csri 79.68 > 74.43, sell 142.6\u4e07 on: 2022-05-31 09:30:00\n[\u56de\u6d4b] 2022-06-06T00:00:00 | wartermark is: (13.68, 80.48)\n[\u56de\u6d4b] 2022-06-20T00:00:00 | wartermark is: (13.70, 80.48)\n[\u56de\u6d4b] 2022-07-04T00:00:00 | wartermark is: (13.62, 80.49)\n[\u56de\u6d4b] 2022-07-18T00:00:00 | wartermark is: (24.10, 80.49)\n[\u56de\u6d4b] 2022-08-01T00:00:00 | wartermark is: (24.10, 80.08)\n[\u56de\u6d4b] 2022-08-02 | crsi 17.07 < 24.10, buy 143.0\u4e07 on: 2022-08-03 09:30:00\n[\u56de\u6d4b] 2022-08-03 | crsi 20.00 < 24.10, buy 13.4\u4e07 on: 2022-08-04 09:30:00\n[\u56de\u6d4b] 2022-08-15T00:00:00 | wartermark is: (16.89, 76.70)\n[\u56de\u6d4b] 2022-08-29T00:00:00 | wartermark is: (16.90, 76.71)\n[\u56de\u6d4b] 2022-09-13T00:00:00 | wartermark is: (16.90, 76.71)\n[\u56de\u6d4b] 2022-09-27T00:00:00 | wartermark is: (16.89, 76.71)\n[\u56de\u6d4b] 2022-10-18T00:00:00 | wartermark is: (14.92, 76.79)\n[\u56de\u6d4b] 2022-11-01T00:00:00 | wartermark is: (19.95, 71.87)\n[\u56de\u6d4b] 2022-11-15T00:00:00 | wartermark is: (20.04, 72.25)\n[\u56de\u6d4b] 2022-11-29T00:00:00 | wartermark is: (19.93, 80.72)\n[\u56de\u6d4b] 2022-12-06 | csri 81.17 > 80.72, sell 148.6\u4e07 on: 2022-12-07 09:30:00\n[\u56de\u6d4b] 2022-12-13T00:00:00 | wartermark is: (22.49, 86.34)\n[\u56de\u6d4b] 2022-12-27T00:00:00 | wartermark is: (22.48, 86.34)\n[\u56de\u6d4b] 2023-01-11T00:00:00 | wartermark is: (20.87, 86.35)\n[\u56de\u6d4b] 2023-02-01T00:00:00 | wartermark is: (20.87, 86.32)\n[\u56de\u6d4b] 2023-02-15T00:00:00 | wartermark is: (20.87, 86.72)\n[\u56de\u6d4b] 2023-03-01T00:00:00 | wartermark is: (20.86, 75.07)\n[\u56de\u6d4b] 2023-03-14 | crsi 18.49 < 20.86, buy 151.4\u4e07 on: 2023-03-15 09:30:00\n[\u56de\u6d4b] 2023-03-15T00:00:00 | wartermark is: (18.31, 75.42)\n[\u56de\u6d4b] 2023-03-29T00:00:00 | wartermark is: (18.31, 74.17)\n[\u56de\u6d4b] 2023-03-31 | csri 75.29 > 74.17, sell 147.9\u4e07 on: 2023-04-03 09:30:00\n[\u56de\u6d4b] 2023-04-13T00:00:00 | wartermark is: (18.30, 76.58)\n[\u56de\u6d4b] 2023-04-27T00:00:00 | wartermark is: (18.30, 76.58)\n[\u56de\u6d4b] 2023-05-16T00:00:00 | wartermark is: (18.30, 77.27)\n[\u56de\u6d4b] 2023-05-30T00:00:00 | wartermark is: (26.37, 77.27)\n[\u56de\u6d4b] 2023-06-13T00:00:00 | wartermark is: (26.14, 77.26)\n[\u56de\u6d4b] 2023-06-19 | crsi 23.69 < 26.14, buy 161.2\u4e07 on: 2023-06-20 09:30:00\n[\u56de\u6d4b] 2023-06-20 | crsi 16.66 < 26.14, buy 14.3\u4e07 on: 2023-06-21 09:30:00\n[\u56de\u6d4b] 2023-06-21 | crsi 22.85 < 26.14, buy 1.3\u4e07 on: 2023-06-26 09:30:00\n[\u56de\u6d4b] 2023-06-26 | crsi 14.03 < 26.14, buy 0.1\u4e07 on: 2023-06-27 09:30:00\n[\u56de\u6d4b] 2023-06-29T00:00:00 | wartermark is: (13.89, 77.36)\n[\u56de\u6d4b] 2023-07-13T00:00:00 | wartermark is: (13.89, 76.52)\n[\u56de\u6d4b] 2023-07-27T00:00:00 | wartermark is: (13.89, 74.21)\n[\u56de\u6d4b] 2023-07-28 | csri 76.79 > 74.21, sell 167.5\u4e07 on: 2023-07-31 09:30:00\n[\u56de\u6d4b] 2023-08-10T00:00:00 | wartermark is: (13.87, 77.56)\n[\u56de\u6d4b] 2023-08-24T00:00:00 | wartermark is: (13.67, 77.56)\n[\u56de\u6d4b] 2023-09-07T00:00:00 | wartermark is: (18.91, 77.56)\n[\u56de\u6d4b] 2023-09-21T00:00:00 | wartermark is: (18.91, 77.29)\n[\u56de\u6d4b] 2023-10-13T00:00:00 | wartermark is: (18.92, 65.53)\n[\u56de\u6d4b] 2023-11-10T00:00:00 | wartermark is: (18.55, 72.27)\n[\u56de\u6d4b] 2023-11-24T00:00:00 | wartermark is: (21.26, 83.15)\n[\u56de\u6d4b] 2023-11-30 | STOP connorrsistrategy_v0.1<2018-12-28 - 2023-11-30>\n/home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/plotting/metrics.py:99: FutureWarning:\n\nComparison of Timestamp with datetime.date is deprecated in order to match the standard library behavior. In a future version these will be considered non-comparable. Use 'ts == pd.Timestamp(date)' or 'ts.date() == date' instead.\n\n"
 },
 {
  "data": {
   "application/vnd.plotly.v1+json": {
    "config": {
     "plotlyServerURL": "https://plot.ly"
    },
    "data": [
     {
      "cells": {
       "font": {
        "size": 10
       },
       "values": [
        [
         "\u8d77\u59cb\u65e5",
         "\u7ed3\u675f\u65e5",
         "\u8d44\u4ea7\u66b4\u9732\u7a97\u53e3",
         "\u4ea4\u6613\u6b21\u6570",
         "\u603b\u5229\u6da6",
         "\u5229\u6da6\u7387",
         "\u80dc\u7387",
         "\u65e5\u5747\u56de\u62a5",
         "\u590f\u666e\u7387",
         "\u6700\u5927\u56de\u64a4",
         "\u5e74\u5316\u56de\u62a5",
         "\u6ce2\u52a8\u7387",
         "sortino",
         "calmar"
        ],
        [
         "2019-01-24",
         "2023-07-31",
         "1095",
         "25",
         "695201.03",
         "69.52%",
         "76.00%",
         "0.05%",
         "0.63",
         "-24.93%",
         "12.92%",
         "16.62%",
         "0.96",
         "0.52"
        ],
        [
         "2018-12-28",
         "2023-11-30",
         "1194",
         "-",
         "-",
         "-0.98%",
         "45.55%",
         "0.02%",
         "0.04",
         "-39.82%",
         "-0.21%",
         "29.76%",
         "0.06",
         "-0.01"
        ]
       ]
      },
      "domain": {
       "x": [
        0.7075,
        0.94
       ],
       "y": [
        0,
        1
       ]
      },
      "header": {
       "values": [
        "\u6307\u6807\u540d",
        "\u7b56\u7565",
        "\u6d77\u5b81\u76ae\u57ce"
       ]
      },
      "type": "table"
     },
     {
      "hovertemplate": "<br>\u51c0\u503c:%{y:.2f}<br>\u6307\u6807:%{text:.1f}",
      "mode": "lines",
      "name": "\u6d77\u5b81\u76ae\u57ce",
      "showlegend": true,
      "text": [
       null,
       42.361421601213614,
       30.862929234020854,
       61.310995239994334,
       74.54242030517885,
       76.27335418265879,
       47.180969771689796,
       44.09527973483293,
       69.98552827363741,
       63.52155581140687,
       56.86748627309229,
       64.77238026215086,
       35.116576335212805,
       62.46357807613375,
       58.16168983045312,
       39.91198504235263,
       40.37688951102862,
       19.22038916749454,
       38.35068263701458,
       27.33903705199199,
       39.16433143620835,
       21.71793152578878,
       24.40306946833539,
       20.481540570243414,
       61.999002909024476,
       64.56777209474049,
       67.7366432174915,
       69.49069593916447,
       68.00814339127139,
       49.177033669224556,
       73.54720648941999,
       51.52365313281973,
       53.47747426342885,
       43.22189924406246,
       73.2513725672374,
       80.27099737108132,
       50.929248335254414,
       64.88922552461095,
       67.14108143752037,
       71.17422219290093,
       68.15245934775056,
       84.06544264723071,
       75.73614118194939,
       82.45962519779772,
       32.12712089742421,
       68.41798542180298,
       53.771423729871124,
       33.86536688077927,
       31.49279711778206,
       57.36697139286289,
       62.81528257680859,
       42.66459603020586,
       42.40767613639125,
       59.70548717168316,
       59.01028455760822,
       36.1338129444791,
       27.63642526425474,
       34.292564288916736,
       30.462799296134364,
       69.52851581816684,
       70.92409286875143,
       52.52787304245488,
       64.02303810383025,
       57.45011673806888,
       59.40555622548095,
       45.329699408521954,
       60.91028567933167,
       31.79497714531568,
       54.25485113795819,
       47.51494120309027,
       57.08365347699819,
       62.67149775561905,
       34.78920253313027,
       61.29584985875075,
       34.46944882722216,
       23.568687398942497,
       44.495051580858075,
       20.857980132308064,
       23.291600077377883,
       20.89433065470296,
       55.52077469558183,
       19.844813897479153,
       58.083049825672646,
       36.04803112619495,
       30.597265011033183,
       61.81472349909987,
       42.885198007213944,
       44.443935014762,
       60.7943028137632,
       56.65081125177011,
       25.33686895422538,
       38.14458629784,
       58.29573606914058,
       37.10347109833906,
       32.36638018344231,
       44.2662339875721,
       63.72920276808971,
       55.790954619502294,
       36.148778846694995,
       36.381560624474965,
       39.15843660392145,
       29.62194849621915,
       19.83265570676217,
       44.88606015730577,
       24.425051808720003,
       57.1797315468057,
       71.6458251440036,
       47.71828184410168,
       62.297629072916585,
       49.398820051478374,
       48.89742946462173,
       63.72651604199056,
       64.54989715620907,
       74.50841016330911,
       68.1267571369156,
       43.45172363372408,
       47.042764319242906,
       48.21077525281584,
       59.87426891313975,
       30.785233488197235,
       68.34677998820503,
       45.50010378586484,
       32.85488637869617,
       58.0574192558777,
       53.423109227949446,
       23.5175535283407,
       35.274239539395616,
       26.49712667438473,
       47.3236722695954,
       55.63987264059778,
       71.18456834459255,
       49.117384222465354,
       38.70879682836908,
       37.48817591640925,
       55.43737893657337,
       27.131039578736175,
       51.347488407803894,
       65.03857596645138,
       40.14864467006982,
       35.58956603863697,
       40.142296256104174,
       65.46581526913438,
       40.42211619188206,
       30.032571271057037,
       22.43891723997223,
       21.131463846331798,
       14.151099993486616,
       22.713861759606758,
       53.288875431127565,
       31.140528610219434,
       59.9109893101753,
       39.44946094241589,
       55.60671130256157,
       45.354681666931334,
       54.209925409908,
       73.66550101533238,
       58.4820486817956,
       62.243063004853816,
       52.653130309017435,
       48.20917438668848,
       29.093476178758255,
       70.40667921894884,
       62.22912217194979,
       55.17072207712883,
       40.090434614397765,
       69.85485132542665,
       50.24131239393599,
       66.98920233660071,
       60.68485135589858,
       51.863357017019574,
       72.04312262491815,
       53.40562645007447,
       49.61743474552537,
       68.28447780227025,
       49.02503504793739,
       24.844508176997355,
       31.031418017916064,
       53.59593851184064,
       60.89494204641298,
       26.46546232039442,
       50.84342600130724,
       24.041554960286806,
       24.084118060669272,
       56.59773236083773,
       29.743872855510528,
       60.623610115894905,
       61.04379182421397,
       62.26680329788615,
       65.79581444034969,
       78.10242405938256,
       70.10691210614729,
       41.33496529267509,
       36.874496721559815,
       24.282419768975576,
       33.99588520034203,
       61.8788080431828,
       62.554511392199935,
       45.76697309514572,
       48.93495631586882,
       69.8767593269397,
       39.36956539675046,
       32.43901441842147,
       26.07193451718489,
       60.78838119968942,
       41.36090198021818,
       46.27015825083301,
       27.12396380490488,
       43.97015322366895,
       39.77009621958158,
       19.664700591125428,
       43.992026846749134,
       32.58570500455399,
       30.0490900817222,
       22.3018067276484,
       50.52545815441564,
       64.3670134629379,
       30.430267418040945,
       57.68773036011188,
       43.649968392543265,
       69.66406102116984,
       46.21235718505584,
       62.6226649178954,
       39.75311898571732,
       65.57992340824633,
       47.98903968112858,
       52.98918386846966,
       40.34795425577597,
       66.95107722046171,
       67.70118747138432,
       53.65577475752668,
       53.6557924537879,
       42.57707996419281,
       31.215628370709155,
       70.1450489061008,
       61.67975577548524,
       76.17394905233554,
       49.78499310480461,
       61.13043617921485,
       44.588134716375514,
       26.25664034504869,
       57.84465694112263,
       37.97775365818944,
       67.33429848000411,
       74.53888570950028,
       80.05892218938449,
       39.8640458303513,
       76.5874414124015,
       42.70715812197221,
       36.545555360682734,
       62.727444787316124,
       32.417569695133956,
       64.82757247628854,
       59.25883055445542,
       59.966616066529845,
       40.61613895097681,
       32.02112547207192,
       41.931051026216835,
       49.07467815716933,
       51.40111466626925,
       25.615214976231083,
       37.023181667166114,
       18.56330568773036,
       13.397878242513448,
       21.93812924711034,
       53.42030043178727,
       59.971818219265685,
       44.66025661747293,
       61.73191589748842,
       37.01181189090469,
       59.64935623699808,
       34.113496913524536,
       38.925846047147466,
       67.11167145906778,
       62.12666830554969,
       43.566018622137086,
       68.87278325499162,
       52.206660045753615,
       52.2066576818479,
       41.78352024457498,
       62.472250697220964,
       50.3054644193086,
       25.892375492841552,
       67.27975690400767,
       43.36561833933704,
       73.75914699076293,
       47.2244363335977,
       72.64026265100705,
       31.501181733734217,
       60.720803644020286,
       37.138615203199485,
       31.11983787566813,
       29.647634577329274,
       26.267372591855565,
       44.5076170642761,
       30.705260816298217,
       60.76939816307845,
       58.0772761698584,
       30.31584707445215,
       56.48730470238022,
       60.95530865362945,
       44.03316245571191,
       47.4104563517329,
       36.27168169569473,
       40.464560256758865,
       38.04194091179348,
       58.001759156606006,
       48.37080895255149,
       60.12077007869045,
       63.37815903749643,
       49.78188993879342,
       32.366658044855576,
       42.02905954345653,
       58.368999908793604,
       35.63422604342416,
       47.166911318217025,
       42.99405746835168,
       70.926963902168,
       45.36284899426172,
       74.34428495399779,
       66.90098795167344,
       40.936434864515256,
       66.10480163914674,
       29.54275883240699,
       51.66634907067719,
       62.9318825085924,
       66.47202077075681,
       43.029134796741126,
       55.75396627024785,
       48.59004099608857,
       36.057054633340975,
       58.60842026974825,
       31.213681078513854,
       46.17006784901861,
       32.11175347835263,
       57.47486849297019,
       30.7421299918315,
       44.2203564886679,
       24.797062362541535,
       48.27025773556861,
       67.56386225726975,
       56.74568899898595,
       45.30129052783897,
       71.67479062665892,
       78.64308562458287,
       83.70370983524693,
       84.54474563628624,
       84.73495115119937,
       34.890248091799506,
       29.956021235531352,
       33.320425900561666,
       44.26343758214761,
       34.936511501980874,
       39.97845775248731,
       28.538863812063255,
       59.484549005573115,
       32.461067961486854,
       39.13512364834782,
       64.55222396300744,
       33.80456557932447,
       32.05535717531953,
       38.78321027595086,
       37.19354998934153,
       56.24515086592351,
       58.89685902917281,
       61.55238309202527,
       64.16803269235,
       78.93681356567767,
       57.685357018281366,
       71.62837127411844,
       72.93582697126644,
       46.37287174000392,
       67.97251994972997,
       69.10870244368247,
       30.669246164911453,
       29.68226897617143,
       32.6452672205675,
       64.77540840718177,
       42.93814410270846,
       38.997628621127525,
       30.52907248220232,
       23.619133584614456,
       30.126833961609808,
       48.89738644252271,
       62.44426286301063,
       55.93623558216978,
       59.87203948644543,
       75.148038877633,
       39.608304529550864,
       51.12428264842496,
       44.82512133058344,
       31.43543917989507,
       63.099426350550345,
       34.23158262354127,
       43.788855945056184,
       68.30261020614944,
       64.49853401416676,
       61.431368603990514,
       47.817315473017665,
       33.953269874593076,
       34.248903054581625,
       56.6004767371171,
       64.99283268674272,
       38.60345516181074,
       38.65027722073659,
       43.03839193634381,
       56.56206256151976,
       59.150602193444946,
       78.54797352357025,
       56.25138988234297,
       52.55250205801634,
       29.282094946688517,
       29.60384851618237,
       63.478036845627734,
       51.87779767126924,
       27.081893517648194,
       54.5850369013489,
       34.9101467726047,
       37.746751480292055,
       50.07981663326155,
       50.63216210872208,
       64.46030544845257,
       48.683315059874,
       31.44439601193216,
       36.042125696593416,
       19.734446408082643,
       23.977437266182218,
       23.64625204801037,
       59.965512241911654,
       33.45118272342691,
       66.17053909501624,
       69.82296491565033,
       45.30082197696877,
       42.11819650892578,
       62.373892285579245,
       43.939919224919244,
       59.137874281764816,
       63.19684437920045,
       30.9497376924103,
       36.32066184008838,
       38.94297909581256,
       31.148274242953963,
       39.41481038413094,
       18.797276465525727,
       21.59461557784738,
       10.084108516749565,
       46.40845435173953,
       63.4677764993964,
       44.19787098610906,
       61.540677721430576,
       49.2733661920721,
       68.73714141716619,
       48.947100257277924,
       44.25260780431723,
       61.12806721654246,
       34.699177856355305,
       68.93375451228827,
       58.88609338429223,
       65.1586305920518,
       56.512536477595944,
       49.82087445099549,
       69.3323061735062,
       47.57861985934145,
       47.84258460299872,
       36.87944997066385,
       62.86181508677751,
       52.647401825633104,
       68.03073488966241,
       65.96752957011027,
       51.36594212579553,
       28.07856147777177,
       47.00229424441746,
       32.985002405984986,
       20.346090172681546,
       39.50911465662585,
       24.142703923879775,
       60.09854174890162,
       42.87094159385086,
       57.60721954681927,
       37.375026699714,
       28.841971157058058,
       62.808021036776836,
       26.415957545067414,
       51.11084638878061,
       21.481577469357546,
       62.31723167812134,
       71.4559966580645,
       40.57163953995866,
       43.57728236640043,
       66.88058525311261,
       49.66798308441667,
       39.27042944795525,
       30.750807110060375,
       20.06373141767945,
       53.65928984232081,
       27.52140437376734,
       49.359302032271046,
       27.570354890999763,
       54.64825705804941,
       63.8971530273845,
       47.019748963310214,
       57.64434339769536,
       36.65460549290647,
       55.10776768417568,
       31.495503090724895,
       27.12921871294671,
       33.06013235968388,
       58.2640150521515,
       40.77674905523208,
       32.26918288578697,
       54.249396610586565,
       51.73167406802977,
       30.32568857243359,
       21.24878299236861,
       32.77876110368337,
       47.27930707648138,
       60.678605624895226,
       57.59189080610913,
       73.35001704020952,
       79.4250550433208,
       78.51274658730455,
       51.819827073133105,
       53.42278452381478,
       53.422738459918,
       51.75607039908679,
       62.001453641360676,
       42.92742694529331,
       68.58584135289685,
       46.66443232293005,
       68.16384116098857,
       38.34544124627826,
       31.20273595871764,
       27.323905190808414,
       56.617993483509274,
       68.92576585869216,
       62.215076798876005,
       61.34327965502107,
       48.54367126524983,
       40.64699685489418,
       42.75731848485927,
       67.1782648624562,
       50.46422048465317,
       59.83259879780485,
       37.325360881706025,
       55.645098256737505,
       48.7801999216772,
       24.005461739996765,
       57.33611418698089,
       72.84046044404386,
       40.743937023504806,
       59.85788045136665,
       61.69043225872727,
       32.31311226975054,
       68.56315934206714,
       68.65898373824773,
       36.8374123186302,
       74.46565074782592,
       39.65394340358542,
       54.86972495903402,
       44.58065908846266,
       47.956813803570604,
       34.019063604924334,
       41.05348547653453,
       30.370791941318004,
       26.90168191355642,
       35.11900834645493,
       52.517092366666624,
       72.40567580376829,
       44.09908305126725,
       60.349907162703026,
       67.15666442569533,
       77.87064743336283,
       83.86597280202058,
       53.197688677796954,
       35.019680881523975,
       43.91017706731325,
       33.49847150430326,
       57.398198341415316,
       49.967748966193106,
       45.6981287917292,
       60.33602491469342,
       59.52150604012553,
       48.00134417059005,
       68.50320714390719,
       38.225185760117846,
       30.862668849987642,
       62.194578812002,
       52.23400184293856,
       48.15787518427799,
       44.92686859041839,
       28.23088389543388,
       66.5923921751929,
       52.45429203578806,
       55.712209320729876,
       59.82742769772051,
       45.44756506362626,
       27.125849557452113,
       47.66805429304588,
       25.586908886672898,
       52.27148300632806,
       37.81635745580541,
       60.3269612891344,
       42.46941118243493,
       33.280511113869444,
       64.64973829354507,
       45.22065943706664,
       40.92665066741565,
       32.30949650677031,
       23.702994813674135,
       30.10766897716748,
       48.00809654558759,
       59.77838697321133,
       47.75056808098609,
       22.104289988590384,
       31.523280502872556,
       43.72957063513951,
       27.45584607797375,
       24.785202257263432,
       58.98389197055689,
       35.843928206481344,
       65.95694463131598,
       53.96930408775638,
       65.27345001190457,
       53.04798148233065,
       31.226847653514014,
       19.548797480511652,
       19.65927413100528,
       16.863006613819554,
       58.005523303520384,
       27.802772325210146,
       65.80715113244419,
       43.466835940298175,
       62.17782568519914,
       44.76628085530613,
       42.44193522442571,
       67.70268569742531,
       72.48773883116405,
       76.62866843492024,
       66.59897196437517,
       66.54196993673938,
       53.449159499151705,
       29.841154061119823,
       66.2667574597912,
       59.09542602970825,
       39.05292188692632,
       53.221171334166776,
       53.952189989752306,
       74.32475054304696,
       49.06094490743021,
       37.20319035749399,
       45.933500951531066,
       66.36922781452027,
       44.6185883444147,
       70.17049580571513,
       45.26332925748944,
       66.17007830280842,
       66.81094312179793,
       76.67031158856737,
       64.99805241954375,
       49.081686130752864,
       66.75420163096761,
       31.971590737232493,
       65.59920974711058,
       35.84235952544704,
       29.871936301827784,
       50.72665116382604,
       74.39561864755052,
       31.930663884538404,
       25.84587649383379,
       54.08307159150801,
       25.657997235515456,
       56.52384799804645,
       70.52507094731887,
       63.45091937401903,
       43.597321907846094,
       36.05215661372571,
       75.44761564216145,
       38.39297405514003,
       58.55036061732994,
       51.51485032165592,
       67.89460009093841,
       54.625541543448946,
       74.63487406812025,
       46.5916621802869,
       29.97949033858352,
       34.64790751314621,
       61.00538893150907,
       66.6127383936742,
       70.32923179724446,
       29.660007702704746,
       60.17228736739688,
       38.908135300699115,
       35.93834087135098,
       51.545302479759215,
       29.80825273472169,
       39.872022568990985,
       58.358917050129456,
       44.95704929523922,
       57.778089626801396,
       77.98465726248544,
       51.98848508802515,
       62.2896917514632,
       40.772372486833824,
       54.0716817113612,
       50.335496833838526,
       42.755426584955934,
       31.48785426725568,
       41.36168746901496,
       20.31942665339358,
       58.124042966893114,
       59.53047133916257,
       34.77015194346529,
       58.38270770069861,
       55.14755780658746,
       58.07089983035734,
       47.557606612995386,
       69.31011171147317,
       60.000165228872824,
       38.82556287124782,
       34.03400103824236,
       46.188725806628476,
       58.47730358417619,
       60.523514342481896,
       74.1726556273005,
       81.00243343886522,
       44.097549897203216,
       52.837129862820895,
       72.21142191512486,
       55.64428424965632,
       35.080755869307005,
       50.4808494433588,
       39.42056286620014,
       71.31902230495162,
       73.9082644090047,
       37.927702445230416,
       41.48819917129979,
       27.616942927931788,
       55.99798752050905,
       46.070467578327225,
       37.53304612773332,
       34.14763701588362,
       21.89216574901541,
       62.51288229469068,
       38.89627915071315,
       71.485300962289,
       30.637394385186024,
       28.161553043508434,
       58.90580689368128,
       24.461025859639307,
       52.68690328167819,
       27.70897233879349,
       50.73067669221908,
       60.751911467521474,
       68.79352669369113,
       59.04210424753794,
       58.94884045311421,
       70.88973526435431,
       31.42607036434437,
       41.3501464858005,
       68.2024115892823,
       44.3473446086392,
       51.419916292696094,
       51.55950770764534,
       44.29456068230051,
       41.6923011594437,
       23.250902100084232,
       60.10335963140738,
       34.36032050620874,
       52.18022380944536,
       43.93675278812086,
       73.29226719076031,
       37.17263953482941,
       34.14025627703692,
       23.155707358498393,
       23.028860623413525,
       61.689889518803476,
       62.0954500873464,
       31.933378695956502,
       22.818886008492047,
       58.8153656467463,
       52.06069678489894,
       60.986752801971456,
       61.046045761600716,
       62.57395741994847,
       43.90963700564881,
       39.063170062607355,
       62.09662902636061,
       54.13120201147844,
       44.68341254391191,
       53.981284830949726,
       65.10292139388116,
       61.91216261217952,
       73.67875737726277,
       35.3438064065838,
       51.98874963616129,
       35.51348352244039,
       69.6101078870358,
       44.80966261312031,
       54.34992116208631,
       38.86074353823187,
       49.35394222599379,
       43.57576392064863,
       40.35630632729746,
       21.061400999186002,
       23.609127943917752,
       13.82548677572562,
       14.99355947427609,
       49.49504870177723,
       30.964330305789755,
       64.13171473817975,
       68.18048347507239,
       37.53836316514806,
       62.96499981765891,
       63.93936447081998,
       42.415238712085944,
       55.83479946516368,
       66.19123957798871,
       67.87498840336896,
       39.27694754815484,
       56.85437496236267,
       48.16525339593488,
       48.04312991124535,
       51.49160611851048,
       29.001840590738592,
       60.6737802207659,
       63.89335834449725,
       54.34608224177836,
       79.6847882387749,
       48.29012315723401,
       68.18164286620737,
       41.28318771738989,
       46.183661816299455,
       38.28001088452152,
       34.885357618692716,
       29.13218811959216,
       56.502427607118456,
       66.68819259424446,
       54.914511326316834,
       58.90879211303156,
       60.78938826227318,
       31.99657808419181,
       35.565023775963915,
       59.67022696986283,
       26.3754868736989,
       66.79617006294554,
       38.711234076161,
       66.93643991192717,
       58.62673012600887,
       47.046874433465575,
       70.86968064206688,
       43.901863795093256,
       63.3303461503265,
       42.80424123843064,
       31.820077440206926,
       40.02659364497533,
       59.18327828940256,
       54.38128229241943,
       66.85308944368258,
       64.10784266267035,
       33.98414739727253,
       24.34444109129744,
       61.842436369382504,
       56.20392014392849,
       60.98550477934706,
       54.41431894745775,
       62.10985688355415,
       73.77211182946546,
       60.29517981979913,
       35.64367611230214,
       66.75803701473549,
       29.201965634468923,
       30.602296609501323,
       17.06538280281202,
       20.00354773000556,
       50.8119935279281,
       71.64639423244824,
       74.15336033762789,
       49.440140822183075,
       64.67196379999837,
       75.94519925730934,
       38.447796503325556,
       44.163722787003344,
       64.87385846784939,
       50.04838037165882,
       34.35659985575274,
       54.56307512952788,
       60.70215078607142,
       68.95664033863429,
       31.492181267677655,
       61.75408422428174,
       40.271834072820916,
       30.165026083359038,
       53.637973784687034,
       33.37471211504639,
       53.523357403836734,
       60.71066894628964,
       41.89042081202545,
       67.27223306905721,
       36.56733776280036,
       38.571707875936625,
       48.87939192344522,
       71.13443507120998,
       35.30212528572657,
       48.28329608731827,
       23.88640846801915,
       31.44989351178334,
       58.26799060998704,
       51.71798934171935,
       27.46874099876527,
       37.212786078532126,
       20.15089957530152,
       64.63216131311296,
       40.167956134402836,
       35.72293337343854,
       38.43793061319493,
       28.58001732805514,
       53.9835600631773,
       60.28246979279593,
       46.72253103459036,
       64.32013214011862,
       59.37791335394144,
       50.690079722681766,
       33.8546939485445,
       65.31499790187449,
       27.753239769540784,
       22.711934184745065,
       46.00574353839934,
       58.15825158745324,
       57.86804939856225,
       23.41412337154698,
       32.95241917021132,
       63.815475372288574,
       55.62396229117605,
       45.28041710250443,
       62.15398484075666,
       58.043186108695494,
       40.30632798778294,
       47.21058269778084,
       62.58459040390571,
       66.90956867320287,
       51.01786126295233,
       64.6649170189233,
       46.31786614043111,
       61.28335728379005,
       40.45946071693337,
       33.900639524785966,
       36.393766884624874,
       26.301756416204544,
       52.32002489201818,
       64.64275133807244,
       56.813171057224984,
       79.91914660463216,
       50.398959896432785,
       50.398782335656556,
       67.33925008338416,
       74.42232202006592,
       81.16683164095893,
       85.48454980324522,
       75.53670890824829,
       33.14472677392582,
       29.26549963624277,
       69.60541140995797,
       74.54378383387971,
       35.52310555670123,
       54.53681022344028,
       32.546906329761505,
       36.4755644024657,
       63.25254739044657,
       35.16510433232886,
       35.18170585179619,
       36.241878856468176,
       67.3178749926577,
       41.91742243253389,
       56.701749664340035,
       74.34640056803384,
       49.29978655009208,
       54.22384418179581,
       32.44031752116758,
       25.377207408748927,
       41.93448516537484,
       38.53587202172279,
       21.084637179458607,
       28.980275630948963,
       60.1146088836436,
       58.416570253687716,
       45.823510782508635,
       50.50220988728048,
       57.85232793896362,
       62.92435803993775,
       64.89961484195774,
       73.07650128211411,
       72.68438828027227,
       73.56619990234888,
       43.5513389537202,
       63.51386220075432,
       49.909651034931755,
       37.65619985685482,
       70.06396688880494,
       63.14810305447157,
       61.96443533608123,
       39.68657992600262,
       33.182852508252175,
       25.093920736850595,
       51.90887572183953,
       72.42381510321478,
       54.20385662007917,
       54.34187593144975,
       44.937999134605285,
       54.31594067600298,
       30.09535373212172,
       71.58812954501816,
       49.614159436065826,
       52.91395480106467,
       51.24733124168494,
       67.36775518322456,
       28.303781691771473,
       53.7736736671824,
       32.088982346984444,
       24.4106594958279,
       32.156113190157704,
       18.494048922202072,
       60.340323572902236,
       27.323343419713776,
       58.28295648695687,
       37.51254035787235,
       66.55904262268373,
       69.48140665121845,
       69.31840915818609,
       53.692703839314184,
       59.11430037118166,
       63.31115520847023,
       48.578369742303416,
       40.28209612440916,
       75.2880691275336,
       67.82586775915847,
       38.430645809385204,
       30.495652761139855,
       51.106346654740356,
       26.64495453746279,
       51.09749596769989,
       72.63563973331375,
       75.82304958202444,
       46.3490464122414,
       47.812804679864996,
       39.57974052448169,
       51.750631209784,
       69.60064813730948,
       31.22295180880805,
       62.42502200662698,
       30.60811823356205,
       53.05555302913164,
       53.83287440264228,
       75.57251623519181,
       76.50637127668176,
       76.06060827764576,
       51.3655670212784,
       63.63801340124146,
       40.319689452356904,
       70.02891429368366,
       66.06386067452607,
       42.252720992232305,
       34.24841533857079,
       36.376679531080605,
       54.55102854034987,
       36.78285753529732,
       46.73655307838329,
       38.615466140378004,
       29.479642445003027,
       29.761014867363475,
       52.60675328218218,
       32.55219134474758,
       59.77803296982396,
       45.59065789671214,
       53.94680525032745,
       73.50100161749104,
       72.49097871650639,
       35.361931643355916,
       64.45719338089116,
       52.41627201273398,
       49.68227101679576,
       42.706949313060846,
       53.117346077289675,
       59.87801028788769,
       28.89375247231389,
       27.577759755570256,
       23.68631731972518,
       16.664844560349213,
       22.850201994765346,
       14.029753540761208,
       54.8298381198594,
       53.497792969702566,
       55.603027879647414,
       55.53120566468814,
       61.798598002926134,
       41.85990784690475,
       52.84701560494096,
       54.07401834916939,
       68.3127290482255,
       66.52357042608257,
       42.765317641203154,
       34.69499284279583,
       64.44302347371045,
       53.663570696890815,
       40.551394060844494,
       65.13936811392601,
       71.89174769591384,
       48.757199583536725,
       69.33672146969413,
       45.13764467976475,
       68.75302859440954,
       50.401067808254076,
       41.44248353606709,
       76.79184516710102,
       74.80161005457285,
       49.97526927366369,
       58.71537704435041,
       64.32939712180996,
       39.358182501345034,
       33.95441443343755,
       28.640999536205907,
       22.65653206515326,
       51.07361768481183,
       24.939218620739677,
       48.68054711460346,
       35.15492950770412,
       56.43526200441496,
       62.33190291845489,
       25.39237904664356,
       35.343889621985475,
       27.553038212688694,
       20.93637641067407,
       43.2540799100173,
       19.10365300983482,
       56.60148890765638,
       63.876046230961414,
       27.998909667746034,
       28.593618331255,
       55.417316114556535,
       63.391665949758,
       40.80075761570583,
       52.67735219880024,
       34.007906520886614,
       37.31469428346533,
       63.971425647137046,
       54.20446202687712,
       38.8583452847449,
       39.359011599521075,
       57.214767487159065,
       58.32100171312998,
       37.45280207634567,
       35.988649402453184,
       33.04050938956805,
       61.792827412428124,
       33.47401442000429,
       31.451691052864277,
       39.24636353478838,
       60.216781256819644,
       24.44120346774579,
       51.255987518261605,
       30.378552028358303,
       54.193389650204246,
       25.721729593680703,
       40.66905950471011,
       51.38185578732759,
       23.926791012597832,
       31.762899458255717,
       43.40522038693413,
       21.476052973509066,
       63.89441388706746,
       62.35839168842339,
       64.02774409242006,
       null,
       71.55287613005031,
       67.14648674604739,
       48.12279696008046,
       40.059162834610355,
       48.6715943092218,
       70.8033875854626,
       63.359285489180934,
       53.047249165375966,
       64.79754610529612,
       62.07034307349304,
       75.38854869336876,
       82.33097713725498,
       49.735430453767094,
       39.216688604261975,
       61.04834208466599,
       49.50174048770927,
       72.19012896274874,
       36.87514486166956,
       54.38610347368249,
       40.158321525477795,
       42.404921997166966,
       68.2791437130498,
       29.68681759542387,
       null
      ],
      "type": "scatter",
      "x": [
       "2018-12-27",
       "2018-12-28",
       "2019-01-02",
       "2019-01-03",
       "2019-01-04",
       "2019-01-07",
       "2019-01-08",
       "2019-01-09",
       "2019-01-10",
       "2019-01-11",
       "2019-01-14",
       "2019-01-15",
       "2019-01-16",
       "2019-01-17",
       "2019-01-18",
       "2019-01-21",
       "2019-01-22",
       "2019-01-23",
       "2019-01-24",
       "2019-01-25",
       "2019-01-28",
       "2019-01-29",
       "2019-01-30",
       "2019-01-31",
       "2019-02-01",
       "2019-02-11",
       "2019-02-12",
       "2019-02-13",
       "2019-02-14",
       "2019-02-15",
       "2019-02-18",
       "2019-02-19",
       "2019-02-20",
       "2019-02-21",
       "2019-02-22",
       "2019-02-25",
       "2019-02-26",
       "2019-02-27",
       "2019-02-28",
       "2019-03-01",
       "2019-03-04",
       "2019-03-05",
       "2019-03-06",
       "2019-03-07",
       "2019-03-08",
       "2019-03-11",
       "2019-03-12",
       "2019-03-13",
       "2019-03-14",
       "2019-03-15",
       "2019-03-18",
       "2019-03-19",
       "2019-03-20",
       "2019-03-21",
       "2019-03-22",
       "2019-03-25",
       "2019-03-26",
       "2019-03-27",
       "2019-03-28",
       "2019-03-29",
       "2019-04-01",
       "2019-04-02",
       "2019-04-03",
       "2019-04-04",
       "2019-04-08",
       "2019-04-09",
       "2019-04-10",
       "2019-04-11",
       "2019-04-12",
       "2019-04-15",
       "2019-04-16",
       "2019-04-17",
       "2019-04-18",
       "2019-04-19",
       "2019-04-22",
       "2019-04-23",
       "2019-04-24",
       "2019-04-25",
       "2019-04-26",
       "2019-04-29",
       "2019-04-30",
       "2019-05-06",
       "2019-05-07",
       "2019-05-08",
       "2019-05-09",
       "2019-05-10",
       "2019-05-13",
       "2019-05-14",
       "2019-05-15",
       "2019-05-16",
       "2019-05-17",
       "2019-05-20",
       "2019-05-21",
       "2019-05-22",
       "2019-05-23",
       "2019-05-24",
       "2019-05-27",
       "2019-05-28",
       "2019-05-29",
       "2019-05-30",
       "2019-05-31",
       "2019-06-03",
       "2019-06-04",
       "2019-06-05",
       "2019-06-06",
       "2019-06-10",
       "2019-06-11",
       "2019-06-12",
       "2019-06-13",
       "2019-06-14",
       "2019-06-17",
       "2019-06-18",
       "2019-06-19",
       "2019-06-20",
       "2019-06-21",
       "2019-06-24",
       "2019-06-25",
       "2019-06-26",
       "2019-06-27",
       "2019-06-28",
       "2019-07-01",
       "2019-07-02",
       "2019-07-03",
       "2019-07-04",
       "2019-07-05",
       "2019-07-08",
       "2019-07-09",
       "2019-07-10",
       "2019-07-11",
       "2019-07-12",
       "2019-07-15",
       "2019-07-16",
       "2019-07-17",
       "2019-07-18",
       "2019-07-19",
       "2019-07-22",
       "2019-07-23",
       "2019-07-24",
       "2019-07-25",
       "2019-07-26",
       "2019-07-29",
       "2019-07-30",
       "2019-07-31",
       "2019-08-01",
       "2019-08-02",
       "2019-08-05",
       "2019-08-06",
       "2019-08-07",
       "2019-08-08",
       "2019-08-09",
       "2019-08-12",
       "2019-08-13",
       "2019-08-14",
       "2019-08-15",
       "2019-08-16",
       "2019-08-19",
       "2019-08-20",
       "2019-08-21",
       "2019-08-22",
       "2019-08-23",
       "2019-08-26",
       "2019-08-27",
       "2019-08-28",
       "2019-08-29",
       "2019-08-30",
       "2019-09-02",
       "2019-09-03",
       "2019-09-04",
       "2019-09-05",
       "2019-09-06",
       "2019-09-09",
       "2019-09-10",
       "2019-09-11",
       "2019-09-12",
       "2019-09-16",
       "2019-09-17",
       "2019-09-18",
       "2019-09-19",
       "2019-09-20",
       "2019-09-23",
       "2019-09-24",
       "2019-09-25",
       "2019-09-26",
       "2019-09-27",
       "2019-09-30",
       "2019-10-08",
       "2019-10-09",
       "2019-10-10",
       "2019-10-11",
       "2019-10-14",
       "2019-10-15",
       "2019-10-16",
       "2019-10-17",
       "2019-10-18",
       "2019-10-21",
       "2019-10-22",
       "2019-10-23",
       "2019-10-24",
       "2019-10-25",
       "2019-10-28",
       "2019-10-29",
       "2019-10-30",
       "2019-10-31",
       "2019-11-01",
       "2019-11-04",
       "2019-11-05",
       "2019-11-06",
       "2019-11-07",
       "2019-11-08",
       "2019-11-11",
       "2019-11-12",
       "2019-11-13",
       "2019-11-14",
       "2019-11-15",
       "2019-11-18",
       "2019-11-19",
       "2019-11-20",
       "2019-11-21",
       "2019-11-22",
       "2019-11-25",
       "2019-11-26",
       "2019-11-27",
       "2019-11-28",
       "2019-11-29",
       "2019-12-02",
       "2019-12-03",
       "2019-12-04",
       "2019-12-05",
       "2019-12-06",
       "2019-12-09",
       "2019-12-10",
       "2019-12-11",
       "2019-12-12",
       "2019-12-13",
       "2019-12-16",
       "2019-12-17",
       "2019-12-18",
       "2019-12-19",
       "2019-12-20",
       "2019-12-23",
       "2019-12-24",
       "2019-12-25",
       "2019-12-26",
       "2019-12-27",
       "2019-12-30",
       "2019-12-31",
       "2020-01-02",
       "2020-01-03",
       "2020-01-06",
       "2020-01-07",
       "2020-01-08",
       "2020-01-09",
       "2020-01-10",
       "2020-01-13",
       "2020-01-14",
       "2020-01-15",
       "2020-01-16",
       "2020-01-17",
       "2020-01-20",
       "2020-01-21",
       "2020-01-22",
       "2020-01-23",
       "2020-02-03",
       "2020-02-04",
       "2020-02-05",
       "2020-02-06",
       "2020-02-07",
       "2020-02-10",
       "2020-02-11",
       "2020-02-12",
       "2020-02-13",
       "2020-02-14",
       "2020-02-17",
       "2020-02-18",
       "2020-02-19",
       "2020-02-20",
       "2020-02-21",
       "2020-02-24",
       "2020-02-25",
       "2020-02-26",
       "2020-02-27",
       "2020-02-28",
       "2020-03-02",
       "2020-03-03",
       "2020-03-04",
       "2020-03-05",
       "2020-03-06",
       "2020-03-09",
       "2020-03-10",
       "2020-03-11",
       "2020-03-12",
       "2020-03-13",
       "2020-03-16",
       "2020-03-17",
       "2020-03-18",
       "2020-03-19",
       "2020-03-20",
       "2020-03-23",
       "2020-03-24",
       "2020-03-25",
       "2020-03-26",
       "2020-03-27",
       "2020-03-30",
       "2020-03-31",
       "2020-04-01",
       "2020-04-02",
       "2020-04-03",
       "2020-04-07",
       "2020-04-08",
       "2020-04-09",
       "2020-04-10",
       "2020-04-13",
       "2020-04-14",
       "2020-04-15",
       "2020-04-16",
       "2020-04-17",
       "2020-04-20",
       "2020-04-21",
       "2020-04-22",
       "2020-04-23",
       "2020-04-24",
       "2020-04-27",
       "2020-04-28",
       "2020-04-29",
       "2020-04-30",
       "2020-05-06",
       "2020-05-07",
       "2020-05-08",
       "2020-05-11",
       "2020-05-12",
       "2020-05-13",
       "2020-05-14",
       "2020-05-15",
       "2020-05-18",
       "2020-05-19",
       "2020-05-20",
       "2020-05-21",
       "2020-05-22",
       "2020-05-25",
       "2020-05-26",
       "2020-05-27",
       "2020-05-28",
       "2020-05-29",
       "2020-06-01",
       "2020-06-02",
       "2020-06-03",
       "2020-06-04",
       "2020-06-05",
       "2020-06-08",
       "2020-06-09",
       "2020-06-10",
       "2020-06-11",
       "2020-06-12",
       "2020-06-15",
       "2020-06-16",
       "2020-06-17",
       "2020-06-18",
       "2020-06-19",
       "2020-06-22",
       "2020-06-23",
       "2020-06-24",
       "2020-06-29",
       "2020-06-30",
       "2020-07-01",
       "2020-07-02",
       "2020-07-03",
       "2020-07-06",
       "2020-07-07",
       "2020-07-08",
       "2020-07-09",
       "2020-07-10",
       "2020-07-13",
       "2020-07-14",
       "2020-07-15",
       "2020-07-16",
       "2020-07-17",
       "2020-07-20",
       "2020-07-21",
       "2020-07-22",
       "2020-07-23",
       "2020-07-24",
       "2020-07-27",
       "2020-07-28",
       "2020-07-29",
       "2020-07-30",
       "2020-07-31",
       "2020-08-03",
       "2020-08-04",
       "2020-08-05",
       "2020-08-06",
       "2020-08-07",
       "2020-08-10",
       "2020-08-11",
       "2020-08-12",
       "2020-08-13",
       "2020-08-14",
       "2020-08-17",
       "2020-08-18",
       "2020-08-19",
       "2020-08-20",
       "2020-08-21",
       "2020-08-24",
       "2020-08-25",
       "2020-08-26",
       "2020-08-27",
       "2020-08-28",
       "2020-08-31",
       "2020-09-01",
       "2020-09-02",
       "2020-09-03",
       "2020-09-04",
       "2020-09-07",
       "2020-09-08",
       "2020-09-09",
       "2020-09-10",
       "2020-09-11",
       "2020-09-14",
       "2020-09-15",
       "2020-09-16",
       "2020-09-17",
       "2020-09-18",
       "2020-09-21",
       "2020-09-22",
       "2020-09-23",
       "2020-09-24",
       "2020-09-25",
       "2020-09-28",
       "2020-09-29",
       "2020-09-30",
       "2020-10-09",
       "2020-10-12",
       "2020-10-13",
       "2020-10-14",
       "2020-10-15",
       "2020-10-16",
       "2020-10-19",
       "2020-10-20",
       "2020-10-21",
       "2020-10-22",
       "2020-10-23",
       "2020-10-26",
       "2020-10-27",
       "2020-10-28",
       "2020-10-29",
       "2020-10-30",
       "2020-11-02",
       "2020-11-03",
       "2020-11-04",
       "2020-11-05",
       "2020-11-06",
       "2020-11-09",
       "2020-11-10",
       "2020-11-11",
       "2020-11-12",
       "2020-11-13",
       "2020-11-16",
       "2020-11-17",
       "2020-11-18",
       "2020-11-19",
       "2020-11-20",
       "2020-11-23",
       "2020-11-24",
       "2020-11-25",
       "2020-11-26",
       "2020-11-27",
       "2020-11-30",
       "2020-12-01",
       "2020-12-02",
       "2020-12-03",
       "2020-12-04",
       "2020-12-07",
       "2020-12-08",
       "2020-12-09",
       "2020-12-10",
       "2020-12-11",
       "2020-12-14",
       "2020-12-15",
       "2020-12-16",
       "2020-12-17",
       "2020-12-18",
       "2020-12-21",
       "2020-12-22",
       "2020-12-23",
       "2020-12-24",
       "2020-12-25",
       "2020-12-28",
       "2020-12-29",
       "2020-12-30",
       "2020-12-31",
       "2021-01-04",
       "2021-01-05",
       "2021-01-06",
       "2021-01-07",
       "2021-01-08",
       "2021-01-11",
       "2021-01-12",
       "2021-01-13",
       "2021-01-14",
       "2021-01-15",
       "2021-01-18",
       "2021-01-19",
       "2021-01-20",
       "2021-01-21",
       "2021-01-22",
       "2021-01-25",
       "2021-01-26",
       "2021-01-27",
       "2021-01-28",
       "2021-01-29",
       "2021-02-01",
       "2021-02-02",
       "2021-02-03",
       "2021-02-04",
       "2021-02-05",
       "2021-02-08",
       "2021-02-09",
       "2021-02-10",
       "2021-02-18",
       "2021-02-19",
       "2021-02-22",
       "2021-02-23",
       "2021-02-24",
       "2021-02-25",
       "2021-02-26",
       "2021-03-01",
       "2021-03-02",
       "2021-03-03",
       "2021-03-04",
       "2021-03-05",
       "2021-03-08",
       "2021-03-09",
       "2021-03-10",
       "2021-03-11",
       "2021-03-12",
       "2021-03-15",
       "2021-03-16",
       "2021-03-17",
       "2021-03-18",
       "2021-03-19",
       "2021-03-22",
       "2021-03-23",
       "2021-03-24",
       "2021-03-25",
       "2021-03-26",
       "2021-03-29",
       "2021-03-30",
       "2021-03-31",
       "2021-04-01",
       "2021-04-02",
       "2021-04-06",
       "2021-04-07",
       "2021-04-08",
       "2021-04-09",
       "2021-04-12",
       "2021-04-13",
       "2021-04-14",
       "2021-04-15",
       "2021-04-16",
       "2021-04-19",
       "2021-04-20",
       "2021-04-21",
       "2021-04-22",
       "2021-04-23",
       "2021-04-26",
       "2021-04-27",
       "2021-04-28",
       "2021-04-29",
       "2021-04-30",
       "2021-05-06",
       "2021-05-07",
       "2021-05-10",
       "2021-05-11",
       "2021-05-12",
       "2021-05-13",
       "2021-05-14",
       "2021-05-17",
       "2021-05-18",
       "2021-05-19",
       "2021-05-20",
       "2021-05-21",
       "2021-05-24",
       "2021-05-25",
       "2021-05-26",
       "2021-05-27",
       "2021-05-28",
       "2021-05-31",
       "2021-06-01",
       "2021-06-02",
       "2021-06-03",
       "2021-06-04",
       "2021-06-07",
       "2021-06-08",
       "2021-06-09",
       "2021-06-10",
       "2021-06-11",
       "2021-06-15",
       "2021-06-16",
       "2021-06-17",
       "2021-06-18",
       "2021-06-21",
       "2021-06-22",
       "2021-06-23",
       "2021-06-24",
       "2021-06-25",
       "2021-06-28",
       "2021-06-29",
       "2021-06-30",
       "2021-07-01",
       "2021-07-02",
       "2021-07-05",
       "2021-07-06",
       "2021-07-07",
       "2021-07-08",
       "2021-07-09",
       "2021-07-12",
       "2021-07-13",
       "2021-07-14",
       "2021-07-15",
       "2021-07-16",
       "2021-07-19",
       "2021-07-20",
       "2021-07-21",
       "2021-07-22",
       "2021-07-23",
       "2021-07-26",
       "2021-07-27",
       "2021-07-28",
       "2021-07-29",
       "2021-07-30",
       "2021-08-02",
       "2021-08-03",
       "2021-08-04",
       "2021-08-05",
       "2021-08-06",
       "2021-08-09",
       "2021-08-10",
       "2021-08-11",
       "2021-08-12",
       "2021-08-13",
       "2021-08-16",
       "2021-08-17",
       "2021-08-18",
       "2021-08-19",
       "2021-08-20",
       "2021-08-23",
       "2021-08-24",
       "2021-08-25",
       "2021-08-26",
       "2021-08-27",
       "2021-08-30",
       "2021-08-31",
       "2021-09-01",
       "2021-09-02",
       "2021-09-03",
       "2021-09-06",
       "2021-09-07",
       "2021-09-08",
       "2021-09-09",
       "2021-09-10",
       "2021-09-13",
       "2021-09-14",
       "2021-09-15",
       "2021-09-16",
       "2021-09-17",
       "2021-09-22",
       "2021-09-23",
       "2021-09-24",
       "2021-09-27",
       "2021-09-28",
       "2021-09-29",
       "2021-09-30",
       "2021-10-08",
       "2021-10-11",
       "2021-10-12",
       "2021-10-13",
       "2021-10-14",
       "2021-10-15",
       "2021-10-18",
       "2021-10-19",
       "2021-10-20",
       "2021-10-21",
       "2021-10-22",
       "2021-10-25",
       "2021-10-26",
       "2021-10-27",
       "2021-10-28",
       "2021-10-29",
       "2021-11-01",
       "2021-11-02",
       "2021-11-03",
       "2021-11-04",
       "2021-11-05",
       "2021-11-08",
       "2021-11-09",
       "2021-11-10",
       "2021-11-11",
       "2021-11-12",
       "2021-11-15",
       "2021-11-16",
       "2021-11-17",
       "2021-11-18",
       "2021-11-19",
       "2021-11-22",
       "2021-11-23",
       "2021-11-24",
       "2021-11-25",
       "2021-11-26",
       "2021-11-29",
       "2021-11-30",
       "2021-12-01",
       "2021-12-02",
       "2021-12-03",
       "2021-12-06",
       "2021-12-07",
       "2021-12-08",
       "2021-12-09",
       "2021-12-10",
       "2021-12-13",
       "2021-12-14",
       "2021-12-15",
       "2021-12-16",
       "2021-12-17",
       "2021-12-20",
       "2021-12-21",
       "2021-12-22",
       "2021-12-23",
       "2021-12-24",
       "2021-12-27",
       "2021-12-28",
       "2021-12-29",
       "2021-12-30",
       "2021-12-31",
       "2022-01-04",
       "2022-01-05",
       "2022-01-06",
       "2022-01-07",
       "2022-01-10",
       "2022-01-11",
       "2022-01-12",
       "2022-01-13",
       "2022-01-14",
       "2022-01-17",
       "2022-01-18",
       "2022-01-19",
       "2022-01-20",
       "2022-01-21",
       "2022-01-24",
       "2022-01-25",
       "2022-01-26",
       "2022-01-27",
       "2022-01-28",
       "2022-02-07",
       "2022-02-08",
       "2022-02-09",
       "2022-02-10",
       "2022-02-11",
       "2022-02-14",
       "2022-02-15",
       "2022-02-16",
       "2022-02-17",
       "2022-02-18",
       "2022-02-21",
       "2022-02-22",
       "2022-02-23",
       "2022-02-24",
       "2022-02-25",
       "2022-02-28",
       "2022-03-01",
       "2022-03-02",
       "2022-03-03",
       "2022-03-04",
       "2022-03-07",
       "2022-03-08",
       "2022-03-09",
       "2022-03-10",
       "2022-03-11",
       "2022-03-14",
       "2022-03-15",
       "2022-03-16",
       "2022-03-17",
       "2022-03-18",
       "2022-03-21",
       "2022-03-22",
       "2022-03-23",
       "2022-03-24",
       "2022-03-25",
       "2022-03-28",
       "2022-03-29",
       "2022-03-30",
       "2022-03-31",
       "2022-04-01",
       "2022-04-06",
       "2022-04-07",
       "2022-04-08",
       "2022-04-11",
       "2022-04-12",
       "2022-04-13",
       "2022-04-14",
       "2022-04-15",
       "2022-04-18",
       "2022-04-19",
       "2022-04-20",
       "2022-04-21",
       "2022-04-22",
       "2022-04-25",
       "2022-04-26",
       "2022-04-27",
       "2022-04-28",
       "2022-04-29",
       "2022-05-05",
       "2022-05-06",
       "2022-05-09",
       "2022-05-10",
       "2022-05-11",
       "2022-05-12",
       "2022-05-13",
       "2022-05-16",
       "2022-05-17",
       "2022-05-18",
       "2022-05-19",
       "2022-05-20",
       "2022-05-23",
       "2022-05-24",
       "2022-05-25",
       "2022-05-26",
       "2022-05-27",
       "2022-05-30",
       "2022-05-31",
       "2022-06-01",
       "2022-06-02",
       "2022-06-06",
       "2022-06-07",
       "2022-06-08",
       "2022-06-09",
       "2022-06-10",
       "2022-06-13",
       "2022-06-14",
       "2022-06-15",
       "2022-06-16",
       "2022-06-17",
       "2022-06-20",
       "2022-06-21",
       "2022-06-22",
       "2022-06-23",
       "2022-06-24",
       "2022-06-27",
       "2022-06-28",
       "2022-06-29",
       "2022-06-30",
       "2022-07-01",
       "2022-07-04",
       "2022-07-05",
       "2022-07-06",
       "2022-07-07",
       "2022-07-08",
       "2022-07-11",
       "2022-07-12",
       "2022-07-13",
       "2022-07-14",
       "2022-07-15",
       "2022-07-18",
       "2022-07-19",
       "2022-07-20",
       "2022-07-21",
       "2022-07-22",
       "2022-07-25",
       "2022-07-26",
       "2022-07-27",
       "2022-07-28",
       "2022-07-29",
       "2022-08-01",
       "2022-08-02",
       "2022-08-03",
       "2022-08-04",
       "2022-08-05",
       "2022-08-08",
       "2022-08-09",
       "2022-08-10",
       "2022-08-11",
       "2022-08-12",
       "2022-08-15",
       "2022-08-16",
       "2022-08-17",
       "2022-08-18",
       "2022-08-19",
       "2022-08-22",
       "2022-08-23",
       "2022-08-24",
       "2022-08-25",
       "2022-08-26",
       "2022-08-29",
       "2022-08-30",
       "2022-08-31",
       "2022-09-01",
       "2022-09-02",
       "2022-09-05",
       "2022-09-06",
       "2022-09-07",
       "2022-09-08",
       "2022-09-09",
       "2022-09-13",
       "2022-09-14",
       "2022-09-15",
       "2022-09-16",
       "2022-09-19",
       "2022-09-20",
       "2022-09-21",
       "2022-09-22",
       "2022-09-23",
       "2022-09-26",
       "2022-09-27",
       "2022-09-28",
       "2022-09-29",
       "2022-09-30",
       "2022-10-10",
       "2022-10-11",
       "2022-10-12",
       "2022-10-13",
       "2022-10-14",
       "2022-10-17",
       "2022-10-18",
       "2022-10-19",
       "2022-10-20",
       "2022-10-21",
       "2022-10-24",
       "2022-10-25",
       "2022-10-26",
       "2022-10-27",
       "2022-10-28",
       "2022-10-31",
       "2022-11-01",
       "2022-11-02",
       "2022-11-03",
       "2022-11-04",
       "2022-11-07",
       "2022-11-08",
       "2022-11-09",
       "2022-11-10",
       "2022-11-11",
       "2022-11-14",
       "2022-11-15",
       "2022-11-16",
       "2022-11-17",
       "2022-11-18",
       "2022-11-21",
       "2022-11-22",
       "2022-11-23",
       "2022-11-24",
       "2022-11-25",
       "2022-11-28",
       "2022-11-29",
       "2022-11-30",
       "2022-12-01",
       "2022-12-02",
       "2022-12-05",
       "2022-12-06",
       "2022-12-07",
       "2022-12-08",
       "2022-12-09",
       "2022-12-12",
       "2022-12-13",
       "2022-12-14",
       "2022-12-15",
       "2022-12-16",
       "2022-12-19",
       "2022-12-20",
       "2022-12-21",
       "2022-12-22",
       "2022-12-23",
       "2022-12-26",
       "2022-12-27",
       "2022-12-28",
       "2022-12-29",
       "2022-12-30",
       "2023-01-03",
       "2023-01-04",
       "2023-01-05",
       "2023-01-06",
       "2023-01-09",
       "2023-01-10",
       "2023-01-11",
       "2023-01-12",
       "2023-01-13",
       "2023-01-16",
       "2023-01-17",
       "2023-01-18",
       "2023-01-19",
       "2023-01-20",
       "2023-01-30",
       "2023-01-31",
       "2023-02-01",
       "2023-02-02",
       "2023-02-03",
       "2023-02-06",
       "2023-02-07",
       "2023-02-08",
       "2023-02-09",
       "2023-02-10",
       "2023-02-13",
       "2023-02-14",
       "2023-02-15",
       "2023-02-16",
       "2023-02-17",
       "2023-02-20",
       "2023-02-21",
       "2023-02-22",
       "2023-02-23",
       "2023-02-24",
       "2023-02-27",
       "2023-02-28",
       "2023-03-01",
       "2023-03-02",
       "2023-03-03",
       "2023-03-06",
       "2023-03-07",
       "2023-03-08",
       "2023-03-09",
       "2023-03-10",
       "2023-03-13",
       "2023-03-14",
       "2023-03-15",
       "2023-03-16",
       "2023-03-17",
       "2023-03-20",
       "2023-03-21",
       "2023-03-22",
       "2023-03-23",
       "2023-03-24",
       "2023-03-27",
       "2023-03-28",
       "2023-03-29",
       "2023-03-30",
       "2023-03-31",
       "2023-04-03",
       "2023-04-04",
       "2023-04-06",
       "2023-04-07",
       "2023-04-10",
       "2023-04-11",
       "2023-04-12",
       "2023-04-13",
       "2023-04-14",
       "2023-04-17",
       "2023-04-18",
       "2023-04-19",
       "2023-04-20",
       "2023-04-21",
       "2023-04-24",
       "2023-04-25",
       "2023-04-26",
       "2023-04-27",
       "2023-04-28",
       "2023-05-04",
       "2023-05-05",
       "2023-05-08",
       "2023-05-09",
       "2023-05-10",
       "2023-05-11",
       "2023-05-12",
       "2023-05-15",
       "2023-05-16",
       "2023-05-17",
       "2023-05-18",
       "2023-05-19",
       "2023-05-22",
       "2023-05-23",
       "2023-05-24",
       "2023-05-25",
       "2023-05-26",
       "2023-05-29",
       "2023-05-30",
       "2023-05-31",
       "2023-06-01",
       "2023-06-02",
       "2023-06-05",
       "2023-06-06",
       "2023-06-07",
       "2023-06-08",
       "2023-06-09",
       "2023-06-12",
       "2023-06-13",
       "2023-06-14",
       "2023-06-15",
       "2023-06-16",
       "2023-06-19",
       "2023-06-20",
       "2023-06-21",
       "2023-06-26",
       "2023-06-27",
       "2023-06-28",
       "2023-06-29",
       "2023-06-30",
       "2023-07-03",
       "2023-07-04",
       "2023-07-05",
       "2023-07-06",
       "2023-07-07",
       "2023-07-10",
       "2023-07-11",
       "2023-07-12",
       "2023-07-13",
       "2023-07-14",
       "2023-07-17",
       "2023-07-18",
       "2023-07-19",
       "2023-07-20",
       "2023-07-21",
       "2023-07-24",
       "2023-07-25",
       "2023-07-26",
       "2023-07-27",
       "2023-07-28",
       "2023-07-31",
       "2023-08-01",
       "2023-08-02",
       "2023-08-03",
       "2023-08-04",
       "2023-08-07",
       "2023-08-08",
       "2023-08-09",
       "2023-08-10",
       "2023-08-11",
       "2023-08-14",
       "2023-08-15",
       "2023-08-16",
       "2023-08-17",
       "2023-08-18",
       "2023-08-21",
       "2023-08-22",
       "2023-08-23",
       "2023-08-24",
       "2023-08-25",
       "2023-08-28",
       "2023-08-29",
       "2023-08-30",
       "2023-08-31",
       "2023-09-01",
       "2023-09-04",
       "2023-09-05",
       "2023-09-06",
       "2023-09-07",
       "2023-09-08",
       "2023-09-11",
       "2023-09-12",
       "2023-09-13",
       "2023-09-14",
       "2023-09-15",
       "2023-09-18",
       "2023-09-19",
       "2023-09-20",
       "2023-09-21",
       "2023-09-22",
       "2023-09-25",
       "2023-09-26",
       "2023-09-27",
       "2023-09-28",
       "2023-10-09",
       "2023-10-10",
       "2023-10-11",
       "2023-10-12",
       "2023-10-13",
       "2023-10-16",
       "2023-10-17",
       "2023-10-18",
       "2023-10-19",
       "2023-10-20",
       "2023-10-23",
       "2023-10-24",
       "2023-10-25",
       "2023-10-26",
       "2023-10-27",
       "2023-10-30",
       "2023-10-31",
       "2023-11-01",
       "2023-11-02",
       "2023-11-03",
       "2023-11-06",
       "2023-11-07",
       "2023-11-08",
       "2023-11-09",
       "2023-11-10",
       "2023-11-13",
       "2023-11-14",
       "2023-11-15",
       "2023-11-16",
       "2023-11-17",
       "2023-11-20",
       "2023-11-21",
       "2023-11-22",
       "2023-11-23",
       "2023-11-24",
       "2023-11-27",
       "2023-11-28",
       "2023-11-29",
       "2023-11-30"
      ],
      "xaxis": "x",
      "y": [
       1,
       1.0021977424621582,
       1,
       0.9846154451370239,
       0.995604395866394,
       1.0373625755310059,
       1.0571428537368774,
       1.0483516454696655,
       1.0417581796646118,
       1.0615384578704834,
       1.0659340620040894,
       1.0659340620040894,
       1.0747252702713013,
       1.0571428537368774,
       1.068131923675537,
       1.072527527809143,
       1.0637362003326416,
       1.0593407154083252,
       1,
       1,
       0.9890110492706299,
       0.9890110492706299,
       0.9604395031929016,
       0.9516482949256897,
       0.9406593441963196,
       0.9604395031929016,
       0.9780219197273254,
       0.995604395866394,
       1.0087913274765015,
       1.0131869316101074,
       1.0131869316101074,
       1.0439560413360596,
       1.0417581796646118,
       1.0417581796646118,
       1.0329669713974,
       1.0615384578704834,
       1.1208791732788086,
       1.1120879650115967,
       1.1186813116073608,
       1.129670262336731,
       1.1450549364089966,
       1.1560440063476562,
       1.1912087202072144,
       1.2087912559509277,
       1.2417582273483276,
       1.1868131160736084,
       1.230769157409668,
       1.2395604848861694,
       1.2087912559509277,
       1.1846153736114502,
       1.2087912559509277,
       1.2351648807525635,
       1.2285715341567993,
       1.2241759300231934,
       1.2461538314819336,
       1.259340763092041,
       1.2329670190811157,
       1.1890109777450562,
       1.1846153736114502,
       1.1758241653442383,
       1.221977949142456,
       1.259340763092041,
       1.2571427822113037,
       1.2813186645507812,
       1.2813186645507812,
       1.2857142686843872,
       1.2769230604171753,
       1.2813186645507812,
       1.2483515739440918,
       1.2527471780776978,
       1.2527471780776978,
       1.2571427822113037,
       1.2659341096878052,
       1.2527471780776978,
       1.2637362480163574,
       1.2505494356155396,
       1.210988998413086,
       1.2131868600845337,
       1.1670329570770264,
       1.1428570747375488,
       1.1142857074737549,
       1.125274658203125,
       1.0593407154083252,
       1.0791207551956177,
       1.068131923675537,
       1.0549451112747192,
       1.0791207551956177,
       1.0703296661376953,
       1.0703296661376953,
       1.0835163593292236,
       1.0879119634628296,
       1.0417581796646118,
       1.0329669713974,
       1.0461539030075073,
       1.0351648330688477,
       1.019780158996582,
       1.019780158996582,
       1.0417581796646118,
       1.0439560413360596,
       1.0329669713974,
       1.024175763130188,
       1.019780158996582,
       1.0109889507293701,
       0.997802197933197,
       0.997802197933197,
       0.9802197217941284,
       0.9912088513374329,
       1.019780158996582,
       1.0131869316101074,
       1.019780158996582,
       1.015384554862976,
       1.0131869316101074,
       1.024175763130188,
       1.0285714864730835,
       1.0461539030075073,
       1.054982304573059,
       1.0461539030075073,
       1.041739821434021,
       1.0395326614379883,
       1.0439469814300537,
       1.028497338294983,
       1.04836106300354,
       1.0439469814300537,
       1.035118579864502,
       1.041739821434021,
       1.0439469814300537,
       0.9975982308387756,
       0.995391309261322,
       0.9887700080871582,
       0.9909770488739014,
       0.995391309261322,
       1.0174620151519775,
       1.0174620151519775,
       1.0108407735824585,
       1.0064265727996826,
       1.0086337327957153,
       0.9909770488739014,
       0.9931840896606445,
       1.0064265727996826,
       1.0020124912261963,
       0.9975982308387756,
       0.995391309261322,
       1.0064265727996826,
       1.0020124912261963,
       0.9909770488739014,
       0.9689062833786011,
       0.9556638598442078,
       0.9225577116012573,
       0.9181435704231262,
       0.9225577116012573,
       0.9137294292449951,
       0.9247647523880005,
       0.9203506112098694,
       0.9247647523880005,
       0.9225577116012573,
       0.9247647523880005,
       0.9534568190574646,
       0.9534568190574646,
       0.9556638598442078,
       0.9534568190574646,
       0.9512496590614319,
       0.9380072355270386,
       0.9600780010223389,
       0.9622851014137268,
       0.9622851014137268,
       0.9556638598442078,
       0.9711134433746338,
       0.9689062833786011,
       0.9755275845527649,
       0.9777345657348633,
       0.9777345657348633,
       0.9931840896606445,
       0.9931840896606445,
       0.9909770488739014,
       0.995391309261322,
       0.9931840896606445,
       0.9689062833786011,
       0.9666992425918579,
       0.9689062833786011,
       0.9733204245567322,
       0.9556638598442078,
       0.9578709602355957,
       0.9402143359184265,
       0.9247647523880005,
       0.9335931539535522,
       0.9203506112098694,
       0.9313859343528748,
       0.9380072355270386,
       0.9424213767051697,
       0.9490426778793335,
       0.9666992425918579,
       0.9711134433746338,
       0.96449214220047,
       0.9556638598442078,
       0.9380072355270386,
       0.9335931539535522,
       0.9446285367012024,
       0.9534568190574646,
       0.9512496590614319,
       0.9512496590614319,
       0.96449214220047,
       0.9556638598442078,
       0.9446285367012024,
       0.9313859343528748,
       0.9424213767051697,
       0.9380072355270386,
       0.9380072355270386,
       0.9225577116012573,
       0.9225577116012573,
       0.9203506112098694,
       0.902694046497345,
       0.902694046497345,
       0.8960728049278259,
       0.8916586637496948,
       0.8828303217887878,
       0.8850374817848206,
       0.8960728049278259,
       0.8850374817848206,
       0.8894516229629517,
       0.887244462966919,
       0.9049010872840881,
       0.900486946105957,
       0.9049010872840881,
       0.8982799053192139,
       0.9049010872840881,
       0.902694046497345,
       0.902694046497345,
       0.8982799053192139,
       0.9071082472801208,
       0.9137294292449951,
       0.9137294292449951,
       0.9137294292449951,
       0.9093151688575745,
       0.902694046497345,
       0.9159364700317383,
       0.9203506112098694,
       0.9313859343528748,
       0.9291790127754211,
       0.9335931539535522,
       0.9291790127754211,
       0.911522388458252,
       0.9181435704231262,
       0.9137294292449951,
       0.9247647523880005,
       0.9402143359184265,
       0.9711134433746338,
       0.9622851014137268,
       0.9998053908348083,
       0.9909770488739014,
       0.979941725730896,
       0.9887700080871582,
       0.9666992425918579,
       0.9821486473083496,
       0.9909770488739014,
       0.9975982308387756,
       0.9887700080871582,
       0.9733204245567322,
       0.9711134433746338,
       0.9711134433746338,
       0.9733204245567322,
       0.9512496590614319,
       0.9490426778793335,
       0.9225577116012573,
       0.829860508441925,
       0.8210322260856628,
       0.829860508441925,
       0.8453100323677063,
       0.8453100323677063,
       0.8607596158981323,
       0.8519312739372253,
       0.8629667162895203,
       0.8497242331504822,
       0.8475171327590942,
       0.8651737570762634,
       0.8740020394325256,
       0.8673808574676514,
       0.8850374817848206,
       0.8850374817848206,
       0.8850374817848206,
       0.8762091398239136,
       0.887244462966919,
       0.887244462966919,
       0.8453100323677063,
       0.8740020394325256,
       0.8740020394325256,
       0.9181435704231262,
       0.9159364700317383,
       0.9468355178833008,
       0.8982799053192139,
       0.9159364700317383,
       0.9049010872840881,
       0.8850374817848206,
       0.8717949390411377,
       0.8541383147239685,
       0.8541383147239685,
       0.8364817500114441,
       0.8585525155067444,
       0.8629667162895203,
       0.838688850402832,
       0.8519312739372253,
       0.8673808574676514,
       0.8585525155067444,
       0.8585525155067444,
       0.8453100323677063,
       0.8408958911895752,
       0.8364817500114441,
       0.8475171327590942,
       0.8431029319763184,
       0.8541383147239685,
       0.8607596158981323,
       0.8563454747200012,
       0.8431029319763184,
       0.8408958911895752,
       0.8475171327590942,
       0.838688850402832,
       0.838688850402832,
       0.8364817500114441,
       0.8541383147239685,
       0.8497242331504822,
       0.8717949390411377,
       0.8784162402153015,
       0.8695878982543945,
       0.8784162402153015,
       0.8519312739372253,
       0.8541383147239685,
       0.8651737570762634,
       0.8740020394325256,
       0.8695878982543945,
       0.8740020394325256,
       0.8717949390411377,
       0.8651737570762634,
       0.8717949390411377,
       0.8607596158981323,
       0.8607596158981323,
       0.8541383147239685,
       0.8607596158981323,
       0.8519312739372253,
       0.8519312739372253,
       0.8364817500114441,
       0.838688850402832,
       0.8497242331504822,
       0.8519312739372253,
       0.8519312739372253,
       0.8673808574676514,
       0.900486946105957,
       0.9909770488739014,
       1.0902955532073975,
       1.1984422206878662,
       1.0902955532073975,
       0.995391309261322,
       0.9843558669090271,
       0.9843558669090271,
       0.9710537195205688,
       0.9688366651535034,
       0.9466664791107178,
       0.9644025564193726,
       0.940015435218811,
       0.9377984404563904,
       0.9865727424621582,
       0.9444494843482971,
       0.920062243938446,
       0.9178452491760254,
       0.9156282544136047,
       0.928930401802063,
       0.9377984404563904,
       0.9466664791107178,
       0.9555345177650452,
       1.002091884613037,
       1.002091884613037,
       1.033130168914795,
       1.0597344636917114,
       1.0442153215408325,
       1.0663853883743286,
       1.0885555744171143,
       1.0242620706558228,
       0.9932239055633545,
       0.9843558669090271,
       1.0264791250228882,
       1.0220451354980469,
       1.0153940916061401,
       0.9998749494552612,
       0.9621856808662415,
       0.9555345177650452,
       0.9644025564193726,
       0.9865727424621582,
       0.9910067915916443,
       0.997657835483551,
       1.0264791250228882,
       1.0109599828720093,
       1.0109599828720093,
       1.004309058189392,
       0.9865727424621582,
       1.0087430477142334,
       0.9865727424621582,
       0.9821387529373169,
       1.0153940916061401,
       1.0264791250228882,
       1.0309131145477295,
       1.0242620706558228,
       1.0065258741378784,
       0.997657835483551,
       1.0065258741378784,
       1.0242620706558228,
       1.0109599828720093,
       1.002091884613037,
       0.997657835483551,
       1.004309058189392,
       1.0087430477142334,
       1.057517409324646,
       1.0553003549575806,
       1.0530833005905151,
       1.028696060180664,
       1.0109599828720093,
       1.028696060180664,
       1.028696060180664,
       0.9910067915916443,
       0.9954409003257751,
       0.9865727424621582,
       0.9821387529373169,
       0.9843558669090271,
       0.9865727424621582,
       0.997657835483551,
       0.997657835483551,
       0.9843558669090271,
       0.9821387529373169,
       0.9533175230026245,
       0.9422324299812317,
       0.9333643913269043,
       0.9444494843482971,
       0.9333643913269043,
       0.9533175230026245,
       0.9710537195205688,
       0.9666196703910828,
       0.959968626499176,
       0.9688366651535034,
       0.9621856808662415,
       0.9644025564193726,
       0.9732707142829895,
       0.9555345177650452,
       0.948883593082428,
       0.9444494843482971,
       0.9377984404563904,
       0.9355813264846802,
       0.920062243938446,
       0.9111942052841187,
       0.8823729753494263,
       0.8823729753494263,
       0.9001091122627258,
       0.8956750631332397,
       0.9045431613922119,
       0.9023261666297913,
       0.9178452491760254,
       0.9134112000465393,
       0.9089770913124084,
       0.9111942052841187,
       0.9023261666297913,
       0.920062243938446,
       0.920062243938446,
       0.9244963526725769,
       0.9244963526725769,
       0.9222792387008667,
       0.9333643913269043,
       0.928930401802063,
       0.9267132878303528,
       0.9222792387008667,
       0.928930401802063,
       0.928930401802063,
       0.9377984404563904,
       0.9422324299812317,
       0.940015435218811,
       0.9267132878303528,
       0.9267132878303528,
       0.9222792387008667,
       0.9001091122627258,
       0.9001091122627258,
       0.8890240788459778,
       0.8978921175003052,
       0.8978921175003052,
       0.9023261666297913,
       0.9001091122627258,
       0.8956750631332397,
       0.9023261666297913,
       0.8845899701118469,
       0.8868070244789124,
       0.862419843673706,
       0.8757219314575195,
       0.9045431613922119,
       0.8956750631332397,
       0.8934581279754639,
       0.9067602157592773,
       0.9045431613922119,
       0.8978921175003052,
       0.8845899701118469,
       0.8602028489112854,
       0.8646368384361267,
       0.8424666523933411,
       0.8446837067604065,
       0.8269475698471069,
       0.8335986137390137,
       0.8469006419181824,
       0.8469006419181824,
       0.8535517454147339,
       0.8446837067604065,
       0.8491176962852478,
       0.8358156085014343,
       0.8202964663505554,
       0.8136454224586487,
       0.8202964663505554,
       0.8158624768257141,
       0.8047773838043213,
       0.8092114329338074,
       0.811428427696228,
       0.7981262803077698,
       0.7781731486320496,
       0.7737391591072083,
       0.7759560942649841,
       0.7826071977615356,
       0.787041187286377,
       0.8025602698326111,
       0.8247305154800415,
       0.8335986137390137,
       0.8313815593719482,
       0.8313815593719482,
       0.8313815593719482,
       0.8313815593719482,
       0.8358156085014343,
       0.8313815593719482,
       0.8402496576309204,
       0.838032603263855,
       0.8469006419181824,
       0.8402496576309204,
       0.8291645646095276,
       0.8225135207176208,
       0.8291645646095276,
       0.8402496576309204,
       0.8469006419181824,
       0.8513347506523132,
       0.8513347506523132,
       0.8469006419181824,
       0.8446837067604065,
       0.8535517454147339,
       0.8535517454147339,
       0.8557687401771545,
       0.8491176962852478,
       0.8513347506523132,
       0.8513347506523132,
       0.8335986137390137,
       0.8402496576309204,
       0.8579857349395752,
       0.8513347506523132,
       0.8579857349395752,
       0.8646368384361267,
       0.8513347506523132,
       0.8668538928031921,
       0.8757219314575195,
       0.8668538928031921,
       0.9023261666297913,
       0.8912410140037537,
       0.8934581279754639,
       0.8868070244789124,
       0.8868070244789124,
       0.8757219314575195,
       0.8712879419326782,
       0.862419843673706,
       0.8535517454147339,
       0.8513347506523132,
       0.8557687401771545,
       0.8801559805870056,
       0.8712879419326782,
       0.877938985824585,
       0.8868070244789124,
       0.9156282544136047,
       0.9644025564193726,
       0.9577516317367554,
       0.9377984404563904,
       0.9311472773551941,
       0.920062243938446,
       0.9222792387008667,
       0.9178452491760254,
       0.9134112000465393,
       0.920062243938446,
       0.9244963526725769,
       0.920062243938446,
       0.9444494843482971,
       0.9333643913269043,
       0.9222263097763062,
       0.9355919361114502,
       0.9355919361114502,
       0.9333643913269043,
       0.928909182548523,
       0.9177711009979248,
       0.9355919361114502,
       0.9333643913269043,
       0.9333643913269043,
       0.9355919361114502,
       0.9311367273330688,
       0.9177711009979248,
       0.9177711009979248,
       0.9044054746627808,
       0.9066331386566162,
       0.9044054746627808,
       0.9133158922195435,
       0.9110883474349976,
       0.9044054746627808,
       0.9155435562133789,
       0.9133158922195435,
       0.9110883474349976,
       0.9066331386566162,
       0.8954951167106628,
       0.8932675719261169,
       0.8932675719261169,
       0.8954951167106628,
       0.8954951167106628,
       0.8776743412017822,
       0.8754467368125916,
       0.8754467368125916,
       0.870991587638855,
       0.8665363788604736,
       0.8732191920280457,
       0.870991587638855,
       0.8799019455909729,
       0.8799019455909729,
       0.8843570947647095,
       0.8843570947647095,
       0.8754467368125916,
       0.8531706929206848,
       0.8420327305793762,
       0.8286671042442322,
       0.8353499174118042,
       0.8242118954658508,
       0.8442603349685669,
       0.8420327305793762,
       0.8487155437469482,
       0.8464879393577576,
       0.8442603349685669,
       0.8531706929206848,
       0.864308774471283,
       0.8776743412017822,
       0.8799019455909729,
       0.8821294903755188,
       0.8821294903755188,
       0.8620811104774475,
       0.8754467368125916,
       0.8799019455909729,
       0.870991587638855,
       0.8732191920280457,
       0.8754467368125916,
       0.8954951167106628,
       0.8954951167106628,
       0.8888123035430908,
       0.8888123035430908,
       0.8999503254890442,
       0.8977227807044983,
       0.9110883474349976,
       0.9088606834411621,
       0.9199987649917603,
       0.9244539737701416,
       0.9378196001052856,
       0.9400471448898315,
       0.9378196001052856,
       0.9445022940635681,
       0.9266815185546875,
       0.9400471448898315,
       0.9311367273330688,
       0.9222263097763062,
       0.9244539737701416,
       0.9534127116203308,
       0.9333643913269043,
       0.9088606834411621,
       0.9155435562133789,
       0.8910399079322815,
       0.9021779894828796,
       0.9333643913269043,
       0.9445022940635681,
       0.9378196001052856,
       0.9244539737701416,
       1.0180131196975708,
       0.9912819266319275,
       0.9979647994041443,
       0.9935095906257629,
       1.011330246925354,
       1.011330246925354,
       1.053654670715332,
       1.0402891635894775,
       1.0001921653747559,
       0.9823715090751648,
       0.9979647994041443,
       1.0180131196975708,
       1.0402891635894775,
       0.9912819266319275,
       1.011330246925354,
       0.9979647994041443,
       0.9845991730690002,
       0.9890543818473816,
       0.9578679203987122,
       0.9556402564048767,
       0.9734610915184021,
       0.9712335467338562,
       0.9868267178535461,
       1.0848411321640015,
       1.0781583786010742,
       1.0892963409423828,
       1.0603375434875488,
       1.0603375434875488,
       1.0581098794937134,
       1.053654670715332,
       1.0291510820388794,
       1.0269235372543335,
       0.9912819266319275,
       1.0046474933624268,
       1.0135579109191895,
       0.9935095906257629,
       1.0046474933624268,
       1.0068750381469727,
       1.0135579109191895,
       1.0135579109191895,
       1.0358339548110962,
       1.0402891635894775,
       1.0269235372543335,
       1.0135579109191895,
       1.0135579109191895,
       1.0202406644821167,
       1.0269235372543335,
       1.0425167083740234,
       1.0692479610443115,
       1.0581098794937134,
       1.0603375434875488,
       1.0803859233856201,
       1.0826135873794556,
       1.067020297050476,
       1.0692479610443115,
       1.0647927522659302,
       1.0915240049362183,
       1.1182550191879272,
       1.1026619672775269,
       1.0959792137145996,
       1.0781583786010742,
       1.0848411321640015,
       1.0848411321640015,
       1.0781583786010742,
       1.0714755058288574,
       1.0514271259307861,
       1.0737031698226929,
       1.0647927522659302,
       1.0959792137145996,
       1.0647927522659302,
       1.0447443723678589,
       1.0581098794937134,
       0.9935095906257629,
       1.0046474933624268,
       0.9801439642906189,
       0.9890543818473816,
       1.009102702140808,
       1.0313787460327148,
       1.0358339548110962,
       1.038061499595642,
       1.053654670715332,
       1.0269235372543335,
       1.0224683284759521,
       1.053654670715332,
       1.0469719171524048,
       1.0514271259307861,
       1.053654670715332,
       1.0491995811462402,
       1.0447443723678589,
       1.009102702140808,
       1.0291510820388794,
       1.011330246925354,
       1.0180131196975708,
       1.0180131196975708,
       1.0737031698226929,
       1.0514271259307861,
       1.0313787460327148,
       0.9912819266319275,
       0.9645506739616394,
       0.9912819266319275,
       1.009102702140808,
       0.9779163002967834,
       0.9133158922195435,
       0.9355919361114502,
       0.9378196001052856,
       0.9556402564048767,
       0.9712335467338562,
       0.9868267178535461,
       0.9779163002967834,
       0.9645506739616394,
       0.9845991730690002,
       0.9890543818473816,
       0.9801439642906189,
       0.9868267178535461,
       1.0046474933624268,
       1.0202406644821167,
       1.0402891635894775,
       1.011330246925354,
       1.024695873260498,
       1.0046474933624268,
       1.0358339548110962,
       1.0336062908172607,
       1.0447443723678589,
       1.0336062908172607,
       1.038061499595642,
       1.0336062908172607,
       1.0269235372543335,
       0.9779163002967834,
       0.9645506739616394,
       0.8932675719261169,
       0.8509430885314941,
       0.8620811104774475,
       0.8487155437469482,
       0.8910399079322815,
       0.928909182548523,
       0.9044054746627808,
       0.928909182548523,
       0.9422747492790222,
       0.928909182548523,
       0.9333643913269043,
       0.9511850476264954,
       0.9690058827400208,
       0.9489575028419495,
       0.9578679203987122,
       0.9534127116203308,
       0.9511850476264954,
       0.9511850476264954,
       0.9110883474349976,
       0.9333643913269043,
       0.9534127116203308,
       0.9578679203987122,
       1.011330246925354,
       1.009102702140808,
       1.0313787460327148,
       1.0135579109191895,
       1.011330246925354,
       1.0046474933624268,
       0.9957371354103088,
       0.9823715090751648,
       0.9890543818473816,
       1.009102702140808,
       1.009102702140808,
       1.0135579109191895,
       1.0157854557037354,
       1.002331256866455,
       0.9956042766571045,
       1.0090584754943848,
       0.9754230976104736,
       1.0113006830215454,
       1.002331256866455,
       1.0247548818588257,
       1.0269972085952759,
       1.0202702283859253,
       1.0471783876419067,
       1.0382089614868164,
       1.0471783876419067,
       1.0382089614868164,
       1.0180277824401855,
       1.0157854557037354,
       1.0247548818588257,
       1.0269972085952759,
       1.0382089614868164,
       1.0426937341690063,
       1.022512435913086,
       0.9933618903160095,
       1.0180277824401855,
       1.0247548818588257,
       1.0337241888046265,
       1.0359665155410767,
       1.0426937341690063,
       1.0539053678512573,
       1.0561476945877075,
       1.044935941696167,
       1.0583900213241577,
       1.0337241888046265,
       1.0202702283859253,
       0.9619690179824829,
       0.941787838935852,
       0.9507572054862976,
       1.044935941696167,
       1.0920253992080688,
       1.0897830724716187,
       1.1009947061538696,
       1.1705076694488525,
       1.1346300840377808,
       1.1234183311462402,
       1.136872410774231,
       1.1346300840377808,
       1.1054794788360596,
       1.1099642515182495,
       1.12117600440979,
       1.136872410774231,
       1.1009947061538696,
       1.1166912317276,
       1.1032371520996094,
       1.071844220161438,
       1.0830559730529785,
       1.0628747940063477,
       1.0740865468978882,
       1.0875407457351685,
       1.0830559730529785,
       1.1054794788360596,
       1.0808136463165283,
       1.0696018934249878,
       1.071844220161438,
       1.1032371520996094,
       1.078548789024353,
       1.0785712003707886,
       1.022512435913086,
       1.006816029548645,
       1.0247548818588257,
       1.0269972085952759,
       1.0000889301300049,
       0.9933618903160095,
       0.9597266912460327,
       1.0000889301300049,
       0.9866349101066589,
       0.9731808304786682,
       0.9642114043235779,
       0.9440301656723022,
       0.9462724328041077,
       0.9597266912460327,
       0.957484245300293,
       0.9776654243469238,
       0.979907751083374,
       0.9776654243469238,
       0.9619690179824829,
       0.9821501970291138,
       0.9485148787498474,
       0.9126372933387756,
       0.9126372933387756,
       0.9283336997032166,
       0.9350607395172119,
       0.8857290148735046,
       0.8767596483230591,
       0.9036678671836853,
       0.9059101939201355,
       0.9059101939201355,
       0.9216066598892212,
       0.9260913729667664,
       0.9238489866256714,
       0.9238489866256714,
       0.9373030662536621,
       0.9507572054862976,
       0.9507572054862976,
       0.9597266912460327,
       0.9574618339538574,
       0.9619690179824829,
       0.9552419185638428,
       0.9485148787498474,
       0.9462724328041077,
       0.9373030662536621,
       0.9395455121994019,
       0.9485148787498474,
       0.9507572054862976,
       0.9686960577964783,
       0.9686960577964783,
       0.9686960577964783,
       0.9754230976104736,
       0.9866349101066589,
       1.0045737028121948,
       1.0269972085952759,
       1.0359665155410767,
       1.0090584754943848,
       0.9911195635795593,
       1.0202702283859253,
       1.0539053678512573,
       1.0314818620681763,
       1.0382089614868164,
       1.0157854557037354,
       1.006816029548645,
       1.0269972085952759,
       1.006816029548645,
       0.9933618903160095,
       0.9866349101066589,
       1.0180277824401855,
       1.0045737028121948,
       1.0157854557037354,
       1.0516630411148071,
       1.0426937341690063,
       1.0426937341690063,
       1.0180277824401855,
       0.9911195635795593,
       0.9888771176338196,
       0.9866349101066589,
       0.9642114043235779,
       0.9552419185638428,
       0.9709383845329285,
       0.9731808304786682,
       0.9686960577964783,
       0.9686960577964783,
       0.9709383845329285,
       0.979907751083374,
       0.9866349101066589,
       1.0000889301300049,
       1.0090584754943848,
       1.0180277824401855,
       1.0113006830215454,
       1.0202702283859253,
       1.0180277824401855,
       1.0090584754943848,
       1.0247548818588257,
       1.0337241888046265,
       1.0382089614868164,
       1.0292396545410156,
       1.0202702283859253,
       1.0090584754943848,
       1.01352059841156,
       1.0337241888046265,
       1.0359665155410767,
       1.0382089614868164,
       1.0359665155410767,
       1.0404512882232666,
       1.0269972085952759,
       1.0516630411148071,
       1.049420714378357,
       1.049420714378357,
       1.049420714378357,
       1.0583900213241577,
       1.0337241888046265,
       1.0382089614868164,
       1.0269972085952759,
       1.0135430097579956,
       1.0090584754943848,
       0.9933618903160095,
       1.006816029548645,
       0.9888771176338196,
       0.9978466033935547,
       0.9933618903160095,
       1.01352059841156,
       1.0292396545410156,
       1.0404512882232666,
       1.0404512882232666,
       1.0426937341690063,
       1.0471783876419067,
       1.044935941696167,
       1.0382089614868164,
       1.0673595666885376,
       1.076328992843628,
       1.0606324672698975,
       1.0426937341690063,
       1.044935941696167,
       1.022512435913086,
       1.0269972085952759,
       1.0673595666885376,
       1.1009947061538696,
       1.0965100526809692,
       1.094267725944519,
       1.0875407457351685,
       1.0875407457351685,
       1.1054794788360596,
       1.0740865468978882,
       1.0897830724716187,
       1.0606324672698975,
       1.0651172399520874,
       1.0696018934249878,
       1.11444890499115,
       1.1503266096115112,
       1.1794770956039429,
       1.1772347688674927,
       1.1906888484954834,
       1.1660230159759521,
       1.2041429281234741,
       1.224324107170105,
       1.2108701467514038,
       1.181719422340393,
       1.1705076694488525,
       1.1794770956039429,
       1.159295916557312,
       1.1615382432937622,
       1.1503266096115112,
       1.1301454305648804,
       1.11444890499115,
       1.1256606578826904,
       1.1099642515182495,
       1.136872410774231,
       1.1301454305648804,
       1.1323877573013306,
       1.1705076694488525,
       1.1974159479141235,
       1.1705076694488525,
       1.1951736211776733,
       1.1906888484954834,
       1.1839618682861328,
       1.1727501153945923,
       1.1727501153945923,
       1.181719422340393,
       1.1548112630844116,
       1.1323877573013306,
       1.1077219247817993,
       1.071844220161438,
       1.0539053678512573,
       1.0292396545410156,
       1.049420714378357,
       1.0539053678512573,
       1.0583900213241577,
       1.0606324672698975,
       1.0673595666885376,
       1.0628747940063477,
       1.0673595666885376,
       1.071844220161438,
       1.0808136463165283,
       1.0875407457351685,
       1.0808136463165283,
       1.0628747940063477,
       1.0785712003707886,
       1.0830559730529785,
       1.076328992843628,
       1.0875407457351685,
       1.1054794788360596,
       1.1054794788360596,
       1.1189335584640503,
       1.1166912317276,
       1.1279031038284302,
       1.1279031038284302,
       1.12117600440979,
       1.1503266096115112,
       1.1660230159759521,
       1.163780689239502,
       1.1660230159759521,
       1.1727501153945923,
       1.163780689239502,
       1.1548112630844116,
       1.143599510192871,
       1.1323877573013306,
       1.1346300840377808,
       1.1122065782546997,
       1.11444890499115,
       1.1122065782546997,
       1.1189335584640503,
       1.1301454305648804,
       1.1009947061538696,
       1.098752498626709,
       1.0987526178359985,
       1.0829269886016846,
       1.0829269886016846,
       0.997016191482544,
       1.0083203315734863,
       1.0354499816894531,
       1.0105810165405273,
       0.9924945831298828,
       1.001537799835205,
       1.0173635482788086,
       1.0105810165405273,
       1.0128419399261475,
       0.997016191482544,
       0.9947554469108582,
       1.0128419399261475,
       1.0151026248931885,
       1.0060594081878662,
       0.9992770552635193,
       1.0083203315734863,
       1.0128419399261475,
       1.0037987232208252,
       0.997016191482544,
       0.990233838558197,
       1.0037987232208252,
       0.990233838558197,
       0.9789297580718994,
       0.9744081497192383,
       0.9857122302055359,
       0.9495392441749573,
       0.9518001079559326,
       0.9427568912506104,
       0.9495392441749573,
       0.9359744191169739,
       0.9337136745452881,
       0.9359744191169739,
       0.9178879857063293,
       0.9111055731773376,
       0.9111055731773376,
       0.8907582759857178,
       0.9133663177490234,
       0.9201487898826599,
       0.9269312024116516,
       0.9269312024116516,
       0.9382352828979492,
       0.9427568912506104,
       0.940496027469635,
       0.9359744191169739,
       0.9359744191169739,
       0.9540608525276184,
       0.9585824608802795,
       0.9585824608802795,
       0.965364933013916,
       0.9676257967948914,
       0.9766690135002136,
       0.997016191482544,
       0.9947554469108582,
       0.9879729747772217,
       0.9947554469108582,
       0.9947554469108582,
       1.0105810165405273,
       1.001537799835205,
       1.0037987232208252,
       0.9992770552635193,
       0.997016191482544,
       1.0083203315734863,
       0.9879729747772217,
       0.990233838558197
      ],
      "yaxis": "y"
     },
     {
      "hovertemplate": "<br>\u51c0\u503c:%{y:.2f}",
      "mode": "lines",
      "name": "\u7b56\u7565",
      "showlegend": true,
      "type": "scatter",
      "x": [
       "2018-12-27",
       "2018-12-28",
       "2019-01-02",
       "2019-01-03",
       "2019-01-04",
       "2019-01-07",
       "2019-01-08",
       "2019-01-09",
       "2019-01-10",
       "2019-01-11",
       "2019-01-14",
       "2019-01-15",
       "2019-01-16",
       "2019-01-17",
       "2019-01-18",
       "2019-01-21",
       "2019-01-22",
       "2019-01-23",
       "2019-01-24",
       "2019-01-25",
       "2019-01-28",
       "2019-01-29",
       "2019-01-30",
       "2019-01-31",
       "2019-02-01",
       "2019-02-11",
       "2019-02-12",
       "2019-02-13",
       "2019-02-14",
       "2019-02-15",
       "2019-02-18",
       "2019-02-19",
       "2019-02-20",
       "2019-02-21",
       "2019-02-22",
       "2019-02-25",
       "2019-02-26",
       "2019-02-27",
       "2019-02-28",
       "2019-03-01",
       "2019-03-04",
       "2019-03-05",
       "2019-03-06",
       "2019-03-07",
       "2019-03-08",
       "2019-03-11",
       "2019-03-12",
       "2019-03-13",
       "2019-03-14",
       "2019-03-15",
       "2019-03-18",
       "2019-03-19",
       "2019-03-20",
       "2019-03-21",
       "2019-03-22",
       "2019-03-25",
       "2019-03-26",
       "2019-03-27",
       "2019-03-28",
       "2019-03-29",
       "2019-04-01",
       "2019-04-02",
       "2019-04-03",
       "2019-04-04",
       "2019-04-08",
       "2019-04-09",
       "2019-04-10",
       "2019-04-11",
       "2019-04-12",
       "2019-04-15",
       "2019-04-16",
       "2019-04-17",
       "2019-04-18",
       "2019-04-19",
       "2019-04-22",
       "2019-04-23",
       "2019-04-24",
       "2019-04-25",
       "2019-04-26",
       "2019-04-29",
       "2019-04-30",
       "2019-05-06",
       "2019-05-07",
       "2019-05-08",
       "2019-05-09",
       "2019-05-10",
       "2019-05-13",
       "2019-05-14",
       "2019-05-15",
       "2019-05-16",
       "2019-05-17",
       "2019-05-20",
       "2019-05-21",
       "2019-05-22",
       "2019-05-23",
       "2019-05-24",
       "2019-05-27",
       "2019-05-28",
       "2019-05-29",
       "2019-05-30",
       "2019-05-31",
       "2019-06-03",
       "2019-06-04",
       "2019-06-05",
       "2019-06-06",
       "2019-06-10",
       "2019-06-11",
       "2019-06-12",
       "2019-06-13",
       "2019-06-14",
       "2019-06-17",
       "2019-06-18",
       "2019-06-19",
       "2019-06-20",
       "2019-06-21",
       "2019-06-24",
       "2019-06-25",
       "2019-06-26",
       "2019-06-27",
       "2019-06-28",
       "2019-07-01",
       "2019-07-02",
       "2019-07-03",
       "2019-07-04",
       "2019-07-05",
       "2019-07-08",
       "2019-07-09",
       "2019-07-10",
       "2019-07-11",
       "2019-07-12",
       "2019-07-15",
       "2019-07-16",
       "2019-07-17",
       "2019-07-18",
       "2019-07-19",
       "2019-07-22",
       "2019-07-23",
       "2019-07-24",
       "2019-07-25",
       "2019-07-26",
       "2019-07-29",
       "2019-07-30",
       "2019-07-31",
       "2019-08-01",
       "2019-08-02",
       "2019-08-05",
       "2019-08-06",
       "2019-08-07",
       "2019-08-08",
       "2019-08-09",
       "2019-08-12",
       "2019-08-13",
       "2019-08-14",
       "2019-08-15",
       "2019-08-16",
       "2019-08-19",
       "2019-08-20",
       "2019-08-21",
       "2019-08-22",
       "2019-08-23",
       "2019-08-26",
       "2019-08-27",
       "2019-08-28",
       "2019-08-29",
       "2019-08-30",
       "2019-09-02",
       "2019-09-03",
       "2019-09-04",
       "2019-09-05",
       "2019-09-06",
       "2019-09-09",
       "2019-09-10",
       "2019-09-11",
       "2019-09-12",
       "2019-09-16",
       "2019-09-17",
       "2019-09-18",
       "2019-09-19",
       "2019-09-20",
       "2019-09-23",
       "2019-09-24",
       "2019-09-25",
       "2019-09-26",
       "2019-09-27",
       "2019-09-30",
       "2019-10-08",
       "2019-10-09",
       "2019-10-10",
       "2019-10-11",
       "2019-10-14",
       "2019-10-15",
       "2019-10-16",
       "2019-10-17",
       "2019-10-18",
       "2019-10-21",
       "2019-10-22",
       "2019-10-23",
       "2019-10-24",
       "2019-10-25",
       "2019-10-28",
       "2019-10-29",
       "2019-10-30",
       "2019-10-31",
       "2019-11-01",
       "2019-11-04",
       "2019-11-05",
       "2019-11-06",
       "2019-11-07",
       "2019-11-08",
       "2019-11-11",
       "2019-11-12",
       "2019-11-13",
       "2019-11-14",
       "2019-11-15",
       "2019-11-18",
       "2019-11-19",
       "2019-11-20",
       "2019-11-21",
       "2019-11-22",
       "2019-11-25",
       "2019-11-26",
       "2019-11-27",
       "2019-11-28",
       "2019-11-29",
       "2019-12-02",
       "2019-12-03",
       "2019-12-04",
       "2019-12-05",
       "2019-12-06",
       "2019-12-09",
       "2019-12-10",
       "2019-12-11",
       "2019-12-12",
       "2019-12-13",
       "2019-12-16",
       "2019-12-17",
       "2019-12-18",
       "2019-12-19",
       "2019-12-20",
       "2019-12-23",
       "2019-12-24",
       "2019-12-25",
       "2019-12-26",
       "2019-12-27",
       "2019-12-30",
       "2019-12-31",
       "2020-01-02",
       "2020-01-03",
       "2020-01-06",
       "2020-01-07",
       "2020-01-08",
       "2020-01-09",
       "2020-01-10",
       "2020-01-13",
       "2020-01-14",
       "2020-01-15",
       "2020-01-16",
       "2020-01-17",
       "2020-01-20",
       "2020-01-21",
       "2020-01-22",
       "2020-01-23",
       "2020-02-03",
       "2020-02-04",
       "2020-02-05",
       "2020-02-06",
       "2020-02-07",
       "2020-02-10",
       "2020-02-11",
       "2020-02-12",
       "2020-02-13",
       "2020-02-14",
       "2020-02-17",
       "2020-02-18",
       "2020-02-19",
       "2020-02-20",
       "2020-02-21",
       "2020-02-24",
       "2020-02-25",
       "2020-02-26",
       "2020-02-27",
       "2020-02-28",
       "2020-03-02",
       "2020-03-03",
       "2020-03-04",
       "2020-03-05",
       "2020-03-06",
       "2020-03-09",
       "2020-03-10",
       "2020-03-11",
       "2020-03-12",
       "2020-03-13",
       "2020-03-16",
       "2020-03-17",
       "2020-03-18",
       "2020-03-19",
       "2020-03-20",
       "2020-03-23",
       "2020-03-24",
       "2020-03-25",
       "2020-03-26",
       "2020-03-27",
       "2020-03-30",
       "2020-03-31",
       "2020-04-01",
       "2020-04-02",
       "2020-04-03",
       "2020-04-07",
       "2020-04-08",
       "2020-04-09",
       "2020-04-10",
       "2020-04-13",
       "2020-04-14",
       "2020-04-15",
       "2020-04-16",
       "2020-04-17",
       "2020-04-20",
       "2020-04-21",
       "2020-04-22",
       "2020-04-23",
       "2020-04-24",
       "2020-04-27",
       "2020-04-28",
       "2020-04-29",
       "2020-04-30",
       "2020-05-06",
       "2020-05-07",
       "2020-05-08",
       "2020-05-11",
       "2020-05-12",
       "2020-05-13",
       "2020-05-14",
       "2020-05-15",
       "2020-05-18",
       "2020-05-19",
       "2020-05-20",
       "2020-05-21",
       "2020-05-22",
       "2020-05-25",
       "2020-05-26",
       "2020-05-27",
       "2020-05-28",
       "2020-05-29",
       "2020-06-01",
       "2020-06-02",
       "2020-06-03",
       "2020-06-04",
       "2020-06-05",
       "2020-06-08",
       "2020-06-09",
       "2020-06-10",
       "2020-06-11",
       "2020-06-12",
       "2020-06-15",
       "2020-06-16",
       "2020-06-17",
       "2020-06-18",
       "2020-06-19",
       "2020-06-22",
       "2020-06-23",
       "2020-06-24",
       "2020-06-29",
       "2020-06-30",
       "2020-07-01",
       "2020-07-02",
       "2020-07-03",
       "2020-07-06",
       "2020-07-07",
       "2020-07-08",
       "2020-07-09",
       "2020-07-10",
       "2020-07-13",
       "2020-07-14",
       "2020-07-15",
       "2020-07-16",
       "2020-07-17",
       "2020-07-20",
       "2020-07-21",
       "2020-07-22",
       "2020-07-23",
       "2020-07-24",
       "2020-07-27",
       "2020-07-28",
       "2020-07-29",
       "2020-07-30",
       "2020-07-31",
       "2020-08-03",
       "2020-08-04",
       "2020-08-05",
       "2020-08-06",
       "2020-08-07",
       "2020-08-10",
       "2020-08-11",
       "2020-08-12",
       "2020-08-13",
       "2020-08-14",
       "2020-08-17",
       "2020-08-18",
       "2020-08-19",
       "2020-08-20",
       "2020-08-21",
       "2020-08-24",
       "2020-08-25",
       "2020-08-26",
       "2020-08-27",
       "2020-08-28",
       "2020-08-31",
       "2020-09-01",
       "2020-09-02",
       "2020-09-03",
       "2020-09-04",
       "2020-09-07",
       "2020-09-08",
       "2020-09-09",
       "2020-09-10",
       "2020-09-11",
       "2020-09-14",
       "2020-09-15",
       "2020-09-16",
       "2020-09-17",
       "2020-09-18",
       "2020-09-21",
       "2020-09-22",
       "2020-09-23",
       "2020-09-24",
       "2020-09-25",
       "2020-09-28",
       "2020-09-29",
       "2020-09-30",
       "2020-10-09",
       "2020-10-12",
       "2020-10-13",
       "2020-10-14",
       "2020-10-15",
       "2020-10-16",
       "2020-10-19",
       "2020-10-20",
       "2020-10-21",
       "2020-10-22",
       "2020-10-23",
       "2020-10-26",
       "2020-10-27",
       "2020-10-28",
       "2020-10-29",
       "2020-10-30",
       "2020-11-02",
       "2020-11-03",
       "2020-11-04",
       "2020-11-05",
       "2020-11-06",
       "2020-11-09",
       "2020-11-10",
       "2020-11-11",
       "2020-11-12",
       "2020-11-13",
       "2020-11-16",
       "2020-11-17",
       "2020-11-18",
       "2020-11-19",
       "2020-11-20",
       "2020-11-23",
       "2020-11-24",
       "2020-11-25",
       "2020-11-26",
       "2020-11-27",
       "2020-11-30",
       "2020-12-01",
       "2020-12-02",
       "2020-12-03",
       "2020-12-04",
       "2020-12-07",
       "2020-12-08",
       "2020-12-09",
       "2020-12-10",
       "2020-12-11",
       "2020-12-14",
       "2020-12-15",
       "2020-12-16",
       "2020-12-17",
       "2020-12-18",
       "2020-12-21",
       "2020-12-22",
       "2020-12-23",
       "2020-12-24",
       "2020-12-25",
       "2020-12-28",
       "2020-12-29",
       "2020-12-30",
       "2020-12-31",
       "2021-01-04",
       "2021-01-05",
       "2021-01-06",
       "2021-01-07",
       "2021-01-08",
       "2021-01-11",
       "2021-01-12",
       "2021-01-13",
       "2021-01-14",
       "2021-01-15",
       "2021-01-18",
       "2021-01-19",
       "2021-01-20",
       "2021-01-21",
       "2021-01-22",
       "2021-01-25",
       "2021-01-26",
       "2021-01-27",
       "2021-01-28",
       "2021-01-29",
       "2021-02-01",
       "2021-02-02",
       "2021-02-03",
       "2021-02-04",
       "2021-02-05",
       "2021-02-08",
       "2021-02-09",
       "2021-02-10",
       "2021-02-18",
       "2021-02-19",
       "2021-02-22",
       "2021-02-23",
       "2021-02-24",
       "2021-02-25",
       "2021-02-26",
       "2021-03-01",
       "2021-03-02",
       "2021-03-03",
       "2021-03-04",
       "2021-03-05",
       "2021-03-08",
       "2021-03-09",
       "2021-03-10",
       "2021-03-11",
       "2021-03-12",
       "2021-03-15",
       "2021-03-16",
       "2021-03-17",
       "2021-03-18",
       "2021-03-19",
       "2021-03-22",
       "2021-03-23",
       "2021-03-24",
       "2021-03-25",
       "2021-03-26",
       "2021-03-29",
       "2021-03-30",
       "2021-03-31",
       "2021-04-01",
       "2021-04-02",
       "2021-04-06",
       "2021-04-07",
       "2021-04-08",
       "2021-04-09",
       "2021-04-12",
       "2021-04-13",
       "2021-04-14",
       "2021-04-15",
       "2021-04-16",
       "2021-04-19",
       "2021-04-20",
       "2021-04-21",
       "2021-04-22",
       "2021-04-23",
       "2021-04-26",
       "2021-04-27",
       "2021-04-28",
       "2021-04-29",
       "2021-04-30",
       "2021-05-06",
       "2021-05-07",
       "2021-05-10",
       "2021-05-11",
       "2021-05-12",
       "2021-05-13",
       "2021-05-14",
       "2021-05-17",
       "2021-05-18",
       "2021-05-19",
       "2021-05-20",
       "2021-05-21",
       "2021-05-24",
       "2021-05-25",
       "2021-05-26",
       "2021-05-27",
       "2021-05-28",
       "2021-05-31",
       "2021-06-01",
       "2021-06-02",
       "2021-06-03",
       "2021-06-04",
       "2021-06-07",
       "2021-06-08",
       "2021-06-09",
       "2021-06-10",
       "2021-06-11",
       "2021-06-15",
       "2021-06-16",
       "2021-06-17",
       "2021-06-18",
       "2021-06-21",
       "2021-06-22",
       "2021-06-23",
       "2021-06-24",
       "2021-06-25",
       "2021-06-28",
       "2021-06-29",
       "2021-06-30",
       "2021-07-01",
       "2021-07-02",
       "2021-07-05",
       "2021-07-06",
       "2021-07-07",
       "2021-07-08",
       "2021-07-09",
       "2021-07-12",
       "2021-07-13",
       "2021-07-14",
       "2021-07-15",
       "2021-07-16",
       "2021-07-19",
       "2021-07-20",
       "2021-07-21",
       "2021-07-22",
       "2021-07-23",
       "2021-07-26",
       "2021-07-27",
       "2021-07-28",
       "2021-07-29",
       "2021-07-30",
       "2021-08-02",
       "2021-08-03",
       "2021-08-04",
       "2021-08-05",
       "2021-08-06",
       "2021-08-09",
       "2021-08-10",
       "2021-08-11",
       "2021-08-12",
       "2021-08-13",
       "2021-08-16",
       "2021-08-17",
       "2021-08-18",
       "2021-08-19",
       "2021-08-20",
       "2021-08-23",
       "2021-08-24",
       "2021-08-25",
       "2021-08-26",
       "2021-08-27",
       "2021-08-30",
       "2021-08-31",
       "2021-09-01",
       "2021-09-02",
       "2021-09-03",
       "2021-09-06",
       "2021-09-07",
       "2021-09-08",
       "2021-09-09",
       "2021-09-10",
       "2021-09-13",
       "2021-09-14",
       "2021-09-15",
       "2021-09-16",
       "2021-09-17",
       "2021-09-22",
       "2021-09-23",
       "2021-09-24",
       "2021-09-27",
       "2021-09-28",
       "2021-09-29",
       "2021-09-30",
       "2021-10-08",
       "2021-10-11",
       "2021-10-12",
       "2021-10-13",
       "2021-10-14",
       "2021-10-15",
       "2021-10-18",
       "2021-10-19",
       "2021-10-20",
       "2021-10-21",
       "2021-10-22",
       "2021-10-25",
       "2021-10-26",
       "2021-10-27",
       "2021-10-28",
       "2021-10-29",
       "2021-11-01",
       "2021-11-02",
       "2021-11-03",
       "2021-11-04",
       "2021-11-05",
       "2021-11-08",
       "2021-11-09",
       "2021-11-10",
       "2021-11-11",
       "2021-11-12",
       "2021-11-15",
       "2021-11-16",
       "2021-11-17",
       "2021-11-18",
       "2021-11-19",
       "2021-11-22",
       "2021-11-23",
       "2021-11-24",
       "2021-11-25",
       "2021-11-26",
       "2021-11-29",
       "2021-11-30",
       "2021-12-01",
       "2021-12-02",
       "2021-12-03",
       "2021-12-06",
       "2021-12-07",
       "2021-12-08",
       "2021-12-09",
       "2021-12-10",
       "2021-12-13",
       "2021-12-14",
       "2021-12-15",
       "2021-12-16",
       "2021-12-17",
       "2021-12-20",
       "2021-12-21",
       "2021-12-22",
       "2021-12-23",
       "2021-12-24",
       "2021-12-27",
       "2021-12-28",
       "2021-12-29",
       "2021-12-30",
       "2021-12-31",
       "2022-01-04",
       "2022-01-05",
       "2022-01-06",
       "2022-01-07",
       "2022-01-10",
       "2022-01-11",
       "2022-01-12",
       "2022-01-13",
       "2022-01-14",
       "2022-01-17",
       "2022-01-18",
       "2022-01-19",
       "2022-01-20",
       "2022-01-21",
       "2022-01-24",
       "2022-01-25",
       "2022-01-26",
       "2022-01-27",
       "2022-01-28",
       "2022-02-07",
       "2022-02-08",
       "2022-02-09",
       "2022-02-10",
       "2022-02-11",
       "2022-02-14",
       "2022-02-15",
       "2022-02-16",
       "2022-02-17",
       "2022-02-18",
       "2022-02-21",
       "2022-02-22",
       "2022-02-23",
       "2022-02-24",
       "2022-02-25",
       "2022-02-28",
       "2022-03-01",
       "2022-03-02",
       "2022-03-03",
       "2022-03-04",
       "2022-03-07",
       "2022-03-08",
       "2022-03-09",
       "2022-03-10",
       "2022-03-11",
       "2022-03-14",
       "2022-03-15",
       "2022-03-16",
       "2022-03-17",
       "2022-03-18",
       "2022-03-21",
       "2022-03-22",
       "2022-03-23",
       "2022-03-24",
       "2022-03-25",
       "2022-03-28",
       "2022-03-29",
       "2022-03-30",
       "2022-03-31",
       "2022-04-01",
       "2022-04-06",
       "2022-04-07",
       "2022-04-08",
       "2022-04-11",
       "2022-04-12",
       "2022-04-13",
       "2022-04-14",
       "2022-04-15",
       "2022-04-18",
       "2022-04-19",
       "2022-04-20",
       "2022-04-21",
       "2022-04-22",
       "2022-04-25",
       "2022-04-26",
       "2022-04-27",
       "2022-04-28",
       "2022-04-29",
       "2022-05-05",
       "2022-05-06",
       "2022-05-09",
       "2022-05-10",
       "2022-05-11",
       "2022-05-12",
       "2022-05-13",
       "2022-05-16",
       "2022-05-17",
       "2022-05-18",
       "2022-05-19",
       "2022-05-20",
       "2022-05-23",
       "2022-05-24",
       "2022-05-25",
       "2022-05-26",
       "2022-05-27",
       "2022-05-30",
       "2022-05-31",
       "2022-06-01",
       "2022-06-02",
       "2022-06-06",
       "2022-06-07",
       "2022-06-08",
       "2022-06-09",
       "2022-06-10",
       "2022-06-13",
       "2022-06-14",
       "2022-06-15",
       "2022-06-16",
       "2022-06-17",
       "2022-06-20",
       "2022-06-21",
       "2022-06-22",
       "2022-06-23",
       "2022-06-24",
       "2022-06-27",
       "2022-06-28",
       "2022-06-29",
       "2022-06-30",
       "2022-07-01",
       "2022-07-04",
       "2022-07-05",
       "2022-07-06",
       "2022-07-07",
       "2022-07-08",
       "2022-07-11",
       "2022-07-12",
       "2022-07-13",
       "2022-07-14",
       "2022-07-15",
       "2022-07-18",
       "2022-07-19",
       "2022-07-20",
       "2022-07-21",
       "2022-07-22",
       "2022-07-25",
       "2022-07-26",
       "2022-07-27",
       "2022-07-28",
       "2022-07-29",
       "2022-08-01",
       "2022-08-02",
       "2022-08-03",
       "2022-08-04",
       "2022-08-05",
       "2022-08-08",
       "2022-08-09",
       "2022-08-10",
       "2022-08-11",
       "2022-08-12",
       "2022-08-15",
       "2022-08-16",
       "2022-08-17",
       "2022-08-18",
       "2022-08-19",
       "2022-08-22",
       "2022-08-23",
       "2022-08-24",
       "2022-08-25",
       "2022-08-26",
       "2022-08-29",
       "2022-08-30",
       "2022-08-31",
       "2022-09-01",
       "2022-09-02",
       "2022-09-05",
       "2022-09-06",
       "2022-09-07",
       "2022-09-08",
       "2022-09-09",
       "2022-09-13",
       "2022-09-14",
       "2022-09-15",
       "2022-09-16",
       "2022-09-19",
       "2022-09-20",
       "2022-09-21",
       "2022-09-22",
       "2022-09-23",
       "2022-09-26",
       "2022-09-27",
       "2022-09-28",
       "2022-09-29",
       "2022-09-30",
       "2022-10-10",
       "2022-10-11",
       "2022-10-12",
       "2022-10-13",
       "2022-10-14",
       "2022-10-17",
       "2022-10-18",
       "2022-10-19",
       "2022-10-20",
       "2022-10-21",
       "2022-10-24",
       "2022-10-25",
       "2022-10-26",
       "2022-10-27",
       "2022-10-28",
       "2022-10-31",
       "2022-11-01",
       "2022-11-02",
       "2022-11-03",
       "2022-11-04",
       "2022-11-07",
       "2022-11-08",
       "2022-11-09",
       "2022-11-10",
       "2022-11-11",
       "2022-11-14",
       "2022-11-15",
       "2022-11-16",
       "2022-11-17",
       "2022-11-18",
       "2022-11-21",
       "2022-11-22",
       "2022-11-23",
       "2022-11-24",
       "2022-11-25",
       "2022-11-28",
       "2022-11-29",
       "2022-11-30",
       "2022-12-01",
       "2022-12-02",
       "2022-12-05",
       "2022-12-06",
       "2022-12-07",
       "2022-12-08",
       "2022-12-09",
       "2022-12-12",
       "2022-12-13",
       "2022-12-14",
       "2022-12-15",
       "2022-12-16",
       "2022-12-19",
       "2022-12-20",
       "2022-12-21",
       "2022-12-22",
       "2022-12-23",
       "2022-12-26",
       "2022-12-27",
       "2022-12-28",
       "2022-12-29",
       "2022-12-30",
       "2023-01-03",
       "2023-01-04",
       "2023-01-05",
       "2023-01-06",
       "2023-01-09",
       "2023-01-10",
       "2023-01-11",
       "2023-01-12",
       "2023-01-13",
       "2023-01-16",
       "2023-01-17",
       "2023-01-18",
       "2023-01-19",
       "2023-01-20",
       "2023-01-30",
       "2023-01-31",
       "2023-02-01",
       "2023-02-02",
       "2023-02-03",
       "2023-02-06",
       "2023-02-07",
       "2023-02-08",
       "2023-02-09",
       "2023-02-10",
       "2023-02-13",
       "2023-02-14",
       "2023-02-15",
       "2023-02-16",
       "2023-02-17",
       "2023-02-20",
       "2023-02-21",
       "2023-02-22",
       "2023-02-23",
       "2023-02-24",
       "2023-02-27",
       "2023-02-28",
       "2023-03-01",
       "2023-03-02",
       "2023-03-03",
       "2023-03-06",
       "2023-03-07",
       "2023-03-08",
       "2023-03-09",
       "2023-03-10",
       "2023-03-13",
       "2023-03-14",
       "2023-03-15",
       "2023-03-16",
       "2023-03-17",
       "2023-03-20",
       "2023-03-21",
       "2023-03-22",
       "2023-03-23",
       "2023-03-24",
       "2023-03-27",
       "2023-03-28",
       "2023-03-29",
       "2023-03-30",
       "2023-03-31",
       "2023-04-03",
       "2023-04-04",
       "2023-04-06",
       "2023-04-07",
       "2023-04-10",
       "2023-04-11",
       "2023-04-12",
       "2023-04-13",
       "2023-04-14",
       "2023-04-17",
       "2023-04-18",
       "2023-04-19",
       "2023-04-20",
       "2023-04-21",
       "2023-04-24",
       "2023-04-25",
       "2023-04-26",
       "2023-04-27",
       "2023-04-28",
       "2023-05-04",
       "2023-05-05",
       "2023-05-08",
       "2023-05-09",
       "2023-05-10",
       "2023-05-11",
       "2023-05-12",
       "2023-05-15",
       "2023-05-16",
       "2023-05-17",
       "2023-05-18",
       "2023-05-19",
       "2023-05-22",
       "2023-05-23",
       "2023-05-24",
       "2023-05-25",
       "2023-05-26",
       "2023-05-29",
       "2023-05-30",
       "2023-05-31",
       "2023-06-01",
       "2023-06-02",
       "2023-06-05",
       "2023-06-06",
       "2023-06-07",
       "2023-06-08",
       "2023-06-09",
       "2023-06-12",
       "2023-06-13",
       "2023-06-14",
       "2023-06-15",
       "2023-06-16",
       "2023-06-19",
       "2023-06-20",
       "2023-06-21",
       "2023-06-26",
       "2023-06-27",
       "2023-06-28",
       "2023-06-29",
       "2023-06-30",
       "2023-07-03",
       "2023-07-04",
       "2023-07-05",
       "2023-07-06",
       "2023-07-07",
       "2023-07-10",
       "2023-07-11",
       "2023-07-12",
       "2023-07-13",
       "2023-07-14",
       "2023-07-17",
       "2023-07-18",
       "2023-07-19",
       "2023-07-20",
       "2023-07-21",
       "2023-07-24",
       "2023-07-25",
       "2023-07-26",
       "2023-07-27",
       "2023-07-28",
       "2023-07-31",
       "2023-08-01",
       "2023-08-02",
       "2023-08-03",
       "2023-08-04",
       "2023-08-07",
       "2023-08-08",
       "2023-08-09",
       "2023-08-10",
       "2023-08-11",
       "2023-08-14",
       "2023-08-15",
       "2023-08-16",
       "2023-08-17",
       "2023-08-18",
       "2023-08-21",
       "2023-08-22",
       "2023-08-23",
       "2023-08-24",
       "2023-08-25",
       "2023-08-28",
       "2023-08-29",
       "2023-08-30",
       "2023-08-31",
       "2023-09-01",
       "2023-09-04",
       "2023-09-05",
       "2023-09-06",
       "2023-09-07",
       "2023-09-08",
       "2023-09-11",
       "2023-09-12",
       "2023-09-13",
       "2023-09-14",
       "2023-09-15",
       "2023-09-18",
       "2023-09-19",
       "2023-09-20",
       "2023-09-21",
       "2023-09-22",
       "2023-09-25",
       "2023-09-26",
       "2023-09-27",
       "2023-09-28",
       "2023-10-09",
       "2023-10-10",
       "2023-10-11",
       "2023-10-12",
       "2023-10-13",
       "2023-10-16",
       "2023-10-17",
       "2023-10-18",
       "2023-10-19",
       "2023-10-20",
       "2023-10-23",
       "2023-10-24",
       "2023-10-25",
       "2023-10-26",
       "2023-10-27",
       "2023-10-30",
       "2023-10-31",
       "2023-11-01",
       "2023-11-02",
       "2023-11-03",
       "2023-11-06",
       "2023-11-07",
       "2023-11-08",
       "2023-11-09",
       "2023-11-10",
       "2023-11-13",
       "2023-11-14",
       "2023-11-15",
       "2023-11-16",
       "2023-11-17",
       "2023-11-20",
       "2023-11-21",
       "2023-11-22",
       "2023-11-23",
       "2023-11-24",
       "2023-11-27",
       "2023-11-28",
       "2023-11-29",
       "2023-11-30"
      ],
      "xaxis": "x",
      "y": [
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       1,
       0.9978685514154816,
       0.9878935514154816,
       0.9878935514154816,
       0.9619585514154816,
       0.9539785514154816,
       0.9440035514154816,
       0.9619585514154816,
       0.9779185514154816,
       0.9938785514154816,
       1.0058485514154816,
       1.0098385514154817,
       1.0098385514154817,
       1.0377685514154815,
       1.0357735514154816,
       1.0357735514154816,
       1.0277935514154817,
       1.0537285514154815,
       1.1075935514154815,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1154197147793579,
       1.1320532897787476,
       1.1215332897787476,
       1.1089092897787476,
       1.1320532897787476,
       1.1236372897787477,
       1.1236372897787477,
       1.1362612897787476,
       1.1404692897787476,
       1.0962852897787476,
       1.0878692897787476,
       1.1004932897787476,
       1.0899732897787475,
       1.0752452897787477,
       1.0752452897787477,
       1.0962852897787476,
       1.0983892897787475,
       1.0878692897787476,
       1.0794532897787477,
       1.0752452897787477,
       1.0668292897787477,
       1.0542052897787475,
       1.0542052897787475,
       1.0373732897787475,
       1.0478932897787476,
       1.0752452897787477,
       1.0689332897787476,
       1.0752452897787477,
       1.0710372897787477,
       1.0689332897787476,
       1.0794532897787477,
       1.0836612897787476,
       1.1004932897787476,
       1.1051376238865662,
       1.1051021123051452,
       1.1050843565144348,
       1.1050754786190795,
       1.1050932344097901,
       1.1050310891423034,
       1.1051109902005005,
       1.1050932344097901,
       1.1050577228283691,
       1.1050843565144348,
       1.1050932344097901,
       1.1049067986073302,
       1.1048979207119751,
       1.1048712870259094,
       1.1048801649212647,
       1.1048979207119751,
       1.1049866996655273,
       1.1049866996655273,
       1.1049600659794616,
       1.1049423101887512,
       1.1049511880841065,
       1.1048801649212647,
       1.1048890428166198,
       1.1049423101887512,
       1.1049245543980408,
       1.1049067986073302,
       1.1048979207119751,
       1.1049423101887512,
       1.1049245543980408,
       1.1048801649212647,
       1.1047913859677123,
       1.1047381185955811,
       1.1046049501652526,
       1.0985140666142275,
       1.1033158224049378,
       1.093712310823517,
       1.105716700300293,
       1.1009149445095825,
       1.105716700300293,
       1.1033158224049378,
       1.105716700300293,
       1.136928112939911,
       1.136928112939911,
       1.1393289908352662,
       1.136928112939911,
       1.1345272350445557,
       1.1201219676724243,
       1.1441307466259767,
       1.146531624521332,
       1.146531624521332,
       1.1393289908352662,
       1.1561351361027528,
       1.1537342582073975,
       1.1609368918934633,
       1.1633377697888185,
       1.1633377697888185,
       1.1801439150563051,
       1.1801439150563051,
       1.1777430371609499,
       1.1825447929516602,
       1.1801439150563051,
       1.1537342582073975,
       1.1513333803120422,
       1.1537342582073975,
       1.158536013998108,
       1.1393289908352662,
       1.1417298687306214,
       1.1225228455677796,
       1.105716700300293,
       1.115320211881714,
       1.1009149445095825,
       1.1129193339863588,
       1.1201219676724243,
       1.1249237234631349,
       1.1321263571492006,
       1.1513333803120422,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1535652485265875,
       1.1578906351439429,
       1.1502016351439426,
       1.1450756351439428,
       1.134823635143943,
       1.137386635143943,
       1.1502016351439426,
       1.137386635143943,
       1.1425126351439432,
       1.1399496351439427,
       1.160453635143943,
       1.155327635143943,
       1.160453635143943,
       1.1527646351439431,
       1.160453635143943,
       1.1578906351439429,
       1.1578906351439429,
       1.1527646351439431,
       1.1630166351439428,
       1.1707056351439429,
       1.1707056351439429,
       1.1707056351439429,
       1.165579635143943,
       1.1578906351439429,
       1.173268635143943,
       1.178394635143943,
       1.191209635143943,
       1.1886466351439429,
       1.1937726351439428,
       1.1886466351439429,
       1.168142635143943,
       1.1758316351439428,
       1.1707056351439429,
       1.183520635143943,
       1.2014616351439429,
       1.2373436351439429,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2269240493636846,
       1.2534296754548406,
       1.2652816754548406,
       1.2860226754548405,
       1.2860226754548405,
       1.3067636754548406,
       1.2949116754548404,
       1.3097266754548404,
       1.2919486754548406,
       1.2889856754548406,
       1.3126896754548405,
       1.3245416754548405,
       1.3156526754548405,
       1.3393566754548405,
       1.3393566754548405,
       1.3393566754548405,
       1.3275046754548405,
       1.3423196754548403,
       1.3423196754548403,
       1.2860226754548405,
       1.3245416754548405,
       1.3245416754548405,
       1.3838016754548406,
       1.3808386754548405,
       1.4223206754548405,
       1.3571346754548406,
       1.3808386754548405,
       1.3660236754548405,
       1.3393566754548405,
       1.3215786754548404,
       1.2978746754548405,
       1.2978746754548405,
       1.2741706754548405,
       1.3038006754548406,
       1.3097266754548404,
       1.2771336754548406,
       1.2949116754548404,
       1.3156526754548405,
       1.3038006754548406,
       1.3038006754548406,
       1.2860226754548405,
       1.2800966754548406,
       1.2741706754548405,
       1.2889856754548406,
       1.2830596754548405,
       1.2978746754548405,
       1.3067636754548406,
       1.3008376754548405,
       1.2830596754548405,
       1.2800966754548406,
       1.2889856754548406,
       1.2771336754548406,
       1.2771336754548406,
       1.2741706754548405,
       1.2978746754548405,
       1.2919486754548406,
       1.3215786754548404,
       1.3304676754548406,
       1.3186156754548406,
       1.3304676754548406,
       1.2949116754548404,
       1.2978746754548405,
       1.3126896754548405,
       1.3245416754548405,
       1.3186156754548406,
       1.3245416754548405,
       1.3215786754548404,
       1.3126896754548405,
       1.3215786754548404,
       1.3067636754548406,
       1.3067636754548406,
       1.2978746754548405,
       1.3067636754548406,
       1.2949116754548404,
       1.2949116754548404,
       1.2741706754548405,
       1.2774750968357609,
       1.2941000968357608,
       1.2974250968357608,
       1.2974250968357608,
       1.320700096835761,
       1.3705750968357608,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.3903185724422213,
       1.37502296671926,
       1.36327096671926,
       1.37796096671926,
       1.36327096671926,
       1.38971296671926,
       1.41321696671926,
       1.40734096671926,
       1.39852696671926,
       1.41027896671926,
       1.40146496671926,
       1.40440296671926,
       1.4161549667192599,
       1.39265096671926,
       1.38383696671926,
       1.37796096671926,
       1.3691469667192602,
       1.36620896671926,
       1.34564296671926,
       1.3341499993514012,
       1.292367999351401,
       1.2923662092941806,
       1.3183182092941803,
       1.3118302092941807,
       1.3248062092941806,
       1.3215622092941806,
       1.3442702092941807,
       1.3377822092941807,
       1.3312942092941806,
       1.3345382092941807,
       1.3215622092941806,
       1.3475142092941805,
       1.3475142092941805,
       1.3540022092941806,
       1.3540022092941806,
       1.3507582092941806,
       1.3669782092941807,
       1.3604902092941809,
       1.3572462092941806,
       1.3507582092941806,
       1.3604902092941809,
       1.3604902092941809,
       1.373466209294181,
       1.3799542092941806,
       1.3767102092941805,
       1.3572462092941806,
       1.3572462092941806,
       1.3507582092941806,
       1.3183182092941803,
       1.3183182092941803,
       1.3020982092941806,
       1.3150742092941807,
       1.3150742092941807,
       1.3215622092941806,
       1.3183182092941803,
       1.3118302092941807,
       1.3215622092941806,
       1.2956102092941806,
       1.2988542092941806,
       1.2631702092941806,
       1.2826342092941807,
       1.3248062092941806,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.3175297353860231,
       1.2999218346221681,
       1.2999218346221681,
       1.302901834622168,
       1.302901834622168,
       1.2790618346221683,
       1.2763096027758547,
       1.2763096027758547,
       1.2698656027758548,
       1.2634216027758547,
       1.2730876027758549,
       1.2698656027758548,
       1.2827536027758548,
       1.2827536027758548,
       1.2891976027758547,
       1.2891976027758547,
       1.2763096027758547,
       1.2440896027758548,
       1.2278862829293962,
       1.2083981129379795,
       1.2181421129379795,
       1.2019021129379794,
       1.2311341129379794,
       1.2278861129379794,
       1.2376301129379794,
       1.2343821129379795,
       1.2311341129379794,
       1.2441261129379795,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2505844918195868,
       1.2657202604760505,
       1.2759242604760503,
       1.2529652604760504,
       1.2657202604760505,
       1.2682712604760504,
       1.2759242604760503,
       1.2759242604760503,
       1.3014342604760505,
       1.3065362604760504,
       1.2912302604760504,
       1.2759242604760503,
       1.2759242604760503,
       1.2835772604760505,
       1.2912302604760504,
       1.3090872604760504,
       1.3396992604760505,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3650217848042823,
       1.3507111524747228,
       1.2603111524747228,
       1.2005496764112615,
       1.216456216386466,
       1.197616216386466,
       1.2572762163864661,
       1.3106562163864661,
       1.2761162163864659,
       1.3106562163864661,
       1.3294962163864663,
       1.3106562163864661,
       1.3169362163864662,
       1.3420562163864658,
       1.367176216386466,
       1.338916216386466,
       1.351476216386466,
       1.3451962163864661,
       1.3420562163864658,
       1.3420562163864658,
       1.285536216386466,
       1.3169362163864662,
       1.3451962163864661,
       1.351476216386466,
       1.426836216386466,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.429761966277213,
       1.405343502747245,
       1.4180152188107062,
       1.5573292188107062,
       1.6269862188107063,
       1.6236692188107062,
       1.640254218810706,
       1.743081218810706,
       1.6900092188107059,
       1.6734242188107062,
       1.693326218810706,
       1.6900092188107059,
       1.6468882188107061,
       1.6535222188107062,
       1.6701072188107062,
       1.693326218810706,
       1.640254218810706,
       1.6634732188107064,
       1.6435712188107061,
       1.5971332188107061,
       1.6137182188107062,
       1.583865218810706,
       1.6004502188107061,
       1.620352218810706,
       1.6137182188107062,
       1.6468882188107061,
       1.6104012188107062,
       1.593816218810706,
       1.5971332188107061,
       1.6435712188107061,
       1.607084218810706,
       1.607084218810706,
       1.524159218810706,
       1.5009402188107062,
       1.5274762188107063,
       1.530793218810706,
       1.4909892188107061,
       1.481038218810706,
       1.431283218810706,
       1.4909892188107061,
       1.4710872188107065,
       1.4511852188107062,
       1.437917218810706,
       1.4080642188107062,
       1.4113812188107062,
       1.431283218810706,
       1.4279662188107058,
       1.4578192188107062,
       1.4611362188107062,
       1.4578192188107062,
       1.434600218810706,
       1.4644532188107062,
       1.4146982188107065,
       1.3616262188107062,
       1.3616262188107062,
       1.384845218810706,
       1.3947962188107061,
       1.3218222188107063,
       1.3085542188107062,
       1.3483582188107062,
       1.3516752188107062,
       1.3516752188107062,
       1.374894218810706,
       1.381528218810706,
       1.378211218810706,
       1.378211218810706,
       1.3981132188107062,
       1.4180152188107062,
       1.4180152188107062,
       1.431283218810706,
       1.4279662188107058,
       1.434600218810706,
       1.4246492188107063,
       1.4146982188107065,
       1.4113812188107062,
       1.3981132188107062,
       1.4014302188107064,
       1.4146982188107065,
       1.4180152188107062,
       1.4445512188107061,
       1.4445512188107061,
       1.4445512188107061,
       1.454502218810706,
       1.4710872188107065,
       1.4976232188107064,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.5139828984041357,
       1.528612798051276,
       1.503748798051276,
       1.516180798051276,
       1.509964798051276,
       1.5379367980512757,
       1.559692798051276,
       1.575232798051276,
       1.575232798051276,
       1.578340798051276,
       1.584556798051276,
       1.581448798051276,
       1.572124798051276,
       1.612528798051276,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.6123069591877508,
       1.561630645797858,
       1.535428176831641,
       1.4994974067362736,
       1.528889266755347,
       1.535421266755347,
       1.541953266755347,
       1.5452192667553473,
       1.5550172667553472,
       1.548485266755347,
       1.5550172667553472,
       1.5615492667553472,
       1.5746132667553472,
       1.5844112667553472,
       1.5746132667553472,
       1.548485266755347,
       1.571347266755347,
       1.5778792667553472,
       1.5680812667553472,
       1.5844112667553472,
       1.610539266755347,
       1.610539266755347,
       1.630135266755347,
       1.6268692667553473,
       1.6431992667553472,
       1.6431992667553472,
       1.6334012667553472,
       1.6758592667553471,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549,
       1.695201025443549
      ],
      "yaxis": "y"
     },
     {
      "mode": "lines",
      "name": "indicator",
      "showlegend": true,
      "type": "scatter",
      "visible": "legendonly",
      "x": [
       "2018-12-27",
       "2018-12-28",
       "2019-01-02",
       "2019-01-03",
       "2019-01-04",
       "2019-01-07",
       "2019-01-08",
       "2019-01-09",
       "2019-01-10",
       "2019-01-11",
       "2019-01-14",
       "2019-01-15",
       "2019-01-16",
       "2019-01-17",
       "2019-01-18",
       "2019-01-21",
       "2019-01-22",
       "2019-01-23",
       "2019-01-24",
       "2019-01-25",
       "2019-01-28",
       "2019-01-29",
       "2019-01-30",
       "2019-01-31",
       "2019-02-01",
       "2019-02-11",
       "2019-02-12",
       "2019-02-13",
       "2019-02-14",
       "2019-02-15",
       "2019-02-18",
       "2019-02-19",
       "2019-02-20",
       "2019-02-21",
       "2019-02-22",
       "2019-02-25",
       "2019-02-26",
       "2019-02-27",
       "2019-02-28",
       "2019-03-01",
       "2019-03-04",
       "2019-03-05",
       "2019-03-06",
       "2019-03-07",
       "2019-03-08",
       "2019-03-11",
       "2019-03-12",
       "2019-03-13",
       "2019-03-14",
       "2019-03-15",
       "2019-03-18",
       "2019-03-19",
       "2019-03-20",
       "2019-03-21",
       "2019-03-22",
       "2019-03-25",
       "2019-03-26",
       "2019-03-27",
       "2019-03-28",
       "2019-03-29",
       "2019-04-01",
       "2019-04-02",
       "2019-04-03",
       "2019-04-04",
       "2019-04-08",
       "2019-04-09",
       "2019-04-10",
       "2019-04-11",
       "2019-04-12",
       "2019-04-15",
       "2019-04-16",
       "2019-04-17",
       "2019-04-18",
       "2019-04-19",
       "2019-04-22",
       "2019-04-23",
       "2019-04-24",
       "2019-04-25",
       "2019-04-26",
       "2019-04-29",
       "2019-04-30",
       "2019-05-06",
       "2019-05-07",
       "2019-05-08",
       "2019-05-09",
       "2019-05-10",
       "2019-05-13",
       "2019-05-14",
       "2019-05-15",
       "2019-05-16",
       "2019-05-17",
       "2019-05-20",
       "2019-05-21",
       "2019-05-22",
       "2019-05-23",
       "2019-05-24",
       "2019-05-27",
       "2019-05-28",
       "2019-05-29",
       "2019-05-30",
       "2019-05-31",
       "2019-06-03",
       "2019-06-04",
       "2019-06-05",
       "2019-06-06",
       "2019-06-10",
       "2019-06-11",
       "2019-06-12",
       "2019-06-13",
       "2019-06-14",
       "2019-06-17",
       "2019-06-18",
       "2019-06-19",
       "2019-06-20",
       "2019-06-21",
       "2019-06-24",
       "2019-06-25",
       "2019-06-26",
       "2019-06-27",
       "2019-06-28",
       "2019-07-01",
       "2019-07-02",
       "2019-07-03",
       "2019-07-04",
       "2019-07-05",
       "2019-07-08",
       "2019-07-09",
       "2019-07-10",
       "2019-07-11",
       "2019-07-12",
       "2019-07-15",
       "2019-07-16",
       "2019-07-17",
       "2019-07-18",
       "2019-07-19",
       "2019-07-22",
       "2019-07-23",
       "2019-07-24",
       "2019-07-25",
       "2019-07-26",
       "2019-07-29",
       "2019-07-30",
       "2019-07-31",
       "2019-08-01",
       "2019-08-02",
       "2019-08-05",
       "2019-08-06",
       "2019-08-07",
       "2019-08-08",
       "2019-08-09",
       "2019-08-12",
       "2019-08-13",
       "2019-08-14",
       "2019-08-15",
       "2019-08-16",
       "2019-08-19",
       "2019-08-20",
       "2019-08-21",
       "2019-08-22",
       "2019-08-23",
       "2019-08-26",
       "2019-08-27",
       "2019-08-28",
       "2019-08-29",
       "2019-08-30",
       "2019-09-02",
       "2019-09-03",
       "2019-09-04",
       "2019-09-05",
       "2019-09-06",
       "2019-09-09",
       "2019-09-10",
       "2019-09-11",
       "2019-09-12",
       "2019-09-16",
       "2019-09-17",
       "2019-09-18",
       "2019-09-19",
       "2019-09-20",
       "2019-09-23",
       "2019-09-24",
       "2019-09-25",
       "2019-09-26",
       "2019-09-27",
       "2019-09-30",
       "2019-10-08",
       "2019-10-09",
       "2019-10-10",
       "2019-10-11",
       "2019-10-14",
       "2019-10-15",
       "2019-10-16",
       "2019-10-17",
       "2019-10-18",
       "2019-10-21",
       "2019-10-22",
       "2019-10-23",
       "2019-10-24",
       "2019-10-25",
       "2019-10-28",
       "2019-10-29",
       "2019-10-30",
       "2019-10-31",
       "2019-11-01",
       "2019-11-04",
       "2019-11-05",
       "2019-11-06",
       "2019-11-07",
       "2019-11-08",
       "2019-11-11",
       "2019-11-12",
       "2019-11-13",
       "2019-11-14",
       "2019-11-15",
       "2019-11-18",
       "2019-11-19",
       "2019-11-20",
       "2019-11-21",
       "2019-11-22",
       "2019-11-25",
       "2019-11-26",
       "2019-11-27",
       "2019-11-28",
       "2019-11-29",
       "2019-12-02",
       "2019-12-03",
       "2019-12-04",
       "2019-12-05",
       "2019-12-06",
       "2019-12-09",
       "2019-12-10",
       "2019-12-11",
       "2019-12-12",
       "2019-12-13",
       "2019-12-16",
       "2019-12-17",
       "2019-12-18",
       "2019-12-19",
       "2019-12-20",
       "2019-12-23",
       "2019-12-24",
       "2019-12-25",
       "2019-12-26",
       "2019-12-27",
       "2019-12-30",
       "2019-12-31",
       "2020-01-02",
       "2020-01-03",
       "2020-01-06",
       "2020-01-07",
       "2020-01-08",
       "2020-01-09",
       "2020-01-10",
       "2020-01-13",
       "2020-01-14",
       "2020-01-15",
       "2020-01-16",
       "2020-01-17",
       "2020-01-20",
       "2020-01-21",
       "2020-01-22",
       "2020-01-23",
       "2020-02-03",
       "2020-02-04",
       "2020-02-05",
       "2020-02-06",
       "2020-02-07",
       "2020-02-10",
       "2020-02-11",
       "2020-02-12",
       "2020-02-13",
       "2020-02-14",
       "2020-02-17",
       "2020-02-18",
       "2020-02-19",
       "2020-02-20",
       "2020-02-21",
       "2020-02-24",
       "2020-02-25",
       "2020-02-26",
       "2020-02-27",
       "2020-02-28",
       "2020-03-02",
       "2020-03-03",
       "2020-03-04",
       "2020-03-05",
       "2020-03-06",
       "2020-03-09",
       "2020-03-10",
       "2020-03-11",
       "2020-03-12",
       "2020-03-13",
       "2020-03-16",
       "2020-03-17",
       "2020-03-18",
       "2020-03-19",
       "2020-03-20",
       "2020-03-23",
       "2020-03-24",
       "2020-03-25",
       "2020-03-26",
       "2020-03-27",
       "2020-03-30",
       "2020-03-31",
       "2020-04-01",
       "2020-04-02",
       "2020-04-03",
       "2020-04-07",
       "2020-04-08",
       "2020-04-09",
       "2020-04-10",
       "2020-04-13",
       "2020-04-14",
       "2020-04-15",
       "2020-04-16",
       "2020-04-17",
       "2020-04-20",
       "2020-04-21",
       "2020-04-22",
       "2020-04-23",
       "2020-04-24",
       "2020-04-27",
       "2020-04-28",
       "2020-04-29",
       "2020-04-30",
       "2020-05-06",
       "2020-05-07",
       "2020-05-08",
       "2020-05-11",
       "2020-05-12",
       "2020-05-13",
       "2020-05-14",
       "2020-05-15",
       "2020-05-18",
       "2020-05-19",
       "2020-05-20",
       "2020-05-21",
       "2020-05-22",
       "2020-05-25",
       "2020-05-26",
       "2020-05-27",
       "2020-05-28",
       "2020-05-29",
       "2020-06-01",
       "2020-06-02",
       "2020-06-03",
       "2020-06-04",
       "2020-06-05",
       "2020-06-08",
       "2020-06-09",
       "2020-06-10",
       "2020-06-11",
       "2020-06-12",
       "2020-06-15",
       "2020-06-16",
       "2020-06-17",
       "2020-06-18",
       "2020-06-19",
       "2020-06-22",
       "2020-06-23",
       "2020-06-24",
       "2020-06-29",
       "2020-06-30",
       "2020-07-01",
       "2020-07-02",
       "2020-07-03",
       "2020-07-06",
       "2020-07-07",
       "2020-07-08",
       "2020-07-09",
       "2020-07-10",
       "2020-07-13",
       "2020-07-14",
       "2020-07-15",
       "2020-07-16",
       "2020-07-17",
       "2020-07-20",
       "2020-07-21",
       "2020-07-22",
       "2020-07-23",
       "2020-07-24",
       "2020-07-27",
       "2020-07-28",
       "2020-07-29",
       "2020-07-30",
       "2020-07-31",
       "2020-08-03",
       "2020-08-04",
       "2020-08-05",
       "2020-08-06",
       "2020-08-07",
       "2020-08-10",
       "2020-08-11",
       "2020-08-12",
       "2020-08-13",
       "2020-08-14",
       "2020-08-17",
       "2020-08-18",
       "2020-08-19",
       "2020-08-20",
       "2020-08-21",
       "2020-08-24",
       "2020-08-25",
       "2020-08-26",
       "2020-08-27",
       "2020-08-28",
       "2020-08-31",
       "2020-09-01",
       "2020-09-02",
       "2020-09-03",
       "2020-09-04",
       "2020-09-07",
       "2020-09-08",
       "2020-09-09",
       "2020-09-10",
       "2020-09-11",
       "2020-09-14",
       "2020-09-15",
       "2020-09-16",
       "2020-09-17",
       "2020-09-18",
       "2020-09-21",
       "2020-09-22",
       "2020-09-23",
       "2020-09-24",
       "2020-09-25",
       "2020-09-28",
       "2020-09-29",
       "2020-09-30",
       "2020-10-09",
       "2020-10-12",
       "2020-10-13",
       "2020-10-14",
       "2020-10-15",
       "2020-10-16",
       "2020-10-19",
       "2020-10-20",
       "2020-10-21",
       "2020-10-22",
       "2020-10-23",
       "2020-10-26",
       "2020-10-27",
       "2020-10-28",
       "2020-10-29",
       "2020-10-30",
       "2020-11-02",
       "2020-11-03",
       "2020-11-04",
       "2020-11-05",
       "2020-11-06",
       "2020-11-09",
       "2020-11-10",
       "2020-11-11",
       "2020-11-12",
       "2020-11-13",
       "2020-11-16",
       "2020-11-17",
       "2020-11-18",
       "2020-11-19",
       "2020-11-20",
       "2020-11-23",
       "2020-11-24",
       "2020-11-25",
       "2020-11-26",
       "2020-11-27",
       "2020-11-30",
       "2020-12-01",
       "2020-12-02",
       "2020-12-03",
       "2020-12-04",
       "2020-12-07",
       "2020-12-08",
       "2020-12-09",
       "2020-12-10",
       "2020-12-11",
       "2020-12-14",
       "2020-12-15",
       "2020-12-16",
       "2020-12-17",
       "2020-12-18",
       "2020-12-21",
       "2020-12-22",
       "2020-12-23",
       "2020-12-24",
       "2020-12-25",
       "2020-12-28",
       "2020-12-29",
       "2020-12-30",
       "2020-12-31",
       "2021-01-04",
       "2021-01-05",
       "2021-01-06",
       "2021-01-07",
       "2021-01-08",
       "2021-01-11",
       "2021-01-12",
       "2021-01-13",
       "2021-01-14",
       "2021-01-15",
       "2021-01-18",
       "2021-01-19",
       "2021-01-20",
       "2021-01-21",
       "2021-01-22",
       "2021-01-25",
       "2021-01-26",
       "2021-01-27",
       "2021-01-28",
       "2021-01-29",
       "2021-02-01",
       "2021-02-02",
       "2021-02-03",
       "2021-02-04",
       "2021-02-05",
       "2021-02-08",
       "2021-02-09",
       "2021-02-10",
       "2021-02-18",
       "2021-02-19",
       "2021-02-22",
       "2021-02-23",
       "2021-02-24",
       "2021-02-25",
       "2021-02-26",
       "2021-03-01",
       "2021-03-02",
       "2021-03-03",
       "2021-03-04",
       "2021-03-05",
       "2021-03-08",
       "2021-03-09",
       "2021-03-10",
       "2021-03-11",
       "2021-03-12",
       "2021-03-15",
       "2021-03-16",
       "2021-03-17",
       "2021-03-18",
       "2021-03-19",
       "2021-03-22",
       "2021-03-23",
       "2021-03-24",
       "2021-03-25",
       "2021-03-26",
       "2021-03-29",
       "2021-03-30",
       "2021-03-31",
       "2021-04-01",
       "2021-04-02",
       "2021-04-06",
       "2021-04-07",
       "2021-04-08",
       "2021-04-09",
       "2021-04-12",
       "2021-04-13",
       "2021-04-14",
       "2021-04-15",
       "2021-04-16",
       "2021-04-19",
       "2021-04-20",
       "2021-04-21",
       "2021-04-22",
       "2021-04-23",
       "2021-04-26",
       "2021-04-27",
       "2021-04-28",
       "2021-04-29",
       "2021-04-30",
       "2021-05-06",
       "2021-05-07",
       "2021-05-10",
       "2021-05-11",
       "2021-05-12",
       "2021-05-13",
       "2021-05-14",
       "2021-05-17",
       "2021-05-18",
       "2021-05-19",
       "2021-05-20",
       "2021-05-21",
       "2021-05-24",
       "2021-05-25",
       "2021-05-26",
       "2021-05-27",
       "2021-05-28",
       "2021-05-31",
       "2021-06-01",
       "2021-06-02",
       "2021-06-03",
       "2021-06-04",
       "2021-06-07",
       "2021-06-08",
       "2021-06-09",
       "2021-06-10",
       "2021-06-11",
       "2021-06-15",
       "2021-06-16",
       "2021-06-17",
       "2021-06-18",
       "2021-06-21",
       "2021-06-22",
       "2021-06-23",
       "2021-06-24",
       "2021-06-25",
       "2021-06-28",
       "2021-06-29",
       "2021-06-30",
       "2021-07-01",
       "2021-07-02",
       "2021-07-05",
       "2021-07-06",
       "2021-07-07",
       "2021-07-08",
       "2021-07-09",
       "2021-07-12",
       "2021-07-13",
       "2021-07-14",
       "2021-07-15",
       "2021-07-16",
       "2021-07-19",
       "2021-07-20",
       "2021-07-21",
       "2021-07-22",
       "2021-07-23",
       "2021-07-26",
       "2021-07-27",
       "2021-07-28",
       "2021-07-29",
       "2021-07-30",
       "2021-08-02",
       "2021-08-03",
       "2021-08-04",
       "2021-08-05",
       "2021-08-06",
       "2021-08-09",
       "2021-08-10",
       "2021-08-11",
       "2021-08-12",
       "2021-08-13",
       "2021-08-16",
       "2021-08-17",
       "2021-08-18",
       "2021-08-19",
       "2021-08-20",
       "2021-08-23",
       "2021-08-24",
       "2021-08-25",
       "2021-08-26",
       "2021-08-27",
       "2021-08-30",
       "2021-08-31",
       "2021-09-01",
       "2021-09-02",
       "2021-09-03",
       "2021-09-06",
       "2021-09-07",
       "2021-09-08",
       "2021-09-09",
       "2021-09-10",
       "2021-09-13",
       "2021-09-14",
       "2021-09-15",
       "2021-09-16",
       "2021-09-17",
       "2021-09-22",
       "2021-09-23",
       "2021-09-24",
       "2021-09-27",
       "2021-09-28",
       "2021-09-29",
       "2021-09-30",
       "2021-10-08",
       "2021-10-11",
       "2021-10-12",
       "2021-10-13",
       "2021-10-14",
       "2021-10-15",
       "2021-10-18",
       "2021-10-19",
       "2021-10-20",
       "2021-10-21",
       "2021-10-22",
       "2021-10-25",
       "2021-10-26",
       "2021-10-27",
       "2021-10-28",
       "2021-10-29",
       "2021-11-01",
       "2021-11-02",
       "2021-11-03",
       "2021-11-04",
       "2021-11-05",
       "2021-11-08",
       "2021-11-09",
       "2021-11-10",
       "2021-11-11",
       "2021-11-12",
       "2021-11-15",
       "2021-11-16",
       "2021-11-17",
       "2021-11-18",
       "2021-11-19",
       "2021-11-22",
       "2021-11-23",
       "2021-11-24",
       "2021-11-25",
       "2021-11-26",
       "2021-11-29",
       "2021-11-30",
       "2021-12-01",
       "2021-12-02",
       "2021-12-03",
       "2021-12-06",
       "2021-12-07",
       "2021-12-08",
       "2021-12-09",
       "2021-12-10",
       "2021-12-13",
       "2021-12-14",
       "2021-12-15",
       "2021-12-16",
       "2021-12-17",
       "2021-12-20",
       "2021-12-21",
       "2021-12-22",
       "2021-12-23",
       "2021-12-24",
       "2021-12-27",
       "2021-12-28",
       "2021-12-29",
       "2021-12-30",
       "2021-12-31",
       "2022-01-04",
       "2022-01-05",
       "2022-01-06",
       "2022-01-07",
       "2022-01-10",
       "2022-01-11",
       "2022-01-12",
       "2022-01-13",
       "2022-01-14",
       "2022-01-17",
       "2022-01-18",
       "2022-01-19",
       "2022-01-20",
       "2022-01-21",
       "2022-01-24",
       "2022-01-25",
       "2022-01-26",
       "2022-01-27",
       "2022-01-28",
       "2022-02-07",
       "2022-02-08",
       "2022-02-09",
       "2022-02-10",
       "2022-02-11",
       "2022-02-14",
       "2022-02-15",
       "2022-02-16",
       "2022-02-17",
       "2022-02-18",
       "2022-02-21",
       "2022-02-22",
       "2022-02-23",
       "2022-02-24",
       "2022-02-25",
       "2022-02-28",
       "2022-03-01",
       "2022-03-02",
       "2022-03-03",
       "2022-03-04",
       "2022-03-07",
       "2022-03-08",
       "2022-03-09",
       "2022-03-10",
       "2022-03-11",
       "2022-03-14",
       "2022-03-15",
       "2022-03-16",
       "2022-03-17",
       "2022-03-18",
       "2022-03-21",
       "2022-03-22",
       "2022-03-23",
       "2022-03-24",
       "2022-03-25",
       "2022-03-28",
       "2022-03-29",
       "2022-03-30",
       "2022-03-31",
       "2022-04-01",
       "2022-04-06",
       "2022-04-07",
       "2022-04-08",
       "2022-04-11",
       "2022-04-12",
       "2022-04-13",
       "2022-04-14",
       "2022-04-15",
       "2022-04-18",
       "2022-04-19",
       "2022-04-20",
       "2022-04-21",
       "2022-04-22",
       "2022-04-25",
       "2022-04-26",
       "2022-04-27",
       "2022-04-28",
       "2022-04-29",
       "2022-05-05",
       "2022-05-06",
       "2022-05-09",
       "2022-05-10",
       "2022-05-11",
       "2022-05-12",
       "2022-05-13",
       "2022-05-16",
       "2022-05-17",
       "2022-05-18",
       "2022-05-19",
       "2022-05-20",
       "2022-05-23",
       "2022-05-24",
       "2022-05-25",
       "2022-05-26",
       "2022-05-27",
       "2022-05-30",
       "2022-05-31",
       "2022-06-01",
       "2022-06-02",
       "2022-06-06",
       "2022-06-07",
       "2022-06-08",
       "2022-06-09",
       "2022-06-10",
       "2022-06-13",
       "2022-06-14",
       "2022-06-15",
       "2022-06-16",
       "2022-06-17",
       "2022-06-20",
       "2022-06-21",
       "2022-06-22",
       "2022-06-23",
       "2022-06-24",
       "2022-06-27",
       "2022-06-28",
       "2022-06-29",
       "2022-06-30",
       "2022-07-01",
       "2022-07-04",
       "2022-07-05",
       "2022-07-06",
       "2022-07-07",
       "2022-07-08",
       "2022-07-11",
       "2022-07-12",
       "2022-07-13",
       "2022-07-14",
       "2022-07-15",
       "2022-07-18",
       "2022-07-19",
       "2022-07-20",
       "2022-07-21",
       "2022-07-22",
       "2022-07-25",
       "2022-07-26",
       "2022-07-27",
       "2022-07-28",
       "2022-07-29",
       "2022-08-01",
       "2022-08-02",
       "2022-08-03",
       "2022-08-04",
       "2022-08-05",
       "2022-08-08",
       "2022-08-09",
       "2022-08-10",
       "2022-08-11",
       "2022-08-12",
       "2022-08-15",
       "2022-08-16",
       "2022-08-17",
       "2022-08-18",
       "2022-08-19",
       "2022-08-22",
       "2022-08-23",
       "2022-08-24",
       "2022-08-25",
       "2022-08-26",
       "2022-08-29",
       "2022-08-30",
       "2022-08-31",
       "2022-09-01",
       "2022-09-02",
       "2022-09-05",
       "2022-09-06",
       "2022-09-07",
       "2022-09-08",
       "2022-09-09",
       "2022-09-13",
       "2022-09-14",
       "2022-09-15",
       "2022-09-16",
       "2022-09-19",
       "2022-09-20",
       "2022-09-21",
       "2022-09-22",
       "2022-09-23",
       "2022-09-26",
       "2022-09-27",
       "2022-09-28",
       "2022-09-29",
       "2022-09-30",
       "2022-10-10",
       "2022-10-11",
       "2022-10-12",
       "2022-10-13",
       "2022-10-14",
       "2022-10-17",
       "2022-10-18",
       "2022-10-19",
       "2022-10-20",
       "2022-10-21",
       "2022-10-24",
       "2022-10-25",
       "2022-10-26",
       "2022-10-27",
       "2022-10-28",
       "2022-10-31",
       "2022-11-01",
       "2022-11-02",
       "2022-11-03",
       "2022-11-04",
       "2022-11-07",
       "2022-11-08",
       "2022-11-09",
       "2022-11-10",
       "2022-11-11",
       "2022-11-14",
       "2022-11-15",
       "2022-11-16",
       "2022-11-17",
       "2022-11-18",
       "2022-11-21",
       "2022-11-22",
       "2022-11-23",
       "2022-11-24",
       "2022-11-25",
       "2022-11-28",
       "2022-11-29",
       "2022-11-30",
       "2022-12-01",
       "2022-12-02",
       "2022-12-05",
       "2022-12-06",
       "2022-12-07",
       "2022-12-08",
       "2022-12-09",
       "2022-12-12",
       "2022-12-13",
       "2022-12-14",
       "2022-12-15",
       "2022-12-16",
       "2022-12-19",
       "2022-12-20",
       "2022-12-21",
       "2022-12-22",
       "2022-12-23",
       "2022-12-26",
       "2022-12-27",
       "2022-12-28",
       "2022-12-29",
       "2022-12-30",
       "2023-01-03",
       "2023-01-04",
       "2023-01-05",
       "2023-01-06",
       "2023-01-09",
       "2023-01-10",
       "2023-01-11",
       "2023-01-12",
       "2023-01-13",
       "2023-01-16",
       "2023-01-17",
       "2023-01-18",
       "2023-01-19",
       "2023-01-20",
       "2023-01-30",
       "2023-01-31",
       "2023-02-01",
       "2023-02-02",
       "2023-02-03",
       "2023-02-06",
       "2023-02-07",
       "2023-02-08",
       "2023-02-09",
       "2023-02-10",
       "2023-02-13",
       "2023-02-14",
       "2023-02-15",
       "2023-02-16",
       "2023-02-17",
       "2023-02-20",
       "2023-02-21",
       "2023-02-22",
       "2023-02-23",
       "2023-02-24",
       "2023-02-27",
       "2023-02-28",
       "2023-03-01",
       "2023-03-02",
       "2023-03-03",
       "2023-03-06",
       "2023-03-07",
       "2023-03-08",
       "2023-03-09",
       "2023-03-10",
       "2023-03-13",
       "2023-03-14",
       "2023-03-15",
       "2023-03-16",
       "2023-03-17",
       "2023-03-20",
       "2023-03-21",
       "2023-03-22",
       "2023-03-23",
       "2023-03-24",
       "2023-03-27",
       "2023-03-28",
       "2023-03-29",
       "2023-03-30",
       "2023-03-31",
       "2023-04-03",
       "2023-04-04",
       "2023-04-06",
       "2023-04-07",
       "2023-04-10",
       "2023-04-11",
       "2023-04-12",
       "2023-04-13",
       "2023-04-14",
       "2023-04-17",
       "2023-04-18",
       "2023-04-19",
       "2023-04-20",
       "2023-04-21",
       "2023-04-24",
       "2023-04-25",
       "2023-04-26",
       "2023-04-27",
       "2023-04-28",
       "2023-05-04",
       "2023-05-05",
       "2023-05-08",
       "2023-05-09",
       "2023-05-10",
       "2023-05-11",
       "2023-05-12",
       "2023-05-15",
       "2023-05-16",
       "2023-05-17",
       "2023-05-18",
       "2023-05-19",
       "2023-05-22",
       "2023-05-23",
       "2023-05-24",
       "2023-05-25",
       "2023-05-26",
       "2023-05-29",
       "2023-05-30",
       "2023-05-31",
       "2023-06-01",
       "2023-06-02",
       "2023-06-05",
       "2023-06-06",
       "2023-06-07",
       "2023-06-08",
       "2023-06-09",
       "2023-06-12",
       "2023-06-13",
       "2023-06-14",
       "2023-06-15",
       "2023-06-16",
       "2023-06-19",
       "2023-06-20",
       "2023-06-21",
       "2023-06-26",
       "2023-06-27",
       "2023-06-28",
       "2023-06-29",
       "2023-06-30",
       "2023-07-03",
       "2023-07-04",
       "2023-07-05",
       "2023-07-06",
       "2023-07-07",
       "2023-07-10",
       "2023-07-11",
       "2023-07-12",
       "2023-07-13",
       "2023-07-14",
       "2023-07-17",
       "2023-07-18",
       "2023-07-19",
       "2023-07-20",
       "2023-07-21",
       "2023-07-24",
       "2023-07-25",
       "2023-07-26",
       "2023-07-27",
       "2023-07-28",
       "2023-07-31",
       "2023-08-01",
       "2023-08-02",
       "2023-08-03",
       "2023-08-04",
       "2023-08-07",
       "2023-08-08",
       "2023-08-09",
       "2023-08-10",
       "2023-08-11",
       "2023-08-14",
       "2023-08-15",
       "2023-08-16",
       "2023-08-17",
       "2023-08-18",
       "2023-08-21",
       "2023-08-22",
       "2023-08-23",
       "2023-08-24",
       "2023-08-25",
       "2023-08-28",
       "2023-08-29",
       "2023-08-30",
       "2023-08-31",
       "2023-09-01",
       "2023-09-04",
       "2023-09-05",
       "2023-09-06",
       "2023-09-07",
       "2023-09-08",
       "2023-09-11",
       "2023-09-12",
       "2023-09-13",
       "2023-09-14",
       "2023-09-15",
       "2023-09-18",
       "2023-09-19",
       "2023-09-20",
       "2023-09-21",
       "2023-09-22",
       "2023-09-25",
       "2023-09-26",
       "2023-09-27",
       "2023-09-28",
       "2023-10-09",
       "2023-10-10",
       "2023-10-11",
       "2023-10-12",
       "2023-10-13",
       "2023-10-16",
       "2023-10-17",
       "2023-10-18",
       "2023-10-19",
       "2023-10-20",
       "2023-10-23",
       "2023-10-24",
       "2023-10-25",
       "2023-10-26",
       "2023-10-27",
       "2023-10-30",
       "2023-10-31",
       "2023-11-01",
       "2023-11-02",
       "2023-11-03",
       "2023-11-06",
       "2023-11-07",
       "2023-11-08",
       "2023-11-09",
       "2023-11-10",
       "2023-11-13",
       "2023-11-14",
       "2023-11-15",
       "2023-11-16",
       "2023-11-17",
       "2023-11-20",
       "2023-11-21",
       "2023-11-22",
       "2023-11-23",
       "2023-11-24",
       "2023-11-27",
       "2023-11-28",
       "2023-11-29",
       "2023-11-30"
      ],
      "xaxis": "x",
      "y": [
       null,
       42.361421601213614,
       30.862929234020854,
       61.310995239994334,
       74.54242030517885,
       76.27335418265879,
       47.180969771689796,
       44.09527973483293,
       69.98552827363741,
       63.52155581140687,
       56.86748627309229,
       64.77238026215086,
       35.116576335212805,
       62.46357807613375,
       58.16168983045312,
       39.91198504235263,
       40.37688951102862,
       19.22038916749454,
       38.35068263701458,
       27.33903705199199,
       39.16433143620835,
       21.71793152578878,
       24.40306946833539,
       20.481540570243414,
       61.999002909024476,
       64.56777209474049,
       67.7366432174915,
       69.49069593916447,
       68.00814339127139,
       49.177033669224556,
       73.54720648941999,
       51.52365313281973,
       53.47747426342885,
       43.22189924406246,
       73.2513725672374,
       80.27099737108132,
       50.929248335254414,
       64.88922552461095,
       67.14108143752037,
       71.17422219290093,
       68.15245934775056,
       84.06544264723071,
       75.73614118194939,
       82.45962519779772,
       32.12712089742421,
       68.41798542180298,
       53.771423729871124,
       33.86536688077927,
       31.49279711778206,
       57.36697139286289,
       62.81528257680859,
       42.66459603020586,
       42.40767613639125,
       59.70548717168316,
       59.01028455760822,
       36.1338129444791,
       27.63642526425474,
       34.292564288916736,
       30.462799296134364,
       69.52851581816684,
       70.92409286875143,
       52.52787304245488,
       64.02303810383025,
       57.45011673806888,
       59.40555622548095,
       45.329699408521954,
       60.91028567933167,
       31.79497714531568,
       54.25485113795819,
       47.51494120309027,
       57.08365347699819,
       62.67149775561905,
       34.78920253313027,
       61.29584985875075,
       34.46944882722216,
       23.568687398942497,
       44.495051580858075,
       20.857980132308064,
       23.291600077377883,
       20.89433065470296,
       55.52077469558183,
       19.844813897479153,
       58.083049825672646,
       36.04803112619495,
       30.597265011033183,
       61.81472349909987,
       42.885198007213944,
       44.443935014762,
       60.7943028137632,
       56.65081125177011,
       25.33686895422538,
       38.14458629784,
       58.29573606914058,
       37.10347109833906,
       32.36638018344231,
       44.2662339875721,
       63.72920276808971,
       55.790954619502294,
       36.148778846694995,
       36.381560624474965,
       39.15843660392145,
       29.62194849621915,
       19.83265570676217,
       44.88606015730577,
       24.425051808720003,
       57.1797315468057,
       71.6458251440036,
       47.71828184410168,
       62.297629072916585,
       49.398820051478374,
       48.89742946462173,
       63.72651604199056,
       64.54989715620907,
       74.50841016330911,
       68.1267571369156,
       43.45172363372408,
       47.042764319242906,
       48.21077525281584,
       59.87426891313975,
       30.785233488197235,
       68.34677998820503,
       45.50010378586484,
       32.85488637869617,
       58.0574192558777,
       53.423109227949446,
       23.5175535283407,
       35.274239539395616,
       26.49712667438473,
       47.3236722695954,
       55.63987264059778,
       71.18456834459255,
       49.117384222465354,
       38.70879682836908,
       37.48817591640925,
       55.43737893657337,
       27.131039578736175,
       51.347488407803894,
       65.03857596645138,
       40.14864467006982,
       35.58956603863697,
       40.142296256104174,
       65.46581526913438,
       40.42211619188206,
       30.032571271057037,
       22.43891723997223,
       21.131463846331798,
       14.151099993486616,
       22.713861759606758,
       53.288875431127565,
       31.140528610219434,
       59.9109893101753,
       39.44946094241589,
       55.60671130256157,
       45.354681666931334,
       54.209925409908,
       73.66550101533238,
       58.4820486817956,
       62.243063004853816,
       52.653130309017435,
       48.20917438668848,
       29.093476178758255,
       70.40667921894884,
       62.22912217194979,
       55.17072207712883,
       40.090434614397765,
       69.85485132542665,
       50.24131239393599,
       66.98920233660071,
       60.68485135589858,
       51.863357017019574,
       72.04312262491815,
       53.40562645007447,
       49.61743474552537,
       68.28447780227025,
       49.02503504793739,
       24.844508176997355,
       31.031418017916064,
       53.59593851184064,
       60.89494204641298,
       26.46546232039442,
       50.84342600130724,
       24.041554960286806,
       24.084118060669272,
       56.59773236083773,
       29.743872855510528,
       60.623610115894905,
       61.04379182421397,
       62.26680329788615,
       65.79581444034969,
       78.10242405938256,
       70.10691210614729,
       41.33496529267509,
       36.874496721559815,
       24.282419768975576,
       33.99588520034203,
       61.8788080431828,
       62.554511392199935,
       45.76697309514572,
       48.93495631586882,
       69.8767593269397,
       39.36956539675046,
       32.43901441842147,
       26.07193451718489,
       60.78838119968942,
       41.36090198021818,
       46.27015825083301,
       27.12396380490488,
       43.97015322366895,
       39.77009621958158,
       19.664700591125428,
       43.992026846749134,
       32.58570500455399,
       30.0490900817222,
       22.3018067276484,
       50.52545815441564,
       64.3670134629379,
       30.430267418040945,
       57.68773036011188,
       43.649968392543265,
       69.66406102116984,
       46.21235718505584,
       62.6226649178954,
       39.75311898571732,
       65.57992340824633,
       47.98903968112858,
       52.98918386846966,
       40.34795425577597,
       66.95107722046171,
       67.70118747138432,
       53.65577475752668,
       53.6557924537879,
       42.57707996419281,
       31.215628370709155,
       70.1450489061008,
       61.67975577548524,
       76.17394905233554,
       49.78499310480461,
       61.13043617921485,
       44.588134716375514,
       26.25664034504869,
       57.84465694112263,
       37.97775365818944,
       67.33429848000411,
       74.53888570950028,
       80.05892218938449,
       39.8640458303513,
       76.5874414124015,
       42.70715812197221,
       36.545555360682734,
       62.727444787316124,
       32.417569695133956,
       64.82757247628854,
       59.25883055445542,
       59.966616066529845,
       40.61613895097681,
       32.02112547207192,
       41.931051026216835,
       49.07467815716933,
       51.40111466626925,
       25.615214976231083,
       37.023181667166114,
       18.56330568773036,
       13.397878242513448,
       21.93812924711034,
       53.42030043178727,
       59.971818219265685,
       44.66025661747293,
       61.73191589748842,
       37.01181189090469,
       59.64935623699808,
       34.113496913524536,
       38.925846047147466,
       67.11167145906778,
       62.12666830554969,
       43.566018622137086,
       68.87278325499162,
       52.206660045753615,
       52.2066576818479,
       41.78352024457498,
       62.472250697220964,
       50.3054644193086,
       25.892375492841552,
       67.27975690400767,
       43.36561833933704,
       73.75914699076293,
       47.2244363335977,
       72.64026265100705,
       31.501181733734217,
       60.720803644020286,
       37.138615203199485,
       31.11983787566813,
       29.647634577329274,
       26.267372591855565,
       44.5076170642761,
       30.705260816298217,
       60.76939816307845,
       58.0772761698584,
       30.31584707445215,
       56.48730470238022,
       60.95530865362945,
       44.03316245571191,
       47.4104563517329,
       36.27168169569473,
       40.464560256758865,
       38.04194091179348,
       58.001759156606006,
       48.37080895255149,
       60.12077007869045,
       63.37815903749643,
       49.78188993879342,
       32.366658044855576,
       42.02905954345653,
       58.368999908793604,
       35.63422604342416,
       47.166911318217025,
       42.99405746835168,
       70.926963902168,
       45.36284899426172,
       74.34428495399779,
       66.90098795167344,
       40.936434864515256,
       66.10480163914674,
       29.54275883240699,
       51.66634907067719,
       62.9318825085924,
       66.47202077075681,
       43.029134796741126,
       55.75396627024785,
       48.59004099608857,
       36.057054633340975,
       58.60842026974825,
       31.213681078513854,
       46.17006784901861,
       32.11175347835263,
       57.47486849297019,
       30.7421299918315,
       44.2203564886679,
       24.797062362541535,
       48.27025773556861,
       67.56386225726975,
       56.74568899898595,
       45.30129052783897,
       71.67479062665892,
       78.64308562458287,
       83.70370983524693,
       84.54474563628624,
       84.73495115119937,
       34.890248091799506,
       29.956021235531352,
       33.320425900561666,
       44.26343758214761,
       34.936511501980874,
       39.97845775248731,
       28.538863812063255,
       59.484549005573115,
       32.461067961486854,
       39.13512364834782,
       64.55222396300744,
       33.80456557932447,
       32.05535717531953,
       38.78321027595086,
       37.19354998934153,
       56.24515086592351,
       58.89685902917281,
       61.55238309202527,
       64.16803269235,
       78.93681356567767,
       57.685357018281366,
       71.62837127411844,
       72.93582697126644,
       46.37287174000392,
       67.97251994972997,
       69.10870244368247,
       30.669246164911453,
       29.68226897617143,
       32.6452672205675,
       64.77540840718177,
       42.93814410270846,
       38.997628621127525,
       30.52907248220232,
       23.619133584614456,
       30.126833961609808,
       48.89738644252271,
       62.44426286301063,
       55.93623558216978,
       59.87203948644543,
       75.148038877633,
       39.608304529550864,
       51.12428264842496,
       44.82512133058344,
       31.43543917989507,
       63.099426350550345,
       34.23158262354127,
       43.788855945056184,
       68.30261020614944,
       64.49853401416676,
       61.431368603990514,
       47.817315473017665,
       33.953269874593076,
       34.248903054581625,
       56.6004767371171,
       64.99283268674272,
       38.60345516181074,
       38.65027722073659,
       43.03839193634381,
       56.56206256151976,
       59.150602193444946,
       78.54797352357025,
       56.25138988234297,
       52.55250205801634,
       29.282094946688517,
       29.60384851618237,
       63.478036845627734,
       51.87779767126924,
       27.081893517648194,
       54.5850369013489,
       34.9101467726047,
       37.746751480292055,
       50.07981663326155,
       50.63216210872208,
       64.46030544845257,
       48.683315059874,
       31.44439601193216,
       36.042125696593416,
       19.734446408082643,
       23.977437266182218,
       23.64625204801037,
       59.965512241911654,
       33.45118272342691,
       66.17053909501624,
       69.82296491565033,
       45.30082197696877,
       42.11819650892578,
       62.373892285579245,
       43.939919224919244,
       59.137874281764816,
       63.19684437920045,
       30.9497376924103,
       36.32066184008838,
       38.94297909581256,
       31.148274242953963,
       39.41481038413094,
       18.797276465525727,
       21.59461557784738,
       10.084108516749565,
       46.40845435173953,
       63.4677764993964,
       44.19787098610906,
       61.540677721430576,
       49.2733661920721,
       68.73714141716619,
       48.947100257277924,
       44.25260780431723,
       61.12806721654246,
       34.699177856355305,
       68.93375451228827,
       58.88609338429223,
       65.1586305920518,
       56.512536477595944,
       49.82087445099549,
       69.3323061735062,
       47.57861985934145,
       47.84258460299872,
       36.87944997066385,
       62.86181508677751,
       52.647401825633104,
       68.03073488966241,
       65.96752957011027,
       51.36594212579553,
       28.07856147777177,
       47.00229424441746,
       32.985002405984986,
       20.346090172681546,
       39.50911465662585,
       24.142703923879775,
       60.09854174890162,
       42.87094159385086,
       57.60721954681927,
       37.375026699714,
       28.841971157058058,
       62.808021036776836,
       26.415957545067414,
       51.11084638878061,
       21.481577469357546,
       62.31723167812134,
       71.4559966580645,
       40.57163953995866,
       43.57728236640043,
       66.88058525311261,
       49.66798308441667,
       39.27042944795525,
       30.750807110060375,
       20.06373141767945,
       53.65928984232081,
       27.52140437376734,
       49.359302032271046,
       27.570354890999763,
       54.64825705804941,
       63.8971530273845,
       47.019748963310214,
       57.64434339769536,
       36.65460549290647,
       55.10776768417568,
       31.495503090724895,
       27.12921871294671,
       33.06013235968388,
       58.2640150521515,
       40.77674905523208,
       32.26918288578697,
       54.249396610586565,
       51.73167406802977,
       30.32568857243359,
       21.24878299236861,
       32.77876110368337,
       47.27930707648138,
       60.678605624895226,
       57.59189080610913,
       73.35001704020952,
       79.4250550433208,
       78.51274658730455,
       51.819827073133105,
       53.42278452381478,
       53.422738459918,
       51.75607039908679,
       62.001453641360676,
       42.92742694529331,
       68.58584135289685,
       46.66443232293005,
       68.16384116098857,
       38.34544124627826,
       31.20273595871764,
       27.323905190808414,
       56.617993483509274,
       68.92576585869216,
       62.215076798876005,
       61.34327965502107,
       48.54367126524983,
       40.64699685489418,
       42.75731848485927,
       67.1782648624562,
       50.46422048465317,
       59.83259879780485,
       37.325360881706025,
       55.645098256737505,
       48.7801999216772,
       24.005461739996765,
       57.33611418698089,
       72.84046044404386,
       40.743937023504806,
       59.85788045136665,
       61.69043225872727,
       32.31311226975054,
       68.56315934206714,
       68.65898373824773,
       36.8374123186302,
       74.46565074782592,
       39.65394340358542,
       54.86972495903402,
       44.58065908846266,
       47.956813803570604,
       34.019063604924334,
       41.05348547653453,
       30.370791941318004,
       26.90168191355642,
       35.11900834645493,
       52.517092366666624,
       72.40567580376829,
       44.09908305126725,
       60.349907162703026,
       67.15666442569533,
       77.87064743336283,
       83.86597280202058,
       53.197688677796954,
       35.019680881523975,
       43.91017706731325,
       33.49847150430326,
       57.398198341415316,
       49.967748966193106,
       45.6981287917292,
       60.33602491469342,
       59.52150604012553,
       48.00134417059005,
       68.50320714390719,
       38.225185760117846,
       30.862668849987642,
       62.194578812002,
       52.23400184293856,
       48.15787518427799,
       44.92686859041839,
       28.23088389543388,
       66.5923921751929,
       52.45429203578806,
       55.712209320729876,
       59.82742769772051,
       45.44756506362626,
       27.125849557452113,
       47.66805429304588,
       25.586908886672898,
       52.27148300632806,
       37.81635745580541,
       60.3269612891344,
       42.46941118243493,
       33.280511113869444,
       64.64973829354507,
       45.22065943706664,
       40.92665066741565,
       32.30949650677031,
       23.702994813674135,
       30.10766897716748,
       48.00809654558759,
       59.77838697321133,
       47.75056808098609,
       22.104289988590384,
       31.523280502872556,
       43.72957063513951,
       27.45584607797375,
       24.785202257263432,
       58.98389197055689,
       35.843928206481344,
       65.95694463131598,
       53.96930408775638,
       65.27345001190457,
       53.04798148233065,
       31.226847653514014,
       19.548797480511652,
       19.65927413100528,
       16.863006613819554,
       58.005523303520384,
       27.802772325210146,
       65.80715113244419,
       43.466835940298175,
       62.17782568519914,
       44.76628085530613,
       42.44193522442571,
       67.70268569742531,
       72.48773883116405,
       76.62866843492024,
       66.59897196437517,
       66.54196993673938,
       53.449159499151705,
       29.841154061119823,
       66.2667574597912,
       59.09542602970825,
       39.05292188692632,
       53.221171334166776,
       53.952189989752306,
       74.32475054304696,
       49.06094490743021,
       37.20319035749399,
       45.933500951531066,
       66.36922781452027,
       44.6185883444147,
       70.17049580571513,
       45.26332925748944,
       66.17007830280842,
       66.81094312179793,
       76.67031158856737,
       64.99805241954375,
       49.081686130752864,
       66.75420163096761,
       31.971590737232493,
       65.59920974711058,
       35.84235952544704,
       29.871936301827784,
       50.72665116382604,
       74.39561864755052,
       31.930663884538404,
       25.84587649383379,
       54.08307159150801,
       25.657997235515456,
       56.52384799804645,
       70.52507094731887,
       63.45091937401903,
       43.597321907846094,
       36.05215661372571,
       75.44761564216145,
       38.39297405514003,
       58.55036061732994,
       51.51485032165592,
       67.89460009093841,
       54.625541543448946,
       74.63487406812025,
       46.5916621802869,
       29.97949033858352,
       34.64790751314621,
       61.00538893150907,
       66.6127383936742,
       70.32923179724446,
       29.660007702704746,
       60.17228736739688,
       38.908135300699115,
       35.93834087135098,
       51.545302479759215,
       29.80825273472169,
       39.872022568990985,
       58.358917050129456,
       44.95704929523922,
       57.778089626801396,
       77.98465726248544,
       51.98848508802515,
       62.2896917514632,
       40.772372486833824,
       54.0716817113612,
       50.335496833838526,
       42.755426584955934,
       31.48785426725568,
       41.36168746901496,
       20.31942665339358,
       58.124042966893114,
       59.53047133916257,
       34.77015194346529,
       58.38270770069861,
       55.14755780658746,
       58.07089983035734,
       47.557606612995386,
       69.31011171147317,
       60.000165228872824,
       38.82556287124782,
       34.03400103824236,
       46.188725806628476,
       58.47730358417619,
       60.523514342481896,
       74.1726556273005,
       81.00243343886522,
       44.097549897203216,
       52.837129862820895,
       72.21142191512486,
       55.64428424965632,
       35.080755869307005,
       50.4808494433588,
       39.42056286620014,
       71.31902230495162,
       73.9082644090047,
       37.927702445230416,
       41.48819917129979,
       27.616942927931788,
       55.99798752050905,
       46.070467578327225,
       37.53304612773332,
       34.14763701588362,
       21.89216574901541,
       62.51288229469068,
       38.89627915071315,
       71.485300962289,
       30.637394385186024,
       28.161553043508434,
       58.90580689368128,
       24.461025859639307,
       52.68690328167819,
       27.70897233879349,
       50.73067669221908,
       60.751911467521474,
       68.79352669369113,
       59.04210424753794,
       58.94884045311421,
       70.88973526435431,
       31.42607036434437,
       41.3501464858005,
       68.2024115892823,
       44.3473446086392,
       51.419916292696094,
       51.55950770764534,
       44.29456068230051,
       41.6923011594437,
       23.250902100084232,
       60.10335963140738,
       34.36032050620874,
       52.18022380944536,
       43.93675278812086,
       73.29226719076031,
       37.17263953482941,
       34.14025627703692,
       23.155707358498393,
       23.028860623413525,
       61.689889518803476,
       62.0954500873464,
       31.933378695956502,
       22.818886008492047,
       58.8153656467463,
       52.06069678489894,
       60.986752801971456,
       61.046045761600716,
       62.57395741994847,
       43.90963700564881,
       39.063170062607355,
       62.09662902636061,
       54.13120201147844,
       44.68341254391191,
       53.981284830949726,
       65.10292139388116,
       61.91216261217952,
       73.67875737726277,
       35.3438064065838,
       51.98874963616129,
       35.51348352244039,
       69.6101078870358,
       44.80966261312031,
       54.34992116208631,
       38.86074353823187,
       49.35394222599379,
       43.57576392064863,
       40.35630632729746,
       21.061400999186002,
       23.609127943917752,
       13.82548677572562,
       14.99355947427609,
       49.49504870177723,
       30.964330305789755,
       64.13171473817975,
       68.18048347507239,
       37.53836316514806,
       62.96499981765891,
       63.93936447081998,
       42.415238712085944,
       55.83479946516368,
       66.19123957798871,
       67.87498840336896,
       39.27694754815484,
       56.85437496236267,
       48.16525339593488,
       48.04312991124535,
       51.49160611851048,
       29.001840590738592,
       60.6737802207659,
       63.89335834449725,
       54.34608224177836,
       79.6847882387749,
       48.29012315723401,
       68.18164286620737,
       41.28318771738989,
       46.183661816299455,
       38.28001088452152,
       34.885357618692716,
       29.13218811959216,
       56.502427607118456,
       66.68819259424446,
       54.914511326316834,
       58.90879211303156,
       60.78938826227318,
       31.99657808419181,
       35.565023775963915,
       59.67022696986283,
       26.3754868736989,
       66.79617006294554,
       38.711234076161,
       66.93643991192717,
       58.62673012600887,
       47.046874433465575,
       70.86968064206688,
       43.901863795093256,
       63.3303461503265,
       42.80424123843064,
       31.820077440206926,
       40.02659364497533,
       59.18327828940256,
       54.38128229241943,
       66.85308944368258,
       64.10784266267035,
       33.98414739727253,
       24.34444109129744,
       61.842436369382504,
       56.20392014392849,
       60.98550477934706,
       54.41431894745775,
       62.10985688355415,
       73.77211182946546,
       60.29517981979913,
       35.64367611230214,
       66.75803701473549,
       29.201965634468923,
       30.602296609501323,
       17.06538280281202,
       20.00354773000556,
       50.8119935279281,
       71.64639423244824,
       74.15336033762789,
       49.440140822183075,
       64.67196379999837,
       75.94519925730934,
       38.447796503325556,
       44.163722787003344,
       64.87385846784939,
       50.04838037165882,
       34.35659985575274,
       54.56307512952788,
       60.70215078607142,
       68.95664033863429,
       31.492181267677655,
       61.75408422428174,
       40.271834072820916,
       30.165026083359038,
       53.637973784687034,
       33.37471211504639,
       53.523357403836734,
       60.71066894628964,
       41.89042081202545,
       67.27223306905721,
       36.56733776280036,
       38.571707875936625,
       48.87939192344522,
       71.13443507120998,
       35.30212528572657,
       48.28329608731827,
       23.88640846801915,
       31.44989351178334,
       58.26799060998704,
       51.71798934171935,
       27.46874099876527,
       37.212786078532126,
       20.15089957530152,
       64.63216131311296,
       40.167956134402836,
       35.72293337343854,
       38.43793061319493,
       28.58001732805514,
       53.9835600631773,
       60.28246979279593,
       46.72253103459036,
       64.32013214011862,
       59.37791335394144,
       50.690079722681766,
       33.8546939485445,
       65.31499790187449,
       27.753239769540784,
       22.711934184745065,
       46.00574353839934,
       58.15825158745324,
       57.86804939856225,
       23.41412337154698,
       32.95241917021132,
       63.815475372288574,
       55.62396229117605,
       45.28041710250443,
       62.15398484075666,
       58.043186108695494,
       40.30632798778294,
       47.21058269778084,
       62.58459040390571,
       66.90956867320287,
       51.01786126295233,
       64.6649170189233,
       46.31786614043111,
       61.28335728379005,
       40.45946071693337,
       33.900639524785966,
       36.393766884624874,
       26.301756416204544,
       52.32002489201818,
       64.64275133807244,
       56.813171057224984,
       79.91914660463216,
       50.398959896432785,
       50.398782335656556,
       67.33925008338416,
       74.42232202006592,
       81.16683164095893,
       85.48454980324522,
       75.53670890824829,
       33.14472677392582,
       29.26549963624277,
       69.60541140995797,
       74.54378383387971,
       35.52310555670123,
       54.53681022344028,
       32.546906329761505,
       36.4755644024657,
       63.25254739044657,
       35.16510433232886,
       35.18170585179619,
       36.241878856468176,
       67.3178749926577,
       41.91742243253389,
       56.701749664340035,
       74.34640056803384,
       49.29978655009208,
       54.22384418179581,
       32.44031752116758,
       25.377207408748927,
       41.93448516537484,
       38.53587202172279,
       21.084637179458607,
       28.980275630948963,
       60.1146088836436,
       58.416570253687716,
       45.823510782508635,
       50.50220988728048,
       57.85232793896362,
       62.92435803993775,
       64.89961484195774,
       73.07650128211411,
       72.68438828027227,
       73.56619990234888,
       43.5513389537202,
       63.51386220075432,
       49.909651034931755,
       37.65619985685482,
       70.06396688880494,
       63.14810305447157,
       61.96443533608123,
       39.68657992600262,
       33.182852508252175,
       25.093920736850595,
       51.90887572183953,
       72.42381510321478,
       54.20385662007917,
       54.34187593144975,
       44.937999134605285,
       54.31594067600298,
       30.09535373212172,
       71.58812954501816,
       49.614159436065826,
       52.91395480106467,
       51.24733124168494,
       67.36775518322456,
       28.303781691771473,
       53.7736736671824,
       32.088982346984444,
       24.4106594958279,
       32.156113190157704,
       18.494048922202072,
       60.340323572902236,
       27.323343419713776,
       58.28295648695687,
       37.51254035787235,
       66.55904262268373,
       69.48140665121845,
       69.31840915818609,
       53.692703839314184,
       59.11430037118166,
       63.31115520847023,
       48.578369742303416,
       40.28209612440916,
       75.2880691275336,
       67.82586775915847,
       38.430645809385204,
       30.495652761139855,
       51.106346654740356,
       26.64495453746279,
       51.09749596769989,
       72.63563973331375,
       75.82304958202444,
       46.3490464122414,
       47.812804679864996,
       39.57974052448169,
       51.750631209784,
       69.60064813730948,
       31.22295180880805,
       62.42502200662698,
       30.60811823356205,
       53.05555302913164,
       53.83287440264228,
       75.57251623519181,
       76.50637127668176,
       76.06060827764576,
       51.3655670212784,
       63.63801340124146,
       40.319689452356904,
       70.02891429368366,
       66.06386067452607,
       42.252720992232305,
       34.24841533857079,
       36.376679531080605,
       54.55102854034987,
       36.78285753529732,
       46.73655307838329,
       38.615466140378004,
       29.479642445003027,
       29.761014867363475,
       52.60675328218218,
       32.55219134474758,
       59.77803296982396,
       45.59065789671214,
       53.94680525032745,
       73.50100161749104,
       72.49097871650639,
       35.361931643355916,
       64.45719338089116,
       52.41627201273398,
       49.68227101679576,
       42.706949313060846,
       53.117346077289675,
       59.87801028788769,
       28.89375247231389,
       27.577759755570256,
       23.68631731972518,
       16.664844560349213,
       22.850201994765346,
       14.029753540761208,
       54.8298381198594,
       53.497792969702566,
       55.603027879647414,
       55.53120566468814,
       61.798598002926134,
       41.85990784690475,
       52.84701560494096,
       54.07401834916939,
       68.3127290482255,
       66.52357042608257,
       42.765317641203154,
       34.69499284279583,
       64.44302347371045,
       53.663570696890815,
       40.551394060844494,
       65.13936811392601,
       71.89174769591384,
       48.757199583536725,
       69.33672146969413,
       45.13764467976475,
       68.75302859440954,
       50.401067808254076,
       41.44248353606709,
       76.79184516710102,
       74.80161005457285,
       49.97526927366369,
       58.71537704435041,
       64.32939712180996,
       39.358182501345034,
       33.95441443343755,
       28.640999536205907,
       22.65653206515326,
       51.07361768481183,
       24.939218620739677,
       48.68054711460346,
       35.15492950770412,
       56.43526200441496,
       62.33190291845489,
       25.39237904664356,
       35.343889621985475,
       27.553038212688694,
       20.93637641067407,
       43.2540799100173,
       19.10365300983482,
       56.60148890765638,
       63.876046230961414,
       27.998909667746034,
       28.593618331255,
       55.417316114556535,
       63.391665949758,
       40.80075761570583,
       52.67735219880024,
       34.007906520886614,
       37.31469428346533,
       63.971425647137046,
       54.20446202687712,
       38.8583452847449,
       39.359011599521075,
       57.214767487159065,
       58.32100171312998,
       37.45280207634567,
       35.988649402453184,
       33.04050938956805,
       61.792827412428124,
       33.47401442000429,
       31.451691052864277,
       39.24636353478838,
       60.216781256819644,
       24.44120346774579,
       51.255987518261605,
       30.378552028358303,
       54.193389650204246,
       25.721729593680703,
       40.66905950471011,
       51.38185578732759,
       23.926791012597832,
       31.762899458255717,
       43.40522038693413,
       21.476052973509066,
       63.89441388706746,
       62.35839168842339,
       64.02774409242006,
       null,
       71.55287613005031,
       67.14648674604739,
       48.12279696008046,
       40.059162834610355,
       48.6715943092218,
       70.8033875854626,
       63.359285489180934,
       53.047249165375966,
       64.79754610529612,
       62.07034307349304,
       75.38854869336876,
       82.33097713725498,
       49.735430453767094,
       39.216688604261975,
       61.04834208466599,
       49.50174048770927,
       72.19012896274874,
       36.87514486166956,
       54.38610347368249,
       40.158321525477795,
       42.404921997166966,
       68.2791437130498,
       29.68681759542387,
       null
      ],
      "yaxis": "y2"
     },
     {
      "hovertemplate": "<br>%{text}",
      "marker": {
       "color": "red",
       "symbol": "triangle-up"
      },
      "mode": "markers",
      "name": "\u4e70\u5165\u6210\u4ea4",
      "text": [
       "\u8d44\u4ea7:99.8\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 1995\u624b \u4ef7\u683c:4.56 \u6210\u4ea4\u989d:91.0\u4e07",
       "\u8d44\u4ea7:113.2\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2104\u624b \u4ef7\u683c:4.83 \u6210\u4ea4\u989d:101.6\u4e07",
       "\u8d44\u4ea7:110.5\u4e07<br>\u5206\u7ea2\u914d\u80a1:\u6d77\u5b81\u76ae\u57ce 9\u624b \u4ef7\u683c:4.81 \u6210\u4ea4\u989d:0.4\u4e07",
       "\u8d44\u4ea7:109.9\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2392\u624b \u4ef7\u683c:4.18 \u6210\u4ea4\u989d:100.1\u4e07",
       "\u8d44\u4ea7:115.8\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2563\u624b \u4ef7\u683c:4.07 \u6210\u4ea4\u989d:104.4\u4e07",
       "\u8d44\u4ea7:125.3\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2963\u624b \u4ef7\u683c:3.63 \u6210\u4ea4\u989d:107.6\u4e07",
       "\u8d44\u4ea7:127.7\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 362\u624b \u4ef7\u683c:3.79 \u6210\u4ea4\u989d:13.7\u4e07",
       "\u8d44\u4ea7:137.5\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2938\u624b \u4ef7\u683c:4.30 \u6210\u4ea4\u989d:126.4\u4e07",
       "\u8d44\u4ea7:133.4\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 276\u624b \u4ef7\u683c:4.10 \u6210\u4ea4\u989d:11.3\u4e07",
       "\u8d44\u4ea7:129.2\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 30\u624b \u4ef7\u683c:3.98 \u6210\u4ea4\u989d:1.2\u4e07",
       "\u8d44\u4ea7:130.0\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2980\u624b \u4ef7\u683c:4.07 \u6210\u4ea4\u989d:121.2\u4e07",
       "\u8d44\u4ea7:127.6\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 242\u624b \u4ef7\u683c:3.92 \u6210\u4ea4\u989d:9.5\u4e07",
       "\u8d44\u4ea7:122.8\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 23\u624b \u4ef7\u683c:3.82 \u6210\u4ea4\u989d:0.9\u4e07",
       "\u8d44\u4ea7:120.8\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 3\u624b \u4ef7\u683c:3.78 \u6210\u4ea4\u989d:0.1\u4e07",
       "\u8d44\u4ea7:126.6\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2551\u624b \u4ef7\u683c:4.45 \u6210\u4ea4\u989d:113.5\u4e07",
       "\u8d44\u4ea7:135.1\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2825\u624b \u4ef7\u683c:4.38 \u6210\u4ea4\u989d:123.7\u4e07",
       "\u8d44\u4ea7:120.1\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 289\u624b \u4ef7\u683c:4.03 \u6210\u4ea4\u989d:11.6\u4e07",
       "\u8d44\u4ea7:121.6\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 26\u624b \u4ef7\u683c:3.74 \u6210\u4ea4\u989d:1.0\u4e07",
       "\u8d44\u4ea7:140.5\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 3028\u624b \u4ef7\u683c:4.28 \u6210\u4ea4\u989d:129.6\u4e07",
       "\u8d44\u4ea7:141.8\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 289\u624b \u4ef7\u683c:4.22 \u6210\u4ea4\u989d:12.2\u4e07",
       "\u8d44\u4ea7:152.9\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 3108\u624b \u4ef7\u683c:4.44 \u6210\u4ea4\u989d:138.1\u4e07",
       "\u8d44\u4ea7:156.2\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2968\u624b \u4ef7\u683c:4.95 \u6210\u4ea4\u989d:146.9\u4e07",
       "\u8d44\u4ea7:153.5\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 271\u624b \u4ef7\u683c:4.79 \u6210\u4ea4\u989d:13.0\u4e07",
       "\u8d44\u4ea7:149.9\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 25\u624b \u4ef7\u683c:4.71 \u6210\u4ea4\u989d:1.2\u4e07",
       "\u8d44\u4ea7:152.9\u4e07<br>\u4e70\u5165:\u6d77\u5b81\u76ae\u57ce 2\u624b \u4ef7\u683c:4.60 \u6210\u4ea4\u989d:0.1\u4e07"
      ],
      "type": "scatter",
      "x": [
       "2019-01-24",
       "2019-05-07",
       "2019-06-21",
       "2019-08-07",
       "2019-11-12",
       "2020-02-04",
       "2020-05-25",
       "2020-09-25",
       "2020-10-29",
       "2020-11-02",
       "2021-07-02",
       "2021-07-09",
       "2021-07-27",
       "2021-07-28",
       "2021-11-30",
       "2022-04-22",
       "2022-04-26",
       "2022-04-27",
       "2022-08-03",
       "2022-08-04",
       "2023-03-15",
       "2023-06-20",
       "2023-06-21",
       "2023-06-26",
       "2023-06-27"
      ],
      "xaxis": "x",
      "y": [
       0.9978685514154816,
       1.1320532897787476,
       1.1051376238865662,
       1.0985140666142275,
       1.1578906351439429,
       1.2534296754548406,
       1.2774750968357609,
       1.37502296671926,
       1.3341499993514012,
       1.2923662092941806,
       1.2999218346221681,
       1.2763096027758547,
       1.2278862829293962,
       1.2083981129379795,
       1.2657202604760505,
       1.3507111524747228,
       1.2005496764112615,
       1.216456216386466,
       1.405343502747245,
       1.4180152188107062,
       1.528612798051276,
       1.561630645797858,
       1.535428176831641,
       1.4994974067362736,
       1.528889266755347
      ],
      "yaxis": "y"
     },
     {
      "hovertemplate": "<br>%{text}",
      "marker": {
       "color": "green",
       "symbol": "triangle-down"
      },
      "mode": "markers",
      "name": "\u5356\u51fa\u6210\u4ea4",
      "text": [
       "\u8d44\u4ea7:111.5\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 1995\u624b \u4ef7\u683c:5.14 \u6210\u4ea4\u989d:102.5\u4e07",
       "\u8d44\u4ea7:110.5\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2104\u624b \u4ef7\u683c:4.76 \u6210\u4ea4\u989d:100.2\u4e07",
       "\u8d44\u4ea7:115.4\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 9\u624b \u4ef7\u683c:4.39 \u6210\u4ea4\u989d:0.4\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2392\u624b \u4ef7\u683c:4.39 \u6210\u4ea4\u989d:105.0\u4e07",
       "\u8d44\u4ea7:122.7\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2563\u624b \u4ef7\u683c:4.36 \u6210\u4ea4\u989d:111.7\u4e07",
       "\u8d44\u4ea7:139.0\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2963\u624b \u4ef7\u683c:4.14 \u6210\u4ea4\u989d:122.7\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 362\u624b \u4ef7\u683c:4.14 \u6210\u4ea4\u989d:15.0\u4e07",
       "\u8d44\u4ea7:131.8\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2938\u624b \u4ef7\u683c:4.06 \u6210\u4ea4\u989d:119.2\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 276\u624b \u4ef7\u683c:4.06 \u6210\u4ea4\u989d:11.2\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 30\u624b \u4ef7\u683c:4.06 \u6210\u4ea4\u989d:1.2\u4e07",
       "\u8d44\u4ea7:125.1\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2980\u624b \u4ef7\u683c:3.85 \u6210\u4ea4\u989d:114.7\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 242\u624b \u4ef7\u683c:3.85 \u6210\u4ea4\u989d:9.3\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 23\u624b \u4ef7\u683c:3.85 \u6210\u4ea4\u989d:0.9\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 3\u624b \u4ef7\u683c:3.85 \u6210\u4ea4\u989d:0.1\u4e07",
       "\u8d44\u4ea7:136.5\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2551\u624b \u4ef7\u683c:4.90 \u6210\u4ea4\u989d:125.0\u4e07",
       "\u8d44\u4ea7:143.0\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2825\u624b \u4ef7\u683c:4.55 \u6210\u4ea4\u989d:128.5\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 289\u624b \u4ef7\u683c:4.55 \u6210\u4ea4\u989d:13.1\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 26\u624b \u4ef7\u683c:4.55 \u6210\u4ea4\u989d:1.2\u4e07",
       "\u8d44\u4ea7:151.4\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 3028\u624b \u4ef7\u683c:4.53 \u6210\u4ea4\u989d:137.2\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 289\u624b \u4ef7\u683c:4.53 \u6210\u4ea4\u989d:13.1\u4e07",
       "\u8d44\u4ea7:161.2\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 3108\u624b \u4ef7\u683c:4.76 \u6210\u4ea4\u989d:147.9\u4e07",
       "\u8d44\u4ea7:169.5\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2968\u624b \u4ef7\u683c:5.19 \u6210\u4ea4\u989d:154.0\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 271\u624b \u4ef7\u683c:5.19 \u6210\u4ea4\u989d:14.1\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 25\u624b \u4ef7\u683c:5.19 \u6210\u4ea4\u989d:1.3\u4e07<br>\u5356\u51fa:\u6d77\u5b81\u76ae\u57ce 2\u624b \u4ef7\u683c:5.19 \u6210\u4ea4\u989d:0.1\u4e07"
      ],
      "type": "scatter",
      "x": [
       "2019-02-26",
       "2019-06-21",
       "2019-10-15",
       "2019-12-31",
       "2020-06-02",
       "2020-12-29",
       "2021-08-10",
       "2021-12-22",
       "2022-05-31",
       "2022-12-07",
       "2023-04-03",
       "2023-07-31"
      ],
      "xaxis": "x",
      "y": [
       1.1154197147793579,
       1.1051376238865662,
       1.1535652485265875,
       1.2269240493636846,
       1.3903185724422213,
       1.3175297353860231,
       1.2505844918195868,
       1.3650217848042823,
       1.429761966277213,
       1.5139828984041357,
       1.6123069591877508,
       1.695201025443549
      ],
      "yaxis": "y"
     }
    ],
    "layout": {
     "annotations": [
      {
       "font": {
        "size": 16
       },
       "showarrow": false,
       "text": "\u8d44\u4ea7\u66f2\u7ebf",
       "x": 0.34875,
       "xanchor": "center",
       "xref": "paper",
       "y": 1,
       "yanchor": "bottom",
       "yref": "paper"
      },
      {
       "font": {
        "size": 16
       },
       "showarrow": false,
       "text": "\u7b56\u7565\u6307\u6807",
       "x": 0.82375,
       "xanchor": "center",
       "xref": "paper",
       "y": 1,
       "yanchor": "bottom",
       "yref": "paper"
      }
     ],
     "height": 435,
     "hoverlabel": {
      "bgcolor": "rgba(255,255,255,0.8)"
     },
     "hovermode": "x unified",
     "margin": {
      "b": 50,
      "l": 20,
      "r": 20,
      "t": 50
     },
     "template": {
      "data": {
       "bar": [
        {
         "error_x": {
          "color": "#2a3f5f"
         },
         "error_y": {
          "color": "#2a3f5f"
         },
         "marker": {
          "line": {
           "color": "#E5ECF6",
           "width": 0.5
          },
          "pattern": {
           "fillmode": "overlay",
           "size": 10,
           "solidity": 0.2
          }
         },
         "type": "bar"
        }
       ],
       "barpolar": [
        {
         "marker": {
          "line": {
           "color": "#E5ECF6",
           "width": 0.5
          },
          "pattern": {
           "fillmode": "overlay",
           "size": 10,
           "solidity": 0.2
          }
         },
         "type": "barpolar"
        }
       ],
       "carpet": [
        {
         "aaxis": {
          "endlinecolor": "#2a3f5f",
          "gridcolor": "white",
          "linecolor": "white",
          "minorgridcolor": "white",
          "startlinecolor": "#2a3f5f"
         },
         "baxis": {
          "endlinecolor": "#2a3f5f",
          "gridcolor": "white",
          "linecolor": "white",
          "minorgridcolor": "white",
          "startlinecolor": "#2a3f5f"
         },
         "type": "carpet"
        }
       ],
       "choropleth": [
        {
         "colorbar": {
          "outlinewidth": 0,
          "ticks": ""
         },
         "type": "choropleth"
        }
       ],
       "contour": [
        {
         "colorbar": {
          "outlinewidth": 0,
          "ticks": ""
         },
         "colorscale": [
          [
           0,
           "#0d0887"
          ],
          [
           0.1111111111111111,
           "#46039f"
          ],
          [
           0.2222222222222222,
           "#7201a8"
          ],
          [
           0.3333333333333333,
           "#9c179e"
          ],
          [
           0.4444444444444444,
           "#bd3786"
          ],
          [
           0.5555555555555556,
           "#d8576b"
          ],
          [
           0.6666666666666666,
           "#ed7953"
          ],
          [
           0.7777777777777778,
           "#fb9f3a"
          ],
          [
           0.8888888888888888,
           "#fdca26"
          ],
          [
           1,
           "#f0f921"
          ]
         ],
         "type": "contour"
        }
       ],
       "contourcarpet": [
        {
         "colorbar": {
          "outlinewidth": 0,
          "ticks": ""
         },
         "type": "contourcarpet"
        }
       ],
       "heatmap": [
        {
         "colorbar": {
          "outlinewidth": 0,
          "ticks": ""
         },
         "colorscale": [
          [
           0,
           "#0d0887"
          ],
          [
           0.1111111111111111,
           "#46039f"
          ],
          [
           0.2222222222222222,
           "#7201a8"
          ],
          [
           0.3333333333333333,
           "#9c179e"
          ],
          [
           0.4444444444444444,
           "#bd3786"
          ],
          [
           0.5555555555555556,
           "#d8576b"
          ],
          [
           0.6666666666666666,
           "#ed7953"
          ],
          [
           0.7777777777777778,
           "#fb9f3a"
          ],
          [
           0.8888888888888888,
           "#fdca26"
          ],
          [
           1,
           "#f0f921"
          ]
         ],
         "type": "heatmap"
        }
       ],
       "heatmapgl": [
        {
         "colorbar": {
          "outlinewidth": 0,
          "ticks": ""
         },
         "colorscale": [
          [
           0,
           "#0d0887"
          ],
          [
           0.1111111111111111,
           "#46039f"
          ],
          [
           0.2222222222222222,
           "#7201a8"
          ],
          [
           0.3333333333333333,
           "#9c179e"
          ],
          [
           0.4444444444444444,
           "#bd3786"
          ],
          [
           0.5555555555555556,
           "#d8576b"
          ],
          [
           0.6666666666666666,
           "#ed7953"
          ],
          [
           0.7777777777777778,
           "#fb9f3a"
          ],
          [
           0.8888888888888888,
           "#fdca26"
          ],
          [
           1,
           "#f0f921"
          ]
         ],
         "type": "heatmapgl"
        }
       ],
       "histogram": [
        {
         "marker": {
          "pattern": {
           "fillmode": "overlay",
           "size": 10,
           "solidity": 0.2
          }
         },
         "type": "histogram"
        }
       ],
       "histogram2d": [
        {
         "colorbar": {
          "outlinewidth": 0,
          "ticks": ""
         },
         "colorscale": [
          [
           0,
           "#0d0887"
          ],
          [
           0.1111111111111111,
           "#46039f"
          ],
          [
           0.2222222222222222,
           "#7201a8"
          ],
          [
           0.3333333333333333,
           "#9c179e"
          ],
          [
           0.4444444444444444,
           "#bd3786"
          ],
          [
           0.5555555555555556,
           "#d8576b"
          ],
          [
           0.6666666666666666,
           "#ed7953"
          ],
          [
           0.7777777777777778,
           "#fb9f3a"
          ],
          [
           0.8888888888888888,
           "#fdca26"
          ],
          [
           1,
           "#f0f921"
          ]
         ],
         "type": "histogram2d"
        }
       ],
       "histogram2dcontour": [
        {
         "colorbar": {
          "outlinewidth": 0,
          "ticks": ""
         },
         "colorscale": [
          [
           0,
           "#0d0887"
          ],
          [
           0.1111111111111111,
           "#46039f"
          ],
          [
           0.2222222222222222,
           "#7201a8"
          ],
          [
           0.3333333333333333,
           "#9c179e"
          ],
          [
           0.4444444444444444,
           "#bd3786"
          ],
          [
           0.5555555555555556,
           "#d8576b"
          ],
          [
           0.6666666666666666,
           "#ed7953"
          ],
          [
           0.7777777777777778,
           "#fb9f3a"
          ],
          [
           0.8888888888888888,
           "#fdca26"
          ],
          [
           1,
           "#f0f921"
          ]
         ],
         "type": "histogram2dcontour"
        }
       ],
       "mesh3d": [
        {
         "colorbar": {
          "outlinewidth": 0,
          "ticks": ""
         },
         "type": "mesh3d"
        }
       ],
       "parcoords": [
        {
         "line": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "type": "parcoords"
        }
       ],
       "pie": [
        {
         "automargin": true,
         "type": "pie"
        }
       ],
       "scatter": [
        {
         "fillpattern": {
          "fillmode": "overlay",
          "size": 10,
          "solidity": 0.2
         },
         "type": "scatter"
        }
       ],
       "scatter3d": [
        {
         "line": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "marker": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "type": "scatter3d"
        }
       ],
       "scattercarpet": [
        {
         "marker": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "type": "scattercarpet"
        }
       ],
       "scattergeo": [
        {
         "marker": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "type": "scattergeo"
        }
       ],
       "scattergl": [
        {
         "marker": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "type": "scattergl"
        }
       ],
       "scattermapbox": [
        {
         "marker": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "type": "scattermapbox"
        }
       ],
       "scatterpolar": [
        {
         "marker": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "type": "scatterpolar"
        }
       ],
       "scatterpolargl": [
        {
         "marker": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "type": "scatterpolargl"
        }
       ],
       "scatterternary": [
        {
         "marker": {
          "colorbar": {
           "outlinewidth": 0,
           "ticks": ""
          }
         },
         "type": "scatterternary"
        }
       ],
       "surface": [
        {
         "colorbar": {
          "outlinewidth": 0,
          "ticks": ""
         },
         "colorscale": [
          [
           0,
           "#0d0887"
          ],
          [
           0.1111111111111111,
           "#46039f"
          ],
          [
           0.2222222222222222,
           "#7201a8"
          ],
          [
           0.3333333333333333,
           "#9c179e"
          ],
          [
           0.4444444444444444,
           "#bd3786"
          ],
          [
           0.5555555555555556,
           "#d8576b"
          ],
          [
           0.6666666666666666,
           "#ed7953"
          ],
          [
           0.7777777777777778,
           "#fb9f3a"
          ],
          [
           0.8888888888888888,
           "#fdca26"
          ],
          [
           1,
           "#f0f921"
          ]
         ],
         "type": "surface"
        }
       ],
       "table": [
        {
         "cells": {
          "fill": {
           "color": "#EBF0F8"
          },
          "line": {
           "color": "white"
          }
         },
         "header": {
          "fill": {
           "color": "#C8D4E3"
          },
          "line": {
           "color": "white"
          }
         },
         "type": "table"
        }
       ]
      },
      "layout": {
       "annotationdefaults": {
        "arrowcolor": "#2a3f5f",
        "arrowhead": 0,
        "arrowwidth": 1
       },
       "autotypenumbers": "strict",
       "coloraxis": {
        "colorbar": {
         "outlinewidth": 0,
         "ticks": ""
        }
       },
       "colorscale": {
        "diverging": [
         [
          0,
          "#8e0152"
         ],
         [
          0.1,
          "#c51b7d"
         ],
         [
          0.2,
          "#de77ae"
         ],
         [
          0.3,
          "#f1b6da"
         ],
         [
          0.4,
          "#fde0ef"
         ],
         [
          0.5,
          "#f7f7f7"
         ],
         [
          0.6,
          "#e6f5d0"
         ],
         [
          0.7,
          "#b8e186"
         ],
         [
          0.8,
          "#7fbc41"
         ],
         [
          0.9,
          "#4d9221"
         ],
         [
          1,
          "#276419"
         ]
        ],
        "sequential": [
         [
          0,
          "#0d0887"
         ],
         [
          0.1111111111111111,
          "#46039f"
         ],
         [
          0.2222222222222222,
          "#7201a8"
         ],
         [
          0.3333333333333333,
          "#9c179e"
         ],
         [
          0.4444444444444444,
          "#bd3786"
         ],
         [
          0.5555555555555556,
          "#d8576b"
         ],
         [
          0.6666666666666666,
          "#ed7953"
         ],
         [
          0.7777777777777778,
          "#fb9f3a"
         ],
         [
          0.8888888888888888,
          "#fdca26"
         ],
         [
          1,
          "#f0f921"
         ]
        ],
        "sequentialminus": [
         [
          0,
          "#0d0887"
         ],
         [
          0.1111111111111111,
          "#46039f"
         ],
         [
          0.2222222222222222,
          "#7201a8"
         ],
         [
          0.3333333333333333,
          "#9c179e"
         ],
         [
          0.4444444444444444,
          "#bd3786"
         ],
         [
          0.5555555555555556,
          "#d8576b"
         ],
         [
          0.6666666666666666,
          "#ed7953"
         ],
         [
          0.7777777777777778,
          "#fb9f3a"
         ],
         [
          0.8888888888888888,
          "#fdca26"
         ],
         [
          1,
          "#f0f921"
         ]
        ]
       },
       "colorway": [
        "#636efa",
        "#EF553B",
        "#00cc96",
        "#ab63fa",
        "#FFA15A",
        "#19d3f3",
        "#FF6692",
        "#B6E880",
        "#FF97FF",
        "#FECB52"
       ],
       "font": {
        "color": "#2a3f5f"
       },
       "geo": {
        "bgcolor": "white",
        "lakecolor": "white",
        "landcolor": "#E5ECF6",
        "showlakes": true,
        "showland": true,
        "subunitcolor": "white"
       },
       "hoverlabel": {
        "align": "left"
       },
       "hovermode": "closest",
       "mapbox": {
        "style": "light"
       },
       "paper_bgcolor": "white",
       "plot_bgcolor": "#E5ECF6",
       "polar": {
        "angularaxis": {
         "gridcolor": "white",
         "linecolor": "white",
         "ticks": ""
        },
        "bgcolor": "#E5ECF6",
        "radialaxis": {
         "gridcolor": "white",
         "linecolor": "white",
         "ticks": ""
        }
       },
       "scene": {
        "xaxis": {
         "backgroundcolor": "#E5ECF6",
         "gridcolor": "white",
         "gridwidth": 2,
         "linecolor": "white",
         "showbackground": true,
         "ticks": "",
         "zerolinecolor": "white"
        },
        "yaxis": {
         "backgroundcolor": "#E5ECF6",
         "gridcolor": "white",
         "gridwidth": 2,
         "linecolor": "white",
         "showbackground": true,
         "ticks": "",
         "zerolinecolor": "white"
        },
        "zaxis": {
         "backgroundcolor": "#E5ECF6",
         "gridcolor": "white",
         "gridwidth": 2,
         "linecolor": "white",
         "showbackground": true,
         "ticks": "",
         "zerolinecolor": "white"
        }
       },
       "shapedefaults": {
        "line": {
         "color": "#2a3f5f"
        }
       },
       "ternary": {
        "aaxis": {
         "gridcolor": "white",
         "linecolor": "white",
         "ticks": ""
        },
        "baxis": {
         "gridcolor": "white",
         "linecolor": "white",
         "ticks": ""
        },
        "bgcolor": "#E5ECF6",
        "caxis": {
         "gridcolor": "white",
         "linecolor": "white",
         "ticks": ""
        }
       },
       "title": {
        "x": 0.05
       },
       "xaxis": {
        "automargin": true,
        "gridcolor": "white",
        "linecolor": "white",
        "ticks": "",
        "title": {
         "standoff": 15
        },
        "zerolinecolor": "white",
        "zerolinewidth": 2
       },
       "yaxis": {
        "automargin": true,
        "gridcolor": "white",
        "linecolor": "white",
        "ticks": "",
        "title": {
         "standoff": 15
        },
        "zerolinecolor": "white",
        "zerolinewidth": 2
       }
      }
     },
     "width": 1040,
     "xaxis": {
      "anchor": "y",
      "domain": [
       0,
       0.6975
      ],
      "nticks": 239,
      "tickangle": 45,
      "type": "category"
     },
     "yaxis": {
      "anchor": "x",
      "domain": [
       0,
       1
      ]
     },
     "yaxis2": {
      "anchor": "x",
      "overlaying": "y",
      "side": "right"
     }
    }
   },
   "text/html": "<div>                            <div id=\"adb90121-2fa2-4210-a1e3-2ff3aa5f2a69\" class=\"plotly-graph-div\" style=\"height:435px; width:1040px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"adb90121-2fa2-4210-a1e3-2ff3aa5f2a69\")) {                    Plotly.newPlot(                        \"adb90121-2fa2-4210-a1e3-2ff3aa5f2a69\",                        [{\"cells\":{\"font\":{\"size\":10},\"values\":[[\"\\u8d77\\u59cb\\u65e5\",\"\\u7ed3\\u675f\\u65e5\",\"\\u8d44\\u4ea7\\u66b4\\u9732\\u7a97\\u53e3\",\"\\u4ea4\\u6613\\u6b21\\u6570\",\"\\u603b\\u5229\\u6da6\",\"\\u5229\\u6da6\\u7387\",\"\\u80dc\\u7387\",\"\\u65e5\\u5747\\u56de\\u62a5\",\"\\u590f\\u666e\\u7387\",\"\\u6700\\u5927\\u56de\\u64a4\",\"\\u5e74\\u5316\\u56de\\u62a5\",\"\\u6ce2\\u52a8\\u7387\",\"sortino\",\"calmar\"],[\"2019-01-24\",\"2023-07-31\",\"1095\",\"25\",\"695201.03\",\"69.52%\",\"76.00%\",\"0.05%\",\"0.63\",\"-24.93%\",\"12.92%\",\"16.62%\",\"0.96\",\"0.52\"],[\"2018-12-28\",\"2023-11-30\",\"1194\",\"-\",\"-\",\"-0.98%\",\"45.55%\",\"0.02%\",\"0.04\",\"-39.82%\",\"-0.21%\",\"29.76%\",\"0.06\",\"-0.01\"]]},\"header\":{\"values\":[\"\\u6307\\u6807\\u540d\",\"\\u7b56\\u7565\",\"\\u6d77\\u5b81\\u76ae\\u57ce\"]},\"type\":\"table\",\"domain\":{\"x\":[0.7075,0.94],\"y\":[0.0,1.0]}},{\"hovertemplate\":\"\\u003cbr\\u003e\\u51c0\\u503c:%{y:.2f}\\u003cbr\\u003e\\u6307\\u6807:%{text:.1f}\",\"mode\":\"lines\",\"name\":\"\\u6d77\\u5b81\\u76ae\\u57ce\",\"showlegend\":true,\"text\":[null,42.361421601213614,30.862929234020854,61.310995239994334,74.54242030517885,76.27335418265879,47.180969771689796,44.09527973483293,69.98552827363741,63.52155581140687,56.86748627309229,64.77238026215086,35.116576335212805,62.46357807613375,58.16168983045312,39.91198504235263,40.37688951102862,19.22038916749454,38.35068263701458,27.33903705199199,39.16433143620835,21.71793152578878,24.40306946833539,20.481540570243414,61.999002909024476,64.56777209474049,67.7366432174915,69.49069593916447,68.00814339127139,49.177033669224556,73.54720648941999,51.52365313281973,53.47747426342885,43.22189924406246,73.2513725672374,80.27099737108132,50.929248335254414,64.88922552461095,67.14108143752037,71.17422219290093,68.15245934775056,84.06544264723071,75.73614118194939,82.45962519779772,32.12712089742421,68.41798542180298,53.771423729871124,33.86536688077927,31.49279711778206,57.36697139286289,62.81528257680859,42.66459603020586,42.40767613639125,59.70548717168316,59.01028455760822,36.1338129444791,27.63642526425474,34.292564288916736,30.462799296134364,69.52851581816684,70.92409286875143,52.52787304245488,64.02303810383025,57.45011673806888,59.40555622548095,45.329699408521954,60.91028567933167,31.79497714531568,54.25485113795819,47.51494120309027,57.08365347699819,62.67149775561905,34.78920253313027,61.29584985875075,34.46944882722216,23.568687398942497,44.495051580858075,20.857980132308064,23.291600077377883,20.89433065470296,55.52077469558183,19.844813897479153,58.083049825672646,36.04803112619495,30.597265011033183,61.81472349909987,42.885198007213944,44.443935014762,60.7943028137632,56.65081125177011,25.33686895422538,38.14458629784,58.29573606914058,37.10347109833906,32.36638018344231,44.2662339875721,63.72920276808971,55.790954619502294,36.148778846694995,36.381560624474965,39.15843660392145,29.62194849621915,19.83265570676217,44.88606015730577,24.425051808720003,57.1797315468057,71.6458251440036,47.71828184410168,62.297629072916585,49.398820051478374,48.89742946462173,63.72651604199056,64.54989715620907,74.50841016330911,68.1267571369156,43.45172363372408,47.042764319242906,48.21077525281584,59.87426891313975,30.785233488197235,68.34677998820503,45.50010378586484,32.85488637869617,58.0574192558777,53.423109227949446,23.5175535283407,35.274239539395616,26.49712667438473,47.3236722695954,55.63987264059778,71.18456834459255,49.117384222465354,38.70879682836908,37.48817591640925,55.43737893657337,27.131039578736175,51.347488407803894,65.03857596645138,40.14864467006982,35.58956603863697,40.142296256104174,65.46581526913438,40.42211619188206,30.032571271057037,22.43891723997223,21.131463846331798,14.151099993486616,22.713861759606758,53.288875431127565,31.140528610219434,59.9109893101753,39.44946094241589,55.60671130256157,45.354681666931334,54.209925409908,73.66550101533238,58.4820486817956,62.243063004853816,52.653130309017435,48.20917438668848,29.093476178758255,70.40667921894884,62.22912217194979,55.17072207712883,40.090434614397765,69.85485132542665,50.24131239393599,66.98920233660071,60.68485135589858,51.863357017019574,72.04312262491815,53.40562645007447,49.61743474552537,68.28447780227025,49.02503504793739,24.844508176997355,31.031418017916064,53.59593851184064,60.89494204641298,26.46546232039442,50.84342600130724,24.041554960286806,24.084118060669272,56.59773236083773,29.743872855510528,60.623610115894905,61.04379182421397,62.26680329788615,65.79581444034969,78.10242405938256,70.10691210614729,41.33496529267509,36.874496721559815,24.282419768975576,33.99588520034203,61.8788080431828,62.554511392199935,45.76697309514572,48.93495631586882,69.8767593269397,39.36956539675046,32.43901441842147,26.07193451718489,60.78838119968942,41.36090198021818,46.27015825083301,27.12396380490488,43.97015322366895,39.77009621958158,19.664700591125428,43.992026846749134,32.58570500455399,30.0490900817222,22.3018067276484,50.52545815441564,64.3670134629379,30.430267418040945,57.68773036011188,43.649968392543265,69.66406102116984,46.21235718505584,62.6226649178954,39.75311898571732,65.57992340824633,47.98903968112858,52.98918386846966,40.34795425577597,66.95107722046171,67.70118747138432,53.65577475752668,53.6557924537879,42.57707996419281,31.215628370709155,70.1450489061008,61.67975577548524,76.17394905233554,49.78499310480461,61.13043617921485,44.588134716375514,26.25664034504869,57.84465694112263,37.97775365818944,67.33429848000411,74.53888570950028,80.05892218938449,39.8640458303513,76.5874414124015,42.70715812197221,36.545555360682734,62.727444787316124,32.417569695133956,64.82757247628854,59.25883055445542,59.966616066529845,40.61613895097681,32.02112547207192,41.931051026216835,49.07467815716933,51.40111466626925,25.615214976231083,37.023181667166114,18.56330568773036,13.397878242513448,21.93812924711034,53.42030043178727,59.971818219265685,44.66025661747293,61.73191589748842,37.01181189090469,59.64935623699808,34.113496913524536,38.925846047147466,67.11167145906778,62.12666830554969,43.566018622137086,68.87278325499162,52.206660045753615,52.2066576818479,41.78352024457498,62.472250697220964,50.3054644193086,25.892375492841552,67.27975690400767,43.36561833933704,73.75914699076293,47.2244363335977,72.64026265100705,31.501181733734217,60.720803644020286,37.138615203199485,31.11983787566813,29.647634577329274,26.267372591855565,44.5076170642761,30.705260816298217,60.76939816307845,58.0772761698584,30.31584707445215,56.48730470238022,60.95530865362945,44.03316245571191,47.4104563517329,36.27168169569473,40.464560256758865,38.04194091179348,58.001759156606006,48.37080895255149,60.12077007869045,63.37815903749643,49.78188993879342,32.366658044855576,42.02905954345653,58.368999908793604,35.63422604342416,47.166911318217025,42.99405746835168,70.926963902168,45.36284899426172,74.34428495399779,66.90098795167344,40.936434864515256,66.10480163914674,29.54275883240699,51.66634907067719,62.9318825085924,66.47202077075681,43.029134796741126,55.75396627024785,48.59004099608857,36.057054633340975,58.60842026974825,31.213681078513854,46.17006784901861,32.11175347835263,57.47486849297019,30.7421299918315,44.2203564886679,24.797062362541535,48.27025773556861,67.56386225726975,56.74568899898595,45.30129052783897,71.67479062665892,78.64308562458287,83.70370983524693,84.54474563628624,84.73495115119937,34.890248091799506,29.956021235531352,33.320425900561666,44.26343758214761,34.936511501980874,39.97845775248731,28.538863812063255,59.484549005573115,32.461067961486854,39.13512364834782,64.55222396300744,33.80456557932447,32.05535717531953,38.78321027595086,37.19354998934153,56.24515086592351,58.89685902917281,61.55238309202527,64.16803269235,78.93681356567767,57.685357018281366,71.62837127411844,72.93582697126644,46.37287174000392,67.97251994972997,69.10870244368247,30.669246164911453,29.68226897617143,32.6452672205675,64.77540840718177,42.93814410270846,38.997628621127525,30.52907248220232,23.619133584614456,30.126833961609808,48.89738644252271,62.44426286301063,55.93623558216978,59.87203948644543,75.148038877633,39.608304529550864,51.12428264842496,44.82512133058344,31.43543917989507,63.099426350550345,34.23158262354127,43.788855945056184,68.30261020614944,64.49853401416676,61.431368603990514,47.817315473017665,33.953269874593076,34.248903054581625,56.6004767371171,64.99283268674272,38.60345516181074,38.65027722073659,43.03839193634381,56.56206256151976,59.150602193444946,78.54797352357025,56.25138988234297,52.55250205801634,29.282094946688517,29.60384851618237,63.478036845627734,51.87779767126924,27.081893517648194,54.5850369013489,34.9101467726047,37.746751480292055,50.07981663326155,50.63216210872208,64.46030544845257,48.683315059874,31.44439601193216,36.042125696593416,19.734446408082643,23.977437266182218,23.64625204801037,59.965512241911654,33.45118272342691,66.17053909501624,69.82296491565033,45.30082197696877,42.11819650892578,62.373892285579245,43.939919224919244,59.137874281764816,63.19684437920045,30.9497376924103,36.32066184008838,38.94297909581256,31.148274242953963,39.41481038413094,18.797276465525727,21.59461557784738,10.084108516749565,46.40845435173953,63.4677764993964,44.19787098610906,61.540677721430576,49.2733661920721,68.73714141716619,48.947100257277924,44.25260780431723,61.12806721654246,34.699177856355305,68.93375451228827,58.88609338429223,65.1586305920518,56.512536477595944,49.82087445099549,69.3323061735062,47.57861985934145,47.84258460299872,36.87944997066385,62.86181508677751,52.647401825633104,68.03073488966241,65.96752957011027,51.36594212579553,28.07856147777177,47.00229424441746,32.985002405984986,20.346090172681546,39.50911465662585,24.142703923879775,60.09854174890162,42.87094159385086,57.60721954681927,37.375026699714,28.841971157058058,62.808021036776836,26.415957545067414,51.11084638878061,21.481577469357546,62.31723167812134,71.4559966580645,40.57163953995866,43.57728236640043,66.88058525311261,49.66798308441667,39.27042944795525,30.750807110060375,20.06373141767945,53.65928984232081,27.52140437376734,49.359302032271046,27.570354890999763,54.64825705804941,63.8971530273845,47.019748963310214,57.64434339769536,36.65460549290647,55.10776768417568,31.495503090724895,27.12921871294671,33.06013235968388,58.2640150521515,40.77674905523208,32.26918288578697,54.249396610586565,51.73167406802977,30.32568857243359,21.24878299236861,32.77876110368337,47.27930707648138,60.678605624895226,57.59189080610913,73.35001704020952,79.4250550433208,78.51274658730455,51.819827073133105,53.42278452381478,53.422738459918,51.75607039908679,62.001453641360676,42.92742694529331,68.58584135289685,46.66443232293005,68.16384116098857,38.34544124627826,31.20273595871764,27.323905190808414,56.617993483509274,68.92576585869216,62.215076798876005,61.34327965502107,48.54367126524983,40.64699685489418,42.75731848485927,67.1782648624562,50.46422048465317,59.83259879780485,37.325360881706025,55.645098256737505,48.7801999216772,24.005461739996765,57.33611418698089,72.84046044404386,40.743937023504806,59.85788045136665,61.69043225872727,32.31311226975054,68.56315934206714,68.65898373824773,36.8374123186302,74.46565074782592,39.65394340358542,54.86972495903402,44.58065908846266,47.956813803570604,34.019063604924334,41.05348547653453,30.370791941318004,26.90168191355642,35.11900834645493,52.517092366666624,72.40567580376829,44.09908305126725,60.349907162703026,67.15666442569533,77.87064743336283,83.86597280202058,53.197688677796954,35.019680881523975,43.91017706731325,33.49847150430326,57.398198341415316,49.967748966193106,45.6981287917292,60.33602491469342,59.52150604012553,48.00134417059005,68.50320714390719,38.225185760117846,30.862668849987642,62.194578812002,52.23400184293856,48.15787518427799,44.92686859041839,28.23088389543388,66.5923921751929,52.45429203578806,55.712209320729876,59.82742769772051,45.44756506362626,27.125849557452113,47.66805429304588,25.586908886672898,52.27148300632806,37.81635745580541,60.3269612891344,42.46941118243493,33.280511113869444,64.64973829354507,45.22065943706664,40.92665066741565,32.30949650677031,23.702994813674135,30.10766897716748,48.00809654558759,59.77838697321133,47.75056808098609,22.104289988590384,31.523280502872556,43.72957063513951,27.45584607797375,24.785202257263432,58.98389197055689,35.843928206481344,65.95694463131598,53.96930408775638,65.27345001190457,53.04798148233065,31.226847653514014,19.548797480511652,19.65927413100528,16.863006613819554,58.005523303520384,27.802772325210146,65.80715113244419,43.466835940298175,62.17782568519914,44.76628085530613,42.44193522442571,67.70268569742531,72.48773883116405,76.62866843492024,66.59897196437517,66.54196993673938,53.449159499151705,29.841154061119823,66.2667574597912,59.09542602970825,39.05292188692632,53.221171334166776,53.952189989752306,74.32475054304696,49.06094490743021,37.20319035749399,45.933500951531066,66.36922781452027,44.6185883444147,70.17049580571513,45.26332925748944,66.17007830280842,66.81094312179793,76.67031158856737,64.99805241954375,49.081686130752864,66.75420163096761,31.971590737232493,65.59920974711058,35.84235952544704,29.871936301827784,50.72665116382604,74.39561864755052,31.930663884538404,25.84587649383379,54.08307159150801,25.657997235515456,56.52384799804645,70.52507094731887,63.45091937401903,43.597321907846094,36.05215661372571,75.44761564216145,38.39297405514003,58.55036061732994,51.51485032165592,67.89460009093841,54.625541543448946,74.63487406812025,46.5916621802869,29.97949033858352,34.64790751314621,61.00538893150907,66.6127383936742,70.32923179724446,29.660007702704746,60.17228736739688,38.908135300699115,35.93834087135098,51.545302479759215,29.80825273472169,39.872022568990985,58.358917050129456,44.95704929523922,57.778089626801396,77.98465726248544,51.98848508802515,62.2896917514632,40.772372486833824,54.0716817113612,50.335496833838526,42.755426584955934,31.48785426725568,41.36168746901496,20.31942665339358,58.124042966893114,59.53047133916257,34.77015194346529,58.38270770069861,55.14755780658746,58.07089983035734,47.557606612995386,69.31011171147317,60.000165228872824,38.82556287124782,34.03400103824236,46.188725806628476,58.47730358417619,60.523514342481896,74.1726556273005,81.00243343886522,44.097549897203216,52.837129862820895,72.21142191512486,55.64428424965632,35.080755869307005,50.4808494433588,39.42056286620014,71.31902230495162,73.9082644090047,37.927702445230416,41.48819917129979,27.616942927931788,55.99798752050905,46.070467578327225,37.53304612773332,34.14763701588362,21.89216574901541,62.51288229469068,38.89627915071315,71.485300962289,30.637394385186024,28.161553043508434,58.90580689368128,24.461025859639307,52.68690328167819,27.70897233879349,50.73067669221908,60.751911467521474,68.79352669369113,59.04210424753794,58.94884045311421,70.88973526435431,31.42607036434437,41.3501464858005,68.2024115892823,44.3473446086392,51.419916292696094,51.55950770764534,44.29456068230051,41.6923011594437,23.250902100084232,60.10335963140738,34.36032050620874,52.18022380944536,43.93675278812086,73.29226719076031,37.17263953482941,34.14025627703692,23.155707358498393,23.028860623413525,61.689889518803476,62.0954500873464,31.933378695956502,22.818886008492047,58.8153656467463,52.06069678489894,60.986752801971456,61.046045761600716,62.57395741994847,43.90963700564881,39.063170062607355,62.09662902636061,54.13120201147844,44.68341254391191,53.981284830949726,65.10292139388116,61.91216261217952,73.67875737726277,35.3438064065838,51.98874963616129,35.51348352244039,69.6101078870358,44.80966261312031,54.34992116208631,38.86074353823187,49.35394222599379,43.57576392064863,40.35630632729746,21.061400999186002,23.609127943917752,13.82548677572562,14.99355947427609,49.49504870177723,30.964330305789755,64.13171473817975,68.18048347507239,37.53836316514806,62.96499981765891,63.93936447081998,42.415238712085944,55.83479946516368,66.19123957798871,67.87498840336896,39.27694754815484,56.85437496236267,48.16525339593488,48.04312991124535,51.49160611851048,29.001840590738592,60.6737802207659,63.89335834449725,54.34608224177836,79.6847882387749,48.29012315723401,68.18164286620737,41.28318771738989,46.183661816299455,38.28001088452152,34.885357618692716,29.13218811959216,56.502427607118456,66.68819259424446,54.914511326316834,58.90879211303156,60.78938826227318,31.99657808419181,35.565023775963915,59.67022696986283,26.3754868736989,66.79617006294554,38.711234076161,66.93643991192717,58.62673012600887,47.046874433465575,70.86968064206688,43.901863795093256,63.3303461503265,42.80424123843064,31.820077440206926,40.02659364497533,59.18327828940256,54.38128229241943,66.85308944368258,64.10784266267035,33.98414739727253,24.34444109129744,61.842436369382504,56.20392014392849,60.98550477934706,54.41431894745775,62.10985688355415,73.77211182946546,60.29517981979913,35.64367611230214,66.75803701473549,29.201965634468923,30.602296609501323,17.06538280281202,20.00354773000556,50.8119935279281,71.64639423244824,74.15336033762789,49.440140822183075,64.67196379999837,75.94519925730934,38.447796503325556,44.163722787003344,64.87385846784939,50.04838037165882,34.35659985575274,54.56307512952788,60.70215078607142,68.95664033863429,31.492181267677655,61.75408422428174,40.271834072820916,30.165026083359038,53.637973784687034,33.37471211504639,53.523357403836734,60.71066894628964,41.89042081202545,67.27223306905721,36.56733776280036,38.571707875936625,48.87939192344522,71.13443507120998,35.30212528572657,48.28329608731827,23.88640846801915,31.44989351178334,58.26799060998704,51.71798934171935,27.46874099876527,37.212786078532126,20.15089957530152,64.63216131311296,40.167956134402836,35.72293337343854,38.43793061319493,28.58001732805514,53.9835600631773,60.28246979279593,46.72253103459036,64.32013214011862,59.37791335394144,50.690079722681766,33.8546939485445,65.31499790187449,27.753239769540784,22.711934184745065,46.00574353839934,58.15825158745324,57.86804939856225,23.41412337154698,32.95241917021132,63.815475372288574,55.62396229117605,45.28041710250443,62.15398484075666,58.043186108695494,40.30632798778294,47.21058269778084,62.58459040390571,66.90956867320287,51.01786126295233,64.6649170189233,46.31786614043111,61.28335728379005,40.45946071693337,33.900639524785966,36.393766884624874,26.301756416204544,52.32002489201818,64.64275133807244,56.813171057224984,79.91914660463216,50.398959896432785,50.398782335656556,67.33925008338416,74.42232202006592,81.16683164095893,85.48454980324522,75.53670890824829,33.14472677392582,29.26549963624277,69.60541140995797,74.54378383387971,35.52310555670123,54.53681022344028,32.546906329761505,36.4755644024657,63.25254739044657,35.16510433232886,35.18170585179619,36.241878856468176,67.3178749926577,41.91742243253389,56.701749664340035,74.34640056803384,49.29978655009208,54.22384418179581,32.44031752116758,25.377207408748927,41.93448516537484,38.53587202172279,21.084637179458607,28.980275630948963,60.1146088836436,58.416570253687716,45.823510782508635,50.50220988728048,57.85232793896362,62.92435803993775,64.89961484195774,73.07650128211411,72.68438828027227,73.56619990234888,43.5513389537202,63.51386220075432,49.909651034931755,37.65619985685482,70.06396688880494,63.14810305447157,61.96443533608123,39.68657992600262,33.182852508252175,25.093920736850595,51.90887572183953,72.42381510321478,54.20385662007917,54.34187593144975,44.937999134605285,54.31594067600298,30.09535373212172,71.58812954501816,49.614159436065826,52.91395480106467,51.24733124168494,67.36775518322456,28.303781691771473,53.7736736671824,32.088982346984444,24.4106594958279,32.156113190157704,18.494048922202072,60.340323572902236,27.323343419713776,58.28295648695687,37.51254035787235,66.55904262268373,69.48140665121845,69.31840915818609,53.692703839314184,59.11430037118166,63.31115520847023,48.578369742303416,40.28209612440916,75.2880691275336,67.82586775915847,38.430645809385204,30.495652761139855,51.106346654740356,26.64495453746279,51.09749596769989,72.63563973331375,75.82304958202444,46.3490464122414,47.812804679864996,39.57974052448169,51.750631209784,69.60064813730948,31.22295180880805,62.42502200662698,30.60811823356205,53.05555302913164,53.83287440264228,75.57251623519181,76.50637127668176,76.06060827764576,51.3655670212784,63.63801340124146,40.319689452356904,70.02891429368366,66.06386067452607,42.252720992232305,34.24841533857079,36.376679531080605,54.55102854034987,36.78285753529732,46.73655307838329,38.615466140378004,29.479642445003027,29.761014867363475,52.60675328218218,32.55219134474758,59.77803296982396,45.59065789671214,53.94680525032745,73.50100161749104,72.49097871650639,35.361931643355916,64.45719338089116,52.41627201273398,49.68227101679576,42.706949313060846,53.117346077289675,59.87801028788769,28.89375247231389,27.577759755570256,23.68631731972518,16.664844560349213,22.850201994765346,14.029753540761208,54.8298381198594,53.497792969702566,55.603027879647414,55.53120566468814,61.798598002926134,41.85990784690475,52.84701560494096,54.07401834916939,68.3127290482255,66.52357042608257,42.765317641203154,34.69499284279583,64.44302347371045,53.663570696890815,40.551394060844494,65.13936811392601,71.89174769591384,48.757199583536725,69.33672146969413,45.13764467976475,68.75302859440954,50.401067808254076,41.44248353606709,76.79184516710102,74.80161005457285,49.97526927366369,58.71537704435041,64.32939712180996,39.358182501345034,33.95441443343755,28.640999536205907,22.65653206515326,51.07361768481183,24.939218620739677,48.68054711460346,35.15492950770412,56.43526200441496,62.33190291845489,25.39237904664356,35.343889621985475,27.553038212688694,20.93637641067407,43.2540799100173,19.10365300983482,56.60148890765638,63.876046230961414,27.998909667746034,28.593618331255,55.417316114556535,63.391665949758,40.80075761570583,52.67735219880024,34.007906520886614,37.31469428346533,63.971425647137046,54.20446202687712,38.8583452847449,39.359011599521075,57.214767487159065,58.32100171312998,37.45280207634567,35.988649402453184,33.04050938956805,61.792827412428124,33.47401442000429,31.451691052864277,39.24636353478838,60.216781256819644,24.44120346774579,51.255987518261605,30.378552028358303,54.193389650204246,25.721729593680703,40.66905950471011,51.38185578732759,23.926791012597832,31.762899458255717,43.40522038693413,21.476052973509066,63.89441388706746,62.35839168842339,64.02774409242006,null,71.55287613005031,67.14648674604739,48.12279696008046,40.059162834610355,48.6715943092218,70.8033875854626,63.359285489180934,53.047249165375966,64.79754610529612,62.07034307349304,75.38854869336876,82.33097713725498,49.735430453767094,39.216688604261975,61.04834208466599,49.50174048770927,72.19012896274874,36.87514486166956,54.38610347368249,40.158321525477795,42.404921997166966,68.2791437130498,29.68681759542387,null],\"x\":[\"2018-12-27\",\"2018-12-28\",\"2019-01-02\",\"2019-01-03\",\"2019-01-04\",\"2019-01-07\",\"2019-01-08\",\"2019-01-09\",\"2019-01-10\",\"2019-01-11\",\"2019-01-14\",\"2019-01-15\",\"2019-01-16\",\"2019-01-17\",\"2019-01-18\",\"2019-01-21\",\"2019-01-22\",\"2019-01-23\",\"2019-01-24\",\"2019-01-25\",\"2019-01-28\",\"2019-01-29\",\"2019-01-30\",\"2019-01-31\",\"2019-02-01\",\"2019-02-11\",\"2019-02-12\",\"2019-02-13\",\"2019-02-14\",\"2019-02-15\",\"2019-02-18\",\"2019-02-19\",\"2019-02-20\",\"2019-02-21\",\"2019-02-22\",\"2019-02-25\",\"2019-02-26\",\"2019-02-27\",\"2019-02-28\",\"2019-03-01\",\"2019-03-04\",\"2019-03-05\",\"2019-03-06\",\"2019-03-07\",\"2019-03-08\",\"2019-03-11\",\"2019-03-12\",\"2019-03-13\",\"2019-03-14\",\"2019-03-15\",\"2019-03-18\",\"2019-03-19\",\"2019-03-20\",\"2019-03-21\",\"2019-03-22\",\"2019-03-25\",\"2019-03-26\",\"2019-03-27\",\"2019-03-28\",\"2019-03-29\",\"2019-04-01\",\"2019-04-02\",\"2019-04-03\",\"2019-04-04\",\"2019-04-08\",\"2019-04-09\",\"2019-04-10\",\"2019-04-11\",\"2019-04-12\",\"2019-04-15\",\"2019-04-16\",\"2019-04-17\",\"2019-04-18\",\"2019-04-19\",\"2019-04-22\",\"2019-04-23\",\"2019-04-24\",\"2019-04-25\",\"2019-04-26\",\"2019-04-29\",\"2019-04-30\",\"2019-05-06\",\"2019-05-07\",\"2019-05-08\",\"2019-05-09\",\"2019-05-10\",\"2019-05-13\",\"2019-05-14\",\"2019-05-15\",\"2019-05-16\",\"2019-05-17\",\"2019-05-20\",\"2019-05-21\",\"2019-05-22\",\"2019-05-23\",\"2019-05-24\",\"2019-05-27\",\"2019-05-28\",\"2019-05-29\",\"2019-05-30\",\"2019-05-31\",\"2019-06-03\",\"2019-06-04\",\"2019-06-05\",\"2019-06-06\",\"2019-06-10\",\"2019-06-11\",\"2019-06-12\",\"2019-06-13\",\"2019-06-14\",\"2019-06-17\",\"2019-06-18\",\"2019-06-19\",\"2019-06-20\",\"2019-06-21\",\"2019-06-24\",\"2019-06-25\",\"2019-06-26\",\"2019-06-27\",\"2019-06-28\",\"2019-07-01\",\"2019-07-02\",\"2019-07-03\",\"2019-07-04\",\"2019-07-05\",\"2019-07-08\",\"2019-07-09\",\"2019-07-10\",\"2019-07-11\",\"2019-07-12\",\"2019-07-15\",\"2019-07-16\",\"2019-07-17\",\"2019-07-18\",\"2019-07-19\",\"2019-07-22\",\"2019-07-23\",\"2019-07-24\",\"2019-07-25\",\"2019-07-26\",\"2019-07-29\",\"2019-07-30\",\"2019-07-31\",\"2019-08-01\",\"2019-08-02\",\"2019-08-05\",\"2019-08-06\",\"2019-08-07\",\"2019-08-08\",\"2019-08-09\",\"2019-08-12\",\"2019-08-13\",\"2019-08-14\",\"2019-08-15\",\"2019-08-16\",\"2019-08-19\",\"2019-08-20\",\"2019-08-21\",\"2019-08-22\",\"2019-08-23\",\"2019-08-26\",\"2019-08-27\",\"2019-08-28\",\"2019-08-29\",\"2019-08-30\",\"2019-09-02\",\"2019-09-03\",\"2019-09-04\",\"2019-09-05\",\"2019-09-06\",\"2019-09-09\",\"2019-09-10\",\"2019-09-11\",\"2019-09-12\",\"2019-09-16\",\"2019-09-17\",\"2019-09-18\",\"2019-09-19\",\"2019-09-20\",\"2019-09-23\",\"2019-09-24\",\"2019-09-25\",\"2019-09-26\",\"2019-09-27\",\"2019-09-30\",\"2019-10-08\",\"2019-10-09\",\"2019-10-10\",\"2019-10-11\",\"2019-10-14\",\"2019-10-15\",\"2019-10-16\",\"2019-10-17\",\"2019-10-18\",\"2019-10-21\",\"2019-10-22\",\"2019-10-23\",\"2019-10-24\",\"2019-10-25\",\"2019-10-28\",\"2019-10-29\",\"2019-10-30\",\"2019-10-31\",\"2019-11-01\",\"2019-11-04\",\"2019-11-05\",\"2019-11-06\",\"2019-11-07\",\"2019-11-08\",\"2019-11-11\",\"2019-11-12\",\"2019-11-13\",\"2019-11-14\",\"2019-11-15\",\"2019-11-18\",\"2019-11-19\",\"2019-11-20\",\"2019-11-21\",\"2019-11-22\",\"2019-11-25\",\"2019-11-26\",\"2019-11-27\",\"2019-11-28\",\"2019-11-29\",\"2019-12-02\",\"2019-12-03\",\"2019-12-04\",\"2019-12-05\",\"2019-12-06\",\"2019-12-09\",\"2019-12-10\",\"2019-12-11\",\"2019-12-12\",\"2019-12-13\",\"2019-12-16\",\"2019-12-17\",\"2019-12-18\",\"2019-12-19\",\"2019-12-20\",\"2019-12-23\",\"2019-12-24\",\"2019-12-25\",\"2019-12-26\",\"2019-12-27\",\"2019-12-30\",\"2019-12-31\",\"2020-01-02\",\"2020-01-03\",\"2020-01-06\",\"2020-01-07\",\"2020-01-08\",\"2020-01-09\",\"2020-01-10\",\"2020-01-13\",\"2020-01-14\",\"2020-01-15\",\"2020-01-16\",\"2020-01-17\",\"2020-01-20\",\"2020-01-21\",\"2020-01-22\",\"2020-01-23\",\"2020-02-03\",\"2020-02-04\",\"2020-02-05\",\"2020-02-06\",\"2020-02-07\",\"2020-02-10\",\"2020-02-11\",\"2020-02-12\",\"2020-02-13\",\"2020-02-14\",\"2020-02-17\",\"2020-02-18\",\"2020-02-19\",\"2020-02-20\",\"2020-02-21\",\"2020-02-24\",\"2020-02-25\",\"2020-02-26\",\"2020-02-27\",\"2020-02-28\",\"2020-03-02\",\"2020-03-03\",\"2020-03-04\",\"2020-03-05\",\"2020-03-06\",\"2020-03-09\",\"2020-03-10\",\"2020-03-11\",\"2020-03-12\",\"2020-03-13\",\"2020-03-16\",\"2020-03-17\",\"2020-03-18\",\"2020-03-19\",\"2020-03-20\",\"2020-03-23\",\"2020-03-24\",\"2020-03-25\",\"2020-03-26\",\"2020-03-27\",\"2020-03-30\",\"2020-03-31\",\"2020-04-01\",\"2020-04-02\",\"2020-04-03\",\"2020-04-07\",\"2020-04-08\",\"2020-04-09\",\"2020-04-10\",\"2020-04-13\",\"2020-04-14\",\"2020-04-15\",\"2020-04-16\",\"2020-04-17\",\"2020-04-20\",\"2020-04-21\",\"2020-04-22\",\"2020-04-23\",\"2020-04-24\",\"2020-04-27\",\"2020-04-28\",\"2020-04-29\",\"2020-04-30\",\"2020-05-06\",\"2020-05-07\",\"2020-05-08\",\"2020-05-11\",\"2020-05-12\",\"2020-05-13\",\"2020-05-14\",\"2020-05-15\",\"2020-05-18\",\"2020-05-19\",\"2020-05-20\",\"2020-05-21\",\"2020-05-22\",\"2020-05-25\",\"2020-05-26\",\"2020-05-27\",\"2020-05-28\",\"2020-05-29\",\"2020-06-01\",\"2020-06-02\",\"2020-06-03\",\"2020-06-04\",\"2020-06-05\",\"2020-06-08\",\"2020-06-09\",\"2020-06-10\",\"2020-06-11\",\"2020-06-12\",\"2020-06-15\",\"2020-06-16\",\"2020-06-17\",\"2020-06-18\",\"2020-06-19\",\"2020-06-22\",\"2020-06-23\",\"2020-06-24\",\"2020-06-29\",\"2020-06-30\",\"2020-07-01\",\"2020-07-02\",\"2020-07-03\",\"2020-07-06\",\"2020-07-07\",\"2020-07-08\",\"2020-07-09\",\"2020-07-10\",\"2020-07-13\",\"2020-07-14\",\"2020-07-15\",\"2020-07-16\",\"2020-07-17\",\"2020-07-20\",\"2020-07-21\",\"2020-07-22\",\"2020-07-23\",\"2020-07-24\",\"2020-07-27\",\"2020-07-28\",\"2020-07-29\",\"2020-07-30\",\"2020-07-31\",\"2020-08-03\",\"2020-08-04\",\"2020-08-05\",\"2020-08-06\",\"2020-08-07\",\"2020-08-10\",\"2020-08-11\",\"2020-08-12\",\"2020-08-13\",\"2020-08-14\",\"2020-08-17\",\"2020-08-18\",\"2020-08-19\",\"2020-08-20\",\"2020-08-21\",\"2020-08-24\",\"2020-08-25\",\"2020-08-26\",\"2020-08-27\",\"2020-08-28\",\"2020-08-31\",\"2020-09-01\",\"2020-09-02\",\"2020-09-03\",\"2020-09-04\",\"2020-09-07\",\"2020-09-08\",\"2020-09-09\",\"2020-09-10\",\"2020-09-11\",\"2020-09-14\",\"2020-09-15\",\"2020-09-16\",\"2020-09-17\",\"2020-09-18\",\"2020-09-21\",\"2020-09-22\",\"2020-09-23\",\"2020-09-24\",\"2020-09-25\",\"2020-09-28\",\"2020-09-29\",\"2020-09-30\",\"2020-10-09\",\"2020-10-12\",\"2020-10-13\",\"2020-10-14\",\"2020-10-15\",\"2020-10-16\",\"2020-10-19\",\"2020-10-20\",\"2020-10-21\",\"2020-10-22\",\"2020-10-23\",\"2020-10-26\",\"2020-10-27\",\"2020-10-28\",\"2020-10-29\",\"2020-10-30\",\"2020-11-02\",\"2020-11-03\",\"2020-11-04\",\"2020-11-05\",\"2020-11-06\",\"2020-11-09\",\"2020-11-10\",\"2020-11-11\",\"2020-11-12\",\"2020-11-13\",\"2020-11-16\",\"2020-11-17\",\"2020-11-18\",\"2020-11-19\",\"2020-11-20\",\"2020-11-23\",\"2020-11-24\",\"2020-11-25\",\"2020-11-26\",\"2020-11-27\",\"2020-11-30\",\"2020-12-01\",\"2020-12-02\",\"2020-12-03\",\"2020-12-04\",\"2020-12-07\",\"2020-12-08\",\"2020-12-09\",\"2020-12-10\",\"2020-12-11\",\"2020-12-14\",\"2020-12-15\",\"2020-12-16\",\"2020-12-17\",\"2020-12-18\",\"2020-12-21\",\"2020-12-22\",\"2020-12-23\",\"2020-12-24\",\"2020-12-25\",\"2020-12-28\",\"2020-12-29\",\"2020-12-30\",\"2020-12-31\",\"2021-01-04\",\"2021-01-05\",\"2021-01-06\",\"2021-01-07\",\"2021-01-08\",\"2021-01-11\",\"2021-01-12\",\"2021-01-13\",\"2021-01-14\",\"2021-01-15\",\"2021-01-18\",\"2021-01-19\",\"2021-01-20\",\"2021-01-21\",\"2021-01-22\",\"2021-01-25\",\"2021-01-26\",\"2021-01-27\",\"2021-01-28\",\"2021-01-29\",\"2021-02-01\",\"2021-02-02\",\"2021-02-03\",\"2021-02-04\",\"2021-02-05\",\"2021-02-08\",\"2021-02-09\",\"2021-02-10\",\"2021-02-18\",\"2021-02-19\",\"2021-02-22\",\"2021-02-23\",\"2021-02-24\",\"2021-02-25\",\"2021-02-26\",\"2021-03-01\",\"2021-03-02\",\"2021-03-03\",\"2021-03-04\",\"2021-03-05\",\"2021-03-08\",\"2021-03-09\",\"2021-03-10\",\"2021-03-11\",\"2021-03-12\",\"2021-03-15\",\"2021-03-16\",\"2021-03-17\",\"2021-03-18\",\"2021-03-19\",\"2021-03-22\",\"2021-03-23\",\"2021-03-24\",\"2021-03-25\",\"2021-03-26\",\"2021-03-29\",\"2021-03-30\",\"2021-03-31\",\"2021-04-01\",\"2021-04-02\",\"2021-04-06\",\"2021-04-07\",\"2021-04-08\",\"2021-04-09\",\"2021-04-12\",\"2021-04-13\",\"2021-04-14\",\"2021-04-15\",\"2021-04-16\",\"2021-04-19\",\"2021-04-20\",\"2021-04-21\",\"2021-04-22\",\"2021-04-23\",\"2021-04-26\",\"2021-04-27\",\"2021-04-28\",\"2021-04-29\",\"2021-04-30\",\"2021-05-06\",\"2021-05-07\",\"2021-05-10\",\"2021-05-11\",\"2021-05-12\",\"2021-05-13\",\"2021-05-14\",\"2021-05-17\",\"2021-05-18\",\"2021-05-19\",\"2021-05-20\",\"2021-05-21\",\"2021-05-24\",\"2021-05-25\",\"2021-05-26\",\"2021-05-27\",\"2021-05-28\",\"2021-05-31\",\"2021-06-01\",\"2021-06-02\",\"2021-06-03\",\"2021-06-04\",\"2021-06-07\",\"2021-06-08\",\"2021-06-09\",\"2021-06-10\",\"2021-06-11\",\"2021-06-15\",\"2021-06-16\",\"2021-06-17\",\"2021-06-18\",\"2021-06-21\",\"2021-06-22\",\"2021-06-23\",\"2021-06-24\",\"2021-06-25\",\"2021-06-28\",\"2021-06-29\",\"2021-06-30\",\"2021-07-01\",\"2021-07-02\",\"2021-07-05\",\"2021-07-06\",\"2021-07-07\",\"2021-07-08\",\"2021-07-09\",\"2021-07-12\",\"2021-07-13\",\"2021-07-14\",\"2021-07-15\",\"2021-07-16\",\"2021-07-19\",\"2021-07-20\",\"2021-07-21\",\"2021-07-22\",\"2021-07-23\",\"2021-07-26\",\"2021-07-27\",\"2021-07-28\",\"2021-07-29\",\"2021-07-30\",\"2021-08-02\",\"2021-08-03\",\"2021-08-04\",\"2021-08-05\",\"2021-08-06\",\"2021-08-09\",\"2021-08-10\",\"2021-08-11\",\"2021-08-12\",\"2021-08-13\",\"2021-08-16\",\"2021-08-17\",\"2021-08-18\",\"2021-08-19\",\"2021-08-20\",\"2021-08-23\",\"2021-08-24\",\"2021-08-25\",\"2021-08-26\",\"2021-08-27\",\"2021-08-30\",\"2021-08-31\",\"2021-09-01\",\"2021-09-02\",\"2021-09-03\",\"2021-09-06\",\"2021-09-07\",\"2021-09-08\",\"2021-09-09\",\"2021-09-10\",\"2021-09-13\",\"2021-09-14\",\"2021-09-15\",\"2021-09-16\",\"2021-09-17\",\"2021-09-22\",\"2021-09-23\",\"2021-09-24\",\"2021-09-27\",\"2021-09-28\",\"2021-09-29\",\"2021-09-30\",\"2021-10-08\",\"2021-10-11\",\"2021-10-12\",\"2021-10-13\",\"2021-10-14\",\"2021-10-15\",\"2021-10-18\",\"2021-10-19\",\"2021-10-20\",\"2021-10-21\",\"2021-10-22\",\"2021-10-25\",\"2021-10-26\",\"2021-10-27\",\"2021-10-28\",\"2021-10-29\",\"2021-11-01\",\"2021-11-02\",\"2021-11-03\",\"2021-11-04\",\"2021-11-05\",\"2021-11-08\",\"2021-11-09\",\"2021-11-10\",\"2021-11-11\",\"2021-11-12\",\"2021-11-15\",\"2021-11-16\",\"2021-11-17\",\"2021-11-18\",\"2021-11-19\",\"2021-11-22\",\"2021-11-23\",\"2021-11-24\",\"2021-11-25\",\"2021-11-26\",\"2021-11-29\",\"2021-11-30\",\"2021-12-01\",\"2021-12-02\",\"2021-12-03\",\"2021-12-06\",\"2021-12-07\",\"2021-12-08\",\"2021-12-09\",\"2021-12-10\",\"2021-12-13\",\"2021-12-14\",\"2021-12-15\",\"2021-12-16\",\"2021-12-17\",\"2021-12-20\",\"2021-12-21\",\"2021-12-22\",\"2021-12-23\",\"2021-12-24\",\"2021-12-27\",\"2021-12-28\",\"2021-12-29\",\"2021-12-30\",\"2021-12-31\",\"2022-01-04\",\"2022-01-05\",\"2022-01-06\",\"2022-01-07\",\"2022-01-10\",\"2022-01-11\",\"2022-01-12\",\"2022-01-13\",\"2022-01-14\",\"2022-01-17\",\"2022-01-18\",\"2022-01-19\",\"2022-01-20\",\"2022-01-21\",\"2022-01-24\",\"2022-01-25\",\"2022-01-26\",\"2022-01-27\",\"2022-01-28\",\"2022-02-07\",\"2022-02-08\",\"2022-02-09\",\"2022-02-10\",\"2022-02-11\",\"2022-02-14\",\"2022-02-15\",\"2022-02-16\",\"2022-02-17\",\"2022-02-18\",\"2022-02-21\",\"2022-02-22\",\"2022-02-23\",\"2022-02-24\",\"2022-02-25\",\"2022-02-28\",\"2022-03-01\",\"2022-03-02\",\"2022-03-03\",\"2022-03-04\",\"2022-03-07\",\"2022-03-08\",\"2022-03-09\",\"2022-03-10\",\"2022-03-11\",\"2022-03-14\",\"2022-03-15\",\"2022-03-16\",\"2022-03-17\",\"2022-03-18\",\"2022-03-21\",\"2022-03-22\",\"2022-03-23\",\"2022-03-24\",\"2022-03-25\",\"2022-03-28\",\"2022-03-29\",\"2022-03-30\",\"2022-03-31\",\"2022-04-01\",\"2022-04-06\",\"2022-04-07\",\"2022-04-08\",\"2022-04-11\",\"2022-04-12\",\"2022-04-13\",\"2022-04-14\",\"2022-04-15\",\"2022-04-18\",\"2022-04-19\",\"2022-04-20\",\"2022-04-21\",\"2022-04-22\",\"2022-04-25\",\"2022-04-26\",\"2022-04-27\",\"2022-04-28\",\"2022-04-29\",\"2022-05-05\",\"2022-05-06\",\"2022-05-09\",\"2022-05-10\",\"2022-05-11\",\"2022-05-12\",\"2022-05-13\",\"2022-05-16\",\"2022-05-17\",\"2022-05-18\",\"2022-05-19\",\"2022-05-20\",\"2022-05-23\",\"2022-05-24\",\"2022-05-25\",\"2022-05-26\",\"2022-05-27\",\"2022-05-30\",\"2022-05-31\",\"2022-06-01\",\"2022-06-02\",\"2022-06-06\",\"2022-06-07\",\"2022-06-08\",\"2022-06-09\",\"2022-06-10\",\"2022-06-13\",\"2022-06-14\",\"2022-06-15\",\"2022-06-16\",\"2022-06-17\",\"2022-06-20\",\"2022-06-21\",\"2022-06-22\",\"2022-06-23\",\"2022-06-24\",\"2022-06-27\",\"2022-06-28\",\"2022-06-29\",\"2022-06-30\",\"2022-07-01\",\"2022-07-04\",\"2022-07-05\",\"2022-07-06\",\"2022-07-07\",\"2022-07-08\",\"2022-07-11\",\"2022-07-12\",\"2022-07-13\",\"2022-07-14\",\"2022-07-15\",\"2022-07-18\",\"2022-07-19\",\"2022-07-20\",\"2022-07-21\",\"2022-07-22\",\"2022-07-25\",\"2022-07-26\",\"2022-07-27\",\"2022-07-28\",\"2022-07-29\",\"2022-08-01\",\"2022-08-02\",\"2022-08-03\",\"2022-08-04\",\"2022-08-05\",\"2022-08-08\",\"2022-08-09\",\"2022-08-10\",\"2022-08-11\",\"2022-08-12\",\"2022-08-15\",\"2022-08-16\",\"2022-08-17\",\"2022-08-18\",\"2022-08-19\",\"2022-08-22\",\"2022-08-23\",\"2022-08-24\",\"2022-08-25\",\"2022-08-26\",\"2022-08-29\",\"2022-08-30\",\"2022-08-31\",\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\",\"2023-03-02\",\"2023-03-03\",\"2023-03-06\",\"2023-03-07\",\"2023-03-08\",\"2023-03-09\",\"2023-03-10\",\"2023-03-13\",\"2023-03-14\",\"2023-03-15\",\"2023-03-16\",\"2023-03-17\",\"2023-03-20\",\"2023-03-21\",\"2023-03-22\",\"2023-03-23\",\"2023-03-24\",\"2023-03-27\",\"2023-03-28\",\"2023-03-29\",\"2023-03-30\",\"2023-03-31\",\"2023-04-03\",\"2023-04-04\",\"2023-04-06\",\"2023-04-07\",\"2023-04-10\",\"2023-04-11\",\"2023-04-12\",\"2023-04-13\",\"2023-04-14\",\"2023-04-17\",\"2023-04-18\",\"2023-04-19\",\"2023-04-20\",\"2023-04-21\",\"2023-04-24\",\"2023-04-25\",\"2023-04-26\",\"2023-04-27\",\"2023-04-28\",\"2023-05-04\",\"2023-05-05\",\"2023-05-08\",\"2023-05-09\",\"2023-05-10\",\"2023-05-11\",\"2023-05-12\",\"2023-05-15\",\"2023-05-16\",\"2023-05-17\",\"2023-05-18\",\"2023-05-19\",\"2023-05-22\",\"2023-05-23\",\"2023-05-24\",\"2023-05-25\",\"2023-05-26\",\"2023-05-29\",\"2023-05-30\",\"2023-05-31\",\"2023-06-01\",\"2023-06-02\",\"2023-06-05\",\"2023-06-06\",\"2023-06-07\",\"2023-06-08\",\"2023-06-09\",\"2023-06-12\",\"2023-06-13\",\"2023-06-14\",\"2023-06-15\",\"2023-06-16\",\"2023-06-19\",\"2023-06-20\",\"2023-06-21\",\"2023-06-26\",\"2023-06-27\",\"2023-06-28\",\"2023-06-29\",\"2023-06-30\",\"2023-07-03\",\"2023-07-04\",\"2023-07-05\",\"2023-07-06\",\"2023-07-07\",\"2023-07-10\",\"2023-07-11\",\"2023-07-12\",\"2023-07-13\",\"2023-07-14\",\"2023-07-17\",\"2023-07-18\",\"2023-07-19\",\"2023-07-20\",\"2023-07-21\",\"2023-07-24\",\"2023-07-25\",\"2023-07-26\",\"2023-07-27\",\"2023-07-28\",\"2023-07-31\",\"2023-08-01\",\"2023-08-02\",\"2023-08-03\",\"2023-08-04\",\"2023-08-07\",\"2023-08-08\",\"2023-08-09\",\"2023-08-10\",\"2023-08-11\",\"2023-08-14\",\"2023-08-15\",\"2023-08-16\",\"2023-08-17\",\"2023-08-18\",\"2023-08-21\",\"2023-08-22\",\"2023-08-23\",\"2023-08-24\",\"2023-08-25\",\"2023-08-28\",\"2023-08-29\",\"2023-08-30\",\"2023-08-31\",\"2023-09-01\",\"2023-09-04\",\"2023-09-05\",\"2023-09-06\",\"2023-09-07\",\"2023-09-08\",\"2023-09-11\",\"2023-09-12\",\"2023-09-13\",\"2023-09-14\",\"2023-09-15\",\"2023-09-18\",\"2023-09-19\",\"2023-09-20\",\"2023-09-21\",\"2023-09-22\",\"2023-09-25\",\"2023-09-26\",\"2023-09-27\",\"2023-09-28\",\"2023-10-09\",\"2023-10-10\",\"2023-10-11\",\"2023-10-12\",\"2023-10-13\",\"2023-10-16\",\"2023-10-17\",\"2023-10-18\",\"2023-10-19\",\"2023-10-20\",\"2023-10-23\",\"2023-10-24\",\"2023-10-25\",\"2023-10-26\",\"2023-10-27\",\"2023-10-30\",\"2023-10-31\",\"2023-11-01\",\"2023-11-02\",\"2023-11-03\",\"2023-11-06\",\"2023-11-07\",\"2023-11-08\",\"2023-11-09\",\"2023-11-10\",\"2023-11-13\",\"2023-11-14\",\"2023-11-15\",\"2023-11-16\",\"2023-11-17\",\"2023-11-20\",\"2023-11-21\",\"2023-11-22\",\"2023-11-23\",\"2023-11-24\",\"2023-11-27\",\"2023-11-28\",\"2023-11-29\",\"2023-11-30\"],\"y\":[1.0,1.0021977424621582,1.0,0.9846154451370239,0.995604395866394,1.0373625755310059,1.0571428537368774,1.0483516454696655,1.0417581796646118,1.0615384578704834,1.0659340620040894,1.0659340620040894,1.0747252702713013,1.0571428537368774,1.068131923675537,1.072527527809143,1.0637362003326416,1.0593407154083252,1.0,1.0,0.9890110492706299,0.9890110492706299,0.9604395031929016,0.9516482949256897,0.9406593441963196,0.9604395031929016,0.9780219197273254,0.995604395866394,1.0087913274765015,1.0131869316101074,1.0131869316101074,1.0439560413360596,1.0417581796646118,1.0417581796646118,1.0329669713974,1.0615384578704834,1.1208791732788086,1.1120879650115967,1.1186813116073608,1.129670262336731,1.1450549364089966,1.1560440063476562,1.1912087202072144,1.2087912559509277,1.2417582273483276,1.1868131160736084,1.230769157409668,1.2395604848861694,1.2087912559509277,1.1846153736114502,1.2087912559509277,1.2351648807525635,1.2285715341567993,1.2241759300231934,1.2461538314819336,1.259340763092041,1.2329670190811157,1.1890109777450562,1.1846153736114502,1.1758241653442383,1.221977949142456,1.259340763092041,1.2571427822113037,1.2813186645507812,1.2813186645507812,1.2857142686843872,1.2769230604171753,1.2813186645507812,1.2483515739440918,1.2527471780776978,1.2527471780776978,1.2571427822113037,1.2659341096878052,1.2527471780776978,1.2637362480163574,1.2505494356155396,1.210988998413086,1.2131868600845337,1.1670329570770264,1.1428570747375488,1.1142857074737549,1.125274658203125,1.0593407154083252,1.0791207551956177,1.068131923675537,1.0549451112747192,1.0791207551956177,1.0703296661376953,1.0703296661376953,1.0835163593292236,1.0879119634628296,1.0417581796646118,1.0329669713974,1.0461539030075073,1.0351648330688477,1.019780158996582,1.019780158996582,1.0417581796646118,1.0439560413360596,1.0329669713974,1.024175763130188,1.019780158996582,1.0109889507293701,0.997802197933197,0.997802197933197,0.9802197217941284,0.9912088513374329,1.019780158996582,1.0131869316101074,1.019780158996582,1.015384554862976,1.0131869316101074,1.024175763130188,1.0285714864730835,1.0461539030075073,1.054982304573059,1.0461539030075073,1.041739821434021,1.0395326614379883,1.0439469814300537,1.028497338294983,1.04836106300354,1.0439469814300537,1.035118579864502,1.041739821434021,1.0439469814300537,0.9975982308387756,0.995391309261322,0.9887700080871582,0.9909770488739014,0.995391309261322,1.0174620151519775,1.0174620151519775,1.0108407735824585,1.0064265727996826,1.0086337327957153,0.9909770488739014,0.9931840896606445,1.0064265727996826,1.0020124912261963,0.9975982308387756,0.995391309261322,1.0064265727996826,1.0020124912261963,0.9909770488739014,0.9689062833786011,0.9556638598442078,0.9225577116012573,0.9181435704231262,0.9225577116012573,0.9137294292449951,0.9247647523880005,0.9203506112098694,0.9247647523880005,0.9225577116012573,0.9247647523880005,0.9534568190574646,0.9534568190574646,0.9556638598442078,0.9534568190574646,0.9512496590614319,0.9380072355270386,0.9600780010223389,0.9622851014137268,0.9622851014137268,0.9556638598442078,0.9711134433746338,0.9689062833786011,0.9755275845527649,0.9777345657348633,0.9777345657348633,0.9931840896606445,0.9931840896606445,0.9909770488739014,0.995391309261322,0.9931840896606445,0.9689062833786011,0.9666992425918579,0.9689062833786011,0.9733204245567322,0.9556638598442078,0.9578709602355957,0.9402143359184265,0.9247647523880005,0.9335931539535522,0.9203506112098694,0.9313859343528748,0.9380072355270386,0.9424213767051697,0.9490426778793335,0.9666992425918579,0.9711134433746338,0.96449214220047,0.9556638598442078,0.9380072355270386,0.9335931539535522,0.9446285367012024,0.9534568190574646,0.9512496590614319,0.9512496590614319,0.96449214220047,0.9556638598442078,0.9446285367012024,0.9313859343528748,0.9424213767051697,0.9380072355270386,0.9380072355270386,0.9225577116012573,0.9225577116012573,0.9203506112098694,0.902694046497345,0.902694046497345,0.8960728049278259,0.8916586637496948,0.8828303217887878,0.8850374817848206,0.8960728049278259,0.8850374817848206,0.8894516229629517,0.887244462966919,0.9049010872840881,0.900486946105957,0.9049010872840881,0.8982799053192139,0.9049010872840881,0.902694046497345,0.902694046497345,0.8982799053192139,0.9071082472801208,0.9137294292449951,0.9137294292449951,0.9137294292449951,0.9093151688575745,0.902694046497345,0.9159364700317383,0.9203506112098694,0.9313859343528748,0.9291790127754211,0.9335931539535522,0.9291790127754211,0.911522388458252,0.9181435704231262,0.9137294292449951,0.9247647523880005,0.9402143359184265,0.9711134433746338,0.9622851014137268,0.9998053908348083,0.9909770488739014,0.979941725730896,0.9887700080871582,0.9666992425918579,0.9821486473083496,0.9909770488739014,0.9975982308387756,0.9887700080871582,0.9733204245567322,0.9711134433746338,0.9711134433746338,0.9733204245567322,0.9512496590614319,0.9490426778793335,0.9225577116012573,0.829860508441925,0.8210322260856628,0.829860508441925,0.8453100323677063,0.8453100323677063,0.8607596158981323,0.8519312739372253,0.8629667162895203,0.8497242331504822,0.8475171327590942,0.8651737570762634,0.8740020394325256,0.8673808574676514,0.8850374817848206,0.8850374817848206,0.8850374817848206,0.8762091398239136,0.887244462966919,0.887244462966919,0.8453100323677063,0.8740020394325256,0.8740020394325256,0.9181435704231262,0.9159364700317383,0.9468355178833008,0.8982799053192139,0.9159364700317383,0.9049010872840881,0.8850374817848206,0.8717949390411377,0.8541383147239685,0.8541383147239685,0.8364817500114441,0.8585525155067444,0.8629667162895203,0.838688850402832,0.8519312739372253,0.8673808574676514,0.8585525155067444,0.8585525155067444,0.8453100323677063,0.8408958911895752,0.8364817500114441,0.8475171327590942,0.8431029319763184,0.8541383147239685,0.8607596158981323,0.8563454747200012,0.8431029319763184,0.8408958911895752,0.8475171327590942,0.838688850402832,0.838688850402832,0.8364817500114441,0.8541383147239685,0.8497242331504822,0.8717949390411377,0.8784162402153015,0.8695878982543945,0.8784162402153015,0.8519312739372253,0.8541383147239685,0.8651737570762634,0.8740020394325256,0.8695878982543945,0.8740020394325256,0.8717949390411377,0.8651737570762634,0.8717949390411377,0.8607596158981323,0.8607596158981323,0.8541383147239685,0.8607596158981323,0.8519312739372253,0.8519312739372253,0.8364817500114441,0.838688850402832,0.8497242331504822,0.8519312739372253,0.8519312739372253,0.8673808574676514,0.900486946105957,0.9909770488739014,1.0902955532073975,1.1984422206878662,1.0902955532073975,0.995391309261322,0.9843558669090271,0.9843558669090271,0.9710537195205688,0.9688366651535034,0.9466664791107178,0.9644025564193726,0.940015435218811,0.9377984404563904,0.9865727424621582,0.9444494843482971,0.920062243938446,0.9178452491760254,0.9156282544136047,0.928930401802063,0.9377984404563904,0.9466664791107178,0.9555345177650452,1.002091884613037,1.002091884613037,1.033130168914795,1.0597344636917114,1.0442153215408325,1.0663853883743286,1.0885555744171143,1.0242620706558228,0.9932239055633545,0.9843558669090271,1.0264791250228882,1.0220451354980469,1.0153940916061401,0.9998749494552612,0.9621856808662415,0.9555345177650452,0.9644025564193726,0.9865727424621582,0.9910067915916443,0.997657835483551,1.0264791250228882,1.0109599828720093,1.0109599828720093,1.004309058189392,0.9865727424621582,1.0087430477142334,0.9865727424621582,0.9821387529373169,1.0153940916061401,1.0264791250228882,1.0309131145477295,1.0242620706558228,1.0065258741378784,0.997657835483551,1.0065258741378784,1.0242620706558228,1.0109599828720093,1.002091884613037,0.997657835483551,1.004309058189392,1.0087430477142334,1.057517409324646,1.0553003549575806,1.0530833005905151,1.028696060180664,1.0109599828720093,1.028696060180664,1.028696060180664,0.9910067915916443,0.9954409003257751,0.9865727424621582,0.9821387529373169,0.9843558669090271,0.9865727424621582,0.997657835483551,0.997657835483551,0.9843558669090271,0.9821387529373169,0.9533175230026245,0.9422324299812317,0.9333643913269043,0.9444494843482971,0.9333643913269043,0.9533175230026245,0.9710537195205688,0.9666196703910828,0.959968626499176,0.9688366651535034,0.9621856808662415,0.9644025564193726,0.9732707142829895,0.9555345177650452,0.948883593082428,0.9444494843482971,0.9377984404563904,0.9355813264846802,0.920062243938446,0.9111942052841187,0.8823729753494263,0.8823729753494263,0.9001091122627258,0.8956750631332397,0.9045431613922119,0.9023261666297913,0.9178452491760254,0.9134112000465393,0.9089770913124084,0.9111942052841187,0.9023261666297913,0.920062243938446,0.920062243938446,0.9244963526725769,0.9244963526725769,0.9222792387008667,0.9333643913269043,0.928930401802063,0.9267132878303528,0.9222792387008667,0.928930401802063,0.928930401802063,0.9377984404563904,0.9422324299812317,0.940015435218811,0.9267132878303528,0.9267132878303528,0.9222792387008667,0.9001091122627258,0.9001091122627258,0.8890240788459778,0.8978921175003052,0.8978921175003052,0.9023261666297913,0.9001091122627258,0.8956750631332397,0.9023261666297913,0.8845899701118469,0.8868070244789124,0.862419843673706,0.8757219314575195,0.9045431613922119,0.8956750631332397,0.8934581279754639,0.9067602157592773,0.9045431613922119,0.8978921175003052,0.8845899701118469,0.8602028489112854,0.8646368384361267,0.8424666523933411,0.8446837067604065,0.8269475698471069,0.8335986137390137,0.8469006419181824,0.8469006419181824,0.8535517454147339,0.8446837067604065,0.8491176962852478,0.8358156085014343,0.8202964663505554,0.8136454224586487,0.8202964663505554,0.8158624768257141,0.8047773838043213,0.8092114329338074,0.811428427696228,0.7981262803077698,0.7781731486320496,0.7737391591072083,0.7759560942649841,0.7826071977615356,0.787041187286377,0.8025602698326111,0.8247305154800415,0.8335986137390137,0.8313815593719482,0.8313815593719482,0.8313815593719482,0.8313815593719482,0.8358156085014343,0.8313815593719482,0.8402496576309204,0.838032603263855,0.8469006419181824,0.8402496576309204,0.8291645646095276,0.8225135207176208,0.8291645646095276,0.8402496576309204,0.8469006419181824,0.8513347506523132,0.8513347506523132,0.8469006419181824,0.8446837067604065,0.8535517454147339,0.8535517454147339,0.8557687401771545,0.8491176962852478,0.8513347506523132,0.8513347506523132,0.8335986137390137,0.8402496576309204,0.8579857349395752,0.8513347506523132,0.8579857349395752,0.8646368384361267,0.8513347506523132,0.8668538928031921,0.8757219314575195,0.8668538928031921,0.9023261666297913,0.8912410140037537,0.8934581279754639,0.8868070244789124,0.8868070244789124,0.8757219314575195,0.8712879419326782,0.862419843673706,0.8535517454147339,0.8513347506523132,0.8557687401771545,0.8801559805870056,0.8712879419326782,0.877938985824585,0.8868070244789124,0.9156282544136047,0.9644025564193726,0.9577516317367554,0.9377984404563904,0.9311472773551941,0.920062243938446,0.9222792387008667,0.9178452491760254,0.9134112000465393,0.920062243938446,0.9244963526725769,0.920062243938446,0.9444494843482971,0.9333643913269043,0.9222263097763062,0.9355919361114502,0.9355919361114502,0.9333643913269043,0.928909182548523,0.9177711009979248,0.9355919361114502,0.9333643913269043,0.9333643913269043,0.9355919361114502,0.9311367273330688,0.9177711009979248,0.9177711009979248,0.9044054746627808,0.9066331386566162,0.9044054746627808,0.9133158922195435,0.9110883474349976,0.9044054746627808,0.9155435562133789,0.9133158922195435,0.9110883474349976,0.9066331386566162,0.8954951167106628,0.8932675719261169,0.8932675719261169,0.8954951167106628,0.8954951167106628,0.8776743412017822,0.8754467368125916,0.8754467368125916,0.870991587638855,0.8665363788604736,0.8732191920280457,0.870991587638855,0.8799019455909729,0.8799019455909729,0.8843570947647095,0.8843570947647095,0.8754467368125916,0.8531706929206848,0.8420327305793762,0.8286671042442322,0.8353499174118042,0.8242118954658508,0.8442603349685669,0.8420327305793762,0.8487155437469482,0.8464879393577576,0.8442603349685669,0.8531706929206848,0.864308774471283,0.8776743412017822,0.8799019455909729,0.8821294903755188,0.8821294903755188,0.8620811104774475,0.8754467368125916,0.8799019455909729,0.870991587638855,0.8732191920280457,0.8754467368125916,0.8954951167106628,0.8954951167106628,0.8888123035430908,0.8888123035430908,0.8999503254890442,0.8977227807044983,0.9110883474349976,0.9088606834411621,0.9199987649917603,0.9244539737701416,0.9378196001052856,0.9400471448898315,0.9378196001052856,0.9445022940635681,0.9266815185546875,0.9400471448898315,0.9311367273330688,0.9222263097763062,0.9244539737701416,0.9534127116203308,0.9333643913269043,0.9088606834411621,0.9155435562133789,0.8910399079322815,0.9021779894828796,0.9333643913269043,0.9445022940635681,0.9378196001052856,0.9244539737701416,1.0180131196975708,0.9912819266319275,0.9979647994041443,0.9935095906257629,1.011330246925354,1.011330246925354,1.053654670715332,1.0402891635894775,1.0001921653747559,0.9823715090751648,0.9979647994041443,1.0180131196975708,1.0402891635894775,0.9912819266319275,1.011330246925354,0.9979647994041443,0.9845991730690002,0.9890543818473816,0.9578679203987122,0.9556402564048767,0.9734610915184021,0.9712335467338562,0.9868267178535461,1.0848411321640015,1.0781583786010742,1.0892963409423828,1.0603375434875488,1.0603375434875488,1.0581098794937134,1.053654670715332,1.0291510820388794,1.0269235372543335,0.9912819266319275,1.0046474933624268,1.0135579109191895,0.9935095906257629,1.0046474933624268,1.0068750381469727,1.0135579109191895,1.0135579109191895,1.0358339548110962,1.0402891635894775,1.0269235372543335,1.0135579109191895,1.0135579109191895,1.0202406644821167,1.0269235372543335,1.0425167083740234,1.0692479610443115,1.0581098794937134,1.0603375434875488,1.0803859233856201,1.0826135873794556,1.067020297050476,1.0692479610443115,1.0647927522659302,1.0915240049362183,1.1182550191879272,1.1026619672775269,1.0959792137145996,1.0781583786010742,1.0848411321640015,1.0848411321640015,1.0781583786010742,1.0714755058288574,1.0514271259307861,1.0737031698226929,1.0647927522659302,1.0959792137145996,1.0647927522659302,1.0447443723678589,1.0581098794937134,0.9935095906257629,1.0046474933624268,0.9801439642906189,0.9890543818473816,1.009102702140808,1.0313787460327148,1.0358339548110962,1.038061499595642,1.053654670715332,1.0269235372543335,1.0224683284759521,1.053654670715332,1.0469719171524048,1.0514271259307861,1.053654670715332,1.0491995811462402,1.0447443723678589,1.009102702140808,1.0291510820388794,1.011330246925354,1.0180131196975708,1.0180131196975708,1.0737031698226929,1.0514271259307861,1.0313787460327148,0.9912819266319275,0.9645506739616394,0.9912819266319275,1.009102702140808,0.9779163002967834,0.9133158922195435,0.9355919361114502,0.9378196001052856,0.9556402564048767,0.9712335467338562,0.9868267178535461,0.9779163002967834,0.9645506739616394,0.9845991730690002,0.9890543818473816,0.9801439642906189,0.9868267178535461,1.0046474933624268,1.0202406644821167,1.0402891635894775,1.011330246925354,1.024695873260498,1.0046474933624268,1.0358339548110962,1.0336062908172607,1.0447443723678589,1.0336062908172607,1.038061499595642,1.0336062908172607,1.0269235372543335,0.9779163002967834,0.9645506739616394,0.8932675719261169,0.8509430885314941,0.8620811104774475,0.8487155437469482,0.8910399079322815,0.928909182548523,0.9044054746627808,0.928909182548523,0.9422747492790222,0.928909182548523,0.9333643913269043,0.9511850476264954,0.9690058827400208,0.9489575028419495,0.9578679203987122,0.9534127116203308,0.9511850476264954,0.9511850476264954,0.9110883474349976,0.9333643913269043,0.9534127116203308,0.9578679203987122,1.011330246925354,1.009102702140808,1.0313787460327148,1.0135579109191895,1.011330246925354,1.0046474933624268,0.9957371354103088,0.9823715090751648,0.9890543818473816,1.009102702140808,1.009102702140808,1.0135579109191895,1.0157854557037354,1.002331256866455,0.9956042766571045,1.0090584754943848,0.9754230976104736,1.0113006830215454,1.002331256866455,1.0247548818588257,1.0269972085952759,1.0202702283859253,1.0471783876419067,1.0382089614868164,1.0471783876419067,1.0382089614868164,1.0180277824401855,1.0157854557037354,1.0247548818588257,1.0269972085952759,1.0382089614868164,1.0426937341690063,1.022512435913086,0.9933618903160095,1.0180277824401855,1.0247548818588257,1.0337241888046265,1.0359665155410767,1.0426937341690063,1.0539053678512573,1.0561476945877075,1.044935941696167,1.0583900213241577,1.0337241888046265,1.0202702283859253,0.9619690179824829,0.941787838935852,0.9507572054862976,1.044935941696167,1.0920253992080688,1.0897830724716187,1.1009947061538696,1.1705076694488525,1.1346300840377808,1.1234183311462402,1.136872410774231,1.1346300840377808,1.1054794788360596,1.1099642515182495,1.12117600440979,1.136872410774231,1.1009947061538696,1.1166912317276,1.1032371520996094,1.071844220161438,1.0830559730529785,1.0628747940063477,1.0740865468978882,1.0875407457351685,1.0830559730529785,1.1054794788360596,1.0808136463165283,1.0696018934249878,1.071844220161438,1.1032371520996094,1.078548789024353,1.0785712003707886,1.022512435913086,1.006816029548645,1.0247548818588257,1.0269972085952759,1.0000889301300049,0.9933618903160095,0.9597266912460327,1.0000889301300049,0.9866349101066589,0.9731808304786682,0.9642114043235779,0.9440301656723022,0.9462724328041077,0.9597266912460327,0.957484245300293,0.9776654243469238,0.979907751083374,0.9776654243469238,0.9619690179824829,0.9821501970291138,0.9485148787498474,0.9126372933387756,0.9126372933387756,0.9283336997032166,0.9350607395172119,0.8857290148735046,0.8767596483230591,0.9036678671836853,0.9059101939201355,0.9059101939201355,0.9216066598892212,0.9260913729667664,0.9238489866256714,0.9238489866256714,0.9373030662536621,0.9507572054862976,0.9507572054862976,0.9597266912460327,0.9574618339538574,0.9619690179824829,0.9552419185638428,0.9485148787498474,0.9462724328041077,0.9373030662536621,0.9395455121994019,0.9485148787498474,0.9507572054862976,0.9686960577964783,0.9686960577964783,0.9686960577964783,0.9754230976104736,0.9866349101066589,1.0045737028121948,1.0269972085952759,1.0359665155410767,1.0090584754943848,0.9911195635795593,1.0202702283859253,1.0539053678512573,1.0314818620681763,1.0382089614868164,1.0157854557037354,1.006816029548645,1.0269972085952759,1.006816029548645,0.9933618903160095,0.9866349101066589,1.0180277824401855,1.0045737028121948,1.0157854557037354,1.0516630411148071,1.0426937341690063,1.0426937341690063,1.0180277824401855,0.9911195635795593,0.9888771176338196,0.9866349101066589,0.9642114043235779,0.9552419185638428,0.9709383845329285,0.9731808304786682,0.9686960577964783,0.9686960577964783,0.9709383845329285,0.979907751083374,0.9866349101066589,1.0000889301300049,1.0090584754943848,1.0180277824401855,1.0113006830215454,1.0202702283859253,1.0180277824401855,1.0090584754943848,1.0247548818588257,1.0337241888046265,1.0382089614868164,1.0292396545410156,1.0202702283859253,1.0090584754943848,1.01352059841156,1.0337241888046265,1.0359665155410767,1.0382089614868164,1.0359665155410767,1.0404512882232666,1.0269972085952759,1.0516630411148071,1.049420714378357,1.049420714378357,1.049420714378357,1.0583900213241577,1.0337241888046265,1.0382089614868164,1.0269972085952759,1.0135430097579956,1.0090584754943848,0.9933618903160095,1.006816029548645,0.9888771176338196,0.9978466033935547,0.9933618903160095,1.01352059841156,1.0292396545410156,1.0404512882232666,1.0404512882232666,1.0426937341690063,1.0471783876419067,1.044935941696167,1.0382089614868164,1.0673595666885376,1.076328992843628,1.0606324672698975,1.0426937341690063,1.044935941696167,1.022512435913086,1.0269972085952759,1.0673595666885376,1.1009947061538696,1.0965100526809692,1.094267725944519,1.0875407457351685,1.0875407457351685,1.1054794788360596,1.0740865468978882,1.0897830724716187,1.0606324672698975,1.0651172399520874,1.0696018934249878,1.11444890499115,1.1503266096115112,1.1794770956039429,1.1772347688674927,1.1906888484954834,1.1660230159759521,1.2041429281234741,1.224324107170105,1.2108701467514038,1.181719422340393,1.1705076694488525,1.1794770956039429,1.159295916557312,1.1615382432937622,1.1503266096115112,1.1301454305648804,1.11444890499115,1.1256606578826904,1.1099642515182495,1.136872410774231,1.1301454305648804,1.1323877573013306,1.1705076694488525,1.1974159479141235,1.1705076694488525,1.1951736211776733,1.1906888484954834,1.1839618682861328,1.1727501153945923,1.1727501153945923,1.181719422340393,1.1548112630844116,1.1323877573013306,1.1077219247817993,1.071844220161438,1.0539053678512573,1.0292396545410156,1.049420714378357,1.0539053678512573,1.0583900213241577,1.0606324672698975,1.0673595666885376,1.0628747940063477,1.0673595666885376,1.071844220161438,1.0808136463165283,1.0875407457351685,1.0808136463165283,1.0628747940063477,1.0785712003707886,1.0830559730529785,1.076328992843628,1.0875407457351685,1.1054794788360596,1.1054794788360596,1.1189335584640503,1.1166912317276,1.1279031038284302,1.1279031038284302,1.12117600440979,1.1503266096115112,1.1660230159759521,1.163780689239502,1.1660230159759521,1.1727501153945923,1.163780689239502,1.1548112630844116,1.143599510192871,1.1323877573013306,1.1346300840377808,1.1122065782546997,1.11444890499115,1.1122065782546997,1.1189335584640503,1.1301454305648804,1.1009947061538696,1.098752498626709,1.0987526178359985,1.0829269886016846,1.0829269886016846,0.997016191482544,1.0083203315734863,1.0354499816894531,1.0105810165405273,0.9924945831298828,1.001537799835205,1.0173635482788086,1.0105810165405273,1.0128419399261475,0.997016191482544,0.9947554469108582,1.0128419399261475,1.0151026248931885,1.0060594081878662,0.9992770552635193,1.0083203315734863,1.0128419399261475,1.0037987232208252,0.997016191482544,0.990233838558197,1.0037987232208252,0.990233838558197,0.9789297580718994,0.9744081497192383,0.9857122302055359,0.9495392441749573,0.9518001079559326,0.9427568912506104,0.9495392441749573,0.9359744191169739,0.9337136745452881,0.9359744191169739,0.9178879857063293,0.9111055731773376,0.9111055731773376,0.8907582759857178,0.9133663177490234,0.9201487898826599,0.9269312024116516,0.9269312024116516,0.9382352828979492,0.9427568912506104,0.940496027469635,0.9359744191169739,0.9359744191169739,0.9540608525276184,0.9585824608802795,0.9585824608802795,0.965364933013916,0.9676257967948914,0.9766690135002136,0.997016191482544,0.9947554469108582,0.9879729747772217,0.9947554469108582,0.9947554469108582,1.0105810165405273,1.001537799835205,1.0037987232208252,0.9992770552635193,0.997016191482544,1.0083203315734863,0.9879729747772217,0.990233838558197],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"hovertemplate\":\"\\u003cbr\\u003e\\u51c0\\u503c:%{y:.2f}\",\"mode\":\"lines\",\"name\":\"\\u7b56\\u7565\",\"showlegend\":true,\"x\":[\"2018-12-27\",\"2018-12-28\",\"2019-01-02\",\"2019-01-03\",\"2019-01-04\",\"2019-01-07\",\"2019-01-08\",\"2019-01-09\",\"2019-01-10\",\"2019-01-11\",\"2019-01-14\",\"2019-01-15\",\"2019-01-16\",\"2019-01-17\",\"2019-01-18\",\"2019-01-21\",\"2019-01-22\",\"2019-01-23\",\"2019-01-24\",\"2019-01-25\",\"2019-01-28\",\"2019-01-29\",\"2019-01-30\",\"2019-01-31\",\"2019-02-01\",\"2019-02-11\",\"2019-02-12\",\"2019-02-13\",\"2019-02-14\",\"2019-02-15\",\"2019-02-18\",\"2019-02-19\",\"2019-02-20\",\"2019-02-21\",\"2019-02-22\",\"2019-02-25\",\"2019-02-26\",\"2019-02-27\",\"2019-02-28\",\"2019-03-01\",\"2019-03-04\",\"2019-03-05\",\"2019-03-06\",\"2019-03-07\",\"2019-03-08\",\"2019-03-11\",\"2019-03-12\",\"2019-03-13\",\"2019-03-14\",\"2019-03-15\",\"2019-03-18\",\"2019-03-19\",\"2019-03-20\",\"2019-03-21\",\"2019-03-22\",\"2019-03-25\",\"2019-03-26\",\"2019-03-27\",\"2019-03-28\",\"2019-03-29\",\"2019-04-01\",\"2019-04-02\",\"2019-04-03\",\"2019-04-04\",\"2019-04-08\",\"2019-04-09\",\"2019-04-10\",\"2019-04-11\",\"2019-04-12\",\"2019-04-15\",\"2019-04-16\",\"2019-04-17\",\"2019-04-18\",\"2019-04-19\",\"2019-04-22\",\"2019-04-23\",\"2019-04-24\",\"2019-04-25\",\"2019-04-26\",\"2019-04-29\",\"2019-04-30\",\"2019-05-06\",\"2019-05-07\",\"2019-05-08\",\"2019-05-09\",\"2019-05-10\",\"2019-05-13\",\"2019-05-14\",\"2019-05-15\",\"2019-05-16\",\"2019-05-17\",\"2019-05-20\",\"2019-05-21\",\"2019-05-22\",\"2019-05-23\",\"2019-05-24\",\"2019-05-27\",\"2019-05-28\",\"2019-05-29\",\"2019-05-30\",\"2019-05-31\",\"2019-06-03\",\"2019-06-04\",\"2019-06-05\",\"2019-06-06\",\"2019-06-10\",\"2019-06-11\",\"2019-06-12\",\"2019-06-13\",\"2019-06-14\",\"2019-06-17\",\"2019-06-18\",\"2019-06-19\",\"2019-06-20\",\"2019-06-21\",\"2019-06-24\",\"2019-06-25\",\"2019-06-26\",\"2019-06-27\",\"2019-06-28\",\"2019-07-01\",\"2019-07-02\",\"2019-07-03\",\"2019-07-04\",\"2019-07-05\",\"2019-07-08\",\"2019-07-09\",\"2019-07-10\",\"2019-07-11\",\"2019-07-12\",\"2019-07-15\",\"2019-07-16\",\"2019-07-17\",\"2019-07-18\",\"2019-07-19\",\"2019-07-22\",\"2019-07-23\",\"2019-07-24\",\"2019-07-25\",\"2019-07-26\",\"2019-07-29\",\"2019-07-30\",\"2019-07-31\",\"2019-08-01\",\"2019-08-02\",\"2019-08-05\",\"2019-08-06\",\"2019-08-07\",\"2019-08-08\",\"2019-08-09\",\"2019-08-12\",\"2019-08-13\",\"2019-08-14\",\"2019-08-15\",\"2019-08-16\",\"2019-08-19\",\"2019-08-20\",\"2019-08-21\",\"2019-08-22\",\"2019-08-23\",\"2019-08-26\",\"2019-08-27\",\"2019-08-28\",\"2019-08-29\",\"2019-08-30\",\"2019-09-02\",\"2019-09-03\",\"2019-09-04\",\"2019-09-05\",\"2019-09-06\",\"2019-09-09\",\"2019-09-10\",\"2019-09-11\",\"2019-09-12\",\"2019-09-16\",\"2019-09-17\",\"2019-09-18\",\"2019-09-19\",\"2019-09-20\",\"2019-09-23\",\"2019-09-24\",\"2019-09-25\",\"2019-09-26\",\"2019-09-27\",\"2019-09-30\",\"2019-10-08\",\"2019-10-09\",\"2019-10-10\",\"2019-10-11\",\"2019-10-14\",\"2019-10-15\",\"2019-10-16\",\"2019-10-17\",\"2019-10-18\",\"2019-10-21\",\"2019-10-22\",\"2019-10-23\",\"2019-10-24\",\"2019-10-25\",\"2019-10-28\",\"2019-10-29\",\"2019-10-30\",\"2019-10-31\",\"2019-11-01\",\"2019-11-04\",\"2019-11-05\",\"2019-11-06\",\"2019-11-07\",\"2019-11-08\",\"2019-11-11\",\"2019-11-12\",\"2019-11-13\",\"2019-11-14\",\"2019-11-15\",\"2019-11-18\",\"2019-11-19\",\"2019-11-20\",\"2019-11-21\",\"2019-11-22\",\"2019-11-25\",\"2019-11-26\",\"2019-11-27\",\"2019-11-28\",\"2019-11-29\",\"2019-12-02\",\"2019-12-03\",\"2019-12-04\",\"2019-12-05\",\"2019-12-06\",\"2019-12-09\",\"2019-12-10\",\"2019-12-11\",\"2019-12-12\",\"2019-12-13\",\"2019-12-16\",\"2019-12-17\",\"2019-12-18\",\"2019-12-19\",\"2019-12-20\",\"2019-12-23\",\"2019-12-24\",\"2019-12-25\",\"2019-12-26\",\"2019-12-27\",\"2019-12-30\",\"2019-12-31\",\"2020-01-02\",\"2020-01-03\",\"2020-01-06\",\"2020-01-07\",\"2020-01-08\",\"2020-01-09\",\"2020-01-10\",\"2020-01-13\",\"2020-01-14\",\"2020-01-15\",\"2020-01-16\",\"2020-01-17\",\"2020-01-20\",\"2020-01-21\",\"2020-01-22\",\"2020-01-23\",\"2020-02-03\",\"2020-02-04\",\"2020-02-05\",\"2020-02-06\",\"2020-02-07\",\"2020-02-10\",\"2020-02-11\",\"2020-02-12\",\"2020-02-13\",\"2020-02-14\",\"2020-02-17\",\"2020-02-18\",\"2020-02-19\",\"2020-02-20\",\"2020-02-21\",\"2020-02-24\",\"2020-02-25\",\"2020-02-26\",\"2020-02-27\",\"2020-02-28\",\"2020-03-02\",\"2020-03-03\",\"2020-03-04\",\"2020-03-05\",\"2020-03-06\",\"2020-03-09\",\"2020-03-10\",\"2020-03-11\",\"2020-03-12\",\"2020-03-13\",\"2020-03-16\",\"2020-03-17\",\"2020-03-18\",\"2020-03-19\",\"2020-03-20\",\"2020-03-23\",\"2020-03-24\",\"2020-03-25\",\"2020-03-26\",\"2020-03-27\",\"2020-03-30\",\"2020-03-31\",\"2020-04-01\",\"2020-04-02\",\"2020-04-03\",\"2020-04-07\",\"2020-04-08\",\"2020-04-09\",\"2020-04-10\",\"2020-04-13\",\"2020-04-14\",\"2020-04-15\",\"2020-04-16\",\"2020-04-17\",\"2020-04-20\",\"2020-04-21\",\"2020-04-22\",\"2020-04-23\",\"2020-04-24\",\"2020-04-27\",\"2020-04-28\",\"2020-04-29\",\"2020-04-30\",\"2020-05-06\",\"2020-05-07\",\"2020-05-08\",\"2020-05-11\",\"2020-05-12\",\"2020-05-13\",\"2020-05-14\",\"2020-05-15\",\"2020-05-18\",\"2020-05-19\",\"2020-05-20\",\"2020-05-21\",\"2020-05-22\",\"2020-05-25\",\"2020-05-26\",\"2020-05-27\",\"2020-05-28\",\"2020-05-29\",\"2020-06-01\",\"2020-06-02\",\"2020-06-03\",\"2020-06-04\",\"2020-06-05\",\"2020-06-08\",\"2020-06-09\",\"2020-06-10\",\"2020-06-11\",\"2020-06-12\",\"2020-06-15\",\"2020-06-16\",\"2020-06-17\",\"2020-06-18\",\"2020-06-19\",\"2020-06-22\",\"2020-06-23\",\"2020-06-24\",\"2020-06-29\",\"2020-06-30\",\"2020-07-01\",\"2020-07-02\",\"2020-07-03\",\"2020-07-06\",\"2020-07-07\",\"2020-07-08\",\"2020-07-09\",\"2020-07-10\",\"2020-07-13\",\"2020-07-14\",\"2020-07-15\",\"2020-07-16\",\"2020-07-17\",\"2020-07-20\",\"2020-07-21\",\"2020-07-22\",\"2020-07-23\",\"2020-07-24\",\"2020-07-27\",\"2020-07-28\",\"2020-07-29\",\"2020-07-30\",\"2020-07-31\",\"2020-08-03\",\"2020-08-04\",\"2020-08-05\",\"2020-08-06\",\"2020-08-07\",\"2020-08-10\",\"2020-08-11\",\"2020-08-12\",\"2020-08-13\",\"2020-08-14\",\"2020-08-17\",\"2020-08-18\",\"2020-08-19\",\"2020-08-20\",\"2020-08-21\",\"2020-08-24\",\"2020-08-25\",\"2020-08-26\",\"2020-08-27\",\"2020-08-28\",\"2020-08-31\",\"2020-09-01\",\"2020-09-02\",\"2020-09-03\",\"2020-09-04\",\"2020-09-07\",\"2020-09-08\",\"2020-09-09\",\"2020-09-10\",\"2020-09-11\",\"2020-09-14\",\"2020-09-15\",\"2020-09-16\",\"2020-09-17\",\"2020-09-18\",\"2020-09-21\",\"2020-09-22\",\"2020-09-23\",\"2020-09-24\",\"2020-09-25\",\"2020-09-28\",\"2020-09-29\",\"2020-09-30\",\"2020-10-09\",\"2020-10-12\",\"2020-10-13\",\"2020-10-14\",\"2020-10-15\",\"2020-10-16\",\"2020-10-19\",\"2020-10-20\",\"2020-10-21\",\"2020-10-22\",\"2020-10-23\",\"2020-10-26\",\"2020-10-27\",\"2020-10-28\",\"2020-10-29\",\"2020-10-30\",\"2020-11-02\",\"2020-11-03\",\"2020-11-04\",\"2020-11-05\",\"2020-11-06\",\"2020-11-09\",\"2020-11-10\",\"2020-11-11\",\"2020-11-12\",\"2020-11-13\",\"2020-11-16\",\"2020-11-17\",\"2020-11-18\",\"2020-11-19\",\"2020-11-20\",\"2020-11-23\",\"2020-11-24\",\"2020-11-25\",\"2020-11-26\",\"2020-11-27\",\"2020-11-30\",\"2020-12-01\",\"2020-12-02\",\"2020-12-03\",\"2020-12-04\",\"2020-12-07\",\"2020-12-08\",\"2020-12-09\",\"2020-12-10\",\"2020-12-11\",\"2020-12-14\",\"2020-12-15\",\"2020-12-16\",\"2020-12-17\",\"2020-12-18\",\"2020-12-21\",\"2020-12-22\",\"2020-12-23\",\"2020-12-24\",\"2020-12-25\",\"2020-12-28\",\"2020-12-29\",\"2020-12-30\",\"2020-12-31\",\"2021-01-04\",\"2021-01-05\",\"2021-01-06\",\"2021-01-07\",\"2021-01-08\",\"2021-01-11\",\"2021-01-12\",\"2021-01-13\",\"2021-01-14\",\"2021-01-15\",\"2021-01-18\",\"2021-01-19\",\"2021-01-20\",\"2021-01-21\",\"2021-01-22\",\"2021-01-25\",\"2021-01-26\",\"2021-01-27\",\"2021-01-28\",\"2021-01-29\",\"2021-02-01\",\"2021-02-02\",\"2021-02-03\",\"2021-02-04\",\"2021-02-05\",\"2021-02-08\",\"2021-02-09\",\"2021-02-10\",\"2021-02-18\",\"2021-02-19\",\"2021-02-22\",\"2021-02-23\",\"2021-02-24\",\"2021-02-25\",\"2021-02-26\",\"2021-03-01\",\"2021-03-02\",\"2021-03-03\",\"2021-03-04\",\"2021-03-05\",\"2021-03-08\",\"2021-03-09\",\"2021-03-10\",\"2021-03-11\",\"2021-03-12\",\"2021-03-15\",\"2021-03-16\",\"2021-03-17\",\"2021-03-18\",\"2021-03-19\",\"2021-03-22\",\"2021-03-23\",\"2021-03-24\",\"2021-03-25\",\"2021-03-26\",\"2021-03-29\",\"2021-03-30\",\"2021-03-31\",\"2021-04-01\",\"2021-04-02\",\"2021-04-06\",\"2021-04-07\",\"2021-04-08\",\"2021-04-09\",\"2021-04-12\",\"2021-04-13\",\"2021-04-14\",\"2021-04-15\",\"2021-04-16\",\"2021-04-19\",\"2021-04-20\",\"2021-04-21\",\"2021-04-22\",\"2021-04-23\",\"2021-04-26\",\"2021-04-27\",\"2021-04-28\",\"2021-04-29\",\"2021-04-30\",\"2021-05-06\",\"2021-05-07\",\"2021-05-10\",\"2021-05-11\",\"2021-05-12\",\"2021-05-13\",\"2021-05-14\",\"2021-05-17\",\"2021-05-18\",\"2021-05-19\",\"2021-05-20\",\"2021-05-21\",\"2021-05-24\",\"2021-05-25\",\"2021-05-26\",\"2021-05-27\",\"2021-05-28\",\"2021-05-31\",\"2021-06-01\",\"2021-06-02\",\"2021-06-03\",\"2021-06-04\",\"2021-06-07\",\"2021-06-08\",\"2021-06-09\",\"2021-06-10\",\"2021-06-11\",\"2021-06-15\",\"2021-06-16\",\"2021-06-17\",\"2021-06-18\",\"2021-06-21\",\"2021-06-22\",\"2021-06-23\",\"2021-06-24\",\"2021-06-25\",\"2021-06-28\",\"2021-06-29\",\"2021-06-30\",\"2021-07-01\",\"2021-07-02\",\"2021-07-05\",\"2021-07-06\",\"2021-07-07\",\"2021-07-08\",\"2021-07-09\",\"2021-07-12\",\"2021-07-13\",\"2021-07-14\",\"2021-07-15\",\"2021-07-16\",\"2021-07-19\",\"2021-07-20\",\"2021-07-21\",\"2021-07-22\",\"2021-07-23\",\"2021-07-26\",\"2021-07-27\",\"2021-07-28\",\"2021-07-29\",\"2021-07-30\",\"2021-08-02\",\"2021-08-03\",\"2021-08-04\",\"2021-08-05\",\"2021-08-06\",\"2021-08-09\",\"2021-08-10\",\"2021-08-11\",\"2021-08-12\",\"2021-08-13\",\"2021-08-16\",\"2021-08-17\",\"2021-08-18\",\"2021-08-19\",\"2021-08-20\",\"2021-08-23\",\"2021-08-24\",\"2021-08-25\",\"2021-08-26\",\"2021-08-27\",\"2021-08-30\",\"2021-08-31\",\"2021-09-01\",\"2021-09-02\",\"2021-09-03\",\"2021-09-06\",\"2021-09-07\",\"2021-09-08\",\"2021-09-09\",\"2021-09-10\",\"2021-09-13\",\"2021-09-14\",\"2021-09-15\",\"2021-09-16\",\"2021-09-17\",\"2021-09-22\",\"2021-09-23\",\"2021-09-24\",\"2021-09-27\",\"2021-09-28\",\"2021-09-29\",\"2021-09-30\",\"2021-10-08\",\"2021-10-11\",\"2021-10-12\",\"2021-10-13\",\"2021-10-14\",\"2021-10-15\",\"2021-10-18\",\"2021-10-19\",\"2021-10-20\",\"2021-10-21\",\"2021-10-22\",\"2021-10-25\",\"2021-10-26\",\"2021-10-27\",\"2021-10-28\",\"2021-10-29\",\"2021-11-01\",\"2021-11-02\",\"2021-11-03\",\"2021-11-04\",\"2021-11-05\",\"2021-11-08\",\"2021-11-09\",\"2021-11-10\",\"2021-11-11\",\"2021-11-12\",\"2021-11-15\",\"2021-11-16\",\"2021-11-17\",\"2021-11-18\",\"2021-11-19\",\"2021-11-22\",\"2021-11-23\",\"2021-11-24\",\"2021-11-25\",\"2021-11-26\",\"2021-11-29\",\"2021-11-30\",\"2021-12-01\",\"2021-12-02\",\"2021-12-03\",\"2021-12-06\",\"2021-12-07\",\"2021-12-08\",\"2021-12-09\",\"2021-12-10\",\"2021-12-13\",\"2021-12-14\",\"2021-12-15\",\"2021-12-16\",\"2021-12-17\",\"2021-12-20\",\"2021-12-21\",\"2021-12-22\",\"2021-12-23\",\"2021-12-24\",\"2021-12-27\",\"2021-12-28\",\"2021-12-29\",\"2021-12-30\",\"2021-12-31\",\"2022-01-04\",\"2022-01-05\",\"2022-01-06\",\"2022-01-07\",\"2022-01-10\",\"2022-01-11\",\"2022-01-12\",\"2022-01-13\",\"2022-01-14\",\"2022-01-17\",\"2022-01-18\",\"2022-01-19\",\"2022-01-20\",\"2022-01-21\",\"2022-01-24\",\"2022-01-25\",\"2022-01-26\",\"2022-01-27\",\"2022-01-28\",\"2022-02-07\",\"2022-02-08\",\"2022-02-09\",\"2022-02-10\",\"2022-02-11\",\"2022-02-14\",\"2022-02-15\",\"2022-02-16\",\"2022-02-17\",\"2022-02-18\",\"2022-02-21\",\"2022-02-22\",\"2022-02-23\",\"2022-02-24\",\"2022-02-25\",\"2022-02-28\",\"2022-03-01\",\"2022-03-02\",\"2022-03-03\",\"2022-03-04\",\"2022-03-07\",\"2022-03-08\",\"2022-03-09\",\"2022-03-10\",\"2022-03-11\",\"2022-03-14\",\"2022-03-15\",\"2022-03-16\",\"2022-03-17\",\"2022-03-18\",\"2022-03-21\",\"2022-03-22\",\"2022-03-23\",\"2022-03-24\",\"2022-03-25\",\"2022-03-28\",\"2022-03-29\",\"2022-03-30\",\"2022-03-31\",\"2022-04-01\",\"2022-04-06\",\"2022-04-07\",\"2022-04-08\",\"2022-04-11\",\"2022-04-12\",\"2022-04-13\",\"2022-04-14\",\"2022-04-15\",\"2022-04-18\",\"2022-04-19\",\"2022-04-20\",\"2022-04-21\",\"2022-04-22\",\"2022-04-25\",\"2022-04-26\",\"2022-04-27\",\"2022-04-28\",\"2022-04-29\",\"2022-05-05\",\"2022-05-06\",\"2022-05-09\",\"2022-05-10\",\"2022-05-11\",\"2022-05-12\",\"2022-05-13\",\"2022-05-16\",\"2022-05-17\",\"2022-05-18\",\"2022-05-19\",\"2022-05-20\",\"2022-05-23\",\"2022-05-24\",\"2022-05-25\",\"2022-05-26\",\"2022-05-27\",\"2022-05-30\",\"2022-05-31\",\"2022-06-01\",\"2022-06-02\",\"2022-06-06\",\"2022-06-07\",\"2022-06-08\",\"2022-06-09\",\"2022-06-10\",\"2022-06-13\",\"2022-06-14\",\"2022-06-15\",\"2022-06-16\",\"2022-06-17\",\"2022-06-20\",\"2022-06-21\",\"2022-06-22\",\"2022-06-23\",\"2022-06-24\",\"2022-06-27\",\"2022-06-28\",\"2022-06-29\",\"2022-06-30\",\"2022-07-01\",\"2022-07-04\",\"2022-07-05\",\"2022-07-06\",\"2022-07-07\",\"2022-07-08\",\"2022-07-11\",\"2022-07-12\",\"2022-07-13\",\"2022-07-14\",\"2022-07-15\",\"2022-07-18\",\"2022-07-19\",\"2022-07-20\",\"2022-07-21\",\"2022-07-22\",\"2022-07-25\",\"2022-07-26\",\"2022-07-27\",\"2022-07-28\",\"2022-07-29\",\"2022-08-01\",\"2022-08-02\",\"2022-08-03\",\"2022-08-04\",\"2022-08-05\",\"2022-08-08\",\"2022-08-09\",\"2022-08-10\",\"2022-08-11\",\"2022-08-12\",\"2022-08-15\",\"2022-08-16\",\"2022-08-17\",\"2022-08-18\",\"2022-08-19\",\"2022-08-22\",\"2022-08-23\",\"2022-08-24\",\"2022-08-25\",\"2022-08-26\",\"2022-08-29\",\"2022-08-30\",\"2022-08-31\",\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\",\"2023-03-02\",\"2023-03-03\",\"2023-03-06\",\"2023-03-07\",\"2023-03-08\",\"2023-03-09\",\"2023-03-10\",\"2023-03-13\",\"2023-03-14\",\"2023-03-15\",\"2023-03-16\",\"2023-03-17\",\"2023-03-20\",\"2023-03-21\",\"2023-03-22\",\"2023-03-23\",\"2023-03-24\",\"2023-03-27\",\"2023-03-28\",\"2023-03-29\",\"2023-03-30\",\"2023-03-31\",\"2023-04-03\",\"2023-04-04\",\"2023-04-06\",\"2023-04-07\",\"2023-04-10\",\"2023-04-11\",\"2023-04-12\",\"2023-04-13\",\"2023-04-14\",\"2023-04-17\",\"2023-04-18\",\"2023-04-19\",\"2023-04-20\",\"2023-04-21\",\"2023-04-24\",\"2023-04-25\",\"2023-04-26\",\"2023-04-27\",\"2023-04-28\",\"2023-05-04\",\"2023-05-05\",\"2023-05-08\",\"2023-05-09\",\"2023-05-10\",\"2023-05-11\",\"2023-05-12\",\"2023-05-15\",\"2023-05-16\",\"2023-05-17\",\"2023-05-18\",\"2023-05-19\",\"2023-05-22\",\"2023-05-23\",\"2023-05-24\",\"2023-05-25\",\"2023-05-26\",\"2023-05-29\",\"2023-05-30\",\"2023-05-31\",\"2023-06-01\",\"2023-06-02\",\"2023-06-05\",\"2023-06-06\",\"2023-06-07\",\"2023-06-08\",\"2023-06-09\",\"2023-06-12\",\"2023-06-13\",\"2023-06-14\",\"2023-06-15\",\"2023-06-16\",\"2023-06-19\",\"2023-06-20\",\"2023-06-21\",\"2023-06-26\",\"2023-06-27\",\"2023-06-28\",\"2023-06-29\",\"2023-06-30\",\"2023-07-03\",\"2023-07-04\",\"2023-07-05\",\"2023-07-06\",\"2023-07-07\",\"2023-07-10\",\"2023-07-11\",\"2023-07-12\",\"2023-07-13\",\"2023-07-14\",\"2023-07-17\",\"2023-07-18\",\"2023-07-19\",\"2023-07-20\",\"2023-07-21\",\"2023-07-24\",\"2023-07-25\",\"2023-07-26\",\"2023-07-27\",\"2023-07-28\",\"2023-07-31\",\"2023-08-01\",\"2023-08-02\",\"2023-08-03\",\"2023-08-04\",\"2023-08-07\",\"2023-08-08\",\"2023-08-09\",\"2023-08-10\",\"2023-08-11\",\"2023-08-14\",\"2023-08-15\",\"2023-08-16\",\"2023-08-17\",\"2023-08-18\",\"2023-08-21\",\"2023-08-22\",\"2023-08-23\",\"2023-08-24\",\"2023-08-25\",\"2023-08-28\",\"2023-08-29\",\"2023-08-30\",\"2023-08-31\",\"2023-09-01\",\"2023-09-04\",\"2023-09-05\",\"2023-09-06\",\"2023-09-07\",\"2023-09-08\",\"2023-09-11\",\"2023-09-12\",\"2023-09-13\",\"2023-09-14\",\"2023-09-15\",\"2023-09-18\",\"2023-09-19\",\"2023-09-20\",\"2023-09-21\",\"2023-09-22\",\"2023-09-25\",\"2023-09-26\",\"2023-09-27\",\"2023-09-28\",\"2023-10-09\",\"2023-10-10\",\"2023-10-11\",\"2023-10-12\",\"2023-10-13\",\"2023-10-16\",\"2023-10-17\",\"2023-10-18\",\"2023-10-19\",\"2023-10-20\",\"2023-10-23\",\"2023-10-24\",\"2023-10-25\",\"2023-10-26\",\"2023-10-27\",\"2023-10-30\",\"2023-10-31\",\"2023-11-01\",\"2023-11-02\",\"2023-11-03\",\"2023-11-06\",\"2023-11-07\",\"2023-11-08\",\"2023-11-09\",\"2023-11-10\",\"2023-11-13\",\"2023-11-14\",\"2023-11-15\",\"2023-11-16\",\"2023-11-17\",\"2023-11-20\",\"2023-11-21\",\"2023-11-22\",\"2023-11-23\",\"2023-11-24\",\"2023-11-27\",\"2023-11-28\",\"2023-11-29\",\"2023-11-30\"],\"y\":[1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,0.9978685514154816,0.9878935514154816,0.9878935514154816,0.9619585514154816,0.9539785514154816,0.9440035514154816,0.9619585514154816,0.9779185514154816,0.9938785514154816,1.0058485514154816,1.0098385514154817,1.0098385514154817,1.0377685514154815,1.0357735514154816,1.0357735514154816,1.0277935514154817,1.0537285514154815,1.1075935514154815,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1154197147793579,1.1320532897787476,1.1215332897787476,1.1089092897787476,1.1320532897787476,1.1236372897787477,1.1236372897787477,1.1362612897787476,1.1404692897787476,1.0962852897787476,1.0878692897787476,1.1004932897787476,1.0899732897787475,1.0752452897787477,1.0752452897787477,1.0962852897787476,1.0983892897787475,1.0878692897787476,1.0794532897787477,1.0752452897787477,1.0668292897787477,1.0542052897787475,1.0542052897787475,1.0373732897787475,1.0478932897787476,1.0752452897787477,1.0689332897787476,1.0752452897787477,1.0710372897787477,1.0689332897787476,1.0794532897787477,1.0836612897787476,1.1004932897787476,1.1051376238865662,1.1051021123051452,1.1050843565144348,1.1050754786190795,1.1050932344097901,1.1050310891423034,1.1051109902005005,1.1050932344097901,1.1050577228283691,1.1050843565144348,1.1050932344097901,1.1049067986073302,1.1048979207119751,1.1048712870259094,1.1048801649212647,1.1048979207119751,1.1049866996655273,1.1049866996655273,1.1049600659794616,1.1049423101887512,1.1049511880841065,1.1048801649212647,1.1048890428166198,1.1049423101887512,1.1049245543980408,1.1049067986073302,1.1048979207119751,1.1049423101887512,1.1049245543980408,1.1048801649212647,1.1047913859677123,1.1047381185955811,1.1046049501652526,1.0985140666142275,1.1033158224049378,1.093712310823517,1.105716700300293,1.1009149445095825,1.105716700300293,1.1033158224049378,1.105716700300293,1.136928112939911,1.136928112939911,1.1393289908352662,1.136928112939911,1.1345272350445557,1.1201219676724243,1.1441307466259767,1.146531624521332,1.146531624521332,1.1393289908352662,1.1561351361027528,1.1537342582073975,1.1609368918934633,1.1633377697888185,1.1633377697888185,1.1801439150563051,1.1801439150563051,1.1777430371609499,1.1825447929516602,1.1801439150563051,1.1537342582073975,1.1513333803120422,1.1537342582073975,1.158536013998108,1.1393289908352662,1.1417298687306214,1.1225228455677796,1.105716700300293,1.115320211881714,1.1009149445095825,1.1129193339863588,1.1201219676724243,1.1249237234631349,1.1321263571492006,1.1513333803120422,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1535652485265875,1.1578906351439429,1.1502016351439426,1.1450756351439428,1.134823635143943,1.137386635143943,1.1502016351439426,1.137386635143943,1.1425126351439432,1.1399496351439427,1.160453635143943,1.155327635143943,1.160453635143943,1.1527646351439431,1.160453635143943,1.1578906351439429,1.1578906351439429,1.1527646351439431,1.1630166351439428,1.1707056351439429,1.1707056351439429,1.1707056351439429,1.165579635143943,1.1578906351439429,1.173268635143943,1.178394635143943,1.191209635143943,1.1886466351439429,1.1937726351439428,1.1886466351439429,1.168142635143943,1.1758316351439428,1.1707056351439429,1.183520635143943,1.2014616351439429,1.2373436351439429,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2269240493636846,1.2534296754548406,1.2652816754548406,1.2860226754548405,1.2860226754548405,1.3067636754548406,1.2949116754548404,1.3097266754548404,1.2919486754548406,1.2889856754548406,1.3126896754548405,1.3245416754548405,1.3156526754548405,1.3393566754548405,1.3393566754548405,1.3393566754548405,1.3275046754548405,1.3423196754548403,1.3423196754548403,1.2860226754548405,1.3245416754548405,1.3245416754548405,1.3838016754548406,1.3808386754548405,1.4223206754548405,1.3571346754548406,1.3808386754548405,1.3660236754548405,1.3393566754548405,1.3215786754548404,1.2978746754548405,1.2978746754548405,1.2741706754548405,1.3038006754548406,1.3097266754548404,1.2771336754548406,1.2949116754548404,1.3156526754548405,1.3038006754548406,1.3038006754548406,1.2860226754548405,1.2800966754548406,1.2741706754548405,1.2889856754548406,1.2830596754548405,1.2978746754548405,1.3067636754548406,1.3008376754548405,1.2830596754548405,1.2800966754548406,1.2889856754548406,1.2771336754548406,1.2771336754548406,1.2741706754548405,1.2978746754548405,1.2919486754548406,1.3215786754548404,1.3304676754548406,1.3186156754548406,1.3304676754548406,1.2949116754548404,1.2978746754548405,1.3126896754548405,1.3245416754548405,1.3186156754548406,1.3245416754548405,1.3215786754548404,1.3126896754548405,1.3215786754548404,1.3067636754548406,1.3067636754548406,1.2978746754548405,1.3067636754548406,1.2949116754548404,1.2949116754548404,1.2741706754548405,1.2774750968357609,1.2941000968357608,1.2974250968357608,1.2974250968357608,1.320700096835761,1.3705750968357608,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.3903185724422213,1.37502296671926,1.36327096671926,1.37796096671926,1.36327096671926,1.38971296671926,1.41321696671926,1.40734096671926,1.39852696671926,1.41027896671926,1.40146496671926,1.40440296671926,1.4161549667192599,1.39265096671926,1.38383696671926,1.37796096671926,1.3691469667192602,1.36620896671926,1.34564296671926,1.3341499993514012,1.292367999351401,1.2923662092941806,1.3183182092941803,1.3118302092941807,1.3248062092941806,1.3215622092941806,1.3442702092941807,1.3377822092941807,1.3312942092941806,1.3345382092941807,1.3215622092941806,1.3475142092941805,1.3475142092941805,1.3540022092941806,1.3540022092941806,1.3507582092941806,1.3669782092941807,1.3604902092941809,1.3572462092941806,1.3507582092941806,1.3604902092941809,1.3604902092941809,1.373466209294181,1.3799542092941806,1.3767102092941805,1.3572462092941806,1.3572462092941806,1.3507582092941806,1.3183182092941803,1.3183182092941803,1.3020982092941806,1.3150742092941807,1.3150742092941807,1.3215622092941806,1.3183182092941803,1.3118302092941807,1.3215622092941806,1.2956102092941806,1.2988542092941806,1.2631702092941806,1.2826342092941807,1.3248062092941806,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.3175297353860231,1.2999218346221681,1.2999218346221681,1.302901834622168,1.302901834622168,1.2790618346221683,1.2763096027758547,1.2763096027758547,1.2698656027758548,1.2634216027758547,1.2730876027758549,1.2698656027758548,1.2827536027758548,1.2827536027758548,1.2891976027758547,1.2891976027758547,1.2763096027758547,1.2440896027758548,1.2278862829293962,1.2083981129379795,1.2181421129379795,1.2019021129379794,1.2311341129379794,1.2278861129379794,1.2376301129379794,1.2343821129379795,1.2311341129379794,1.2441261129379795,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2505844918195868,1.2657202604760505,1.2759242604760503,1.2529652604760504,1.2657202604760505,1.2682712604760504,1.2759242604760503,1.2759242604760503,1.3014342604760505,1.3065362604760504,1.2912302604760504,1.2759242604760503,1.2759242604760503,1.2835772604760505,1.2912302604760504,1.3090872604760504,1.3396992604760505,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3650217848042823,1.3507111524747228,1.2603111524747228,1.2005496764112615,1.216456216386466,1.197616216386466,1.2572762163864661,1.3106562163864661,1.2761162163864659,1.3106562163864661,1.3294962163864663,1.3106562163864661,1.3169362163864662,1.3420562163864658,1.367176216386466,1.338916216386466,1.351476216386466,1.3451962163864661,1.3420562163864658,1.3420562163864658,1.285536216386466,1.3169362163864662,1.3451962163864661,1.351476216386466,1.426836216386466,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.429761966277213,1.405343502747245,1.4180152188107062,1.5573292188107062,1.6269862188107063,1.6236692188107062,1.640254218810706,1.743081218810706,1.6900092188107059,1.6734242188107062,1.693326218810706,1.6900092188107059,1.6468882188107061,1.6535222188107062,1.6701072188107062,1.693326218810706,1.640254218810706,1.6634732188107064,1.6435712188107061,1.5971332188107061,1.6137182188107062,1.583865218810706,1.6004502188107061,1.620352218810706,1.6137182188107062,1.6468882188107061,1.6104012188107062,1.593816218810706,1.5971332188107061,1.6435712188107061,1.607084218810706,1.607084218810706,1.524159218810706,1.5009402188107062,1.5274762188107063,1.530793218810706,1.4909892188107061,1.481038218810706,1.431283218810706,1.4909892188107061,1.4710872188107065,1.4511852188107062,1.437917218810706,1.4080642188107062,1.4113812188107062,1.431283218810706,1.4279662188107058,1.4578192188107062,1.4611362188107062,1.4578192188107062,1.434600218810706,1.4644532188107062,1.4146982188107065,1.3616262188107062,1.3616262188107062,1.384845218810706,1.3947962188107061,1.3218222188107063,1.3085542188107062,1.3483582188107062,1.3516752188107062,1.3516752188107062,1.374894218810706,1.381528218810706,1.378211218810706,1.378211218810706,1.3981132188107062,1.4180152188107062,1.4180152188107062,1.431283218810706,1.4279662188107058,1.434600218810706,1.4246492188107063,1.4146982188107065,1.4113812188107062,1.3981132188107062,1.4014302188107064,1.4146982188107065,1.4180152188107062,1.4445512188107061,1.4445512188107061,1.4445512188107061,1.454502218810706,1.4710872188107065,1.4976232188107064,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.5139828984041357,1.528612798051276,1.503748798051276,1.516180798051276,1.509964798051276,1.5379367980512757,1.559692798051276,1.575232798051276,1.575232798051276,1.578340798051276,1.584556798051276,1.581448798051276,1.572124798051276,1.612528798051276,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.6123069591877508,1.561630645797858,1.535428176831641,1.4994974067362736,1.528889266755347,1.535421266755347,1.541953266755347,1.5452192667553473,1.5550172667553472,1.548485266755347,1.5550172667553472,1.5615492667553472,1.5746132667553472,1.5844112667553472,1.5746132667553472,1.548485266755347,1.571347266755347,1.5778792667553472,1.5680812667553472,1.5844112667553472,1.610539266755347,1.610539266755347,1.630135266755347,1.6268692667553473,1.6431992667553472,1.6431992667553472,1.6334012667553472,1.6758592667553471,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549,1.695201025443549],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"mode\":\"lines\",\"name\":\"indicator\",\"showlegend\":true,\"visible\":\"legendonly\",\"x\":[\"2018-12-27\",\"2018-12-28\",\"2019-01-02\",\"2019-01-03\",\"2019-01-04\",\"2019-01-07\",\"2019-01-08\",\"2019-01-09\",\"2019-01-10\",\"2019-01-11\",\"2019-01-14\",\"2019-01-15\",\"2019-01-16\",\"2019-01-17\",\"2019-01-18\",\"2019-01-21\",\"2019-01-22\",\"2019-01-23\",\"2019-01-24\",\"2019-01-25\",\"2019-01-28\",\"2019-01-29\",\"2019-01-30\",\"2019-01-31\",\"2019-02-01\",\"2019-02-11\",\"2019-02-12\",\"2019-02-13\",\"2019-02-14\",\"2019-02-15\",\"2019-02-18\",\"2019-02-19\",\"2019-02-20\",\"2019-02-21\",\"2019-02-22\",\"2019-02-25\",\"2019-02-26\",\"2019-02-27\",\"2019-02-28\",\"2019-03-01\",\"2019-03-04\",\"2019-03-05\",\"2019-03-06\",\"2019-03-07\",\"2019-03-08\",\"2019-03-11\",\"2019-03-12\",\"2019-03-13\",\"2019-03-14\",\"2019-03-15\",\"2019-03-18\",\"2019-03-19\",\"2019-03-20\",\"2019-03-21\",\"2019-03-22\",\"2019-03-25\",\"2019-03-26\",\"2019-03-27\",\"2019-03-28\",\"2019-03-29\",\"2019-04-01\",\"2019-04-02\",\"2019-04-03\",\"2019-04-04\",\"2019-04-08\",\"2019-04-09\",\"2019-04-10\",\"2019-04-11\",\"2019-04-12\",\"2019-04-15\",\"2019-04-16\",\"2019-04-17\",\"2019-04-18\",\"2019-04-19\",\"2019-04-22\",\"2019-04-23\",\"2019-04-24\",\"2019-04-25\",\"2019-04-26\",\"2019-04-29\",\"2019-04-30\",\"2019-05-06\",\"2019-05-07\",\"2019-05-08\",\"2019-05-09\",\"2019-05-10\",\"2019-05-13\",\"2019-05-14\",\"2019-05-15\",\"2019-05-16\",\"2019-05-17\",\"2019-05-20\",\"2019-05-21\",\"2019-05-22\",\"2019-05-23\",\"2019-05-24\",\"2019-05-27\",\"2019-05-28\",\"2019-05-29\",\"2019-05-30\",\"2019-05-31\",\"2019-06-03\",\"2019-06-04\",\"2019-06-05\",\"2019-06-06\",\"2019-06-10\",\"2019-06-11\",\"2019-06-12\",\"2019-06-13\",\"2019-06-14\",\"2019-06-17\",\"2019-06-18\",\"2019-06-19\",\"2019-06-20\",\"2019-06-21\",\"2019-06-24\",\"2019-06-25\",\"2019-06-26\",\"2019-06-27\",\"2019-06-28\",\"2019-07-01\",\"2019-07-02\",\"2019-07-03\",\"2019-07-04\",\"2019-07-05\",\"2019-07-08\",\"2019-07-09\",\"2019-07-10\",\"2019-07-11\",\"2019-07-12\",\"2019-07-15\",\"2019-07-16\",\"2019-07-17\",\"2019-07-18\",\"2019-07-19\",\"2019-07-22\",\"2019-07-23\",\"2019-07-24\",\"2019-07-25\",\"2019-07-26\",\"2019-07-29\",\"2019-07-30\",\"2019-07-31\",\"2019-08-01\",\"2019-08-02\",\"2019-08-05\",\"2019-08-06\",\"2019-08-07\",\"2019-08-08\",\"2019-08-09\",\"2019-08-12\",\"2019-08-13\",\"2019-08-14\",\"2019-08-15\",\"2019-08-16\",\"2019-08-19\",\"2019-08-20\",\"2019-08-21\",\"2019-08-22\",\"2019-08-23\",\"2019-08-26\",\"2019-08-27\",\"2019-08-28\",\"2019-08-29\",\"2019-08-30\",\"2019-09-02\",\"2019-09-03\",\"2019-09-04\",\"2019-09-05\",\"2019-09-06\",\"2019-09-09\",\"2019-09-10\",\"2019-09-11\",\"2019-09-12\",\"2019-09-16\",\"2019-09-17\",\"2019-09-18\",\"2019-09-19\",\"2019-09-20\",\"2019-09-23\",\"2019-09-24\",\"2019-09-25\",\"2019-09-26\",\"2019-09-27\",\"2019-09-30\",\"2019-10-08\",\"2019-10-09\",\"2019-10-10\",\"2019-10-11\",\"2019-10-14\",\"2019-10-15\",\"2019-10-16\",\"2019-10-17\",\"2019-10-18\",\"2019-10-21\",\"2019-10-22\",\"2019-10-23\",\"2019-10-24\",\"2019-10-25\",\"2019-10-28\",\"2019-10-29\",\"2019-10-30\",\"2019-10-31\",\"2019-11-01\",\"2019-11-04\",\"2019-11-05\",\"2019-11-06\",\"2019-11-07\",\"2019-11-08\",\"2019-11-11\",\"2019-11-12\",\"2019-11-13\",\"2019-11-14\",\"2019-11-15\",\"2019-11-18\",\"2019-11-19\",\"2019-11-20\",\"2019-11-21\",\"2019-11-22\",\"2019-11-25\",\"2019-11-26\",\"2019-11-27\",\"2019-11-28\",\"2019-11-29\",\"2019-12-02\",\"2019-12-03\",\"2019-12-04\",\"2019-12-05\",\"2019-12-06\",\"2019-12-09\",\"2019-12-10\",\"2019-12-11\",\"2019-12-12\",\"2019-12-13\",\"2019-12-16\",\"2019-12-17\",\"2019-12-18\",\"2019-12-19\",\"2019-12-20\",\"2019-12-23\",\"2019-12-24\",\"2019-12-25\",\"2019-12-26\",\"2019-12-27\",\"2019-12-30\",\"2019-12-31\",\"2020-01-02\",\"2020-01-03\",\"2020-01-06\",\"2020-01-07\",\"2020-01-08\",\"2020-01-09\",\"2020-01-10\",\"2020-01-13\",\"2020-01-14\",\"2020-01-15\",\"2020-01-16\",\"2020-01-17\",\"2020-01-20\",\"2020-01-21\",\"2020-01-22\",\"2020-01-23\",\"2020-02-03\",\"2020-02-04\",\"2020-02-05\",\"2020-02-06\",\"2020-02-07\",\"2020-02-10\",\"2020-02-11\",\"2020-02-12\",\"2020-02-13\",\"2020-02-14\",\"2020-02-17\",\"2020-02-18\",\"2020-02-19\",\"2020-02-20\",\"2020-02-21\",\"2020-02-24\",\"2020-02-25\",\"2020-02-26\",\"2020-02-27\",\"2020-02-28\",\"2020-03-02\",\"2020-03-03\",\"2020-03-04\",\"2020-03-05\",\"2020-03-06\",\"2020-03-09\",\"2020-03-10\",\"2020-03-11\",\"2020-03-12\",\"2020-03-13\",\"2020-03-16\",\"2020-03-17\",\"2020-03-18\",\"2020-03-19\",\"2020-03-20\",\"2020-03-23\",\"2020-03-24\",\"2020-03-25\",\"2020-03-26\",\"2020-03-27\",\"2020-03-30\",\"2020-03-31\",\"2020-04-01\",\"2020-04-02\",\"2020-04-03\",\"2020-04-07\",\"2020-04-08\",\"2020-04-09\",\"2020-04-10\",\"2020-04-13\",\"2020-04-14\",\"2020-04-15\",\"2020-04-16\",\"2020-04-17\",\"2020-04-20\",\"2020-04-21\",\"2020-04-22\",\"2020-04-23\",\"2020-04-24\",\"2020-04-27\",\"2020-04-28\",\"2020-04-29\",\"2020-04-30\",\"2020-05-06\",\"2020-05-07\",\"2020-05-08\",\"2020-05-11\",\"2020-05-12\",\"2020-05-13\",\"2020-05-14\",\"2020-05-15\",\"2020-05-18\",\"2020-05-19\",\"2020-05-20\",\"2020-05-21\",\"2020-05-22\",\"2020-05-25\",\"2020-05-26\",\"2020-05-27\",\"2020-05-28\",\"2020-05-29\",\"2020-06-01\",\"2020-06-02\",\"2020-06-03\",\"2020-06-04\",\"2020-06-05\",\"2020-06-08\",\"2020-06-09\",\"2020-06-10\",\"2020-06-11\",\"2020-06-12\",\"2020-06-15\",\"2020-06-16\",\"2020-06-17\",\"2020-06-18\",\"2020-06-19\",\"2020-06-22\",\"2020-06-23\",\"2020-06-24\",\"2020-06-29\",\"2020-06-30\",\"2020-07-01\",\"2020-07-02\",\"2020-07-03\",\"2020-07-06\",\"2020-07-07\",\"2020-07-08\",\"2020-07-09\",\"2020-07-10\",\"2020-07-13\",\"2020-07-14\",\"2020-07-15\",\"2020-07-16\",\"2020-07-17\",\"2020-07-20\",\"2020-07-21\",\"2020-07-22\",\"2020-07-23\",\"2020-07-24\",\"2020-07-27\",\"2020-07-28\",\"2020-07-29\",\"2020-07-30\",\"2020-07-31\",\"2020-08-03\",\"2020-08-04\",\"2020-08-05\",\"2020-08-06\",\"2020-08-07\",\"2020-08-10\",\"2020-08-11\",\"2020-08-12\",\"2020-08-13\",\"2020-08-14\",\"2020-08-17\",\"2020-08-18\",\"2020-08-19\",\"2020-08-20\",\"2020-08-21\",\"2020-08-24\",\"2020-08-25\",\"2020-08-26\",\"2020-08-27\",\"2020-08-28\",\"2020-08-31\",\"2020-09-01\",\"2020-09-02\",\"2020-09-03\",\"2020-09-04\",\"2020-09-07\",\"2020-09-08\",\"2020-09-09\",\"2020-09-10\",\"2020-09-11\",\"2020-09-14\",\"2020-09-15\",\"2020-09-16\",\"2020-09-17\",\"2020-09-18\",\"2020-09-21\",\"2020-09-22\",\"2020-09-23\",\"2020-09-24\",\"2020-09-25\",\"2020-09-28\",\"2020-09-29\",\"2020-09-30\",\"2020-10-09\",\"2020-10-12\",\"2020-10-13\",\"2020-10-14\",\"2020-10-15\",\"2020-10-16\",\"2020-10-19\",\"2020-10-20\",\"2020-10-21\",\"2020-10-22\",\"2020-10-23\",\"2020-10-26\",\"2020-10-27\",\"2020-10-28\",\"2020-10-29\",\"2020-10-30\",\"2020-11-02\",\"2020-11-03\",\"2020-11-04\",\"2020-11-05\",\"2020-11-06\",\"2020-11-09\",\"2020-11-10\",\"2020-11-11\",\"2020-11-12\",\"2020-11-13\",\"2020-11-16\",\"2020-11-17\",\"2020-11-18\",\"2020-11-19\",\"2020-11-20\",\"2020-11-23\",\"2020-11-24\",\"2020-11-25\",\"2020-11-26\",\"2020-11-27\",\"2020-11-30\",\"2020-12-01\",\"2020-12-02\",\"2020-12-03\",\"2020-12-04\",\"2020-12-07\",\"2020-12-08\",\"2020-12-09\",\"2020-12-10\",\"2020-12-11\",\"2020-12-14\",\"2020-12-15\",\"2020-12-16\",\"2020-12-17\",\"2020-12-18\",\"2020-12-21\",\"2020-12-22\",\"2020-12-23\",\"2020-12-24\",\"2020-12-25\",\"2020-12-28\",\"2020-12-29\",\"2020-12-30\",\"2020-12-31\",\"2021-01-04\",\"2021-01-05\",\"2021-01-06\",\"2021-01-07\",\"2021-01-08\",\"2021-01-11\",\"2021-01-12\",\"2021-01-13\",\"2021-01-14\",\"2021-01-15\",\"2021-01-18\",\"2021-01-19\",\"2021-01-20\",\"2021-01-21\",\"2021-01-22\",\"2021-01-25\",\"2021-01-26\",\"2021-01-27\",\"2021-01-28\",\"2021-01-29\",\"2021-02-01\",\"2021-02-02\",\"2021-02-03\",\"2021-02-04\",\"2021-02-05\",\"2021-02-08\",\"2021-02-09\",\"2021-02-10\",\"2021-02-18\",\"2021-02-19\",\"2021-02-22\",\"2021-02-23\",\"2021-02-24\",\"2021-02-25\",\"2021-02-26\",\"2021-03-01\",\"2021-03-02\",\"2021-03-03\",\"2021-03-04\",\"2021-03-05\",\"2021-03-08\",\"2021-03-09\",\"2021-03-10\",\"2021-03-11\",\"2021-03-12\",\"2021-03-15\",\"2021-03-16\",\"2021-03-17\",\"2021-03-18\",\"2021-03-19\",\"2021-03-22\",\"2021-03-23\",\"2021-03-24\",\"2021-03-25\",\"2021-03-26\",\"2021-03-29\",\"2021-03-30\",\"2021-03-31\",\"2021-04-01\",\"2021-04-02\",\"2021-04-06\",\"2021-04-07\",\"2021-04-08\",\"2021-04-09\",\"2021-04-12\",\"2021-04-13\",\"2021-04-14\",\"2021-04-15\",\"2021-04-16\",\"2021-04-19\",\"2021-04-20\",\"2021-04-21\",\"2021-04-22\",\"2021-04-23\",\"2021-04-26\",\"2021-04-27\",\"2021-04-28\",\"2021-04-29\",\"2021-04-30\",\"2021-05-06\",\"2021-05-07\",\"2021-05-10\",\"2021-05-11\",\"2021-05-12\",\"2021-05-13\",\"2021-05-14\",\"2021-05-17\",\"2021-05-18\",\"2021-05-19\",\"2021-05-20\",\"2021-05-21\",\"2021-05-24\",\"2021-05-25\",\"2021-05-26\",\"2021-05-27\",\"2021-05-28\",\"2021-05-31\",\"2021-06-01\",\"2021-06-02\",\"2021-06-03\",\"2021-06-04\",\"2021-06-07\",\"2021-06-08\",\"2021-06-09\",\"2021-06-10\",\"2021-06-11\",\"2021-06-15\",\"2021-06-16\",\"2021-06-17\",\"2021-06-18\",\"2021-06-21\",\"2021-06-22\",\"2021-06-23\",\"2021-06-24\",\"2021-06-25\",\"2021-06-28\",\"2021-06-29\",\"2021-06-30\",\"2021-07-01\",\"2021-07-02\",\"2021-07-05\",\"2021-07-06\",\"2021-07-07\",\"2021-07-08\",\"2021-07-09\",\"2021-07-12\",\"2021-07-13\",\"2021-07-14\",\"2021-07-15\",\"2021-07-16\",\"2021-07-19\",\"2021-07-20\",\"2021-07-21\",\"2021-07-22\",\"2021-07-23\",\"2021-07-26\",\"2021-07-27\",\"2021-07-28\",\"2021-07-29\",\"2021-07-30\",\"2021-08-02\",\"2021-08-03\",\"2021-08-04\",\"2021-08-05\",\"2021-08-06\",\"2021-08-09\",\"2021-08-10\",\"2021-08-11\",\"2021-08-12\",\"2021-08-13\",\"2021-08-16\",\"2021-08-17\",\"2021-08-18\",\"2021-08-19\",\"2021-08-20\",\"2021-08-23\",\"2021-08-24\",\"2021-08-25\",\"2021-08-26\",\"2021-08-27\",\"2021-08-30\",\"2021-08-31\",\"2021-09-01\",\"2021-09-02\",\"2021-09-03\",\"2021-09-06\",\"2021-09-07\",\"2021-09-08\",\"2021-09-09\",\"2021-09-10\",\"2021-09-13\",\"2021-09-14\",\"2021-09-15\",\"2021-09-16\",\"2021-09-17\",\"2021-09-22\",\"2021-09-23\",\"2021-09-24\",\"2021-09-27\",\"2021-09-28\",\"2021-09-29\",\"2021-09-30\",\"2021-10-08\",\"2021-10-11\",\"2021-10-12\",\"2021-10-13\",\"2021-10-14\",\"2021-10-15\",\"2021-10-18\",\"2021-10-19\",\"2021-10-20\",\"2021-10-21\",\"2021-10-22\",\"2021-10-25\",\"2021-10-26\",\"2021-10-27\",\"2021-10-28\",\"2021-10-29\",\"2021-11-01\",\"2021-11-02\",\"2021-11-03\",\"2021-11-04\",\"2021-11-05\",\"2021-11-08\",\"2021-11-09\",\"2021-11-10\",\"2021-11-11\",\"2021-11-12\",\"2021-11-15\",\"2021-11-16\",\"2021-11-17\",\"2021-11-18\",\"2021-11-19\",\"2021-11-22\",\"2021-11-23\",\"2021-11-24\",\"2021-11-25\",\"2021-11-26\",\"2021-11-29\",\"2021-11-30\",\"2021-12-01\",\"2021-12-02\",\"2021-12-03\",\"2021-12-06\",\"2021-12-07\",\"2021-12-08\",\"2021-12-09\",\"2021-12-10\",\"2021-12-13\",\"2021-12-14\",\"2021-12-15\",\"2021-12-16\",\"2021-12-17\",\"2021-12-20\",\"2021-12-21\",\"2021-12-22\",\"2021-12-23\",\"2021-12-24\",\"2021-12-27\",\"2021-12-28\",\"2021-12-29\",\"2021-12-30\",\"2021-12-31\",\"2022-01-04\",\"2022-01-05\",\"2022-01-06\",\"2022-01-07\",\"2022-01-10\",\"2022-01-11\",\"2022-01-12\",\"2022-01-13\",\"2022-01-14\",\"2022-01-17\",\"2022-01-18\",\"2022-01-19\",\"2022-01-20\",\"2022-01-21\",\"2022-01-24\",\"2022-01-25\",\"2022-01-26\",\"2022-01-27\",\"2022-01-28\",\"2022-02-07\",\"2022-02-08\",\"2022-02-09\",\"2022-02-10\",\"2022-02-11\",\"2022-02-14\",\"2022-02-15\",\"2022-02-16\",\"2022-02-17\",\"2022-02-18\",\"2022-02-21\",\"2022-02-22\",\"2022-02-23\",\"2022-02-24\",\"2022-02-25\",\"2022-02-28\",\"2022-03-01\",\"2022-03-02\",\"2022-03-03\",\"2022-03-04\",\"2022-03-07\",\"2022-03-08\",\"2022-03-09\",\"2022-03-10\",\"2022-03-11\",\"2022-03-14\",\"2022-03-15\",\"2022-03-16\",\"2022-03-17\",\"2022-03-18\",\"2022-03-21\",\"2022-03-22\",\"2022-03-23\",\"2022-03-24\",\"2022-03-25\",\"2022-03-28\",\"2022-03-29\",\"2022-03-30\",\"2022-03-31\",\"2022-04-01\",\"2022-04-06\",\"2022-04-07\",\"2022-04-08\",\"2022-04-11\",\"2022-04-12\",\"2022-04-13\",\"2022-04-14\",\"2022-04-15\",\"2022-04-18\",\"2022-04-19\",\"2022-04-20\",\"2022-04-21\",\"2022-04-22\",\"2022-04-25\",\"2022-04-26\",\"2022-04-27\",\"2022-04-28\",\"2022-04-29\",\"2022-05-05\",\"2022-05-06\",\"2022-05-09\",\"2022-05-10\",\"2022-05-11\",\"2022-05-12\",\"2022-05-13\",\"2022-05-16\",\"2022-05-17\",\"2022-05-18\",\"2022-05-19\",\"2022-05-20\",\"2022-05-23\",\"2022-05-24\",\"2022-05-25\",\"2022-05-26\",\"2022-05-27\",\"2022-05-30\",\"2022-05-31\",\"2022-06-01\",\"2022-06-02\",\"2022-06-06\",\"2022-06-07\",\"2022-06-08\",\"2022-06-09\",\"2022-06-10\",\"2022-06-13\",\"2022-06-14\",\"2022-06-15\",\"2022-06-16\",\"2022-06-17\",\"2022-06-20\",\"2022-06-21\",\"2022-06-22\",\"2022-06-23\",\"2022-06-24\",\"2022-06-27\",\"2022-06-28\",\"2022-06-29\",\"2022-06-30\",\"2022-07-01\",\"2022-07-04\",\"2022-07-05\",\"2022-07-06\",\"2022-07-07\",\"2022-07-08\",\"2022-07-11\",\"2022-07-12\",\"2022-07-13\",\"2022-07-14\",\"2022-07-15\",\"2022-07-18\",\"2022-07-19\",\"2022-07-20\",\"2022-07-21\",\"2022-07-22\",\"2022-07-25\",\"2022-07-26\",\"2022-07-27\",\"2022-07-28\",\"2022-07-29\",\"2022-08-01\",\"2022-08-02\",\"2022-08-03\",\"2022-08-04\",\"2022-08-05\",\"2022-08-08\",\"2022-08-09\",\"2022-08-10\",\"2022-08-11\",\"2022-08-12\",\"2022-08-15\",\"2022-08-16\",\"2022-08-17\",\"2022-08-18\",\"2022-08-19\",\"2022-08-22\",\"2022-08-23\",\"2022-08-24\",\"2022-08-25\",\"2022-08-26\",\"2022-08-29\",\"2022-08-30\",\"2022-08-31\",\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\",\"2023-03-02\",\"2023-03-03\",\"2023-03-06\",\"2023-03-07\",\"2023-03-08\",\"2023-03-09\",\"2023-03-10\",\"2023-03-13\",\"2023-03-14\",\"2023-03-15\",\"2023-03-16\",\"2023-03-17\",\"2023-03-20\",\"2023-03-21\",\"2023-03-22\",\"2023-03-23\",\"2023-03-24\",\"2023-03-27\",\"2023-03-28\",\"2023-03-29\",\"2023-03-30\",\"2023-03-31\",\"2023-04-03\",\"2023-04-04\",\"2023-04-06\",\"2023-04-07\",\"2023-04-10\",\"2023-04-11\",\"2023-04-12\",\"2023-04-13\",\"2023-04-14\",\"2023-04-17\",\"2023-04-18\",\"2023-04-19\",\"2023-04-20\",\"2023-04-21\",\"2023-04-24\",\"2023-04-25\",\"2023-04-26\",\"2023-04-27\",\"2023-04-28\",\"2023-05-04\",\"2023-05-05\",\"2023-05-08\",\"2023-05-09\",\"2023-05-10\",\"2023-05-11\",\"2023-05-12\",\"2023-05-15\",\"2023-05-16\",\"2023-05-17\",\"2023-05-18\",\"2023-05-19\",\"2023-05-22\",\"2023-05-23\",\"2023-05-24\",\"2023-05-25\",\"2023-05-26\",\"2023-05-29\",\"2023-05-30\",\"2023-05-31\",\"2023-06-01\",\"2023-06-02\",\"2023-06-05\",\"2023-06-06\",\"2023-06-07\",\"2023-06-08\",\"2023-06-09\",\"2023-06-12\",\"2023-06-13\",\"2023-06-14\",\"2023-06-15\",\"2023-06-16\",\"2023-06-19\",\"2023-06-20\",\"2023-06-21\",\"2023-06-26\",\"2023-06-27\",\"2023-06-28\",\"2023-06-29\",\"2023-06-30\",\"2023-07-03\",\"2023-07-04\",\"2023-07-05\",\"2023-07-06\",\"2023-07-07\",\"2023-07-10\",\"2023-07-11\",\"2023-07-12\",\"2023-07-13\",\"2023-07-14\",\"2023-07-17\",\"2023-07-18\",\"2023-07-19\",\"2023-07-20\",\"2023-07-21\",\"2023-07-24\",\"2023-07-25\",\"2023-07-26\",\"2023-07-27\",\"2023-07-28\",\"2023-07-31\",\"2023-08-01\",\"2023-08-02\",\"2023-08-03\",\"2023-08-04\",\"2023-08-07\",\"2023-08-08\",\"2023-08-09\",\"2023-08-10\",\"2023-08-11\",\"2023-08-14\",\"2023-08-15\",\"2023-08-16\",\"2023-08-17\",\"2023-08-18\",\"2023-08-21\",\"2023-08-22\",\"2023-08-23\",\"2023-08-24\",\"2023-08-25\",\"2023-08-28\",\"2023-08-29\",\"2023-08-30\",\"2023-08-31\",\"2023-09-01\",\"2023-09-04\",\"2023-09-05\",\"2023-09-06\",\"2023-09-07\",\"2023-09-08\",\"2023-09-11\",\"2023-09-12\",\"2023-09-13\",\"2023-09-14\",\"2023-09-15\",\"2023-09-18\",\"2023-09-19\",\"2023-09-20\",\"2023-09-21\",\"2023-09-22\",\"2023-09-25\",\"2023-09-26\",\"2023-09-27\",\"2023-09-28\",\"2023-10-09\",\"2023-10-10\",\"2023-10-11\",\"2023-10-12\",\"2023-10-13\",\"2023-10-16\",\"2023-10-17\",\"2023-10-18\",\"2023-10-19\",\"2023-10-20\",\"2023-10-23\",\"2023-10-24\",\"2023-10-25\",\"2023-10-26\",\"2023-10-27\",\"2023-10-30\",\"2023-10-31\",\"2023-11-01\",\"2023-11-02\",\"2023-11-03\",\"2023-11-06\",\"2023-11-07\",\"2023-11-08\",\"2023-11-09\",\"2023-11-10\",\"2023-11-13\",\"2023-11-14\",\"2023-11-15\",\"2023-11-16\",\"2023-11-17\",\"2023-11-20\",\"2023-11-21\",\"2023-11-22\",\"2023-11-23\",\"2023-11-24\",\"2023-11-27\",\"2023-11-28\",\"2023-11-29\",\"2023-11-30\"],\"y\":[null,42.361421601213614,30.862929234020854,61.310995239994334,74.54242030517885,76.27335418265879,47.180969771689796,44.09527973483293,69.98552827363741,63.52155581140687,56.86748627309229,64.77238026215086,35.116576335212805,62.46357807613375,58.16168983045312,39.91198504235263,40.37688951102862,19.22038916749454,38.35068263701458,27.33903705199199,39.16433143620835,21.71793152578878,24.40306946833539,20.481540570243414,61.999002909024476,64.56777209474049,67.7366432174915,69.49069593916447,68.00814339127139,49.177033669224556,73.54720648941999,51.52365313281973,53.47747426342885,43.22189924406246,73.2513725672374,80.27099737108132,50.929248335254414,64.88922552461095,67.14108143752037,71.17422219290093,68.15245934775056,84.06544264723071,75.73614118194939,82.45962519779772,32.12712089742421,68.41798542180298,53.771423729871124,33.86536688077927,31.49279711778206,57.36697139286289,62.81528257680859,42.66459603020586,42.40767613639125,59.70548717168316,59.01028455760822,36.1338129444791,27.63642526425474,34.292564288916736,30.462799296134364,69.52851581816684,70.92409286875143,52.52787304245488,64.02303810383025,57.45011673806888,59.40555622548095,45.329699408521954,60.91028567933167,31.79497714531568,54.25485113795819,47.51494120309027,57.08365347699819,62.67149775561905,34.78920253313027,61.29584985875075,34.46944882722216,23.568687398942497,44.495051580858075,20.857980132308064,23.291600077377883,20.89433065470296,55.52077469558183,19.844813897479153,58.083049825672646,36.04803112619495,30.597265011033183,61.81472349909987,42.885198007213944,44.443935014762,60.7943028137632,56.65081125177011,25.33686895422538,38.14458629784,58.29573606914058,37.10347109833906,32.36638018344231,44.2662339875721,63.72920276808971,55.790954619502294,36.148778846694995,36.381560624474965,39.15843660392145,29.62194849621915,19.83265570676217,44.88606015730577,24.425051808720003,57.1797315468057,71.6458251440036,47.71828184410168,62.297629072916585,49.398820051478374,48.89742946462173,63.72651604199056,64.54989715620907,74.50841016330911,68.1267571369156,43.45172363372408,47.042764319242906,48.21077525281584,59.87426891313975,30.785233488197235,68.34677998820503,45.50010378586484,32.85488637869617,58.0574192558777,53.423109227949446,23.5175535283407,35.274239539395616,26.49712667438473,47.3236722695954,55.63987264059778,71.18456834459255,49.117384222465354,38.70879682836908,37.48817591640925,55.43737893657337,27.131039578736175,51.347488407803894,65.03857596645138,40.14864467006982,35.58956603863697,40.142296256104174,65.46581526913438,40.42211619188206,30.032571271057037,22.43891723997223,21.131463846331798,14.151099993486616,22.713861759606758,53.288875431127565,31.140528610219434,59.9109893101753,39.44946094241589,55.60671130256157,45.354681666931334,54.209925409908,73.66550101533238,58.4820486817956,62.243063004853816,52.653130309017435,48.20917438668848,29.093476178758255,70.40667921894884,62.22912217194979,55.17072207712883,40.090434614397765,69.85485132542665,50.24131239393599,66.98920233660071,60.68485135589858,51.863357017019574,72.04312262491815,53.40562645007447,49.61743474552537,68.28447780227025,49.02503504793739,24.844508176997355,31.031418017916064,53.59593851184064,60.89494204641298,26.46546232039442,50.84342600130724,24.041554960286806,24.084118060669272,56.59773236083773,29.743872855510528,60.623610115894905,61.04379182421397,62.26680329788615,65.79581444034969,78.10242405938256,70.10691210614729,41.33496529267509,36.874496721559815,24.282419768975576,33.99588520034203,61.8788080431828,62.554511392199935,45.76697309514572,48.93495631586882,69.8767593269397,39.36956539675046,32.43901441842147,26.07193451718489,60.78838119968942,41.36090198021818,46.27015825083301,27.12396380490488,43.97015322366895,39.77009621958158,19.664700591125428,43.992026846749134,32.58570500455399,30.0490900817222,22.3018067276484,50.52545815441564,64.3670134629379,30.430267418040945,57.68773036011188,43.649968392543265,69.66406102116984,46.21235718505584,62.6226649178954,39.75311898571732,65.57992340824633,47.98903968112858,52.98918386846966,40.34795425577597,66.95107722046171,67.70118747138432,53.65577475752668,53.6557924537879,42.57707996419281,31.215628370709155,70.1450489061008,61.67975577548524,76.17394905233554,49.78499310480461,61.13043617921485,44.588134716375514,26.25664034504869,57.84465694112263,37.97775365818944,67.33429848000411,74.53888570950028,80.05892218938449,39.8640458303513,76.5874414124015,42.70715812197221,36.545555360682734,62.727444787316124,32.417569695133956,64.82757247628854,59.25883055445542,59.966616066529845,40.61613895097681,32.02112547207192,41.931051026216835,49.07467815716933,51.40111466626925,25.615214976231083,37.023181667166114,18.56330568773036,13.397878242513448,21.93812924711034,53.42030043178727,59.971818219265685,44.66025661747293,61.73191589748842,37.01181189090469,59.64935623699808,34.113496913524536,38.925846047147466,67.11167145906778,62.12666830554969,43.566018622137086,68.87278325499162,52.206660045753615,52.2066576818479,41.78352024457498,62.472250697220964,50.3054644193086,25.892375492841552,67.27975690400767,43.36561833933704,73.75914699076293,47.2244363335977,72.64026265100705,31.501181733734217,60.720803644020286,37.138615203199485,31.11983787566813,29.647634577329274,26.267372591855565,44.5076170642761,30.705260816298217,60.76939816307845,58.0772761698584,30.31584707445215,56.48730470238022,60.95530865362945,44.03316245571191,47.4104563517329,36.27168169569473,40.464560256758865,38.04194091179348,58.001759156606006,48.37080895255149,60.12077007869045,63.37815903749643,49.78188993879342,32.366658044855576,42.02905954345653,58.368999908793604,35.63422604342416,47.166911318217025,42.99405746835168,70.926963902168,45.36284899426172,74.34428495399779,66.90098795167344,40.936434864515256,66.10480163914674,29.54275883240699,51.66634907067719,62.9318825085924,66.47202077075681,43.029134796741126,55.75396627024785,48.59004099608857,36.057054633340975,58.60842026974825,31.213681078513854,46.17006784901861,32.11175347835263,57.47486849297019,30.7421299918315,44.2203564886679,24.797062362541535,48.27025773556861,67.56386225726975,56.74568899898595,45.30129052783897,71.67479062665892,78.64308562458287,83.70370983524693,84.54474563628624,84.73495115119937,34.890248091799506,29.956021235531352,33.320425900561666,44.26343758214761,34.936511501980874,39.97845775248731,28.538863812063255,59.484549005573115,32.461067961486854,39.13512364834782,64.55222396300744,33.80456557932447,32.05535717531953,38.78321027595086,37.19354998934153,56.24515086592351,58.89685902917281,61.55238309202527,64.16803269235,78.93681356567767,57.685357018281366,71.62837127411844,72.93582697126644,46.37287174000392,67.97251994972997,69.10870244368247,30.669246164911453,29.68226897617143,32.6452672205675,64.77540840718177,42.93814410270846,38.997628621127525,30.52907248220232,23.619133584614456,30.126833961609808,48.89738644252271,62.44426286301063,55.93623558216978,59.87203948644543,75.148038877633,39.608304529550864,51.12428264842496,44.82512133058344,31.43543917989507,63.099426350550345,34.23158262354127,43.788855945056184,68.30261020614944,64.49853401416676,61.431368603990514,47.817315473017665,33.953269874593076,34.248903054581625,56.6004767371171,64.99283268674272,38.60345516181074,38.65027722073659,43.03839193634381,56.56206256151976,59.150602193444946,78.54797352357025,56.25138988234297,52.55250205801634,29.282094946688517,29.60384851618237,63.478036845627734,51.87779767126924,27.081893517648194,54.5850369013489,34.9101467726047,37.746751480292055,50.07981663326155,50.63216210872208,64.46030544845257,48.683315059874,31.44439601193216,36.042125696593416,19.734446408082643,23.977437266182218,23.64625204801037,59.965512241911654,33.45118272342691,66.17053909501624,69.82296491565033,45.30082197696877,42.11819650892578,62.373892285579245,43.939919224919244,59.137874281764816,63.19684437920045,30.9497376924103,36.32066184008838,38.94297909581256,31.148274242953963,39.41481038413094,18.797276465525727,21.59461557784738,10.084108516749565,46.40845435173953,63.4677764993964,44.19787098610906,61.540677721430576,49.2733661920721,68.73714141716619,48.947100257277924,44.25260780431723,61.12806721654246,34.699177856355305,68.93375451228827,58.88609338429223,65.1586305920518,56.512536477595944,49.82087445099549,69.3323061735062,47.57861985934145,47.84258460299872,36.87944997066385,62.86181508677751,52.647401825633104,68.03073488966241,65.96752957011027,51.36594212579553,28.07856147777177,47.00229424441746,32.985002405984986,20.346090172681546,39.50911465662585,24.142703923879775,60.09854174890162,42.87094159385086,57.60721954681927,37.375026699714,28.841971157058058,62.808021036776836,26.415957545067414,51.11084638878061,21.481577469357546,62.31723167812134,71.4559966580645,40.57163953995866,43.57728236640043,66.88058525311261,49.66798308441667,39.27042944795525,30.750807110060375,20.06373141767945,53.65928984232081,27.52140437376734,49.359302032271046,27.570354890999763,54.64825705804941,63.8971530273845,47.019748963310214,57.64434339769536,36.65460549290647,55.10776768417568,31.495503090724895,27.12921871294671,33.06013235968388,58.2640150521515,40.77674905523208,32.26918288578697,54.249396610586565,51.73167406802977,30.32568857243359,21.24878299236861,32.77876110368337,47.27930707648138,60.678605624895226,57.59189080610913,73.35001704020952,79.4250550433208,78.51274658730455,51.819827073133105,53.42278452381478,53.422738459918,51.75607039908679,62.001453641360676,42.92742694529331,68.58584135289685,46.66443232293005,68.16384116098857,38.34544124627826,31.20273595871764,27.323905190808414,56.617993483509274,68.92576585869216,62.215076798876005,61.34327965502107,48.54367126524983,40.64699685489418,42.75731848485927,67.1782648624562,50.46422048465317,59.83259879780485,37.325360881706025,55.645098256737505,48.7801999216772,24.005461739996765,57.33611418698089,72.84046044404386,40.743937023504806,59.85788045136665,61.69043225872727,32.31311226975054,68.56315934206714,68.65898373824773,36.8374123186302,74.46565074782592,39.65394340358542,54.86972495903402,44.58065908846266,47.956813803570604,34.019063604924334,41.05348547653453,30.370791941318004,26.90168191355642,35.11900834645493,52.517092366666624,72.40567580376829,44.09908305126725,60.349907162703026,67.15666442569533,77.87064743336283,83.86597280202058,53.197688677796954,35.019680881523975,43.91017706731325,33.49847150430326,57.398198341415316,49.967748966193106,45.6981287917292,60.33602491469342,59.52150604012553,48.00134417059005,68.50320714390719,38.225185760117846,30.862668849987642,62.194578812002,52.23400184293856,48.15787518427799,44.92686859041839,28.23088389543388,66.5923921751929,52.45429203578806,55.712209320729876,59.82742769772051,45.44756506362626,27.125849557452113,47.66805429304588,25.586908886672898,52.27148300632806,37.81635745580541,60.3269612891344,42.46941118243493,33.280511113869444,64.64973829354507,45.22065943706664,40.92665066741565,32.30949650677031,23.702994813674135,30.10766897716748,48.00809654558759,59.77838697321133,47.75056808098609,22.104289988590384,31.523280502872556,43.72957063513951,27.45584607797375,24.785202257263432,58.98389197055689,35.843928206481344,65.95694463131598,53.96930408775638,65.27345001190457,53.04798148233065,31.226847653514014,19.548797480511652,19.65927413100528,16.863006613819554,58.005523303520384,27.802772325210146,65.80715113244419,43.466835940298175,62.17782568519914,44.76628085530613,42.44193522442571,67.70268569742531,72.48773883116405,76.62866843492024,66.59897196437517,66.54196993673938,53.449159499151705,29.841154061119823,66.2667574597912,59.09542602970825,39.05292188692632,53.221171334166776,53.952189989752306,74.32475054304696,49.06094490743021,37.20319035749399,45.933500951531066,66.36922781452027,44.6185883444147,70.17049580571513,45.26332925748944,66.17007830280842,66.81094312179793,76.67031158856737,64.99805241954375,49.081686130752864,66.75420163096761,31.971590737232493,65.59920974711058,35.84235952544704,29.871936301827784,50.72665116382604,74.39561864755052,31.930663884538404,25.84587649383379,54.08307159150801,25.657997235515456,56.52384799804645,70.52507094731887,63.45091937401903,43.597321907846094,36.05215661372571,75.44761564216145,38.39297405514003,58.55036061732994,51.51485032165592,67.89460009093841,54.625541543448946,74.63487406812025,46.5916621802869,29.97949033858352,34.64790751314621,61.00538893150907,66.6127383936742,70.32923179724446,29.660007702704746,60.17228736739688,38.908135300699115,35.93834087135098,51.545302479759215,29.80825273472169,39.872022568990985,58.358917050129456,44.95704929523922,57.778089626801396,77.98465726248544,51.98848508802515,62.2896917514632,40.772372486833824,54.0716817113612,50.335496833838526,42.755426584955934,31.48785426725568,41.36168746901496,20.31942665339358,58.124042966893114,59.53047133916257,34.77015194346529,58.38270770069861,55.14755780658746,58.07089983035734,47.557606612995386,69.31011171147317,60.000165228872824,38.82556287124782,34.03400103824236,46.188725806628476,58.47730358417619,60.523514342481896,74.1726556273005,81.00243343886522,44.097549897203216,52.837129862820895,72.21142191512486,55.64428424965632,35.080755869307005,50.4808494433588,39.42056286620014,71.31902230495162,73.9082644090047,37.927702445230416,41.48819917129979,27.616942927931788,55.99798752050905,46.070467578327225,37.53304612773332,34.14763701588362,21.89216574901541,62.51288229469068,38.89627915071315,71.485300962289,30.637394385186024,28.161553043508434,58.90580689368128,24.461025859639307,52.68690328167819,27.70897233879349,50.73067669221908,60.751911467521474,68.79352669369113,59.04210424753794,58.94884045311421,70.88973526435431,31.42607036434437,41.3501464858005,68.2024115892823,44.3473446086392,51.419916292696094,51.55950770764534,44.29456068230051,41.6923011594437,23.250902100084232,60.10335963140738,34.36032050620874,52.18022380944536,43.93675278812086,73.29226719076031,37.17263953482941,34.14025627703692,23.155707358498393,23.028860623413525,61.689889518803476,62.0954500873464,31.933378695956502,22.818886008492047,58.8153656467463,52.06069678489894,60.986752801971456,61.046045761600716,62.57395741994847,43.90963700564881,39.063170062607355,62.09662902636061,54.13120201147844,44.68341254391191,53.981284830949726,65.10292139388116,61.91216261217952,73.67875737726277,35.3438064065838,51.98874963616129,35.51348352244039,69.6101078870358,44.80966261312031,54.34992116208631,38.86074353823187,49.35394222599379,43.57576392064863,40.35630632729746,21.061400999186002,23.609127943917752,13.82548677572562,14.99355947427609,49.49504870177723,30.964330305789755,64.13171473817975,68.18048347507239,37.53836316514806,62.96499981765891,63.93936447081998,42.415238712085944,55.83479946516368,66.19123957798871,67.87498840336896,39.27694754815484,56.85437496236267,48.16525339593488,48.04312991124535,51.49160611851048,29.001840590738592,60.6737802207659,63.89335834449725,54.34608224177836,79.6847882387749,48.29012315723401,68.18164286620737,41.28318771738989,46.183661816299455,38.28001088452152,34.885357618692716,29.13218811959216,56.502427607118456,66.68819259424446,54.914511326316834,58.90879211303156,60.78938826227318,31.99657808419181,35.565023775963915,59.67022696986283,26.3754868736989,66.79617006294554,38.711234076161,66.93643991192717,58.62673012600887,47.046874433465575,70.86968064206688,43.901863795093256,63.3303461503265,42.80424123843064,31.820077440206926,40.02659364497533,59.18327828940256,54.38128229241943,66.85308944368258,64.10784266267035,33.98414739727253,24.34444109129744,61.842436369382504,56.20392014392849,60.98550477934706,54.41431894745775,62.10985688355415,73.77211182946546,60.29517981979913,35.64367611230214,66.75803701473549,29.201965634468923,30.602296609501323,17.06538280281202,20.00354773000556,50.8119935279281,71.64639423244824,74.15336033762789,49.440140822183075,64.67196379999837,75.94519925730934,38.447796503325556,44.163722787003344,64.87385846784939,50.04838037165882,34.35659985575274,54.56307512952788,60.70215078607142,68.95664033863429,31.492181267677655,61.75408422428174,40.271834072820916,30.165026083359038,53.637973784687034,33.37471211504639,53.523357403836734,60.71066894628964,41.89042081202545,67.27223306905721,36.56733776280036,38.571707875936625,48.87939192344522,71.13443507120998,35.30212528572657,48.28329608731827,23.88640846801915,31.44989351178334,58.26799060998704,51.71798934171935,27.46874099876527,37.212786078532126,20.15089957530152,64.63216131311296,40.167956134402836,35.72293337343854,38.43793061319493,28.58001732805514,53.9835600631773,60.28246979279593,46.72253103459036,64.32013214011862,59.37791335394144,50.690079722681766,33.8546939485445,65.31499790187449,27.753239769540784,22.711934184745065,46.00574353839934,58.15825158745324,57.86804939856225,23.41412337154698,32.95241917021132,63.815475372288574,55.62396229117605,45.28041710250443,62.15398484075666,58.043186108695494,40.30632798778294,47.21058269778084,62.58459040390571,66.90956867320287,51.01786126295233,64.6649170189233,46.31786614043111,61.28335728379005,40.45946071693337,33.900639524785966,36.393766884624874,26.301756416204544,52.32002489201818,64.64275133807244,56.813171057224984,79.91914660463216,50.398959896432785,50.398782335656556,67.33925008338416,74.42232202006592,81.16683164095893,85.48454980324522,75.53670890824829,33.14472677392582,29.26549963624277,69.60541140995797,74.54378383387971,35.52310555670123,54.53681022344028,32.546906329761505,36.4755644024657,63.25254739044657,35.16510433232886,35.18170585179619,36.241878856468176,67.3178749926577,41.91742243253389,56.701749664340035,74.34640056803384,49.29978655009208,54.22384418179581,32.44031752116758,25.377207408748927,41.93448516537484,38.53587202172279,21.084637179458607,28.980275630948963,60.1146088836436,58.416570253687716,45.823510782508635,50.50220988728048,57.85232793896362,62.92435803993775,64.89961484195774,73.07650128211411,72.68438828027227,73.56619990234888,43.5513389537202,63.51386220075432,49.909651034931755,37.65619985685482,70.06396688880494,63.14810305447157,61.96443533608123,39.68657992600262,33.182852508252175,25.093920736850595,51.90887572183953,72.42381510321478,54.20385662007917,54.34187593144975,44.937999134605285,54.31594067600298,30.09535373212172,71.58812954501816,49.614159436065826,52.91395480106467,51.24733124168494,67.36775518322456,28.303781691771473,53.7736736671824,32.088982346984444,24.4106594958279,32.156113190157704,18.494048922202072,60.340323572902236,27.323343419713776,58.28295648695687,37.51254035787235,66.55904262268373,69.48140665121845,69.31840915818609,53.692703839314184,59.11430037118166,63.31115520847023,48.578369742303416,40.28209612440916,75.2880691275336,67.82586775915847,38.430645809385204,30.495652761139855,51.106346654740356,26.64495453746279,51.09749596769989,72.63563973331375,75.82304958202444,46.3490464122414,47.812804679864996,39.57974052448169,51.750631209784,69.60064813730948,31.22295180880805,62.42502200662698,30.60811823356205,53.05555302913164,53.83287440264228,75.57251623519181,76.50637127668176,76.06060827764576,51.3655670212784,63.63801340124146,40.319689452356904,70.02891429368366,66.06386067452607,42.252720992232305,34.24841533857079,36.376679531080605,54.55102854034987,36.78285753529732,46.73655307838329,38.615466140378004,29.479642445003027,29.761014867363475,52.60675328218218,32.55219134474758,59.77803296982396,45.59065789671214,53.94680525032745,73.50100161749104,72.49097871650639,35.361931643355916,64.45719338089116,52.41627201273398,49.68227101679576,42.706949313060846,53.117346077289675,59.87801028788769,28.89375247231389,27.577759755570256,23.68631731972518,16.664844560349213,22.850201994765346,14.029753540761208,54.8298381198594,53.497792969702566,55.603027879647414,55.53120566468814,61.798598002926134,41.85990784690475,52.84701560494096,54.07401834916939,68.3127290482255,66.52357042608257,42.765317641203154,34.69499284279583,64.44302347371045,53.663570696890815,40.551394060844494,65.13936811392601,71.89174769591384,48.757199583536725,69.33672146969413,45.13764467976475,68.75302859440954,50.401067808254076,41.44248353606709,76.79184516710102,74.80161005457285,49.97526927366369,58.71537704435041,64.32939712180996,39.358182501345034,33.95441443343755,28.640999536205907,22.65653206515326,51.07361768481183,24.939218620739677,48.68054711460346,35.15492950770412,56.43526200441496,62.33190291845489,25.39237904664356,35.343889621985475,27.553038212688694,20.93637641067407,43.2540799100173,19.10365300983482,56.60148890765638,63.876046230961414,27.998909667746034,28.593618331255,55.417316114556535,63.391665949758,40.80075761570583,52.67735219880024,34.007906520886614,37.31469428346533,63.971425647137046,54.20446202687712,38.8583452847449,39.359011599521075,57.214767487159065,58.32100171312998,37.45280207634567,35.988649402453184,33.04050938956805,61.792827412428124,33.47401442000429,31.451691052864277,39.24636353478838,60.216781256819644,24.44120346774579,51.255987518261605,30.378552028358303,54.193389650204246,25.721729593680703,40.66905950471011,51.38185578732759,23.926791012597832,31.762899458255717,43.40522038693413,21.476052973509066,63.89441388706746,62.35839168842339,64.02774409242006,null,71.55287613005031,67.14648674604739,48.12279696008046,40.059162834610355,48.6715943092218,70.8033875854626,63.359285489180934,53.047249165375966,64.79754610529612,62.07034307349304,75.38854869336876,82.33097713725498,49.735430453767094,39.216688604261975,61.04834208466599,49.50174048770927,72.19012896274874,36.87514486166956,54.38610347368249,40.158321525477795,42.404921997166966,68.2791437130498,29.68681759542387,null],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y2\"},{\"hovertemplate\":\"\\u003cbr\\u003e%{text}\",\"marker\":{\"color\":\"red\",\"symbol\":\"triangle-up\"},\"mode\":\"markers\",\"name\":\"\\u4e70\\u5165\\u6210\\u4ea4\",\"text\":[\"\\u8d44\\u4ea7:99.8\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 1995\\u624b \\u4ef7\\u683c:4.56 \\u6210\\u4ea4\\u989d:91.0\\u4e07\",\"\\u8d44\\u4ea7:113.2\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2104\\u624b \\u4ef7\\u683c:4.83 \\u6210\\u4ea4\\u989d:101.6\\u4e07\",\"\\u8d44\\u4ea7:110.5\\u4e07\\u003cbr\\u003e\\u5206\\u7ea2\\u914d\\u80a1:\\u6d77\\u5b81\\u76ae\\u57ce 9\\u624b \\u4ef7\\u683c:4.81 \\u6210\\u4ea4\\u989d:0.4\\u4e07\",\"\\u8d44\\u4ea7:109.9\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2392\\u624b \\u4ef7\\u683c:4.18 \\u6210\\u4ea4\\u989d:100.1\\u4e07\",\"\\u8d44\\u4ea7:115.8\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2563\\u624b \\u4ef7\\u683c:4.07 \\u6210\\u4ea4\\u989d:104.4\\u4e07\",\"\\u8d44\\u4ea7:125.3\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2963\\u624b \\u4ef7\\u683c:3.63 \\u6210\\u4ea4\\u989d:107.6\\u4e07\",\"\\u8d44\\u4ea7:127.7\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 362\\u624b \\u4ef7\\u683c:3.79 \\u6210\\u4ea4\\u989d:13.7\\u4e07\",\"\\u8d44\\u4ea7:137.5\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2938\\u624b \\u4ef7\\u683c:4.30 \\u6210\\u4ea4\\u989d:126.4\\u4e07\",\"\\u8d44\\u4ea7:133.4\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 276\\u624b \\u4ef7\\u683c:4.10 \\u6210\\u4ea4\\u989d:11.3\\u4e07\",\"\\u8d44\\u4ea7:129.2\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 30\\u624b \\u4ef7\\u683c:3.98 \\u6210\\u4ea4\\u989d:1.2\\u4e07\",\"\\u8d44\\u4ea7:130.0\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2980\\u624b \\u4ef7\\u683c:4.07 \\u6210\\u4ea4\\u989d:121.2\\u4e07\",\"\\u8d44\\u4ea7:127.6\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 242\\u624b \\u4ef7\\u683c:3.92 \\u6210\\u4ea4\\u989d:9.5\\u4e07\",\"\\u8d44\\u4ea7:122.8\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 23\\u624b \\u4ef7\\u683c:3.82 \\u6210\\u4ea4\\u989d:0.9\\u4e07\",\"\\u8d44\\u4ea7:120.8\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 3\\u624b \\u4ef7\\u683c:3.78 \\u6210\\u4ea4\\u989d:0.1\\u4e07\",\"\\u8d44\\u4ea7:126.6\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2551\\u624b \\u4ef7\\u683c:4.45 \\u6210\\u4ea4\\u989d:113.5\\u4e07\",\"\\u8d44\\u4ea7:135.1\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2825\\u624b \\u4ef7\\u683c:4.38 \\u6210\\u4ea4\\u989d:123.7\\u4e07\",\"\\u8d44\\u4ea7:120.1\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 289\\u624b \\u4ef7\\u683c:4.03 \\u6210\\u4ea4\\u989d:11.6\\u4e07\",\"\\u8d44\\u4ea7:121.6\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 26\\u624b \\u4ef7\\u683c:3.74 \\u6210\\u4ea4\\u989d:1.0\\u4e07\",\"\\u8d44\\u4ea7:140.5\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 3028\\u624b \\u4ef7\\u683c:4.28 \\u6210\\u4ea4\\u989d:129.6\\u4e07\",\"\\u8d44\\u4ea7:141.8\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 289\\u624b \\u4ef7\\u683c:4.22 \\u6210\\u4ea4\\u989d:12.2\\u4e07\",\"\\u8d44\\u4ea7:152.9\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 3108\\u624b \\u4ef7\\u683c:4.44 \\u6210\\u4ea4\\u989d:138.1\\u4e07\",\"\\u8d44\\u4ea7:156.2\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2968\\u624b \\u4ef7\\u683c:4.95 \\u6210\\u4ea4\\u989d:146.9\\u4e07\",\"\\u8d44\\u4ea7:153.5\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 271\\u624b \\u4ef7\\u683c:4.79 \\u6210\\u4ea4\\u989d:13.0\\u4e07\",\"\\u8d44\\u4ea7:149.9\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 25\\u624b \\u4ef7\\u683c:4.71 \\u6210\\u4ea4\\u989d:1.2\\u4e07\",\"\\u8d44\\u4ea7:152.9\\u4e07\\u003cbr\\u003e\\u4e70\\u5165:\\u6d77\\u5b81\\u76ae\\u57ce 2\\u624b \\u4ef7\\u683c:4.60 \\u6210\\u4ea4\\u989d:0.1\\u4e07\"],\"x\":[\"2019-01-24\",\"2019-05-07\",\"2019-06-21\",\"2019-08-07\",\"2019-11-12\",\"2020-02-04\",\"2020-05-25\",\"2020-09-25\",\"2020-10-29\",\"2020-11-02\",\"2021-07-02\",\"2021-07-09\",\"2021-07-27\",\"2021-07-28\",\"2021-11-30\",\"2022-04-22\",\"2022-04-26\",\"2022-04-27\",\"2022-08-03\",\"2022-08-04\",\"2023-03-15\",\"2023-06-20\",\"2023-06-21\",\"2023-06-26\",\"2023-06-27\"],\"y\":[0.9978685514154816,1.1320532897787476,1.1051376238865662,1.0985140666142275,1.1578906351439429,1.2534296754548406,1.2774750968357609,1.37502296671926,1.3341499993514012,1.2923662092941806,1.2999218346221681,1.2763096027758547,1.2278862829293962,1.2083981129379795,1.2657202604760505,1.3507111524747228,1.2005496764112615,1.216456216386466,1.405343502747245,1.4180152188107062,1.528612798051276,1.561630645797858,1.535428176831641,1.4994974067362736,1.528889266755347],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"hovertemplate\":\"\\u003cbr\\u003e%{text}\",\"marker\":{\"color\":\"green\",\"symbol\":\"triangle-down\"},\"mode\":\"markers\",\"name\":\"\\u5356\\u51fa\\u6210\\u4ea4\",\"text\":[\"\\u8d44\\u4ea7:111.5\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 1995\\u624b \\u4ef7\\u683c:5.14 \\u6210\\u4ea4\\u989d:102.5\\u4e07\",\"\\u8d44\\u4ea7:110.5\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2104\\u624b \\u4ef7\\u683c:4.76 \\u6210\\u4ea4\\u989d:100.2\\u4e07\",\"\\u8d44\\u4ea7:115.4\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 9\\u624b \\u4ef7\\u683c:4.39 \\u6210\\u4ea4\\u989d:0.4\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2392\\u624b \\u4ef7\\u683c:4.39 \\u6210\\u4ea4\\u989d:105.0\\u4e07\",\"\\u8d44\\u4ea7:122.7\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2563\\u624b \\u4ef7\\u683c:4.36 \\u6210\\u4ea4\\u989d:111.7\\u4e07\",\"\\u8d44\\u4ea7:139.0\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2963\\u624b \\u4ef7\\u683c:4.14 \\u6210\\u4ea4\\u989d:122.7\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 362\\u624b \\u4ef7\\u683c:4.14 \\u6210\\u4ea4\\u989d:15.0\\u4e07\",\"\\u8d44\\u4ea7:131.8\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2938\\u624b \\u4ef7\\u683c:4.06 \\u6210\\u4ea4\\u989d:119.2\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 276\\u624b \\u4ef7\\u683c:4.06 \\u6210\\u4ea4\\u989d:11.2\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 30\\u624b \\u4ef7\\u683c:4.06 \\u6210\\u4ea4\\u989d:1.2\\u4e07\",\"\\u8d44\\u4ea7:125.1\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2980\\u624b \\u4ef7\\u683c:3.85 \\u6210\\u4ea4\\u989d:114.7\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 242\\u624b \\u4ef7\\u683c:3.85 \\u6210\\u4ea4\\u989d:9.3\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 23\\u624b \\u4ef7\\u683c:3.85 \\u6210\\u4ea4\\u989d:0.9\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 3\\u624b \\u4ef7\\u683c:3.85 \\u6210\\u4ea4\\u989d:0.1\\u4e07\",\"\\u8d44\\u4ea7:136.5\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2551\\u624b \\u4ef7\\u683c:4.90 \\u6210\\u4ea4\\u989d:125.0\\u4e07\",\"\\u8d44\\u4ea7:143.0\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2825\\u624b \\u4ef7\\u683c:4.55 \\u6210\\u4ea4\\u989d:128.5\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 289\\u624b \\u4ef7\\u683c:4.55 \\u6210\\u4ea4\\u989d:13.1\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 26\\u624b \\u4ef7\\u683c:4.55 \\u6210\\u4ea4\\u989d:1.2\\u4e07\",\"\\u8d44\\u4ea7:151.4\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 3028\\u624b \\u4ef7\\u683c:4.53 \\u6210\\u4ea4\\u989d:137.2\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 289\\u624b \\u4ef7\\u683c:4.53 \\u6210\\u4ea4\\u989d:13.1\\u4e07\",\"\\u8d44\\u4ea7:161.2\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 3108\\u624b \\u4ef7\\u683c:4.76 \\u6210\\u4ea4\\u989d:147.9\\u4e07\",\"\\u8d44\\u4ea7:169.5\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2968\\u624b \\u4ef7\\u683c:5.19 \\u6210\\u4ea4\\u989d:154.0\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 271\\u624b \\u4ef7\\u683c:5.19 \\u6210\\u4ea4\\u989d:14.1\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 25\\u624b \\u4ef7\\u683c:5.19 \\u6210\\u4ea4\\u989d:1.3\\u4e07\\u003cbr\\u003e\\u5356\\u51fa:\\u6d77\\u5b81\\u76ae\\u57ce 2\\u624b \\u4ef7\\u683c:5.19 \\u6210\\u4ea4\\u989d:0.1\\u4e07\"],\"x\":[\"2019-02-26\",\"2019-06-21\",\"2019-10-15\",\"2019-12-31\",\"2020-06-02\",\"2020-12-29\",\"2021-08-10\",\"2021-12-22\",\"2022-05-31\",\"2022-12-07\",\"2023-04-03\",\"2023-07-31\"],\"y\":[1.1154197147793579,1.1051376238865662,1.1535652485265875,1.2269240493636846,1.3903185724422213,1.3175297353860231,1.2505844918195868,1.3650217848042823,1.429761966277213,1.5139828984041357,1.6123069591877508,1.695201025443549],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.6975],\"type\":\"category\",\"tickangle\":45,\"nticks\":239},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0]},\"yaxis2\":{\"anchor\":\"x\",\"overlaying\":\"y\",\"side\":\"right\"},\"annotations\":[{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"\\u8d44\\u4ea7\\u66f2\\u7ebf\",\"x\":0.34875,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"\\u7b56\\u7565\\u6307\\u6807\",\"x\":0.82375,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"}],\"margin\":{\"l\":20,\"r\":20,\"t\":50,\"b\":50},\"width\":1040,\"height\":435,\"hoverlabel\":{\"bgcolor\":\"rgba(255,255,255,0.8)\"},\"hovermode\":\"x unified\"},                        {\"responsive\": true}                    ).then(function(){\n                            \nvar gd = document.getElementById('adb90121-2fa2-4210-a1e3-2ff3aa5f2a69');\nvar x = new MutationObserver(function (mutations, observer) {{\n        var display = window.getComputedStyle(gd).display;\n        if (!display || display === 'none') {{\n            console.log([gd, 'removed!']);\n            Plotly.purge(gd);\n            observer.disconnect();\n        }}\n}});\n\n// Listen for the removal of the full notebook cells\nvar notebookContainer = gd.closest('#notebook-container');\nif (notebookContainer) {{\n    x.observe(notebookContainer, {childList: true});\n}}\n\n// Listen for the clearing of the current output cell\nvar outputEl = gd.closest('.output');\nif (outputEl) {{\n    x.observe(outputEl, {childList: true});\n}}\n\n                        })                };                });            </script>        </div>"
  },
  "metadata": {},
  "output_type": "display_data"
 }
]
```
