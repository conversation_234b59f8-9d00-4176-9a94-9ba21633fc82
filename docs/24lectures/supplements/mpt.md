---
title: 投资组合理论与实战

puppeteer:
    format: "A4"
    scale: 1
    margin:
        top: 2.5 cm
        right: 2cm
        bottom: 2.5 cm
        left: 2cm
    displayHeaderFooter: true
    headerTemplate: '<div style="width:100%; text-align:center; border-bottom: 1pt solid #eeeeee; margin: 20px 10px 10px; font-size: 10pt;padding-bottom:10px"><span class=title></span></div>'
    footerTemplate: '<div style="display:flex; justify-content:space-around;width:100%;border-top: 1pt solid #eeeeee; margin: 10px 10px 20px; font-size: 8pt;color:#aaa"><div style="width:30%"><span class=pageNumber></span>/<span class=totalPages></span></div><div style="width:30%">大富翁量化课程</div><div style="width:30%">宽粉（quantfans_99)</div>'
---



- [基本概念](#基本概念)
  - [收益率、夏普和波动率计算](#收益率夏普和波动率计算)
- [蒙特卡洛模拟](#蒙特卡洛模拟)
  - [确定最佳投资组合](#确定最佳投资组合)
- [优化算法--scipy.optimize](#优化算法--scipyoptimize)
  - [minimize\_scalar](#minimize_scalar)
  - [使用 minimize 方法](#使用-minimize-方法)
- [PyportfolioOpt](#pyportfolioopt)
  - [数据预处理](#数据预处理)
  - [计算协方差](#计算协方差)
  - [求解最优资产组合](#求解最优资产组合)
  - [可视化与有效前沿](#可视化与有效前沿)
- [CAPM与MPT](#capm与mpt)
- [结束语](#结束语)
- [参考文献](#参考文献)



现代投资组合理论（MPT）是由马科维茨于 1952 年提出的，是现代金融 7 个基本理论之一。它用数学术语描述了多元化和风险管理等概念，为投资者提供了构建多元化投资组合的工具集，即假定投资者投资于多个资产，在满足给定预期回报率下，可以通过优化求解出风险最小的投资组合。

所有的这些资产组合构成一条曲线（以资产组合的标准差为横轴，预期回报率为纵轴），称为前沿资产组合曲线，其中曲线的上半部分又被称为有效前沿。

![75%](https://images.jieyu.ai/images/2023/10/portfolio-optimisation.png)

这个图是一个上下对称图，在图的下半部分上的每一个点，都可以在上半部分找到一个对应点，它们具有相同的风险，但有效前沿上的点具有更大的预期收益率。因此下半部分投资是**没有投资者愿意持有的**。

每个投资者根据自己的风险承受能力（效用函数），在有效前沿上选择自己的投资组合。

马科维茨也因为对 MPT 理论的贡献，从而获得了 1990 年诺贝尔经济学奖。他本人非常看好通过资产组合来分散投资风险这一理念，认为“资产组合是投资中惟一免费可得的午餐”。

## 基本概念
我们以最简单的两个风险资产组合为例来解释有效前沿：

$$
R_p = w_1R_1 + (1-w_1)R_2
$$

这里：

$R_p$ 是组合的收益
$W_1$ 是资产 1 的配比权重
$R_1$ 是资产 1 的收益。

组合的风险用方差来衡量：

$$
\sigma_p = \sqrt{w_1^2\sigma_1^2 + w_2^2\sigma_2^2 + 2w_1w_2Cov(R_1, R_2)}\newline 
        =\sqrt{w_1^2\sigma_1^2 + w_2^2\sigma_2^2 + 2w_1w_2\rho_{1,2}\sigma_1\sigma_2}
$$

这里的$\rho_{1,2}$ 是资产 1 与资产 2 之间的相关系数。

当两个资产的相关系数为 1 时，上式就化简为：

$$
\sigma_p =\sqrt{w_1^2\sigma_1^2 + w_2^2\sigma_2^2 + 2w_1w_2\sigma_1\sigma_2}\newline
        =\sqrt{(w_1\sigma_1 + w_2\sigma_2)^2}\newline 
        = w_1\sigma_1 + w_2\sigma_2
$$

当两个资产之间的相关系数为-1 时，

$$
\sigma_p =\sqrt{w_1^2\sigma_1^2 + w_2^2\sigma_2^2 - 2w_1w_2\sigma_1\sigma_2}\newline
        =\sqrt{(w_1\sigma_1 - w_2\sigma_2)^2}\newline 
        = w_1\sigma_1 - w_2\sigma_2
$$

从上式可以看出，如果两个资产完全负相关，那么等权重配置两个资产，则资产收益变为零，从而构成一个无风险的组合。此时由于 $\sigma_p$等于零，所以它就成为图形上最左端的点。

当其中一个资产为 100%时，则构成图形中上下两个点。

在这个系列中，我们将首先用 4 个标的的组合，先后用蒙特卡洛方法和优化算法分别演示如何求得最佳资产组合，这是比较底层的方法，当我们掌握原理之后，则可以使用第三方库来完成这项工作。

### 收益率、夏普和波动率计算

让我们首先任意分配一组权重，看看这样得到的组合的**收益率**、**夏普率**等各项参数如何。在这个过程中，我们主要是了解：

1. 如何获取数据
2. 如何产生随机权重向量（重点是满足权重和为 1 的约束条件）
3. 如何计算夏普率、波动率等指标。
   
假定我们的资产组合是：

```
600519 贵州茅台
300750 宁德时代
300059 东方财富
601398 工商银行
```

我们通过下面的代码获得它们近一年的收益，保存在 returns dataframe 中：

```python
import arrow
import akshare as ak
import pandas as pd
import numpy as np
from IPython.display import display


stocks = ["600519", "300750", "300059", "601398"]

frames = {}

now = arrow.now()
start = now.shift(years = -1)
end = now.format("YYYYMMDD")
start = start.format("YYYYMMDD")

for code in stocks:
    bars = ak.stock_zh_a_hist(symbol=code, 
                              period="daily", 
                              start_date=start, 
                              end_date=end, 
                              adjust="qfq")
    
    bars.index = pd.to_datetime(bars["日期"])
    frames[code] = bars["收盘"]

prices = pd.DataFrame(frames)
returns = prices.pct_change()

returns.dropna(how='any', inplace=True)
display(returns.head().style.format('{:,.2%}'))
```

这段代码我们在前一篇文章讲 CAPM 时已经见过了。

接下来，我们先随机分配一个权重（这是蒙特卡洛方法的第一步），计算出它的夏普率：

```python
import numpy as np
from empyrical import sharpe_ratio

weights = np.array(np.random.random(4))
print('Random Weights:')
print(weights)

print('\nRebalance')
weights = weights/np.sum(weights)
print(weights)

# 生成每日每个标的对组合的贡献
weighted_returns = weights * returns
weighted_returns.head()

# 把每一行按列加总，就得到了每日资产收益
port_returns = weighted_returns.sum(axis=1)

# 然后计算组合资产的波动
cov = np.cov(port_returns.T)
port_vol = np.sqrt(np.dot(np.dot(weights, cov), weights.T)) # 0.01

# 使用 sharpe_ratio来计算夏普率
sr = sharpe_ratio(port_returns) # 0.18

print("Sharpe Ratio and Vol")
print(f"{sr:.2f} {port_vol:.2f}")
```

在代码中，我们先是随机生成了一个权重矩阵，然后对它进行了归一化（权重矩阵各项之和必须为 1）。

一些文章会使用对数收益率。但是，在计算方差时，多数人倾向于几何方差是没有意义的（从 google 搜索结果看）。

我们使用了 empyrical 中的 sharpe_ratio 方法来计算夏普率，为简单起见，我们将 risk_free 利率设置为 0。 empyrical 是用来计算策略各项指标，如夏普率、sortino、maxdrawdown 等指标的工具，由 quantpian 开发并开源。像这样的常用量化库，在**大富翁量代交易课程**中都有介绍。

最终，我们得到以下结果：

```
Random Weights:
[0.48349071 0.32903015 0.85308562 0.64038565]

Rebalance
[0.20966711 0.14268485 0.36994299 0.27770505]
Sharpe Ratio and Vol
0.72 0.01
```

最终我们得到了该资产组合的夏普率为 0.72。一般认为，如果我们投资的是指数或者权重股，那么夏普超过 1 是可以接受的投资；对其它高风险权益类投资，一般要超过 1.8，但很少有资产能超过 3。在**大富翁量化交易课程**里，我们通过蒙特卡洛方法讨论了夏普率与最大回撤之间的关系，即夏普率为 1 时，对应的最大回撤为多少是可能出现的；夏普率为 2 时，对应的最大回撤为多少是可能出现的，等等。

一切准备工作都已就绪，接下来我们将介绍如何使用蒙特卡洛方法来求得上述资产组合的最优配置。


## 蒙特卡洛模拟

所谓蒙特卡洛模拟，就是随机产生大量的资产分配方案，然后再计算各种分配方案下，所得到的波动率、夏普率，再根据最优的夏普率，反查资产分配方案。

主要操作我们在上一节已经介绍过，这一步主要做的事情是不断地重复。我们先把代码给出来，再进行解释：

```python
num_ports = 5000

w = np.zeros((num_ports, len(stocks)))
vol_arr = np.zeros(num_ports)
sharpe_arr = np.zeros(num_ports)
port_return_arr = np.zeros((num_ports, len(returns)))
cov_arr = np.zeros(num_ports)

for i in range(num_ports):
    weights = np.array(np.random.random(len(stocks)))
    weights = weights/np.sum(weights)  
                      
    w[i,:] = weights
    
    weighted_returns = weights * returns
    port_return_i = weighted_returns.sum(axis=1)
    port_return_arr[i,:] = port_return_i

    cov = np.cov(port_return_i)
    cov_arr[i] = cov
    vol_arr[i] = np.sqrt(np.dot(weights.T, np.dot(cov, weights)))
    sharpe_arr[i] = sharpe_ratio(port_return_i)
```

我们主要定义了这样四个数组：

1. 权重矩阵 all_weights。我们打算重复 5000 次采样，由于资产组合共有 4 个标的，所以，它是一个 5000 * 4 的矩阵。每一行对应一次资产组合分配。
2. 夏普率数组。它是一个 size 为 5000 的数组，记录了每一次计算出来的 sharepe 率。
3. 波动率数组。它也是一个 size 为 5000 的数组，记录了每一次计算邮来的波动率。
4. port_return_arr，在示例中，它是一个 5000 * 241 大小的矩阵，每一行记录了组合在过去一年中每一天的收益。

当上述代码运行完成之后，我们就得到了 5000 组夏普值。根据夏普值的定义，我们直接找到夏普值最大的那一组，就是风险最小、收益最高的资产组合。

### 确定最佳投资组合

我们使用 np.argmax 来寻找夏普最大时的位置，此点即为最佳投资组合：

```python
# 检查最高的 SHARPE
pos = np.argmax(sharpe_arr)
print(pos, sharpe_arr[pos])
print("stocks", stocks)
print("Portfolio Allocation:", w[pos])
```

这样我们得到资产分配方案类似如下：

|      | 标的 1 | 标的 2 | 标的 3 | 标的 4 |
| ---- | ------ | ------ | ------ | ------ |
| 比例 | 35.3%  | 0.3%   | 0.8%   | 64%    |

我们将上述试验结果绘制成图形，来看看是否符合有效前沿理论：

```python
import matplotlib.pyplot as plt

annual_return = np.prod((1 + port_return_arr), axis=1) - 1
plt.scatter(vol_arr, annual_return, c=sharpe_arr, cmap='RdYlBu')
plt.colorbar(label='Sharpe Ratio')

plt.scatter(vol_arr[pos], annual_return[pos], c='red',s=80)
```

我们以波动率为 x 轴，年化回报为 y 轴。在每一个 x 上，都存在若干组年化回报数据，有正有负。显然，对于同一个 x，正好是那些处在有效前沿上的组合，正好是收益最大或者亏损最大的组合。

按照 MPT 理论，只有那些在 y 轴上方，且处于有效前沿上的才是值得关注的组合，然后根据我们的风险承受能力，来选择这条线上对应的组合。

我们把夏普率最高的那组方案，用红色的点标注出来。

![50%](https://images.jieyu.ai/images/2023/10/mpt-sharpe-vol.png)

从走势图来看，我们这样求出来的资产组合确实是最优的。但是，如果我们将它与文章开头的那个图相比，我们会得出什么结论？

1. 这把牌不行。如果你愿意承担较大风险，这把牌也不能给你想要的收益。浪得不够狠。

2. 资产组合并没有形成直观的有效前沿，原因主要是投资组合整体的收益率受组合内资产的收益率限制，即min(组合内资产收益率)≤资产组合收益率≤max(组合内资产收益率)，因此并不是能实现所有收益率。

因此我们得到结论：**你得重新选标的。**

另外，如果你现在就急于用MPT来进行投资。。。你还得再学点啥。

历史当然总是在重复自己，一切历史都是当代史。但是仍然有很多东西要讨论。

首先，我们应该放多少支标的到这个组合里来？我们的示例中只使用了4支，如果我们对沪深300或者中证1000来做指增，会不会更好一点？

其次，上述两幅图是针对同一资产组合，不同时间段情况所形成，很明显收益及风险都有较大差异。如果我们在不同的时间点来优化投资组合，我们得到的仓位显然会有所不同。因此我们提出问题：我们应该多久计算一次并执行调仓？会不会有这样一种情况，每次调仓是在用过去的最优解，而它很快就变成了次优或者最劣解？也就是，这个组合它的动量周期是多久？

显然，任何一个有价值的方案，往往都不是一篇短文能cover的，我们会在后续的文章中不断深入探讨这个问题。

好，我们先放下这些问题，先来看一个技术问题：

**执行上述循环5000次，我们花了大约 5.2 秒。**

这是只有4支标的的情况。很显然，随着标的数的增加，我们需要暴力搜索的空间也随之变大。这种方法，在标的数增加到50支、100支时，是否还可行呢？

在讨论可行性问题前，我们可以尝试进行一些速度优化。

![50%](https://images.jieyu.ai/images/2023/12/vectorize_vs_for.png)

我们可以使用下面的算法，把部分计算移出了循环：

```python
from empyrical import sharpe_ratio
import time
start = time.time()
num_ports = 5000
all_weights = np.random.rand(num_ports, len(stocks))

shape = (len(all_weights), -1)
all_weights = all_weights/all_weights.sum(axis=1).reshape(shape)

# got 241 daily summed returns over 5000 iters
all_port_returns = np.dot(all_weights,returns.T)

all_cov = np.cov(all_port_returns).diagonal()

# 计算波动率
all_vol = []
all_sharpe = []

annual_return = np.prod((1 + all_port_returns), axis=1) - 1

for i in range(num_ports):
    all_vol.append(np.sqrt(np.dot(all_weights[i].T, np.dot(all_cov[i], all_weights[i]))))
    all_sharpe.append(sharpe_ratio(all_port_returns[i]))

end = time.time()
print("计算用时", end-start)

# 绘图
all_vol = np.array(all_vol)
pos = np.argsort(all_vol)

plt.scatter(all_vol, annual_return, c=all_sharpe, cmap='RdYlBu')
plt.colorbar(label='Sharpe Ratio')

max_sharpe_pos = np.argmax(all_sharpe)
plt.scatter(all_vol[max_sharpe_pos], annual_return[max_sharpe_pos], c='red',s=80)
```

不过，在按行计算波动率和夏普率的时候，我不得不做出妥协，改用循环。在这种情况下（投资组合中只有4只标的），我们现在的执行时间需要0.65秒。提升了大概8倍的速度（初始为5.2s左右）。

PyPortfolioOpt的作者曾在一个示例中用到了这样的方法：

```
n_samples = 10000
w = np.random.dirichlet(np.ones(ef.n_assets), n_samples)
rets = w.dot(ef.expected_returns)
stds = np.sqrt(np.diag(w @ ef.cov_matrix @ w.T))
sharpes = rets / stds
```
计算速度非常之快（小于0.1秒），但需要对其中的ef.cov_matrix进行替换，感兴趣的同学可以研究下它的源码。

对于一个仅有4支标的的组合来说，这个速度（不到1秒）显然可以令人满意了。但我们也在上一篇文章中，提出一个问题，随着标的数的增加，使用蒙特卡洛模拟方法，我们需要暴力搜索的空间也随之变大。那么，究竟会增大多少呢，此时还可不可行？

我们先来看只有两支标的情况。假设资产权重的间隔是1%，即某个资产要么分配1%，要么分配2%，而不会分配1.05%这样的非整数权重。这样我们就可以算得，如果要把这些权重分布全部模拟到，搜索空间将是：

A: 1-100 ~ 101个选择项
B: 1个选择项

当A的权重选定以后，在权重和为1的约束下，B就只有一个选择。因此，总共是101次搜索。

当如果标的数量上升，我们为了模拟尽可能多的情况，一定会扩大模拟次数，因此总的运行时长迅速上涨。同时随着标的数的增加，我们要搜索的次数需要呈指数级增加，而不是按标的数线性增加。

因此，搜索次数是一个组合问题。假设存在四个标的，并不是简单的4×100就可以模拟出所有组合，而是存在：
$C_{99}^3$种分配方案。

对任意n个标的，每个标的的权重间隔按1%计算的话，这个搜索空间将是：

$C_{101-(n-1)}^{n-1}$次。如果我们有50支标的，则需要搜索最少**1.998e+21亿**次！

!!! tip
    可以使用 math.comb来计算组合数。

所以，使用蒙特卡洛方法，会存在无法穷尽所有样本空间的可能性。我们必须采用数学的方法，来优化这个求解过程。

## 优化算法--scipy.optimize

寻找给定收益率下的最小波动率，或者给定波动率下的最大夏普率，这实际上是一类常见的优化问题，即：

![](https://images.jieyu.ai/images/2023/12/mpt-optimize-formula.png)

我们可以使用 Scipy.optimize工具库来求解类似问题。

**scipy.optimize包的主要功能**
* SciPy Optimize提供最小化(或最大化)目标函数的功能，可能存在约束。
* 它包括解决非线性问题(支持局部和全局优化算法) ，线性规划，约束和非线性最小二乘，根寻找和曲线拟合。
* 当我们需要优化输入函数的参数时，scipy.optimize包含很多有用的方法，并可以处理不同种类的函数

它提供以下方法：
* minimize_scalar( )：使一个变量最小化
* minimize( )：使多个变量最小化
* curve_fit( )：寻找最优曲线，适合a set of data
* root_scalar( )：求一个变量的零点
* root( )：求多个变量的零点
* linprog( )：在线性等式和不等式的约束下，最小化线性目标函数

在这些方法中，我们主要介绍**minimize_scalar**和**minimize**。

### minimize_scalar

有一类问题，比如解方程，它的解是一个数字，此时就可以用 minimize_scalar 方法来求解。

例如，我们的函数为：

$y=3x^4-2x+1$

minimize_scalar( )就是帮助我们找到函数最小值(y最小)时精确的坐标。通俗地说，就是解方程。

```python
from scipy.optimize import minimize_scalar

def objective_function(x):
    return 3 * x ** 4 - 2 * x + 1
# 定义需要求解的函数

res = minimize_scalar(objective_function)
# 求出函数的最小值

print(res)
```
我们重点解释一下输出结果，这将在后面继续使用：

```
 message: 
          Optimization terminated successfully;
          The returned value satisfies the termination criteria
          (using xtol = 1.48e-08 )
 success: True
     fun: 0.17451818777634331
       x: 0.5503212087491959
     nit: 12
    nfev: 15
```

这里的fun是函数值，x是变量值，如果我们把x的值代入到公式 $3*x^4-2*x+1$，我们会得得到0.17,即fun的值。

要注意，不是所有情况都能进行优化求解的。比如，$y=x^3$没有最小值，如果我们对它求解，会导致overflowerror.

另外的情况是，存在多个最小值的情况，此时minimize_scalar不能保证找到函数的全局最小值。

我们以$y=x^4-x^2$为例，它存在多个最小值，minimize_scalar ()不能保证找到函数的全局最小值。我们尝试一下：



```python
from scipy.optimize import minimize_scalar
# help(minimize)

def objective_function(x):
    return x**4 - x**2

res = minimize_scalar(objective_function)
res
```
我们将得到如下结果：

```
 message: 
          Optimization terminated successfully;
          The returned value satisfies the termination criteria
          (using xtol = 1.48e-08 )
 success: True
     fun: -0.24999999999999994
       x: 0.7071067853059209
     nit: 11
    nfev: 14
```

这样求解出来的方程的根是0.707。但我们知道，该方程至少还有一个-0.707的根，因此， minimize_scalar默认情况下，是无法找出全部的根的。但我们可能通过设定参数，来控制用于优化的求解器。一共有三种设定方式：
* brent:布伦特算法，也是该函数的默认算法
* golden:黄金分割算法，根据已有研究，该方式效果略差于brent
* bounded:布伦特算法的“有界实现”

brent和golden属于bracket：这是一个由两个或三个元素组成的序列，它提供了对最小区域边界的初始猜测。但是，这些求解器**并不保证所找到的最小值将在此范围内**。

bounded属于bounds：这是一个两个元素的序列，严格限制搜索区域的最小值。因此当最小值在已知范围内时，限制搜索区域才是有用的。这个概念我们在后面还将进一步接触。我们来看使用bounds方式的例子：

```python
# 当我们采用bounds方式限定时
res = minimize_scalar(objective_function, method='bounded', bounds=(-1, 0))
res
```

现在，我们求解的x将落在区间(-1,0)之间，我们因此得到：

```
 message: Solution found.
 success: True
  status: 0
     fun: -0.24999999999998732
       x: -0.707106701474177
     nit: 10
    nfev: 10
```

这次我们得到了期望中的负数解。

不过，最佳资产组合的求解要比这个复杂，我们必须使用另一个方法，即`minimize`方法，具体优化算法则是最小顺序二乘法。

### 使用 minimize 方法
在前面的示例中，我们了解了目标函数、bounds限定方式以及求解结果 OptimizeResults对象，特别是它的`fun`属性和`x`属性。接下来我们还要介绍约束（constraints）概念。掌握这些概念之后，就基本掌握了凸优化。

我们先来看`minimize`方法的签名：

```
scipy.optimize.minimize(fun, 
                        x0, 
                        args=(), 
                        method=None, 
                        jac=None, 
                        hess=None, 
                        hessp=None, 
                        bounds=None, 
                        constraints=(), 
                        tol=None, 
                        callback=None, 
                        options=None)
```
我们重点介绍跟本章主题相关的参数，主要是`fun`, `x0`, `method`, `bounds`和`constraints`。

这里的`fun`是指目标函数，之前我们已经接触过这个概念。如果目标函数需要传入参数，则它的参数将通过`args`来传入。

优化方法使用一系列迭代来进行求解满足目标函数的物理量`x`，它需要从一组初始值开始。`x0`就是我们提供给优化方法的初始值。它的最终结果，将通过OptimalResult.x来传递回来。

对不同的问题，有不同的优化方案。`minimize`方法支持了大约14种优化方案，此外还允许你自定义优化算法。在进行优化求解之前，我们一般需要指定一种优化算法。

`bound`和`constraints`参数，我们将我们结合有效前沿求解问题，来介绍如何使用。同时，也讲解其它参数的使用。

我们先来构建一个最小方差的投资组合。我们先来定义目标函数：

```python
def portfolio_sd(w):
    return np.sqrt(np.transpose(w) @ (returns.cov() * 253) @ w)
```

注意这里的常数253。我们使用它来将收益进行年化。这是一种粗略但简单的方法。接下来，我们需要定义约束条件，以实现“在某个收益率时，波动最小”这个需求。首先，我们要定义年化收益率：

```python
def portfolio_returns(w):
    return (np.sum(returns.mean() * w)) * 253
```

然后，我们来定义约束。minimize方法要求通过dict来传入约束条件。它支持两种表达式，即'eq'和'ineq'。

```python
constraints = ({'type': 'eq', 'fun': lambda x: np.sum(x) - 1})
```

这里定义了一个约束。'eq'表明'fun'指向的表达式必须等于零。'fun'的值可以是lambda表达式，也可以是常规函数。这个lambda表达式的含义是，权重x的各项之和，必须等于1（这是显然的！）。需要指出的是，这里的`fun`并不是`minimize`首个参数的`fun`，而是一种约束条件。它的参数是惟一的，即`minimize`的第二个参数`x0`所代表的那个物理量。

我们也可以指定一个'ineq'表达式，这意味着`fun`指向的表达式或者函数，其返回值必须大于零。
```python
constraints = ({
    'type': 'ineq', 'fun': lambda x: x[0],
})
```
同样的，这里的`x`是传入的是在迭代过程中的、我们要求解的满足目标函数的物理量。在最佳投资组合求解示例中，它是各项资产的权重。因此，x[0]就表示第0号资产的权重必须大于0。这是我们在本例中，另外一个约束。

实际上，我们对资产权重的约束是[0,1]，上述表达式只能完成一半目标。正确的做法是使用bounds：

```python
x0 = np.ones(len(stocks)) / len(stocks)
bounds = tuple((0,1) for _ in x0)
```
有多少个资产，我们就要建立多少个这样的有界约束。

现在，我们把所有的材料放在一起“煮”：

```python
from scipy.optimize import minimize

res = minimize(
    fun = portfolio_sd,
    x0 = x0,
    method = 'SLSQP',
    bounds = bounds,
    constraints = constraints
)
res
```
这样我们得到以下结果：

```
 message: Optimization terminated successfully
 success: True
  status: 0
     fun: 0.14842169691096224
       x: [ 2.500e-01  2.500e-01  2.500e-01  2.500e-01]
     nit: 1
     jac: [ 0.000e+00  0.000e+00  0.000e+00  0.000e+00]
    nfev: 5
    njev: 1
```
nit表明函数运行的次数，由于我们并没有加上收益率约束，所以这个函数实际上什么也没干，我们从x的值可以看出这一点。

我们加上收益率约束再试一次:

```python
constraints = (
    {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},
    {'type': 'eq', 'fun': lambda x: portfolio_returns(x) - 0.1}
)

res = minimize(
    fun = portfolio_sd,
    x0 = x0,
    method = 'SLSQP',
    bounds = bounds,
    constraints = constraints,
    options = dict(disp=True)
)
res
```
!!! Info
    注意我们在这里加了一个options参数。通过它可以进一步揭示迭代过程。

这次仍然只迭代一次就结束了，但权重x的值变了。我们用它求解出来权重值看一下，得到的组合收益是否为0.1：

```python
portfolio_returns(res.x)
```
返回结果的确是0.1，说明求解过程正确。同时，只迭代一次就得到正确结果，这个效率也令人满意。

现在，如果我们再来求解有效前沿，应该怎么做？


显然，我们应该先找出该组合的理论最大、最小收益，然后在这个区间里进行线性划分，将每一个收益点作为约束条件，求解出对应的资产分配权重，然后再依据这个权重分配，计算其它指标，比如sharpe。

组合的理论最大收益是把所有的仓位都分配给表现最好的股票得到的；反之，如果所有的仓位都分配给表现最差的资产，则我们会得到最小收益。

returns表记录了每一个标的的daily return，我们可以用下面的方法来求每一个标的的年化：

```python
all_annual_returns = (1 + returns.mean()) ** 253 - 1
best = np.max(all_annual_returns)
worst = np.min(all_annual_returns)
print(best, worst)
```

我们得到资产组合的最大收益是20%，最差收益是-28%。现在我们来求解有效前沿，这一次，我们给足完整的代码：

```python {.line-numbers}
sharpes = []
vols = []
weights = []
rets = []

all_annual_returns = (1 + returns.mean()) ** 253 - 1
best = np.max(all_annual_returns)
worst = np.min(all_annual_returns)

x0 = np.ones(len(stocks)) / len(stocks)
constraints = (
    {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},
    {'type': 'eq', 'fun': lambda x: portfolio_returns(x) - target}
)

# 这一次，我们要求每个标的至少分配1%的权重
bounds = tuple((0.01, 1) for _ in stocks)

for target in np.linspace(worst, best, 100):
    res = minimize(
        fun = portfolio_sd,
        x0 = x0,
        method = 'SLSQP',
        bounds = bounds,
        constraints = constraints
    )

    vols.append(res.fun)
    weights.append(res.x)
    rets.append(target)

    # 计算sharpe
    sharpe = sharpe_ratio(returns.dot(res.x))
    sharpes.append(sharpe)

# 绘制图形
plt.scatter(vols, rets, c=sharpes, cmap='RdYlBu')
pos = np.argmax(sharpes)
plt.colorbar(label='Sharpe Ratio')
plt.scatter(vols[pos], rets[pos], marker='*', s=80, color='red')

```

运行总共花了11秒。看上去比运行5000次蒙特卡洛模拟还要慢，但是，它是几乎涵盖了可能的每一种组合。这是5000次蒙特卡洛模拟做不到的。

代码中有一些地方需要进一步解释。我们在前面讲解过lambda表达式`np.sum(x) - 1`中x的来源。您应该注意到，我们并没有在任何地方声明它。实际上，它只是一个形式参数，您可以使用任何变量名，`minimize`方法都会将迭代中的根变量传递给它 -- 在这个例子中，就是求解中的权重矩阵。然而，另一个约束条件：

```markdown
    lambda x: portfolio_returns(x) - target
```

这里的target来自何处？它的值又是如何传递的呢？实际上，我们必须提前使用这个变量，但只到第19行，我们才在for循环中声明这个变量，并且将最新的值传递给了lambda表达式。

还有一个浮点数问题。即'eq'表达式，它的结果是要跟0相比较的。但是由于各种浮点误差，理论上等于零的表达式，在计算机看来可能不等于0。比如， 在 portfolio_returns(x) - target < 0.001的时候，其实我们已经可以认为这个优化到位了，但minimize会一直寻找到这个差值小于1e-7为止。

我们可以通过对portfolio_returns的返回值和target进行取整（比如，小数点后3位）来加快计算速度，但当我们这样做的时候，要注意由于不知道`minimize`内部实现时的步长，有可能导致优化失败。

如果我们有进一步加快速度的需要，可以考虑使用scipy.optimize包中的另外一个方法，`fmin_slsqp`:

```
    scipy.optimize.fmin_slsqp(
        func, 
        x0, 
        eqcons=(), 
        bounds=(), 
        acc=1e-06, 
        iprint=1, 
        disp=None, 
        full_output=0, 
        epsilon=1.4901161193847656e-08, 
        callback=None)
```
它提供了`acc`和`epsilon`这两个参数，应该跟我们这里的思路是一致的。它的默认值也可能就是`minimize`内部实现时的默认值。它也是顺序最小二乘法优化方法。只不过文档比较少，也没有示例。这里就不展开了，供大家参考。


我们已经探讨了资产组合理论的方方面面，并且学会了如何使用蒙特卡洛方法和凸优化方法来求解约束条件下的最佳资产组合。

但是，在实际投资中，我们遇到的情况会更复杂，并不是我们上面介绍的方法就可以完全 cover 的。比如，我们可能会遇到以下情况（需求）：

1. 在均值、方差模型中，我们把收益率为正的波动也计入进来了。但实际上，我们可能只关注收益率为负的那部分波动 -- 这正是策略评估指标 - sortino 指标被发明出来的原因。这只是我们优化模型的路径之一。我们可能还希望实现各种新的发现，但时间和能力可能都不允许。
2. 迄今为止，我们只考虑了权重为正的情况。也就是，我们只允许做多。但实际上，通过做空来对冲风险的需求也是存在的。如果我们要允许做空，又该如何？
3. 尽管我们的标的池里有 500 个标的，但如果必须将投资组合限制为最多 10 项资产，又该如何处理？这可不仅仅是从中挑选 10 支标的再把前面的流程走一遍那么简单。毕竟，该如何从 500 个标的的标的池里，选出最佳的 10 支呢？
4. 如果我们希望进行行业中性化呢（即每个行业均衡配置资产，而不要出现资产主要配置在某一个行业内的情况）？

## PyportfolioOpt

这里，我们就介绍一个比较重要的专门用于资产组合优化的工具库。注意，我们已经介绍了 scipy.optimize 库，除此之外，类似的库还有 cvxopt 和 cvxpy。但是，这些库都是比较底层的库，它们主要用于提供各种凸优化算法，不仅仅用于投资领域。

[Pyportfolio](https://pyportfolioopt.readthedocs.io) 则是一个专门用于资产组合优化的库。这个库最初由 Robert Martin 开发。他是一名交易员，Python 爱好者和天体物理学家。这个库在 github 上获得了接近 4k 的 stars。

![75%](https://images.jieyu.ai/images/2023/12/pyportfolioopt.png)

Pyportfolio 提供了以下功能：

1. 多种期望回报模型。比如，在我们前面的方法中，我们使用的 returns 计算方法比较简单，可以称之为**平均历史回报**。我们在之前的系列中，介绍了 CAPM，这也是一种回报率。pyportfolioopt 工具库除了提供**平均历史回报**、**指数加权平均历史回报**之外，它还支持** CAPM 回报**。CAPM 与 MPT 双剑合壁，能产生什么样的化学反应，大家也可以在自己的投资中，轻仓尝试一下。
   
2. 在风险模型方面，之前主要介绍了协方差模型。而 pyportfolioopt 还支持半协方差（Semicovariance，以更关注下行变化的风险度量，呼应我们第 2 个需求）、指数协方差（为最近的数据赋予更多的权重）以及协方差收缩技术
3. 在目标函数上，pyportfolioopt 还提供了最大夏普率 (Maximum Sharpe Ratio)、最小波动率（前面的例子已介绍）、最大二次效用（Maximum quadratic utility）等。
4. 允许持有空头头寸。
   
以上这些变种组合起来，就为我们拓展 MPT 的应用提供了很多花样玩法。也许，其中就会有一款，正好适合当下的市场。除了这些优化方法外，还有一些常用功能也是我们非常需要的：

1. 可视化。显然，我们需要绘制协方差矩阵、有效前沿、权重矩阵（这是使用饼图的恰当地方，因为权重之和刚好为 1）。
2. 数据预处理，以及后处理。

在了解了 PyportfolioOpt 的基本特性之后，我们再来看看如何使用它。

当前的版本是是 1.5.4，尽管它的基本架构已经稳定，但维护还是很活跃，并且支持从 3.8 到 3.12 的各个版本的 Python。它已经使用了 Poetry 来进行依赖管理。

安装PyportfolioOpt请使用以下命令：

```
pip install PyPortfolioOpt
```

它还提供了一个 Docker 容器（真的很贴心了！），供我们练习它提供的一些示例。

接下来，我们将通过一个示例来演示 pyPortforlioOpt的用法。在这个示例中，我们将演示：
1. 如何将数据处理成 PyPortfolioOpt 需要的输入格式
2. 计算协方差矩阵、以及可视化
3. 优化一个多空组合，使得方差最小化
4. 最大化夏普率

### 数据预处理
我们以使用 zillionare-omicron为例来获取初始数据，并将其处理为 PyPortfolioOpt 需要的输入格式。

!!! tip
    推荐在**大富翁量化交易课程**课件环境中运行本示例。

```python
from coursea import *

await init()

# 上证50指数
SH50_CODE_LIST = ["688599.XSHG","688111.XSHG","603986.XSHG","603799.XSHG","603501.XSHG","603288.XSHG",
                  "603260.XSHG","603259.XSHG","601919.XSHG","601899.XSHG","601888.XSHG","601857.XSHG",
                  "601728.XSHG","601669.XSHG","601668.XSHG","601633.XSHG","601628.XSHG","601398.XSHG",
                  "601390.XSHG","601318.XSHG","601288.XSHG","601225.XSHG","601166.XSHG","601088.XSHG",
                  "601066.XSHG","601012.XSHG","600905.XSHG","600900.XSHG","600893.XSHG","600887.XSHG",
                  "600809.XSHG","600745.XSHG","600690.XSHG","600519.XSHG","600438.XSHG","600436.XSHG",
                  "600406.XSHG","600309.XSHG","600276.XSHG","600196.XSHG","600111.XSHG","600104.XSHG",
                  "600089.XSHG","600050.XSHG","600048.XSHG","600036.XSHG","600031.XSHG","600030.XSHG",
                  "600028.XSHG","600010.XSHG"]

async def get_prices(stock_list, start_date, end_date):
    frames = [tf.int2date(f) for f in tf.get_frames(start_date, end_date, FrameType.DAY)]
    dfs = [pd.DataFrame([], index=frames)]
    for code in stock_list:
        bars = await Stock.get_bars_in_range(code, FrameType.DAY, frames[0], frames[-1])
        index = [v.item().date() for v in bars["frame"]]
        dfs.append(pd.DataFrame(bars["close"], columns=[code], index=index))
    
    return pd.concat(dfs, axis=1)

# 投资组合个数
start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 1, 1)
prices = await get_prices(SH50_CODE_LIST, start, end)
prices.tail()
```

我们将得到以下结果：

![](https://images.jieyu.ai/images/2023/12/mpt-5-price-df.png)

我们将各资产走势绘制成下图：

![](https://images.jieyu.ai/images/2023/12/mpt-equity-wave.png)

上述代码中，技巧可能在第24行，它将多个dataframe按行进行拼接，最终成为一张宽表。这种格式，在使用pyfolio进行因子分析时，也曾使用过。

### 计算协方差

在前面的示例中，我们都是手动计算的协方差。在实际工作中，一方面我们可以使用pandas的DataFrame的协方差计算函数`cov`；在 PyPortfolioOpt 中，我们也可以使用它提供的`sample_cov`方法：

```python
from pypfopt import risk_models
from pypfopt import plotting

sample_cov = risk_models.sample_cov(prices, frequency=252)
sample_cov.head()
```
结果如下：

![](https://images.jieyu.ai/images/2023/12/mpt-cov.png)

区别在于，如果我们使用PyPortfolioOpt的sample_cov方法，就不需要进行收益率计算和去无效值操作，即一步操作，完成以下功能：

```
prices = ... # pd.DataFrame, price of each instrument
returns = prices.pct_change().dropna(how='any')
cov = returns.cov()
```

我们通过以下命令，来可视化该矩阵：

```python
plotting.plot_covariance(sample_cov, plot_correlation=True)
```

![](https://images.jieyu.ai/images/2023/12/mpt-cov-visualize.png)

### 求解最优资产组合

我们以最大收益、最低波动来条件来求解最优资产组合：

```python
from pypfopt import EfficientFrontier
from pypfopt import risk_models
from pypfopt.expected_returns import mean_historical_return

mu = mean_historical_return(prices)
S = risk_models.sample_cov(prices)

ef = EfficientFrontier(mu, S, weight_bounds=(0, 1))

# 求解出最小波动率时的权重
w = ef.min_volatility()

# 输出最小波动率时的年化收益、波动率和sharpe
ef.portfolio_performance(verbose=True)
df_w = pd.DataFrame(np.array([item for item in w.items()], dtype=[("code", "O"), ("w", "f4")]))

# 显示权重
df_w.head()
```

使用方法很简单。首先，我们求得回报均值，构建risk model，这里我们使用的是均值方差模型，因此，通过sampe_cov来返回这个模型。

然后我们在第8行，实例化有效前沿对象。它接收三个参数，即均值、risk model和权重约束）。

然后在第11行，我们以最小波动率为目标进行优化求解，得到的结果即为权重向量（以orderdict类型表示）。我们可以根据这个向量来自行计算对应的组合年化收益、夏普率，但也可以使用 pypfopt 自带的方法 `portfolio_performance`，如代码第14行所示。

最后，我们将权重向量转换为dataframe进行显示，这纯粹是为了美观。

这样，我们就求得了波动最小时的期望收益、资产组合（即权重），以及它们的夏普等指标。

### 可视化与有效前沿

我们刚刚使用的方法，仅生成了单个最优投资组合。但很多时候，我们可能还希望获得整个有效前沿。

```python
fig, ax = plt.subplots()
ef = EfficientFrontier(mu, S, weight_bounds=(0, 1))
ef_max_sharpe = ef.deepcopy()
plotting.plot_efficient_frontier(ef, ax=ax, show_assets=False)

# Find the tangency portfolio
ef_max_sharpe.max_sharpe()
ret_tangent, std_tangent, _ = ef_max_sharpe.portfolio_performance()
ax.scatter(std_tangent, ret_tangent, marker="*", s=100, c="r", label="Max Sharpe")

# Generate random portfolios
n_samples = 10000
w = np.random.dirichlet(np.ones(ef.n_assets), n_samples)
rets = w.dot(ef.expected_returns)
stds = np.sqrt(np.diag(w @ ef.cov_matrix @ w.T))
sharpes = rets / stds
ax.scatter(stds, rets, marker=".", c=sharpes, cmap="viridis_r")

# Output
ax.set_title("Efficient Frontier with random portfolios")
ax.legend()
plt.tight_layout()
plt.show()
```

最终我们会得到下图：

![66%](https://images.jieyu.ai/images/2023/12/mpt_ef_with_random_port.png)

这里有几点需要说明：

1. 我们共生成个两次有效前沿实例。第一个使用的是均值方差模型（即回报与波动率）进行的求解，第二个则是对最优夏普率进行的求解。对任何一个实例，只能进行一次求解，所以，我们生成了两个实例。这里使用的是deepcopy方法，也可以完全重新初始化。

2. 第13行我们使用了狄利克雷分布。它是一个在(0,1)区间内分布，但分布总和为1的分布。我们在之前是使用下面的方法来做到这一点的：

```python
w = np.random.random(size=50)
w = w/sum(w)
```
使用狄利克雷分布的写法看上去更优雅。不过要注意，两种方法的分布特征是不一样的。

## CAPM与MPT
前面一个系列讲解了CAPM。它与MPT有着千丝万缕的联系。首先，我们在求解最佳夏普率时，使用了 tangency portfolio，即切线投资组合这一说法（注释第6行）。切线投资组合是一种位于风险回报空间中有效前沿与最高可能资本市场线 (CML) 相切点的投资组合。

![66%](https://images.jieyu.ai/images/2023/12/mpt-cml.png)

这里的切线的斜率，就是夏普率。

另外，我们在之前的计算中，使用的都是均值回报率，通过`mean_historical_return`求得。但实际上，均值回报率参考意义并不太大。因为我们不能简单地假设历史会重演。一种可能的办法，是通过CAPM求得资产的alpha，以此作为资产回报再来计算有效前沿。因此，在这一点上，CAPM与MPT理论有机地结合到了一起。

pyfolio支持了这种做法。我们在实例化 EfficientFrontier对象时，可以这样：

```python
from pypfopt.expected_returns import capm_return

# 不用历史回报，而改为capm_return

#mu = mean_historical_return(prices)
mu = capm_return(prices)
S = risk_models.sample_cov(prices)

ef = EfficientFrontier(mu, S, weight_bounds=(0, 1))
```

不过，在我们的示例中，使用capm return可能会出错，提示没有任何一种资产的CAPM收益为正。这不是pyfolioopt的错，也不是我们的错。

这个市场错了。

## 结束语

这一系列的理代投资组合理论就介绍到这里。在这个系列中，我们除了掌握了如何运用投资组合理论来实际管理资产之外，我们还学到了蒙特卡洛方法、凸优化方法，掌握了sharpe率的计算等重要的量化工具。我们还介绍了MPT与CAPM是如何连接起来的，如果您对纯理论研究比较感兴趣，可以顺着这部分继续深入研究。我们在参考文献里给出了一些阅读资料。

现代投资组合的理论，以及其它重要的金融学理论，尽管在学术上非常成功（获得了诺奖），但我们也要注意，它们过于精巧的理论架构，往往是脱离实际的，也因而不被在实践中获得伟大成功的那些投资大师所看重 -- 比如查理.芒格就特别看不上有效市场假说和现代投资组合理论。

在穷查理宝典中，他这样写到：

!!! quote

    Beta 系数、现代投资组合理论等等——这些在我看来都没什么道理。我们想要做到的是，用低廉的价格，甚至是合理的价格，来购买那些拥有可持续竞争优势的企业。<br><br>大学教授怎么可以散播这种无稽之谈（股价波动是衡量风险的尺度）呢？几十年来，我一直都在等待这种胡言乱语结束。现在这样乱讲的人少了一些，但还是有。<br><br>顺便说一声，我给那些信奉极端的有效市场理论的人取了个名字 -- 叫做“神经病”。那是一种逻辑上自洽的理论，让他们能够做出漂亮的数学题。所以我想，这种理论对那些有很高数学才华的人非常有吸引力。可是它的基本假设和现实生活并不相符。

他还提到，这位世界上最伟大的经济学家之一，竟然是伯克希尔·哈撒韦的大股东，自从巴菲特掌管伯克希尔之后不久，他就开始投钱进来。他的教科书总是 教导学生说股市是极其有效率的，没有人能够打败它。但他自己的钱却流进了伯克希尔，这让他发了大财。

尽管如此，学习这些理论，掌握它背后的数学方法、工具和思想，对增强我们的量化基本功，还是非常有必要的。在学习投资的道路上，不存在这样的一个理论和系统，只要你学习了它就能战胜市场。只要你不打算做资本市场里的骗子，一万小时定律就仍然对你适用。从基础入手，构建自己的方法论和洞察市场，才能树立自己的竞争门槛。

这也是大富翁量化交易课程在编撰时秉持的原则。我们不承诺教会你马上可以一招致胜的策略，相反地，我们把市场上流行的量化工具、金融理论一一拆解，还原成一个个高复用的知识点，然后按照量化交易的天然流程重新组织起来，从而使得学员打下坚实的量化基础。

## 参考文献

1. [Yang (ken) Wu: 使用Python和R进行投资组合优化](https://www.kenwuyang.com/en/post/portfolio-optimization-with-python) 
2. [Pyportfolio 工具库](https://pyportfolioopt.readthedocs.io)
3. [Pyportfolio cookbook](https://github.com/robertmartin8/PyPortfolioOpt/tree/master/cookbook)
4. [Investopedia: 了解资本市场线及其计算方法](https://www.investopedia.com/terms/c/cml.asp)
5. [普林斯顿大学: 均值方差分析及CAPM定价模型](https://www.princeton.edu/~markus/teaching/Eco525/05%20CAPM_a.pdf)
6. [CFA水平测试: The CAL and CML](https://analystnotes.com/cfa-study-notes-the-cal-and-cml.html)
