None
## 子进程中使用异步的示例

```{.python .input  n=1}
import time
import os
import asyncio
import pandas as pd

from multiprocessing import Process, Manager

# 创建一个全局dict, 这个全局的dict就可以在进程间通信
factors = Manager().dict() 

def worker(f, *args):
    import asyncio
    asyncio.run(f(*args))
    
async def calc_ma(code, n, wins):
    from coursea import init, Stock, FrameType
    import omicron
    from omicron.talib import moving_average
    await init()
    
    bars = await Stock.get_bars(code, n, FrameType.DAY)
    
    ma_factors = {}
    for win in wins:
        ma = moving_average(bars["close"], win)
        ma_factors[win] = ma
        
    factors[code] = ma_factors

async def main():
    global factors
    
    # 获取计算因子所需要的各种数据，IO密集型，可以用协程处理
    secs = ["000001.XSHE", "600000.XSHG"]
    # tasks = [asyncio.create_task(fetch_price(code)) for code in secs]
    # data = await asyncio.gather(*tasks)

    # 计算因子，计算密集型，可以用多进程处理
    n = 150
    wins = [5, 10, 20, 60, 120]
    procs = []
    for code in secs:
        # 子进程计算因子，将计算结果存入factors。这里使用code来进行划分
        # 每个子进程处理的代码各不相同，所以不相冲突
        proc = Process(target=worker, args=(calc_ma, code, n, wins)) 
        proc.start()
        procs.append(proc)
        
    for proc in procs:
        # 让父进程等待子进程运行完成
        proc.join()
        
    print("计算完成，因子结果为：")
    print(factors)
    
# 在notebook中我们这样运行
await main()

# 如果是独立的python脚本：

# if __name__ == "__main__":
#     asyncio.run(main())
```

```{.json .output n=1}
[
 {
  "name": "stdout",
  "output_type": "stream",
  "text": "init securities done\ninit securities done\n\u8ba1\u7b97\u5b8c\u6210\uff0c\u56e0\u5b50\u7ed3\u679c\u4e3a\uff1a\n{'000001.XSHE': {5: array([       nan,        nan,        nan,        nan, 12.889885 ,\n       12.8762   , 12.782483 , 12.716102 , 12.686815 , 12.616528 ,\n       12.563832 , 12.632167 , 12.680978 , 12.696597 , 12.731742 ,\n       12.926984 , 13.163226 , 13.42485  , 13.735286 , 14.055483 ,\n       14.186295 , 14.25463  , 14.291725 , 14.356155 , 14.410823 ,\n       14.514302 , 14.600209 , 14.682212 , 14.717356 , 14.731023 ,\n       14.734927 , 14.654879 , 14.559211 , 14.401064 , 14.176536 ,\n       14.024246 , 13.895386 , 13.803622 , 13.737241 , 13.702097 ,\n       13.653287 , 13.581047 , 13.47757  , 13.370186 , 13.434616 ,\n       13.461951 , 13.530286 , 13.618144 , 13.702098 , 13.612287 ,\n       13.549809 , 13.579096 , 13.616191 , 13.700145 , 13.731384 ,\n       13.713813 , 13.588859 , 13.385806 , 13.161278 , 13.005084 ,\n       12.844986 , 12.751269 , 12.677077 , 12.600933 , 12.507216 ,\n       12.474025 , 12.423263 , 12.438881 , 12.452548 , 12.458406 ,\n       12.446692 , 12.395929 , 12.352976 , 12.296355 , 12.310022 ,\n       12.311975 , 12.321736 , 12.310022 , 12.339309 , 12.313927 ,\n       12.280736 , 12.276832 , 12.290499 , 12.339309 , 12.429121 ,\n       12.501361 , 12.538456 , 12.50136  , 12.339309 , 12.198733 ,\n       12.056207 , 11.960538 , 11.970301 , 12.093303 , 12.216306 ,\n       12.450596 , 12.626314 , 12.682934 , 12.706363 , 12.649743 ,\n       12.554074 , 12.448644 , 12.380309 , 12.310021 , 12.255354 ,\n       12.167495 , 12.087446 , 11.99373  , 11.884395 , 11.837536 ,\n       11.759439 , 11.693057 , 11.6130085, 11.546626 , 11.513434 ,\n       11.499768 , 11.493911 , 11.560293 , 11.663772 , 11.654009 ,\n       11.63058  , 11.616913 , 11.579722 , 11.535388 , 11.541911 ,\n       11.530006 , 11.504006 , 11.480006 , 11.394007 , 11.324007 ,\n       11.294006 , 11.258006 , 11.234006 , 11.296005 , 11.320005 ,\n       11.328006 , 11.340006 , 11.334006 , 11.276006 , 11.238006 ,\n       11.218005 , 11.256006 , 11.308006 , 11.330007 , 11.338007 ,\n       11.352007 , 11.330007 , 11.306006 , 11.296007 , 11.366007 ],\n      dtype=float32), 10: array([       nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan, 12.753206 ,\n       12.720016 , 12.707325 , 12.698539 , 12.691706 , 12.674133 ,\n       12.745406 , 12.8976965, 13.052913 , 13.2159395, 13.39361  ,\n       13.556636 , 13.708925 , 13.858286 , 14.045718 , 14.233151 ,\n       14.350296 , 14.427417 , 14.486966 , 14.536753 , 14.57092  ,\n       14.624611 , 14.62754  , 14.620707 , 14.559206 , 14.453775 ,\n       14.379584 , 14.27513  , 14.181414 , 14.06915  , 13.939315 ,\n       13.838765 , 13.738215 , 13.6405945, 13.553711 , 13.568354 ,\n       13.557616 , 13.555664 , 13.547854 , 13.53614  , 13.52345  ,\n       13.5058775, 13.5546875, 13.617165 , 13.701118 , 13.671832 ,\n       13.631807 , 13.583972 , 13.500995 , 13.430709 , 13.368232 ,\n       13.279396 , 13.170061 , 13.03144  , 12.8811035, 12.756148 ,\n       12.659504 , 12.587264 , 12.557979 , 12.52674  , 12.48281  ,\n       12.460358 , 12.409595 , 12.395927 , 12.374451 , 12.384213 ,\n       12.379333 , 12.358832 , 12.331498 , 12.317832 , 12.311975 ,\n       12.296355 , 12.299284 , 12.30026  , 12.339309 , 12.371524 ,\n       12.3910475, 12.407643 , 12.395929 , 12.339309 , 12.313928 ,\n       12.278785 , 12.249498 , 12.235831 , 12.216307 , 12.2075205,\n       12.253402 , 12.2934265, 12.326617 , 12.399834 , 12.433024 ,\n       12.502336 , 12.537478 , 12.531622 , 12.508193 , 12.452548 ,\n       12.3607855, 12.268045 , 12.187019 , 12.097208 , 12.046445 ,\n       11.963467 , 11.890251 , 11.803369 , 11.715509 , 11.675485 ,\n       11.629603 , 11.593483 , 11.58665  , 11.605197 , 11.58372  ,\n       11.565173 , 11.555411 , 11.570006 , 11.599578 , 11.597959 ,\n       11.580292 , 11.560458 , 11.529863 , 11.464696 , 11.432958 ,\n       11.412005 , 11.381005 , 11.357005 , 11.345005 , 11.322005 ,\n       11.311005 , 11.299005 , 11.284004 , 11.286004 , 11.279004 ,\n       11.273005 , 11.298004 , 11.321005 , 11.303004 , 11.288005 ,\n       11.285005 , 11.293005 , 11.307005 , 11.3130045, 11.352004 ],\n      dtype=float32), 20: array([       nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan, 13.07341  ,\n       13.138329 , 13.208129 , 13.278415 , 13.368715 , 13.453646 ,\n       13.547854 , 13.6625595, 13.769943 , 13.87635  , 13.982269 ,\n       14.090629 , 14.168238 , 14.239501 , 14.302466 , 14.343467 ,\n       14.3649435, 14.351275 , 14.334192 , 14.302953 , 14.255118 ,\n       14.231689 , 14.182879 , 14.130652 , 14.05646  , 14.011067 ,\n       13.968602 , 13.9154   , 13.864636 , 13.802648 , 13.731384 ,\n       13.672324 , 13.646454 , 13.628882 , 13.627418 , 13.620096 ,\n       13.594714 , 13.569821 , 13.524428 , 13.483428 , 13.445844 ,\n       13.392641 , 13.362378 , 13.324306 , 13.291115 , 13.213994 ,\n       13.145659 , 13.085622 , 13.0294895, 12.978726 , 12.925524 ,\n       12.86988  , 12.78983  , 12.713686 , 12.62778  , 12.570183 ,\n       12.51942  , 12.47305  , 12.444739 , 12.422287 , 12.397394 ,\n       12.378358 , 12.354441 , 12.348095 , 12.356881 , 12.37787  ,\n       12.385192 , 12.383239 , 12.363715 , 12.328571 , 12.312952 ,\n       12.28757  , 12.274391 , 12.268046 , 12.277808 , 12.289522 ,\n       12.322225 , 12.350535 , 12.361274 , 12.369571 , 12.373476 ,\n       12.390559 , 12.393489 , 12.383727 , 12.36225  , 12.330035 ,\n       12.307094 , 12.280736 , 12.256819 , 12.248521 , 12.239735 ,\n       12.232902 , 12.213865 , 12.167495 , 12.111851 , 12.064017 ,\n       11.995194 , 11.930764 , 11.886835 , 11.851203 , 11.8150835,\n       11.76432  , 11.722832 , 11.686688 , 11.657545 , 11.636723 ,\n       11.604949 , 11.576972 , 11.558257 , 11.534948 , 11.508341 ,\n       11.48859  , 11.468209 , 11.463508 , 11.472293 , 11.459983 ,\n       11.44565  , 11.429733 , 11.406936 , 11.375353 , 11.355983 ,\n       11.342506 , 11.339507 , 11.339007 , 11.324007 , 11.305007 ,\n       11.298007 , 11.296007 , 11.295507 , 11.299507 , 11.315508 ],\n      dtype=float32), 60: array([       nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan, 13.591456 ,\n       13.587551 , 13.58446  , 13.577789 , 13.572094 , 13.559566 ,\n       13.554036 , 13.554524 , 13.5546875, 13.552572 , 13.54639  ,\n       13.544274 , 13.534838 , 13.527353 , 13.519218 , 13.511247 ,\n       13.493023 , 13.464713 , 13.434451 , 13.402886 , 13.366116 ,\n       13.334227 , 13.299896 , 13.26768  , 13.234816 , 13.2009735,\n       13.16648  , 13.128083 , 13.085943 , 13.036644 , 12.989949 ,\n       12.943254 , 12.903555 , 12.870201 , 12.844332 , 12.826597 ,\n       12.812117 , 12.797798 , 12.77681  , 12.758425 , 12.7389   ,\n       12.720514 , 12.703431 , 12.68537  , 12.670076 , 12.640627 ,\n       12.612642 , 12.583193 , 12.550002 , 12.5186   , 12.49273  ,\n       12.463444 , 12.426022 , 12.383068 , 12.339139 , 12.3079   ,\n       12.278939 , 12.251443 , 12.230943 , 12.214347 , 12.195312 ,\n       12.177739 , 12.156913 , 12.139496 , 12.125551 , 12.114868 ,\n       12.09907  , 12.080308 , 12.059589 , 12.037339 , 12.020334 ,\n       12.003013 , 11.98548  , 11.966341 , 11.953975 , 11.937832 ,\n       11.921014 , 11.903668 , 11.885006 , 11.865367 , 11.848172 ,\n       11.832454 , 11.818601 , 11.803133 , 11.78126  , 11.757248 ,\n       11.736675 , 11.717897 , 11.703521 , 11.694318 , 11.687854 ],\n      dtype=float32), 120: array([       nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan,        nan,\n              nan,        nan,        nan,        nan, 12.893388 ,\n       12.88265  , 12.870691 , 12.858647 , 12.848827 , 12.837222 ,\n       12.826558 , 12.817421 , 12.807143 , 12.79496  , 12.783366 ,\n       12.773647 , 12.760163 , 12.746851 , 12.736602 , 12.724544 ,\n       12.707024 , 12.6841955, 12.659733 , 12.634131 , 12.607149 ,\n       12.583345 , 12.559254 , 12.535412 , 12.508043 , 12.4791155,\n       12.451584 , 12.422996 , 12.394737 , 12.365487 , 12.338907 ],\n      dtype=float32)}, '600000.XSHG': {5: array([      nan,       nan,       nan,       nan, 6.983261 , 6.9794335,\n       6.944986 , 6.9201074, 6.9201074, 6.8894877, 6.862695 , 6.8780055,\n       6.8990564, 6.8990564, 6.91628  , 6.9411583, 6.9583817, 6.9679503,\n       6.987088 , 6.9985704, 7.008139 , 7.0062256, 6.990916 , 6.990916 ,\n       6.9890018, 6.9928293, 6.994743 , 7.006225 , 7.0100527, 7.023449 ,\n       7.0368457, 7.0464144, 7.0521555, 7.034932 , 7.0062256, 6.981347 ,\n       6.956468 , 6.9373307, 6.9315896, 6.927762 , 6.927762 , 6.922021 ,\n       6.908624 , 6.8875732, 6.8933144, 6.902883 , 6.9105377, 6.9181933,\n       6.927762 , 6.908624 , 6.8875732, 6.891401 , 6.8990564, 6.9315906,\n       6.964124 , 6.989003 , 6.998572 , 6.998572 , 6.9602966, 6.908626 ,\n       6.8531275, 6.809111 , 6.7804046, 6.7727494, 6.7861457, 6.807197 ,\n       6.8263345, 6.843558 , 6.862695 , 6.8665233, 6.876092 , 6.8722644,\n       6.8684363, 6.864609 , 6.8722644, 6.8780055, 6.881833 , 6.889488 ,\n       6.893316 , 6.891402 , 6.891402 , 6.904798 , 6.9124537, 6.946901 ,\n       7.015796 , 7.0770354, 7.159327 , 7.2205667, 7.239704 , 7.2435317,\n       7.2301354, 7.19186  , 7.1937737, 7.2301345, 7.2684097, 7.3851485,\n       7.477009 , 7.4961467, 7.494233 , 7.467441 , 7.394718 , 7.3392196,\n       7.321996 , 7.3239098, 7.3086   , 7.2798934, 7.2377915, 7.176552 ,\n       7.1057434, 7.0636415, 7.0387626, 7.0234528, 7.034935 , 7.029194 ,\n       7.034935 , 7.034935 , 7.0368485, 7.0578995, 7.113398 , 7.153587 ,\n       7.1574144, 7.1727242, 7.161242 , 7.1382766, 7.113398 , 7.0961747,\n       7.0636415, 7.0387626, 6.9832644, 6.9373345, 6.912456 , 6.8914046,\n       6.8856635, 6.9162836, 6.933507 , 6.941162 , 6.946903 , 6.9430757,\n       6.9239383, 6.9239383, 6.9239383, 6.960299 , 7.002402 , 7.040677 ,\n       7.061728 , 7.0923476, 7.0961757, 7.096348 , 7.102348 , 7.134089 ],\n      dtype=float32), 10: array([      nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan, 6.9363737, 6.921064 , 6.911495 ,\n       6.9095817, 6.9095817, 6.902884 , 6.9019275, 6.918194 , 6.9335036,\n       6.943073 , 6.9574256, 6.974649 , 6.9823046, 6.9794335, 6.989002 ,\n       6.993787 , 7.0004845, 7.0004845, 6.998571 , 7.0004845, 7.0062256,\n       7.0148377, 7.020579 , 7.029191 , 7.022493 , 7.0148377, 7.0090966,\n       7.0014415, 6.9947433, 6.983261 , 6.966995 , 6.954555 , 6.9392447,\n       6.9229784, 6.9095817, 6.9105387, 6.915323 , 6.91628  , 6.9134088,\n       6.9076676, 6.90097  , 6.895229 , 6.90097  , 6.908625 , 6.929676 ,\n       6.936374 , 6.9382873, 6.9449854, 6.948813 , 6.945943 , 6.936374 ,\n       6.921064 , 6.9038405, 6.8894877, 6.8665223, 6.847385 , 6.8301616,\n       6.817722 , 6.8119807, 6.817722 , 6.8263335, 6.841644 , 6.849298 ,\n       6.855996 , 6.8636513, 6.8693924, 6.877048 , 6.877048 , 6.878961 ,\n       6.878961 , 6.8818316, 6.884702 , 6.8933144, 6.90097  , 6.9201074,\n       6.953598 , 6.984218 , 7.032062 , 7.0665092, 7.0933013, 7.1296625,\n       7.1535845, 7.175592 , 7.2071686, 7.234918 , 7.2559695, 7.30764  ,\n       7.334432 , 7.344958 , 7.362181 , 7.3679223, 7.3899302, 7.408111 ,\n       7.4090676, 7.4090676, 7.3880167, 7.3373027, 7.2885017, 7.24927  ,\n       7.2148223, 7.186116 , 7.159324 , 7.1306176, 7.105739 , 7.067464 ,\n       7.049283 , 7.036844 , 7.030146 , 7.046413 , 7.0712914, 7.094257 ,\n       7.09617  , 7.104782 , 7.1095667, 7.125833 , 7.1334887, 7.1267905,\n       7.118179 , 7.099998 , 7.0607657, 7.0253615, 7.0043106, 6.9775186,\n       6.9622087, 6.9497695, 6.9354167, 6.9268045, 6.919149 , 6.914365 ,\n       6.920106 , 6.928718 , 6.9325457, 6.9535966, 6.972734 , 6.982303 ,\n       6.9928284, 7.008138 , 7.0282326, 7.04937  , 7.0715075, 7.0979037],\n      dtype=float32), 20: array([      nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan, 6.9469004, 6.947857 , 6.9469004, 6.9445086, 6.9492927,\n       6.948336 , 6.951207 , 6.9593406, 6.9660387, 6.97178  , 6.981827 ,\n       6.994745 , 7.001443 , 7.004314 , 7.0057487, 7.004314 , 7.004792 ,\n       7.0009646, 6.9966583, 6.991874 , 6.9866114, 6.984698 , 6.979913 ,\n       6.9760857, 6.9660387, 6.9626894, 6.962211 , 6.958862 , 6.9540772,\n       6.945465 , 6.933983 , 6.9248924, 6.9201083, 6.915802 , 6.9196296,\n       6.9234576, 6.926807 , 6.9306345, 6.931113 , 6.926807 , 6.918673 ,\n       6.9081473, 6.902406 , 6.899057 , 6.8981004, 6.891881 , 6.8842254,\n       6.881355 , 6.880398 , 6.881833 , 6.881355 , 6.881355 , 6.87657  ,\n       6.8727427, 6.865087 , 6.8583894, 6.853605 , 6.8473854, 6.845472 ,\n       6.848343 , 6.854084 , 6.8631744, 6.8713083, 6.8784842, 6.891881 ,\n       6.9114966, 6.9306345, 6.9545565, 6.9727373, 6.9861336, 7.0057497,\n       7.019146 , 7.034456 , 7.054072 , 7.0775156, 7.104787 , 7.145932 ,\n       7.1832504, 7.2057376, 7.227745 , 7.248796 , 7.2717614, 7.291856 ,\n       7.308122 , 7.321997 , 7.321997 , 7.3224754, 7.3114715, 7.2971187,\n       7.2885065, 7.2770243, 7.2746325, 7.2693696, 7.257409 , 7.238271 ,\n       7.2186556, 7.187079 , 7.1593294, 7.147847 , 7.1430626, 7.1401916,\n       7.127753 , 7.117706 , 7.1076584, 7.0966544, 7.091392 , 7.081823 ,\n       7.0741677, 7.073211 , 7.066035 , 7.0598154, 7.0502467, 7.0411563,\n       7.0358934, 7.037807 , 7.0344577, 7.026802 , 7.0186687, 7.0071864,\n       6.9904413, 6.9770446, 6.968433 , 6.965562 , 6.9674754, 6.96604  ,\n       6.9641266, 6.967476 , 6.9736953, 6.981872 , 6.9958115, 7.0133157],\n      dtype=float32), 60: array([      nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan, 6.950729 ,\n       6.946902 , 6.943074 , 6.9398847, 6.937812 , 6.9343033, 6.932549 ,\n       6.9331865, 6.9335055, 6.933027 , 6.9323893, 6.933665 , 6.9327083,\n       6.930954 , 6.930156 , 6.928721 , 6.928402 , 6.9263287, 6.924415 ,\n       6.922342 , 6.91979  , 6.9186735, 6.9178762, 6.9178762, 6.9186735,\n       6.922023 , 6.925691 , 6.9315915, 6.935738 , 6.937811 , 6.940363 ,\n       6.941798 , 6.943712 , 6.94754  , 6.954078 , 6.9622116, 6.975448 ,\n       6.98709  , 6.994107 , 7.0009646, 7.007184 , 7.014361 , 7.021857 ,\n       7.0285544, 7.037326 , 7.041791 , 7.0457783, 7.0491276, 7.050084 ,\n       7.0521574, 7.0547094, 7.0583773, 7.0601315, 7.0614076, 7.060291 ,\n       7.06061  , 7.0622044, 7.0633206, 7.0663505, 7.0730486, 7.0810227,\n       7.087561 , 7.0936213, 7.0980864, 7.103509 , 7.108293 , 7.1116424,\n       7.1133966, 7.114353 , 7.113556 , 7.1141934, 7.114672 , 7.114991 ,\n       7.1157885, 7.1178617, 7.119297 , 7.119935 , 7.120414 , 7.120254 ,\n       7.120414 , 7.122009 , 7.1226463, 7.1250386, 7.1277494, 7.128228 ,\n       7.125836 , 7.123922 , 7.119776 , 7.117398 , 7.1167817, 7.1167154],\n      dtype=float32), 120: array([      nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan,       nan,\n             nan,       nan,       nan,       nan,       nan, 7.0158777,\n       7.017234 , 7.01835  , 7.018988 , 7.0206623, 7.0213003, 7.0220976,\n       7.023294 , 7.023932 , 7.023294 , 7.023294 , 7.0241714, 7.0238523,\n       7.0233736, 7.0240116, 7.0240116, 7.0241714, 7.0233736, 7.0223374,\n       7.0213804, 7.0209017, 7.020663 , 7.0214605, 7.0228157, 7.0234537,\n       7.0239325, 7.02481  , 7.025687 , 7.0265718, 7.0273   , 7.0285425],\n      dtype=float32)}}\n"
 }
]
```
