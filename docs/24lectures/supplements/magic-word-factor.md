
在A股，一直有炒名字的现象。一方面，很多个股的名字，反映了它的行业，比如证券、传媒、文化等等。因此，一旦行业基本面好转，相同名字的个股就可能都有行情。另一方面则是魔幻的炒作，比如2018年的东方，2023年底的龙字等等。这也是一种题材炒作。

题材股的炒作确实是利润获取最快的一种方式。关键是我们能先人一步发现当下正在炒作的题材。

这篇文章给出了一个思路，就是通过涨停板，看看上榜的标的中，是否有重复汉字。如果有并且达到一定的阈值，则有可能出现了炒”东方“，炒”龙“字这样的行情。本文原文发表在[大富翁量化](https://www.jieyu.ai)上。这里是可运行的源码。

``` python
import akshare as ak
import datetime
from typing import List, Set
from coursea import *
await init()

import jieba
import re

async def get_zt(dt):
    df = await Stock.get_buy_limit_secs(dt)
    data = []
    for code in df.code:
        name = await Security.alias(code)
        data.append((name, code))

    return pd.DataFrame(data, columns=["alias", "code"])

async def find_hot_concept(df: pd.DataFrame, threshold_1 = 0.2, threshold_2 = 4):
    text = " ".join(filter(lambda x: x, df["alias"]))

    # 排除掉没有信息量的词
    cleaned = re.sub(r"股份|科技|控股", "", text)

    one = {}
    two = {}
    for word in cleaned:
        if word == " ":
            continue
        if word in one:
            one[word] += 1
        else:
            one[word] = 1

    for word in jieba.cut(cleaned):
        if word == " " and len(word) != 2:
            continue
        if word in two:
            two[word] += 1
        else:
            two[word] = 1

    two = [(k,v) for k,v in two.items()]
    two = sorted(two, key = lambda x: x[1], reverse=True)

    # 优先找两个字的题材，但排除ST
    for item in two:
        if item[0] == "ST":
            continue

        print(item)
        if item[1] >= threshold_2:
            return item[0]
        else:
            break

    one = [(k,v) for k,v in one.items()]
    one = sorted(one, key = lambda x: x[1], reverse=True)

    for item in one:
        if item[0] in ('*', "S", "T", "A"):
            continue

        print(item)
        if item[1] >= len(df) * threshold_1:
            return item[0]
        else:
            return None

async def build_board(concept: str, buy_limit_secs: Set[str])->List[str]:
    """构建炒作题材中，非涨停个股名单
    
    """
    secs = []
    for row in Security._securities:
        if concept in row["alias"] and row["code"] not in buy_limit_secs:
            secs.append(row["code"])

    return secs
    
async def get_forward_returns(dt: datetime.date, n=10):
    """查找不在涨停清单里，但名字包括key的标的，获取5日内行情"""
    df = await get_zt(dt)
    concept = await find_hot_concept(df)
    if concept is None:
        return

    secs = await build_board(concept, set(df["alias"]))
    
    end = tf.day_shift(dt, n)
    barss = {}
    for sec in secs:
        bars = await Stock.get_bars(sec, n+1, FrameType.DAY, end=end)
        if len(bars) != n + 1:
            continue
        barss[sec] = bars["close"]

    df = pd.DataFrame.from_dict(barss)
    returns = []
    for period in (1, 5, 10):
        returns.append(df.pct_change(period).mean())

    df = pd.concat(returns, axis=1).rename(columns={0:"1d", 1:"5d", 2:"10d"})
    mn = df.mean()
    print(f"{dt} {concept} 1D: {mn.iloc[0]:.2%} 5D: {mn.iloc[1]:.2%} 10D: {mn.iloc[2]:.2%}")
    return df

frames = tf.get_frames(datetime.date(2019, 2, 10), datetime.date(2019, 3, 5), FrameType.DAY)
returns = {}
for frame in frames:
    try:
        df = await get_forward_returns(tf.int2date(frame))
        if df is not None:
            returns[frame] = df
    except Exception as e:
        pass
```


## 改造成策略

这个因子没有考虑到题材的持续性。比如，有可能龙字在21号有两支上板，22号4支，23号5支，单日都没能超出阈值，这就会被我们的算法忽略。

在策略中，我们可能需要把这一指标做成移动求和，也许能更好地反映题材是如何发酵并逐渐被更多人关注的，并且有可能在该题材最终大热之前被我们的算法发现。

另外，能上板的标的，往往是中证1000或者中证1000之后的个股。因此，我们还要关注中证1000的走势。如果中证1000走势太差，我们要么不进场，即使进场了，也应该设置一个比较近的止损线。

## 进一步思考

如果我们拓宽思路，还可以从涨停板的角度，不去寻找这种魔性汉字炒作，而是对板块涨停家数、比例进行建模和跟踪，找到最热的板块进行操作。

