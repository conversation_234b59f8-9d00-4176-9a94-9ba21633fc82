```python
# 参考文档：聚宽《多因子系列报告之一：因子测试框架 （光大）》
import os

import jqdatasdk as jq
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import statsmodels.api as sm

account = os.environ.get("jq_account")
passwd = os.environ.get("jq_password")
jq.auth(account, passwd)

import warnings

warnings.filterwarnings("ignore")
```

## 生成因子


```python
import arrow
from coursea import *

await init()

start = arrow.get("2015-01-01").date()
end = arrow.get("2017-12-31").date()

stocks = jq.get_index_stocks("000300.XSHG")


factor_10d_return = []

async for code, bars in Stock.batch_get_day_level_bars_in_range(
    stocks, FrameType.DAY, start, end
):
    df = pd.DataFrame()
    df["date"] = bars["frame"][10:]
    df["instrument"] = [code] * (len(bars) - 10)
    df["factor_ret_10"] = bars["close"][10:] / bars["close"][:-10] - 1

    factor_10d_return.append(df)

factor_10d_return = pd.concat(factor_10d_return)
factor_10d_return = factor_10d_return.pivot(
    index="date", columns="instrument", values="factor_ret_10"
)

days = factor_10d_return.index
# 我们得到一个243列（instruments)，722行（日期）的宽表
factor_10d_return.fillna(0, inplace=True)
factor_10d_return
```

## 异常值处理


```python
def mad_clip(arr, k: int = 3, axis=1):

    med = np.median(arr, axis=axis).reshape(arr.shape[0], -1)
    mad = np.median(np.abs(arr - med), axis=axis)

    return np.clip(arr.T, med.flatten() - k * mad, med.flatten() + k * mad).T


no_outlier = mad_clip(factor_10d_return)
no_outlier
```

## 处理缺失值


```python
n = len(no_outlier.columns)

# 如果某列缺20%以上，则抛弃
p = np.sum(no_outlier.isna(), axis=0) / n

# 更新 instruments
instruments = no_outlier.columns[p < 0.2]
tmp_ = no_outlier[instruments]

median = tmp_.median(axis=1, skipna=True)

# 我们本应该使用tmp_.fillna(median)
# 但这里替换失败，原因不明，故用下面的方法取代

no_missed = tmp_.apply(lambda x: x.fillna(median[median.index == x.index].iloc[0]))

no_missed
```

## 标准化


```python
from scipy.stats import zscore

zscored = zscore(no_missed)
zscored
```

## 中性化

### 行业哑变量

获取行业属性，转化为哑变量


```python
# from jqdata import *
sw = jq.get_industries(name="sw_l1").index
sw
# industry=pd.DataFrame(0,columns=output.columns,index=range(0,28))
# for i in range(len(sw)):
#     temp=list(set(output.columns).intersection(set(get_industry_stocks(sw[i]))))
#     industry.loc[i,temp]=1

industries = []
for date in days:
    for code, value in jq.get_industry(stocks).items():
        industries.append((date, code, value.get("sw_l1").get("industry_name")))

industries = pd.DataFrame(industries, columns=["date", "instrument", "industry"])
display(industries[:3])
dummies = pd.get_dummies(industries, columns=["industry"])

dummies.set_index(["instrument", "date"], inplace=True)
dummies = dummies.dropna(how="any", axis=1)
dummies[:3]
```

### 市值变量

获取市值变量并调整其分布


```python
from jqdatasdk import get_fundamentals, query, valuation

caps = []

q = query(valuation.circulating_cap, valuation.code).filter(
    valuation.code.in_(instruments)
)

for dt in days:
    m = get_fundamentals(q, date=dt)

    m.set_index("code", inplace=True)

    # 对数法进行分布无偏调整
    m.circulating_cap = np.log(m.circulating_cap)
    m["date"] = [dt] * len(m)

    caps.append(m)
```

### 行业中性化 + 市值中性化


```python
from sklearn.linear_model import LinearRegression

df = pd.concat(caps)
df.index.rename("instrument", inplace=True)
df.set_index([df.index, "date"], inplace=True)

X = pd.concat([df, dummies], axis=1)
X = X.dropna(how="any", axis=1)

model = LinearRegression(fit_intercept=False)

residues = []

instruments = list(factor_10d_return.columns)
for date, group in X.groupby(by="date", group_keys=True):
    Xi = group.droplevel(1)
    Xi = Xi[Xi.index.isin(instruments)]
    y = factor_10d_return[factor_10d_return.index == date].T

    res = model.fit(Xi, y)
    coef = res.coef_

    # index is instrument, colums is '2015-01-19', ...
    residue = y - np.dot(Xi, coef.T)
    residues.append(residue)

neutralized = pd.concat(residues, axis=1)
X = neutralized.T
```

## 回归分析和IC法
### 获取T+1期收益值，并与X对齐


```python
y = []

# 取1日涨跌为y值
ystart = X.index[0]
yend = tf.day_shift(X.index[-1], 1)

async for code, bars in Stock.batch_get_day_level_bars_in_range(
    stocks, FrameType.DAY, ystart, yend
):
    df = pd.DataFrame()
    df["date"] = bars["frame"][1:]
    df["instrument"] = [code] * (len(bars) - 1)
    df["y"] = bars["close"][1:] / bars["close"][:-1] - 1

    y.append(df)

y = pd.concat(y)
y = y.pivot(index="date", columns="instrument", values="y")

y.fillna(0, inplace=True)

# 对齐到X的日期， y[2015-01-19]实际上是X[2015-01-19]对应的T+1期收益
y.index = X.index

# remove instruments that not in X
common_cols = set(y.columns).intersection(X.columns)
y = y[common_cols]
```


```python
f = [0] * len(y)
t = [0] * len(y)

for i in range(len(y)):
    yi = y.iloc[i, :].sort_index()
    xi = X.iloc[i, :].sort_index()

    model = sm.RLM(xi, yi, M=sm.robust.norms.HuberT())
    res = model.fit()

    f[i] = res.params.item()
    t[i] = res.tvalues.item()

    if i == 0:
        # 对回归的结果画图
        y_fit = res.fittedvalues
        fig, ax = plt.subplots(figsize=(8, 6))

        ax.plot(np.arange(len(y.iloc[0])), y.iloc[0], "o", label="data")
        ax.plot(np.arange(len(y_fit)), y_fit, "r--.", label="OLS")
```

### 检验结果


```python
columns = []
values = []

# 因子收益序列>0的概率
columns.append("因子正收益率")
values.append(sum(pd.Series(f) > 0) / len(f))

# t值绝对值的均值---回归假设检验的t值
columns.append("T值的绝对值均值")
values.append(np.mean(np.abs(t)))

# t值绝对值大于等于2的概率---回归假设检验的t值
columns.append("T>2")
values.append(np.sum(np.abs(t) > 2) / len(t))

# 计算IC值序列
IC = [0] * len(y.columns)
for i in range(len(y.columns)):
    IC[i] = np.corrcoef(pd.Series(f).rank(), y.iloc[:, i].rank())[0, 1]

# 计算IC值的均值
columns.append("mean_IC")
values.append(np.mean(IC))

# 计算IC值的标准差
columns.append("std_IC")
values.append(np.std(IC))

# IC大于0的比例
columns.append("IC > 0")
values.append(np.sum(pd.Series(IC) > 0) / len(IC))

# IC绝对值大于0.02的比例
columns.append("IC绝对值>=0.02")
values.append(np.sum(pd.Series(IC) > 0.02) / len(IC))

# IR值
columns.append("IR")
values.append(np.mean(IC) / np.std(IC))
    
result_df = pd.DataFrame({"指标名称": columns, "因子检验值": values})
result_df
```


```python
# 因子收益时间序列图
plt.bar(range(len(f)), f)
```


```python
# 因子收益分布直方图
plt.hist(f)
```


```python
# 回归因子收益t值绝对值
plt.bar(range(len(t)), abs(pd.Series(t)))
```


```python
# 因子IC值序列
plt.bar(range(len(IC)), pd.Series(IC))
```

## 分层回溯法


```python
# 获取个股累积收益率
y = []

# 取1日涨跌为y值
ystart = X.index[0]
yend = tf.day_shift(X.index[-1], 1)

async for code, bars in Stock.batch_get_day_level_bars_in_range(
    stocks, FrameType.DAY, ystart, yend
):
    if len(bars) < 1:
        continue
        
    df = pd.DataFrame()
    df["date"] = bars["frame"]
    df["instrument"] = [code] * len(bars)
    
    # 注意这里与节 0.6.1的区别，此时求的是累积收益
    df["y"] = bars["close"] / bars["close"][0] - 1

    y.append(df)

y = pd.concat(y)
y = y.pivot(index="date", columns="instrument", values="y")

# 如何填充缺失值？对首期为np.nan的，填充1；否则前向填充，意味着不涨不跌
y.iloc[0].fillna(1, inplace=True)
y.fillna(None, method='ffill', inplace=True)
y = y.iloc[1:,:]
```


```python
y.index[0], X.index[0], y.index[-1], X.index[-1]
```


```python
# 分层计算每一期的涨跌
fc1 = [0] * len(X.index)
fc2 = [0] * len(X.index)
fc3 = [0] * len(X.index)
fc4 = [0] * len(X.index)
fc5 = [0] * len(X.index)
for i in range(len(X.index)):
    g1, g2, g3, g4, g5 = X.iloc[i,:].groupby(pd.qcut(X.iloc[0,:], 5, labels=False)).apply(lambda x: x.index)
    
    # y[i]的日期刚好是X[i]的后一天
    fc1[i] = np.mean(y.iloc[i, :][g1])
    fc2[i] = np.mean(y.iloc[i, :][g2])
    fc3[i] = np.mean(y.iloc[i, :][g3])
    fc4[i] = np.mean(y.iloc[i, :][g4])
    fc5[i] = np.mean(y.iloc[i, :][g5])

fcsum = pd.DataFrame(fc1)
fcsum[1] = fc2
fcsum[2] = fc3
fcsum[3] = fc4
fcsum[4] = fc5

fcsum.columns = ["1", "2", "3", "4", "5"]
```


```python
# 画图
fcsum.plot(title = "10日收益因子分层回测")
```
