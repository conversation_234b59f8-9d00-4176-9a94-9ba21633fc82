```python
from coursea import *
from zigzag import peak_valley_pivots
from omicron.talib import moving_average
from alphalens.utils import get_clean_factor_and_forward_returns


await init()
secs = await Security.select().types(["stock"]).eval()

from zigzag import peak_valley_pivots

def scaled_sigmoid(x, start, end):
    """当`x`落在`[start,end]`区间时，函数值为[0,1]且在该区间有较好的响应灵敏度
    """
    n = np.abs(start - end)

    score = 2/(1 + np.exp(-np.log(40_000)*(x - start - n)/n + np.log(5e-3)))
    return score/2
    
def score_width(verts):
    width = verts[-1] - verts[0]
    return scaled_sigmoid(width, 5, 25)

def score_height(ts, vert_price):
    std = np.std(ts)
    
    high = max(vert_price)
    low = min(vert_price)
    x = (high - low) / std

    return scaled_sigmoid(x, 1, 3)

def score_skew(verts):
    v0, v1, v2 = verts

    # range from 0 - 1
    ss = (v2 - v1) / (v2 - v0)

    # print("ss is:", ss)
    if ss < 0.33:
        return scaled_sigmoid(ss, 0, 0.33)
    else:
        return 1 - scaled_sigmoid(ss, -0.1, 1)

def score_bounce(vert_price):
    high, low = np.max(vert_price), np.min(vert_price)
    now = vert_price[-1]
    
    ss = (now - low)/(high - low)

    if ss < 0.618:
        return scaled_sigmoid(ss, 0, 0.618)
    else:
        return 1 - scaled_sigmoid(ss, 0.5, 1)

def round_bottom(ts):
    change_rate = ts[1:] / ts[:-1] - 1
    std = np.std(change_rate)
    
    pvs = peak_valley_pivots(ts.astype(np.float64), 2 * std, -2 * std)
    
    if pvs[-1] != 1:
        return None
    
    verts = np.argwhere(pvs != 0).flatten()[-3:]
    
    if len(verts) < 3:
        return None
    
    vert_price = ts[verts]

    width = verts[-1] - verts[0]
    skew = (verts[-1]-verts[-2])/width

    sw = score_width(verts)
    sh = score_height(ts, vert_price)
    ss = score_skew(verts)
    sb = score_bounce(vert_price)

    # 如何利用上述四项，给出一个综合评分？我们通过对权重的卷积来实现
    # 权重需要自行调整
    w = np.array([1, 1, 1, 1])
    return np.array([sw, sh, ss, sb]).dot(w) / len(w)
```

```python
async def calc_rb_factor(codes, start, end):
    rb_factor = []
    prices = []
    start_ = tf.day_shift(start, -50)
    end_ = tf.day_shift(end, 100)
    
    for code in codes:
        bars = await Stock.get_bars_in_range(code, FrameType.DAY, start_, end_)
        ma = moving_average(bars["close"][:-100], 5)
        
        df = pd.DataFrame(bars[["frame", "close"]], columns=["frame","close"])
        df["code"] = [code] * len(bars)
        prices.append(df.iloc[50:-100])
        
        for i in range(50, len(ma)):
            score = round_bottom(ma[i-50:i]) or 0
            rb_factor.append((bars[i]['frame'].item().date(), code, score))
        
    factor = pd.DataFrame(rb_factor, columns = ["date","asset","score"])
    factor["date"] = pd.to_datetime(factor["date"], utc=True)
    factor = factor.set_index(['date','asset'])
    
    prices = pd.concat(prices).pivot(index='frame', columns='code', values='close')
    prices.index = pd.to_datetime(prices.index, utc=True)
    prices = prices.rename_axis('date')
    
    return factor, prices

start = datetime.date(2022, 1, 1)
end = datetime.date(2023, 1, 1)
factor, prices = await calc_rb_factor(secs[:50], start, end)      
```

```python
factor
```

```python
prices
```

```python
factor_data = get_clean_factor_and_forward_returns(factor, prices, bins=5, quantiles=None)
from alphalens.tears import create_full_tear_sheet
create_full_tear_sheet(factor_data, False)
```
