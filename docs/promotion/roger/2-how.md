<style scoped>
.board {
    position: absolute;
    width: 200px;
    height: 300px;
    top: 15vh;
    box-shadow: 2px 5px 5px rgba(0,0,0,0.3);
}

.cap {
    color: black;
    text-align: center;
    width: 100%;
    margin-top: 40px;
    /* line-height: 80px; */
}
</style>

# 提升内容生产力度

<div class="board" style="left: -30px">

![](./factors.png)
<div class="cap">因子与策略</div>
</div>

<div class="board" style="left: 190px;">

![alt text](./figure.png)
<div class="cap">人物与职场</div>
</div>

<div class="board" style="left: 405px;">

![alt text](./tools.png)
<div class="cap">量化工具库</div>
</div>

<div class="board" style="left: 620px;">

![alt text](./freshman.png)
<div class="cap">量化萌新</div>
</div>

<div class="board" style="left: 835px;">

![alt text](./news.png)
<div class="cap">一周量化要闻</div>
</div>


<!--

更新越多，平台给的流量越多。

根据先前的数据推算，在连续发文的情况下，在进入到5万左右后，能做到半月~20天涨粉1000，也就是2%。这样复利增长12个月，会增长3万粉丝。这是在只靠日更一个增长点的情况下。

具体内容方面，
因子与策略，讲原理、讲实施、讲结果，结果定期找效果好的更新，这样50个因子就可以提供至少120天期内容。因子与策略方面的内容，深度的不多，从过往数据来看，推出之后，比较受欢迎。

人物与职场：很少有人能每天坚持学习干货。娱乐性的内容总是更受欢迎。加入劝学、激励人的内容，会让我们的媒体号更有腔调，增加用户好感。人是万物的尺度，做教育，要记得教育是两个字，一是教，二是育。

量化工具库：这部分我们课程内容有，主要是numpy与pandas、scipy、statsmodels等、还包括数据库、可视化、机器学习，内容也比较多。

量化萌新：入门级的内容总是看的人最多，我们也需要新生的力量。这部分人最容易参与互动。他们无法辨别内容质量，但会为免费、高大上的人设感动。

一周量化要闻：利用人的恐惧心理。一些人总觉得不了解这个圈子的信息，就没有在做量化，就与量化脱节了。同时，作为一个周刊，会培养用户的阅读习惯和账号粘性。分享一些此类信息，也会让人觉得我们是圈内人。

-->

---

<svg width="0" height="0">
  <defs>
    <clipPath id="myCurve" clipPathUnits="objectBoundingBox">
      <path d="M 0,0
              Q 0.3 0, 0.5 0.5
              T 1 0.8
              T 1,0
              Z
              " />
    </clipPath>
  </defs>
</svg>

<style scoped>
    .img-right {
        position: absolute;
        width: 50%;
        height: 100%;
        top: -10vh;
        right:0;
        /* clip-path: polygon(0 0, 100% 0%, 100% 100%); */
        clip-path: url(#myCurve);
        background: linear-gradient(to bottom, #f3b167, #ec38bc, #7303c0, #03001e);
        filter: drop-shadow(0, 7px 4px rgba(0,0,0,0.5));
    }
</style>

# 强化运营

## :mortar_board: 提升资质和品牌
<v-clicks>

## :books: 免费资料引流
## :red_envelope: 适时加推流
## :spiral_calendar: 账号活跃度管理
## :dancing_women: 捕捉流量密码
### 视频号 | B站
### 每周量化资讯能够蹭上热点
### 小鹅通提升活跃
</v-clicks>

<div class="img-right"
    v-motion
    :click-1="{opacity: 0}"
    :enter="{opacity: 1}"
    >

![alt text](./champion.jpg)
</div>

<div class="img-right"
    style="width: 60%"
    v-motion
    :click-1="{opacity: 1}"
    :click-2="{opacity: 0}"
    :enter="{opacity: 0}"
    >

![alt text](./free-books.jpg)

</div>

<div class="img-right"
    style="width: 60%"
    v-motion
    :click-2="{opacity: 1}"
    :click-3="{opacity: 0}"
    :enter="{opacity: 0}"
    >

![alt text](./ad.jpg)

</div>


<div class="img-right"
    style="width: 50%"
    v-motion
    :click-3="{opacity: 1}"
    :click-4="{opacity: 0}"
    :enter="{opacity: 0}"
    >

![alt text](./followers.jpg)

</div>

<div class="img-right"
    style="width: 50%"
    v-motion
    :click-4="{opacity: 1}"
    :enter="{opacity: 0}"
    >

![alt text](./sexy.png)

</div>

<!-- 

1. 资质问题影响到推广和转化
2. 通过免费资料，要求关注、点赞、收藏。不超过1周做一次。
   免费资料引流：
   1. 小红书、公众号等平台发文，通过免费资料引流
   2. 免费PDF，通水印、页眉页脚引流
   3. slidev是新锐presentation技术，目前可用模板较少，分享模板可以得到不错的关注
   4. 利用github网站分享资料，吸引star，完成引流（适当购买）
3. 资质提升、转化率正常情况下，可适时增加推流
4. 由于缺乏人手，没有做评论回复、互访、群活跃等提高账号权得的活动
5. 今年视频号可能还是流量洼地；B站是学习型的，这个也要上。通过每周量化资讯，适当蹭一些热点和流量。开通小鹅通，它有一些不错的运营活动。


提升更新频率+强化运营带来乘数效应，再加上后期适当推流，从对标数据来看，1年涨5万粉完全能实现

-->

---
layout: section
---

# 如何卖出300万的课
## 并且保证毛利率70%以上

---

# 一堂好课： 迎合需求是关键

## :game_die: 独立交易者，半量化
<v-clicks>

## :student: 求职者
## :supervillain: 主观转量化私募
</v-clicks>

<!--

推出第一门课程时，主要是按我们自己开发量化框架、进入投资领域的路径来安排这门课的。现在来看，从市场的角度来看，我们应该从用户的需求出发，重新组织这门课。

不同的人群，需求不一样。

独立交易者、半量化方式。这批人可能是最大的利基。假设30岁以上程序员，50%都是利基，30岁以下程序员，10%是利基，这样大约有160万候选用户。对这部分人，不能向他们出售完整的量化课程，他们需要的是解决自己问题的一个方案。比如，有的人长线看好某支个股，但他可能希望等到大盘下跌企稳、或者个股跌破均线、出现某种形态才交易。我们可以通过文章来引流，直接出售此类代码。售价大约在399~799元。后者包安装（电商独立建站费用起步价一般是500）。类似tradeing view,但以适合中国国情的方式来实现。这些文章不仅实现了引流，也为我们的媒体号提供了源源不断、吸引人的内容。这部分内容在有了框架之后，实习生即可生产。

专业人士： 他们最大的需求是求职、换岗、快速适应工作需求，不追求交易胜率。在有了技术联盟之后，以技术联盟理事单位名义，发一个证书，这将是国内首个量化专业证书。后续效果主要看我们课程内容。只要课程内容足够好，覆盖量化场景，没有理由站不住脚。这个证书适合在学校推广。

后续可以在学校做一些比赛。我们承担一些费用。

课程需要考虑所学内容，如何尽量无缝地转换到工作中的问题。课程的数据接口可能需要增加一套本地文件接口。

主观转量化：他们学习的目标主要是了解量化系统，有采购的需要。每年预期能售出12套，就可以启动市场。即使开始不能赚钱，也能够融资。

内容与价格是是相互影响的问题。内容完全满足客户需求，他能接受的价格范围就大一些。当前版的课程，内容上策略部分谈得太少、绘图部分讲得太多、最后缺少一个整体运行起来的示例。一部分客户会觉得有些内容不必要，他不愿意为这些内容付费。这个问题，可以通过灵活编排解决，把该增加的内容补齐之后，同样的内容，拆出几个组合出来，每个组合中核心内容为付费，其它内容作为赠课。

基于不同人设、可组合的课程拆分之后，我们争取从399到6999元，都能有所覆盖。最后，推一个3万元的1：1指导课，我来做助教，Roger冠名，推荐就业。

只有满足了用户的需求，才能做出一堂好课。做一堂好课虽然慢，却是惟一正确的方式。
-->

---

<svg width="0" height="0">
  <defs>
    <clipPath id="myCurve" clipPathUnits="objectBoundingBox">
      <path d="M 0,0
              Q 0.3 0, 0.5 0.5
              T 1 0.8
              T 1,0
              Z
              " />
    </clipPath>
  </defs>
</svg>

<style scoped>
    .img-right {
        position: absolute;
        width: 50%;
        height: 100%;
        top: -10vh;
        right:0;
        /* clip-path: polygon(0 0, 100% 0%, 100% 100%); */
        clip-path: url(#myCurve);
        background: linear-gradient(to bottom, #f3b167, #ec38bc, #7303c0, #03001e);
        filter: drop-shadow(-2px, 7px 4px rgba(0,0,0,0.5));
    }
</style>

# 一堂好课：教好很重要

## :bullettrain_side: 专属环境、海量数据、回测引擎

<v-clicks>

## :artificial_satellite: Slidev
## :abacus: Nbgrader
## :hammer_and_wrench: Zillionare
</v-clicks>

<div class="img-right"
    style="width: 50%"
    v-motion
    :click-1="{opacity: 0, scale:0}"
    :enter="{opacity: 1, scale:1}"
    >

![alt text](./env.jpg)
</div>

<div class="img-right"
    style="width: 50%"
    v-motion
    :click-1="{opacity: 1}"
    :click-2="{opacity: 0}"
    :enter="{opacity: 0}"
    >

![alt text](./slidev.png)
</div>

<div class="img-right"
    style="width: 50%"
    v-motion
    :click-2="{opacity: 1}"
    :click-3="{opacity: 0}"
    :enter="{opacity: 0}"
    >

![alt text](./nbgrader.png)

</div>


<div class="img-right"
    style="width: 40%;"
    v-motion
    :click-3="{opacity: 1}"
    :click-4="{opacity: 0}"
    :enter="{opacity: 0}"
    >

![alt text](./zillionare.png)

</div>

<!-- 
课程内容再好、老师再强，如果无法高效地传授给学生，也是惘然。

我们使用了jupyerlab构建的环境，在面向专业人士的课程中提供，全网量化中独一份。

slidev是一个非常棒的presentation技术，可以制作动画、交互式构图和嵌入jupyterlab，这样讲代码更方便，不会在跳转中丢失线索。

nbgrader是uc berkley在用的教学工具，用来分发、收集和批改学习作业。

Zillionare更新到可以使用免费数据。这样可以拓宽低端和高端课程人群，并且可以结合付费文章
 -->



---

<style scoped>
    .img-right {
        position:absolute;
        top:0;
        width:100%;
        height:110%;
        left:50%;
        background-image: url('ad.jpg');
        background-size: cover;
        border-bottom-left-radius:80%;
        filter: drop-shadow(-2px 5px 5px rgba(0, 0, 0, 0.7));
    }
</style>

# 促销

## :level_slider: 扩大价格覆盖

<v-clicks>

## :moneybag: 积分兑课程


## :coin: 1元试听

## 🪿小鹅通活动
</v-clicks>

<div class="img-right"/>

<!--
课程按需求拆分、灵活组合之后，再加上等级服务套餐，就能变化出各种档次出来。这里的关键是要基本不增加成本：不增加课程制作成本、不增加售前沟通成本、不增加服务成本（除非服务本身是产品的一部分）。

只卖给用户他想要的东西。同时，注重性价比的人，我们不卖服务；注重个人时间效率，希望尽快学会的人，我们推给他更好的服务。

我们争取从399到6999元，都能有所覆盖。最后，推一个3万元的1：1指导课，我来做助教，Roger冠名，推荐就业。

[click]

小鹅通可以跟一些平台的直播打通。用户在直播期间的互动可以得积分，我们要让积分兑课程，考虑官方回收积分，让积分有价值。

[click]
让用户1元听完3节课，在这3节课中，有互动答疑课，在这节课里实现转化。课程不能按顺序讲，要讲整个课程的精华，但留下钩子。也就是多抛出问题，但对答案只讲部分。

小鹅通有很多玩法，还没条件一一探索-->

---

<style scoped>
    .img-right {
        position:absolute;
        top:0;
        width:100%;
        height:110%;
        left:50%;
        background-image: url('chess.jpg');
        background-size: cover;
        border-bottom-left-radius:80%;
        filter: drop-shadow(-2px 5px 5px rgba(0, 0, 0, 0.7));
    }
</style>

# 数据推演

## 4999 or 6999？
## 37人 * 0.5
## 推广情况

<div class="img-right"/>


<!-- 

目前主推的价格是4999元，曾经按6999销售过。

本以为5000是个坎，但发现其实差别并不大。关键是4999已经和同行拉开了较大差距。 

在连续发文（坚持15天以上）的情况下，能做到1月售出7人次。目前一月发文约5篇左右。目前转化率约为万分之二，即一万次阅读，可以转化2人购买。
现在的销售为兼职，不懂量化，也不懂营销。如果她能以量化学习者的身份在群里活跃，去年初期的经验表明转化效率会比较高。那时候我们只有一部大纲。
-->

---
layout: section
---
# 如何给基金引流
## 10 * 200万

---

<style scoped>
    .img-banquet {
        position:absolute;
        top:0;
        width:100%;
        height:110%;
        left:50%;
        background-image: url('banquet.jpg');
        background-size: cover;
        border-bottom-right-radius:80%;
        filter: drop-shadow(-2px 5px 5px rgba(0, 0, 0, 0.5));
    }
    .img-alfanso {
        position:absolute;
        top:0;
        width:100%;
        height:110%;
        left:50%;
        background-image: url('https://images.jieyu.ai/images/2024/06/Alfonso-fireworks.jpg');
        background-size: cover;
        border-bottom-left-radius:80%;
        border-bottom-right-radius:80%;
        filter: drop-shadow(-2px 5px 5px rgba(0, 0, 0, 0.5));
    }
</style>

# 线下课


## 转化路径
普通群 > 邀请群 > VIP > 线下课 >晚宴

<v-clicks>

## 通过自媒体募资

</v-clicks>

<div v-motion 
    class="img-banquet"
    :click-1="{opacity: 0, scale:0}"
    :enter="{opacity: 1, scale:1}"/>

<div v-motion class="img-alfanso"
    :click-1="{opacity: 1}"
    :enter="{opacity: 0}"/>


<!--
普通群通过公众号、1元试听等方式引流

普通群福利：课程积分免费领（小鹅通）、课程积分可转让（官方回收）、高赞付费文章（50元以下）免费读、因子选股结果播报、一周文章汇总、一周资讯汇总

邀请群从普通群里筛选，主要是课程铁粉，付费文章，群里会分析投资攻略（发会议链接、不在群里讨论），要付费筛选

投资规模达到一定金额的，引导加企微，配专属客服跟踪，定期礼物拜访（包括荐股），加入VIP群，为线下课准备客源

线下课要跟专门的展会公司合办，比如像云核变量一类的，我们提供部分客源，对方提供部分客源。我们的主要诉求是募资。这部分付费因为有会议成本、差旅成本，收费能打平开支即可满意。

目前看，投入产出比可能不如课程。最终收益取决于背后的理财产品的收益能力。

[click]

Alfonso Peccatiello，曾经管理过$20B资金。从ING离职后，做了两年半的播客(podcast)，他的一些见解和预测得到了认可。今年5月，他成立新的对冲基金，其中有1亿美元的资金，来自他的播客订阅者。这些是自来粉，因为认可他的理念和投资框架。他的公司有三名雇员，大部分营销由他自己完成。

阿方索的例子表明，通过自媒体募集资金是可行的（尽管国情不同，但都有方法）。

-->

