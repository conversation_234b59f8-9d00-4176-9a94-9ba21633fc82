<style>

body {
    padding: 1em;
}

#sidebar-toc-btn {
    display: none;
}

h1 {
    font-weight: 400 !important;
}

h4 {
    color: #808080 !important;
    font-weight: 100 !important;
    font-size: 1em;
    margin-left: 1em;
    display: none;
}

.top-banner-wrapper {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
}

.top-banner {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: #2E7D32;
    clip-path: path('M0,0 L1000,0 L1000,80 Q500,100 0,80 Z');
    color: white;
}


chap {
    display: block;
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.4);
    margin-bottom: 20px;
}

header {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));

    border-radius: 50%;
    backdrop-filter: blur(10px);
    background: url('https://www.pythontutorial.net/wp-content/uploads/2022/08/numpy-tutorial.svg'), rgba(33, 114, 3, 0.034);
    background-size: cover;
}

header > h2 {
    position: absolute;
    margin-top: 0 !important;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: #2E7D32;
    color: white !important;
    clip-path: path('M0,80 Q250,100 500,80 T1000,80 L1000,0 L0,0 Z');
    display: flex;
    justify-content: center;
    align-items: center;
}

outline {
  position: absolute;
  top: 50%;
  width: 100%;
  height: 60px;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 两列等宽 */
  gap: 10px; /* 列间距 */
  align-items: start; 
  padding: 20px 50px;
  color: #333 !important;
}

outline.three-cols {
  display: grid;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 5px;
  column-gap: 5px;
  align-items: start; 
  padding: 20px 10px;
}

outline > h3 {
  font-size: 0.8em !important;
  font-weight: 400 !important;
  margin: 0 0 0 10px !important;

  break-inside: avoid;
}


</style>

<div style='width:120px;position:absolute;top:30px;right:30px'>
<img src='https://images.jieyu.ai/images/hot/logo/gzh.jpg'>
</div>

![](https://images.jieyu.ai/images/hot/course/numpy/numpy-main.png)


<chap id="01">

<header>

##  聚类：寻找 Pair Trading 标的
</header>

<more>

介绍了因子投资的起源以及因子投资策略面临的困难。介绍了课程编排思路及课程安排。

</more>

<outline>

### 1.1. 因子投资的起源
### 1.2. 寻找 Alpha
### 1.3. 从 CAPM 拓展到多因子
### 1.4. 从因子分析到因子投资
### 1.5. 从因子模型到交易策略
### 1.6. 关于课程编排
</outline>
</chap>

