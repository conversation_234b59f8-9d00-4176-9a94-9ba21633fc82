---
clicks: 3
title: 教学方式
---

<style>
/* 只在当前页面生效 */
.slidev-page-10 .seq{
  animation: pageSeqAnimation 2s ease-in-out;
  animation-fill-mode: forwards;
}

@keyframes pageSeqAnimation {
  0%, 50% {
    /* 初始状态：居中，无边框，无背景，大字体 */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    border: none;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    font-size: 3.75rem;
    line-height: 1;
  }
  100% {
    /* 最终状态：左上角，有边框，有背景 */
    position: fixed;
    top: 0;
    left: 0;
    transform: translate(0, 0);
    text-align: left;
    border-left: 5px solid rgba(0, 0, 0, 0.2);
    border-right: 5px solid rgba(0, 0, 0, 0.2);
    border-top: none;
    border-bottom: none;
    background-color: aliceblue;
    padding: 5px 20px 5px 10px;
    border-radius: 0 20px 0 20px;
    font-size: inherit;
    line-height: inherit;
  }
}
</style>


<div v-motion class="abs w-full"
    :initial='{opacity: 0, x: 0}'
     :enter='{opacity: 1, x: 0, transition: {duration: 2000, delay: 500}}'
     :click-1='{x: -500, opacity: 1, transition: {duration: 500}}'
     :click-2="{opacity: 0}"
     :exit="{opacity:0}">
<img src='https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250712213825.png'>
</div>


<div class="abs w-50% mt-30 left-50%" v-motion 
    :enter="{opacity: 0}"
    :click-1="{opacity: 1}">

* <mdi-school class="text-green-400 animate-bounce mr-2" />基于大富翁量化环境构建
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-2='{opacity: 1}'>

</div>

<!--
[click]

基于大富翁量化环境和我们购买的商业数据，在量化24课中，用户可以体验基于真实、准确的数据进行的回测

我们的回测框架具有动态复权、防呆设计等特点，学员部署后，评价工业级水准。


[click]

我们构建了基于容器的用户学习环境。课件代码只读，但可运行，容器环境可重置。基于这些特性，我们的环境允许学员有全部权限去运行代码，但不担心被损坏。这样我们就构建出来一个高可用、稳定的学习环境。即使出问题，也可以通过重置来恢复。


[click]



1. 强调实战环境，商业数据
2. slidev 强大的演示能力，代码逐行高亮
3. on-spot 代码执行和结果展示，杜绝场景切换引起的注意力分散
4. 带有习题。演示习题目录
5. 精美的排版，截图
-->
