---
clicks: 3
title: 教学方式
---


<div class='abs w-full' v-motion
     :enter='{opacity: 0, x:0}'
     :click-1='{x:-500}'
     :click-2="{opacity: 0}">

<div style='text-align:center;margin: 0 auto 1rem' v-motion
     :enter='{opacity: 0}'
     :click-1='{opacity: 1, transition: {duration: 800, delay: 2000}}'>
<img src='https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250712213825.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class="abs w-50% mt-30 left-50%" v-motion 
    :enter="{opacity: 0}"
    :click-1="{opacity: 1}">

* <mdi-school class="text-green-400 animate-bounce mr-2" />基于大富翁量化环境
* <mdi-chart-line class="text-blue-400 animate-bounce mr-2" />多用户 相互隔离 容灾环境
* <mdi-puzzle-outline class="text-purple-400 animate-swing mr-2"/>领先的演示技术 更清晰的讲解及代码对照
* <mdi-notebook class="text-orange-400 animate-bounce mr-2"/>on-spot代码运行 确保授课连贯，学员注意力不分散
</div>

<!--

[click]

基于大富翁量化环境和我们购买的商业数据，在量化24课中，用户可以体验基于真实、准确的数据进行的回测

我们的回测框架具有动态复权、防呆设计等特点，学员部署后，评价工业级水准。


[click]

我们构建了基于容器的用户学习环境。课件代码只读，但可运行，容器环境可重置。基于这些特性，我们的环境允许学员有全部权限去运行代码，但不担心被损坏。这样我们就构建出来一个高可用、稳定的学习环境。即使出问题，也可以通过重置来恢复。


[click]



1. 强调实战环境，商业数据
2. slidev 强大的演示能力，代码逐行高亮
3. on-spot 代码执行和结果展示，杜绝场景切换引起的注意力分散
4. 带有习题。演示习题目录
5. 精美的排版，截图
-->
