---
clicks: 10
title: 教学方式
---

<style>
/* 只在当前页面生效 */
.slidev-page-11 .seq{
  animation: pageSeqAnimation 2s ease-in-out;
  animation-fill-mode: forwards;
}

@keyframes pageSeqAnimation {
  0%, 50% {
    /* 初始状态：居中，无边框，无背景，大字体 */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    border: none;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    font-size: 3.75rem;
    line-height: 1;
  }
  100% {
    /* 最终状态：左上角，有边框，有背景 */
    position: fixed;
    top: 0;
    left: 0;
    transform: translate(0, 0);
    text-align: left;
    border-left: 5px solid rgba(0, 0, 0, 0.2);
    border-right: 5px solid rgba(0, 0, 0, 0.2);
    border-top: none;
    border-bottom: none;
    background-color: aliceblue;
    padding: 5px 20px 5px 10px;
    border-radius: 0 20px 0 20px;
    font-size: inherit;
    line-height: inherit;
  }
}
</style>


<div v-motion class="abs w-full"
    :initial='{opacity: 0, x: 0}'
     :enter='{opacity: 1, x: 0, transition: {duration: 2000, delay: 500}}'
     :click-1='{x: -500, opacity: 1, transition: {duration: 500}}'
     :click-2="{opacity: 0}"
     :exit="{opacity:0}">
<img src='https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250712213825.png'>
</div>


<div v-motion class="abs w-full"
    :initial='{opacity: 0, x: -500, y: -150, scale: 0.7}'
     :click-2='{x: -270, opacity: 1, transition: {duration: 500}}'
     :click-3="{opacity: 0}"
     :exit="{opacity:0}"
     style="border: 2px solid rgba(0,0,0,0.1); border-radius: 12px; box-shadow: 0 8px 16px rgba(0,0,0,0.15); overflow: hidden;">
<img src='https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250713154952.png?v=2' style="width: 100%; height: auto; display: block;">
</div>

<div class='abs mt-10 w-50%' v-motion
     :enter='{opacity: 0}'
     :click-3='{opacity: 1}'>

```python{all|1|3-6|8|20,21,24}{at: 3,lines: true}
import numpy as np
from empyrical import sharpe_ratio

weights = np.array(np.random.random(4))
print('Random Weights:')
print(weights)

print('\nRebalance')
weights = weights/np.sum(weights)
print(weights)

# 生成每日每个标的对组合的贡献
weighted_returns = weights * returns
weighted_returns.head()

# 把每一行按列加总，就得到了每日资产收益
port_returns = weighted_returns.sum(axis=1)

# 然后计算组合资产的波动
cov = np.cov(port_returns.T)
port_vol = np.sqrt(np.dot(np.dot(weights, cov), weights.T)) # 0.01

# 使用 sharpe_ratio来计算夏普率
sr = sharpe_ratio(port_returns) # 0.18

print("Sharpe Ratio and Vol")
print(f"{sr:.2f} {port_vol:.2f}")
```
</div>


<!-- right start -->

<div class="abs w-50% mt-30 left-50%" v-motion 
    :enter="{opacity: 0}"
    :click-1="{opacity: 1}"
    :click-2="{opacity: 0}">

* <mdi-school class="text-green-400 animate-bounce mr-2" />基于大富翁量化环境构建
</div>

<div class="abs w-50% mt-30 left-50%" v-motion 
     :enter='{opacity: 0}'
     :click-2='{opacity: 1}'>
  
* <mdi-school class="text-green-400 animate-bounce mr-2" />基于大富翁量化环境构建
* <mdi-school class="text-green-400 animate-bounce mr-2" />Docker + Jupyter Lab，高可用，放开学员探索
</div>

<div class="abs w-50% mt-30 left-50%" v-motion 
     :enter='{opacity: 0}'
     :click-3='{opacity: 1}'>
  
* <mdi-school class="text-green-400 animate-bounce mr-2" />基于大富翁量化环境构建
* <mdi-school class="text-green-400 animate-bounce mr-2" />Docker + Jupyter Lab，高可用，放开学员探索
* <mdi-school class="text-green-400 animate-bounce mr-2" />基于Slidev演示 代码可逐行高亮讲解
</div>

<div class="abs w-50% mt-30 left-50%" v-motion 
     :enter='{opacity: 0}'
     :click-3='{opacity: 1}'>
  
* <mdi-school class="text-green-400 animate-bounce mr-2" />基于大富翁量化环境构建
* <mdi-school class="text-green-400 animate-bounce mr-2" />Docker + Jupyter Lab，高可用，放开学员探索
* <mdi-school class="text-green-400 animate-bounce mr-2" />基于Slidev演示 代码可逐行高亮讲解
* <mdi-school class="text-green-400 animate-bounce mr-2" />现场代码运行 避免场景切换分散注意力
</div>

<!--
接下来我就介绍下我们的教学是如何实施的，供您参考评估。

[click]

量化24课中，学员是需要体验量化全流程的。所以，我们基于大富翁量化环境和购买的商业数据，构建了学员的课程环境。在这个环境中，学员可以编写策略运行回测，完全使用真实的数据，并且我们的回测服务器会自动拦截T0交易，涨跌停板交易等错误；在客户端是动态前复权；在服务器端是真实复权、按真实成交量进行撮合。

我们的回测框架具有动态复权、防呆设计等特点，学员部署后，评价工业级水准。

因子课和pandas课程中我们没有使用大富翁回测框架，全部使用第三方开源软件，学员学成后，可以快速在本地复现。

[click]

我们构建了基于容器和Jupyter lab的用户学习环境。课件代码放在只读卷上。基于这些特性，我们的环境允许学员有全部权限去运行代码，但不担心被损坏。这样我们就构建出来一个高可用、稳定的学习环境。即使出问题，也可以通过重置来恢复。

这一点对于提供给学校使用，我相信也是很重要的。

[click]

我们比较注重技术创新。在因子课中，我们完全基于最新的slidev工具来制作演示文稿。

slidev强大的动画能力不仅可以时刻锁定注意力，在讲解代码方面，更能做到逐行高亮。

[click]
[click]
[click]

刚刚我们就演示了这种能力。

[click]
我们还做到了在演示文稿中，直接运行代码，查看结果，这样避免了临时切换到notebook时，注意力的分散。因为演示文稿的进度与notebook没法同步，所以，如果不能在演示文稿中直接展示代码并运行，需要切换到notebook中的话，都要在notebook中查找代码，这会浪费富贵的课堂时间。

[click]

带有习题。演示习题目录

[click]
我们的notebook有精美的排版，很受同学们欢迎。
-->
