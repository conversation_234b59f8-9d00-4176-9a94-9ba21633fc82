---
title: 量化人的Numpy和Pandas
layout: two-cols
clicks: 1
---


<div class="w-60% mt-10 abs left-30" v-motion
        :enter="{opacity: 1}"
        :click-1="{opacity: 0, transition: {duration: 2000, delay: 0}}">
        <img src="https://images.jieyu.ai/images/hot/course/numpy/numpy-main.png" class="w-full h-auto object-contain">
</div>

::right::

<div class="abs w-full mt-20 left-0" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

* <mdi-medal class="text-orange-400 animate-bounce mr-2" />初级课程
* <mdi-target class="text-blue-400 animate-bounce mr-2" />量化场景下灵活高效使用Numpy&Pandas
* <mdi-flask class="text-purple-400 animate-swing mr-2"/>拆解量化库 提取高频知识点 重写示例
* <mdi-brain class="text-pink-400 animate-bounce mr-2"/>20课，仅文本
</div>


<!--
Pandas有很多教程，但在学习中，由于没有结合量化场景举例，学生学完之后，遇到具体的问题，仍然想不起来，这些地方其实有非常高效的解决方案，有原生API可以一两行代码解决这个问题。
-->
