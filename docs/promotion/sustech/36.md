---
title: 量化人的Numpy和Pandas
layout: two-cols
---

<div class="w-60% mt-10 abs left-30" v-motion
        :enter="{opacity: 1}"
        :click-1="{opacity: 0, transition: {duration: 2000, delay: 0}}">
        <img src="https://images.jieyu.ai/images/hot/course/numpy/numpy-main.png" class="w-full h-auto object-contain">
</div>

::right::

<div class="abs w-full mt-20 left-0" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

* <mdi-school class="text-green-400 animate-bounce mr-2" />初级课程
* <mdi-chart-line class="text-blue-400 animate-bounce mr-2" />量化场景下灵活高效使用Numpy&Pandas
* <mdi-puzzle-outline class="text-purple-400 animate-swing mr-2"/>拆解量化库 提取高频知识点 重写示例
* <mdi-notebook class="text-orange-400 animate-bounce mr-2"/>20课，仅notebook
</div>

<!--
Pandas有很多教程，我们这门课与其它教程的不同之处在什么地方呢？

核心点在于，我们是深入到常用的量化库、量化策略当中，去分析它们是如何高效地解决数据存储、转换和计算问题的，找到他们是如何使用numpy和pandas的，然后，我们把一些高频、重要的知识点提取出来，构造数据和环境，形成适合在课程中讲解的示例。

所以，这门课也很受欢迎 。比如，像汕头大学的燕波老师，就是在公众号上看了我们这个系列之后，决定参加我们因子分析课程的。
-->
