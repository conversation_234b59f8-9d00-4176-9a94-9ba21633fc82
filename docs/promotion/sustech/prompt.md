# SUStech 演示文档开发提示词记录

本文档记录了在开发 SUStech 演示文档过程中的所有提示词和需求。

## 1. 基础设置和样式

### 1.1 复制网站元素
- **提示词**: "在http://ke.quantide.cn/的首页，有一个<div class="mission-title"/>，请把它复制到sustech/10.md中来"
- **需求**: 从官网首页提取核心标语元素

### 1.2 居中显示
- **提示词**: "将传播知识与希望居中显示"
- **提示词**: "需要上下居中"
- **提示词**: "传播知识与希望这一段需要在页面上垂直居中"
- **需求**: 实现文字的水平和垂直居中对齐

### 1.3 渐变背景效果
- **提示词**: "请实现这个效果，并且要注意到两个图构成了动画"
- **需求**: 实现类似提供图片的渐变背景和动画效果，包含：
  - 从蓝紫色到橙色的渐变背景
  - 旋转光晕效果
  - 闪光动画效果

## 2. 图片和布局

### 2.1 图片容器设计
- **提示词**: "因为图片大小不一样，所以，需要通过装框的方式，来实现外观大小一致"
- **需求**: 为不同尺寸的图片创建统一的容器框架

### 2.2 图片布局优化
- **提示词**: "在第44-46行间，插入三个图，并且各占1/4宽，均匀分布。请使用tailwind语法实现"
- **提示词**: "三个图片容器的布局存在对齐问题：1. 整体布局偏向左侧，没有在页面中完全居中 2. 最右侧的第三张图片右边有过多的空白区域 3. 三个图片容器之间的间距分布不够均匀"
- **需求**: 使用 Tailwind CSS 实现图片的均匀分布和完美居中

### 2.3 图片显示方式
- **提示词**: "将186到189行，改为通过background-image来显示图片。并且此div显示为250px的圆"
- **提示词**: "由于这三张图大小一致，所以，不用装框，直接显示即可"
- **需求**: 根据图片特性选择合适的显示方式

### 2.4 图片宽度控制
- **提示词**: "w-1/4有没有可能对图片不生效？"
- **需求**: 解决 Tailwind CSS 宽度类对图片元素的兼容性问题

## 3. 动画和交互

### 3.1 动画持续时间
- **提示词**: "给<div v-motion abs w-200px :enter="{x: -300, y: 100, opacity: 0}" :click-1="{x: -300, y: 100, opacity: 1}" :dur="3000">中的click-1指定动画持续时间"
- **需求**: 为 v-motion 动画添加自定义持续时间

### 3.2 依次显示动画
- **提示词**: "基本保持当前语法不变，有没有可能让三张图依次显示（使用transition）"
- **需求**: 实现图片的依次出现效果，而不是同时显示

## 4. 文本和内容

### 4.1 颜色设置
- **提示词**: "给Since 2001文本改颜色为灰色"
- **需求**: 为特定文本元素设置灰色样式

### 4.2 文本框设计
- **提示词**: "在第72行处，插入三个文本框。要求边框圆角、阴影，文字为正常的0.8倍"
- **需求**: 创建具有特定样式的文本框组件

### 4.3 网格布局
- **提示词**: "现在，要求第70行开始的div块中，4个元素（三个为刚生成的文本框，余下的为一个图片框），等长等宽，两行两行分布。图片在右下"
- **需求**: 实现2x2网格布局，图片位于右下角

### 4.4 推荐卡片设计
- **提示词**: "grid-item是对书籍推荐者的引用。因此，我们要先对推荐者进行简介--这包括一个头像，对他本人的20字左右的简介--可以左右分布，成为grid-item的第一行。然后，在第二行，是他们对本书的推荐，约150字"
- **需求**: 设计书籍推荐卡片，包含推荐者信息和推荐内容

### 4.5 头像设计
- **提示词**: "将107行替换为一个人物头像框和一段文本。头像框架为圆形，左上角，浮动"
- **提示词**: "现在，要把人像换成文字，因为没有头像图片。另外，要在头像下，加上20字左右简介"
- **需求**: 实现文字头像和个人简介的组合显示

### 4.6 文本样式优化
- **提示词**: "如何减少103-105段这个div中，文字的行距？"
- **需求**: 调整文本的行高以获得更紧密的排版

## 5. 特殊组件

### 5.1 开源软件展示
- **提示词**: "在第96行，插入三个文本框，用来显示我们的开源量化软件。文本框由header和content组成，圆角，阴影，三个header背景色各不相同，但符合互补色规范。content白色背景。"
- **需求**: 创建展示开源软件的卡片组件，使用互补色设计

## 6. 布局问题诊断

### 6.1 对齐问题分析
- **提示词**: "在这个10.md中，三张图片在y方向上不能对齐，原因是?只回答原因，不修改代码"
- **需求**: 分析布局问题的根本原因

### 6.2 性能优化思考
- **提示词**: "sort_values("date")会不会比较慢？在后面终归是要将它转换成为index的。转换成index之后，是否会自然重排序？"
- **需求**: 分析代码性能问题和优化可能性

## 总结

这些提示词涵盖了：
- CSS 样式和布局设计
- 动画和交互效果
- 响应式设计
- 组件化开发
- 性能优化
- 问题诊断

体现了从基础样式到复杂交互的完整开发流程。
