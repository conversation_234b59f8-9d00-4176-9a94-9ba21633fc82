---
title: 匡醍团队
layout: two-cols
clicks: 5
---

<div class='abs top-10% w-full left-50%' v-motion
     :enter='{opacity: 1, x: 0, scale: 1}'
     :click-2='{opacity: 1, scale: 0.7, x: -300}'
     :click-5='{opacity: 0, transition: {duration: 500}}'>

<div style='width:80%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/hot/me-2025-2-7.jpg'>
<span style='font-size:0.8em;width:80%;color:grey;'>创始人:杨勇</span>
</div>
</div>

<div class="abs w-full top-80% text-center left-50%" v-motion :enter="{opacity: 0}"
    :click-1="{opacity: 1}"
    :click-2="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />计算机硕士 | 华中科技大学
</div>

<div class="abs w-100% top-70% left-20 text-xs" v-motion :enter="{opacity: 0}"
    :click-2="{opacity: 1}"
    :click-3="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />Python技术专家
</div>

<div class="abs w-100% top-70% left-20 text-xs" v-motion :enter="{opacity: 0}"
    :click-3="{opacity: 1}"
    :click-4="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />Python技术专家
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> 《Python高效编程实践指南》
</div>

<div class='abs w-full mt-20' v-motion
     :enter='{opacity: 0}'
     :click-5='{opacity: 1, transition: {duration: 2000}}'>

<div style='text-align:center;margin: 0 auto 1rem'>
<div style='
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background-image: url("https://images.jieyu.ai/images/hot/instructor/portrait-half-transparent.png");
    background-size: 150%;
    background-position-y: 20%;
    background-position-x: 50%;
    margin: 0 auto 1rem auto;
    border: 3px solid #fff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
'></div>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>杨勇</span>
<span style='font-size:0.6em;display:inline-block;width:100%;text-align:center;color:grey'>创始人</span>
</div>
</div>

::right::


<div class='abs w-full flex justify-center items-center left--20px' v-motion
     :enter='{opacity: 0}'
     :click-3='{opacity: 1, transition: {duration: 3000}}'
     :click-4='{opacity: 0, transition: {duration: 500}}'>

<style scoped>
.grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
    width: 500px;
    height: 350px;
}

.grid-item {
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 16px;
    font-size: 0.8em;
    background-color: white;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.recommender-info {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.recommender-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #4facfe;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9em;
    font-weight: bold;
    margin-right: 12px;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

.recommender-desc {
    font-size: 0.7em;
    color: #444;
    line-height: 1.3;
}

.recommendation-text {
    font-size: 0.4rem;
    color: #a0a0a0;
    line-height: 1.5;
    text-align: left;
}

.image-item {
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 10px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-item img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}
</style>

<div class="grid-container">
<div class="grid-item">
    <div class="recommender-info">
        <div class="recommender-avatar">陈晓苏</div>
        <div class="recommender-desc">
            华中科技大学<br>计算机教授 博士生导师
        </div>
    </div>
    <div class="recommendation-text">
        我非常愿意也十分荣幸向有起于Python编程实践的工程开发技术人员以及在读的大学生爱好者推荐此书，希望你们能从中获取一些有益于自己编程能力和技巧的帮助。
    </div>
</div>

<div class="grid-item">
    <div class="recommender-info">
        <div class="recommender-avatar">朱灵引</div>
        <div class="recommender-desc">
            博士 勤远私募 合伙人<br>
            June Blackbox 合伙人
        </div>
    </div>
    <div class="recommendation-text">
        回想自己十多年前在芝加哥入行时，首次使用Python开发量化投资策略和研究系统时，不免四顾茫然，不知道有什么样的资源和工具，应该怎么去设计和实现。如果彼时手头有这样一本书，那必定会让我的努力更加的有的放矢，既省去不少无效的摸爬滚打，也让结果变得更稳定可靠，一路走来，多产出数个夏普3以上的策略也是很有可能的。
    </div>
</div>

<div class="grid-item">
    <div class="recommender-info">
        <div class="recommender-avatar">梁偲</div>
        <div class="recommender-desc">
            朝阳永续 产品总监 FRM持证人
        </div>
    </div>
    <div class="recommendation-text">
        量化投资是一趟艰难而纯粹的旅途，朝阳永续一直投身于此，用我们的数据和工具为这条旅途上的专业机构和虔诚探索者搭桥问路。器利方能路远，我深切地感受到，Python以简洁的语法和强大的金融生态库支持，让复杂的金融模型变得易于理解和实现，从而成为量化人披荆斩棘的瑞士军刀。然而，少有人能够站在量化人的视角，将Python构建完整系统的过程庖丁解牛，入木三分。杨勇老师这本书深入浅出地揭示了Python的优雅与力量，为读者呈现出用Python构建完整项目的实践经验。每一个专题都是为了解决实战中的痛点而生，视角独特，不落窠臼，值得每一位量化人品读。
    </div>
</div>

<div class="image-item">
    <img src='https://images.jieyu.ai/images/hot/mybook/book-with-flower.png' alt="书籍图片">
</div>
</div>

</div>

<div class="abs w-full mt-25" v-motion 
    :enter="{opacity: 0}"
    :click-4="{opacity: 1, transition: {duration: 2000}}"
    :click-5="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />华中科技大学
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> Python技术专家
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>高级技术经理@IBM/Oracle
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>副总裁@海豚浏览器
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>连续创业 (2017~)
</div>

<div class='abs w-full mt-20' v-motion
     :enter='{opacity: 0}'
     :click-5='{opacity: 1, transition: {duration: 1000}}'>

<div style='text-align:center;margin: 0 auto 1rem'>
<div style='
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background-image: url("https://images.jieyu.ai/images/hot/instructor/flora-2.jpg");
    background-size: cover;
    background-position: center;
    margin: 0 auto 1rem auto;
    border: 3px solid #fff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
'></div>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'>陈敬</span>
<span style='font-size:0.6em;display:inline-block;width:100%;text-align:center;color:grey'>联合创始人 四川大学硕士</span>
<span style='font-size:0.6em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<!--

-->
