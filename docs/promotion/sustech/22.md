---
title: 团队
layout: two-cols
clicks: 10
---

<div class='abs top-10% w-full left-50%' v-motion
     :enter='{opacity: 1, x: 0, scale: 1}'
     :click-2='{opacity: 1, scale: 0.7, x: -300}'
     :click-5='{x: 0}'>

<div style='width:80%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/hot/me-2025-2-7.jpg'>
<span style='font-size:0.8em;width:80%;color:grey;'>创始人:杨勇</span>
</div>
</div>

<div class="abs w-full top-80% text-center left-50%" v-motion :enter="{opacity: 0}"
    :click-1="{opacity: 1}"
    :click-2="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />计算机硕士 | 华中科技大学
</div>

<div class="abs w-100% top-70% left-20 text-xs" v-motion :enter="{opacity: 0}"
    :click-2="{opacity: 1}"
    :click-3="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />Python技术专家
</div>

<div class="abs w-full mt-40 left-50%" v-motion :enter="{opacity: 0}"
    :click-8="{opacity: 1}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />计算机硕士@华中科技大学
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> Python技术专家
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>高级技术经理@IBM/Oracle
</div>

<div class='abs left-50% w-50% mt-50' v-motion
     :enter='{opacity: 0}'
     :click-4='{opacity: 1}'>

著有《Python高效编程实战指南》
</div>

<div class="abs w-full mt-40 left-50%" v-motion :enter="{opacity: 0}"
    :click-6="{opacity: 1}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />计算机硕士@华中科技大学
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> Python技术专家
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>高级技术经理@IBM/Oracle
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>副总裁@海豚浏览器
</div>

::right::

<div class='abs mt-12' v-motion
     :enter='{opacity: 0, scale: 0.8, y:0}'
     :click-2='{opacity: 1}'
     :click-3='{y: -100, scale: 0.5}'
     :click-4='{opacity: 0}'>

<div style='width:400px;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/hot/mybook/book-with-flower.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
</div>

<div class='abs' v-motion
     :enter='{opacity: 0}'
     :click-4='{opacity: 1}'>

<style scoped>
.text-box {
    border: 1px solid #ddd;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin: 8px;
    font-size: 0.8em;
    background-color: white;
    display: inline-block;
    width: 200px;
}
</style>

<div class="text-box">
    这是第一个文本框的内容
</div>

<div class="text-box">
    这是第二个文本框的内容
</div>

<div class="text-box">
    这是第三个文本框的内容
</div>

<div style='width:400px;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/hot/mybook/book-with-flower.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>

</div>

<!--

-->
