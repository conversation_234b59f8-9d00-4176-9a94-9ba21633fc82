---
title: 团队
layout: two-cols
clicks: 10
---

<div class='abs top-10% w-full left-50%' v-motion
     :enter='{opacity: 1, x: 0, scale: 1}'
     :click-2='{opacity: 1, scale: 0.7, x: -300}'
     :click-5='{x: 0}'>

<div style='width:80%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/hot/me-2025-2-7.jpg'>
<span style='font-size:0.8em;width:80%;color:grey;'>创始人:杨勇</span>
</div>
</div>

<div class="abs w-full top-80% text-center left-50%" v-motion :enter="{opacity: 0}"
    :click-1="{opacity: 1}"
    :click-2="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />计算机硕士 | 华中科技大学
</div>

<div class="abs w-100% top-70% left-20 text-xs" v-motion :enter="{opacity: 0}"
    :click-2="{opacity: 1}"
    :click-3="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />Python技术专家
</div>

<div class="abs w-100% top-70% left-20 text-xs" v-motion :enter="{opacity: 0}"
    :click-3="{opacity: 1}"
    :click-4="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />Python技术专家
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> 《Python高效编程实践指南》
</div>

<div class="abs w-full mt-40 left-50%" v-motion :enter="{opacity: 0}"
    :click-8="{opacity: 1}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />计算机硕士@华中科技大学
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> Python技术专家
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>高级技术经理@IBM/Oracle
</div>



<div class="abs w-full mt-40 left-50%" v-motion :enter="{opacity: 0}"
    :click-6="{opacity: 1}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />计算机硕士@华中科技大学
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/> Python技术专家
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>高级技术经理@IBM/Oracle
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>副总裁@海豚浏览器
</div>

::right::


<div class='abs w-full flex justify-center items-center left--20px' v-motion
     :enter='{opacity: 0}'
     :click-3='{opacity: 1}'>

<style scoped>
.grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
    width: 600px;
    height: 400px;
}

.grid-item {
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 16px;
    font-size: 0.8em;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.image-item {
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 10px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-item img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}
</style>

<div class="grid-container">
<div class="grid-item">
    <div style="position: relative;">
        <div style="
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-image: url('https://images.jieyu.ai/images/2025/07/avatar.jpg');
            background-size: cover;
            background-position: center;
            float: left;
            margin-right: 12px;
            margin-bottom: 8px;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        "></div>
        <div style="overflow: hidden;">
            这里是关于这个人物的介绍文本，可以描述他们的背景、经历或者相关信息。文本会自动环绕圆形头像显示。
        </div>
    </div>
</div>

<div class="grid-item">
    这是第二个文本框的内容
</div>

<div class="grid-item">
    这是第三个文本框的内容
</div>

<div class="image-item">
    <img src='https://images.jieyu.ai/images/hot/mybook/book-with-flower.png' alt="书籍图片">
</div>
</div>

</div>

<!--

-->
