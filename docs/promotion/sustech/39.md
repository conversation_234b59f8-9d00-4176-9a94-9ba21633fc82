---
title: 匡醍学员
clicks: 3
---

<style scoped>
.profile-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.profile-item {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-left: 4px solid #4facfe;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  flex-shrink: 0;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.profile-content {
  flex: 1;
}

.profile-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.profile-title {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
  font-style: italic;
}

.profile-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #555;
}

.avatar-1 { background: linear-gradient(135deg, #667eea, #764ba2); }
.avatar-2 { background: linear-gradient(135deg, #f093fb, #f5576c); }
.avatar-3 { background: linear-gradient(135deg, #4facfe, #00f2fe); }
</style>

<div class="profile-container">

  <!-- 学员档案 1 -->
  <div class="profile-item" v-motion
       :enter="{opacity: 0, x: -50}"
       :click-1="{opacity: 1, x: 0, transition: {duration: 800}}">
    <div class="profile-avatar avatar-1">
      李明
    </div>
    <div class="profile-content">
      <div class="profile-name">李明</div>
      <div class="profile-title">某知名券商量化研究员</div>
      <div class="profile-description">
        "匡醍的因子分析课程让我对量化投资有了全新的认识。课程内容既有理论深度，又有实战价值。特别是机器学习在因子挖掘中的应用，为我的日常工作提供了很多新思路。老师的讲解深入浅出，代码示例也很实用。"
      </div>
    </div>
  </div>

  <!-- 学员档案 2 -->
  <div class="profile-item" v-motion
       :enter="{opacity: 0, x: -50}"
       :click-2="{opacity: 1, x: 0, transition: {duration: 800, delay: 300}}">
    <div class="profile-avatar avatar-2">
      王雅
    </div>
    <div class="profile-content">
      <div class="profile-name">王雅琳</div>
      <div class="profile-title">金融科技公司算法工程师</div>
      <div class="profile-description">
        "作为一名有编程背景但缺乏金融知识的工程师，量化24课帮我快速建立了量化交易的完整知识体系。从数据获取到策略实现，每个环节都讲得很清楚。现在我已经能够独立开发和测试量化策略了，这对我的职业发展帮助很大。"
      </div>
    </div>
  </div>

  <!-- 学员档案 3 -->
  <div class="profile-item" v-motion
       :enter="{opacity: 0, x: -50}"
       :click-3="{opacity: 1, x: 0, transition: {duration: 800, delay: 600}}">
    <div class="profile-avatar avatar-3">
      张博
    </div>
    <div class="profile-content">
      <div class="profile-name">张博士</div>
      <div class="profile-title">高校金融学教授</div>
      <div class="profile-description">
        "匡醍的课程设计很有特色，理论与实践结合得很好。我把课程内容融入到自己的教学中，学生们反响很积极。特别是Numpy和Pandas的量化应用部分，解决了很多实际编程中的痛点问题。推荐给所有想要深入学习量化投资的朋友。"
      </div>
    </div>
  </div>

</div>
