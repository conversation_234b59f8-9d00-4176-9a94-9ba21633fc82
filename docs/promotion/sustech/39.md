---
title: 匡醍学员
clicks: 1
---

<style scoped>
.profile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  padding: 3rem;
  height: 50vh;
}

.profile-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.3s ease;
}

.profile-item:hover {
  transform: translateY(-5px);
}

.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
  box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

.profile-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.profile-title {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.3;
  max-width: 120px;
}

.avatar-0 {
    background: linear-gradient(to right, #007bff, #0056b3);
}

.avatar-1 { 
    background-image: url('https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250713165444.png');
    background-size: cover; 
}
.avatar-2 { background-image: url('https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250713165509.png'); 
    background-size: cover;
}
.avatar-3 { background-image: url('https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250713170105.png');
background-size: cover;
 }
.avatar-4 { 
    background-image: url('https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/roger.png');
    background-size: cover;
 }
.avatar-5 { background-image: url('https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250713171215.png');
    background-size: cover;
 }

.avatar-6 { background-image: url('https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250713172626.png');
    background-size: cover;
 }
</style>

<div class="profile-container" v-motion
     :initial='{opacity: 1}'
     :click-1='{opacity: 0}'>

  <!-- 学员 4 -->
  <div class="profile-item">
    <div class="profile-avatar avatar-4"></div>
    <div class="profile-name">朱灵引</div>
    <div class="profile-title">勤远基金<br>合伙人</div>
  </div>

  <div class="profile-item">
    <div class="profile-avatar avatar-0">张连华</div>
    <div class="profile-name">张连华</div>
    <div class="profile-title">中银投资<br>基金经理</div>
  </div>
  
  <!-- 学员 1 -->
  <div class="profile-item">
    <div class="profile-avatar avatar-1"></div>
    <div class="profile-name">占忆晨</div>
    <div class="profile-title">MIT<br>物理博士</div>
  </div>

  <!-- 学员 2 -->
  <div class="profile-item">
    <div class="profile-avatar avatar-2"></div>
    <div class="profile-name">PhiloSheep</div>
    <div class="profile-title">Stanford <br>物理博士</div>
  </div>

</div>

<div class="profile-container abs top-0 left-0 w-full" v-motion
     :enter='{opacity: 0}'
     :click-1='{opacity: 1}'
     :click-2='{opacity: 0}'>

  <div class="profile-item">
    <div class="profile-avatar avatar-6"></div>
    <div class="profile-name">梁偲</div>
    <div class="profile-title">朝阳永续<br>产品经理</div>
  </div>
  
  <div class="profile-item">
    <div class="profile-avatar avatar-0"></div>
    <div class="profile-name">燕波</div>
    <div class="profile-title">汕头大学<br>老师</div>
  </div>

  <div class="profile-item">
    <div class="profile-avatar avatar-5"></div>
    <div class="profile-name">Sikai</div>
    <div class="profile-title">溯跃私募<br>基金经理</div>
  </div>

  <div class="profile-item">
    <div class="profile-avatar avatar-3"></div>
    <div class="profile-name">李昱琦</div>
    <div class="profile-title">配邦私募<br>基金经理</div>
  </div>

</div>

<div class="abs bottom-10 text-gray-300 w-full text-center">
涉及客户信息 请协助我们保密
</div>

<!--
这里是我们部分学员的信息。限于篇幅，选取了8名。

学员总数93人，购课超过130人次。部分学员有复购。课程售价以4500、5000为主。

与华泰、国金、申万等券商有业务往来。
-->
