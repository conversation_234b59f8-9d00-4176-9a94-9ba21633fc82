---
clicks: 10
---
<style scoped>

.mission {
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
	background-size: 400% 400%;
	animation: gradientShift 8s ease infinite;
	border-radius: 20px;
	overflow: hidden;
}

.mission::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 30%, rgba(255,255,255,0) 70%);
	animation: rotate 15s linear infinite;
}

.mission::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg,
		transparent 30%,
		rgba(255,255,255,0.1) 50%,
		transparent 70%);
	animation: shimmer 3s ease-in-out infinite;
}

.mission-title {
	font-size: 3rem;
	color: white;
	margin: 0;
	position: relative;
	z-index: 2;
	text-shadow:
		0 2px 10px rgba(0, 0, 0, 0.3),
		0 0 20px rgba(255, 255, 255, 0.2);
	text-align: center;
	font-weight: 600;
	letter-spacing: 2px;
}

@keyframes gradientShift {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

@keyframes rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%);
		opacity: 0;
	}
	50% {
		opacity: 1;
	}
	100% {
		transform: translateX(100%);
		opacity: 0;
	}
}
</style>

<div class="mission">
    <h2 class="mission-title">传播知识和希望</h2>
</div>

<!---->
