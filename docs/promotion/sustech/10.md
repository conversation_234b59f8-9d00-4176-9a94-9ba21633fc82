---
clicks: 2
---
<style scoped>
body, html {
	margin: 0;
	padding: 0;
}

.mission {
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
	background-size: 400% 400%;
	animation: gradientShift 8s ease infinite;
	/* border-radius: 20px; */
	overflow: hidden;
	margin: 0;
	padding: 0;
}

.mission::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 30%, rgba(255,255,255,0) 70%);
	animation: rotate 15s linear infinite;
    z-index: -1;
}

.mission::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg,
		transparent 30%,
		rgba(255,255,255,0.1) 50%,
		transparent 70%);
	animation: shimmer 3s ease-in-out infinite;
    z-index: -1;
}

.mission-title {
    position: absolute;
	font-size: 3rem;
	color: white;
	text-shadow:
		0 2px 10px rgba(0, 0, 0, 0.3),
		0 0 20px rgba(255, 255, 255, 0.2);
	font-weight: 600;
	letter-spacing: 2px;
}

@keyframes gradientShift {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

@keyframes rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%);
		opacity: 0;
	}
	50% {
		opacity: 1;
	}
	100% {
		transform: translateX(100%);
		opacity: 0;
	}
}
</style>

<div class="mission">
<h2 class="mission-title" v-motion 
    :enter="{x: 0, y: 0, scale: 1}" 
    :click-1="{y: -120, scale: 0.7}">传播知识和希望
</h2>

</div>

<div v-motion class="abs w-300px top-40%"
    :initial="{x: 1000, opacity: 0}"
    :click-1="{x: 133, opacity: 1, transition: {duration: 3000}}"
>
    <img src="https://images.jieyu.ai/images/2025/07/mission.jpg?v=2"/>
</div>

<div v-motion class="abs w-300px top-40%"
    :initial="{x: -250, opacity: 0}"
    :click-1="{x: 566, opacity: 1, transition: {duration: 3000}}"
>
    <img src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250712153344.png"/>
</div>


<!--


我们是一家与众不同的公司，成立之初就把传播知识与希望作为自己的使命。确立这个使命，也有一些来由，反映了我这几年来的一些思考。

我本来是做互联网的，2017年起创业时，就已经明白，移动互联网已经进入下半场。人工智能普通人参与的机会似乎又很小，一时之间，似乎低垂的果实已被摘尽，不知道该做什么、能做什么。

正如任正非所说，似乎到了人生路窄的时候。但也正是从那时起，从此走上了求知与寻找希望的道路。

一路走来，尽管经历了外部环境螺旋式下滑，但只要自己坚持求知、保持好奇心、勇于探索，就似乎总能在困顿中找到新的方向，在迷茫中看到希望的曙光。

这种对知识的渴望和对未知的探索，不仅让我自己获得成长，更让我们明白了传播知识的价值。

现在，我也希望把这种想法传播开来 —— 让更多的人相信，只要保持学习的热情和探索的勇气，就一定能找到属于自己的光。

[click]

这次能够认识王赫教授，有机会介绍自己的一些探索，也是非常荣幸。

大约还在2018年前后，当时我在网上看哈佛还是哪个大学的公开课，发现这样的名校，居然在上一门名为IOS 8编程的课，当时就感到非常震撼。感觉这样的顶级学府，也会开设这么接地气的编程课，同时跟随工业界的脚本这么紧 -- 因为当时ios 8才刚出来不到一年应该是，所以也是希望国内高校能够有这种探索。

这次得知南方科技大学在开量化实验班，特别是还愿意接触我们这种名不见经传的小公司，就感到非常振奋。这也确实反映了南科大求是治学、日新立校的核心价值观。

我们也非常希望能达成合作。这里还有我自己的一点体验。在求学期间，总感觉理论学起来比较枯燥，后来工作十多年来，才明白这些理论、以及老师们搭建的理论体系实际上多么重要。

在我们匡醍的课程中，我想我也是尽可能地在尝试回答我当初的困惑，如何把理论与实践更紧密地结合起来。

-->
