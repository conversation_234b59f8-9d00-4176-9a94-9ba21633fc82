---
clicks: 3
---
<style scoped>
body, html {
	margin: 0;
	padding: 0;
}

.mission {
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
	background-size: 400% 400%;
	animation: gradientShift 8s ease infinite;
	/* border-radius: 20px; */
	overflow: hidden;
	margin: 0;
	padding: 0;
}

.mission::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 30%, rgba(255,255,255,0) 70%);
	animation: rotate 15s linear infinite;
    z-index: -1;
}

.mission::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg,
		transparent 30%,
		rgba(255,255,255,0.1) 50%,
		transparent 70%);
	animation: shimmer 3s ease-in-out infinite;
    z-index: -1;
}

.mission-title {
    position: absolute;
	font-size: 3rem;
	color: white;
	text-shadow:
		0 2px 10px rgba(0, 0, 0, 0.3),
		0 0 20px rgba(255, 255, 255, 0.2);
	font-weight: 600;
	letter-spacing: 2px;
}

@keyframes gradientShift {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

@keyframes rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%);
		opacity: 0;
	}
	50% {
		opacity: 1;
	}
	100% {
		transform: translateX(100%);
		opacity: 0;
	}
}
</style>

<div class="mission">
<h2 class="mission-title" v-motion 
    :enter="{x: 0, y: 0, scale: 1}" 
    :click-1="{y: -120, scale: 0.7}">传播知识和希望
</h2>

</div>

<div v-motion abs w-250px
    :enter="{x: 375, y: 250, opacity: 0}"
    :click-1="{opacity: 1, transition: {duration: 3000}}"
    :click-2="{x: 167}"
    :click-3="{x: 62.5}"
>
    <img src="https://images.jieyu.ai/images/2025/07/mission.jpg?v=2"/>
</div>

<div v-motion abs w-250px
    :enter="{x: 375, y: 100, opacity: 0}"
    :click-2="{x: 584, opacity: 1, transition: {duration: 3000}}"
    :click-3="{x: 685}"
>
    <img src="https://images.jieyu.ai/images/2025/07/mission.jpg?v=2"/>
</div>

<div v-motion abs w-250px
    :enter="{x: 375, y: 0, opacity: 0}"
    :click-3="{y: -100, opacity: 1, transition: {duration: 3000}}"
>
    <img src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/20250712153344.png"/>
</div>


<!--


[click]

我们是一家与众不同的公司，成立之初就把传播知识与希望作为自己的使命。


[click]

在求学期间，总感觉理论学起来比较枯燥，后来工作十多年来，才明白这些理论、以及它搭建的体系多么重要。

后来看哈佛公开课，他们有上Ios开发课，觉得实践课进入大学也是正常的，学生有了实践机会，才会更懂得底层基础知识的重要性。

[click]

南方科技大学？

-->
