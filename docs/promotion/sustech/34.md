---
title: 因子挖掘与机器学习策略
layout: two-cols
---

<div class="w-60% mt-10 abs left-30" v-motion
        :enter="{opacity: 1}"
        :click-1="{opacity: 0, transition: {duration: 2000, delay: 0}}">
        <img src="https://images.jieyu.ai/images/hot/course/factor-ml/fa-main.png" class="w-full h-auto object-contain">
</div>

::right::

<div class="abs w-full mt-20 left-0" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

* <mdi-medal class="text-orange-400 animate-bounce mr-2" />中级课程
* <mdi-target class="text-blue-400 animate-bounce mr-2" />聚焦策略研究全流程
* <mdi-flask class="text-purple-400 animate-swing mr-2"/>因子检验从原理到实践
* <mdi-format-list-group class="text-green-400 animate-heart-beat mr-2"/>市面常用因子归类讲解
* <mdi-brain class="text-pink-400 animate-bounce mr-2"/>机器学习从入门到精通
* <mdi-puzzle class="text-red-400 animate-bounce mr-2"/>量化场景下机器学习的难点与解决方案
</div>

<!--
因子挖掘与机器学习策略是一门中级课程。

量化24课知识涵盖面广，着重点在于帮助学员了解量化交易的全貌，学完后可以立即着手实施，但我们只介绍了三个策略。如何研究出自己的新策略？这就需要靠这门课了。

这门课先是介绍了如何把一个想法建模为因子，用什么样的方法来检验它的好坏。然后我们归类介绍了市面上已知且常用的因子。掌握这些知识，也是未来进入私募的一个基础。

如何把这么多因子组合成策略？我们认可、并在本课程中介绍的，是通过机器学习方法来组合因子，形成策略。

在这门课程里，我们提出并解决了机器学习交易策略的几个难点。第一是通过解剖梯度提升决策树的决策原理，揭示了为什么机器学习不可能从纯粹的价格数据中，学习到规律，并且给出了一个如何解决这类问题的示例；二是在量化场景下，机器学习社区常用的评估函数无法使用的问题，我们也给出了方案。这是我们课程的创新和贡献。
-->
