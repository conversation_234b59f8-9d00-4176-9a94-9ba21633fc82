---
title: 量化24课
layout: two-cols
---

<div class="w-60% mt-10 abs left-30" v-motion
        :enter="{opacity: 1}"
        :click-1="{opacity: 0, transition: {duration: 2000, delay: 0}}">
    <img src="https://images.jieyu.ai/images/hot/course/24lectures/24lecture-main.png" class="w-full h-auto object-contain">
</div>

::right::

<div class="abs w-full mt-20 left-0" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

* <mdi-sprout class="text-blue-400 animate-bounce mr-2" />入门级课程
* <mdi-view-grid class="text-blue-400 animate-bounce mr-2" />涵盖量化交易全流程
* <mdi-code-tags class="text-purple-400 animate-swing mr-2"/>量化中统计学概念及代码实现
* <mdi-database class="text-cyan animate-heart-beat mr-2"/>海量真实数据和精准回测支持
* <mdi-play-circle class="text-yellow-400 animate-bounce mr-2"/>40万字节Notebook 26小时视频
</div>

<!--
量化24课是我们从2023年4月起，开始授课的课程，到现在已经有两年时间了。

课程难度偏入门一点。课程共分六个部分，
先从证券和交易所编码、复权等基础概念讲起，介绍如何获得数据（包括tushare）。

第二部分介绍三类策略，一步步介绍如何编写策略，最后讲如何把手写的策略中的共同部分，抽象成为策略框架。

第三部分讲，如何进行基础的技术分析。这里面会讲解量化中常用的一些统计学概念，以及他们是如何用代码实现的

第四部分介绍了数据可视化，也包括量化中的常用评估指标如何计算和可视化。

第五部分是讲回测。我们介绍了backtrader这个回测框架。在学生掌握基础回测之后，我们介绍了回测当中的一些陷阱。这是比较需要工业界经验的地方。

第六部分我们介绍接入实盘有哪些方案。

课程由notebook加视频组成。notebook约40万字节，视频时长26小时。部分课程配有习题。

这门课程中我们有一些创新和贡献，比如，向学生介绍了python四舍五入算法在量化交易中会引起重要误差的问题；介绍了一维数据的聚类算法--这个可以用来发现股价的平台整理；介绍了通过zigzag来寻找股价的顶和底，从而刻画股价的波浪趋势；又比如我们通过蒙特卡洛方法，揭示了夏普率与最大回撤之间的分布关系，这是在实盘中，决定量化策略是否要继续运行的重要依据。
-->
