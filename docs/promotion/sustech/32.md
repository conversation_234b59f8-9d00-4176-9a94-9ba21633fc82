---
title: 量化24课和大富翁框架
layout: two-cols
clicks: 4
---


<div class="w-60% mt-10 abs left-30" v-motion
        :enter="{opacity: 1}"
        :click-1="{opacity: 0, transition: {duration: 2000, delay: 0}}">
    <img src="https://images.jieyu.ai/images/hot/course/24lectures/24lecture-main.png" class="w-full h-auto object-contain">
</div>

::right::

<div class="abs w-full mt-20 left-0" v-motion :enter="{opacity: 1}"
    :click-1="{opacity: 0}">

* <mdi-chart-timeline-variant-shimmer class="text-blue-400 animate-bounce mr-2" />涵盖量化交易全流程
* <mdi-chart-donut class="text-purple-400 animate-swing mr-2"/>量化中统计学概念及期代码实现
* <mdi-book-open-variant-outline class="text-cyan animate-heart-beat mr-2"/>海量真实数据和精准回测支持
* <mdi-medal-outline class="text-yellow-400 animate-bounce mr-2"/>40万字节，
</div>

<!--

-->
