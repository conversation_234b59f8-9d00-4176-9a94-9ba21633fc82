---
layout: fact
---

<style scoped>

.box {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 643px;
    height: 300px;
    /* box-shadow: 0px 2px 3px rgba(0,0,0,0.5); */
    background-color: #FFDA12;
}
.box-2 {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 643px;
    height: 300px;
    clip-path: polygon(60px 150px, 130px 50px, 250px 50px, 300px 0px, 
                       643px 0, 643px 300px,
                       300px 300px, 250px 250px, 130px 250px, 60px 150px);
    background-color: black;
}

.badge {
    position: absolute;
    width: 36px;
    height: 36px;
    top: 10px;
    left: 10px;
    z-index: 100;
}

.social {
    width: 16px;
    height: 16px;
}

.bottom-row {
    display: flex;
    justify-content: space-around;
    align-items: center;
    color: white;
    font-size: 14px;
    position: absolute;
    top: 270px;
    width: 25%;
    left: 70%;
}
.title {
    position: absolute;
    left: 120px;
    top: 125px;
    font-size: 32px;
    color: #FFDA12;
    border-bottom: 1px solid #FFDA12;
    width: 100%;
}

.title::after {
    content: "VIP礼券";
    font-size: 16px;
    position: relative;
    top: 40px;
    left: 60px
}

.value {
    font-size: 14px;
    font-style: italic;
    position: absolute;
    top: 10px;
    right: 20px;
    color: #FFDA12;
}
</style>

# 黑钻卡

<div class="box">

<div class="badge"><img src="https://images.jieyu.ai/images/hot/lhfy-badge.png"></div>

</div>

<div class="box-2">
<div class="value">价值: 2000元</div>
<div class="title">ZILLIONARE量化课程</div>
<div class="bottom-row">
<div >量化风云@</div>
<div class="social" style="left: 300px;clip-path: polygon(5% 5%, 95% 5%, 95% 95%, 5% 95%, 5% 5%);"><img src="https://images.jieyu.ai/images/hot/xhs-logo.jpg"></div>
<div class="social" style="left: 360px"><img src="https://images.jieyu.ai/images/hot/zhihu-logo.png"></div>
<div class="social" style="left: 420px"><img src="https://images.jieyu.ai/images/hot/wechat-logo.png"></div>
</div>
</div>

---

<style scoped>
.box {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 643px;
    height: 300px;
    /* box-shadow: 0px 2px 3px rgba(0,0,0,0.5); */
    background-color: black;
}
.box-2 {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 643px;
    height: 300px;
    clip-path: polygon(60px 150px, 130px 50px, 250px 50px, 300px 0px, 
                       643px 0, 643px 300px,
                       300px 300px, 250px 250px, 130px 250px, 60px 150px);
    background-color: #FFDA12;
    filter: drop-shadow(0, 2px,5px , rgba(0,0,0,0.2));
}

.badge {
    position: absolute;
    width: 36px;
    height: 36px;
    top: 10px;
    left: 10px;
    z-index: 100;
}

.social {
    width: 16px;
    height: 16px;
}

.bottom-row {
    display: flex;
    justify-content: space-around;
    align-items: center;
    color: black;
    font-size: 14px;
    position: absolute;
    top: 270px;
    width: 25%;
    left: 70%;
}

.title {
    position: absolute;
    left: 120px;
    top: 125px;
    font-size: 32px;
    color: black;
    border-bottom: 1px solid black;
    width: 100%;
}

.title::after {
    content: "VIP礼券";
    font-size: 16px;
    position: relative;
    top: 40px;
    left: 60px
}

.value {
    font-size: 14px;
    font-style: italic;
    position: absolute;
    top: 10px;
    right: 20px;
    color: black;
}

</style>

# 黄金卡


<div class="box">

<div class="badge"><img src="https://images.jieyu.ai/images/hot/lhfy-badge.png"></div>

</div>

<div class="box-2">
<div class="value">价值: 1000元</div>
<div class="title">ZILLIONARE量化课程</div>
<div class="bottom-row">
<div >量化风云@</div>
<div class="social" style="left: 300px;clip-path: polygon(5% 5%, 95% 5%, 95% 95%, 5% 95%, 5% 5%);"><img src="https://images.jieyu.ai/images/hot/xhs-logo.jpg"></div>
<div class="social" style="left: 360px"><img src="https://images.jieyu.ai/images/hot/zhihu-logo.png"></div>
<div class="social" style="left: 420px"><img src="https://images.jieyu.ai/images/hot/wechat-logo.png"></div>
</div>
</div>

---

<style scoped>
.box {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 643px;
    height: 300px;
    /* box-shadow: 0px 2px 3px rgba(0,0,0,0.5); */
    background: url('https://images.jieyu.ai/images/hot/black-gold.jpg');
    background-size: cover;
    color: white;
}

.title {
    position: relative;
    font-size: 20px;
    border-bottom: 1px solid black;
    text-align: center;
    margin: 50px auto;
    padding-bottom: 5px;
    width: 300px;
}

.desc {
    font-size: 14px;
}

.code {
    position: relative;
    width: 80px;
    top: 20px;
    left: 420px;
}

.code-box {
    position: relative;
    width: 120px;
    height: 30px;
    top: -8px;
    left: 480px;
    background: white;
    font-size: 16px;
    color: black;
    padding: 5px;
}

.qr {
    position: relative;
    width: 80px;
    height: 80px;
    top: -240px;
    left: 500px;
    background-image: url('https://images.jieyu.ai/images/hot/quantfans.png');
    background-size: cover;
}
</style>

# 背面



<div class="box">

<div class="title">
礼券使用说明
</div>

<div class="desc">


1. 本券可用于购买大富翁系列量化课程。单次课程可以使用多张兑换券。本券不支持现金返还；抵扣之后，即使有余额也不能换成现金。
2. 课程咨询及报名请联系: quantfans_99

</div>

<div class="code">
券码：
</div>

<div class="code-box"></div>

<div class="qr"/>
</div>
