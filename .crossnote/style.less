
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

.markdown-preview.markdown-preview {
  // modify your style here
  // eg: background-color: blue;
    img {
    width: calc(100%);
    margin: 0 auto;
    display: block;
    }

    img[alt="25%"] {
    width: 25%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    }

    img[alt="33%"] {
    width: 33%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    }

    img[alt="50%"] {
    width: 50%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    }

    img[alt="66%"] {
    width: 66%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    }

    img[alt="75%"] {
    width: 75%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    }


    img[alt="100%"] {
    width: 75%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    }

    img[alt="R50"] {
    position: relative;
    float: right;
    width: 45%;
    margin-left: 2vw !important;
    }

    img[alt="R33"] {
    position: relative;
    float: right;
    width: 33%;
    margin-left: 2vw !important;
    }

    img[alt="L50"] {
    position: relative;
    float: left;
    width: 45%;
    margin-right: 2vw !important;
    }

    img[alt="L33"] {
    position: relative;
    float: left;
    width: 33%;
    margin-right: 2vw !important;
    }

    /* 基本样式 */
    .language-tip {
        background-color: rgba(255,255,255,0.05);
        border-left: .4em solid #218838;
        box-shadow: 0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12),0 3px 1px -2px rgba(0,0,0,.2);
        padding: 0 0 1em 0;
        margin: 10px 0;
        border-radius: 4px;
        font-family: sans-serif;
        font-size: 14px;
        line-height: 1.5;
    }

    /* 标题样式 */
    .language-tip:before {
        content: "💎  Tip";
        display: block;
        font-weight: bold;
        margin-bottom: 5px;
        background-color: rgba(0, 192,165, .1);
        border-bottom: 1px solid rgba(0, 191, 165, .2);
        line-height: 2.5em;
        padding-left: 1.5em;
    }



    /* 内容样式 */
    .language-tip code {
        white-space: pre-wrap; /* 使代码块中的文本可以换行 */
        word-wrap: break-word; /* 长单词或 URL 地址自动换行 */
        display: block;
        font-family: monospace;
        font-size: 14px;
        color: #333;
        margin-top: 1em;
    }
}
