import asyncio
import datetime
import functools
import logging
import os
import pickle
import random
import warnings
from collections import OrderedDict
from typing import Dict, List, Optional, Union

import cfg4py
import numpy as np
import omicron
import pandas as pd
import talib as ta
import tushare as ts
from coretypes import Frame, FrameType
from IPython.display import clear_output
from numpy.typing import NDArray
from omicron import tf
from omicron.core.backtestlog import BacktestLogger
from omicron.extensions import (
    array_math_round,
    array_price_equal,
    bars_since,
    count_between,
    fill_nan,
    find_runs,
    math_round,
    price_equal,
    smallest_n_argpos,
    top_n_argpos,
)
from omicron.models.board import Board, BoardType
from omicron.models.security import Security
from omicron.models.stock import Stock
from omicron.plotting.candlestick import Candlestick
from omicron.plotting.metrics import MetricsGraph
from omicron.strategy.base import BaseStrategy
from omicron.talib import moving_average
from traderclient import TraderClient

warnings.filterwarnings("ignore")

cfg = cfg4py.get_instance()
logger = BacktestLogger.getLogger("notebook")


def enable_logging(flag: bool = True):
    global logger

    if not flag:
        logger.handlers.clear()
        return


async def init():
    cfg4py.init("/etc/zillionare")
    # enable_logging()
    await omicron.init()
    Board.init("192.168.100.101")


class AsyncLRUCache:
    def __init__(self, maxsize=128):
        self.maxsize = maxsize
        self.cache = OrderedDict()
        self.lock = asyncio.Lock()

    def __call__(self, func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            key = str(args) + str(kwargs)
            async with self.lock:
                if key not in self.cache:
                    result = await func(*args, **kwargs)
                    if len(self.cache) >= self.maxsize:
                        self.cache.popitem(last=False)
                    self.cache[key] = result
                return self.cache[key]

        return wrapper


@AsyncLRUCache()
async def load_bars(
    start: Union[datetime.date, datetime.datetime],
    end: Union[datetime.date, datetime.datetime],
    frame_type: FrameType,
    universe: Union[NDArray, int] = 500,
) -> pd.DataFrame:
    if frame_type == FrameType.DAY:
        assert (
            isinstance(start, datetime.date)
            and isinstance(end, datetime.date)
            and not isinstance(start, datetime.datetime)
            and not isinstance(end, datetime.datetime)
        ), "start/end 必须为datetime.date类型"
        if start >= datetime.date(2005, 1, 1) and end <= datetime.date(2023, 12, 31):
            return load_day_bars(start, end, universe)

    if frame_type in tf.minute_level_frames:
        assert isinstance(start, datetime.datetime) and isinstance(
            end, datetime.datetime
        ), "start/end 必须为datetime.datetime类型"

    print("正在加载数据中...")
    barss = []
    if isinstance(universe, int):
        n = universe
        secs = await Security.select().eval()
        secs = np.random.choice(secs, n, replace=False)
    else:
        secs = universe
    for sec in secs:
        bars = await Stock.get_bars_in_range(sec, frame_type, start, end)
        df = pd.DataFrame(bars).assign(asset=sec)
        df["price"] = df.open.shift(-1)
        barss.append(df)

    df = pd.concat(barss, ignore_index=True)
    return df.rename(columns={"frame": "date"}).set_index(["date", "asset"])


def load_day_bars(
    start: datetime.date, end: datetime.date, universe: Union[NDArray, int] = 500
):
    barss = None

    assert (
        isinstance(start, datetime.date)
        and isinstance(end, datetime.date)
        and not isinstance(start, datetime.datetime)
        and not isinstance(end, datetime.datetime)
    ), "start/end 必须为datetime.date类型"

    if end > datetime.date(2023, 12, 31) or end < datetime.date(2005, 1, 1):
        raise ValueError("Allow time range: 2005-01-04 ~ 2023-12-29")

    def _load_bars():
        nonlocal barss

        if barss is None:
            with open("/data/bars_1d_2005_2023.pkl", "rb") as f:
                barss = pickle.load(f)

        return barss

    barss = _load_bars()
    keys = list(barss.keys())
    if isinstance(universe, int):
        if universe == -1:
            selected_keys = keys
        else:
            selected_keys = random.sample(keys, min(universe, len(keys)))
    else:
        selected_keys = universe

    dfs = []
    for symbol in selected_keys:
        qry = "frame >= @start & frame <= @end"
        df = pd.DataFrame(barss[symbol]).assign(asset=symbol).query(qry)

        if len(df) == 0:
            logger.debug(f"no bars for {symbol} from {start} to {end}")
            continue
        # 前复权
        last = df.iloc[-1]["factor"]
        adjust_factor = df["factor"] / last
        adjust = ["open", "high", "low", "close", "volume"]
        df.loc[:, adjust] = df.loc[:, adjust].multiply(adjust_factor, axis="index")

        dfs.append(df)

    df = pd.concat(dfs, ignore_index=True)
    df.set_index(["frame", "asset"], inplace=True)
    df.index.names = ["date", "asset"]
    df.drop("factor", axis=1, inplace=True)
    df["price"] = df["open"].shift(-1)
    return df


def load_sectors():
    cache = "/data/sectors.pkl"
    try:
        with open(cache, "rb") as f:
            return pickle.load(f)
    except Exception:
        pass

    # 设置 Tushare Pro API 密钥
    token = os.environ.get("tushare_token")
    ts.set_token(token)
    pro = ts.pro_api()

    # 获取申万一级行业分类
    sw_classify = pro.index_classify(level="L1", src="SW2021")
    sw_classify.set_index("index_code", inplace=True)
    dfs = []
    for code in sw_classify.index:
        df = pro.index_member(index_code=code)
        df["index_name"] = sw_classify.loc[code, "industry_name"]
        dfs.append(df)

    sectors = pd.concat(dfs)
    sectors.rename(columns={"con_code": "asset", "index_name": "sector"}, inplace=True)
    sectors_sorted = sectors.sort_values(by="in_date", ascending=True)

    # 去重，保留每个重复组中的最后一个记录（即 in_date 最大的记录）
    sectors = sectors_sorted.drop_duplicates(subset=["asset"], keep="last")

    def translate(asset):
        x, y = asset.split(".")
        map_ = {"SH": "XSHG", "SZ": "XSHE"}
        return f"{x}.{map_.get(y.upper())}"

    sectors["asset"] = sectors.asset.apply(translate)
    sectors.set_index("asset", inplace=True)

    with open(cache, "wb") as f:
        pickle.dump(sectors["sector"], f)

    return sectors["sector"]


def fetch_valuation(date: datetime.date) -> pd.DataFrame:
    """
    获取指定日期的市值

    Args:
        date (datetime.date): 日期
    Returns:
        asset为索引，valuation为列的dataframe
    """
    year = date.year
    month = date.month
    df = None
    try:
        with open(f"/data/tushare_daily_basic/{year}/{month}/{date}.pkl", "rb") as f:
            df = pickle.load(f)
    except Exception:
        pass
    ts.set_token(os.environ.get("tushare_token"))
    pro = ts.pro_api()

    df = pro.daily_basic(
        ts_code="",
        trade_date=str(tf.date2int(date)),
        fields="ts_code,trade_date,circ_mv",
    )
    df = df.rename(columns={"ts_code": "asset", "circ_mv": "valuation"})

    def translate(asset):
        x, y = asset.split(".")
        map_ = {"SH": "XSHG", "SZ": "XSHE"}
        return f"{x}.{map_.get(y.upper())}"

    df.asset = df.asset.apply(translate)
    df.set_index("asset", inplace=True)

    os.makedirs(f"/data/tushare_daily_basic/{year}/{month}", exist_ok=True)
    try:
        with open(f"/data/tushare_daily_basic/{year}/{month}/{date}.pkl", "wb") as f:
            pickle.dump(df, f)
    except Exception:
        pass
    return df["valuation"]
