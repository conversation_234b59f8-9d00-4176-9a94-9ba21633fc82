{"cells": [{"cell_type": "markdown", "id": "33be2c43", "metadata": {}, "source": ["股民都很熟悉KDJ指标，也都知道这个指标有时候灵、有时候不灵。其实，现在私募界流行的是基于KDJ改良的一个指标，而如果你还只会用KDJ，当然要被主力割韭菜了。\n", "\n", "我是量化研究员芝士，今天的视频就给大家讲讲这个指标。内容都是干货，也可能有一定难度，建议你点赞收藏，下来后再仔细看。如果觉得指标好用，还可以转发给朋友，一起来研究。\n", "\n", "我们今天要介绍的这个指标，就是SKDJ，与KDJ只有一个字母之差，它是慢速KDJ的意思。KDJ指标是属于较快的随机波动，SKDJ指标则是属于较慢的随机波动，依股市经验，SKDJ指标较适合用于做短线。由于它不易出现抖动的杂讯，买卖点较KDJ指标明确。SKDJ指标的K值在低档出现与股价背离时，应当作买点，尤其是K值第二次超越D值时。\n", "\n", "用伪码表示为："]}, {"cell_type": "markdown", "id": "5ccff5e1", "metadata": {}, "source": ["```\n", "LOWV=N日内最低价的最低值\n", "HIGHV=N日内最高价的最高值\n", "\n", "RSV=(收盘价-LOWV)/(HIGHV-LOWV)*100的M日指数移动平均\n", "\n", "K=RSV的M日指数移动平均\n", "D=K的M日简单移动平均\n", "\n", "# N一般取9, <PERSON>一般取3\n", "```\n"]}, {"cell_type": "markdown", "id": "ba553456", "metadata": {}, "source": ["下面我们看看如何用python来实现这段伪码。我们通常使用talib来计算各种技术指标，不过，这个指标比较新，所以talib也没有提供。今天，我们就把这个秘密告诉大家："]}, {"cell_type": "code", "execution_count": 4, "id": "0cd71973", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'SKDJ_K': array([ -13.57125092,  -13.57125092,  -13.57125092,  -13.57125092,\n", "        -17.42414186,  -25.02373758,  -34.72313304,  -28.93837667,\n", "         -9.25340851,   11.18863187,   25.34481354,   30.22796663,\n", "         32.18113132,   24.04636317,   -7.69882048,  -54.74451692,\n", "        -95.6329832 , -120.38246134, -124.8136583 , -131.66487383,\n", "       -146.27481805, -164.78757878, -177.03114589, -190.00443018,\n", "       -200.53511883, -228.62316637, -249.90971551, -265.60787566,\n", "       -262.45821431, -239.19943921, -208.84446542, -189.08901332,\n", "       -174.29184163, -185.0504444 , -204.60450863, -225.94215016,\n", "       -246.8624558 , -266.00743936, -282.12565097, -289.42621964,\n", "       -310.59034611, -335.83199941, -334.67485019, -300.34692186,\n", "       -257.01249462, -224.24419365, -199.49107948, -182.1379396 ,\n", "       -189.81520817, -197.04186741, -187.85058665, -167.93460391,\n", "       -139.31631758, -128.06411725, -122.43248341, -135.80613986,\n", "       -146.97641161, -154.8177902 , -154.93434934, -152.66366563,\n", "       -149.14862837, -144.99119122, -125.35298317, -104.3616848 ,\n", "        -76.75331303,  -66.74673311,  -47.91026069,  -32.04374675,\n", "        -19.28011704,  -24.35938303,  -29.72265765,  -36.50064911,\n", "        -44.96266585,  -55.33545404,  -61.25264457,  -73.79777108,\n", "       -101.22357293, -135.86771694, -170.1949378 , -187.11165177,\n", "       -192.78151036, -189.88770193, -177.01349711, -164.10232272,\n", "       -159.42256149, -157.77627118, -144.98533413, -123.7538895 ,\n", "       -101.4762709 ,  -84.8468504 ,  -69.55115438,  -65.25343304,\n", "        -63.14369531,  -61.79927856,  -48.78941113,  -26.48210734,\n", "         -5.63354557,    6.00023723,   13.77231688,   27.75834151,\n", "         42.28986943,   50.71111691,   55.97408407,   58.78687766,\n", "         57.53807481,   42.09975989,   28.96287514,   24.40992935,\n", "         33.73044743,   43.15678482,   59.57256811,   65.23350442,\n", "         66.13650022,   53.15590846,   39.13552319,   44.09227176,\n", "         63.86052276,   83.22343731,   78.18054924,   65.16822237]), 'SKDJ_D': array([          nan,           nan,  -13.57125092,  -13.57125092,\n", "        -14.8555479 ,  -18.67304345,  -25.72367082,  -29.56174909,\n", "        -24.30497274,   -9.0010511 ,    9.09334563,   22.25380401,\n", "         29.25130383,   28.81848704,   16.17622467,  -12.79899141,\n", "        -52.69210687,  -90.25332049, -113.60970095, -125.62033116,\n", "       -134.25111672, -147.57575689, -162.69784757, -177.27438495,\n", "       -189.19023163, -206.3875718 , -226.35600024, -248.04691918,\n", "       -259.32526849, -255.75517639, -236.83403965, -212.37763932,\n", "       -190.74177346, -182.81043312, -187.98226489, -205.1990344 ,\n", "       -225.80303819, -246.27068177, -264.99851537, -279.18643666,\n", "       -294.04740557, -311.94952172, -327.03239857, -323.61792382,\n", "       -297.34475556, -260.53453671, -226.91592258, -201.95773758,\n", "       -190.48140908, -189.66500506, -191.56922074, -184.27568599,\n", "       -165.03383604, -145.10501291, -129.93763941, -128.76758017,\n", "       -135.07167829, -145.86678056, -152.24285038, -154.13860172,\n", "       -152.24888111, -148.93449507, -139.83093425, -124.90195306,\n", "       -102.15599367,  -82.62057698,  -63.80343561,  -48.90024685,\n", "        -33.07804149,  -25.22774894,  -24.45405257,  -30.19422993,\n", "        -37.06199087,  -45.59958967,  -53.85025482,  -63.46195656,\n", "        -78.75799619, -103.62968698, -135.76207589, -164.39143551,\n", "       -183.36269998, -189.92695469, -186.56090313, -177.00117392,\n", "       -166.84612711, -160.43371847, -154.06138894, -142.1718316 ,\n", "       -123.40516484, -103.3590036 ,  -85.29142523,  -73.21714594,\n", "        -65.98276091,  -63.3988023 ,  -57.910795  ,  -45.69026567,\n", "        -26.96835468,   -8.70513856,    4.71300285,   15.84363188,\n", "         27.94017594,   40.25310928,   49.6583568 ,   55.15735954,\n", "         57.43301218,   52.80823745,   42.86690328,   31.82418812,\n", "         29.0344173 ,   33.76572053,   45.48660012,   55.98761912,\n", "         63.64752425,   61.5086377 ,   52.80931062,   45.46123447,\n", "         49.02943924,   63.72541061,   75.08816977,   75.52406964])}\n"]}], "source": ["import omicron\n", "from coretypes import BarsArray, FrameType\n", "import pandas as pd\n", "from omicron.talib import exp_moving_average as ema\n", "from omicron.talib import moving_average as ma\n", "from omicron.models.stock import Stock\n", "import arrow\n", "\n", "await omicron.init()\n", "\n", "def skdj(bars: BarsArray, n: int=9, m: int=3):\n", "    \"\"\"慢速KDJ指标计算\n", "\n", "    Args:\n", "        bars: 行情数据\n", "    \"\"\"\n", "    close = bars[\"close\"]\n", "    lowv = np.min(bars[\"low\"][-n:])\n", "    highv = np.max(bars[\"high\"][-n:])\n", "\n", "    rsv = ema((close - lowv) / (highv - lowv) * 100, m)\n", "    k = ema(rsv, m)\n", "    d = ma(k, m)\n", "\n", "    return {\n", "        'SKDJ_K': k,\n", "        'SKDJ_D': d\n", "    }\n", "\n", "code = \"000001.XSHG\"\n", "bars = await Stock.get_bars(code, 120, end=arrow.now().date(), frame_type=FrameType.DAY)\n", "\n", "print(skdj(bars))"]}, {"cell_type": "markdown", "id": "66df9ed8", "metadata": {}, "source": ["代码我们也可以提供，欢迎私聊主播获取。"]}], "metadata": {"kernelspec": {"display_name": "cheese", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16 (default, Jan 17 2023, 23:13:24) \n[GCC 11.2.0]"}, "vscode": {"interpreter": {"hash": "43303443550d9225dc01e5080767f3f1700bf96098c698ccaeec78283f13fbac"}}}, "nbformat": 4, "nbformat_minor": 5}