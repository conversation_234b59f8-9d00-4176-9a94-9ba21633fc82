{"cells": [{"cell_type": "markdown", "id": "31c58d08", "metadata": {}, "source": ["## 1. 小市值策略\n", "在一定的时间周期内，持有市值最小的若干只股票，周期结束时卖出；在下一周期开始时，再买入市值最小的若干只股票，在周期结束时卖出，如此循环。1981年Banz基于纽交所长达40年的数据发现，小市值股票月均收益率比其他股票高0.4%。因此，小市值策略是在全球范围来讲，普遍适用的一个策略。市值因子也被纳入了Fama三因子模型和五因子模型，有时候也被称为规模因子。\n", "\n", "研究表现，A股市场上，2016年以前，规模因子的显著性甚至超过了欧美等发达国家市场。不过，任何因子都很难一直有效，到了2017-2018年期间，大市值股票的表现明显优于小市值股票，使得规模因子在A股市场上的有效性存疑。\n", "\n", "我们在聚宽平台上，以它提供的示例策略，运行后，得到的结果表明，该因子在最近两年依然有效：\n", "\n", "![](https://images.jieyu.ai/images/2023/04/20230426161715.png)\n", "\n", "下面，我们就先来实现这个策略，然后来讨论小市值策略应该如何使用的问题。\n", "\n", "### 1.1. 初始化"]}, {"cell_type": "code", "execution_count": null, "id": "df75f9eb", "metadata": {}, "outputs": [], "source": ["import logging\n", "import os\n", "from IPython.display import clear_output\n", "\n", "import cfg4py\n", "from coretypes import FrameType\n", "import omicron\n", "from omicron import tf\n", "from omicron.models.security import Security\n", "from omicron.models.stock import Stock\n", "\n", "import jqdatasdk as jq\n", "\n", "\n", "account = os.environ.get(\"jq_account\")\n", "passwd = os.environ.get(\"jq_password\")\n", "jq.auth(account, passwd)\n", "\n", "cfg = cfg4py.init(\"/etc/zillionare\")\n", "\n", "logging.basicConfig(level=logging.WARNING)\n", "logger = logging.getLogger(\"test\")\n", "# logger.addHandler(logging.StreamHandler(stream=sys.stdout))\n", "\n", "await omicron.init()\n", "clear_output()"]}, {"cell_type": "markdown", "id": "565623de", "metadata": {}, "source": ["### 1.2. 策略主体代码"]}, {"cell_type": "code", "execution_count": null, "id": "1edc5751", "metadata": {}, "outputs": [], "source": ["from jqdatasdk import query, valuation\n", "import uuid\n", "from traderclient import TraderClient\n", "import datetime\n", "from typing import List, Optional\n", "import numpy as np\n", "from traderclient.errors import TradeError\n", "# from omicron.plotting.metrics import MetricsGraph\n", "\n", "# set the envar during debugging mode only\n", "# os.environ[\"TRADER_CLIENT_TIMEOUT\"] = \"600\"\n", "\n", "\n", "class SmallCapStrategy:\n", "    def __init__(\n", "        self,\n", "        start: datetime.date,\n", "        end: datetime.date,\n", "        account: Optional[str] = None,\n", "        token: Optional[str] = None,\n", "        url: Optional[str] = None,\n", "        pool_size_ratio=0.01,\n", "    ):\n", "\n", "        self.pool_size_ratio = pool_size_ratio\n", "        self.bills = None\n", "        self.metrics = None\n", "\n", "        self.start = start\n", "        self.end = end\n", "        self.token = token or uuid.uuid4().hex\n", "        self.account = account or f\"smallcap-{self.token[-4:]}\"\n", "\n", "        self.broker = TraderClient(\n", "            url or cfg.backtest.url,\n", "            self.account,\n", "            self.token,\n", "            is_backtest=True,\n", "            start=self.start,\n", "            end=self.end,\n", "        )\n", "\n", "    async def backtest(self):\n", "        for i, frame in enumerate(tf.get_frames(self.start, self.end, FrameType.DAY)):\n", "            # 没到调仓时间\n", "            if i % 5 != 0:\n", "                continue\n", "\n", "            dt = tf.int2date(frame)\n", "            positions = self.broker.positions(dt)\n", "            buylist = await self.choose_target(dt)\n", "\n", "            to_sell = np.setdiff1d(positions[\"security\"], buylist)\n", "            to_buy = np.setdiff1d(buylist, positions[\"security\"])\n", "\n", "            # 优先处理卖出，以释放资金\n", "            for sec in to_sell:\n", "                try:\n", "                    volume = self.broker.available_shares(sec, dt)\n", "                    self.broker.market_sell(\n", "                        sec, volume, order_time=tf.combine_time(dt, 14, 55)\n", "                    )\n", "                except TradeError as e:\n", "                    logger.warning(str(e).split(\"\\n\")[0])\n", "                except Exception as e:\n", "                    logger.exception(e)\n", "\n", "            cash = self.broker.available_money\n", "            per_stock = cash / len(to_buy)\n", "            for sec in to_buy[:-1]:\n", "                try:\n", "                    await self.broker.buy_by_money(\n", "                        sec, per_stock, order_time=tf.combine_time(dt, 14, 55)\n", "                    )\n", "                except TradeError as e:\n", "                    logger.warning(str(e).split(\"\\n\")[0])\n", "                except Exception as e:\n", "                    logger.exception(e)\n", "\n", "            # 多余的资金结转到最后一支股票上\n", "            cash = self.broker.available_money\n", "            sec = to_buy[-1]\n", "            await self.broker.buy_by_money(\n", "                sec, cash, order_time=tf.combine_time(dt, 14, 55)\n", "            )\n", "\n", "        self.broker.stop_backtest()\n", "        self.bills = self.broker.bills()\n", "        self.metrics = self.broker.metrics(baseline=\"399300.XSHE\")\n", "\n", "    async def plot(self):\n", "        mg = MetricsGraph(self.bills, self.metrics)\n", "        await mg.plot()\n", "\n", "    async def filter_paused_stock(self, buylist: List[str], dt: datetime.date):\n", "        secs = await Security.select(dt).eval()\n", "        in_trading = jq.get_price(\n", "            secs, fields=[\"paused\"], start_date=dt, end_date=dt, skip_paused=True\n", "        )[\"code\"].to_numpy()\n", "\n", "        return np.intersect1d(buylist, in_trading)\n", "\n", "    async def choose_target(self, dt: datetime.date):\n", "        # 聚宽示例策略在这里加了20~30亿市值限制，也有它的道理。也许动态调整更好\n", "        q = (\n", "            query(valuation.code, valuation.market_cap)\n", "            .filter(valuation.market_cap.between(20, 30))\n", "            .order_by(valuation.market_cap.asc())\n", "        )\n", "\n", "        # 选出低市值的股票，构成buylist\n", "        df = jq.get_fundamentals(q, dt)\n", "\n", "        buylist = list(df[\"code\"])\n", "\n", "        # 过滤停牌股票\n", "        buylist = await self.filter_paused_stock(buylist, dt)\n", "        logger.debug(\"got %s secs after paused been filtered\", len(buylist))\n", "\n", "        # 随着市场扩容，可以多取，但最少取5支\n", "        size = max(int(len(df) * self.pool_size_ratio), 5)\n", "        return buylist[:size]"]}, {"cell_type": "markdown", "id": "a1ed8ac2", "metadata": {}, "source": ["上述代码中，依赖了一个回测指标绘图的类，这里我们给出代码，暂不做展开："]}, {"cell_type": "code", "execution_count": null, "id": "b35eae66", "metadata": {}, "outputs": [], "source": ["import datetime\n", "import logging\n", "from collections import defaultdict\n", "from copy import deepcopy\n", "from typing import List, Union\n", "\n", "import arrow\n", "import numpy as np\n", "import pandas as pd\n", "import plotly.graph_objects as go\n", "from coretypes import BarsArray, Frame, FrameType\n", "from numpy.typing import NDArray\n", "from plotly.subplots import make_subplots\n", "\n", "from omicron import tf\n", "from omicron.extensions import fill_nan\n", "from omicron.models.security import Security\n", "from omicron.models.stock import Stock\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "class MetricsGraph:\n", "    def __init__(self, bills: dict, metrics: dict):\n", "        self.metrics = metrics\n", "        self.trades = bills[\"trades\"]\n", "        self.positions = bills[\"positions\"]\n", "        self.start = arrow.get(bills[\"assets\"][0][0]).date()\n", "        self.end = arrow.get(bills[\"assets\"][-1][0]).date()\n", "\n", "        self.frames = [\n", "            tf.int2date(f) for f in tf.get_frames(self.start, self.end, FrameType.DAY)\n", "        ]\n", "\n", "        # 记录日期到下标的反向映射，这对于在不o\n", "        self._frame2pos = {f: i for i, f in enumerate(self.frames)}\n", "        self.ticks = self._format_tick(self.frames)\n", "\n", "        self.assets = pd.DataFrame(bills[\"assets\"], columns=[\"frame\", \"assets\"])[\n", "            \"assets\"\n", "        ].to_numpy()\n", "        self.nv = self.assets / self.assets[0]\n", "\n", "    def _fill_missing_prices(self, bars: BarsArray, frames: Union[List, NDArray]):\n", "        \"\"\"将bars中缺失值采用其前值替换\n", "\n", "        当baseline为个股时，可能存在停牌的情况，这样导致由此计算的参考收益无法与回测的资产收益对齐，因此需要进行调整。\n", "\n", "        出于这个目的，本函数只返回处理后的收盘价。\n", "\n", "        Args:\n", "            bars: 基线行情数据。\n", "            frames: 日期索引\n", "\n", "        Returns:\n", "            补充缺失值后的收盘价序列\n", "        \"\"\"\n", "        _close = pd.DataFrame(\n", "            {\n", "                \"close\": pd.Series(bars[\"close\"], index=bars[\"frame\"]),\n", "                \"frame\": pd.Series(np.empty((len(frames),)), index=frames),\n", "            }\n", "        )[\"close\"].to_numpy()\n", "\n", "        # 这里使用omicron中的fill_nan，是因为如果数组的第一个元素即为NaN的话，那么DataFrame.fillna(method='ffill')将无法处理这样的情况(仍然保持为nan)\n", "\n", "        return fill_nan(_close)\n", "\n", "    def _format_tick(self, frames: Union[Frame, List[Frame]]) -> Union[str, NDArray]:\n", "        if type(frames) == datetime.date:\n", "            x = frames\n", "            return f\"{x.year:02}-{x.month:02}-{x.day:02}\"\n", "        elif type(frames) == datetime.datetime:\n", "            x = frames\n", "            return f\"{x.month:02}-{x.day:02} {x.hour:02}:{x.minute:02}\"\n", "        elif type(frames[0]) == datetime.date:  # type: ignore\n", "            return np.array([f\"{x.year:02}-{x.month:02}-{x.day:02}\" for x in frames])\n", "        else:\n", "            return np.array(\n", "                [f\"{x.month:02}-{x.day:02} {x.hour:02}:{x.minute:02}\" for x in frames]  # type: ignore\n", "            )\n", "\n", "    async def _metrics_trace(self):\n", "        metric_names = {\n", "            \"start\": \"起始日\",\n", "            \"end\": \"结束日\",\n", "            \"window\": \"资产暴露窗口\",\n", "            \"total_tx\": \"交易次数\",\n", "            \"total_profit\": \"总利润\",\n", "            \"total_profit_rate\": \"利润率\",\n", "            \"win_rate\": \"胜率\",\n", "            \"mean_return\": \"日均回报\",\n", "            \"sharpe\": \"夏普率\",\n", "            \"max_drawdown\": \"最大回撤\",\n", "            \"annual_return\": \"年化回报\",\n", "            \"volatility\": \"波动率\",\n", "            \"sortino\": \"sortino\",\n", "            \"calmar\": \"calmar\",\n", "        }\n", "\n", "        # bug: plotly go.Table.Cells format not work here\n", "        metric_formatter = {\n", "            \"start\": \"{}\",\n", "            \"end\": \"{}\",\n", "            \"window\": \"{}\",\n", "            \"total_tx\": \"{}\",\n", "            \"total_profit\": \"{:.2f}\",\n", "            \"total_profit_rate\": \"{:.2%}\",\n", "            \"win_rate\": \"{:.2%}\",\n", "            \"mean_return\": \"{:.2%}\",\n", "            \"sharpe\": \"{:.2f}\",\n", "            \"max_drawdown\": \"{:.2%}\",\n", "            \"annual_return\": \"{:.2%}\",\n", "            \"volatility\": \"{:.2%}\",\n", "            \"sortino\": \"{:.2f}\",\n", "            \"calmar\": \"{:.2f}\",\n", "        }\n", "\n", "        metrics = deepcopy(self.metrics)\n", "        baseline = metrics[\"baseline\"]\n", "        del metrics[\"baseline\"]\n", "\n", "        if \"code\" in baseline:\n", "            baseline_name = await Security.alias(baseline[\"code\"])\n", "            del baseline[\"code\"]\n", "        else:\n", "            baseline_name = \"基准\"\n", "\n", "        metrics_formatted = []\n", "        for k in metric_names.keys():\n", "            if metrics.get(k):\n", "                metrics_formatted.append(metric_formatter[k].format(metrics.get(k)) )\n", "            else:\n", "                metrics_formatted.append(\"-\")\n", "\n", "        baseline_formatted = []\n", "        for k in metric_names.keys():\n", "            if baseline.get(k):\n", "                baseline_formatted.append(metric_formatter[k].format(baseline.get(k)) )\n", "            else:\n", "                baseline_formatted.append(\"-\")\n", "                \n", "        return go.Table(\n", "            header=dict(values=[\"指标名\", \"策略\", baseline_name]),\n", "            cells=dict(\n", "                values=[\n", "                    [metric_names[k] for k in metrics],\n", "                    metrics_formatted,\n", "                    baseline_formatted\n", "                ],\n", "                font_size=10,\n", "            ),\n", "        )\n", "\n", "    async def _trade_info_trace(self):\n", "        \"\"\"构建hover text 序列\"\"\"\n", "        X = []\n", "        Y = []\n", "        data = []\n", "\n", "        # convert trades into hover_info\n", "        merged = defaultdict(list)\n", "        for _, trade in self.trades.items():\n", "            trade_date = arrow.get(trade[\"time\"]).date()\n", "\n", "            ipos = self._frame2pos.get(trade_date)\n", "            if ipos is None:\n", "                logger.warning(\n", "                    \"date  %s in trade record not in backtest range\", trade_date\n", "                )\n", "                continue\n", "\n", "            name = await Security.alias(trade[\"security\"])\n", "            price = trade[\"price\"]\n", "            side = trade[\"order_side\"]\n", "            filled = trade[\"filled\"]\n", "\n", "            trade_text = f\"{side}:{name} {filled/100:.0f}手 价格:{price:.02f} 成交额{filled * price/10000:.1f}万\"\n", "\n", "            merged[trade_date].append(trade_text)\n", "\n", "        for dt, text in merged.items():\n", "            ipos = self._frame2pos.get(dt)\n", "            Y.append(self.nv[ipos])\n", "            X.append(self._format_tick(dt))\n", "\n", "            asset = self.assets[ipos]\n", "            hover = f\"资产:{asset/10000:.1f}万<br>{'<br>'.join(text)}\"\n", "            data.append(hover)\n", "\n", "        trace = go.<PERSON>atter(x=X, y=Y, mode=\"markers\", text=data, name=\"交易详情\")\n", "        return trace\n", "\n", "    async def plot(self, baseline_code: str = \"399300.XSHE\"):\n", "        \"\"\"绘制资产曲线及回测指标图\"\"\"\n", "        n = len(self.assets)\n", "        bars = await Stock.get_bars(baseline_code, n, FrameType.DAY, self.end)\n", "\n", "        baseline_prices = self._fill_missing_prices(bars, self.frames)\n", "        baseline_prices /= baseline_prices[0]\n", "\n", "        fig = make_subplots(\n", "            rows=1,\n", "            cols=2,\n", "            shared_xaxes=False,\n", "            specs=[\n", "                [{\"type\": \"scatter\"}, {\"type\": \"table\"}],\n", "            ],\n", "            column_width=[0.75, 0.25],\n", "            horizontal_spacing=0.01,\n", "            subplot_titles=(\"资产曲线\", \"策略指标\"),\n", "        )\n", "\n", "        fig.add_trace(await self._metrics_trace(), row=1, col=2)\n", "\n", "        print(\"baseline\", len(baseline_prices))\n", "        baseline_trace = go.<PERSON>(\n", "            y=baseline_prices,\n", "            x=self.ticks,\n", "            mode=\"lines\",\n", "            name=\"baseline\",\n", "            showlegend=True,\n", "        )\n", "        fig.add_trace(baseline_trace, row=1, col=1)\n", "\n", "        nv_trace = go.<PERSON>(\n", "            y=self.nv, x=self.ticks, mode=\"lines\", name=\"策略净值\", showlegend=True\n", "        )\n", "        fig.add_trace(nv_trace, row=1, col=1)\n", "\n", "        trade_info_trace = await self._trade_info_trace()\n", "        fig.add_trace(trade_info_trace, row=1, col=1)\n", "\n", "        fig.update_xaxes(type=\"category\", tickangle=45, nticks=len(self.ticks) // 5)\n", "        fig.update_layout(margin=dict(l=20, r=20, t=50, b=50), width=1040, height=435)\n", "        fig.show()"]}, {"cell_type": "markdown", "id": "31b6b626", "metadata": {}, "source": ["接下来，我们以一个较短的时间窗口调用一下这个策略。"]}, {"cell_type": "code", "execution_count": null, "id": "11179184", "metadata": {}, "outputs": [], "source": ["start = tf.day_shift(datetime.date(2023, 4, 1), 0)\n", "end = tf.day_shift(datetime.date(2023, 4, 28), 0)\n", "\n", "s = SmallCapStrategy(start, end)\n", "await s.back<PERSON>()\n", "await s.plot()"]}, {"attachments": {}, "cell_type": "markdown", "id": "c1bda113", "metadata": {}, "source": ["运行后，我们将得到下面的输出：\n", "\n", "![](https://images.jieyu.ai/images/2023/05/smallcap.png)\n", "\n", "阅读这个例子的源码，我们需要注意以下几点：\n", "1. 一个最简单的策略，它的回测框架至少要包括哪些内容？\n", "2. TraderClient的作用和基本使用方法？\n", "3. 回测结束，如何将资产曲线和策略指标绘制出来？\n", "\n", "## 1.3. 策略优化\n", "\n", "有许多因素可能影响到策略优化。\n", "\n", "首先，回测系统可以防止在跌停板上卖出，阻止在涨停板上买入，但不是相反。显然，我们不应该在涨停板上卖出股票，因为根据统计，连板指数大约是以每天1.8%的速度在上涨。因此，在涨停板上卖出股票，意味着我们将在这部分资金上损失1.8%的超额收益。这不是一个需要调节的参数，应该直接写死在我们的策略中。\n", "\n", "其次，我们在简单地回测中，就发现买入了好几支ST的股票。实际上，这些标的是应该排除掉的，或者说，至少应该在年报前后排除掉。这一部分，至少排除掉ST个股，应该写死在我们的策略中。\n", "\n", "接下来，是一些需要通过反复测试来进行调优的参数。\n", "\n", "首先，注意到我们设置了一个pool_size_ratio这个参数。在一个较长跨度的回测中，我们需要注意到A股的交易品种是在不断增长的。如果我们固定地取20支股票，那么在2005年前后，这可能会占到小市值股票中的相当一部分。而策略的用意则是要找出最小市值的那一部分。\n", "\n", "因此，我们设置了pool_size_ratio这个参数，并且为防止取值过少，我们还在代码中硬性规定了不得少于5支。\n", "\n", "另外，策略并不是严格按照市值来进行筛选的。实际上，它首先排除掉了不在20亿到30亿这个区间的一些标的。这么做有一定的道理，因为一支股票如果市值太小，它的成长能力也可能不够强。但这应该成为一个被测试的参数。\n", "\n", "再次，我们对持仓周期的选择是5天。这也是一个可以调节的参数。\n", "\n", "最后，我们没有设定首次买入的时间。实际上，如果放在周四收盘买入，情况可能会有所不同。\n", "\n", "这些参数应该作为SmallCapStrategy初始化参数的一部分，并且使用.model_selection.GridSearchCV来自动进行参数搜索。\n"]}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.16 (default, Mar  2 2023, 03:21:46) \n[GCC 11.2.0]"}, "vscode": {"interpreter": {"hash": "046c8af816d98eb55bb2070cfc0dea3510f631af20975c6dcbe41ce56bdf6058"}}}, "nbformat": 4, "nbformat_minor": 5}