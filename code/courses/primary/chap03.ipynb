{"cells": [{"cell_type": "markdown", "id": "a83fa115", "metadata": {}, "source": ["---\n", "title: 数据处理基础\n", "---\n", "## 1. <PERSON><PERSON><PERSON>\n", "### 1.1. <PERSON><PERSON><PERSON>\n", "### 1.2. <PERSON><PERSON>\n", "## 2. <PERSON><PERSON><PERSON>\n", "### 2.1. 安装<PERSON><PERSON><PERSON>\n", "#### 2.1.1. Unubtu/Linux下安装Talib\n", "#### 2.1.2. Windows下安装Talib\n", "### 2.2. 常见指标函数\n", "### 2.3. 模式识别函数\n", "## 3. 数字信号处理\n", "### 3.1. zigzag与顶底标记\n", "### 3.2. 一维聚类与平台突破\n", "### 3.3. 曲线（直线）拟合\n", "### 3.4. 凹凸性（拐头）判断\n", "## 4. 数学基础\n", "### 4.1. 导数\n", "### 4.2. 概率密度函数\n", "### 4.3. 相关性"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.10 (default, Mar 13 2023, 10:26:41) \n[GCC 9.4.0]"}, "vscode": {"interpreter": {"hash": "31f2aee4e71d21fbe5cf8b01ff0e069b9275f58929596ceb00d14d90e3e16cd6"}}}, "nbformat": 4, "nbformat_minor": 5}