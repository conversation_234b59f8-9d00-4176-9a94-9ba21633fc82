{"cells": [{"cell_type": "markdown", "id": "60877a59", "metadata": {}, "source": ["---\n", "title: 量化交易简介及本课程的定位\n", "---\n", "\n", "什么是量化交易？从维基百科词条的收录情况来看，量化交易这个概念还没有一个权威的定义：目前还只有高频交易和算法交易被收录了。\n", "\n", "可以认为，**量化交易是指通过建立数学模型和利用计算机程序来分析、预测金融市场走势的方法**。在量化分析中，投资者使用计算机编写程序，以历史市场数据为基础，通过建立模型来判断市场趋势和进行投资决策。其优势是快速处理大量信息、减少主观因素的干扰。**量化交易的本质特征是，在执行交易前，对该笔交易可能产生的风险和收益都有一个明确的数学期望**，而不是像主观交易那样：尽管我们也可以做出收益预估，但无法同时确定收益的幅度、时间和实现概率。\n", "\n", "量化分析使用了统计分析、时间序列分析、机器学习、人工神经网络等技术。这些技术可以帮助投资者高效地从海量的信息中找出资产价格波动的规律。\n", "\n", "尽管量化交易的出现较晚，但人类对资产证券进行量化分析由来以久。一般认为，朱尔斯·雷格纳特算是量化分析的第一人。他出生于1834年，在巴黎证券交易所担任经纪人助理期间，他对股票价格数据进行了大量的数据分析，发现股票价格涨跌的背后隐藏的规律。他利用这些规律在47岁左右就实现了财富自由。随后，他把这些分析方法写成了《概率计算与股票交易哲学》一书。雷格纳特在两个世纪之前，就以自己的实践证明了量化分析在证券交易中的重要价值。\n", "\n", "全球范围内的量化交易可以追溯到上市纪70年代。不过，量化交易真正走入大众视野，可能要从80年代起，西蒙斯创建文艺复兴，或者从美国证券交易委员会（SEC）在1988年允许电子交易开始的。\n", "\n", "国内的量化交易史可以追溯到2005年8月。当时证监会批准了首只以量化投资策略为主的股票型基金——嘉实元和稳健成长证券投资基金成立。2014年，中国证券投资基金业协会发布了《量化投资基金管理办法》，这是A股量化投资行业的重要里程碑，标志着该行业进入了规范化阶段。同时，随着科技的进步和数据的积累，出现了越来越多的量化投资公司和基金。\n", "\n", "今天，国内证券市场的量化渗透率在30%左右，国外量化渗透率大约在85%上下，这一数据表明，当下是进入量化行业最好的时机。\n", "\n", "## 1. 量化交易策略分类\n", "\n", "我们很难对量化交易策略进行一个系统和完备的分类。分类标准很多，分类边界之间相互渗透进入，而且新的策略还在不断探索和发现中。这里谨给出我们自己的看法。\n", "\n", "### 1.1. 对冲策略\n", "\n", "对冲是通过**同时做空和做多多个相关**金融资产的交易策略，其目标是**减少或消除市场风险**。又可细分为套期保值、配对交易和期权交易等。\n", "\n", "对冲策略有着坚实的金融理论基础，这些理论包括现代投资组合理论、资本资产定价模型和期权定价模型等。\n", "\n", "现代投资组合理论1950年由马科维茨发表。该理论认为，通过在多个资产之间分散投资，可以在不增加风险的情况下提高收益。马科维茨将统计学中的数学期望和概率等概念引入金融投资，为其学说打下了坚实的理论基础，也为现代金融学理论铺下了奠基石。\n", "\n", "资本资产定价模型（CAPM）由美国经济学家William <PERSON>、<PERSON>和Jan <PERSON>在1960年代提出，提供了一种衡量资产预期收益率的方法，该模型将资产的预期收益率与市场风险紧密联系起来。我们熟知的衡量投资策略的指标——夏普率，也正是以这位经济学家的名字来命名的。\n", "\n", "期权定价模型提供了一种计算期权价值的方法，该模型基于期权价格的变化和所涉及的风险因素来计算期权的合理价值。1973年由Fisher Black， Myron Scholes和Robert Merton共同提出，最终由Scholes和Merton获得诺贝尔经济学奖。\n", "\n", "这三大理论，加上有效市场假说和MM定理与公司财务，就奠定了现代金融理论的四梁八柱。\n", "\n", "![](https://images.jieyu.ai/images/2023/03/20230310170203.png)\n", "\n", "这些理论我们这里不做进一步讨论，但在授课过程中，我们可能会提到它们的一些重要观点。对系统研究这些理论感兴趣的同学，可以看看我们列出的参考书目。\n", "\n", "对冲策略是大型投资机构的必备策略。全球排名靠前的基金公司多数是对冲基金，包括桥水、文芝复兴等等。\n", "\n", "### 1.2. 高频套利\n", "\n", "高频套利是一种抢帽子的策略。高频利策略比较简单，容易理解，因此，我们在这里略微介绍一下。假设有下面的委买委卖单（order book)：\n", "\n", "![](https://images.jieyu.ai/images/2023/03/20230308155751.png){: .img-center-50}\n", "\n", "在这个委买委卖单中，是不可能成交的。但是，如果有一个做市商，同时下了如下的一个买单和卖单：\n", "\n", "![](https://images.jieyu.ai/images/2023/03/20230308163339.png){: .img-center-50}\n", "\n", "这样他就以9.8元的价格持有了该品种的5手股票。如果有人想主动买入该品种，无疑该做市商卖一的5手最先成交，因此，在这样一笔交易中，该做市商就获得了接近2%的收益（不算手续费）。当然这里有一个前提，就是股价不能大幅波动。如果股价大幅向下波动的话，这笔做市单就会赔掉。此时，算法一般会根据风控要求，在第一时间，小亏出局。\n", "\n", "这里讲的，只是最简单的高频套利，它只利用了公开信息。还有利用交易所报价协议，从未公开信息中进行套利的策略。此外，还有跨交易所的价差套利，这在数字货币领域是比较常见的。\n", "\n", "高频套利本质上是同时挂买单和卖单，中间赚差价。高频套利策略简单易懂，确定性高，风险较低，但它对速度要求很高。现在做这项套利的，一般都用上了FPGA，还把计算中心搭建在交易所旁边，以期获得最小的网络延时。在我们的参考书目中，就有讲述这方面的故事的。\n", "\n", "### 1.3. 技术分析类策略\n", "\n", "我们把趋势跟踪、动量策略和均值回归等策略统一归类到技术分析类。具体来说，趋势跟踪有商品期货中的CTA策略。技术指标中很多是动量策略，以及Alpha 101中的许多因子也是动量策略，剩下的部分则主要是均值回归策略，比如利用RSI/WR来寻找顶和底等。\n", "\n", "技术分析类策略非常适合量化投资初学者。这一类策略的原理来自于自然或者生活中的哲理、以及交易心理学。比如，物理运动有这样的特点，运动的物体具有运动惯性 -- 运用到投资上，就是趋势一旦形成，就很可能持续一段时间，这就是趋势跟踪；另一方面，做周期运动的物体，当它运动到周期的尽头时，就会产生反转。表现在股价上，就是涨到一定程度，就会发生回归；跌到一定程度，也会发生回归，这就是价值回归。\n", "\n", "尽管技术分析类策略不像对冲策略那样有坚实的理论基础，但投资在很大程度上，就是人与人之间的博弈，本来也就很难用数学公式来刻画人的贪婪与恐惧。所以在小规模的资金量上，遇上恰当的时机，这些策略也非常好用，往往能创造出高收益的奇迹。\n", "\n", "作为投资者，我们应该如何进入策略的研究呢？\n", "\n", "首先我们要学习和熟知市场上流行过和正在流行的策略。为什么呢？交易策略对时机和标的有选择性，就像时装的流行一样，存在着周期轮回现象。所以今天失效的策略，也许在下一个周期就会重新发挥作用。其次，在旧的标的上失去光芒的策略，也可能在某些新兴标的上大放异彩。\n", "\n", "此外，我们学习他人的策略，更要注意探究这些策略背后的原理，分析方法和技巧。策略可能一时失效，但如果我们掌握了策略背后的原理和方法，就有可能结合当前市场的特点，找到新的交易圣杯。比如，我们可以对参数进行调优，或者将多个简单策略（因子）组合起来，构成更复杂的策略。\n", "\n", "最后，我们也要擅于利用最新的技术，这在人工智能时代有更重要的意义。过去，因子的挖掘都是精通数学和金融的分析师才能干的活。今天，普通人也可能利用机器学习，对因子进行暴力挖掘。只要效果好，不用在乎现象背后的深层次驱动原理。关于这一点，人工智能已经在图像领域充分证明过了。\n", "\n", "## 2. 本课程的定位\n", "\n", "我们对量化交易进了一番全景式鸟瞰后，学员可能要问，我们正在学习的这门课程，它在量化交易中又处于什么样的定位呢？\n", "\n", "一开始我们就讲过，量化交易是通过建立数学模型和利用计算机程序来分析金融市场走势的方法。对金融市场进行数学建模，是策略研究的范畴；而计算机程序则是手段，是登堂入室的门槛。\n", "\n", "我们这门课，就是要把学员引进量化交易的宝殿，让大家具备独立进行量化策略研究所需要的编程相关的技能。\n", "\n", "这门课共分六个部分。\n", "\n", "第一部分，我们介绍如何获取数据。我们介绍的数据源，从免费到付费的都有，适合不同需要的人。这些数据源包括了Akshare，tushare，聚宽， Baostock和yfinance等等。在这部分的最后，我们介绍了我们自己开发的大富翁框架在数据方面的用法。在课程的学习过程中，我们将使用大富翁提供的数据，这样可以为学员提供一个稳定、免费和实时的数据。\n", "\n", "学完这一部分，你将掌握获取证券列表、k线数据以及对交易日历进行运算等基本操作。\n", "\n", "第二部分，我们将介绍一些常见的策略。其目标是帮助大家练习和巩固第一阶段学习到的知识，并为下一阶段——深入数据处理进行铺垫。这里我们会介绍横扫A股十余年的小市值策略、80年代傲视群视的布林带策略和一个简单的利用概率进行套利的策略。\n", "\n", "第三部分，我们将介绍数据处理的一些基础知识。尽管我们在这里称之为基础，但相对于其它同类课程，可能会更有深度，毕竟作为量化框架的开发者，在这个话题上显然更有实战经验。我们将先介绍Numpy和Pandas这两个常用的数据分析工具；然后是ta-lib这个几乎无人不知的指标分析工具，我们会在这里介绍部分指标函数，以及部分在有效性排名上居前列的模式识别函数。接下来，我们会稍微提高一点难度，介绍几个有用的数字信号处理方法。在这一部分的最后，我们还会带大家回顾一下一些简单和常用的数学知识。\n", "\n", "第四部分，我们将介绍数据可视化的内容。我们对策略进行评估时，会用到很多指标。要理解和比较这些指标，绘图是更直观和更容易理解的方式。另外，当策略发出买卖点信号时，我们在很多情况下，都希望将买卖点标识在行情k线图上，以确定这些买卖点是否还有优化空间。因此，可视化是量化交易开发中必不可少的技能。我们将从制图原理讲起，分类介绍几个常用的制图工具。在介绍这些工具时，我们都会以k线图绘制为例。\n", "\n", "第五部分，我们将介绍回测。能够利用历史数据进行回测，从而预测未来，是量化最根本的特征。在这一部分，我们将首先介绍评价策略优劣的常见误区，进而介绍最常用的策略评估指标。然后我们将介绍一个广泛使用、可以一键将这些指标可视化展示的一个Python库；在做完这些准备之后，我们将讨论回测的两种主流驱动模型，以及应该如何编写对应的代码。最后，我们还将介绍回测陷阱，避开这些陷阱，才能保证从回测到实盘的收益一致性。\n", "\n", "第六部分可能是本课程最轻松的内容。我们将介绍几种接入实盘的方式，比较它们的优劣，以便大家根据需要来选用。\n", "\n", "在这门课程中，我们尽管只涉及到了少量的策略，但为大家编写技术分析类策略，进行了足够充分的准备和入门引导。我们的策略研发类课程也在积极准备中，欢迎大家持续关注。\n", "\n", "## 3. 如何学习本课程\n", "\n", "### 3.1. 知识储备\n", "这门课的内容以A股市场的股票投资为主。学员应该具备基本的理财知识，对股票交易的相关法律法规和交易制度有一定了解。\n", "\n", "此外，学员应该具备基础的Python知识，比如至少写过500行以上的代码，实现过一些完整的功能，有一定的调试能力。在本课程中，我们使用Python 3.8以上的版本。有一些语法，比如asyncio， 类型注解等，学员如果还不熟悉的，需要自学一下。\n", "\n", "如果学员在上课之前，还不满足上述知识储备要求，可以根据我们列出的参考资料，在课前进行预习。\n", "\n", "### 3.2. 在线实验环境\n", "\n", "为了确保大家尽快进入学习状态，不把时间花在环境和查错上，我们为开设这门课，提供了一个免安装的在线实验环境。每个学员在报名后，都将分配到一个Jupyter Hub的账号。使用这个账号，就可以登录到coursea.jieyu.ai网站上，跟随老师的讲课，进行练习。\n", "\n", "我们使用的技术是Jupyter Lab，有过人工智能学习经验的同学可能比较熟悉了。我们向其它同学做一个简单的介绍。Jupyter Lab是探索式编程的利器。可以把Jupyter Lab看成按单元格组织的网页。在每一个单元格，我们都可以书写代码片段，或者是描述性文字。如果是代码，其运行结果将展示在单元格下方。因此，我们可以通过Jupyter Lab来编写策略，得到策略运行的资产曲线图，并且将说明文字、代码及运行结果分享给团队中的其它人。\n", "\n", "有一些同学可能对Jupyer Notebook比较熟悉。我们使用Jupyer Lab，是考虑到Jupyer社区已经准备放弃Jupyer Notebook了。为了保证大家今后的衔接，所以直接使用了Jupyter Lab。\n", "\n", "下面，我们简单介绍一下，我们的实验环境如何使用。当学员拿到自己的账号后，请在浏览器中输入coursea.jieyu.ai/{username}这个地址（将{username}替换成为自己的账号）回车后，将出现下面的界面：\n", "\n", "![](https://images.jieyu.ai/images/2023/03/20230314181220.png){: .img-center-75}\n", "\n", "输入自己的用户名和密码，点击“Log in\"，系统会处理你的登录请求，为你创建一个个人专属的实验环境。因此，这一步可能需要等待十秒左右。最后，你将进入到以下界面：\n", "\n", "![](https://images.jieyu.ai/images/2023/03/20230310112849.png){: .img-center-75}\n", "\n", "在我们上课时，右边的文件区已经包含了这门课的课件。学员在上课时，需要打开它，并且跟随老师的讲解进行操作。现在，我们打开其中的一个文件：\n", "\n", "![](https://images.jieyu.ai/images/2023/03/20230310113821.png){: .img-center-75}\n", "\n", "在上图中，我们看到有Markdown类型的单元格，也有代码单元格，还有代码执行的输出单元。在每个单元格的右上侧，都有一个工具条，用来复制、移动、增加和删除单元格。单元格有所谓的命令模式和编辑模式。如果我们要运行某个单元格，可以点击顶部工具条上的运行键，也可以使用快捷键（Ctrl + Enter）。\n", "\n", "学习代码最好的方式之一，就是自己敲一遍代码，运行一下，改改参数，看看它的结果。在这么做之前，建议大家对原教材先备份，再修改。如果我们对某个函数的用法不太熟，可以将光标定位到该函数上，再按Shift + Tab键，这样就会出现如下帮助文档：\n", "\n", "![](https://images.jieyu.ai/images/2023/03/20230310115001.png){: .img-center-75}\n", "\n", "![](https://images.jieyu.ai/images/2023/03/20230313103423.png)\n", "\n", "注意，我们的课件和示例都是只读的，学员可以修改并运行，但无法保存。如果你需要保存自己的修改，可以先将课件/示例的notebook文档拷贝到根目录，在这里的文件是可以修改并保存的。\n", "\n", "## 4. 小结\n", "\n", "在结束今天的课程之前，我们来回顾一下今天所学的内容。\n", "\n", "我们首先简单介绍了量化交易的定义和本质特征，量化交易的起源及一些重要的金融理论。随后，我们介绍了本课程在量化交易中的定位和学习要求。最后，我们介绍了如何使用在线实验环境。\n", "\n", "## 5. 参考资料\n", "请见[参考资料](https://www.jieyu.ai/电子书)"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}