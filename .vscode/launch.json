{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
        {
            "name": "Python: deploy",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/convert.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "deploy",
                "l24",
                "docs/24lectures/课程指南.md"
            ]
        },
        {
            "name": "Python: summary",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/summary.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "preprocess",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/convert.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "preprocess",
                "docs/factor-analysis/courseware/18.md",
                "/tmp/footnotes.md"
            ]
        },
        {
            "name": "Python: subtitles",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/convert.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "subtitles",
                "lesson24",
                "1",
                "5"
            ]
        },
        {
            "name": "Python: toc",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/toc.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "Python: magic",
            "type": "python",
            "request": "launch",
            "program": "~/workspace/cheese_course/docs/24lectures/supplements/test.py",
            "console": "integratedTerminal",
            "justMyCode": false,
        },
        {
            "name": "Python: provision",
            "type": "python",
            "request": "launch",
            "program": "~/workspace/cheese_course/scripts/provision.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "register",
                "fa",
                "aaron",
                "aaron@wx",
                "--teacher"
            ]
        },
        {
            "name": "Python: preview",
            "type": "python",
            "request": "launch",
            "program": "~/workspace/cheese_course/scripts/convert.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "preview",
                "docs/factor-analysis/courseware/12.md"
            ]
        },
        {
            "name": "Python: notebook",
            "type": "python",
            "request": "launch",
            "program": "~/workspace/cheese_course/scripts/convert.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "notebook",
                "~/workspace/cheese_course/docs/factor-analysis/courseware/02.md"
            ]
        },
        {
            "name": "Transcript",
            "type": "python",
            "request": "launch",
            "program": "~/workspace/cheese_course/scripts/transcript.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "cwd": "${workspaceFolder}/",
            "args": [
                "transcript",
                "/Volumes/share/data/autobackup/ke/factor-ml/test/raw.mp4"
            ]
        },
        {
            "name": "cut",
            "type": "python",
            "request": "launch",
            "program": "~/workspace/cheese_course/scripts/transcript.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "cut",
                "/tmp/test"
            ]
        },
        {
            "name": "merge",
            "type": "python",
            "request": "launch",
            "program": "~/workspace/cheese_course/scripts/transcript.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "merge",
                "/tmp/fa-17",
                "/Volumes/share/data/autobackup/ke/factor-ml/fa-17"
            ]
        },
        {
            "name": "test",
            "type": "python",
            "request": "launch",
            "program": "~/workspace/cheese_course/scripts/transcript.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "test"
            ]
        },
        {
            "name": "assignment",
            "type": "python",
            "request": "launch",
            "program": "~/workspace/cheese_course/scripts/convert.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "assignment",
                "fa",
                "docs/factor-analysis/assignments/01.md",
                "--remote"
            ]
        },
    ]
}
