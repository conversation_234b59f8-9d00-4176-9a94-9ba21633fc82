#! /home/<USER>/miniconda3/envs/coursea/bin/python
"""
deploy courseware (teacher/student) to coursea.jieyu.ai

转换md为pdf命令，在mac下运行：

pandoc --pdf-engine=weasyprint -f markdown -t pdf  -V mainfont='WenQuanYi Micro Hei' -V geometry:"top=2cm,bottom=2cm,left=2cm,right=2cm" input.md  > output.pdf

"""

import json
import logging
import os
import re
import shutil
import sys
from pathlib import Path
from typing import List, Optional
from urllib.parse import parse_qs, urlparse

import arrow
import black
import fire
import frontmatter
import nbformat
import paramiko
from rich import print

logging.basicConfig(level=logging.INFO)

host = "*************"
logger = logging.getLogger("tonotebook")
mystAdmons = {
    "info": "hint",
    "hint": "hint",
    "warning": "warning",
    "attention": "warning",
    "note": "note",
    "tip": "tip",
    "failure": "error",
    "more": "seealso",
    "important": "important",
    "bug": "error",
}

courses = {"l24": "24lectures", "fa": "factor-analysis", "pandas": "量化交易中的Numpy和Pandas", "blog":"blog"}

# project home, 即~/workspace/cheese_course
workspace = Path(__file__).parent.parent
remote_home = Path("~/courses")


class Remote:
    def __init__(self, ip: str, user: str, keyfile: str):
        print(ip, user, keyfile)
        self.client = paramiko.SSHClient()
        self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.client.load_host_keys(os.path.expanduser("~/.ssh/known_hosts"))
        self.client.connect(ip, username=user, key_filename=keyfile, port=22101)

    def execute(self, cmd: str):
        """在远程机器上执行命令。需要事先配置免密登录"""
        _, ssh_stdout, error = self.client.exec_command(cmd)
        status = ssh_stdout.channel.recv_exit_status()

        for line in error:
            print(line)

        for line in ssh_stdout:
            yield line

        return status


def guess_chap_from_filename(filename: Path):
    matched = re.search(r"(\d+)", str(filename))
    if matched is not None:
        return int(matched.group(1))


def to_html_image(lines: List[str], chap: int):
    """
    ![caption](http://link?width=30%&align=left)

    转换为:

    <div id='' style='width:"?";float:left'>
        <img src=''>
        <div style='color: #808080;font-size:0.8em'></div>
    </div>

    并可在其它地方通过[][#caption]来引用
    """
    seq = 0

    buffer = []
    pattern = r"\s*!\[(.*?)\]\((.*?)\)"
    template = (
        "<div id='{id_}' style='width:{width};{float};text-align:center'>"
        + "<img src='{url}' {marginWhenFloat}>"
        + "<span style='color: #808080;font-size:0.8em'>{alt_text}</span>"
        + "</div>"
    )

    id_map = {}
    # 查找图片、替换成html
    for line in lines:
        matched = re.match(pattern, line)
        if matched is not None:
            seq += 1
            # alt_text = f"图{chap}-{seq} {matched.group(1)}"
            title = matched.group(1)
            tag = f"图{chap}-{seq}"
            alt_text = f"{tag} {title}"

            url = matched.group(2)

            # 给id增加编号
            id_ = f"fig-{chap}-{seq}"

            # 保存新旧锚点映射
            key = matched.group(1)
            id_map[key] = (id_, tag)

            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)

            width = query_params.get("width", ["80%"])[0]
            if not (width.endswith("%") or width.endswith("px")):
                width += "px"
            float_ = query_params.get("float", ["margin:1em auto"])[0]
            marginWhenFloat = ""
            if float_ == "left":
                float_ = f"float: left; margin: 0em 2em 1em 0"
                marginWhenFloat = "style='margin-top: 2em'"
            if float_ == "right":
                float_ = f"float: right; margin:0 em 0 2em 1em"
                marginWhenFloat = "style='margin-top: 2em'"

            line = template.format(
                id_=id_,
                url=url,
                alt_text=alt_text,
                width=width,
                float=float_,
                marginWhenFloat=marginWhenFloat,
            )

        buffer.append(line)

    # 查找图像锚点并更新
    lines = []
    pattern = "\\[([^[]+?)\\]\\(#([^#)]+)\\)"

    def replace_match(match):
        name = match.group(1)
        if name.endswith("</sup>"):  # 这是脚注
            return match.group(0)

        anchor = match.group(2)
        if anchor.startswith("example-"):
            return f"[](#{anchor})"

        new_anchor = id_map.get(anchor, anchor)[0].lower()
        alt_text = id_map.get(anchor, anchor)[1]
        return f" [{alt_text}](#{new_anchor}) "

    for line in buffer:
        line_ = re.sub(pattern, replace_match, line)
        lines.append(line_)

    return lines


def insert_seq_to_code(i, lines, text: str):
    """
    在第i行之后的第一个```之后，插入text
    """
    i += 1
    for j in range(i, len(lines)):
        if lines[j].startswith("```"):
            if i == j or "".join(lines[i:j]) == "":
                lines[j + 1] = text + "\n" + lines[j + 1]
                break

        if j - i > 5:
            print("警告：<Example标记之后，未能立即找到```python，请检查")


def format_code_blocks_in_markdown(content: str):
    # 使用字符串拼接避免 Black 格式化问题
    triple_backticks = "```"
    code_block_pattern = re.compile(
        rf"{triple_backticks}\s*python(.*?){triple_backticks}python", re.DOTALL
    )

    def format_match(match):
        code = match.group(1).strip()
        try:
            # 使用 Black 格式化代码
            formatted_code = black.format_str(code, mode=black.FileMode())
            # 使用字符串拼接避免 Black 格式化问题
            triple_backticks = "```"
            return f"{triple_backticks}python\n{formatted_code}\n{triple_backticks}python"
        except Exception as e:
            print(f"Error formatting code block: {e}")
            return match.group(0)

    # 替换所有代码块
    formatted_content = code_block_pattern.sub(format_match, content)
    return formatted_content


def mark_code_snippets(lines: List[str], chap: int):
    """
    1. 将<Example id="?"/>转换为[](#example-chap-seq)，并且更新相应的链接，
    找到邻接的```并在其后插入 示例-chap-seq
 如果找到类似[示例 4-1](04.md#example-4-1)，将其转换为[示例 4-1](04.ipynb#example-4-1)
    """
    seq = 0
    pattern = r"\<Example\sid=(.+)\/\>"
    anchors = {}
    buffer = []
    for i, line in enumerate(lines):
        matched = re.match(pattern, line)
        if matched is not None:
            seq += 1
            id_ = f"example-{chap}-{seq}"
            anchors[matched.group(1)] = id_
            buffer.append(
                f'<div id="{id_}" style="margin-top:-3em;opacity:0.05">示例-{chap}-{seq}</div>'
            )
            insert_seq_to_code(i, lines, f"#示例 {chap}-{seq}")
        else:
            old = r"\[(.*?)\]\((\d+)\.md#(.*?)\)"
            new = r"[\1](\2.ipynb#\3)"
            line = re.sub(old, new, line)
            buffer.append(line)

    lines = []
    pattern = r"\[\]\(#example-(.*?)\)"

    def replace_match(match):
        anchor = match.group(1)
        new_anchor = anchors.get(anchor, anchor).lower()
        alt_text = new_anchor.replace("example-", "示例 ")
        return f" [{alt_text}](#{new_anchor}) "

    for line in buffer:
        line_ = re.sub(pattern, replace_match, line)
        lines.append(line_)

    return lines

def seek_adnomition_end(i, lines):
    """
    寻找 admonition 块的结束位置
    规则：遇到连续两个空行时结束，但要忽略 fenced code blocks 内部的空行
    """
    in_fenced_block = False
    # 使用字符串拼接避免 Black 格式化问题
    triple_backticks = "```"
    fenced_pattern = re.compile(rf'^\s*{triple_backticks}')
    consecutive_empty_lines = 0
    
    for m in range(i, len(lines)):
        line = lines[m]
        
        # 检查是否进入或退出 fenced code block
        if fenced_pattern.match(line):
            in_fenced_block = not in_fenced_block
            consecutive_empty_lines = 0  # 重置空行计数
            continue
        
        # 如果不在 fenced code block 内，检查空行
        if not in_fenced_block:
            if line == "":
                consecutive_empty_lines += 1
                # 如果遇到连续两个空行，则结束 admonition
                if consecutive_empty_lines >= 2:
                    return m - 1  # 返回第一个空行的位置
            else:
                consecutive_empty_lines = 0  # 重置空行计数
        
    return len(lines)


def convert_footnotes(text: str) -> str:
    """当转换markdown为ipynb时，标准格式的footnote转换后的无法被myst渲染，需要处理成<span id="_id">content</span>格式

    本方法要求文章结尾处必须有## footnotes存在，且footnotes必须出现在文章末

    Args:
        text: the whole content of the article
    """
    result = re.search(r"#.*\sFootnotes", text, re.I)
    if result is None:
        print("[red]No footnotes found.[/red]")
        return

    istart = result.span()[1]
    imain_end = result.span()[0]

    footnotes = {}
    id_pattern = r"\[\^(?P<key>.*?)\]:\s*(?P<content>.*)"
    link_pattern = r"\[(?P<caption>[^\]]+)\]\((?P<link>[^\)]+)\)"

    for line in text[istart:].split("\n"):
        matches = re.search(id_pattern, line)
        if matches is not None:
            id_ = matches.group("key")
            content = matches.group("content")

            links = re.findall(link_pattern, content)
            for caption, link in links:
                content = content.replace(
                    f"[{caption}]({link})", f"<a href='{link}'>{caption}</a>"
                )
            footnotes[id_] = content

    main = []
    rebuilt = []
    pat_link = r"\[\^([^\]]+)\]"
    seq = 1
    for line in text[:imain_end].split("\n"):
        items = re.findall(pat_link, line)
        if len(items) == 0:
            main.append(line)
        else:
            for item in items:
                line = line.replace(f"[^{item}]", f"[<sup>[{seq}]</sup>](#{item})")
                rebuilt.append(
                    f"<div id='{item}' style='margin-bottom:1em'><span style='margin-right:0.5em'>{seq}.</span>{footnotes.get(item)}</div>"
                )
                seq += 1
            main.append(line)

    splitter = """<h2 style="border-bottom: 1px solid gray; width:7rem;padding:5rem 0 0.5rem">Footnotes
    </h2>\n\n"""

    return "\n".join(main) + splitter + "\n".join(rebuilt)


def replace_adnomition(lines, i, m):
    """
    将 admonition 转换为 myst 格式
    处理 fenced code blocks：将 ```python 转换为 ````python
    """
    matched = re.search(r"(tip|warning|note|attention|hint|more)", lines[i], flags=re.I)
    tag = "note"
    if matched is not None:
        tag = mystAdmons.get(matched.group(1).lower())

    # 处理内容，移除 admonition 缩进并转换 fenced code blocks
    content = []
    # 使用字符串拼接避免 Black 格式化问题
    triple_backticks = "```"
    fenced_pattern = re.compile(rf'^(\s*)({triple_backticks})(.*)')

    for line in lines[i + 1 : m]:
        # 移除 admonition 的缩进（4个空格或1个制表符）
        if line.startswith("    "):
            processed_line = line[4:]
        elif line.startswith("\t"):
            processed_line = line[1:]
        else:
            processed_line = line

        # 检查是否是 fenced code block 标记
        fenced_match = fenced_pattern.match(processed_line)
        if fenced_match:
            # 将 ```转换为````（增加一个反引号）
            indent, backticks, rest = fenced_match.groups()
            processed_line = f"{indent}`{backticks}{rest}"

        content.append(processed_line)

    return [f"```{{{tag}}}", *content, "```"]


def to_myst_adnomition(lines: List[str]):
    buffer = []
    i: int = 0

    while i < len(lines):
        line = lines[i]
        if line.startswith("!!!"):
            m = seek_adnomition_end(i + 1, lines)
            repl = replace_adnomition(lines, i, m)

            buffer.extend(repl)
            i = m
        else:
            buffer.append(line)
            i += 1

    return buffer


def process_front_matter(content: str) -> tuple[dict, str]:
    post = frontmatter.loads(content)

    title = post.metadata.get("title", "")
    content = post.content
    if not content.startswith("#"):
        content = f"# {title}\n\n{post.content}"
    else:
        if not title:
            title = content.split("\n")[0].lstrip("#").strip()
            post.metadata["title"] = title

    return post.metadata, content


def get_copyrights() -> str:
    copyright = """\n\n---\n\n<b>版权声明</b><br>本课程全部文字、图片、代码、习题等所有材料，除声明引用外，版权归<b>匡醍</b>所有。所有草稿版本均通过第三方git服务进行管理，作为拥有版权的证明。未经作者书面授权，请勿引用和传播。联系我们：公众号 Quantide"""

    return copyright


def strip_html_comments(content: str) -> str:
    return re.sub(r"<!--.*?-->", "", content, flags=re.DOTALL)


def to_pandoc_code_block(lines: List[str]):
    """pandoc 转换markdown为ipynb要求代码段使用`code`，而不是`python`

    暂时未使用
    """
    buffer = []
    for line in lines:
        replaced = re.sub(r"^```\s?python", "```code", line)
        buffer.append(replaced)

    return buffer


def strip_output(content: str) -> str:
    """部分输出结果在文章页面是以图片展示的。转换为ipynb前，需要去掉"""
    pattern = r"<!-- BEGIN IPYNB STRIPOUT -->.*?<!-- END IPYNB STRIPOUT -->"
    # 使用 re.sub 替换匹配的内容
    return re.sub(pattern, "", content, flags=re.DOTALL)


def preprocess(in_file: Path, out_file: Path):
    chap = guess_chap_from_filename(in_file)

    with open(in_file, "r") as f:
        content = f.read()
        meta, content = process_front_matter(content)
        content = strip_output(content)
        content = strip_html_comments(content)
        content = format_code_blocks_in_markdown(content)

        c2 = convert_footnotes(content)
        if c2 is not None:
            lines = c2.split("\n")
        else:
            lines = content.split("\n")

        lines = to_myst_adnomition(lines)

        # 将markdown格式的image转换为html格式
        lines = to_html_image(lines, chap)

        # 给<Example>示例编号
        lines = mark_code_snippets(lines, chap)

        with open(out_file, "w", encoding="utf-8") as f:
            content = "\n".join(lines)
            f.write(content)

            if content.find("版权声明") == -1:
                f.write(get_copyrights())

        return meta

def add_meta(meta: dict, notebook: Path):
    if meta.get("date") is None:
        mtime = arrow.get(notebook.stat().st_mtime, tzinfo="Asia/Shanghai")
        meta["date"] = mtime.format("YYYY-MM-DD HH:mm:ss")

    nb = nbformat.read(notebook, as_version=4)
    nb_meta = nb.get("metadata")
    nb_meta.update(meta)

    new_notebook = nbformat.v4.new_notebook(
        cells = nb.cells,
        metadata = nb_meta,
    )

    nbformat.write(new_notebook, notebook)


def convert_to_ipynb(in_file: Path, out_file: Optional[str | Path] = None) -> Path:
    """将markdown 转换为notebook格式"""
    in_file = Path(in_file)
    if out_file is not None:
        out_file = Path(out_file)
    stem = os.path.splitext(os.path.basename(in_file))[0]

    preprocessed = Path("/tmp/") / f"{stem}.md"
    meta = preprocess(in_file, preprocessed)

    if out_file is None:
        out_file = in_file.with_suffix(".ipynb")
    else:
        out_file = Path(out_file)

    print(f"converting {preprocessed} to {out_file}")

    os.system(f"notedown --match=python {preprocessed} -o {out_file}")
    add_meta(meta, out_file)

    return out_file


def preview(course: str, file: str, user: str):
    """将docs/下的文件，部署到remote机器上course_{course}_{}下的wip目录"""
    if not file.startswith("docs/"):
        print(f"[red]文件必须在docs目录下:{file}[/red]")
        return

    if courses.get(course) is None:
        print(f"[red]course {course} not found[/red]")
        sys.exit(1)

    course_home = f"docs/{courses.get(course)}/"
    to = remote_home / course / user / (file.replace(course_home, ""))

    src = workspace / file
    if src.suffix == ".md":
        src = convert_to_ipynb(src.resolve())
        to = to.with_suffix(".ipynb")

    print(f"copy {src} to omega:{to}")
    os.system(f"scp {src} omega:{to}")


def deploy(
    course: str, file: str, to: str | Path | None = None, convert_md: bool = True
):
    """将docs/*/*下的文件，部署到remote机器上~/courses/{course}/下的同名文件夹中，或者to指定的文件夹"""
    if not file.startswith("docs/"):
        print(f"[red]只能部署docs目录下的文件:{file}[/red]")
        return

    if courses.get(course) is None:
        print(f"[red]course {course} not found[/red]")
        sys.exit(1)

    path = workspace / file  # type: ignore

    if file.endswith(".md") and convert_md:
        src = convert_to_ipynb(path)
        file = file.replace(".md", ".ipynb")
    else:
        src = path

    course_home = courses.get(course)

    if to is None:
        to = remote_home / file.replace(f"docs/{course_home}", course)

    print(f"scp {src} to remote omega:{to}")
    os.system(f"scp {src} omega:{to}")


def _find_comments_end(lines, istart) -> int:
    for iend in range(istart, len(lines)):
        line = lines[iend]
        if re.match(r"-->\s*\n\s*", line):
            return iend

    if not lines[-1].endswith("-->"):
        raise ValueError(f"发现未配对注释：{istart}")
    else:
        raise ValueError(f"Something wrong with paring last line: {lines[-1]}")


def _split_by_punctuation(lines: List[str]):
    lines = "".join(lines)
    stripped = re.sub(r"<!--\s*", "", lines)
    stripped = re.sub(r"\s*-->\s*", "", stripped)

    trans = str.maketrans(
        {
            ",": "\n",
            "。": "\n",
            ";": "\n",
            "；": "\n",
            "，": "\n",
            "?": "?\n",
            "？": "？\n",
        }
    )

    return "\n<!--\n" + stripped.translate(trans) + "\n-->\n"


def split_lines(file: str, write_file=True):
    """对slide稿按标点符号切分成行"""
    if not file.endswith(".md"):
        file = file + ".md"

    if not file.startswith("/"):
        file = os.path.join("/apps/cheese_course/", file)
    with open(file, "r") as f:
        lines = f.readlines()

    buffer = []
    istart = 0
    while istart < len(lines):
        line = lines[istart]
        if line.startswith("<!--"):
            iend = _find_comments_end(lines, istart)
            buffer.append(_split_by_punctuation(lines[istart:iend]))
            istart = iend + 1
        else:
            istart += 1
            buffer.append(line)

    if not write_file:
        return "".join(buffer)

    basename = os.path.basename(file)
    tmp_file = os.path.join("/tmp/", basename)
    bak_file = os.path.join("/tmp/", f"{basename}.bak")
    with open(tmp_file, "w") as f:
        f.write("".join(buffer))

    shutil.copyfile(file, bak_file)
    shutil.copyfile(tmp_file, file)

    return "".join(buffer)

def assignment(course: str, file: str, remote: bool = False, debug: bool = False):
    """将作业md文件转换为notebook格式的习题和答案

    步骤为：
       1. 将{file}原文转换为{file}-answers.ipynb
       2. 搜索{file}中，python代码块中的BEGIN SOLUTION和END SOLUTION之间的代码，
    替换为 YOUR CODE HERE，然后将生成的markdown转换为{file}-questions.ipynb

    转换后的文件临时保存在/tmp/{course}/目录下。

    Args:
        course: 课程id
        file: 源文件， markdown格式
        remote: 如果为 False，则不执行部署，否则，将转换后的文件部署到指定的课程的assignments目录下。允许值为l24, fa, pandas。
        debug: debug模式下，保留当前目录下的ipynb文件和/tmp下的markdown文件
    """
    if not file.endswith(".md"):
        print("[red]file must be a markdown file[/red]")
        sys.exit(1)

    if not file.startswith("docs/"):
        print("[red]file must be under docs/[/red]")
        sys.exit(1)

    src = Path(workspace) / file

    answers_notebook = Path(f"{src.parent}/{src.stem}-answers.ipynb")
    questions_notebook = Path(f"{src.parent}/{src.stem}-questions.ipynb")

    answers_notebook.parent.mkdir(parents=True, exist_ok=True)
    questions_notebook.parent.mkdir(parents=True, exist_ok=True)

    print("answers_notebook path:", answers_notebook)
    print("questions_notebook path:", questions_notebook)

    # 1. Generate answers notebook from original file
    convert_to_ipynb(src, answers_notebook)

    # 2. 处理答案文件，为每个包含解答的单元格添加标记ID
    with open(answers_notebook, "r", encoding="utf-8") as f:
        answers_nb = json.load(f)

    # 查找所有包含 # BEGIN SOLUTION 的代码单元格并编号
    solution_cells = []
    for idx, cell in enumerate(answers_nb["cells"]):
        if cell["cell_type"] == "code" and "# BEGIN SOLUTION" in "".join(
            cell.get("source", [])
        ):
            cell_id = f"solution-cell-{len(solution_cells) + 1}"
            cell["metadata"]["id"] = cell_id
            solution_cells.append((idx, cell_id))

    # 保存修改后的答案文件
    with open(answers_notebook, "w", encoding="utf-8") as f:
        json.dump(answers_nb, f, indent=2)

    # 3. Process questions version
    with open(src, "r", encoding="utf-8") as f:
        content = f.read()

    # 获取答案文件的文件名（不含路径）
    answers_filename = Path(answers_notebook).name
    print("answers_filename", answers_filename)

    # 构造Markdown超链接文本
    ref_markdown = (
        f"\n\n[答案参考：{src.stem}.ipynb的单元格{cell_id}]"
        f"(../answers/{src.stem}.ipynb#{cell_id})\n\n"
    )

    def add_ref_links(content, src_stem):
        counter = 0

        def replacement(match):
            nonlocal counter
            counter += 1

            cell_id = f"solution-cell-{counter}"

            ref_markdown = (
                f"\n\n[答案参考：{src_stem}.ipynb的单元格{cell_id}]"
                f"(../answers/{src_stem}.ipynb#{cell_id})\n\n"
            )

            return ref_markdown + match.group(0)

        return re.sub(r"(```\s*python)", replacement, content)

    content = add_ref_links(content, src.stem)

    # Replace solution blocks with placeholders
    question_content = re.sub(
        r"#\s*BEGIN SOLUTION.*?#\s*END SOLUTION",
        """# BEGIN SOLUTION\n# 请按要求补全代码，并删除抛出异常的代码行。\nraise NotImplementedError\n#END SOLUTION""",
        content,
        flags=re.DOTALL,
    )

    # Create temp markdown for questions
    q_md = Path(f"/tmp/{course}/{src.stem}-questions.md")
    q_md.parent.mkdir(parents=True, exist_ok=True)
    with open(q_md, "w", encoding="utf-8") as f:
        f.write(question_content)

    # Convert to notebook
    to = src.with_stem(src.stem + "-questions").with_suffix(".ipynb")
    question_notebook = convert_to_ipynb(q_md, to)

    if remote:
        to = remote_home / course / "assignments/questions" / f"{src.stem}.ipynb"
        from_ = question_notebook.relative_to(workspace)
        deploy(course, str(from_), to=str(to))

        to = remote_home / course / "assignments/answers" / f"{src.stem}.ipynb"
        from_ = answers_notebook.relative_to(workspace)
        deploy(course, str(from_), to=str(to))

    if not debug:
        q_md.unlink()
        question_notebook.unlink()
        answers_notebook.unlink()


if __name__ == "__main__":
    fire.Fire(
        {
            "deploy": deploy,
            "notebook": convert_to_ipynb,
            # python scripts/convert.py preview docs/**/01.md fa aaron
            "preview": preview,
            # 将markdown格式的作业转换为两份ipynb，一份为习题，一份则包含答案
            "assignment": assignment,
            "preprocess": preprocess,
        }
    )
