import os
import re
from ctypes.wintypes import tagRECT
from textwrap import dedent

_dir = "/apps/cheese_course/docs/courseware/notes/"

IDEOGRAPHIC_SPACE = 0x3000


def is_asian(char):
    """Is the character Asian?"""
    return ord(char) > IDEOGRAPHIC_SPACE


def filter_jchars(c):
    """Filters Asian characters to spaces"""
    if is_asian(c):
        return " "
    return c


def nonj_len(word):
    """Returns number of non-Asian words in {word}
    – 日本語AアジアンB -> 2
    – hello -> 1
    @param word: A word, possibly containing Asian characters
    """
    # Here are the steps:
    # 日spam本eggs
    # -> [' ', 's', 'p', 'a', 'm', ' ', 'e', 'g', 'g', 's']
    # -> ' spam eggs'
    # -> ['spam', 'eggs']
    # The length of which is 2!
    chars = [filter_jchars(c) for c in word]
    return len("".join(chars).split())


def get_wordcount(text):
    """Get the word/character count for text

    @param text: The text of the segment
    """

    characters = len(text)
    chars_no_spaces = sum([not x.isspace() for x in text])
    asian_chars = sum([is_asian(x) for x in text])
    non_asian_words = nonj_len(text)
    words = non_asian_words + asian_chars

    return dict(
        characters=characters,
        chars_no_spaces=chars_no_spaces,
        asian_chars=asian_chars,
        non_asian_words=non_asian_words,
        words=words,
    )


def dict2obj(dictionary):
    """Transform a dictionary into an object"""

    class Obj(object):
        def __init__(self, dictionary):
            self.__dict__.update(dictionary)

    return Obj(dictionary)


def get_wordcount_obj(text):
    """Get the wordcount as an object rather than a dictionary"""
    return dict2obj(get_wordcount(text))


def count_words(_dir: str):
    """计算_dir目录中markdown文件中的词数"""
    count = {}
    for name in os.listdir(_dir):
        if name.endswith(".md"):
            path = os.path.join(_dir, name)
            with open(path, "r") as f:
                content = f.read(-1)
                if len(content) > 300:
                    count[name] = get_wordcount(content)
    print(count)


def count_chars(_dir: str):
    chars = []
    for name in os.listdir(_dir):
        if name.endswith(".md"):
            path = os.path.join(_dir, name)
            with open(path, "r") as f:
                content = f.read(-1)
                if len(content) > 300:
                    chars.append(len(content))

    return sum(chars), sum(chars) / len(chars)


def _count_code_lines(file: str):
    FENCED_BLOCK_RE = re.compile(
        dedent(
            r"""
            (?P<fence>^(?:~{3,}|`{3,}))[ ]*                          # opening fence
            ((\{(?P<attrs>[^\}\n]*)\})|                              # (optional {attrs} or
            (\.?(?P<lang>[\w#.+-]*)[ ]*)?                            # optional (.)lang
            (hl_lines=(?P<quot>"|')(?P<hl_lines>.*?)(?P=quot)[ ]*)?) # optional hl_lines)
            \n                                                       # newline (end of opening fence)
            (?P<code>.*?)(?<=\n)                                     # the code block
            (?P=fence)[ ]*$                                          # closing fence
        """
        ),
        re.MULTILINE | re.DOTALL | re.VERBOSE,
    )

    with open(file, "r") as f:
        text = f.read(-1)

        ln = 0
        snippets = 0
        for m in FENCED_BLOCK_RE.finditer(text):
            lines = m.group("code").split("\n")
            # print(lines)
            # print(len(lines))

            ln += len(lines)
            snippets += 1
        return ln, snippets


def summary_code(_dir):
    code_snippets = []
    line_numbers = []
    for name in os.listdir(_dir):
        if name.endswith(".md"):
            path = os.path.join(_dir, name)

            ln, snippets = _count_code_lines(path)
            if ln > 0:
                line_numbers.append(ln)
                code_snippets.append(snippets)

    return (
        sum(line_numbers),
        sum(line_numbers) / len(line_numbers),
        sum(code_snippets),
        sum(code_snippets) / len(code_snippets),
    )


print("chars", count_chars(_dir))
print("summary code", summary_code(_dir))
