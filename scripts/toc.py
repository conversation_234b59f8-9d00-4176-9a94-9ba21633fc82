import os
import re

import frontmatter
import md_toc

notes_dir = "/apps/cheese_course/docs/courseware/notes"

files = []


def compare_filename(name):
    ord_a = re.match(r".+第(\d+)课\.md", name)
    if ord_a is None:
        raise ValueError(f"wrong file name: {name}")

    return int(ord_a.group(1))


def valid_filename(name):
    ord_a = re.match(r".+第(\d+)课\.md", name)
    return ord_a is not None


def collect_files():
    for item in os.listdir(notes_dir):
        # files.append(os.path.join(notes_dir, file))
        if valid_filename(item):
            files.append(item)

    return sorted(files, key=compare_filename)


def extract_toc():
    files = collect_files()

    content = []
    for file in files:
        path = os.path.join(notes_dir, file)
        ord = compare_filename(file)
        try:
            post = frontmatter.load(path)
            title = post.get("title", "")
        except Exception as e:
            print("failed to parse frontmatter:", file)
            title = ""

        content.append(f"## 第{ord}课 {title}\n")
        toc = md_toc.build_toc(path, no_list_coherence=True, keep_header_levels=4)
        for item in toc.split("\n"):
            item = "  " + item.replace("[", f"[{ord}.") + "\n"
            content.append(item)

    script_dir = os.path.dirname(__file__)
    output = os.path.join(script_dir, "../docs/courseware/notes/toc.md")
    with open(output, "w") as f:
        f.writelines(content)


extract_toc()
