## 1. 课程环境

### 1.1. 创建

课程环境将使用新的工程 provision 来创建。此项目相关功能不再使用。这包括：

1. scripts/privision.*
2. scripts/containers
3. scripts/deployment
4. scripts/users


### 1.2. 课程环境故障

#### 1.2.1. 查看容器内存使用情况
```
docker stats --no-stream --format "table {{.Name}}\t{{.MemUsage}}" | awk 'NR==1; NR>1{split($2, a, "/"); print $1, a[1], a[2]}' | sort -k2,2h -k3,3hr
```

**docker允许直接映射文件，也允许将host上的目录、文件映射到容器中已存在的目录，作为子目录和文件。**

#### 1.2.2. 配置jupyter 自动关闭idle kernel

```
jupyter lab \
    --log-level=DEBUG \
    --MappingKernelManager.cull_idle_timeout=10 \
    --MappingKernelManager.cull_interval=10 \
    --MappingKernelManager.cull_connected=True
```


## 2. 课程视频剪辑

使用transcript.py，连接到 mini-two机器上运行。

需要whisper.cpp及pysub2等库。需要安装ffmpeg。

字幕处理流程如下：

1. 首先，使用剪映对影片进行粗剪，去掉片头等待、片尾空白及中间有剪辑标记的区域，导出为 h264编码，2k分辨率的视频。

### 2.1. 生成原始字幕
1. 切换为course环境
2. 调用 python scripts/transcript.py transcript /path/to/*/parent/*.mp4

在转换过程中，会自动引用words.md作为字典，进行错误纠正。转换的结果会保存在/tmp/parent目录中。parent为包含视频文章的父级目录。

### 2.2. 编辑字幕

手工，可以vscode中完成。生成的字幕需要进一步编辑，比如，发现其中未被纠正的错误，删除个别表达不通顺的地方。删除的方法是在该行字幕前加上 [del] 标记。这样再调用cut方法，这些部分就会被删除。

### 2.3. 应用字幕

调用以下命令：

python scripts/transcript.py cut /path/to/parent

在生成原始字幕阶段，如果没有指定保存目录的话，此时的目录就是默认的/tmp/parent

### 2.4. 合并剪辑

<!-- 合并cut的结果，传入工作目录，比如/tmp/14 和保存名字 -->
3. python scripts/transcript.py merge /tmp/* fa-14

### 2.5. 视频压缩

ffmpeg -i /tmp/lesson11-with-sub.mp4  -preset slow -crf 23 -c:a copy /tmp/第11课.mp4

ffmpeg -i /tmp/raw/full.mp4  -preset slow -crf 23 -c:a copy ~/139/factor-ml/fa-11/fa-11.mp4
-hwaccel auto用来decode

ffmpeg -hwaccel videotoolbox -i mysource -c:v h264_videotoolbox -c:a copy myoutput

烧入字幕："-vf",
        f'''"subtitles={out_srt}:force_style='FontName=WenQuanYi Micro Hei Light,FontSize=24,PrimaryColour=&H00FFFF&'"''',

ffmpeg -i /tmp/fa-07/full.mp4 -vf 'chromakey=0x39D172:0.07:0.1,format=yuva420p,drawbox=x=0:y=0:w=iw:h=ih:color=white@1:t=fill' -c:a copy -f mp4 -video_track_timescale 600 /tmp/output.mp4


## 3. 课程增强语法

### 3.1. 图像语法

在convert.py中实现。

![caption](http://link?width=30%&align=left)

这将转化为：
```md
    <div id='' style='width:"?";float:left'>
        <img src=''>
        <div style='color: #808080;font-size:0.8em'>caption</div>
    </div>
```
然后可以在其它地方，通过[][#caption]来引用。此语法的主要作用是方便引用。

### 3.2. 代码编号

在代码之前，加上<Example id="my-sample-code"/>，这段代码就会被转化为 示例-n，并且拥有一个anchor。

这样会在其后的```引起的代码段开头，插入一个# 示例 chap-n的注释，并在其前面，插入一个

<div id='example-3-1' style='margin-top:-3em;opacity:0.05'>示例-3-1</div>

这里chap是当前的章，n为用户提供的编号。

如果要在其它地方引用这个示例，就可以使用[](#my-sample-code)这样的语法来引用。它会被自动替换为[示例3-1](#my-sample-code).。。

### 3.3. 练习题

练习题源文件使用markdown开发。在要设计为补全代码（即练习）的地方，通过 <#BEGING SOLUTION>和<<#END SOLUTION>对进行标记。如果要验证补全代码，可以下面加上assert语句。

示例：

```python
# BEGIN SOLUTION
rf = bond[bond.index=='中债国债收益率曲线']['1年'].mean()

rf = rf / 100
# END SOLUTION

assert rf - 0.021 < 1e-4
```

练习编写完成之后，可以通过 `python scripts/convert.py docs/path/to/assignment.md` 来生成练习和答案两份notebook。在练习题的notebook中，上述#BEGIN SOLUTION 和 <#END SOLUTION>会被替换为

```
# BEGIN SOLUTION
# 请按要求补全代码，并删除抛出异常的代码行。
raise NotImplementedError
# END SOLUTION
```

### 3.4. Code Snippets

1. `imgc`。插入一个可设置宽度、cpation的居中对齐图片。Html标准语法。
2. `imgl`。插入一个可设置宽度、cpation的居左浮动对齐图片。Html标准语法。
3. `imgr`。插入一个可设置宽度、cpation的居右浮动对齐图片。Html标准语法。
4. `stripout`。通过covert.py(cheese_course)/publish.py(zillionare)执行发布（deploy, preview, publish）时，会自动删除这一段。作用：在markdown中显示代码运行的结果。但当markdown转换为notebook之后，这部分结果会以执行结果的方式显示出来。为了避免notebook运行之后，出现不必要的重复，我们需要在markdown中，将这些内容标注为 stripout。
5. `paid`。用来隐藏付费内容。通过 publish.py 执行之后，生成的notebook中会包含这些内容，但生成的markdown中不会包含这些内容，从而可以发布到公众号、官网等处。生成的markdown会出现在/tmp目录下，发布公众号需要使用/tmp目录下、经过处理的文件，而不是源文件。
   ```info
   需要检查官网是如何实现的？
   ```

其它 code snippets，一部分是用来编写 slides 用的，一部分是用来编写习题用的。

## 4. 课件部署命令 （covert.py）

课件一般使用markdown格式编写，在部署时再转换成为ipynb格式。转换命令应该通过 covert.py 脚本来执行，它除了调用notedown之外，还将进行一些预处理

### 预览

我们使用markdown开发课件，部署时转换为ipynb格式。为防止本地环境与部署环境差异，需要在正式部署前，进行预览。命令是：

```bash
python scripts/convert.py preview {course_id} docs/path/to/*.md
```

这些文件会copy到服务器上 ~/courses/{course_id}/.{user} 目录下，从而被映射到course_{course_id}_{user}容器的 wip 目录下。

### 仅转换 notebook

```bash
python scripts/convert.py notebook path/to/courseware/*.md
```

与直接调用notedown相比，这个命令还会对markdown进行预处理，即使用我们的扩展语法。

### 部署正课

```bash
python scripts/convert.py deploy {course_id} path/to/* --convert_md
```
这将把文件部署到远程服务器上 ~/course/{course_id}/path/to/*下。如果*为markdown，且conver_md为True，还将转换为ipynb格式。

### 生成习题

```bash
python scripts/convert.py assignment {course_id} path/to/assignments/*.md --remote --debug
```

此时，markdown文件会被转换再部署。但部署的文件是没有运行、不带运行结果的。如果要带运行结果部署，可分两步进行，先运行notebook，再deploy刚刚生成的ipynb文件。

## 5. Markdown与Notebook格式转换

在课程相关开发中，我们一般使用python scripts/convert.py的相关命令来进行markdown与notebook格式的转换。这将进行必要的预处理，使得一些增加的markdown语法，在转换成notebook之后，能在我们课程环境下得到支持，比如admonition，footnotes，图片及示例代码的编码及引用，在admonition中嵌套代码等等。

转换是通过notedown来实现的。在调试场合，我们也可能直接使用notedown:

### 5.1. markdown > notebook

```
notedown path/to/courseware/*.md -o path/to/courseware/*.ipynb --match=python
```

这里--match=python的作用是，仅将```python引起的代码段转换为notebook中的可执行单元。

### 5.2. notebook > markdown

```
notedown path/to/courseware/*.ipynb -o path/to/courseware/*.md --strip
```

这里的--strip的作用是，将notebook中的输出单元删除（一般来说，它们在markdown中无法美观地显示）


## 6. 商品宣传图

商品宣传图是通过slidev或者MPE来生成的。

不同的渠道对主图、规格图有不同的长宽比要求，所以，在slidev_themes中，存在对应的项目。生成时，使用以下命令：

```
./slidev serve docs/promotion/lz/market.md lzmarket
```

商品详情图通过markdown preview enhanced 打开预览，再在浏览器中打开，调整宽度，再使用长截图。
